cmake_minimum_required(VERSION 3.22.1)

project("engine-lib")

# Включаем заголовочные файлы
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/includes)

# Путь к предкомпилированным библиотекам
# Мапим arm64-v8a на aarch64, так как библиотеки собраны для aarch64
if (ANDROID_ABI STREQUAL "arm64-v8a")
    set(LIB_ABI "aarch64")
else ()
    set(LIB_ABI ${ANDROID_ABI})
endif ()
set(LIBS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${LIB_ABI})

find_library(ANDROID_LOG_LIB log)

# Создаем shared библиотеку для JNI
add_library(${PROJECT_NAME} SHARED jni_bridge.cpp)

SET(WHITE_LABEL_NAME asta)
SET(WHITEMCLIENT_PLATFORM_LIBS ${WHITE_LABEL_NAME}android)
TARGET_COMPILE_DEFINITIONS(${PROJECT_NAME} PRIVATE -DBUILD_ANDROID=1)
foreach(lib ${WHITEMCLIENT_PLATFORM_LIBS})
    add_library(${lib} STATIC IMPORTED)
    set_target_properties(${lib} PROPERTIES IMPORTED_LOCATION ${LIBS_DIR}/lib${lib}.a)
endforeach()

SET(WHITEMCLIENT_LIBS ${WHITE_LABEL_NAME}client)
foreach(lib ${WHITEMCLIENT_LIBS})
    ADD_LIBRARY(${lib} STATIC IMPORTED)
    SET_TARGET_PROPERTIES(${lib} PROPERTIES IMPORTED_LOCATION ${LIBS_DIR}/lib${lib}.a)
endforeach()

SET(WHITEMCLIENT_HATN_LIBS
        hatnopenssl
        hatnrocksdb
        hatnrocksdbschema

        hatnclientserver
        hatnmq
        hatnclientapp
        hatnapp
        hatnapi
        hatndb
        hatnnetwork
        hatncrypt
        hatnlogcontext
        hatndataunit
        hatnbase
        hatncommon
)

foreach(lib ${WHITEMCLIENT_HATN_LIBS})
    add_library(${lib} STATIC IMPORTED)
    set_target_properties(${lib} PROPERTIES IMPORTED_LOCATION ${LIBS_DIR}/lib${lib}.a)
endforeach()

SET(WHITEMCLIENT_DEPS_LIBS
        boost_locale
        boost_thread
        boost_atomic
        boost_timer
        boost_container
        boost_program_options
        boost_date_time
        boost_random
        boost_exception
        boost_filesystem
        boost_iostreams
        boost_regex
        boost_system

        cares
        rocksdb
        lz4
        ssl
        crypto
        fmt
)

foreach(lib ${WHITEMCLIENT_DEPS_LIBS})
    add_library(${lib} STATIC IMPORTED)
    set_target_properties(${lib} PROPERTIES IMPORTED_LOCATION ${LIBS_DIR}/lib${lib}.a)
endforeach()

SET(WHITEMCLIENT_SYS_LIBS iconv)
ADD_LIBRARY(${WHITEMCLIENT_SYS_LIBS} STATIC IMPORTED)
SET_TARGET_PROPERTIES(${WHITEMCLIENT_SYS_LIBS} PROPERTIES IMPORTED_LOCATION ${LIBS_DIR}/lib${WHITEMCLIENT_SYS_LIBS}.a)

set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -lc++abi")
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -lc++abi")

TARGET_LINK_LIBRARIES(${PROJECT_NAME} PUBLIC
        ${WHITEMCLIENT_PLATFORM_LIBS}
        ${WHITEMCLIENT_LIBS}
        ${WHITEMCLIENT_HATN_LIBS}
        ${WHITEMCLIENT_DEPS_LIBS}
        ${ANDROID_LOG_LIB}
)

SET(WHITEMCLIENT_BRIDGE_LIB whitemclientbridge)
ADD_LIBRARY(${WHITEMCLIENT_BRIDGE_LIB} STATIC IMPORTED)
SET_TARGET_PROPERTIES(${WHITEMCLIENT_BRIDGE_LIB} PROPERTIES IMPORTED_LOCATION ${LIBS_DIR}/lib${WHITEMCLIENT_BRIDGE_LIB}.a)

TARGET_LINK_LIBRARIES(${PROJECT_NAME} PUBLIC ${WHITEMCLIENT_BRIDGE_LIB})