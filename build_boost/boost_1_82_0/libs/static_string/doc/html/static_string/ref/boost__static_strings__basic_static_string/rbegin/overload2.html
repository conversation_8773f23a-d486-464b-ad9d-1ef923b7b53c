<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_static_string::rbegin (2 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Boost.StaticString">
<link rel="up" href="../rbegin.html" title="basic_static_string::rbegin">
<link rel="prev" href="overload1.html" title="basic_static_string::rbegin (1 of 2 overloads)">
<link rel="next" href="../crbegin.html" title="basic_static_string::crbegin">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../rbegin.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../crbegin.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="static_string.ref.boost__static_strings__basic_static_string.rbegin.overload2"></a><a class="link" href="overload2.html" title="basic_static_string::rbegin (2 of 2 overloads)">basic_static_string::rbegin
          (2 of 2 overloads)</a>
</h5></div></div></div>
<p>
            Return a reverse iterator to the beginning.
          </p>
<h6>
<a name="static_string.ref.boost__static_strings__basic_static_string.rbegin.overload2.h0"></a>
            <span class="phrase"><a name="static_string.ref.boost__static_strings__basic_static_string.rbegin.overload2.synopsis"></a></span><a class="link" href="overload2.html#static_string.ref.boost__static_strings__basic_static_string.rbegin.overload2.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="keyword">constexpr</span> <span class="identifier">const_reverse_iterator</span>
<span class="identifier">rbegin</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2019, 2020 Krystian Stasiowski<br>Copyright © 2016-2019 Vinnie
      Falco<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../rbegin.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../crbegin.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
