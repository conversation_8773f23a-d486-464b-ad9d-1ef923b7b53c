<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.16">
<meta name="author" content="Peter Dimov">
<title>Boost.Variant2: A never valueless variant type</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700">
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;-webkit-tap-highlight-color:transparent}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos{border-right:1px solid;opacity:.35;padding-right:.5em}
pre.pygments .lineno{border-right:1px solid;opacity:.35;display:inline-block;margin-right:.75em}
pre.pygments .lineno::before{content:"";margin-right:-.125em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all tr,table.stripes-odd tr:nth-of-type(odd),table.stripes-even tr:nth-of-type(even),table.stripes-hover tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
ol>li p,ul>li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
.gist .file-data>table{border:0;background:#fff;width:100%;margin-bottom:0}
.gist .file-data>table td.line-data{width:99%}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
<style>
pre.rouge table td { padding: 5px; }
pre.rouge table pre { margin: 0; }
pre.rouge .cm {
  color: #999988;
  font-style: italic;
}
pre.rouge .cp {
  color: #999999;
  font-weight: bold;
}
pre.rouge .c1 {
  color: #999988;
  font-style: italic;
}
pre.rouge .cs {
  color: #999999;
  font-weight: bold;
  font-style: italic;
}
pre.rouge .c, pre.rouge .ch, pre.rouge .cd, pre.rouge .cpf {
  color: #999988;
  font-style: italic;
}
pre.rouge .err {
  color: #a61717;
  background-color: #e3d2d2;
}
pre.rouge .gd {
  color: #000000;
  background-color: #ffdddd;
}
pre.rouge .ge {
  color: #000000;
  font-style: italic;
}
pre.rouge .gr {
  color: #aa0000;
}
pre.rouge .gh {
  color: #999999;
}
pre.rouge .gi {
  color: #000000;
  background-color: #ddffdd;
}
pre.rouge .go {
  color: #888888;
}
pre.rouge .gp {
  color: #555555;
}
pre.rouge .gs {
  font-weight: bold;
}
pre.rouge .gu {
  color: #aaaaaa;
}
pre.rouge .gt {
  color: #aa0000;
}
pre.rouge .kc {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kd {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kn {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kp {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kr {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kt {
  color: #445588;
  font-weight: bold;
}
pre.rouge .k, pre.rouge .kv {
  color: #000000;
  font-weight: bold;
}
pre.rouge .mf {
  color: #009999;
}
pre.rouge .mh {
  color: #009999;
}
pre.rouge .il {
  color: #009999;
}
pre.rouge .mi {
  color: #009999;
}
pre.rouge .mo {
  color: #009999;
}
pre.rouge .m, pre.rouge .mb, pre.rouge .mx {
  color: #009999;
}
pre.rouge .sa {
  color: #000000;
  font-weight: bold;
}
pre.rouge .sb {
  color: #d14;
}
pre.rouge .sc {
  color: #d14;
}
pre.rouge .sd {
  color: #d14;
}
pre.rouge .s2 {
  color: #d14;
}
pre.rouge .se {
  color: #d14;
}
pre.rouge .sh {
  color: #d14;
}
pre.rouge .si {
  color: #d14;
}
pre.rouge .sx {
  color: #d14;
}
pre.rouge .sr {
  color: #009926;
}
pre.rouge .s1 {
  color: #d14;
}
pre.rouge .ss {
  color: #990073;
}
pre.rouge .s, pre.rouge .dl {
  color: #d14;
}
pre.rouge .na {
  color: #008080;
}
pre.rouge .bp {
  color: #999999;
}
pre.rouge .nb {
  color: #0086B3;
}
pre.rouge .nc {
  color: #445588;
  font-weight: bold;
}
pre.rouge .no {
  color: #008080;
}
pre.rouge .nd {
  color: #3c5d5d;
  font-weight: bold;
}
pre.rouge .ni {
  color: #800080;
}
pre.rouge .ne {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nf, pre.rouge .fm {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nl {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nn {
  color: #555555;
}
pre.rouge .nt {
  color: #000080;
}
pre.rouge .vc {
  color: #008080;
}
pre.rouge .vg {
  color: #008080;
}
pre.rouge .vi {
  color: #008080;
}
pre.rouge .nv, pre.rouge .vm {
  color: #008080;
}
pre.rouge .ow {
  color: #000000;
  font-weight: bold;
}
pre.rouge .o {
  color: #000000;
  font-weight: bold;
}
pre.rouge .w {
  color: #bbbbbb;
}
pre.rouge {
  background-color: #f8f8f8;
}
</style>
</head>
<body class="article toc2 toc-left">
<div id="header">
<h1>Boost.Variant2: A never valueless variant type</h1>
<div class="details">
<span id="author" class="author">Peter Dimov</span><br>
</div>
<div id="toc" class="toc2">
<div id="toctitle">Table of Contents</div>
<ul class="sectlevel1">
<li><a href="#overview">Overview</a>
<ul class="sectlevel2">
<li><a href="#overview_description">Description</a></li>
<li><a href="#overview_usage_examples">Usage Examples</a></li>
<li><a href="#overview_construction_and_assignment">Construction and Assignment</a></li>
<li><a href="#overview_inspecting_the_value">Inspecting the Value</a></li>
<li><a href="#overview_visitation">Visitation</a></li>
<li><a href="#overview_default_construction">Default Construction</a></li>
</ul>
</li>
<li><a href="#changelog">Revision History</a>
<ul class="sectlevel2">
<li><a href="#changelog_changes_in_1_81_0">Changes in 1.81.0</a></li>
<li><a href="#changelog_changes_in_1_79_0">Changes in 1.79.0</a></li>
<li><a href="#changelog_changes_in_1_78_0">Changes in 1.78.0</a></li>
<li><a href="#changelog_changes_in_1_76_0">Changes in 1.76.0</a></li>
<li><a href="#changelog_changes_in_1_74_0">Changes in 1.74.0</a></li>
<li><a href="#changelog_changes_in_1_73_0">Changes in 1.73.0</a></li>
<li><a href="#changelog_changes_in_1_71_0">Changes in 1.71.0</a></li>
</ul>
</li>
<li><a href="#design">Design</a>
<ul class="sectlevel2">
<li><a href="#design_features">Features</a></li>
<li><a href="#design_rationale">Rationale</a>
<ul class="sectlevel3">
<li><a href="#design_never_valueless">Never Valueless</a></li>
<li><a href="#design_strong_exception_safety">Strong Exception Safety</a></li>
</ul>
</li>
<li><a href="#design_differences_with_stdvariant">Differences with std::variant</a></li>
<li><a href="#design_differences_with_boost_variant">Differences with Boost.Variant</a></li>
</ul>
</li>
<li><a href="#implementation">Implementation</a>
<ul class="sectlevel2">
<li><a href="#implementation_dependencies">Dependencies</a></li>
<li><a href="#implementation_supported_compilers">Supported Compilers</a></li>
</ul>
</li>
<li><a href="#reference">Reference</a>
<ul class="sectlevel2">
<li><a href="#ref_boostvariant2variant_hpp">&lt;boost/variant2/variant.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_synopsis">Synopsis</a></li>
<li><a href="#ref_variant">variant</a>
<ul class="sectlevel4">
<li><a href="#ref_constructors">Constructors</a></li>
<li><a href="#ref_destructor">Destructor</a></li>
<li><a href="#ref_assignment">Assignment</a></li>
<li><a href="#ref_modifiers">Modifiers</a></li>
<li><a href="#ref_value_status">Value Status</a></li>
<li><a href="#ref_swap">Swap</a></li>
<li><a href="#ref_converting_constructors_extension">Converting Constructors (extension)</a></li>
<li><a href="#ref_subset_extension">Subset (extension)</a></li>
</ul>
</li>
<li><a href="#ref_variant_alternative">variant_alternative</a></li>
<li><a href="#ref_holds_alternative">holds_alternative</a></li>
<li><a href="#ref_get">get</a></li>
<li><a href="#ref_get_if">get_if</a></li>
<li><a href="#ref_unsafe_get_extension">unsafe_get (extension)</a></li>
<li><a href="#ref_relational_operators">Relational Operators</a></li>
<li><a href="#ref_swap_2">swap</a></li>
<li><a href="#ref_visit">visit</a></li>
<li><a href="#ref_visit_by_index_extension">visit_by_index (extension)</a></li>
<li><a href="#ref_stream_insertion_extension">Stream Insertion (extension)</a></li>
<li><a href="#ref_bad_variant_access">bad_variant_access</a></li>
</ul>
</li>
<li><a href="#ref_boostvariant2_hpp">&lt;boost/variant2.hpp&gt;</a></li>
</ul>
</li>
<li><a href="#copyright">Copyright and License</a></li>
</ul>
</div>
</div>
<div id="content">
<div class="sect1">
<h2 id="overview">Overview</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="overview_description">Description</h3>
<div class="paragraph">
<p>This library implements a type-safe discriminated/tagged union type,
<code>variant&lt;T&#8230;&#8203;&gt;</code>, that is API-compatible with the C&#43;&#43;17 Standard&#8217;s
<a href="http://en.cppreference.com/w/cpp/utility/variant"><code>std::variant&lt;T&#8230;&#8203;&gt;</code></a>.</p>
</div>
<div class="paragraph">
<p>A <code>variant&lt;T1, T2, &#8230;&#8203;, Tn&gt;</code> variable can hold a value of any of the
types <code>T1</code>, <code>T2</code>, &#8230;&#8203;, <code>Tn</code>. For example,
<code>variant&lt;int64_t, double, std::string&gt;</code> can hold an <code>int64_t</code> value, a
<code>double</code> value, or a <code>string</code> value.</p>
</div>
<div class="paragraph">
<p>Such a type is sometimes called a "tagged union", because it&#8217;s roughly
equivalent to</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">V</span>
<span class="p">{</span>
    <span class="k">enum</span> <span class="n">tag</span> <span class="p">{</span> <span class="n">tag_int64_t</span><span class="p">,</span> <span class="n">tag_double</span><span class="p">,</span> <span class="n">tag_string</span> <span class="p">};</span>

    <span class="n">tag</span> <span class="n">tag_</span><span class="p">;</span>

    <span class="k">union</span>
    <span class="p">{</span>
        <span class="kt">int64_t</span>     <span class="n">i_</span><span class="p">;</span>
        <span class="kt">double</span>      <span class="n">d_</span><span class="p">;</span>
        <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">s_</span><span class="p">;</span>
    <span class="p">};</span>
<span class="p">};</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="overview_usage_examples">Usage Examples</h3>
<div class="paragraph">
<p>Variants can be used to represent dynamically-typed values. A configuration
file of the form</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">server</span><span class="p">.</span><span class="n">host</span><span class="o">=</span><span class="n">test</span><span class="p">.</span><span class="n">example</span><span class="p">.</span><span class="n">com</span>
<span class="n">server</span><span class="p">.</span><span class="n">port</span><span class="o">=</span><span class="mi">9174</span>
<span class="n">cache</span><span class="p">.</span><span class="n">max_load</span><span class="o">=</span><span class="mf">0.7</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>can be represented as <code>std::map&lt;std::string, variant&lt;int64_t, double,
std::string&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>Variants can also represent polymorphism. To take a classic example, a
polymorphic collection of shapes:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#define _USE_MATH_DEFINES
#include &lt;iostream&gt;
#include &lt;vector&gt;
#include &lt;memory&gt;
#include &lt;cmath&gt;
</span>
<span class="k">class</span> <span class="nc">Shape</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">virtual</span> <span class="o">~</span><span class="n">Shape</span><span class="p">()</span> <span class="o">=</span> <span class="k">default</span><span class="p">;</span>
    <span class="k">virtual</span> <span class="kt">double</span> <span class="n">area</span><span class="p">()</span> <span class="k">const</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">};</span>

<span class="k">class</span> <span class="nc">Rectangle</span><span class="o">:</span> <span class="k">public</span> <span class="n">Shape</span>
<span class="p">{</span>
<span class="nl">private:</span>

    <span class="kt">double</span> <span class="n">width_</span><span class="p">,</span> <span class="n">height_</span><span class="p">;</span>

<span class="nl">public:</span>

    <span class="n">Rectangle</span><span class="p">(</span> <span class="kt">double</span> <span class="n">width</span><span class="p">,</span> <span class="kt">double</span> <span class="n">height</span> <span class="p">)</span><span class="o">:</span>
        <span class="n">width_</span><span class="p">(</span> <span class="n">width</span> <span class="p">),</span> <span class="n">height_</span><span class="p">(</span> <span class="n">height</span> <span class="p">)</span> <span class="p">{}</span>

    <span class="k">virtual</span> <span class="kt">double</span> <span class="n">area</span><span class="p">()</span> <span class="k">const</span> <span class="p">{</span> <span class="k">return</span> <span class="n">width_</span> <span class="o">*</span> <span class="n">height_</span><span class="p">;</span> <span class="p">}</span>
<span class="p">};</span>

<span class="k">class</span> <span class="nc">Circle</span><span class="o">:</span> <span class="k">public</span> <span class="n">Shape</span>
<span class="p">{</span>
<span class="nl">private:</span>

    <span class="kt">double</span> <span class="n">radius_</span><span class="p">;</span>

<span class="nl">public:</span>

    <span class="k">explicit</span> <span class="n">Circle</span><span class="p">(</span> <span class="kt">double</span> <span class="n">radius</span> <span class="p">)</span><span class="o">:</span> <span class="n">radius_</span><span class="p">(</span> <span class="n">radius</span> <span class="p">)</span> <span class="p">{}</span>
    <span class="k">virtual</span> <span class="kt">double</span> <span class="n">area</span><span class="p">()</span> <span class="k">const</span> <span class="p">{</span> <span class="k">return</span> <span class="n">M_PI</span> <span class="o">*</span> <span class="n">radius_</span> <span class="o">*</span> <span class="n">radius_</span><span class="p">;</span> <span class="p">}</span>
<span class="p">};</span>

<span class="kt">double</span> <span class="nf">total_area</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">vector</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">unique_ptr</span><span class="o">&lt;</span><span class="n">Shape</span><span class="o">&gt;&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="kt">double</span> <span class="n">s</span> <span class="o">=</span> <span class="mf">0.0</span><span class="p">;</span>

    <span class="k">for</span><span class="p">(</span> <span class="k">auto</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">p</span><span class="o">:</span> <span class="n">v</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">s</span> <span class="o">+=</span> <span class="n">p</span><span class="o">-&gt;</span><span class="n">area</span><span class="p">();</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">s</span><span class="p">;</span>
<span class="p">}</span>

<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">vector</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">unique_ptr</span><span class="o">&lt;</span><span class="n">Shape</span><span class="o">&gt;&gt;</span> <span class="n">v</span><span class="p">;</span>

    <span class="n">v</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">unique_ptr</span><span class="o">&lt;</span><span class="n">Shape</span><span class="o">&gt;</span><span class="p">(</span> <span class="k">new</span> <span class="n">Circle</span><span class="p">(</span> <span class="mf">1.0</span> <span class="p">)</span> <span class="p">)</span> <span class="p">);</span>
    <span class="n">v</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">unique_ptr</span><span class="o">&lt;</span><span class="n">Shape</span><span class="o">&gt;</span><span class="p">(</span> <span class="k">new</span> <span class="n">Rectangle</span><span class="p">(</span> <span class="mf">2.0</span><span class="p">,</span> <span class="mf">3.0</span> <span class="p">)</span> <span class="p">)</span> <span class="p">);</span>

    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"Total area: "</span> <span class="o">&lt;&lt;</span> <span class="n">total_area</span><span class="p">(</span> <span class="n">v</span> <span class="p">)</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>can instead be represented as a collection of <code>variant&lt;Rectangle, Circle&gt;</code>
values. This requires the possible <code>Shape</code> types be known in advance, as is
often the case. In return, we no longer need virtual functions, or to allocate
the values on the heap with <code>new Rectangle</code> and <code>new Circle</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#define _USE_MATH_DEFINES
#include &lt;iostream&gt;
#include &lt;vector&gt;
#include &lt;cmath&gt;
</span>
<span class="cp">#include &lt;boost/variant2/variant.hpp&gt;
</span><span class="k">using</span> <span class="k">namespace</span> <span class="n">boost</span><span class="o">::</span><span class="n">variant2</span><span class="p">;</span>

<span class="k">struct</span> <span class="nc">Rectangle</span>
<span class="p">{</span>
    <span class="kt">double</span> <span class="n">width_</span><span class="p">,</span> <span class="n">height_</span><span class="p">;</span>
    <span class="kt">double</span> <span class="n">area</span><span class="p">()</span> <span class="k">const</span> <span class="p">{</span> <span class="k">return</span> <span class="n">width_</span> <span class="o">*</span> <span class="n">height_</span><span class="p">;</span> <span class="p">}</span>
<span class="p">};</span>

<span class="k">struct</span> <span class="nc">Circle</span>
<span class="p">{</span>
    <span class="kt">double</span> <span class="n">radius_</span><span class="p">;</span>
    <span class="kt">double</span> <span class="n">area</span><span class="p">()</span> <span class="k">const</span> <span class="p">{</span> <span class="k">return</span> <span class="n">M_PI</span> <span class="o">*</span> <span class="n">radius_</span> <span class="o">*</span> <span class="n">radius_</span><span class="p">;</span> <span class="p">}</span>
<span class="p">};</span>

<span class="kt">double</span> <span class="nf">total_area</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">vector</span><span class="o">&lt;</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">Rectangle</span><span class="p">,</span> <span class="n">Circle</span><span class="o">&gt;&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="kt">double</span> <span class="n">s</span> <span class="o">=</span> <span class="mf">0.0</span><span class="p">;</span>

    <span class="k">for</span><span class="p">(</span> <span class="k">auto</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">x</span><span class="o">:</span> <span class="n">v</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">s</span> <span class="o">+=</span> <span class="n">visit</span><span class="p">(</span> <span class="p">[](</span> <span class="k">auto</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">y</span> <span class="p">){</span> <span class="k">return</span> <span class="n">y</span><span class="p">.</span><span class="n">area</span><span class="p">();</span> <span class="p">},</span> <span class="n">x</span> <span class="p">);</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">s</span><span class="p">;</span>
<span class="p">}</span>

<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">vector</span><span class="o">&lt;</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">Rectangle</span><span class="p">,</span> <span class="n">Circle</span><span class="o">&gt;&gt;</span> <span class="n">v</span><span class="p">;</span>

    <span class="n">v</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span> <span class="n">Circle</span><span class="p">{</span> <span class="mf">1.0</span> <span class="p">}</span> <span class="p">);</span>
    <span class="n">v</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span> <span class="n">Rectangle</span><span class="p">{</span> <span class="mf">2.0</span><span class="p">,</span> <span class="mf">3.0</span> <span class="p">}</span> <span class="p">);</span>

    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"Total area: "</span> <span class="o">&lt;&lt;</span> <span class="n">total_area</span><span class="p">(</span> <span class="n">v</span> <span class="p">)</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="overview_construction_and_assignment">Construction and Assignment</h3>
<div class="paragraph">
<p>If we look at the</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++">    <span class="n">v</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span> <span class="n">Circle</span><span class="p">{</span> <span class="mf">1.0</span> <span class="p">}</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>line, we can deduce that <code>variant&lt;Rectangle, Circle&gt;</code> can be (implicitly)
constructed from <code>Circle</code> (and <code>Rectangle</code>), and indeed it can. It can also
be assigned a <code>Circle</code> or a <code>Rectangle</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">variant</span><span class="o">&lt;</span><span class="n">Rectangle</span><span class="p">,</span> <span class="n">Circle</span><span class="o">&gt;</span> <span class="n">v</span> <span class="o">=</span> <span class="n">Circle</span><span class="p">{</span> <span class="mf">1.0</span> <span class="p">};</span> <span class="c1">// v holds Circle</span>
<span class="n">v</span> <span class="o">=</span> <span class="n">Rectangle</span><span class="p">{</span> <span class="mf">2.0</span><span class="p">,</span> <span class="mf">3.0</span> <span class="p">};</span>                    <span class="c1">// v now holds Rectangle</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>If we try to construct <code>variant&lt;int, float&gt;</code> from something that is neither
<code>int</code> nor <code>float</code>, say, <code>(short)1</code>, the behavior is "as if" the <code>variant</code> has
declared two constructors,</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">variant</span><span class="o">::</span><span class="n">variant</span><span class="p">(</span><span class="kt">int</span> <span class="n">x</span><span class="p">);</span>
<span class="n">variant</span><span class="o">::</span><span class="n">variant</span><span class="p">(</span><span class="kt">float</span> <span class="n">x</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>and the standard overload resolution rules are used to pick the one that will
be used. So <code>variant&lt;int, float&gt;((short)1)</code> will hold an <code>int</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="overview_inspecting_the_value">Inspecting the Value</h3>
<div class="paragraph">
<p>Putting values into a <code>variant</code> is easy, but taking them out is necessarily a
bit more convoluted. It&#8217;s not possible for <code>variant&lt;int, float&gt;</code> to define a
member function <code>get() const</code>, because such a function will need its return
type fixed at compile time, and whether the correct return type is <code>int</code> or
<code>float</code> will only become known at run time.</p>
</div>
<div class="paragraph">
<p>There are a few ways around that. First, there is the accessor member function</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">variant</span><span class="o">::</span><span class="n">index</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>that returns the zero-based index of the current type. For <code>variant&lt;int,
float&gt;</code>, it will return <code>0</code> for <code>int</code> and <code>1</code> for <code>float</code>.</p>
</div>
<div class="paragraph">
<p>Once we have the index, we can use the free function <code>get&lt;N&gt;</code> to obtain the
value. Since we&#8217;re passing the type index to <code>get</code>, it knows what to return.
<code>get&lt;0&gt;(v)</code> will return <code>int</code>, and <code>get&lt;1&gt;(v)</code> will return <code>float</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">f</span><span class="p">(</span> <span class="n">variant</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">switch</span><span class="p">(</span> <span class="n">v</span><span class="p">.</span><span class="n">index</span><span class="p">()</span> <span class="p">)</span>
    <span class="p">{</span>
    <span class="k">case</span> <span class="mi">0</span><span class="p">:</span>

        <span class="c1">// use get&lt;0&gt;(v)</span>
        <span class="k">break</span><span class="p">;</span>

    <span class="k">case</span> <span class="mi">1</span><span class="p">:</span>

        <span class="c1">// use get&lt;1&gt;(v)</span>
        <span class="k">break</span><span class="p">;</span>

    <span class="nl">default:</span>

        <span class="n">assert</span><span class="p">(</span><span class="nb">false</span><span class="p">);</span> <span class="c1">// never happens</span>
    <span class="p">}</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>If we call <code>get&lt;0&gt;(v)</code>, and <code>v.index()</code> is not currently <code>0</code>, an exception
(of type <code>bad_variant_access</code>) will be thrown.</p>
</div>
<div class="paragraph">
<p>An alternative approach is to use <code>get&lt;int&gt;(v)</code> or <code>get&lt;float&gt;(v)</code>. This
works similarly.</p>
</div>
<div class="paragraph">
<p>Another alternative that avoids the possibility of <code>bad_variant_access</code> is
to use <code>get_if</code>. Instead of a reference to the contained value, it returns
a pointer to it, returning <code>nullptr</code> to indicate type mismatch. <code>get_if</code>
takes a pointer to the <code>variant</code>, so in our example we&#8217;ll use something along
the following lines:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">f</span><span class="p">(</span> <span class="n">variant</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">if</span><span class="p">(</span> <span class="kt">int</span> <span class="k">const</span> <span class="o">*</span> <span class="n">p</span> <span class="o">=</span> <span class="n">get_if</span><span class="o">&lt;</span><span class="kt">int</span><span class="o">&gt;</span><span class="p">(</span><span class="o">&amp;</span><span class="n">v</span><span class="p">)</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// use *p</span>
    <span class="p">}</span>
    <span class="k">else</span> <span class="k">if</span><span class="p">(</span> <span class="kt">float</span> <span class="k">const</span> <span class="o">*</span> <span class="n">p</span> <span class="o">=</span> <span class="n">get_if</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span><span class="p">(</span><span class="o">&amp;</span><span class="n">v</span><span class="p">)</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// use *p</span>
    <span class="p">}</span>
    <span class="k">else</span>
    <span class="p">{</span>
        <span class="n">assert</span><span class="p">(</span><span class="nb">false</span><span class="p">);</span> <span class="c1">// never happens</span>
    <span class="p">}</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="overview_visitation">Visitation</h3>
<div class="paragraph">
<p>Last but not least, there&#8217;s <code>visit</code>. <code>visit(f, v)</code> calls the a function object
<code>f</code> with the value contained in the <code>variant</code> <code>v</code> and returns the result. When
<code>v</code> is <code>variant&lt;int, float&gt;</code>, it will call <code>f</code> with either an <code>int</code> or a
<code>float</code>. The function object must be prepared to accept both.</p>
</div>
<div class="paragraph">
<p>In practice, this can be achieved by having the function take a type that can
be passed either <code>int</code> or <code>float</code>, such as <code>double</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">double</span> <span class="nf">f</span><span class="p">(</span> <span class="kt">double</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span> <span class="k">return</span> <span class="n">x</span><span class="p">;</span> <span class="p">}</span>

<span class="kt">double</span> <span class="nf">g</span><span class="p">(</span> <span class="n">variant</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="n">visit</span><span class="p">(</span> <span class="n">f</span><span class="p">,</span> <span class="n">v</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>By using a function object with an overloaded <code>operator()</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">F</span>
<span class="p">{</span>
    <span class="kt">void</span> <span class="k">operator</span><span class="p">()(</span><span class="kt">int</span> <span class="n">x</span><span class="p">)</span> <span class="k">const</span> <span class="p">{</span> <span class="cm">/* use x */</span> <span class="p">}</span>
    <span class="kt">void</span> <span class="k">operator</span><span class="p">()(</span><span class="kt">float</span> <span class="n">x</span><span class="p">)</span> <span class="k">const</span> <span class="p">{</span> <span class="cm">/* use x */</span> <span class="p">}</span>
<span class="p">};</span>

<span class="kt">void</span> <span class="nf">g</span><span class="p">(</span> <span class="n">variant</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">visit</span><span class="p">(</span> <span class="n">F</span><span class="p">(),</span> <span class="n">v</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Or by using a polymorphic lambda, as we did in our <code>Circle</code>/<code>Rectangle</code>
example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">g</span><span class="p">(</span> <span class="n">variant</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">visit</span><span class="p">(</span> <span class="p">[</span><span class="o">&amp;</span><span class="p">](</span> <span class="k">auto</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">x</span> <span class="p">){</span> <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="n">x</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span> <span class="p">},</span> <span class="n">v</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p><code>visit</code> can also take more than one <code>variant</code>. <code>visit(f, v1, v2)</code> calls
<code>f(x1, x2)</code>, where <code>x1</code> is the value contained in <code>v1</code> and <code>x2</code> is the value
in <code>v2</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="overview_default_construction">Default Construction</h3>
<div class="paragraph">
<p>The default constructor of <code>variant</code> value-initializes the first type in
the list. <code>variant&lt;int, float&gt;{}</code> holds <code>0</code> (of type <code>int</code>), and
<code>variant&lt;float, int&gt;{}</code> holds <code>0.0f</code>.</p>
</div>
<div class="paragraph">
<p>This is usually the desired behavior. However, in cases such as
<code>variant&lt;std::mutex, std::recursive_mutex&gt;</code>, one might legitimately wish to
avoid constructing a <code>std::mutex</code> by default. A provided type, <code>monostate</code>,
can be used as the first type in those scenarios. <code>variant&lt;monostate,
std::mutex, std::recursive_mutex&gt;</code> will default-construct a <code>monostate</code>,
which is basically a no-op, as <code>monostate</code> is effectively an empty <code>struct</code>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changelog">Revision History</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="changelog_changes_in_1_81_0">Changes in 1.81.0</h3>
<div class="ulist">
<ul>
<li>
<p>Added support for <code>boost::json::value_from</code> and <code>boost::json::value_to</code>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changelog_changes_in_1_79_0">Changes in 1.79.0</h3>
<div class="ulist">
<ul>
<li>
<p>Added <code>operator&lt;&lt;</code> for <code>monostate</code>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changelog_changes_in_1_78_0">Changes in 1.78.0</h3>
<div class="ulist">
<ul>
<li>
<p>Added <code>&lt;boost/variant2.hpp&gt;</code>.</p>
</li>
<li>
<p>Added <code>unsafe_get&lt;I&gt;</code>.</p>
</li>
<li>
<p>Added <code>visit_by_index</code>.</p>
</li>
<li>
<p>Added <code>operator&lt;&lt;</code>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changelog_changes_in_1_76_0">Changes in 1.76.0</h3>
<div class="ulist">
<ul>
<li>
<p>Improved generated code for the double buffered case.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changelog_changes_in_1_74_0">Changes in 1.74.0</h3>
<div class="ulist">
<ul>
<li>
<p>Added support for derived types in <code>visit</code></p>
</li>
<li>
<p>Improved compilation performance for many (hundreds of) alternatives.</p>
</li>
<li>
<p>Added support for <code>visit&lt;R&gt;</code></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changelog_changes_in_1_73_0">Changes in 1.73.0</h3>
<div class="ulist">
<ul>
<li>
<p>Added support for <code>std::hash</code>, <code>boost::hash</code>.</p>
</li>
<li>
<p><code>variant&lt;T&#8230;&#8203;&gt;</code> is now trivial when all types in <code>T&#8230;&#8203;</code> are trivial.
This improves performance by enabling it to be passed to, and returned
from, functions in registers.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changelog_changes_in_1_71_0">Changes in 1.71.0</h3>
<div class="paragraph">
<p>After the Boost formal review, the implementation has been
changed to provide the strong exception safety guarantee,
instead of basic. <code>expected</code> has been removed.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="design">Design</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="design_features">Features</h3>
<div class="paragraph">
<p>This <code>variant</code> implementation has two distinguishing features:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>It&#8217;s never "valueless", that is, <code>variant&lt;T1, T2, &#8230;&#8203;, Tn&gt;</code> has an
invariant that it always contains a valid value of one of the types
<code>T1</code>, <code>T2</code>, &#8230;&#8203;, <code>Tn</code>.</p>
</li>
<li>
<p>It provides the strong exception safety guarantee on assignment and
<code>emplace</code>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>This is achieved with the use of double storage, unless all of the
contained types have a non-throwing move constructor.</p>
</div>
</div>
<div class="sect2">
<h3 id="design_rationale">Rationale</h3>
<div class="sect3">
<h4 id="design_never_valueless">Never Valueless</h4>
<div class="paragraph">
<p>It makes intuitive sense that <code>variant&lt;X, Y, Z&gt;</code> can hold only values
of type <code>X</code>, type <code>Y</code>, or type <code>Z</code>, and nothing else.</p>
</div>
<div class="paragraph">
<p>If we think of <code>variant</code> as an extension of <code>union</code>, since a <code>union</code>
has a state called "no active member", an argument can be made that a
<code>variant&lt;X, Y, Z&gt;</code> should also have such an additional state, holding
none of <code>X</code>, <code>Y</code>, <code>Z</code>.</p>
</div>
<div class="paragraph">
<p>This however makes <code>variant</code> less convenient in practice and less useful
as a building block. If we really need a variable that only holds <code>X</code>,
<code>Y</code>, or <code>Z</code>, the additional empty state creates complications that need
to be worked around. And in the case where we do need this additional
empty state, we can just use <code>variant&lt;empty, X, Y, Z&gt;</code>, with a suitable
<code>struct empty {};</code>.</p>
</div>
<div class="paragraph">
<p>From a pure design perspective, the case for no additional empty state is
solid. Implementation considerations, however, argue otherwise.</p>
</div>
<div class="paragraph">
<p>When we replace the current value of the <code>variant</code> (of, say, type <code>X</code>) with
another (of type <code>Y</code>), since the new value needs to occupy the same storage
as the old one, we need to destroy the old <code>X</code> first, then construct a new
<code>Y</code> in its place. But since this is C&#43;&#43;, the construction can fail with an
exception. At this point the <code>variant</code> is in the "has no active member"
state that we&#8217;ve agreed it cannot be in.</p>
</div>
<div class="paragraph">
<p>This is a legitimate problem, and it is this problem that makes having
an empty/valueless state so appealing. We just leave the <code>variant</code> empty on
exception and we&#8217;re done.</p>
</div>
<div class="paragraph">
<p>As explained, though, this is undesirable from a design perspective as it
makes the component less useful and less elegant.</p>
</div>
<div class="paragraph">
<p>There are several ways around the issue. The most straightforward one is to
just disallow types whose construction can throw. Since we can always create
a temporary value first, then use the move constructor to initialize the one
in the <code>variant</code>, it&#8217;s enough to require a nonthrowing move constructor,
rather than all constructors to be nonthrowing.</p>
</div>
<div class="paragraph">
<p>Unfortunately, under at least one popular standard library implementation,
node based containers such as <code>std::list</code> and <code>std::map</code> have a potentially
throwing move constructor. Disallowing <code>variant&lt;X, std::map&lt;Y, Z&gt;&gt;</code> is hardly
practical, so the exceptional case cannot be avoided.</p>
</div>
<div class="paragraph">
<p>On exception, we could also construct some other value, leaving the <code>variant</code>
valid; but in the general case, that construction can also throw. If one of
the types has a nonthrowing default constructor, we can use it; but if not,
we can&#8217;t.</p>
</div>
<div class="paragraph">
<p>The approach Boost.Variant takes here is to allocate a temporary copy of
the value on the heap. On exception, a pointer to that temporary copy can be
stored into the <code>variant</code>. Pointer operations don&#8217;t throw.</p>
</div>
<div class="paragraph">
<p>Another option is to use double buffering. If our <code>variant</code> occupies twice
the storage, we can construct the new value in the unused half, then, once
the construction succeeds, destroy the old value in the other half.</p>
</div>
<div class="paragraph">
<p>When <code>std::variant</code> was standardized, none of those approaches was deemed
palatable, as all of them either introduce overhead or are too restrictive
with respect to the types a <code>variant</code> can contain. So as a compromise,
<code>std::variant</code> took a way that can (noncharitably) be described as "having
your cake and eating it too."</p>
</div>
<div class="paragraph">
<p>Since the described exceptional situation is relatively rare, <code>std::variant</code>
has a special case, called "valueless", into which it goes on exception,
but the interface acknowledges its existence as little as possible, allowing
users to pretend that it doesn&#8217;t exist.</p>
</div>
<div class="paragraph">
<p>This is, arguably, not that bad from a practical point of view, but it leaves
many of us wanting. Rare states that "never" occur are undertested and when
that "never" actually happens, it&#8217;s usually in the most inconvenient of times.</p>
</div>
<div class="paragraph">
<p>This implementation does not follow <code>std::variant</code>; it statically guarantees
that <code>variant</code> is never in a valueless state. The function
<code>valueless_by_exception</code> is provided for compatibility, but it always returns
<code>false</code>.</p>
</div>
<div class="paragraph">
<p>Instead, if the contained types are such that it&#8217;s not possible to avoid an
exceptional situation when changing the contained value, double storage is
used.</p>
</div>
</div>
<div class="sect3">
<h4 id="design_strong_exception_safety">Strong Exception Safety</h4>
<div class="paragraph">
<p>The initial submission only provided the basic exception safety guarantee.
If an attempt to change the contained value (via assignment or <code>emplace</code>)
failed with an exception, and a type with a nonthrowing default constructor
existed among the alternatives, a value of that type was created into the
<code>variant</code>. The upside of this decision was that double storage was needed
less frequently.</p>
</div>
<div class="paragraph">
<p>The reviewers were fairly united in hating it. Constructing a random type
was deemed too unpredictable and not complying with the spirit of the
basic guarantee. The default constructor of the chosen type, even if
nonthrowing, may still have undesirable side effects. Or, if not that, a
value of that type may have special significance for the surrounding code.
Therefore, some argued, the <code>variant</code> should either remain with its
old value, or transition into the new one, without synthesizing other
states.</p>
</div>
<div class="paragraph">
<p>At the other side of the spectrum, there were those who considered double
storage unacceptable. But they considered it unacceptable in principle,
regardless of the frequency with which it was used.</p>
</div>
<div class="paragraph">
<p>As a result, providing the strong exception safety guarantee on assignment
and <code>emplace</code> was declared an acceptance condition.</p>
</div>
<div class="paragraph">
<p>In retrospect, this was the right decision. The reason the strong guarantee
is generally not provided is because it doesn&#8217;t compose. When <code>X</code> and <code>Y</code>
provide the basic guarantee on assignment, so does <code>struct { X x; Y y; };</code>.
Similarly, when <code>X</code> and <code>Y</code> have nonthrowing assignments, so does the
<code>struct</code>. But this doesn&#8217;t hold for the strong guarantee.</p>
</div>
<div class="paragraph">
<p>The usual practice is to provide the basic guarantee on assignment and
let the user synthesize a "strong" assignment out of either a nonthrowing
<code>swap</code> or a nonthrowing move assignment. That is, given <code>x1</code> and <code>x2</code> of
type <code>X</code>, instead of the "basic" <code>x1 = x2;</code>, use either <code>X(x2).swap(x1);</code>
or <code>x1 = X(x2);</code>.</p>
</div>
<div class="paragraph">
<p>Nearly all types provide a nonthrowing <code>swap</code> or a nonthrowing move
assignment, so this works well. Nearly all, except <code>variant</code>, which in the
general case has neither a nonthrowing <code>swap</code> nor a nonthrowing move
assignment. If <code>variant</code> does not provide the strong guarantee itself, it&#8217;s
impossible for the user to synthesize it.</p>
</div>
<div class="paragraph">
<p>So it should, and so it does.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="design_differences_with_stdvariant">Differences with std::variant</h3>
<div class="paragraph">
<p>The main differences between this implementation and <code>std::variant</code> are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>No valueless-by-exception state: <code>valueless_by_exception()</code> always
returns <code>false</code>.</p>
</li>
<li>
<p>Strong exception safety guarantee on assignment and <code>emplace</code>.</p>
</li>
<li>
<p><code>emplace</code> first constructs the new value and then destroys the old one;
in the single storage case, this translates to constructing a temporary
and then moving it into place.</p>
</li>
<li>
<p>A converting constructor from, e.g. <code>variant&lt;int, float&gt;</code> to
<code>variant&lt;float, double, int&gt;</code> is provided as an extension.</p>
</li>
<li>
<p>The reverse operation, going from <code>variant&lt;float, double, int&gt;</code> to
<code>variant&lt;int, float&gt;</code> is provided as the member function <code>subset&lt;U&#8230;&#8203;&gt;</code>.
(This operation can throw if the current state of the variant cannot be
represented.)</p>
</li>
<li>
<p><code>unsafe_get</code>, an unchecked alternative to <code>get</code> and <code>get_if</code>, is provided
as an extension.</p>
</li>
<li>
<p><code>visit_by_index</code>, a visitation function that takes a single variant and a
number of function objects, one per alternative, is provided as an extension.</p>
</li>
<li>
<p>The C&#43;&#43;20 additions and changes to <code>std::variant</code> have not yet been
implemented.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="design_differences_with_boost_variant">Differences with Boost.Variant</h3>
<div class="paragraph">
<p>This library is API compatible with <code>std::variant</code>. As such, its interface
is different from Boost.Variant&#8217;s. For example, visitation is performed via
<code>visit</code> instead of <code>apply_visitor</code>.</p>
</div>
<div class="paragraph">
<p>Recursive variants are not supported.</p>
</div>
<div class="paragraph">
<p>Double storage is used instead of temporary heap backup. This <code>variant</code> is
always "stack-based", it never allocates, and never throws <code>bad_alloc</code> on
its own.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="implementation">Implementation</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="implementation_dependencies">Dependencies</h3>
<div class="paragraph">
<p>This implementation only depends on Boost.Config, Boost.Assert, and Boost.Mp11.</p>
</div>
</div>
<div class="sect2">
<h3 id="implementation_supported_compilers">Supported Compilers</h3>
<div class="ulist">
<ul>
<li>
<p>GCC 4.8 or later with <code>-std=c++11</code> or above</p>
</li>
<li>
<p>Clang 3.9 or later with <code>-std=c++11</code> or above</p>
</li>
<li>
<p>Visual Studio 2015 or later</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Tested on <a href="https://github.com/boostorg/variant2/actions">Github Actions</a> and
<a href="https://ci.appveyor.com/project/pdimov/variant2-fkab9">Appveyor</a>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="reference">Reference</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="ref_boostvariant2variant_hpp">&lt;boost/variant2/variant.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_synopsis">Synopsis</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">variant2</span> <span class="p">{</span>

<span class="c1">// in_place_type</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">in_place_type_t</span> <span class="p">{};</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">in_place_type_t</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span> <span class="n">in_place_type</span><span class="p">{};</span>

<span class="c1">// in_place_index</span>

<span class="k">template</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">in_place_index_t</span> <span class="p">{};</span>
<span class="k">template</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">in_place_index_t</span><span class="o">&lt;</span><span class="n">I</span><span class="o">&gt;</span> <span class="n">in_place_index</span><span class="p">{};</span>

<span class="c1">// variant</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">class</span> <span class="nc">variant</span><span class="p">;</span>

<span class="c1">// variant_size</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_size</span> <span class="p">{};</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_size</span><span class="o">&lt;</span><span class="n">T</span> <span class="k">const</span><span class="o">&gt;:</span> <span class="n">variant_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span> <span class="p">{};</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_size</span><span class="o">&lt;</span><span class="n">T</span> <span class="k">volatile</span><span class="o">&gt;:</span> <span class="n">variant_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span> <span class="p">{};</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_size</span><span class="o">&lt;</span><span class="n">T</span> <span class="k">const</span> <span class="k">volatile</span><span class="o">&gt;:</span> <span class="n">variant_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span> <span class="p">{};</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&amp;&gt;:</span> <span class="n">variant_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span> <span class="p">{};</span> <span class="c1">// extension</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&amp;&amp;&gt;:</span> <span class="n">variant_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span> <span class="p">{};</span> <span class="c1">// extension</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="kr">inline</span> <span class="k">constexpr</span> <span class="kt">size_t</span> <span class="n">variant_size_v</span> <span class="o">=</span> <span class="n">variant_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;::</span><span class="n">value</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">variant_size</span><span class="o">&lt;</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;:</span>
    <span class="n">std</span><span class="o">::</span><span class="n">integral_constant</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">...(</span><span class="n">T</span><span class="p">)</span><span class="o">&gt;</span> <span class="p">{};</span>

<span class="c1">// variant_alternative</span>

<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span> <span class="p">{};</span>

<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span> <span class="k">const</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span> <span class="k">volatile</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span> <span class="k">const</span> <span class="k">volatile</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span><span class="o">&amp;&gt;</span><span class="p">;</span> <span class="c1">// extension</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span><span class="o">&amp;&amp;&gt;</span><span class="p">;</span> <span class="c1">// extension</span>

<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">using</span> <span class="n">variant_alternative_t</span> <span class="o">=</span> <span class="k">typename</span> <span class="n">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;</span><span class="p">;</span>

<span class="c1">// variant_npos</span>

<span class="k">constexpr</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">variant_npos</span> <span class="o">=</span> <span class="o">-</span><span class="mi">1</span><span class="p">;</span>

<span class="c1">// holds_alternative</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">holds_alternative</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="c1">// get</span>

<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;&amp;</span>
    <span class="n">get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;&amp;</span>
    <span class="n">get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">U</span><span class="o">&amp;</span> <span class="n">get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">U</span><span class="o">&amp;&amp;</span> <span class="n">get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">U</span><span class="o">&amp;</span> <span class="n">get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">U</span><span class="o">&amp;&amp;</span> <span class="n">get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span>

<span class="c1">// get_if</span>

<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">add_pointer_t</span><span class="o">&lt;</span><span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&gt;</span>
    <span class="n">get_if</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;*</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">add_pointer_t</span><span class="o">&lt;</span><span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&gt;</span>
    <span class="n">get_if</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;*</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">add_pointer_t</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;</span>
    <span class="n">get_if</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;*</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">add_pointer_t</span><span class="o">&lt;</span><span class="k">const</span> <span class="n">U</span><span class="o">&gt;</span>
    <span class="n">get_if</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;*</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="c1">// unsafe_get (extension)</span>

<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">unsafe_get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;&amp;</span>
    <span class="n">unsafe_get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">unsafe_get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;&amp;</span>
    <span class="n">unsafe_get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span>

<span class="c1">// relational operators</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&gt;</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;=</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&gt;=</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span>

<span class="c1">// swap</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">swap</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>

<span class="c1">// visit</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span> <span class="o">=</span> <span class="cm">/*unspecified*/</span><span class="p">,</span> <span class="k">class</span> <span class="nc">F</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">V</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="cm">/*see below*/</span> <span class="n">visit</span><span class="p">(</span><span class="n">F</span><span class="o">&amp;&amp;</span> <span class="n">f</span><span class="p">,</span> <span class="n">V</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">v</span><span class="p">);</span>

<span class="c1">// visit_by_index (extension)</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span> <span class="o">=</span> <span class="cm">/*unspecified*/</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">F</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="cm">/*see below*/</span> <span class="n">visit_by_index</span><span class="p">(</span><span class="n">V</span><span class="o">&amp;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="n">F</span><span class="o">&amp;&amp;</span><span class="p">..</span> <span class="n">f</span><span class="p">);</span>

<span class="c1">// monostate</span>

<span class="k">struct</span> <span class="nc">monostate</span> <span class="p">{};</span>

<span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span><span class="n">monostate</span><span class="p">,</span> <span class="n">monostate</span><span class="p">)</span> <span class="k">noexcept</span> <span class="p">{</span> <span class="k">return</span> <span class="nb">true</span><span class="p">;</span> <span class="p">}</span>
<span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span><span class="n">monostate</span><span class="p">,</span> <span class="n">monostate</span><span class="p">)</span> <span class="k">noexcept</span> <span class="p">{</span> <span class="k">return</span> <span class="nb">false</span><span class="p">;</span> <span class="p">}</span>
<span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;</span><span class="p">(</span><span class="n">monostate</span><span class="p">,</span> <span class="n">monostate</span><span class="p">)</span> <span class="k">noexcept</span> <span class="p">{</span> <span class="k">return</span> <span class="nb">false</span><span class="p">;</span> <span class="p">}</span>
<span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&gt;</span><span class="p">(</span><span class="n">monostate</span><span class="p">,</span> <span class="n">monostate</span><span class="p">)</span> <span class="k">noexcept</span> <span class="p">{</span> <span class="k">return</span> <span class="nb">false</span><span class="p">;</span> <span class="p">}</span>
<span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;=</span><span class="p">(</span><span class="n">monostate</span><span class="p">,</span> <span class="n">monostate</span><span class="p">)</span> <span class="k">noexcept</span> <span class="p">{</span> <span class="k">return</span> <span class="nb">true</span><span class="p">;</span> <span class="p">}</span>
<span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&gt;=</span><span class="p">(</span><span class="n">monostate</span><span class="p">,</span> <span class="n">monostate</span><span class="p">)</span> <span class="k">noexcept</span> <span class="p">{</span> <span class="k">return</span> <span class="nb">true</span><span class="p">;</span> <span class="p">}</span>

<span class="c1">// stream insertion (extension)</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">Ch</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tr</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">Ch</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tr</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="n">monostate</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>

<span class="c1">// bad_variant_access</span>

<span class="k">class</span> <span class="nc">bad_variant_access</span><span class="p">;</span>

<span class="p">}</span> <span class="c1">// namespace variant2</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="ref_variant">variant</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">variant2</span> <span class="p">{</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">class</span> <span class="nc">variant</span>
<span class="p">{</span>
<span class="nl">public:</span>

  <span class="c1">// constructors</span>

  <span class="k">constexpr</span> <span class="n">variant</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>

  <span class="k">constexpr</span> <span class="n">variant</span><span class="p">(</span> <span class="n">variant</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>
  <span class="k">constexpr</span> <span class="n">variant</span><span class="p">(</span> <span class="n">variant</span><span class="o">&amp;&amp;</span> <span class="n">r</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">&gt;</span>
    <span class="k">constexpr</span> <span class="n">variant</span><span class="p">(</span> <span class="n">U</span><span class="o">&amp;&amp;</span> <span class="n">u</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">constexpr</span> <span class="k">explicit</span> <span class="n">variant</span><span class="p">(</span> <span class="n">in_place_type_t</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>
  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">constexpr</span> <span class="k">explicit</span> <span class="n">variant</span><span class="p">(</span> <span class="n">in_place_type_t</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;</span><span class="p">,</span>
      <span class="n">std</span><span class="o">::</span><span class="n">initializer_list</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span> <span class="n">il</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">constexpr</span> <span class="k">explicit</span> <span class="n">variant</span><span class="p">(</span> <span class="n">in_place_index_t</span><span class="o">&lt;</span><span class="n">I</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>
  <span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">constexpr</span> <span class="k">explicit</span> <span class="n">variant</span><span class="p">(</span> <span class="n">in_place_index_t</span><span class="o">&lt;</span><span class="n">I</span><span class="o">&gt;</span><span class="p">,</span>
      <span class="n">std</span><span class="o">::</span><span class="n">initializer_list</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span> <span class="n">il</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

  <span class="c1">// destructor</span>

  <span class="o">~</span><span class="n">variant</span><span class="p">();</span>

  <span class="c1">// assignment</span>

  <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">variant</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>
  <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">variant</span><span class="o">&amp;&amp;</span> <span class="n">r</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">U</span><span class="o">&amp;&amp;</span> <span class="n">u</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>

  <span class="c1">// modifiers</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">constexpr</span> <span class="n">U</span><span class="o">&amp;</span> <span class="n">emplace</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>
  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">constexpr</span> <span class="n">U</span><span class="o">&amp;</span> <span class="n">emplace</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">initializer_list</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span> <span class="n">il</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
      <span class="n">emplace</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>
  <span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
      <span class="n">emplace</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">initializer_list</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span> <span class="n">il</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

  <span class="c1">// value status</span>

  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">valueless_by_exception</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="k">constexpr</span> <span class="kt">size_t</span> <span class="n">index</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="c1">// swap</span>

  <span class="kt">void</span> <span class="n">swap</span><span class="p">(</span> <span class="n">variant</span><span class="o">&amp;</span> <span class="n">r</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>

  <span class="c1">// converting constructors (extension)</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="n">variant</span><span class="p">(</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">r</span> <span class="p">)</span>
    <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="n">variant</span><span class="p">(</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">r</span> <span class="p">)</span>
    <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span>

  <span class="c1">// subset (extension)</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="n">subset</span><span class="p">()</span> <span class="o">&amp;</span> <span class="p">;</span>
  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="n">subset</span><span class="p">()</span> <span class="o">&amp;&amp;</span> <span class="p">;</span>
  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="n">subset</span><span class="p">()</span> <span class="k">const</span><span class="o">&amp;</span> <span class="p">;</span>
  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="n">subset</span><span class="p">()</span> <span class="k">const</span><span class="o">&amp;&amp;</span> <span class="p">;</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace variant2</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>In the descriptions that follow, let <code>i</code> be in the range <code>[0, sizeof&#8230;&#8203;(T))</code>,
and <code>Ti</code> be the <code>i</code>-th type in <code>T&#8230;&#8203;</code>.</p>
</div>
<div class="sect4">
<h5 id="ref_constructors">Constructors</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="nf">variant</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">is_nothrow_default_constructible_v</span><span class="o">&lt;</span><span class="n">T0</span><span class="o">&gt;</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Constructs a <code>variant</code> holding a value-initialized value of
type <code>T0</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>index() == 0</code>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>Any exception thrown by the value-initialization of <code>T0</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
<code>std::is_default_constructible_v&lt;T0&gt;</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="nf">variant</span><span class="p">(</span> <span class="n">variant</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">w</span> <span class="p">)</span>
  <span class="k">noexcept</span><span class="p">(</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_nothrow_copy_constructible</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;::</span><span class="n">value</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes the variant to hold the same alternative and value as
<code>w</code>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>Any exception thrown by the initialization of the contained value.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
<code>std::is_copy_constructible_v&lt;Ti&gt;</code> is <code>true</code> for all <code>i</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="nf">variant</span><span class="p">(</span> <span class="n">variant</span><span class="o">&amp;&amp;</span> <span class="n">w</span> <span class="p">)</span>
  <span class="k">noexcept</span><span class="p">(</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_nothrow_move_constructible</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;::</span><span class="n">value</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes the variant to hold the same alternative and value as
<code>w</code>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>Any exception thrown by the move-initialization of the contained
value.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
<code>std::is_move_constructible_v&lt;Ti&gt;</code> is <code>true</code> for all <code>i</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="nf">variant</span><span class="p">(</span> <span class="n">U</span><span class="o">&amp;&amp;</span> <span class="n">u</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span><span class="cm">/*see below*/</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Let <code>Tj</code> be a type that is determined as follows: build an imaginary function
<code>FUN(Ti)</code> for each alternative type <code>Ti</code>. The overload <code>FUN(Tj)</code> selected by
overload resolution for the expression <code>FUN(std::forward&lt;U&gt;(u))</code> defines the
alternative <code>Tj</code> which is the type of the contained value after construction.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes <code>*this</code> to hold the alternative type <code>Tj</code> and
initializes the contained value from <code>std::forward&lt;U&gt;(u)</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>holds_alternative&lt;Tj&gt;(*this)</code>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>Any exception thrown by the initialization of the contained value.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>The expression inside <code>noexcept</code> is equivalent to
<code>std::is_nothrow_constructible_v&lt;Tj, U&gt;</code>. This function does not participate in
overload resolution unless</p>
<div class="ulist">
<ul>
<li>
<p><code>sizeof&#8230;&#8203;(T)</code> is nonzero,</p>
</li>
<li>
<p><code>std::is_same_v&lt;std::remove_cvref_t&lt;U&gt;, variant&gt;</code> is <code>false</code>,</p>
</li>
<li>
<p><code>std::remove_cvref_t&lt;U&gt;</code> is neither a specialization of <code>in_place_type_t</code> nor a
specialization of <code>in_place_index_t</code>,</p>
</li>
<li>
<p><code>std::is_constructible_v&lt;Tj, U&gt;</code> is <code>true</code>, and</p>
</li>
<li>
<p>the expression <code>FUN(std::forward&lt;U&gt;(u))</code> is well-formed.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">explicit</span> <span class="nf">variant</span><span class="p">(</span> <span class="n">in_place_type_t</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes the contained value of type <code>U</code> with the arguments
<code>std::forward&lt;A&gt;(a)&#8230;&#8203;</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>holds_alternative&lt;U&gt;(*this)</code>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>Any exception thrown by the initialization of the contained value.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
there is exactly one occurrence of <code>U</code> in <code>T&#8230;&#8203;</code> and
<code>std::is_constructible_v&lt;U, A&#8230;&#8203;&gt;</code> is true.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">explicit</span> <span class="nf">variant</span><span class="p">(</span> <span class="n">in_place_type_t</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">initializer_list</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span> <span class="n">il</span><span class="p">,</span>
    <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes the contained value of type <code>U</code> with the arguments <code>il</code>,
<code>std::forward&lt;A&gt;(a)&#8230;&#8203;</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>holds_alternative&lt;U&gt;(*this)</code>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>Any exception thrown by the initialization of the contained value.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
there is exactly one occurrence of <code>U</code> in <code>T&#8230;&#8203;</code> and
<code>std::is_constructible_v&lt;U, initializer_list&lt;V&gt;&amp;, A&#8230;&#8203;&gt;</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">explicit</span> <span class="nf">variant</span><span class="p">(</span> <span class="n">in_place_index_t</span><span class="o">&lt;</span><span class="n">I</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes the contained value of type <code>TI</code> with the arguments
<code>std::forward&lt;A&gt;(a)&#8230;&#8203;</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>index() == I</code>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>Any exception thrown by the initialization of the contained value.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
<code>I &lt; sizeof&#8230;&#8203;(T)</code> and <code>std::is_constructible_v&lt;TI, A&#8230;&#8203;&gt;</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">explicit</span> <span class="nf">variant</span><span class="p">(</span> <span class="n">in_place_index_t</span><span class="o">&lt;</span><span class="n">I</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">initializer_list</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span> <span class="n">il</span><span class="p">,</span>
    <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes the contained value of type <code>TI</code> with the arguments
<code>il</code>, <code>std::forward&lt;A&gt;(a)&#8230;&#8203;</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>index() == I</code>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>Any exception thrown by the initialization of the contained value.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
<code>I &lt; sizeof&#8230;&#8203;(T)</code> and
<code>std::is_constructible_v&lt;TI, initializer_list&lt;V&gt;&amp;, A&#8230;&#8203;&gt;</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_destructor">Destructor</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="o">~</span><span class="n">variant</span><span class="p">();</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Destroys the currently contained value.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_assignment">Assignment</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">variant</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&amp;</span> <span class="n">r</span> <span class="p">)</span>
  <span class="k">noexcept</span><span class="p">(</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_nothrow_copy_constructible</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;::</span><span class="n">value</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Let <code>j</code> be <code>r.index()</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>emplace&lt;j&gt;(get&lt;j&gt;(r))</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>*this</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>index() == r.index()</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This operator does not participate in overload resolution unless
<code>std::is_copy_constructible_v&lt;Ti&gt; &amp;&amp; std::is_copy_assignable_v&lt;Ti&gt;</code> is
<code>true</code> for all <code>i</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">variant</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">variant</span><span class="o">&amp;&amp;</span> <span class="n">r</span> <span class="p">)</span>
  <span class="k">noexcept</span><span class="p">(</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_nothrow_move_constructible</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;::</span><span class="n">value</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Let <code>j</code> be <code>r.index()</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>emplace&lt;j&gt;(get&lt;j&gt;(std::move(r)))</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>*this</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>index() == r.index()</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This operator does not participate in overload resolution unless
<code>std::is_move_constructible_v&lt;Ti&gt; &amp;&amp; std::is_move_assignable_v&lt;Ti&gt;</code> is
<code>true</code> for all <code>i</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">U</span><span class="o">&amp;&amp;</span> <span class="n">u</span> <span class="p">)</span>
  <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Let <code>Tj</code> be a type that is determined as follows: build an imaginary function
<code>FUN(Ti)</code> for each alternative type <code>Ti</code>. The overload <code>FUN(Tj)</code> selected by
overload resolution for the expression <code>FUN(std::forward&lt;U&gt;(u))</code> defines the
alternative <code>Tj</code> which is the type of the contained value after construction.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>emplace&lt;j&gt;(std::forward&lt;U&gt;(u))</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>*this</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>index() == j</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>The expression inside <code>noexcept</code> is <code>std::is_nothrow_constructible_v&lt;Tj, U&amp;&amp;&gt;</code>.
This operator does not participate in overload resolution unless</p>
<div class="ulist">
<ul>
<li>
<p><code>std::is_same_v&lt;std::remove_cvref_t&lt;T&gt;, variant&gt;</code> is <code>false</code>,</p>
</li>
<li>
<p><code>std::is_constructible_v&lt;Tj, U&amp;&amp;&gt; &amp;&amp; std::is_assignable_v&lt;Tj&amp;, U&amp;&amp;&gt;</code> is
<code>true</code>,  and</p>
</li>
<li>
<p>the expression <code>FUN(std::forward&lt;U&gt;(u))</code> (with <code>FUN</code> being the
above-mentioned set of imaginary functions) is well-formed.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_modifiers">Modifiers</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">U</span><span class="o">&amp;</span> <span class="n">emplace</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Let <code>I</code> be the zero-based index of <code>U</code> in <code>T&#8230;&#8203;</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Equivalent to: <code>return emplace&lt;I&gt;(std::forward&lt;A&gt;(a)&#8230;&#8203;);</code></p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function shall not participate in overload resolution unless
<code>std::is_constructible_v&lt;U, A&amp;&amp;&#8230;&#8203;&gt;</code> is <code>true</code> and <code>U</code> occurs exactly once
in <code>T&#8230;&#8203;</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">U</span><span class="o">&amp;</span> <span class="n">emplace</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">initializer_list</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span> <span class="n">il</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Let <code>I</code> be the zero-based index of <code>U</code> in <code>T&#8230;&#8203;</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Equivalent to: <code>return emplace&lt;I&gt;(il, std::forward&lt;A&gt;(a)&#8230;&#8203;);</code></p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function shall not participate in overload resolution unless
<code>std::is_constructible_v&lt;U, std::initializer_list&lt;V&gt;&amp;, A&amp;&amp;&#8230;&#8203;&gt;</code> is <code>true</code>
and <code>U</code> occurs exactly once in <code>T&#8230;&#8203;</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">emplace</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>I &lt; sizeof&#8230;&#8203;(T)</code>.</p>
</dd>
<dt class="hdlist1">Effects:  </dt>
<dd>
<p>Initializes a new contained value as if using the expression
<code>Ti(std::forward&lt;A&gt;(a)&#8230;&#8203;)</code>, then destroys the currently contained value.</p>
</dd>
<dt class="hdlist1">Ensures:  </dt>
<dd>
<p><code>index() == I</code>.</p>
</dd>
<dt class="hdlist1">Returns:  </dt>
<dd>
<p>A reference to the new contained value.</p>
</dd>
<dt class="hdlist1">Throws:   </dt>
<dd>
<p>Nothing unless the initialization of the new contained value throws.</p>
</dd>
<dt class="hdlist1">Exception Safety: </dt>
<dd>
<p>Strong. On exception, the contained value is unchanged.</p>
</dd>
<dt class="hdlist1">Remarks:  </dt>
<dd>
<p>This function shall not participate in overload resolution unless
<code>std::is_constructible_v&lt;Ti, A&amp;&amp;&#8230;&#8203;&gt;</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">emplace</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">initializer_list</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span> <span class="n">il</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>I &lt; sizeof&#8230;&#8203;(T)</code>.</p>
</dd>
<dt class="hdlist1">Effects:  </dt>
<dd>
<p>Initializes a new contained value as if using the expression
<code>Ti(il, std::forward&lt;A&gt;(a)&#8230;&#8203;)</code>, then destroys the currently contained value.</p>
</dd>
<dt class="hdlist1">Ensures:  </dt>
<dd>
<p><code>index() == I</code>.</p>
</dd>
<dt class="hdlist1">Returns:  </dt>
<dd>
<p>A reference to the new contained value.</p>
</dd>
<dt class="hdlist1">Throws:   </dt>
<dd>
<p>Nothing unless the initialization of the new contained value throws.</p>
</dd>
<dt class="hdlist1">Exception Safety: </dt>
<dd>
<p>Strong. On exception, the contained value is unchanged.</p>
</dd>
<dt class="hdlist1">Remarks:  </dt>
<dd>
<p>This function shall not participate in overload resolution unless
<code>std::is_constructible_v&lt;Ti, std::initializer_list&lt;V&gt;&amp;, A&amp;&amp;&#8230;&#8203;&gt;</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_value_status">Value Status</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">valueless_by_exception</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns:  </dt>
<dd>
<p><code>false</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This function is provided purely for compatibility with <code>std::variant</code>.
</td>
</tr>
</table>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">size_t</span> <span class="n">index</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns:  </dt>
<dd>
<p>The zero-based index of the active alternative.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_swap">Swap</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">swap</span><span class="p">(</span> <span class="n">variant</span><span class="o">&amp;</span> <span class="n">r</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_nothrow_move_constructible</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">...,</span>
  <span class="n">is_nothrow_swappable</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;::</span><span class="n">value</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects:  </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If <code>index() == r.index()</code>, calls <code>swap(get&lt;I&gt;(*this), get&lt;I&gt;(r))</code>,
where <code>I</code> is <code>index()</code>.</p>
</li>
<li>
<p>Otherwise, as if
<code>variant tmp(std::move(*this)); *this = std::move(r); r = std::move(tmp);</code></p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_converting_constructors_extension">Converting Constructors (extension)</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="n">variant</span><span class="p">(</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">r</span> <span class="p">)</span>
  <span class="k">noexcept</span><span class="p">(</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_nothrow_copy_constructible</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;::</span><span class="n">value</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes the contained value from the contained value of <code>r</code>.</p>
</dd>
<dt class="hdlist1">Throws:  </dt>
<dd>
<p>Any exception thrown by the initialization of the contained value.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
all types in <code>U&#8230;&#8203;</code> are in <code>T&#8230;&#8203;</code> and
<code>std::is_copy_constructible_v&lt;Ui&gt;::value</code> is <code>true</code> for all <code>Ui</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="n">variant</span><span class="p">(</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">r</span> <span class="p">)</span>
  <span class="k">noexcept</span><span class="p">(</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_nothrow_move_constructible</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;::</span><span class="n">value</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes the contained value from the contained value of
<code>std::move(r)</code>.</p>
</dd>
<dt class="hdlist1">Throws:  </dt>
<dd>
<p>Any exception thrown by the initialization of the contained value.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
all types in <code>U&#8230;&#8203;</code> are in <code>T&#8230;&#8203;</code> and
<code>std::is_move_constructible_v&lt;Ui&gt;::value</code> is <code>true</code> for all <code>Ui</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_subset_extension">Subset (extension)</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="n">subset</span><span class="p">()</span> <span class="o">&amp;</span> <span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="n">subset</span><span class="p">()</span> <span class="k">const</span><span class="o">&amp;</span> <span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A <code>variant&lt;U&#8230;&#8203;&gt;</code> whose contained value is copy-initialized from
the contained value of <code>*this</code> and has the same type.</p>
</dd>
<dt class="hdlist1">Throws:  </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If the active alternative of <code>*this</code> is not among the types in <code>U&#8230;&#8203;</code>,
<code>bad_variant_access</code>.</p>
</li>
<li>
<p>Otherwise, any exception thrown by the initialization of the contained value.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
all types in <code>U&#8230;&#8203;</code> are in <code>T&#8230;&#8203;</code> and
<code>std::is_copy_constructible_v&lt;Ui&gt;::value</code> is <code>true</code> for all <code>Ui</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="n">subset</span><span class="p">()</span> <span class="o">&amp;&amp;</span> <span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">constexpr</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">U</span><span class="p">...</span><span class="o">&gt;</span> <span class="n">subset</span><span class="p">()</span> <span class="k">const</span><span class="o">&amp;&amp;</span> <span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A <code>variant&lt;U&#8230;&#8203;&gt;</code> whose contained value is move-initialized from
the contained value of <code>*this</code> and has the same type.</p>
</dd>
<dt class="hdlist1">Throws:  </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If the active alternative of <code>*this</code> is not among the types in <code>U&#8230;&#8203;</code>,
<code>bad_variant_access</code>.</p>
</li>
<li>
<p>Otherwise, any exception thrown by the initialization of the contained value.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This function does not participate in overload resolution unless
all types in <code>U&#8230;&#8203;</code> are in <code>T&#8230;&#8203;</code> and
<code>std::is_move_constructible_v&lt;Ui&gt;::value</code> is <code>true</code> for all <code>Ui</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect3">
<h4 id="ref_variant_alternative">variant_alternative</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span> <span class="k">const</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span> <span class="k">volatile</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span> <span class="k">const</span> <span class="k">volatile</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span><span class="o">&amp;&gt;</span><span class="p">;</span> <span class="c1">// extension</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">T</span><span class="o">&amp;&amp;&gt;</span><span class="p">;</span> <span class="c1">// extension</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>If <code>typename variant_alternative&lt;I, T&gt;::type</code> exists and is <code>U</code>,</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>variant_alternative&lt;I, T const&gt;::type</code> is <code>U const</code>;</p>
</li>
<li>
<p><code>variant_alternative&lt;I, T volatile&gt;::type</code> is <code>U volatile</code>;</p>
</li>
<li>
<p><code>variant_alternative&lt;I, T const volatile&gt;::type</code> is <code>U const volatile</code>.</p>
</li>
<li>
<p><code>variant_alternative&lt;I, T&amp;&gt;::type</code> is <code>U&amp;</code>.</p>
</li>
<li>
<p><code>variant_alternative&lt;I, T&amp;&amp;&gt;::type</code> is <code>U&amp;&amp;</code>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Otherwise, these structs have no member <code>type</code>.</p>
</div>
</div>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">variant_alternative</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>When <code>I &lt; sizeof&#8230;&#8203;(T)</code>, the nested type <code>type</code> is an alias for the <code>I</code>-th
(zero-based) type in <code>T&#8230;&#8203;</code>. Otherwise, there is no member <code>type</code>.</p>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_holds_alternative">holds_alternative</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">holds_alternative</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p>The type <code>U</code> occurs exactly once in <code>T&#8230;&#8203;</code>. Otherwise, the
program is ill-formed.</p>
</dd>
<dt class="hdlist1">Returns:  </dt>
<dd>
<p><code>true</code> if <code>index()</code> is equal to the zero-based index of <code>U</code>
in <code>T&#8230;&#8203;</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_get">get</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;&amp;</span>
    <span class="n">get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;&amp;</span>
    <span class="n">get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>If <code>v.index()</code> is <code>I</code>, returns a reference to the object stored in
the variant. Otherwise, throws <code>bad_variant_access</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>These functions do not participate in overload resolution
unless <code>I</code> &lt; <code>sizeof&#8230;&#8203;(T)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">U</span><span class="o">&amp;</span> <span class="n">get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">U</span><span class="o">&amp;&amp;</span> <span class="n">get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">U</span><span class="o">&amp;</span> <span class="n">get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">U</span><span class="o">&amp;&amp;</span> <span class="n">get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p>The type <code>U</code> occurs exactly once in <code>T&#8230;&#8203;</code>. Otherwise, the
program is ill-formed.</p>
</dd>
<dt class="hdlist1">Effects:  </dt>
<dd>
<p>If <code>v</code> holds a value of type <code>U</code>, returns a reference to that value.
Otherwise, throws <code>bad_variant_access</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_get_if">get_if</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">add_pointer_t</span><span class="o">&lt;</span><span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&gt;</span>
    <span class="n">get_if</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;*</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">add_pointer_t</span><span class="o">&lt;</span><span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&gt;</span>
    <span class="n">get_if</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;*</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects:  </dt>
<dd>
<p>A pointer to the value stored in the variant, if
<code>v != nullptr &amp;&amp; v-&gt;index() == I</code>. Otherwise, <code>nullptr</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>These functions do not participate in overload resolution
unless <code>I</code> &lt; <code>sizeof&#8230;&#8203;(T)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">add_pointer_t</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;</span>
    <span class="n">get_if</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;*</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">U</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">add_pointer_t</span><span class="o">&lt;</span><span class="k">const</span> <span class="n">U</span><span class="o">&gt;</span>
    <span class="n">get_if</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;*</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p>The type <code>U</code> occurs exactly once in <code>T&#8230;&#8203;</code>. Otherwise, the
program is ill-formed.</p>
</dd>
<dt class="hdlist1">Effects:  </dt>
<dd>
<p>Equivalent to: <code>return get_if&lt;I&gt;(v);</code> with <code>I</code> being
the zero-based index of <code>U</code> in <code>T&#8230;&#8203;</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_unsafe_get_extension">unsafe_get (extension)</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">unsafe_get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;&amp;</span>
    <span class="n">unsafe_get</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;</span>
    <span class="n">unsafe_get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">size_t</span> <span class="n">I</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="k">const</span> <span class="n">variant_alternative_t</span><span class="o">&lt;</span><span class="n">I</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;&amp;&amp;</span>
    <span class="n">unsafe_get</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;&amp;</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>v.index() == I</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>a reference to the object stored in the variant.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_relational_operators">Relational Operators</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>v.index() == w.index() &amp;&amp; get&lt;I&gt;(v) == get&lt;I&gt;(w)</code>, where <code>I</code>
is <code>v.index()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!(v == w)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>v.index() &lt; w.index() || (v.index() == w.index() &amp;&amp; get&lt;I&gt;(v) &lt; get&lt;I&gt;(w))</code>,
where <code>I</code> is <code>v.index()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&gt;</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>w &lt; v</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;=</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>v.index() &lt; w.index() || (v.index() == w.index() &amp;&amp; get&lt;I&gt;(v) &lt;= get&lt;I&gt;(w))</code>,
where <code>I</code> is <code>v.index()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&gt;=</span><span class="p">(</span><span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="k">const</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>w &lt;= v</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_swap_2">swap</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="nf">swap</span><span class="p">(</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&amp;</span> <span class="n">w</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">(</span> <span class="cm">/*see below*/</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Equivalent to <code>v.swap(w)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_visit">visit</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span> <span class="o">=</span> <span class="cm">/*unspecified*/</span><span class="p">,</span> <span class="k">class</span> <span class="nc">F</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">V</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="cm">/*see below*/</span> <span class="n">visit</span><span class="p">(</span><span class="n">F</span><span class="o">&amp;&amp;</span> <span class="n">f</span><span class="p">,</span> <span class="n">V</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>std::forward&lt;F&gt;(f)(get&lt;I&gt;(std::forward&lt;V&gt;(v))&#8230;&#8203;)</code>, where
<code>I&#8230;&#8203;</code> is <code>v.index()&#8230;&#8203;</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>If <code>R</code> is given explicitly, as in <code>visit&lt;int&gt;</code>, the return
type is <code>R</code>. Otherwise, it&#8217;s deduced from <code>F</code>. All possible applications
of <code>F</code> to the variant alternatives must have the same return type for
this deduction to succeed.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_visit_by_index_extension">visit_by_index (extension)</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span> <span class="o">=</span> <span class="cm">/*unspecified*/</span><span class="p">,</span> <span class="k">class</span> <span class="nc">V</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">F</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="cm">/*see below*/</span> <span class="n">visit_by_index</span><span class="p">(</span><span class="n">V</span><span class="o">&amp;&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="n">F</span><span class="o">&amp;&amp;</span><span class="p">..</span> <span class="n">f</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>variant_size&lt;V&gt;::value == sizeof&#8230;&#8203;(F)</code>, or the program is ill-formed.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>std::forward&lt;Fi&gt;(fi)(get&lt;i&gt;(std::forward&lt;V&gt;(v)))</code>, where
<code>i</code> is <code>v.index()</code> and <code>Fi</code> and <code>fi</code> are the <code>i</code>-th element of <code>F&#8230;&#8203;</code> and <code>f&#8230;&#8203;</code>
accordingly.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>If <code>R</code> is given explicitly, as in <code>visit_by_index&lt;int&gt;</code>, the return
type is <code>R</code>. Otherwise, it&#8217;s deduced from <code>F&#8230;&#8203;</code> and <code>V</code>. All the applications
of <code>Fi</code> to the corresponding variant alternatives must have the same return type
for this deduction to succeed.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_stream_insertion_extension">Stream Insertion (extension)</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">Ch</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tr</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>sizeof&#8230;&#8203;(T) != 0</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>os &lt;&lt; get&lt;I&gt;(v)</code>, where <code>I</code> is <code>v.index()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">Ch</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tr</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="n">monostate</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>os &lt;&lt; "monostate"</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>os</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_bad_variant_access">bad_variant_access</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">class</span> <span class="nc">bad_variant_access</span><span class="o">:</span> <span class="k">public</span> <span class="n">std</span><span class="o">::</span><span class="n">exception</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="n">bad_variant_access</span><span class="p">()</span> <span class="k">noexcept</span> <span class="o">=</span> <span class="k">default</span><span class="p">;</span>

    <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">what</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span>
    <span class="p">{</span>
        <span class="k">return</span> <span class="s">"bad_variant_access"</span><span class="p">;</span>
    <span class="p">}</span>
<span class="p">};</span></code></pre>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostvariant2_hpp">&lt;boost/variant2.hpp&gt;</h3>
<div class="paragraph">
<p>This convenience header includes <code>&lt;boost/variant2/variant.hpp&gt;</code>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="copyright">Copyright and License</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This documentation is copyright 2018, 2019 Peter Dimov and is distributed under
the <a href="http://www.boost.org/LICENSE_1_0.txt">Boost Software License, Version 1.0</a>.</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2023-04-10 13:47:40 UTC
</div>
</div>
<style>

*:not(pre)>code { background: none; color: #600000; }
:not(pre):not([class^=L])>code { background: none; color: #600000; }

</style>
</body>
</html>