<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.16">
<meta name="author" content="<PERSON><PERSON>">
<title>Boost.Endian: The Boost Endian Library</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700">
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;-webkit-tap-highlight-color:transparent}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos{border-right:1px solid;opacity:.35;padding-right:.5em}
pre.pygments .lineno{border-right:1px solid;opacity:.35;display:inline-block;margin-right:.75em}
pre.pygments .lineno::before{content:"";margin-right:-.125em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all tr,table.stripes-odd tr:nth-of-type(odd),table.stripes-even tr:nth-of-type(even),table.stripes-hover tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
ol>li p,ul>li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
.gist .file-data>table{border:0;background:#fff;width:100%;margin-bottom:0}
.gist .file-data>table td.line-data{width:99%}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
<style>
pre.rouge table td { padding: 5px; }
pre.rouge table pre { margin: 0; }
pre.rouge .cm {
  color: #999988;
  font-style: italic;
}
pre.rouge .cp {
  color: #999999;
  font-weight: bold;
}
pre.rouge .c1 {
  color: #999988;
  font-style: italic;
}
pre.rouge .cs {
  color: #999999;
  font-weight: bold;
  font-style: italic;
}
pre.rouge .c, pre.rouge .ch, pre.rouge .cd, pre.rouge .cpf {
  color: #999988;
  font-style: italic;
}
pre.rouge .err {
  color: #a61717;
  background-color: #e3d2d2;
}
pre.rouge .gd {
  color: #000000;
  background-color: #ffdddd;
}
pre.rouge .ge {
  color: #000000;
  font-style: italic;
}
pre.rouge .gr {
  color: #aa0000;
}
pre.rouge .gh {
  color: #999999;
}
pre.rouge .gi {
  color: #000000;
  background-color: #ddffdd;
}
pre.rouge .go {
  color: #888888;
}
pre.rouge .gp {
  color: #555555;
}
pre.rouge .gs {
  font-weight: bold;
}
pre.rouge .gu {
  color: #aaaaaa;
}
pre.rouge .gt {
  color: #aa0000;
}
pre.rouge .kc {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kd {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kn {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kp {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kr {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kt {
  color: #445588;
  font-weight: bold;
}
pre.rouge .k, pre.rouge .kv {
  color: #000000;
  font-weight: bold;
}
pre.rouge .mf {
  color: #009999;
}
pre.rouge .mh {
  color: #009999;
}
pre.rouge .il {
  color: #009999;
}
pre.rouge .mi {
  color: #009999;
}
pre.rouge .mo {
  color: #009999;
}
pre.rouge .m, pre.rouge .mb, pre.rouge .mx {
  color: #009999;
}
pre.rouge .sa {
  color: #000000;
  font-weight: bold;
}
pre.rouge .sb {
  color: #d14;
}
pre.rouge .sc {
  color: #d14;
}
pre.rouge .sd {
  color: #d14;
}
pre.rouge .s2 {
  color: #d14;
}
pre.rouge .se {
  color: #d14;
}
pre.rouge .sh {
  color: #d14;
}
pre.rouge .si {
  color: #d14;
}
pre.rouge .sx {
  color: #d14;
}
pre.rouge .sr {
  color: #009926;
}
pre.rouge .s1 {
  color: #d14;
}
pre.rouge .ss {
  color: #990073;
}
pre.rouge .s, pre.rouge .dl {
  color: #d14;
}
pre.rouge .na {
  color: #008080;
}
pre.rouge .bp {
  color: #999999;
}
pre.rouge .nb {
  color: #0086B3;
}
pre.rouge .nc {
  color: #445588;
  font-weight: bold;
}
pre.rouge .no {
  color: #008080;
}
pre.rouge .nd {
  color: #3c5d5d;
  font-weight: bold;
}
pre.rouge .ni {
  color: #800080;
}
pre.rouge .ne {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nf, pre.rouge .fm {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nl {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nn {
  color: #555555;
}
pre.rouge .nt {
  color: #000080;
}
pre.rouge .vc {
  color: #008080;
}
pre.rouge .vg {
  color: #008080;
}
pre.rouge .vi {
  color: #008080;
}
pre.rouge .nv, pre.rouge .vm {
  color: #008080;
}
pre.rouge .ow {
  color: #000000;
  font-weight: bold;
}
pre.rouge .o {
  color: #000000;
  font-weight: bold;
}
pre.rouge .w {
  color: #bbbbbb;
}
pre.rouge {
  background-color: #f8f8f8;
}
</style>
</head>
<body class="article toc2 toc-left">
<div id="header">
<h1>Boost.Endian: The Boost Endian Library</h1>
<div class="details">
<span id="author" class="author">Beman Dawes</span><br>
</div>
<div id="toc" class="toc2">
<div id="toctitle">Table of Contents</div>
<ul class="sectlevel1">
<li><a href="#overview">Overview</a>
<ul class="sectlevel2">
<li><a href="#overview_abstract">Abstract</a></li>
<li><a href="#overview_endianness">Introduction to endianness</a></li>
<li><a href="#overview_introduction">Introduction to the Boost.Endian library</a></li>
<li><a href="#overview_intrinsics">Built-in support for Intrinsics</a></li>
<li><a href="#overview_performance">Performance</a></li>
<li><a href="#overview_cpp03_support">C&#43;&#43;03 support for C&#43;&#43;11 features</a></li>
<li><a href="#overview_faq">Overall FAQ</a></li>
</ul>
</li>
<li><a href="#changelog">Revision History</a>
<ul class="sectlevel2">
<li><a href="#overview_changes_in_1_75_0">Changes in 1.75.0</a></li>
<li><a href="#overview_changes_in_1_74_0">Changes in 1.74.0</a></li>
<li><a href="#overview_changes_in_1_72_0">Changes in 1.72.0</a></li>
<li><a href="#overview_changes_in_1_71_0">Changes in 1.71.0</a></li>
</ul>
</li>
<li><a href="#choosing">Choosing between Conversion Functions, Buffer Types, and Arithmetic Types</a>
<ul class="sectlevel2">
<li><a href="#choosing_background">Background</a></li>
<li><a href="#choosing_characteristics">Characteristics</a></li>
<li><a href="#choosing_design_patterns">Design patterns</a></li>
<li><a href="#choosing_use_case_examples">Use case examples</a></li>
</ul>
</li>
<li><a href="#conversion">Endian Conversion Functions</a>
<ul class="sectlevel2">
<li><a href="#conversion_introduction">Introduction</a></li>
<li><a href="#conversion_reference">Reference</a></li>
<li><a href="#conversion_faq">FAQ</a></li>
<li><a href="#conversion_acknowledgements">Acknowledgements</a></li>
</ul>
</li>
<li><a href="#buffers">Endian Buffer Types</a>
<ul class="sectlevel2">
<li><a href="#buffers_introduction">Introduction</a></li>
<li><a href="#buffers_example">Example</a></li>
<li><a href="#buffers_limitations">Limitations</a></li>
<li><a href="#buffers_feature_set">Feature set</a></li>
<li><a href="#buffers_enums_and_typedefs">Enums and typedefs</a></li>
<li><a href="#buffers_class_template_endian_buffer">Class template <code>endian_buffer</code></a></li>
<li><a href="#buffers_faq">FAQ</a></li>
<li><a href="#buffers_design_considerations_for_boost_endian_buffers">Design considerations for Boost.Endian buffers</a></li>
<li><a href="#buffers_c11">C&#43;&#43;11</a></li>
<li><a href="#buffers_compilation">Compilation</a></li>
</ul>
</li>
<li><a href="#arithmetic">Endian Arithmetic Types</a>
<ul class="sectlevel2">
<li><a href="#arithmetic_introduction">Introduction</a></li>
<li><a href="#arithmetic_example">Example</a></li>
<li><a href="#arithmetic_limitations">Limitations</a></li>
<li><a href="#arithmetic_feature_set">Feature set</a></li>
<li><a href="#arithmetic_enums_and_typedefs">Enums and typedefs</a></li>
<li><a href="#arithmetic_class_template_endian_arithmetic">Class template <code>endian_arithmetic</code></a></li>
<li><a href="#arithmetic_faq">FAQ</a></li>
<li><a href="#arithmetic_design_considerations_for_boost_endian_types">Design considerations for Boost.Endian types</a></li>
<li><a href="#arithmetic_experience">Experience</a></li>
<li><a href="#arithmetic_motivating_use_cases">Motivating use cases</a></li>
<li><a href="#arithmetic_c11">C&#43;&#43;11</a></li>
<li><a href="#arithmetic_compilation">Compilation</a></li>
<li><a href="#arithmetic_acknowledgements">Acknowledgements</a></li>
</ul>
</li>
<li><a href="#appendix_history">Appendix A: History and Acknowledgments</a>
<ul class="sectlevel2">
<li><a href="#apph_history">History</a></li>
<li><a href="#apph_compatibility_with_interim_releases">Compatibility with interim releases</a></li>
<li><a href="#apph_future_directions">Future directions</a></li>
<li><a href="#apph_acknowledgements">Acknowledgements</a></li>
</ul>
</li>
<li><a href="#apph_copyright_and_license">Appendix B: Copyright and License</a></li>
</ul>
</div>
</div>
<div id="content">
<div class="sect1">
<h2 id="overview">Overview</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="overview_abstract">Abstract</h3>
<div class="paragraph">
<p>Boost.Endian provides facilities to manipulate the
<a href="#overview_endianness">endianness</a> of integers and user-defined types.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Three approaches to endianness are supported. Each has a long history of
successful use, and each approach has use cases where it is preferred over the
other approaches.</p>
</li>
<li>
<p>Primary uses:</p>
<div class="ulist">
<ul>
<li>
<p>Data portability. The Endian library supports binary data exchange, via
either external media or network transmission, regardless of platform
endianness.</p>
</li>
<li>
<p>Program portability. POSIX-based and Windows-based operating systems
traditionally supply libraries with non-portable functions to perform endian
conversion. There are at least four incompatible sets of functions in common
use. The Endian library is portable across all C&#43;&#43; platforms.</p>
</li>
</ul>
</div>
</li>
<li>
<p>Secondary use: Minimizing data size via sizes and/or alignments not supported
by the standard C&#43;&#43; integer types.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="overview_endianness">Introduction to endianness</h3>
<div class="paragraph">
<p>Consider the following code:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">int16_t</span> <span class="n">i</span> <span class="o">=</span> <span class="mh">0x0102</span><span class="p">;</span>
<span class="kt">FILE</span> <span class="o">*</span> <span class="n">file</span> <span class="o">=</span> <span class="n">fopen</span><span class="p">(</span><span class="s">"test.bin"</span><span class="p">,</span> <span class="s">"wb"</span><span class="p">);</span> <span class="c1">// binary file!</span>
<span class="n">fwrite</span><span class="p">(</span><span class="o">&amp;</span><span class="n">i</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">(</span><span class="kt">int16_t</span><span class="p">),</span> <span class="mi">1</span><span class="p">,</span> <span class="n">file</span><span class="p">);</span>
<span class="n">fclose</span><span class="p">(</span><span class="n">file</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>On OS X, Linux, or Windows systems with an Intel CPU, a hex dump of the
"test.bin" output file produces:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="mo">0201</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>On OS X systems with a PowerPC CPU, or Solaris systems with a SPARC CPU, a hex
dump of the "test.bin" output file produces:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="mo">0102</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>What&#8217;s happening here is that Intel CPUs order the bytes of an integer with the
least-significant byte first, while SPARC CPUs place the most-significant byte
first. Some CPUs, such as the PowerPC, allow the operating system to choose
which ordering applies.</p>
</div>
<div class="paragraph">
<p>Most-significant-byte-first ordering is traditionally called "big endian"
ordering and least-significant-byte-first is traditionally called
"little-endian" ordering. The names are derived from
<a href="http://en.wikipedia.org/wiki/Jonathan_Swift">Jonathan Swift</a>'s satirical novel
<em><a href="http://en.wikipedia.org/wiki/Gulliver&#8217;s_Travels">Gulliver&#8217;s Travels</a></em>, where
rival kingdoms opened their soft-boiled eggs at different ends.</p>
</div>
<div class="paragraph">
<p>See Wikipedia&#8217;s <a href="http://en.wikipedia.org/wiki/Endianness">Endianness</a> article for
an extensive discussion of endianness.</p>
</div>
<div class="paragraph">
<p>Programmers can usually ignore endianness, except when reading a core  dump on
little-endian systems. But programmers  have to deal with endianness when
exchanging binary integers and binary floating point values between computer
systems with differing endianness, whether by physical file transfer or over a
network. And programmers may also want to use the library when minimizing either
internal or external data sizes is advantageous.</p>
</div>
</div>
<div class="sect2">
<h3 id="overview_introduction">Introduction to the Boost.Endian library</h3>
<div class="paragraph">
<p>Boost.Endian provides three different approaches to dealing with endianness. All
three approaches support integers and user-define types (UDTs).</p>
</div>
<div class="paragraph">
<p>Each approach has a long history of successful use, and each approach has use
cases where it is preferred to the other approaches. See
<a href="#choosing">Choosing between Conversion Functions, Buffer Types, and Arithmetic Types</a>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><a href="#conversion">Endian conversion functions</a></dt>
<dd>
<p>The application uses the built-in integer types to hold values, and calls the
provided conversion functions to convert byte ordering as needed. Both mutating
and non-mutating conversions are supplied, and each comes in unconditional and
conditional variants.</p>
</dd>
<dt class="hdlist1"><a href="#buffers">Endian buffer types</a></dt>
<dd>
<p>The application uses the provided endian buffer types to hold values, and
explicitly converts to and from the built-in integer types. Buffer sizes of 8,
16, 24, 32, 40, 48, 56, and 64 bits (i.e. 1, 2, 3, 4, 5, 6, 7, and 8 bytes) are
provided. Unaligned integer buffer types are provided for all sizes, and aligned
buffer types are provided for 16, 32, and 64-bit sizes. The provided specific
types are typedefs for a generic class template that may be used directly for
less common use cases.</p>
</dd>
<dt class="hdlist1"><a href="#arithmetic">Endian arithmetic types</a></dt>
<dd>
<p>The application uses the provided endian arithmetic types, which supply the same
operations as the built-in C&#43;&#43; arithmetic types. All conversions are implicit.
Arithmetic sizes of 8, 16, 24, 32, 40, 48, 56, and 64 bits (i.e. 1, 2, 3, 4, 5,
6, 7, and 8 bytes) are provided. Unaligned integer types are provided for all
sizes and aligned arithmetic types are provided for 16, 32, and 64-bit sizes.
The provided specific types are typedefs for a generic class template that may
be used directly in generic code of for less common use cases.</p>
</dd>
</dl>
</div>
<div class="paragraph">
<p>Boost Endian is a header-only library. C&#43;&#43;11 features affecting interfaces,
such as <code>noexcept</code>, are  used only if available. See
<a href="#overview_cpp03_support">C&#43;&#43;03 support for C&#43;&#43;11 features</a> for details.</p>
</div>
</div>
<div class="sect2">
<h3 id="overview_intrinsics">Built-in support for Intrinsics</h3>
<div class="paragraph">
<p>Most compilers, including GCC, Clang, and Visual C&#43;&#43;, supply  built-in support
for byte swapping intrinsics. The Endian library uses these intrinsics when
available since they may result in smaller and faster generated code,
particularly for optimized builds.</p>
</div>
<div class="paragraph">
<p>Defining the macro <code>BOOST_ENDIAN_NO_INTRINSICS</code> will suppress use of the
intrinsics. This is useful when a compiler has no intrinsic support or fails to
locate the appropriate header, perhaps because it is an older release or has
very limited supporting libraries.</p>
</div>
<div class="paragraph">
<p>The macro <code>BOOST_ENDIAN_INTRINSIC_MSG</code> is defined as either
<code>"no byte swap intrinsics"</code> or a string describing the particular set of
intrinsics being used. This is useful for eliminating missing intrinsics as a
source of performance issues.</p>
</div>
</div>
<div class="sect2">
<h3 id="overview_performance">Performance</h3>
<div class="paragraph">
<p>Consider this problem:</p>
</div>
<div class="sect3">
<h4 id="overview_example_1">Example 1</h4>
<div class="paragraph">
<p>Add 100 to a big endian value in a file, then write the result to a file</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Endian arithmetic type approach</th>
<th class="tableblock halign-left valign-top">Endian conversion function approach</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">big_int32_at</span> <span class="n">x</span><span class="p">;</span>

<span class="p">...</span> <span class="n">read</span> <span class="n">into</span> <span class="n">x</span> <span class="n">from</span> <span class="n">a</span> <span class="n">file</span> <span class="p">...</span>


<span class="n">x</span> <span class="o">+=</span> <span class="mi">100</span><span class="p">;</span>


<span class="p">...</span> <span class="n">write</span> <span class="n">x</span> <span class="n">to</span> <span class="n">a</span> <span class="n">file</span> <span class="p">...</span></code></pre>
</div>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">int32_t</span> <span class="n">x</span><span class="p">;</span>

<span class="p">...</span> <span class="n">read</span> <span class="n">into</span> <span class="n">x</span> <span class="n">from</span> <span class="n">a</span> <span class="n">file</span> <span class="p">...</span>

<span class="n">big_to_native_inplace</span><span class="p">(</span><span class="n">x</span><span class="p">);</span>
<span class="n">x</span> <span class="o">+=</span> <span class="mi">100</span><span class="p">;</span>
<span class="n">native_to_big_inplace</span><span class="p">(</span><span class="n">x</span><span class="p">);</span>

<span class="p">...</span> <span class="n">write</span> <span class="n">x</span> <span class="n">to</span> <span class="n">a</span> <span class="n">file</span> <span class="p">...</span></code></pre>
</div>
</div></div></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p><strong>There will be no performance difference between the two approaches in optimized
builds, regardless of the native endianness of the machine.</strong> That&#8217;s because
optimizing compilers will generate exactly the same code for each. That
conclusion was confirmed by studying the generated assembly code for GCC and
Visual C&#43;&#43;. Furthermore, time spent doing I/O will determine the speed of this
application.</p>
</div>
<div class="paragraph">
<p>Now consider a slightly different problem:</p>
</div>
</div>
<div class="sect3">
<h4 id="overview_example_2">Example 2</h4>
<div class="paragraph">
<p>Add a million values to a big endian value in a file, then write the result to a
file</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Endian arithmetic type approach</th>
<th class="tableblock halign-left valign-top">Endian conversion function approach</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">big_int32_at</span> <span class="n">x</span><span class="p">;</span>

<span class="p">...</span> <span class="n">read</span> <span class="n">into</span> <span class="n">x</span> <span class="n">from</span> <span class="n">a</span> <span class="n">file</span> <span class="p">...</span>



<span class="k">for</span> <span class="p">(</span><span class="kt">int32_t</span> <span class="n">i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="n">i</span> <span class="o">&lt;</span> <span class="mi">1000000</span><span class="p">;</span> <span class="o">++</span><span class="n">i</span><span class="p">)</span>
  <span class="n">x</span> <span class="o">+=</span> <span class="n">i</span><span class="p">;</span>



<span class="p">...</span> <span class="n">write</span> <span class="n">x</span> <span class="n">to</span> <span class="n">a</span> <span class="n">file</span> <span class="p">...</span></code></pre>
</div>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">int32_t</span> <span class="n">x</span><span class="p">;</span>

<span class="p">...</span> <span class="n">read</span> <span class="n">into</span> <span class="n">x</span> <span class="n">from</span> <span class="n">a</span> <span class="n">file</span> <span class="p">...</span>

<span class="n">big_to_native_inplace</span><span class="p">(</span><span class="n">x</span><span class="p">);</span>

<span class="k">for</span> <span class="p">(</span><span class="kt">int32_t</span> <span class="n">i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="n">i</span> <span class="o">&lt;</span> <span class="mi">1000000</span><span class="p">;</span> <span class="o">++</span><span class="n">i</span><span class="p">)</span>
  <span class="n">x</span> <span class="o">+=</span> <span class="n">i</span><span class="p">;</span>

<span class="n">native_to_big_inplace</span><span class="p">(</span><span class="n">x</span><span class="p">);</span>

<span class="p">...</span> <span class="n">write</span> <span class="n">x</span> <span class="n">to</span> <span class="n">a</span> <span class="n">file</span> <span class="p">...</span></code></pre>
</div>
</div></div></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>With the Endian arithmetic approach, on little endian platforms an implicit
conversion from and then back to big endian is done inside the loop. With the
Endian conversion function approach, the user has ensured the conversions are
done outside the loop, so the code may run more quickly on little endian
platforms.</p>
</div>
</div>
<div class="sect3">
<h4 id="overview_timings">Timings</h4>
<div class="paragraph">
<p>These tests were run against release builds on a circa 2012 4-core little endian
X64 Intel Core i5-3570K CPU @ 3.40GHz under Windows 7.</p>
</div>
<div class="admonitionblock caution">
<table>
<tr>
<td class="icon">
<div class="title">Caution</div>
</td>
<td class="content">
The Windows CPU timer has very high granularity. Repeated runs of the
same tests often yield considerably different results.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>See <code>test/loop_time_test.cpp</code> for the actual code and <code>benchmark/Jamfile.v2</code> for
the build setup.</p>
</div>
<div class="sect4">
<h5 id="overview_gnu_c_version_4_8_2_on_linux_virtual_machine">GNU C++ version 4.8.2 on Linux virtual machine</h5>
<div class="paragraph">
<p>Iterations: 10'000'000'000, Intrinsics: <code>__builtin_bswap16</code>, etc.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 33.3333%;">
<col style="width: 33.3333%;">
<col style="width: 33.3334%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Test Case</th>
<th class="tableblock halign-left valign-top">Endian arithmetic type</th>
<th class="tableblock halign-left valign-top">Endian conversion function</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">16-bit aligned big endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.46 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.28 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">16-bit aligned little endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.28 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.22 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">32-bit aligned big endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.40 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.11 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">32-bit aligned little endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.11 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.10 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">64-bit aligned big endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">14.02 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.10 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">64-bit aligned little endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.00 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.03 s</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect4">
<h5 id="overview_microsoft_visual_c_version_14_0">Microsoft Visual C++ version 14.0</h5>
<div class="paragraph">
<p>Iterations: 10'000'000'000, Intrinsics: <code>&lt;cstdlib&gt;</code> <code>_byteswap_ushort</code>, etc.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 33.3333%;">
<col style="width: 33.3333%;">
<col style="width: 33.3334%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Test Case</th>
<th class="tableblock halign-left valign-top">Endian arithmetic type</th>
<th class="tableblock halign-left valign-top">Endian conversion function</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">16-bit aligned big endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.27 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.26 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">16-bit aligned little endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.29 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.32 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">32-bit aligned big endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.36 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.24 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">32-bit aligned little endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.24 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.24 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">64-bit aligned big endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">13.65 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.34 s</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">64-bit aligned little endian</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.35 s</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.73 s</p></td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
<div class="sect2">
<h3 id="overview_cpp03_support">C&#43;&#43;03 support for C&#43;&#43;11 features</h3>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">C&#43;&#43;11 Feature</th>
<th class="tableblock halign-left valign-top">Action with C&#43;&#43;03 Compilers</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Scoped enums</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Uses header
<a href="http://www.boost.org/libs/core/doc/html/core/scoped_enum.html">boost/core/scoped_enum.hpp</a>
to emulate C&#43;&#43;11 scoped enums.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>noexcept</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Uses <code>BOOST_NOEXCEPT</code> macro, which is defined as null for compilers not
supporting this C&#43;&#43;11 feature.</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">C&#43;&#43;11 PODs
(<a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2342.htm">N2342</a>)</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Takes advantage of C&#43;&#43;03 compilers that relax C&#43;&#43;03 POD rules, but see
Limitations <a href="#buffers_limitations">here</a> and <a href="#arithmetic_limitations">here</a>.
Also see macros for explicit POD control <a href="#buffers_compilation">here</a> and
<a href="#arithmetic_compilation">here</a></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="overview_faq">Overall FAQ</h3>
<div class="dlist">
<dl>
<dt class="hdlist1">Is the implementation header only?</dt>
<dd>
<p>Yes.</p>
</dd>
<dt class="hdlist1">Are C&#43;&#43;03 compilers supported?</dt>
<dd>
<p>Yes.</p>
</dd>
<dt class="hdlist1">Does the implementation use compiler intrinsic built-in byte swapping?</dt>
<dd>
<p>Yes, if available. See <a href="#overview_intrinsics">Intrinsic built-in support</a>.</p>
</dd>
<dt class="hdlist1">Why bother with endianness?</dt>
<dd>
<p>Binary data portability is the primary use case.</p>
</dd>
<dt class="hdlist1">Does endianness have any uses outside of portable binary file or network I/O formats?</dt>
<dd>
<p>Using the unaligned integer types with a size tailored to the application&#8217;s
needs is a minor secondary use that saves internal or external memory space. For
example, using <code>big_int40_buf_t</code> or <code>big_int40_t</code> in a large array saves a lot
of space compared to one of the 64-bit types.</p>
</dd>
<dt class="hdlist1">Why bother with binary I/O? Why not just use C&#43;&#43; Standard Library stream inserters and extractors?</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Data interchange formats often specify binary integer data. Binary integer
data is smaller and therefore I/O is faster and file sizes are smaller. Transfer
between systems is less expensive.</p>
</li>
<li>
<p>Furthermore, binary integer data is of fixed size, and so fixed-size disk
records are possible without padding, easing sorting and allowing random access.</p>
</li>
<li>
<p>Disadvantages, such as the inability to use text utilities on the resulting
files, limit usefulness to applications where the binary I/O advantages are
paramount.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Which is better, big-endian or little-endian?</dt>
<dd>
<p>Big-endian tends to be preferred in a networking environment and is a bit more
of an industry standard, but little-endian may be preferred for applications
that run primarily on x86, x86-64, and other little-endian CPU&#8217;s. The
<a href="http://en.wikipedia.org/wiki/Endian">Wikipedia</a> article gives more pros and cons.</p>
</dd>
<dt class="hdlist1">Why are only big and little native endianness supported?</dt>
<dd>
<p>These are the only endian schemes that have any practical value today. PDP-11
and the other middle endian approaches are interesting  curiosities but have no
relevance for today&#8217;s C&#43;&#43; developers. The same is true for architectures that
allow runtime endianness switching. The
<a href="#conversion_native_order_specification">specification for native ordering</a> has
been carefully crafted to allow support for such orderings in the future, should
the need arise. Thanks to Howard Hinnant for suggesting this.</p>
</dd>
<dt class="hdlist1">Why do both the buffer and arithmetic types exist?</dt>
<dd>
<p>Conversions in the buffer types are explicit. Conversions in the arithmetic
types are implicit. This fundamental difference is a deliberate design feature
that would be lost if the inheritance hierarchy were collapsed.
The original design provided only arithmetic types. Buffer types were requested
during formal review by those wishing total control over when conversion occurs.
They also felt that buffer types would be less likely to be misused by
maintenance programmers not familiar with the implications of performing a lot
of integer operations on the endian arithmetic integer types.</p>
</dd>
<dt class="hdlist1">What is gained by using the buffer types rather than always just using the arithmetic types?</dt>
<dd>
<p>Assurance that hidden conversions are not performed. This is of overriding
importance to users concerned about achieving the ultimate in terms of speed.
"Always just using the arithmetic types" is fine for other users. When the
ultimate in speed needs to be ensured, the arithmetic types can be used in the
same design patterns or idioms that would be used for buffer types, resulting in
the same code being generated for either types.</p>
</dd>
<dt class="hdlist1">What are the limitations of integer support?</dt>
<dd>
<p>Tests have only been performed on machines that  use two&#8217;s complement
arithmetic. The Endian conversion functions only support 8, 16, 32, and 64-bit
aligned integers. The endian types only support 8, 16, 24, 32, 40, 48, 56, and
64-bit unaligned integers, and 8, 16, 32, and 64-bit aligned integers.</p>
</dd>
<dt class="hdlist1">Is there floating point support?</dt>
<dd>
<p>An attempt was made to support four-byte <code>float</code>s and eight-byte
<code>double</code>s, limited to
<a href="http://en.wikipedia.org/wiki/IEEE_floating_point">IEEE 754</a> (also known as
ISO/IEC/IEEE 60559) floating point and further limited to systems where floating
point endianness does not differ from integer endianness. Even with those
limitations, support for floating point types was not reliable and was removed.
For example, simply reversing the endianness of a floating point number can
result in a signaling-NAN.</p>
<div class="paragraph">
<p>Support for <code>float</code> and <code>double</code> has since been reinstated for <code>endian_buffer</code>,
<code>endian_arithmetic</code> and the conversion functions that reverse endianness in place.
The conversion functions that take and return by value still do not support floating
point due to the above issues; reversing the bytes of a floating point number
does not necessarily produce another valid floating point number.</p>
</div>
</dd>
</dl>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changelog">Revision History</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="overview_changes_in_1_75_0">Changes in 1.75.0</h3>
<div class="ulist">
<ul>
<li>
<p><code>endian_arithmetic</code> no longer inherits from <code>endian_buffer</code></p>
</li>
<li>
<p>When <code>BOOST_ENDIAN_NO_CTORS</code> is defined, the unaligned <code>endian_buffer</code> and
<code>endian_arithmetic</code> are C&#43;&#43;03 PODs, to enable use of <code>__attribute__((packed))</code></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="overview_changes_in_1_74_0">Changes in 1.74.0</h3>
<div class="ulist">
<ul>
<li>
<p>Enabled scoped enumeration types in <code>endian_reverse</code></p>
</li>
<li>
<p>Enabled <code>bool</code>, <code>enum</code>, <code>float</code>, <code>double</code> in <code>endian_reverse_inplace</code></p>
</li>
<li>
<p>Added an overload of <code>endian_reverse_inplace</code> for arrays</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="overview_changes_in_1_72_0">Changes in 1.72.0</h3>
<div class="ulist">
<ul>
<li>
<p>Made <code>endian_reverse</code>, <code>conditional_reverse</code> and <code>*_to_*</code> <code>constexpr</code>
on GCC and Clang</p>
</li>
<li>
<p>Added convenience load and store functions</p>
</li>
<li>
<p>Added floating point convenience typedefs</p>
</li>
<li>
<p>Added a non-const overload of <code>data()</code>; changed its return type to <code>unsigned char*</code></p>
</li>
<li>
<p>Added <code>__int128</code> support to <code>endian_reverse</code> when available</p>
</li>
<li>
<p>Added a convenience header <code>boost/endian.hpp</code></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="overview_changes_in_1_71_0">Changes in 1.71.0</h3>
<div class="ulist">
<ul>
<li>
<p>Clarified requirements on the value type template parameter</p>
</li>
<li>
<p>Added support for <code>float</code> and <code>double</code> to <code>endian_buffer</code> and <code>endian_arithmetic</code></p>
</li>
<li>
<p>Added <code>endian_load</code>, <code>endian_store</code></p>
</li>
<li>
<p>Updated <code>endian_reverse</code> to correctly support all non-<code>bool</code> integral types</p>
</li>
<li>
<p>Moved deprecated names to the deprecated header <code>endian.hpp</code></p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="choosing">Choosing between Conversion Functions, Buffer Types, and Arithmetic Types</h2>
<div class="sectionbody">
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Deciding which is the best endianness approach (conversion functions, buffer
types, or arithmetic types) for a particular application involves complex
engineering trade-offs. It is hard to assess those trade-offs without some
understanding of the different interfaces, so you might want to read the
<a href="#conversion">conversion functions</a>, <a href="#buffers">buffer types</a>, and
<a href="#arithmetic">arithmetic types</a> pages before proceeding.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>The best approach to endianness for a particular application depends on the
interaction between the application&#8217;s needs and the characteristics of each of
the three  approaches.</p>
</div>
<div class="paragraph">
<p><strong>Recommendation:</strong> If you are new to endianness, uncertain, or don&#8217;t want to
invest the time to study engineering trade-offs, use
<a href="#arithmetic">endian arithmetic types</a>. They are safe, easy to use, and easy to
maintain. Use the <em><a href="#choosing_anticipating_need">anticipating need</a></em> design
pattern locally around performance hot spots like lengthy loops, if needed.</p>
</div>
<div class="sect2">
<h3 id="choosing_background">Background</h3>
<div class="paragraph">
<p>A dealing with endianness usually implies a program portability or a data
portability requirement, and often both. That means real programs dealing with
endianness are usually complex, so the examples shown here would really be
written as multiple functions spread across multiple translation units. They
would involve interfaces that can not be altered as they are supplied by
third-parties or the standard library.</p>
</div>
</div>
<div class="sect2">
<h3 id="choosing_characteristics">Characteristics</h3>
<div class="paragraph">
<p>The characteristics that differentiate the three approaches to endianness are
the endianness invariants, conversion explicitness, arithmetic operations, sizes
available, and alignment requirements.</p>
</div>
<div class="sect3">
<h4 id="choosing_endianness_invariants">Endianness invariants</h4>
<div class="paragraph">
<p><strong>Endian conversion functions</strong> use objects of the ordinary C&#43;&#43; arithmetic types
like <code>int</code> or <code>unsigned short</code> to hold values. That breaks the implicit
invariant that the C&#43;&#43; language rules apply. The usual language rules only apply
if the endianness of the object is currently set to the native endianness for
the platform. That can make it very hard to reason about logic flow, and result
in difficult to find bugs.</p>
</div>
<div class="paragraph">
<p>For example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">data_t</span>  <span class="c1">// big endian</span>
<span class="p">{</span>
  <span class="kt">int32_t</span>   <span class="n">v1</span><span class="p">;</span>  <span class="c1">// description ...</span>
  <span class="kt">int32_t</span>   <span class="n">v2</span><span class="p">;</span>  <span class="c1">// description ...</span>
  <span class="p">...</span> <span class="n">additional</span> <span class="n">character</span> <span class="n">data</span> <span class="n">members</span> <span class="p">(</span><span class="n">i</span><span class="p">.</span><span class="n">e</span><span class="p">.</span> <span class="n">non</span><span class="o">-</span><span class="n">endian</span><span class="p">)</span>
  <span class="kt">int32_t</span>   <span class="n">v3</span><span class="p">;</span>  <span class="c1">// description ...</span>
<span class="p">};</span>

<span class="n">data_t</span> <span class="n">data</span><span class="p">;</span>

<span class="n">read</span><span class="p">(</span><span class="n">data</span><span class="p">);</span>
<span class="n">big_to_native_inplace</span><span class="p">(</span><span class="n">data</span><span class="p">.</span><span class="n">v1</span><span class="p">);</span>
<span class="n">big_to_native_inplace</span><span class="p">(</span><span class="n">data</span><span class="p">.</span><span class="n">v2</span><span class="p">);</span>

<span class="p">...</span>

<span class="o">++</span><span class="n">v1</span><span class="p">;</span>
<span class="n">third_party</span><span class="o">::</span><span class="n">func</span><span class="p">(</span><span class="n">data</span><span class="p">.</span><span class="n">v2</span><span class="p">);</span>

<span class="p">...</span>

<span class="n">native_to_big_inplace</span><span class="p">(</span><span class="n">data</span><span class="p">.</span><span class="n">v1</span><span class="p">);</span>
<span class="n">native_to_big_inplace</span><span class="p">(</span><span class="n">data</span><span class="p">.</span><span class="n">v2</span><span class="p">);</span>
<span class="n">write</span><span class="p">(</span><span class="n">data</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The programmer didn&#8217;t bother to convert <code>data.v3</code> to native endianness because
that member isn&#8217;t used. A later maintainer needs to pass <code>data.v3</code> to the
third-party function, so adds <code>third_party::func(data.v3);</code> somewhere deep in
the code. This causes a silent failure because the usual invariant that an
object of type <code>int32_t</code> holds a value as described by the C&#43;&#43; core language
does not apply.</p>
</div>
<div class="paragraph">
<p><strong>Endian buffer and arithmetic types</strong> hold values internally as arrays of
characters with an invariant that the endianness of the array never changes.
That makes these types easier to use and programs easier to maintain.</p>
</div>
<div class="paragraph">
<p>Here is the same example, using an endian arithmetic type:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">data_t</span>
<span class="p">{</span>
  <span class="n">big_int32_t</span>   <span class="n">v1</span><span class="p">;</span>  <span class="c1">// description ...</span>
  <span class="n">big_int32_t</span>   <span class="n">v2</span><span class="p">;</span>  <span class="c1">// description ...</span>
  <span class="p">...</span> <span class="n">additional</span> <span class="n">character</span> <span class="n">data</span> <span class="n">members</span> <span class="p">(</span><span class="n">i</span><span class="p">.</span><span class="n">e</span><span class="p">.</span> <span class="n">non</span><span class="o">-</span><span class="n">endian</span><span class="p">)</span>
  <span class="n">big_int32_t</span>   <span class="n">v3</span><span class="p">;</span>  <span class="c1">// description ...</span>
<span class="p">};</span>

<span class="n">data_t</span> <span class="n">data</span><span class="p">;</span>

<span class="n">read</span><span class="p">(</span><span class="n">data</span><span class="p">);</span>

<span class="p">...</span>

<span class="o">++</span><span class="n">v1</span><span class="p">;</span>
<span class="n">third_party</span><span class="o">::</span><span class="n">func</span><span class="p">(</span><span class="n">data</span><span class="p">.</span><span class="n">v2</span><span class="p">);</span>

<span class="p">...</span>

<span class="n">write</span><span class="p">(</span><span class="n">data</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>A later maintainer can add <code>third_party::func(data.v3)</code> and it will just-work.</p>
</div>
</div>
<div class="sect3">
<h4 id="choosing_conversion_explicitness">Conversion explicitness</h4>
<div class="paragraph">
<p><strong>Endian conversion functions</strong> and <strong>buffer types</strong> never perform implicit
conversions. This gives users explicit control of when conversion occurs, and
may help avoid unnecessary conversions.</p>
</div>
<div class="paragraph">
<p><strong>Endian arithmetic types</strong> perform conversion implicitly. That makes these types
very easy to use, but can result in unnecessary conversions. Failure to hoist
conversions out of inner loops can bring a performance penalty.</p>
</div>
</div>
<div class="sect3">
<h4 id="choosing_arithmetic_operations">Arithmetic operations</h4>
<div class="paragraph">
<p><strong>Endian conversion functions</strong> do not supply arithmetic operations, but this is
not a concern since this approach uses ordinary C&#43;&#43; arithmetic types to hold
values.</p>
</div>
<div class="paragraph">
<p><strong>Endian buffer types</strong> do not supply arithmetic operations. Although this
approach avoids unnecessary conversions, it can result in the introduction of
additional variables and confuse maintenance programmers.</p>
</div>
<div class="paragraph">
<p><strong>Endian arithmetic types</strong> do supply arithmetic operations. They are very easy to
use if lots of arithmetic is involved.</p>
</div>
</div>
<div class="sect3">
<h4 id="choosing_sizes">Sizes</h4>
<div class="paragraph">
<p><strong>Endianness conversion functions</strong> only support 1, 2, 4, and 8 byte integers.
That&#8217;s sufficient for many applications.</p>
</div>
<div class="paragraph">
<p><strong>Endian buffer and arithmetic types</strong> support 1, 2, 3, 4, 5, 6, 7, and 8 byte
integers. For an application where memory use or I/O speed is the limiting
factor, using sizes tailored to application needs can be useful.</p>
</div>
</div>
<div class="sect3">
<h4 id="choosing_alignments">Alignments</h4>
<div class="paragraph">
<p><strong>Endianness conversion functions</strong> only support aligned integer and
floating-point types. That&#8217;s sufficient for most applications.</p>
</div>
<div class="paragraph">
<p><strong>Endian buffer and arithmetic types</strong> support both aligned and unaligned
integer and floating-point types. Unaligned types are rarely needed, but when
needed they are often very useful and workarounds are painful. For example:</p>
</div>
<div class="paragraph">
<p>Non-portable code like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">S</span> <span class="p">{</span>
  <span class="kt">uint16_t</span> <span class="n">a</span><span class="p">;</span> <span class="c1">// big endian</span>
  <span class="kt">uint32_t</span> <span class="n">b</span><span class="p">;</span> <span class="c1">// big endian</span>
<span class="p">}</span> <span class="n">__attribute__</span> <span class="p">((</span><span class="n">packed</span><span class="p">));</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Can be replaced with portable code like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">S</span> <span class="p">{</span>
  <span class="n">big_uint16_ut</span> <span class="n">a</span><span class="p">;</span>
  <span class="n">big_uint32_ut</span> <span class="n">b</span><span class="p">;</span>
<span class="p">};</span></code></pre>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="choosing_design_patterns">Design patterns</h3>
<div class="paragraph">
<p>Applications often traffic in endian data as records or packets containing
multiple endian data elements. For simplicity, we will just call them records.</p>
</div>
<div class="paragraph">
<p>If desired endianness differs from native endianness, a conversion has to be
performed. When should that conversion occur? Three design patterns have
evolved.</p>
</div>
<div class="sect3">
<h4 id="choosing_convert_only_as_needed_i_e_lazy">Convert only as needed (i.e. lazy)</h4>
<div class="paragraph">
<p>This pattern defers conversion to the point in the code where the data
element is actually used.</p>
</div>
<div class="paragraph">
<p>This pattern is appropriate when which endian element is actually used varies
greatly according to record content or other circumstances</p>
</div>
</div>
<div class="sect3">
<h4 id="choosing_anticipating_need">Convert in anticipation of need</h4>
<div class="paragraph">
<p>This pattern performs conversion to native endianness in anticipation of use,
such as immediately after reading records. If needed, conversion to the output
endianness is performed after all possible needs have passed, such as just
before writing records.</p>
</div>
<div class="paragraph">
<p>One implementation of this pattern is to create a proxy record with endianness
converted to native in a read function, and expose only that proxy to the rest
of the implementation. If a write function, if needed, handles the conversion
from native to the desired output endianness.</p>
</div>
<div class="paragraph">
<p>This pattern is appropriate when all endian elements in a record are typically
used regardless of record content or other circumstances.</p>
</div>
</div>
<div class="sect3">
<h4 id="choosing_convert_only_as_needed_except_locally_in_anticipation_of_need">Convert only as needed, except locally in anticipation of need</h4>
<div class="paragraph">
<p>This pattern in general defers conversion but for specific local needs does
anticipatory conversion. Although particularly appropriate when coupled with the
endian buffer or arithmetic types, it also works well with the conversion
functions.</p>
</div>
<div class="paragraph">
<p>Example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">data_t</span>
<span class="p">{</span>
  <span class="n">big_int32_t</span>   <span class="n">v1</span><span class="p">;</span>
  <span class="n">big_int32_t</span>   <span class="n">v2</span><span class="p">;</span>
  <span class="n">big_int32_t</span>   <span class="n">v3</span><span class="p">;</span>
<span class="p">};</span>

<span class="n">data_t</span> <span class="n">data</span><span class="p">;</span>

<span class="n">read</span><span class="p">(</span><span class="n">data</span><span class="p">);</span>

<span class="p">...</span>
<span class="o">++</span><span class="n">v1</span><span class="p">;</span>
<span class="p">...</span>

<span class="kt">int32_t</span> <span class="n">v3_temp</span> <span class="o">=</span> <span class="n">data</span><span class="p">.</span><span class="n">v3</span><span class="p">;</span>  <span class="c1">// hoist conversion out of loop</span>

<span class="k">for</span> <span class="p">(</span><span class="kt">int32_t</span> <span class="n">i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="n">i</span> <span class="o">&lt;</span> <span class="err"><code></span><span class="n">large</span><span class="o">-</span><span class="n">number</span><span class="err"></code></span><span class="p">;</span> <span class="o">++</span><span class="n">i</span><span class="p">)</span>
<span class="p">{</span>
  <span class="p">...</span> <span class="err"><code></span><span class="n">lengthy</span> <span class="n">computation</span> <span class="n">that</span> <span class="n">accesses</span> <span class="n">v3_temp</span><span class="err"></code></span> <span class="p">...</span>
<span class="p">}</span>
<span class="n">data</span><span class="p">.</span><span class="n">v3</span> <span class="o">=</span> <span class="n">v3_temp</span><span class="p">;</span>

<span class="n">write</span><span class="p">(</span><span class="n">data</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>In general the above pseudo-code leaves conversion up to the endian arithmetic
type <code>big_int32_t</code>. But to avoid conversion inside the loop, a temporary is
created before the loop is entered, and then used to set the new value of
<code>data.v3</code> after the loop is complete.</p>
</div>
<div class="paragraph">
<p>Question: Won&#8217;t the compiler&#8217;s optimizer hoist the conversion out of the loop
anyhow?</p>
</div>
<div class="paragraph">
<p>Answer: VC&#43;&#43; 2015 Preview, and probably others, does not, even for a toy test
program. Although the savings is small (two register <code>bswap</code> instructions), the
cost might be significant if the loop is repeated enough times. On the other
hand, the program may be so dominated by I/O time that even a lengthy loop will
be immaterial.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="choosing_use_case_examples">Use case examples</h3>
<div class="sect3">
<h4 id="choosing_porting_endian_unaware_codebase">Porting endian unaware codebase</h4>
<div class="paragraph">
<p>An existing codebase runs on  big endian systems. It does not currently deal
with endianness. The codebase needs to be modified so it can run on little
endian systems under various operating systems. To ease transition and protect
value of existing files, external data will continue to be maintained as big
endian.</p>
</div>
<div class="paragraph">
<p>The <a href="#arithmetic">endian arithmetic approach</a> is recommended to meet these
needs. A relatively small number of header files dealing with binary I/O layouts
need to change types. For example, <code>short</code> or <code>int16_t</code> would change to
<code>big_int16_t</code>. No changes are required for <code>.cpp</code> files.</p>
</div>
</div>
<div class="sect3">
<h4 id="choosing_porting_endian_aware_codebase">Porting endian aware codebase</h4>
<div class="paragraph">
<p>An existing codebase runs on little-endian Linux systems. It already deals with
endianness via
<a href="http://man7.org/linux/man-pages/man3/endian.3.html">Linux provided functions</a>.
Because of a business merger, the codebase has to be quickly modified for
Windows and possibly other operating systems, while still supporting Linux. The
codebase is reliable and the programmers are all well-aware of endian issues.</p>
</div>
<div class="paragraph">
<p>These factors all argue for an <a href="#conversion">endian conversion approach</a> that
just mechanically changes the calls to <code>htobe32</code>, etc. to
<code>boost::endian::native_to_big</code>, etc. and replaces <code>&lt;endian.h&gt;</code> with
<code>&lt;boost/endian/conversion.hpp&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="choosing_reliability_and_arithmetic_speed">Reliability and arithmetic-speed</h4>
<div class="paragraph">
<p>A new, complex, multi-threaded application is to be developed that must run
on little endian machines, but do big endian network I/O. The developers believe
computational speed for endian variable is critical but have seen numerous bugs
result from inability to reason about endian conversion state. They are also
worried that future maintenance changes could inadvertently introduce a lot of
slow conversions if full-blown endian arithmetic types are used.</p>
</div>
<div class="paragraph">
<p>The <a href="#buffers">endian buffers</a> approach is made-to-order for this use case.</p>
</div>
</div>
<div class="sect3">
<h4 id="choosing_reliability_and_ease_of_use">Reliability and ease-of-use</h4>
<div class="paragraph">
<p>A new, complex, multi-threaded application is to be developed that must run on
little endian machines, but do big endian network I/O. The developers believe
computational speed for endian variables is <strong>not critical</strong> but have seen
numerous bugs result from inability to reason about endian conversion state.
They are also concerned about ease-of-use both during development and long-term
maintenance.</p>
</div>
<div class="paragraph">
<p>Removing concern about conversion speed and adding concern about ease-of-use
tips the balance strongly in favor the
<a href="#arithmetic">endian arithmetic approach</a>.</p>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="conversion">Endian Conversion Functions</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="conversion_introduction">Introduction</h3>
<div class="paragraph">
<p>Header <code>boost/endian/conversion.hpp</code> provides byte order reversal and conversion
functions that convert objects of the built-in integer types between native,
big, or little endian byte ordering. User defined types are also supported.</p>
</div>
</div>
<div class="sect2">
<h3 id="conversion_reference">Reference</h3>
<div class="paragraph">
<p>Functions are implemented <code>inline</code> if appropriate. For C&#43;&#43;03 compilers,
<code>noexcept</code> is elided. Boost scoped enum emulation is used so that the library
still works for compilers that do not support scoped enums.</p>
</div>
<div class="sect3">
<h4 id="conversion_definitions">Definitions</h4>
<div class="paragraph">
<p><strong>Endianness</strong> refers to the ordering of bytes within internal or external
integers and other arithmetic data. Most-significant byte first is called
<strong>big endian</strong> ordering. Least-significant byte first is called
<strong>little endian</strong> ordering. Other orderings are possible and some CPU
architectures support both big and little ordering.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
The names are derived from
<a href="http://en.wikipedia.org/wiki/Jonathan_Swift">Jonathan Swift</a>'s satirical novel
<em><a href="http://en.wikipedia.org/wiki/Gulliver&#8217;s_Travels">Gulliver&#8217;s Travels</a></em>, where
rival kingdoms opened their soft-boiled eggs at different ends. Wikipedia has an
extensive description of <a href="https://en.wikipedia.org/wiki/Endianness">Endianness</a>.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>The standard integral types (C&#43;&#43;std [basic.fundamental]) except <code>bool</code> and
the scoped enumeration types (C&#43;&#43;std [dcl.enum]) are collectively called the
<strong>endian types</strong>. In the absence of padding bits, which is true on the platforms
supported by the Boost.Endian library, endian types have the property that all
of their bit patterns are valid values, which means that when an object of an
endian type has its constituent bytes reversed, the result is another valid value.
This allows <code>endian_reverse</code> to take and return by value.</p>
</div>
<div class="paragraph">
<p>Other built-in types, such as <code>bool</code>, <code>float</code>, or unscoped enumerations, do not
have the same property, which means that reversing their constituent bytes may
produce an invalid value, leading to undefined behavior. These types are therefore
disallowed in <code>endian_reverse</code>, but are still allowed in <code>endian_reverse_inplace</code>.
Even if an object becomes invalid as a result of reversing its bytes, as long as
its value is never read, there would be no undefined behavior.</p>
</div>
</div>
<div class="sect3">
<h4 id="conversion_header_boostendianconversion_hpp_synopsis">Header <code>&lt;boost/endian/conversion.hpp&gt;</code> Synopsis</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#define BOOST_ENDIAN_INTRINSIC_MSG \
   &#8220;message describing presence or absence of intrinsics&#8221;
</span>
<span class="k">namespace</span> <span class="n">boost</span>
<span class="p">{</span>
<span class="k">namespace</span> <span class="n">endian</span>
<span class="p">{</span>
  <span class="k">enum</span> <span class="k">class</span> <span class="nc">order</span>
  <span class="p">{</span>
    <span class="n">native</span> <span class="o">=</span> <span class="err"><code></span><span class="n">see</span> <span class="n">below</span><span class="err"></code></span><span class="p">,</span>
    <span class="n">big</span>    <span class="o">=</span> <span class="err"><code></span><span class="n">see</span> <span class="n">below</span><span class="err"></code></span><span class="p">,</span>
    <span class="n">little</span> <span class="o">=</span> <span class="err"><code></span><span class="n">see</span> <span class="n">below</span><span class="err"></code></span><span class="p">,</span>
  <span class="p">};</span>

  <span class="c1">// Byte reversal functions</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Endian</span><span class="p">&gt;</span>
    <span class="n">Endian</span> <span class="n">endian_reverse</span><span class="p">(</span><span class="n">Endian</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
    <span class="n">EndianReversible</span> <span class="n">big_to_native</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
    <span class="n">EndianReversible</span> <span class="n">native_to_big</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
    <span class="n">EndianReversible</span> <span class="n">little_to_native</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
    <span class="n">EndianReversible</span> <span class="n">native_to_little</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="n">order</span> <span class="n">O1</span><span class="p">,</span> <span class="n">order</span> <span class="n">O2</span><span class="p">,</span> <span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
    <span class="n">EndianReversible</span> <span class="n">conditional_reverse</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
    <span class="n">EndianReversible</span> <span class="n">conditional_reverse</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">,</span>
      <span class="n">order</span> <span class="n">order1</span><span class="p">,</span> <span class="n">order</span> <span class="n">order2</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="c1">// In-place byte reversal functions</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">endian_reverse_inplace</span><span class="p">(</span><span class="n">EndianReversible</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">N</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">endian_reverse_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span> <span class="p">(</span><span class="o">&amp;</span><span class="n">x</span><span class="p">)[</span><span class="n">N</span><span class="p">])</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">big_to_native_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">native_to_big_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">little_to_native_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">native_to_little_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="n">order</span> <span class="n">O1</span><span class="p">,</span> <span class="n">order</span> <span class="n">O2</span><span class="p">,</span> <span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">conditional_reverse_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
   <span class="kt">void</span> <span class="n">conditional_reverse_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">,</span>
     <span class="n">order</span> <span class="n">order1</span><span class="p">,</span> <span class="n">order</span> <span class="n">order2</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="c1">// Generic load and store functions</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">N</span><span class="p">,</span> <span class="n">order</span> <span class="n">Order</span><span class="p">&gt;</span>
    <span class="n">T</span> <span class="n">endian_load</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">N</span><span class="p">,</span> <span class="n">order</span> <span class="n">Order</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">endian_store</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">T</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="c1">// Convenience load functions</span>

  <span class="n">boost</span><span class="o">::</span><span class="kt">int16_t</span> <span class="n">load_little_s16</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint16_t</span> <span class="n">load_little_u16</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">int16_t</span> <span class="n">load_big_s16</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint16_t</span> <span class="n">load_big_u16</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="n">boost</span><span class="o">::</span><span class="kt">int32_t</span> <span class="n">load_little_s24</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint32_t</span> <span class="n">load_little_u24</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">int32_t</span> <span class="n">load_big_s24</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint32_t</span> <span class="n">load_big_u24</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="n">boost</span><span class="o">::</span><span class="kt">int32_t</span> <span class="n">load_little_s32</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint32_t</span> <span class="n">load_little_u32</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">int32_t</span> <span class="n">load_big_s32</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint32_t</span> <span class="n">load_big_u32</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">load_little_s40</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">load_little_u40</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">load_big_s40</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">load_big_u40</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">load_little_s48</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">load_little_u48</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">load_big_s48</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">load_big_u48</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">load_little_s56</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">load_little_u56</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">load_big_s56</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">load_big_u56</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">load_little_s64</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">load_little_u64</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">load_big_s64</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"><strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">load_big_u64</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o"></strong></span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="c1">// Convenience store functions</span>

  <span class="kt">void</span> <span class="n">store_little_s16</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int16_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_little_u16</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint16_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_s16</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int16_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_u16</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint16_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="kt">void</span> <span class="n">store_little_s24</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int32_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_little_u24</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint32_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_s24</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int32_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_u24</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint32_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="kt">void</span> <span class="n">store_little_s32</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int32_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_little_u32</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint32_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_s32</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int32_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_u32</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint32_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="kt">void</span> <span class="n">store_little_s40</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_little_u40</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_s40</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_u40</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="kt">void</span> <span class="n">store_little_s48</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_little_u48</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_s48</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_u48</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="kt">void</span> <span class="n">store_little_s56</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_little_u56</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_s56</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_u56</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

  <span class="kt">void</span> <span class="n">store_little_s64</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_little_u64</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_s64</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"><strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">int64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
  <span class="kt">void</span> <span class="n">store_big_u64</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o"></strong></span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="kt">uint64_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="p">}</span> <span class="c1">// namespace endian</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The values of <code>order::little</code> and <code>order::big</code> shall not be equal to one
another.</p>
</div>
<div class="paragraph">
<p>The value of <code>order::native</code> shall be:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>equal to <code>order::big</code> if the execution environment is big endian, otherwise</p>
</li>
<li>
<p>equal to <code>order::little</code> if the execution environment is little endian,
otherwise</p>
</li>
<li>
<p>unequal to both <code>order::little</code> and <code>order::big</code>.</p>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="conversion_requirements">Requirements</h4>
<div class="sect4">
<h5 id="conversion_template_argument_requirements">Template argument requirements</h5>
<div class="paragraph">
<p>The template definitions in the <code>boost/endian/conversion.hpp</code> header refer to
various named requirements whose details are set out in the tables in this
subsection. In these tables, <code>T</code> is an object or reference type to be supplied
by a C&#43;&#43; program instantiating a template; <code>x</code> is a value of type (possibly
<code>const</code>) <code>T</code>; <code>mlx</code> is a modifiable lvalue of type <code>T</code>.</p>
</div>
<div class="sect5">
<h6 id="conversion_endianreversible">EndianReversible requirements (in addition to <code>CopyConstructible</code>)</h6>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 33.3333%;">
<col style="width: 33.3333%;">
<col style="width: 33.3334%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Expression</th>
<th class="tableblock halign-left valign-top">Return</th>
<th class="tableblock halign-left valign-top">Requirements</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>endian_reverse(x)</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>T</code></p></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>T</code> is an endian type or a class type.</p>
</div>
<div class="paragraph">
<p>If <code>T</code> is an endian type, returns the value of <code>x</code> with the order of bytes
reversed.</p>
</div>
<div class="paragraph">
<p>If <code>T</code> is a class type, the function:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Is expected to be implemented by the user, as a non-member function in the same
namespace as <code>T</code> that can be found by argument dependent lookup (ADL);</p>
</li>
<li>
<p>Should return the value of <code>x</code> with the order of bytes reversed for all data members
of types or arrays of types that meet the <code>EndianReversible</code> requirements.</p>
</li>
</ul>
</div></div></td>
</tr>
</tbody>
</table>
</div>
<div class="sect5">
<h6 id="conversion_endianreversibleinplace">EndianReversibleInplace requirements</h6>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Expression</th>
<th class="tableblock halign-left valign-top">Requirements</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>endian_reverse_inplace(mlx)</code></p></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>T</code> is an integral type, an enumeration type, <code>float</code>, <code>double</code>, a class type,
or an array type.</p>
</div>
<div class="paragraph">
<p>If <code>T</code> is not a class type or an array type, reverses the order of bytes in <code>mlx</code>.</p>
</div>
<div class="paragraph">
<p>If <code>T</code> is a class type, the function:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Is expected to be implemented by the user, as a non-member function in the same
namespace as <code>T</code> that can be found by argument dependent lookup (ADL);</p>
</li>
<li>
<p>Should reverse the order of bytes of all data members of <code>mlx</code> that have types or
arrays of types that meet the <code>EndianReversible</code> or <code>EndianReversibleInplace</code>
requirements.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>If <code>T</code> is an array type, calls <code>endian_reverse_inplace</code> on each element.</p>
</div></div></td>
</tr>
</tbody>
</table>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Because there is a function template for <code>endian_reverse_inplace</code> that
calls <code>endian_reverse</code> for class types, only <code>endian_reverse</code> is required for a
user-defined type to meet the <code>EndianReversibleInplace</code> requirements. Although
user-defined types are not required to supply an <code>endian_reverse_inplace</code> function,
doing so may improve efficiency.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect4">
<h5 id="conversion_customization_points_for_user_defined_types_udts">Customization points for user-defined types (UDTs)</h5>
<div class="paragraph">
<p>This subsection describes requirements on the Endian library&#8217;s  implementation.</p>
</div>
<div class="paragraph">
<p>The library&#8217;s function templates requiring
<code><a href="#conversion_endianreversible">EndianReversible</a></code> are required to perform
reversal of endianness if needed by making an unqualified call to
<code>endian_reverse()</code>.</p>
</div>
<div class="paragraph">
<p>The library&#8217;s function templates requiring
<code><a href="#conversion_endianreversibleinplace">EndianReversibleInplace</a></code> are required to
perform reversal of endianness if needed by making an unqualified call to
<code>endian_reverse_inplace()</code>.</p>
</div>
<div class="paragraph">
<p>See <code>example/udt_conversion_example.cpp</code> for an example user-defined type.</p>
</div>
</div>
</div>
<div class="sect3">
<h4 id="conversion_byte_reversal_functions">Byte Reversal Functions</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Endian</span><span class="p">&gt;</span>
<span class="n">Endian</span> <span class="n">endian_reverse</span><span class="p">(</span><span class="n">Endian</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires</dt>
<dd>
<p><code>Endian</code> must be a standard integral type that is not <code>bool</code>,
or a scoped enumeration type.</p>
</dd>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>x</code>, with the order of its constituent bytes reversed.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
<span class="n">EndianReversible</span> <span class="n">big_to_native</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>conditional_reverse&lt;order::big, order::native&gt;(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
<span class="n">EndianReversible</span> <span class="n">native_to_big</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>conditional_reverse&lt;order::native, order::big&gt;(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
<span class="n">EndianReversible</span> <span class="n">little_to_native</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>conditional_reverse&lt;order::little, order::native&gt;(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
<span class="n">EndianReversible</span> <span class="n">native_to_little</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>conditional_reverse&lt;order::native, order::little&gt;(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="n">order</span> <span class="n">O1</span><span class="p">,</span> <span class="n">order</span> <span class="n">O2</span><span class="p">,</span> <span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
<span class="n">EndianReversible</span> <span class="n">conditional_reverse</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>x</code> if <code>O1 == O2,</code> otherwise <code>endian_reverse(x)</code>.</p>
</dd>
<dt class="hdlist1">Remarks</dt>
<dd>
<p>Whether <code>x</code> or <code>endian_reverse(x)</code> is to be returned shall be
determined at compile time.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
<span class="n">EndianReversible</span> <span class="n">conditional_reverse</span><span class="p">(</span><span class="n">EndianReversible</span> <span class="n">x</span><span class="p">,</span>
     <span class="n">order</span> <span class="n">order1</span><span class="p">,</span> <span class="n">order</span> <span class="n">order2</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>order1 == order2? x: endian_reverse(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="conversion_in_place_byte_reversal_functions">In-place Byte Reversal Functions</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversible</span><span class="p">&gt;</span>
<span class="kt">void</span> <span class="n">endian_reverse_inplace</span><span class="p">(</span><span class="n">EndianReversible</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>When <code>EndianReversible</code> is a class type,
<code>x = endian_reverse(x);</code>. When <code>EndianReversible</code> is an integral
type, an enumeration type, <code>float</code>, or <code>double</code>, reverses the
order of the constituent bytes of <code>x</code>. Otherwise, the program is
ill-formed.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">N</span><span class="p">&gt;</span>
<span class="kt">void</span> <span class="n">endian_reverse_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span> <span class="p">(</span><span class="o">&amp;</span><span class="n">x</span><span class="p">)[</span><span class="n">N</span><span class="p">])</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>Calls <code>endian_reverse_inplace(x[i])</code> for <code>i</code> from <code>0</code> to <code>N-1</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
<span class="kt">void</span> <span class="n">big_to_native_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>conditional_reverse_inplace&lt;order::big, order::native&gt;(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
<span class="kt">void</span> <span class="n">native_to_big_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>conditional_reverse_inplace&lt;order::native, order::big&gt;(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
<span class="kt">void</span> <span class="n">little_to_native_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>conditional_reverse_inplace&lt;order::little, order::native&gt;(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
<span class="kt">void</span> <span class="n">native_to_little_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>conditional_reverse_inplace&lt;order::native, order::little&gt;(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="n">order</span> <span class="n">O1</span><span class="p">,</span> <span class="n">order</span> <span class="n">O2</span><span class="p">,</span> <span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
<span class="kt">void</span> <span class="n">conditional_reverse_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>None if <code>O1 == O2,</code> otherwise <code>endian_reverse_inplace(x)</code>.</p>
</dd>
<dt class="hdlist1">Remarks</dt>
<dd>
<p>Which effect applies shall be determined at compile time.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">EndianReversibleInplace</span><span class="p">&gt;</span>
<span class="kt">void</span> <span class="n">conditional_reverse_inplace</span><span class="p">(</span><span class="n">EndianReversibleInplace</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">,</span>
     <span class="n">order</span> <span class="n">order1</span><span class="p">,</span> <span class="n">order</span> <span class="n">order2</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>If <code>order1 == order2</code> then <code>endian_reverse_inplace(x)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="conversion_generic_load_and_store_functions">Generic Load and Store Functions</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">N</span><span class="p">,</span> <span class="n">order</span> <span class="n">Order</span><span class="p">&gt;</span>
<span class="n">T</span> <span class="n">endian_load</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires</dt>
<dd>
<p><code>sizeof(T)</code> must be 1, 2, 4, or 8. <code>N</code> must be between 1 and
<code>sizeof(T)</code>, inclusive. <code>T</code> must be trivially copyable. If <code>N</code> is not
equal to <code>sizeof(T)</code>, <code>T</code> must be integral or <code>enum</code>.</p>
</dd>
<dt class="hdlist1">Effects</dt>
<dd>
<p>Reads <code>N</code> bytes starting from <code>p</code>, in forward or reverse order
depending on whether <code>Order</code> matches the native endianness or not,
interprets the resulting bit pattern as a value of type <code>T</code>, and returns it.
If <code>sizeof(T)</code> is bigger than <code>N</code>, zero-extends when <code>T</code> is unsigned,
sign-extends otherwise.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">N</span><span class="p">,</span> <span class="n">order</span> <span class="n">Order</span><span class="p">&gt;</span>
<span class="kt">void</span> <span class="n">endian_store</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">p</span><span class="p">,</span> <span class="n">T</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires</dt>
<dd>
<p><code>sizeof(T)</code> must be 1, 2, 4, or 8. <code>N</code> must be between 1 and
<code>sizeof(T)</code>, inclusive. <code>T</code> must be trivially copyable. If <code>N</code> is not
equal to <code>sizeof(T)</code>, <code>T</code> must be integral or <code>enum</code>.</p>
</dd>
<dt class="hdlist1">Effects</dt>
<dd>
<p>Writes to <code>p</code> the <code>N</code> least significant bytes from the object
representation of <code>v</code>, in forward or reverse order depending on whether
<code>Order</code> matches the native endianness or not.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="conversion_convenience_load_functions">Convenience Load Functions</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kr">inline</span> <span class="n">boost</span><span class="o">::</span><span class="n">intM_t</span> <span class="n">load_little_sN</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Reads an N-bit signed little-endian integer from <code>p</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>endian_load&lt;boost::intM_t, N/8, order::little&gt;( p )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kr">inline</span> <span class="n">boost</span><span class="o">::</span><span class="n">uintM_t</span> <span class="n">load_little_uN</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Reads an N-bit unsigned little-endian integer from <code>p</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>endian_load&lt;boost::uintM_t, N/8, order::little&gt;( p )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kr">inline</span> <span class="n">boost</span><span class="o">::</span><span class="n">intM_t</span> <span class="n">load_big_sN</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Reads an N-bit signed big-endian integer from <code>p</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>endian_load&lt;boost::intM_t, N/8, order::big&gt;( p )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kr">inline</span> <span class="n">boost</span><span class="o">::</span><span class="n">uintM_t</span> <span class="n">load_big_uN</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">p</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Reads an N-bit unsigned big-endian integer from <code>p</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>endian_load&lt;boost::uintM_t, N/8, order::big&gt;( p )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="conversion_convenience_store_functions">Convenience Store Functions</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kr">inline</span> <span class="kt">void</span> <span class="n">store_little_sN</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="n">intM_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Writes an N-bit signed little-endian integer to <code>p</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>endian_store&lt;boost::intM_t, N/8, order::little&gt;( p, v )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kr">inline</span> <span class="kt">void</span> <span class="n">store_little_uN</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="n">uintM_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Writes an N-bit unsigned little-endian integer to <code>p</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>endian_store&lt;boost::uintM_t, N/8, order::little&gt;( p, v )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kr">inline</span> <span class="kt">void</span> <span class="n">store_big_sN</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="n">intM_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Writes an N-bit signed big-endian integer to <code>p</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>endian_store&lt;boost::intM_t, N/8, order::big&gt;( p, v )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kr">inline</span> <span class="kt">void</span> <span class="n">store_big_uN</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">p</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="n">uintM_t</span> <span class="n">v</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="paragraph">
<p>Writes an N-bit unsigned big-endian integer to <code>p</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>endian_store&lt;boost::uintM_t, N/8, order::big&gt;( p, v )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect2">
<h3 id="conversion_faq">FAQ</h3>
<div class="paragraph">
<p>See the <a href="#overview_faq">Overview FAQ</a> for a library-wide FAQ.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Why are both value returning and modify-in-place functions provided?</dt>
<dd>
<p>Returning the result by value is the standard C and C&#43;&#43; idiom for functions
that compute a value from an argument. Modify-in-place functions allow cleaner
code in many real-world endian use cases and are more efficient for user-defined
types that have members such as string data that do not need to be reversed.
Thus both forms are provided.</p>
</dd>
<dt class="hdlist1">Why not use the Linux names (htobe16, htole16, be16toh, le16toh, etc.) ?</dt>
<dd>
<p>Those names are non-standard and vary even between POSIX-like operating
systems. A C&#43;&#43; library TS was going to use those names, but found they were
sometimes implemented as macros. Since macros do not respect scoping and
namespace rules, to use them would be very error prone.</p>
</dd>
</dl>
</div>
</div>
<div class="sect2">
<h3 id="conversion_acknowledgements">Acknowledgements</h3>
<div class="paragraph">
<p>Tomas Puverle was instrumental in identifying and articulating the need to
support endian conversion as separate from endian integer types. Phil Endecott
suggested the form of the value returning signatures. Vicente Botet and other
reviewers suggested supporting  user defined types. General reverse template
implementation approach using <code>std::reverse</code> suggested by Mathias Gaunard.
Portable implementation approach for 16, 32, and 64-bit integers suggested by
tymofey, with avoidance of undefined behavior as suggested by Giovanni Piero
Deretta, and a further refinement suggested by Pyry Jahkola. Intrinsic builtins
implementation approach for 16, 32, and 64-bit integers suggested by several
reviewers, and by David Stone, who provided his Boost licensed macro
implementation that became the starting point for
<code>boost/endian/detail/intrinsic.hpp</code>.  Pierre Talbot provided the
<code>int8_t endian_reverse()</code> and templated <code>endian_reverse_inplace()</code>
implementations.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="buffers">Endian Buffer Types</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="buffers_introduction">Introduction</h3>
<div class="paragraph">
<p>The internal byte order of arithmetic types is traditionally called
<strong>endianness</strong>. See the <a href="http://en.wikipedia.org/wiki/Endian">Wikipedia</a> for a full
exploration of <strong>endianness</strong>, including definitions of <strong>big endian</strong> and <strong>little
endian</strong>.</p>
</div>
<div class="paragraph">
<p>Header <code>boost/endian/buffers.hpp</code> provides <code>endian_buffer</code>, a portable endian
integer binary buffer class template with control over byte order, value type,
size, and alignment independent of the platform&#8217;s native endianness. Typedefs
provide easy-to-use names for common configurations.</p>
</div>
<div class="paragraph">
<p>Use cases primarily involve data portability, either via files or network
connections, but these byte-holders may also be used to reduce memory use, file
size, or network activity since they provide binary numeric sizes not otherwise
available.</p>
</div>
<div class="paragraph">
<p>Class <code>endian_buffer</code> is aimed at users who wish explicit control over when
endianness conversions occur. It also serves as the base class for the
<a href="#arithmetic">endian_arithmetic</a> class template, which is aimed at users who
wish fully automatic endianness conversion and direct support for all normal
arithmetic operations.</p>
</div>
</div>
<div class="sect2">
<h3 id="buffers_example">Example</h3>
<div class="paragraph">
<p>The <code>example/endian_example.cpp</code> program writes a binary file containing
four-byte, big-endian and little-endian integers:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#include &lt;iostream&gt;
#include &lt;cstdio&gt;
#include &lt;boost/endian/buffers.hpp&gt;  // see Synopsis below
#include &lt;boost/static_assert.hpp&gt;
</span>
<span class="k">using</span> <span class="k">namespace</span> <span class="n">boost</span><span class="o">::</span><span class="n">endian</span><span class="p">;</span>

<span class="k">namespace</span>
<span class="p">{</span>
  <span class="c1">//  This is an extract from a very widely used GIS file format.</span>
  <span class="c1">//  Why the designer decided to mix big and little endians in</span>
  <span class="c1">//  the same file is not known. But this is a real-world format</span>
  <span class="c1">//  and users wishing to write low level code manipulating these</span>
  <span class="c1">//  files have to deal with the mixed endianness.</span>

  <span class="k">struct</span> <span class="nc">header</span>
  <span class="p">{</span>
    <span class="n">big_int32_buf_t</span>     <span class="n">file_code</span><span class="p">;</span>
    <span class="n">big_int32_buf_t</span>     <span class="n">file_length</span><span class="p">;</span>
    <span class="n">little_int32_buf_t</span>  <span class="n">version</span><span class="p">;</span>
    <span class="n">little_int32_buf_t</span>  <span class="n">shape_type</span><span class="p">;</span>
  <span class="p">};</span>

  <span class="k">const</span> <span class="kt">char</span><span class="o">*</span> <span class="n">filename</span> <span class="o">=</span> <span class="s">"test.dat"</span><span class="p">;</span>
<span class="p">}</span>

<span class="kt">int</span> <span class="nf">main</span><span class="p">(</span><span class="kt">int</span><span class="p">,</span> <span class="kt">char</span><span class="o">*</span> <span class="p">[])</span>
<span class="p">{</span>
  <span class="n">header</span> <span class="n">h</span><span class="p">;</span>

  <span class="n">BOOST_STATIC_ASSERT</span><span class="p">(</span><span class="k">sizeof</span><span class="p">(</span><span class="n">h</span><span class="p">)</span> <span class="o">==</span> <span class="mi">16U</span><span class="p">);</span>  <span class="c1">// reality check</span>

  <span class="n">h</span><span class="p">.</span><span class="n">file_code</span>   <span class="o">=</span> <span class="mh">0x01020304</span><span class="p">;</span>
  <span class="n">h</span><span class="p">.</span><span class="n">file_length</span> <span class="o">=</span> <span class="k">sizeof</span><span class="p">(</span><span class="n">header</span><span class="p">);</span>
  <span class="n">h</span><span class="p">.</span><span class="n">version</span>     <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="n">h</span><span class="p">.</span><span class="n">shape_type</span>  <span class="o">=</span> <span class="mh">0x01020304</span><span class="p">;</span>

  <span class="c1">//  Low-level I/O such as POSIX read/write or &lt;cstdio&gt;</span>
  <span class="c1">//  fread/fwrite is sometimes used for binary file operations</span>
  <span class="c1">//  when ultimate efficiency is important. Such I/O is often</span>
  <span class="c1">//  performed in some C++ wrapper class, but to drive home the</span>
  <span class="c1">//  point that endian integers are often used in fairly</span>
  <span class="c1">//  low-level code that does bulk I/O operations, &lt;cstdio&gt;</span>
  <span class="c1">//  fopen/fwrite is used for I/O in this example.</span>

  <span class="n">std</span><span class="o">::</span><span class="kt">FILE</span><span class="o">*</span> <span class="n">fi</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">fopen</span><span class="p">(</span><span class="n">filename</span><span class="p">,</span> <span class="s">"wb"</span><span class="p">);</span>  <span class="c1">// MUST BE BINARY</span>

  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="n">fi</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"could not open "</span> <span class="o">&lt;&lt;</span> <span class="n">filename</span> <span class="o">&lt;&lt;</span> <span class="sc">'\n'</span><span class="p">;</span>
    <span class="k">return</span> <span class="mi">1</span><span class="p">;</span>
  <span class="p">}</span>

  <span class="k">if</span> <span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">fwrite</span><span class="p">(</span><span class="o">&amp;</span><span class="n">h</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">(</span><span class="n">header</span><span class="p">),</span> <span class="mi">1</span><span class="p">,</span> <span class="n">fi</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"write failure for "</span> <span class="o">&lt;&lt;</span> <span class="n">filename</span> <span class="o">&lt;&lt;</span> <span class="sc">'\n'</span><span class="p">;</span>
    <span class="k">return</span> <span class="mi">1</span><span class="p">;</span>
  <span class="p">}</span>

  <span class="n">std</span><span class="o">::</span><span class="n">fclose</span><span class="p">(</span><span class="n">fi</span><span class="p">);</span>

  <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"created file "</span> <span class="o">&lt;&lt;</span> <span class="n">filename</span> <span class="o">&lt;&lt;</span> <span class="sc">'\n'</span><span class="p">;</span>

  <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>After compiling and executing <code>example/endian_example.cpp</code>, a hex dump of
<code>test.dat</code> shows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="mo">01020304</span> <span class="mo">00000010</span> <span class="mo">01000000</span> <span class="mo">04030201</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Notice that the first two 32-bit integers are big endian while the second two
are little endian, even though the machine this was compiled and run on was
little endian.</p>
</div>
</div>
<div class="sect2">
<h3 id="buffers_limitations">Limitations</h3>
<div class="paragraph">
<p>Requires <code>&lt;climits&gt;</code>, <code>CHAR_BIT == 8</code>. If <code>CHAR_BIT</code> is some other value,
compilation will result in an <code>#error</code>. This restriction is in place because the
design, implementation, testing, and documentation has only considered issues
related to 8-bit bytes, and there have been no real-world use cases presented
for other sizes.</p>
</div>
<div class="paragraph">
<p>In C&#43;&#43;03, <code>endian_buffer</code> does not meet the requirements for POD types because
it has constructors and a private data member. This means that
common use cases are relying on unspecified behavior in that the C&#43;&#43; Standard
does not guarantee memory layout for non-POD types. This has not been a problem
in practice since all known C&#43;&#43; compilers  lay out memory as if <code>endian</code> were
a POD type. In C&#43;&#43;11, it is possible to specify the default constructor as
trivial, and private data members and base classes  no longer disqualify a type
from being a POD type. Thus under C&#43;&#43;11, <code>endian_buffer</code> will no longer be
relying on unspecified behavior.</p>
</div>
</div>
<div class="sect2">
<h3 id="buffers_feature_set">Feature set</h3>
<div class="ulist">
<ul>
<li>
<p>Big endian| little endian | native endian byte ordering.</p>
</li>
<li>
<p>Signed | unsigned</p>
</li>
<li>
<p>Unaligned | aligned</p>
</li>
<li>
<p>1-8 byte (unaligned) | 1, 2, 4, 8 byte (aligned)</p>
</li>
<li>
<p>Choice of  value type</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="buffers_enums_and_typedefs">Enums and typedefs</h3>
<div class="paragraph">
<p>Two scoped enums are provided:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">enum</span> <span class="k">class</span> <span class="nc">order</span> <span class="p">{</span> <span class="n">big</span><span class="p">,</span> <span class="n">little</span><span class="p">,</span> <span class="n">native</span> <span class="p">};</span>

<span class="k">enum</span> <span class="k">class</span> <span class="nc">align</span> <span class="p">{</span> <span class="n">no</span><span class="p">,</span> <span class="n">yes</span> <span class="p">};</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>One class template is provided:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="n">order</span> <span class="n">Order</span><span class="p">,</span> <span class="k">typename</span> <span class="nc">T</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">Nbits</span><span class="p">,</span>
  <span class="n">align</span> <span class="n">Align</span> <span class="o">=</span> <span class="n">align</span><span class="o">::</span><span class="n">no</span><span class="p">&gt;</span>
<span class="k">class</span> <span class="nc">endian_buffer</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Typedefs, such as <code>big_int32_buf_t</code>, provide convenient naming conventions for
common use cases:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 20%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Name</th>
<th class="tableblock halign-left valign-top">Alignment</th>
<th class="tableblock halign-left valign-top">Endianness</th>
<th class="tableblock halign-left valign-top">Sign</th>
<th class="tableblock halign-left valign-top">Sizes in bits (n)</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>big_intN_buf_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">big</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>big_uintN_buf_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">big</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>little_intN_buf_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">little</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>little_uintN_buf_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">little</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>native_intN_buf_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">native</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>native_uintN_buf_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">native</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>big_intN_buf_at</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">big</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,32,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>big_uintN_buf_at</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">big</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,32,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>little_intN_buf_at</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">little</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,32,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>little_uintN_buf_at</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">little</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,32,64</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>The unaligned types do not cause compilers to insert padding bytes in classes
and structs. This is an important characteristic that can be exploited to
minimize wasted space in memory, files, and network transmissions.</p>
</div>
<div class="admonitionblock caution">
<table>
<tr>
<td class="icon">
<div class="title">Caution</div>
</td>
<td class="content">
Code that uses aligned types is possibly non-portable because alignment
requirements vary between hardware architectures and because alignment may be
affected by compiler switches or pragmas. For example, alignment of an 64-bit
integer may be to a 32-bit boundary on a 32-bit machine and to a 64-bit boundary
on a 64-bit machine. Furthermore, aligned types are only available on
architectures with 8, 16, 32, and 64-bit integer types.
</td>
</tr>
</table>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<div class="title">Tip</div>
</td>
<td class="content">
Prefer unaligned buffer types.
</td>
</tr>
</table>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<div class="title">Tip</div>
</td>
<td class="content">
Protect yourself against alignment ills. For example:
</td>
</tr>
</table>
</div>
<div class="dlist none">
<dl>
<dt></dt>
<dd>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">static_assert</span><span class="p">(</span><span class="k">sizeof</span><span class="p">(</span><span class="n">containing_struct</span><span class="p">)</span> <span class="o">==</span> <span class="mi">12</span><span class="p">,</span> <span class="s">"sizeof(containing_struct) is wrong"</span><span class="p">);</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>Note: One-byte big and little buffer types have identical layout on all
platforms, so they never actually reverse endianness. They are provided to
enable generic code, and to improve code readability and searchability.</p>
</div>
</div>
<div class="sect2">
<h3 id="buffers_class_template_endian_buffer">Class template <code>endian_buffer</code></h3>
<div class="paragraph">
<p>An <code>endian_buffer</code> is a byte-holder for arithmetic types with
user-specified endianness, value type, size, and alignment.</p>
</div>
<div class="sect3">
<h4 id="buffers_synopsis">Synopsis</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span>
<span class="p">{</span>
  <span class="k">namespace</span> <span class="n">endian</span>
  <span class="p">{</span>
    <span class="c1">//  C++11 features emulated if not available</span>

    <span class="k">enum</span> <span class="k">class</span> <span class="nc">align</span> <span class="p">{</span> <span class="n">no</span><span class="p">,</span> <span class="n">yes</span> <span class="p">};</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="n">order</span> <span class="n">Order</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">Nbits</span><span class="p">,</span>
      <span class="n">align</span> <span class="n">Align</span> <span class="o">=</span> <span class="n">align</span><span class="o">::</span><span class="n">no</span><span class="p">&gt;</span>
    <span class="k">class</span> <span class="nc">endian_buffer</span>
    <span class="p">{</span>
    <span class="nl">public:</span>

      <span class="k">typedef</span> <span class="n">T</span> <span class="n">value_type</span><span class="p">;</span>

      <span class="n">endian_buffer</span><span class="p">()</span> <span class="k">noexcept</span> <span class="o">=</span> <span class="k">default</span><span class="p">;</span>
      <span class="k">explicit</span> <span class="n">endian_buffer</span><span class="p">(</span><span class="n">T</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

      <span class="n">endian_buffer</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span><span class="n">T</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">value_type</span> <span class="n">value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="kt">unsigned</span> <span class="kt">char</span><span class="o">*</span> <span class="n">data</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span><span class="o">*</span> <span class="n">data</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="nl">private:</span>

      <span class="kt">unsigned</span> <span class="kt">char</span> <span class="n">value_</span><span class="p">[</span><span class="n">Nbits</span> <span class="o">/</span> <span class="n">CHAR_BIT</span><span class="p">];</span> <span class="c1">// exposition only</span>
    <span class="p">};</span>

    <span class="c1">//  stream inserter</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">,</span> <span class="n">order</span> <span class="n">Order</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">,</span>
      <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n_bits</span><span class="p">,</span> <span class="n">align</span> <span class="n">Align</span><span class="p">&gt;</span>
    <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
      <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span>
        <span class="k">const</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">Order</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">n_bits</span><span class="p">,</span> <span class="n">Align</span><span class="o">&gt;&amp;</span> <span class="n">x</span><span class="p">);</span>

    <span class="c1">//  stream extractor</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">,</span> <span class="n">order</span> <span class="n">Order</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">,</span>
      <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n_bits</span><span class="p">,</span> <span class="n">align</span> <span class="n">A</span><span class="p">&gt;</span>
    <span class="n">std</span><span class="o">::</span><span class="n">basic_istream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
      <span class="k">operator</span><span class="o">&gt;&gt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">basic_istream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">is</span><span class="p">,</span>
        <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">Order</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">n_bits</span><span class="p">,</span> <span class="n">Align</span><span class="o">&gt;&amp;</span> <span class="n">x</span><span class="p">);</span>

    <span class="c1">// typedefs</span>

    <span class="c1">// unaligned big endian signed integer buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>        <span class="n">big_int8_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>      <span class="n">big_int16_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>      <span class="n">big_int24_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>      <span class="n">big_int32_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>      <span class="n">big_int40_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>      <span class="n">big_int48_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>      <span class="n">big_int56_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>      <span class="n">big_int64_buf_t</span><span class="p">;</span>

    <span class="c1">// unaligned big endian unsigned integer buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>       <span class="n">big_uint8_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>     <span class="n">big_uint16_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>     <span class="n">big_uint24_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>     <span class="n">big_uint32_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>     <span class="n">big_uint40_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>     <span class="n">big_uint48_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>     <span class="n">big_uint56_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>     <span class="n">big_uint64_buf_t</span><span class="p">;</span>

    <span class="c1">// unaligned big endian floating point buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>              <span class="n">big_float32_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>             <span class="n">big_float64_buf_t</span><span class="p">;</span>

    <span class="c1">// unaligned little endian signed integer buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>     <span class="n">little_int8_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>   <span class="n">little_int16_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>   <span class="n">little_int24_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>   <span class="n">little_int32_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>   <span class="n">little_int40_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>   <span class="n">little_int48_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>   <span class="n">little_int56_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>   <span class="n">little_int64_buf_t</span><span class="p">;</span>

    <span class="c1">// unaligned little endian unsigned integer buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>    <span class="n">little_uint8_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>  <span class="n">little_uint16_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>  <span class="n">little_uint24_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>  <span class="n">little_uint32_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>  <span class="n">little_uint40_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>  <span class="n">little_uint48_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>  <span class="n">little_uint56_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>  <span class="n">little_uint64_buf_t</span><span class="p">;</span>

    <span class="c1">// unaligned little endian floating point buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>           <span class="n">little_float32_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>          <span class="n">little_float64_buf_t</span><span class="p">;</span>

    <span class="c1">// unaligned native endian signed integer types</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>     <span class="n">native_int8_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>   <span class="n">native_int16_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>   <span class="n">native_int24_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>   <span class="n">native_int32_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>   <span class="n">native_int40_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>   <span class="n">native_int48_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>   <span class="n">native_int56_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>   <span class="n">native_int64_buf_t</span><span class="p">;</span>

    <span class="c1">// unaligned native endian unsigned integer types</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>    <span class="n">native_uint8_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>  <span class="n">native_uint16_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>  <span class="n">native_uint24_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>  <span class="n">native_uint32_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>  <span class="n">native_uint40_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>  <span class="n">native_uint48_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>  <span class="n">native_uint56_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>  <span class="n">native_uint64_buf_t</span><span class="p">;</span>

    <span class="c1">// unaligned native endian floating point types</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>           <span class="n">native_float32_buf_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>          <span class="n">native_float64_buf_t</span><span class="p">;</span>

    <span class="c1">// aligned big endian signed integer buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int8_t</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>       <span class="n">big_int8_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int16_t</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>     <span class="n">big_int16_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int32_t</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>     <span class="n">big_int32_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int64_t</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>     <span class="n">big_int64_buf_at</span><span class="p">;</span>

    <span class="c1">// aligned big endian unsigned integer buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint8_t</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>      <span class="n">big_uint8_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint16_t</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">big_uint16_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint32_t</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">big_uint32_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint64_t</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">big_uint64_buf_at</span><span class="p">;</span>

    <span class="c1">// aligned big endian floating point buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>       <span class="n">big_float32_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>      <span class="n">big_float64_buf_at</span><span class="p">;</span>

    <span class="c1">// aligned little endian signed integer buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int8_t</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">little_int8_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int16_t</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>  <span class="n">little_int16_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int32_t</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>  <span class="n">little_int32_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int64_t</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>  <span class="n">little_int64_buf_at</span><span class="p">;</span>

    <span class="c1">// aligned little endian unsigned integer buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint8_t</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>   <span class="n">little_uint8_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint16_t</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span> <span class="n">little_uint16_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint32_t</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span> <span class="n">little_uint32_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint64_t</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span> <span class="n">little_uint64_buf_at</span><span class="p">;</span>

    <span class="c1">// aligned little endian floating point buffers</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">little_float32_buf_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>   <span class="n">little_float64_buf_at</span><span class="p">;</span>

    <span class="c1">// aligned native endian typedefs are not provided because</span>
    <span class="c1">// &lt;cstdint&gt; types are superior for this use case</span>

  <span class="p">}</span> <span class="c1">// namespace endian</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expository data member <code>value_</code> stores the current value of the
<code>endian_buffer</code> object as a sequence of bytes ordered as specified by the
<code>Order</code> template parameter. The <code>CHAR_BIT</code> macro is defined in <code>&lt;climits&gt;</code>.
The only supported value of <code>CHAR_BIT</code> is 8.</p>
</div>
<div class="paragraph">
<p>The valid values of <code>Nbits</code> are as follows:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>When <code>sizeof(T)</code> is 1, <code>Nbits</code> shall be 8;</p>
</li>
<li>
<p>When <code>sizeof(T)</code> is 2, <code>Nbits</code> shall be 16;</p>
</li>
<li>
<p>When <code>sizeof(T)</code> is 4, <code>Nbits</code> shall be 24 or 32;</p>
</li>
<li>
<p>When <code>sizeof(T)</code> is 8, <code>Nbits</code> shall be 40, 48, 56, or 64.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Other values of <code>sizeof(T)</code> are not supported.</p>
</div>
<div class="paragraph">
<p>When <code>Nbits</code> is equal to <code>sizeof(T)*8</code>, <code>T</code> must be a trivially copyable type
(such as <code>float</code>) that is assumed to have the same endianness as <code>uintNbits_t</code>.</p>
</div>
<div class="paragraph">
<p>When <code>Nbits</code> is less than <code>sizeof(T)*8</code>, <code>T</code> must be either a standard integral
type (C&#43;&#43;std, [basic.fundamental]) or an <code>enum</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="buffers_members">Members</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">endian_buffer</span><span class="p">()</span> <span class="k">noexcept</span> <span class="o">=</span> <span class="k">default</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>Constructs an uninitialized object.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">explicit</span> <span class="n">endian_buffer</span><span class="p">(</span><span class="n">T</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>endian_store&lt;T, Nbits/8, Order&gt;( value_, v )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">endian_buffer</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span><span class="n">T</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p><code>endian_store&lt;T, Nbits/8, Order&gt;( value_, v )</code>.</p>
</dd>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>*this</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">value_type</span> <span class="n">value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>endian_load&lt;T, Nbits/8, Order&gt;( value_ )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">unsigned</span> <span class="kt">char</span><span class="o">*</span> <span class="n">data</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span><span class="o">*</span> <span class="n">data</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p>A pointer to the first byte of <code>value_</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="buffers_non_member_functions">Non-member functions</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">,</span> <span class="n">order</span> <span class="n">Order</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">,</span>
  <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n_bits</span><span class="p">,</span> <span class="n">align</span> <span class="n">Align</span><span class="p">&gt;</span>
<span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">Order</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">n_bits</span><span class="p">,</span> <span class="n">Align</span><span class="o">&gt;&amp;</span> <span class="n">x</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>os &lt;&lt; x.value()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">,</span> <span class="n">order</span> <span class="n">Order</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">,</span>
  <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n_bits</span><span class="p">,</span> <span class="n">align</span> <span class="n">A</span><span class="p">&gt;</span>
<span class="n">std</span><span class="o">::</span><span class="n">basic_istream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="k">operator</span><span class="o">&gt;&gt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">basic_istream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">is</span><span class="p">,</span>
  <span class="n">endian_buffer</span><span class="o">&lt;</span><span class="n">Order</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">n_bits</span><span class="p">,</span> <span class="n">Align</span><span class="o">&gt;&amp;</span> <span class="n">x</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">T</span> <span class="n">i</span><span class="p">;</span>
<span class="k">if</span> <span class="p">(</span><span class="n">is</span> <span class="o">&gt;&gt;</span> <span class="n">i</span><span class="p">)</span>
  <span class="n">x</span> <span class="o">=</span> <span class="n">i</span><span class="p">;</span></code></pre>
</div>
</div>
</dd>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>is</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect2">
<h3 id="buffers_faq">FAQ</h3>
<div class="paragraph">
<p>See the <a href="#overview_faq">Overview FAQ</a> for a library-wide FAQ.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Why not just use Boost.Serialization?</dt>
<dd>
<p>Serialization involves a conversion for every object involved in I/O. Endian
integers require no conversion or copying. They are already in the desired
format for binary I/O. Thus they can be read or written in bulk.</p>
</dd>
<dt class="hdlist1">Are endian types PODs?</dt>
<dd>
<p>Yes for C&#43;&#43;11. No for C&#43;&#43;03, although several
<a href="#buffers_compilation">macros</a> are available to force PODness in all cases.</p>
</dd>
<dt class="hdlist1">What are the implications of endian integer types not being PODs with C&#43;&#43;03 compilers?</dt>
<dd>
<p>They can&#8217;t be used in unions. Also, compilers aren&#8217;t required to align or lay
out storage in portable ways, although this potential problem hasn&#8217;t prevented
use of Boost.Endian with real compilers.</p>
</dd>
<dt class="hdlist1">What good is native endianness?</dt>
<dd>
<p>It  provides alignment and size guarantees not available from the built-in
types. It eases generic  programming.</p>
</dd>
<dt class="hdlist1">Why bother with the aligned endian types?</dt>
<dd>
<p>Aligned integer operations may be faster (as much as 10 to 20 times faster) if
the endianness and alignment of  the type matches the endianness and alignment
requirements of the machine. The code, however, is likely to be somewhat less
portable than with the unaligned types.</p>
</dd>
</dl>
</div>
</div>
<div class="sect2">
<h3 id="buffers_design_considerations_for_boost_endian_buffers">Design considerations for Boost.Endian buffers</h3>
<div class="ulist">
<ul>
<li>
<p>Must be suitable for I/O - in other words, must be memcpyable.</p>
</li>
<li>
<p>Must provide exactly the size and internal byte ordering specified.</p>
</li>
<li>
<p>Must work correctly when the internal integer representation has more bits
that the sum of the bits in the external byte representation. Sign extension
must work correctly when the internal integer representation type has more
bits than the sum of the bits in the external bytes. For example, using
a 64-bit integer internally to represent 40-bit (5 byte) numbers must work for
both positive and negative values.</p>
</li>
<li>
<p>Must work correctly (including using the same defined external
representation) regardless of whether a compiler treats char as signed or
unsigned.</p>
</li>
<li>
<p>Unaligned types must not cause compilers to insert padding bytes.</p>
</li>
<li>
<p>The implementation should supply optimizations with great care. Experience
has shown that optimizations of endian integers often become pessimizations
when changing  machines or compilers. Pessimizations can also happen when
changing compiler switches, compiler versions, or CPU models of the same
architecture.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="buffers_c11">C&#43;&#43;11</h3>
<div class="paragraph">
<p>The availability of the C&#43;&#43;11
<a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2346.htm">Defaulted
Functions</a> feature is detected automatically, and will be used if present to
ensure that objects of <code>class endian_buffer</code> are trivial, and thus
PODs.</p>
</div>
</div>
<div class="sect2">
<h3 id="buffers_compilation">Compilation</h3>
<div class="paragraph">
<p>Boost.Endian is implemented entirely within headers, with no need to link to
any Boost object libraries.</p>
</div>
<div class="paragraph">
<p>Several macros allow user control over features:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>BOOST_ENDIAN_NO_CTORS</code> causes <code>class endian_buffer</code> to have no
constructors. The intended use is for compiling user code that must be
portable between compilers regardless of C&#43;&#43;11
<a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2346.htm">Defaulted
Functions</a> support. Use of constructors will always fail,</p>
</li>
<li>
<p><code>BOOST_ENDIAN_FORCE_PODNESS</code> causes <code>BOOST_ENDIAN_NO_CTORS</code> to be defined if
the compiler does not support C&#43;&#43;11
<a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2346.htm">Defaulted
Functions</a>. This is ensures that objects of <code>class endian_buffer</code> are PODs, and
so can be used in C&#43;&#43;03 unions. In C&#43;&#43;11, <code>class endian_buffer</code> objects are
PODs, even though they have constructors, so can always be used in unions.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="arithmetic">Endian Arithmetic Types</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="arithmetic_introduction">Introduction</h3>
<div class="paragraph">
<p>Header <code>boost/endian/arithmetic.hpp</code> provides integer binary types with
control over byte order, value type, size, and alignment. Typedefs provide
easy-to-use names for common configurations.</p>
</div>
<div class="paragraph">
<p>These types provide portable byte-holders for integer data, independent of
particular computer architectures. Use cases almost always involve I/O, either
via files or network connections. Although data portability is the primary
motivation, these integer byte-holders may also be used to reduce memory use,
file size, or network activity since they provide binary integer sizes not
otherwise available.</p>
</div>
<div class="paragraph">
<p>Such integer byte-holder types are traditionally called <strong>endian</strong> types. See the
<a href="http://en.wikipedia.org/wiki/Endian">Wikipedia</a> for a full exploration of
<strong>endianness</strong>, including definitions of <strong>big endian</strong> and <strong>little endian</strong>.</p>
</div>
<div class="paragraph">
<p>Boost endian integers provide the same full set of C&#43;&#43; assignment, arithmetic,
and relational operators as C&#43;&#43; standard integral types, with the standard
semantics.</p>
</div>
<div class="paragraph">
<p>Unary arithmetic operators are <code>+</code>, <code>-</code>,  <code>~</code>, <code>!</code>, plus both prefix and postfix
<code>--</code> and <code>++</code>. Binary arithmetic operators are <code>+</code>, <code>+=</code>, <code>-</code>, <code>-=</code>, <code>*</code>,
<code>*=</code>, <code>/</code>, <code>/=</code>, <code>&amp;</code>, <code>&amp;=</code>, <code>|</code>, <code>|=</code>, <code>^</code>, <code>^=</code>, <code>&lt;&lt;</code>, <code>&lt;&lt;=</code>, <code>&gt;&gt;</code>, and
<code>&gt;&gt;=</code>. Binary relational operators are <code>==</code>, <code>!=</code>, <code>&lt;</code>, <code>&lt;=</code>, <code>&gt;</code>, and <code>&gt;=</code>.</p>
</div>
<div class="paragraph">
<p>Implicit conversion to the underlying value type is provided. An implicit
constructor converting from the underlying value type is provided.</p>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_example">Example</h3>
<div class="paragraph">
<p>The <code>endian_example.cpp</code> program writes a binary file containing four-byte,
big-endian and little-endian integers:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#include &lt;iostream&gt;
#include &lt;cstdio&gt;
#include &lt;boost/endian/arithmetic.hpp&gt;
#include &lt;boost/static_assert.hpp&gt;
</span>
<span class="k">using</span> <span class="k">namespace</span> <span class="n">boost</span><span class="o">::</span><span class="n">endian</span><span class="p">;</span>

<span class="k">namespace</span>
<span class="p">{</span>
  <span class="c1">//  This is an extract from a very widely used GIS file format.</span>
  <span class="c1">//  Why the designer decided to mix big and little endians in</span>
  <span class="c1">//  the same file is not known. But this is a real-world format</span>
  <span class="c1">//  and users wishing to write low level code manipulating these</span>
  <span class="c1">//  files have to deal with the mixed endianness.</span>

  <span class="k">struct</span> <span class="nc">header</span>
  <span class="p">{</span>
    <span class="n">big_int32_t</span>     <span class="n">file_code</span><span class="p">;</span>
    <span class="n">big_int32_t</span>     <span class="n">file_length</span><span class="p">;</span>
    <span class="n">little_int32_t</span>  <span class="n">version</span><span class="p">;</span>
    <span class="n">little_int32_t</span>  <span class="n">shape_type</span><span class="p">;</span>
  <span class="p">};</span>

  <span class="k">const</span> <span class="kt">char</span><span class="o">*</span> <span class="n">filename</span> <span class="o">=</span> <span class="s">"test.dat"</span><span class="p">;</span>
<span class="p">}</span>

<span class="kt">int</span> <span class="nf">main</span><span class="p">(</span><span class="kt">int</span><span class="p">,</span> <span class="kt">char</span><span class="o">*</span> <span class="p">[])</span>
<span class="p">{</span>
  <span class="n">header</span> <span class="n">h</span><span class="p">;</span>

  <span class="n">BOOST_STATIC_ASSERT</span><span class="p">(</span><span class="k">sizeof</span><span class="p">(</span><span class="n">h</span><span class="p">)</span> <span class="o">==</span> <span class="mi">16U</span><span class="p">);</span>  <span class="c1">// reality check</span>

  <span class="n">h</span><span class="p">.</span><span class="n">file_code</span>   <span class="o">=</span> <span class="mh">0x01020304</span><span class="p">;</span>
  <span class="n">h</span><span class="p">.</span><span class="n">file_length</span> <span class="o">=</span> <span class="k">sizeof</span><span class="p">(</span><span class="n">header</span><span class="p">);</span>
  <span class="n">h</span><span class="p">.</span><span class="n">version</span>     <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="n">h</span><span class="p">.</span><span class="n">shape_type</span>  <span class="o">=</span> <span class="mh">0x01020304</span><span class="p">;</span>

  <span class="c1">//  Low-level I/O such as POSIX read/write or &lt;cstdio&gt;</span>
  <span class="c1">//  fread/fwrite is sometimes used for binary file operations</span>
  <span class="c1">//  when ultimate efficiency is important. Such I/O is often</span>
  <span class="c1">//  performed in some C++ wrapper class, but to drive home the</span>
  <span class="c1">//  point that endian integers are often used in fairly</span>
  <span class="c1">//  low-level code that does bulk I/O operations, &lt;cstdio&gt;</span>
  <span class="c1">//  fopen/fwrite is used for I/O in this example.</span>

  <span class="n">std</span><span class="o">::</span><span class="kt">FILE</span><span class="o">*</span> <span class="n">fi</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">fopen</span><span class="p">(</span><span class="n">filename</span><span class="p">,</span> <span class="s">"wb"</span><span class="p">);</span>  <span class="c1">// MUST BE BINARY</span>

  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="n">fi</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"could not open "</span> <span class="o">&lt;&lt;</span> <span class="n">filename</span> <span class="o">&lt;&lt;</span> <span class="sc">'\n'</span><span class="p">;</span>
    <span class="k">return</span> <span class="mi">1</span><span class="p">;</span>
  <span class="p">}</span>

  <span class="k">if</span> <span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">fwrite</span><span class="p">(</span><span class="o">&amp;</span><span class="n">h</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">(</span><span class="n">header</span><span class="p">),</span> <span class="mi">1</span><span class="p">,</span> <span class="n">fi</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"write failure for "</span> <span class="o">&lt;&lt;</span> <span class="n">filename</span> <span class="o">&lt;&lt;</span> <span class="sc">'\n'</span><span class="p">;</span>
    <span class="k">return</span> <span class="mi">1</span><span class="p">;</span>
  <span class="p">}</span>

  <span class="n">std</span><span class="o">::</span><span class="n">fclose</span><span class="p">(</span><span class="n">fi</span><span class="p">);</span>

  <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"created file "</span> <span class="o">&lt;&lt;</span> <span class="n">filename</span> <span class="o">&lt;&lt;</span> <span class="sc">'\n'</span><span class="p">;</span>

  <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>After compiling and executing <code>endian_example.cpp</code>, a hex dump of <code>test.dat</code>
shows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="mo">01020304</span> <span class="mo">00000010</span> <span class="mo">01000000</span> <span class="mo">04030201</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Notice that the first two 32-bit integers are big endian while the second two
are little endian, even though the machine this was compiled and run on was
little endian.</p>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_limitations">Limitations</h3>
<div class="paragraph">
<p>Requires <code>&lt;climits&gt;</code>, <code>CHAR_BIT == 8</code>. If <code>CHAR_BIT</code> is some other value,
compilation will result in an <code>#error</code>. This restriction is in place because the
design, implementation, testing, and documentation has only considered issues
related to 8-bit bytes, and there have been no real-world use cases presented
for other sizes.</p>
</div>
<div class="paragraph">
<p>In C&#43;&#43;03, <code>endian_arithmetic</code> does not meet the requirements for POD types
because it has constructors, private data members, and a base class. This means
that common use cases are relying on unspecified behavior in that the C&#43;&#43;
Standard does not guarantee memory layout for non-POD types. This has not been a
problem in practice since all known C&#43;&#43; compilers  lay out memory as if
<code>endian</code> were a POD type. In C&#43;&#43;11, it is possible to specify the default
constructor as trivial, and private data members and base classes  no longer
disqualify a type from being a POD type. Thus under C&#43;&#43;11, <code>endian_arithmetic</code>
will no longer be relying on unspecified behavior.</p>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_feature_set">Feature set</h3>
<div class="ulist">
<ul>
<li>
<p>Big endian| little endian | native endian byte ordering.</p>
</li>
<li>
<p>Signed | unsigned</p>
</li>
<li>
<p>Unaligned | aligned</p>
</li>
<li>
<p>1-8 byte (unaligned) | 1, 2, 4, 8 byte (aligned)</p>
</li>
<li>
<p>Choice of value type</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_enums_and_typedefs">Enums and typedefs</h3>
<div class="paragraph">
<p>Two scoped enums are provided:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">enum</span> <span class="k">class</span> <span class="nc">order</span> <span class="p">{</span> <span class="n">big</span><span class="p">,</span> <span class="n">little</span><span class="p">,</span> <span class="n">native</span> <span class="p">};</span>

<span class="k">enum</span> <span class="k">class</span> <span class="nc">align</span> <span class="p">{</span> <span class="n">no</span><span class="p">,</span> <span class="n">yes</span> <span class="p">};</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>One class template is provided:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="n">order</span> <span class="n">Order</span><span class="p">,</span> <span class="k">typename</span> <span class="nc">T</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n_bits</span><span class="p">,</span>
  <span class="n">align</span> <span class="n">Align</span> <span class="o">=</span> <span class="n">align</span><span class="o">::</span><span class="n">no</span><span class="p">&gt;</span>
<span class="k">class</span> <span class="nc">endian_arithmetic</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Typedefs, such as <code>big_int32_t</code>, provide convenient naming conventions for
common use cases:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 20%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Name</th>
<th class="tableblock halign-left valign-top">Alignment</th>
<th class="tableblock halign-left valign-top">Endianness</th>
<th class="tableblock halign-left valign-top">Sign</th>
<th class="tableblock halign-left valign-top">Sizes in bits (n)</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>big_intN_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">big</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>big_uintN_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">big</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>little_intN_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">little</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>little_uintN_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">little</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>native_intN_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">native</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>native_uintN_t</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">no</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">native</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,24,32,40,48,56,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>big_intN_at</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">big</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,32,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>big_uintN_at</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">big</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,32,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>little_intN_at</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">little</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">signed</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,32,64</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>little_uintN_at</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">yes</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">little</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">unsigned</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8,16,32,64</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>The unaligned types do not cause compilers to insert padding bytes in classes
and structs. This is an important characteristic that can be exploited to
minimize wasted space in memory, files, and network transmissions.</p>
</div>
<div class="admonitionblock caution">
<table>
<tr>
<td class="icon">
<div class="title">Caution</div>
</td>
<td class="content">
Code that uses aligned types is possibly non-portable because
alignment requirements vary between hardware architectures and because
alignment may be affected by compiler switches or pragmas. For example,
alignment of an 64-bit integer may be to a 32-bit boundary on a 32-bit machine.
Furthermore, aligned types are only available on architectures with 8, 16, 32,
and 64-bit integer types.
</td>
</tr>
</table>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<div class="title">Tip</div>
</td>
<td class="content">
Prefer unaligned arithmetic types.
</td>
</tr>
</table>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<div class="title">Tip</div>
</td>
<td class="content">
Protect yourself against alignment ills. For example:
</td>
</tr>
</table>
</div>
<div class="dlist none">
<dl>
<dt></dt>
<dd>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">static_assert</span><span class="p">(</span><span class="k">sizeof</span><span class="p">(</span><span class="n">containing_struct</span><span class="p">)</span> <span class="o">==</span> <span class="mi">12</span><span class="p">,</span> <span class="s">"sizeof(containing_struct) is wrong"</span><span class="p">);</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
One-byte arithmetic types have identical layout on all platforms, so they
never actually reverse endianness. They are provided to enable generic code,
and to improve code readability and searchability.
</td>
</tr>
</table>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_class_template_endian_arithmetic">Class template <code>endian_arithmetic</code></h3>
<div class="paragraph">
<p><code>endian_arithmetic</code> is an integer byte-holder with user-specified endianness,
value type, size, and alignment. The usual operations on arithmetic types are
supplied.</p>
</div>
<div class="sect3">
<h4 id="arithmetic_synopsis">Synopsis</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#include &lt;boost/endian/buffers.hpp&gt;
</span>
<span class="k">namespace</span> <span class="n">boost</span>
<span class="p">{</span>
  <span class="k">namespace</span> <span class="n">endian</span>
  <span class="p">{</span>
    <span class="c1">//  C++11 features emulated if not available</span>

    <span class="k">enum</span> <span class="k">class</span> <span class="nc">align</span> <span class="p">{</span> <span class="n">no</span><span class="p">,</span> <span class="n">yes</span> <span class="p">};</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="n">order</span> <span class="n">Order</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n_bits</span><span class="p">,</span>
      <span class="n">align</span> <span class="n">Align</span> <span class="o">=</span> <span class="n">align</span><span class="o">::</span><span class="n">no</span><span class="p">&gt;</span>
    <span class="k">class</span> <span class="nc">endian_arithmetic</span>
    <span class="p">{</span>
    <span class="nl">public:</span>

      <span class="k">typedef</span> <span class="n">T</span> <span class="n">value_type</span><span class="p">;</span>

      <span class="c1">// if BOOST_ENDIAN_FORCE_PODNESS is defined &amp;&amp; C++11 PODs are not</span>
      <span class="c1">// available then these two constructors will not be present</span>
      <span class="n">endian_arithmetic</span><span class="p">()</span> <span class="k">noexcept</span> <span class="o">=</span> <span class="k">default</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="p">(</span><span class="n">T</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span><span class="n">T</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="k">operator</span> <span class="n">value_type</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">value_type</span> <span class="n">value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="kt">unsigned</span> <span class="kt">char</span><span class="o">*</span> <span class="n">data</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span><span class="o">*</span> <span class="n">data</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

      <span class="c1">// arithmetic operations</span>
      <span class="c1">//   note that additional operations are provided by the value_type</span>
      <span class="n">value_type</span> <span class="k">operator</span><span class="o">+</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">+=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">-=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">*=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">/=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">%=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">&amp;=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">|=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">^=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">&lt;&lt;=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">&gt;&gt;=</span><span class="p">(</span><span class="n">value_type</span> <span class="n">y</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">++</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">--</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span> <span class="k">operator</span><span class="o">++</span><span class="p">(</span><span class="kt">int</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
      <span class="n">endian_arithmetic</span> <span class="k">operator</span><span class="o">--</span><span class="p">(</span><span class="kt">int</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

      <span class="c1">// Stream inserter</span>
      <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">&gt;</span>
      <span class="k">friend</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
        <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="k">const</span> <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">);</span>

      <span class="c1">// Stream extractor</span>
      <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">&gt;</span>
      <span class="k">friend</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_istream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
        <span class="k">operator</span><span class="o">&gt;&gt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">basic_istream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">is</span><span class="p">,</span> <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">);</span>
    <span class="p">};</span>

    <span class="c1">// typedefs</span>

    <span class="c1">// unaligned big endian signed integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>        <span class="n">big_int8_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>      <span class="n">big_int16_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>      <span class="n">big_int24_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>      <span class="n">big_int32_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>      <span class="n">big_int40_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>      <span class="n">big_int48_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>      <span class="n">big_int56_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>      <span class="n">big_int64_t</span><span class="p">;</span>

    <span class="c1">// unaligned big endian unsigned integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>       <span class="n">big_uint8_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>     <span class="n">big_uint16_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>     <span class="n">big_uint24_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>     <span class="n">big_uint32_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>     <span class="n">big_uint40_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>     <span class="n">big_uint48_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>     <span class="n">big_uint56_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>     <span class="n">big_uint64_t</span><span class="p">;</span>

    <span class="c1">// unaligned big endian floating point types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>              <span class="n">big_float32_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>             <span class="n">big_float64_t</span><span class="p">;</span>

    <span class="c1">// unaligned little endian signed integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>     <span class="n">little_int8_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>   <span class="n">little_int16_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>   <span class="n">little_int24_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>   <span class="n">little_int32_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>   <span class="n">little_int40_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>   <span class="n">little_int48_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>   <span class="n">little_int56_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>   <span class="n">little_int64_t</span><span class="p">;</span>

    <span class="c1">// unaligned little endian unsigned integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>    <span class="n">little_uint8_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>  <span class="n">little_uint16_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>  <span class="n">little_uint24_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>  <span class="n">little_uint32_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>  <span class="n">little_uint40_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>  <span class="n">little_uint48_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>  <span class="n">little_uint56_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>  <span class="n">little_uint64_t</span><span class="p">;</span>

    <span class="c1">// unaligned little endian floating point types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>           <span class="n">little_float32_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>          <span class="n">little_float64_t</span><span class="p">;</span>

    <span class="c1">// unaligned native endian signed integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>     <span class="n">native_int8_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>   <span class="n">native_int16_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>   <span class="n">native_int24_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>   <span class="n">native_int32_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>   <span class="n">native_int40_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>   <span class="n">native_int48_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>   <span class="n">native_int56_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">int_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>   <span class="n">native_int64_t</span><span class="p">;</span>

    <span class="c1">// unaligned native endian unsigned integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least8_t</span><span class="p">,</span> <span class="mi">8</span><span class="o">&gt;</span>    <span class="n">native_uint8_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least16_t</span><span class="p">,</span> <span class="mi">16</span><span class="o">&gt;</span>  <span class="n">native_uint16_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">24</span><span class="o">&gt;</span>  <span class="n">native_uint24_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least32_t</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>  <span class="n">native_uint32_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">40</span><span class="o">&gt;</span>  <span class="n">native_uint40_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">48</span><span class="o">&gt;</span>  <span class="n">native_uint48_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">56</span><span class="o">&gt;</span>  <span class="n">native_uint56_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">uint_least64_t</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>  <span class="n">native_uint64_t</span><span class="p">;</span>

    <span class="c1">// unaligned native endian floating point types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="o">&gt;</span>           <span class="n">native_float32_t</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">native</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="o">&gt;</span>          <span class="n">native_float64_t</span><span class="p">;</span>

    <span class="c1">// aligned big endian signed integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int8_t</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>       <span class="n">big_int8_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int16_t</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>     <span class="n">big_int16_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int32_t</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>     <span class="n">big_int32_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">int64_t</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>     <span class="n">big_int64_at</span><span class="p">;</span>

    <span class="c1">// aligned big endian unsigned integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint8_t</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>      <span class="n">big_uint8_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint16_t</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">big_uint16_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint32_t</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">big_uint32_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">uint64_t</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">big_uint64_at</span><span class="p">;</span>

    <span class="c1">// aligned big endian floating point types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>       <span class="n">big_float32_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">big</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>      <span class="n">big_float64_at</span><span class="p">;</span>

    <span class="c1">// aligned little endian signed integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int8_t</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">little_int8_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int16_t</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>  <span class="n">little_int16_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int32_t</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>  <span class="n">little_int32_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">int64_t</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>  <span class="n">little_int64_at</span><span class="p">;</span>

    <span class="c1">// aligned little endian unsigned integer types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint8_t</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>   <span class="n">little_uint8_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint16_t</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span> <span class="n">little_uint16_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint32_t</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span> <span class="n">little_uint32_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">uint64_t</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span> <span class="n">little_uint64_at</span><span class="p">;</span>

    <span class="c1">// aligned little endian floating point types</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>    <span class="n">little_float32_at</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">endian_arithmetic</span><span class="o">&lt;</span><span class="n">order</span><span class="o">::</span><span class="n">little</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="n">align</span><span class="o">::</span><span class="n">yes</span><span class="o">&gt;</span>   <span class="n">little_float64_at</span><span class="p">;</span>

    <span class="c1">// aligned native endian typedefs are not provided because</span>
    <span class="c1">// &lt;cstdint&gt; types are superior for that use case</span>

  <span class="p">}</span> <span class="c1">// namespace endian</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The only supported value of <code>CHAR_BIT</code> is 8.</p>
</div>
<div class="paragraph">
<p>The valid values of <code>Nbits</code> are as follows:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>When <code>sizeof(T)</code> is 1, <code>Nbits</code> shall be 8;</p>
</li>
<li>
<p>When <code>sizeof(T)</code> is 2, <code>Nbits</code> shall be 16;</p>
</li>
<li>
<p>When <code>sizeof(T)</code> is 4, <code>Nbits</code> shall be 24 or 32;</p>
</li>
<li>
<p>When <code>sizeof(T)</code> is 8, <code>Nbits</code> shall be 40, 48, 56, or 64.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Other values of <code>sizeof(T)</code> are not supported.</p>
</div>
<div class="paragraph">
<p>When <code>Nbits</code> is equal to <code>sizeof(T)*8</code>, <code>T</code> must be a standard arithmetic type.</p>
</div>
<div class="paragraph">
<p>When <code>Nbits</code> is less than <code>sizeof(T)*8</code>, <code>T</code> must be a standard integral type
(C&#43;&#43;std, [basic.fundamental]) that is not <code>bool</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="arithmetic_members">Members</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">endian_arithmetic</span><span class="p">()</span> <span class="k">noexcept</span> <span class="o">=</span> <span class="k">default</span><span class="p">;</span>  <span class="c1">// C++03: endian(){}</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>Constructs an uninitialized object.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">endian_arithmetic</span><span class="p">(</span><span class="n">T</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>See <code>endian_buffer::endian_buffer(T)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span><span class="n">T</span> <span class="n">v</span><span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>See <code>endian_buffer::operator=(T)</code>.</p>
</dd>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>*this</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">value_type</span> <span class="n">value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p>See <code>endian_buffer::value()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">unsigned</span> <span class="kt">char</span><span class="o">*</span> <span class="n">data</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">unsigned</span> <span class="kt">char</span> <span class="k">const</span><span class="o">*</span> <span class="n">data</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p>See <code>endian_buffer::data()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">operator</span> <span class="n">T</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>value()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="arithmetic_other_operators">Other operators</h4>
<div class="paragraph">
<p>Other operators on endian objects are forwarded to the equivalent operator on
<code>value_type</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="arithmetic_stream_inserter">Stream inserter</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">&gt;</span>
<span class="k">friend</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
  <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="k">const</span> <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>os &lt;&lt; +x</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="arithmetic_stream_extractor">Stream extractor</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">&gt;</span>
<span class="k">friend</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_istream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
  <span class="k">operator</span><span class="o">&gt;&gt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">basic_istream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">is</span><span class="p">,</span> <span class="n">endian_arithmetic</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects</dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">T</span> <span class="n">i</span><span class="p">;</span>
<span class="k">if</span> <span class="p">(</span><span class="n">is</span> <span class="o">&gt;&gt;</span> <span class="n">i</span><span class="p">)</span>
  <span class="n">x</span> <span class="o">=</span> <span class="n">i</span><span class="p">;</span></code></pre>
</div>
</div>
</dd>
<dt class="hdlist1">Returns</dt>
<dd>
<p><code>is</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_faq">FAQ</h3>
<div class="paragraph">
<p>See the <a href="#overview_faq">Overview FAQ</a> for a library-wide FAQ.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Why not just use Boost.Serialization?</dt>
<dd>
<p>Serialization involves a conversion for every object involved in I/O. Endian
integers require no conversion or copying. They are already in the desired
format for binary I/O. Thus they can be read or written in bulk.</p>
</dd>
<dt class="hdlist1">Are endian types PODs?</dt>
<dd>
<p>Yes for C&#43;&#43;11. No for C&#43;&#43;03, although several
<a href="#arithmetic_compilation">macros</a> are available to force PODness in all cases.</p>
</dd>
<dt class="hdlist1">What are the implications of endian integer types not being PODs with C&#43;&#43;03 compilers?</dt>
<dd>
<p>They can&#8217;t be used in unions. Also, compilers aren&#8217;t required to align or lay
out storage in portable ways, although this potential problem hasn&#8217;t prevented
use of Boost.Endian with real compilers.</p>
</dd>
<dt class="hdlist1">What good is native endianness?</dt>
<dd>
<p>It  provides alignment and size guarantees not available from the built-in
types. It eases generic programming.</p>
</dd>
<dt class="hdlist1">Why bother with the aligned endian types?</dt>
<dd>
<p>Aligned integer operations may be faster (as much as 10 to 20 times faster)
if the endianness and alignment of the type matches the endianness and
alignment requirements of the machine. The code, however, will be somewhat less
portable than with the unaligned types.</p>
</dd>
<dt class="hdlist1">Why provide the arithmetic operations?</dt>
<dd>
<p>Providing a full set of operations reduces program clutter and makes code
both easier to write and to read. Consider incrementing a variable in a record.
It is very convenient to write:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="o">++</span><span class="n">record</span><span class="p">.</span><span class="n">foo</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Rather than:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">int</span> <span class="nf">temp</span><span class="p">(</span><span class="n">record</span><span class="p">.</span><span class="n">foo</span><span class="p">);</span>
<span class="o">++</span><span class="n">temp</span><span class="p">;</span>
<span class="n">record</span><span class="p">.</span><span class="n">foo</span> <span class="o">=</span> <span class="n">temp</span><span class="p">;</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_design_considerations_for_boost_endian_types">Design considerations for Boost.Endian types</h3>
<div class="ulist">
<ul>
<li>
<p>Must be suitable for I/O - in other words, must be memcpyable.</p>
</li>
<li>
<p>Must provide exactly the size and internal byte ordering specified.</p>
</li>
<li>
<p>Must work correctly when the internal integer representation has more bits
that the sum of the bits in the external byte representation. Sign extension
must work correctly when the internal integer representation type has more
bits than the sum of the bits in the external bytes. For example, using
a 64-bit integer internally to represent 40-bit (5 byte) numbers must work for
both positive and negative values.</p>
</li>
<li>
<p>Must work correctly (including using the same defined external
representation) regardless of whether a compiler treats char as signed or
unsigned.</p>
</li>
<li>
<p>Unaligned types must not cause compilers to insert padding bytes.</p>
</li>
<li>
<p>The implementation should supply optimizations with great care. Experience
has shown that optimizations of endian integers often become pessimizations
when changing machines or compilers. Pessimizations can also happen when
changing compiler switches, compiler versions, or CPU models of the same
architecture.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_experience">Experience</h3>
<div class="paragraph">
<p>Classes with similar functionality have been independently developed by
several Boost programmers and used very successful in high-value, high-use
applications for many years. These independently developed endian libraries
often evolved from C libraries that were also widely used. Endian types have
proven widely useful across a wide range of computer architectures and
applications.</p>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_motivating_use_cases">Motivating use cases</h3>
<div class="paragraph">
<p>Neil Mayhew writes: "I can also provide a meaningful use-case for this
library: reading TrueType font files from disk and processing the contents. The
data format has fixed endianness (big) and has unaligned values in various
places. Using Boost.Endian simplifies and cleans the code wonderfully."</p>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_c11">C&#43;&#43;11</h3>
<div class="paragraph">
<p>The availability of the C&#43;&#43;11
<a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2346.htm">Defaulted
Functions</a> feature is detected automatically, and will be used if present to
ensure that objects of <code>class endian_arithmetic</code> are trivial, and thus PODs.</p>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_compilation">Compilation</h3>
<div class="paragraph">
<p>Boost.Endian is implemented entirely within headers, with no need to link to any
Boost object libraries.</p>
</div>
<div class="paragraph">
<p>Several macros allow user control over features:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>BOOST_ENDIAN_NO_CTORS causes <code>class endian_arithmetic</code> to have no
constructors. The intended use is for compiling user code that must be portable
between compilers regardless of C&#43;&#43;11
<a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2346.htm">Defaulted
Functions</a> support. Use of constructors will always fail,</p>
</li>
<li>
<p>BOOST_ENDIAN_FORCE_PODNESS causes BOOST_ENDIAN_NO_CTORS to be defined if
the compiler does not support C&#43;&#43;11
<a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2346.htm">Defaulted
Functions</a>. This is ensures that objects of <code>class endian_arithmetic</code> are PODs,
and so can be used in C&#43;&#43;03 unions. In C&#43;&#43;11, <code>class endian_arithmetic</code>
objects are PODs, even though they have constructors, so can always be used in
unions.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="arithmetic_acknowledgements">Acknowledgements</h3>
<div class="paragraph">
<p>Original design developed by Darin Adler based on classes developed by Mark
Borgerding. Four original class templates combined into a single
<code>endian_arithmetic</code> class template by Beman Dawes, who put the library together,
provided documentation,  added the typedefs, and also added the
<code>unrolled_byte_loops</code> sign partial specialization to correctly extend the sign
when cover integer size differs from endian representation size.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="appendix_history">Appendix A: History and Acknowledgments</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="apph_history">History</h3>
<div class="sect3">
<h4 id="apph_changes_requested_by_formal_review">Changes requested by formal review</h4>
<div class="paragraph">
<p>The library was reworked from top to bottom to accommodate changes requested
during the formal review. The issues that were required to be resolved before
a mini-review are shown in <strong>bold</strong> below, with the resolution indicated.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Common use case scenarios should be developed.</dt>
<dd>
<p>Done. The documentation have been refactored. A page is now devoted to
<a href="#choosing">Choosing the Approach</a> to endianness. See
<a href="#choosing_use_cases">Use cases</a> for use case scenarios.</p>
</dd>
<dt class="hdlist1">Example programs should be developed for the common use case scenarios.</dt>
<dd>
<p>Done. See <a href="#choosing">Choosing the Approach</a>. Example code has been added
throughout.</p>
</dd>
<dt class="hdlist1">Documentation should illuminate the differences between endian integer/float type and endian conversion approaches to the common use case scenarios, and provide guidelines for choosing the most appropriate approach in user&#8217;s applications.</dt>
<dd>
<p>Done. See <a href="#choosing">Choosing the Approach</a>.</p>
</dd>
<dt class="hdlist1">Conversion functions supplying results via return should be provided.</dt>
<dd>
<p>Done. See <a href="#conversion">Conversion Functions</a>.</p>
</dd>
<dt class="hdlist1">Platform specific performance enhancements such as use of compiler intrinsics or relaxed alignment requirements should be supported.</dt>
<dd>
<p>Done. Compiler (Clang, GCC, VisualC&#43;&#43;, etc.) intrinsics and built-in
functions are used in the implementation where appropriate, as requested. See
<a href="#overview_intrinsic">Built-in support for Intrinsics</a>. See
<a href="#overview_timings">Timings for Example 2</a> to gauge the impact of intrinsics.</p>
</dd>
<dt class="hdlist1">Endian integer (and floating) types should be implemented via the conversion functions. If that can&#8217;t be done efficiently, consideration should be given to expanding the conversion function signatures to  resolve the inefficiencies.</dt>
<dd>
<p>Done. For the endian types, the implementation uses the endian conversion
functions, and thus the intrinsics, as requested.</p>
</dd>
<dt class="hdlist1">Benchmarks that measure performance should be provided. It should be possible to compare platform specific performance enhancements against portable base implementations, and to compare endian integer approaches against endian conversion approaches for the common use case scenarios.</dt>
<dd>
<p>Done. See <a href="#overview_timings">Timings for Example 2</a>. The <code>endian/test</code>
directory  also contains several additional benchmark and speed test programs.</p>
</dd>
<dt class="hdlist1">Float (32-bits) and double (64-bits) should be supported. IEEE 754 is the primary use case.</dt>
<dd>
<p>Done. The <a href="#buffers">endian buffer types</a>,
<a href="#arithmetic">endian arithmetic types</a> and
<a href="#conversion">endian conversion functions</a> now support 32-bit <code>(float)</code>
and 64-bit <code>(double)</code> floating point, as requested.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This answer is outdated. The support for <code>float</code> and <code>double</code> was subsequently found
      problematic and has been removed. Recently, support for <code>float</code> and <code>double</code> has
      been reinstated for <code>endian_buffer</code> and <code>endian_arithmetic</code>, but not for the
      conversion functions.
</td>
</tr>
</table>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Support for user defined types (UDTs) is desirable, and should be provided where there would be no conflict with the other concerns.</dt>
<dd>
<p>Done. See <a href="#conversion_customization">Customization points for user-defined
types (UDTs)</a>.</p>
</dd>
<dt class="hdlist1">There is some concern that endian integer/float arithmetic operations might used inadvertently or inappropriately. The impact of adding an endian_buffer class without arithmetic operations should be investigated.</dt>
<dd>
<p>Done. The endian types have been decomposed into class template
<code><a href="#buffers">endian_buffer</a></code> and class template
<code><a href="#arithmetic">endian_arithmetic</a></code>. Class <code>endian_buffer</code> is a public base
class for <code>endian_arithmetic</code>, and can also be used by users as a stand-alone
class.</p>
</dd>
<dt class="hdlist1">Stream insertion and extraction of the endian integer/float types should be documented and included in the test coverage.</dt>
<dd>
<p>Done. See <a href="#buffers_stream_inserter">Stream inserter</a> and
<a href="#buffers_stream_extractor">Stream extractor</a>.</p>
</dd>
<dt class="hdlist1">Binary I/O support that was investigated during development of the Endian library should be put up for mini-review for inclusion in the Boost I/O library.</dt>
<dd>
<p>Not done yet. Will be handled as a separate min-review soon after the Endian
mini-review.</p>
</dd>
<dt class="hdlist1">Other requested changes.</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>In addition to the named-endianness conversion functions, functions that
perform compile-time (via template) and run-time (via function argument)
dispatch are now provided.</p>
</li>
<li>
<p><code>order::native</code> is now a synonym for <code>order::big</code> or <code>order::little</code> according
to the endianness of the platform. This reduces the number of template
specializations required.</p>
</li>
<li>
<p>Headers have been reorganized to make them easier to read, with a synopsis
at the front and implementation following.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</div>
<div class="sect3">
<h4 id="apph_other_changes_since_formal_review">Other changes since formal review</h4>
<div class="ulist">
<ul>
<li>
<p>Header <code>boost/endian/endian.hpp</code> has been renamed to
<code>boost/endian/arithmetic.hpp</code>. Headers
<code>boost/endian/conversion.hpp</code> and <code>boost/endian/buffers.hpp</code> have been added.
Infrastructure file names were changed accordingly.</p>
</li>
<li>
<p>The endian arithmetic type aliases have been renamed, using a naming pattern
that is consistent for both integer and floating point, and a consistent set of
aliases supplied for the endian buffer types.</p>
</li>
<li>
<p>The unaligned-type alias names still have the <code>_t</code> suffix, but the
aligned-type alias names now have an <code>_at</code> suffix.</p>
</li>
<li>
<p><code>endian_reverse()</code> overloads for <code>int8_t</code> and <code>uint8_t</code> have been added for
improved generality. (Pierre Talbot)</p>
</li>
<li>
<p>Overloads of <code>endian_reverse_inplace()</code> have been replaced with a single
<code>endian_reverse_inplace()</code> template. (Pierre Talbot)</p>
</li>
<li>
<p>For X86 and X64 architectures, which permit unaligned loads and stores,
unaligned little endian buffer and arithmetic types use regular loads and
stores when the size is exact. This makes unaligned little endian buffer and
arithmetic types significantly more efficient on these architectures. (Jeremy
Maitin-Shepard)</p>
</li>
<li>
<p>C&#43;&#43;11 features affecting interfaces, such as <code>noexcept</code>, are now used.
C&#43;&#43;03 compilers are still supported.</p>
</li>
<li>
<p>Acknowledgements have been updated.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect2">
<h3 id="apph_compatibility_with_interim_releases">Compatibility with interim releases</h3>
<div class="paragraph">
<p>Prior to the official Boost release, class template <code>endian_arithmetic</code> has been
used for a decade or more with the same functionality but under the name
<code>endian</code>. Other names also changed in the official release. If the macro
<code>BOOST_ENDIAN_DEPRECATED_NAMES</code> is defined, those old now deprecated names are
still supported. However, the class template <code>endian</code> name is only provided for
compilers supporting C&#43;&#43;11 template aliases. For C&#43;&#43;03 compilers, the name
will have to be changed to <code>endian_arithmetic</code>.</p>
</div>
<div class="paragraph">
<p>To support backward header compatibility, deprecated header
<code>boost/endian/endian.hpp</code> forwards to <code>boost/endian/arithmetic.hpp</code>. It requires
<code>BOOST_ENDIAN_DEPRECATED_NAMES</code> be defined. It should only be used while
transitioning to the official Boost release of the library as it will be removed
in some future release.</p>
</div>
</div>
<div class="sect2">
<h3 id="apph_future_directions">Future directions</h3>
<div class="dlist">
<dl>
<dt class="hdlist1">Standardization.</dt>
<dd>
<p>The plan is to submit Boost.Endian to the C&#43;&#43; standards committee for possible
inclusion in a Technical Specification or the C&#43;&#43; standard itself.</p>
</dd>
<dt class="hdlist1">Specializations for <code>numeric_limits</code>.</dt>
<dd>
<p>Roger Leigh requested that all <code>boost::endian</code> types provide <code>numeric_limits</code>
specializations.
See <a href="https://github.com/boostorg/endian/issues/4">GitHub issue 4</a>.</p>
</dd>
<dt class="hdlist1">Character buffer support.</dt>
<dd>
<p>Peter Dimov pointed out during the mini-review that getting and setting basic
arithmetic types (or <code>&lt;cstdint&gt;</code> equivalents) from/to an offset into an array of
unsigned char is a common need. See
<a href="http://lists.boost.org/Archives/boost/2015/01/219574.php">Boost.Endian
mini-review posting</a>.</p>
</dd>
<dt class="hdlist1">Out-of-range detection.</dt>
<dd>
<p>Peter Dimov pointed suggested during the mini-review that throwing an exception
on buffer values being out-of-range might be desirable. See the end of
<a href="http://lists.boost.org/Archives/boost/2015/01/219659.php">this posting</a> and
subsequent replies.</p>
</dd>
</dl>
</div>
</div>
<div class="sect2">
<h3 id="apph_acknowledgements">Acknowledgements</h3>
<div class="paragraph">
<p>Comments and suggestions were received from Adder, Benaka Moorthi, Christopher
Kohlhoff, Cliff Green, Daniel James, Dave Handley, Gennaro Proto, Giovanni Piero
Deretta, Gordon Woodhull, dizzy, Hartmut Kaiser, Howard Hinnant, Jason Newton,
Jeff Flinn, Jeremy Maitin-Shepard, John Filo, John Maddock, Kim Barrett, Marsh
Ray, Martin Bonner, Mathias Gaunard, Matias Capeletto, Neil Mayhew, Nevin Liber,
Olaf van der Spek, Paul Bristow, Peter Dimov, Pierre Talbot, Phil Endecott,
Philip Bennefall, Pyry Jahkola, Rene Rivera, Robert Stewart, Roger Leigh, Roland
Schwarz, Scott McMurray, Sebastian Redl, Tim Blechmann, Tim Moore, tymofey,
Tomas Puverle, Vincente Botet, Yuval Ronen and Vitaly Budovsk. Apologies if
anyone has been missed.</p>
</div>
<div class="paragraph">
<p>The documentation was converted into Asciidoc format by Glen Fernandes.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="apph_copyright_and_license">Appendix B: Copyright and License</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This documentation is</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Copyright 2011-2016 Beman Dawes</p>
</li>
<li>
<p>Copyright 2019 Peter Dimov</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>and is distributed under the <a href="http://www.boost.org/LICENSE_1_0.txt">Boost Software License, Version 1.0</a>.</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2023-04-10 13:47:33 UTC
</div>
</div>
<style>

*:not(pre)>code { background: none; color: #600000; }
:not(pre):not([class^=L])>code { background: none; color: #600000; }
table tr.even, table tr.alt, table tr:nth-of-type(even) { background: none; }

</style>
</body>
</html>