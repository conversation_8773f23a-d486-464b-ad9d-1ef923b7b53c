<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Reference</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.Histogram">
<link rel="up" href="../index.html" title="Chapter 1. Boost.Histogram">
<link rel="prev" href="concepts.html" title="Concepts">
<link rel="next" href="../boost/histogram/axis/null_type.html" title="Struct null_type">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="concepts.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/histogram/axis/null_type.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="histogram.reference"></a>Reference</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#header.boost.histogram_hpp">Header &lt;boost/histogram.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.accumulators_hpp">Header &lt;boost/histogram/accumulators.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.algorithm_hpp">Header &lt;boost/histogram/algorithm.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis_hpp">Header &lt;boost/histogram/axis.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.fwd_hpp">Header &lt;boost/histogram/fwd.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.histogram_hpp">Header &lt;boost/histogram/histogram.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.indexed_hpp">Header &lt;boost/histogram/indexed.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.literals_hpp">Header &lt;boost/histogram/literals.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.make_histogram_hpp">Header &lt;boost/histogram/make_histogram.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.make_profile_hpp">Header &lt;boost/histogram/make_profile.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.multi_index_hpp">Header &lt;boost/histogram/multi_index.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.ostream_hpp">Header &lt;boost/histogram/ostream.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.sample_hpp">Header &lt;boost/histogram/sample.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.serialization_hpp">Header &lt;boost/histogram/serialization.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.storage_adaptor_hpp">Header &lt;boost/histogram/storage_adaptor.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.unlimited_storage_hpp">Header &lt;boost/histogram/unlimited_storage.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.unsafe_access_hpp">Header &lt;boost/histogram/unsafe_access.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.weight_hpp">Header &lt;boost/histogram/weight.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.accumulators.count_hpp">Header &lt;boost/histogram/accumulators/count.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.accumulators.fraction_hpp">Header &lt;boost/histogram/accumulators/fraction.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.accumulators.is_thread_safe_hpp">Header &lt;boost/histogram/accumulators/is_thread_safe.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.accumulators.mean_hpp">Header &lt;boost/histogram/accumulators/mean.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.accumulators.ostream_hpp">Header &lt;boost/histogram/accumulators/ostream.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.accumulators.sum_hpp">Header &lt;boost/histogram/accumulators/sum.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.accumulators.weighted_mean_hpp">Header &lt;boost/histogram/accumulators/weighted_mean.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.accumulators.weighted_sum_hpp">Header &lt;boost/histogram/accumulators/weighted_sum.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.algorithm.empty_hpp">Header &lt;boost/histogram/algorithm/empty.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.algorithm.project_hpp">Header &lt;boost/histogram/algorithm/project.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.algorithm.reduce_hpp">Header &lt;boost/histogram/algorithm/reduce.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.algorithm.sum_hpp">Header &lt;boost/histogram/algorithm/sum.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.boolean_hpp">Header &lt;boost/histogram/axis/boolean.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.category_hpp">Header &lt;boost/histogram/axis/category.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.integer_hpp">Header &lt;boost/histogram/axis/integer.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.interval_view_hpp">Header &lt;boost/histogram/axis/interval_view.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.iterator_hpp">Header &lt;boost/histogram/axis/iterator.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.metadata_base_hpp">Header &lt;boost/histogram/axis/metadata_base.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.option_hpp">Header &lt;boost/histogram/axis/option.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.ostream_hpp">Header &lt;boost/histogram/axis/ostream.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.polymorphic_bin_hpp">Header &lt;boost/histogram/axis/polymorphic_bin.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.regular_hpp">Header &lt;boost/histogram/axis/regular.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.traits_hpp">Header &lt;boost/histogram/axis/traits.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.variable_hpp">Header &lt;boost/histogram/axis/variable.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.axis.variant_hpp">Header &lt;boost/histogram/axis/variant.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.utility.binomial_proportion_interval_hpp">Header &lt;boost/histogram/utility/binomial_proportion_interval.hpp&gt;</a></span></dt>
<dd><dl></dl></dd>
<dt><span class="section"><a href="reference.html#header.boost.histogram.utility.clopper_pearson_interval_hpp">Header &lt;boost/histogram/utility/clopper_pearson_interval.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.utility.jeffreys_interval_hpp">Header &lt;boost/histogram/utility/jeffreys_interval.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.utility.wald_interval_hpp">Header &lt;boost/histogram/utility/wald_interval.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.histogram.utility.wilson_interval_hpp">Header &lt;boost/histogram/utility/wilson_interval.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram_hpp"></a>Header &lt;<a href="../../../../../boost/histogram.hpp" target="_top">boost/histogram.hpp</a>&gt;</h3></div></div></div>
<p>Includes all standard headers of the Boost.Histogram library. </p>
<p>Extra headers not automatically included are:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p><a href="reference.html#header.boost.histogram.ostream_hpp" target="_top">boost/histogram/ostream.hpp</a></p></li>
<li class="listitem"><p><a href="reference.html#header.boost.histogram.axis.ostream_hpp" target="_top">boost/histogram/axis/ostream.hpp</a></p></li>
<li class="listitem"><p><a href="reference.html#header.boost.histogram.accumulators.ostream_hpp" target="_top">boost/histogram/accumulators/ostream.hpp</a></p></li>
<li class="listitem"><p><a href="reference.html#header.boost.histogram.serialization_hpp" target="_top">boost/histogram/serialization.hpp</a></p></li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.accumulators_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/accumulators.hpp" target="_top">boost/histogram/accumulators.hpp</a>&gt;</h3></div></div></div>
<p>Includes all accumulator headers of the Boost.Histogram library. </p>
<p>Extra header not automatically included:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><a href="reference.html#header.boost.histogram.accumulators.ostream_hpp" target="_top">boost/histogram/accumulators/ostream.hpp</a></p></li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.algorithm_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/algorithm.hpp" target="_top">boost/histogram/algorithm.hpp</a>&gt;</h3></div></div></div>
<p>Includes all algorithm headers of the Boost.Histogram library. </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis.hpp" target="_top">boost/histogram/axis.hpp</a>&gt;</h3></div></div></div>
<p>Includes all axis headers of the Boost.Histogram library. </p>
<p>Extra header not automatically included:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><a href="reference.html#header.boost.histogram.axis.ostream_hpp" target="_top">boost/histogram/axis/ostream.hpp</a></p></li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.fwd_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/fwd.hpp" target="_top">boost/histogram/fwd.hpp</a>&gt;</h3></div></div></div>
<p>Forward declarations, tag types and type aliases. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <a class="link" href="../boost/histogram/unlimited_storage.html" title="Class template unlimited_storage">unlimited_storage</a><span class="special">&lt;</span><span class="special">&gt;</span> <a name="boost.histogram.default_storage"></a><span class="identifier">default_storage</span><span class="special">;</span>  <span class="comment">// Default storage, optimized for unweighted histograms. </span>
    <span class="keyword">typedef</span> <a class="link" href="../boost/histogram/storage_adaptor.html" title="Class template storage_adaptor">storage_adaptor</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">A</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.histogram.dense_storage"></a><span class="identifier">dense_storage</span><span class="special">;</span>  <span class="comment">// Vector-like storage for fast zero-overhead access to cells. </span>
    <span class="keyword">typedef</span> <span class="identifier">dense_storage</span><span class="special">&lt;</span> <a class="link" href="../boost/histogram/accumulators/mean.html" title="Class template mean">accumulators::mean</a><span class="special">&lt;</span><span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.histogram.profile_storage"></a><span class="identifier">profile_storage</span><span class="special">;</span>  <span class="comment">// Dense storage which tracks means of samples in each cell. </span>

    <span class="keyword">typedef</span> <span class="identifier">dense_storage</span><span class="special">&lt;</span> <a class="link" href="../boost/histogram/accumulators/weighted_sum.html" title="Class template weighted_sum">accumulators::weighted_sum</a><span class="special">&lt;</span><span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.histogram.weight_storage"></a><span class="identifier">weight_storage</span><span class="special">;</span>  <span class="comment">// Dense storage which tracks sums of weights and a variance estimate. </span>
    <span class="keyword">typedef</span> <span class="identifier">dense_storage</span><span class="special">&lt;</span> <a class="link" href="../boost/histogram/accumulators/weighted_mean.html" title="Class template weighted_mean">accumulators::weighted_mean</a><span class="special">&lt;</span><span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.histogram.weighted_profile_storage"></a><span class="identifier">weighted_profile_storage</span><span class="special">;</span>  <span class="comment">// Dense storage which tracks means of weighted samples in each cell. </span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">typedef</span> <a class="link" href="../boost/histogram/axis/null_type.html" title="Struct null_type">null_type</a> <a name="boost.histogram.axis.empty_type"></a><span class="identifier">empty_type</span><span class="special">;</span>  <span class="comment">// Another alias for an empty metadata type. </span>
      <span class="keyword">typedef</span> <span class="keyword">int</span> <a name="boost.histogram.axis.index_type"></a><span class="identifier">index_type</span><span class="special">;</span>  <span class="comment">// Integral type for axis indices. </span>

      <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/null_type.html" title="Struct null_type">null_type</a><span class="special">;</span>

      <span class="keyword">typedef</span> <span class="keyword">double</span> <a name="boost.histogram.axis.real_index_type"></a><span class="identifier">real_index_type</span><span class="special">;</span>  <span class="comment">// Real type for axis indices. </span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">utility</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/utility/clopper_pearson_interval.html" title="Class template clopper_pearson_interval">clopper_pearson_interval</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/utility/jeffreys_interval.html" title="Class template jeffreys_interval">jeffreys_interval</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/utility/wald_interval.html" title="Class template wald_interval">wald_interval</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/utility/wilson_interval.html" title="Class template wilson_interval">wilson_interval</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.histogram_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/histogram.hpp" target="_top">boost/histogram/histogram.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axes<span class="special">,</span> <span class="keyword">typename</span> Storage<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A1<span class="special">,</span> <span class="keyword">typename</span> S1<span class="special">,</span> <span class="keyword">typename</span> A2<span class="special">,</span> <span class="keyword">typename</span> S2<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/operator_idm12003.html" title="Function template operator*"><span class="keyword">operator</span><span class="special">*</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A1</span><span class="special">,</span> <span class="identifier">S1</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A2</span><span class="special">,</span> <span class="identifier">S2</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A<span class="special">,</span> <span class="keyword">typename</span> S<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/operator_idm12019.html" title="Function template operator*"><span class="keyword">operator</span><span class="special">*</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A</span><span class="special">,</span> <span class="identifier">S</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">double</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A<span class="special">,</span> <span class="keyword">typename</span> S<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/operator_idm12032.html" title="Function template operator*"><span class="keyword">operator</span><span class="special">*</span></a><span class="special">(</span><span class="keyword">double</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A</span><span class="special">,</span> <span class="identifier">S</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A1<span class="special">,</span> <span class="keyword">typename</span> S1<span class="special">,</span> <span class="keyword">typename</span> A2<span class="special">,</span> <span class="keyword">typename</span> S2<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/operator_.html" title="Function template operator+"><span class="keyword">operator</span><span class="special">+</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A1</span><span class="special">,</span> <span class="identifier">S1</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A2</span><span class="special">,</span> <span class="identifier">S2</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A1<span class="special">,</span> <span class="keyword">typename</span> S1<span class="special">,</span> <span class="keyword">typename</span> A2<span class="special">,</span> <span class="keyword">typename</span> S2<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/operator-.html" title="Function template operator-"><span class="keyword">operator</span><span class="special">-</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A1</span><span class="special">,</span> <span class="identifier">S1</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A2</span><span class="special">,</span> <span class="identifier">S2</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A1<span class="special">,</span> <span class="keyword">typename</span> S1<span class="special">,</span> <span class="keyword">typename</span> A2<span class="special">,</span> <span class="keyword">typename</span> S2<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/operator/_idm12077.html" title="Function template operator/"><span class="keyword">operator</span><span class="special">/</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A1</span><span class="special">,</span> <span class="identifier">S1</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A2</span><span class="special">,</span> <span class="identifier">S2</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A<span class="special">,</span> <span class="keyword">typename</span> S<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/operator/_idm12093.html" title="Function template operator/"><span class="keyword">operator</span><span class="special">/</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A</span><span class="special">,</span> <span class="identifier">S</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">double</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.indexed_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/indexed.hpp" target="_top">boost/histogram/indexed.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">enum</span> <a class="link" href="../boost/histogram/coverage.html" title="Type coverage">coverage</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Histogram<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/indexed_range.html" title="Class template indexed_range">indexed_range</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Histogram<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/indexed_idm12119.html" title="Function template indexed"><span class="identifier">indexed</span></a><span class="special">(</span><span class="identifier">Histogram</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">coverage</span> <span class="special">=</span> <span class="identifier">coverage</span><span class="special">::</span><span class="identifier">inner</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Histogram<span class="special">,</span> <span class="keyword">typename</span> Iterable<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/indexed_idm12144.html" title="Function template indexed"><span class="identifier">indexed</span></a><span class="special">(</span><span class="identifier">Histogram</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Iterable</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.literals_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/literals.hpp" target="_top">boost/histogram/literals.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">literals</span> <span class="special">{</span>

      <span class="comment">// Suffix operator to generate literal compile-time numbers, 0_c, 12_c, etc. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">char</span><span class="special">...</span> digits<span class="special">&gt;</span> <span class="keyword">auto</span> <a name="boost.histogram.literals.operator_c_idm12379"></a><span class="keyword">operator</span><span class="string">""</span><span class="identifier">_c</span><span class="special">(</span><span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.make_histogram_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/make_histogram.hpp" target="_top">boost/histogram/make_histogram.hpp</a>&gt;</h3></div></div></div>
<p>Collection of factory functions to conveniently create histograms. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Axes<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_histogram_idm12389.html" title="Function template make_histogram"><span class="identifier">make_histogram</span></a><span class="special">(</span><span class="identifier">Axis</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Axes</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterable<span class="special">&gt;</span> <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_histogram_idm12406.html" title="Function template make_histogram"><span class="identifier">make_histogram</span></a><span class="special">(</span><span class="identifier">Iterable</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_histogram_idm12417.html" title="Function template make_histogram"><span class="identifier">make_histogram</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Storage<span class="special">,</span> <span class="keyword">typename</span> Axis<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Axes<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_histogram_wi_idm12432.html" title="Function template make_histogram_with"><span class="identifier">make_histogram_with</span></a><span class="special">(</span><span class="identifier">Storage</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Axis</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Axes</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Storage<span class="special">,</span> <span class="keyword">typename</span> Iterable<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_histogram_wi_idm12454.html" title="Function template make_histogram_with"><span class="identifier">make_histogram_with</span></a><span class="special">(</span><span class="identifier">Storage</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Iterable</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Storage<span class="special">,</span> <span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_histogram_wi_idm12470.html" title="Function template make_histogram_with"><span class="identifier">make_histogram_with</span></a><span class="special">(</span><span class="identifier">Storage</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Axes<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_weighted_his_idm12490.html" title="Function template make_weighted_histogram"><span class="identifier">make_weighted_histogram</span></a><span class="special">(</span><span class="identifier">Axis</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Axes</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterable<span class="special">&gt;</span> <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_weighted_his_idm12507.html" title="Function template make_weighted_histogram"><span class="identifier">make_weighted_histogram</span></a><span class="special">(</span><span class="identifier">Iterable</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_weighted_his_idm12518.html" title="Function template make_weighted_histogram"><span class="identifier">make_weighted_histogram</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.make_profile_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/make_profile.hpp" target="_top">boost/histogram/make_profile.hpp</a>&gt;</h3></div></div></div>
<p>Collection of factory functions to conveniently create profiles. </p>
<p>Profiles are histograms which accept an additional sample and compute the mean of the sample in each cell. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Axes<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_profile_idm12538.html" title="Function template make_profile"><span class="identifier">make_profile</span></a><span class="special">(</span><span class="identifier">Axis</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Axes</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterable<span class="special">&gt;</span> <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_profile_idm12555.html" title="Function template make_profile"><span class="identifier">make_profile</span></a><span class="special">(</span><span class="identifier">Iterable</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_profile_idm12566.html" title="Function template make_profile"><span class="identifier">make_profile</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Axes<span class="special">&gt;</span> 
      <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_weighted_pro_idm12581.html" title="Function template make_weighted_profile"><span class="identifier">make_weighted_profile</span></a><span class="special">(</span><span class="identifier">Axis</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Axes</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterable<span class="special">&gt;</span> <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_weighted_pro_idm12598.html" title="Function template make_weighted_profile"><span class="identifier">make_weighted_profile</span></a><span class="special">(</span><span class="identifier">Iterable</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">auto</span> <a class="link" href="../boost/histogram/make_weighted_pro_idm12609.html" title="Function template make_weighted_profile"><span class="identifier">make_weighted_profile</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.multi_index_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/multi_index.hpp" target="_top">boost/histogram/multi_index.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> Size<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/multi_index.html" title="Struct template multi_index">multi_index</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/multi_index_stati_idm12688.html" title="Struct multi_index&lt;static_cast&lt; std::size_t &gt;(-1)&gt;">multi_index</a><span class="special">&lt;</span><span class="keyword">static_cast</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="special">&gt;</span><span class="special">(</span><span class="special">-</span><span class="number">1</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.ostream_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/ostream.hpp" target="_top">boost/histogram/ostream.hpp</a>&gt;</h3></div></div></div>
<p>A simple streaming operator for the histogram type. </p>
<p>The text representation is rudimentary and not guaranteed to be stable between versions of Boost.Histogram. This header is not included by any other header and must be explicitly included to use the streaming operator.</p>
<p>To use your own, simply include your own implementation instead of this header. </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.sample_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/sample.hpp" target="_top">boost/histogram/sample.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/sample_type.html" title="Struct template sample_type">sample_type</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Ts<span class="special">&gt;</span> <span class="keyword">auto</span> <a class="link" href="../boost/histogram/sample.html" title="Function template sample"><span class="identifier">sample</span></a><span class="special">(</span><span class="identifier">Ts</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.serialization_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/serialization.hpp" target="_top">boost/histogram/serialization.hpp</a>&gt;</h3></div></div></div>
<p>Headers from <a href="https://www.boost.org/doc/libs/develop/libs/serialization/doc/index.html" target="_top">Boost.Serialization</a> needed to serialize STL types that are used internally by the Boost.Histogram classes.</p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.storage_adaptor_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/storage_adaptor.hpp" target="_top">boost/histogram/storage_adaptor.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/storage_adaptor.html" title="Class template storage_adaptor">storage_adaptor</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.unlimited_storage_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/unlimited_storage.hpp" target="_top">boost/histogram/unlimited_storage.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Allocator<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/unlimited_storage.html" title="Class template unlimited_storage">unlimited_storage</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.unsafe_access_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/unsafe_access.hpp" target="_top">boost/histogram/unsafe_access.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/histogram/unsafe_access.html" title="Struct unsafe_access">unsafe_access</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.weight_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/weight.hpp" target="_top">boost/histogram/weight.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/weight_type.html" title="Struct template weight_type">weight_type</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">auto</span> <a class="link" href="../boost/histogram/weight.html" title="Function template weight"><span class="identifier">weight</span></a><span class="special">(</span><span class="identifier">T</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.accumulators.count_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/accumulators/count.hpp" target="_top">boost/histogram/accumulators/count.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">,</span> <span class="keyword">bool</span> ThreadSafe<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/accumulators/count.html" title="Class template count">count</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.accumulators.fraction_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/accumulators/fraction.hpp" target="_top">boost/histogram/accumulators/fraction.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/accumulators/fraction.html" title="Class template fraction">fraction</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.accumulators.is_thread_safe_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/accumulators/is_thread_safe.hpp" target="_top">boost/histogram/accumulators/is_thread_safe.hpp</a>&gt;</h3></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.accumulators.mean_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/accumulators/mean.hpp" target="_top">boost/histogram/accumulators/mean.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/accumulators/mean.html" title="Class template mean">mean</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.accumulators.ostream_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/accumulators/ostream.hpp" target="_top">boost/histogram/accumulators/ostream.hpp</a>&gt;</h3></div></div></div>
<p>Simple streaming operators for the builtin accumulator types. </p>
<p>The text representation is not guaranteed to be stable between versions of Boost.Histogram. This header is only included by <a href="reference.html#header.boost.histogram.ostream_hpp" target="_top">boost/histogram/ostream.hpp</a>. To use your own, include your own implementation instead of this header and do not include<a href="reference.html#header.boost.histogram.ostream_hpp" target="_top">boost/histogram/ostream.hpp</a>.</p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.accumulators.sum_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/accumulators/sum.hpp" target="_top">boost/histogram/accumulators/sum.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/accumulators/sum.html" title="Class template sum">sum</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.accumulators.weighted_mean_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/accumulators/weighted_mean.hpp" target="_top">boost/histogram/accumulators/weighted_mean.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/accumulators/weighted_mean.html" title="Class template weighted_mean">weighted_mean</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.accumulators.weighted_sum_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/accumulators/weighted_sum.hpp" target="_top">boost/histogram/accumulators/weighted_sum.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/accumulators/weighted_sum.html" title="Class template weighted_sum">weighted_sum</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.algorithm.empty_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/algorithm/empty.hpp" target="_top">boost/histogram/algorithm/empty.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">algorithm</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A<span class="special">,</span> <span class="keyword">typename</span> S<span class="special">&gt;</span> 
        <span class="keyword">auto</span> <a class="link" href="../boost/histogram/algorithm/empty.html" title="Function template empty"><span class="identifier">empty</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A</span><span class="special">,</span> <span class="identifier">S</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">coverage</span><span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.algorithm.project_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/algorithm/project.hpp" target="_top">boost/histogram/algorithm/project.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">algorithm</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A<span class="special">,</span> <span class="keyword">typename</span> S<span class="special">,</span> <span class="keyword">unsigned</span> N<span class="special">,</span> <span class="keyword">typename</span><span class="special">...</span> Ns<span class="special">&gt;</span> 
        <span class="keyword">auto</span> <a class="link" href="../boost/histogram/algorithm/project_idm13789.html" title="Function template project"><span class="identifier">project</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A</span><span class="special">,</span> <span class="identifier">S</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                     <span class="identifier">std</span><span class="special">::</span><span class="identifier">integral_constant</span><span class="special">&lt;</span> <span class="keyword">unsigned</span><span class="special">,</span> <span class="identifier">N</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">Ns</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A<span class="special">,</span> <span class="keyword">typename</span> S<span class="special">,</span> <span class="keyword">typename</span> Iterable<span class="special">&gt;</span> 
        <span class="keyword">auto</span> <a class="link" href="../boost/histogram/algorithm/project_idm13808.html" title="Function template project"><span class="identifier">project</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A</span><span class="special">,</span> <span class="identifier">S</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Iterable</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.algorithm.reduce_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/algorithm/reduce.hpp" target="_top">boost/histogram/algorithm/reduce.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">algorithm</span> <span class="special">{</span>
      <span class="keyword">typedef</span> <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a class="link" href="../boost/histogram/algorithm/reduce_command.html" title="Type definition reduce_command"><span class="identifier">reduce_command</span></a><span class="special">;</span>

      <span class="comment">// Whether to behave like <code class="computeroutput">shrink</code> or<code class="computeroutput">crop</code> regarding removed bins.</span>
      <span class="keyword">enum</span> <a name="boost.histogram.algorithm.slice_mode"></a>slice_mode <span class="special">{</span> shrink, crop <span class="special">}</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/crop_idm13826.html" title="Function crop"><span class="identifier">crop</span></a><span class="special">(</span><span class="keyword">unsigned</span><span class="special">,</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">double</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/crop_idm13847.html" title="Function crop"><span class="identifier">crop</span></a><span class="special">(</span><span class="keyword">double</span><span class="special">,</span> <span class="keyword">double</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/crop_and_rebin_idm13865.html" title="Function crop_and_rebin"><span class="identifier">crop_and_rebin</span></a><span class="special">(</span><span class="keyword">unsigned</span><span class="special">,</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">unsigned</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/crop_and_rebin_idm13892.html" title="Function crop_and_rebin"><span class="identifier">crop_and_rebin</span></a><span class="special">(</span><span class="keyword">double</span><span class="special">,</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">unsigned</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/rebin_idm13915.html" title="Function rebin"><span class="identifier">rebin</span></a><span class="special">(</span><span class="keyword">unsigned</span><span class="special">,</span> <span class="keyword">unsigned</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/rebin_idm13931.html" title="Function rebin"><span class="identifier">rebin</span></a><span class="special">(</span><span class="keyword">unsigned</span><span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Histogram<span class="special">,</span> <span class="keyword">typename</span> Iterable<span class="special">&gt;</span> 
        <span class="identifier">Histogram</span> <a class="link" href="../boost/histogram/algorithm/reduce_idm13943.html" title="Function template reduce"><span class="identifier">reduce</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Histogram</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Iterable</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Histogram<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Ts<span class="special">&gt;</span> 
        <span class="identifier">Histogram</span> <a class="link" href="../boost/histogram/algorithm/reduce_idm13976.html" title="Function template reduce"><span class="identifier">reduce</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Histogram</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">reduce_command</span> <span class="special">&amp;</span><span class="special">,</span> 
                         <span class="keyword">const</span> <span class="identifier">Ts</span> <span class="special">&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/shrink_idm14017.html" title="Function shrink"><span class="identifier">shrink</span></a><span class="special">(</span><span class="keyword">unsigned</span><span class="special">,</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">double</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/shrink_idm14039.html" title="Function shrink"><span class="identifier">shrink</span></a><span class="special">(</span><span class="keyword">double</span><span class="special">,</span> <span class="keyword">double</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/shrink_and_rebin_idm14057.html" title="Function shrink_and_rebin"><span class="identifier">shrink_and_rebin</span></a><span class="special">(</span><span class="keyword">unsigned</span><span class="special">,</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">unsigned</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> <a class="link" href="../boost/histogram/algorithm/shrink_and_rebin_idm14082.html" title="Function shrink_and_rebin"><span class="identifier">shrink_and_rebin</span></a><span class="special">(</span><span class="keyword">double</span><span class="special">,</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">unsigned</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> 
      <a class="link" href="../boost/histogram/algorithm/slice_idm14105.html" title="Function slice"><span class="identifier">slice</span></a><span class="special">(</span><span class="keyword">unsigned</span><span class="special">,</span> <span class="identifier">axis</span><span class="special">::</span><span class="identifier">index_type</span><span class="special">,</span> <span class="identifier">axis</span><span class="special">::</span><span class="identifier">index_type</span><span class="special">,</span> 
            <span class="identifier">slice_mode</span> <span class="special">=</span> <span class="identifier">slice_mode</span><span class="special">::</span><span class="identifier">shrink</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> 
      <a class="link" href="../boost/histogram/algorithm/slice_idm14134.html" title="Function slice"><span class="identifier">slice</span></a><span class="special">(</span><span class="identifier">axis</span><span class="special">::</span><span class="identifier">index_type</span><span class="special">,</span> <span class="identifier">axis</span><span class="special">::</span><span class="identifier">index_type</span><span class="special">,</span> 
            <span class="identifier">slice_mode</span> <span class="special">=</span> <span class="identifier">slice_mode</span><span class="special">::</span><span class="identifier">shrink</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> 
      <a class="link" href="../boost/histogram/algorithm/slice_and_rebin_idm14159.html" title="Function slice_and_rebin"><span class="identifier">slice_and_rebin</span></a><span class="special">(</span><span class="keyword">unsigned</span><span class="special">,</span> <span class="identifier">axis</span><span class="special">::</span><span class="identifier">index_type</span><span class="special">,</span> <span class="identifier">axis</span><span class="special">::</span><span class="identifier">index_type</span><span class="special">,</span> <span class="keyword">unsigned</span><span class="special">,</span> 
                      <span class="identifier">slice_mode</span> <span class="special">=</span> <span class="identifier">slice_mode</span><span class="special">::</span><span class="identifier">shrink</span><span class="special">)</span><span class="special">;</span>
      <span class="identifier">reduce_command</span> 
      <a class="link" href="../boost/histogram/algorithm/slice_and_rebin_idm14191.html" title="Function slice_and_rebin"><span class="identifier">slice_and_rebin</span></a><span class="special">(</span><span class="identifier">axis</span><span class="special">::</span><span class="identifier">index_type</span><span class="special">,</span> <span class="identifier">axis</span><span class="special">::</span><span class="identifier">index_type</span><span class="special">,</span> <span class="keyword">unsigned</span><span class="special">,</span> 
                      <span class="identifier">slice_mode</span> <span class="special">=</span> <span class="identifier">slice_mode</span><span class="special">::</span><span class="identifier">shrink</span><span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.algorithm.sum_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/algorithm/sum.hpp" target="_top">boost/histogram/algorithm/sum.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">algorithm</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> A<span class="special">,</span> <span class="keyword">typename</span> S<span class="special">&gt;</span> 
        <span class="keyword">auto</span> <a class="link" href="../boost/histogram/algorithm/sum.html" title="Function template sum"><span class="identifier">sum</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/histogram.html" title="Class template histogram">histogram</a><span class="special">&lt;</span> <span class="identifier">A</span><span class="special">,</span> <span class="identifier">S</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">coverage</span> <span class="special">=</span> <span class="identifier">coverage</span><span class="special">::</span><span class="identifier">all</span><span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.boolean_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/boolean.hpp" target="_top">boost/histogram/axis/boolean.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> MetaData<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/boolean.html" title="Class template boolean">boolean</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.category_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/category.hpp" target="_top">boost/histogram/axis/category.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Value<span class="special">,</span> <span class="keyword">typename</span> MetaData<span class="special">,</span> <span class="keyword">typename</span> Options<span class="special">,</span> 
               <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
        <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/category.html" title="Class template category">category</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.integer_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/integer.hpp" target="_top">boost/histogram/axis/integer.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Value<span class="special">,</span> <span class="keyword">typename</span> MetaData<span class="special">,</span> <span class="keyword">typename</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/integer.html" title="Class template integer">integer</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.interval_view_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/interval_view.hpp" target="_top">boost/histogram/axis/interval_view.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/interval_view.html" title="Class template interval_view">interval_view</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.iterator_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/iterator.hpp" target="_top">boost/histogram/axis/iterator.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Derived<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/iterator_mixin.html" title="Class template iterator_mixin">iterator_mixin</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.metadata_base_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/metadata_base.hpp" target="_top">boost/histogram/axis/metadata_base.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Metadata<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/metadata_base.html" title="Class template metadata_base">metadata_base</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.option_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/option.hpp" target="_top">boost/histogram/axis/option.hpp</a>&gt;</h3></div></div></div>
<p>Options for builtin axis types. </p>
<p>Options <code class="computeroutput">circular</code> and<code class="computeroutput">growth</code> are mutually exclusive. Options<code class="computeroutput">circular</code> and<code class="computeroutput">underflow</code> are mutually exclusive.</p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">namespace</span> <span class="identifier">option</span> <span class="special">{</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">unsigned</span> Pos<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/option/bit.html" title="Struct template bit">bit</a><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">unsigned</span> Bits<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/option/bitset.html" title="Struct template bitset">bitset</a><span class="special">;</span>

        <span class="keyword">constexpr</span> <span class="identifier">circular_t</span> <a class="link" href="../boost/histogram/axis/option/circular.html" title="Global circular">circular</a><span class="special">;</span>        <span class="comment">// Instance of <code class="computeroutput">circular_t</code>.</span>

        <span class="keyword">typedef</span> <a class="link" href="../boost/histogram/axis/option/bit.html" title="Struct template bit">bit</a><span class="special">&lt;</span> <span class="number">2</span> <span class="special">&gt;</span> <a name="boost.histogram.axis.option.circular_t"></a><span class="identifier">circular_t</span><span class="special">;</span>  <span class="comment">// Axis is circular. Mutually exclusive with <code class="computeroutput">growth</code> and<code class="computeroutput">underflow</code>.</span>

        <span class="keyword">constexpr</span> <span class="identifier">growth_t</span> <a class="link" href="../boost/histogram/axis/option/growth.html" title="Global growth">growth</a><span class="special">;</span>        <span class="comment">// Instance of <code class="computeroutput">growth_t</code>.</span>

        <span class="keyword">typedef</span> <a class="link" href="../boost/histogram/axis/option/bit.html" title="Struct template bit">bit</a><span class="special">&lt;</span> <span class="number">3</span> <span class="special">&gt;</span> <a name="boost.histogram.axis.option.growth_t"></a><span class="identifier">growth_t</span><span class="special">;</span>  <span class="comment">// Axis can grow. Mutually exclusive with <code class="computeroutput">circular</code>.</span>

        <span class="keyword">constexpr</span> <span class="identifier">none_t</span> <a class="link" href="../boost/histogram/axis/option/none.html" title="Global none">none</a><span class="special">;</span>        <span class="comment">// Instance of <code class="computeroutput">none_t</code>.</span>

        <span class="keyword">typedef</span> <a class="link" href="../boost/histogram/axis/option/bitset.html" title="Struct template bitset">bitset</a><span class="special">&lt;</span> <span class="number">0</span> <span class="special">&gt;</span> <a name="boost.histogram.axis.option.none_t"></a><span class="identifier">none_t</span><span class="special">;</span>  <span class="comment">// All options off. </span>

        <span class="keyword">constexpr</span> <span class="identifier">overflow_t</span> <a class="link" href="../boost/histogram/axis/option/overflow.html" title="Global overflow">overflow</a><span class="special">;</span>        <span class="comment">// Instance of <code class="computeroutput">overflow_t</code>.</span>

        <span class="keyword">typedef</span> <a class="link" href="../boost/histogram/axis/option/bit.html" title="Struct template bit">bit</a><span class="special">&lt;</span> <span class="number">1</span> <span class="special">&gt;</span> <a name="boost.histogram.axis.option.overflow_t"></a><span class="identifier">overflow_t</span><span class="special">;</span>  <span class="comment">// Axis has overflow bin. </span>

        <span class="keyword">constexpr</span> <span class="identifier">underflow_t</span> <a class="link" href="../boost/histogram/axis/option/underflow.html" title="Global underflow">underflow</a><span class="special">;</span>        <span class="comment">// Instance of <code class="computeroutput">underflow_t</code>.</span>

        <span class="keyword">typedef</span> <a class="link" href="../boost/histogram/axis/option/bit.html" title="Struct template bit">bit</a><span class="special">&lt;</span> <span class="number">0</span> <span class="special">&gt;</span> <a name="boost.histogram.axis.option.underflow_t"></a><span class="identifier">underflow_t</span><span class="special">;</span>  <span class="comment">// Axis has an underflow bin. Mutually exclusive with <code class="computeroutput">circular</code>.</span>

        <span class="comment">// Set intersection of the option arguments. </span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">unsigned</span> B1<span class="special">,</span> <span class="keyword">unsigned</span> B2<span class="special">&gt;</span> 
          <span class="keyword">constexpr</span> <span class="keyword">auto</span> <a name="boost.histogram.axis.option.operator&amp;"></a><span class="keyword">operator</span><span class="special">&amp;</span><span class="special">(</span><a class="link" href="../boost/histogram/axis/option/bitset.html" title="Struct template bitset">bitset</a><span class="special">&lt;</span> <span class="identifier">B1</span> <span class="special">&gt;</span><span class="special">,</span> <a class="link" href="../boost/histogram/axis/option/bitset.html" title="Struct template bitset">bitset</a><span class="special">&lt;</span> <span class="identifier">B2</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>

        <span class="comment">// Set difference of the option arguments. </span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">unsigned</span> B1<span class="special">,</span> <span class="keyword">unsigned</span> B2<span class="special">&gt;</span> 
          <span class="keyword">constexpr</span> <span class="keyword">auto</span> <a name="boost.histogram.axis.option.operator-"></a><span class="keyword">operator</span><span class="special">-</span><span class="special">(</span><a class="link" href="../boost/histogram/axis/option/bitset.html" title="Struct template bitset">bitset</a><span class="special">&lt;</span> <span class="identifier">B1</span> <span class="special">&gt;</span><span class="special">,</span> <a class="link" href="../boost/histogram/axis/option/bitset.html" title="Struct template bitset">bitset</a><span class="special">&lt;</span> <span class="identifier">B2</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>

        <span class="comment">// Set union of the axis option arguments. </span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">unsigned</span> B1<span class="special">,</span> <span class="keyword">unsigned</span> B2<span class="special">&gt;</span> 
          <span class="keyword">constexpr</span> <span class="keyword">auto</span> <a name="boost.histogram.axis.option.operator_idm14879"></a><span class="keyword">operator</span><span class="special">|</span><span class="special">(</span><a class="link" href="../boost/histogram/axis/option/bitset.html" title="Struct template bitset">bitset</a><span class="special">&lt;</span> <span class="identifier">B1</span> <span class="special">&gt;</span><span class="special">,</span> <a class="link" href="../boost/histogram/axis/option/bitset.html" title="Struct template bitset">bitset</a><span class="special">&lt;</span> <span class="identifier">B2</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
      <span class="special">}</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.ostream_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/ostream.hpp" target="_top">boost/histogram/axis/ostream.hpp</a>&gt;</h3></div></div></div>
<p>Simple streaming operators for the builtin axis types. </p>
<p>The text representation is not guaranteed to be stable between versions of Boost.Histogram. This header is only included by <a href="reference.html#header.boost.histogram.ostream_hpp" target="_top">boost/histogram/ostream.hpp</a>. To use your own, include your own implementation instead of this header and do not include<a href="reference.html#header.boost.histogram.ostream_hpp" target="_top">boost/histogram/ostream.hpp</a>.</p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.polymorphic_bin_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/polymorphic_bin.hpp" target="_top">boost/histogram/axis/polymorphic_bin.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> RealType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/polymorphic_bin.html" title="Class template polymorphic_bin">polymorphic_bin</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.regular_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/regular.hpp" target="_top">boost/histogram/axis/regular.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Value <span class="special">=</span> <span class="keyword">double</span><span class="special">,</span> <span class="keyword">typename</span> MetaData <span class="special">=</span> <span class="identifier">use_default</span><span class="special">,</span> 
               <span class="keyword">typename</span> Options <span class="special">=</span> <span class="identifier">use_default</span><span class="special">&gt;</span> 
        <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/circular.html" title="Class template circular">circular</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Value<span class="special">,</span> <span class="keyword">typename</span> Transform<span class="special">,</span> <span class="keyword">typename</span> MetaData<span class="special">,</span> 
               <span class="keyword">typename</span> Options<span class="special">&gt;</span> 
        <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/regular.html" title="Class template regular">regular</a><span class="special">;</span>

      <span class="comment">// Helper function to mark argument as step size. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="identifier">step_type</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <a name="boost.histogram.axis.step"></a><span class="identifier">step</span><span class="special">(</span><span class="identifier">T</span> t<span class="special">)</span><span class="special">;</span>
      <span class="keyword">namespace</span> <span class="identifier">transform</span> <span class="special">{</span>
        <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/transform/id.html" title="Struct id">id</a><span class="special">;</span>
        <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/transform/log.html" title="Struct log">log</a><span class="special">;</span>
        <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/transform/pow.html" title="Struct pow">pow</a><span class="special">;</span>
        <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/transform/sqrt.html" title="Struct sqrt">sqrt</a><span class="special">;</span>
      <span class="special">}</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.traits_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/traits.hpp" target="_top">boost/histogram/axis/traits.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">namespace</span> <span class="identifier">traits</span> <span class="special">{</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/traits/get_options.html" title="Struct template get_options">get_options</a><span class="special">;</span>

        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/traits/is_continuous.html" title="Struct template is_continuous">is_continuous</a><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/traits/is_inclusive.html" title="Struct template is_inclusive">is_inclusive</a><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/traits/is_ordered.html" title="Struct template is_ordered">is_ordered</a><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/traits/is_reducible.html" title="Struct template is_reducible">is_reducible</a><span class="special">;</span>

        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/histogram/axis/traits/value_type.html" title="Struct template value_type">value_type</a><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">constexpr</span> <span class="keyword">bool</span> <a class="link" href="../boost/histogram/axis/traits/continuous.html" title="Function template continuous"><span class="identifier">continuous</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="identifier">index_type</span> <a class="link" href="../boost/histogram/axis/traits/extent.html" title="Function template extent"><span class="identifier">extent</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">constexpr</span> <span class="keyword">bool</span> <a class="link" href="../boost/histogram/axis/traits/inclusive.html" title="Function template inclusive"><span class="identifier">inclusive</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> 
          <span class="identifier">axis</span><span class="special">::</span><span class="identifier">index_type</span> <a class="link" href="../boost/histogram/axis/traits/index.html" title="Function template index"><span class="identifier">index</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">U</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span> <a class="link" href="../boost/histogram/axis/traits/metadata.html" title="Function template metadata"><span class="identifier">metadata</span></a><span class="special">(</span><span class="identifier">Axis</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">constexpr</span> <span class="keyword">unsigned</span> <a class="link" href="../boost/histogram/axis/traits/options.html" title="Function template options"><span class="identifier">options</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">constexpr</span> <span class="keyword">bool</span> <a class="link" href="../boost/histogram/axis/traits/ordered.html" title="Function template ordered"><span class="identifier">ordered</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">constexpr</span> <span class="keyword">unsigned</span> <span class="keyword">int</span> <a class="link" href="../boost/histogram/axis/traits/rank.html" title="Function template rank"><span class="identifier">rank</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> 
          <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">index_type</span><span class="special">,</span> <span class="identifier">index_type</span> <span class="special">&gt;</span> <a class="link" href="../boost/histogram/axis/traits/update.html" title="Function template update"><span class="identifier">update</span></a><span class="special">(</span><span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">U</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> 
          <span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span> <a class="link" href="../boost/histogram/axis/traits/value.html" title="Function template value"><span class="identifier">value</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">real_index_type</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Result<span class="special">,</span> <span class="keyword">typename</span> Axis<span class="special">&gt;</span> 
          <span class="identifier">Result</span> <a class="link" href="../boost/histogram/axis/traits/value_as.html" title="Function template value_as"><span class="identifier">value_as</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">real_index_type</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Axis<span class="special">&gt;</span> <span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span> <a class="link" href="../boost/histogram/axis/traits/width.html" title="Function template width"><span class="identifier">width</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">index_type</span><span class="special">)</span><span class="special">;</span>
        <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Result<span class="special">,</span> <span class="keyword">typename</span> Axis<span class="special">&gt;</span> 
          <span class="identifier">Result</span> <a class="link" href="../boost/histogram/axis/traits/width_as.html" title="Function template width_as"><span class="identifier">width_as</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Axis</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">index_type</span><span class="special">)</span><span class="special">;</span>
      <span class="special">}</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.variable_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/variable.hpp" target="_top">boost/histogram/axis/variable.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Value<span class="special">,</span> <span class="keyword">typename</span> MetaData<span class="special">,</span> <span class="keyword">typename</span> Options<span class="special">,</span> 
               <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
        <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/variable.html" title="Class template variable">variable</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.axis.variant_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/axis/variant.hpp" target="_top">boost/histogram/axis/variant.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">axis</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Ts<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">;</span>

      <span class="comment">// Return reference to T, throws std::runtime_error if type does not match. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Us<span class="special">&gt;</span> 
        <span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span> <a name="boost.histogram.axis.get_idm15815"></a><span class="identifier">get</span><span class="special">(</span><a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> v<span class="special">)</span><span class="special">;</span>

      <span class="comment">// Return movable reference to T, throws unspecified exception if type does not match. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Us<span class="special">&gt;</span> 
        <span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span> <a name="boost.histogram.axis.get_idm15825"></a><span class="identifier">get</span><span class="special">(</span><a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> v<span class="special">)</span><span class="special">;</span>

      <span class="comment">// Return const reference to T, throws unspecified exception if type does not match. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Us<span class="special">&gt;</span> 
        <span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span> <a name="boost.histogram.axis.get_idm15835"></a><span class="identifier">get</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> v<span class="special">)</span><span class="special">;</span>

      <span class="comment">// Returns pointer to T in variant or null pointer if type does not match. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Us<span class="special">&gt;</span> <span class="keyword">auto</span> <a name="boost.histogram.axis.get_if_idm15845"></a><span class="identifier">get_if</span><span class="special">(</span><a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">*</span> v<span class="special">)</span><span class="special">;</span>

      <span class="comment">// Returns pointer to const T in variant or null pointer if type does not match. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Us<span class="special">&gt;</span> 
        <span class="keyword">auto</span> <a name="boost.histogram.axis.get_if_idm15855"></a><span class="identifier">get_if</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">*</span> v<span class="special">)</span><span class="special">;</span>

      <span class="comment">// The negation of operator==. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Us<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Ts<span class="special">&gt;</span> 
        <span class="keyword">bool</span> <a name="boost.histogram.axis.operator!=_idm15865"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> u<span class="special">,</span> 
                        <span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Ts</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> t<span class="special">)</span><span class="special">;</span>

      <span class="comment">// The negation of operator==. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Us<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
        <span class="keyword">bool</span> <a name="boost.histogram.axis.operator!=_idm15879"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> u<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> t<span class="special">)</span><span class="special">;</span>

      <span class="comment">// The negation of operator==. </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Us<span class="special">&gt;</span> 
        <span class="keyword">bool</span> <a name="boost.histogram.axis.operator!=_idm15891"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> t<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> u<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Us<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Vs<span class="special">&gt;</span> 
        <span class="keyword">bool</span> <a class="link" href="../boost/histogram/axis/operator___idm15903.html" title="Function template operator=="><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Vs</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Us<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
        <span class="keyword">bool</span> <a class="link" href="../boost/histogram/axis/operator___idm15919.html" title="Function template operator=="><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

      <span class="comment">// Apply visitor to variant (reference). </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Visitor<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Us<span class="special">&gt;</span> 
        <span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span> <a name="boost.histogram.axis.visit_idm16043"></a><span class="identifier">visit</span><span class="special">(</span><span class="identifier">Visitor</span> <span class="special">&amp;&amp;</span> vis<span class="special">,</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> var<span class="special">)</span><span class="special">;</span>

      <span class="comment">// Apply visitor to variant (movable reference). </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Visitor<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Us<span class="special">&gt;</span> 
        <span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span> <a name="boost.histogram.axis.visit_idm16055"></a><span class="identifier">visit</span><span class="special">(</span><span class="identifier">Visitor</span> <span class="special">&amp;&amp;</span> vis<span class="special">,</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> var<span class="special">)</span><span class="special">;</span>

      <span class="comment">// Apply visitor to variant (const reference). </span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Visitor<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Us<span class="special">&gt;</span> 
        <span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span> <a name="boost.histogram.axis.visit_idm16067"></a><span class="identifier">visit</span><span class="special">(</span><span class="identifier">Visitor</span> <span class="special">&amp;&amp;</span> vis<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/histogram/axis/variant.html" title="Class template variant">variant</a><span class="special">&lt;</span> <span class="identifier">Us</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> var<span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.utility.binomial_proportion_interval_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/utility/binomial_proportion_interval.hpp" target="_top">boost/histogram/utility/binomial_proportion_interval.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">histogram</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">utility</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/histogram/utility/binomial_proporti_idm16083.html" title="Class template binomial_proportion_interval">binomial_proportion_interval</a><span class="special">;</span>
      <span class="keyword">class</span> <a class="link" href="../boost/histogram/utility/confidence_level.html" title="Class confidence_level">confidence_level</a><span class="special">;</span>
      <span class="keyword">class</span> <a class="link" href="../boost/histogram/utility/deviation.html" title="Class deviation">deviation</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.utility.clopper_pearson_interval_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/utility/clopper_pearson_interval.hpp" target="_top">boost/histogram/utility/clopper_pearson_interval.hpp</a>&gt;</h3></div></div></div></div>
<div class="section"><div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.utility.jeffreys_interval_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/utility/jeffreys_interval.hpp" target="_top">boost/histogram/utility/jeffreys_interval.hpp</a>&gt;</h3></div></div></div></div>
<div class="section"><div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.utility.wald_interval_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/utility/wald_interval.hpp" target="_top">boost/histogram/utility/wald_interval.hpp</a>&gt;</h3></div></div></div></div>
<div class="section"><div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.histogram.utility.wilson_interval_hpp"></a>Header &lt;<a href="../../../../../boost/histogram/utility/wilson_interval.hpp" target="_top">boost/histogram/utility/wilson_interval.hpp</a>&gt;</h3></div></div></div></div>
</div>
<div class="copyright-footer">Copyright © 2016-2019 Hans
      Dembinski<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="concepts.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/histogram/axis/null_type.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
