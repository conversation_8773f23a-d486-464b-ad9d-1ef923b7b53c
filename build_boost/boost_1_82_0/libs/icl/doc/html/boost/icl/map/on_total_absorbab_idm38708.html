<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct template on_total_absorbable&lt;Type, true, true&gt;</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../map.html#idm56561" title="Description">
<link rel="prev" href="on_total_absorbab_idm38690.html" title="Struct template on_total_absorbable&lt;Type, true, false&gt;">
<link rel="next" href="../partial_absorber.html" title="Struct partial_absorber">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="on_total_absorbab_idm38690.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../map.html#idm56561"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../partial_absorber.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.icl.map.on_total_absorbab_idm38708"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct template on_total_absorbable&lt;Type, true, true&gt;</span></h2>
<p>boost::icl::map::on_total_absorbable&lt;Type, true, true&gt;</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/icl/map_hpp.html" title="Header &lt;boost/icl/map.hpp&gt;">boost/icl/map.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
<span class="keyword">struct</span> <a class="link" href="on_total_absorbab_idm38708.html" title="Struct template on_total_absorbable&lt;Type, true, true&gt;">on_total_absorbable</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">true</span><span class="special">,</span> <span class="keyword">true</span><span class="special">&gt;</span> <span class="special">{</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">element_type</span> <a name="boost.icl.map.on_total_absorbab_idm38708.element_type"></a><span class="identifier">element_type</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="on_total_absorbab_idm38708.html#idm38717-bb">public static functions</a></span>
  <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="on_total_absorbab_idm38708.html#idm38718-bb"><span class="identifier">flip</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">typename</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">element_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm58268"></a><h2>Description</h2>
<div class="refsect2">
<a name="idm58270"></a><h3>
<a name="idm38717-bb"></a><code class="computeroutput">on_total_absorbable</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><pre class="literallayout"><span class="keyword">static</span> <span class="keyword">void</span> <a name="idm38718-bb"></a><span class="identifier">flip</span><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span> object<span class="special">,</span> <span class="keyword">const</span> <span class="keyword">typename</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">element_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span></pre></li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="on_total_absorbab_idm38690.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../map.html#idm56561"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../partial_absorber.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
