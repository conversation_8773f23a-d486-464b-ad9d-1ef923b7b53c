<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Miscellaneous Interval Functions</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../function_reference.html" title="Function Reference">
<link rel="prev" href="additional_interval_orderings.html" title="Additional Interval Orderings">
<link rel="next" href="../acknowledgments.html" title="Acknowledgments">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="additional_interval_orderings.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../function_reference.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../acknowledgments.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_icl.function_reference.miscellaneous_interval_functions"></a><a class="link" href="miscellaneous_interval_functions.html" title="Miscellaneous Interval Functions">Miscellaneous
      Interval Functions</a>
</h3></div></div></div>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  T
                </p>
              </th>
<th>
                <p>
                  discrete<br> _interval
                </p>
              </th>
<th>
                <p>
                  continuous<br> _interval
                </p>
              </th>
<th>
                <p>
                  right_open<br> _interval
                </p>
              </th>
<th>
                <p>
                  left_open<br> _interval
                </p>
              </th>
<th>
                <p>
                  closed<br> _interval
                </p>
              </th>
<th>
                <p>
                  open<br> _interval
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Interval bounds
                </p>
              </td>
<td>
                <p>
                  dynamic
                </p>
              </td>
<td>
                <p>
                  dynamic
                </p>
              </td>
<td>
                <p>
                  static
                </p>
              </td>
<td>
                <p>
                  static
                </p>
              </td>
<td>
                <p>
                  static
                </p>
              </td>
<td>
                <p>
                  static
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Form
                </p>
              </td>
<td>
              </td>
<td>
              </td>
<td>
                <p>
                  asymmetric
                </p>
              </td>
<td>
                <p>
                  asymmetric
                </p>
              </td>
<td>
                <p>
                  symmetric
                </p>
              </td>
<td>
                <p>
                  symmetric
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="emphasis"><em><span class="bold"><strong>Miscellaneous</strong></span></em></span>
                </p>
              </td>
<td>
              </td>
<td>
              </td>
<td>
              </td>
<td>
              </td>
<td>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span> <span class="identifier">touches</span><span class="special">(</span><span class="keyword">const</span>
                  <span class="identifier">T</span><span class="special">&amp;,</span>
                  <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">inner_complement</span><span class="special">(</span><span class="keyword">const</span>
                  <span class="identifier">T</span><span class="special">&amp;,</span>
                  <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">difference_type</span> <span class="identifier">distance</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;,</span> <span class="keyword">const</span>
                  <span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
<td>
                <p>
                  1
                </p>
              </td>
</tr>
</tbody>
</table></div>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  <span class="emphasis"><em><span class="bold"><strong>Miscellaneous Interval Functions</strong></span></em></span>
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span> <span class="identifier">touches</span><span class="special">(</span><span class="keyword">const</span>
                  <span class="identifier">T</span><span class="special">&amp;,</span>
                  <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">touches</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span><span class="identifier">y</span><span class="special">)</span></code>
                  Between the disjoint intervals <code class="computeroutput"><span class="identifier">x</span></code>
                  and <code class="computeroutput"><span class="identifier">y</span></code> are no elements.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">inner_complement</span><span class="special">(</span><span class="keyword">const</span>
                  <span class="identifier">T</span><span class="special">&amp;,</span>
                  <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">z</span> <span class="special">=</span>
                  <span class="identifier">inner_complement</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span><span class="identifier">y</span><span class="special">)</span></code>
                  <code class="computeroutput"><span class="identifier">z</span></code> is the interval
                  between <code class="computeroutput"><span class="identifier">x</span></code> and
                  <code class="computeroutput"><span class="identifier">y</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">difference_type</span> <span class="identifier">distance</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;,</span> <span class="keyword">const</span>
                  <span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">d</span> <span class="special">=</span>
                  <span class="identifier">distance</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span><span class="identifier">y</span><span class="special">)</span></code>
                  If the domain type of the interval has a difference_type, <code class="computeroutput"><span class="identifier">d</span></code> is the distance between <code class="computeroutput"><span class="identifier">x</span></code> and <code class="computeroutput"><span class="identifier">y</span></code>.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<p>
        <span class="emphasis"><em><span class="bold"><strong>Back to section . . .</strong></span></em></span>
      </p>
<div class="informaltable"><table class="table">
<colgroup><col></colgroup>
<thead><tr></tr></thead>
<tbody>
<tr><td>
                <p>
                  <a class="link" href="../interface/function_synopsis.html#additional_interval_functions"><span class="emphasis"><em><span class="bold"><strong>Additional interval functions</strong></span></em></span></a>
                </p>
              </td></tr>
<tr><td>
                <p>
                  <a class="link" href="../interface/function_synopsis.html#function_synopsis_table"><span class="emphasis"><em><span class="bold"><strong>Function
                  Synopsis</strong></span></em></span></a>
                </p>
              </td></tr>
<tr><td>
                <p>
                  <a class="link" href="../interface.html" title="Interface"><span class="emphasis"><em><span class="bold"><strong>Interface</strong></span></em></span></a>
                </p>
              </td></tr>
</tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="additional_interval_orderings.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../function_reference.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../acknowledgments.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
