<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Element iteration</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../function_reference.html" title="Function Reference">
<link rel="prev" href="iterator_related.html" title="Iterator related">
<link rel="next" href="streaming__conversion.html" title="Streaming, conversion">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="iterator_related.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../function_reference.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="streaming__conversion.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_icl.function_reference.element_iteration"></a><a class="link" href="element_iteration.html" title="Element iteration">Element
      iteration</a>
</h3></div></div></div>
<p>
        This section refers to <span class="emphasis"><em><span class="bold"><strong>element iteration</strong></span></em></span>
        over <span class="emphasis"><em><span class="bold"><strong>interval containers</strong></span></em></span>.
        Element iterators are available as associated types on interval sets and
        interval maps.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Variant
                </p>
              </th>
<th>
                <p>
                  Associated element iterator type for interval container <code class="computeroutput"><span class="identifier">T</span></code>
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  forward
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="identifier">element_iterator</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  const forward
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="identifier">element_const_iterator</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  reverse
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="identifier">element_reverse_iterator</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  const reverse
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="identifier">element_const_reverse_iterator</span></code>
                </p>
              </td>
</tr>
</tbody>
</table></div>
<p>
        There are also associated iterators types <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="identifier">iterator</span></code>,
        <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="identifier">const_iterator</span></code>, <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="identifier">reverse_iterator</span></code>
        and <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="identifier">reverse_const_iterator</span></code> on interval containers.
        These are <span class="emphasis"><em><span class="bold"><strong>segment iterators</strong></span></em></span>.
        Segment iterators are "first citizen iterators". Iteration over
        segments is fast, compared to an iteration over elements, particularly if
        intervals are large. But if we want to view our interval containers as containers
        of elements that are usable with std::algoritms, we need to iterate over
        elements.
      </p>
<p>
        Iteration over elements . . .
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            is possible only for integral or discrete <code class="computeroutput"><span class="identifier">domain_types</span></code>
          </li>
<li class="listitem">
            can be very <span class="emphasis"><em><span class="bold"><strong>slow</strong></span></em></span>
            if the intervals are very large.
          </li>
<li class="listitem">
            and is therefore <span class="emphasis"><em><span class="bold"><strong>depreciated</strong></span></em></span>
          </li>
</ul></div>
<p>
        On the other hand, sometimes iteration over interval containers on the element
        level might be desired, if you have some interface that works for <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">SortedAssociativeContainers</span></code>
        of elements and you need to quickly use it with an interval container. Accepting
        the poorer performance might be less bothersome at times than adjusting your
        whole interface for segment iteration.
      </p>
<div class="caution"><table border="0" summary="Caution">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Caution]" src="../../../../../../doc/src/images/caution.png"></td>
<th align="left">Caution</th>
</tr>
<tr><td align="left" valign="top"><p>
          So we advice you to choose element iteration over interval containers
          <span class="emphasis"><em><span class="bold"><strong>judiciously</strong></span></em></span>. Do not
          use element iteration <span class="emphasis"><em><span class="bold"><strong>by default or habitual</strong></span></em></span>.
          Always try to achieve results using member functions, global functions
          or operators (preferably inplace versions) or iteration over segments first.
        </p></td></tr>
</table></div>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  <span class="emphasis"><em><span class="bold"><strong>Synopsis Complexities</strong></span></em></span>
                </p>
              </th>
<th>
                <p>
                  interval<br> sets
                </p>
              </th>
<th>
                <p>
                  interval<br> maps
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">J</span> <span class="identifier">elements_begin</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  <span class="emphasis"><em>O(1)</em></span>
                </p>
              </td>
<td>
                <p>
                  <span class="emphasis"><em>O(1)</em></span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">J</span> <span class="identifier">elements_end</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  <span class="emphasis"><em>O(1)</em></span>
                </p>
              </td>
<td>
                <p>
                  <span class="emphasis"><em>O(1)</em></span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">J</span> <span class="identifier">elements_rbegin</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  <span class="emphasis"><em>O(1)</em></span>
                </p>
              </td>
<td>
                <p>
                  <span class="emphasis"><em>O(1)</em></span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">J</span> <span class="identifier">elements_rend</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;)</span></code>
                </p>
              </td>
<td>
                <p>
                  <span class="emphasis"><em>O(1)</em></span>
                </p>
              </td>
<td>
                <p>
                  <span class="emphasis"><em>O(1)</em></span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  <span class="emphasis"><em><span class="bold"><strong>Element iteration</strong></span></em></span>
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
</p>
<pre class="table-programlisting">      <span class="identifier">element_iterator</span> <span class="identifier">elements_begin</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;)</span>
<span class="identifier">element_const_iterator</span> <span class="identifier">elements_begin</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></pre>
<p>
                </p>
              </td>
<td>
                <p>
                  Returns an element iterator to the first element of the container.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
</p>
<pre class="table-programlisting">      <span class="identifier">element_iterator</span> <span class="identifier">elements_end</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;)</span>
<span class="identifier">element_const_iterator</span> <span class="identifier">elements_end</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></pre>
<p>
                </p>
              </td>
<td>
                <p>
                  Returns an element iterator to a position <code class="computeroutput"><span class="identifier">elements_end</span><span class="special">(</span><span class="identifier">c</span><span class="special">)</span></code> after the last element of the
                  container.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
</p>
<pre class="table-programlisting">      <span class="identifier">element_reverse_iterator</span> <span class="identifier">elements_rbegin</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;)</span>
<span class="identifier">element_const_reverse_iterator</span> <span class="identifier">elements_rbegin</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></pre>
<p>
                </p>
              </td>
<td>
                <p>
                  Returns a reverse element iterator to the last element of the container.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
</p>
<pre class="table-programlisting">      <span class="identifier">element_reverse_iterator</span> <span class="identifier">elements_rend</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;)</span>
<span class="identifier">element_const_reverse_iterator</span> <span class="identifier">elements_rend</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></pre>
<p>
                </p>
              </td>
<td>
                <p>
                  Returns a reverse element iterator to a position <code class="computeroutput"><span class="identifier">elements_rend</span><span class="special">(</span><span class="identifier">c</span><span class="special">)</span></code>
                  before the first element of the container.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<p>
        <span class="emphasis"><em><span class="bold"><strong>Example</strong></span></em></span>
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">interval_set</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="identifier">inter_set</span><span class="special">;</span>
<span class="identifier">inter_set</span><span class="special">.</span><span class="identifier">add</span><span class="special">(</span><span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="number">0</span><span class="special">,</span><span class="number">3</span><span class="special">))</span>
         <span class="special">.</span><span class="identifier">add</span><span class="special">(</span><span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="number">7</span><span class="special">,</span><span class="number">9</span><span class="special">));</span>

<span class="keyword">for</span><span class="special">(</span><span class="identifier">interval_set</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">element_const_iterator</span> <span class="identifier">creeper</span> <span class="special">=</span> <span class="identifier">elements_begin</span><span class="special">(</span><span class="identifier">inter_set</span><span class="special">);</span>
    <span class="identifier">creeper</span> <span class="special">!=</span> <span class="identifier">elements_end</span><span class="special">(</span><span class="identifier">inter_set</span><span class="special">);</span> <span class="special">++</span><span class="identifier">creeper</span><span class="special">)</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="special">*</span><span class="identifier">creeper</span> <span class="special">&lt;&lt;</span> <span class="string">" "</span><span class="special">;</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
<span class="comment">//Program output: 0 1 2 7 8</span>

<span class="keyword">for</span><span class="special">(</span><span class="identifier">interval_set</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">element_reverse_iterator</span> <span class="identifier">repeerc</span> <span class="special">=</span> <span class="identifier">elements_rbegin</span><span class="special">(</span><span class="identifier">inter_set</span><span class="special">);</span>
    <span class="identifier">repeerc</span> <span class="special">!=</span> <span class="identifier">elements_rend</span><span class="special">(</span><span class="identifier">inter_set</span><span class="special">);</span> <span class="special">++</span><span class="identifier">repeerc</span><span class="special">)</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="special">*</span><span class="identifier">repeerc</span> <span class="special">&lt;&lt;</span> <span class="string">" "</span><span class="special">;</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
<span class="comment">//Program output: 8 7 2 1 0</span>
</pre>
<p>
      </p>
<p>
        <span class="emphasis"><em><span class="bold"><strong>See also . . .</strong></span></em></span>
      </p>
<div class="informaltable"><table class="table">
<colgroup><col></colgroup>
<thead><tr></tr></thead>
<tbody><tr><td>
                <p>
                  <a class="link" href="iterator_related.html" title="Iterator related"><span class="emphasis"><em><span class="bold"><strong>Segment iteration</strong></span></em></span></a>
                </p>
              </td></tr></tbody>
</table></div>
<p>
        <span class="emphasis"><em><span class="bold"><strong>Back to section . . .</strong></span></em></span>
      </p>
<div class="informaltable"><table class="table">
<colgroup><col></colgroup>
<thead><tr></tr></thead>
<tbody>
<tr><td>
                <p>
                  <a class="link" href="../interface/function_synopsis.html#function_synopsis_table"><span class="emphasis"><em><span class="bold"><strong>Function
                  Synopsis</strong></span></em></span></a>
                </p>
              </td></tr>
<tr><td>
                <p>
                  <a class="link" href="../interface.html" title="Interface"><span class="emphasis"><em><span class="bold"><strong>Interface</strong></span></em></span></a>
                </p>
              </td></tr>
</tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="iterator_related.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../function_reference.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="streaming__conversion.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
