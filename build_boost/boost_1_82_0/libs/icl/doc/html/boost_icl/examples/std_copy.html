<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Std copy</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../examples.html" title="Examples">
<link rel="prev" href="user_groups.html" title="User groups">
<link rel="next" href="std_transform.html" title="Std transform">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="user_groups.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="std_transform.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_icl.examples.std_copy"></a><a class="link" href="std_copy.html" title="Std copy">Std copy</a>
</h3></div></div></div>
<p>
        The standard algorithm <a href="http://www.cplusplus.com/reference/algorithm/copy/" target="_top"><code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">copy</span></code></a>
        can be used to fill interval containers from standard containers of intervals
        or interval value pairs (segments). Because intervals do not represent <span class="emphasis"><em><span class="bold"><strong>elements</strong></span></em></span> but <span class="emphasis"><em><span class="bold"><strong>sets</strong></span></em></span>,
        that can be empty or contain more than one element, the usage of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">copy</span></code>
        differs from what we are familiar with using <span class="emphasis"><em>containers of elements</em></span>.
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Use <code class="computeroutput"><span class="identifier">icl</span><span class="special">::</span><span class="identifier">inserter</span></code> from <code class="computeroutput"><span class="preprocessor">#include</span>
            <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">iterator</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code> instead of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">inserter</span></code>
            to call insertions on the target interval container.
          </li>
<li class="listitem">
            As shown in the examples above and below this point, most of the time
            we will not be interested to <code class="computeroutput"><span class="identifier">insert</span></code>
            segments into <code class="computeroutput"><a class="link" href="../../boost/icl/interval_map.html" title="Class template interval_map">interval_maps</a></code>
            but to <a class="link" href="../function_reference/addition.html" title="Addition"><span class="emphasis"><em><span class="bold"><strong>add</strong></span></em></span></a> them, in order to generate
            the desired aggregation results. You can use <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">copy</span></code>
            with an <code class="computeroutput"><span class="identifier">icl</span><span class="special">::</span><span class="identifier">adder</span></code> instead of an <code class="computeroutput"><span class="identifier">icl</span><span class="special">::</span><span class="identifier">inserter</span></code>
            to achieve this.
          </li>
</ul></div>
<p>
</p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">vector</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">algorithm</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">interval_map</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">;</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">icl</span><span class="special">;</span>

<span class="comment">// 'make_segments' returns a vector of interval value pairs, which</span>
<span class="comment">// are not sorted. The values are taken from the minimal example</span>
<span class="comment">// in section 'interval combining styles'.</span>
<span class="identifier">vector</span><span class="special">&lt;</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;,</span> <span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">make_segments</span><span class="special">()</span>
<span class="special">{</span>
    <span class="identifier">vector</span><span class="special">&lt;</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;,</span> <span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">segment_vec</span><span class="special">;</span>
    <span class="identifier">segment_vec</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">make_pair</span><span class="special">(</span><span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="number">2</span><span class="special">,</span><span class="number">4</span><span class="special">),</span> <span class="number">1</span><span class="special">));</span>
    <span class="identifier">segment_vec</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">make_pair</span><span class="special">(</span><span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="number">4</span><span class="special">,</span><span class="number">5</span><span class="special">),</span> <span class="number">1</span><span class="special">));</span>
    <span class="identifier">segment_vec</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">make_pair</span><span class="special">(</span><span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="number">1</span><span class="special">,</span><span class="number">3</span><span class="special">),</span> <span class="number">1</span><span class="special">));</span>
    <span class="keyword">return</span> <span class="identifier">segment_vec</span><span class="special">;</span>
<span class="special">}</span>

<span class="comment">// 'show_segments' displays the source segements.</span>
<span class="keyword">void</span> <span class="identifier">show_segments</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">vector</span><span class="special">&lt;</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;,</span> <span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;&amp;</span> <span class="identifier">segments</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">vector</span><span class="special">&lt;</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;,</span> <span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">const_iterator</span> <span class="identifier">iter</span> <span class="special">=</span> <span class="identifier">segments</span><span class="special">.</span><span class="identifier">begin</span><span class="special">();</span>
    <span class="keyword">while</span><span class="special">(</span><span class="identifier">iter</span> <span class="special">!=</span> <span class="identifier">segments</span><span class="special">.</span><span class="identifier">end</span><span class="special">())</span>
    <span class="special">{</span>
        <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"("</span> <span class="special">&lt;&lt;</span> <span class="identifier">iter</span><span class="special">-&gt;</span><span class="identifier">first</span> <span class="special">&lt;&lt;</span> <span class="string">","</span> <span class="special">&lt;&lt;</span> <span class="identifier">iter</span><span class="special">-&gt;</span><span class="identifier">second</span> <span class="special">&lt;&lt;</span> <span class="string">")"</span><span class="special">;</span>
        <span class="special">++</span><span class="identifier">iter</span><span class="special">;</span>
    <span class="special">}</span>
<span class="special">}</span>

<span class="keyword">void</span> <span class="identifier">std_copy</span><span class="special">()</span>
<span class="special">{</span>
    <span class="comment">// So we have some segments stored in an std container.</span>
    <span class="identifier">vector</span><span class="special">&lt;</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;,</span> <span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">segments</span> <span class="special">=</span> <span class="identifier">make_segments</span><span class="special">();</span>
    <span class="comment">// Display the input</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"input sequence: "</span><span class="special">;</span> <span class="identifier">show_segments</span><span class="special">(</span><span class="identifier">segments</span><span class="special">);</span> <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"\n\n"</span><span class="special">;</span>

    <span class="comment">// We are going to 'std::copy' those segments into an interval_map:</span>
    <span class="identifier">interval_map</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="identifier">segmap</span><span class="special">;</span>

    <span class="comment">// Use an 'icl::inserter' from &lt;boost/icl/iterator.hpp&gt; to call </span>
    <span class="comment">// insertion on the interval container.</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">copy</span><span class="special">(</span><span class="identifier">segments</span><span class="special">.</span><span class="identifier">begin</span><span class="special">(),</span> <span class="identifier">segments</span><span class="special">.</span><span class="identifier">end</span><span class="special">(),</span>
              <span class="identifier">icl</span><span class="special">::</span><span class="identifier">inserter</span><span class="special">(</span><span class="identifier">segmap</span><span class="special">,</span> <span class="identifier">segmap</span><span class="special">.</span><span class="identifier">end</span><span class="special">()));</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"icl::inserting: "</span> <span class="special">&lt;&lt;</span> <span class="identifier">segmap</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">segmap</span><span class="special">.</span><span class="identifier">clear</span><span class="special">();</span>

    <span class="comment">// When we are feeding data into interval_maps, most of the time we are</span>
    <span class="comment">// intending to compute an aggregation result. So we are not interested</span>
    <span class="comment">// the std::insert semantincs but the aggregating icl::addition semantics.</span>
    <span class="comment">// To achieve this there is an icl::add_iterator and an icl::adder function </span>
    <span class="comment">// provided in &lt;boost/icl/iterator.hpp&gt;.</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">copy</span><span class="special">(</span><span class="identifier">segments</span><span class="special">.</span><span class="identifier">begin</span><span class="special">(),</span> <span class="identifier">segments</span><span class="special">.</span><span class="identifier">end</span><span class="special">(),</span>
              <span class="identifier">icl</span><span class="special">::</span><span class="identifier">adder</span><span class="special">(</span><span class="identifier">segmap</span><span class="special">,</span> <span class="identifier">segmap</span><span class="special">.</span><span class="identifier">end</span><span class="special">()));</span> <span class="comment">//Aggregating associated values</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"icl::adding   : "</span> <span class="special">&lt;&lt;</span> <span class="identifier">segmap</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

    <span class="comment">// In this last case, the semantics of 'std::copy' transforms to the </span>
    <span class="comment">// generalized addition operation, that is implemented by operator</span>
    <span class="comment">// += or + on itl maps and sets.</span>
<span class="special">}</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"&gt;&gt;    Interval Container Library: Example std_copy.cpp    &lt;&lt;\n"</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"-----------------------------------------------------------\n"</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Using std::copy to fill an interval_map:\n\n"</span><span class="special">;</span>

    <span class="identifier">std_copy</span><span class="special">();</span>
    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>

<span class="comment">// Program output:</span>
<span class="comment">/*---------------------------------------------------------
&gt;&gt;    Interval Container Library: Example std_copy.cpp    &lt;&lt;
-----------------------------------------------------------
Using std::copy to fill an interval_map:

input sequence: ([2,4),1)([4,5),1)([1,3),1)

icl::inserting: {([1,5)-&gt;1)}
icl::adding   : {([1,2)-&gt;1)([2,3)-&gt;2)([3,5)-&gt;1)}
---------------------------------------------------------*/</span>
</pre>
<p>
      </p>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="user_groups.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="std_transform.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
