<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Dynamic interval</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../examples.html" title="Examples">
<link rel="prev" href="interval.html" title="Interval">
<link rel="next" href="static_interval.html" title="Static interval">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="interval.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="static_interval.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_icl.examples.dynamic_interval"></a><a class="link" href="dynamic_interval.html" title="Dynamic interval">Dynamic interval</a>
</h3></div></div></div>
<p>
</p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">math</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">type_traits</span><span class="special">/</span><span class="identifier">is_same</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">interval_set</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">split_interval_set</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="comment">// Dynamically bounded intervals 'discrete_interval' and 'continuous_interval'</span>
<span class="comment">// are indirectly included via interval containers as library defaults.</span>
<span class="preprocessor">#include</span> <span class="string">"../toytime.hpp"</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">rational</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">;</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">icl</span><span class="special">;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"&gt;&gt;Interval Container Library: Sample interval.cpp &lt;&lt;\n"</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"----------------------------------------------------\n"</span><span class="special">;</span>

    <span class="comment">// Dynamically bounded intervals are the library default for </span>
    <span class="comment">// interval parameters in interval containers.</span>
    <span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">((</span>
        <span class="identifier">boost</span><span class="special">::</span><span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">interval_type</span>
                      <span class="special">,</span> <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">value</span>
                      <span class="special">));</span>


    <span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">((</span>
        <span class="identifier">boost</span><span class="special">::</span><span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;::</span><span class="identifier">interval_type</span>
                      <span class="special">,</span> <span class="identifier">continuous_interval</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">value</span>
                      <span class="special">));</span>

    <span class="comment">// As we can see the library default chooses the appropriate</span>
    <span class="comment">// class template instance discrete_interval&lt;T&gt; or continuous_interval&lt;T&gt;</span>
    <span class="comment">// dependent on the domain_type T. The library default for intervals</span>
    <span class="comment">// is also available via the template 'interval':</span>
    <span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">((</span>
        <span class="identifier">boost</span><span class="special">::</span><span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">type</span>
                      <span class="special">,</span> <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">value</span>
                      <span class="special">));</span>

    <span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">((</span>
        <span class="identifier">boost</span><span class="special">::</span><span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;::</span><span class="identifier">type</span>
                      <span class="special">,</span> <span class="identifier">continuous_interval</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">value</span>
                      <span class="special">));</span>

    <span class="comment">// template interval also provides static functions for the four border types</span>

    <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">type</span>    <span class="identifier">int_interval</span>  <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">closed</span><span class="special">(</span><span class="number">3</span><span class="special">,</span> <span class="number">7</span><span class="special">);</span>
    <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">sqrt_interval</span> <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="number">1</span><span class="special">/</span><span class="identifier">sqrt</span><span class="special">(</span><span class="number">2.0</span><span class="special">),</span> <span class="identifier">sqrt</span><span class="special">(</span><span class="number">2.0</span><span class="special">));</span>
    <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">city_interval</span> <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;::</span><span class="identifier">left_open</span><span class="special">(</span><span class="string">"Barcelona"</span><span class="special">,</span> <span class="string">"Boston"</span><span class="special">);</span>
    <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">Time</span><span class="special">&gt;::</span><span class="identifier">type</span>   <span class="identifier">time_interval</span> <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">Time</span><span class="special">&gt;::</span><span class="identifier">open</span><span class="special">(</span><span class="identifier">Time</span><span class="special">(</span><span class="identifier">monday</span><span class="special">,</span><span class="number">8</span><span class="special">,</span><span class="number">30</span><span class="special">),</span> <span class="identifier">Time</span><span class="special">(</span><span class="identifier">monday</span><span class="special">,</span><span class="number">17</span><span class="special">,</span><span class="number">20</span><span class="special">));</span>

    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"----- Dynamically bounded intervals ----------------------------------------\n"</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"  discrete_interval&lt;int&gt;   : "</span> <span class="special">&lt;&lt;</span> <span class="identifier">int_interval</span>  <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"continuous_interval&lt;double&gt;: "</span> <span class="special">&lt;&lt;</span> <span class="identifier">sqrt_interval</span> <span class="special">&lt;&lt;</span> <span class="string">" does "</span>
                                            <span class="special">&lt;&lt;</span> <span class="identifier">string</span><span class="special">(</span><span class="identifier">contains</span><span class="special">(</span><span class="identifier">sqrt_interval</span><span class="special">,</span> <span class="identifier">sqrt</span><span class="special">(</span><span class="number">2.0</span><span class="special">))?</span><span class="string">""</span><span class="special">:</span><span class="string">"NOT"</span><span class="special">)</span>
                                            <span class="special">&lt;&lt;</span> <span class="string">" contain sqrt(2)"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"continuous_interval&lt;string&gt;: "</span> <span class="special">&lt;&lt;</span> <span class="identifier">city_interval</span> <span class="special">&lt;&lt;</span> <span class="string">" does "</span>
                                            <span class="special">&lt;&lt;</span> <span class="identifier">string</span><span class="special">(</span><span class="identifier">contains</span><span class="special">(</span><span class="identifier">city_interval</span><span class="special">,</span><span class="string">"Barcelona"</span><span class="special">)?</span><span class="string">""</span><span class="special">:</span><span class="string">"NOT"</span><span class="special">)</span>
                                            <span class="special">&lt;&lt;</span> <span class="string">" contain 'Barcelona'"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"continuous_interval&lt;string&gt;: "</span> <span class="special">&lt;&lt;</span> <span class="identifier">city_interval</span> <span class="special">&lt;&lt;</span> <span class="string">" does "</span>
                                            <span class="special">&lt;&lt;</span> <span class="identifier">string</span><span class="special">(</span><span class="identifier">contains</span><span class="special">(</span><span class="identifier">city_interval</span><span class="special">,</span> <span class="string">"Berlin"</span><span class="special">)?</span><span class="string">""</span><span class="special">:</span><span class="string">"NOT"</span><span class="special">)</span>
                                            <span class="special">&lt;&lt;</span> <span class="string">" contain 'Berlin'"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"  discrete_interval&lt;Time&gt;  : "</span> <span class="special">&lt;&lt;</span> <span class="identifier">time_interval</span> <span class="special">&lt;&lt;</span> <span class="string">"\n\n"</span><span class="special">;</span>

    <span class="comment">// Using dynamically bounded intervals allows to apply operations</span>
    <span class="comment">// with intervals and also with elements on all interval containers </span>
    <span class="comment">// including interval containers of continuous domain types:</span>

    <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">unit_interval</span>
        <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="number">0</span><span class="special">),</span> <span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="number">1</span><span class="special">));</span>
    <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">unit_set</span><span class="special">(</span><span class="identifier">unit_interval</span><span class="special">);</span>
    <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">ratio_set</span><span class="special">(</span><span class="identifier">unit_set</span><span class="special">);</span>
    <span class="identifier">ratio_set</span> <span class="special">-=</span> <span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="number">1</span><span class="special">,</span><span class="number">3</span><span class="special">);</span> <span class="comment">// Subtract 1/3 from the set</span>

    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"----- Manipulation of single values in continuous sets ---------------------\n"</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"1/3 subtracted from [0..1) : "</span> <span class="special">&lt;&lt;</span> <span class="identifier">ratio_set</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"The set does "</span> <span class="special">&lt;&lt;</span> <span class="identifier">string</span><span class="special">(</span><span class="identifier">contains</span><span class="special">(</span><span class="identifier">ratio_set</span><span class="special">,</span> <span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="number">1</span><span class="special">,</span><span class="number">3</span><span class="special">))?</span><span class="string">""</span><span class="special">:</span><span class="string">"NOT"</span><span class="special">)</span>
                                            <span class="special">&lt;&lt;</span> <span class="string">" contain '1/3'"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">ratio_set</span> <span class="special">^=</span> <span class="identifier">unit_set</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Flipping the holey set     : "</span> <span class="special">&lt;&lt;</span> <span class="identifier">ratio_set</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"yields the subtracted      :     1/3\n\n"</span><span class="special">;</span>

    <span class="comment">// Of course we can use interval types that are different from the</span>
    <span class="comment">// library default by explicit instantiation:</span>
    <span class="identifier">split_interval_set</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">,</span> <span class="identifier">closed_interval</span><span class="special">&lt;</span><span class="identifier">Time</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">intuitive_times</span><span class="special">;</span>
    <span class="comment">// Interval set 'intuitive_times' uses statically bounded closed intervals</span>
    <span class="identifier">intuitive_times</span> <span class="special">+=</span> <span class="identifier">closed_interval</span><span class="special">&lt;</span><span class="identifier">Time</span><span class="special">&gt;(</span><span class="identifier">Time</span><span class="special">(</span><span class="identifier">monday</span><span class="special">,</span>  <span class="number">9</span><span class="special">,</span><span class="number">00</span><span class="special">),</span> <span class="identifier">Time</span><span class="special">(</span><span class="identifier">monday</span><span class="special">,</span> <span class="number">10</span><span class="special">,</span><span class="number">59</span><span class="special">));</span>
    <span class="identifier">intuitive_times</span> <span class="special">+=</span> <span class="identifier">closed_interval</span><span class="special">&lt;</span><span class="identifier">Time</span><span class="special">&gt;(</span><span class="identifier">Time</span><span class="special">(</span><span class="identifier">monday</span><span class="special">,</span> <span class="number">10</span><span class="special">,</span><span class="number">00</span><span class="special">),</span> <span class="identifier">Time</span><span class="special">(</span><span class="identifier">monday</span><span class="special">,</span> <span class="number">11</span><span class="special">,</span><span class="number">59</span><span class="special">));</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"----- Here we are NOT using the library default for intervals --------------\n"</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">intuitive_times</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>

<span class="comment">// Program output:</span>
<span class="comment">//&gt;&gt;Interval Container Library: Sample interval.cpp &lt;&lt;</span>
<span class="comment">//----------------------------------------------------</span>
<span class="comment">//----- Dynamically bounded intervals ----------------------------------------</span>
<span class="comment">//  discrete_interval&lt;int&gt;   : [3,7]</span>
<span class="comment">//continuous_interval&lt;double&gt;: [0.707107,1.41421) does NOT contain sqrt(2)</span>
<span class="comment">//continuous_interval&lt;string&gt;: (Barcelona,Boston] does NOT contain 'Barcelona'</span>
<span class="comment">//continuous_interval&lt;string&gt;: (Barcelona,Boston] does  contain 'Berlin'</span>
<span class="comment">//  discrete_interval&lt;Time&gt;  : (mon:08:30,mon:17:20)</span>
<span class="comment">//</span>
<span class="comment">//----- Manipulation of single values in continuous sets ---------------------</span>
<span class="comment">//1/3 subtracted from [0..1) : {[0/1,1/3)(1/3,1/1)}</span>
<span class="comment">//The set does NOT contain '1/3'</span>
<span class="comment">//Flipping the holey set     : {[1/3,1/3]}</span>
<span class="comment">//yields the subtracted      :     1/3</span>
<span class="comment">//</span>
<span class="comment">//----- Here we are NOT using the library default for intervals --------------</span>
<span class="comment">//{[mon:09:00,mon:09:59][mon:10:00,mon:10:59][mon:11:00,mon:11:59]}</span>
</pre>
<p>
      </p>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="interval.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="static_interval.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
