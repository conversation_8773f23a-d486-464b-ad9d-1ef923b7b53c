<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Man power</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../examples.html" title="Examples">
<link rel="prev" href="time_grids.html" title="Time grids for months and weeks">
<link rel="next" href="user_groups.html" title="User groups">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="time_grids.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="user_groups.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_icl.examples.man_power"></a><a class="link" href="man_power.html" title="Man power">Man power</a>
</h3></div></div></div>
<p>
        <code class="computeroutput"><a class="link" href="../../boost/icl/interval_set.html" title="Class template interval_set">Interval_sets</a></code> and
        <code class="computeroutput"><a class="link" href="../../boost/icl/interval_map.html" title="Class template interval_map">interval_maps</a></code> can be
        filled and manipulated using set style operations such as union <code class="computeroutput"><span class="special">+=</span></code>, difference <code class="computeroutput"><span class="special">-=</span></code>
        and intersection <code class="computeroutput"><span class="special">&amp;=</span></code>.
      </p>
<p>
        In this example <span class="bold"><strong>man power</strong></span> a number of those
        operations are demonstrated in the process of calculation the available working
        times (man-power) of a company's employees accounting for weekends, holidays,
        sickness times and vacations.
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">// The next line includes &lt;boost/gregorian/date.hpp&gt;</span>
<span class="comment">// and a few lines of adapter code.</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">gregorian</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">discrete_interval</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">interval_map</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">;</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">icl</span><span class="special">;</span>


<span class="comment">// Function weekends returns the interval_set of weekends that are contained in</span>
<span class="comment">// the date interval 'scope'</span>
<span class="identifier">interval_set</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span> <span class="identifier">weekends</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;&amp;</span> <span class="identifier">scope</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span> <span class="identifier">weekends</span><span class="special">;</span>

    <span class="identifier">date</span> <span class="identifier">cur_weekend_sat</span>
        <span class="special">=</span> <span class="identifier">first</span><span class="special">(</span><span class="identifier">scope</span><span class="special">)</span>
          <span class="special">+</span> <span class="identifier">days</span><span class="special">(</span><span class="identifier">days_until_weekday</span><span class="special">(</span><span class="identifier">first</span><span class="special">(</span><span class="identifier">scope</span><span class="special">),</span> <span class="identifier">greg_weekday</span><span class="special">(</span><span class="identifier">Saturday</span><span class="special">)))</span>
          <span class="special">-</span> <span class="identifier">weeks</span><span class="special">(</span><span class="number">1</span><span class="special">);</span>
    <span class="identifier">week_iterator</span> <span class="identifier">week_iter</span><span class="special">(</span><span class="identifier">cur_weekend_sat</span><span class="special">);</span>

    <span class="keyword">for</span><span class="special">(;</span> <span class="identifier">week_iter</span> <span class="special">&lt;=</span> <span class="identifier">last</span><span class="special">(</span><span class="identifier">scope</span><span class="special">);</span> <span class="special">++</span><span class="identifier">week_iter</span><span class="special">)</span>
        <span class="identifier">weekends</span> <span class="special">+=</span> <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(*</span><span class="identifier">week_iter</span><span class="special">,</span> <span class="special">*</span><span class="identifier">week_iter</span> <span class="special">+</span> <span class="identifier">days</span><span class="special">(</span><span class="number">2</span><span class="special">));</span>

    <span class="identifier">weekends</span> <span class="special">&amp;=</span> <span class="identifier">scope</span><span class="special">;</span> <span class="comment">// cut off the surplus</span>

    <span class="keyword">return</span> <span class="identifier">weekends</span><span class="special">;</span>
<span class="special">}</span>

<span class="comment">// The available working time for the employees of a company is calculated</span>
<span class="comment">// for a period of 3 months accounting for weekends and holidays.</span>
<span class="comment">//    The available daily working time for the employees is calculated</span>
<span class="comment">// using interval_sets and interval_maps demonstrating a number of</span>
<span class="comment">// addition, subtraction and intersection operations.</span>
<span class="keyword">void</span> <span class="identifier">man_power</span><span class="special">()</span>
<span class="special">{</span>
    <span class="identifier">date</span> <span class="identifier">someday</span> <span class="special">=</span> <span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-08-01"</span><span class="special">);</span>
    <span class="identifier">date</span> <span class="identifier">thenday</span> <span class="special">=</span> <span class="identifier">someday</span> <span class="special">+</span> <span class="identifier">months</span><span class="special">(</span><span class="number">3</span><span class="special">);</span>

    <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span> <span class="identifier">scope</span> <span class="special">=</span> <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="identifier">someday</span><span class="special">,</span> <span class="identifier">thenday</span><span class="special">);</span>

    <span class="comment">// ------------------------------------------------------------------------</span>
    <span class="comment">// (1) In a first step, the regular working times are computed for the</span>
    <span class="comment">// company within the given scope. From all available days, the weekends</span>
    <span class="comment">// and holidays have to be subtracted: </span>
    <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span> <span class="identifier">worktime</span><span class="special">(</span><span class="identifier">scope</span><span class="special">);</span>
    <span class="comment">// Subtract the weekends</span>
    <span class="identifier">worktime</span> <span class="special">-=</span> <span class="identifier">weekends</span><span class="special">(</span><span class="identifier">scope</span><span class="special">);</span>
    <span class="comment">// Subtract holidays</span>
    <span class="identifier">worktime</span> <span class="special">-=</span> <span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-10-03"</span><span class="special">);</span> <span class="comment">//German reunification ;)</span>

    <span class="comment">// company holidays (fictitious ;)</span>
    <span class="identifier">worktime</span> <span class="special">-=</span> <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;::</span><span class="identifier">closed</span><span class="special">(</span><span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-08-18"</span><span class="special">),</span>
                                                <span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-08-22"</span><span class="special">));</span>

    <span class="comment">//-------------------------------------------------------------------------</span>
    <span class="comment">// (2) Now we calculate the individual work times for some employees</span>
    <span class="comment">//-------------------------------------------------------------------------</span>
    <span class="comment">// In the company works Claudia. </span>
    <span class="comment">// This is the map of her regular working times:</span>
    <span class="identifier">interval_map</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="identifier">claudias_working_hours</span><span class="special">;</span>

    <span class="comment">// Claudia is working 8 hours a day. So the next statement says</span>
    <span class="comment">// that every day in the whole scope is mapped to 8 hours worktime.</span>
    <span class="identifier">claudias_working_hours</span> <span class="special">+=</span> <span class="identifier">make_pair</span><span class="special">(</span><span class="identifier">scope</span><span class="special">,</span> <span class="number">8</span><span class="special">);</span>

    <span class="comment">// But Claudia only works 8 hours on regular working days so we do</span>
    <span class="comment">// an intersection of the interval_map with the interval_set worktime:</span>
    <span class="identifier">claudias_working_hours</span> <span class="special">&amp;=</span> <span class="identifier">worktime</span><span class="special">;</span>

    <span class="comment">// Yet, in addition Claudia has her own absence times like</span>
    <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span> <span class="identifier">claudias_seminar</span> <span class="special">(</span><span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-09-16"</span><span class="special">),</span>
                                              <span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-09-24"</span><span class="special">),</span>
                                              <span class="identifier">interval_bounds</span><span class="special">::</span><span class="identifier">closed</span><span class="special">());</span>
    <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span> <span class="identifier">claudias_vacation</span><span class="special">(</span><span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-08-01"</span><span class="special">),</span>
                                              <span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-08-14"</span><span class="special">),</span>
                                              <span class="identifier">interval_bounds</span><span class="special">::</span><span class="identifier">closed</span><span class="special">());</span>

    <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span> <span class="identifier">claudias_absence_times</span><span class="special">(</span><span class="identifier">claudias_seminar</span><span class="special">);</span>
    <span class="identifier">claudias_absence_times</span> <span class="special">+=</span> <span class="identifier">claudias_vacation</span><span class="special">;</span>

    <span class="comment">// All the absence times have to subtracted from the map of her working times</span>
    <span class="identifier">claudias_working_hours</span> <span class="special">-=</span> <span class="identifier">claudias_absence_times</span><span class="special">;</span>

    <span class="comment">//-------------------------------------------------------------------------</span>
    <span class="comment">// Claudia's boss is Bodo. He only works part time. </span>
    <span class="comment">// This is the map of his regular working times:</span>
    <span class="identifier">interval_map</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="identifier">bodos_working_hours</span><span class="special">;</span>

    <span class="comment">// Bodo is working 4 hours a day.</span>
    <span class="identifier">bodos_working_hours</span> <span class="special">+=</span> <span class="identifier">make_pair</span><span class="special">(</span><span class="identifier">scope</span><span class="special">,</span> <span class="number">4</span><span class="special">);</span>

    <span class="comment">// Bodo works only on regular working days</span>
    <span class="identifier">bodos_working_hours</span> <span class="special">&amp;=</span> <span class="identifier">worktime</span><span class="special">;</span>

    <span class="comment">// Bodos additional absence times</span>
    <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span>      <span class="identifier">bodos_flu</span><span class="special">(</span><span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-09-19"</span><span class="special">),</span> <span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-09-29"</span><span class="special">),</span>
                                           <span class="identifier">interval_bounds</span><span class="special">::</span><span class="identifier">closed</span><span class="special">());</span>
    <span class="identifier">discrete_interval</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span> <span class="identifier">bodos_vacation</span><span class="special">(</span><span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-08-15"</span><span class="special">),</span> <span class="identifier">from_string</span><span class="special">(</span><span class="string">"2008-09-03"</span><span class="special">),</span>
                                           <span class="identifier">interval_bounds</span><span class="special">::</span><span class="identifier">closed</span><span class="special">());</span>

    <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">&gt;</span> <span class="identifier">bodos_absence_times</span><span class="special">(</span><span class="identifier">bodos_flu</span><span class="special">);</span>
    <span class="identifier">bodos_absence_times</span> <span class="special">+=</span> <span class="identifier">bodos_vacation</span><span class="special">;</span>

    <span class="comment">// All the absence times have to be subtracted from the map of his working times</span>
    <span class="identifier">bodos_working_hours</span> <span class="special">-=</span> <span class="identifier">bodos_absence_times</span><span class="special">;</span>

    <span class="comment">//-------------------------------------------------------------------------</span>
    <span class="comment">// (3) Finally we want to calculate the available manpower of the company</span>
    <span class="comment">// for the selected time scope: This is done by adding up the employees</span>
    <span class="comment">// working time maps:</span>
    <span class="identifier">interval_map</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="identifier">manpower</span><span class="special">;</span>
    <span class="identifier">manpower</span> <span class="special">+=</span> <span class="identifier">claudias_working_hours</span><span class="special">;</span>
    <span class="identifier">manpower</span> <span class="special">+=</span> <span class="identifier">bodos_working_hours</span><span class="special">;</span>


    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">first</span><span class="special">(</span><span class="identifier">scope</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="string">" - "</span> <span class="special">&lt;&lt;</span> <span class="identifier">last</span><span class="special">(</span><span class="identifier">scope</span><span class="special">)</span>
         <span class="special">&lt;&lt;</span> <span class="string">"    available man-power:"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"---------------------------------------------------------------\n"</span><span class="special">;</span>

    <span class="keyword">for</span><span class="special">(</span><span class="identifier">interval_map</span><span class="special">&lt;</span><span class="identifier">date</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">iterator</span> <span class="identifier">it</span> <span class="special">=</span> <span class="identifier">manpower</span><span class="special">.</span><span class="identifier">begin</span><span class="special">();</span>
        <span class="identifier">it</span> <span class="special">!=</span> <span class="identifier">manpower</span><span class="special">.</span><span class="identifier">end</span><span class="special">();</span> <span class="identifier">it</span><span class="special">++)</span>
    <span class="special">{</span>
        <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">first</span><span class="special">(</span><span class="identifier">it</span><span class="special">-&gt;</span><span class="identifier">first</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="string">" - "</span> <span class="special">&lt;&lt;</span> <span class="identifier">last</span><span class="special">(</span><span class="identifier">it</span><span class="special">-&gt;</span><span class="identifier">first</span><span class="special">)</span>
             <span class="special">&lt;&lt;</span> <span class="string">" -&gt; "</span> <span class="special">&lt;&lt;</span> <span class="identifier">it</span><span class="special">-&gt;</span><span class="identifier">second</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="special">}</span>
<span class="special">}</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"&gt;&gt;Interval Container Library: Sample man_power.cpp &lt;&lt;\n"</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"---------------------------------------------------------------\n"</span><span class="special">;</span>
    <span class="identifier">man_power</span><span class="special">();</span>
    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>

<span class="comment">// Program output:</span>
<span class="comment">/*
&gt;&gt;Interval Container Library: Sample man_power.cpp &lt;&lt;
---------------------------------------------------------------
2008-Aug-01 - 2008-Oct-31    available man-power:
---------------------------------------------------------------
2008-Aug-01 - 2008-Aug-01 -&gt; 4
2008-Aug-04 - 2008-Aug-08 -&gt; 4
2008-Aug-11 - 2008-Aug-14 -&gt; 4
2008-Aug-15 - 2008-Aug-15 -&gt; 8
2008-Aug-25 - 2008-Aug-29 -&gt; 8
2008-Sep-01 - 2008-Sep-03 -&gt; 8
2008-Sep-04 - 2008-Sep-05 -&gt; 12
2008-Sep-08 - 2008-Sep-12 -&gt; 12
2008-Sep-15 - 2008-Sep-15 -&gt; 12
2008-Sep-16 - 2008-Sep-18 -&gt; 4
2008-Sep-25 - 2008-Sep-26 -&gt; 8
2008-Sep-29 - 2008-Sep-29 -&gt; 8
2008-Sep-30 - 2008-Oct-02 -&gt; 12
2008-Oct-06 - 2008-Oct-10 -&gt; 12
2008-Oct-13 - 2008-Oct-17 -&gt; 12
2008-Oct-20 - 2008-Oct-24 -&gt; 12
2008-Oct-27 - 2008-Oct-31 -&gt; 12
*/</span>
</pre>
<p>
      </p>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="time_grids.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="user_groups.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
