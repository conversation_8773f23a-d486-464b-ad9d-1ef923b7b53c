<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Static interval</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../examples.html" title="Examples">
<link rel="prev" href="dynamic_interval.html" title="Dynamic interval">
<link rel="next" href="interval_container.html" title="Interval container">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="dynamic_interval.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="interval_container.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_icl.examples.static_interval"></a><a class="link" href="static_interval.html" title="Static interval">Static interval</a>
</h3></div></div></div>
<p>
</p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">math</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">type_traits</span><span class="special">/</span><span class="identifier">is_same</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="comment">// We can change the library default for the interval types by defining </span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_ICL_USE_STATIC_BOUNDED_INTERVALS</span>
<span class="comment">// prior to other inluces from the icl.</span>
<span class="comment">// The interval type that is automatically used with interval</span>
<span class="comment">// containers then is the statically bounded right_open_interval.</span>

<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">interval_set</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">split_interval_set</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="comment">// The statically bounded interval type 'right_open_interval'</span>
<span class="comment">// is indirectly included via interval containers.</span>


<span class="preprocessor">#include</span> <span class="string">"../toytime.hpp"</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">icl</span><span class="special">/</span><span class="identifier">rational</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">;</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">icl</span><span class="special">;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"&gt;&gt; Interval Container Library: Sample static_interval.cpp &lt;&lt;\n"</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"------------------------------------------------------------\n"</span><span class="special">;</span>

    <span class="comment">// Statically bounded intervals are the user defined library default for </span>
    <span class="comment">// interval parameters in interval containers now.</span>
    <span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">((</span>
        <span class="identifier">boost</span><span class="special">::</span><span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">interval_type</span>
                      <span class="special">,</span> <span class="identifier">right_open_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">value</span>
                      <span class="special">));</span>

    <span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">((</span>
        <span class="identifier">boost</span><span class="special">::</span><span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;::</span><span class="identifier">interval_type</span>
                      <span class="special">,</span> <span class="identifier">right_open_interval</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">value</span>
                      <span class="special">));</span>

    <span class="comment">// As we can see the library default both for discrete and continuous</span>
    <span class="comment">// domain_types T is 'right_open_interval&lt;T&gt;'.</span>
    <span class="comment">// The user defined library default for intervals is also available via </span>
    <span class="comment">// the template 'interval':</span>
    <span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">((</span>
        <span class="identifier">boost</span><span class="special">::</span><span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">type</span>
                      <span class="special">,</span> <span class="identifier">right_open_interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">value</span>
                      <span class="special">));</span>

    <span class="comment">// Again we are declaring and initializing the four test intervals that have been used</span>
    <span class="comment">// in the example 'interval' and 'dynamic_interval'</span>
    <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">type</span>    <span class="identifier">int_interval</span>  <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="number">3</span><span class="special">,</span> <span class="number">8</span><span class="special">);</span> <span class="comment">// shifted the upper bound</span>
    <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">sqrt_interval</span> <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="number">1</span><span class="special">/</span><span class="identifier">sqrt</span><span class="special">(</span><span class="number">2.0</span><span class="special">),</span> <span class="identifier">sqrt</span><span class="special">(</span><span class="number">2.0</span><span class="special">));</span>

    <span class="comment">// Interval ("Barcelona", "Boston"] can not be represented because there is no 'steppable next' on</span>
    <span class="comment">// lower bound "Barcelona". Ok. this is a different interval:</span>
    <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">city_interval</span> <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="string">"Barcelona"</span><span class="special">,</span> <span class="string">"Boston"</span><span class="special">);</span>

    <span class="comment">// Toy Time is discrete again so we can transfrom open(Time(monday,8,30), Time(monday,17,20))</span>
    <span class="comment">//                                       to right_open(Time(monday,8,31), Time(monday,17,20))</span>
    <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">Time</span><span class="special">&gt;::</span><span class="identifier">type</span>   <span class="identifier">time_interval</span> <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">Time</span><span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="identifier">Time</span><span class="special">(</span><span class="identifier">monday</span><span class="special">,</span><span class="number">8</span><span class="special">,</span><span class="number">31</span><span class="special">),</span> <span class="identifier">Time</span><span class="special">(</span><span class="identifier">monday</span><span class="special">,</span><span class="number">17</span><span class="special">,</span><span class="number">20</span><span class="special">));</span>

    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"----- Statically bounded intervals ----------------------------------------\n"</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"right_open_interval&lt;int&gt;   : "</span> <span class="special">&lt;&lt;</span> <span class="identifier">int_interval</span>  <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"right_open_interval&lt;double&gt;: "</span> <span class="special">&lt;&lt;</span> <span class="identifier">sqrt_interval</span> <span class="special">&lt;&lt;</span> <span class="string">" does "</span>
                                            <span class="special">&lt;&lt;</span> <span class="identifier">string</span><span class="special">(</span><span class="identifier">contains</span><span class="special">(</span><span class="identifier">sqrt_interval</span><span class="special">,</span> <span class="identifier">sqrt</span><span class="special">(</span><span class="number">2.0</span><span class="special">))?</span><span class="string">""</span><span class="special">:</span><span class="string">"NOT"</span><span class="special">)</span>
                                            <span class="special">&lt;&lt;</span> <span class="string">" contain sqrt(2)"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"right_open_interval&lt;string&gt;: "</span> <span class="special">&lt;&lt;</span> <span class="identifier">city_interval</span> <span class="special">&lt;&lt;</span> <span class="string">" does "</span>
                                            <span class="special">&lt;&lt;</span> <span class="identifier">string</span><span class="special">(</span><span class="identifier">contains</span><span class="special">(</span><span class="identifier">city_interval</span><span class="special">,</span><span class="string">"Barcelona"</span><span class="special">)?</span><span class="string">""</span><span class="special">:</span><span class="string">"NOT"</span><span class="special">)</span>
                                            <span class="special">&lt;&lt;</span> <span class="string">" contain 'Barcelona'"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"right_open_interval&lt;string&gt;: "</span> <span class="special">&lt;&lt;</span> <span class="identifier">city_interval</span> <span class="special">&lt;&lt;</span> <span class="string">" does "</span>
                                            <span class="special">&lt;&lt;</span> <span class="identifier">string</span><span class="special">(</span><span class="identifier">contains</span><span class="special">(</span><span class="identifier">city_interval</span><span class="special">,</span> <span class="string">"Boston"</span><span class="special">)?</span><span class="string">""</span><span class="special">:</span><span class="string">"NOT"</span><span class="special">)</span>
                                            <span class="special">&lt;&lt;</span> <span class="string">" contain 'Boston'"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"right_open_interval&lt;Time&gt;  : "</span> <span class="special">&lt;&lt;</span> <span class="identifier">time_interval</span> <span class="special">&lt;&lt;</span> <span class="string">"\n\n"</span><span class="special">;</span>

    <span class="comment">// Using statically bounded intervals does not allows to apply operations</span>
    <span class="comment">// with elements on all interval containers, if their domain_type is continuous. </span>
    <span class="comment">// The code that follows is identical to example 'dynamic_interval'. Only 'internally'</span>
    <span class="comment">// the library default for the interval template now is 'right_open_interval' </span>
    <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">unit_interval</span>
        <span class="special">=</span> <span class="identifier">interval</span><span class="special">&lt;</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">right_open</span><span class="special">(</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="number">0</span><span class="special">),</span> <span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="number">1</span><span class="special">));</span>
    <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">unit_set</span><span class="special">(</span><span class="identifier">unit_interval</span><span class="special">);</span>
    <span class="identifier">interval_set</span><span class="special">&lt;</span><span class="identifier">rational</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">ratio_set</span><span class="special">(</span><span class="identifier">unit_set</span><span class="special">);</span>
    <span class="comment">// ratio_set -= rational&lt;int&gt;(1,3); // This line will not compile, because we can not</span>
                                        <span class="comment">// represent a singleton interval as right_open_interval.</span>
    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>

<span class="comment">// Program output:</span>
<span class="comment">//&gt;&gt; Interval Container Library: Sample static_interval.cpp &lt;&lt;</span>
<span class="comment">//------------------------------------------------------------</span>
<span class="comment">//----- Statically bounded intervals ----------------------------------------</span>
<span class="comment">//right_open_interval&lt;int&gt;   : [3,8)</span>
<span class="comment">//right_open_interval&lt;double&gt;: [0.707107,1.41421) does NOT contain sqrt(2)</span>
<span class="comment">//right_open_interval&lt;string&gt;: [Barcelona,Boston) does  contain 'Barcelona'</span>
<span class="comment">//right_open_interval&lt;string&gt;: [Barcelona,Boston) does NOT contain 'Boston'</span>
<span class="comment">//right_open_interval&lt;Time&gt;  : [mon:08:31,mon:17:20)</span>
</pre>
<p>
      </p>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="dynamic_interval.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="interval_container.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
