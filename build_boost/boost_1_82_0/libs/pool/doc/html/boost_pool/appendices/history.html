<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Appendix A: History</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Pool">
<link rel="up" href="../appendices.html" title="Appendices">
<link rel="prev" href="../appendices.html" title="Appendices">
<link rel="next" href="faq.html" title="Appendix B: FAQ">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../appendices.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../appendices.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="faq.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_pool.appendices.history"></a><a class="link" href="history.html" title="Appendix A: History">Appendix A: History</a>
</h3></div></div></div>
<h5>
<a name="boost_pool.appendices.history.h0"></a>
        <span class="phrase"><a name="boost_pool.appendices.history.version_2_0_0__january_11__2011"></a></span><a class="link" href="history.html#boost_pool.appendices.history.version_2_0_0__january_11__2011">Version
        2.0.0, January 11, 2011</a>
      </h5>
<p>
        <span class="emphasis"><em>Documentation and testing revision</em></span>
      </p>
<p>
        <span class="bold"><strong>Features:</strong></span>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Fix issues <a href="https://svn.boost.org/trac/boost/ticket/1252" target="_top">1252</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/4960" target="_top">4960</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/5526" target="_top">5526</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/5700" target="_top">5700</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/2696" target="_top">2696</a>.
          </li>
<li class="listitem">
            Documentation converted and rewritten and revised by Paul A. Bristow
            using Quickbook, Doxygen, for html and pdf, based on Stephen Cleary's
            html version, Revised 05 December, 2006.
          </li>
</ul></div>
<p>
        This used Opera 11.0, and <code class="computeroutput"><span class="identifier">html_to_quickbook</span><span class="special">.</span><span class="identifier">css</span></code> as
        a special display format. On the Opera full taskbar (chose <span class="emphasis"><em>enable
        full taskbar</em></span>) View, Style, Manage modes, Display.
      </p>
<p>
        Choose <span class="emphasis"><em>add <code class="computeroutput"><span class="special">\</span><span class="identifier">boost</span><span class="special">-</span><span class="identifier">sandbox</span><span class="special">\</span><span class="identifier">boost_docs</span><span class="special">\</span><span class="identifier">trunk</span><span class="special">\</span><span class="identifier">doc</span><span class="special">\</span><span class="identifier">style</span><span class="special">\</span><span class="identifier">html</span><span class="special">\</span><span class="identifier">conversion</span><span class="special">\</span><span class="identifier">html_to_quickbook</span><span class="special">.</span><span class="identifier">css</span></code></em></span>
        to My Style Sheet. Html pages are now displayed as Quickbook and can be copied
        and pasted into quickbook files using your favored text editor for Quickbook.
      </p>
<h5>
<a name="boost_pool.appendices.history.h1"></a>
        <span class="phrase"><a name="boost_pool.appendices.history.version_1_0_0__january_1__2000"></a></span><a class="link" href="history.html#boost_pool.appendices.history.version_1_0_0__january_1__2000">Version
        1.0.0, January 1, 2000</a>
      </h5>
<p>
        <span class="emphasis"><em>First release</em></span>
      </p>
</div>
<div class="copyright-footer">Copyright © 2000-2006 Stephen Cleary<br>Copyright © 2011 Paul A. Bristow<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../appendices.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../appendices.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="faq.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
