<?xml version="1.0" standalone="yes"?>
<library-reference id="utilities"><title>Utilities</title><header name="boost/log/utility/exception_handler.hpp">
<para><para><PERSON><PERSON> </para>

<para>12.07.2009</para>

This header contains tools for exception handlers support in different parts of the library. </para><namespace name="boost">
<namespace name="log">
<class name="exception_handler"><template>
      <template-type-parameter name="SequenceT"/>
      <template-type-parameter name="HandlerT"/>
    </template><description><para>An exception handler functional object. The handler aggregates a user-defined functional object that will be called when one of the specified exception types is caught. </para></description><typedef name="handler_type"><purpose>The exception handler type. </purpose><type>HandlerT</type></typedef>
<typedef name="result_type"><purpose>The handler result type. </purpose><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><description><para>Exception launcher. Rethrows the current exception in order to detect its type and pass it to the aggregated function object.</para><para><note><para>Must be called from within a <computeroutput>catch</computeroutput> statement. </para>
</note>
</para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="handler"><paramtype>handler_type const &amp;</paramtype></parameter><description><para>Initializing constructor. Creates an exception handler with the specified function object that will receive the exception. </para></description></constructor>
</class><class name="nothrow_exception_handler"><template>
      <template-type-parameter name="SequenceT"/>
      <template-type-parameter name="HandlerT"/>
    </template><inherit access="public">boost::log::exception_handler&lt; SequenceT, HandlerT &gt;</inherit><description><para>A no-throw exception handler functional object. Acts similar to <computeroutput><classname alt="boost::log::exception_handler">exception_handler</classname></computeroutput>, but in case if the exception cannot be handled the exception is not propagated from the handler. Instead the user-defined functional object is called with no parameters. </para></description><typedef name="handler_type"><purpose>The exception handler type. </purpose><type>HandlerT</type></typedef>
<typedef name="result_type"><purpose>The handler result type. </purpose><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><description><para>Exception launcher. Rethrows the current exception in order to detect its type and pass it to the aggregated function object. If the type of the exception could not be detected, the user-defined handler is called with no arguments.</para><para><note><para>Must be called from within a <computeroutput>catch</computeroutput> statement. </para>
</note>
</para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="handler"><paramtype>handler_type const &amp;</paramtype></parameter><description><para>Initializing constructor. Creates an exception handler with the specified function object that will receive the exception. </para></description></constructor>
</class>































<function name="make_exception_suppressor"><type><classname>nop</classname></type><description><para>The function creates an empty exception handler that effectively suppresses any exception </para></description></function>
<function name="make_exception_handler"><type><classname>exception_handler</classname>&lt; typename HandlerT::exception_types, HandlerT &gt;</type><template>
          <template-type-parameter name="HandlerT"/>
        </template><parameter name="handler"><paramtype>HandlerT const &amp;</paramtype><description><para>User-defined functional object that will receive exceptions. </para></description></parameter><description><para>The function creates an exception handler functional object. The handler will call to the user-specified functional object with an exception as its argument.</para><para>

<note><para>This form requires the user-defined functional object to have an <computeroutput>exception_types</computeroutput> nested type. This type should be an MPL sequence of all expected exception types. </para>
</note>
</para></description><returns><para>A nullary functional object that should be called from within a <computeroutput>catch</computeroutput> statement.</para>
</returns></function>
<function name="make_exception_handler"><type><classname>nothrow_exception_handler</classname>&lt; typename HandlerT::exception_types, HandlerT &gt;</type><template>
          <template-type-parameter name="HandlerT"/>
        </template><parameter name="handler"><paramtype>HandlerT const &amp;</paramtype><description><para>User-defined functional object that will receive exceptions. </para></description></parameter><parameter name=""><paramtype>std::nothrow_t const &amp;</paramtype></parameter><description><para>The function creates an exception handler functional object. The handler will call to the user-specified functional object with an exception as its argument. If the exception type cannot be identified, the handler will call the user-defined functor with no arguments, instead of propagating exception to the caller.</para><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts.</para><para>

<note><para>This form requires the user-defined functional object to have an <computeroutput>exception_types</computeroutput> nested type. This type should be an MPL sequence of all expected exception types. </para>
</note>
</para></description><returns><para>A nullary functional object that should be called from within a <computeroutput>catch</computeroutput> statement.</para>
</returns></function>
<function name="make_exception_handler"><type><classname>exception_handler</classname>&lt; MPL_sequence_of_ExceptionsT, HandlerT &gt;</type><template>
          <template-nontype-parameter name="ExceptionsT"><type>typename...</type></template-nontype-parameter>
          <template-type-parameter name="HandlerT"/>
        </template><parameter name="handler"><paramtype>HandlerT const &amp;</paramtype><description><para>User-defined functional object that will receive exceptions. </para></description></parameter><description><para>The function creates an exception handler functional object. The handler will call to the user-specified functional object with an exception as its argument. All expected exception types should be specified as first template parameters explicitly, in the order they would be specified in a corresponding <computeroutput>try/catch</computeroutput> statement.</para><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts.</para><para>

</para></description><returns><para>A nullary functional object that should be called from within a <computeroutput>catch</computeroutput> statement. </para>
</returns></function>
<function name="make_exception_handler"><type><classname>nothrow_exception_handler</classname>&lt; MPL_sequence_of_ExceptionsT, HandlerT &gt;</type><template>
          <template-nontype-parameter name="ExceptionsT"><type>typename...</type></template-nontype-parameter>
          <template-type-parameter name="HandlerT"/>
        </template><parameter name="handler"><paramtype>HandlerT const &amp;</paramtype><description><para>User-defined functional object that will receive exceptions. </para></description></parameter><parameter name=""><paramtype>std::nothrow_t const &amp;</paramtype></parameter><description><para>The function creates an exception handler functional object. The handler will call to the user-specified functional object with an exception as its argument. If the exception type cannot be identified, the handler will call the user-defined functor with no arguments, instead of propagating exception to the caller. All expected exception types should be specified as first template parameters explicitly, in the order they would be specified in a corresponding <computeroutput>try/catch</computeroutput> statement.</para><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts.</para><para>

</para></description><returns><para>A nullary functional object that should be called from within a <computeroutput>catch</computeroutput> statement. </para>
</returns></function>














































































</namespace>
</namespace>
<macro name="BOOST_LOG_MAX_EXCEPTION_TYPES"><purpose>Maximum number of exception types that can be specified for exception handlers. </purpose></macro>
</header>
<header name="boost/log/utility/formatting_ostream.hpp">
<para><para>Andrey Semashev </para>

<para>11.07.2012</para>

The header contains implementation of a string stream used for log record formatting. </para><namespace name="boost">
<namespace name="log">


























<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="value"><paramtype>T</paramtype></parameter></function>
<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="value"><paramtype>T const &amp;</paramtype></parameter></function>
<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="value"><paramtype>T &amp;</paramtype></parameter></function>
<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;&amp;</paramtype></parameter><parameter name="value"><paramtype>T</paramtype></parameter></function>
<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;&amp;</paramtype></parameter><parameter name="value"><paramtype>T const &amp;</paramtype></parameter></function>
<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;&amp;</paramtype></parameter><parameter name="value"><paramtype>T &amp;</paramtype></parameter></function>



















































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/formatting_ostream_fwd.hpp">
<para><para>Andrey Semashev </para>

<para>11.07.2012</para>

The header contains forward declaration of a string stream used for log record formatting. </para><namespace name="boost">
<namespace name="log">
<class name="basic_formatting_ostream"><template>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="TraitsT"/>
      <template-type-parameter name="AllocatorT"/>
    </template><purpose>Stream for log records formatting. </purpose><description><para>Stream wrapper for log records formatting.</para><para>This stream wrapper is used by the library for log record formatting. It implements the standard string stream interface with a few differences:</para><para><itemizedlist>
<listitem><para>It does not derive from standard types <computeroutput>std::basic_ostream</computeroutput>, <computeroutput>std::basic_ios</computeroutput> and <computeroutput>std::ios_base</computeroutput>, although it tries to implement their interfaces closely. There are a few small differences, mostly regarding <computeroutput>rdbuf</computeroutput> and <computeroutput>str</computeroutput> signatures, as well as the supported insertion operator overloads. The actual wrapped stream can be accessed through the <computeroutput>stream</computeroutput> methods. </para>
</listitem>
<listitem><para>By default, <computeroutput>bool</computeroutput> values are formatted using alphabetical representation rather than numeric. </para>
</listitem>
<listitem><para>The stream supports writing strings of character types different from the stream character type. The stream will perform character code conversion as needed using the imbued locale. </para>
</listitem>
<listitem><para>The stream operates on an external string object rather than on the embedded one. The string can be attached or detached from the stream dynamically.</para>
</listitem>
</itemizedlist>
Although <computeroutput><classname alt="boost::log::basic_formatting_ostream">basic_formatting_ostream</classname></computeroutput> does not derive from <computeroutput>std::basic_ostream</computeroutput>, users are not required to add special overloads of <computeroutput>operator&lt;&lt;</computeroutput> for it since the stream will by default reuse the operators for <computeroutput>std::basic_ostream</computeroutput>. However, one can define special overloads of <computeroutput>operator&lt;&lt;</computeroutput> for <computeroutput><classname alt="boost::log::basic_formatting_ostream">basic_formatting_ostream</classname></computeroutput> if a certain type needs special formatting when output to log. </para></description><method-group name="public member functions">
<method name="attach"><type>void</type><parameter name="str"><paramtype>string_type &amp;</paramtype><description><para>The string buffer to attach. </para></description></parameter><description><para>Attaches the stream to the string. The string will be used to store the formatted characters.</para><para>
</para></description></method>
<method name="detach"><type>void</type><description><para>Detaches the stream from the string. Any buffered data is flushed to the string. </para></description></method>
<method name="str" cv="const"><type>string_type const  &amp;</type><description><para>
</para></description><returns><para>Reference to the attached string. The string must be attached before calling this method. </para>
</returns></method>
<method name="stream"><type>ostream_type &amp;</type><description><para>
</para></description><returns><para>Reference to the wrapped stream </para>
</returns></method>
<method name="stream" cv="const"><type>ostream_type const  &amp;</type><description><para>
</para></description><returns><para>Reference to the wrapped stream </para>
</returns></method>
<method name="flags" cv="const"><type>fmtflags</type></method>
<method name="flags"><type>fmtflags</type><parameter name="f"><paramtype>fmtflags</paramtype></parameter></method>
<method name="setf"><type>fmtflags</type><parameter name="f"><paramtype>fmtflags</paramtype></parameter></method>
<method name="setf"><type>fmtflags</type><parameter name="f"><paramtype>fmtflags</paramtype></parameter><parameter name="mask"><paramtype>fmtflags</paramtype></parameter></method>
<method name="unsetf"><type>void</type><parameter name="f"><paramtype>fmtflags</paramtype></parameter></method>
<method name="precision" cv="const"><type>std::streamsize</type></method>
<method name="precision"><type>std::streamsize</type><parameter name="p"><paramtype>std::streamsize</paramtype></parameter></method>
<method name="width" cv="const"><type>std::streamsize</type></method>
<method name="width"><type>std::streamsize</type><parameter name="w"><paramtype>std::streamsize</paramtype></parameter></method>
<method name="getloc" cv="const"><type>std::locale</type></method>
<method name="imbue"><type>std::locale</type><parameter name="loc"><paramtype>std::locale const &amp;</paramtype></parameter></method>
<method name="iword"><type>long &amp;</type><parameter name="index"><paramtype>int</paramtype></parameter></method>
<method name="pword"><type>void *&amp;</type><parameter name="index"><paramtype>int</paramtype></parameter></method>
<method name="register_callback"><type>void</type><parameter name="fn"><paramtype>event_callback</paramtype></parameter><parameter name="index"><paramtype>int</paramtype></parameter></method>
<method name="conversion-operator" cv="const" specifiers="explicit"><type>bool</type></method>
<method name="operator!" cv="const"><type>bool</type></method>
<method name="rdstate" cv="const"><type>iostate</type></method>
<method name="clear"><type>void</type><parameter name="state"><paramtype>iostate</paramtype><default>goodbit</default></parameter></method>
<method name="setstate"><type>void</type><parameter name="state"><paramtype>iostate</paramtype></parameter></method>
<method name="good" cv="const"><type>bool</type></method>
<method name="eof" cv="const"><type>bool</type></method>
<method name="fail" cv="const"><type>bool</type></method>
<method name="bad" cv="const"><type>bool</type></method>
<method name="exceptions" cv="const"><type>iostate</type></method>
<method name="exceptions"><type>void</type><parameter name="s"><paramtype>iostate</paramtype></parameter></method>
<method name="tie" cv="const"><type>ostream_type *</type></method>
<method name="tie"><type>ostream_type *</type><parameter name="strm"><paramtype>ostream_type *</paramtype></parameter></method>
<method name="rdbuf" cv="const"><type>streambuf_type *</type></method>
<method name="copyfmt"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="rhs"><paramtype>std::basic_ios&lt; char_type, traits_type &gt; &amp;</paramtype></parameter></method>
<method name="copyfmt"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="rhs"><paramtype><classname>basic_formatting_ostream</classname> &amp;</paramtype></parameter></method>
<method name="fill" cv="const"><type>char_type</type></method>
<method name="fill"><type>char_type</type><parameter name="ch"><paramtype>char_type</paramtype></parameter></method>
<method name="narrow" cv="const"><type>char</type><parameter name="ch"><paramtype>char_type</paramtype></parameter><parameter name="def"><paramtype>char</paramtype></parameter></method>
<method name="widen" cv="const"><type>char_type</type><parameter name="ch"><paramtype>char</paramtype></parameter></method>
<method name="flush"><type><classname>basic_formatting_ostream</classname> &amp;</type></method>
<method name="tellp"><type>pos_type</type></method>
<method name="seekp"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="pos"><paramtype>pos_type</paramtype></parameter></method>
<method name="seekp"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="off"><paramtype>off_type</paramtype></parameter><parameter name="dir"><paramtype>std::ios_base::seekdir</paramtype></parameter></method>
<method name="put"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="c"><paramtype>char_type</paramtype></parameter></method>
<method name="put"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="OtherCharT"/>
        </template><parameter name="c"><paramtype>OtherCharT</paramtype></parameter></method>
<method name="write"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="p"><paramtype>const char_type *</paramtype></parameter><parameter name="size"><paramtype>std::streamsize</paramtype></parameter></method>
<method name="write"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="OtherCharT"/>
        </template><parameter name="p"><paramtype>const OtherCharT *</paramtype></parameter><parameter name="size"><paramtype>std::streamsize</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="manip"><paramtype>ios_base_manip</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="manip"><paramtype>basic_ios_manip</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="manip"><paramtype>stream_manip</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="c"><paramtype>char</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="p"><paramtype>const char *</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="c"><paramtype>wchar_t</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="p"><paramtype>const wchar_t *</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="c"><paramtype>char16_t</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="p"><paramtype>const char16_t *</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="c"><paramtype>char32_t</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="p"><paramtype>const char32_t *</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>bool</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>signed char</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>unsigned char</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>short</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>unsigned short</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>int</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>unsigned int</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>long</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>unsigned long</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>long long</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>unsigned long long</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>float</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>double</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="value"><paramtype>long double</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="buf"><paramtype>std::basic_streambuf&lt; char_type, traits_type &gt; *</paramtype></parameter></method>
</method-group>
<constructor><description><para>Default constructor. Creates an empty record that is equivalent to the invalid record handle. The stream capability is not available after construction.</para><para>
</para></description><postconditions><para><computeroutput>!*this == true</computeroutput> </para>
</postconditions></constructor>
<constructor specifiers="explicit"><parameter name="str"><paramtype>string_type &amp;</paramtype><description><para>The string buffer to attach. </para></description></parameter><description><para>Initializing constructor. Attaches the string to the constructed stream. The string will be used to store the formatted characters.</para><para>

</para></description><postconditions><para><computeroutput>!*this == false</computeroutput> </para>
</postconditions></constructor>
<destructor><description><para>Destructor. Destroys the record, releases any sinks and attribute values that were involved in processing this record. </para></description></destructor>
<method-group name="public static functions">
<method name="xalloc" specifiers="static"><type>int</type></method>
<method name="sync_with_stdio" specifiers="static"><type>bool</type><parameter name="sync"><paramtype>bool</paramtype><default>true</default></parameter></method>
</method-group>
<method-group name="protected member functions">
<method name="init_stream"><type>void</type></method>
</method-group>
<method-group name="private member functions">
<method name="formatted_write"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="p"><paramtype>const char_type *</paramtype></parameter><parameter name="size"><paramtype>std::streamsize</paramtype></parameter></method>
<method name="formatted_write"><type><classname>basic_formatting_ostream</classname> &amp;</type><template>
          <template-type-parameter name="OtherCharT"/>
        </template><parameter name="p"><paramtype>const OtherCharT *</paramtype></parameter><parameter name="size"><paramtype>std::streamsize</paramtype></parameter></method>
<method name="aligned_write"><type>void</type><parameter name="p"><paramtype>const char_type *</paramtype></parameter><parameter name="size"><paramtype>std::streamsize</paramtype></parameter></method>
<method name="aligned_write"><type>void</type><template>
          <template-type-parameter name="OtherCharT"/>
        </template><parameter name="p"><paramtype>const OtherCharT *</paramtype></parameter><parameter name="size"><paramtype>std::streamsize</paramtype></parameter></method>
</method-group>
<constructor cv="= delete"><parameter name="that"><paramtype><classname>basic_formatting_ostream</classname> const &amp;</paramtype></parameter><purpose>Copy constructor (closed) </purpose></constructor>
<copy-assignment cv="= delete"><type><classname>basic_formatting_ostream</classname> &amp;</type><parameter name="that"><paramtype><classname>basic_formatting_ostream</classname> const &amp;</paramtype></parameter><purpose>Assignment (closed) </purpose></copy-assignment>
</class><typedef name="formatting_ostream"><type><classname>basic_formatting_ostream</classname>&lt; char &gt;</type></typedef>
<typedef name="wformatting_ostream"><type><classname>basic_formatting_ostream</classname>&lt; wchar_t &gt;</type></typedef>



















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header includes all functional helpers. </para></header>
<header name="boost/log/utility/functional/as_action.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains function object adapter for compatibility with Boost.Spirit actions interface requirements. </para><namespace name="boost">
<namespace name="log">
<struct name="as_action_adapter"><template>
      <template-type-parameter name="FunT"/>
    </template><purpose>Function object adapter for Boost.Spirit actions. </purpose><typedef name="result_type"><type>FunT::result_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="AttributeT"/>
          <template-type-parameter name="ContextT"/>
        </template><parameter name="attr"><paramtype>AttributeT const &amp;</paramtype></parameter><parameter name="ctx"><paramtype>ContextT const &amp;</paramtype></parameter><parameter name="pass"><paramtype>bool &amp;</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter></constructor>
</struct>













































































<function name="as_action"><type><classname>as_action_adapter</classname>&lt; FunT &gt;</type><template>
          <template-type-parameter name="FunT"/>
        </template><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter></function>




































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/begins_with.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains a predicate for checking if the provided string begins with a substring. </para><namespace name="boost">
<namespace name="log">
<struct name="begins_with_fun"><purpose>The <computeroutput>begins_with</computeroutput> functor. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></method>
</method-group>
</struct>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/bind.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains function object adapters. This is a lightweight alternative to what Boost.Phoenix and Boost.Bind provides. </para><namespace name="boost">
<namespace name="log">
<struct name="binder1st"><template>
      <template-type-parameter name="FunT"/>
      <template-type-parameter name="FirstArgT"/>
    </template><inherit access="private">FunT</inherit><purpose>First argument binder. </purpose><typedef name="result_type"><type>FunT::result_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T0"/>
        </template><parameter name="arg0"><paramtype>T0 const &amp;</paramtype></parameter></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T0"/>
          <template-type-parameter name="T1"/>
        </template><parameter name="arg0"><paramtype>T0 const &amp;</paramtype></parameter><parameter name="arg1"><paramtype>T1 const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter><parameter name="arg"><paramtype><emphasis>unspecified</emphasis></paramtype></parameter></constructor>
</struct><struct-specialization name="binder1st"><template>
      <template-type-parameter name="FunT"/>
      <template-type-parameter name="FirstArgT"/>
    </template><specialization><template-arg>FunT &amp;</template-arg><template-arg>FirstArgT</template-arg></specialization><purpose>First argument binder. </purpose><typedef name="result_type"><type>remove_cv&lt; FunT &gt;::type::result_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T0"/>
        </template><parameter name="arg0"><paramtype>T0 const &amp;</paramtype></parameter></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T0"/>
          <template-type-parameter name="T1"/>
        </template><parameter name="arg0"><paramtype>T0 const &amp;</paramtype></parameter><parameter name="arg1"><paramtype>T1 const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="fun"><paramtype>FunT &amp;</paramtype></parameter><parameter name="arg"><paramtype><emphasis>unspecified</emphasis></paramtype></parameter></constructor>
</struct-specialization><struct name="binder2nd"><template>
      <template-type-parameter name="FunT"/>
      <template-type-parameter name="SecondArgT"/>
    </template><inherit access="private">FunT</inherit><purpose>Second argument binder. </purpose><typedef name="result_type"><type>FunT::result_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="arg"><paramtype>T const &amp;</paramtype></parameter></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T0"/>
          <template-type-parameter name="T1"/>
        </template><parameter name="arg0"><paramtype>T0 const &amp;</paramtype></parameter><parameter name="arg1"><paramtype>T1 const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter><parameter name="arg"><paramtype><emphasis>unspecified</emphasis></paramtype></parameter></constructor>
</struct><struct-specialization name="binder2nd"><template>
      <template-type-parameter name="FunT"/>
      <template-type-parameter name="SecondArgT"/>
    </template><specialization><template-arg>FunT &amp;</template-arg><template-arg>SecondArgT</template-arg></specialization><purpose>Second argument binder. </purpose><typedef name="result_type"><type>remove_cv&lt; FunT &gt;::type::result_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="arg"><paramtype>T const &amp;</paramtype></parameter></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T0"/>
          <template-type-parameter name="T1"/>
        </template><parameter name="arg0"><paramtype>T0 const &amp;</paramtype></parameter><parameter name="arg1"><paramtype>T1 const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="fun"><paramtype>FunT &amp;</paramtype></parameter><parameter name="arg"><paramtype><emphasis>unspecified</emphasis></paramtype></parameter></constructor>
</struct-specialization><struct name="binder3rd"><template>
      <template-type-parameter name="FunT"/>
      <template-type-parameter name="ThirdArgT"/>
    </template><inherit access="private">FunT</inherit><purpose>Third argument binder. </purpose><typedef name="result_type"><type>FunT::result_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T0"/>
          <template-type-parameter name="T1"/>
        </template><parameter name="arg0"><paramtype>T0 const &amp;</paramtype></parameter><parameter name="arg1"><paramtype>T1 const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter><parameter name="arg"><paramtype><emphasis>unspecified</emphasis></paramtype></parameter></constructor>
</struct><struct-specialization name="binder3rd"><template>
      <template-type-parameter name="FunT"/>
      <template-type-parameter name="ThirdArgT"/>
    </template><specialization><template-arg>FunT &amp;</template-arg><template-arg>ThirdArgT</template-arg></specialization><purpose>Third argument binder. </purpose><typedef name="result_type"><type>remove_cv&lt; FunT &gt;::type::result_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T0"/>
          <template-type-parameter name="T1"/>
        </template><parameter name="arg0"><paramtype>T0 const &amp;</paramtype></parameter><parameter name="arg1"><paramtype>T1 const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="fun"><paramtype>FunT &amp;</paramtype></parameter><parameter name="arg"><paramtype><emphasis>unspecified</emphasis></paramtype></parameter></constructor>
</struct-specialization>







































































<function name="bind1st"><type><classname>binder1st</classname>&lt; FunT, FirstArgT &gt;</type><template>
          <template-type-parameter name="FunT"/>
          <template-type-parameter name="FirstArgT"/>
        </template><parameter name="fun"><paramtype>FunT</paramtype></parameter><parameter name="arg"><paramtype>FirstArgT const &amp;</paramtype></parameter></function>
<function name="bind1st"><type><classname>binder1st</classname>&lt; FunT, FirstArgT &gt;</type><template>
          <template-type-parameter name="FunT"/>
          <template-type-parameter name="FirstArgT"/>
        </template><parameter name="fun"><paramtype>FunT</paramtype></parameter><parameter name="arg"><paramtype>FirstArgT &amp;</paramtype></parameter></function>
<function name="bind2nd"><type><classname>binder2nd</classname>&lt; FunT, SecondArgT &gt;</type><template>
          <template-type-parameter name="FunT"/>
          <template-type-parameter name="SecondArgT"/>
        </template><parameter name="fun"><paramtype>FunT</paramtype></parameter><parameter name="arg"><paramtype>SecondArgT const &amp;</paramtype></parameter></function>
<function name="bind2nd"><type><classname>binder2nd</classname>&lt; FunT, SecondArgT &gt;</type><template>
          <template-type-parameter name="FunT"/>
          <template-type-parameter name="SecondArgT"/>
        </template><parameter name="fun"><paramtype>FunT</paramtype></parameter><parameter name="arg"><paramtype>SecondArgT &amp;</paramtype></parameter></function>
<function name="bind3rd"><type><classname>binder3rd</classname>&lt; FunT, ThirdArgT &gt;</type><template>
          <template-type-parameter name="FunT"/>
          <template-type-parameter name="ThirdArgT"/>
        </template><parameter name="fun"><paramtype>FunT</paramtype></parameter><parameter name="arg"><paramtype>ThirdArgT const &amp;</paramtype></parameter></function>
<function name="bind3rd"><type><classname>binder3rd</classname>&lt; FunT, ThirdArgT &gt;</type><template>
          <template-type-parameter name="FunT"/>
          <template-type-parameter name="ThirdArgT"/>
        </template><parameter name="fun"><paramtype>FunT</paramtype></parameter><parameter name="arg"><paramtype>ThirdArgT &amp;</paramtype></parameter></function>





































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/bind_assign.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains a function object that assigns the received value to the bound object. This is a lightweight alternative to what Boost.Phoenix and Boost.Lambda provides. </para><namespace name="boost">
<namespace name="log">
<struct name="assign_fun"><purpose>The function object that assigns its second operand to the first one. </purpose><typedef name="result_type"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><template>
          <template-type-parameter name="LeftT"/>
          <template-type-parameter name="RightT"/>
        </template><parameter name="assignee"><paramtype>LeftT &amp;</paramtype></parameter><parameter name="val"><paramtype>RightT const &amp;</paramtype></parameter></method>
</method-group>
</struct>






































































<function name="bind_assign"><type><classname>binder1st</classname>&lt; <classname>assign_fun</classname>, AssigneeT &amp; &gt;</type><template>
          <template-type-parameter name="AssigneeT"/>
        </template><parameter name="assignee"><paramtype>AssigneeT &amp;</paramtype></parameter></function>











































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/bind_output.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains a function object that puts the received value to the bound stream. This is a lightweight alternative to what Boost.Phoenix and Boost.Lambda provides. </para><namespace name="boost">
<namespace name="log">
<struct name="output_fun"><purpose>The function object that outputs its second operand to the first one. </purpose><typedef name="result_type"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="val"><paramtype>T const &amp;</paramtype></parameter></method>
</method-group>
</struct>





































































<function name="bind_output"><type><classname>binder1st</classname>&lt; <classname>output_fun</classname>, StreamT &amp; &gt;</type><template>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter></function>












































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/bind_to_log.hpp">
<para><para>Andrey Semashev </para>

<para>06.11.2012</para>

This header contains a function object that puts the received value to the bound stream using the <computeroutput>to_log</computeroutput> manipulator. This is a lightweight alternative to what Boost.Phoenix and Boost.Lambda provides. </para><namespace name="boost">
<namespace name="log">
<struct name="to_log_fun"><template>
      <template-type-parameter name="TagT"><default>void</default></template-type-parameter>
    </template><purpose>The function object that outputs its second operand to the first one. </purpose><typedef name="result_type"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="val"><paramtype>T const &amp;</paramtype></parameter></method>
</method-group>
</struct><struct-specialization name="to_log_fun"><template>
    </template><specialization><template-arg>void</template-arg></specialization><purpose>The function object that outputs its second operand to the first one. </purpose><typedef name="result_type"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="val"><paramtype>T const &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization>



































































<function name="bind_to_log"><type><classname>binder1st</classname>&lt; <classname>to_log_fun</classname>&lt; &gt;, StreamT &amp; &gt;</type><template>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter></function>
<function name="bind_to_log"><type><classname>binder1st</classname>&lt; <classname>to_log_fun</classname>&lt; TagT &gt;, StreamT &amp; &gt;</type><template>
          <template-type-parameter name="TagT"/>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter></function>













































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/contains.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains a predicate for checking if the provided string contains a substring. </para><namespace name="boost">
<namespace name="log">
<struct name="contains_fun"><purpose>The <computeroutput>contains</computeroutput> functor. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></method>
</method-group>
</struct>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/ends_with.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains a predicate for checking if the provided string ends with a substring. </para><namespace name="boost">
<namespace name="log">
<struct name="ends_with_fun"><purpose>The <computeroutput>ends_with</computeroutput> functor. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></method>
</method-group>
</struct>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/fun_ref.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains function object reference adapter. The adapter stores a reference to external function object and forwards all calls to the referred function. </para><namespace name="boost">
<namespace name="log">
<struct name="function_reference_wrapper"><template>
      <template-type-parameter name="FunT"/>
    </template><purpose>Reference wrapper for function objects. </purpose><typedef name="result_type"><type>FunT::result_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-nontype-parameter name="ArgsT"><type>typename...</type></template-nontype-parameter>
        </template><parameter name="args"><paramtype>ArgsT const &amp;...</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="fun"><paramtype>FunT &amp;</paramtype></parameter></constructor>
</struct>


































































<function name="fun_ref"><type><classname>function_reference_wrapper</classname>&lt; FunT &gt;</type><template>
          <template-type-parameter name="FunT"/>
        </template><parameter name="fun"><paramtype>FunT &amp;</paramtype></parameter></function>















































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/in_range.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains a predicate for checking if the provided value is within a half-open range. </para><namespace name="boost">
<namespace name="log">
<struct name="in_range_fun"><purpose>The in_range functor. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="value"><paramtype>T const &amp;</paramtype></parameter><parameter name="rng"><paramtype>std::pair&lt; U, U &gt; const &amp;</paramtype></parameter></method>
</method-group>
<method-group name="private static functions">
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="value"><paramtype>T const &amp;</paramtype></parameter><parameter name="rng"><paramtype>std::pair&lt; U, U &gt; const &amp;</paramtype></parameter><parameter name=""><paramtype>false_type</paramtype></parameter></method>
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="value"><paramtype>T const &amp;</paramtype></parameter><parameter name="rng"><paramtype>std::pair&lt; U, U &gt; const &amp;</paramtype></parameter><parameter name=""><paramtype>true_type</paramtype></parameter></method>
</method-group>
</struct>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/logical.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains logical predicates for value comparison, analogous to <computeroutput>std::less</computeroutput>, <computeroutput>std::greater</computeroutput> and others. The main difference from the standard equivalents is that the predicates defined in this header are not templates and therefore do not require a fixed argument type. Furthermore, both arguments may have different types, in which case the comparison is performed without type conversion.</para><para><note><para>In case if arguments are integral, the conversion is performed according to the standard C++ rules in order to avoid warnings from the compiler. </para>
</note>
</para><namespace name="boost">
<namespace name="log">
<struct name="equal_to"><purpose>Equality predicate. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></method>
</method-group>
<method-group name="private static functions">
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>false_type</paramtype></parameter></method>
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>true_type</paramtype></parameter></method>
</method-group>
</struct><struct name="greater"><purpose>Greater predicate. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></method>
</method-group>
<method-group name="private static functions">
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>false_type</paramtype></parameter></method>
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>true_type</paramtype></parameter></method>
</method-group>
</struct><struct name="greater_equal"><purpose>Greater or equal predicate. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></method>
</method-group>
<method-group name="private static functions">
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>false_type</paramtype></parameter></method>
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>true_type</paramtype></parameter></method>
</method-group>
</struct><struct name="less"><purpose>Less predicate. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></method>
</method-group>
<method-group name="private static functions">
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>false_type</paramtype></parameter></method>
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>true_type</paramtype></parameter></method>
</method-group>
</struct><struct name="less_equal"><purpose>Less or equal predicate. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></method>
</method-group>
<method-group name="private static functions">
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>false_type</paramtype></parameter></method>
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>true_type</paramtype></parameter></method>
</method-group>
</struct><struct name="not_equal_to"><purpose>Inequality predicate. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></method>
</method-group>
<method-group name="private static functions">
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>false_type</paramtype></parameter></method>
<method name="op" specifiers="static"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype>T const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter><parameter name=""><paramtype>true_type</paramtype></parameter></method>
</method-group>
</struct>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/matches.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains a predicate for checking if the provided string matches a regular expression. </para><namespace name="boost">
<namespace name="log">
<struct name="matches_fun"><purpose>The regex matching functor. </purpose><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="StringT"/>
          <template-type-parameter name="ExpressionT"/>
        </template><parameter name="str"><paramtype>StringT const &amp;</paramtype></parameter><parameter name="expr"><paramtype>ExpressionT const &amp;</paramtype></parameter></method>
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="StringT"/>
          <template-type-parameter name="ExpressionT"/>
          <template-type-parameter name="ArgT"/>
        </template><parameter name="str"><paramtype>StringT const &amp;</paramtype></parameter><parameter name="expr"><paramtype>ExpressionT const &amp;</paramtype></parameter><parameter name="arg"><paramtype>ArgT const &amp;</paramtype></parameter></method>
</method-group>
</struct>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/nop.hpp">
<para><para>Andrey Semashev </para>

<para>30.03.2008</para>

This header contains a function object that does nothing. </para><namespace name="boost">
<namespace name="log">
<struct name="nop"><purpose>The function object that does nothing. </purpose><typedef name="result_type"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const noexcept"><type>void</type></method>
<method name="operator()" cv="const noexcept"><type>void</type><template>
          <template-nontype-parameter name="ArgsT"><type>typename...</type></template-nontype-parameter>
        </template><parameter name="..."><paramtype>ArgsT const &amp;</paramtype></parameter></method>
</method-group>
</struct>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/functional/save_result.hpp">
<para><para>Andrey Semashev </para>

<para>19.01.2013</para>

This header contains function object adapter that saves the result of the adopted function to an external variable. </para><namespace name="boost">
<namespace name="log">
<struct name="save_result_wrapper"><template>
      <template-type-parameter name="FunT"/>
      <template-type-parameter name="AssigneeT"/>
    </template><purpose>Function object wrapper for saving the adopted function object result. </purpose><typedef name="result_type"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="ArgT"/>
        </template><parameter name="arg"><paramtype>ArgT const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="fun"><paramtype>FunT</paramtype></parameter><parameter name="assignee"><paramtype>AssigneeT &amp;</paramtype></parameter></constructor>
</struct>

































































<function name="save_result"><type><classname>save_result_wrapper</classname>&lt; FunT, AssigneeT &gt;</type><template>
          <template-type-parameter name="FunT"/>
          <template-type-parameter name="AssigneeT"/>
        </template><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter><parameter name="assignee"><paramtype>AssigneeT &amp;</paramtype></parameter></function>
















































</namespace>
</namespace>
</header>
<header name="boost/log/utility/ipc/object_name.hpp">
<para><para>Andrey Semashev </para>

<para>05.03.2016</para>

The header contains declaration of a system object name wrapper. </para><namespace name="boost">
<namespace name="log">
<namespace name="ipc">
<class name="object_name"><purpose>A system object name class. </purpose><description><para>In order to identify a system-wide object such as a shared memory segment or a named synchronization primitive the object has to be given a name. The format of the name is specific to the operating system and the <computeroutput><classname alt="boost::log::ipc::object_name">object_name</classname></computeroutput> class provides an abstraction for names of objects. It also provides means for scoping, which allows to avoid name clashes between different processes.</para><para>The object name is a UTF-8 encoded string. The portable object name should consist of the following characters:</para><programlisting>
A B C D E F G H I J K L M N O P Q R S T U V W X Y Z
a b c d e f g h i j k l m n o p q r s t u v w x y z
0 1 2 3 4 5 6 7 8 9 . _ -
</programlisting><para><note><para>The character set corresponds to the POSIX Portable Filename Character Set (<ulink url="http://pubs.opengroup.org/onlinepubs/9699919799/basedefs/V1_chap03.html#tag_03_278">http://pubs.opengroup.org/onlinepubs/9699919799/basedefs/V1_chap03.html#tag_03_278</ulink>).</para>
</note>
Use of other characters may result in non-portable system-specific behavior.</para><para>The name can have one of the following scopes:</para><para><itemizedlist>
<listitem><para><computeroutput>global</computeroutput> - objects within this scope are visible to any process on the system. In order to use this scope the process may need to have extended privileges. This scope is not available for Windows Store applications. </para>
</listitem>
<listitem><para><computeroutput>user</computeroutput> - objects within this scope can be opened by processes running under the same user as the current process. </para>
</listitem>
<listitem><para><computeroutput>session</computeroutput> - objects within this scope are visible to processes within the session of the current process. The definition of a session may vary between operating systems. On POSIX, a session is typically a group of processes attached to a single virtual terminal device. On Windows a session is started when a user logs into the system. There is also a separate session for Windows services. </para>
</listitem>
<listitem><para><computeroutput>process_group</computeroutput> - objects within this scope are visible to processes within the process group of the current process. Currently, on Windows all processes running in the current session are considered members of the same process group. This may change in future.</para>
</listitem>
</itemizedlist>
The scopes are not overlapping. For instance, if an object is created in the global scope, the object cannot be opened with the same name but in user's scope.</para><para>Note that name scoping is not a security feature. On some systems any process on the system has technical capability to open objects within any scope. The scope is only used to help avoid name clashes between processes using <computeroutput><classname alt="boost::log::ipc::object_name">object_name</classname></computeroutput> to identify objects. </para></description><enum name="scope"><enumvalue name="global"><purpose>The name has global scope; any process in the system has the potential to open the resource identified by the name. </purpose></enumvalue><enumvalue name="user"><purpose>The name is limited to processes running under the current user. </purpose></enumvalue><enumvalue name="session"><purpose>The name is limited to processes running in the current login session. </purpose></enumvalue><enumvalue name="process_group"><purpose>The name is limited to processes running in the current process group. </purpose></enumvalue><purpose>Name scopes. </purpose></enum>
<method-group name="public member functions">
<method name="empty" cv="const noexcept"><type>bool</type><description><para>Returns <computeroutput>true</computeroutput> if the object name is empty </para></description></method>
<method name="size" cv="const noexcept"><type>std::size_t</type><description><para>Returns length of the name, in bytes </para></description></method>
<method name="c_str" cv="const noexcept"><type>const char *</type><description><para>Returns the name string </para></description></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="that"><paramtype><classname>object_name</classname> &amp;</paramtype></parameter><description><para>Swaps the object name with another object name </para></description></method>
</method-group>
<constructor cv="noexcept"><description><para>Default constructor. The method creates an empty object name.</para><para>
</para></description><postconditions><para><computeroutput>empty() == true</computeroutput> </para>
</postconditions></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>object_name</classname> &amp;&amp;</paramtype></parameter><description><para>Move constructor. </para></description></constructor>
<constructor><parameter name="that"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Copy constructor. </para></description></constructor>
<constructor><parameter name="ns"><paramtype>scope</paramtype><description><para>The scope of the object name </para></description></parameter><parameter name="str"><paramtype>const char *</paramtype><description><para>The object name, must not be NULL. </para></description></parameter><description><para>Constructor from the object name 
</para></description></constructor>
<constructor><parameter name="ns"><paramtype>scope</paramtype><description><para>The scope of the object name </para></description></parameter><parameter name="str"><paramtype>std::string const &amp;</paramtype><description><para>The object name </para></description></parameter><description><para>Constructor from the object name 
</para></description></constructor>
<copy-assignment cv="noexcept"><type><classname>object_name</classname> &amp;</type><parameter name="that"><paramtype><classname>object_name</classname> &amp;&amp;</paramtype></parameter><description><para>Move assignment </para></description></copy-assignment>
<copy-assignment><type><classname>object_name</classname> &amp;</type><parameter name="that"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Copy assignment </para></description></copy-assignment>
<method-group name="public static functions">
<method name="from_native" specifiers="static"><type><classname>object_name</classname></type><parameter name="str"><paramtype>const char *</paramtype><description><para>The object name string, must not be <computeroutput>NULL</computeroutput>. The string format is specific to the operating system. </para></description></parameter><description><para>Constructor from the native string.</para><para>
</para></description></method>
<method name="from_native" specifiers="static"><type><classname>object_name</classname></type><parameter name="str"><paramtype>std::string const &amp;</paramtype><description><para>The object name string. The string format is specific to the operating system. </para></description></parameter><description><para>Constructor from the native string.</para><para>
</para></description></method>
</method-group>
<method-group name="friend functions">
<method name="swap" cv="noexcept"><type>friend void</type><parameter name="left"><paramtype><classname>object_name</classname> &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>object_name</classname> &amp;</paramtype></parameter><description><para>Swaps two object names </para></description></method>
<method name="to_string"><type>friend std::string</type><parameter name="name"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Returns string representation of the object name </para></description></method>
<method name="operator==" cv="noexcept"><type>friend bool</type><parameter name="left"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Equality operator </para></description></method>
<method name="operator!=" cv="noexcept"><type>friend bool</type><parameter name="left"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Inequality operator </para></description></method>
<method name="operator&lt;" cv="noexcept"><type>friend bool</type><parameter name="left"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Less operator </para></description></method>
<method name="operator&gt;" cv="noexcept"><type>friend bool</type><parameter name="left"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Greater operator </para></description></method>
<method name="operator&lt;=" cv="noexcept"><type>friend bool</type><parameter name="left"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Less or equal operator </para></description></method>
<method name="operator&gt;=" cv="noexcept"><type>friend bool</type><parameter name="left"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Greater or equal operator </para></description></method>
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, TraitsT &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
        </template><parameter name="strm"><paramtype>std::basic_ostream&lt; CharT, TraitsT &gt; &amp;</paramtype></parameter><parameter name="name"><paramtype><classname>object_name</classname> const &amp;</paramtype></parameter><description><para>Stream ouput operator </para></description></method>
</method-group>
</class></namespace>



















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/ipc/reliable_message_queue.hpp">
<para><para>Lingxi Li </para>

<para>Andrey Semashev </para>

<para>01.01.2016</para>

The header contains declaration of a reliable interprocess message queue. </para><namespace name="boost">
<namespace name="log">
<namespace name="ipc">
<class name="reliable_message_queue"><purpose>A reliable interprocess message queue. </purpose><description><para>The queue implements a reliable one-way channel of passing messages from one or multiple writers to a single reader. The format of the messages is user-defined and must be consistent across all writers and the reader. The queue does not enforce any specific format of the messages, other than they should be supplied as a contiguous array of bytes.</para><para>The queue internally uses a process-shared storage identified by an <computeroutput><classname alt="boost::log::ipc::object_name">object_name</classname></computeroutput> (the queue name). Refer to <computeroutput><classname alt="boost::log::ipc::object_name">object_name</classname></computeroutput> documentation for details on restrictions imposed on object names.</para><para>The queue storage is organized as a fixed number of blocks of a fixed size. The block size must be an integer power of 2 and is expressed in bytes. Each written message, together with some metadata added by the queue, consumes an integer number of blocks. Each read message received by the reader releases the blocks allocated for that message. As such the maximum size of a message is slightly less than block size times capacity of the queue. For efficiency, it is recommended to choose block size large enough to accommodate most of the messages to be passed through the queue.</para><para>The queue is considered empty when no messages are enqueued (all blocks are free). The queue is considered full at the point of enqueueing a message when there is not enough free blocks to accommodate the message.</para><para>The queue is reliable in that it will not drop successfully sent messages that are not received by the reader, other than the case when a non-empty queue is destroyed by the last user. If a message cannot be enqueued by the writer because the queue is full, the queue can either block the writer or return an error or throw an exception, depending on the policy specified at the queue creation. The policy is object local, i.e. different writers and the reader can have different overflow policies.</para><para>If the queue is empty and the reader attempts to dequeue a message, it will block until a message is enqueued by a writer.</para><para>A blocked reader or writer can be unblocked by calling <computeroutput>stop_local</computeroutput>. After this method is called, all threads blocked on this particular object are released and return <computeroutput>operation_result::aborted</computeroutput>. The other instances of the queue (in the current or other processes) are unaffected. In order to restore the normal functioning of the queue instance after the <computeroutput>stop_local</computeroutput> call the user has to invoke <computeroutput>reset_local</computeroutput>.</para><para>The queue does not guarantee any particular order of received messages from different writer threads. Messages sent by a particular writer thread will be received in the order of sending.</para><para>Methods of this class are not thread-safe, unless otherwise specified. </para></description><enum name="operation_result"><enumvalue name="succeeded"><purpose>The operation has completed successfully. </purpose></enumvalue><enumvalue name="no_space"><purpose>The message could not be sent because the queue is full. </purpose></enumvalue><enumvalue name="aborted"><purpose>The operation has been aborted because the queue method <computeroutput>stop_local()</computeroutput> has been called. </purpose></enumvalue><purpose>Result codes for various operations on the queue. </purpose></enum>
<enum name="overflow_policy"><enumvalue name="block_on_overflow"><purpose>Block the send operation when the queue is full. </purpose></enumvalue><enumvalue name="fail_on_overflow"><purpose>Return <computeroutput>operation_result::no_space</computeroutput> when the queue is full. </purpose></enumvalue><enumvalue name="throw_on_overflow"><purpose>Throw <computeroutput>capacity_limit_reached</computeroutput> exception when the queue is full. </purpose></enumvalue><purpose>Interprocess queue overflow policies. </purpose></enum>
<typedef name="size_type"><purpose>Queue message size type. </purpose><type>uint32_t</type></typedef>
<method-group name="public member functions">
<method name="swap" cv="noexcept"><type>void</type><parameter name="that"><paramtype><classname>reliable_message_queue</classname> &amp;</paramtype><description><para>The other object to swap with. </para></description></parameter><description><para>The method swaps the object with <emphasis>that</emphasis>.</para><para>
</para></description></method>
<method name="create"><type>void</type><parameter name="name"><paramtype><classname>object_name</classname> const &amp;</paramtype><description><para>Name of the message queue to be associated with. </para></description></parameter><parameter name="capacity"><paramtype>uint32_t</paramtype><description><para>Maximum number of allocation blocks the queue can hold. </para></description></parameter><parameter name="block_size"><paramtype>size_type</paramtype><description><para>Size in bytes of allocation block. Must be a power of 2. </para></description></parameter><parameter name="oflow_policy"><paramtype>overflow_policy</paramtype><default>block_on_overflow</default><description><para>Queue behavior policy in case of overflow. </para></description></parameter><parameter name="perms"><paramtype><classname>permissions</classname> const &amp;</paramtype><default><classname alt="boost::log::permissions">permissions</classname>()</default><description><para>Access permissions for the associated message queue. </para></description></parameter><description><para>The method creates the message queue to be associated with the object. After the call, the object will be in running state if a message queue is successfully created.</para><para>


</para></description><requires><para><computeroutput>is_open() == false</computeroutput> </para>
</requires><postconditions><para><computeroutput>is_open() == true</computeroutput></para>
</postconditions></method>
<method name="open_or_create"><type>void</type><parameter name="name"><paramtype><classname>object_name</classname> const &amp;</paramtype><description><para>Name of the message queue to be associated with. </para></description></parameter><parameter name="capacity"><paramtype>uint32_t</paramtype><description><para>Maximum number of allocation blocks the queue can hold. </para></description></parameter><parameter name="block_size"><paramtype>size_type</paramtype><description><para>Size in bytes of allocation block. Must be a power of 2. </para></description></parameter><parameter name="oflow_policy"><paramtype>overflow_policy</paramtype><default>block_on_overflow</default><description><para>Queue behavior policy in case of overflow. </para></description></parameter><parameter name="perms"><paramtype><classname>permissions</classname> const &amp;</paramtype><default><classname alt="boost::log::permissions">permissions</classname>()</default><description><para>Access permissions for the associated message queue. </para></description></parameter><description><para>The method creates or opens the message queue to be associated with the object. After the call, the object will be in running state if a message queue is successfully created or opened. If the message queue that is identified by the name already exists then the other queue parameters are ignored. The actual queue parameters can be obtained with accessors from this object after this method returns.</para><para>


</para></description><requires><para><computeroutput>is_open() == false</computeroutput> </para>
</requires><postconditions><para><computeroutput>is_open() == true</computeroutput></para>
</postconditions></method>
<method name="open"><type>void</type><parameter name="name"><paramtype><classname>object_name</classname> const &amp;</paramtype><description><para>Name of the message queue to be associated with. </para></description></parameter><parameter name="oflow_policy"><paramtype>overflow_policy</paramtype><default>block_on_overflow</default><description><para>Queue behavior policy in case of overflow. </para></description></parameter><parameter name="perms"><paramtype><classname>permissions</classname> const &amp;</paramtype><default><classname alt="boost::log::permissions">permissions</classname>()</default><description><para>Access permissions for the associated message queue. The permissions will only be used if the queue implementation has to create system objects while operating. This parameter is currently not used on POSIX systems. </para></description></parameter><description><para>The method opens the existing message queue to be associated with the object. After the call, the object will be in running state if a message queue is successfully opened.</para><para>


</para></description><requires><para><computeroutput>is_open() == false</computeroutput> </para>
</requires><postconditions><para><computeroutput>is_open() == true</computeroutput></para>
</postconditions></method>
<method name="is_open" cv="const noexcept"><type>bool</type><description><para>Tests whether the object is associated with any message queue.</para><para>
</para></description><returns><para><computeroutput>true</computeroutput> if the object is associated with a message queue, and <computeroutput>false</computeroutput> otherwise. </para>
</returns></method>
<method name="clear"><type>void</type><description><para>This method empties the associated message queue. Concurrent calls to this method, <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, and <computeroutput>stop_local()</computeroutput> are allowed.</para><para>
</para></description><requires><para><computeroutput>is_open() == true</computeroutput> </para>
</requires></method>
<method name="name" cv="const"><type><classname>object_name</classname> const  &amp;</type><description><para>The method returns the name of the associated message queue.</para><para>

</para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires><returns><para>Name of the associated message queue </para>
</returns></method>
<method name="capacity" cv="const"><type>uint32_t</type><description><para>The method returns the maximum number of allocation blocks the associated message queue can hold. Note that the returned value may be different from the corresponding value passed to the constructor or <computeroutput>open_or_create()</computeroutput>, for the message queue may not have been created by this object.</para><para>

</para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires><returns><para>Maximum number of allocation blocks the associated message queue can hold. </para>
</returns></method>
<method name="block_size" cv="const"><type>size_type</type><description><para>The method returns the allocation block size, in bytes. Each message in the associated message queue consumes an integer number of allocation blocks. Note that the returned value may be different from the corresponding value passed to the constructor or <computeroutput>open_or_create()</computeroutput>, for the message queue may not have been created by this object.</para><para>

</para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires><returns><para>Allocation block size, in bytes. </para>
</returns></method>
<method name="stop_local"><type>void</type><description><para>The method wakes up all threads that are blocked in calls to <computeroutput>send()</computeroutput> or <computeroutput>receive()</computeroutput>. Those calls would then return <computeroutput>operation_result::aborted</computeroutput>. Note that, the method does not block until the woken-up threads have actually returned from <computeroutput>send()</computeroutput> or <computeroutput>receive()</computeroutput>. Other means is needed to ensure that calls to <computeroutput>send()</computeroutput> or <computeroutput>receive()</computeroutput> have returned, e.g., joining the threads that might be blocking on the calls.</para><para>The method also puts the object in stopped state. When in stopped state, calls to <computeroutput>send()</computeroutput> or <computeroutput>receive()</computeroutput> will return immediately with return value <computeroutput>operation_result::aborted</computeroutput> when they would otherwise block in running state.</para><para>Concurrent calls to this method, <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, and <computeroutput>clear()</computeroutput> are allowed.</para><para>
</para></description><requires><para><computeroutput>is_open() == true</computeroutput> </para>
</requires></method>
<method name="reset_local"><type>void</type><description><para>The method puts the object in running state where calls to <computeroutput>send()</computeroutput> or <computeroutput>receive()</computeroutput> may block. This method is not thread-safe.</para><para>
</para></description><requires><para><computeroutput>is_open() == true</computeroutput> </para>
</requires></method>
<method name="close" cv="noexcept"><type>void</type><description><para>The method disassociates the associated message queue, if any. No other threads should be using this object before calling this method. The <computeroutput>stop_local()</computeroutput> method can be used to have any threads currently blocked in <computeroutput>send()</computeroutput> or <computeroutput>receive()</computeroutput> return, and prevent further calls to them from blocking. Typically, before calling this method, one would first call <computeroutput>stop_local()</computeroutput> and then join all threads that might be blocking on <computeroutput>send()</computeroutput> or <computeroutput>receive()</computeroutput> to ensure that they have returned from the calls. The associated message queue is destroyed if the object represents the last outstanding reference to it.</para><para>
</para></description><postconditions><para><computeroutput>is_open() == false</computeroutput> </para>
</postconditions></method>
<method name="send"><type>operation_result</type><parameter name="message_data"><paramtype>void const *</paramtype><description><para>The message data to send. Ignored when <computeroutput>message_size</computeroutput> is <computeroutput>0</computeroutput>. </para></description></parameter><parameter name="message_size"><paramtype>size_type</paramtype><description><para>Size of the message data in bytes. If the size is larger than the associated message queue capacity, an <computeroutput>std::logic_error</computeroutput> exception is thrown.</para></description></parameter><description><para>The method sends a message to the associated message queue. When the object is in running state and the queue has no free space for the message, the method either blocks or throws an exception, depending on the overflow policy that was specified on the queue opening/creation. If blocking policy is in effect, the blocking can be interrupted by calling <computeroutput>stop_local()</computeroutput>, in which case the method returns <computeroutput>operation_result::aborted</computeroutput>. When the object is already in the stopped state, the method does not block but returns immediately with return value <computeroutput>operation_result::aborted</computeroutput>.</para><para>It is possible to send an empty message by passing <computeroutput>0</computeroutput> to the parameter <computeroutput>message_size</computeroutput>.</para><para>Concurrent calls to <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, <computeroutput>stop_local()</computeroutput>, and <computeroutput>clear()</computeroutput> are allowed.</para><para>


<emphasis role="bold">Throws:</emphasis> <computeroutput>std::logic_error</computeroutput> in case if the message size exceeds the queue capacity, <computeroutput>system_error</computeroutput> in case if a native OS method fails. </para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires></method>
<method name="try_send"><type>bool</type><parameter name="message_data"><paramtype>void const *</paramtype><description><para>The message data to send. Ignored when <computeroutput>message_size</computeroutput> is <computeroutput>0</computeroutput>. </para></description></parameter><parameter name="message_size"><paramtype>size_type</paramtype><description><para>Size of the message data in bytes. If the size is larger than the maximum size allowed by the associated message queue, an <computeroutput>std::logic_error</computeroutput> exception is thrown.</para></description></parameter><description><para>The method performs an attempt to send a message to the associated message queue. The method is non-blocking, and always returns immediately. <computeroutput>boost::system::system_error</computeroutput> is thrown for errors resulting from native operating system calls. Note that it is possible to send an empty message by passing <computeroutput>0</computeroutput> to the parameter <computeroutput>message_size</computeroutput>. Concurrent calls to <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, <computeroutput>stop_local()</computeroutput>, and <computeroutput>clear()</computeroutput> are allowed.</para><para>


<emphasis role="bold">Throws:</emphasis> <computeroutput>std::logic_error</computeroutput> in case if the message size exceeds the queue capacity, <computeroutput>system_error</computeroutput> in case if a native OS method fails. </para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires><returns><para><computeroutput>true</computeroutput> if the message is successfully sent, and <computeroutput>false</computeroutput> otherwise (e.g., when the queue is full).</para>
</returns></method>
<method name="receive"><type>operation_result</type><parameter name="buffer"><paramtype>void *</paramtype><description><para>The memory buffer to store the received message in. </para></description></parameter><parameter name="buffer_size"><paramtype>size_type</paramtype><description><para>The size of the buffer, in bytes. </para></description></parameter><parameter name="message_size"><paramtype>size_type &amp;</paramtype><description><para>Receives the size of the received message, in bytes.</para></description></parameter><description><para>The method takes a message from the associated message queue. When the object is in running state and the queue is empty, the method blocks. The blocking is interrupted when <computeroutput>stop_local()</computeroutput> is called, in which case the method returns <computeroutput>operation_result::aborted</computeroutput>. When the object is already in the stopped state and the queue is empty, the method does not block but returns immediately with return value <computeroutput>operation_result::aborted</computeroutput>.</para><para>Concurrent calls to <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, <computeroutput>stop_local()</computeroutput>, and <computeroutput>clear()</computeroutput> are allowed.</para><para>


</para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires></method>
<method name="receive"><type>operation_result</type><template>
          <template-type-parameter name="ElementT"/>
          <template-nontype-parameter name="SizeV"><type>size_type</type></template-nontype-parameter>
        </template><parameter name="buffer"><paramtype>ElementT(&amp;)</paramtype><description><para>The memory buffer to store the received message in. </para></description></parameter><parameter name="message_size"><paramtype>size_type &amp;</paramtype><description><para>Receives the size of the received message, in bytes.</para></description></parameter><description><para>The method takes a message from the associated message queue. When the object is in running state and the queue is empty, the method blocks. The blocking is interrupted when <computeroutput>stop_local()</computeroutput> is called, in which case the method returns <computeroutput>operation_result::aborted</computeroutput>. When the object is already in the stopped state and the queue is empty, the method does not block but returns immediately with return value <computeroutput>operation_result::aborted</computeroutput>.</para><para>Concurrent calls to <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, <computeroutput>stop_local()</computeroutput>, and <computeroutput>clear()</computeroutput> are allowed.</para><para>


</para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires></method>
<method name="receive"><type>operation_result</type><template>
          <template-type-parameter name="ContainerT"/>
        </template><parameter name="container"><paramtype>ContainerT &amp;</paramtype><description><para>The container to store the received message in. The container should have value type of <computeroutput>char</computeroutput>, <computeroutput>signed char</computeroutput> or <computeroutput>unsigned char</computeroutput> and support inserting elements at the end.</para></description></parameter><description><para>The method takes a message from the associated message queue. When the object is in running state and the queue is empty, the method blocks. The blocking is interrupted when <computeroutput>stop_local()</computeroutput> is called, in which case the method returns <computeroutput>operation_result::aborted</computeroutput>. When the object is already in the stopped state and the queue is empty, the method does not block but returns immediately with return value <computeroutput>operation_result::aborted</computeroutput>.</para><para>Concurrent calls to <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, <computeroutput>stop_local()</computeroutput>, and <computeroutput>clear()</computeroutput> are allowed.</para><para>


</para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires></method>
<method name="try_receive"><type>bool</type><parameter name="buffer"><paramtype>void *</paramtype><description><para>The memory buffer to store the received message in. </para></description></parameter><parameter name="buffer_size"><paramtype>size_type</paramtype><description><para>The size of the buffer, in bytes. </para></description></parameter><parameter name="message_size"><paramtype>size_type &amp;</paramtype><description><para>Receives the size of the received message, in bytes.</para></description></parameter><description><para>The method performs an attempt to take a message from the associated message queue. The method is non-blocking, and always returns immediately.</para><para>Concurrent calls to <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, <computeroutput>stop_local()</computeroutput>, and <computeroutput>clear()</computeroutput> are allowed.</para><para>


</para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires><returns><para><computeroutput>true</computeroutput> if a message is successfully received, and <computeroutput>false</computeroutput> otherwise (e.g., when the queue is empty). </para>
</returns></method>
<method name="try_receive"><type>bool</type><template>
          <template-type-parameter name="ElementT"/>
          <template-nontype-parameter name="SizeV"><type>size_type</type></template-nontype-parameter>
        </template><parameter name="buffer"><paramtype>ElementT(&amp;)</paramtype><description><para>The memory buffer to store the received message in. </para></description></parameter><parameter name="message_size"><paramtype>size_type &amp;</paramtype><description><para>Receives the size of the received message, in bytes.</para></description></parameter><description><para>The method performs an attempt to take a message from the associated message queue. The method is non-blocking, and always returns immediately.</para><para>Concurrent calls to <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, <computeroutput>stop_local()</computeroutput>, and <computeroutput>clear()</computeroutput> are allowed.</para><para>


</para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires><returns><para><computeroutput>true</computeroutput> if a message is successfully received, and <computeroutput>false</computeroutput> otherwise (e.g., when the queue is empty). </para>
</returns></method>
<method name="try_receive"><type>bool</type><template>
          <template-type-parameter name="ContainerT"/>
        </template><parameter name="container"><paramtype>ContainerT &amp;</paramtype><description><para>The container to store the received message in. The container should have value type of <computeroutput>char</computeroutput>, <computeroutput>signed char</computeroutput> or <computeroutput>unsigned char</computeroutput> and support inserting elements at the end.</para></description></parameter><description><para>The method performs an attempt to take a message from the associated message queue. The method is non-blocking, and always returns immediately.</para><para>Concurrent calls to <computeroutput>send()</computeroutput>, <computeroutput>try_send()</computeroutput>, <computeroutput>receive()</computeroutput>, <computeroutput>try_receive()</computeroutput>, <computeroutput>stop_local()</computeroutput>, and <computeroutput>clear()</computeroutput> are allowed.</para><para>


</para></description><requires><para><computeroutput>is_open() == true</computeroutput></para>
</requires><returns><para><computeroutput>true</computeroutput> if a message is successfully received, and <computeroutput>false</computeroutput> otherwise (e.g., when the queue is empty). </para>
</returns></method>
</method-group>
<constructor cv="noexcept"><description><para>Default constructor. The method constructs an object that is not associated with any message queue.</para><para>
</para></description><postconditions><para><computeroutput>is_open() == false</computeroutput> </para>
</postconditions></constructor>
<constructor><parameter name=""><paramtype><classname>open_mode::create_only_tag</classname></paramtype></parameter><parameter name="name"><paramtype><classname>object_name</classname> const &amp;</paramtype><description><para>Name of the message queue to be associated with. </para></description></parameter><parameter name="capacity"><paramtype>uint32_t</paramtype><description><para>Maximum number of allocation blocks the queue can hold. </para></description></parameter><parameter name="block_size"><paramtype>size_type</paramtype><description><para>Size in bytes of allocation block. Must be a power of 2. </para></description></parameter><parameter name="oflow_policy"><paramtype>overflow_policy</paramtype><default>block_on_overflow</default><description><para>Queue behavior policy in case of overflow. </para></description></parameter><parameter name="perms"><paramtype><classname>permissions</classname> const &amp;</paramtype><default><classname alt="boost::log::permissions">permissions</classname>()</default><description><para>Access permissions for the associated message queue. </para></description></parameter><description><para>Constructor. The method is used to construct an object and create the associated message queue. The constructed object will be in running state if the message queue is successfully created.</para><para>

</para></description><postconditions><para><computeroutput>is_open() == true</computeroutput></para>
</postconditions></constructor>
<constructor><parameter name=""><paramtype><classname>open_mode::open_or_create_tag</classname></paramtype></parameter><parameter name="name"><paramtype><classname>object_name</classname> const &amp;</paramtype><description><para>Name of the message queue to be associated with. </para></description></parameter><parameter name="capacity"><paramtype>uint32_t</paramtype><description><para>Maximum number of allocation blocks the queue can hold. </para></description></parameter><parameter name="block_size"><paramtype>size_type</paramtype><description><para>Size in bytes of allocation block. Must be a power of 2. </para></description></parameter><parameter name="oflow_policy"><paramtype>overflow_policy</paramtype><default>block_on_overflow</default><description><para>Queue behavior policy in case of overflow. </para></description></parameter><parameter name="perms"><paramtype><classname>permissions</classname> const &amp;</paramtype><default><classname alt="boost::log::permissions">permissions</classname>()</default><description><para>Access permissions for the associated message queue. </para></description></parameter><description><para>Constructor. The method is used to construct an object and create or open the associated message queue. The constructed object will be in running state if the message queue is successfully created or opened. If the message queue that is identified by the name already exists then the other queue parameters are ignored. The actual queue parameters can be obtained with accessors from the constructed object.</para><para>

</para></description><postconditions><para><computeroutput>is_open() == true</computeroutput></para>
</postconditions></constructor>
<constructor><parameter name=""><paramtype><classname>open_mode::open_only_tag</classname></paramtype></parameter><parameter name="name"><paramtype><classname>object_name</classname> const &amp;</paramtype><description><para>Name of the message queue to be associated with. </para></description></parameter><parameter name="oflow_policy"><paramtype>overflow_policy</paramtype><default>block_on_overflow</default><description><para>Queue behavior policy in case of overflow. </para></description></parameter><parameter name="perms"><paramtype><classname>permissions</classname> const &amp;</paramtype><default><classname alt="boost::log::permissions">permissions</classname>()</default><description><para>Access permissions for the associated message queue. The permissions will only be used if the queue implementation has to create system objects while operating. This parameter is currently not used on POSIX systems. </para></description></parameter><description><para>Constructor. The method is used to construct an object and open the existing message queue. The constructed object will be in running state if the message queue is successfully opened.</para><para>

</para></description><postconditions><para><computeroutput>is_open() == true</computeroutput></para>
</postconditions></constructor>
<constructor specifiers="explicit"><template>
          <template-nontype-parameter name="Args"><type>typename...</type></template-nontype-parameter>
        </template><parameter name="args"><paramtype>Args const &amp;...</paramtype></parameter><description><para>Constructor with named parameters. The method is used to construct an object and create or open the associated message queue. The constructed object will be in running state if the message queue is successfully created.</para><para>The following named parameters are accepted:</para><para><itemizedlist>
<listitem><para>open_mode - One of the open mode tags: <computeroutput>open_mode::create_only</computeroutput>, <computeroutput>open_mode::open_only</computeroutput> or <computeroutput>open_mode::open_or_create</computeroutput>.</para>
</listitem><listitem><para>name - Name of the message queue to be associated with.</para>
</listitem><listitem><para>capacity - Maximum number of allocation blocks the queue can hold. Used only if the queue is created.</para>
</listitem><listitem><para>block_size - Size in bytes of allocation block. Must be a power of 2. Used only if the queue is created.</para>
</listitem><listitem><para>overflow_policy - Queue behavior policy in case of overflow, see <computeroutput>overflow_policy</computeroutput>.</para>
</listitem><listitem><para>permissions - Access permissions for the associated message queue.</para>
</listitem></itemizedlist>
</para><para>
</para></description><postconditions><para><computeroutput>is_open() == true</computeroutput> </para>
</postconditions></constructor>
<destructor><description><para>Destructor. Calls <computeroutput>close()</computeroutput>. </para></description></destructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>reliable_message_queue</classname> &amp;&amp;</paramtype><description><para>The object to be moved. </para></description></parameter><description><para>Move constructor. The method move-constructs an object from <computeroutput>other</computeroutput>. After the call, the constructed object becomes <computeroutput>other</computeroutput>, while <computeroutput>other</computeroutput> is left in default constructed state.</para><para>
</para></description></constructor>
<copy-assignment cv="noexcept"><type><classname>reliable_message_queue</classname> &amp;</type><parameter name="that"><paramtype><classname>reliable_message_queue</classname> &amp;&amp;</paramtype><description><para>The object to be moved.</para></description></parameter><description><para>Move assignment operator. If the object is associated with a message queue, <computeroutput>close()</computeroutput> is first called and the precondition to calling <computeroutput>close()</computeroutput> applies. After the call, the object becomes <emphasis>that</emphasis> while <emphasis>that</emphasis> is left in default constructed state.</para><para>

</para></description><returns><para>A reference to the assigned object. </para>
</returns></copy-assignment>
<method-group name="friend functions">
<method name="swap" cv="noexcept"><type>friend void</type><parameter name="a"><paramtype><classname>reliable_message_queue</classname> &amp;</paramtype></parameter><parameter name="b"><paramtype><classname>reliable_message_queue</classname> &amp;</paramtype></parameter><purpose>Swaps the two <computeroutput><classname alt="boost::log::ipc::reliable_message_queue">reliable_message_queue</classname></computeroutput> objects. </purpose></method>
</method-group>
<method-group name="public static functions">
<method name="remove" specifiers="static"><type>void</type><parameter name="name"><paramtype><classname>object_name</classname> const &amp;</paramtype><description><para>Name of the message queue to be removed. </para></description></parameter><description><para>The method frees system-wide resources, associated with the interprocess queue with the supplied name. The queue referred to by the specified name must not be opened in any process at the point of this call. After this call succeeds a new queue with the specified name can be created.</para><para>This call can be useful to recover from an earlier process misbehavior (e.g. a crash without properly closing the message queue). In this case resources allocated for the interprocess queue may remain allocated after the last process closed the queue, which in turn may prevent creating a new queue with the same name. By calling this method before creating a queue the application can attempt to ensure it starts with a clean slate.</para><para>On some platforms resources associated with the queue are automatically reclaimed by the operating system when the last process using those resources terminates (even if it terminates abnormally). On these platforms this call may be a no-op. However, portable code should still call this method at appropriate places to ensure compatibility with other platforms and future library versions, which may change implementation of the queue.</para><para>
</para></description></method>
</method-group>
</class></namespace>



















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/manipulators.hpp">
<para><para>Andrey Semashev </para>

<para>06.11.2012</para>

This header includes all manipulators. </para></header>
<header name="boost/log/utility/manipulators/add_value.hpp">
<para><para>Andrey Semashev </para>

<para>26.11.2012</para>

This header contains the <computeroutput>add_value</computeroutput> manipulator. </para><namespace name="boost">
<namespace name="log">
<class name="add_value_manip"><template>
      <template-type-parameter name="RefT"/>
    </template><purpose>Attribute value manipulator. </purpose><typedef name="reference_type"><purpose>Stored reference type. </purpose><type>RefT</type></typedef>
<typedef name="value_type"><purpose>Attribute value type. </purpose><type>remove_cv&lt; typename remove_reference&lt; reference_type &gt;::type &gt;::type</type></typedef>
<method-group name="public member functions">
<method name="get_name" cv="const"><type>attribute_name</type><purpose>Returns attribute name. </purpose></method>
<method name="get_value" cv="const"><type>get_value_result_type</type><purpose>Returns attribute value. </purpose></method>
</method-group>
<constructor><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="value"><paramtype>reference_type</paramtype></parameter><purpose>Initializing constructor. </purpose></constructor>
</class>













































































































<function name="operator&lt;&lt;"><type>basic_record_ostream&lt; CharT &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RefT"/>
        </template><parameter name="strm"><paramtype>basic_record_ostream&lt; CharT &gt; &amp;</paramtype></parameter><parameter name="manip"><paramtype><classname>add_value_manip</classname>&lt; RefT &gt; const &amp;</paramtype></parameter><purpose>The operator attaches an attribute value to the log record. </purpose></function>
<overloaded-function name="add_value"><signature><type><classname>add_value_manip</classname>&lt; T &amp;&amp; &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="value"><paramtype>T &amp;&amp;</paramtype></parameter></signature><signature><type><classname>add_value_manip</classname>&lt; typename DescriptorT::value_type &amp;&amp; &gt;</type><template>
          <template-type-parameter name="DescriptorT"/>
          <template-nontype-parameter name="ActorT"><type>template&lt; typename &gt; class</type></template-nontype-parameter>
        </template><parameter name=""><paramtype>expressions::attribute_keyword&lt; DescriptorT, ActorT &gt; const &amp;</paramtype></parameter><parameter name="value"><paramtype>typename DescriptorT::value_type &amp;&amp;</paramtype></parameter></signature><signature><type><classname>add_value_manip</classname>&lt; typename DescriptorT::value_type &amp; &gt;</type><template>
          <template-type-parameter name="DescriptorT"/>
          <template-nontype-parameter name="ActorT"><type>template&lt; typename &gt; class</type></template-nontype-parameter>
        </template><parameter name=""><paramtype>expressions::attribute_keyword&lt; DescriptorT, ActorT &gt; const &amp;</paramtype></parameter><parameter name="value"><paramtype>typename DescriptorT::value_type &amp;</paramtype></parameter></signature><signature><type><classname>add_value_manip</classname>&lt; typename DescriptorT::value_type const  &amp; &gt;</type><template>
          <template-type-parameter name="DescriptorT"/>
          <template-nontype-parameter name="ActorT"><type>template&lt; typename &gt; class</type></template-nontype-parameter>
        </template><parameter name=""><paramtype>expressions::attribute_keyword&lt; DescriptorT, ActorT &gt; const &amp;</paramtype></parameter><parameter name="value"><paramtype>typename DescriptorT::value_type const &amp;</paramtype></parameter></signature><purpose>The function creates a manipulator that attaches an attribute value to a log record. </purpose></overloaded-function>



</namespace>
</namespace>
</header>
<header name="boost/log/utility/manipulators/auto_newline.hpp">
<para><para>Andrey Semashev </para>

<para>23.06.2019</para>

The header contains implementation of a stream manipulator for inserting a newline, unless there is already one inserted. </para><namespace name="boost">
<namespace name="log">
<struct name="auto_newline_manip"><description><para>Stream manipulator for inserting a newline character, unless the last character inserted into the stream is already a newline. </para></description></struct><data-member name="auto_newline"><type>struct <classname>boost::log::auto_newline_manip</classname></type></data-member>













































































































<function name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname>&lt; CharT, TraitsT, AllocatorT &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
          <template-type-parameter name="AllocatorT"/>
        </template><parameter name="strm"><paramtype><classname>basic_formatting_ostream</classname>&lt; CharT, TraitsT, AllocatorT &gt; &amp;</paramtype></parameter><parameter name=""><paramtype><classname>auto_newline_manip</classname></paramtype></parameter><description><para>Stream output operator for the <computeroutput>auto_newline</computeroutput> manipulator </para></description></function>





</namespace>
</namespace>
</header>
<header name="boost/log/utility/manipulators/dump.hpp">
<para><para>Andrey Semashev </para>

<para>03.05.2013</para>

This header contains the <computeroutput>dump</computeroutput> output manipulator. </para><namespace name="boost">
<namespace name="log">
<class name="bounded_dump_manip"><inherit access="public">dump_manip</inherit><purpose>Manipulator for printing binary representation of the data with a size limit. </purpose><method-group name="public member functions">
<method name="get_max_size" cv="const noexcept"><type>std::size_t</type></method>
</method-group>
<constructor cv="noexcept"><parameter name="data"><paramtype>const void *</paramtype></parameter><parameter name="size"><paramtype>std::size_t</paramtype></parameter><parameter name="max_size"><paramtype>std::size_t</paramtype></parameter></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>bounded_dump_manip</classname> const &amp;</paramtype></parameter></constructor>
</class><class name="dump_manip"><purpose>Manipulator for printing binary representation of the data. </purpose><method-group name="public member functions">
<method name="get_data" cv="const noexcept"><type>const void *</type></method>
<method name="get_size" cv="const noexcept"><type>std::size_t</type></method>
</method-group>
<constructor cv="noexcept"><parameter name="data"><paramtype>const void *</paramtype></parameter><parameter name="size"><paramtype>std::size_t</paramtype></parameter></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>dump_manip</classname> const &amp;</paramtype></parameter></constructor>
</class>






































































































<function name="operator&lt;&lt;"><type>std::basic_ostream&lt; CharT, TraitsT &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
        </template><parameter name="strm"><paramtype>std::basic_ostream&lt; CharT, TraitsT &gt; &amp;</paramtype></parameter><parameter name="manip"><paramtype><classname>dump_manip</classname> const &amp;</paramtype></parameter><purpose>The operator outputs binary data to a stream. </purpose></function>
<function name="operator&lt;&lt;"><type>std::basic_ostream&lt; CharT, TraitsT &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
        </template><parameter name="strm"><paramtype>std::basic_ostream&lt; CharT, TraitsT &gt; &amp;</paramtype></parameter><parameter name="manip"><paramtype><classname>bounded_dump_manip</classname> const &amp;</paramtype></parameter><purpose>The operator outputs binary data to a stream. </purpose></function>
<function name="dump"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="data"><paramtype>T *</paramtype><description><para>The pointer to the beginning of the region </para></description></parameter><parameter name="size"><paramtype>std::size_t</paramtype><description><para>The size of the region, in bytes </para></description></parameter><purpose>Creates a stream manipulator that will output contents of a memory region in hexadecimal form. </purpose><description><para>

</para></description><returns><para>The manipulator that is to be put to a stream </para>
</returns></function>
<function name="dump_elements"><type><classname>dump_manip</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="data"><paramtype>T *</paramtype><description><para>The pointer to the beginning of the array </para></description></parameter><parameter name="count"><paramtype>std::size_t</paramtype><description><para>The size of the region, in number of <computeroutput>T</computeroutput> elements </para></description></parameter><purpose>Creates a stream manipulator that will dump elements of an array in hexadecimal form. </purpose><description><para>

</para></description><returns><para>The manipulator that is to be put to a stream </para>
</returns></function>
<function name="dump"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="data"><paramtype>T *</paramtype><description><para>The pointer to the beginning of the region </para></description></parameter><parameter name="size"><paramtype>std::size_t</paramtype><description><para>The size of the region, in bytes </para></description></parameter><parameter name="max_size"><paramtype>std::size_t</paramtype><description><para>The maximum number of bytes of the region to output </para></description></parameter><purpose>Creates a stream manipulator that will output contents of a memory region in hexadecimal form. </purpose><description><para>

</para></description><returns><para>The manipulator that is to be put to a stream </para>
</returns></function>
<function name="dump_elements"><type><classname>bounded_dump_manip</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="data"><paramtype>T *</paramtype><description><para>The pointer to the beginning of the array </para></description></parameter><parameter name="count"><paramtype>std::size_t</paramtype><description><para>The size of the region, in number of <computeroutput>T</computeroutput> elements </para></description></parameter><parameter name="max_count"><paramtype>std::size_t</paramtype><description><para>The maximum number of elements to output </para></description></parameter><purpose>Creates a stream manipulator that will dump elements of an array in hexadecimal form. </purpose><description><para>

</para></description><returns><para>The manipulator that is to be put to a stream </para>
</returns></function>






</namespace>
</namespace>
</header>
<header name="boost/log/utility/manipulators/invoke.hpp">
<para><para>Andrey Semashev </para>

<para>27.02.2022</para>

The header contains implementation of a stream manipulator for invoking a user-defined function. </para><namespace name="boost">
<namespace name="log">
<class name="invoke_manipulator"><template>
      <template-type-parameter name="FunctionT"/>
    </template><description><para>Stream manipulator for invoking a user-defined function as part of stream output. </para></description><method-group name="public member functions">
<method name="output" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter><purpose>The method invokes the saved function with the output stream. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="func"><paramtype>FunctionT const &amp;</paramtype></parameter><purpose>Initializing constructor. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="func"><paramtype>FunctionT &amp;&amp;</paramtype></parameter><purpose>Initializing constructor. </purpose></constructor>
</class>




































































































<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="FunctionT"/>
        </template><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="manip"><paramtype><classname>invoke_manipulator</classname>&lt; FunctionT &gt; const &amp;</paramtype></parameter><description><para>Stream output operator for <computeroutput><classname alt="boost::log::invoke_manipulator">invoke_manipulator</classname></computeroutput>. Invokes the function saved in the manipulator. </para></description></function>
<function name="invoke_manip"><type><classname>invoke_manipulator</classname>&lt; unspecified &gt;</type><template>
          <template-type-parameter name="FunctionT"/>
          <template-nontype-parameter name="Args"><type>typename...</type></template-nontype-parameter>
        </template><parameter name="func"><paramtype>FunctionT &amp;&amp;</paramtype><description><para>User-defined function to invoke on output. The function must be callable with a reference to the output stream as the first argument, followed by <emphasis>args</emphasis>. </para></description></parameter><parameter name="args"><paramtype>Args &amp;&amp;...</paramtype><description><para>Additional arguments to pass to <emphasis>func</emphasis>. </para></description></parameter><description><para>Invoke manipulator generator function.</para><para>

<note><para><emphasis>args</emphasis> are only supported since C++14. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>












</namespace>
</namespace>
</header>
<header name="boost/log/utility/manipulators/optional.hpp">
<para><para>Andrey Semashev </para>

<para>12.05.2020</para>

The header contains implementation of a stream manipulator for inserting an optional value. </para><namespace name="boost">
<namespace name="log">
<class name="optional_manipulator"><template>
      <template-type-parameter name="OptionalT"/>
      <template-type-parameter name="NoneT"/>
    </template><description><para>Stream manipulator for inserting an optional value. </para></description><method-group name="public member functions">
<method name="output" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter><purpose>The method outputs the value, if it is present, otherwise outputs the "none" marker. </purpose></method>
</method-group>
<constructor cv="noexcept"><parameter name="opt"><paramtype>stored_optional_type</paramtype></parameter><parameter name="none"><paramtype>stored_none_type</paramtype></parameter><purpose>Initializing constructor. </purpose></constructor>
</class><class-specialization name="optional_manipulator"><template>
      <template-type-parameter name="OptionalT"/>
    </template><specialization><template-arg>OptionalT</template-arg><template-arg>void</template-arg></specialization><description><para>Stream manipulator for inserting an optional value. Specialization for no "none" marker. </para></description><method-group name="public member functions">
<method name="output" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter><purpose>The method outputs the value, if it is present. </purpose></method>
</method-group>
<constructor cv="noexcept"><parameter name="opt"><paramtype>stored_optional_type</paramtype></parameter><purpose>Initializing constructor. </purpose></constructor>
</class-specialization>



























































































<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="OptionalT"/>
          <template-type-parameter name="NoneT"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="manip"><paramtype><classname>optional_manipulator</classname>&lt; OptionalT, NoneT &gt; const &amp;</paramtype></parameter><description><para>Stream output operator for <computeroutput><classname alt="boost::log::optional_manipulator">optional_manipulator</classname></computeroutput>. Outputs the optional value or the "none" marker, if one was specified on manipulator construction. </para></description></function>
<function name="optional_manip"><type>boost::enable_if_c&lt; is_scalar&lt; OptionalT &gt;::value &amp;&amp;is_scalar&lt; NoneT &gt;::value, <classname>optional_manipulator</classname>&lt; OptionalT, NoneT &gt;&gt;::type</type><template>
          <template-type-parameter name="OptionalT"/>
          <template-type-parameter name="NoneT"/>
        </template><parameter name="opt"><paramtype>OptionalT</paramtype><description><para>Optional value to output. The optional value must support contextual conversion to <computeroutput>bool</computeroutput> and dereferencing, and its dereferencing result must support stream output. </para></description></parameter><parameter name="none"><paramtype>NoneT</paramtype><description><para>Marker used to indicate when the value is not present. Optional. If not specified, nothing is output if the value is not present. </para></description></parameter><description><para>Optional manipulator generator function.</para><para>

<note><para>Both <emphasis>opt</emphasis> and <emphasis>none</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="optional_manip"><type>boost::enable_if_c&lt; is_scalar&lt; OptionalT &gt;::value &amp;&amp;!is_scalar&lt; NoneT &gt;::value, <classname>optional_manipulator</classname>&lt; OptionalT, NoneT &gt;&gt;::type</type><template>
          <template-type-parameter name="OptionalT"/>
          <template-type-parameter name="NoneT"/>
        </template><parameter name="opt"><paramtype>OptionalT</paramtype><description><para>Optional value to output. The optional value must support contextual conversion to <computeroutput>bool</computeroutput> and dereferencing, and its dereferencing result must support stream output. </para></description></parameter><parameter name="none"><paramtype>NoneT const &amp;</paramtype><description><para>Marker used to indicate when the value is not present. Optional. If not specified, nothing is output if the value is not present. </para></description></parameter><description><para>Optional manipulator generator function.</para><para>

<note><para>Both <emphasis>opt</emphasis> and <emphasis>none</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="optional_manip"><type>boost::enable_if_c&lt; is_scalar&lt; OptionalT &gt;::value, <classname>optional_manipulator</classname>&lt; OptionalT, NoneElementT * &gt;&gt;::type</type><template>
          <template-type-parameter name="OptionalT"/>
          <template-type-parameter name="NoneElementT"/>
          <template-nontype-parameter name="N"><type>std::size_t</type></template-nontype-parameter>
        </template><parameter name="opt"><paramtype>OptionalT</paramtype><description><para>Optional value to output. The optional value must support contextual conversion to <computeroutput>bool</computeroutput> and dereferencing, and its dereferencing result must support stream output. </para></description></parameter><parameter name="none"><paramtype>NoneElementT(&amp;)</paramtype><description><para>Marker used to indicate when the value is not present. Optional. If not specified, nothing is output if the value is not present. </para></description></parameter><description><para>Optional manipulator generator function.</para><para>

<note><para>Both <emphasis>opt</emphasis> and <emphasis>none</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="optional_manip"><type>boost::enable_if_c&lt; !is_scalar&lt; OptionalT &gt;::value &amp;&amp;!is_array&lt; OptionalT &gt;::value &amp;&amp;is_scalar&lt; NoneT &gt;::value, <classname>optional_manipulator</classname>&lt; OptionalT, NoneT &gt;&gt;::type</type><template>
          <template-type-parameter name="OptionalT"/>
          <template-type-parameter name="NoneT"/>
        </template><parameter name="opt"><paramtype>OptionalT const &amp;</paramtype><description><para>Optional value to output. The optional value must support contextual conversion to <computeroutput>bool</computeroutput> and dereferencing, and its dereferencing result must support stream output. </para></description></parameter><parameter name="none"><paramtype>NoneT</paramtype><description><para>Marker used to indicate when the value is not present. Optional. If not specified, nothing is output if the value is not present. </para></description></parameter><description><para>Optional manipulator generator function.</para><para>

<note><para>Both <emphasis>opt</emphasis> and <emphasis>none</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="optional_manip"><type>boost::enable_if_c&lt; !is_scalar&lt; OptionalT &gt;::value &amp;&amp;!is_array&lt; OptionalT &gt;::value &amp;&amp;!is_scalar&lt; NoneT &gt;::value, <classname>optional_manipulator</classname>&lt; OptionalT, NoneT &gt;&gt;::type</type><template>
          <template-type-parameter name="OptionalT"/>
          <template-type-parameter name="NoneT"/>
        </template><parameter name="opt"><paramtype>OptionalT const &amp;</paramtype><description><para>Optional value to output. The optional value must support contextual conversion to <computeroutput>bool</computeroutput> and dereferencing, and its dereferencing result must support stream output. </para></description></parameter><parameter name="none"><paramtype>NoneT const &amp;</paramtype><description><para>Marker used to indicate when the value is not present. Optional. If not specified, nothing is output if the value is not present. </para></description></parameter><description><para>Optional manipulator generator function.</para><para>

<note><para>Both <emphasis>opt</emphasis> and <emphasis>none</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="optional_manip"><type>boost::enable_if_c&lt; !is_scalar&lt; OptionalT &gt;::value &amp;&amp;!is_array&lt; OptionalT &gt;::value, <classname>optional_manipulator</classname>&lt; OptionalT, NoneElementT * &gt;&gt;::type</type><template>
          <template-type-parameter name="OptionalT"/>
          <template-type-parameter name="NoneElementT"/>
          <template-nontype-parameter name="N"><type>std::size_t</type></template-nontype-parameter>
        </template><parameter name="opt"><paramtype>OptionalT const &amp;</paramtype><description><para>Optional value to output. The optional value must support contextual conversion to <computeroutput>bool</computeroutput> and dereferencing, and its dereferencing result must support stream output. </para></description></parameter><parameter name="none"><paramtype>NoneElementT(&amp;)</paramtype><description><para>Marker used to indicate when the value is not present. Optional. If not specified, nothing is output if the value is not present. </para></description></parameter><description><para>Optional manipulator generator function.</para><para>

<note><para>Both <emphasis>opt</emphasis> and <emphasis>none</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="optional_manip"><type>boost::enable_if_c&lt; is_scalar&lt; OptionalT &gt;::value, <classname>optional_manipulator</classname>&lt; OptionalT, void &gt;&gt;::type</type><template>
          <template-type-parameter name="OptionalT"/>
        </template><parameter name="opt"><paramtype>OptionalT</paramtype><description><para>Optional value to output. The optional value must support contextual conversion to <computeroutput>bool</computeroutput> and dereferencing, and its dereferencing result must support stream output. </para></description></parameter><description><para>Optional manipulator generator function.</para><para>

<note><para><emphasis>opt</emphasis> object must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="optional_manip"><type>boost::enable_if_c&lt; !is_scalar&lt; OptionalT &gt;::value &amp;&amp;!is_array&lt; OptionalT &gt;::value, <classname>optional_manipulator</classname>&lt; OptionalT, void &gt;&gt;::type</type><template>
          <template-type-parameter name="OptionalT"/>
        </template><parameter name="opt"><paramtype>OptionalT const &amp;</paramtype><description><para>Optional value to output. The optional value must support contextual conversion to <computeroutput>bool</computeroutput> and dereferencing, and its dereferencing result must support stream output. </para></description></parameter><description><para>Optional manipulator generator function.</para><para>

<note><para><emphasis>opt</emphasis> object must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>














</namespace>
</namespace>
</header>
<header name="boost/log/utility/manipulators/range.hpp">
<para><para>Andrey Semashev </para>

<para>11.05.2020</para>

The header contains implementation of a stream manipulator for inserting a range of elements, optionally separated with a delimiter. </para><namespace name="boost">
<namespace name="log">
<class name="range_manipulator"><template>
      <template-type-parameter name="RangeT"/>
      <template-type-parameter name="DelimiterT"/>
    </template><description><para>Stream manipulator for inserting a range of elements, optionally separated with a delimiter. </para></description><method-group name="public member functions">
<method name="output" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter><purpose>The method outputs elements of the range separated with delimiter. </purpose></method>
</method-group>
<constructor cv="noexcept"><parameter name="range"><paramtype>RangeT const &amp;</paramtype></parameter><parameter name="delimiter"><paramtype>stored_delimiter_type</paramtype></parameter><purpose>Initializing constructor. </purpose></constructor>
</class><class-specialization name="range_manipulator"><template>
      <template-type-parameter name="RangeT"/>
    </template><specialization><template-arg>RangeT</template-arg><template-arg>void</template-arg></specialization><description><para>Stream manipulator for inserting a range of elements. Specialization for when there is no delimiter. </para></description><method-group name="public member functions">
<method name="output" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter><purpose>The method outputs elements of the range. </purpose></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="range"><paramtype>RangeT const &amp;</paramtype></parameter><purpose>Initializing constructor. </purpose></constructor>
</class-specialization>






















































































<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="DelimiterT"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="manip"><paramtype><classname>range_manipulator</classname>&lt; RangeT, DelimiterT &gt; const &amp;</paramtype></parameter><description><para>Stream output operator for <computeroutput><classname alt="boost::log::range_manipulator">range_manipulator</classname></computeroutput>. Outputs every element of the range, separated with a delimiter, if one was specified on manipulator construction. </para></description></function>
<function name="range_manip"><type>boost::enable_if_c&lt; is_scalar&lt; DelimiterT &gt;::value, <classname>range_manipulator</classname>&lt; RangeT, DelimiterT &gt;&gt;::type</type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="DelimiterT"/>
        </template><parameter name="range"><paramtype>RangeT const &amp;</paramtype><description><para>Range of elements to output. The range must support begin and end iterators, and its elements must support stream output. </para></description></parameter><parameter name="delimiter"><paramtype>DelimiterT</paramtype><description><para>Delimiter to separate elements in the output. Optional. If not specified, elements are output without separation. </para></description></parameter><description><para>Range manipulator generator function.</para><para>

<note><para>Both <emphasis>range</emphasis> and <emphasis>delimiter</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="range_manip"><type>boost::disable_if_c&lt; is_scalar&lt; DelimiterT &gt;::value, <classname>range_manipulator</classname>&lt; RangeT, DelimiterT &gt;&gt;::type</type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="DelimiterT"/>
        </template><parameter name="range"><paramtype>RangeT const &amp;</paramtype><description><para>Range of elements to output. The range must support begin and end iterators, and its elements must support stream output. </para></description></parameter><parameter name="delimiter"><paramtype>DelimiterT const &amp;</paramtype><description><para>Delimiter to separate elements in the output. Optional. If not specified, elements are output without separation. </para></description></parameter><description><para>Range manipulator generator function.</para><para>

<note><para>Both <emphasis>range</emphasis> and <emphasis>delimiter</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="range_manip"><type><classname>range_manipulator</classname>&lt; RangeT, DelimiterElementT * &gt;</type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="DelimiterElementT"/>
          <template-nontype-parameter name="N"><type>std::size_t</type></template-nontype-parameter>
        </template><parameter name="range"><paramtype>RangeT const &amp;</paramtype><description><para>Range of elements to output. The range must support begin and end iterators, and its elements must support stream output. </para></description></parameter><parameter name="delimiter"><paramtype>DelimiterElementT(&amp;)</paramtype><description><para>Delimiter to separate elements in the output. Optional. If not specified, elements are output without separation. </para></description></parameter><description><para>Range manipulator generator function.</para><para>

<note><para>Both <emphasis>range</emphasis> and <emphasis>delimiter</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="range_manip"><type><classname>range_manipulator</classname>&lt; RangeT, void &gt;</type><template>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="range"><paramtype>RangeT const &amp;</paramtype><description><para>Range of elements to output. The range must support begin and end iterators, and its elements must support stream output. </para></description></parameter><description><para>Range manipulator generator function.</para><para>

<note><para><emphasis>delimiter</emphasis> object must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>























</namespace>
</namespace>
</header>
<header name="boost/log/utility/manipulators/to_log.hpp">
<para><para>Andrey Semashev </para>

<para>06.11.2012</para>

This header contains the <computeroutput>to_log</computeroutput> output manipulator. </para><namespace name="boost">
<namespace name="log">
<class name="to_log_manip"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="TagT"><default>void</default></template-type-parameter>
    </template><purpose>Generic manipulator for customizing output to log. </purpose><typedef name="value_type"><purpose>Output value type. </purpose><type>T</type></typedef>
<typedef name="tag_type"><purpose>Value tag type. </purpose><type>TagT</type></typedef>
<method-group name="public member functions">
<method name="get" cv="const noexcept"><type>value_type const  &amp;</type></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="value"><paramtype>value_type const &amp;</paramtype></parameter></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>to_log_manip</classname> const &amp;</paramtype></parameter></constructor>
</class>



















































































<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="manip"><paramtype><classname>to_log_manip</classname>&lt; T, TagT &gt;</paramtype></parameter></function>
<function name="to_log"><type><classname>to_log_manip</classname>&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="value"><paramtype>T const &amp;</paramtype></parameter></function>
<function name="to_log"><type><classname>to_log_manip</classname>&lt; T, TagT &gt;</type><template>
          <template-type-parameter name="TagT"/>
          <template-type-parameter name="T"/>
        </template><parameter name="value"><paramtype>T const &amp;</paramtype></parameter></function>




























</namespace>
</namespace>
</header>
<header name="boost/log/utility/manipulators/tuple.hpp">
<para><para>Andrey Semashev </para>

<para>11.05.2020</para>

The header contains implementation of a stream manipulator for inserting a tuple or any heterogeneous sequence of elements, optionally separated with a delimiter. </para><namespace name="boost">
<namespace name="log">
<class name="tuple_manipulator"><template>
      <template-type-parameter name="TupleT"/>
      <template-type-parameter name="DelimiterT"/>
    </template><description><para>Stream manipulator for inserting a heterogeneous sequence of elements, optionally separated with a delimiter. </para></description><struct name="output_visitor"><template>
      <template-type-parameter name="StreamT"/>
    </template><typedef name="result_type"><type>boost::true_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name=""><paramtype>boost::true_type</paramtype></parameter><parameter name="elem"><paramtype>T const &amp;</paramtype></parameter></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name=""><paramtype>boost::false_type</paramtype></parameter><parameter name="elem"><paramtype>T const &amp;</paramtype></parameter></method>
</method-group>
<constructor cv="noexcept"><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="delimiter"><paramtype>stored_delimiter_type</paramtype></parameter></constructor>
</struct><method-group name="public member functions">
<method name="output" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter><purpose>The method outputs elements of the sequence separated with delimiter. </purpose></method>
</method-group>
<constructor cv="noexcept"><parameter name="tuple"><paramtype>TupleT const &amp;</paramtype></parameter><parameter name="delimiter"><paramtype>stored_delimiter_type</paramtype></parameter><purpose>Initializing constructor. </purpose></constructor>
</class><class-specialization name="tuple_manipulator"><template>
      <template-type-parameter name="TupleT"/>
    </template><specialization><template-arg>TupleT</template-arg><template-arg>void</template-arg></specialization><description><para>Stream manipulator for inserting a heterogeneous sequence of elements. Specialization for when there is no delimiter. </para></description><struct name="output_visitor"><template>
      <template-type-parameter name="StreamT"/>
    </template><typedef name="result_type"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="elem"><paramtype>T const &amp;</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter></constructor>
</struct><method-group name="public member functions">
<method name="output" cv="const"><type>void</type><template>
          <template-type-parameter name="StreamT"/>
        </template><parameter name="stream"><paramtype>StreamT &amp;</paramtype></parameter><purpose>The method outputs elements of the sequence. </purpose></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="tuple"><paramtype>TupleT const &amp;</paramtype></parameter><purpose>Initializing constructor. </purpose></constructor>
</class-specialization>














































































<function name="operator&lt;&lt;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="StreamT"/>
          <template-type-parameter name="TupleT"/>
          <template-type-parameter name="DelimiterT"/>
        </template><parameter name="strm"><paramtype>StreamT &amp;</paramtype></parameter><parameter name="manip"><paramtype><classname>tuple_manipulator</classname>&lt; TupleT, DelimiterT &gt; const &amp;</paramtype></parameter><description><para>Stream output operator for <computeroutput><classname alt="boost::log::tuple_manipulator">tuple_manipulator</classname></computeroutput>. Outputs every element of the sequence, separated with a delimiter, if one was specified on manipulator construction. </para></description></function>
<function name="tuple_manip"><type>boost::enable_if_c&lt; is_scalar&lt; DelimiterT &gt;::value, <classname>tuple_manipulator</classname>&lt; TupleT, DelimiterT &gt;&gt;::type</type><template>
          <template-type-parameter name="TupleT"/>
          <template-type-parameter name="DelimiterT"/>
        </template><parameter name="tuple"><paramtype>TupleT const &amp;</paramtype><description><para>Heterogeneous sequence of elements to output. The sequence must be supported by Boost.Fusion, and its elements must support stream output. </para></description></parameter><parameter name="delimiter"><paramtype>DelimiterT</paramtype><description><para>Delimiter to separate elements in the output. Optional. If not specified, elements are output without separation. </para></description></parameter><description><para>Tuple manipulator generator function.</para><para>

<note><para>Both <emphasis>tuple</emphasis> and <emphasis>delimiter</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="tuple_manip"><type>boost::disable_if_c&lt; is_scalar&lt; DelimiterT &gt;::value, <classname>tuple_manipulator</classname>&lt; TupleT, DelimiterT &gt;&gt;::type</type><template>
          <template-type-parameter name="TupleT"/>
          <template-type-parameter name="DelimiterT"/>
        </template><parameter name="tuple"><paramtype>TupleT const &amp;</paramtype><description><para>Heterogeneous sequence of elements to output. The sequence must be supported by Boost.Fusion, and its elements must support stream output. </para></description></parameter><parameter name="delimiter"><paramtype>DelimiterT const &amp;</paramtype><description><para>Delimiter to separate elements in the output. Optional. If not specified, elements are output without separation. </para></description></parameter><description><para>Tuple manipulator generator function.</para><para>

<note><para>Both <emphasis>tuple</emphasis> and <emphasis>delimiter</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="tuple_manip"><type><classname>tuple_manipulator</classname>&lt; TupleT, DelimiterElementT * &gt;</type><template>
          <template-type-parameter name="TupleT"/>
          <template-type-parameter name="DelimiterElementT"/>
          <template-nontype-parameter name="N"><type>std::size_t</type></template-nontype-parameter>
        </template><parameter name="tuple"><paramtype>TupleT const &amp;</paramtype><description><para>Heterogeneous sequence of elements to output. The sequence must be supported by Boost.Fusion, and its elements must support stream output. </para></description></parameter><parameter name="delimiter"><paramtype>DelimiterElementT(&amp;)</paramtype><description><para>Delimiter to separate elements in the output. Optional. If not specified, elements are output without separation. </para></description></parameter><description><para>Tuple manipulator generator function.</para><para>

<note><para>Both <emphasis>tuple</emphasis> and <emphasis>delimiter</emphasis> objects must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>
<function name="tuple_manip"><type><classname>tuple_manipulator</classname>&lt; TupleT, void &gt;</type><template>
          <template-type-parameter name="TupleT"/>
        </template><parameter name="tuple"><paramtype>TupleT const &amp;</paramtype><description><para>Heterogeneous sequence of elements to output. The sequence must be supported by Boost.Fusion, and its elements must support stream output. </para></description></parameter><description><para>Tuple manipulator generator function.</para><para>

<note><para><emphasis>tuple</emphasis> object must outlive the created manipulator object. </para>
</note>
</para></description><returns><para>Manipulator to be inserted into the stream.</para>
</returns></function>































</namespace>
</namespace>
</header>
<header name="boost/log/utility/once_block.hpp">
<para>The header defines classes and macros for once-blocks. </para><para><para>Andrey Semashev </para>

<para>23.06.2010 </para>

</para><namespace name="boost">
<namespace name="log">
<struct name="once_block_flag"><purpose>A flag to detect if a code block has already been executed. </purpose><description><para>This structure should be used in conjunction with the <computeroutput>BOOST_LOG_ONCE_BLOCK_FLAG</computeroutput> macro. Usage example:</para><para><computeroutput> <classname alt="boost::log::once_block_flag">once_block_flag</classname> flag = BOOST_LOG_ONCE_BLOCK_INIT;</computeroutput></para><para><computeroutput>void foo() { BOOST_LOG_ONCE_BLOCK_FLAG(flag) { puts("Hello, world once!"); } } </computeroutput> </para></description></struct>


















































































































</namespace>
</namespace>
<macro name="BOOST_LOG_ONCE_BLOCK_INIT"><description><para>The static initializer for <computeroutput>once_block_flag</computeroutput>. </para></description></macro>
<macro name="BOOST_LOG_ONCE_BLOCK_FLAG" kind="functionlike"><macro-parameter name="flag_var"/><description><para>Begins a code block to be executed only once, with protection against thread concurrency. User has to provide the flag variable that controls whether the block has already been executed. </para></description></macro>
<macro name="BOOST_LOG_ONCE_BLOCK" kind="functionlike"><macro-parameter name=""/><description><para>Begins a code block to be executed only once, with protection against thread concurrency. </para></description></macro>
</header>
<header name="boost/log/utility/open_mode.hpp">
<para><para>Andrey Semashev </para>

<para>01.01.2016</para>

The header defines resource opening modes. </para><namespace name="boost">
<namespace name="log">
<namespace name="open_mode">
<struct name="create_only_tag"><purpose>Create a new resource; fail if exists already. </purpose></struct><struct name="open_only_tag"><purpose>Opens an existing resource; fail if not exist. </purpose></struct><struct name="open_or_create_tag"><purpose>Creates a new resource or opens an existing one. </purpose></struct><data-member name="create_only"><type>struct <classname>boost::log::open_mode::create_only_tag</classname></type></data-member>
<data-member name="open_only"><type>struct <classname>boost::log::open_mode::open_only_tag</classname></type></data-member>
<data-member name="open_or_create"><type>struct <classname>boost::log::open_mode::open_or_create_tag</classname></type></data-member>
</namespace>



















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/permissions.hpp">
<para><para>Lingxi Li </para>

<para>Andrey Semashev </para>

<para>14.10.2015</para>

The header contains an abstraction wrapper for security permissions. </para><namespace name="boost">
<namespace name="interprocess">
</namespace>
<namespace name="log">
<class name="permissions"><purpose>Access permissions wrapper. </purpose><description><para>On Windows platforms, it represents a pointer to <computeroutput>SECURITY_ATTRIBUTES</computeroutput>. The user is responsible for allocating and reclaiming resources associated with the pointer, <computeroutput>permissions</computeroutput> instance does not own them.</para><para>On POSIX platforms, it represents a <computeroutput>mode_t</computeroutput> value. </para></description><typedef name="native_type"><purpose>The type of security permissions, specific to the operating system. </purpose><type>implementation_defined</type></typedef>
<method-group name="public member functions">
<method name="set_native" cv="noexcept"><type>void</type><parameter name="perms"><paramtype>native_type</paramtype></parameter><description><para>Sets permissions from the OS-specific permissions. </para></description></method>
<method name="get_native" cv="const noexcept"><type>native_type</type><description><para>Returns the underlying OS-specific permissions. </para></description></method>
<method name="set_default" cv="noexcept"><type>void</type><description><para>Sets the default permissions, which are equivalent to <computeroutput>NULL</computeroutput> <computeroutput>SECURITY_ATTRIBUTES</computeroutput> on Windows and <computeroutput>0644</computeroutput> on POSIX platforms. </para></description></method>
<method name="set_unrestricted"><type>void</type><description><para>Sets unrestricted permissions, which are equivalent to <computeroutput>SECURITY_ATTRIBUTES</computeroutput> with <computeroutput>NULL</computeroutput> DACL on Windows and <computeroutput>0666</computeroutput> on POSIX platforms. </para></description></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="that"><paramtype><classname>permissions</classname> &amp;</paramtype><description><para>The other object to swap with. </para></description></parameter><description><para>The method swaps the object with <emphasis>that</emphasis>.</para><para>
</para></description></method>
</method-group>
<constructor cv="noexcept"><description><para>Default constructor. The method constructs an object that represents a null <computeroutput>SECURITY_ATTRIBUTES</computeroutput> pointer on Windows platforms, and a <computeroutput>mode_t</computeroutput> value <computeroutput>0644</computeroutput> on POSIX platforms. </para></description></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>permissions</classname> const &amp;</paramtype></parameter><description><para>Copy constructor. </para></description></constructor>
<copy-assignment cv="noexcept"><type><classname>permissions</classname> &amp;</type><parameter name="that"><paramtype><classname>permissions</classname> const &amp;</paramtype></parameter><description><para>Copy assignment. </para></description></copy-assignment>
<constructor cv="noexcept"><parameter name="perms"><paramtype>native_type</paramtype></parameter><description><para>Initializing constructor. </para></description></constructor>
<constructor cv="noexcept"><parameter name="perms"><paramtype>boost::interprocess::permissions const &amp;</paramtype></parameter><description><para>Initializing constructor. </para></description></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>permissions</classname> &amp;&amp;</paramtype></parameter><description><para>Move constructor. </para></description></constructor>
<copy-assignment cv="noexcept"><type><classname>permissions</classname> &amp;</type><parameter name="that"><paramtype><classname>permissions</classname> &amp;&amp;</paramtype></parameter><description><para>Move assignment. </para></description></copy-assignment>
<method-group name="friend functions">
<method name="swap" cv="noexcept"><type>friend void</type><parameter name="a"><paramtype><classname>permissions</classname> &amp;</paramtype></parameter><parameter name="b"><paramtype><classname>permissions</classname> &amp;</paramtype></parameter><purpose>Swaps the two <computeroutput>permissions</computeroutput> objects. </purpose></method>
</method-group>
</class>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/record_ordering.hpp">
<para><para>Andrey Semashev </para>

<para>23.08.2009</para>

This header contains ordering predicates for logging records. </para><namespace name="boost">
<namespace name="log">
<class name="abstract_ordering"><template>
      <template-type-parameter name="FunT"><default><classname alt="boost::log::less">less</classname></default></template-type-parameter>
    </template><inherit access="private">FunT</inherit><purpose>Ordering predicate, based on opaque pointers to the record view implementation data. </purpose><description><para>Since record views only refer to a shared implementation data, this predicate is able to order the views by comparing the pointers to the data. Therefore two views are considered to be equivalent if they refer to the same implementation data. Otherwise it is not specified whether one record is ordered before the other until the predicate is applied. Note that the ordering may change every time the application runs.</para><para>This kind of ordering may be useful if log records are to be stored in an associative container with as least performance overhead as possible, when the particular order is not important.</para><para>The <computeroutput>FunT</computeroutput> template argument is the predicate that is used to actually compare pointers. It should be able to compare <computeroutput>const void*</computeroutput> pointers. The compared pointers may refer to distinct memory regions, the pointers must not be interpreted in any way. </para></description><typedef name="result_type"><purpose>Result type. </purpose><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><parameter name="left"><paramtype>record_view const &amp;</paramtype></parameter><parameter name="right"><paramtype>record_view const &amp;</paramtype></parameter><description><para>Ordering operator </para></description></method>
</method-group>
<constructor><description><para>Default constructor. Requires <computeroutput>FunT</computeroutput> to be default constructible. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter><description><para>Initializing constructor. Constructs <computeroutput>FunT</computeroutput> instance as a copy of the <emphasis>fun</emphasis> argument. </para></description></constructor>
</class><class name="attribute_value_ordering"><template>
      <template-type-parameter name="ValueT"/>
      <template-type-parameter name="FunT"><default><classname alt="boost::log::less">less</classname></default></template-type-parameter>
    </template><inherit access="private">FunT</inherit><purpose>Ordering predicate, based on attribute values associated with records. </purpose><description><para>This predicate allows to order log records based on values of a specifically named attribute associated with them. Two given log records being compared should both have the specified attribute value of the specified type to be able to be ordered properly. As a special case, if neither of the records have the value, these records are considered equivalent. Otherwise, the ordering results are unspecified. </para></description><struct name="l1_visitor"><typedef name="result_type"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="LeftT"/>
        </template><parameter name="left"><paramtype>LeftT const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="owner"><paramtype><classname>attribute_value_ordering</classname> const &amp;</paramtype></parameter><parameter name="right"><paramtype>record_view const &amp;</paramtype></parameter><parameter name="result"><paramtype>bool &amp;</paramtype></parameter></constructor>
</struct><struct name="l2_visitor"><template>
      <template-type-parameter name="LeftT"/>
    </template><typedef name="result_type"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="RightT"/>
        </template><parameter name="right"><paramtype>RightT const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter><parameter name="left"><paramtype>LeftT const &amp;</paramtype></parameter><parameter name="result"><paramtype>bool &amp;</paramtype></parameter></constructor>
</struct><typedef name="result_type"><purpose>Result type. </purpose><type>bool</type></typedef>
<typedef name="value_type"><purpose>Compared attribute value type. </purpose><type>ValueT</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>result_type</type><parameter name="left"><paramtype>record_view const &amp;</paramtype></parameter><parameter name="right"><paramtype>record_view const &amp;</paramtype></parameter><description><para>Ordering operator </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="name"><paramtype>attribute_name const &amp;</paramtype><description><para>The attribute value name to be compared </para></description></parameter><parameter name="fun"><paramtype>FunT const &amp;</paramtype><default>FunT()</default><description><para>The ordering functor </para></description></parameter><description><para>Initializing constructor.</para><para>
</para></description></constructor>
</class>























<function name="make_attr_ordering"><type><classname>attribute_value_ordering</classname>&lt; ValueT, FunT &gt;</type><template>
          <template-type-parameter name="ValueT"/>
          <template-type-parameter name="FunT"/>
        </template><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter><description><para>The function constructs a log record ordering predicate </para></description></function>
<function name="make_attr_ordering"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="FunT"/>
        </template><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="fun"><paramtype>FunT const &amp;</paramtype></parameter><description><para>The function constructs a log record ordering predicate </para></description></function>

























































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/setup.hpp">
<para><para>Andrey Semashev </para>

<para>16.02.2013</para>

This header includes all library setup helpers. </para></header>
<header name="boost/log/utility/setup/common_attributes.hpp">
<para><para>Andrey Semashev </para>

<para>16.05.2008</para>

The header contains implementation of convenience functions for registering commonly used attributes. </para><namespace name="boost">
<namespace name="log">
































































<function name="add_common_attributes"><type>void</type><purpose>Simple attribute initialization routine. </purpose><description><para>The function adds commonly used attributes to the logging system. Specifically, the following attributes are registered globally:</para><para><itemizedlist>
<listitem><para>LineID - logging records counter with value type <computeroutput>unsigned int</computeroutput> </para>
</listitem>
<listitem><para>TimeStamp - local time generator with value type <computeroutput>boost::posix_time::ptime</computeroutput> </para>
</listitem>
<listitem><para>ProcessID - current process identifier with value type <computeroutput>attributes::current_process_id::value_type</computeroutput> </para>
</listitem>
<listitem><para>ThreadID - in multithreaded builds, current thread identifier with value type <computeroutput>attributes::current_thread_id::value_type</computeroutput> </para>
</listitem>
</itemizedlist>
</para></description></function>


















































</namespace>
</namespace>
</header>
<header name="boost/log/utility/setup/console.hpp">
<para><para>Andrey Semashev </para>

<para>16.05.2008</para>

The header contains implementation of convenience functions for enabling logging to console. </para><namespace name="boost">
<namespace name="log">




























































<function name="add_console_log"><type>shared_ptr&lt; sinks::synchronous_sink&lt; sinks::basic_text_ostream_backend&lt; CharT &gt; &gt;&gt;</type><template>
          <template-type-parameter name="CharT"/>
          <template-nontype-parameter name="ArgsT"><type>typename...</type></template-nontype-parameter>
        </template><parameter name="strm"><paramtype>std::basic_ostream&lt; CharT &gt; &amp;</paramtype><description><para>One of the standard console streams: <computeroutput>std::cout</computeroutput>, <computeroutput>std::cerr</computeroutput> or <computeroutput>std::clog</computeroutput> (or the corresponding wide-character analogues). </para></description></parameter><parameter name="args"><paramtype>ArgsT... const &amp;</paramtype><description><para>Optional additional named arguments for the sink initialization. The following arguments are supported: <itemizedlist>
<listitem><para><computeroutput>filter</computeroutput> Specifies a filter to install into the sink. May be a string that represents a filter, or a filter lambda expression. </para>
</listitem>
<listitem><para><computeroutput>format</computeroutput> Specifies a formatter to install into the sink. May be a string that represents a formatter, or a formatter lambda expression (either streaming or Boost.Format-like notation). </para>
</listitem>
<listitem><para><computeroutput>auto_flush</computeroutput> A boolean flag that shows whether the sink should automatically flush the stream after each written record. </para>
</listitem>
<listitem><para><computeroutput>auto_newline_mode</computeroutput> - Specifies automatic trailing newline insertion mode. Must be a value of the <computeroutput>auto_newline_mode</computeroutput> enum. By default, is <computeroutput>auto_newline_mode::insert_if_missing</computeroutput>. </para>
</listitem>
</itemizedlist>
</para></description></parameter><description><para>The function constructs sink for the specified console stream and adds it to the core</para><para>

</para></description><returns><para>Pointer to the constructed sink. </para>
</returns></function>
<function name="add_console_log"><type>shared_ptr&lt; sinks::synchronous_sink&lt; sinks::basic_text_ostream_backend&lt; CharT &gt; &gt;&gt;</type><template>
          <template-type-parameter name="CharT"/>
          <template-nontype-parameter name="ArgsT"><type>typename...</type></template-nontype-parameter>
        </template><parameter name="args"><paramtype>ArgsT... const &amp;</paramtype></parameter><description><para>Equivalent to: <computeroutput>add_console_log(std::clog);</computeroutput> or <computeroutput>add_console_log(std::wclog);</computeroutput>, depending on the <computeroutput>CharT</computeroutput> type.</para><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></function>
<function name="add_console_log"><type>shared_ptr&lt; sinks::synchronous_sink&lt; sinks::text_ostream_backend &gt;&gt;</type><description><para>The function constructs sink for the <computeroutput>std::clog</computeroutput> stream and adds it to the core</para><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts.</para><para>
</para></description><returns><para>Pointer to the constructed sink. </para>
</returns></function>
<function name="wadd_console_log"><type>shared_ptr&lt; sinks::synchronous_sink&lt; sinks::wtext_ostream_backend &gt;&gt;</type><description><para>The function constructs sink for the <computeroutput>std::wclog</computeroutput> stream and adds it to the core</para><para>
</para></description><returns><para>Pointer to the constructed sink. </para>
</returns></function>



















































</namespace>
</namespace>
</header>
<header name="boost/log/utility/setup/file.hpp">
<para><para>Andrey Semashev </para>

<para>16.05.2008</para>

The header contains implementation of convenience functions for enabling logging to a file. </para><namespace name="boost">
<namespace name="log">



























































<function name="add_file_log"><type>shared_ptr&lt; sinks::synchronous_sink&lt; sinks::text_file_backend &gt; &gt;</type><template>
          <template-nontype-parameter name="ArgsT"><type>typename...</type></template-nontype-parameter>
        </template><parameter name="args"><paramtype>ArgsT... const &amp;</paramtype><description><para>A number of named arguments. The following parameters are supported: <itemizedlist>
<listitem><para><computeroutput>file_name</computeroutput> The active file name or its pattern. This parameter is mandatory. </para>
</listitem>
<listitem><para><computeroutput>target_file_name</computeroutput> - Specifies the target file name pattern to use to rename the log file on rotation, before passing it to the file collector. The pattern may contain the same placeholders as the <computeroutput>file_name</computeroutput> parameter. By default, no renaming is done, i.e. the written log file keeps its name according to <computeroutput>file_name</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>open_mode</computeroutput> The mask that describes the open mode for the file. See <computeroutput>std::ios_base::openmode</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>rotation_size</computeroutput> The size of the file at which rotation should occur. See <computeroutput>basic_text_file_backend</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>time_based_rotation</computeroutput> The predicate for time-based file rotations. See <computeroutput>basic_text_file_backend</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>auto_flush</computeroutput> A boolean flag that shows whether the sink should automatically flush the file after each written record. </para>
</listitem>
<listitem><para><computeroutput>auto_newline_mode</computeroutput> - Specifies automatic trailing newline insertion mode. Must be a value of the <computeroutput>auto_newline_mode</computeroutput> enum. By default, is <computeroutput>auto_newline_mode::insert_if_missing</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>target</computeroutput> The target directory to store rotated files in. Enables file collector and, if specified, limits associated with the target directory. See <computeroutput>sinks::file::make_collector</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>max_size</computeroutput> The maximum total size of rotated files in the target directory. See <computeroutput>sinks::file::make_collector</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>min_free_space</computeroutput> Minimum free space in the target directory. See <computeroutput>sinks::file::make_collector</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>max_files</computeroutput> The maximum total number of rotated files in the target directory. See <computeroutput>sinks::file::make_collector</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>scan_method</computeroutput> The method of scanning the target directory for log files. See <computeroutput>sinks::file::scan_method</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput>filter</computeroutput> Specifies a filter to install into the sink. May be a string that represents a filter, or a filter lambda expression. </para>
</listitem>
<listitem><para><computeroutput>format</computeroutput> Specifies a formatter to install into the sink. May be a string that represents a formatter, or a formatter lambda expression (either streaming or Boost.Format-like notation).</para>
</listitem>
</itemizedlist>
</para></description></parameter><description><para>The function initializes the logging library to write logs to a file stream.</para><para>

<note><para>The <computeroutput>target</computeroutput> named argument is required to enable the file collector and the limits associated with the target directory. If the parameter is not specified, the file collector will not be created and the limits will not be maintained. </para>
</note>
</para></description><returns><para>Pointer to the constructed sink.</para>
</returns></function>























































</namespace>
</namespace>
</header>
<header name="boost/log/utility/setup/filter_parser.hpp">
<para><para>Andrey Semashev </para>

<para>31.03.2008</para>

The header contains definition of a filter parser function. </para><namespace name="boost">
<namespace name="log">
<class name="basic_filter_factory"><template>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="AttributeValueT"/>
    </template><inherit access="public">boost::log::filter_factory&lt; CharT &gt;</inherit><description><para>The base class for filter factories. The class defines default implementations for most filter expressions. In order to be able to construct filters, the attribute value type must support reading from a stream. Also, the default filters will rely on relational operators for the type, so these operators must also be defined. </para></description><typedef name="value_type"><purpose>The type(s) of the attribute value expected. </purpose><type>AttributeValueT</type></typedef>
<typedef name="string_type"><type>base_type::string_type</type></typedef>
<method-group name="public member functions">
<method name="on_exists_test" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><description><para>The callback for filter for the attribute existence test </para></description></method>
<method name="on_equality_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for equality relation filter </para></description></method>
<method name="on_inequality_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for inequality relation filter </para></description></method>
<method name="on_less_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for less relation filter </para></description></method>
<method name="on_greater_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for greater relation filter </para></description></method>
<method name="on_less_or_equal_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for less or equal relation filter </para></description></method>
<method name="on_greater_or_equal_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for greater or equal relation filter </para></description></method>
<method name="on_custom_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="rel"><paramtype>string_type const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for custom relation filter </para></description></method>
<method name="parse_argument" specifiers="virtual"><type>value_type</type><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The function parses the argument value for a binary relation </para></description></method>
</method-group>
</class><struct name="filter_factory"><template>
      <template-type-parameter name="CharT"/>
    </template><description><para>The interface class for all filter factories. </para></description><typedef name="char_type"><purpose>Character type. </purpose><type>CharT</type></typedef>
<typedef name="string_type"><purpose>String type. </purpose><type>std::basic_string&lt; char_type &gt;</type></typedef>
<method-group name="public member functions">
<method name="on_exists_test" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><description><para>The callback for filter for the attribute existence test </para></description></method>
<method name="on_equality_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for equality relation filter </para></description></method>
<method name="on_inequality_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for inequality relation filter </para></description></method>
<method name="on_less_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for less relation filter </para></description></method>
<method name="on_greater_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for greater relation filter </para></description></method>
<method name="on_less_or_equal_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for less or equal relation filter </para></description></method>
<method name="on_greater_or_equal_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for greater or equal relation filter </para></description></method>
<method name="on_custom_relation" specifiers="virtual"><type>filter</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype></parameter><parameter name="rel"><paramtype>string_type const &amp;</paramtype></parameter><parameter name="arg"><paramtype>string_type const &amp;</paramtype></parameter><description><para>The callback for custom relation filter </para></description></method>
</method-group>
<destructor><description><para>Default constructor</para><para>Virtual destructor </para></description></destructor>
<constructor cv="= delete"><parameter name=""><paramtype><classname>filter_factory</classname> const &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= delete"><type><classname>filter_factory</classname> &amp;</type><parameter name=""><paramtype><classname>filter_factory</classname> const &amp;</paramtype></parameter></copy-assignment>
</struct>


















































<function name="register_filter_factory"><type>void</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="name"><paramtype>attribute_name const &amp;</paramtype><description><para>Attribute name to associate the factory with </para></description></parameter><parameter name="factory"><paramtype>shared_ptr&lt; <classname>filter_factory</classname>&lt; CharT &gt; &gt; const &amp;</paramtype><description><para>The filter factory </para></description></parameter><description><para>The function registers a filter factory object for the specified attribute name. The factory will be used to construct a filter during parsing the filter string.</para><para>

</para></description><requires><para><computeroutput>name != NULL &amp;&amp; factory != NULL</computeroutput>, <computeroutput>name</computeroutput> points to a zero-terminated string </para>
</requires></function>
<function name="register_filter_factory"><type>boost::enable_if_c&lt; is_base_and_derived&lt; <classname>filter_factory</classname>&lt; typename FactoryT::char_type &gt;, FactoryT &gt;::value &gt;::type</type><template>
          <template-type-parameter name="FactoryT"/>
        </template><parameter name="name"><paramtype>attribute_name const &amp;</paramtype><description><para>Attribute name to associate the factory with </para></description></parameter><parameter name="factory"><paramtype>shared_ptr&lt; FactoryT &gt; const &amp;</paramtype><description><para>The filter factory </para></description></parameter><description><para>The function registers a filter factory object for the specified attribute name. The factory will be used to construct a filter during parsing the filter string.</para><para>

</para></description><requires><para><computeroutput>name != NULL &amp;&amp; factory != NULL</computeroutput>, <computeroutput>name</computeroutput> points to a zero-terminated string </para>
</requires></function>
<function name="register_simple_filter_factory"><type>void</type><template>
          <template-type-parameter name="AttributeValueT"/>
          <template-type-parameter name="CharT"/>
        </template><parameter name="name"><paramtype>attribute_name const &amp;</paramtype><description><para>Attribute name to associate the factory with </para></description></parameter><description><para>The function registers a simple filter factory object for the specified attribute name. The factory will support attribute values of type <computeroutput>AttributeValueT</computeroutput>, which must support all relation operations, such as equality comparison and less/greater ordering, and also extraction from stream.</para><para>

</para></description><requires><para><computeroutput>name != NULL</computeroutput>, <computeroutput>name</computeroutput> points to a zero-terminated string </para>
</requires></function>
<function name="register_simple_filter_factory"><type>void</type><template>
          <template-type-parameter name="AttributeValueT"/>
        </template><parameter name="name"><paramtype>attribute_name const &amp;</paramtype><description><para>Attribute name to associate the factory with </para></description></parameter><description><para>The function registers a simple filter factory object for the specified attribute name. The factory will support attribute values of type <computeroutput>AttributeValueT</computeroutput>, which must support all relation operations, such as equality comparison and less/greater ordering, and also extraction from stream.</para><para>

</para></description><requires><para><computeroutput>name != NULL</computeroutput>, <computeroutput>name</computeroutput> points to a zero-terminated string </para>
</requires></function>
<function name="register_simple_filter_factory"><type>void</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="DescriptorT"/>
          <template-nontype-parameter name="ActorT"><type>template&lt; typename &gt; class</type></template-nontype-parameter>
        </template><parameter name="keyword"><paramtype>expressions::attribute_keyword&lt; DescriptorT, ActorT &gt; const &amp;</paramtype><description><para>Attribute keyword to associate the factory with </para></description></parameter><description><para>The function registers a simple filter factory object for the specified attribute keyword. The factory will support attribute values described by the keyword. The values must support all relation operations, such as equality comparison and less/greater ordering, and also extraction from stream.</para><para>

</para></description><requires><para><computeroutput>name != NULL</computeroutput>, <computeroutput>name</computeroutput> points to a zero-terminated string </para>
</requires></function>
<function name="parse_filter"><type>filter</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="begin"><paramtype>const CharT *</paramtype><description><para>Pointer to the first character of the sequence </para></description></parameter><parameter name="end"><paramtype>const CharT *</paramtype><description><para>Pointer to the after-the-last character of the sequence </para></description></parameter><description><para>The function parses a filter from the sequence of characters</para><para>


<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception, if a filter cannot be recognized in the character sequence. </para></description><requires><para><computeroutput>begin &lt;= end</computeroutput>, both pointers must not be <computeroutput>NULL</computeroutput> </para>
</requires><returns><para>A function object that can be used as a filter.</para>
</returns></function>
<function name="parse_filter"><type>filter</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
          <template-type-parameter name="AllocatorT"/>
        </template><parameter name="str"><paramtype>std::basic_string&lt; CharT, TraitsT, AllocatorT &gt; const &amp;</paramtype><description><para>A string that contains filter description </para></description></parameter><description><para>The function parses a filter from the string</para><para>

<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception, if a filter cannot be recognized in the character sequence. </para></description><returns><para>A function object that can be used as a filter.</para>
</returns></function>
<function name="parse_filter"><type>filter</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="str"><paramtype>const CharT *</paramtype><description><para>A string that contains filter description. </para></description></parameter><description><para>The function parses a filter from the string</para><para>


<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception, if a filter cannot be recognized in the character sequence. </para></description><requires><para><computeroutput>str != NULL</computeroutput>, <computeroutput>str</computeroutput> points to a zero-terminated string. </para>
</requires><returns><para>A function object that can be used as a filter.</para>
</returns></function>
























































</namespace>
</namespace>
</header>
<header name="boost/log/utility/setup/formatter_parser.hpp">
<para><para>Andrey Semashev </para>

<para>07.04.2008</para>

The header contains definition of a formatter parser function, along with facilities to add support for custom formatters. </para><namespace name="boost">
<namespace name="log">
<class name="basic_formatter_factory"><template>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="AttributeValueT"/>
    </template><inherit access="public">boost::log::formatter_factory&lt; CharT &gt;</inherit><description><para>Base class for formatter factories. This class provides default implementation of formatter expressions for types supporting stream output. The factory does not take into account any additional parameters that may be specified. </para></description><typedef name="value_type"><purpose>Attribute value type. </purpose><type>AttributeValueT</type></typedef>
<typedef name="formatter_type"><type>base_type::formatter_type</type></typedef>
<typedef name="args_map"><type>base_type::args_map</type></typedef>
<method-group name="public member functions">
<method name="create_formatter" specifiers="virtual"><type>formatter_type</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype><description><para>Attribute name </para></description></parameter><parameter name="args"><paramtype>args_map const &amp;</paramtype><description><para>Formatter arguments </para></description></parameter><description><para>The function creates a formatter for the specified attribute.</para><para>
</para></description></method>
</method-group>
</class><struct name="formatter_factory"><template>
      <template-type-parameter name="CharT"/>
    </template><description><para>Formatter factory base interface. </para></description><typedef name="char_type"><purpose>Character type. </purpose><type>CharT</type></typedef>
<typedef name="string_type"><purpose>String type. </purpose><type>std::basic_string&lt; char_type &gt;</type></typedef>
<typedef name="formatter_type"><purpose>The formatter function object. </purpose><type>basic_formatter&lt; char_type &gt;</type></typedef>
<typedef name="args_map"><description><para>Type of the map of formatter factory arguments [argument name -&gt; argument value]. This type of maps will be passed to formatter factories on attempt to create a formatter. </para></description><type>std::map&lt; string_type, string_type &gt;</type></typedef>
<method-group name="public member functions">
<method name="create_formatter" cv="= 0" specifiers="virtual"><type>formatter_type</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype><description><para>Attribute name </para></description></parameter><parameter name="args"><paramtype>args_map const &amp;</paramtype><description><para>Formatter arguments </para></description></parameter><description><para>The function creates a formatter for the specified attribute.</para><para>
</para></description></method>
</method-group>
<destructor><description><para>Default constructor</para><para>Virtual destructor </para></description></destructor>
<constructor cv="= delete"><parameter name=""><paramtype><classname>formatter_factory</classname> const &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= delete"><type><classname>formatter_factory</classname> &amp;</type><parameter name=""><paramtype><classname>formatter_factory</classname> const &amp;</paramtype></parameter></copy-assignment>
</struct>












































<function name="register_formatter_factory"><type>void</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="attr_name"><paramtype>attribute_name const &amp;</paramtype><description><para>Attribute name </para></description></parameter><parameter name="factory"><paramtype>shared_ptr&lt; <classname>formatter_factory</classname>&lt; CharT &gt; &gt; const &amp;</paramtype><description><para>Pointer to the formatter factory </para></description></parameter><purpose>The function registers a user-defined formatter factory. </purpose><description><para>The function registers a user-defined formatter factory. The registered factory function will be called when the formatter parser detects the specified attribute name in the formatter string.</para><para>

</para></description><requires><para><computeroutput>!!attr_name &amp;&amp; !!factory</computeroutput>.</para>
</requires></function>
<function name="register_formatter_factory"><type>boost::enable_if_c&lt; is_base_and_derived&lt; <classname>formatter_factory</classname>&lt; typename FactoryT::char_type &gt;, FactoryT &gt;::value &gt;::type</type><template>
          <template-type-parameter name="FactoryT"/>
        </template><parameter name="attr_name"><paramtype>attribute_name const &amp;</paramtype><description><para>Attribute name </para></description></parameter><parameter name="factory"><paramtype>shared_ptr&lt; FactoryT &gt; const &amp;</paramtype><description><para>Pointer to the formatter factory </para></description></parameter><purpose>The function registers a user-defined formatter factory. </purpose><description><para>The function registers a user-defined formatter factory. The registered factory function will be called when the formatter parser detects the specified attribute name in the formatter string.</para><para>

</para></description><requires><para><computeroutput>!!attr_name &amp;&amp; !!factory</computeroutput>.</para>
</requires></function>
<function name="register_simple_formatter_factory"><type>void</type><template>
          <template-type-parameter name="AttributeValueT"/>
          <template-type-parameter name="CharT"/>
        </template><parameter name="attr_name"><paramtype>attribute_name const &amp;</paramtype><description><para>Attribute name </para></description></parameter><purpose>The function registers a simple formatter factory. </purpose><description><para>The function registers a simple formatter factory. The registered factory will generate formatters that will be equivalent to the <computeroutput>log::expressions::attr</computeroutput> formatter (i.e. that will use the native <computeroutput>operator&lt;&lt;</computeroutput> to format the attribute value). The factory does not use any arguments from the format string, if specified.</para><para>

</para></description><requires><para><computeroutput>!!attr_name</computeroutput>.</para>
</requires></function>
<function name="parse_formatter"><type>basic_formatter&lt; CharT &gt;</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="begin"><paramtype>const CharT *</paramtype><description><para>Pointer to the first character of the sequence </para></description></parameter><parameter name="end"><paramtype>const CharT *</paramtype><description><para>Pointer to the after-the-last character of the sequence </para></description></parameter><description><para>The function parses a formatter from the sequence of characters</para><para>


<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception, if a formatter cannot be recognized in the character sequence. </para></description><requires><para><computeroutput>begin &lt;= end</computeroutput>, both pointers must not be NULL </para>
</requires><returns><para>The parsed formatter.</para>
</returns></function>
<function name="parse_formatter"><type>basic_formatter&lt; CharT &gt;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
          <template-type-parameter name="AllocatorT"/>
        </template><parameter name="str"><paramtype>std::basic_string&lt; CharT, TraitsT, AllocatorT &gt; const &amp;</paramtype><description><para>A string that contains format description </para></description></parameter><description><para>The function parses a formatter from the string</para><para>

<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception, if a formatter cannot be recognized in the character sequence. </para></description><returns><para>The parsed formatter.</para>
</returns></function>
<function name="parse_formatter"><type>basic_formatter&lt; CharT &gt;</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="str"><paramtype>const CharT *</paramtype><description><para>A string that contains format description. </para></description></parameter><description><para>The function parses a formatter from the string</para><para>


<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception, if a formatter cannot be recognized in the character sequence. </para></description><requires><para><computeroutput>str != NULL</computeroutput>, <computeroutput>str</computeroutput> points to a zero-terminated string </para>
</requires><returns><para>The parsed formatter.</para>
</returns></function>
































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/setup/from_settings.hpp">
<para><para>Andrey Semashev </para>

<para>11.10.2009</para>

The header contains definition of facilities that allows to initialize the library from settings. </para><namespace name="boost">
<namespace name="log">
<struct name="sink_factory"><template>
      <template-type-parameter name="CharT"/>
    </template><description><para>Sink factory base interface </para></description><typedef name="char_type"><purpose>Character type. </purpose><type>CharT</type></typedef>
<typedef name="string_type"><purpose>String type. </purpose><type>std::basic_string&lt; char_type &gt;</type></typedef>
<typedef name="settings_section"><purpose>Settings section type. </purpose><type><classname>basic_settings_section</classname>&lt; char_type &gt;</type></typedef>
<method-group name="public member functions">
<method name="create_sink" cv="= 0" specifiers="virtual"><type>shared_ptr&lt; sinks::sink &gt;</type><parameter name="settings"><paramtype>settings_section const &amp;</paramtype><description><para>Sink parameters </para></description></parameter><description><para>The function creates a formatter for the specified attribute.</para><para>
</para></description></method>
</method-group>
<destructor><description><para>Default constructor</para><para>Virtual destructor </para></description></destructor>
<constructor cv="= delete"><parameter name=""><paramtype><classname>sink_factory</classname> const &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= delete"><type><classname>sink_factory</classname> &amp;</type><parameter name=""><paramtype><classname>sink_factory</classname> const &amp;</paramtype></parameter></copy-assignment>
</struct>







































<function name="init_from_settings"><type>void</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="setts"><paramtype><classname>basic_settings_section</classname>&lt; CharT &gt; const &amp;</paramtype><description><para>Library settings container</para></description></parameter><description><para>The function initializes the logging library from a settings container</para><para>
<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception if the provided settings are not valid. </para></description></function>
<function name="register_sink_factory"><type>void</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="sink_name"><paramtype>const char *</paramtype><description><para>The custom sink name. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><parameter name="factory"><paramtype>shared_ptr&lt; <classname>sink_factory</classname>&lt; CharT &gt; &gt; const &amp;</paramtype><description><para>Pointer to the custom sink factory. Must not be NULL. </para></description></parameter><purpose>The function registers a factory for a custom sink. </purpose><description><para>The function registers a factory for a sink. The factory will be called to create sink instance when the parser discovers the specified sink type in the settings file. The factory must accept a map of parameters [parameter name -&gt; parameter value] that it may use to initialize the sink. The factory must return a non-NULL pointer to the constructed sink instance.</para><para>
</para></description></function>
<function name="register_sink_factory"><type>void</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="sink_name"><paramtype>std::string const &amp;</paramtype><description><para>The custom sink name </para></description></parameter><parameter name="factory"><paramtype>shared_ptr&lt; <classname>sink_factory</classname>&lt; CharT &gt; &gt; const &amp;</paramtype><description><para>Pointer to the custom sink factory. Must not be NULL. </para></description></parameter><purpose>The function registers a factory for a custom sink. </purpose><description><para>The function registers a factory for a sink. The factory will be called to create sink instance when the parser discovers the specified sink type in the settings file. The factory must accept a map of parameters [parameter name -&gt; parameter value] that it may use to initialize the sink. The factory must return a non-NULL pointer to the constructed sink instance.</para><para>
</para></description></function>
<function name="register_sink_factory"><type>boost::enable_if_c&lt; is_base_and_derived&lt; <classname>sink_factory</classname>&lt; typename FactoryT::char_type &gt;, FactoryT &gt;::value &gt;::type</type><template>
          <template-type-parameter name="FactoryT"/>
        </template><parameter name="sink_name"><paramtype>const char *</paramtype><description><para>The custom sink name. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><parameter name="factory"><paramtype>shared_ptr&lt; FactoryT &gt; const &amp;</paramtype><description><para>Pointer to the custom sink factory. Must not be NULL. </para></description></parameter><purpose>The function registers a factory for a custom sink. </purpose><description><para>The function registers a factory for a sink. The factory will be called to create sink instance when the parser discovers the specified sink type in the settings file. The factory must accept a map of parameters [parameter name -&gt; parameter value] that it may use to initialize the sink. The factory must return a non-NULL pointer to the constructed sink instance.</para><para>
</para></description></function>
<function name="register_sink_factory"><type>boost::enable_if_c&lt; is_base_and_derived&lt; <classname>sink_factory</classname>&lt; typename FactoryT::char_type &gt;, FactoryT &gt;::value &gt;::type</type><template>
          <template-type-parameter name="FactoryT"/>
        </template><parameter name="sink_name"><paramtype>std::string const &amp;</paramtype><description><para>The custom sink name </para></description></parameter><parameter name="factory"><paramtype>shared_ptr&lt; FactoryT &gt; const &amp;</paramtype><description><para>Pointer to the custom sink factory. Must not be NULL. </para></description></parameter><purpose>The function registers a factory for a custom sink. </purpose><description><para>The function registers a factory for a sink. The factory will be called to create sink instance when the parser discovers the specified sink type in the settings file. The factory must accept a map of parameters [parameter name -&gt; parameter value] that it may use to initialize the sink. The factory must return a non-NULL pointer to the constructed sink instance.</para><para>
</para></description></function>






































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/setup/from_stream.hpp">
<para><para>Andrey Semashev </para>

<para>22.03.2008</para>

The header contains definition of facilities that allows to initialize the library from a settings file. </para><namespace name="boost">
<namespace name="log">







































<function name="init_from_stream"><type>void</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="strm"><paramtype>std::basic_istream&lt; CharT &gt; &amp;</paramtype><description><para>Stream, that provides library settings</para></description></parameter><description><para>The function initializes the logging library from a stream containing logging settings</para><para>
<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception if the read data cannot be interpreted as the library settings </para></description></function>











































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/setup/settings.hpp">
<para><para>Andrey Semashev </para>

<para>11.10.2009</para>

The header contains definition of the library settings container. </para><namespace name="boost">
<namespace name="log">
<class name="basic_settings"><template>
      <template-type-parameter name="CharT"/>
    </template><inherit access="public">boost::log::basic_settings_section&lt; CharT &gt;</inherit><purpose>The class represents settings container. </purpose><description><para>All settings are presented as a number of named parameters divided into named sections. The parameters values are stored as strings. Individual parameters may be queried via subscript operators, like this:</para><para><computeroutput><programlisting>
optional&lt; string &gt; param = settings["Section1"]["Param1"]; // reads parameter "Param1" in section "Section1"
                                                           // returns an empty value if no such parameter exists
settings["Section2"]["Param2"] = 10; // sets the parameter "Param2" in section "Section2"
                                     // to value "10"
</programlisting></computeroutput></para><para>There are also other methods to work with parameters. </para></description><typedef name="section"><purpose>Section type. </purpose><type><classname>basic_settings_section</classname>&lt; CharT &gt;</type></typedef>
<typedef name="property_tree_type"><purpose>Property tree type. </purpose><type>section::property_tree_type</type></typedef>
<method-group name="public member functions">
</method-group>
<constructor><description><para>Default constructor. Creates an empty settings container. </para></description></constructor>
<constructor><parameter name="that"><paramtype><classname>basic_settings</classname> const &amp;</paramtype></parameter><description><para>Copy constructor. </para></description></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>this_type</classname> &amp;&amp;</paramtype></parameter><description><para>Move constructor. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="tree"><paramtype>property_tree_type const &amp;</paramtype></parameter><description><para>Initializing constructor. Creates a settings container with the copy of the specified property tree. </para></description></constructor>
<destructor><description><para>Destructor </para></description></destructor>
<copy-assignment><type><classname>basic_settings</classname> &amp;</type><parameter name="that"><paramtype><classname>basic_settings</classname> const &amp;</paramtype></parameter><description><para>Copy assignment operator. </para></description></copy-assignment>
<copy-assignment cv="noexcept"><type><classname>basic_settings</classname> &amp;</type><parameter name="that"><paramtype><classname>basic_settings</classname> &amp;&amp;</paramtype></parameter><description><para>Move assignment operator. </para></description></copy-assignment>
</class><class name="basic_settings_section"><template>
      <template-type-parameter name="CharT"/>
    </template><purpose>The class represents a reference to the settings container section. </purpose><description><para>The section refers to a sub-tree of the library settings container. It does not own the referred sub-tree but allows for convenient access to parameters within the subsection. </para></description><typedef name="char_type"><purpose>Character type. </purpose><type>CharT</type></typedef>
<typedef name="string_type"><purpose>String type. </purpose><type>std::basic_string&lt; char_type &gt;</type></typedef>
<typedef name="property_tree_type"><purpose>Property tree type. </purpose><type>property_tree::basic_ptree&lt; std::string, string_type &gt;</type></typedef>
<typedef name="path_type"><purpose>Property tree path type. </purpose><type>property_tree_type::path_type</type></typedef>
<typedef name="const_reference"><description><para>Constant reference to the parameter value </para></description><type>implementation_defined</type></typedef>
<typedef name="reference"><description><para>Mutable reference to the parameter value </para></description><type>implementation_defined</type></typedef>
<typedef name="const_iterator"><description><para>Constant iterator over nested parameters and subsections </para></description><type>implementation_defined</type></typedef>
<typedef name="iterator"><description><para>Mutable iterator over nested parameters and subsections </para></description><type>implementation_defined</type></typedef>
<method-group name="public member functions">
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>bool</type><description><para>Checks if the section refers to the container. </para></description></method>
<method name="operator!" cv="const noexcept"><type>bool</type><description><para>Checks if the section refers to the container. </para></description></method>
<method name="begin"><type>iterator</type><description><para>Returns an iterator over the nested subsections and parameters. </para></description></method>
<method name="end"><type>iterator</type><description><para>Returns an iterator over the nested subsections and parameters. </para></description></method>
<method name="begin" cv="const"><type>const_iterator</type><description><para>Returns an iterator over the nested subsections and parameters. </para></description></method>
<method name="end" cv="const"><type>const_iterator</type><description><para>Returns an iterator over the nested subsections and parameters. </para></description></method>
<method name="rbegin"><type>reverse_iterator</type><description><para>Returns a reverse iterator over the nested subsections and parameters. </para></description></method>
<method name="rend"><type>reverse_iterator</type><description><para>Returns a reverse iterator over the nested subsections and parameters. </para></description></method>
<method name="rbegin" cv="const"><type>const_reverse_iterator</type><description><para>Returns a reverse iterator over the nested subsections and parameters. </para></description></method>
<method name="rend" cv="const"><type>const_reverse_iterator</type><description><para>Returns a reverse iterator over the nested subsections and parameters. </para></description></method>
<method name="empty" cv="const"><type>bool</type><description><para>Checks if the container is empty (i.e. contains no sections and parameters). </para></description></method>
<method name="operator[]"><type>reference</type><parameter name="section_name"><paramtype>std::string const &amp;</paramtype><description><para>The name of the section in which the parameter resides </para></description></parameter><description><para>Accessor to a single parameter. This operator should be used in conjunction with the subsequent subscript operator that designates the parameter name.</para><para>

</para></description><returns><para>An unspecified reference type that can be used for parameter name specifying </para>
</returns></method>
<method name="operator[]" cv="const"><type>const_reference</type><parameter name="section_name"><paramtype>std::string const &amp;</paramtype><description><para>The name of the section in which the parameter resides </para></description></parameter><description><para>Accessor to a single parameter. This operator should be used in conjunction with the subsequent subscript operator that designates the parameter name.</para><para>

</para></description><returns><para>An unspecified reference type that can be used for parameter name specifying </para>
</returns></method>
<method name="operator[]"><type>reference</type><parameter name="section_name"><paramtype>const char *</paramtype><description><para>The name of the section in which the parameter resides </para></description></parameter><description><para>Accessor to a single parameter. This operator should be used in conjunction with the subsequent subscript operator that designates the parameter name.</para><para>

</para></description><returns><para>An unspecified reference type that can be used for parameter name specifying </para>
</returns></method>
<method name="operator[]" cv="const"><type>const_reference</type><parameter name="section_name"><paramtype>const char *</paramtype><description><para>The name of the section in which the parameter resides </para></description></parameter><description><para>Accessor to a single parameter. This operator should be used in conjunction with the subsequent subscript operator that designates the parameter name.</para><para>

</para></description><returns><para>An unspecified reference type that can be used for parameter name specifying </para>
</returns></method>
<method name="property_tree" cv="const"><type>property_tree_type const  &amp;</type><description><para>Accessor for the embedded property tree </para></description></method>
<method name="property_tree"><type>property_tree_type &amp;</type><description><para>Accessor for the embedded property tree </para></description></method>
<method name="has_section" cv="const"><type>bool</type><parameter name="section_name"><paramtype>string_type const &amp;</paramtype><description><para>The name of the section </para></description></parameter><description><para>Checks if the specified section is present in the container.</para><para>
</para></description></method>
<method name="has_parameter" cv="const"><type>bool</type><parameter name="section_name"><paramtype>string_type const &amp;</paramtype><description><para>The name of the section in which the parameter resides </para></description></parameter><parameter name="param_name"><paramtype>string_type const &amp;</paramtype><description><para>The name of the parameter </para></description></parameter><description><para>Checks if the specified parameter is present in the container.</para><para>
</para></description></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="that"><paramtype><classname>basic_settings_section</classname> &amp;</paramtype></parameter><description><para>Swaps two references to settings sections. </para></description></method>
</method-group>
<constructor cv="noexcept"><description><para>Default constructor. Creates an empty settings container. </para></description></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>basic_settings_section</classname> const &amp;</paramtype></parameter><description><para>Copy constructor. </para></description></constructor>
<method-group name="protected member functions">
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="tree"><paramtype>property_tree_type *</paramtype></parameter></constructor>
</class><typedef name="settings"><purpose>Convenience typedef for narrow-character logging. </purpose><type><classname>basic_settings</classname>&lt; char &gt;</type></typedef>
<typedef name="settings_section"><purpose>Convenience typedef for narrow-character logging. </purpose><type><classname>basic_settings_section</classname>&lt; char &gt;</type></typedef>
<typedef name="wsettings"><purpose>Convenience typedef for wide-character logging. </purpose><type><classname>basic_settings</classname>&lt; wchar_t &gt;</type></typedef>
<typedef name="wsettings_section"><purpose>Convenience typedef for wide-character logging. </purpose><type><classname>basic_settings_section</classname>&lt; wchar_t &gt;</type></typedef>






































<function name="swap"><type>void</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="left"><paramtype><classname>basic_settings_section</classname>&lt; CharT &gt; &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>basic_settings_section</classname>&lt; CharT &gt; &amp;</paramtype></parameter></function>












































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/setup/settings_parser.hpp">
<para><para>Andrey Semashev </para>

<para>20.07.2012</para>

The header contains definition of a settings parser function. </para><namespace name="boost">
<namespace name="log">





































<function name="parse_settings"><type><classname>basic_settings</classname>&lt; CharT &gt;</type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="strm"><paramtype>std::basic_istream&lt; CharT &gt; &amp;</paramtype><description><para>Stream, that provides library settings</para></description></parameter><description><para>The function parses library settings from an input stream</para><para>
<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception if the read data cannot be interpreted as the library settings </para></description></function>













































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/strictest_lock.hpp">
<para><para>Andrey Semashev </para>

<para>30.05.2010</para>

The header contains definition of the <computeroutput>strictest_lock</computeroutput> metafunction that allows to select a lock with the strictest access requirements. </para><namespace name="boost">
<namespace name="log">
<struct name="strictest_lock"><template>
      <template-nontype-parameter name="LocksT"><type>typename...</type></template-nontype-parameter>
    </template><purpose>The metafunction selects the most strict lock type of the specified. </purpose><description><para>The template supports all lock types provided by the Boost.Thread library (except for <computeroutput>upgrade_to_unique_lock</computeroutput>), plus additional pseudo-lock <computeroutput>no_lock</computeroutput> that indicates no locking at all. Exclusive locks are considered the strictest, shared locks are weaker, and <computeroutput>no_lock</computeroutput> is the weakest. </para></description><typedef name="type"><type>implementation_defined</type></typedef>
</struct><struct name="thread_access_mode_of"><template>
      <template-type-parameter name="LockT"/>
    </template><purpose>The trait allows to select an access mode by the lock type. </purpose></struct><struct-specialization name="thread_access_mode_of"><template>
      <template-type-parameter name="MutexT"/>
    </template><specialization><template-arg>boost::log::aux::exclusive_lock_guard&lt; MutexT &gt;</template-arg></specialization><inherit access="public">boost::integral_constant&lt; lock_access_mode, exclusive_access &gt;</inherit></struct-specialization><struct-specialization name="thread_access_mode_of"><template>
      <template-type-parameter name="MutexT1"/>
      <template-type-parameter name="MutexT2"/>
    </template><specialization><template-arg>boost::log::aux::multiple_unique_lock2&lt; MutexT1</template-arg><template-arg>MutexT2 &gt;</template-arg></specialization><inherit access="public">boost::integral_constant&lt; lock_access_mode, exclusive_access &gt;</inherit></struct-specialization><struct-specialization name="thread_access_mode_of"><template>
      <template-type-parameter name="MutexT"/>
    </template><specialization><template-arg>boost::log::aux::shared_lock_guard&lt; MutexT &gt;</template-arg></specialization><inherit access="public">boost::integral_constant&lt; lock_access_mode, shared_access &gt;</inherit></struct-specialization><struct-specialization name="thread_access_mode_of"><template>
      <template-type-parameter name="MutexT"/>
    </template><specialization><template-arg>lock_guard&lt; MutexT &gt;</template-arg></specialization><inherit access="public">boost::integral_constant&lt; lock_access_mode, exclusive_access &gt;</inherit></struct-specialization><struct-specialization name="thread_access_mode_of"><template>
      <template-type-parameter name="MutexT"/>
    </template><specialization><template-arg>no_lock&lt; MutexT &gt;</template-arg></specialization><inherit access="public">boost::integral_constant&lt; lock_access_mode, unlocked_access &gt;</inherit></struct-specialization><struct-specialization name="thread_access_mode_of"><template>
      <template-type-parameter name="MutexT"/>
    </template><specialization><template-arg>shared_lock&lt; MutexT &gt;</template-arg></specialization><inherit access="public">boost::integral_constant&lt; lock_access_mode, shared_access &gt;</inherit></struct-specialization><struct-specialization name="thread_access_mode_of"><template>
      <template-type-parameter name="MutexT"/>
    </template><specialization><template-arg>shared_lock_guard&lt; MutexT &gt;</template-arg></specialization><inherit access="public">boost::integral_constant&lt; lock_access_mode, shared_access &gt;</inherit></struct-specialization><struct-specialization name="thread_access_mode_of"><template>
      <template-type-parameter name="MutexT"/>
    </template><specialization><template-arg>unique_lock&lt; MutexT &gt;</template-arg></specialization><inherit access="public">boost::integral_constant&lt; lock_access_mode, exclusive_access &gt;</inherit></struct-specialization><struct-specialization name="thread_access_mode_of"><template>
      <template-type-parameter name="MutexT"/>
    </template><specialization><template-arg>upgrade_lock&lt; MutexT &gt;</template-arg></specialization><inherit access="public">boost::integral_constant&lt; lock_access_mode, shared_access &gt;</inherit></struct-specialization><enum name="lock_access_mode"><enumvalue name="unlocked_access"><purpose>A thread that owns this kind of lock doesn't restrict other threads in any way. </purpose></enumvalue><enumvalue name="shared_access"><purpose>A thread that owns this kind of lock requires that no other thread modify the locked data. </purpose></enumvalue><enumvalue name="exclusive_access"><purpose>A thread that owns this kind of lock requires that no other thread has access to the locked data. </purpose></enumvalue><purpose>Access modes for different types of locks. </purpose></enum>



















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/string_literal.hpp">
<para><para>Andrey Semashev </para>

<para>24.06.2007</para>

The header contains implementation of a constant string literal wrapper. </para><namespace name="boost">
<namespace name="log">





















<function name="operator&lt;&lt;"><type>std::basic_ostream&lt; CharT, StrmTraitsT &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="StrmTraitsT"/>
          <template-type-parameter name="LitTraitsT"/>
        </template><parameter name="strm"><paramtype>std::basic_ostream&lt; CharT, StrmTraitsT &gt; &amp;</paramtype></parameter><parameter name="lit"><paramtype><classname>basic_string_literal</classname>&lt; CharT, LitTraitsT &gt; const &amp;</paramtype></parameter><purpose>Output operator. </purpose></function>
<function name="swap"><type>constexpr void</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
        </template><parameter name="left"><paramtype><classname>basic_string_literal</classname>&lt; CharT, TraitsT &gt; &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>basic_string_literal</classname>&lt; CharT, TraitsT &gt; &amp;</paramtype></parameter><purpose>External swap. </purpose></function>
<function name="str_literal"><type>constexpr <classname>basic_string_literal</classname>&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="LenV"><type>std::size_t</type></template-nontype-parameter>
        </template><parameter name="p"><paramtype>T(&amp;)</paramtype></parameter><purpose>Creates a string literal wrapper from a constant string literal. </purpose></function>



























































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/string_literal_fwd.hpp">
<para><para>Andrey Semashev </para>

<para>24.06.2007</para>

The header contains forward declaration of a constant string literal wrapper. </para><namespace name="boost">
<namespace name="log">
<class name="basic_string_literal"><template>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="TraitsT"/>
    </template><purpose>String literal wrapper. </purpose><description><para>The <computeroutput><classname alt="boost::log::basic_string_literal">basic_string_literal</classname></computeroutput> is a thin wrapper around a constant string literal. It provides interface similar to STL strings, but because of read-only nature of string literals, lacks ability to modify string contents. However, <computeroutput><classname alt="boost::log::basic_string_literal">basic_string_literal</classname></computeroutput> objects can be assigned to and cleared.</para><para>The main advantage of this class comparing to other string classes is that it doesn't dynamically allocate memory and therefore is fast, thin and exception safe. </para></description><method-group name="public member functions">
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (equality)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if the comparand string equals to this string, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><description><para>Lexicographical comparison (equality)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if the comparand string equals to this string, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype>string_type const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (equality)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if the comparand string equals to this string, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (inequality)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if the comparand string is not equal to this string, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><description><para>Lexicographical comparison (inequality)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if the comparand string is not equal to this string, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype>string_type const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (inequality)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if the comparand string is not equal to this string, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&lt;" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (less ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is less than the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&lt;" cv="const noexcept"><type>bool</type><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><description><para>Lexicographical comparison (less ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is less than the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&lt;" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype>string_type const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (less ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is less than the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&lt;=" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (less or equal ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is less or equal to the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&lt;=" cv="const noexcept"><type>bool</type><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><description><para>Lexicographical comparison (less or equal ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is less or equal to the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&lt;=" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype>string_type const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (less or equal ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is less or equal to the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&gt;" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (greater ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is greater than the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&gt;" cv="const noexcept"><type>bool</type><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><description><para>Lexicographical comparison (greater ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is greater than the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&gt;" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype>string_type const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (greater ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is greater than the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&gt;=" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (greater or equal ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is greater or equal to the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&gt;=" cv="const noexcept"><type>bool</type><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><description><para>Lexicographical comparison (greater or qual ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is greater or equal to the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator&gt;=" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype>string_type const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographical comparison (greater or equal ordering)</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if this string is greater or equal to the comparand, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="operator[]" cv="const noexcept"><type>constexpr const_reference</type><parameter name="i"><paramtype>size_type</paramtype><description><para>Requested character index </para></description></parameter><description><para>Subscript operator</para><para>


</para></description><requires><para><computeroutput>i &lt; size()</computeroutput> </para>
</requires><returns><para>Constant reference to the requested character </para>
</returns></method>
<method name="at" cv="const"><type>const_reference</type><parameter name="i"><paramtype>size_type</paramtype><description><para>Requested character index </para></description></parameter><description><para>Checked subscript</para><para>

<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception if index <emphasis>i</emphasis> is out of string boundaries </para></description><returns><para>Constant reference to the requested character</para>
</returns></method>
<method name="c_str" cv="const noexcept"><type>constexpr const_pointer</type><description><para>
</para></description><returns><para>Pointer to the beginning of the literal </para>
</returns></method>
<method name="data" cv="const noexcept"><type>constexpr const_pointer</type><description><para>
</para></description><returns><para>Pointer to the beginning of the literal </para>
</returns></method>
<method name="size" cv="const noexcept"><type>constexpr size_type</type><description><para>
</para></description><returns><para>Length of the literal </para>
</returns></method>
<method name="length" cv="const noexcept"><type>constexpr size_type</type><description><para>
</para></description><returns><para>Length of the literal </para>
</returns></method>
<method name="empty" cv="const noexcept"><type>constexpr bool</type><description><para>
</para></description><returns><para><computeroutput>true</computeroutput> if the literal is an empty string, <computeroutput>false</computeroutput> otherwise </para>
</returns></method>
<method name="begin" cv="const noexcept"><type>constexpr const_iterator</type><description><para>
</para></description><returns><para>Iterator that points to the first character of the literal </para>
</returns></method>
<method name="end" cv="const noexcept"><type>constexpr const_iterator</type><description><para>
</para></description><returns><para>Iterator that points after the last character of the literal </para>
</returns></method>
<method name="rbegin" cv="const noexcept"><type>const_reverse_iterator</type><description><para>
</para></description><returns><para>Reverse iterator that points to the last character of the literal </para>
</returns></method>
<method name="rend" cv="const noexcept"><type>const_reverse_iterator</type><description><para>
</para></description><returns><para>Reverse iterator that points before the first character of the literal </para>
</returns></method>
<method name="str" cv="const"><type>string_type</type><description><para>
</para></description><returns><para>STL string constructed from the literal </para>
</returns></method>
<method name="clear" cv="noexcept"><type>constexpr void</type><description><para>The method clears the literal</para><para>
</para></description><postconditions><para><computeroutput>empty() == true</computeroutput> </para>
</postconditions></method>
<method name="swap" cv="noexcept"><type>constexpr void</type><parameter name="that"><paramtype><classname>this_type</classname> &amp;</paramtype></parameter><description><para>The method swaps two literals </para></description></method>
<method name="assign" cv="noexcept"><type>constexpr <classname>this_type</classname> &amp;</type><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Source literal to copy string from </para></description></parameter><description><para>Assignment from another literal</para><para>

</para></description><postconditions><para><computeroutput>*this == that</computeroutput> </para>
</postconditions></method>
<method name="assign" cv="noexcept"><type>constexpr <classname>this_type</classname> &amp;</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="LenV"><type>size_type</type></template-nontype-parameter>
        </template><parameter name="p"><paramtype>T(&amp;)</paramtype><description><para>A zero-terminated constant sequence of characters </para></description></parameter><description><para>Assignment from another literal</para><para>

</para></description><postconditions><para><computeroutput>*this == p</computeroutput> </para>
</postconditions></method>
<method name="copy" cv="const"><type>size_type</type><parameter name="str"><paramtype>value_type *</paramtype><description><para>Pointer to the external buffer beginning. Must not be NULL. The buffer must have enough capacity to accommodate the requested number of characters. </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>Maximum number of characters to copy </para></description></parameter><parameter name="pos"><paramtype>size_type</paramtype><default>0</default><description><para>Starting position to start copying from </para></description></parameter><description><para>The method copies the literal or its portion to an external buffer</para><para>


<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception if <emphasis>pos</emphasis> is out of range. </para></description><requires><para><computeroutput>pos &lt;= size()</computeroutput> </para>
</requires><returns><para>Number of characters copied</para>
</returns></method>
<method name="compare" cv="const"><type>int</type><parameter name="pos"><paramtype>size_type</paramtype><description><para>Starting position within this string to perform comparison to </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>Length of the substring of this string to perform comparison to </para></description></parameter><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a sequence of characters, must not be NULL. </para></description></parameter><parameter name="len"><paramtype>size_type</paramtype><description><para>Number of characters in the sequence <emphasis>str</emphasis>. </para></description></parameter><description><para>Lexicographically compares the argument string to a part of this string</para><para>


<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception if <emphasis>pos</emphasis> is out of range. </para></description><requires><para><computeroutput>pos &lt;= size()</computeroutput> </para>
</requires><returns><para>Zero if the comparand equals this string, a negative value if this string is less than the comparand, a positive value if this string is greater than the comparand.</para>
</returns></method>
<method name="compare" cv="const noexcept"><type>int</type><parameter name="pos"><paramtype>size_type</paramtype><description><para>Starting position within this string to perform comparison to </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>Length of the substring of this string to perform comparison to </para></description></parameter><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><description><para>Lexicographically compares the argument string to a part of this string</para><para>


<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception if <emphasis>pos</emphasis> is out of range. </para></description><requires><para><computeroutput>pos &lt;= size()</computeroutput> </para>
</requires><returns><para>Zero if the comparand equals this string, a negative value if this string is less than the comparand, a positive value if this string is greater than the comparand.</para>
</returns></method>
<method name="compare" cv="const noexcept"><type>int</type><parameter name="pos"><paramtype>size_type</paramtype><description><para>Starting position within this string to perform comparison to </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>Length of the substring of this string to perform comparison to </para></description></parameter><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographically compares the argument string literal to a part of this string</para><para>


<emphasis role="bold">Throws:</emphasis> An <computeroutput>std::exception</computeroutput>-based exception if <emphasis>pos</emphasis> is out of range. </para></description><requires><para><computeroutput>pos &lt;= size()</computeroutput> </para>
</requires><returns><para>Zero if the comparand equals this string, a negative value if this string is less than the comparand, a positive value if this string is greater than the comparand.</para>
</returns></method>
<method name="compare" cv="const noexcept"><type>int</type><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a sequence of characters, must not be NULL. </para></description></parameter><parameter name="len"><paramtype>size_type</paramtype><description><para>Number of characters in the sequence <emphasis>str</emphasis>. </para></description></parameter><description><para>Lexicographically compares the argument string to this string</para><para>

</para></description><returns><para>Zero if the comparand equals this string, a negative value if this string is less than the comparand, a positive value if this string is greater than the comparand. </para>
</returns></method>
<method name="compare" cv="const noexcept"><type>int</type><parameter name="str"><paramtype>const_pointer</paramtype><description><para>Comparand. Must point to a zero-terminated sequence of characters, must not be NULL. </para></description></parameter><description><para>Lexicographically compares the argument string to this string</para><para>

</para></description><returns><para>Zero if the comparand equals this string, a negative value if this string is less than the comparand, a positive value if this string is greater than the comparand. </para>
</returns></method>
<method name="compare" cv="const noexcept"><type>int</type><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Lexicographically compares the argument string to this string</para><para>

</para></description><returns><para>Zero if the comparand equals this string, a negative value if this string is less than the comparand, a positive value if this string is greater than the comparand. </para>
</returns></method>
</method-group>
<constructor cv="noexcept"><description><para>Constructor</para><para>
</para></description><postconditions><para><computeroutput>empty() == true</computeroutput> </para>
</postconditions></constructor>
<constructor cv="noexcept"><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="LenV"><type>size_type</type></template-nontype-parameter>
        </template><parameter name="p"><paramtype>T(&amp;)</paramtype><description><para>A zero-terminated constant sequence of characters </para></description></parameter><description><para>Constructor from a string literal</para><para>

</para></description><postconditions><para><computeroutput>*this == p</computeroutput> </para>
</postconditions></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>basic_string_literal</classname> const &amp;</paramtype><description><para>Source literal to copy string from </para></description></parameter><description><para>Copy constructor</para><para>

</para></description><postconditions><para><computeroutput>*this == that</computeroutput> </para>
</postconditions></constructor>
<copy-assignment cv="noexcept"><type>constexpr <classname>this_type</classname> &amp;</type><parameter name="that"><paramtype><classname>this_type</classname> const &amp;</paramtype><description><para>Source literal to copy string from </para></description></parameter><description><para>Assignment operator</para><para>

</para></description><postconditions><para><computeroutput>*this == that</computeroutput> </para>
</postconditions></copy-assignment>
<copy-assignment cv="noexcept"><type>constexpr <classname>this_type</classname> &amp;</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="LenV"><type>size_type</type></template-nontype-parameter>
        </template><parameter name="p"><paramtype>T(&amp;)</paramtype><description><para>A zero-terminated constant sequence of characters </para></description></parameter><description><para>Assignment from a string literal</para><para>

</para></description><postconditions><para><computeroutput>*this == p</computeroutput> </para>
</postconditions></copy-assignment>
</class><typedef name="string_literal"><purpose>String literal type for narrow characters. </purpose><type><classname>basic_string_literal</classname>&lt; char &gt;</type></typedef>
<typedef name="wstring_literal"><purpose>String literal type for wide characters. </purpose><type><classname>basic_string_literal</classname>&lt; wchar_t &gt;</type></typedef>



















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/type_dispatch/date_time_types.hpp">
<para><para>Andrey Semashev </para>

<para>13.03.2008</para>

The header contains definition of date and time-related types supported by the library by default. </para><namespace name="boost">
<namespace name="log">
<typedef name="native_date_time_types"><description><para>An MPL-sequence of natively supported date and time types of attributes </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="boost_date_time_types"><description><para>An MPL-sequence of Boost date and time types of attributes </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="date_time_types"><description><para>An MPL-sequence with the complete list of the supported date and time types </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="native_date_types"><description><para>An MPL-sequence of natively supported date types of attributes </para></description><type>native_date_time_types</type></typedef>
<typedef name="boost_date_types"><description><para>An MPL-sequence of Boost date types of attributes </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="date_types"><description><para>An MPL-sequence with the complete list of the supported date types </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="native_time_types"><description><para>An MPL-sequence of natively supported time types </para></description><type>native_date_time_types</type></typedef>
<typedef name="boost_time_types"><purpose>An MPL-sequence of Boost time types. </purpose><type>boost_date_time_types</type></typedef>
<typedef name="time_types"><description><para>An MPL-sequence with the complete list of the supported time types </para></description><type>date_time_types</type></typedef>
<typedef name="native_time_duration_types"><description><para>An MPL-sequence of natively supported time duration types of attributes </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="boost_time_duration_types"><description><para>An MPL-sequence of Boost time duration types of attributes </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="time_duration_types"><description><para>An MPL-sequence with the complete list of the supported time duration types </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="boost_time_period_types"><description><para>An MPL-sequence of Boost time duration types of attributes </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="time_period_types"><description><para>An MPL-sequence with the complete list of the supported time period types </para></description><type>boost_time_period_types</type></typedef>



















































































































</namespace>
</namespace>
<macro name="BOOST_LOG_NATIVE_DATE_TIME_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of the standard C date/time types. </purpose></macro>
<macro name="BOOST_LOG_NATIVE_DATE_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of the standard C date types. </purpose></macro>
<macro name="BOOST_LOG_BOOST_DATE_TIME_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of the Boost date/time types. </purpose></macro>
<macro name="BOOST_LOG_DATE_TIME_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of date/time types. </purpose></macro>
<macro name="BOOST_LOG_BOOST_DATE_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of the Boost date types. </purpose></macro>
<macro name="BOOST_LOG_DATE_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of date types. </purpose></macro>
<macro name="BOOST_LOG_NATIVE_TIME_DURATION_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of the standard time duration types. </purpose></macro>
<macro name="BOOST_LOG_BOOST_TIME_DURATION_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of the Boost time duration types. </purpose></macro>
<macro name="BOOST_LOG_TIME_DURATION_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of time duration types. </purpose></macro>
<macro name="BOOST_LOG_BOOST_TIME_PERIOD_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of the Boost time period types. </purpose></macro>
<macro name="BOOST_LOG_TIME_PERIOD_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of time period types. </purpose></macro>
</header>
<header name="boost/log/utility/type_dispatch/dynamic_type_dispatcher.hpp">
<para><para>Andrey Semashev </para>

<para>15.04.2007</para>

The header contains implementation of the run-time type dispatcher. </para><namespace name="boost">
<namespace name="log">
<class name="dynamic_type_dispatcher"><inherit access="public">type_dispatcher</inherit><purpose>A dynamic type dispatcher. </purpose><description><para>The type dispatcher can be used to pass objects of arbitrary types from one component to another. With regard to the library, the type dispatcher can be used to extract attribute values.</para><para>The dynamic type dispatcher can be initialized in run time and, therefore, can support different types, depending on runtime conditions. Each supported type is associated with a functional object that will be called when an object of the type is dispatched. </para></description><method-group name="public member functions">
<method name="register_type"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="VisitorT"/>
        </template><parameter name="visitor"><paramtype>VisitorT const &amp;</paramtype><description><para>Function object that will be associated with the type <computeroutput>T</computeroutput> </para></description></parameter><description><para>The method registers a new type</para><para>
</para></description></method>
<method name="registered_types_count" cv="const"><type>dispatching_map::size_type</type><description><para>The method returns the number of registered types </para></description></method>
</method-group>
<constructor><description><para>Default constructor </para></description></constructor>
<constructor><parameter name="that"><paramtype><classname>dynamic_type_dispatcher</classname> const &amp;</paramtype></parameter><description><para>Copy constructor </para></description></constructor>
<copy-assignment><type><classname>dynamic_type_dispatcher</classname> &amp;</type><parameter name="that"><paramtype><classname>dynamic_type_dispatcher</classname> const &amp;</paramtype></parameter><description><para>Copy assignment </para></description></copy-assignment>
</class>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/type_dispatch/standard_types.hpp">
<para><para>Andrey Semashev </para>

<para>19.05.2007</para>

The header contains definition of standard types supported by the library by default. </para><namespace name="boost">
<namespace name="log">
<typedef name="integral_types"><description><para>An MPL-sequence of integral types of attributes, supported by default </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="floating_point_types"><description><para>An MPL-sequence of FP types of attributes, supported by default </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="arithmetic_types"><description><para>An MPL-sequence of all numeric types of attributes, supported by default </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="numeric_types"><purpose>Deprecated alias. </purpose><type>arithmetic_types</type></typedef>
<typedef name="string_types"><description><para>An MPL-sequence of string types of attributes, supported by default </para></description><type>mpl::vector&lt;&gt;</type></typedef>
<typedef name="default_attribute_types"><purpose>Deprecated alias. </purpose><type>default_attribute_value_types</type></typedef>

































































<function name="BOOST_PP_CAT"><type>typedef</type><parameter name=""><paramtype>mpl::vector</paramtype></parameter><parameter name=""><paramtype>BOOST_PP_SEQ_SIZE((bool)(signed char)(unsigned char)(short)(unsigned short)(int)(unsigned int)(long)(unsigned long)(char)(wchar_t)(char16_t)(char32_t)(float)(double)(long double)(std::string)(boost::log::string_literal)(std::wstring)(boost::log::wstring_literal))</paramtype></parameter><description><para>An MPL-sequence of all attribute value types that are supported by the library by default. </para></description></function>

















































</namespace>
</namespace>
<macro name="BOOST_LOG_AUX_STANDARD_TYPE_WCHAR_T" kind="functionlike"><macro-parameter name=""/></macro>
<macro name="BOOST_LOG_AUX_STANDARD_TYPE_CHAR16_T" kind="functionlike"><macro-parameter name=""/></macro>
<macro name="BOOST_LOG_AUX_STANDARD_TYPE_CHAR32_T" kind="functionlike"><macro-parameter name=""/></macro>
<macro name="BOOST_LOG_STANDARD_CHAR_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of character types. </purpose></macro>
<macro name="BOOST_LOG_AUX_STANDARD_LONG_LONG_TYPES" kind="functionlike"><macro-parameter name=""/></macro>
<macro name="BOOST_LOG_STANDARD_INTEGRAL_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of integral types. </purpose></macro>
<macro name="BOOST_LOG_STANDARD_FLOATING_POINT_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of floating point types. </purpose></macro>
<macro name="BOOST_LOG_STANDARD_ARITHMETIC_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of arithmetic types. </purpose></macro>
<macro name="BOOST_LOG_AUX_STANDARD_STRING_TYPES" kind="functionlike"><macro-parameter name=""/></macro>
<macro name="BOOST_LOG_AUX_STANDARD_WSTRING_TYPES" kind="functionlike"><macro-parameter name=""/></macro>
<macro name="BOOST_LOG_STANDARD_STRING_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of string types. </purpose></macro>
<macro name="BOOST_LOG_DEFAULT_ATTRIBUTE_VALUE_TYPES" kind="functionlike"><macro-parameter name=""/><purpose>Boost.Preprocessor sequence of the default attribute value types supported by the library. </purpose></macro>
</header>
<header name="boost/log/utility/type_dispatch/static_type_dispatcher.hpp">
<para><para>Andrey Semashev </para>

<para>15.04.2007</para>

The header contains implementation of a compile-time type dispatcher. </para><namespace name="boost">
<namespace name="log">
<class name="static_type_dispatcher"><template>
      <template-type-parameter name="T"/>
    </template><purpose>A static type dispatcher class. </purpose><description><para>The type dispatcher can be used to pass objects of arbitrary types from one component to another. With regard to the library, the type dispatcher can be used to extract attribute values.</para><para>Static type dispatchers allow to specify one or several supported types at compile time. </para></description><method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="ReceiverT"/>
        </template><parameter name="receiver"><paramtype>ReceiverT &amp;</paramtype><description><para>Unary function object that will be called on a dispatched value. The receiver must be callable with an argument of any of the supported types of the dispatcher. </para></description></parameter><description><para>Constructor. Initializes the dispatcher internals.</para><para>The <emphasis>receiver</emphasis> object is not copied inside the dispatcher, but references to it may be kept by the dispatcher after construction. The receiver object must remain valid until the dispatcher is destroyed.</para><para>
</para></description></constructor>
<constructor cv="= delete"><parameter name=""><paramtype><classname>static_type_dispatcher</classname> const &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= delete"><type><classname>static_type_dispatcher</classname> &amp;</type><parameter name=""><paramtype><classname>static_type_dispatcher</classname> const &amp;</paramtype></parameter></copy-assignment>
</class>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/type_dispatch/type_dispatcher.hpp">
<para><para>Andrey Semashev </para>

<para>15.04.2007</para>

The header contains definition of generic type dispatcher interfaces. </para><namespace name="boost">
<namespace name="log">
<class name="type_dispatcher"><purpose>A type dispatcher interface. </purpose><description><para>All type dispatchers support this interface. It is used to acquire the visitor interface for the requested type. </para></description><class name="callback"><template>
      <template-type-parameter name="T"/>
    </template><description><para>This interface is used by type dispatchers to consume the dispatched value. </para></description><method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="value"><paramtype>T const &amp;</paramtype><description><para>The dispatched value </para></description></parameter><description><para>The operator invokes the visitor-specific logic with the given value</para><para>
</para></description></method>
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>bool</type><description><para>The operator checks if the visitor is attached to a receiver </para></description></method>
<method name="operator!" cv="const noexcept"><type>bool</type><description><para>The operator checks if the visitor is not attached to a receiver </para></description></method>
</method-group>
</class><method-group name="protected member functions">
<method name="BOOST_DEFAULTED_FUNCTION"><type/><parameter name=""><paramtype><classname>type_dispatcher</classname>(<classname>type_dispatcher</classname> const &amp;that)</paramtype></parameter><parameter name=""><paramtype>:m_get_callback_impl(that.m_get_callback_impl) {}</paramtype></parameter><description><para>The method requests a callback for the value of type <computeroutput>T</computeroutput> </para><para>
</para></description><returns><para>The type-specific callback or an empty value, if the type is not supported </para>
</returns></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="get_callback_impl"><paramtype>get_callback_impl_type</paramtype></parameter><description><para>Initializing constructor </para></description></constructor>
</class>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/unique_identifier_name.hpp">
<para><para>Andrey Semashev </para>

<para>30.04.2008</para>

The header contains <computeroutput>BOOST_LOG_UNIQUE_IDENTIFIER_NAME</computeroutput> macro definition. </para><macro name="BOOST_LOG_UNIQUE_IDENTIFIER_NAME" kind="functionlike"><macro-parameter name="prefix"/><description><para>Constructs a unique (in the current file scope) token that can be used as a variable name. The name will contain a prefix passed in the <emphasis>prefix</emphasis> argument. This allows to use the macro multiple times on a single line. </para></description></macro>
</header>
<header name="boost/log/utility/unused_variable.hpp">
<para><para>Andrey Semashev </para>

<para>10.05.2008</para>

The header contains definition of a macro to suppress compiler warnings about unused variables. </para><namespace name="boost">
<namespace name="log">



















































































































</namespace>
</namespace>
<macro name="BOOST_LOG_UNUSED_VARIABLE" kind="functionlike"><macro-parameter name="type"/><macro-parameter name="var"/><macro-parameter name="initializer"/><purpose>The macro suppresses compiler warnings for <computeroutput>var</computeroutput> being unused. </purpose></macro>
</header>
<header name="boost/log/utility/use_std_allocator.hpp">
<para><para>Andrey Semashev </para>

<para>04.03.2021</para>

The header defines <computeroutput>use_std_allocator</computeroutput> tag type. </para><namespace name="boost">
<namespace name="log">
<struct name="use_std_allocator"><purpose>Tag type that indicates that a specialization of <computeroutput>std::allocator</computeroutput> should be used for allocating memory. </purpose><description><para>This tag type can be used in template parameters in various components of Boost.Log. The type itself is not an allocator type. </para></description></struct>


















































































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/value_ref.hpp">
<para><para>Andrey Semashev </para>

<para>27.07.2012</para>

The header contains implementation of a value reference wrapper. </para><namespace name="boost">
<namespace name="log">
<function name="swap"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; &amp;</paramtype></parameter><purpose>Free swap function. </purpose></function>
<function name="operator&lt;&lt;"><type>std::basic_ostream&lt; CharT, TraitsT &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="strm"><paramtype>std::basic_ostream&lt; CharT, TraitsT &gt; &amp;</paramtype></parameter><parameter name="val"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter><purpose>Stream output operator. </purpose></function>
<function name="operator&lt;&lt;"><type><classname>basic_formatting_ostream</classname>&lt; CharT, TraitsT, AllocatorT &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
          <template-type-parameter name="AllocatorT"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="strm"><paramtype><classname>basic_formatting_ostream</classname>&lt; CharT, TraitsT, AllocatorT &gt; &amp;</paramtype></parameter><parameter name="val"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter><purpose>Log formatting operator. </purpose></function>
<function name="operator=="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></function>
<function name="operator=="><type>bool</type><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="left"><paramtype>U const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter></function>
<function name="operator=="><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="TagT1"/>
          <template-type-parameter name="T2"/>
          <template-type-parameter name="TagT2"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T1, TagT1 &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T2, TagT2 &gt; const &amp;</paramtype></parameter></function>
<function name="operator!="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></function>
<function name="operator!="><type>bool</type><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="left"><paramtype>U const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter></function>
<function name="operator!="><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="TagT1"/>
          <template-type-parameter name="T2"/>
          <template-type-parameter name="TagT2"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T1, TagT1 &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T2, TagT2 &gt; const &amp;</paramtype></parameter></function>
<function name="operator&lt;"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></function>
<function name="operator&lt;"><type>bool</type><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="left"><paramtype>U const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter></function>
<function name="operator&lt;"><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="TagT1"/>
          <template-type-parameter name="T2"/>
          <template-type-parameter name="TagT2"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T1, TagT1 &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T2, TagT2 &gt; const &amp;</paramtype></parameter></function>
<function name="operator&gt;"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></function>
<function name="operator&gt;"><type>bool</type><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="left"><paramtype>U const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter></function>
<function name="operator&gt;"><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="TagT1"/>
          <template-type-parameter name="T2"/>
          <template-type-parameter name="TagT2"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T1, TagT1 &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T2, TagT2 &gt; const &amp;</paramtype></parameter></function>
<function name="operator&lt;="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></function>
<function name="operator&lt;="><type>bool</type><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="left"><paramtype>U const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter></function>
<function name="operator&lt;="><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="TagT1"/>
          <template-type-parameter name="T2"/>
          <template-type-parameter name="TagT2"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T1, TagT1 &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T2, TagT2 &gt; const &amp;</paramtype></parameter></function>
<function name="operator&gt;="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
          <template-type-parameter name="U"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype>U const &amp;</paramtype></parameter></function>
<function name="operator&gt;="><type>bool</type><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="TagT"/>
        </template><parameter name="left"><paramtype>U const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T, TagT &gt; const &amp;</paramtype></parameter></function>
<function name="operator&gt;="><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="TagT1"/>
          <template-type-parameter name="T2"/>
          <template-type-parameter name="TagT2"/>
        </template><parameter name="left"><paramtype><classname>value_ref</classname>&lt; T1, TagT1 &gt; const &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>value_ref</classname>&lt; T2, TagT2 &gt; const &amp;</paramtype></parameter></function>






























































































</namespace>
</namespace>
</header>
<header name="boost/log/utility/value_ref_fwd.hpp">
<para><para>Andrey Semashev </para>

<para>27.07.2012</para>

The header contains forward declaration of a value reference wrapper. </para><namespace name="boost">
<namespace name="log">
<class name="value_ref"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="TagT"/>
    </template><purpose>Reference wrapper for a stored attribute value. </purpose><description><para>The <computeroutput><classname alt="boost::log::value_ref">value_ref</classname></computeroutput> class template provides access to the stored attribute value. It is not a traditional reference wrapper since it may be empty (i.e. refer to no value at all) and it can also refer to values of different types. Therefore its interface and behavior combines features of Boost.Ref, Boost.Optional and Boost.Variant, depending on the use case.</para><para>The template parameter <computeroutput>T</computeroutput> can be a single type or an MPL sequence of possible types being referred. The reference wrapper will act as either an optional reference or an optional variant of references to the specified types. In any case, the referred values will not be modifiable (i.e. <computeroutput><classname alt="boost::log::value_ref">value_ref</classname></computeroutput> always models a const reference).</para><para>Template parameter <computeroutput>TagT</computeroutput> is optional. It can be used for customizing the operations on this reference wrapper, such as putting the referred value to log. </para></description><method-group name="public member functions">
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>bool</type><description><para>The operator verifies if the wrapper refers to a value. </para></description></method>
<method name="operator!" cv="const noexcept"><type>bool</type><description><para>The operator verifies if the wrapper does not refer to a value. </para></description></method>
<method name="empty" cv="const noexcept"><type>bool</type><description><para>
</para></description><returns><para><computeroutput>true</computeroutput> if the wrapper does not refer to a value. </para>
</returns></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="that"><paramtype><classname>value_ref</classname> &amp;</paramtype></parameter><description><para>Swaps two reference wrappers </para></description></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><template>
          <template-type-parameter name="U"/>
        </template><parameter name="val"><paramtype>U const &amp;</paramtype></parameter><description><para>Default constructor. Creates a reference wrapper that does not refer to a value.</para><para>Initializing constructor. Creates a reference wrapper that refers to the specified value. </para></description></constructor>
</class>


















































































































</namespace>
</namespace>
</header>
</library-reference>