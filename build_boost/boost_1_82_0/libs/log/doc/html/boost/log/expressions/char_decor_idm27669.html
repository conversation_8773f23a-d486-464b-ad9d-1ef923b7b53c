<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function template char_decor</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Log v2">
<link rel="up" href="../../../expressions.html#header.boost.log.expressions.formatters.char_decorator_hpp" title="Header &lt;boost/log/expressions/formatters/char_decorator.hpp&gt;">
<link rel="prev" href="char_decor_idm27654.html" title="Function template char_decor">
<link rel="next" href="csv_decor.html" title="Global csv_decor">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="char_decor_idm27654.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../expressions.html#header.boost.log.expressions.formatters.char_decorator_hpp"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="csv_decor.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.log.expressions.char_decor_idm27669"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function template char_decor</span></h2>
<p>boost::log::expressions::char_decor</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../expressions.html#header.boost.log.expressions.formatters.char_decorator_hpp" title="Header &lt;boost/log/expressions/formatters/char_decorator.hpp&gt;">boost/log/expressions/formatters/char_decorator.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> FromRangeT<span class="special">,</span> <span class="keyword">typename</span> ToRangeT<span class="special">&gt;</span> 
  <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <span class="identifier">char_decor</span><span class="special">(</span><span class="identifier">FromRangeT</span> <span class="keyword">const</span> <span class="special">&amp;</span> from<span class="special">,</span> <span class="identifier">ToRangeT</span> <span class="keyword">const</span> <span class="special">&amp;</span> to<span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm43214"></a><h2>Description</h2>
<p>The function returns a decorator generator object. The generator provides <code class="computeroutput">operator[]</code> that can be used to construct the actual decorator.</p>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>The <span class="emphasis"><em>from</em></span> and <span class="emphasis"><em>to</em></span> sequences mush be of the same size. Every <code class="computeroutput">from[i]</code> substring occurrence in the output will be replaced with <code class="computeroutput">to[i]</code>. </p></td></tr>
</table></div>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">from</code></span></p></td>
<td><p>A sequence of strings that will be sought in the output. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">to</code></span></p></td>
<td><p>A sequence of strings that will be used as replacements.</p></td>
</tr>
</tbody>
</table></div></td>
</tr></tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2007-2022 Andrey Semashev<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>).
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="char_decor_idm27654.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../expressions.html#header.boost.log.expressions.formatters.char_decorator_hpp"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="csv_decor.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
