<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class reliable_message_queue</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Log v2">
<link rel="up" href="../../../utilities.html#header.boost.log.utility.ipc.reliable_message_queue_hpp" title="Header &lt;boost/log/utility/ipc/reliable_message_queue.hpp&gt;">
<link rel="prev" href="object_name.html" title="Class object_name">
<link rel="next" href="../add_value_manip.html" title="Class template add_value_manip">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="object_name.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../utilities.html#header.boost.log.utility.ipc.reliable_message_queue_hpp"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../add_value_manip.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.log.ipc.reliable_message_queue"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class reliable_message_queue</span></h2>
<p>boost::log::ipc::reliable_message_queue — A reliable interprocess message queue. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../utilities.html#header.boost.log.utility.ipc.reliable_message_queue_hpp" title="Header &lt;boost/log/utility/ipc/reliable_message_queue.hpp&gt;">boost/log/utility/ipc/reliable_message_queue.hpp</a>&gt;

</span>
<span class="keyword">class</span> <a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">uint32_t</span> <a name="boost.log.ipc.reliable_message_queue.size_type"></a><span class="identifier">size_type</span><span class="special">;</span>  <span class="comment">// Queue message size type. </span>

  <span class="comment">// Result codes for various operations on the queue. </span>
  <span class="keyword">enum</span> <a name="boost.log.ipc.reliable_message_queue.operation_result"></a>operation_result <span class="special">{</span> succeeded, no_space, aborted <span class="special">}</span><span class="special">;</span>

  <span class="comment">// Interprocess queue overflow policies. </span>
  <span class="keyword">enum</span> <a name="boost.log.ipc.reliable_message_queue.overflow_policy"></a>overflow_policy <span class="special">{</span> block_on_overflow, fail_on_overflow, 
                         throw_on_overflow <span class="special">}</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="reliable_message_queue.html#boost.log.ipc.reliable_message_queueconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="reliable_message_queue.html#idm36994-bb"><span class="identifier">reliable_message_queue</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="reliable_message_queue.html#idm37001-bb"><span class="identifier">reliable_message_queue</span></a><span class="special">(</span><a class="link" href="../open_mode/create_only_tag.html" title="Struct create_only_tag">open_mode::create_only_tag</a><span class="special">,</span> <a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                         <span class="identifier">uint32_t</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> 
                         <span class="identifier">overflow_policy</span> <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
                         <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="reliable_message_queue.html#idm37036-bb"><span class="identifier">reliable_message_queue</span></a><span class="special">(</span><a class="link" href="../open_mode/open_or_create_tag.html" title="Struct open_or_create_tag">open_mode::open_or_create_tag</a><span class="special">,</span> <a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                         <span class="identifier">uint32_t</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> 
                         <span class="identifier">overflow_policy</span> <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
                         <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="reliable_message_queue.html#idm37071-bb"><span class="identifier">reliable_message_queue</span></a><span class="special">(</span><a class="link" href="../open_mode/open_only_tag.html" title="Struct open_only_tag">open_mode::open_only_tag</a><span class="special">,</span> <a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                         <span class="identifier">overflow_policy</span> <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
                         <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="keyword">explicit</span> <a class="link" href="reliable_message_queue.html#idm37098-bb"><span class="identifier">reliable_message_queue</span></a><span class="special">(</span><span class="identifier">Args</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="reliable_message_queue.html#idm37133-bb"><span class="identifier">reliable_message_queue</span></a><span class="special">(</span><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;</span> <a class="link" href="reliable_message_queue.html#idm37145-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="reliable_message_queue.html#idm37129-bb"><span class="special">~</span><span class="identifier">reliable_message_queue</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="reliable_message_queue.html#idm36531-bb">public member functions</a></span>
  <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm36532-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm36543-bb"><span class="identifier">create</span></a><span class="special">(</span><a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">uint32_t</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> 
              <span class="identifier">overflow_policy</span> <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
              <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm36579-bb"><span class="identifier">open_or_create</span></a><span class="special">(</span><a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">uint32_t</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> 
                      <span class="identifier">overflow_policy</span> <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
                      <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm36615-bb"><span class="identifier">open</span></a><span class="special">(</span><a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">overflow_policy</span> <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
            <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="reliable_message_queue.html#idm36643-bb"><span class="identifier">is_open</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm36652-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span>  <span class="special">&amp;</span> <a class="link" href="reliable_message_queue.html#idm36665-bb"><span class="identifier">name</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">uint32_t</span> <a class="link" href="reliable_message_queue.html#idm36676-bb"><span class="identifier">capacity</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="reliable_message_queue.html#idm36687-bb"><span class="identifier">block_size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm36698-bb"><span class="identifier">stop_local</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm36723-bb"><span class="identifier">reset_local</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm36733-bb"><span class="identifier">close</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">operation_result</span> <a class="link" href="reliable_message_queue.html#idm36747-bb"><span class="identifier">send</span></a><span class="special">(</span><span class="keyword">void</span> <span class="keyword">const</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="reliable_message_queue.html#idm36782-bb"><span class="identifier">try_send</span></a><span class="special">(</span><span class="keyword">void</span> <span class="keyword">const</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">operation_result</span> <a class="link" href="reliable_message_queue.html#idm36817-bb"><span class="identifier">receive</span></a><span class="special">(</span><span class="keyword">void</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ElementT<span class="special">,</span> <span class="identifier">size_type</span> SizeV<span class="special">&gt;</span> 
    <span class="identifier">operation_result</span> <a class="link" href="reliable_message_queue.html#idm36847-bb"><span class="identifier">receive</span></a><span class="special">(</span><span class="identifier">ElementT</span><span class="special">(</span><span class="special">&amp;</span><span class="special">)</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ContainerT<span class="special">&gt;</span> <span class="identifier">operation_result</span> <a class="link" href="reliable_message_queue.html#idm36877-bb"><span class="identifier">receive</span></a><span class="special">(</span><span class="identifier">ContainerT</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="reliable_message_queue.html#idm36904-bb"><span class="identifier">try_receive</span></a><span class="special">(</span><span class="keyword">void</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ElementT<span class="special">,</span> <span class="identifier">size_type</span> SizeV<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="reliable_message_queue.html#idm36935-bb"><span class="identifier">try_receive</span></a><span class="special">(</span><span class="identifier">ElementT</span><span class="special">(</span><span class="special">&amp;</span><span class="special">)</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ContainerT<span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="reliable_message_queue.html#idm36966-bb"><span class="identifier">try_receive</span></a><span class="special">(</span><span class="identifier">ContainerT</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="reliable_message_queue.html#idm37162-bb">friend functions</a></span>
  <span class="keyword">friend</span> <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm37163-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="reliable_message_queue.html#idm37174-bb">public static functions</a></span>
  <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="reliable_message_queue.html#idm37175-bb"><span class="identifier">remove</span></a><span class="special">(</span><a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm81617"></a><h2>Description</h2>
<p>The queue implements a reliable one-way channel of passing messages from one or multiple writers to a single reader. The format of the messages is user-defined and must be consistent across all writers and the reader. The queue does not enforce any specific format of the messages, other than they should be supplied as a contiguous array of bytes.</p>
<p>The queue internally uses a process-shared storage identified by an <code class="computeroutput"><a class="link" href="object_name.html" title="Class object_name">object_name</a></code> (the queue name). Refer to <code class="computeroutput"><a class="link" href="object_name.html" title="Class object_name">object_name</a></code> documentation for details on restrictions imposed on object names.</p>
<p>The queue storage is organized as a fixed number of blocks of a fixed size. The block size must be an integer power of 2 and is expressed in bytes. Each written message, together with some metadata added by the queue, consumes an integer number of blocks. Each read message received by the reader releases the blocks allocated for that message. As such the maximum size of a message is slightly less than block size times capacity of the queue. For efficiency, it is recommended to choose block size large enough to accommodate most of the messages to be passed through the queue.</p>
<p>The queue is considered empty when no messages are enqueued (all blocks are free). The queue is considered full at the point of enqueueing a message when there is not enough free blocks to accommodate the message.</p>
<p>The queue is reliable in that it will not drop successfully sent messages that are not received by the reader, other than the case when a non-empty queue is destroyed by the last user. If a message cannot be enqueued by the writer because the queue is full, the queue can either block the writer or return an error or throw an exception, depending on the policy specified at the queue creation. The policy is object local, i.e. different writers and the reader can have different overflow policies.</p>
<p>If the queue is empty and the reader attempts to dequeue a message, it will block until a message is enqueued by a writer.</p>
<p>A blocked reader or writer can be unblocked by calling <code class="computeroutput">stop_local</code>. After this method is called, all threads blocked on this particular object are released and return <code class="computeroutput">operation_result::aborted</code>. The other instances of the queue (in the current or other processes) are unaffected. In order to restore the normal functioning of the queue instance after the <code class="computeroutput">stop_local</code> call the user has to invoke <code class="computeroutput">reset_local</code>.</p>
<p>The queue does not guarantee any particular order of received messages from different writer threads. Messages sent by a particular writer thread will be received in the order of sending.</p>
<p>Methods of this class are not thread-safe, unless otherwise specified. </p>
<div class="refsect2">
<a name="idm81636"></a><h3>
<a name="boost.log.ipc.reliable_message_queueconstruct-copy-destruct"></a><code class="computeroutput">reliable_message_queue</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm36994-bb"></a><span class="identifier">reliable_message_queue</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p>Default constructor. The method constructs an object that is not associated with any message queue.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">is_open() == false</code> </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm37001-bb"></a><span class="identifier">reliable_message_queue</span><span class="special">(</span><a class="link" href="../open_mode/create_only_tag.html" title="Struct create_only_tag">open_mode::create_only_tag</a><span class="special">,</span> <a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span> name<span class="special">,</span> 
                       <span class="identifier">uint32_t</span> capacity<span class="special">,</span> <span class="identifier">size_type</span> block_size<span class="special">,</span> 
                       <span class="identifier">overflow_policy</span> oflow_policy <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
                       <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> perms <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>Constructor. The method is used to construct an object and create the associated message queue. The constructed object will be in running state if the message queue is successfully created.</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">block_size</code></span></p></td>
<td><p>Size in bytes of allocation block. Must be a power of 2. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity</code></span></p></td>
<td><p>Maximum number of allocation blocks the queue can hold. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">name</code></span></p></td>
<td><p>Name of the message queue to be associated with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">oflow_policy</code></span></p></td>
<td><p>Queue behavior policy in case of overflow. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">perms</code></span></p></td>
<td><p>Access permissions for the associated message queue. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm37036-bb"></a><span class="identifier">reliable_message_queue</span><span class="special">(</span><a class="link" href="../open_mode/open_or_create_tag.html" title="Struct open_or_create_tag">open_mode::open_or_create_tag</a><span class="special">,</span> 
                       <a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span> name<span class="special">,</span> <span class="identifier">uint32_t</span> capacity<span class="special">,</span> 
                       <span class="identifier">size_type</span> block_size<span class="special">,</span> 
                       <span class="identifier">overflow_policy</span> oflow_policy <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
                       <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> perms <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>Constructor. The method is used to construct an object and create or open the associated message queue. The constructed object will be in running state if the message queue is successfully created or opened. If the message queue that is identified by the name already exists then the other queue parameters are ignored. The actual queue parameters can be obtained with accessors from the constructed object.</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">block_size</code></span></p></td>
<td><p>Size in bytes of allocation block. Must be a power of 2. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity</code></span></p></td>
<td><p>Maximum number of allocation blocks the queue can hold. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">name</code></span></p></td>
<td><p>Name of the message queue to be associated with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">oflow_policy</code></span></p></td>
<td><p>Queue behavior policy in case of overflow. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">perms</code></span></p></td>
<td><p>Access permissions for the associated message queue. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm37071-bb"></a><span class="identifier">reliable_message_queue</span><span class="special">(</span><a class="link" href="../open_mode/open_only_tag.html" title="Struct open_only_tag">open_mode::open_only_tag</a><span class="special">,</span> <a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span> name<span class="special">,</span> 
                       <span class="identifier">overflow_policy</span> oflow_policy <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
                       <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> perms <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>Constructor. The method is used to construct an object and open the existing message queue. The constructed object will be in running state if the message queue is successfully opened.</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">name</code></span></p></td>
<td><p>Name of the message queue to be associated with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">oflow_policy</code></span></p></td>
<td><p>Queue behavior policy in case of overflow. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">perms</code></span></p></td>
<td><p>Access permissions for the associated message queue. The permissions will only be used if the queue implementation has to create system objects while operating. This parameter is currently not used on POSIX systems. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> Args<span class="special">&gt;</span> 
  <span class="keyword">explicit</span> <a name="idm37098-bb"></a><span class="identifier">reliable_message_queue</span><span class="special">(</span><span class="identifier">Args</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p>Constructor with named parameters. The method is used to construct an object and create or open the associated message queue. The constructed object will be in running state if the message queue is successfully created.</p>
<p>The following named parameters are accepted:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p>open_mode - One of the open mode tags: <code class="computeroutput">open_mode::create_only</code>, <code class="computeroutput">open_mode::open_only</code> or <code class="computeroutput">open_mode::open_or_create</code>.</p></li>
<li class="listitem"><p>name - Name of the message queue to be associated with.</p></li>
<li class="listitem"><p>capacity - Maximum number of allocation blocks the queue can hold. Used only if the queue is created.</p></li>
<li class="listitem"><p>block_size - Size in bytes of allocation block. Must be a power of 2. Used only if the queue is created.</p></li>
<li class="listitem"><p>overflow_policy - Queue behavior policy in case of overflow, see <code class="computeroutput">overflow_policy</code>.</p></li>
<li class="listitem"><p>permissions - Access permissions for the associated message queue.</p></li>
</ul></div>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code> </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm37133-bb"></a><span class="identifier">reliable_message_queue</span><span class="special">(</span><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;&amp;</span> that<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p>Move constructor. The method move-constructs an object from <code class="computeroutput">other</code>. After the call, the constructed object becomes <code class="computeroutput">other</code>, while <code class="computeroutput">other</code> is left in default constructed state.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">that</code></span></p></td>
<td><p>The object to be moved. </p></td>
</tr></tbody>
</table></div></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;</span> <a name="idm37145-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;&amp;</span> that<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p>Move assignment operator. If the object is associated with a message queue, <code class="computeroutput">close()</code> is first called and the precondition to calling <code class="computeroutput">close()</code> applies. After the call, the object becomes <span class="emphasis"><em>that</em></span> while <span class="emphasis"><em>that</em></span> is left in default constructed state.</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">that</code></span></p></td>
<td><p>The object to be moved.</p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A reference to the assigned object. </p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm37129-bb"></a><span class="special">~</span><span class="identifier">reliable_message_queue</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Destructor. Calls <code class="computeroutput">close()</code>. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm81958"></a><h3>
<a name="idm36531-bb"></a><code class="computeroutput">reliable_message_queue</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36532-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;</span> that<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p>The method swaps the object with <span class="emphasis"><em>that</em></span>.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">that</code></span></p></td>
<td><p>The other object to swap with. </p></td>
</tr></tbody>
</table></div></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36543-bb"></a><span class="identifier">create</span><span class="special">(</span><a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span> name<span class="special">,</span> <span class="identifier">uint32_t</span> capacity<span class="special">,</span> <span class="identifier">size_type</span> block_size<span class="special">,</span> 
            <span class="identifier">overflow_policy</span> oflow_policy <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
            <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> perms <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>The method creates the message queue to be associated with the object. After the call, the object will be in running state if a message queue is successfully created.</p>
<p>


</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">block_size</code></span></p></td>
<td><p>Size in bytes of allocation block. Must be a power of 2. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity</code></span></p></td>
<td><p>Maximum number of allocation blocks the queue can hold. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">name</code></span></p></td>
<td><p>Name of the message queue to be associated with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">oflow_policy</code></span></p></td>
<td><p>Queue behavior policy in case of overflow. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">perms</code></span></p></td>
<td><p>Access permissions for the associated message queue. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == false</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36579-bb"></a><span class="identifier">open_or_create</span><span class="special">(</span><a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span> name<span class="special">,</span> <span class="identifier">uint32_t</span> capacity<span class="special">,</span> 
                    <span class="identifier">size_type</span> block_size<span class="special">,</span> 
                    <span class="identifier">overflow_policy</span> oflow_policy <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
                    <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> perms <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>The method creates or opens the message queue to be associated with the object. After the call, the object will be in running state if a message queue is successfully created or opened. If the message queue that is identified by the name already exists then the other queue parameters are ignored. The actual queue parameters can be obtained with accessors from this object after this method returns.</p>
<p>


</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">block_size</code></span></p></td>
<td><p>Size in bytes of allocation block. Must be a power of 2. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity</code></span></p></td>
<td><p>Maximum number of allocation blocks the queue can hold. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">name</code></span></p></td>
<td><p>Name of the message queue to be associated with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">oflow_policy</code></span></p></td>
<td><p>Queue behavior policy in case of overflow. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">perms</code></span></p></td>
<td><p>Access permissions for the associated message queue. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == false</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36615-bb"></a><span class="identifier">open</span><span class="special">(</span><a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span> name<span class="special">,</span> 
          <span class="identifier">overflow_policy</span> oflow_policy <span class="special">=</span> <span class="identifier">block_on_overflow</span><span class="special">,</span> 
          <a class="link" href="../permissions.html" title="Class permissions">permissions</a> <span class="keyword">const</span> <span class="special">&amp;</span> perms <span class="special">=</span> <a class="link" href="../permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>The method opens the existing message queue to be associated with the object. After the call, the object will be in running state if a message queue is successfully opened.</p>
<p>


</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">name</code></span></p></td>
<td><p>Name of the message queue to be associated with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">oflow_policy</code></span></p></td>
<td><p>Queue behavior policy in case of overflow. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">perms</code></span></p></td>
<td><p>Access permissions for the associated message queue. The permissions will only be used if the queue implementation has to create system objects while operating. This parameter is currently not used on POSIX systems. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == false</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm36643-bb"></a><span class="identifier">is_open</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p>Tests whether the object is associated with any message queue.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">true</code> if the object is associated with a message queue, and <code class="computeroutput">false</code> otherwise. </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36652-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>This method empties the associated message queue. Concurrent calls to this method, <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, and <code class="computeroutput">stop_local()</code> are allowed.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code> </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span>  <span class="special">&amp;</span> <a name="idm36665-bb"></a><span class="identifier">name</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>The method returns the name of the associated message queue.</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Name of the associated message queue </p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">uint32_t</span> <a name="idm36676-bb"></a><span class="identifier">capacity</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>The method returns the maximum number of allocation blocks the associated message queue can hold. Note that the returned value may be different from the corresponding value passed to the constructor or <code class="computeroutput">open_or_create()</code>, for the message queue may not have been created by this object.</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Maximum number of allocation blocks the associated message queue can hold. </p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm36687-bb"></a><span class="identifier">block_size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>The method returns the allocation block size, in bytes. Each message in the associated message queue consumes an integer number of allocation blocks. Note that the returned value may be different from the corresponding value passed to the constructor or <code class="computeroutput">open_or_create()</code>, for the message queue may not have been created by this object.</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Allocation block size, in bytes. </p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36698-bb"></a><span class="identifier">stop_local</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>The method wakes up all threads that are blocked in calls to <code class="computeroutput">send()</code> or <code class="computeroutput">receive()</code>. Those calls would then return <code class="computeroutput">operation_result::aborted</code>. Note that, the method does not block until the woken-up threads have actually returned from <code class="computeroutput">send()</code> or <code class="computeroutput">receive()</code>. Other means is needed to ensure that calls to <code class="computeroutput">send()</code> or <code class="computeroutput">receive()</code> have returned, e.g., joining the threads that might be blocking on the calls.</p>
<p>The method also puts the object in stopped state. When in stopped state, calls to <code class="computeroutput">send()</code> or <code class="computeroutput">receive()</code> will return immediately with return value <code class="computeroutput">operation_result::aborted</code> when they would otherwise block in running state.</p>
<p>Concurrent calls to this method, <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, and <code class="computeroutput">clear()</code> are allowed.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code> </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36723-bb"></a><span class="identifier">reset_local</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>The method puts the object in running state where calls to <code class="computeroutput">send()</code> or <code class="computeroutput">receive()</code> may block. This method is not thread-safe.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code> </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36733-bb"></a><span class="identifier">close</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p>The method disassociates the associated message queue, if any. No other threads should be using this object before calling this method. The <code class="computeroutput">stop_local()</code> method can be used to have any threads currently blocked in <code class="computeroutput">send()</code> or <code class="computeroutput">receive()</code> return, and prevent further calls to them from blocking. Typically, before calling this method, one would first call <code class="computeroutput">stop_local()</code> and then join all threads that might be blocking on <code class="computeroutput">send()</code> or <code class="computeroutput">receive()</code> to ensure that they have returned from the calls. The associated message queue is destroyed if the object represents the last outstanding reference to it.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">is_open() == false</code> </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">operation_result</span> <a name="idm36747-bb"></a><span class="identifier">send</span><span class="special">(</span><span class="keyword">void</span> <span class="keyword">const</span> <span class="special">*</span> message_data<span class="special">,</span> <span class="identifier">size_type</span> message_size<span class="special">)</span><span class="special">;</span></pre>
<p>The method sends a message to the associated message queue. When the object is in running state and the queue has no free space for the message, the method either blocks or throws an exception, depending on the overflow policy that was specified on the queue opening/creation. If blocking policy is in effect, the blocking can be interrupted by calling <code class="computeroutput">stop_local()</code>, in which case the method returns <code class="computeroutput">operation_result::aborted</code>. When the object is already in the stopped state, the method does not block but returns immediately with return value <code class="computeroutput">operation_result::aborted</code>.</p>
<p>It is possible to send an empty message by passing <code class="computeroutput">0</code> to the parameter <code class="computeroutput">message_size</code>.</p>
<p>Concurrent calls to <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, <code class="computeroutput">stop_local()</code>, and <code class="computeroutput">clear()</code> are allowed.</p>
<p>


<span class="bold"><strong>Throws:</strong></span> <code class="computeroutput">std::logic_error</code> in case if the message size exceeds the queue capacity, <code class="computeroutput">system_error</code> in case if a native OS method fails. </p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">message_data</code></span></p></td>
<td><p>The message data to send. Ignored when <code class="computeroutput">message_size</code> is <code class="computeroutput">0</code>. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">message_size</code></span></p></td>
<td><p>Size of the message data in bytes. If the size is larger than the associated message queue capacity, an <code class="computeroutput">std::logic_error</code> exception is thrown.</p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm36782-bb"></a><span class="identifier">try_send</span><span class="special">(</span><span class="keyword">void</span> <span class="keyword">const</span> <span class="special">*</span> message_data<span class="special">,</span> <span class="identifier">size_type</span> message_size<span class="special">)</span><span class="special">;</span></pre>
<p>The method performs an attempt to send a message to the associated message queue. The method is non-blocking, and always returns immediately. <code class="computeroutput">boost::system::system_error</code> is thrown for errors resulting from native operating system calls. Note that it is possible to send an empty message by passing <code class="computeroutput">0</code> to the parameter <code class="computeroutput">message_size</code>. Concurrent calls to <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, <code class="computeroutput">stop_local()</code>, and <code class="computeroutput">clear()</code> are allowed.</p>
<p>


<span class="bold"><strong>Throws:</strong></span> <code class="computeroutput">std::logic_error</code> in case if the message size exceeds the queue capacity, <code class="computeroutput">system_error</code> in case if a native OS method fails. </p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">message_data</code></span></p></td>
<td><p>The message data to send. Ignored when <code class="computeroutput">message_size</code> is <code class="computeroutput">0</code>. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">message_size</code></span></p></td>
<td><p>Size of the message data in bytes. If the size is larger than the maximum size allowed by the associated message queue, an <code class="computeroutput">std::logic_error</code> exception is thrown.</p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">true</code> if the message is successfully sent, and <code class="computeroutput">false</code> otherwise (e.g., when the queue is full).</p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">operation_result</span> 
<a name="idm36817-bb"></a><span class="identifier">receive</span><span class="special">(</span><span class="keyword">void</span> <span class="special">*</span> buffer<span class="special">,</span> <span class="identifier">size_type</span> buffer_size<span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&amp;</span> message_size<span class="special">)</span><span class="special">;</span></pre>
<p>The method takes a message from the associated message queue. When the object is in running state and the queue is empty, the method blocks. The blocking is interrupted when <code class="computeroutput">stop_local()</code> is called, in which case the method returns <code class="computeroutput">operation_result::aborted</code>. When the object is already in the stopped state and the queue is empty, the method does not block but returns immediately with return value <code class="computeroutput">operation_result::aborted</code>.</p>
<p>Concurrent calls to <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, <code class="computeroutput">stop_local()</code>, and <code class="computeroutput">clear()</code> are allowed.</p>
<p>


</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer</code></span></p></td>
<td><p>The memory buffer to store the received message in. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer_size</code></span></p></td>
<td><p>The size of the buffer, in bytes. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">message_size</code></span></p></td>
<td><p>Receives the size of the received message, in bytes.</p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ElementT<span class="special">,</span> <span class="identifier">size_type</span> SizeV<span class="special">&gt;</span> 
  <span class="identifier">operation_result</span> <a name="idm36847-bb"></a><span class="identifier">receive</span><span class="special">(</span><span class="identifier">ElementT</span><span class="special">(</span><span class="special">&amp;</span><span class="special">)</span> buffer<span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&amp;</span> message_size<span class="special">)</span><span class="special">;</span></pre>
<p>The method takes a message from the associated message queue. When the object is in running state and the queue is empty, the method blocks. The blocking is interrupted when <code class="computeroutput">stop_local()</code> is called, in which case the method returns <code class="computeroutput">operation_result::aborted</code>. When the object is already in the stopped state and the queue is empty, the method does not block but returns immediately with return value <code class="computeroutput">operation_result::aborted</code>.</p>
<p>Concurrent calls to <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, <code class="computeroutput">stop_local()</code>, and <code class="computeroutput">clear()</code> are allowed.</p>
<p>


</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer</code></span></p></td>
<td><p>The memory buffer to store the received message in. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">message_size</code></span></p></td>
<td><p>Receives the size of the received message, in bytes.</p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ContainerT<span class="special">&gt;</span> <span class="identifier">operation_result</span> <a name="idm36877-bb"></a><span class="identifier">receive</span><span class="special">(</span><span class="identifier">ContainerT</span> <span class="special">&amp;</span> container<span class="special">)</span><span class="special">;</span></pre>
<p>The method takes a message from the associated message queue. When the object is in running state and the queue is empty, the method blocks. The blocking is interrupted when <code class="computeroutput">stop_local()</code> is called, in which case the method returns <code class="computeroutput">operation_result::aborted</code>. When the object is already in the stopped state and the queue is empty, the method does not block but returns immediately with return value <code class="computeroutput">operation_result::aborted</code>.</p>
<p>Concurrent calls to <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, <code class="computeroutput">stop_local()</code>, and <code class="computeroutput">clear()</code> are allowed.</p>
<p>


</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">container</code></span></p></td>
<td><p>The container to store the received message in. The container should have value type of <code class="computeroutput">char</code>, <code class="computeroutput">signed char</code> or <code class="computeroutput">unsigned char</code> and support inserting elements at the end.</p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm36904-bb"></a><span class="identifier">try_receive</span><span class="special">(</span><span class="keyword">void</span> <span class="special">*</span> buffer<span class="special">,</span> <span class="identifier">size_type</span> buffer_size<span class="special">,</span> 
                 <span class="identifier">size_type</span> <span class="special">&amp;</span> message_size<span class="special">)</span><span class="special">;</span></pre>
<p>The method performs an attempt to take a message from the associated message queue. The method is non-blocking, and always returns immediately.</p>
<p>Concurrent calls to <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, <code class="computeroutput">stop_local()</code>, and <code class="computeroutput">clear()</code> are allowed.</p>
<p>


</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer</code></span></p></td>
<td><p>The memory buffer to store the received message in. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer_size</code></span></p></td>
<td><p>The size of the buffer, in bytes. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">message_size</code></span></p></td>
<td><p>Receives the size of the received message, in bytes.</p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">true</code> if a message is successfully received, and <code class="computeroutput">false</code> otherwise (e.g., when the queue is empty). </p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ElementT<span class="special">,</span> <span class="identifier">size_type</span> SizeV<span class="special">&gt;</span> 
  <span class="keyword">bool</span> <a name="idm36935-bb"></a><span class="identifier">try_receive</span><span class="special">(</span><span class="identifier">ElementT</span><span class="special">(</span><span class="special">&amp;</span><span class="special">)</span> buffer<span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&amp;</span> message_size<span class="special">)</span><span class="special">;</span></pre>
<p>The method performs an attempt to take a message from the associated message queue. The method is non-blocking, and always returns immediately.</p>
<p>Concurrent calls to <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, <code class="computeroutput">stop_local()</code>, and <code class="computeroutput">clear()</code> are allowed.</p>
<p>


</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer</code></span></p></td>
<td><p>The memory buffer to store the received message in. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">message_size</code></span></p></td>
<td><p>Receives the size of the received message, in bytes.</p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">true</code> if a message is successfully received, and <code class="computeroutput">false</code> otherwise (e.g., when the queue is empty). </p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ContainerT<span class="special">&gt;</span> <span class="keyword">bool</span> <a name="idm36966-bb"></a><span class="identifier">try_receive</span><span class="special">(</span><span class="identifier">ContainerT</span> <span class="special">&amp;</span> container<span class="special">)</span><span class="special">;</span></pre>
<p>The method performs an attempt to take a message from the associated message queue. The method is non-blocking, and always returns immediately.</p>
<p>Concurrent calls to <code class="computeroutput">send()</code>, <code class="computeroutput">try_send()</code>, <code class="computeroutput">receive()</code>, <code class="computeroutput">try_receive()</code>, <code class="computeroutput">stop_local()</code>, and <code class="computeroutput">clear()</code> are allowed.</p>
<p>


</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">container</code></span></p></td>
<td><p>The container to store the received message in. The container should have value type of <code class="computeroutput">char</code>, <code class="computeroutput">signed char</code> or <code class="computeroutput">unsigned char</code> and support inserting elements at the end.</p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">is_open() == true</code></p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">true</code> if a message is successfully received, and <code class="computeroutput">false</code> otherwise (e.g., when the queue is empty). </p></td>
</tr>
</tbody>
</table></div>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm82798"></a><h3>
<a name="idm37162-bb"></a><code class="computeroutput">reliable_message_queue</code> friend functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">void</span> <a name="idm37163-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;</span> a<span class="special">,</span> <a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a> <span class="special">&amp;</span> b<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Swaps the two <code class="computeroutput"><code class="computeroutput"><a class="link" href="reliable_message_queue.html" title="Class reliable_message_queue">reliable_message_queue</a></code></code> objects. </li></ol></div>
</div>
<div class="refsect2">
<a name="idm82822"></a><h3>
<a name="idm37174-bb"></a><code class="computeroutput">reliable_message_queue</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">void</span> <a name="idm37175-bb"></a><span class="identifier">remove</span><span class="special">(</span><a class="link" href="object_name.html" title="Class object_name">object_name</a> <span class="keyword">const</span> <span class="special">&amp;</span> name<span class="special">)</span><span class="special">;</span></pre>
<p>The method frees system-wide resources, associated with the interprocess queue with the supplied name. The queue referred to by the specified name must not be opened in any process at the point of this call. After this call succeeds a new queue with the specified name can be created.</p>
<p>This call can be useful to recover from an earlier process misbehavior (e.g. a crash without properly closing the message queue). In this case resources allocated for the interprocess queue may remain allocated after the last process closed the queue, which in turn may prevent creating a new queue with the same name. By calling this method before creating a queue the application can attempt to ensure it starts with a clean slate.</p>
<p>On some platforms resources associated with the queue are automatically reclaimed by the operating system when the last process using those resources terminates (even if it terminates abnormally). On these platforms this call may be a no-op. However, portable code should still call this method at appropriate places to ensure compatibility with other platforms and future library versions, which may change implementation of the queue.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">name</code></span></p></td>
<td><p>Name of the message queue to be removed. </p></td>
</tr></tbody>
</table></div></td>
</tr></tbody>
</table></div>
</li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2007-2022 Andrey Semashev<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>).
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="object_name.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../utilities.html#header.boost.log.utility.ipc.reliable_message_queue_hpp"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../add_value_manip.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
