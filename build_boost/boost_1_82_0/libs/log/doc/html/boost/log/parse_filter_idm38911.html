<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function template parse_filter</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Log v2">
<link rel="up" href="../../utilities.html#header.boost.log.utility.setup.filter_parser_hpp" title="Header &lt;boost/log/utility/setup/filter_parser.hpp&gt;">
<link rel="prev" href="register_simple_f_idm38893.html" title="Function template register_simple_filter_factory">
<link rel="next" href="parse_filter_idm38934.html" title="Function template parse_filter">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="register_simple_f_idm38893.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../utilities.html#header.boost.log.utility.setup.filter_parser_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="parse_filter_idm38934.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.log.parse_filter_idm38911"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function template parse_filter</span></h2>
<p>boost::log::parse_filter</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../utilities.html#header.boost.log.utility.setup.filter_parser_hpp" title="Header &lt;boost/log/utility/setup/filter_parser.hpp&gt;">boost/log/utility/setup/filter_parser.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">&gt;</span> 
  <span class="identifier">filter</span> <span class="identifier">parse_filter</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> begin<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> end<span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm90904"></a><h2>Description</h2>
<p>The function parses a filter from the sequence of characters</p>
<p>


<span class="bold"><strong>Throws:</strong></span> An <code class="computeroutput">std::exception</code>-based exception, if a filter cannot be recognized in the character sequence. </p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">begin</code></span></p></td>
<td><p>Pointer to the first character of the sequence </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">end</code></span></p></td>
<td><p>Pointer to the after-the-last character of the sequence </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">begin &lt;= end</code>, both pointers must not be <code class="computeroutput">NULL</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A function object that can be used as a filter.</p></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2007-2022 Andrey Semashev<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>).
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="register_simple_f_idm38893.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../utilities.html#header.boost.log.utility.setup.filter_parser_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="parse_filter_idm38934.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
