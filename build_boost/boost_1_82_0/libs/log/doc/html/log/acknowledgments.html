<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Acknowledgments</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.Log v2">
<link rel="up" href="../index.html" title="Chapter 1. Boost.Log v2">
<link rel="prev" href="todo.html" title="TODO in future releases">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="todo.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="log.acknowledgments"></a><a class="link" href="acknowledgments.html" title="Acknowledgments">Acknowledgments</a>
</h2></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          Vladimir Prus managed the library review in Boost and actually reviewed
          it in the process.
        </li>
<li class="listitem">
          Adam Wulkiewicz created the logo used on the <a href="https://github.com/boostorg/log" target="_top">GitHub
          project page</a>. The logo was taken from his <a href="https://github.com/awulkiew/boost-logos" target="_top">collection</a>
          of Boost logos.
        </li>
<li class="listitem">
          Luca Rigini wrote the initial implementation of the NT event log sink and
          made a lot of suggestions on how to improve the library with regard to
          writing user-defined sinks.
        </li>
<li class="listitem">
          Jean-Daniel Michaud, Michael Lacher and all others who took part in the
          discussion of the requirements to the library on <a href="http://www.crystalclearsoftware.com/cgi-bin/boost_wiki/wiki.pl?Boost.Logging" target="_top">Wiki</a>.
        </li>
<li class="listitem">
          John Torjo, Gennadiy Rozental and others for their discussion on John's
          logging library on the Boost developers list. It helped a lot to learn
          the requirements and possible solutions for the library.
        </li>
<li class="listitem">
          All authors of the great Boost libraries that were involved in this library
          (notably, <a href="http://www.boost.org/doc/libs/release/libs/smart_ptr/smart_ptr.htm" target="_top">Boost.SmartPtr</a>,
          <a href="http://www.boost.org/doc/libs/release/doc/html/thread.html" target="_top">Boost.Thread</a>,
          <a href="http://www.boost.org/doc/libs/release/doc/html/date_time.html" target="_top">Boost.DateTime</a>,
          <a href="http://www.boost.org/doc/libs/release/libs/filesystem/doc/index.htm" target="_top">Boost.Filesystem</a>,
          <a href="http://www.boost.org/doc/libs/release/doc/html/intrusive.html" target="_top">Boost.Intrusive</a>,
          <a href="http://www.boost.org/doc/libs/release/libs/spirit/doc/html/index.html" target="_top">Boost.Spirit2</a>
          and others) and <a href="http://www.boost.org/doc/libs/release/doc/html/quickbook.html" target="_top">Boost.Quickbook</a>
          authors for a simple yet powerful documenting tool.
        </li>
<li class="listitem">
          All the reviewers and the users who made suggestions and offered their
          feedback on the library. Most notably, Steven Watanabe for his in-depth
          studying the docs and the code, with a lot of fruitful comments on both.
        </li>
</ul></div>
</div>
<div class="copyright-footer">Copyright © 2007-2022 Andrey Semashev<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>).
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="todo.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
</body>
</html>
