<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Macro BOOST_LOG_FUNC</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="index.html" title="Chapter 1. Boost.Log v2">
<link rel="up" href="attributes.html#header.boost.log.attributes.named_scope_hpp" title="Header &lt;boost/log/attributes/named_scope.hpp&gt;">
<link rel="prev" href="BOOST_LOG_FUNCTION.html" title="Macro BOOST_LOG_FUNCTION">
<link rel="next" href="boost/log/add_scoped_logger_idm25901.html" title="Function template add_scoped_logger_attribute">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="BOOST_LOG_FUNCTION.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="attributes.html#header.boost.log.attributes.named_scope_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost/log/add_scoped_logger_idm25901.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="BOOST_LOG_FUNC"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Macro BOOST_LOG_FUNC</span></h2>
<p>BOOST_LOG_FUNC</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="attributes.html#header.boost.log.attributes.named_scope_hpp" title="Header &lt;boost/log/attributes/named_scope.hpp&gt;">boost/log/attributes/named_scope.hpp</a>&gt;

</span>BOOST_LOG_FUNC()</pre></div>
<div class="refsect1">
<a name="idm35906"></a><h2>Description</h2>
<p>Macro for function scope markup. The scope name is constructed with help of compiler and contains the current function name. It may be shorter than what <code class="computeroutput">BOOST_LOG_FUNCTION</code> macro produces. The scope name is pushed to the end of the current thread scope list.</p>
<p>Not all compilers have support for this macro. The exact form of the scope name may vary from one compiler to another. </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2007-2022 Andrey Semashev<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>).
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="BOOST_LOG_FUNCTION.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="attributes.html#header.boost.log.attributes.named_scope_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost/log/add_scoped_logger_idm25901.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
