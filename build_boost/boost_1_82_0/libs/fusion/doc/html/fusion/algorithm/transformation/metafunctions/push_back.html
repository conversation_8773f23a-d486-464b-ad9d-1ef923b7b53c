<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>push_back</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Fusion 2.2">
<link rel="up" href="../metafunctions.html" title="Metafunctions">
<link rel="prev" href="pop_front.html" title="pop_front">
<link rel="next" href="push_front.html" title="push_front">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="pop_front.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../metafunctions.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="push_front.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="fusion.algorithm.transformation.metafunctions.push_back"></a><a class="link" href="push_back.html" title="push_back">push_back</a>
</h5></div></div></div>
<h6>
<a name="fusion.algorithm.transformation.metafunctions.push_back.h0"></a>
            <span class="phrase"><a name="fusion.algorithm.transformation.metafunctions.push_back.description"></a></span><a class="link" href="push_back.html#fusion.algorithm.transformation.metafunctions.push_back.description">Description</a>
          </h6>
<p>
            Returns the result type of <a class="link" href="../functions/push_back.html" title="push_back"><code class="computeroutput"><span class="identifier">push_back</span></code></a>, given the types of
            the input sequence and element to push.
          </p>
<h6>
<a name="fusion.algorithm.transformation.metafunctions.push_back.h1"></a>
            <span class="phrase"><a name="fusion.algorithm.transformation.metafunctions.push_back.synopsis"></a></span><a class="link" href="push_back.html#fusion.algorithm.transformation.metafunctions.push_back.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">typename</span> <span class="identifier">Sequence</span><span class="special">,</span>
    <span class="keyword">typename</span> <span class="identifier">T</span>
    <span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">push_back</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>unspecified</em></span> <span class="identifier">type</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<div class="table">
<a name="fusion.algorithm.transformation.metafunctions.push_back.t0"></a><p class="title"><b>Table 1.103. Parameters</b></p>
<div class="table-contents"><table class="table" summary="Parameters">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Parameter
                    </p>
                  </th>
<th>
                    <p>
                      Requirement
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
</tr></thead>
<tbody>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">Sequence</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      A model of <a class="link" href="../../../sequence/concepts/forward_sequence.html" title="Forward Sequence">Forward
                      Sequence</a>
                    </p>
                  </td>
<td>
                    <p>
                      Operation's argument
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">T</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Any type
                    </p>
                  </td>
<td>
                    <p>
                      Operation's argument
                    </p>
                  </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><h6>
<a name="fusion.algorithm.transformation.metafunctions.push_back.h2"></a>
            <span class="phrase"><a name="fusion.algorithm.transformation.metafunctions.push_back.expression_semantics"></a></span><a class="link" href="push_back.html#fusion.algorithm.transformation.metafunctions.push_back.expression_semantics">Expression
            Semantics</a>
          </h6>
<pre class="programlisting"><a class="link" href="push_back.html" title="push_back"><code class="computeroutput"><span class="identifier">result_of</span><span class="special">::</span><span class="identifier">push_back</span></code></a><span class="special">&lt;</span><span class="identifier">Sequence</span><span class="special">,</span> <span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">type</span>
</pre>
<p>
            <span class="bold"><strong>Return type</strong></span>:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
                A model of <a class="link" href="../../../sequence/concepts/forward_sequence.html" title="Forward Sequence">Forward
                Sequence</a>.
              </li></ul></div>
<p>
            <span class="bold"><strong>Semantics</strong></span>: Returns a sequence with the
            elements of <code class="computeroutput"><span class="identifier">Sequence</span></code>
            and an element of type <code class="computeroutput"><span class="identifier">T</span></code>
            added to the end.
          </p>
<h6>
<a name="fusion.algorithm.transformation.metafunctions.push_back.h3"></a>
            <span class="phrase"><a name="fusion.algorithm.transformation.metafunctions.push_back.complexity"></a></span><a class="link" href="push_back.html#fusion.algorithm.transformation.metafunctions.push_back.complexity">Complexity</a>
          </h6>
<p>
            Constant.
          </p>
<h6>
<a name="fusion.algorithm.transformation.metafunctions.push_back.h4"></a>
            <span class="phrase"><a name="fusion.algorithm.transformation.metafunctions.push_back.header"></a></span><a class="link" href="push_back.html#fusion.algorithm.transformation.metafunctions.push_back.header">Header</a>
          </h6>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">fusion</span><span class="special">/</span><span class="identifier">algorithm</span><span class="special">/</span><span class="identifier">transformation</span><span class="special">/</span><span class="identifier">push_back</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">fusion</span><span class="special">/</span><span class="identifier">include</span><span class="special">/</span><span class="identifier">push_back</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2001-2006, 2011, 2012 Joel de Guzman,
      Dan Marsden, Tobias Schwinger<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="pop_front.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../metafunctions.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="push_front.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
