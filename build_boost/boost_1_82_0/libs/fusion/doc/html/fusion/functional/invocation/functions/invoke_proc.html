<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>invoke_procedure</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Fusion 2.2">
<link rel="up" href="../functions.html" title="Functions">
<link rel="prev" href="invoke.html" title="invoke">
<link rel="next" href="invoke_fobj.html" title="invoke_function_object">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="invoke.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../functions.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="invoke_fobj.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="fusion.functional.invocation.functions.invoke_proc"></a><a class="link" href="invoke_proc.html" title="invoke_procedure">invoke_procedure</a>
</h5></div></div></div>
<h6>
<a name="fusion.functional.invocation.functions.invoke_proc.h0"></a>
            <span class="phrase"><a name="fusion.functional.invocation.functions.invoke_proc.description"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.functions.invoke_proc.description">Description</a>
          </h6>
<p>
            Calls a <a class="link" href="../../concepts/callable.html" title="Callable Object">Callable
            Object</a> with the arguments from a <a class="link" href="../../../sequence.html" title="Sequence">Sequence</a>.
            The result of the call is ignored.
          </p>
<p>
            The first template parameter can be specialized explicitly to avoid copying
            and/or to control the const qualification of a function object.
          </p>
<p>
            For pointers to class members corresponding object can be specified as
            a reference, pointer, or smart pointer. In case of the latter, a freestanding
            <code class="literal">get_pointer</code> function must be defined (Boost provides
            this function for <code class="literal">std::auto_ptr</code> and <a href="http://www.boost.org/libs/smart_ptr#shared_ptr" target="_top"><code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">shared_ptr</span></code></a>).
          </p>
<p>
            The target function must not be a pointer to a member object (dereferencing
            such a pointer without returning anything does not make sense, so it
            isn't implemented).
          </p>
<h6>
<a name="fusion.functional.invocation.functions.invoke_proc.h1"></a>
            <span class="phrase"><a name="fusion.functional.invocation.functions.invoke_proc.synopsis"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.functions.invoke_proc.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">typename</span> <span class="identifier">Function</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">Sequence</span>
    <span class="special">&gt;</span>
<span class="keyword">typename</span> <a class="link" href="../metafunctions/invoke_proc.html" title="invoke_procedure"><code class="computeroutput"><span class="identifier">result_of</span><span class="special">::</span><span class="identifier">invoke_procedure</span></code></a><span class="special">&lt;</span><span class="identifier">Function</span><span class="special">,</span> <span class="identifier">Sequence</span><span class="special">&gt;::</span><span class="identifier">type</span>
<span class="identifier">invoke_procedure</span><span class="special">(</span><span class="identifier">Function</span> <span class="identifier">f</span><span class="special">,</span> <span class="identifier">Sequence</span> <span class="special">&amp;</span> <span class="identifier">s</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">typename</span> <span class="identifier">Function</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">Sequence</span>
    <span class="special">&gt;</span>
<span class="keyword">typename</span> <a class="link" href="../metafunctions/invoke_proc.html" title="invoke_procedure"><code class="computeroutput"><span class="identifier">result_of</span><span class="special">::</span><span class="identifier">invoke_procedure</span></code></a><span class="special">&lt;</span><span class="identifier">Function</span><span class="special">,</span> <span class="identifier">Sequence</span> <span class="keyword">const</span><span class="special">&gt;::</span><span class="identifier">type</span>
<span class="identifier">invoke_procedure</span><span class="special">(</span><span class="identifier">Function</span> <span class="identifier">f</span><span class="special">,</span> <span class="identifier">Sequence</span> <span class="keyword">const</span> <span class="special">&amp;</span> <span class="identifier">s</span><span class="special">);</span>
</pre>
<h6>
<a name="fusion.functional.invocation.functions.invoke_proc.h2"></a>
            <span class="phrase"><a name="fusion.functional.invocation.functions.invoke_proc.parameters"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.functions.invoke_proc.parameters">Parameters</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Parameter
                    </p>
                  </th>
<th>
                    <p>
                      Requirement
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
</tr></thead>
<tbody>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">f</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Model of <a class="link" href="../../concepts/callable.html" title="Callable Object">Callable
                      Object</a>
                    </p>
                  </td>
<td>
                    <p>
                      The function to call.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">s</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Model of <a class="link" href="../../../sequence/concepts/forward_sequence.html" title="Forward Sequence">Forward
                      Sequence</a>
                    </p>
                  </td>
<td>
                    <p>
                      The arguments.
                    </p>
                  </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="fusion.functional.invocation.functions.invoke_proc.h3"></a>
            <span class="phrase"><a name="fusion.functional.invocation.functions.invoke_proc.expression_semantics"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.functions.invoke_proc.expression_semantics">Expression
            Semantics</a>
          </h6>
<pre class="programlisting"><span class="identifier">invoke_procedure</span><span class="special">(</span><span class="identifier">f</span><span class="special">,</span><span class="identifier">s</span><span class="special">);</span>
</pre>
<p>
            <span class="bold"><strong>Return type</strong></span>: <code class="computeroutput"><span class="keyword">void</span></code>
          </p>
<p>
            <span class="bold"><strong>Semantics</strong></span>: Invokes <code class="computeroutput"><span class="identifier">f</span></code>
            with the elements in <code class="computeroutput"><span class="identifier">s</span></code>
            as arguments.
          </p>
<h6>
<a name="fusion.functional.invocation.functions.invoke_proc.h4"></a>
            <span class="phrase"><a name="fusion.functional.invocation.functions.invoke_proc.header"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.functions.invoke_proc.header">Header</a>
          </h6>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">fusion</span><span class="special">/</span><span class="identifier">functional</span><span class="special">/</span><span class="identifier">invocation</span><span class="special">/</span><span class="identifier">invoke_procedure</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<h6>
<a name="fusion.functional.invocation.functions.invoke_proc.h5"></a>
            <span class="phrase"><a name="fusion.functional.invocation.functions.invoke_proc.example"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.functions.invoke_proc.example">Example</a>
          </h6>
<pre class="programlisting"><a class="link" href="../../../container/vector.html" title="vector"><code class="computeroutput"><span class="identifier">vector</span></code></a><span class="special">&lt;</span><span class="keyword">int</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="identifier">v</span><span class="special">(</span><span class="number">1</span><span class="special">,</span><span class="number">2</span><span class="special">);</span>
<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">lambda</span><span class="special">;</span>
<span class="identifier">invoke_procedure</span><span class="special">(</span><span class="identifier">_1</span> <span class="special">+=</span> <span class="identifier">_2</span><span class="special">,</span> <span class="identifier">v</span><span class="special">);</span>
<span class="identifier">assert</span><span class="special">(</span><a class="link" href="../../../sequence/intrinsic/functions/front.html" title="front"><code class="computeroutput"><span class="identifier">front</span></code></a><span class="special">(</span><span class="identifier">v</span><span class="special">)</span> <span class="special">==</span> <span class="number">3</span><span class="special">);</span>
</pre>
<h6>
<a name="fusion.functional.invocation.functions.invoke_proc.h6"></a>
            <span class="phrase"><a name="fusion.functional.invocation.functions.invoke_proc.see_also"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.functions.invoke_proc.see_also">See
            also</a>
          </h6>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <a class="link" href="invoke.html" title="invoke"><code class="computeroutput"><span class="identifier">invoke</span></code></a>
              </li>
<li class="listitem">
                <a class="link" href="invoke_fobj.html" title="invoke_function_object"><code class="computeroutput"><span class="identifier">invoke_function_object</span></code></a>
              </li>
<li class="listitem">
                <a class="link" href="../metafunctions/invoke_proc.html" title="invoke_procedure"><code class="computeroutput"><span class="identifier">result_of</span><span class="special">::</span><span class="identifier">invoke_procedure</span></code></a>
              </li>
<li class="listitem">
                <a class="link" href="../../adapters/fused_procedure.html" title="fused_procedure"><code class="computeroutput"><span class="identifier">fused_procedure</span></code></a>
              </li>
<li class="listitem">
                <a class="link" href="../../generation/functions/mk_fused_proc.html" title="make_fused_procedure"><code class="computeroutput"><span class="identifier">make_fused_procedure</span></code></a>
              </li>
</ul></div>
</div>
<div class="copyright-footer">Copyright © 2001-2006, 2011, 2012 Joel de Guzman,
      Dan Marsden, Tobias Schwinger<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="invoke.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../functions.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="invoke_fobj.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
