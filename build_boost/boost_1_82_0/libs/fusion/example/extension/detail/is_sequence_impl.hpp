/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>
    Copyright (c) 2005-2006 Dan <PERSON>

    Di<PERSON>ributed under the Boost Software License, Version 1.0. (See accompanying 
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_FUSION_IS_SEQUENCE_IMPL_20060228_1946)
#define BOOST_FUSION_IS_SEQUENCE_IMPL_20060228_1946

#include <boost/mpl/bool.hpp>

namespace example
{
    struct example_sequence_tag;
}

namespace boost { namespace fusion
{
    namespace extension
    {
        template<typename Tag>
        struct is_sequence_impl;

        template<>
        struct is_sequence_impl<example::example_sequence_tag>
        {
            template<typename T>
            struct apply : mpl::true_ {};
        };
    }
}}

#endif
