<?xml version="1.0" standalone="yes"?>
<library-reference><header name="boost/algorithm/string.hpp">
<para>Cumulative include for string_algo library </para></header>
<header name="boost/algorithm/string/case_conv.hpp">
<para>Defines sequence case-conversion algorithms. Algorithms convert each element in the input sequence to the desired case using provided locales. </para><namespace name="boost">
<namespace name="algorithm">

















<overloaded-function name="to_lower_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input range </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for conversion </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Convert to lower case. </purpose><description><para>Each element of the input sequence is converted to lower case. The result is a copy of the input converted to lower case. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a copy of the input</para>
</returns></overloaded-function>

<function name="to_lower"><type>void</type><template>
          <template-type-parameter name="WritableRangeT"/>
        </template><parameter name="Input"><paramtype>WritableRangeT &amp;</paramtype><description><para>A range </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>a locale used for conversion </para></description></parameter><purpose>Convert to lower case. </purpose><description><para>Each element of the input sequence is converted to lower case. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="to_upper_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input range </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for conversion </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Convert to upper case. </purpose><description><para>Each element of the input sequence is converted to upper case. The result is a copy of the input converted to upper case. It is returned as a sequence or copied to the output iterator</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a copy of the input</para>
</returns></overloaded-function>

<function name="to_upper"><type>void</type><template>
          <template-type-parameter name="WritableRangeT"/>
        </template><parameter name="Input"><paramtype>WritableRangeT &amp;</paramtype><description><para>An input range </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>a locale used for conversion </para></description></parameter><purpose>Convert to upper case. </purpose><description><para>Each element of the input sequence is converted to upper case. The input sequence is modified in-place.</para><para>
</para></description></function>



































































































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/classification.hpp">
<para>Classification predicates are included in the library to give some more convenience when using algorithms like <computeroutput>trim()</computeroutput> and <computeroutput>all()</computeroutput>. They wrap functionality of STL classification functions ( e.g. <computeroutput>std::isspace()</computeroutput> ) into generic functors. </para><namespace name="boost">
<namespace name="algorithm">
<function name="is_classified"><type><emphasis>unspecified</emphasis></type><parameter name="Type"><paramtype>std::ctype_base::mask</paramtype><description><para>A <computeroutput>std::ctype</computeroutput> category </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_classified predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate. This predicate holds if the input is of specified <computeroutput>std::ctype</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_space"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_space predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::space</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_alnum"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_alnum predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::alnum</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_alpha"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_alpha predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::alpha</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_cntrl"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_cntrl predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::cntrl</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_digit"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_digit predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::digit</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_graph"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_graph predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::graph</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_lower"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_lower predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::lower</computeroutput> category.</para><para>

</para></description><returns><para>An instance of <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_print"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_print predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::print</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_punct"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_punct predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::punct</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_upper"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_upper predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::upper</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_xdigit"><type><emphasis>unspecified</emphasis></type><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for classification </para></description></parameter><purpose>is_xdigit predicate </purpose><description><para>Construct the <computeroutput>is_classified</computeroutput> predicate for the <computeroutput>ctype_base::xdigit</computeroutput> category.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_classified</computeroutput> predicate </para>
</returns></function>
<function name="is_any_of"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Set"><paramtype>const RangeT &amp;</paramtype><description><para>A set of characters to be recognized </para></description></parameter><purpose>is_any_of predicate </purpose><description><para>Construct the <computeroutput>is_any_of</computeroutput> predicate. The predicate holds if the input is included in the specified set of characters.</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_any_of</computeroutput> predicate </para>
</returns></function>
<function name="is_from_range"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="CharT"/>
        </template><parameter name="From"><paramtype>CharT</paramtype><description><para>The start of the range </para></description></parameter><parameter name="To"><paramtype>CharT</paramtype><description><para>The end of the range </para></description></parameter><purpose>is_from_range predicate </purpose><description><para>Construct the <computeroutput>is_from_range</computeroutput> predicate. The predicate holds if the input is included in the specified range. (i.e. From &lt;= Ch &lt;= To )</para><para>

</para></description><returns><para>An instance of the <computeroutput>is_from_range</computeroutput> predicate </para>
</returns></function>
<function name="operator&amp;&amp;"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="Pred1T"/>
          <template-type-parameter name="Pred2T"/>
        </template><parameter name="Pred1"><paramtype>const predicate_facade&lt; Pred1T &gt; &amp;</paramtype><description><para>The first predicate </para></description></parameter><parameter name="Pred2"><paramtype>const predicate_facade&lt; Pred2T &gt; &amp;</paramtype><description><para>The second predicate </para></description></parameter><purpose>predicate 'and' composition predicate </purpose><description><para>Construct the <computeroutput>class_and</computeroutput> predicate. This predicate can be used to logically combine two classification predicates. <computeroutput>class_and</computeroutput> holds, if both predicates return true.</para><para>

</para></description><returns><para>An instance of the <computeroutput>class_and</computeroutput> predicate </para>
</returns></function>
<function name="operator||"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="Pred1T"/>
          <template-type-parameter name="Pred2T"/>
        </template><parameter name="Pred1"><paramtype>const predicate_facade&lt; Pred1T &gt; &amp;</paramtype><description><para>The first predicate </para></description></parameter><parameter name="Pred2"><paramtype>const predicate_facade&lt; Pred2T &gt; &amp;</paramtype><description><para>The second predicate </para></description></parameter><purpose>predicate 'or' composition predicate </purpose><description><para>Construct the <computeroutput>class_or</computeroutput> predicate. This predicate can be used to logically combine two classification predicates. <computeroutput>class_or</computeroutput> holds, if one of the predicates return true.</para><para>

</para></description><returns><para>An instance of the <computeroutput>class_or</computeroutput> predicate </para>
</returns></function>
<function name="operator!"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="PredT"/>
        </template><parameter name="Pred"><paramtype>const predicate_facade&lt; PredT &gt; &amp;</paramtype><description><para>The predicate to be negated </para></description></parameter><purpose>predicate negation operator </purpose><description><para>Construct the <computeroutput>class_not</computeroutput> predicate. This predicate represents a negation. <computeroutput>class_or</computeroutput> holds if of the predicates return false.</para><para>

</para></description><returns><para>An instance of the <computeroutput>class_not</computeroutput> predicate </para>
</returns></function>









































































































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/compare.hpp">
<para>Defines element comparison predicates. Many algorithms in this library can take an additional argument with a predicate used to compare elements. This makes it possible, for instance, to have case insensitive versions of the algorithms. </para><namespace name="boost">
<namespace name="algorithm">
<struct name="is_equal"><purpose><classname alt="boost::algorithm::is_equal">is_equal</classname> functor </purpose><description><para>Standard STL equal_to only handle comparison between arguments of the same type. This is a less restrictive version which wraps operator ==. </para></description><method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="T2"/>
        </template><parameter name="Arg1"><paramtype>const T1 &amp;</paramtype></parameter><parameter name="Arg2"><paramtype>const T2 &amp;</paramtype></parameter><purpose>Function operator. </purpose><description><para>Compare two operands for equality </para></description></method>
</method-group>
</struct><struct name="is_iequal"><purpose>case insensitive version of <classname alt="boost::algorithm::is_equal">is_equal</classname> </purpose><description><para>Case insensitive comparison predicate. Comparison is done using specified locales. </para></description><method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="T2"/>
        </template><parameter name="Arg1"><paramtype>const T1 &amp;</paramtype></parameter><parameter name="Arg2"><paramtype>const T2 &amp;</paramtype></parameter><purpose>Function operator. </purpose><description><para>Compare two operands. Case is ignored. </para></description></method>
</method-group>
<constructor><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>locales used for comparison </para></description></parameter><purpose>Constructor. </purpose><description><para>
</para></description></constructor>
</struct><struct name="is_iless"><purpose>case insensitive version of <classname alt="boost::algorithm::is_less">is_less</classname> </purpose><description><para>Case insensitive comparison predicate. Comparison is done using specified locales. </para></description><method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="T2"/>
        </template><parameter name="Arg1"><paramtype>const T1 &amp;</paramtype></parameter><parameter name="Arg2"><paramtype>const T2 &amp;</paramtype></parameter><purpose>Function operator. </purpose><description><para>Compare two operands. Case is ignored. </para></description></method>
</method-group>
<constructor><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>locales used for comparison </para></description></parameter><purpose>Constructor. </purpose><description><para>
</para></description></constructor>
</struct><struct name="is_less"><purpose><classname alt="boost::algorithm::is_less">is_less</classname> functor </purpose><description><para>Convenient version of standard std::less. Operation is templated, therefore it is not required to specify the exact types upon the construction </para></description><method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="T2"/>
        </template><parameter name="Arg1"><paramtype>const T1 &amp;</paramtype></parameter><parameter name="Arg2"><paramtype>const T2 &amp;</paramtype></parameter><purpose>Functor operation. </purpose><description><para>Compare two operands using &gt; operator </para></description></method>
</method-group>
</struct><struct name="is_not_greater"><purpose><classname alt="boost::algorithm::is_not_greater">is_not_greater</classname> functor </purpose><description><para>Convenient version of standard std::not_greater_to. Operation is templated, therefore it is not required to specify the exact types upon the construction </para></description><method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="T2"/>
        </template><parameter name="Arg1"><paramtype>const T1 &amp;</paramtype></parameter><parameter name="Arg2"><paramtype>const T2 &amp;</paramtype></parameter><purpose>Functor operation. </purpose><description><para>Compare two operands using &gt; operator </para></description></method>
</method-group>
</struct><struct name="is_not_igreater"><purpose>case insensitive version of <classname alt="boost::algorithm::is_not_greater">is_not_greater</classname> </purpose><description><para>Case insensitive comparison predicate. Comparison is done using specified locales. </para></description><method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="T2"/>
        </template><parameter name="Arg1"><paramtype>const T1 &amp;</paramtype></parameter><parameter name="Arg2"><paramtype>const T2 &amp;</paramtype></parameter><purpose>Function operator. </purpose><description><para>Compare two operands. Case is ignored. </para></description></method>
</method-group>
<constructor><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>locales used for comparison </para></description></parameter><purpose>Constructor. </purpose><description><para>
</para></description></constructor>
</struct>

























































































































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/concept.hpp">
<para>Defines concepts used in string_algo library </para><namespace name="boost">
<namespace name="algorithm">
<struct name="FinderConcept"><template>
      <template-type-parameter name="FinderT"/>
      <template-type-parameter name="IteratorT"/>
    </template><purpose>Finder concept. </purpose><description><para>Defines the Finder concept. Finder is a functor which selects an arbitrary part of a string. Search is performed on the range specified by starting and ending iterators.</para><para>Result of the find operation must be convertible to iterator_range. </para></description><method-group name="public member functions">
<method name="constraints"><type>void</type></method>
</method-group>
</struct><struct name="FormatterConcept"><template>
      <template-type-parameter name="FormatterT"/>
      <template-type-parameter name="FinderT"/>
      <template-type-parameter name="IteratorT"/>
    </template><purpose>Formatter concept. </purpose><description><para>Defines the Formatter concept. Formatter is a functor, which takes a result from a finder operation and transforms it in a specific way.</para><para>Result must be a container supported by container_traits, or a reference to it. </para></description><method-group name="public member functions">
<method name="constraints"><type>void</type></method>
</method-group>
</struct>

























































































































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/constants.hpp">
<namespace name="boost">
<namespace name="algorithm">
<enum name="token_compress_mode_type"><enumvalue name="token_compress_on"><purpose>Compress adjacent tokens. </purpose></enumvalue><enumvalue name="token_compress_off"><purpose>Do not compress adjacent tokens. </purpose></enumvalue><purpose>Token compression mode. </purpose><description><para>Specifies token compression mode for the token_finder. </para></description></enum>


























































































































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/erase.hpp">
<para>Defines various erase algorithms. Each algorithm removes part(s) of the input according to a searching criteria. </para><namespace name="boost">
<namespace name="algorithm">


















































































<overloaded-function name="erase_range_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="SearchRange"><paramtype>const iterator_range&lt; typename range_const_iterator&lt; RangeT &gt;::type &gt; &amp;</paramtype><description><para>A range in the input to be removed </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="SearchRange"><paramtype>const iterator_range&lt; typename range_const_iterator&lt; SequenceT &gt;::type &gt; &amp;</paramtype></parameter></signature><purpose>Erase range algorithm. </purpose><description><para>Remove the given range from the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="erase_range"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="SearchRange"><paramtype>const iterator_range&lt; typename range_iterator&lt; SequenceT &gt;::type &gt; &amp;</paramtype><description><para>A range in the input to be removed </para></description></parameter><purpose>Erase range algorithm. </purpose><description><para>Remove the given range from the input. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="erase_first_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter></signature><purpose>Erase first algorithm. </purpose><description><para>Remove the first occurrence of the substring from the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="erase_first"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><purpose>Erase first algorithm. </purpose><description><para>Remove the first occurrence of the substring from the input. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="ierase_first_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Erase first algorithm ( case insensitive ) </purpose><description><para>Remove the first occurrence of the substring from the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator. Searching is case insensitive.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="ierase_first"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Erase first algorithm ( case insensitive ) </purpose><description><para>Remove the first occurrence of the substring from the input. The input sequence is modified in-place. Searching is case insensitive.</para><para>
</para></description></function>
<overloaded-function name="erase_last_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter></signature><purpose>Erase last algorithm. </purpose><description><para>Remove the last occurrence of the substring from the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="erase_last"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><purpose>Erase last algorithm. </purpose><description><para>Remove the last occurrence of the substring from the input. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="ierase_last_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Erase last algorithm ( case insensitive ) </purpose><description><para>Remove the last occurrence of the substring from the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator. Searching is case insensitive.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="ierase_last"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Erase last algorithm ( case insensitive ) </purpose><description><para>Remove the last occurrence of the substring from the input. The input sequence is modified in-place. Searching is case insensitive.</para><para>
</para></description></function>
<overloaded-function name="erase_nth_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index of the match to be replaced. The index is 0-based. For negative N, matches are counted from the end of string. </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter><parameter name="Nth"><paramtype>int</paramtype></parameter></signature><purpose>Erase nth algorithm. </purpose><description><para>Remove the Nth occurrence of the substring in the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="erase_nth"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index of the match to be replaced. The index is 0-based. For negative N, matches are counted from the end of string. </para></description></parameter><purpose>Erase nth algorithm. </purpose><description><para>Remove the Nth occurrence of the substring in the input. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="ierase_nth_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index of the match to be replaced. The index is 0-based. For negative N, matches are counted from the end of string. </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter><parameter name="Nth"><paramtype>int</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Erase nth algorithm ( case insensitive ) </purpose><description><para>Remove the Nth occurrence of the substring in the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator. Searching is case insensitive.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="ierase_nth"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index of the match to be replaced. The index is 0-based. For negative N, matches are counted from the end of string. </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Erase nth algorithm. </purpose><description><para>Remove the Nth occurrence of the substring in the input. The input sequence is modified in-place. Searching is case insensitive.</para><para>
</para></description></function>
<overloaded-function name="erase_all_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter></signature><purpose>Erase all algorithm. </purpose><description><para>Remove all the occurrences of the string from the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="erase_all"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><purpose>Erase all algorithm. </purpose><description><para>Remove all the occurrences of the string from the input. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="ierase_all_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Erase all algorithm ( case insensitive ) </purpose><description><para>Remove all the occurrences of the string from the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator. Searching is case insensitive.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="ierase_all"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Erase all algorithm ( case insensitive ) </purpose><description><para>Remove all the occurrences of the string from the input. The input sequence is modified in-place. Searching is case insensitive.</para><para>
</para></description></function>
<overloaded-function name="erase_head_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the head. For N&gt;=0, at most N characters are extracted. For N&lt;0, size(Input)-|N| characters are extracted. </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="N"><paramtype>int</paramtype></parameter></signature><purpose>Erase head algorithm. </purpose><description><para>Remove the head from the input. The head is a prefix of a sequence of given size. If the sequence is shorter then required, the whole string is considered to be the head. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="erase_head"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the head For N&gt;=0, at most N characters are extracted. For N&lt;0, size(Input)-|N| characters are extracted. </para></description></parameter><purpose>Erase head algorithm. </purpose><description><para>Remove the head from the input. The head is a prefix of a sequence of given size. If the sequence is shorter then required, the whole string is considered to be the head. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="erase_tail_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the tail.</para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="N"><paramtype>int</paramtype></parameter></signature><purpose>Erase tail algorithm. </purpose><description><para>Remove the tail from the input. The tail is a suffix of a sequence of given size. If the sequence is shorter then required, the whole string is considered to be the tail. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>
For N&gt;=0, at most N characters are extracted. For N&lt;0, size(Input)-|N| characters are extracted. 
<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="erase_tail"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the tail For N&gt;=0, at most N characters are extracted. For N&lt;0, size(Input)-|N| characters are extracted. </para></description></parameter><purpose>Erase tail algorithm. </purpose><description><para>Remove the tail from the input. The tail is a suffix of a sequence of given size. If the sequence is shorter then required, the whole string is considered to be the tail. The input sequence is modified in-place.</para><para>
</para></description></function>







































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/find.hpp">
<para>Defines a set of find algorithms. The algorithms are searching for a substring of the input. The result is given as an <computeroutput>iterator_range</computeroutput> delimiting the substring. </para><namespace name="boost">
<namespace name="algorithm">























<function name="find"><type>iterator_range&lt; typename range_iterator&lt; RangeT &gt;::type &gt;</type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="FinderT"/>
        </template><parameter name="Input"><paramtype>RangeT &amp;</paramtype><description><para>A string which will be searched. </para></description></parameter><parameter name="Finder"><paramtype>const FinderT &amp;</paramtype><description><para>Finder object used for searching. </para></description></parameter><purpose>Generic find algorithm. </purpose><description><para>Search the input using the given finder.</para><para>

</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>RangeT::iterator</computeroutput> or <computeroutput>RangeT::const_iterator</computeroutput>, depending on the constness of the input parameter. </para>
</returns></function>
<function name="find_first"><type>iterator_range&lt; typename range_iterator&lt; Range1T &gt;::type &gt;</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>Range1T &amp;</paramtype><description><para>A string which will be searched. </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><purpose>Find first algorithm. </purpose><description><para>Search for the first occurrence of the substring in the input.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>RangeT::iterator</computeroutput> or <computeroutput>RangeT::const_iterator</computeroutput>, depending on the constness of the input parameter.</para>
</returns></function>
<function name="ifind_first"><type>iterator_range&lt; typename range_iterator&lt; Range1T &gt;::type &gt;</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>Range1T &amp;</paramtype><description><para>A string which will be searched. </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Find first algorithm ( case insensitive ) </purpose><description><para>Search for the first occurrence of the substring in the input. Searching is case insensitive.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>Range1T::iterator</computeroutput> or <computeroutput>Range1T::const_iterator</computeroutput>, depending on the constness of the input parameter.</para>
</returns></function>
<function name="find_last"><type>iterator_range&lt; typename range_iterator&lt; Range1T &gt;::type &gt;</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>Range1T &amp;</paramtype><description><para>A string which will be searched. </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><purpose>Find last algorithm. </purpose><description><para>Search for the last occurrence of the substring in the input.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>Range1T::iterator</computeroutput> or <computeroutput>Range1T::const_iterator</computeroutput>, depending on the constness of the input parameter.</para>
</returns></function>
<function name="ifind_last"><type>iterator_range&lt; typename range_iterator&lt; Range1T &gt;::type &gt;</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>Range1T &amp;</paramtype><description><para>A string which will be searched. </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Find last algorithm ( case insensitive ) </purpose><description><para>Search for the last match a string in the input. Searching is case insensitive.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>Range1T::iterator</computeroutput> or <computeroutput>Range1T::const_iterator</computeroutput>, depending on the constness of the input parameter.</para>
</returns></function>
<function name="find_nth"><type>iterator_range&lt; typename range_iterator&lt; Range1T &gt;::type &gt;</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>Range1T &amp;</paramtype><description><para>A string which will be searched. </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index (zero-indexed) of the match to be found. For negative N, the matches are counted from the end of string. </para></description></parameter><purpose>Find n-th algorithm. </purpose><description><para>Search for the n-th (zero-indexed) occurrence of the substring in the input.</para><para>

</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>Range1T::iterator</computeroutput> or <computeroutput>Range1T::const_iterator</computeroutput>, depending on the constness of the input parameter. </para>
</returns></function>
<function name="ifind_nth"><type>iterator_range&lt; typename range_iterator&lt; Range1T &gt;::type &gt;</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>Range1T &amp;</paramtype><description><para>A string which will be searched. </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index (zero-indexed) of the match to be found. For negative N, the matches are counted from the end of string. </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Find n-th algorithm ( case insensitive ). </purpose><description><para>Search for the n-th (zero-indexed) occurrence of the substring in the input. Searching is case insensitive.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>Range1T::iterator</computeroutput> or <computeroutput>Range1T::const_iterator</computeroutput>, depending on the constness of the input parameter.</para>
</returns></function>
<function name="find_head"><type>iterator_range&lt; typename range_iterator&lt; RangeT &gt;::type &gt;</type><template>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>RangeT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the head For N&gt;=0, at most N characters are extracted. For N&lt;0, at most size(Input)-|N| characters are extracted. </para></description></parameter><purpose>Find head algorithm. </purpose><description><para>Get the head of the input. Head is a prefix of the string of the given size. If the input is shorter then required, whole input is considered to be the head.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>Range1T::iterator</computeroutput> or <computeroutput>Range1T::const_iterator</computeroutput>, depending on the constness of the input parameter.</para>
</returns></function>
<function name="find_tail"><type>iterator_range&lt; typename range_iterator&lt; RangeT &gt;::type &gt;</type><template>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>RangeT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the tail. For N&gt;=0, at most N characters are extracted. For N&lt;0, at most size(Input)-|N| characters are extracted. </para></description></parameter><purpose>Find tail algorithm. </purpose><description><para>Get the tail of the input. Tail is a suffix of the string of the given size. If the input is shorter then required, whole input is considered to be the tail.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>RangeT::iterator</computeroutput> or <computeroutput>RangeT::const_iterator</computeroutput>, depending on the constness of the input parameter.</para>
</returns></function>
<function name="find_token"><type>iterator_range&lt; typename range_iterator&lt; RangeT &gt;::type &gt;</type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>RangeT &amp;</paramtype><description><para>A input string. </para></description></parameter><parameter name="Pred"><paramtype>PredicateT</paramtype><description><para>A unary predicate to identify a token </para></description></parameter><parameter name="eCompress"><paramtype>token_compress_mode_type</paramtype><default>token_compress_off</default><description><para>Enable/Disable compressing of adjacent tokens </para></description></parameter><purpose>Find token algorithm. </purpose><description><para>Look for a given token in the string. Token is a character that matches the given predicate. If the "token compress mode" is enabled, adjacent tokens are considered to be one match.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>RangeT::iterator</computeroutput> or <computeroutput>RangeT::const_iterator</computeroutput>, depending on the constness of the input parameter.</para>
</returns></function>

























































































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/find_format.hpp">
<para>Defines generic replace algorithms. Each algorithm replaces part(s) of the input. The part to be replaced is looked up using a Finder object. Result of finding is then used by a Formatter object to generate the replacement. </para><namespace name="boost">
<namespace name="algorithm">






















































































































































<overloaded-function name="find_format_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="FinderT"/>
          <template-type-parameter name="FormatterT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Finder"><paramtype>FinderT</paramtype><description><para>A Finder object used to search for a match to be replaced </para></description></parameter><parameter name="Formatter"><paramtype>FormatterT</paramtype><description><para>A Formatter object used to format a match </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="FinderT"/>
          <template-type-parameter name="FormatterT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Finder"><paramtype>FinderT</paramtype></parameter><parameter name="Formatter"><paramtype>FormatterT</paramtype></parameter></signature><purpose>Generic replace algorithm. </purpose><description><para>Use the Finder to search for a substring. Use the Formatter to format this substring and replace it in the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="find_format"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="FinderT"/>
          <template-type-parameter name="FormatterT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Finder"><paramtype>FinderT</paramtype><description><para>A Finder object used to search for a match to be replaced </para></description></parameter><parameter name="Formatter"><paramtype>FormatterT</paramtype><description><para>A Formatter object used to format a match </para></description></parameter><purpose>Generic replace algorithm. </purpose><description><para>Use the Finder to search for a substring. Use the Formatter to format this substring and replace it in the input. The input is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="find_format_all_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="FinderT"/>
          <template-type-parameter name="FormatterT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Finder"><paramtype>FinderT</paramtype><description><para>A Finder object used to search for a match to be replaced </para></description></parameter><parameter name="Formatter"><paramtype>FormatterT</paramtype><description><para>A Formatter object used to format a match </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="FinderT"/>
          <template-type-parameter name="FormatterT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Finder"><paramtype>FinderT</paramtype></parameter><parameter name="Formatter"><paramtype>FormatterT</paramtype></parameter></signature><purpose>Generic replace all algorithm. </purpose><description><para>Use the Finder to search for a substring. Use the Formatter to format this substring and replace it in the input. Repeat this for all matching substrings. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="find_format_all"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="FinderT"/>
          <template-type-parameter name="FormatterT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Finder"><paramtype>FinderT</paramtype><description><para>A Finder object used to search for a match to be replaced </para></description></parameter><parameter name="Formatter"><paramtype>FormatterT</paramtype><description><para>A Formatter object used to format a match </para></description></parameter><purpose>Generic replace all algorithm. </purpose><description><para>Use the Finder to search for a substring. Use the Formatter to format this substring and replace it in the input. Repeat this for all matching substrings.The input is modified in-place.</para><para>
</para></description></function>






























</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/find_iterator.hpp">
<para>Defines find iterator classes. Find iterator repeatedly applies a Finder to the specified input string to search for matches. Dereferencing the iterator yields the current match or a range between the last and the current match depending on the iterator used. </para><namespace name="boost">
<namespace name="algorithm">
<class name="find_iterator"><template>
      <template-type-parameter name="IteratorT"/>
    </template><inherit access="public">iterator_facade&lt; find_iterator&lt; IteratorT &gt;, const iterator_range&lt; IteratorT &gt;, forward_traversal_tag &gt;</inherit><purpose><classname alt="boost::algorithm::find_iterator">find_iterator</classname> </purpose><description><para>Find iterator encapsulates a Finder and allows for incremental searching in a string. Each increment moves the iterator to the next match.</para><para>Find iterator is a readable forward traversal iterator.</para><para>Dereferencing the iterator yields an iterator_range delimiting the current match. </para></description><data-member name="End"><type>IteratorT</type></data-member>
<data-member name="Finder"><type>IteratorT FinderT</type></data-member>
<method-group name="public member functions">
<method name="BOOST_DEFAULTED_FUNCTION"><type/><parameter name="operator"><paramtype><classname>find_iterator</classname> &amp;</paramtype><default>(const <classname alt="boost::algorithm::find_iterator">find_iterator</classname> &amp;Other)</default></parameter><parameter name=""><paramtype>{ if(this==&amp;Other) return *this;this-&gt;base_type::operator=(Other);m_Match=Other.m_Match;m_End=Other.m_End;return *this;}</paramtype></parameter><purpose>Copy assignment. </purpose><description><para>Assigns a copy of the find_iteratorConstructor</para><para>Construct new <classname alt="boost::algorithm::find_iterator">find_iterator</classname> for a given finder and a range. </para></description></method>
<method name="m_Match"><type>IteratorT FinderT</type><parameter name=""><paramtype>Begin</paramtype></parameter><parameter name=""><paramtype>Begin</paramtype></parameter></method>
<method name="m_End"><type>IteratorT FinderT</type><parameter name=""><paramtype>End</paramtype></parameter></method>
<method name="eof" cv="const"><type>bool</type><purpose>Eof check. </purpose><description><para>Check the eof condition. Eof condition means that there is nothing more to be searched i.e. <classname alt="boost::algorithm::find_iterator">find_iterator</classname> is after the last match. </para></description></method>
</method-group>
<constructor><parameter name="Other"><paramtype>const <classname>find_iterator</classname> &amp;</paramtype></parameter><purpose>Default constructor. </purpose><description><para>Construct null iterator. All null iterators are equal.</para><para>
Construct a copy of the <classname alt="boost::algorithm::find_iterator">find_iterator</classname> </para></description><postconditions><para>eof()==trueCopy constructor</para>
</postconditions></constructor>
<constructor><template>
          <template-type-parameter name="FinderT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Col"><paramtype>RangeT &amp;</paramtype></parameter><parameter name="Finder"><paramtype>FinderT</paramtype></parameter><purpose>Constructor. </purpose><description><para>Construct new <classname alt="boost::algorithm::find_iterator">find_iterator</classname> for a given finder and a range. </para></description></constructor>
<method-group name="private member functions">
<method name="dereference" cv="const"><type>const match_type &amp;</type></method>
<method name="increment"><type>void</type></method>
<method name="equal" cv="const"><type>bool</type><parameter name="Other"><paramtype>const <classname>find_iterator</classname> &amp;</paramtype></parameter></method>
</method-group>
</class><class name="split_iterator"><template>
      <template-type-parameter name="IteratorT"/>
    </template><inherit access="public">iterator_facade&lt; split_iterator&lt; IteratorT &gt;, const iterator_range&lt; IteratorT &gt;, forward_traversal_tag &gt;</inherit><purpose><classname alt="boost::algorithm::split_iterator">split_iterator</classname> </purpose><description><para>Split iterator encapsulates a Finder and allows for incremental searching in a string. Unlike the find iterator, split iterator iterates through gaps between matches.</para><para>Find iterator is a readable forward traversal iterator.</para><para>Dereferencing the iterator yields an iterator_range delimiting the current match. </para></description><data-member name="End"><type>IteratorT</type></data-member>
<data-member name="Finder"><type>IteratorT FinderT</type></data-member>
<method-group name="public member functions">
<method name="BOOST_DEFAULTED_FUNCTION"><type/><parameter name="operator"><paramtype><classname>split_iterator</classname> &amp;</paramtype><default>(const <classname alt="boost::algorithm::split_iterator">split_iterator</classname> &amp;Other)</default></parameter><parameter name=""><paramtype>{ if(this==&amp;Other) return *this;this-&gt;base_type::operator=(Other);m_Match=Other.m_Match;m_Next=Other.m_Next;m_End=Other.m_End;m_bEof=Other.m_bEof;return *this;}</paramtype></parameter><purpose>Assignment operator. </purpose><description><para>Assigns a copy of the split_iteratorConstructor</para><para>Construct new <classname alt="boost::algorithm::split_iterator">split_iterator</classname> for a given finder and a range. </para></description></method>
<method name="m_Match"><type>IteratorT FinderT</type><parameter name=""><paramtype>Begin</paramtype></parameter><parameter name=""><paramtype>Begin</paramtype></parameter></method>
<method name="m_Next"><type>IteratorT FinderT</type><parameter name=""><paramtype>Begin</paramtype></parameter></method>
<method name="m_End"><type>IteratorT FinderT</type><parameter name=""><paramtype>End</paramtype></parameter></method>
<method name="m_bEof"><type>IteratorT FinderT</type><parameter name=""><paramtype>false</paramtype></parameter></method>
<method name="eof" cv="const"><type>bool</type><purpose>Eof check. </purpose><description><para>Check the eof condition. Eof condition means that there is nothing more to be searched i.e. <classname alt="boost::algorithm::find_iterator">find_iterator</classname> is after the last match. </para></description></method>
</method-group>
<constructor><purpose>Default constructor. </purpose><description><para>Construct null iterator. All null iterators are equal.</para><para>
</para></description><postconditions><para>eof()==true </para>
</postconditions></constructor>
<constructor><parameter name="Other"><paramtype>const <classname>split_iterator</classname> &amp;</paramtype></parameter><purpose>Copy constructor. </purpose><description><para>Construct a copy of the <classname alt="boost::algorithm::split_iterator">split_iterator</classname> </para></description></constructor>
<constructor><template>
          <template-type-parameter name="FinderT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Col"><paramtype>RangeT &amp;</paramtype></parameter><parameter name="Finder"><paramtype>FinderT</paramtype></parameter><purpose>Constructor. </purpose><description><para>Construct new <classname alt="boost::algorithm::split_iterator">split_iterator</classname> for a given finder and a collection. </para></description></constructor>
<method-group name="private member functions">
<method name="dereference" cv="const"><type>const match_type &amp;</type></method>
<method name="increment"><type>void</type></method>
<method name="equal" cv="const"><type>bool</type><parameter name="Other"><paramtype>const <classname>split_iterator</classname> &amp;</paramtype></parameter></method>
</method-group>
</class>











































<function name="make_find_iterator"><type><classname>find_iterator</classname>&lt; typename range_iterator&lt; RangeT &gt;::type &gt;</type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="FinderT"/>
        </template><parameter name="Collection"><paramtype>RangeT &amp;</paramtype></parameter><parameter name="Finder"><paramtype>FinderT</paramtype></parameter><purpose>find iterator construction helper </purpose><description><para>Construct a find iterator to iterate through the specified string </para></description></function>
<function name="make_split_iterator"><type><classname>split_iterator</classname>&lt; typename range_iterator&lt; RangeT &gt;::type &gt;</type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="FinderT"/>
        </template><parameter name="Collection"><paramtype>RangeT &amp;</paramtype></parameter><parameter name="Finder"><paramtype>FinderT</paramtype></parameter><purpose>split iterator construction helper </purpose><description><para>Construct a split iterator to iterate through the specified collection </para></description></function>












































































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/finder.hpp">
<para>Defines Finder generators. Finder object is a functor which is able to find a substring matching a specific criteria in the input. Finders are used as a pluggable components for replace, find and split facilities. This header contains generator functions for finders provided in this library. </para><namespace name="boost">
<namespace name="algorithm">

































<overloaded-function name="first_finder"><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter></signature><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter><parameter name="Comp"><paramtype>PredicateT</paramtype></parameter></signature><purpose>"First" finder </purpose><description><para>Construct the <computeroutput>first_finder</computeroutput>. The finder searches for the first occurrence of the string in a given input. The result is given as an <computeroutput>iterator_range</computeroutput> delimiting the match.</para><para>

</para></description><returns><para>An instance of the <computeroutput>first_finder</computeroutput> object </para>
</returns></overloaded-function>

<overloaded-function name="last_finder"><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter></signature><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter><parameter name="Comp"><paramtype>PredicateT</paramtype></parameter></signature><purpose>"Last" finder </purpose><description><para>Construct the <computeroutput>last_finder</computeroutput>. The finder searches for the last occurrence of the string in a given input. The result is given as an <computeroutput>iterator_range</computeroutput> delimiting the match.</para><para>

</para></description><returns><para>An instance of the <computeroutput>last_finder</computeroutput> object </para>
</returns></overloaded-function>

<overloaded-function name="nth_finder"><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Search"><paramtype>const RangeT &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index of the match to be find </para></description></parameter></signature><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Search"><paramtype>const RangeT &amp;</paramtype></parameter><parameter name="Nth"><paramtype>int</paramtype></parameter><parameter name="Comp"><paramtype>PredicateT</paramtype></parameter></signature><purpose>"Nth" finder </purpose><description><para>Construct the <computeroutput>nth_finder</computeroutput>. The finder searches for the n-th (zero-indexed) occurrence of the string in a given input. The result is given as an <computeroutput>iterator_range</computeroutput> delimiting the match.</para><para>

</para></description><returns><para>An instance of the <computeroutput>nth_finder</computeroutput> object </para>
</returns></overloaded-function>

<function name="head_finder"><type><emphasis>unspecified</emphasis></type><parameter name="N"><paramtype>int</paramtype><description><para>The size of the head </para></description></parameter><purpose>"Head" finder </purpose><description><para>Construct the <computeroutput>head_finder</computeroutput>. The finder returns a head of a given input. The head is a prefix of a string up to n elements in size. If an input has less then n elements, whole input is considered a head. The result is given as an <computeroutput>iterator_range</computeroutput> delimiting the match.</para><para>

</para></description><returns><para>An instance of the <computeroutput>head_finder</computeroutput> object </para>
</returns></function>
<function name="tail_finder"><type><emphasis>unspecified</emphasis></type><parameter name="N"><paramtype>int</paramtype><description><para>The size of the head </para></description></parameter><purpose>"Tail" finder </purpose><description><para>Construct the <computeroutput>tail_finder</computeroutput>. The finder returns a tail of a given input. The tail is a suffix of a string up to n elements in size. If an input has less then n elements, whole input is considered a head. The result is given as an <computeroutput>iterator_range</computeroutput> delimiting the match.</para><para>

</para></description><returns><para>An instance of the <computeroutput>tail_finder</computeroutput> object </para>
</returns></function>
<function name="token_finder"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Pred"><paramtype>PredicateT</paramtype><description><para>An element selection predicate </para></description></parameter><parameter name="eCompress"><paramtype>token_compress_mode_type</paramtype><default>token_compress_off</default><description><para>Compress flag </para></description></parameter><purpose>"Token" finder </purpose><description><para>Construct the <computeroutput>token_finder</computeroutput>. The finder searches for a token specified by a predicate. It is similar to std::find_if algorithm, with an exception that it return a range of instead of a single iterator.</para><para>If "compress token mode" is enabled, adjacent matching tokens are concatenated into one match. Thus the finder can be used to search for continuous segments of characters satisfying the given predicate.</para><para>The result is given as an <computeroutput>iterator_range</computeroutput> delimiting the match.</para><para>

</para></description><returns><para>An instance of the <computeroutput>token_finder</computeroutput> object </para>
</returns></function>
<overloaded-function name="range_finder"><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="ForwardIteratorT"/>
        </template><parameter name="Begin"><paramtype>ForwardIteratorT</paramtype><description><para>Beginning of the range </para></description></parameter><parameter name="End"><paramtype>ForwardIteratorT</paramtype><description><para>End of the range </para></description></parameter></signature><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="ForwardIteratorT"/>
        </template><parameter name="Range"><paramtype>iterator_range&lt; ForwardIteratorT &gt;</paramtype></parameter></signature><purpose>"Range" finder </purpose><description><para>Construct the <computeroutput>range_finder</computeroutput>. The finder does not perform any operation. It simply returns the given range for any input.</para><para>

</para></description><returns><para>An instance of the <computeroutput>range_finger</computeroutput> object </para>
</returns></overloaded-function>















































































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/formatter.hpp">
<para>Defines Formatter generators. Formatter is a functor which formats a string according to given parameters. A Formatter works in conjunction with a Finder. A Finder can provide additional information for a specific Formatter. An example of such a cooperation is regex_finder and regex_formatter.</para><para>Formatters are used as pluggable components for replace facilities. This header contains generator functions for the Formatters provided in this library. </para><namespace name="boost">
<namespace name="algorithm">




























































































































































<function name="const_formatter"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Format"><paramtype>const RangeT &amp;</paramtype><description><para>A predefined value used as a result for formatting </para></description></parameter><purpose>Constant formatter. </purpose><description><para>Constructs a <computeroutput>const_formatter</computeroutput>. Const formatter always returns the same value, regardless of the parameter.</para><para>

</para></description><returns><para>An instance of the <computeroutput>const_formatter</computeroutput> object. </para>
</returns></function>
<function name="identity_formatter"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
        </template><purpose>Identity formatter. </purpose><description><para>Constructs an <computeroutput>identity_formatter</computeroutput>. Identity formatter always returns the parameter.</para><para>
</para></description><returns><para>An instance of the <computeroutput>identity_formatter</computeroutput> object. </para>
</returns></function>
<function name="empty_formatter"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="RangeT"/>
        </template><parameter name=""><paramtype>const RangeT &amp;</paramtype></parameter><purpose>Empty formatter. </purpose><description><para>Constructs an <computeroutput>empty_formatter</computeroutput>. Empty formatter always returns an empty sequence.</para><para>

</para></description><returns><para>An instance of the <computeroutput>empty_formatter</computeroutput> object. </para>
</returns></function>
<function name="dissect_formatter"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="FinderT"/>
        </template><parameter name="Finder"><paramtype>const FinderT &amp;</paramtype><description><para>a finder used to select a portion of the formatted sequence </para></description></parameter><purpose>Empty formatter. </purpose><description><para>Constructs a <computeroutput>dissect_formatter</computeroutput>. Dissect formatter uses a specified finder to extract a portion of the formatted sequence. The first finder's match is returned as a result</para><para>

</para></description><returns><para>An instance of the <computeroutput>dissect_formatter</computeroutput> object. </para>
</returns></function>


























</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/iter_find.hpp">
<para>Defines generic split algorithms. Split algorithms can be used to divide a sequence into several part according to a given criteria. Result is given as a 'container of containers' where elements are copies or references to extracted parts.</para><para>There are two algorithms provided. One iterates over matching substrings, the other one over the gaps between these matches. </para><namespace name="boost">
<namespace name="algorithm">
















































































<function name="iter_find"><type>SequenceSequenceT &amp;</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="FinderT"/>
        </template><parameter name="Result"><paramtype>SequenceSequenceT &amp;</paramtype><description><para>A 'container container' to contain the result of search. Both outer and inner container must have constructor taking a pair of iterators as an argument. Typical type of the result is <computeroutput>std::vector&lt;boost::iterator_range&lt;iterator&gt;&gt;</computeroutput> (each element of such a vector will container a range delimiting a match). </para></description></parameter><parameter name="Input"><paramtype>RangeT &amp;&amp;</paramtype><description><para>A container which will be searched. </para></description></parameter><parameter name="Finder"><paramtype>FinderT</paramtype><description><para>A Finder object used for searching </para></description></parameter><purpose>Iter find algorithm. </purpose><description><para>This algorithm executes a given finder in iteration on the input, until the end of input is reached, or no match is found. Iteration is done using built-in <classname alt="boost::algorithm::find_iterator">find_iterator</classname>, so the real searching is performed only when needed. In each iteration new match is found and added to the result.</para><para>

<note><para>Prior content of the result will be overwritten. </para>
</note>
</para></description><returns><para>A reference to the result</para>
</returns></function>
<function name="iter_split"><type>SequenceSequenceT &amp;</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="FinderT"/>
        </template><parameter name="Result"><paramtype>SequenceSequenceT &amp;</paramtype><description><para>A 'container container' to contain the result of search. Both outer and inner container must have constructor taking a pair of iterators as an argument. Typical type of the result is <computeroutput>std::vector&lt;boost::iterator_range&lt;iterator&gt;&gt;</computeroutput> (each element of such a vector will container a range delimiting a match). </para></description></parameter><parameter name="Input"><paramtype>RangeT &amp;&amp;</paramtype><description><para>A container which will be searched. </para></description></parameter><parameter name="Finder"><paramtype>FinderT</paramtype><description><para>A finder object used for searching </para></description></parameter><purpose>Split find algorithm. </purpose><description><para>This algorithm executes a given finder in iteration on the input, until the end of input is reached, or no match is found. Iteration is done using built-in <classname alt="boost::algorithm::find_iterator">find_iterator</classname>, so the real searching is performed only when needed. Each match is used as a separator of segments. These segments are then returned in the result.</para><para>

<note><para>Prior content of the result will be overwritten. </para>
</note>
</para></description><returns><para>A reference to the result</para>
</returns></function>








































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/join.hpp">
<para>Defines join algorithm.</para><para>Join algorithm is a counterpart to split algorithms. It joins strings from a 'list' by adding user defined separator. Additionally there is a version that allows simple filtering by providing a predicate. </para><namespace name="boost">
<namespace name="algorithm">



















































































































<function name="join"><type>range_value&lt; SequenceSequenceT &gt;::type</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="Range1T"/>
        </template><parameter name="Input"><paramtype>const SequenceSequenceT &amp;</paramtype><description><para>A container that holds the input strings. It must be a container-of-containers. </para></description></parameter><parameter name="Separator"><paramtype>const Range1T &amp;</paramtype><description><para>A string that will separate the joined segments. </para></description></parameter><purpose>Join algorithm. </purpose><description><para>This algorithm joins all strings in a 'list' into one long string. Segments are concatenated by given separator.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>Concatenated string.</para>
</returns></function>
<function name="join_if"><type>range_value&lt; SequenceSequenceT &gt;::type</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const SequenceSequenceT &amp;</paramtype><description><para>A container that holds the input strings. It must be a container-of-containers. </para></description></parameter><parameter name="Separator"><paramtype>const Range1T &amp;</paramtype><description><para>A string that will separate the joined segments. </para></description></parameter><parameter name="Pred"><paramtype>PredicateT</paramtype><description><para>A segment selection predicate </para></description></parameter><purpose>Conditional join algorithm. </purpose><description><para>This algorithm joins all strings in a 'list' into one long string. Segments are concatenated by given separator. Only segments that satisfy the predicate will be added to the result.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>Concatenated string.</para>
</returns></function>





































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/predicate.hpp">
<para>Defines string-related predicates. The predicates determine whether a substring is contained in the input string under various conditions: a string starts with the substring, ends with the substring, simply contains the substring or if both strings are equal. Additionaly the algorithm <computeroutput>all()</computeroutput> checks all elements of a container to satisfy a condition.</para><para>All predicates provide the strong exception guarantee. </para><namespace name="boost">
<namespace name="algorithm">





























































<overloaded-function name="starts_with"><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype><description><para>A test sequence </para></description></parameter><parameter name="Comp"><paramtype>PredicateT</paramtype><description><para>An element comparison predicate </para></description></parameter></signature><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype></parameter></signature><purpose>'Starts with' predicate </purpose><description><para>This predicate holds when the test string is a prefix of the Input. In other words, if the input starts with the test. When the optional predicate is specified, it is used for character-wise comparison.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></overloaded-function>

<function name="istarts_with"><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype><description><para>A test sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>'Starts with' predicate ( case insensitive ) </purpose><description><para>This predicate holds when the test string is a prefix of the Input. In other words, if the input starts with the test. Elements are compared case insensitively.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></function>
<overloaded-function name="ends_with"><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype><description><para>A test sequence </para></description></parameter><parameter name="Comp"><paramtype>PredicateT</paramtype><description><para>An element comparison predicate </para></description></parameter></signature><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype></parameter></signature><purpose>'Ends with' predicate </purpose><description><para>This predicate holds when the test string is a suffix of the Input. In other words, if the input ends with the test. When the optional predicate is specified, it is used for character-wise comparison.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></overloaded-function>

<function name="iends_with"><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype><description><para>A test sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>'Ends with' predicate ( case insensitive ) </purpose><description><para>This predicate holds when the test container is a suffix of the Input. In other words, if the input ends with the test. Elements are compared case insensitively.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></function>
<overloaded-function name="contains"><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype><description><para>A test sequence </para></description></parameter><parameter name="Comp"><paramtype>PredicateT</paramtype><description><para>An element comparison predicate </para></description></parameter></signature><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype></parameter></signature><purpose>'Contains' predicate </purpose><description><para>This predicate holds when the test container is contained in the Input. When the optional predicate is specified, it is used for character-wise comparison.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></overloaded-function>

<function name="icontains"><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype><description><para>A test sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>'Contains' predicate ( case insensitive ) </purpose><description><para>This predicate holds when the test container is contained in the Input. Elements are compared case insensitively.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></function>
<overloaded-function name="equals"><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype><description><para>A test sequence </para></description></parameter><parameter name="Comp"><paramtype>PredicateT</paramtype><description><para>An element comparison predicate </para></description></parameter></signature><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype></parameter></signature><purpose>'Equals' predicate </purpose><description><para>This predicate holds when the test container is equal to the input container i.e. all elements in both containers are same. When the optional predicate is specified, it is used for character-wise comparison.</para><para>

<note><para>This is a two-way version of <computeroutput>std::equal</computeroutput> algorithm</para>
</note>
<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></overloaded-function>

<function name="iequals"><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Test"><paramtype>const Range2T &amp;</paramtype><description><para>A test sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>'Equals' predicate ( case insensitive ) </purpose><description><para>This predicate holds when the test container is equal to the input container i.e. all elements in both containers are same. Elements are compared case insensitively.</para><para>

<note><para>This is a two-way version of <computeroutput>std::equal</computeroutput> algorithm</para>
</note>
<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></function>
<overloaded-function name="lexicographical_compare"><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Arg1"><paramtype>const Range1T &amp;</paramtype><description><para>First argument </para></description></parameter><parameter name="Arg2"><paramtype>const Range2T &amp;</paramtype><description><para>Second argument </para></description></parameter><parameter name="Pred"><paramtype>PredicateT</paramtype><description><para>Comparison predicate </para></description></parameter></signature><signature><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Arg1"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Arg2"><paramtype>const Range2T &amp;</paramtype></parameter></signature><purpose>Lexicographical compare predicate. </purpose><description><para>This predicate is an overload of std::lexicographical_compare for range arguments</para><para>It check whether the first argument is lexicographically less then the second one.</para><para>If the optional predicate is specified, it is used for character-wise comparison</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></overloaded-function>

<function name="ilexicographical_compare"><type>bool</type><template>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Arg1"><paramtype>const Range1T &amp;</paramtype><description><para>First argument </para></description></parameter><parameter name="Arg2"><paramtype>const Range2T &amp;</paramtype><description><para>Second argument </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Lexicographical compare predicate (case-insensitive) </purpose><description><para>This predicate is an overload of std::lexicographical_compare for range arguments. It check whether the first argument is lexicographically less then the second one. Elements are compared case insensitively</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></function>
<function name="all"><type>bool</type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Pred"><paramtype>PredicateT</paramtype><description><para>A predicate </para></description></parameter><purpose>'All' predicate </purpose><description><para>This predicate holds it all its elements satisfy a given condition, represented by the predicate.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>The result of the test</para>
</returns></function>













































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/regex.hpp">
<para>Defines regex variants of the algorithms. </para><namespace name="boost">
<namespace name="algorithm">
































































































































































<function name="find_regex"><type>iterator_range&lt; typename range_iterator&lt; RangeT &gt;::type &gt;</type><template>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Input"><paramtype>RangeT &amp;</paramtype><description><para>A container which will be searched. </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default><description><para>Regex options </para></description></parameter><purpose>Find regex algorithm. </purpose><description><para>Search for a substring matching the given regex in the input.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An <computeroutput>iterator_range</computeroutput> delimiting the match. Returned iterator is either <computeroutput>RangeT::iterator</computeroutput> or <computeroutput>RangeT::const_iterator</computeroutput>, depending on the constness of the input parameter.</para>
</returns></function>
<overloaded-function name="replace_regex_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
          <template-type-parameter name="FormatStringTraitsT"/>
          <template-type-parameter name="FormatStringAllocatorT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Format"><paramtype>const std::basic_string&lt; CharT, FormatStringTraitsT, FormatStringAllocatorT &gt; &amp;</paramtype><description><para>Regex format definition </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default|format_default</default><description><para>Regex options </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
          <template-type-parameter name="FormatStringTraitsT"/>
          <template-type-parameter name="FormatStringAllocatorT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype></parameter><parameter name="Format"><paramtype>const std::basic_string&lt; CharT, FormatStringTraitsT, FormatStringAllocatorT &gt; &amp;</paramtype></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default|format_default</default></parameter></signature><purpose>Replace regex algorithm. </purpose><description><para>Search for a substring matching given regex and format it with the specified format.</para><para>The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="replace_regex"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
          <template-type-parameter name="FormatStringTraitsT"/>
          <template-type-parameter name="FormatStringAllocatorT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Format"><paramtype>const std::basic_string&lt; CharT, FormatStringTraitsT, FormatStringAllocatorT &gt; &amp;</paramtype><description><para>Regex format definition </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default|format_default</default><description><para>Regex options </para></description></parameter><purpose>Replace regex algorithm. </purpose><description><para>Search for a substring matching given regex and format it with the specified format. The input string is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="replace_all_regex_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
          <template-type-parameter name="FormatStringTraitsT"/>
          <template-type-parameter name="FormatStringAllocatorT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Format"><paramtype>const std::basic_string&lt; CharT, FormatStringTraitsT, FormatStringAllocatorT &gt; &amp;</paramtype><description><para>Regex format definition </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default|format_default</default><description><para>Regex options </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
          <template-type-parameter name="FormatStringTraitsT"/>
          <template-type-parameter name="FormatStringAllocatorT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype></parameter><parameter name="Format"><paramtype>const std::basic_string&lt; CharT, FormatStringTraitsT, FormatStringAllocatorT &gt; &amp;</paramtype></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default|format_default</default></parameter></signature><purpose>Replace all regex algorithm. </purpose><description><para>Format all substrings, matching given regex, with the specified format. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="replace_all_regex"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
          <template-type-parameter name="FormatStringTraitsT"/>
          <template-type-parameter name="FormatStringAllocatorT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Format"><paramtype>const std::basic_string&lt; CharT, FormatStringTraitsT, FormatStringAllocatorT &gt; &amp;</paramtype><description><para>Regex format definition </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default|format_default</default><description><para>Regex options </para></description></parameter><purpose>Replace all regex algorithm. </purpose><description><para>Format all substrings, matching given regex, with the specified format. The input string is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="erase_regex_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default><description><para>Regex options </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default></parameter></signature><purpose>Erase regex algorithm. </purpose><description><para>Remove a substring matching given regex from the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="erase_regex"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default><description><para>Regex options </para></description></parameter><purpose>Erase regex algorithm. </purpose><description><para>Remove a substring matching given regex from the input. The input string is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="erase_all_regex_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default><description><para>Regex options </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default></parameter></signature><purpose>Erase all regex algorithm. </purpose><description><para>Erase all substrings, matching given regex, from the input. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="erase_all_regex"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default><description><para>Regex options </para></description></parameter><purpose>Erase all regex algorithm. </purpose><description><para>Erase all substrings, matching given regex, from the input. The input string is modified in-place.</para><para>
</para></description></function>
<function name="find_all_regex"><type>SequenceSequenceT &amp;</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Result"><paramtype>SequenceSequenceT &amp;</paramtype><description><para>A container that can hold copies of references to the substrings. </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>A container which will be searched. </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default><description><para>Regex options </para></description></parameter><purpose>Find all regex algorithm. </purpose><description><para>This algorithm finds all substrings matching the give regex in the input.</para><para>Each part is copied and added as a new element to the output container. Thus the result container must be able to hold copies of the matches (in a compatible structure like std::string) or a reference to it (e.g. using the iterator range class). Examples of such a container are <computeroutput>std::vector&lt;std::string&gt;</computeroutput> or <computeroutput>std::list&lt;boost::iterator_range&lt;std::string::iterator&gt;&gt;</computeroutput> </para><para>

<note><para>Prior content of the result will be overwritten.</para>
</note>
<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>A reference to the result</para>
</returns></function>
<function name="split_regex"><type>SequenceSequenceT &amp;</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Result"><paramtype>SequenceSequenceT &amp;</paramtype><description><para>A container that can hold copies of references to the substrings.</para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>A container which will be searched. </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default><description><para>Regex options </para></description></parameter><purpose>Split regex algorithm. </purpose><description><para>Tokenize expression. This function is equivalent to C strtok. Input sequence is split into tokens, separated by separators. Separator is an every match of the given regex. Each part is copied and added as a new element to the output container. Thus the result container must be able to hold copies of the matches (in a compatible structure like std::string) or a reference to it (e.g. using the iterator range class). Examples of such a container are <computeroutput>std::vector&lt;std::string&gt;</computeroutput> or <computeroutput>std::list&lt;boost::iterator_range&lt;std::string::iterator&gt;&gt;</computeroutput> </para><para>

<note><para>Prior content of the result will be overwritten.</para>
</note>
<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>A reference to the result</para>
</returns></function>
<function name="join_if"><type>range_value&lt; SequenceSequenceT &gt;::type</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Input"><paramtype>const SequenceSequenceT &amp;</paramtype><description><para>A container that holds the input strings. It must be a container-of-containers. </para></description></parameter><parameter name="Separator"><paramtype>const Range1T &amp;</paramtype><description><para>A string that will separate the joined segments. </para></description></parameter><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>match_default</default><description><para>Regex options </para></description></parameter><purpose>Conditional join algorithm. </purpose><description><para>This algorithm joins all strings in a 'list' into one long string. Segments are concatenated by given separator. Only segments that match the given regular expression will be added to the result</para><para>This is a specialization of join_if algorithm.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>Concatenated string.</para>
</returns></function>










</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/regex_find_format.hpp">
<para>Defines the <computeroutput>regex_finder</computeroutput> and <computeroutput>regex_formatter</computeroutput> generators. These two functors are designed to work together. <computeroutput>regex_formatter</computeroutput> uses additional information about a match contained in the regex_finder search result. </para><namespace name="boost">
<namespace name="algorithm">
















































































































































































<function name="regex_finder"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="RegexTraitsT"/>
        </template><parameter name="Rx"><paramtype>const basic_regex&lt; CharT, RegexTraitsT &gt; &amp;</paramtype><description><para>A regular expression </para></description></parameter><parameter name="MatchFlags"><paramtype>match_flag_type</paramtype><default>match_default</default><description><para>Regex search options </para></description></parameter><purpose>"Regex" finder </purpose><description><para>Construct the <computeroutput>regex_finder</computeroutput>. Finder uses the regex engine to search for a match. Result is given in <computeroutput>regex_search_result</computeroutput>. This is an extension of the iterator_range. In addition it contains match results from the <computeroutput>regex_search</computeroutput> algorithm.</para><para>

</para></description><returns><para>An instance of the <computeroutput>regex_finder</computeroutput> object </para>
</returns></function>
<function name="regex_formatter"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="TraitsT"/>
          <template-type-parameter name="AllocT"/>
        </template><parameter name="Format"><paramtype>const std::basic_string&lt; CharT, TraitsT, AllocT &gt; &amp;</paramtype><description><para>Regex format definition </para></description></parameter><parameter name="Flags"><paramtype>match_flag_type</paramtype><default>format_default</default><description><para>Format flags </para></description></parameter><purpose>Regex formatter. </purpose><description><para>Construct the <computeroutput>regex_formatter</computeroutput>. Regex formatter uses the regex engine to format a match found by the <computeroutput>regex_finder</computeroutput>. This formatted it designed to closely cooperate with <computeroutput>regex_finder</computeroutput>.</para><para>

</para></description><returns><para>An instance of the <computeroutput>regex_formatter</computeroutput> functor </para>
</returns></function>








</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/replace.hpp">
<para>Defines various replace algorithms. Each algorithm replaces part(s) of the input according to set of searching and replace criteria. </para><namespace name="boost">
<namespace name="algorithm">





















































































































<overloaded-function name="replace_range_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="SearchRange"><paramtype>const iterator_range&lt; typename range_const_iterator&lt; Range1T &gt;::type &gt; &amp;</paramtype><description><para>A range in the input to be substituted </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="SearchRange"><paramtype>const iterator_range&lt; typename range_const_iterator&lt; SequenceT &gt;::type &gt; &amp;</paramtype></parameter><parameter name="Format"><paramtype>const RangeT &amp;</paramtype></parameter></signature><purpose>Replace range algorithm. </purpose><description><para>Replace the given range in the input string. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="replace_range"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="SearchRange"><paramtype>const iterator_range&lt; typename range_iterator&lt; SequenceT &gt;::type &gt; &amp;</paramtype><description><para>A range in the input to be substituted </para></description></parameter><parameter name="Format"><paramtype>const RangeT &amp;</paramtype><description><para>A substitute string </para></description></parameter><purpose>Replace range algorithm. </purpose><description><para>Replace the given range in the input string. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="replace_first_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="Range3T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range3T &amp;</paramtype><description><para>A substitute string </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype></parameter></signature><purpose>Replace first algorithm. </purpose><description><para>Replace the first match of the search substring in the input with the format string. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="replace_first"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter><purpose>Replace first algorithm. </purpose><description><para>replace the first match of the search substring in the input with the format string. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="ireplace_first_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="Range3T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range3T &amp;</paramtype><description><para>A substitute string </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="Range1T"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype></parameter><parameter name="Format"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Replace first algorithm ( case insensitive ) </purpose><description><para>Replace the first match of the search substring in the input with the format string. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator. Searching is case insensitive.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="ireplace_first"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Replace first algorithm ( case insensitive ) </purpose><description><para>Replace the first match of the search substring in the input with the format string. Input sequence is modified in-place. Searching is case insensitive.</para><para>
</para></description></function>
<overloaded-function name="replace_last_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="Range3T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range3T &amp;</paramtype><description><para>A substitute string </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype></parameter></signature><purpose>Replace last algorithm. </purpose><description><para>Replace the last match of the search string in the input with the format string. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="replace_last"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter><purpose>Replace last algorithm. </purpose><description><para>Replace the last match of the search string in the input with the format string. Input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="ireplace_last_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="Range3T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range3T &amp;</paramtype><description><para>A substitute string </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Replace last algorithm ( case insensitive ) </purpose><description><para>Replace the last match of the search string in the input with the format string. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator. Searching is case insensitive.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="ireplace_last"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Replace last algorithm ( case insensitive ) </purpose><description><para>Replace the last match of the search string in the input with the format string.The input sequence is modified in-place. Searching is case insensitive.</para><para>
</para></description></function>
<overloaded-function name="replace_nth_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="Range3T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index of the match to be replaced. The index is 0-based. For negative N, matches are counted from the end of string. </para></description></parameter><parameter name="Format"><paramtype>const Range3T &amp;</paramtype><description><para>A substitute string </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Nth"><paramtype>int</paramtype></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype></parameter></signature><purpose>Replace nth algorithm. </purpose><description><para>Replace an Nth (zero-indexed) match of the search string in the input with the format string. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="replace_nth"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index of the match to be replaced. The index is 0-based. For negative N, matches are counted from the end of string. </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter><purpose>Replace nth algorithm. </purpose><description><para>Replace an Nth (zero-indexed) match of the search string in the input with the format string. Input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="ireplace_nth_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="Range3T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index of the match to be replaced. The index is 0-based. For negative N, matches are counted from the end of string. </para></description></parameter><parameter name="Format"><paramtype>const Range3T &amp;</paramtype><description><para>A substitute string </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Nth"><paramtype>int</paramtype></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Replace nth algorithm ( case insensitive ) </purpose><description><para>Replace an Nth (zero-indexed) match of the search string in the input with the format string. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator. Searching is case insensitive.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="ireplace_nth"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Nth"><paramtype>int</paramtype><description><para>An index of the match to be replaced. The index is 0-based. For negative N, matches are counted from the end of string. </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Replace nth algorithm ( case insensitive ) </purpose><description><para>Replace an Nth (zero-indexed) match of the search string in the input with the format string. Input sequence is modified in-place. Searching is case insensitive.</para><para>
</para></description></function>
<overloaded-function name="replace_all_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="Range3T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range3T &amp;</paramtype><description><para>A substitute string </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype></parameter></signature><purpose>Replace all algorithm. </purpose><description><para>Replace all occurrences of the search string in the input with the format string. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="replace_all"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter><purpose>Replace all algorithm. </purpose><description><para>Replace all occurrences of the search string in the input with the format string. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="ireplace_all_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
          <template-type-parameter name="Range3T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range3T &amp;</paramtype><description><para>A substitute string </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default></parameter></signature><purpose>Replace all algorithm ( case insensitive ) </purpose><description><para>Replace all occurrences of the search string in the input with the format string. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator. Searching is case insensitive.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="ireplace_all"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="Search"><paramtype>const Range1T &amp;</paramtype><description><para>A substring to be searched for </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Replace all algorithm ( case insensitive ) </purpose><description><para>Replace all occurrences of the search string in the input with the format string.The input sequence is modified in-place. Searching is case insensitive.</para><para>
</para></description></function>
<overloaded-function name="replace_head_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the head. For N&gt;=0, at most N characters are extracted. For N&lt;0, size(Input)-|N| characters are extracted. </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="N"><paramtype>int</paramtype></parameter><parameter name="Format"><paramtype>const RangeT &amp;</paramtype></parameter></signature><purpose>Replace head algorithm. </purpose><description><para>Replace the head of the input with the given format string. The head is a prefix of a string of given size. If the sequence is shorter then required, whole string if considered to be the head. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="replace_head"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the head. For N&gt;=0, at most N characters are extracted. For N&lt;0, size(Input)-|N| characters are extracted. </para></description></parameter><parameter name="Format"><paramtype>const RangeT &amp;</paramtype><description><para>A substitute string </para></description></parameter><purpose>Replace head algorithm. </purpose><description><para>Replace the head of the input with the given format string. The head is a prefix of a string of given size. If the sequence is shorter then required, the whole string is considered to be the head. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="replace_tail_copy"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const Range1T &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the tail. For N&gt;=0, at most N characters are extracted. For N&lt;0, size(Input)-|N| characters are extracted. </para></description></parameter><parameter name="Format"><paramtype>const Range2T &amp;</paramtype><description><para>A substitute string </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="N"><paramtype>int</paramtype></parameter><parameter name="Format"><paramtype>const RangeT &amp;</paramtype></parameter></signature><purpose>Replace tail algorithm. </purpose><description><para>Replace the tail of the input with the given format string. The tail is a suffix of a string of given size. If the sequence is shorter then required, whole string is considered to be the tail. The result is a modified copy of the input. It is returned as a sequence or copied to the output iterator.</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a modified copy of the input</para>
</returns></overloaded-function>

<function name="replace_tail"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input string </para></description></parameter><parameter name="N"><paramtype>int</paramtype><description><para>Length of the tail. For N&gt;=0, at most N characters are extracted. For N&lt;0, size(Input)-|N| characters are extracted. </para></description></parameter><parameter name="Format"><paramtype>const RangeT &amp;</paramtype><description><para>A substitute string </para></description></parameter><purpose>Replace tail algorithm. </purpose><description><para>Replace the tail of the input with the given format sequence. The tail is a suffix of a string of given size. If the sequence is shorter then required, the whole string is considered to be the tail. The input sequence is modified in-place.</para><para>
</para></description></function>




































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/sequence_traits.hpp">
<para>Traits defined in this header are used by various algorithms to achieve better performance for specific containers. Traits provide fail-safe defaults. If a container supports some of these features, it is possible to specialize the specific trait for this container. For lacking compilers, it is possible of define an override for a specific tester function.</para><para>Due to a language restriction, it is not currently possible to define specializations for stl containers without including the corresponding header. To decrease the overhead needed by this inclusion, user can selectively include a specialization header for a specific container. They are located in boost/algorithm/string/stl directory. Alternatively she can include boost/algorithm/string/std_collection_traits.hpp header which contains specializations for all stl containers. </para><namespace name="boost">
<namespace name="algorithm">
<class name="has_const_time_erase"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Const time erase trait. </purpose><description><para>This trait specifies that the sequence's erase method has constant time complexity. </para></description><typedef name="type"><type>mpl::bool_&lt; <classname>has_const_time_erase</classname>&lt; T &gt;::value &gt;</type></typedef>
<data-member name="value" specifiers="static"><type>const bool</type></data-member>
</class><class name="has_const_time_insert"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Const time insert trait. </purpose><description><para>This trait specifies that the sequence's insert method has constant time complexity. </para></description><typedef name="type"><type>mpl::bool_&lt; <classname>has_const_time_insert</classname>&lt; T &gt;::value &gt;</type></typedef>
<data-member name="value" specifiers="static"><type>const bool</type></data-member>
</class><class name="has_native_replace"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Native replace trait. </purpose><description><para>This trait specifies that the sequence has <computeroutput>std::string</computeroutput> like replace method </para></description><typedef name="type"><type>mpl::bool_&lt; <classname>has_native_replace</classname>&lt; T &gt;::value &gt;</type></typedef>
<data-member name="value" specifiers="static"><type>const bool</type></data-member>
</class><class name="has_stable_iterators"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Stable iterators trait. </purpose><description><para>This trait specifies that the sequence has stable iterators. It means that operations like insert/erase/replace do not invalidate iterators. </para></description><typedef name="type"><type>mpl::bool_&lt; <classname>has_stable_iterators</classname>&lt; T &gt;::value &gt;</type></typedef>
<data-member name="value" specifiers="static"><type>const bool</type></data-member>
</class>

























































































































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/split.hpp">
<para>Defines basic split algorithms. Split algorithms can be used to divide a string into several parts according to given criteria.</para><para>Each part is copied and added as a new element to the output container. Thus the result container must be able to hold copies of the matches (in a compatible structure like std::string) or a reference to it (e.g. using the iterator range class). Examples of such a container are <computeroutput>std::vector&lt;std::string&gt;</computeroutput> or <computeroutput>std::list&lt;boost::iterator_range&lt;std::string::iterator&gt;&gt;</computeroutput> </para><namespace name="boost">
<namespace name="algorithm">













































































<function name="find_all"><type>SequenceSequenceT &amp;</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Result"><paramtype>SequenceSequenceT &amp;</paramtype><description><para>A container that can hold copies of references to the substrings </para></description></parameter><parameter name="Input"><paramtype>Range1T &amp;&amp;</paramtype><description><para>A container which will be searched. </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><purpose>Find all algorithm. </purpose><description><para>This algorithm finds all occurrences of the search string in the input.</para><para>Each part is copied and added as a new element to the output container. Thus the result container must be able to hold copies of the matches (in a compatible structure like std::string) or a reference to it (e.g. using the iterator range class). Examples of such a container are <computeroutput>std::vector&lt;std::string&gt;</computeroutput> or <computeroutput>std::list&lt;boost::iterator_range&lt;std::string::iterator&gt;&gt;</computeroutput> </para><para>

<note><para>Prior content of the result will be overwritten.</para>
</note>
<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>A reference the result</para>
</returns></function>
<function name="ifind_all"><type>SequenceSequenceT &amp;</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="Range1T"/>
          <template-type-parameter name="Range2T"/>
        </template><parameter name="Result"><paramtype>SequenceSequenceT &amp;</paramtype><description><para>A container that can hold copies of references to the substrings </para></description></parameter><parameter name="Input"><paramtype>Range1T &amp;&amp;</paramtype><description><para>A container which will be searched. </para></description></parameter><parameter name="Search"><paramtype>const Range2T &amp;</paramtype><description><para>A substring to be searched for. </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for case insensitive comparison </para></description></parameter><purpose>Find all algorithm ( case insensitive ) </purpose><description><para>This algorithm finds all occurrences of the search string in the input. Each part is copied and added as a new element to the output container. Thus the result container must be able to hold copies of the matches (in a compatible structure like std::string) or a reference to it (e.g. using the iterator range class). Examples of such a container are <computeroutput>std::vector&lt;std::string&gt;</computeroutput> or <computeroutput>std::list&lt;boost::iterator_range&lt;std::string::iterator&gt;&gt;</computeroutput> </para><para>Searching is case insensitive.</para><para>

<note><para>Prior content of the result will be overwritten.</para>
</note>
<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>A reference the result</para>
</returns></function>
<function name="split"><type>SequenceSequenceT &amp;</type><template>
          <template-type-parameter name="SequenceSequenceT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Result"><paramtype>SequenceSequenceT &amp;</paramtype><description><para>A container that can hold copies of references to the substrings</para></description></parameter><parameter name="Input"><paramtype>RangeT &amp;&amp;</paramtype><description><para>A container which will be searched. </para></description></parameter><parameter name="Pred"><paramtype>PredicateT</paramtype><description><para>A predicate to identify separators. This predicate is supposed to return true if a given element is a separator. </para></description></parameter><parameter name="eCompress"><paramtype>token_compress_mode_type</paramtype><default>token_compress_off</default><description><para>If eCompress argument is set to token_compress_on, adjacent separators are merged together. Otherwise, every two separators delimit a token. </para></description></parameter><purpose>Split algorithm. </purpose><description><para>Tokenize expression. This function is equivalent to C strtok. Input sequence is split into tokens, separated by separators. Separators are given by means of the predicate.</para><para>Each part is copied and added as a new element to the output container. Thus the result container must be able to hold copies of the matches (in a compatible structure like std::string) or a reference to it (e.g. using the iterator range class). Examples of such a container are <computeroutput>std::vector&lt;std::string&gt;</computeroutput> or <computeroutput>std::list&lt;boost::iterator_range&lt;std::string::iterator&gt;&gt;</computeroutput> </para><para>

<note><para>Prior content of the result will be overwritten.</para>
</note>
<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>A reference the result</para>
</returns></function>










































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/std_containers_traits.hpp">
<para>This file includes sequence traits for stl containers. </para></header>
<header name="boost/algorithm/string/trim.hpp">
<para>Defines trim algorithms. Trim algorithms are used to remove trailing and leading spaces from a sequence (string). Space is recognized using given locales.</para><para>Parametric (<computeroutput>_if</computeroutput>) variants use a predicate (functor) to select which characters are to be trimmed.. Functions take a selection predicate as a parameter, which is used to determine whether a character is a space. Common predicates are provided in classification.hpp header. </para><namespace name="boost">
<namespace name="algorithm">














































<overloaded-function name="trim_left_copy_if"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input range </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype></parameter></signature><purpose>Left trim - parametric. </purpose><description><para>Remove all leading spaces from the input. The supplied predicate is used to determine which characters are considered spaces. The result is a trimmed copy of the input. It is returned as a sequence or copied to the output iterator</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a copy of the input</para>
</returns></overloaded-function>

<function name="trim_left_copy"><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>a locale used for 'space' classification </para></description></parameter><purpose>Left trim - parametric. </purpose><description><para>Remove all leading spaces from the input. The result is a trimmed copy of the input.</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>A trimmed copy of the input</para>
</returns></function>
<function name="trim_left_if"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter><purpose>Left trim. </purpose><description><para>Remove all leading spaces from the input. The supplied predicate is used to determine which characters are considered spaces. The input sequence is modified in-place.</para><para>
</para></description></function>
<function name="trim_left"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for 'space' classification </para></description></parameter><purpose>Left trim. </purpose><description><para>Remove all leading spaces from the input. The Input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="trim_right_copy_if"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input range </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype></parameter></signature><purpose>Right trim - parametric. </purpose><description><para>Remove all trailing spaces from the input.</para><para>The supplied predicate is used to determine which characters are considered spaces. The result is a trimmed copy of the input. It is returned as a sequence or copied to the output iterator</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a copy of the input</para>
</returns></overloaded-function>

<function name="trim_right_copy"><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for 'space' classification </para></description></parameter><purpose>Right trim. </purpose><description><para>Remove all trailing spaces from the input. The result is a trimmed copy of the input</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>A trimmed copy of the input</para>
</returns></function>
<function name="trim_right_if"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter><purpose>Right trim - parametric. </purpose><description><para>Remove all trailing spaces from the input. The supplied predicate is used to determine which characters are considered spaces. The input sequence is modified in-place.</para><para>
</para></description></function>
<function name="trim_right"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for 'space' classification </para></description></parameter><purpose>Right trim. </purpose><description><para>Remove all trailing spaces from the input. The input sequence is modified in-place.</para><para>
</para></description></function>
<overloaded-function name="trim_copy_if"><signature><type>OutputIteratorT</type><template>
          <template-type-parameter name="OutputIteratorT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Output"><paramtype>OutputIteratorT</paramtype><description><para>An output iterator to which the result will be copied </para></description></parameter><parameter name="Input"><paramtype>const RangeT &amp;</paramtype><description><para>An input range </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter></signature><signature><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype></parameter></signature><purpose>Trim - parametric. </purpose><description><para>Remove all trailing and leading spaces from the input. The supplied predicate is used to determine which characters are considered spaces. The result is a trimmed copy of the input. It is returned as a sequence or copied to the output iterator</para><para>

<note><para>The second variant of this function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>An output iterator pointing just after the last inserted character or a copy of the input</para>
</returns></overloaded-function>

<function name="trim_copy"><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for 'space' classification </para></description></parameter><purpose>Trim. </purpose><description><para>Remove all leading and trailing spaces from the input. The result is a trimmed copy of the input</para><para>

<note><para>This function provides the strong exception-safety guarantee </para>
</note>
</para></description><returns><para>A trimmed copy of the input</para>
</returns></function>
<function name="trim_if"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter><purpose>Trim. </purpose><description><para>Remove all leading and trailing spaces from the input. The supplied predicate is used to determine which characters are considered spaces. The input sequence is modified in-place.</para><para>
</para></description></function>
<function name="trim"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for 'space' classification </para></description></parameter><purpose>Trim. </purpose><description><para>Remove all leading and trailing spaces from the input. The input sequence is modified in-place.</para><para>
</para></description></function>





























































































































</namespace>
</namespace>
</header>
<header name="boost/algorithm/string/trim_all.hpp">
<para>Defines trim_all algorithms.</para><para>Just like <computeroutput>trim</computeroutput>, <computeroutput>trim_all</computeroutput> removes all trailing and leading spaces from a sequence (string). In addition, spaces in the middle of the sequence are truncated to just one character. Space is recognized using given locales.</para><para><computeroutput>trim_fill</computeroutput> acts as trim_all, but the spaces in the middle are replaces with a user-define sequence of character.</para><para>Parametric (<computeroutput>_if</computeroutput>) variants use a predicate (functor) to select which characters are to be trimmed.. Functions take a selection predicate as a parameter, which is used to determine whether a character is a space. Common predicates are provided in classification.hpp header. </para><namespace name="boost">
<namespace name="algorithm">


















































































































































































<function name="trim_all_copy_if"><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter><purpose>Trim All - parametric. </purpose><description><para>Remove all leading and trailing spaces from the input and compress all other spaces to a single character. The result is a trimmed copy of the input</para><para>

</para></description><returns><para>A trimmed copy of the input </para>
</returns></function>
<function name="trim_all_if"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter><purpose>Trim All. </purpose><description><para>Remove all leading and trailing spaces from the input and compress all other spaces to a single character. The input sequence is modified in-place.</para><para>
</para></description></function>
<function name="trim_all_copy"><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for 'space' classification </para></description></parameter><purpose>Trim All. </purpose><description><para>Remove all leading and trailing spaces from the input and compress all other spaces to a single character. The result is a trimmed copy of the input</para><para>

</para></description><returns><para>A trimmed copy of the input </para>
</returns></function>
<function name="trim_all"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for 'space' classification </para></description></parameter><purpose>Trim All. </purpose><description><para>Remove all leading and trailing spaces from the input and compress all other spaces to a single character. The input sequence is modified in-place.</para><para>

</para></description><returns><para>A trimmed copy of the input </para>
</returns></function>
<function name="trim_fill_copy_if"><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Fill"><paramtype>const RangeT &amp;</paramtype><description><para>A string used to fill the inner spaces </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter><purpose>Trim Fill - parametric. </purpose><description><para>Remove all leading and trailing spaces from the input and replace all every block of consecutive spaces with a fill string defined by user. The result is a trimmed copy of the input</para><para>

</para></description><returns><para>A trimmed copy of the input </para>
</returns></function>
<function name="trim_fill_if"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
          <template-type-parameter name="PredicateT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Fill"><paramtype>const RangeT &amp;</paramtype><description><para>A string used to fill the inner spaces </para></description></parameter><parameter name="IsSpace"><paramtype>PredicateT</paramtype><description><para>A unary predicate identifying spaces </para></description></parameter><purpose>Trim Fill. </purpose><description><para>Remove all leading and trailing spaces from the input and replace all every block of consecutive spaces with a fill string defined by user. The input sequence is modified in-place.</para><para>
</para></description></function>
<function name="trim_fill_copy"><type>SequenceT</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>const SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Fill"><paramtype>const RangeT &amp;</paramtype><description><para>A string used to fill the inner spaces </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for 'space' classification </para></description></parameter><purpose>Trim Fill. </purpose><description><para>Remove all leading and trailing spaces from the input and replace all every block of consecutive spaces with a fill string defined by user. The result is a trimmed copy of the input</para><para>

</para></description><returns><para>A trimmed copy of the input </para>
</returns></function>
<function name="trim_fill"><type>void</type><template>
          <template-type-parameter name="SequenceT"/>
          <template-type-parameter name="RangeT"/>
        </template><parameter name="Input"><paramtype>SequenceT &amp;</paramtype><description><para>An input sequence </para></description></parameter><parameter name="Fill"><paramtype>const RangeT &amp;</paramtype><description><para>A string used to fill the inner spaces </para></description></parameter><parameter name="Loc"><paramtype>const std::locale &amp;</paramtype><default>std::locale()</default><description><para>A locale used for 'space' classification </para></description></parameter><purpose>Trim Fill. </purpose><description><para>Remove all leading and trailing spaces from the input and replace all every block of consecutive spaces with a fill string defined by user. The input sequence is modified in-place.</para><para>

</para></description><returns><para>A trimmed copy of the input </para>
</returns></function>
</namespace>
</namespace>
</header>
<header name="boost/algorithm/string_regex.hpp">
<para>Cumulative include for string_algo library. In addition to string.hpp contains also regex-related stuff. </para></header>
</library-reference>