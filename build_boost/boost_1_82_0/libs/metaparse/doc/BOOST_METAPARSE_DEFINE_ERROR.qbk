[#BOOST_METAPARSE_DEFINE_ERROR]
[section BOOST_METAPARSE_DEFINE_ERROR]

[h1 Synopsis]

  #define BOOST_METAPARSE_DEFINE_ERROR(name, msg) \
    // unspecified

This is a macro.

[table Arguments
  [[Name]   [Type]]
  [[`name`] [identifier token]]
  [[`msg`]  [string literal]]
]

[h1 Description]

Macro for defining a [link parsing_error_message parsing error message] class.
`name` is the name of the class representing the error message and `msg` is a
string literal containing the description of the error.

[h1 Header]

  #include <boost/metaparse/define_error.hpp>

[h1 Expression semantics]

For any `n` name and `m` string literal, given the following is defined:

  BOOST_METAPARSE_DEFINE_ERROR(n, m);

the following pairs of expressions are equivalent:

  n::get_value()
  std::string(m)

  n::type
  n

[h1 Example]

  #include <boost/metaparse/define_error.hpp>
  #include <boost/metaparse/repeated1.hpp>
  #include <boost/metaparse/letter.hpp>
  #include <boost/metaparse/int_.hpp>
  #include <boost/metaparse/token.hpp>
  #include <boost/metaparse/sequence.hpp>
  #include <boost/metaparse/change_error_message.hpp>
  #include <boost/metaparse/start.hpp>
  #include <boost/metaparse/get_message.hpp>
  #include <boost/metaparse/string.hpp>
  
  #include <type_traits>
  
  using namespace boost::metaparse;
  
  BOOST_METAPARSE_DEFINE_ERROR(age_expected, "Age expected");
  
  using name_token = token<repeated1<letter>>;
  using age_token = token<change_error_message<int_, age_expected>>;
  
  using name_age = sequence<name_token, age_token>;
  
  static_assert(
    std::is_same<
      age_expected,
      get_message<name_age::apply<BOOST_METAPARSE_STRING("Joe "), start>>::type
    >::type::value,
    "the error message should be age_expected when the age is missing"
  );

[endsect]

