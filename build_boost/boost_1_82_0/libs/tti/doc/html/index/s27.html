<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Index</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. The Type Traits Introspection Library">
<link rel="up" href="../index.html" title="Chapter 1. The Type Traits Introspection Library">
<link rel="prev" href="../the_type_traits_introspection_library/tti_acknowledgments.html" title="Acknowledgments">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../the_type_traits_introspection_library/tti_acknowledgments.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="idm14312"></a>Index</h2></div></div></div>
<p><a class="link" href="s27.html#idx_id_0">A</a> <a class="link" href="s27.html#idx_id_1">B</a> <a class="link" href="s27.html#idx_id_2">E</a> <a class="link" href="s27.html#idx_id_3">G</a> <a class="link" href="s27.html#idx_id_4">H</a> <a class="link" href="s27.html#idx_id_5">I</a> <a class="link" href="s27.html#idx_id_6">M</a> <a class="link" href="s27.html#idx_id_7">N</a> <a class="link" href="s27.html#idx_id_8">S</a> <a class="link" href="s27.html#idx_id_9">T</a> <a class="link" href="s27.html#idx_id_10">U</a> <a class="link" href="s27.html#idx_id_11">V</a></p>
<div class="variablelist"><dl class="variablelist">
<dt>
<a name="idx_id_0"></a><span class="term">A</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">An example using the macro metafunctions</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_UNION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_1"></a><span class="term">B</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_CLASS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_class_hpp.html" title="Header &lt;boost/tti/has_class.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_class.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_CLASS.html" title="Macro BOOST_TTI_HAS_CLASS"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_CLASS_GEN.html" title="Macro BOOST_TTI_HAS_CLASS_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_CLASS_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type.tbspinner" title="Table 1.3. TTI Specific Inner Types"><span class="index-entry-level-1">TTI Specific Inner Types</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_CLASS_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../reference.html#header.boost.tti.gen.has_class_gen_hpp" title="Header &lt;boost/tti/gen/has_class_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_class_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_CLASS_GEN.html" title="Macro BOOST_TTI_HAS_CLASS_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_CLASS_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_data_hpp.html" title="Header &lt;boost/tti/has_data.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_data.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_data.html" title="Introspecting data"><span class="index-entry-level-1">Introspecting data</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_DATA.html" title="Macro BOOST_TTI_HAS_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_DATA_GEN.html" title="Macro BOOST_TTI_HAS_DATA_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_DATA_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_DATA_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_data_gen_hpp.html" title="Header &lt;boost/tti/gen/has_data_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_data_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_DATA_GEN.html" title="Macro BOOST_TTI_HAS_DATA_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_DATA_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_ENUM</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_enum_hpp.html" title="Header &lt;boost/tti/has_enum.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_enum.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_ENUM.html" title="Macro BOOST_TTI_HAS_ENUM"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_ENUM_GEN.html" title="Macro BOOST_TTI_HAS_ENUM_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_ENUM_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type.tbspinner" title="Table 1.3. TTI Specific Inner Types"><span class="index-entry-level-1">TTI Specific Inner Types</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_ENUM_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_enum_gen_hpp.html" title="Header &lt;boost/tti/gen/has_enum_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_enum_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_ENUM_GEN.html" title="Macro BOOST_TTI_HAS_ENUM_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_ENUM_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_function_hpp.html" title="Header &lt;boost/tti/has_function.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_function.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_function.html" title="Introspecting function"><span class="index-entry-level-1">Introspecting function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNCTION.html" title="Macro BOOST_TTI_HAS_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNCTION_GEN.html" title="Macro BOOST_TTI_HAS_FUNCTION_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_FUNCTION_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_FUNCTION_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_function_gen_hpp.html" title="Header &lt;boost/tti/gen/has_function_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_function_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNCTION_GEN.html" title="Macro BOOST_TTI_HAS_FUNCTION_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_FUNCTION_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_function_template_hpp.html" title="Header &lt;boost/tti/has_function_template.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_function_template.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_function_template.html" title="Introspecting function template"><span class="index-entry-level-1">Introspecting function template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNC_idm6162.html" title="Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNC_idm5967.html" title="Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_function_template_gen_hpp.html" title="Header &lt;boost/tti/gen/has_function_template_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_function_template_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_function_template.html" title="Introspecting function template"><span class="index-entry-level-1">Introspecting function template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNC_idm5967.html" title="Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_MEMBER_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_enclosing_type.html" title="Enclosing Type"><span class="index-entry-level-1">Enclosing Type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_data_hpp.html" title="Header &lt;boost/tti/has_member_data.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_member_data.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">History</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_data.html" title="Introspecting member data"><span class="index-entry-level-1">Introspecting member data</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMBER_DATA.html" title="Macro BOOST_TTI_HAS_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5974.html" title="Macro BOOST_TTI_HAS_MEMBER_DATA_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_DATA_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_func_sig.html" title="Nested Types and Function Signatures"><span class="index-entry-level-1">Nested Types and Function Signatures</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_MEMBER_DATA_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_member_data_gen_hpp.html" title="Header &lt;boost/tti/gen/has_member_data_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_member_data_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5974.html" title="Macro BOOST_TTI_HAS_MEMBER_DATA_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_DATA_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_MEMBER_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_enclosing_type.html" title="Enclosing Type"><span class="index-entry-level-1">Enclosing Type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_function_hpp.html" title="Header &lt;boost/tti/has_member_function.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_member_function.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">History</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_function.html" title="Introspecting member function"><span class="index-entry-level-1">Introspecting member function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm6210.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5981.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_FUNCTION_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html#the_type_traits_introspection_library.tti_functionality.tti_functionality_nm_gen" title="Macro metafunction name generation considerations"><span class="index-entry-level-1">Macro metafunction name generation considerations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_func_sig.html" title="Nested Types and Function Signatures"><span class="index-entry-level-1">Nested Types and Function Signatures</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_MEMBER_FUNCTION_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_member_function_gen_hpp.html" title="Header &lt;boost/tti/gen/has_member_function_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_member_function_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5981.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_FUNCTION_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_enclosing_type.html" title="Enclosing Type"><span class="index-entry-level-1">Enclosing Type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_function_template_hpp.html" title="Header &lt;boost/tti/has_member_function_template.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_member_function_template.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_function_template.html" title="Introspecting member function template"><span class="index-entry-level-1">Introspecting member function template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm6237.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5988.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_member_function_template_gen_hpp.html" title="Header &lt;boost/tti/gen/has_member_function_template_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_member_function_template_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_function_template.html" title="Introspecting member function template"><span class="index-entry-level-1">Introspecting member function template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5988.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_data_hpp.html" title="Header &lt;boost/tti/has_static_member_data.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_static_member_data.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">History</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_data.html" title="Introspecting static member data"><span class="index-entry-level-1">Introspecting static member data</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6264.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm5995.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html" title="Nested Types"><span class="index-entry-level-1">Nested Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_static_member_data_gen_hpp.html" title="Header &lt;boost/tti/gen/has_static_member_data_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_static_member_data_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm5995.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_function_hpp.html" title="Header &lt;boost/tti/has_static_member_function.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_static_member_function.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">History</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_function.html" title="Introspecting static member function"><span class="index-entry-level-1">Introspecting static member function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6285.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6002.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_func_sig.html" title="Nested Types and Function Signatures"><span class="index-entry-level-1">Nested Types and Function Signatures</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_static_member_function_gen_hpp.html" title="Header &lt;boost/tti/gen/has_static_member_function_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_static_member_function_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6002.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_function_template_hpp.html" title="Header &lt;boost/tti/has_static_member_function_template.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_static_member_function_template.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_function_template.html" title="Introspecting static member function template"><span class="index-entry-level-1">Introspecting static member function template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6312.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6009.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_static_member_function_template_gen_hpp.html" title="Header &lt;boost/tti/gen/has_static_member_function_template_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_static_member_function_template_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_function_template.html" title="Introspecting static member function template"><span class="index-entry-level-1">Introspecting static member function template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6009.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_template_hpp.html" title="Header &lt;boost/tti/has_template.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_template.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_function_templates.html" title="Introspecting function templates technique"><span class="index-entry-level-1">Introspecting function templates technique</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TEMPLATE.html" title="Macro BOOST_TTI_HAS_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TEMPLATE_GEN.html" title="Macro BOOST_TTI_HAS_TEMPLATE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_TEMPLATE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_template.html#the_type_traits_introspection_library.tti_detail_has_template.tti_detail_has_template_macro" title="Using the BOOST_TTI_HAS_TEMPLATE macro"><span class="index-entry-level-1">Using the BOOST_TTI_HAS_TEMPLATE macro</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_template/tti_detail_has_template_metafunction.html" title="Using the has_template_(xxx) metafunction"><span class="index-entry-level-1">Using the has_template_(xxx) metafunction</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_TEMPLATE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_template_gen_hpp.html" title="Header &lt;boost/tti/gen/has_template_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_template_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TEMPLATE_GEN.html" title="Macro BOOST_TTI_HAS_TEMPLATE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_TEMPLATE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_TYPE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html" title="General Functionality"><span class="index-entry-level-1">General Functionality</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_type_hpp.html" title="Header &lt;boost/tti/has_type.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_type.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">History</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html" title="Introspecting an inner type"><span class="index-entry-level-1">Introspecting an inner type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TYPE.html" title="Macro BOOST_TTI_HAS_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TYPE_GEN.html" title="Macro BOOST_TTI_HAS_TYPE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_TYPE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html#the_type_traits_introspection_library.tti_functionality.tti_functionality_nm_gen" title="Macro metafunction name generation considerations"><span class="index-entry-level-1">Macro metafunction name generation considerations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html" title="Nested Types"><span class="index-entry-level-1">Nested Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_TYPE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html" title="General Functionality"><span class="index-entry-level-1">General Functionality</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_type_gen_hpp.html" title="Header &lt;boost/tti/gen/has_type_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_type_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TYPE_GEN.html" title="Macro BOOST_TTI_HAS_TYPE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_TYPE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_UNION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_union_hpp.html" title="Header &lt;boost/tti/has_union.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_union.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_UNION.html" title="Macro BOOST_TTI_HAS_UNION"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_UNION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_UNION_GEN.html" title="Macro BOOST_TTI_HAS_UNION_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_UNION_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">TTI Macro Metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type.tbspinner" title="Table 1.3. TTI Specific Inner Types"><span class="index-entry-level-1">TTI Specific Inner Types</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_HAS_UNION_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_union_gen_hpp.html" title="Header &lt;boost/tti/gen/has_union_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/has_union_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_UNION_GEN.html" title="Macro BOOST_TTI_HAS_UNION_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_UNION_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">boost_tti_marker_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE.html" title="Macro BOOST_TTI_MEMBER_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_ME_idm6453.html" title="Macro BOOST_TTI_TRAIT_MEMBER_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_MEMBER_TYPE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_MEMBER_TYPE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_usingMM.html" title="An example using the macro metafunctions"><span class="index-entry-level-1">An example using the macro metafunctions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_enclosing_type.html" title="Enclosing Type"><span class="index-entry-level-1">Enclosing Type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/member_type_hpp.html" title="Header &lt;boost/tti/member_type.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/member_type.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">History</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE.html" title="Macro BOOST_TTI_MEMBER_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE_GEN.html" title="Macro BOOST_TTI_MEMBER_TYPE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_MEMBER_TYPE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html" title="Nested Types"><span class="index-entry-level-1">Nested Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_func_sig.html" title="Nested Types and Function Signatures"><span class="index-entry-level-1">Nested Types and Function Signatures</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_metafunction.html" title="Struct template valid_member_metafunction"><span class="index-entry-level-1">Struct template valid_member_metafunction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">Struct template valid_member_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html#the_type_traits_introspection_library.tti_nested_type.tbmacronested" title="Table 1.4. TTI Nested Type Macro Metafunction"><span class="index-entry-level-1">TTI Nested Type Macro Metafunction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html#the_type_traits_introspection_library.tti_nested_type.existtbmacronested" title="Table 1.5. TTI Nested Type Macro Metafunction Existence"><span class="index-entry-level-1">TTI Nested Type Macro Metafunction Existence</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_MEMBER_TYPE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/member_type_gen_hpp.html" title="Header &lt;boost/tti/gen/member_type_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/member_type_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE_GEN.html" title="Macro BOOST_TTI_MEMBER_TYPE_GEN"><span class="index-entry-level-1">Macro BOOST_TTI_MEMBER_TYPE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_NAMESPACE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/namespace_gen_hpp.html" title="Header &lt;boost/tti/gen/namespace_gen.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/gen/namespace_gen.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/member_type_hpp.html" title="Header &lt;boost/tti/member_type.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/member_type.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_NAMESPACE.html" title="Macro BOOST_TTI_NAMESPACE"><span class="index-entry-level-1">Macro BOOST_TTI_NAMESPACE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">Struct template valid_member_type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_CLASS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_class_hpp.html" title="Header &lt;boost/tti/has_class.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_class.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type" title="Introspecting a specific user-defined type"><span class="index-entry-level-1">Introspecting a specific user-defined type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_CLASS.html" title="Macro BOOST_TTI_TRAIT_HAS_CLASS"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_CLASS</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_data_hpp.html" title="Header &lt;boost/tti/has_data.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_data.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_DATA.html" title="Macro BOOST_TTI_TRAIT_HAS_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_DATA</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_ENUM</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_enum_hpp.html" title="Header &lt;boost/tti/has_enum.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_enum.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type" title="Introspecting a specific user-defined type"><span class="index-entry-level-1">Introspecting a specific user-defined type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_ENUM.html" title="Macro BOOST_TTI_TRAIT_HAS_ENUM"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_ENUM</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_function_hpp.html" title="Header &lt;boost/tti/has_function.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_function.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6124.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_FUNCTION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_function_template_hpp.html" title="Header &lt;boost/tti/has_function_template.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_function_template.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_function_template.html" title="Introspecting function template"><span class="index-entry-level-1">Introspecting function template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6145.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_MEMBER_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_data_hpp.html" title="Header &lt;boost/tti/has_member_data.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_member_data.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6178.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_function_hpp.html" title="Header &lt;boost/tti/has_member_function.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_member_function.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6199.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_function_template_hpp.html" title="Header &lt;boost/tti/has_member_function_template.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_member_function_template.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_function_template.html" title="Introspecting member function template"><span class="index-entry-level-1">Introspecting member function template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6220.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_data_hpp.html" title="Header &lt;boost/tti/has_static_member_data.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_static_member_data.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6253.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_function_hpp.html" title="Header &lt;boost/tti/has_static_member_function.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_static_member_function.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6274.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_function_template_hpp.html" title="Header &lt;boost/tti/has_static_member_function_template.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_static_member_function_template.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_function_template.html" title="Introspecting static member function template"><span class="index-entry-level-1">Introspecting static member function template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6295.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_template_hpp.html" title="Header &lt;boost/tti/has_template.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_template.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6328.html" title="Macro BOOST_TTI_TRAIT_HAS_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_template/tti_detail_has_template_metafunction.html" title="Using the has_template_(xxx) metafunction"><span class="index-entry-level-1">Using the has_template_(xxx) metafunction</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_TYPE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html" title="General Functionality"><span class="index-entry-level-1">General Functionality</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_type_hpp.html" title="Header &lt;boost/tti/has_type.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_type.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_TYPE.html" title="Macro BOOST_TTI_TRAIT_HAS_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_TYPE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_HAS_UNION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_union_hpp.html" title="Header &lt;boost/tti/has_union.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/has_union.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type" title="Introspecting a specific user-defined type"><span class="index-entry-level-1">Introspecting a specific user-defined type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_UNION.html" title="Macro BOOST_TTI_TRAIT_HAS_UNION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_UNION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_TTI_TRAIT_MEMBER_TYPE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/member_type_hpp.html" title="Header &lt;boost/tti/member_type.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/tti/member_type.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_ME_idm6453.html" title="Macro BOOST_TTI_TRAIT_MEMBER_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html" title="Nested Types"><span class="index-entry-level-1">Nested Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_metafunction.html" title="Struct template valid_member_metafunction"><span class="index-entry-level-1">Struct template valid_member_metafunction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">Struct template valid_member_type</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_2"></a><span class="term">E</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Enclosing Type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_enclosing_type.html" title="Enclosing Type"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_enclosing_type.html" title="Enclosing Type"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_enclosing_type.html" title="Enclosing Type"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_enclosing_type.html" title="Enclosing Type"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_3"></a><span class="term">G</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">General Functionality</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html" title="General Functionality"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html" title="General Functionality"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html" title="General Functionality"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_TYPE</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_4"></a><span class="term">H</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_class_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../reference.html#header.boost.tti.gen.has_class_gen_hpp" title="Header &lt;boost/tti/gen/has_class_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_CLASS_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_data_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_data_gen_hpp.html" title="Header &lt;boost/tti/gen/has_data_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_DATA_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_enum_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_enum_gen_hpp.html" title="Header &lt;boost/tti/gen/has_enum_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_ENUM_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_function_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_function_gen_hpp.html" title="Header &lt;boost/tti/gen/has_function_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_function_template_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_function_template_gen_hpp.html" title="Header &lt;boost/tti/gen/has_function_template_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_member_data_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_member_data_gen_hpp.html" title="Header &lt;boost/tti/gen/has_member_data_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_member_function_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_member_function_gen_hpp.html" title="Header &lt;boost/tti/gen/has_member_function_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_member_function_template_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_member_function_template_gen_hpp.html" title="Header &lt;boost/tti/gen/has_member_function_template_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_static_member_data_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_static_member_data_gen_hpp.html" title="Header &lt;boost/tti/gen/has_static_member_data_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_static_member_function_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_static_member_function_gen_hpp.html" title="Header &lt;boost/tti/gen/has_static_member_function_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_static_member_function_template_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_static_member_function_template_gen_hpp.html" title="Header &lt;boost/tti/gen/has_static_member_function_template_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_template_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_template_gen_hpp.html" title="Header &lt;boost/tti/gen/has_template_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_type_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_type_gen_hpp.html" title="Header &lt;boost/tti/gen/has_type_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/has_union_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/has_union_gen_hpp.html" title="Header &lt;boost/tti/gen/has_union_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_UNION_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/member_type_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/member_type_gen_hpp.html" title="Header &lt;boost/tti/gen/member_type_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE_GEN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/gen/namespace_gen.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/gen/namespace_gen_hpp.html" title="Header &lt;boost/tti/gen/namespace_gen.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_NAMESPACE</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_class.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_class_hpp.html" title="Header &lt;boost/tti/has_class.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_class_hpp.html" title="Header &lt;boost/tti/has_class.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_CLASS</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_data.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_data_hpp.html" title="Header &lt;boost/tti/has_data.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_data_hpp.html" title="Header &lt;boost/tti/has_data.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_DATA</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_enum.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_enum_hpp.html" title="Header &lt;boost/tti/has_enum.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_enum_hpp.html" title="Header &lt;boost/tti/has_enum.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_ENUM</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_function.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_function_hpp.html" title="Header &lt;boost/tti/has_function.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_function_hpp.html" title="Header &lt;boost/tti/has_function.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_FUNCTION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_function_template.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_function_template_hpp.html" title="Header &lt;boost/tti/has_function_template.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_function_template_hpp.html" title="Header &lt;boost/tti/has_function_template.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_member_data.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_data_hpp.html" title="Header &lt;boost/tti/has_member_data.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_data_hpp.html" title="Header &lt;boost/tti/has_member_data.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_MEMBER_DATA</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_member_function.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_function_hpp.html" title="Header &lt;boost/tti/has_member_function.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_function_hpp.html" title="Header &lt;boost/tti/has_member_function.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_member_function_template.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_function_template_hpp.html" title="Header &lt;boost/tti/has_member_function_template.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_member_function_template_hpp.html" title="Header &lt;boost/tti/has_member_function_template.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_static_member_data.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_data_hpp.html" title="Header &lt;boost/tti/has_static_member_data.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_data_hpp.html" title="Header &lt;boost/tti/has_static_member_data.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_static_member_function.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_function_hpp.html" title="Header &lt;boost/tti/has_static_member_function.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_function_hpp.html" title="Header &lt;boost/tti/has_static_member_function.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_static_member_function_template.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_function_template_hpp.html" title="Header &lt;boost/tti/has_static_member_function_template.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_static_member_function_template_hpp.html" title="Header &lt;boost/tti/has_static_member_function_template.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_template.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_template_hpp.html" title="Header &lt;boost/tti/has_template.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_template_hpp.html" title="Header &lt;boost/tti/has_template.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_TEMPLATE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_type.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_type_hpp.html" title="Header &lt;boost/tti/has_type.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_type_hpp.html" title="Header &lt;boost/tti/has_type.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_TYPE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/has_union.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_union_hpp.html" title="Header &lt;boost/tti/has_union.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_HAS_UNION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/has_union_hpp.html" title="Header &lt;boost/tti/has_union.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_UNION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/tti/member_type.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/member_type_hpp.html" title="Header &lt;boost/tti/member_type.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/member_type_hpp.html" title="Header &lt;boost/tti/member_type.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_NAMESPACE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../header/boost/tti/member_type_hpp.html" title="Header &lt;boost/tti/member_type.hpp&gt;"><span class="index-entry-level-1">BOOST_TTI_TRAIT_MEMBER_TYPE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">History</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_history.html" title="History"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_5"></a><span class="term">I</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting a specific user-defined type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type" title="Introspecting a specific user-defined type"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type" title="Introspecting a specific user-defined type"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type" title="Introspecting a specific user-defined type"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_UNION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting an inner type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html" title="Introspecting an inner type"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting data</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_data.html" title="Introspecting data"><span class="index-entry-level-1">BOOST_TTI_HAS_DATA</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting function</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_function.html" title="Introspecting function"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting function template</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_function_template.html" title="Introspecting function template"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_function_template.html" title="Introspecting function template"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_function_template.html" title="Introspecting function template"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting function templates technique</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_function_templates.html" title="Introspecting function templates technique"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting member data</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_data.html" title="Introspecting member data"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting member function</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_function.html" title="Introspecting member function"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting member function template</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_function_template.html" title="Introspecting member function template"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_function_template.html" title="Introspecting member function template"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_member_function_template.html" title="Introspecting member function template"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting static member data</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_data.html" title="Introspecting static member data"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting static member function</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_function.html" title="Introspecting static member function"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introspecting static member function template</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_function_template.html" title="Introspecting static member function template"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_function_template.html" title="Introspecting static member function template"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_static_member_function_template.html" title="Introspecting static member function template"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_6"></a><span class="term">M</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_CLASS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_CLASS.html" title="Macro BOOST_TTI_HAS_CLASS"><span class="index-entry-level-1">BOOST_TTI_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_CLASS.html" title="Macro BOOST_TTI_HAS_CLASS"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_CLASS_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_CLASS_GEN.html" title="Macro BOOST_TTI_HAS_CLASS_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_CLASS_GEN.html" title="Macro BOOST_TTI_HAS_CLASS_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_CLASS_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_DATA.html" title="Macro BOOST_TTI_HAS_DATA"><span class="index-entry-level-1">BOOST_TTI_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_DATA.html" title="Macro BOOST_TTI_HAS_DATA"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_DATA_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_DATA_GEN.html" title="Macro BOOST_TTI_HAS_DATA_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_DATA_GEN.html" title="Macro BOOST_TTI_HAS_DATA_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_DATA_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_ENUM</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_ENUM.html" title="Macro BOOST_TTI_HAS_ENUM"><span class="index-entry-level-1">BOOST_TTI_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_ENUM.html" title="Macro BOOST_TTI_HAS_ENUM"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_ENUM_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_ENUM_GEN.html" title="Macro BOOST_TTI_HAS_ENUM_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_ENUM_GEN.html" title="Macro BOOST_TTI_HAS_ENUM_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_ENUM_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNCTION.html" title="Macro BOOST_TTI_HAS_FUNCTION"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNCTION.html" title="Macro BOOST_TTI_HAS_FUNCTION"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_FUNCTION_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNCTION_GEN.html" title="Macro BOOST_TTI_HAS_FUNCTION_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNCTION_GEN.html" title="Macro BOOST_TTI_HAS_FUNCTION_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNC_idm6162.html" title="Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNC_idm6162.html" title="Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNC_idm5967.html" title="Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNC_idm5967.html" title="Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_TEMPLATE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_MEMBER_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMBER_DATA.html" title="Macro BOOST_TTI_HAS_MEMBER_DATA"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMBER_DATA.html" title="Macro BOOST_TTI_HAS_MEMBER_DATA"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_MEMBER_DATA_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5974.html" title="Macro BOOST_TTI_HAS_MEMBER_DATA_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5974.html" title="Macro BOOST_TTI_HAS_MEMBER_DATA_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_MEMBER_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm6210.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm6210.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_MEMBER_FUNCTION_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5981.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5981.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm6237.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm6237.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5988.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm5988.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6264.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6264.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm5995.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm5995.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6285.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6285.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6002.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6002.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6312.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6312.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6009.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6009.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TEMPLATE.html" title="Macro BOOST_TTI_HAS_TEMPLATE"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TEMPLATE.html" title="Macro BOOST_TTI_HAS_TEMPLATE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_TEMPLATE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TEMPLATE_GEN.html" title="Macro BOOST_TTI_HAS_TEMPLATE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TEMPLATE_GEN.html" title="Macro BOOST_TTI_HAS_TEMPLATE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_TYPE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TYPE.html" title="Macro BOOST_TTI_HAS_TYPE"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TYPE.html" title="Macro BOOST_TTI_HAS_TYPE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_TYPE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TYPE_GEN.html" title="Macro BOOST_TTI_HAS_TYPE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TYPE_GEN.html" title="Macro BOOST_TTI_HAS_TYPE_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_UNION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_UNION.html" title="Macro BOOST_TTI_HAS_UNION"><span class="index-entry-level-1">BOOST_TTI_HAS_UNION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_UNION.html" title="Macro BOOST_TTI_HAS_UNION"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_HAS_UNION_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_UNION_GEN.html" title="Macro BOOST_TTI_HAS_UNION_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_UNION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_UNION_GEN.html" title="Macro BOOST_TTI_HAS_UNION_GEN"><span class="index-entry-level-1">BOOST_TTI_HAS_UNION_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_MEMBER_TYPE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE.html" title="Macro BOOST_TTI_MEMBER_TYPE"><span class="index-entry-level-1">boost_tti_marker_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE.html" title="Macro BOOST_TTI_MEMBER_TYPE"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE.html" title="Macro BOOST_TTI_MEMBER_TYPE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_MEMBER_TYPE_GEN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE_GEN.html" title="Macro BOOST_TTI_MEMBER_TYPE_GEN"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE_GEN.html" title="Macro BOOST_TTI_MEMBER_TYPE_GEN"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE_GEN</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_NAMESPACE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_NAMESPACE.html" title="Macro BOOST_TTI_NAMESPACE"><span class="index-entry-level-1">BOOST_TTI_NAMESPACE</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_CLASS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_CLASS.html" title="Macro BOOST_TTI_TRAIT_HAS_CLASS"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_CLASS.html" title="Macro BOOST_TTI_TRAIT_HAS_CLASS"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_CLASS.html" title="Macro BOOST_TTI_TRAIT_HAS_CLASS"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_DATA.html" title="Macro BOOST_TTI_TRAIT_HAS_DATA"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_DATA.html" title="Macro BOOST_TTI_TRAIT_HAS_DATA"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_DATA.html" title="Macro BOOST_TTI_TRAIT_HAS_DATA"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_ENUM</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_ENUM.html" title="Macro BOOST_TTI_TRAIT_HAS_ENUM"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_ENUM.html" title="Macro BOOST_TTI_TRAIT_HAS_ENUM"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_ENUM.html" title="Macro BOOST_TTI_TRAIT_HAS_ENUM"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6124.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6124.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6124.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6145.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6145.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6145.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6178.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6178.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6178.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6199.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6199.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6199.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6220.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6220.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6220.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6253.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6253.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6253.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6274.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6274.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6274.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6295.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6295.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6295.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_TEMPLATE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6328.html" title="Macro BOOST_TTI_TRAIT_HAS_TEMPLATE"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6328.html" title="Macro BOOST_TTI_TRAIT_HAS_TEMPLATE"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6328.html" title="Macro BOOST_TTI_TRAIT_HAS_TEMPLATE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_TYPE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_TYPE.html" title="Macro BOOST_TTI_TRAIT_HAS_TYPE"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_TYPE.html" title="Macro BOOST_TTI_TRAIT_HAS_TYPE"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_TYPE.html" title="Macro BOOST_TTI_TRAIT_HAS_TYPE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_HAS_UNION</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_UNION.html" title="Macro BOOST_TTI_TRAIT_HAS_UNION"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_UNION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_UNION.html" title="Macro BOOST_TTI_TRAIT_HAS_UNION"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_UNION.html" title="Macro BOOST_TTI_TRAIT_HAS_UNION"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_TTI_TRAIT_MEMBER_TYPE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_ME_idm6453.html" title="Macro BOOST_TTI_TRAIT_MEMBER_TYPE"><span class="index-entry-level-1">boost_tti_marker_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_ME_idm6453.html" title="Macro BOOST_TTI_TRAIT_MEMBER_TYPE"><span class="index-entry-level-1">BOOST_TTI_TRAIT_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_ME_idm6453.html" title="Macro BOOST_TTI_TRAIT_MEMBER_TYPE"><span class="index-entry-level-1">trait</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_ME_idm6453.html" title="Macro BOOST_TTI_TRAIT_MEMBER_TYPE"><span class="index-entry-level-1">type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro metafunction name generation considerations</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html#the_type_traits_introspection_library.tti_functionality.tti_functionality_nm_gen" title="Macro metafunction name generation considerations"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_functionality.html#the_type_traits_introspection_library.tti_functionality.tti_functionality_nm_gen" title="Macro metafunction name generation considerations"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_7"></a><span class="term">N</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Nested Types</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html" title="Nested Types"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html" title="Nested Types"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html" title="Nested Types"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html" title="Nested Types"><span class="index-entry-level-1">BOOST_TTI_TRAIT_MEMBER_TYPE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Nested Types and Function Signatures</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_func_sig.html" title="Nested Types and Function Signatures"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_func_sig.html" title="Nested Types and Function Signatures"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_func_sig.html" title="Nested Types and Function Signatures"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_func_sig.html" title="Nested Types and Function Signatures"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_8"></a><span class="term">S</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Struct template valid_member_metafunction</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_metafunction.html" title="Struct template valid_member_metafunction"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_metafunction.html" title="Struct template valid_member_metafunction"><span class="index-entry-level-1">BOOST_TTI_TRAIT_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_metafunction.html" title="Struct template valid_member_metafunction"><span class="index-entry-level-1">type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_metafunction.html" title="Struct template valid_member_metafunction"><span class="index-entry-level-1">valid_member_metafunction</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Struct template valid_member_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">BOOST_TTI_NAMESPACE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">BOOST_TTI_TRAIT_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">valid_member_type</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_9"></a><span class="term">T</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">trait</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_CLASS.html" title="Macro BOOST_TTI_TRAIT_HAS_CLASS"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_DATA.html" title="Macro BOOST_TTI_TRAIT_HAS_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_ENUM.html" title="Macro BOOST_TTI_TRAIT_HAS_ENUM"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6124.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6145.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6178.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6199.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6220.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6253.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6274.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6295.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6328.html" title="Macro BOOST_TTI_TRAIT_HAS_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_TYPE.html" title="Macro BOOST_TTI_TRAIT_HAS_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_UNION.html" title="Macro BOOST_TTI_TRAIT_HAS_UNION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_UNION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_ME_idm6453.html" title="Macro BOOST_TTI_TRAIT_MEMBER_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_MEMBER_TYPE</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">TTI Macro Metafunctions</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail.html#the_type_traits_introspection_library.tti_detail.tbmacros" title="Table 1.2. TTI Macro Metafunctions"><span class="index-entry-level-1">BOOST_TTI_HAS_UNION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">TTI Nested Type Macro Metafunction</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html#the_type_traits_introspection_library.tti_nested_type.tbmacronested" title="Table 1.4. TTI Nested Type Macro Metafunction"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">TTI Nested Type Macro Metafunction Existence</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_nested_type.html#the_type_traits_introspection_library.tti_nested_type.existtbmacronested" title="Table 1.5. TTI Nested Type Macro Metafunction Existence"><span class="index-entry-level-1">BOOST_TTI_MEMBER_TYPE</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">TTI Specific Inner Types</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type.tbspinner" title="Table 1.3. TTI Specific Inner Types"><span class="index-entry-level-1">BOOST_TTI_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type.tbspinner" title="Table 1.3. TTI Specific Inner Types"><span class="index-entry-level-1">BOOST_TTI_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_type.html#the_type_traits_introspection_library.tti_detail_has_type.tti_detail_has_specific_type.tbspinner" title="Table 1.3. TTI Specific Inner Types"><span class="index-entry-level-1">BOOST_TTI_HAS_UNION</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_CLASS.html" title="Macro BOOST_TTI_HAS_CLASS"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_DATA.html" title="Macro BOOST_TTI_HAS_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_ENUM.html" title="Macro BOOST_TTI_HAS_ENUM"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNCTION.html" title="Macro BOOST_TTI_HAS_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_FUNC_idm6162.html" title="Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMBER_DATA.html" title="Macro BOOST_TTI_HAS_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm6210.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_MEMB_idm6237.html" title="Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6264.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6285.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_STAT_idm6312.html" title="Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TEMPLATE.html" title="Macro BOOST_TTI_HAS_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_TYPE.html" title="Macro BOOST_TTI_HAS_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_HAS_UNION.html" title="Macro BOOST_TTI_HAS_UNION"><span class="index-entry-level-1">Macro BOOST_TTI_HAS_UNION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_MEMBER_TYPE.html" title="Macro BOOST_TTI_MEMBER_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_CLASS.html" title="Macro BOOST_TTI_TRAIT_HAS_CLASS"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_CLASS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_DATA.html" title="Macro BOOST_TTI_TRAIT_HAS_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_ENUM.html" title="Macro BOOST_TTI_TRAIT_HAS_ENUM"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_ENUM</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6124.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6145.html" title="Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6178.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6199.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6220.html" title="Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6253.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_DATA</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6274.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6295.html" title="Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_STATIC_MEMBER_FUNCTION_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HA_idm6328.html" title="Macro BOOST_TTI_TRAIT_HAS_TEMPLATE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_TYPE.html" title="Macro BOOST_TTI_TRAIT_HAS_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_HAS_UNION.html" title="Macro BOOST_TTI_TRAIT_HAS_UNION"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_HAS_UNION</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_TTI_TRAIT_ME_idm6453.html" title="Macro BOOST_TTI_TRAIT_MEMBER_TYPE"><span class="index-entry-level-1">Macro BOOST_TTI_TRAIT_MEMBER_TYPE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_metafunction.html" title="Struct template valid_member_metafunction"><span class="index-entry-level-1">Struct template valid_member_metafunction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">Struct template valid_member_type</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_10"></a><span class="term">U</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Using the BOOST_TTI_HAS_TEMPLATE macro</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_template.html#the_type_traits_introspection_library.tti_detail_has_template.tti_detail_has_template_macro" title="Using the BOOST_TTI_HAS_TEMPLATE macro"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Using the has_template_(xxx) metafunction</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_template/tti_detail_has_template_metafunction.html" title="Using the has_template_(xxx) metafunction"><span class="index-entry-level-1">BOOST_TTI_HAS_TEMPLATE</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../the_type_traits_introspection_library/tti_detail_has_template/tti_detail_has_template_metafunction.html" title="Using the has_template_(xxx) metafunction"><span class="index-entry-level-1">BOOST_TTI_TRAIT_HAS_TEMPLATE</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_11"></a><span class="term">V</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">valid_member_metafunction</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_metafunction.html" title="Struct template valid_member_metafunction"><span class="index-entry-level-1">Struct template valid_member_metafunction</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">valid_member_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/tti/valid_member_type.html" title="Struct template valid_member_type"><span class="index-entry-level-1">Struct template valid_member_type</span></a></p></li></ul></div>
</li>
</ul></div></dd>
</dl></div>
</div>
<div class="copyright-footer">Copyright © 2011-2013 Tropic Software
      East Inc<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../the_type_traits_introspection_library/tti_acknowledgments.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
</body>
</html>
