[/
 / Copyright (c) 2008 <PERSON>
 / Copyright (c) 2006, 2008 <PERSON><PERSON>
 / Copyright (c) 2009-2018 <PERSON> Botet Escriba
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[library Boost.Chrono
    [quickbook 1.5]
    [version 2.0.8]
    [authors [<PERSON><PERSON><PERSON>, <PERSON>]]
    [authors [<PERSON><PERSON>, <PERSON><PERSON>]]
    [authors [<PERSON><PERSON><PERSON>, <PERSON>]]
    [copyright 2008 <PERSON> Hi<PERSON>]
    [copyright 2006, 2008 <PERSON><PERSON>]
    [copyright 2009-2013 Vicente J. Botet Escriba]
    [category utilities system]
    [id chrono]
    [dirname chrono]
    [purpose
        Useful time utilities.
    ]
    [license
        Distributed under the Boost Software License, Version 1.0.
        (See accompanying file LICENSE_1_0.txt or copy at
        [@http://www.boost.org/LICENSE_1_0.txt])
    ]
]

[/==================]
[def __Boost_Chrono__ [*Boost.Chrono]]
[def __Boost_Chrono [*Boost.Chrono]]

[def __see_bellow__ ['see bellow]]

[/===============================================]
[def __inherit [*Inherits:]]
[def __std_ref [*C++ Standard Reference:]]
[def __header [*Header:]]
[def __compat [*Compiler Compatibility:]]
[def __examples [*Examples:]]
[def __example [*Example:]]
[def __type [*type:]]
[def __returns [*Returns:]]
[def __return_type [*Return Type:]]
[def __throws [*Throws:]]
[def __remarks [*Remarks:]]
[def __effects [*Effects:]]
[def __post_conditions [*Post Conditions:]]
[def __post_condition [*Post Conditions:]]
[def __postcondition [*Post Conditions:]]
[def __pre_conditions [*Pre Conditions:]]
[def __precondition [*Pre Conditions:]]
[def __requires [*Requires:]]
[def __params [*Parameters:]]
[template param [p] [*[p]:]]

[template mu[]'''&#x3BC;'''] [/  Greek small letter mu]
[template plusminus[]'''&#x00B1;'''] [/  ? plus or minus sign]

[def __mus [mu]s]

[/===============================================]
[def __common_type `common_type`]


[/===============================================]
[def __ratio `ratio`]

[def __ratio_add `ratio_add`]
[def __ratio_subtract `ratio_subtract`]
[def __ratio_multiply `ratio_multiply`]
[def __ratio_divide `ratio_divide`]


[def __ratio_equal `ratio_equal`]
[def __ratio_not_equal `ratio_not_equal`]
[def __ratio_less `ratio_less`]
[def __ratio_less_equal `ratio_less_equal`]
[def __ratio_greater `ratio_greater`]
[def __ratio_greater_equal `ratio_greater_equal`]

[def __failbit `failbit`]


[def __atto `atto`]
[def __femto `femto`]
[def __pico `pico`]
[def __nano `nano`]
[def __micro `micro`]
[def __milli `milli`]
[def __centi `centi`]
[def __deci `deci`]
[def __deca `deca`]
[def __hecto `hecto`]
[def __kilo `kilo`]
[def __mega `mega`]
[def __giga `giga`]
[def __tera `tera`]
[def __peta `peta`]
[def __exa `exa`]


[/==================]


[template chrono_conf[link_text] [link chrono.reference.cpp0x.chrono_chrono_hpp.conf [link_text]]]

[def __BOOST_CHRONO_HEADER_ONLY [link chrono.reference.cpp0x.chrono_chrono_hpp.conf.header_only `BOOST_CHRONO_HEADER_ONLY`]]

[def __BOOST_CHRONO_USES_STATIC_ASSERT [chrono_conf `BOOST_CHRONO_USES_STATIC_ASSERT`]]
[def __BOOST_CHRONO_USES_MPL_ASSERT [chrono_conf `BOOST_CHRONO_USES_MPL_ASSERT`]]
[def __BOOST_CHRONO_USES_ARRAY_ASSERT [chrono_conf `BOOST_CHRONO_USES_ARRAY_ASSERT`]]

[def __BOOST_CHRONO_HAS_CLOCK_STEADY [link chrono.reference.cpp0x.system_clocks_hpp.BOOST_CHRONO_HAS_CLOCK_STEADY `BOOST_CHRONO_HAS_CLOCK_STEADY`]]

[def __BOOST_CHRONO_HAS_THREAD_CLOCK [link chrono.reference.other_clocks.thread_clock_hpp.BOOST_CHRONO_HAS_THREAD_CLOCK `BOOST_CHRONO_HAS_THREAD_CLOCK`]]



[def __clock_req [link chrono.reference.cpp0x.clock `Clock`] requirements]
[def __Clock [link chrono.reference.cpp0x.clock `Clock`]]
[def __Clock [link chrono.reference.cpp0x.clock `Clock`'s]]
[def __TrivialClock [link chrono.reference.cpp0x.clock `TrivialClock`]]

[def __To [link chrono.reference.cpp0x.duration_hpp.duration `To`]]
[def __ToDuration [link chrono.reference.cpp0x.duration_hpp.duration `ToDuration`]]
[def __Duration [link chrono.reference.cpp0x.duration_hpp.duration `Duration`]]
[def __Duration1 [link chrono.reference.cpp0x.duration_hpp.duration `Duration1`]]
[def __Duration2 [link chrono.reference.cpp0x.duration_hpp.duration `Duration2`]]
[def __duration [link chrono.reference.cpp0x.duration_hpp.duration `duration`]]
[def __durations [link chrono.reference.cpp0x.duration_hpp.duration `duration`]s]
[def __duration_s [link chrono.reference.cpp0x.duration_hpp.duration `duration`]'s]

[def __duration__c_0 [link chrono.reference.cpp0x.duration_hpp.duration.duration_c_0 `duration`]]
[def __duration__c_1 [link chrono.reference.cpp0x.duration_hpp.duration.duration_c_1 `duration`]]
[def __duration__c_2 [link chrono.reference.cpp0x.duration_hpp.duration.duration_c_2 `duration`]]
[def __duration__count [link chrono.reference.cpp0x.duration_hpp.duration.count `count`]]
[def __duration__op_unary_plus [link chrono.reference.cpp0x.duration_hpp.duration.op_plus `operator+`]]
[def __duration__op_unary_minus [link chrono.reference.cpp0x.duration_hpp.duration.op_minus `operator-`]]
[def __duration__op_pre_inc [link chrono.reference.cpp0x.duration_hpp.duration.op_pre_inc `operator++`]]
[def __duration__op_post_inc [link chrono.reference.cpp0x.duration_hpp.duration.op_post_inc `operator++`]]
[def __duration__op_pre_dec [link chrono.reference.cpp0x.duration_hpp.duration.op_pre_dec `operator--`]]
[def __duration__op_post_dec [link chrono.reference.cpp0x.duration_hpp.duration.op_post_dec `operator--`]]
[def __duration__op_plus_eq [link chrono.reference.cpp0x.duration_hpp.duration.op_plus_eq `operator+=`]]
[def __duration__op_minus_eq [link chrono.reference.cpp0x.duration_hpp.duration.op_minus_eq `operator-=`]]
[def __duration__op_mult_eq [link chrono.reference.cpp0x.duration_hpp.duration.op_mult_eq `operator*=`]]
[def __duration__op_div_eq [link chrono.reference.cpp0x.duration_hpp.duration.op_div_eq `operator/=`]]
[def __duration__op_mod_eq [link chrono.reference.cpp0x.duration_hpp.duration.op_mod_eq `operator%=`]]
[def __duration__op_mod_eq2 [link chrono.reference.cpp0x.duration_hpp.duration.op_mod_eq2 `operator%=`]]
[def __duration__zero [link chrono.reference.cpp0x.duration_hpp.duration.zero `zero`]]
[def __duration__min [link chrono.reference.cpp0x.duration_hpp.duration.min `min`]]
[def __duration__max [link chrono.reference.cpp0x.duration_hpp.duration.max `max`]]
[def __duration__op_plus_1 [link chrono.reference.cpp0x.duration_hpp.arith.duration__op_plus_1 `operator+`]]
[def __duration__op_minus_1 [link chrono.reference.cpp0x.duration_hpp.arith.duration__op_minus_1 `operator-`]]
[def __duration__op_mult_1 [link chrono.reference.cpp0x.duration_hpp.arith.duration__op_mult_1 `operator*`]]
[def __duration__op_mult_2 [link chrono.reference.cpp0x.duration_hpp.arith.duration__op_mult_2 `operator*`]]
[def __duration__op_div_1 [link chrono.reference.cpp0x.duration_hpp.arith.duration__op_div_1 `operator/`]]
[def __duration__op_div_2 [link chrono.reference.cpp0x.duration_hpp.arith.duration__op_div_2 `operator/`]]
[def __duration__op_div_3 [link chrono.reference.cpp0x.duration_hpp.arith.duration__op_div_3 `operator/`]]
[def __duration__op_mod_1 [link chrono.reference.cpp0x.duration_hpp.arith.duration__op_mod_1 `operator%`]]
[def __duration__op_mod_2 [link chrono.reference.cpp0x.duration_hpp.arith.duration__op_mod_2 `operator%`]]
[def __duration__op_eq_1 [link chrono.reference.cpp0x.duration_hpp.cmp.duration__op_eq_1 `operator==`]]
[def __duration__op_neq_1 [link chrono.reference.cpp0x.duration_hpp.cmp.duration__op_neq_1 `operator!=`]]
[def __duration__op_lt_1 [link chrono.reference.cpp0x.duration_hpp.cmp.duration__op_lt_1 `operator<`]]
[def __duration__op_leq_1 [link chrono.reference.cpp0x.duration_hpp.cmp.duration__op_leq_1 `operator<=`]]
[def __duration__op_gt_1 [link chrono.reference.cpp0x.duration_hpp.cmp.duration__op_gt_1 `operator>`]]
[def __duration__op_geq_1 [link chrono.reference.cpp0x.duration_hpp.cmp.duration__op_geq_1 `operator>=`]]



[/==================]
[def __time_point [link chrono.reference.cpp0x.time_point_hpp.time_point `time_point`]]
[def __time_points [link chrono.reference.cpp0x.time_point_hpp.time_point `time_point`]s]
[def __time_point_s [link chrono.reference.cpp0x.time_point_hpp.time_point `time_point`]'s]


[def __time_point__c_1 [link chrono.reference.cpp0x.time_point_hpp.time_point.time_point_c_1 `time_point`]]
[def __time_point__c_2 [link chrono.reference.cpp0x.time_point_hpp.time_point.time_point_c_2 `time_point`]]
[def __time_point__c_3 [link chrono.reference.cpp0x.time_point_hpp.time_point.time_point_c_3 `time_point`]]
[def __time_point__time_since_epoch [link chrono.reference.cpp0x.time_point_hpp.time_point.time_since_epoch `time_since_epoch`]]
[def __time_point__op_unary_plus [link chrono.reference.cpp0x.time_point_hpp.time_point.op_unary_plus `operator+`]]
[def __time_point__op_unary_minus [link chrono.reference.cpp0x.time_point_hpp.time_point.op_unary_minus `operator-`]]
[def __time_point__op_pre_inc [link chrono.reference.cpp0x.time_point_hpp.time_point.op_pre_inc `operator++`]]
[def __time_point__op_post_inc [link chrono.reference.cpp0x.time_point_hpp.time_point.op_post_inc `operator++`]]
[def __time_point__op_pre_dec [link chrono.reference.cpp0x.time_point_hpp.time_point.op_pre_dec `operator--`]]
[def __time_point__op_post_dec [link chrono.reference.cpp0x.time_point_hpp.time_point.op_post_dec `operator--`]]
[def __time_point__op_plus_eq_1[link chrono.reference.cpp0x.time_point_hpp.time_point.op_plus_eq_1 `operator+=`]]
[def __time_point__op_plus_eq_2[link chrono.reference.cpp0x.time_point_hpp.time_point.op_plus_eq_2 `operator+=`]]
[def __time_point__op_minus_eq_1 [link chrono.reference.cpp0x.time_point_hpp.time_point.op_minus_eq_1 `operator-=`]]
[def __time_point__op_minus_eq_2 [link chrono.reference.cpp0x.time_point_hpp.time_point.op_minus_eq_2 `operator-=`]]
[def __time_point__min [link chrono.reference.cpp0x.time_point_hpp.time_point.min `min`]]
[def __time_point__max [link chrono.reference.cpp0x.time_point_hpp.time_point.max `max`]]
[def __time_point__op_plus_1 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_plus_1 `operator+`]]
[def __time_point__op_plus_2 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_plus_2 `operator+`]]
[def __time_point__op_minus_1 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_minus_1 `operator-`]]
[def __time_point__op_minus_2 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_minus_2 `operator-`]]
[def __time_point__op_mult_1 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_mult_1 `operator*`]]
[def __time_point__op_mult_2 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_mult_2 `operator*`]]
[def __time_point__op_div_1 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_div_1 `operator/`]]
[def __time_point__op_div_2 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_div_2 `operator/`]]
[def __time_point__op_div_3 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_div_3 `operator/`]]
[def __time_point__op_mod_1 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_mod_1 `operator%`]]
[def __time_point__op_mod_2 [link chrono.reference.cpp0x.time_point_hpp.arith.time_point__op_mod_2 `operator%`]]
[def __time_point__op_eq [link chrono.reference.cpp0x.time_point_hpp.cmp.time_point__op_eq `operator==`]]
[def __time_point__op_neq [link chrono.reference.cpp0x.time_point_hpp.cmp.time_point__op_neq `operator!=`]]
[def __time_point__op_lt [link chrono.reference.cpp0x.time_point_hpp.cmp.time_point__op_lt `operator<`]]
[def __time_point__op_leq [link chrono.reference.cpp0x.time_point_hpp.cmp.time_point__op_leq `operator<=`]]
[def __time_point__op_gt [link chrono.reference.cpp0x.time_point_hpp.cmp.time_point__op_gt `operator>`]]
[def __time_point__op_geq [link chrono.reference.cpp0x.time_point_hpp.cmp.time_point__op_geq `operator>=`]]

[def __time_point_units [link chrono.reference.io.time_point_units_hpp.time_point_units `time_point_units`]]






[/==================]
[def __system_clock [link chrono.reference.cpp0x.system_clocks_hpp.system_clock `system_clock`]]
[def __steady_clock [link chrono.reference.cpp0x.system_clocks_hpp.steady_clock `steady_clock`]]
[def __high_resolution_clock [link chrono.reference.cpp0x.system_clocks_hpp.high_resolution_clock `high_resolution_clock`]]

[/==================]

[def __process_real_cpu_clock [link chrono.reference.other_clocks.process_cpu_clocks_hpp.process_real_cpu_clock `process_real_cpu_clock`]]
[def __process_system_cpu_clock [link chrono.reference.other_clocks.process_cpu_clocks_hpp.process_system_cpu_clock `process_system_cpu_clock`]]
[def __process_user_cpu_clock [link chrono.reference.other_clocks.process_cpu_clocks_hpp.process_user_cpu_clock `process_user_cpu_clock`]]
[def __process_cpu_clock [link chrono.reference.other_clocks.process_cpu_clocks_hpp.process_cpu_clock `process_cpu_clock`]]
[def __thread_clock [link chrono.reference.other_clocks.thread_clock_hpp.thread_clock `thread_clock`]]

[/==================]
[def __duration_cast [link chrono.reference.cpp0x.duration_hpp.duration_cast `duration_cast`]]
[def __time_point_cast [link chrono.reference.cpp0x.time_point_hpp.time_point_cast `time_point_cast`]]


[def __nanoseconds [link chrono.reference.cpp0x.duration_hpp.duration_typedefs `nanoseconds`]]
[def __microseconds [link chrono.reference.cpp0x.duration_hpp.duration_typedefs `microseconds`]]
[def __milliseconds [link chrono.reference.cpp0x.duration_hpp.duration_typedefs `milliseconds`]]
[def __seconds [link chrono.reference.cpp0x.duration_hpp.duration_typedefs `seconds`]]
[def __minutes [link chrono.reference.cpp0x.duration_hpp.duration_typedefs `minutes`]]
[def __hours [link chrono.reference.cpp0x.duration_hpp.duration_typedefs `hours`]]

[def __common_type_spe [link chrono.reference.cpp0x.duration_hpp.common_type_spe `common_type`]]

[/==================]
[def __treat_as_floating_point [link chrono.reference.cpp0x.duration_hpp.traits.treat_as_floating_point `treat_as_floating_point`]]
[def __duration_values [link chrono.reference.cpp0x.duration_hpp.traits.duration_values `duration_values`]]
[def __zero [link chrono.reference.cpp0x.duration_hpp.traits.duration_values.zero `zero`]]
[def __max [link chrono.reference.cpp0x.duration_hpp.traits.duration_values.max `max`]]
[def __min [link chrono.reference.cpp0x.duration_hpp.traits.duration_values.min `min`]]



[/==================]

[def __duration_punct [link chrono.reference.io_v1.chrono_io_hpp.duration_punct `duration_punct`]]
[def __duration_punct__c1 [link chrono.reference.io_v1.chrono_io_hpp.duration_punct.c1 `duration_punct`]]
[def __duration_punct__c2 [link chrono.reference.io_v1.chrono_io_hpp.duration_punct.c2 `duration_punct`]]
[def __duration_punct__is_prefix_name [link chrono.reference.io_v1.chrono_io_hpp.duration_punct.is_prefix_name `is_prefix_name`]]
[def __duration_punct__is_symbol_name [link chrono.reference.io_v1.chrono_io_hpp.duration_punct.is_symbol_name `is_symbol_name`]]
[def __duration_punct__get_duration_style [link chrono.reference.io_v1.chrono_io_hpp.duration_punct.get_duration_style `get_duration_style`]]

[def __duration_short [link chrono.reference.io_v1.chrono_io_hpp.manipulators `duration_short`]]
[def __duration_long [link chrono.reference.io_v1.chrono_io_hpp.manipulators `duration_long`]]


[def __duration_style [link chrono.reference.io.duration_style_hpp.duration_style `duration_style`]]
[def __duration_style_type [link chrono.reference.io.duration_style_hpp.duration_style `duration_style`]]
[def __prefix [link chrono.reference.io.duration_style_hpp.duration_style `prefix`]]
[def __symbol [link chrono.reference.io.duration_style_hpp.duration_style `symbol`]]

[def __symbol_format [link chrono.reference.io.duration_io_hpp.manipulators.symbol_format `symbol_format`]]
[def __name_format [link chrono.reference.io.duration_io_hpp.manipulators.name_format `name_format`]]
[def __duration_fmt [link chrono.reference.io.duration_io_hpp.manipulators.duration_fmt `duration_fmt`]]
[def __duration_fmt__c1 [link chrono.reference.io.duration_io_hpp.manipulators.duration_fmt.c1 `duration_fmt`]]
[def __duration_fmt__op_duration_style [link chrono.reference.io.duration_io_hpp.manipulators.duration_fmt.op_duration_style `operator duration_style`]]
[def __duration_fmt__get_duration_style [link chrono.reference.io.duration_io_hpp.manipulators.duration_fmt.op_duration_style `get_duration_style`]]
[def __duration_fmt__op_in [link chrono.reference.io.duration_io_hpp.manipulators.duration_fmt.op_in `operator >>`]]
[def __duration_fmt__op_out [link chrono.reference.io.duration_io_hpp.manipulators.duration_fmt.op_out `operator <<`]]


[def __duration_style_io_saver [link chrono.reference.io.ios_state_hpp.ioss.duration_style_io_saver `duration_style_io_saver`]]
[def __duration_style_io_saver__c1 [link chrono.reference.io.ios_state_hpp.ioss.duration_style_io_saver.c1 `duration_style_io_saver`]]
[def __duration_style_io_saver__c2 [link chrono.reference.io.ios_state_hpp.ioss.duration_style_io_saver.c2 `duration_style_io_saver`]]
[def __duration_style_io_saver__d [link chrono.reference.io.ios_state_hpp.ioss.duration_style_io_saver.d `~duration_style_io_saver`]]
[def __duration_style_io_saver__restore [link chrono.reference.io.ios_state_hpp.ioss.duration_style_io_saver.restore `restore`]]

[def __get_duration_style [link chrono.reference.io.ios_state_hpp.sag.get_duration_style `get_duration_style`]]
[def __set_duration_style [link chrono.reference.io.ios_state_hpp.sag.set_duration_style `set_duration_style`]]
[def __get_timezone [link chrono.reference.io.ios_state_hpp.sag.get_timezone `get_timezone`]]
[def __set_timezone [link chrono.reference.io.ios_state_hpp.sag.set_timezone `set_timezone`]]
[def __get_time_fmt [link chrono.reference.io.ios_state_hpp.sag.get_time_fmt `get_time_fmt`]]
[def __set_time_fmt [link chrono.reference.io.ios_state_hpp.sag.set_time_fmt `set_time_fmt`]]

[def __duration__op_in [link chrono.reference.io.duration_io_hpp.streams.duration__op_in `operator >>`]]
[def __duration__op_out [link chrono.reference.io.duration_io_hpp.streams.duration__op_out `operator <<`]]

[def __duration_get [link chrono.reference.io.duration_get_hpp.duration_get `duration_get`]]
[def __duration_get_c [link chrono.reference.io.duration_get_hpp.duration_get.c `duration_get`]]
[def __duration_get_d [link chrono.reference.io.duration_get_hpp.duration_get.d `~duration_get`]]
[def __duration_get_get [link chrono.reference.io.duration_get_hpp.duration_get.get `get`]]
[def __duration_get_get2 [link chrono.reference.io.duration_get_hpp.duration_get.get2 `get`]]
[def __duration_get_get_unit [link chrono.reference.io.duration_get_hpp.duration_get.get_unit `get_unit`]]
[def __duration_get_get_value [link chrono.reference.io.duration_get_hpp.duration_get.get_value `get_value`]]

[def __duration_put [link chrono.reference.io.duration_put_hpp.duration_put `duration_put`]]
[def __duration_put_c [link chrono.reference.io.duration_put_hpp.duration_put.c `duration_put`]]
[def __duration_put_d [link chrono.reference.io.duration_put_hpp.duration_put.d `~duration_put`]]
[def __duration_put_put [link chrono.reference.io.duration_put_hpp.duration_put.put `put`]]
[def __duration_put_put2 [link chrono.reference.io.duration_put_hpp.duration_put.put2 `put`]]
[def __duration_put_put_unit [link chrono.reference.io.duration_put_hpp.duration_put.put_unit `put_unit`]]
[def __duration_put_put_value [link chrono.reference.io.duration_put_hpp.duration_put.put_value `put_value`]]

[def __duration_units [link chrono.reference.io.duration_units_hpp.duration_units `duration_units`]]
[def __duration_units_c [link chrono.reference.io.duration_units_hpp.duration_units.c `duration_units`]]
[def __duration_units_d [link chrono.reference.io.duration_units_hpp.duration_units.d `~duration_units`]]
[def __duration_units_get_n_d_valid_units_start [link chrono.reference.io.duration_units_hpp.duration_units.get_n_d_valid_units_start `get_n_d_valid_units_start`]]
[def __duration_units_get_n_d_valid_units_end [link chrono.reference.io.duration_units_hpp.duration_units.get_n_d_valid_units_end `get_n_d_valid_units_end`]]
[def __duration_units_get_n_d_unit [link chrono.reference.io.duration_units_hpp.duration_units.get_n_d_unit `get_n_d_unit`]]
[def __duration_units_do_get_n_d_unit [link chrono.reference.io.duration_units_hpp.duration_units.do_get_n_d_unit `do_get_n_d_unit`]]
[def __duration_units_match_n_d_valid_unit [link chrono.reference.io.duration_units_hpp.duration_units.match_n_d_valid_unit `match_n_d_valid_unit`]]
[def __duration_units_get_valid_units_start [link chrono.reference.io.duration_units_hpp.duration_units.get_valid_units_start `get_valid_units_start`]]
[def __duration_units_get_valid_units_end [link chrono.reference.io.duration_units_hpp.duration_units.get_valid_units_end `get_valid_units_end`]]
[def __duration_units_get_unit [link chrono.reference.io.duration_units_hpp.duration_units.get_unit `get_unit`]]
[def __duration_units_do_get_unit [link chrono.reference.io.duration_units_hpp.duration_units.do_get_unit `do_get_unit`]]
[def __duration_units_match_valid_unit [link chrono.reference.io.duration_units_hpp.duration_units.match_valid_unit `match_valid_unit`]]
[def __duration_units_get_pattern [link chrono.reference.io.duration_units_hpp.duration_units.get_pattern `get_pattern`]]
[def __duration_units_is_named_unit [link chrono.reference.io.duration_units_hpp.duration_units.is_named_unit `is_named_unit`]]
[def __duration_units_do_is_named_unit [link chrono.reference.io.duration_units_hpp.duration_units.do_is_named_unit `do_is_named_unit`]]


[/==================]

[def __timezone [link chrono.reference.io.timezone_hpp.timezone `timezone`]]
[def __utc [link chrono.reference.io.timezone_hpp.timezone `utc`]]
[def __local [link chrono.reference.io.timezone_hpp.timezone `local`]]


[def __time_fmt1 [link chrono.reference.io.time_point_io_hpp.manip.time_fmt1 `time_fmt`]]
[def __time_fmt2 [link chrono.reference.io.time_point_io_hpp.manip.time_fmt2 `time_fmt`]]

[def __timezone_io_saver [link chrono.reference.io.ios_state_hpp.ioss.timezone_io_saver `timezone_io_saver`]]
[def __timezone_io_saver__c1 [link chrono.reference.io.ios_state_hpp.ioss.timezone_io_saver.c1 `timezone_io_saver`]]
[def __timezone_io_saver__c2 [link chrono.reference.io.ios_state_hpp.ioss.timezone_io_saver.c2 `timezone_io_saver`]]
[def __timezone_io_saver__d [link chrono.reference.io.ios_state_hpp.ioss.timezone_io_saver.d `~timezone_io_saver`]]
[def __timezone_io_saver__restore [link chrono.reference.io.ios_state_hpp.ioss.timezone_io_saver.restore `restore`]]

[def __time_fmt_io_saver [link chrono.reference.io.ios_state_hpp.ioss.time_fmt_io_saver `time_fmt_io_saver`]]
[def __time_fmt_io_saver__c1 [link chrono.reference.io.ios_state_hpp.ioss.time_fmt_io_saver.c1 `time_fmt_io_saver`]]
[def __time_fmt_io_saver__c2 [link chrono.reference.io.ios_state_hpp.ioss.time_fmt_io_saver.c2 `time_fmt_io_saver`]]
[def __time_fmt_io_saver__d [link chrono.reference.io.ios_state_hpp.ioss.time_fmt_io_saver.d `~time_fmt_io_saver`]]
[def __time_fmt_io_saver__restore [link chrono.reference.io.ios_state_hpp.ioss.time_fmt_io_saver.restore `restore`]]

[/==================]

[def __round [link chrono.reference.round.round_hpp `round`]]
[def __ceil [link chrono.reference.round.ceil_hpp `ceil`]]
[def __floor [link chrono.reference.round.floor_hpp `floor`]]

[/===============]
[section Overview]
[/===============]

[:["What is time, then? If nobody asks me, I know; if I have to explain it to someone who has asked me, I do not know."]]
[:[*['-- Augustine ]]]


[/====================================]
[heading How to Use This Documentation]
[/====================================]

This documentation makes use of the following naming and formatting conventions.

* Code is in `fixed width font` and is syntax-highlighted.
* Replaceable text that you will need to supply is in [~italics].
* Free functions are rendered in the code font followed by `()`, as in `free_function()`.
* If a name refers to a class template, it is specified like this:   `class_template<>`; that is, it is in code font and its name is followed by `<>`   to indicate that it is a class template.
* If a name refers to a function-like macro, it is specified like this: `MACRO()`;
  that is, it is uppercase in code font and its name is followed by `()` to   indicate that it is a function-like macro. Object-like macros appear without the   trailing `()`.
* Names that refer to /concepts/ in the generic programming sense are  specified in CamelCase.

[note In addition, notes such as this one specify non-essential information that provides additional background or rationale.]

Finally, you can mentally add the following to any code fragments in this document:

    // Include all of Chrono files
    #include <boost/chrono.hpp>

[/=================]
[section Motivation]
[/=================]

[heading Time]

We all deal with time every day of our lives. We've intuitively known it since birth. Thus we are all very familiar with it and believe it to be a simple matter. The modeling of time in computer programs should be similarly simple. The unfortunate truth is that this perceived simplicity is only skin deep. Fortunately, we do not need a terribly complicated solution to meet the bulk of our needs. However, overly simplistic solutions can be dangerous and inefficient, and won't adapt as the computer industry evolves.

__Boost_Chrono__ implements the new time facilities in C++11, as proposed in [@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2661.htm [*N2661 - A Foundation to Sleep On]].  That document provides background and motivation for key design decisions and is the source of a good deal of information in this documentation.

[/
__Boost_Chrono__ proposes a solution that is precision neutral, with a very simple end user interface which supports multiple clocks, multiple precisions (both coarser and finer than we will ever need), separate types for points in time and time durations, efficiency, and compile-time enforced safety.
]


In addition to the clocks provided by the standard proposal, __Boost_Chrono__ provides specific process and thread clocks.


[/
See [@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2661.htm [*N2661 - A Foundation to Sleep On]] which is very informative and provides motivation for key design decisions for __common_type, __ratio and `chrono`. This documentation contains a lot of extracts from this document.
]

[heading Wall clock versus system and user time]

To make the timing facilities of Boost.Chrono more generally useful, the library provides a number of clocks that are thin wrappers around the operating system's process time API, thereby allowing the extraction of wall clock time, user CPU time, and system CPU time of the process.  Wall clock time is the sum of CPU time and system CPU time. (On POSIX-like systems, this relies on `times()`.  On Windows, it relies on `GetProcessTimes()`.)

[/
It is also helpful if such timing information is broken down into real (wall clock) time, CPU time spent by the user, and CPU time spent by the operating system servicing user requests.
]

[/
3 concrete process clocks:

# __process_real_cpu_clock,
# __process_user_cpu_clock,
# __process_system_cpu_clock

providing a
]

[/__Boost_Chrono__ provides
thin wrappers around the operating system's process timer API. For POSIX-like systems, that's the times() function, while for Windows, it's the GetProcessTimes() function.
]

[/These clocks capture the specific time unitarily. __Boost_Chrono__ provides also a clock __process_cpu_clock that captures the three times at once.
]

[endsect]

[/==================]
[section Description]
[/==================]

The __Boost_Chrono__ library provides:

[heading Standard]

* A means to represent time durations: managed by the generic __duration class . Examples of time durations include days, __minutes, __seconds  and __nanoseconds, which can be represented with a fixed number of clock ticks per unit. All of these units of time duration are united with a generic interface by the __duration facility.
* A type for representing points in time: __time_point. A __time_point represents an epoch plus or minus a __duration. The library leaves epochs unspecified. A __time_point is associated with a /clock/.
* Several clocks, some of which may not be available on a particular platform: __system_clock, __steady_clock and __high_resolution_clock. A clock is a pairing of a __time_point and __duration, and a function which returns a __time_point representing ['now].

[heading Other clocks]

To make the timing facilities more generally useful, __Boost_Chrono__ provides a number of clocks that are thin wrappers around the operating system's time APIs, thereby allowing the extraction of wall clock time, user CPU time, system CPU time spent by the process,

* __process_real_cpu_clock, captures wall clock CPU time spent by the current process.
* __process_user_cpu_clock, captures user-CPU time spent by the current process.
* __process_system_cpu_clock, captures system-CPU time  spent by the current process.
* A tuple-like class __process_cpu_clock, that captures real, user-CPU, and system-CPU process times together.
* A __thread_clock thread steady clock giving the time spent by the current thread (when supported by a platform).


[/It is also helpful if such timing information is broken down into real (wall clock) time, CPU time spent by the user, and CPU time spent by the operating system servicing user requests. process clocks provide a thin wrapper around the operating system's process timer API. For POSIX-like systems, that's the times() function, while for Windows, it's the GetProcessTimes() function.
]

Lastly, __Boost_Chrono__ includes [@http://www.boost.org/libs/typeof typeof] registration for __duration and __time_point to permit using emulated auto with C++03 compilers.

[heading I/O]

It provides I/O for __duration and __time_point. This I/O makes use of these types much more convenient. In following the "you only pay for what you use" philosophy, this extra functionality is located in a header separate from <boost/chrono/chrono.hpp>, namely <boost/chrono/chrono_io.hpp>.

It builds on `<boost/ratio/ratio_io.hpp>` to provide readable and flexible formatting and parsing for types in `<boost/chrono.hpp>`.
This textural representation uses [@http://en.wikipedia.org/wiki/SI_prefix#List_of_SI_prefixes SI prefixes] whenever possible. 
This makes it easy for `boost::milliseconds` to be represented by the text "milliseconds", or a hypothetical meter 
class to print out "millimeter".
The __duration and the __time_point i/o can be customized through the new facets: __duration_units and __time_point_units. 
The user can specialize these facets so that the chrono i/o could be localizable. 
However Boost.Chrono doesn't provides a complete locale solution.  

`system_clock::time_point` I/O is proposed in terms of UTC timepoints, strongly guided by 
ISO 9899:1999, Programming languages - C, ISO 9945:2003, Information Technology - Portable Operating System Interface (POSIX) and 
ISO 8601:2004, Data elements and interchange formats - Information interchange - Representation of dates and times.

[heading Rounding utilities]

A few simple rounding utility functions for working with durations.

[heading Caveat Emptor]

The underlying clocks provided by operating systems are subject to many seemingly arbitrary policies and implementation irregularities. That's a polite way of saying they tend to be flakey, and each operating system or even each clock has its own cruel and unusual forms of flakiness. Don't bet the farm on their accuracy, unless you have become deeply familiar with exactly what the specific operating system is guaranteeing, which is often very little.

[endsect]

[endsect]


[/==============================]
[section:users_guide User's Guide]
[/==============================]

[/======================================]
[section:getting_started Getting Started]
[/======================================]

[/======================================]
[section:install Installing Chrono]
[/======================================]

[/=================================]
[heading Getting __Boost_Chrono__ ]
[/=================================]

__Boost_Chrono__ is in the latest Boost release in the folder `/boost/chrono`.  Documentation, tests and examples folder are at `boost/libs/chrono/`.

You can also access the latest (unstable?) state from the [@https://svn.boost.org/svn/boost-trunk Boost trunk] directories boost/chrono and libs/chrono. Just go to [@http://svn.boost.org/trac/boost/wiki/BoostSubversion here] and follow the instructions there for anonymous SVN access.


[/==========================================]
[heading Where to install Boost.Chrono? ]
[/==========================================]

The simple way is to decompress (or checkout from SVN) the files in your BOOST_ROOT directory.


[/=================================]
[heading Building Boost.Chrono ]
[/=================================]

__Boost_Chrono__ can be configured as a header-only library defining __BOOST_CHRONO_HEADER_ONLY. 
However Boost.Chrono depends on the non header-only library Boost.System, so that you will need to link with boost_system.

Boost.System has an undocumented feature (use of macro BOOST_ERROR_CODE_HEADER_ONLY) to make it header only.
 
If __BOOST_CHRONO_HEADER_ONLY is not defined you need to compile it and build the library before use, for example using:

    bjam libs/chrono/build

[/===================]
[heading Requirements]
[/===================]

In particular, __Boost_Chrono__ depends on:

[variablelist
[
    [[@http://www.boost.org/libs/config [*Boost.Config]]] [for configuration purposes, ...]
]
[
    [[@http://www.boost.org/libs/exception [*Boost.Exception]]] [for throw_exception, ...]
]
[
    [[@http://www.boost.org/libs/integer [*Boost.Integer]]] [for cstdint conformance, ...]
]
[
    [[@http://www.boost.org/libs/mpl [*Boost.MPL]]] [for MPL Assert and bool, logical ...]
]
[
    [[@http://www.boost.org/libs/operators [*Boost.Operators]]] [for operators, ...]
]
[
    [[@http://www.boost.org/libs/ratio [*Boost.Ratio]]] [for ratio, milli, micro, ...]
]
[
    [[@http://www.boost.org/libs/system [*Boost.System]]] [for error_code, ...]
]
[
    [[@http://www.boost.org/libs/type_traits [*Boost.TypeTraits]]] [for is_base, is_convertible, common_type, ...]
]
[
    [[@http://www.boost.org/libs/utility [*Boost.Utility/EnableIf]]] [for enable_if, ...]
]
]


[/=========================================================]
[heading Building an Executable that Uses Boost.Chrono ]
[/=========================================================]

In addition to link with the __Boost_Chrono__ library you need also to link with the [*Boost.System] library.
If [*Boost.System] is configured defining BOOST_ERROR_CODE_HEADER_ONLY you will no need to link with it as the dependent part is header only then.


[/=========================]
[heading Exception safety ]
[/=========================]

All functions in the library are exception-neutral and provide strong guarantee of exception safety as long as the underlying parameters provide it.


[/=====================]
[heading Thread safety ]
[/=====================]

All functions in the library are thread-unsafe except when noted explicitly.

As Boost.Chrono doesn't use mutable global variables the thread-safety analysis is limited to the access to each instance variable. It is not thread safe to use a function that modifies the access to a user variable if another can be reading or writing it.


[/========================]
[heading Tested compilers ]
[/========================]

The implementation will eventually work with most C++03 conforming compilers.
Currently I use to test with on:

Windows with

* MSVC 10.0

MinGW with

* GCC 4.5.0
* GCC 4.5.0 -std=c++0x
* GCC 4.5.2
* GCC 4.5.2 -std=c++0x
* GCC 4.6.0
* GCC 4.6.0 -std=c++0x
* GCC 4.8.0
* GCC 4.8.0 -std=c++0x

Ubuntu with
* GCC 4.4.6
* GCC 4.4.6 -std=c++0x
* GCC 4.5.4
* GCC 4.5.4 -std=c++0x
* GCC 4.6.1
* GCC 4.6.1 -std=c++0x
* Intel 12.1.3
* Intel 12.1.3 -std=c++0x

OsX with

* GCC 4.1.2
* GCC 4.6.2
* GCC 4.6.2 -std=c++0x
* GCC 4.7.0
* GCC 4.7.0 -std=c++0x
* GCC 4.7.1
* GCC 4.7.1 -std=c++0x
* GCC 4.7.2
* GCC 4.7.2 -std=c++0x
* GCC 4.8.0
* GCC 4.8.0 -std=c++0x
* GCC 4.8.1
* GCC 4.8.1 -std=c++0x
* clang 3.1
* clang 3.1 -std=c++0x -stdlib=libc++
* clang 3.2
* clang 3.2 -std=c++11 -stdlib=libc++


The committed code is tested with much more compilers. There are two compilers (VACPP and Borland) that don't provide the needed features.
Other as Intel and Sun have some issues with i/o. While everything compiles and link correctly, there are some runtime issues I have not cached yet. See the regression tests for details.

[note Please let us know how this works on other platforms/compilers.]

[note Please send any questions, comments and bug reports to boost <at> lists <dot> boost <dot> org.]

[endsect]
[/====================]
[section Hello World! ]
[/====================]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
If all you want to do is to time a program's execution, here is a complete program:

    #include <boost/chrono.hpp>
    #include <cmath>

    int main()
    {
        boost::chrono::system_clock::time_point start = boost::chrono::system_clock::now();

        for ( long i = 0; i < 10000000; ++i )
        std::sqrt( 123.456L ); // burn some time

        boost::chrono::__duration<double> sec = boost::chrono::system_clock::now() - start;
        std::cout << "took " << sec.count() << " seconds\n";
        return 0;
    }

Output was:

    took 0.832 seconds

[endsect]

[endsect]


[section Tutorial]

[section Duration]

The __duration is the heart of this library. The interface that the user will see in everyday use is nearly identical to that of [*Boost.DateTime] time __durations authored by Jeff Garland, both in syntax and in behavior. This has been a very popular boost library for 7 years. There is an enormous positive history with this interface.

The library consists of six units of time __duration:

* __hours
* __minutes
* __seconds
* __milliseconds
* __microseconds
* __nanoseconds

These units were chosen as a subset of the boost library because they are the most common units used when sleeping, waiting on a condition variable, or waiting to obtain the lock on a mutex. Each of these units is nothing but a thin wrapper around a signed integral count. That is, when you construct __minutes`(3)`, all that happens is a `3` is stored inside `minutes`. When you construct __microseconds`(3)`, all that happens is a `3` is stored inside __microseconds.

The only context in which these different types differ is when being converted to one another. At this time, unit-specific compile-time conversion constants are used to convert the source unit to the target unit. Only conversions from coarser units to finer units are allowed (in Boost). This restriction ensures that all conversions are always exact. That is, __microseconds can always represent any value __minutes has.

In [*Boost.DateTime], these units are united via inheritance. __Boost_Chrono__ instead unites these units through the class template __duration. That is, in __Boost_Chrono__ all six of the above units are nothing but typedefs to different instantiations of __duration. This change from Boost.DateTime has a far reaching positive impact, while not changing the syntax of the everyday use at all.

The most immediate positive impact is that the library can immediately generate any unit, with any precision it needs. This is sometimes necessary when doing comparisons or arithmetic between __durations of differing precision, assuming one wants the comparison and arithmetic to be exact.

A secondary benefit is that by publishing the class template __duration interface, user code can very easily create __durations with any precision they desire. The __ratio utility is used to specify the precision, so as long as the precision can be expressed by a rational constant with respect to seconds, this framework can exactly represent it (one third of a second is no problem, and neither is one third of a __femto second). All of this utility and flexibility comes at no cost just by making use of the no-run-time-overhead __ratio facility.

In Boost.DateTime, __hours does not have the same representation as __nanoseconds. The former is usually represented with a `long` whereas a `long long` is required for the latter. The reason for this is simply range. You don't need many hours to cover an extremely large range of time. But this isn't true of nanoseconds. Being able to reduce the sizeof overhead for some units when possible, can be a significant performance advantage.

__Boost_Chrono__ continues, and generalizes that philosophy. Not only can one specify the precision of a __duration, one can also specify its representation. This can be any integral type, or even a floating-point type. Or it can be a user-defined type which emulates an arithmetic type. The six predefined units all use signed integral types as their representation. And they all have a minimum range of [plusminus] 292 years. __nanoseconds needs 64 bits to cover that range. __hours needs only 23 bits to cover that range.


[section  So What Exactly is a `duration` and How Do I Use One?]

A __duration has a representation and a tick period (precision).

    template <class Rep, class Period = __ratio<1> > class duration;

The representation is simply any arithmetic type, or an emulation of such a type. The representation stores a count of ticks. This count is the only data member stored in a __duration. If the representation is floating-point, it can store fractions of a tick to the precision of the representation. The tick period is represented by a __ratio and is encoded into the __duration_s type, instead of stored. The tick period only has an impact on the behavior of the __duration when a conversion between different __durations is attempted. The tick period is completely ignored when simply doing arithmetic among like __durations.

__example

    typedef boost::chrono::__duration<long, boost::ratio<60> > minutes;
    minutes m1(3);                 // m1 stores 3
    minutes m2(2);                 // m2 stores 2
    minutes m3 = m1 + m2;          // m3 stores 5

    typedef boost::chrono::__duration<long long, boost::micro> microseconds;
    microseconds us1(3);           // us1 stores 3
    microseconds us2(2);           // us2 stores 2
    microseconds us3 = us1 + us2;  // us3 stores 5

    microseconds us4 = m3 + us3;   // us4 stores 300000005

In the final line of code above, there is an implicit conversion from minutes to microseconds, resulting in a relatively large number of microseconds.

If you need to access the tick count within a __duration, there is a member `count()` which simply returns the stored tick count.

    long long tc = us4.count();    // tc is 300000005

These __duration_s have very simple, very predictable, and very observable behavior. After all, this is really nothing but the time-tested interface of Jeff's boost time __duration library (unified with templates instead of inheritance).

[endsect]

[section What Happens if I Assign `m3 + us3` to `minutes` Instead of `microseconds`?]

    minutes m4 = m3 + us3;

It won't compile! The rationale is that implicit truncation error should not be allowed to happen. If this were to compile, then `m4` would hold `5`, the same value as `m3`. The value associated with `us3` has been effectively ignored. This is similar to the problem of assigning a double to an `int`: the fractional part gets silently discarded.
[endsect]

[section But What if the Truncation Behavior is What I Want to Do?]

There is a __duration_cast facility to explicitly ask for this behavior:

    minutes m4 = boost::chrono::__duration_cast<minutes>(m3 + us3);  // m4.count() == 5

In general, one can perform __duration arithmetic at will. If __duration_cast isn't used, and it compiles, the arithmetic is exact. If one wants to override this exact arithmetic behavior, __duration_cast can be used to explicitly specify that desire. The __duration_cast has the same efficiency as the implicit conversion, and will even be exact as often as it can.

You can use __duration_cast`<>` to convert the __duration into whatever units you desire.  This facility will round down (truncate) if an exact conversion is not possible.  For example:

    boost::chrono::__nanoseconds start;
    boost::chrono::__nanoseconds end;
    typedef boost::chrono::__milliseconds ms;
    ms d = boost::chrono::__duration_cast<ms>(end - start);

    // d now holds the number of milliseconds from start to end.

    std::cout << d.count() << "ms\n";

We can convert to __nanoseconds, or some integral-based duration which __nanoseconds will always exactly convert to, then __duration_cast`<>` is unnecessary:

    typedef boost::chrono::__nanoseconds ns;
    ns d = end - start;
    std::cout << ns.count() << "ns\n";

If you need seconds with a floating-point representation you can also eliminate the __duration_cast`<>`:

    typedef boost::chrono::__duration<double> sec;  // seconds, stored with a double
    sec d = end - start;
    std::cout << sec.count() << "s\n";

If you're not sure if you need __duration_cast`<>` or not, feel free to try it without.  If the conversion is exact, or if the destination has a floating-point representation, it will compile: else it will not compile.


If you need to use __duration_cast`<>`, but want to round up, instead of down when the conversion is inexact, here is a handy little helper function to do so.  Writing it is actually a good starter project for understanding __Boost_Chrono__:

    template <class __ToDuration, class Rep, class Period>
    ToDuration
    round_up(boost::chrono::__duration<Rep, Period> d)
    {
        // first round down
        ToDuration result = boost::chrono::__duration_cast<ToDuration>(d);
        if (result < d)  // comparisons are *always* exact
           ++result;     // increment by one tick period
        return result;
    }

    typedef boost::chrono::__milliseconds ms;
    ms d = round_up<ms>(end - start);
    // d now holds the number of milliseconds from start to end, rounded up.
    std::cout << d.count() << "ms\n";

[endsect]

[section:round Rounding functions]


__Boost_Chrono__ provides few simple rounding utility functions for working with durations.


    // round down
    template <class __To, class Rep, class Period>
    To
    floor(const duration<Rep, Period>& d)
    {
        return duration_cast<To>(d);
    }

    // round to nearest, to even on tie
    template <class __To, class Rep, class Period>
    To
    round(const duration<Rep, Period>& d)
    {
        To t0 = duration_cast<To>(d);
        To t1 = t0;
        ++t1;
        BOOST_AUTO(diff0, d - t0);
        BOOST_AUTO(diff1, t1 - d);
        if (diff0 == diff1)
        {
            if (t0.count() & 1)
                return t1;
            return t0;
        }
        else if (diff0 < diff1)
            return t0;
        return t1;
    }
    // round up
    template <class __To, class Rep, class Period>
    To
    ceil(const duration<Rep, Period>& d)
    {
        To t = duration_cast<To>(d);
        if (t < d)
            ++t;
        return t;
    }


The beauty of the chrono library is the ease and accuracy with which such conversions can be made. For example to convert from __milliseconds (`1/1000` of a second), to `1/30` of a second, one must multiply the milliseconds by `0.03`. It is common knowledge that you can't exactly represent `0.03` in a computer. Nevertheless round will exactly (with no round off error) detect a tie and round to even when this happens. The differences `diff0` and `diff1` are not approximate, but exact differences, even when `d` has the units of millisecond and `To` is `1/30` of a second. The unit of `diff0` and `diff1` is `1/3000` of a second which both millisecond and `1/30` of a second exactly convert to (with no truncation error).

Similarly, the comparison `t < d` in __ceil is exact, even when there is no exact conversion between `t` and `d`.
Example use of rounding functions

    #include <iostream>
    #include <boost/chrono/chrono_io.hpp>
    #include <boost/chrono/floor.hpp>
    #include <boost/chrono/round.hpp>
    #include <boost/chrono/ceil.hpp>

    int main()
    {
        using namespace boost::chrono;
        milliseconds ms(2500);
        std::cout << floor<seconds>(ms) << '\n';
        std::cout << round<seconds>(ms) << '\n';
        std::cout << ceil<seconds>(ms) << '\n';
        ms = milliseconds(2516);
        typedef duration<long, boost::ratio<1, 30> > frame_rate;
        std::cout << floor<frame_rate>(ms) << '\n';
        std::cout << round<frame_rate>(ms) << '\n';
        std::cout << ceil<frame_rate>(ms) << '\n';

        return 0;
    }

The output of this program should be

    2 seconds
    2 seconds
    3 seconds
    75 [1/30]seconds
    75 [1/30]seconds
    76 [1/30]seconds

[endsect]

[section Trafficking in floating-point Durations]

I don't want to deal with writing `duration_cast` all over the place. I'm content with the precision of my floating-point representation.

Not a problem. When the destination of a conversion has floating-point representation, all conversions are allowed to happen implicitly.

    typedef boost::chrono::__duration<double, __ratio<60> > dminutes;
    dminutes dm4 = m3 + us3;  // dm4.count() == 5.000000083333333

[endsect]

[section How Expensive is All of this?]

If you were writing these conversions by hand, you could not make it more efficient. The use of __ratio ensures that all conversion constants are simplified as much as possible at compile-time. This usually results in the numerator or denominator of the conversion factor simplifying to `1`, and being subsequently ignored in converting the run-time values of the tick counts.

[endsect]

[section  How Complicated is it to Build a Function Taking a `duration` Parameter?]

There are several options open to the user:

* If the author of the function wants to accept any __duration, and is willing to work in floating-point __durations, he can simply use any floating-point __duration as the parameter:

          void f(boost::chrono::duration<double> d)  // accept floating-point seconds
          {
              // d.count() == 3.e-6 when passed boost::chrono::microseconds(3)
          }

          f(boost::chrono::microseconds(3));

* If the author of the function wants to traffic only in integral __durations, and is content with handling nothing finer than say nanoseconds (just as an example), he can simply specify nanoseconds as the parameter:

          void f(boost::chrono::nanoseconds d)
          {
              // d.count() == 3000 when passed boost::chrono::microseconds(3)
          }

          f(boost::chrono::microseconds(3));

In this design, if the client wants to pass in a floating-point __duration, or a __duration of finer precision than nanoseconds, then the client is responsible for choosing his own rounding mode in the conversion to nanoseconds.

          boost::chrono::__duration<double> s(1./3);  // 1/3 of a second
          f(boost::chrono::duration_cast<boost::chrono::nanoseconds>(s));  // round towards zero in conversion to nanoseconds

In the example above, the client of f has chosen "round towards zero" as the desired rounding mode to nanoseconds. If the client has a __duration that won't exactly convert to nanoseconds, and fails to choose how the conversion will take place, the compiler will refuse the call:

        f(s);  // does not compile

* If the author of the function wants to accept any __duration, but wants to work with integral representations and wants to control the rounding mode internally, then he can template the function:

        template <class Rep, class Period>
        void f(boost::chrono::__duration<Rep, Period> d)
        {
            // convert d to nanoseconds, rounding up if it is not an exact conversion
            boost::chrono::nanoseconds ns = boost::chrono::duration_cast<boost::chrono::nanoseconds>(d);
            if (ns < d)
                ++ns;
            // ns.count() == 333333334 when passed 1/3 of a floating-point second
        }

          f(boost::chrono::__duration<double>(1./3));

*  If the author in the example does not want to accept floating-point based __durations, he can enforce that behavior like so:

        template <class Period>
        void f(boost::chrono::__duration<long long, Period> d)
        {
            // convert d to nanoseconds, rounding up if it is not an exact conversion
            boost::chrono::nanoseconds ns = boost::chrono::duration_cast<nanoseconds>(d);
            if (ns < d)
                ++ns;
            // ns.count() == 333333334 when passed 333333333333 picoseconds
        }
        // About 1/3 of a second worth of picoseconds
        f(boost::chrono::__duration<long long, boost::pico>(333333333333));

Clients with floating-point __durations who want to use f will now have to convert to an integral __duration themselves before passing the result to f.

In summary, the author of f has quite a bit of flexibility and control in the interface he wants to provide his clients with, and easy options for manipulating that __duration internal to his function.
[endsect]

[section Is it possible for the user to pass a __duration to a function with the units being ambiguous?]

No. No matter which option the author of `f` chooses above, the following client code will not compile:

    f(3);  // Will not compile, 3 is not implicitly convertible to any __duration

[endsect]

[section Can Durations Overflow?]

This depend on the representation. The default typedefs uses a representation that don't handle overflows. The user can define his own representation that manage overflow as required by its application.

[endsect]
[endsect]

[section Clocks]

While __durations only have precision and representation to concern themselves, clocks and __time_points are intimately related and refer to one another. Because clocks are simpler to explain, we will do so first without fully explaining __time_points. Once clocks are introduced, it will be easier to then fill in what a __time_point is.

A clock is a concept which bundles 3 things:

# A concrete __duration type.
# A concrete __time_point type.
# A function called now() which returns the concrete __time_point.

The standard defines three system-wide clocks that are associated to the computer time.

* __system_clock represents system-wide realtime clock that can be synchronized with an external clock.

* __steady_clock can not be changed explicitly and the time since the initial epoch increase in a steady way.

* __high_resolution_clock intend to use the system-wide clock provided by the platform with the highest resolution.

__Boost_Chrono__ provides them when supported by the underlying platform. A given platform may not be able to supply all three of these clocks.

The library adds some clocks that are specific to a process or a thread, that is there is a clock per process or per thread.


The user is also able to easily create more clocks.

Given a clock named Clock, it will have:

    class Clock {
    public:
        typedef an arithmetic-like type        rep;
        typedef an instantiation of ratio      period;
        typedef boost::chrono::__duration<rep, period> __duration;
        typedef boost::chrono::__time_point<__Clock>     time_point;
        static constexpr bool is_steady =      true or false;

        static time_point now();
    };

One can get the current time from Clock with:

    Clock::time_point t1 = Clock::now();

And one can get the time __duration between two __time_points associated with Clock with:

    Clock::duration d = Clock::now() - t1;

And one can specify a past or future __time_point with:

    Clock::time_point t2 = Clock::now() + d;

Note how even if a particular clock becomes obsolete, the next clock in line will have the same API. There is no new learning curve to come up. The only source code changes will be simply changing the type of the clock. The same __duration and __time_point framework continues to work as new clocks are introduced. And multiple clocks are safely and easily handled within the same program.


[endsect]


[section Time Point]

A __time_point represents a point in time, as opposed to a __duration of time. Another way of saying the same thing, is that a __time_point represents an epoch plus or minus a __duration. Examples of __time_points include:

* 3 minutes after the computer booted.
* 03:14:07 UTC on Tuesday, January 19, 2038
* 20 milliseconds after I started that timer.

In each of the examples above, a different epoch is implied. Sometimes an epoch has meaning for several millennia. Other times the meaning of an epoch is lost after a while (such as the start of a timer, or when the computer booted). However, if two __time_points are known to share the same epoch, they can be subtracted, yielding a valid __duration, even if the definition of the epoch no longer has meaning.

In __Boost_Chrono__, an epoch is a purely abstract and unspecified concept. There is no type representing an epoch. It is simply an idea that relates (or doesn't) __time_points to a clock, and in the case that they share a clock, __time_points to one another. __time_points associated with different clocks are generally not interoperable unless the relationship between the epochs associated with each clock is known.

[section  So What Exactly is a `time_point` and How Do I Use One?]

A __time_point has a clock and a __duration.

    template <class __Clock, class __Duration = typename Clock::duration> class __time_point;

The __time_point's clock is not stored. It is simply embedded into the __time_point's type and serves two purposes:

# Because __time_points originating from different clocks have different types, the compiler can be instructed to fail if incompatible __time_points are used in inappropriate ways.
# Given a __time_point, one often needs to compare that __time_point to "now". This is very simple as long as the __time_point knows what clock it is defined with respect to.

A __time_point's __duration is stored as the only data member of the __time_point. Thus __time_points and their corresponding __duration have exactly the same layout. But they have very different meanings. For example, it is one thing to say I want to sleep for 3 minutes. It is a completely different thing to say I want to sleep until 3 minutes past the time I started that timer (unless you just happened to start that timer now). Both meanings (and options for sleeping) have great practical value in common use cases for sleeping, waiting on a condition variable, and waiting for a mutex's lock. These same concepts and tools are found (for example) in Ada.

A timer example:

    void f()
    {
        boost::chrono::steady_clock::time_point start = boost::chrono::steady_clock::now();
        g();
        h();
        __duration<double> sec = boost::chrono::steady_clock::now() - start;
        cout << "f() took " << sec.count() << " seconds\n";
    }

Note that if one is using the __duration between two clock __time_points in a way where the precision of the __duration matters, it is good practice to convert the clock's  __duration to a known __duration. This insulates the code from future changes which may be made to the clock's  precision in the future. For example __steady_clock could easily be based on the clock speed of the cpu. When you upgrade to a faster machine, you do not want your code that assumed a certain tick period of this clock to start experiencing run-time failures because your timing code has silently changed meaning.

A delay loop example:

    // delay for at least 500 nanoseconds:
    auto go = boost::chrono::steady_clock::now() + boost::chrono::nanoseconds(500);
    while (boost::chrono::steady_clock::now() < go)
        ;

The above code will delay as close as possible to half a microsecond, no matter what the precision of __steady_clock is. The more precise __steady_clock becomes, the more accurate will be the delay to 500 nanoseconds.


[endsect]



[/
[section How to Define a Thread Clock]

On posix systems for which the macro _POSIX_THREAD_CPUTIME is defined we can get the time associated to a specific thread.

    class thread_clock {
    public:
        typedef __nanoseconds                          duration;
        typedef duration::rep                        rep;
        typedef duration::period                     period;
        typedef chrono::__time_point<thread_clock>    time_point;
        static constexpr bool is_steady =            BOOST_CHRONO_THREAD_CLOCK_IS_STEADY;

        static time_point now( ) {
            // get the current thread
            pthread_t pth=pthread_self(void);
            // get the clock_id associated to the current thread
            clockid_t clock_id;
            pthread_getcpuclockid(pth, clock_id);
            // get the timespec associated to the thread clock
            struct timespec ts;
            if ( ::clock_gettime( clock_id, &ts ) )
            {
                boost::throw_exception(
                system::system_error( errno, system::system_category, "chrono::thread_clock" ));
            }

            // transform to nanoseconds
            return time_point(duration(
                static_cast<thread_clock::rep>( ts.tv_sec ) * 1000000000 + ts.tv_nsec));

        }
        static time_point now( system::error_code & ec ) {
            // get the current thread
            pthread_t pth=pthread_self(void);
            // get the clock_id associated to the current thread
            clockid_t clock_id;
            pthread_getcpuclockid(pth, clock_id);
            // get the timespec associated to the thread clock
            struct timespec ts;
            if ( ::clock_gettime( clock_id, &ts ) )
            {
              ec.assign( errno, system::system_category );
              return time_point();
            }
            ec.clear();
            // transform to nanoseconds
            return time_point(duration(
                static_cast<thread_clock::rep>( ts.tv_sec ) * 1000000000 + ts.tv_nsec));
        }
    };

[endsect]
]


[endsect]

[section Specific Clocks]
[section system_clock]

__system_clock is useful when you need to correlate the time with a known epoch so you can convert it to a calendar time. Note the specific functions in the __system_clock class.


[endsect]
[section steady_clock]

__steady_clock is useful when you need to wait for a specific amount of time. __steady_clock time can not be reset. As other steady clocks, it is usually based on the processor tick.

Here is a polling solution, but it will probably be too inefficient:

    boost::chrono::__steady_clock::time_point start= chrono::__steady_clock::now();
    boost::chrono::__steady_clock::duration delay= chrono::seconds(5);
    while (boost::chrono::__steady_clock::now() - start <= delay) {}

[endsect]

[section high_resolution_clock]

When available, __high_resolution_clock is usually more expensive than the other system-wide clocks, so they are used only when the provided resolution is required to the application.

[endsect]

[section process_cpu_clock]

Process and thread clocks are used usually to measure the time spent by code blocks, as a basic time-spent profiling of different blocks of code (Boost.Chrono.Stopwatch is a clear example of this use).

[endsect]

[section thread_clock]

You can use __thread_clock whenever you want to measure the time spent by the current thread. For example:

    boost::chrono::__thread_clock::time_point start=boost::chrono::__thread_clock::now();
    // ... do something ...

    typedef boost::chrono::__milliseconds ms;
    ms d = boost::chrono::__thread_clock::now() - start;
    // d now holds the number of milliseconds from start to end.
    std::cout << d.count() << "ms\n";

If you need seconds with a floating-point representation you can do:

    typedef boost::chrono::__duration<double> sec;  // seconds, stored with a double.
    sec d = end - start;
    std::cout << sec.count() << "s\n";

If you would like to programmatically inspect `__thread_clock::duration`, you can get the representation type with `__thread_clock::rep`, and the tick period with `__thread_clock::period` (which should be a type __ratio which has nested values `__ratio::num` and `__ratio::den`).  The tick period of __thread_clock is `__thread_clock::period::num / __thread_clock::period::den` seconds: `1/1000000000` in this case (`1` billionth of a second), stored in a `long long`.

[endsect]
[endsect]

[section I/O]

[section:duration_io duration]

Any __duration can be streamed out to a `basic_ostream`. The run-time value of the __duration is formatted according to the rules and current format settings for __duration`::rep` get_duration_style and the durationpunct facet. 

the format is either

  <value> <unit>

or 

  <unit> <value>



[warning Need to be changed 
This is followed by a single space and then the compile-time unit name of the __duration. This unit name is built on the string returned from `ratio_string<>` and the data used to construct the __duration_punct which was inserted into the stream's locale. If a __duration_punct has not been inserted into the stream's locale, a default constructed __duration_punct will be added to the stream's locale.

__duration unit names come in two varieties: long(prefix) and short(symbol). The default constructed __duration_punct provides names in the long(prefix) format. These names are English descriptions. Other languages are supported by constructing a __duration_punct with the proper spellings for "hours", "minutes" and "seconds", and their abbreviations (for the short format). The short or long format can be easily chosen by streaming a `duration_short()` or `duration_long()` manipulator respectively or using the parameterized manipulator `duration_fmt(duration_style::prefix)` or `duration_fmt(duration_style::symbol)`.

]
__example

    #include <iostream>
    #include <boost/chrono/chrono_io.hpp>

    int main()
    {
        using namespace std;
        using namespace boost;

        cout << "milliseconds(1) = "
             <<  boost::chrono::milliseconds(1) << '\n';

        cout << "milliseconds(3) + microseconds(10) = "
             <<  boost::chrono::milliseconds(3) + boost::chrono::microseconds(10) << '\n';

        cout << "hours(3) + minutes(10) = "
             <<  boost::chrono::hours(3) + boost::chrono::minutes(10) << '\n';

        typedef boost::chrono::duration<long long, boost::ratio<1, 2500000000> > ClockTick;
        cout << "ClockTick(3) + boost::chrono::nanoseconds(10) = "
             <<  ClockTick(3) + boost::chrono::nanoseconds(10) << '\n';

       // ...
        return 0;
    }

The output could be

    milliseconds(1) = 1 microsecond
    milliseconds(3) + microseconds(10) = 3010 microseconds
    hours(3) + minutes(10) = 190 minutes
    ClockTick(3) + nanoseconds(10) = 56 [1/5000000000]seconds

    Set cout to use short names:
    milliseconds(3) + microseconds(10) = 3010 __mus
    hours(3) + minutes(10) = 190 m
    ClockTick(3) + nanoseconds(10) = 56 [1/5000000000]s

    system_clock::now() = 129387415616250000 [1/10000000]s since Jan 1, 1970
    monotonic_clock::now() = 37297387636417 ns since boot

    Set cout to use long names:
    high_resolution_clock::now() = 37297387655134 nanoseconds since boot

As can be seen, each duration type can be streamed without having to manually stream the compile-time units after the run-time value. And when the compile-time unit is known to be a "common unit", English names are used. For "uncommon units" a unit name is composed from the reduced numerator and denominator of the associated __ratio. Whatever stream/locale settings are set for `duration::rep` are used for the value. Additionally, when the value is 1, singular forms for the units are used.

Sometimes it is desired to shorten these names by using the SI symbols instead of SI prefixes. This can be accomplished with the use of the __symbol_format manipulator [footnote __duration_short in V1]:

        cout << "\nSet cout to use short names:\n";
        cout << boost::chrono::symbol_format;

        cout << "milliseconds(3) + microseconds(10) = "
             <<  boost::chrono::milliseconds(3) + boost::chrono::microseconds(10) << '\n';

        cout << "hours(3) + minutes(10) = "
             <<  boost::chrono::hours(3) + boost::chrono::minutes(10) << '\n';

        cout << "ClockTick(3) + nanoseconds(10) = "
             <<  ClockTick(3) + boost::chrono::nanoseconds(10) << '\n';


The output could be

    Set cout to use short names:
    milliseconds(3) + microseconds(10) = 3010 __mus
    hours(3) + minutes(10) = 190 m
    ClockTick(3) + nanoseconds(10) = 56 [1/5000000000]s

    system_clock::now() = 129387415616250000 [1/10000000]s since Jan 1, 1970
    monotonic_clock::now() = 37297387636417 ns since boot

    Set cout to use long names:
    high_resolution_clock::now() = 37297387655134 nanoseconds since boot

The [mu] for microsecond is specified to be U+00B5, encoded as UTF-8, UTF-16 or UTF-32 as appropriate for the stream's character size.

When the format decision is taken at runtime, it could be better to use the parameterized manipulator __duration_fmt as in

  duration_style style;
  //...
  cout << duration_fmt(style);



Parsing a __duration follows rules analogous to the __duration converting constructor. A value and a unit (SI symbol or prefixed) are read from the `basic_istream`. If the __duration has an integral representation, then the value parsed must be exactly representable in the target __duration (after conversion to the target __duration units), else __failbit is set. __durations based on floating-point representations can be parsed using any units that do not cause overflow.

For example a stream containing "5000 milliseconds" can be parsed into seconds, but if the stream contains "3001 ms", parsing into `seconds` will cause __failbit to be set.

__example

    #include <boost/chrono/chrono_io.hpp>
    #include <sstream>
    #include <cassert>

    int main()
    {
        using namespace std;

        istringstream in("5000 milliseconds 4000 ms 3001 ms");
        boost::chrono::seconds d(0);
        in >> d;
        assert(in.good());
        assert(d == seconds(5));
        in >> d;
        assert(in.good());
        assert(d == seconds(4));
        in >> d;
        assert(in.fail());
        assert(d == seconds(4));

        return 0;
    }


Note that a __duration failure may occur late in the parsing process. This means that the characters making up the failed parse in the stream are usually consumed despite the failure to successfully parse.

Sometimes in templated code it is difficult to know what the unit of your duration is. It is all deterministic, and inspect-able. But it can be inconvenient to do so, especially if you just need to print out a "debugging" statement. For example:

    // round to nearest, to even on tie
    template <class __To, class Rep, class Period>
    To
    round(const duration<Rep, Period>& d)
    {
        To t0 = duration_cast<To>(d);
        To t1 = t0;
        ++t1;
        auto diff0 = d - t0;
        cout << "diff0 = " << diff0 << '\n';
        auto diff1 = t1 - d;
        cout << "diff1 = " << diff1 << '\n';
        if (diff0 == diff1)
        {
            if (t0.count() & 1)
                return t1;
            return t0;
        }
        else if (diff0 < diff1)
            return t0;
        return t1;
    }

This is where I/O for duration really shines. The compiler knows what the type of diff0 is and with this proposal that type (with proper units) will automatically be printed out for you. For example:

    milliseconds ms = round<milliseconds>(nanoseconds(123));  // diff0 = 123 nanoseconds
                                                              // diff1 = 999877 nanoseconds
    milliseconds ms = round<milliseconds>(Ticks(44));         // diff0 = 2 [1/3000]seconds
                                                              // diff1 = 1 [1/3000]second

This simple I/O will make duration so much more accessible to programmers.

[endsect]

[section:system_clock_time_point_io `system_clock::time_point`]

[/warning 

This feature has been disable defining `BOOST_CHRONO_DONT_PROVIDE_DATE_IO_FOR_SYSTEM_CLOCK_TIME_POINT` until a fix for the following ticket is found:

- [@http://svn.boost.org/trac/boost/ticket/7547 #7547] time_point<system_clock> input version 2 fails to compile 

- [@http://svn.boost.org/trac/boost/ticket/7546 #7546] time_point<system_clock> output version 2 fails to compile 

In this case the io operators behave like any time_point as defined in next section.

In order to enable this features on compilers working with, you will need to define `BOOST_CHRONO_PROVIDES_DATE_IO_FOR_SYSTEM_CLOCK_TIME_POINT`.

]

__system_clock is special. It is the only clock that has conversions between its `time_point` and `time_t`. C subsequently relates time_t to the [@http://en.wikipedia.org/wiki/Gregorian_calendar Gregorian calendar] via `ctime`, `gmtime`, `localtime`, and `strftime`. Neither C, nor POSIX relate `time_t` to any calendar other than the [@http://en.wikipedia.org/wiki/Gregorian_calendar Gregorian calendar]. ISO 8601 is specified only in terms of the [@http://en.wikipedia.org/wiki/Gregorian_calendar Gregorian calendar].

__Boost_Chrono provides `system_clock::time_point` I/O in terms of the Gregorian calendar, and no other calendar. However as `system_clock::time_point` remains convertible with `time_t`, it is possible for clients to create other calendars which interoperate with `time_t` and subsequently `system_clock::time_point`.

Furthermore, it is existing practice for all major hosted operating systems to store system time in a format which facilitates display as [@http://en.wikipedia.org/wiki/Coordinated_Universal_Time Coordinated Universal Time] (UTC). Therefore __Boost_Chrono provides that the default output for `system_clock::time_point` be in a format that represents a point in time with respect to UTC.

    cout << system_clock::now() << '\n';

could output

    2011-09-15 18:36:59.325132 +0000

This format is strongly influenced by ISO 8601, but places a ' ' between the date and time instead of a 'T'. The former appears to more accurately represent existing practice. A fully numeric format was chosen so as to be understandable to as large a group of human readers as possible. A 24 hour format was chosen for the same reasons.

Of the referenced standards, only ISO 8601 discusses the output of fractional seconds. Neither C nor POSIX have built-in functionality for this. However it appears to be universal (as of this writing) that `system_clock::period` is sub-second. And it seems desirable that if you stream out a `system_clock::time_point`, you ought to be able to stream it back in and get the same value. Therefore the streaming of fractional seconds (at least by default) appears to be unavoidable.

Finally the trailing " +0000" disambiguates the UTC-formatted `system_clock::time_point` from one formatted with respect to the local time zone of the computer. The latter can easily be achieved with:

    cout << time_fmt(local) << system_clock::now() << '\n';

that could result in

    2011-09-15 14:36:59.325132 -0400

Note that `system_clock::time_point` itself is neither UTC, nor the local time. However in practice, `system_clock::time_point` is a count of ticks beyond some epoch which is synchronized with UTC. So as a mobile computer moves across time zones, the time zone traversal does not impact the value of a `system_clock::time_point` produced by `system_clock::now()`. And it is only in formatting it for human consumption that one can choose UTC or the local time zone. C and POSIX treat `time_t` just as __Boost_Chrono treats `system_clock::time_point`:

    tm* gmtime(const time_t* timer) -> UTC
    tm* localtime(const time_t* timer) -> local time

This proposal simply extends the C/POSIX `time_t` functionality to C++ syntax and `system_clock::time_point`.

The `time_fmt()` manipulator is "sticky". It will remain in effect until the stream destructs or until it is changed. The stream can be reset to its default state with:

    cout << time_fmt(utc);

And the formatting can be further customized by using the time format sequences. For example:

    cout << time_fmt(local, "%A %B %e, %Y %r");
    cout << system_clock::now() << '\n';  // Sunday April 24, 2011 02:36:59 PM

When specifying formatting manipulators for wide streams, use wide strings.

You can use the same manipulators with istreams to specify parsing sequences.

Unfortunately there are no formatting/parsing sequences which indicate fractional seconds. __Boost_Chrono does not provide such sequences. In the meantime, one can format and parse fractional seconds for `system_clock::time_point` by defaulting the format, or by using an empty string in `time_fmt()`.

The stream's current locale may impact the parsing/format sequences supplied to the `system_clock::time_point` manipulators (e.g. names of days of the week, and names of months).
[endsect]

[section:other_clocks_time_point_io Other clocks time_point]

Unlike `system_clock::time_point`, the other clocks have no conversion with `time_t`. There is likely no relationship between steady_clock::time_point and UTC at all (UTC is not steady).

In general a __time_point is formatted by outputting its internal __duration followed by a string that describes the __time_point`::clock` epoch. This string will vary for each distinct clock, and for each implementation of the supplied clocks.

    #ifdef BOOST_CHRONO_HAS_CLOCK_STEADY
        cout << "steady_clock::now() = " << boost::chrono::steady_clock::now() << '\n';
    #endif
        cout << "\nSet cout to use long names:\n"
                << boost::chrono::duration_long
                << "high_resolution_clock::now() = "
                << boost::chrono::high_resolution_clock::now() << '\n';

The output could be

    steady_clock::now() = 37297387636417 ns since boot

    Set cout to use long names:
    high_resolution_clock::now() = 37297387655134 nanoseconds since boot


[/
There ...
        cout << "\nsystem_clock::now() = " << boost::chrono::system_clock::now() << '\n';

The output could be
    system_clock::now() = 129387415616250000 [1/10000000]s since Jan 1, 1970

]

Parsing a __time_point involves first parsing a __duration and then parsing the epoch string. If the epoch string does not match that associated with `time_point::clock` then failbit will be set.

__example

    #include <boost/chrono/chrono_io.hpp>
    #include <sstream>
    #include <iostream>
    #include <cassert>

    int main()
    {
        using namespace std;

        boost::chrono::high_resolution_clock::time_point t0 = boost::chrono::high_resolution_clock::now();
        stringstream io;
        io << t0;
        boost::chrono::high_resolution_clock::time_point t1;
        io >> t1;
        assert(!io.fail());
        cout << io.str() << '\n';
        cout << t0 << '\n';
        cout << t1 << '\n';
        boost::chrono::high_resolution_clock::time_point t = boost::chrono::high_resolution_clock::now();
        cout << t << '\n';

        cout << "That took " << t - t0 << '\n';
        cout << "That took " << t - t1 << '\n';

        return 0;
    }

The output could be:

    50908679121461 nanoseconds since boot
    That took 649630 nanoseconds

Here's a simple example to find out how many hours the computer has been up (on this platform):

    #include <boost/chrono/chrono_io.hpp>
    #include <iostream>

    int main()
    {
        using namespace std;
        using namespace boost;

        typedef boost::chrono::time_point<boost::chrono::steady_clock, boost::chrono::duration<double, boost::ratio<3600> > > T;
        T tp = boost::chrono::steady_clock::now();
        std::cout << tp << '\n';
        return 0;
    }

The output could be:

    17.8666 hours since boot

[endsect]

[section Low level I/O]
[/====================]

The I/O interface described in the preceding I/O sections were at the user level. These services are based on low level services that are useful when writing libraries. The low level services are related to access to the associated ios state and locale facets. The design follows the C++ IOStreams standard design:

The library encapsulate the locale-dependent parsing and formatting of __duration into a new facet class. Let's focus on formatting in this example. The concerned facet class is  __duration_put, analogous to time_put, money_put, etc. 

The use of this facet is similar to the time_put facet.

[endsect] [/ Low level I/O]

[endsect] [/ I/O]

[endsect]
[/===============]
[section Examples]
[/===============]



[section Duration]

[/===============]
[section How you Override the Duration's Default Constructor]

Next we show how to override the __duration_s default constructor to do anything you want (in this case set it to zero). All we need to do is to change the representation

    namespace I_dont_like_the_default_duration_behavior {

    template <class R>
    class zero_default
    {
    public:
        typedef R rep;

    private:
        rep rep_;
    public:
        zero_default(rep i = 0) : rep_(i) {}
        operator rep() const {return rep_;}

        zero_default& operator+=(zero_default x) {rep_ += x.rep_; return *this;}
        zero_default& operator-=(zero_default x) {rep_ -= x.rep_; return *this;}
        zero_default& operator*=(zero_default x) {rep_ *= x.rep_; return *this;}
        zero_default& operator/=(zero_default x) {rep_ /= x.rep_; return *this;}

        zero_default  operator+ () const {return *this;}
        zero_default  operator- () const {return zero_default(-rep_);}
        zero_default& operator++()       {++rep_; return *this;}
        zero_default  operator++(int)    {return zero_default(rep_++);}
        zero_default& operator--()       {--rep_; return *this;}
        zero_default  operator--(int)    {return zero_default(rep_--);}

        friend zero_default operator+(zero_default x, zero_default y) {return x += y;}
        friend zero_default operator-(zero_default x, zero_default y) {return x -= y;}
        friend zero_default operator*(zero_default x, zero_default y) {return x *= y;}
        friend zero_default operator/(zero_default x, zero_default y) {return x /= y;}

        friend bool operator==(zero_default x, zero_default y) {return x.rep_ == y.rep_;}
        friend bool operator!=(zero_default x, zero_default y) {return !(x == y);}
        friend bool operator< (zero_default x, zero_default y) {return x.rep_ < y.rep_;}
        friend bool operator<=(zero_default x, zero_default y) {return !(y < x);}
        friend bool operator> (zero_default x, zero_default y) {return y < x;}
        friend bool operator>=(zero_default x, zero_default y) {return !(x < y);}
    };

    typedef boost::chrono::__duration<zero_default<long long>, boost::nano        > nanoseconds;
    typedef boost::chrono::__duration<zero_default<long long>, boost::micro       > microseconds;
    typedef boost::chrono::__duration<zero_default<long long>, boost::milli       > milliseconds;
    typedef boost::chrono::__duration<zero_default<long long>                      > seconds;
    typedef boost::chrono::__duration<zero_default<long long>, boost::ratio<60>   > minutes;
    typedef boost::chrono::__duration<zero_default<long long>, boost::ratio<3600> > hours;
    }

Usage

    using namespace I_dont_like_the_default_duration_behavior;

    milliseconds ms;
    std::cout << ms.count() << '\n';

['See the source file [@boost:libs/chrono/example/i_dont_like_the_default_duration_behavior.cpp example/i_dont_like_the_default_duration_behavior.cpp]]

[endsect]
[/
[/=========================]
[section runtime_resolution]

This example shows how to handle duration with resolution not known until run-time

    class duration
    {
    public:
        typedef long long rep;
    private:
        rep rep_;

        static const double ticks_per_nanosecond;

    public:
        typedef boost::chrono::duration<double, boost::nano> tonanosec;

        duration() {} // = default;
        explicit duration(const rep& r) : rep_(r) {}

        // conversions
        explicit duration(const tonanosec& d)
                : rep_(static_cast<rep>(d.count() * ticks_per_nanosecond)) {}

        // explicit
           operator tonanosec() const {return tonanosec(rep_/ticks_per_nanosecond);}

        // observer

        rep count() const {return rep_;}

        // arithmetic

        duration& operator+=(const duration& d) {rep_ += d.rep_; return *this;}
        duration& operator-=(const duration& d) {rep_ += d.rep_; return *this;}
        duration& operator*=(rep rhs)           {rep_ *= rhs; return *this;}
        duration& operator/=(rep rhs)           {rep_ /= rhs; return *this;}

        duration  operator+() const {return *this;}
        duration  operator-() const {return duration(-rep_);}
        duration& operator++()      {++rep_; return *this;}
        duration  operator++(int)   {return duration(rep_++);}
        duration& operator--()      {--rep_; return *this;}
        duration  operator--(int)   {return duration(rep_--);}

        friend duration operator+(duration x, duration y) {return x += y;}
        friend duration operator-(duration x, duration y) {return x -= y;}
        friend duration operator*(duration x, rep y)      {return x *= y;}
        friend duration operator*(rep x, duration y)      {return y *= x;}
        friend duration operator/(duration x, rep y)      {return x /= y;}

        friend bool operator==(duration x, duration y) {return x.rep_ == y.rep_;}
        friend bool operator!=(duration x, duration y) {return !(x == y);}
        friend bool operator< (duration x, duration y) {return x.rep_ < y.rep_;}
        friend bool operator<=(duration x, duration y) {return !(y < x);}
        friend bool operator> (duration x, duration y) {return y < x;}
        friend bool operator>=(duration x, duration y) {return !(x < y);}
    };

['See the source file [@boost:libs/chrono/example/runtime_resolution.cpp here]]

[endsect]
]
[/================]
[section Saturating]

A "saturating" signed integral type is developed.  This type has +/- infinity and a NaN (like IEEE floating-point) but otherwise obeys signed integral arithmetic. This class is subsequently used as the template parameter Rep in boost::chrono::__duration to demonstrate a duration class that does not silently ignore overflow.

['See the source file [@boost:libs/chrono/example/saturating.cpp example/saturating.cpp]]

[endsect]



[/==================]
[section xtime Conversions]

Example round_up utility:  converts d to To, rounding up for inexact conversions
Being able to *easily* write this function is a major feature!

    #include <boost/chrono.hpp>
    #include <boost/type_traits.hpp>

    #include <iostream>

    template <class To, class Rep, class Period>
    To
    round_up(boost::chrono::duration<Rep, Period> d)
    {
        To result = boost::chrono::duration_cast<To>(d);
        if (result < d)
            ++result;
        return result;
    }

To demonstrate interaction with an xtime-like facility:


    struct xtime
    {
        long sec;
        unsigned long usec;
    };

    template <class Rep, class Period>
    xtime
    to_xtime_truncate(boost::chrono::__duration<Rep, Period> d)
    {
        xtime xt;
        xt.sec = static_cast<long>(boost::chrono::__duration_cast<__seconds>(d).count());
        xt.usec = static_cast<long>(boost::chrono::__duration_cast<__microseconds>(d - __seconds(xt.sec)).count());
        return xt;
    }

    template <class Rep, class Period>
    xtime
    to_xtime_round_up(boost::chrono::__duration<Rep, Period> d)
    {
        xtime xt;
        xt.sec = static_cast<long>(boost::chrono::__duration_cast<__seconds>(d).count());
        xt.usec = static_cast<unsigned long>(round_up<boost::chrono::__microseconds>(d - boost::chrono::__seconds(xt.sec)).count());
        return xt;
    }

    microseconds
    from_xtime(xtime xt)
    {
        return boost::chrono::__seconds(xt.sec) + boost::chrono::__microseconds(xt.usec);
    }

    void print(xtime xt)
    {
        std::cout << '{' << xt.sec << ',' << xt.usec << "}\n";
    }

Usage

        xtime xt = to_xtime_truncate(seconds(3) + boost::chrono::__milliseconds(251));
        print(xt);
        boost::chrono::milliseconds ms = boost::chrono::__duration_cast<boost::chrono::__milliseconds>(from_xtime(xt));
        std::cout << ms.count() << " milliseconds\n";
        xt = to_xtime_round_up(ms);
        print(xt);
        xt = to_xtime_truncate(boost::chrono::seconds(3) + __nanoseconds(999));
        print(xt);
        xt = to_xtime_round_up(boost::chrono::seconds(3) + __nanoseconds(999));
        print(xt);


['See the source file [@boost:libs/chrono/example/xtime.cpp xtime.cpp]]

[endsect]


[endsect]
[section Clocks]

[/==================]
[section Cycle count]

Users can easily create their own clocks, with both points in time and time durations which have a representation and precision of their own choosing. For example if there is a hardware counter which simply increments a count with each cycle of the cpu, one can very easily build clocks, time points and durations on top of that, using only a few tens of lines of code. Such systems can be used to call the time-sensitive threading API's such as sleep, wait on a condition variable, or wait for a mutex lock. The API proposed herein is not sensitive as to whether this is a 300MHz clock (with a 3 1/3 nanosecond tick period) or a 3GHz clock (with a tick period of  1/3 of a nanosecond). And the resulting code will be just as efficient as if the user wrote a special purpose clock cycle counter.


    #include <boost/chrono.hpp>
    #include <boost/type_traits.hpp>
    #include <iostream>

    template <long long speed>
    struct cycle_count
    {
        typedef typename boost::__ratio_multiply__<boost::__ratio<speed>, boost::__mega>::type
            frequency;  // Mhz
        typedef typename boost::__ratio_divide__<boost::__ratio<1>, frequency>::type period;
        typedef long long rep;
        typedef boost::chrono::__duration<rep, period> duration;
        typedef boost::chrono::__time_point<cycle_count> time_point;

        static time_point now()
        {
            static long long tick = 0;
            // return exact cycle count
            return time_point(duration(++tick));  // fake access to clock cycle count
        }
    };

    template <long long speed>
    struct approx_cycle_count
    {
        static const long long frequency = speed * 1000000;  // MHz
        typedef nanoseconds duration;
        typedef duration::rep rep;
        typedef duration::period period;
        static const long long nanosec_per_sec = period::den;
        typedef boost::chrono::__time_point<approx_cycle_count> time_point;

        static time_point now()
        {
            static long long tick = 0;
            // return cycle count as an approximate number of nanoseconds
            // compute as if nanoseconds is only duration in the std::lib
            return time_point(duration(++tick * nanosec_per_sec / frequency));
        }
    };

['See the source file [@boost:libs/chrono/example/cycle_count.cpp cycle_count.cpp]]

[endsect]

[/==================]
[section xtime_clock]

This example demonstrates the use of a timeval-like struct to be used as the representation type for both __duration and __time_point.

    class xtime {
    private:
        long tv_sec;
        long tv_usec;

        void fixup() {
            if (tv_usec < 0) {
                tv_usec += 1000000;
                --tv_sec;
            }
        }

    public:
        explicit xtime(long sec, long usec) {
            tv_sec = sec;
            tv_usec = usec;
            if (tv_usec < 0 || tv_usec >= 1000000) {
                tv_sec += tv_usec / 1000000;
                tv_usec %= 1000000;
                fixup();
            }
        }

        explicit xtime(long long usec) {
            tv_usec = static_cast<long>(usec % 1000000);
            tv_sec  = static_cast<long>(usec / 1000000);
            fixup();
        }

        // explicit
        operator long long() const {return static_cast<long long>(tv_sec) * 1000000 + tv_usec;}

        xtime& operator += (xtime rhs) {
            tv_sec += rhs.tv_sec;
            tv_usec += rhs.tv_usec;
            if (tv_usec >= 1000000) {
                tv_usec -= 1000000;
                ++tv_sec;
            }
            return *this;
        }

        xtime& operator -= (xtime rhs) {
            tv_sec -= rhs.tv_sec;
            tv_usec -= rhs.tv_usec;
            fixup();
            return *this;
        }

        xtime& operator %= (xtime rhs) {
            long long t = tv_sec * 1000000 + tv_usec;
            long long r = rhs.tv_sec * 1000000 + rhs.tv_usec;
            t %= r;
            tv_sec = static_cast<long>(t / 1000000);
            tv_usec = static_cast<long>(t % 1000000);
            fixup();
            return *this;
        }

        friend xtime operator+(xtime x, xtime y) {return x += y;}
        friend xtime operator-(xtime x, xtime y) {return x -= y;}
        friend xtime operator%(xtime x, xtime y) {return x %= y;}

        friend bool operator==(xtime x, xtime y)
            { return (x.tv_sec == y.tv_sec && x.tv_usec == y.tv_usec); }

        friend bool operator<(xtime x, xtime y) {
            if (x.tv_sec == y.tv_sec)
                return (x.tv_usec < y.tv_usec);
            return (x.tv_sec < y.tv_sec);
        }

        friend bool operator!=(xtime x, xtime y) { return !(x == y); }
        friend bool operator> (xtime x, xtime y) { return y < x; }
        friend bool operator<=(xtime x, xtime y) { return !(y < x); }
        friend bool operator>=(xtime x, xtime y) { return !(x < y); }

        friend std::ostream& operator<<(std::ostream& os, xtime x)
            {return os << '{' << x.tv_sec << ',' << x.tv_usec << '}';}
    };

Clock based on timeval-like struct.

    class xtime_clock
    {
    public:
        typedef xtime                                  rep;
        typedef boost::micro                           period;
        typedef boost::chrono::duration<rep, period>   duration;
        typedef boost::chrono::time_point<xtime_clock> time_point;

        static time_point now()
        {
        #if defined(BOOST_CHRONO_WINDOWS_API)
            time_point t(duration(xtime(0)));
            gettimeofday((timeval*)&t, 0);
            return t;

        #elif defined(BOOST_CHRONO_MAC_API)

            time_point t(duration(xtime(0)));
            gettimeofday((timeval*)&t, 0);
            return t;

        #elif defined(BOOST_CHRONO_POSIX_API)
            //time_point t(0,0);

            timespec ts;
            ::clock_gettime( CLOCK_REALTIME, &ts );

            xtime xt( ts.tv_sec, ts.tv_nsec/1000);
            return time_point(duration(xt));

        #endif  // POSIX
        }
    };

Usage of xtime_clock

        std::cout << "sizeof xtime_clock::time_point = " << sizeof(xtime_clock::time_point) << '\n';
        std::cout << "sizeof xtime_clock::duration = " << sizeof(xtime_clock::duration) << '\n';
        std::cout << "sizeof xtime_clock::rep = " << sizeof(xtime_clock::rep) << '\n';
        xtime_clock::duration delay(boost::chrono::milliseconds(5));
        xtime_clock::time_point start = xtime_clock::now();
        while (xtime_clock::now() - start <= delay) {}
        xtime_clock::time_point stop = xtime_clock::now();
        xtime_clock::duration elapsed = stop - start;
        std::cout << "paused " << boost::chrono::::nanoseconds(elapsed).count() << " nanoseconds\n";


['See the source file [@boost:libs/chrono/example/timeval_demo.cpp example/timeval_demo.cpp]]

[endsect]
[endsect]



[/
[/======================================================]
[section Howard Hinnant's original demonstration program]

['See the source file [@boost:libs/chrono/example/time2_demo.cpp example/time2_demo.cpp]]

[endsect]
]
[section Time Point]

[/==================]
[section min Utility]

The user can define a function returning the earliest __time_point as follows:

    template <class __Clock, class __Duration1, class __Duration2>
    typename boost::__common_type<__time_point<__Clock, __Duration1>,
                         __time_point<__Clock, __Duration2> >::type
    min(__time_point<__Clock, __Duration1> t1, __time_point<__Clock, __Duration2> t2)
    {
        return t2 < t1 ? t2 : t1;
    }

Being able to *easily* write this function is a major feature!

    BOOST_AUTO(t1, system_clock::now() + seconds(3));
    BOOST_AUTO(t2, system_clock::now() + nanoseconds(3));
    BOOST_AUTO(t3, min(t1, t2));

['See the source file [@boost:libs/chrono/example/min_time_point.cpp example/min_time_point.cpp]]

[endsect]


[/===============================================================]
[section A Tiny Program that Times How Long Until a Key is Struck]

    #include <boost/chrono.hpp>
    #include <iostream>
    #include <iomanip>

    using namespace boost::chrono;

    template< class __Clock >
    class timer
    {
      typename __Clock::time_point start;
    public:
      timer() : start( __Clock::now() ) {}
      typename __Clock::duration elapsed() const
      {
        return __Clock::now() - start;
      }
      double seconds() const
      {
        return elapsed().count() * ((double)Clock::period::num/Clock::period::den);
      }
    };

    int main()
    {
      timer<__system_clock> t1;
      timer<__steady_clock> t2;
      timer<__high_resolution_clock> t3;

      std::cout << "Type the Enter key: ";
      std::cin.get();

      std::cout << std::fixed << std::setprecision(9);
      std::cout << "system_clock-----------: "
                << t1.seconds() << " seconds\n";
      std::cout << "steady_clock--------: "
                << t2.seconds() << " seconds\n";
      std::cout << "high_resolution_clock--: "
                << t3.seconds() << " seconds\n";

      __system_clock::time_point d4 = __system_clock::now();
      __system_clock::time_point d5 = __system_clock::now();

      std::cout << "\nsystem_clock latency-----------: " << (d5 - d4).count() << std::endl;

      __steady_clock::time_point d6 = __steady_clock::now();
      __steady_clock::time_point d7 = __steady_clock::now();

      std::cout << "steady_clock latency--------: " << (d7 - d6).count() << std::endl;

      __high_resolution_clock::time_point d8 = __high_resolution_clock::now();
      __high_resolution_clock::time_point d9 = __high_resolution_clock::now();

      std::cout << "high_resolution_clock latency--: " << (d9 - d8).count() << std::endl;

      std::time_t now = __system_clock::to_time_t(__system_clock::now());

      std::cout << "\nsystem_clock::now() reports UTC is "
        << std::asctime(std::gmtime(&now)) << "\n";

      return 0;
    }

The output of this program run looks like this:




['See the source file [@boost:libs/chrono/example/await_keystroke.cpp example/await_keystroke.cpp]]

[endsect]

[/
[/===============================================================]
[section Time Command]

    #include <boost/chrono/stopclock.hpp>
    #include <cstdlib>
    #include <string>
    #include <iostream>

    int main( int argc, char * argv[] )
    {
      if ( argc == 1 )
      {
        std::cout << "invoke: timex [-v] command [args...]\n"
          "  command will be executed and timings displayed\n"
          "  -v option causes command and args to be displayed\n";
        return 1;
      }

      std::string s;

      bool verbose = false;
      if ( argc > 1 && *argv[1] == '-' && *(argv[1]+1) == 'v' )
      {
        verbose = true;
        ++argv;
        --argc;
      }

      for ( int i = 1; i < argc; ++i )
      {
        if ( i > 1 ) s += ' ';
        s += argv[i];
      }

      if ( verbose )
        { std::cout << "command: \"" << s.c_str() << "\"\n"; }

      boost::chrono::__stopclock<> t;

      return std::system( s.c_str() );
    }

['See the source file [@boost:libs/chrono/example/timex.cpp example/timex.cpp]]

[endsect]
]

[section 24 Hours Display]

In the example above we take advantage of the fact that __time_points convert as long as they have the same clock, and as long as their internal __durations convert. We also take advantage of the fact that a __duration with a floating-point representation will convert from anything. Finally the I/O system discovers the more readable "hours" unit for our `duration<double, ratio<3600>>`.

There are many other ways to format __durations and __time_points. For example see [@http://en.wikipedia.org/wiki/ISO_8601#Durations ISO 8601]. Instead of coding every possibility into `operator<<`, which would lead to significant code bloat for even the most trivial uses, this document seeks to inform the reader how to write custom I/O when desired.

As an example, the function below streams arbitrary __durations to arbitrary `basic_ostreams` using the format:

    [-]d/hh:mm:ss.cc

Where:

* `d` is the number of `days`
* `h` is the number of `hours`
* `m` is the number of `minutes`
* `ss.cc` is the number of `seconds` rounded to the nearest hundredth of a second

    #include <boost/chrono/chrono_io.hpp>
    #include <ostream>
    #include <iostream>

    // format duration as [-]d/hh::mm::ss.cc
    template <class CharT, class Traits, class Rep, class Period>
    std::basic_ostream<CharT, Traits>&
    display(std::basic_ostream<CharT, Traits>& os,
            boost::chrono::duration<Rep, Period> d)
    {
        using namespace std;
        using namespace boost;

        typedef boost::chrono::duration<long long, boost::ratio<86400> > days;
        typedef boost::chrono::duration<long long, boost:centi> centiseconds;

        // if negative, print negative sign and negate
        if (d < boost::chrono::duration<Rep, Period>(0))
        {
            d = -d;
            os << '-';
        }
        // round d to nearest centiseconds, to even on tie
        centiseconds cs = boost::chrono::duration_cast<centiseconds>(d);
        if (d - cs > boost::chrono::milliseconds(5)
            || (d - cs == boost::chrono::milliseconds(5) && cs.count() & 1))
            ++cs;
        // separate seconds from centiseconds
        boost::chrono::seconds s = boost::chrono::duration_cast<boost::chrono::seconds>(cs);
        cs -= s;
        // separate minutes from seconds
        boost::chrono::minutes m = boost::chrono::duration_cast<boost::chrono::minutes>(s);
        s -= m;
        // separate hours from minutes
        boost::chrono::hours h = boost::chrono::duration_cast<boost::chrono::hours>(m);
        m -= h;
        // separate days from hours
        days dy = boost::chrono::duration_cast<days>(h);
        h -= dy;
        // print d/hh:mm:ss.cc
        os << dy.count() << '/';
        if (h < boost::chrono::hours(10))
            os << '0';
        os << h.count() << ':';
        if (m < boost::chrono::minutes(10))
            os << '0';
        os << m.count() << ':';
        if (s < boost::chrono::seconds(10))
            os << '0';
        os << s.count() << '.';
        if (cs < boost::chrono::centiseconds(10))
            os << '0';
        os << cs.count();
        return os;
    }

    int main()
    {
        using namespace std;
        using namespace boost;

        display(cout, boost::chrono::steady_clock::now().time_since_epoch()
                      + boost::chrono::duration<long, boost::mega>(1)) << '\n';
        display(cout, -boost::chrono::milliseconds(6)) << '\n';
        display(cout, boost::chrono::duration<long, boost::mega>(1)) << '\n';
        display(cout, -boost::chrono::duration<long, boost::mega>(1)) << '\n';
    }

The output could be:

    12/06:03:22.95
    -0/00:00:00.01
    11/13:46:40.00
    -11/13:46:40.00

[endsect]


[/=======================================================]
[section Simulated Thread Interface Demonstration Program]

The C++11 standard library's multi-threading library requires the ability to deal with the representation of time in a manner consistent with modern C++ practices. Next is a simulation of this interface.

The non-member sleep functions can be emulated as follows:

    namespace boost { namespace this_thread {

    template <class Rep, class Period>
    void sleep_for(const chrono::__duration<Rep, Period>& d) {
        chrono::__microseconds t = chrono::__duration_cast<chrono::__microseconds>(d);
        if (t < d)
            ++t;
        if (t > chrono::__microseconds(0))
            std::cout << "sleep_for " << t.count() << " microseconds\n";
    }

    template <class __Clock, class __Duration>
    void sleep_until(const chrono::__time_point<__Clock, __Duration>& t) {
        using namespace chrono;
        typedef __time_point<__Clock, __Duration> Time;
        typedef __system_clock::time_point SysTime;
        if (t > __Clock::now()) {
            typedef typename __common_type<typename Time::duration,
                                         typename SysTime::duration>::type D;
            /* auto */ D d = t - __Clock::now();
            microseconds us = __duration_cast<__microseconds>(d);
            if (us < d)
                ++us;
            SysTime st = __system_clock::now() + us;
            std::cout << "sleep_until    ";
            detail::print_time(st);
            std::cout << " which is " << (st - __system_clock::now()).count() << " microseconds away\n";
        }
    }

    }}


Next is the `boost::thread::timed_mutex` modified functions

    namespace boost {
    struct timed_mutex {
        // ...

        template <class Rep, class Period>
        bool try_lock_for(const chrono::__duration<Rep, Period>& d) {
            chrono::__microseconds t = chrono::__duration_cast<chrono::__microseconds>(d);
            if (t <= chrono::__microseconds(0))
                return try_lock();
            std::cout << "try_lock_for " << t.count() << " microseconds\n";
            return true;
        }

        template <class __Clock, class __Duration>
        bool try_lock_until(const chrono::__time_point<__Clock, __Duration>& t)
        {
            using namespace chrono;
            typedef __time_point<__Clock, __Duration> Time;
            typedef __system_clock::time_point SysTime;
            if (t <= __Clock::now())
                return try_lock();
            typedef typename __common_type<typename Time::duration,
              typename __Clock::duration>::type D;
            /* auto */ D d = t - __Clock::now();
            microseconds us = __duration_cast<__microseconds>(d);
            SysTime st = __system_clock::now() + us;
            std::cout << "try_lock_until ";
            detail::print_time(st);
            std::cout << " which is " << (st - __system_clock::now()).count()
              << " microseconds away\n";
            return true;
        }
    };
    }

`boost::thread::condition_variable` time related function are modified as follows:

    namespace boost {
    struct condition_variable
    {
        // ...

        template <class Rep, class Period>
        bool wait_for(mutex&, const chrono::__duration<Rep, Period>& d) {
            chrono::microseconds t = chrono::__duration_cast<chrono::microseconds>(d);
            std::cout << "wait_for " << t.count() << " microseconds\n";
            return true;
        }

        template <class __Clock, class __Duration>
        bool wait_until(mutex&, const chrono::__time_point<__Clock, __Duration>& t) {
            using namespace boost::chrono;
            typedef __time_point<__Clock, __Duration> Time;
            typedef __system_clock::time_point SysTime;
            if (t <= __Clock::now())
                return false;
            typedef typename __common_type<typename Time::duration,
              typename __Clock::duration>::type D;
            /* auto */ D d = t - __Clock::now();
            microseconds us = __duration_cast<__microseconds>(d);
            SysTime st = __system_clock::now() + us;
             std::cout << "wait_until     ";
            detail::print_time(st);
            std::cout << " which is " << (st - __system_clock::now()).count()
              << " microseconds away\n";
            return true;
        }
    };
    }

Next follows how simple is the usage of this functions:

    boost::mutex m;
    boost::timed_mutex mut;
    boost::condition_variable cv;

    using namespace boost;

    this_thread::sleep_for(chrono::__seconds(3));
    this_thread::sleep_for(chrono::__nanoseconds(300));
    chrono::__system_clock::time_point time_limit = chrono::__system_clock::now() + chrono::__seconds_(4) + chrono::__milliseconds(500);
    this_thread::sleep_until(time_limit);

    mut.try_lock_for(chrono::__milliseconds(30));
    mut.try_lock_until(time_limit);

    cv.wait_for(m, chrono::__minutes(1));    // real code would put this in a loop
    cv.wait_until(m, time_limit);  // real code would put this in a loop

    // For those who prefer floating-point
    this_thread::sleep_for(chrono::__duration<double>(0.25));
    this_thread::sleep_until(chrono::__system_clock::now() + chrono::__duration<double>(1.5));


['See the source file [@boost:libs/chrono/example/simulated_thread_interface_demo.cpp example/simulated_thread_interface_demo.cpp]]

[endsect]

[endsect]
[section IO]
[/=======================================================]
[section:french French Output]

Example use of output in French

    #include <boost/chrono/chrono_io.hpp>
    #include <iostream>
    #include <locale>

    int main()
    {
        using namespace std;
        using namespace boost;
        using namespace boost::chrono;

        cout.imbue(locale(locale(), new duration_punct<char>
            (
                duration_punct<char>::use_long,
                "secondes", "minutes", "heures",
                "s", "m", "h"
            )));
        hours h(5);
        minutes m(45);
        seconds s(15);
        milliseconds ms(763);
        cout << h << ", " << m << ", " << s << " et " << ms << '\n';
    }

Output is:

    5 heures, 45 minutes, 15 secondes et 763 millisecondes

['See the source file [@boost:libs/chrono/example/french.cpp example/french.cpp]]

[endsect] [/section:french French Output]
[endsect] [/section IO]

[endsect] [/section Examples]


[/================================]
[section:ext_references External Resources]
[/================================]

[variablelist

[
    [[@http://www.open-std.org/jtc1/sc22/wg21 [*C++ Standards Committee's current Working Paper]]]
    [The most authoritative reference material for the library is the C++ Standards Committee's current Working Paper (WP).  20.11 Time utilities "time"]
]

[
    [[@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2661.htm [*N2661 - A Foundation to Sleep On]]]
    [From Howard E. Hinnant, Walter E. Brown, Jeff Garland  and Marc Paterno. Is very informative and provides motivation for key design decisions]
]


[
    [[@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2010/n3134.html#934 [*LGW 934. duration is missing operator%]]]
    [From Terry Golubiewski. Is very informative and provides motivation for key design decisions]
]

[
    [[@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2010/n3135.html#935 [*LGW 935. clock error handling needs to be specified]]]
    [From Beman Dawes. This issue has been stated as NAD Future.]
]

]

[endsect]

[endsect]

[/=================]
[section:reference Reference ]
[/=================]

As `constexpr` will not be supported by some compilers, it is replaced in the code by `BOOST_CONSTEXPR` for `constexpr` functions and `BOOST_STATIC_CONSTEXPR` for struct/class static fields. The same applies to `noexecpt` which is replaced by `noexcept` in the code.

The documentation doesn't use these macros.

[/=============================================]
[section:chrono_include_hpp Header `<boost/chrono/include.hpp>`]
[/=============================================]

Include all the chrono header files.

    #include <boost/chrono/chrono.hpp>
    #include <boost/chrono/chrono_io.hpp>
    #include <boost/chrono/process_cpu_clocks.hpp>
    #include <boost/chrono/thread_clocks.hpp>
    #include <boost/chrono/ceil.hpp>
    #include <boost/chrono/floor.hpp>
    #include <boost/chrono/round.hpp>

[endsect]

[section:cpp0x Included on the C++11 Recommendation]

[/=============================================]
[section:chrono_hpp Header `<boost/chrono.hpp>`]
[/=============================================]

Include only the standard files.

    #include <boost/chrono/chrono.hpp>

[endsect]

[/=============================================]
[section:chrono_chrono_hpp Header `<boost/chrono/chrono.hpp>`]
[/=============================================]

Include only the standard files.

    #include <boost/chrono/duration.hpp>
    #include <boost/chrono/time_point.hpp>
    #include <boost/chrono/system_clocks.hpp>
    #include <boost/chrono/typeof/boost/chrono/chrono.hpp>

[section:limitations Limitations and Extensions]

At present, there is no know limitation respect to the C++11 standard.

The current implementation provides in addition:

* clock error handling as specified in [@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2010/n3135.html#935 clock error handling needs to be specified].
* process and thread clocks.


[endsect] [/section:limitations Limitations and Extensions]

[section:conf Configuration Macros]

[section:assert How Assert Behaves?]

When `BOOST_NO_CXX11_STATIC_ASSERT` is defined, the user can select the way static assertions are reported. Define

* `BOOST_CHRONO_USES_STATIC_ASSERT`: define it if you want to use Boost.StaticAssert.
* `BOOST_CHRONO_USES_MPL_ASSERT`: define it if you want to use Boost.MPL static assertions.
* `BOOST_CHRONO_USES_ARRAY_ASSERT`: define it if you want to use internal static assertions.

The default behavior is as `BOOST_CHRONO_USES_ARRAY_ASSERT` was defined.

When `BOOST_CHRONO_USES_MPL_ASSERT` is not defined the following symbols are defined as

    #define BOOST_CHRONO_A_DURATION_REPRESENTATION_CAN_NOT_BE_A_DURATION \
        "A duration representation can not be a duration"
    #define BOOST_CHRONO_SECOND_TEMPLATE_PARAMETER_OF_DURATION_MUST_BE_A_STD_RATIO \
        "Second template parameter of duration must be a boost::ratio"
    #define BOOST_CHRONO_DURATION_PERIOD_MUST_BE_POSITIVE \
        "duration period must be positive"
    #define BOOST_CHRONO_SECOND_TEMPLATE_PARAMETER_OF_TIME_POINT_MUST_BE_A_BOOST_CHRONO_DURATION \
        "Second template parameter of time_point must be a boost::chrono::duration"

Depending on the static assertion used system you will have an hint of the failing assertion either through the symbol or through the text.

[endsect]

[section:no_hybrid Don't provide Hybrid Error Handling]

When `BOOST_CHRONO_DONT_PROVIDE_HYBRID_ERROR_HANDLING` is defined the lib doesn't provides the hybrid error handling prototypes:

  Clock::time_point Clock::now(system::error_code&ec=boost::throws());

This allow to be closer to the standard and to avoid the Boost.System dependency, making possible to have Boost.Chrono as a header-only library.

By default `BOOST_CHRONO_PROVIDE_HYBRID_ERROR_HANDLING` is defined.

[endsect] [/section:header_only How to Build Boost.Chrono as a Header Only Library?]

[section:header_only How to Build Boost.Chrono as a Header Only Library?]

When `BOOST_CHRONO_HEADER_ONLY` is defined the lib is header-only.

If in addition `BOOST_USE_WINDOWS_H` is defined `<windows.h>` is included, otherwise files in `boost/detail/win` are used to reduce the impact of including `<windows.h>`.

However, you will either need to define `BOOST_CHRONO_DONT_PROVIDE_HYBRID_ERROR_HANDLING` or link with Boost.System.

[endsect] [/section:header_only How to Build Boost.Chrono as a Header Only Library?]

[section:deprecated_io Deprecated IO]

Version 2.0.0 deprecates the preceding IO features. 

However the default version is always version 1. 
If you don't want to include the deprecated features you could define `BOOST_CHRONO_DONT_PROVIDES_DEPRECATED_IO_SINCE_V2_0_0`.

[/
These deprecated features will be provided by default up to boost 1.55. 
Since 1.55 these features will not be included any more by default. 
Since this version, if you want to include the deprecated features yet you could define `BOOST_CHRONO_PROVIDES_DEPRECATED_IO_SINCE_V2_0_0`. 
These id features will be only available until boost 1.58, that is you have 1 year and a half to move to the new features.
]

[endsect]


[section:system_clock_time_point time_point<system_clock,D> specialization limitation]

[/warning

The time_point<system_clock,D> formatter/parser specializations don't work yet. It has been disable defining `BOOST_CHRONO_DONT_PROVIDE_DATE_IO_FOR_SYSTEM_CLOCK_TIME_POINT` until a fix for the following ticket is found:

- [@http://svn.boost.org/trac/boost/ticket/7547 #7547] time_point<system_clock> input version 2 fails to compile 

- [@http://svn.boost.org/trac/boost/ticket/7546 #7546] time_point<system_clock> output version 2 fails to compile 

In this case the io operators behave like any time_point as if the specialization was removed.

In order to enable this features on compilers working with, you will need to define `BOOST_CHRONO_PROVIDES_DATE_IO_FOR_SYSTEM_CLOCK_TIME_POINT`.

]

[endsect]

[section:version Version]

`BOOST_CHRONO_VERSION` defines the Boost.Chrono version. 
The default version is 1. In this case the following breaking or extending macros are defined if the opposite is not requested: 

* `BOOST_CHRONO_PROVIDES_DEPRECATED_IO_SINCE_V2_0_0`

The user can request the version 2 by defining `BOOST_CHRONO_VERSION` to 2. In this case the following breaking or extending macros are defined if the opposite is not requested: 

* Breaking change `BOOST_CHRONO_DONT_PROVIDES_DEPRECATED_IO_SINCE_V2_0_0 `

The default value for `BOOST_CHRONO_VERSION` will be changed to 2 since Boost 1.55.

[endsect]


[endsect] [/section:conf Configuration Macros]

[endsect] [/section:chrono_hpp Header `<boost/chrono.hpp>`]


[/=============================================]
[section:duration_hpp Header `<boost/chrono/duration.hpp>`]
[/=============================================]

This file contains duration specific classes and non-member functions.

    namespace boost {
      namespace chrono {

        template <class Rep, class Period = __ratio<1> >  class __duration;

      }
      template <class Rep1, class Period1, class Rep2, class Period2>
      struct __common_type_spe<duration<Rep1, Period1>,
                         duration<Rep2, Period2> >;

      namespace chrono {

        // customization traits
        template <class Rep> struct __treat_as_floating_point;
        template <class Rep> struct __duration_values;

        // duration arithmetic
        template <class Rep1, class Period1, class Rep2, class Period2>
        constexpr
        typename common_type<duration<Rep1, Period1>, duration<Rep2, Period2> >::type
        __duration__op_plus_1(
            const duration<Rep1, Period1>& lhs,
            const duration<Rep2, Period2>& rhs);

        template <class Rep1, class Period1, class Rep2, class Period2>
        constexpr
        typename common_type<duration<Rep1, Period1>, duration<Rep2, Period2> >::type
        __duration__op_minus_1(
            const duration<Rep1, Period1>& lhs,
            const duration<Rep2, Period2>& rhs);

        template <class Rep1, class Period, class Rep2>
        constexpr
        duration<typename common_type<Rep1, Rep2>::type, Period>
        __duration__op_mult_1(
            const duration<Rep1, Period>& d,
            const Rep2& s);

        template <class Rep1, class Period, class Rep2>
        constexpr
        duration<typename common_type<Rep1, Rep2>::type, Period>
        __duration__op_mult_2(
            const Rep1& s,
            const duration<Rep2, Period>& d);

        template <class Rep1, class Period, class Rep2>
        constexpr
        duration<typename common_type<Rep1, Rep2>::type, Period>
        __duration__op_div_2(
            const duration<Rep1, Period>& d,
            const Rep2& s);

        template <class Rep1, class Period1, class Rep2, class Period2>
        constexpr
        typename common_type<Rep1, Rep2>::type
        __duration__op_div_3(
            const duration<Rep1, Period1>& lhs,
            const duration<Rep2, Period2>& rhs);

        #ifdef BOOST_CHRONO_EXTENSIONS
        // Used to get frequency of events
        template <class Rep1, class Rep2, class Period>
        constexpr
        double __duration__op_div_1(
            const Rep1& s,
            const duration<Rep2, Period>& d);
        #endif

        // duration comparisons
        template <class Rep1, class Period1, class Rep2, class Period2>
        constexpr bool __duration__op_eq_1(
            const duration<Rep1, Period1>& lhs,
            const duration<Rep2, Period2>& rhs);

        template <class Rep1, class Period1, class Rep2, class Period2>
        constexpr bool __duration__op_neq_1(
            const duration<Rep1, Period1>& lhs,
            const duration<Rep2, Period2>& rhs);

        template <class Rep1, class Period1, class Rep2, class Period2>
        constexpr bool __duration__op_le_1(
            const duration<Rep1, Period1>& lhs,
            const duration<Rep2, Period2>& rhs);
        template <class Rep1, class Period1, class Rep2, class Period2>
        constexpr bool __duration__op_leq_1(
            const duration<Rep1, Period1>& lhs,
            const duration<Rep2, Period2>& rhs);

        template <class Rep1, class Period1, class Rep2, class Period2>
        constexpr bool __duration__op_gt_1(
            const duration<Rep1, Period1>& lhs,
            const duration<Rep2, Period2>& rhs);
        template <class Rep1, class Period1, class Rep2, class Period2>
        constexpr bool __duration__op_geq_1(
            const duration<Rep1, Period1>& lhs,
            const duration<Rep2, Period2>& rhs);

        // duration_cast

        template <class ToDuration, class Rep, class Period>
        constexpr
        ToDuration __duration_cast(const duration<Rep, Period>& d);

        // convenience typedefs
        typedef duration<boost::int_least64_t, nano> __nanoseconds;    // at least 64 bits needed
        typedef duration<boost::int_least64_t, micro> __microseconds;  // at least 55 bits needed
        typedef duration<boost::int_least64_t, milli> __milliseconds;  // at least 45 bits needed
        typedef duration<boost::int_least64_t> __seconds;              // at least 35 bits needed
        typedef duration<boost::int_least32_t, ratio< 60> > __minutes; // at least 29 bits needed
        typedef duration<boost::int_least32_t, ratio<3600> > __hours;  // at least 23 bits needed

      }
    }


[section:traits Time-related Traits]

[section:treat_as_floating_point Metafunction `treat_as_floating_point<>`]

    template <class Rep> struct treat_as_floating_point
        : boost::is_floating_point<Rep> {};

The __duration template uses the __treat_as_floating_point trait to help determine if a __duration with one tick period can be converted to another __duration with a different tick period. If `treat_as_floating_point<Rep>::value` is `true`, then `Rep` is a floating-point type and implicit conversions are allowed among __durations. Otherwise, the implicit convertibility depends on the tick periods of the __durations. If `Rep` is a class type which emulates a floating-point type, the author of `Rep` can specialize __treat_as_floating_point so that __duration will treat this `Rep` as if it were a floating-point type. Otherwise `Rep` is assumed to be an integral type, or a class emulating an integral type.


[endsect]
[section:duration_values Class Template `duration_values`]

    template <class Rep>
    struct duration_values
    {
    public:
        static constexpr Rep __zero();
        static constexpr Rep __max();
        static constexpr Rep __min();
    };

The __duration template uses the __duration_values trait to construct special values of the __duration_s representation (`Rep`). This is done because the representation might be a class type with behavior which requires some other implementation to return these special values. In that case, the author of that class type should specialize __duration_values to return the indicated values.

[section:zero Static Member Function `zero()`]

    static constexpr Rep zero();

__returns `Rep(0)`. [*Note:] `Rep(0)` is specified instead of `Rep()` since `Rep()` may have some other meaning, such as an uninitialized value.

__remarks The value returned corresponds to the additive identity.

[endsect]
[section:max Static Member Function `max()`]

    static constexpr Rep max();

__returns `numeric_limits<Rep>::max()`.

__remarks The value returned compares greater than zero().

[endsect]
[section:min Static Member Function `min()`]

    static constexpr Rep min();

__returns `numeric_limits<Rep>::lowest()`.

__remarks The value returned compares less than or equal to `zero()`.

[endsect]

[endsect]

[endsect]

[section:common_type_spe `common_type` Specialization]

    template <class Rep1, class Period1, class Rep2, class Period2>
    struct __common_type<chrono::__duration<Rep1, Period1>, chrono::__duration<Rep2, Period2> >
    {
        typedef chrono::__duration<typename __common_type<Rep1, Rep2>::type, __see_bellow__> type;
    };

The period of the __duration indicated by this specialization of __common_type is the greatest common divisor of `Period1` and `Period2`. This can be computed by forming a __ratio of the greatest common divisor of `Period1::num` and `Period2::num`, and the least common multiple of `Period1::den` and `Period2::den`.

[*Note:] The typedef type is the __duration with the largest tick period possible where both __duration arguments will convert to it without requiring a division operation. The representation of this type is intended to be able to hold any value resulting from this conversion, with the possible exception of round-off error when floating-point __durations are involved (but not truncation error).

[endsect]


[section:duration Class Template `duration<>`]

A __duration measures time between two points in time (__time_point). A __duration has a representation which holds a count of ticks, and a tick period. The tick period is the amount of time which occurs from one tick to another in units of a second. It is expressed as a rational constant using __ratio.

    namespace boost { namespace chrono {

        template <class Rep, class Period>
        class duration {
        public:
            typedef Rep rep;
            typedef Period period;
        private:
            rep rep_; // exposition only
        public:
            constexpr __duration__c_0();
            template <class Rep2>
            constexpr explicit __duration__c_1(const Rep2& r);

            template <class Rep2, class Period2>
            constexpr __duration__c_2(const duration<Rep2, Period2>& d);

            duration& operator=(const duration&) = default;

            constexpr rep __duration__count() const;

            constexpr duration  __duration__op_plus();
            constexpr duration  __duration__op_minus();
            duration& __duration__op_pre_inc();
            duration  __duration__op_post_inc(int);
            duration& __duration__op_pre_dec();
            duration  __duration__op_post_dec(int);

            duration& __duration__op_plus_eq(const duration& d);
            duration& __duration__op_minus_eq(const duration& d);

            duration& __duration__op_mult_eq(const rep& rhs);
            duration& __duration__op_div_eq(const rep& rhs);
            duration& __duration__op_mod_eq(const rep& rhs);
            duration& __duration__op_mod_eq2(const duration& rhs);

            static constexpr duration __duration__zero();
            static constexpr duration __duration__min();
            static constexpr duration __duration__max();
        };

    }}

`Rep` must be an arithmetic type, or a class emulating an arithmetic type, compile diagnostic otherwise. If __duration is instantiated with the type of `Rep` being a __duration, compile diagnostic is issued.

`Period` must be an instantiation of `ratio`, compile diagnostic otherwise.

`Period::num` must be positive, compile diagnostic otherwise.

Examples:

* `__duration<long, __ratio<60> >` holds a count of minutes using a long.

* `__duration<long long, milli>` holds a count of milliseconds using a long long.

* `__duration<double, __ratio<1, 30> >` holds a count using a double with a tick period of 1/30 second (a tick frequency of 30 Hz).

The following members of __duration do not throw an exception unless the indicated operations on the representations throw an exception.

[section:duration_c_0 Constructor `duration()`]

            constexpr duration();

__effects  Constructs an object of type __duration initialized from `duration_values<rep>::zero()` in C++98 or `BOOST_CHRONO_DURATION_DEFAULTS_TO_ZERO` is defined, otherwise the duration is uninitialized.

[endsect]

[section:duration_c_1 Constructor `duration(const Rep2&)`]

    template <class Rep2>
    constexpr explicit duration(const Rep2& r);

__remarks `Rep2` is implicitly convertible to `rep`, and

* `treat_as_floating_point<rep>::value` is `true`, or
* `!treat_as_floating_point<rep>::value && !treat_as_floating_point<Rep2>::value` is `true`.

If these constraints are not met, this constructor will not participate in overload resolution. [*Note:] This requirement prevents construction of an integral-based __duration with a floating-point representation. Such a construction could easily lead to confusion about the value of the __duration.

__example

        __duration<int, milli> d(3.5);  // do not compile
        __duration<int, milli> d(3);    // ok

__effects  Constructs an object of type __duration.

__post_conditions `count() == static_cast<rep>(r)`.

[endsect]
[section:duration_c_2 Constructor `duration(const duration&)`]

    template <class Rep2, class Period2>
    constexpr __duration(const __duration<Rep2, Period2>& d);

__remarks `treat_as_floating_point<rep>::value`, or `ratio_divide<Period2, period>::type::den == 1`, else this constructor will not participate in overload resolution. [*note] This requirement prevents implicit truncation error when converting between integral-based __durations. Such a construction could easily lead to confusion about the value of the __duration.

__example

        __duration<int, milli> ms(3);
        __duration<int, micro> us = ms;  // ok
        __duration<int, milli> ms2 = us; // do not compile

__effects  Constructs an object of type __duration, constructing `rep_` from `duration_cast<__duration>(d).count()`.

[endsect]
[section:count Member Function `count() const`]

    constexpr rep count() const;

__returns `rep_`.

[endsect]
[section:op_unary_plus Member Function `operator+() const`]

    constexpr __duration operator+() const;

__returns `*this`.

[endsect]
[section:op_unary_minus Member Function `operator-() const`]

    constexpr __duration operator-() const;

__returns `__duration(-rep_)`.

[endsect]
[section:op_pre_inc Member Function `operator++()`]

    __duration& operator++();

__effects  `++rep_`.

__returns `*this`.

[endsect]
[section:op_post_inc Member Function `operator++(int)`]

    __duration operator++(int);

__returns `__duration(rep_++)`.

[endsect]
[section:op_pre_dec Member Function `operator--()`]

    __duration& operator--();

__effects  `--rep_`.

__returns `*this`.

[endsect]
[section:op_post_dec Member Function `operator--(int)`]

    __duration operator--(int);

__returns `__duration(rep_--)`.

[endsect]
[section:op_plus_eq Member Function `operator+=(const duration&)`]

    __duration& operator+=(const __duration& d);

__effects  `rep_ += d.count()`.

__returns `*this`.

[endsect]
[section:op_minus_eq Member Function `operator-=(const duration&)`]

    __duration& operator-=(const __duration& d);

__effects  `rep_ -= d.count()`.

__returns `*this`.

[endsect]
[section:op_mod_eq Member Function `operator%=(const duration&)`]

    __duration& operator%=(const __duration& d);

__effects  `rep_ %= d.count()`.

__returns `*this`.

[endsect]
[section:op_mult_eq Member Function `operator*=(const rep&)`]

    __duration& operator*=(const rep& rhs);

__effects  `rep_ *= rhs`.

__returns `*this`.

[endsect]
[section:op_div_eq Member Function `operator/=(const rep&)`]

    __duration& operator/=(const rep& rhs);

__effects  `rep_ /= rhs`.

__returns `*this`.

[endsect]
[section:op_mod_eq2 Member Function `operator%=(const rep&)`]

    __duration& operator%=(const rep& rhs);

__effects  `rep_ %= rhs`.

__returns `*this`.

[endsect]


[section:zero Static Member Function `zero()`]

    static constexpr __duration zero();

__returns `__duration(__duration_values<rep>::zero())`.

[endsect]
[section:min Static Member Function `min()`]

    static constexpr __duration min();

__returns `__duration(__duration_values<rep>::min()).`

[endsect]
[section:max Static Member Function `max()`]

    static constexpr __duration max();

__returns `__duration(__duration_values<rep>::max())`.

[endsect]

[endsect]


[section:arith `duration` Non-Member Arithmetic]

[section:duration__op_plus_1 Non-Member Function `operator+(duration,duration)`]

    template <class Rep1, class Period1, class Rep2, class Period2>
    constexpr
    typename __common_type<__duration<Rep1, Period1>, __duration<Rep2, Period2> >::type
    operator+(const __duration<Rep1, Period1>& lhs, const __duration<Rep2, Period2>& rhs);

__returns `CD(CD(lhs).count() + CD(rhs).count())` where `CD` is the type of the return value.

[endsect]
[section:duration__op_minus_1 Non-Member Function `operator-(duration,duration)`]

    template <class Rep1, class Period1, class Rep2, class Period2>
    constexpr
    typename __common_type<__duration<Rep1, Period1>, __duration<Rep2, Period2> >::type
    operator-(const __duration<Rep1, Period1>& lhs, const __duration<Rep2, Period2>& rhs);

__returns `CD(CD(lhs).count() - CD(rhs).count())` where `CD` is the type of the return value.

[endsect]
[section:duration__op_mult_1 Non-Member Function `operator*(duration,Rep1)`]

    template <class Rep1, class Period, class Rep2>
    constexpr
    __duration<typename __common_type<Rep1, Rep2>::type, Period>
    operator*(const __duration<Rep1, Period>& d, const Rep2& s);

__requires Let `CR` represent the __common_type of `Rep1` and `Rep2`. This function will not participate in overload resolution unless both `Rep1` and `Rep2` are implicitly convertible to `CR`.

__returns `CD(CD(d).count() * s)` where `CD` is the type of the return value.

[endsect]
[section:duration__op_mult_2 Non-Member Function `operator*(Rep1,duration)`]

    template <class Rep1, class Period, class Rep2>
    constexpr
    __duration<typename __common_type<Rep1, Rep2>::type, Period>
    operator*(const Rep1& s, const __duration<Rep2, Period>& d);

__requires Let `CR` represent the __common_type of `Rep1` and `Rep2`. This function will not participate in overload resolution unless both `Rep1` and `Rep2` are implicitly convertible to `CR`.

__returns `d * s`.

[endsect]


[section:duration__op_div_1 Non-Member Function `operator/(duration,Rep2)`]

    template <class Rep1, class Period, class Rep2>
    constexpr
    __duration<typename __common_type<Rep1, Rep2>::type, Period>
    operator/(const __duration<Rep1, Period>& d, const Rep2& s);

__requires Let `CR` represent the __common_type of `Rep1` and `Rep2`. This function will not participate in overload resolution unless both `Rep1` and `Rep2` are implicitly convertible to `CR`, and `Rep2` is  not an instantiation of __duration.

__returns `CD(CD(d).count() / s)` where `CD` is the type of the return value.

[endsect]
[section:duration__op_div_2 Non-Member Function `operator/(duration,duration)`]

    template <class Rep1, class Period1, class Rep2, class Period2>
    constexpr
    typename __common_type<Rep1, Rep2>::type
    operator/(const __duration<Rep1, Period1>& lhs, const __duration<Rep2, Period2>& rhs);

__remarks Let `CD` represent the __common_type of the two __duration arguments.
__returns Returns `CD(lhs).count() / CD(rhs).count()`.

[endsect]

[section:duration__op_div_3 Non-Member Function `operator/(Rep1,duration)`]

Included only if BOOST_CHRONO_EXTENSIONS is defined.

This overloading could be used to get the frequency of an event counted by `Rep1`.

    template <class Rep1, class Rep2, class Period>
    constexpr
    double operator/(const Rep1& s, const __duration<Rep2, Period>& d);

__remarks Let `CR` represent the __common_type of `Rep1` and `Rep2`. This function will not participate in overload resolution unless both `Rep1` and `Rep2` are implicitly convertible to `CR`, and `Rep1` is  not an instantiation of __duration. Let `CD` represent __duration<CR,Period>.

__returns `CR(s)/CD(d).count()` where `CD` is the type of the return value.


[endsect]


[section:duration__op_mod_1 Non-Member Function `operator%(duration,Rep2)`]

  template <class Rep1, class Period, class Rep2>
  constexpr
  __duration<typename __common_type<Rep1, Rep2>::type, Period>
  operator%(const __duration<Rep1, Period>& d, const Rep2& s);

__remarks Let `CR` represent the __common_type of `Rep1` and `Rep2`. This function will not participate in overload resolution unless Rep2 must be implicitly convertible to CR and Rep2 must not be an instantiation of __duration.

__returns CD(CD(d).count() % s) where `CD` is the type of the return value.

[endsect]

[section:duration__op_mod_2 Non-Member Function `operator%(duration,duration)`]

  template <class Rep1, class Period1, class Rep2, class Period2>
  constexpr
  typename __common_type<__duration<Rep1, Period1>, __duration<Rep2, Period2> >::type
  operator%(const __duration<Rep1, Period1>& lhs,
            const __duration<Rep2, Period2>& rhs);

__remarks This function will not participate in overload resolution unless

__returns CD(CD(lhs).count() % CD(rhs).count()) where `CD` is the type of the return value.

[endsect]
[endsect]

[section:cmp `duration` Non-Member Comparaisons]

[section:duration__op_eq_1 Non-Member Function `operator==(duration,duration)`]

    template <class Rep1, class Period1, class Rep2, class Period2>
    bool operator==(const __duration<Rep1, Period1>& lhs,
                    const __duration<Rep2, Period2>& rhs);

__returns Let `CD` represent the __common_type of the two __duration arguments.

__returns Returns `CD(lhs).count() == CD(rhs).count()`

[endsect]
[section:duration__op_neq_1 Non-Member Function `operator!=(duration,duration)`]

    template <class Rep1, class Period1, class Rep2, class Period2>
    bool operator!=(const __duration<Rep1, Period1>& lhs,
                    const __duration<Rep2, Period2>& rhs);

__returns `!(lhs == rhs)`.

[endsect]
[section:duration__op_lt_1 Non-Member Function `operator<(duration,duration)`]

    template <class Rep1, class Period1, class Rep2, class Period2>
    bool operator< (const __duration<Rep1, Period1>& lhs,
                    const __duration<Rep2, Period2>& rhs);

__returns Let `CD` represent the __common_type of the two __duration arguments. Returns `CD(lhs).count() < CD(rhs).count()`

[endsect]
[section:duration__op_leq_1 Non-Member Function `operator<=(duration,duration)`]

    template <class Rep1, class Period1, class Rep2, class Period2>
    bool operator<=(const __duration<Rep1, Period1>& lhs,
                    const __duration<Rep2, Period2>& rhs);

__returns `!(rhs < lhs)`.

[endsect]
[section:duration__op_gt_1 Non-Member Function `operator>(duration,duration)`]

    template <class Rep1, class Period1, class Rep2, class Period2>
    bool operator> (const __duration<Rep1, Period1>& lhs,
                    const __duration<Rep2, Period2>& rhs);

__returns `rhs < lhs`.

[endsect]
[section:duration__op_geq_1 Non-Member Function `operator>=(duration,duration)`]

    template <class Rep1, class Period1, class Rep2, class Period2>
    bool operator>=(const __duration<Rep1, Period1>& lhs,
                    const __duration<Rep2, Period2>& rhs);

__returns `!(lhs < rhs)`.

[endsect]
[endsect]
[section:duration_cast Non-Member Function `duration_cast(duration)`]

    template <class __ToDuration, class Rep, class Period>
    __ToDuration duration_cast(const __duration<Rep, Period>& d);

__requires This function will not participate in overload resolution unless `ToDuration` is an instantiation of __duration.

__returns Forms `CF` which is a __ratio resulting from `ratio_divide<Period, typename ToDuration::period>::type`. Let `CR` be the __common_type of `ToDuration::rep`, `Rep`, and `intmax_t`.

* If `CF::num == 1` and `CF::den == 1`, then returns `ToDuration(static_cast<typename ToDuration::rep>(d.count())) `
* else if `CF::num != 1` and `CF::den == 1`, then returns
              `ToDuration(static_cast<typename ToDuration::rep>(static_cast<CR>(d.count()) *
                                                               static_cast<CR>(CF::num)))`
* else if `CF::num == 1` and `CF::den != 1`, then returns
              `ToDuration(static_cast<typename ToDuration::rep>(static_cast<CR>(d.count()) /
                                                               static_cast<CR>(CF::den)))`
* else returns
              `ToDuration(static_cast<typename ToDuration::rep>(static_cast<CR>(d.count()) *
                                                               static_cast<CR>(CF::num)   /
                                                               static_cast<CR>(CF::den)))`

__remarks This function does not rely on any implicit conversions. All conversions must be accomplished through `static_cast`. The implementation avoids all multiplications or divisions when it is known at compile-time that it can be avoided because one or more arguments are `1`. All intermediate computations are carried out in the widest possible representation and only converted to the destination representation at the final step.


[endsect]


[section:duration_typedefs `duration` typedefs]

        // convenience typedefs
        typedef __duration<boost::int_least64_t, nano> nanoseconds;    // at least 64 bits needed
        typedef __duration<boost::int_least64_t, micro> microseconds;  // at least 55 bits needed
        typedef __duration<boost::int_least64_t, milli> milliseconds;  // at least 45 bits needed
        typedef __duration<boost::int_least64_t> seconds;              // at least 35 bits needed
        typedef __duration<boost::int_least32_t, __ratio< 60> > minutes; // at least 29 bits needed
        typedef __duration<boost::int_least32_t, __ratio<3600> > hours;  // at least 23 bits needed

[endsect]

[endsect]

[section:clock `Clock` Requirements]

A clock represents a bundle consisting of a  __duration, a  __time_point, and a function `now()` to get the current __time_point. A clock must meet the requirements in the following Table.

In this table `C1` and `C2` denote `Clock` types. `t1` and `t2` are values returned from `C1::now()` where the call returning `t1` happens before the call returning `t2` and both of these calls occur before `C1::time_point::max()`.
(note This means C1 did not wrap around between t1 and t2.).

[table Clock Requirements
    [[expression]        [return type]  [operational semantics]]
    [[`C1::rep`]        [An arithmetic type or class emulating an arithmetic type. ]  [The representation type of the  __duration and __time_point.]]
    [[`C1::period`]        [`ratio`]  [The tick period of the clock in seconds.]]
    [[`C1::duration`]        [`chrono::duration<C1::rep, C1::period>`]  [The  __duration type of the `clock`.]]
    [[`C1::time_point`]        [`chrono::time_point<C1> or chrono::time_point<C2, C1::duration>`]  [The  __time_point type of the `clock`. Different clocks are permitted to share a __time_point definition if it is valid to compare their time_points by comparing their respective __durations. `C1` and `C2` must refer to the same epoch.]]
    [[`C1::is_steady`]        [`constexpr bool`]  [`true` if `t1 <= t2` is always `true`, else `false`. *Note*: A `clock` that can be adjusted backwards is not steady]]
    [[`C1::now()`]        [`C1::time_point`]  [Returns a __time_point representing the current point in time.]]
]

Models of __Clock:

* __system_clock
* __steady_clock
* __high_resolution_clock
* __process_real_cpu_clock
* __process_user_cpu_clock
* __process_system_cpu_clock
* __process_cpu_clock
* __thread_clock

[endsect]

[section:trivial_clock `TrivialClock` Requirements]

A type `TC` meets the `TrivialClock` requirements if:

* `TC` satisfies the `Clock` requirements,
* the types `TC::rep`, `TC::duration`, and `TC::time_point` satisfy the requirements of `EqualityComparable`, `LessThanComparable`, `DefaultConstructible`, `CopyConstructible`, `CopyAssignable`, `Destructible`, and the requirements of numeric types.

[note This means, in particular, that operations on these types will not throw exceptions.]

* lvalues of the types `TC::rep`, `TC::duration`, and `TC::time_point` are swappable,
* the function `TC::now()` does not throw exceptions and it is thread-safe, and
* the type `TC::time_point::clock` meets the `TrivialClock` requirements, recursively.

Models of __TrivialClock:

* __system_clock
* __steady_clock
* __high_resolution_clock
* __process_real_cpu_clock
* __process_user_cpu_clock
* __process_system_cpu_clock
* __thread_clock

[endsect]

[section:ecclock `EcClock` Requirements]

A type `EcC` meets the `EcClock` requirements if

* `TC` satisfies the `TrivialClock ` requirements, and
* it adds `now()` interfaces allowing to recover internal error codes as described in the following table.
* the added now() function is thread-safe.

In this table `C1` denotes a `EcClock` type and `ec` is an instance of a `boost::system::error_code`.

[table Clock Requirements
    [[expression]        [return type]  [operational semantics]]
    [[`C1::now(ec)`]        [`C1::time_point`]  [Returns a __time_point representing the current point in time. `ec` will stores the error-code in case something was wrong internally.]]
    [[`C1::now(boost::throws())`]        [`C1::time_point`]  [Returns a __time_point representing the current point in time.
    Throws a `boost::system::system_error` exception in case something was wrong internally.]]
]

Models of __Clock:

* __system_clock
* __steady_clock
* __high_resolution_clock
* __process_real_cpu_clock
* __process_user_cpu_clock
* __process_system_cpu_clock
* __process_cpu_clock
* __thread_clock

[endsect]


[/=============================================]
[section:time_point_hpp Header `<boost/chrono/time_point.hpp>`]
[/=============================================]

This file contains __time_point specific classes and non-member functions.

    namespace boost {
      namespace chrono {

        template <class __Clock, class __Duration = typename Clock::duration>
        class __time_point;

      }
      template <class __Clock, class __Duration1, class __Duration2>
      struct __common_type_spe<time_point<Clock, Duration1>,
                         time_point<Clock, Duration2> >;

      namespace chrono {

        // time_point arithmetic
        template <class __Clock, class __Duration1, class Rep2, class Period2>
        constexpr time_point<Clock, typename common_type<Duration1, duration<Rep2, Period2> >::type>
        __time_point__op_plus_1(const time_point<Clock, Duration1>& lhs,
                  const duration<Rep2, Period2>& rhs);

        template <class Rep1, class Period1, class __Clock, class __Duration2>
        constexpr time_point<Clock, typename common_type<duration<Rep1, Period1>, Duration2>::type>
        __time_point__op_plus_2(const duration<Rep1, Period1>& lhs,
                  const time_point<Clock, Duration2>& rhs);

        template <class __Clock, class __Duration1, class Rep2, class Period2>
        constexpr time_point<Clock, typename common_type<Duration1, duration<Rep2, Period2> >::type>
        __time_point__op_minus_1(const time_point<Clock, Duration1>& lhs,
                  const duration<Rep2, Period2>& rhs);

        template <class __Clock, class __Duration1, class __Duration2>
        constexpr typename common_type<Duration1, Duration2>::type
        __time_point__op_minus_2(const time_point<Clock, Duration1>& lhs,
                  const time_point<Clock, Duration2>& rhs);

        // time_point comparisons
        template <class __Clock, class __Duration1, class __Duration2>
        constexpr bool
        __time_point__op_eq(const time_point<Clock, Duration1>& lhs,
                   const time_point<Clock, Duration2>& rhs);
        template <class __Clock, class __Duration1, class __Duration2>
        constexpr bool
        __time_point__op_neq(const time_point<Clock, Duration1>& lhs,
                   const time_point<Clock, Duration2>& rhs);
        template <class __Clock, class __Duration1, class __Duration2>
        constexpr bool
        __time_point__op_lt(const time_point<Clock, Duration1>& lhs,
                  const time_point<Clock, Duration2>& rhs);
        template <class __Clock, class __Duration1, class __Duration2>
        constexpr bool
        __time_point__op_leq(const time_point<Clock, Duration1>& lhs,
                   const time_point<Clock, Duration2>& rhs);
        template <class __Clock, class __Duration1, class __Duration2>
        constexpr bool
        __time_point__op_gt(const time_point<Clock, Duration1>& lhs,
                  const time_point<Clock, Duration2>& rhs);
        template <class __Clock, class __Duration1, class __Duration2>
        constexpr bool
        __time_point__op_geq(const time_point<Clock, Duration1>& lhs,
                   const time_point<Clock, Duration2>& rhs);

        // time_point_cast
        template <class __ToDuration, class __Clock, class __Duration>
        constexpr time_point<Clock, ToDuration>
        __time_point_cast(const time_point<Clock, Duration>& t);

      }
    }


[section:common_type_spe2 `common_type` specialization]

    template <class __Clock, class __Duration1, class __Duration2>
    struct __common_type<chrono::__time_point<__Clock, __Duration1>, chrono::__time_point<__Clock, __Duration2> >
    {
        typedef chrono::__time_point<__Clock, typename __common_type<Duration1, __Duration2>::type> type;
    };

The __common_type of two __time_points is a __time_point with the same __Clock (both have the same __Clock), and the __common_type of the two __durations.

[endsect]

[section:time_point Class template `time_point<>`]

A __time_point represents a point in time with respect to a specific clock.

    template <class __Clock, class __Duration>
    class time_point {
    public:
        typedef Clock                     clock;
        typedef Duration                  duration;
        typedef typename duration::rep    rep;
        typedef typename duration::period period;
    private:
        duration d_; // exposition only
    public:
        constexpr __time_point__c_1();

        constexpr explicit __time_point__c_2(const duration& d);

        // conversions
        template <class __Duration2>
        constexpr
        __time_point__c_3(const time_point<clock, __Duration2>& t);

        // observer
        constexpr duration __time_point__time_since_epoch() const;

        // arithmetic

        #ifdef BOOST_CHRONO_EXTENSIONS
        constexpr time_point  __time_point__op_unary_plus();
        constexpr time_point  __time_point__op_unary_minus();
        time_point& __time_point__op_pre_inc();
        time_point  __time_point__op_post_inc(int);
        time_point& __time_point__op_pre_dec();
        time_point  __time_point__op_post_dec(int);

        time_point& __time_point__op_plus_eq_1(const rep& d);
        time_point& __time_point__op_minus_eq_1(const rep& d);
        #endif

        time_point& __time_point__op_plus_eq_2(const duration& d);
        time_point& __time_point__op_minus_eq_2(const duration& d);

        // special values

        static constexpr time_point __time_point__min();
        static constexpr time_point __time_point__max();
    };

Clock must meet the __clock_req.

Duration must be an instantiation of __duration, compile diagnostic otherwise.

[section:time_point_c_1 Constructor `time_point()`]

    constexpr time_point();

__effects  Constructs an object of __time_point, initializing `d_` with `duration::zero()`. This __time_point represents the epoch.

[endsect]
[section:time_point_c_2 Constructor `time_point(const duration&)`]

    constexpr time_point(const duration& d);

__effects  Constructs an object of __time_point, initializing `d_` with `d`. This __time_point represents the epoch `+ d`.

[endsect]
[section:time_point_c_3 Copy Constructor `time_point(const time_point&)`]

    template <class __Duration2>
    constexpr
    time_point(const __time_point<clock, __Duration2>& t);

__requires This function will not participate in overload resolution unless `Duration2` is implicitly convertible to __duration.

__effects  Constructs an object of __time_point, initializing `d_` with `t.time_since_epoch()`.

[endsect]

[section:time_since_epoch Member Function `time_since_epoch() const`]

    constexpr duration time_since_epoch() const;

__returns `d_`.

[endsect]

[section:op_unary_plus Member Function `operator+() const`]

    constexpr __time_point operator+() const;

__returns `*this`.

[endsect]
[section:op_unary_minus Member Function `operator-() const`]

    constexpr __time_point operator-() const;

__returns `__time_point(-d_)`.

[endsect]
[section:op_pre_inc Member Function `operator++()`]

    __time_point& operator++();

__effects  `++d_`.

__returns `*this`.

[endsect]
[section:op_post_inc Member Function `operator++(int)`]

    __time_point operator++(int);

__returns `__time_point(d_++)`.

[endsect]
[section:op_pre_dec Member Function `operator--()`]

    __time_point& operator--();

__effects  `--d_`.

__returns `*this`.

[endsect]
[section:op_post_dec Member Function `operator--(int)`]

    __time_point operator--(int);

__returns `__time_point(d_--)`.

[endsect]

[section:op_plus_eq_1 Member Function `operator+=(const rep&)`]

    __time_point& operator+=(const rep& r);

__effects  `d_ += duration(r)`.

__returns `*this`.

[endsect]
[section:op_minus_eq_1 Member Function `operator-=(const rep&)`]

    __time_point& operator-=(const rep& r);

__effects  `d_ -= duration(r)`

__returns `*this`.

[endsect]


[section:op_plus_eq_2 Member Function `operator+=`]

    time_point& operator+=(const duration& d);

__effects  `d_ += d`.

__returns `*this`.

[endsect]
[section:op_minus_eq_2 Member Function `operator-=`]

    time_point& operator-=(const duration& d);

__effects  `d_ -= d`

__returns `*this`.

[endsect]
[section:min Static Member Function `min`]

    static constexpr time_point min();

__returns `time_point(duration::min())`.

[endsect]
[section:max Static Member Function `max`]

    static constexpr time_point max();

__returns `time_point(duration::max())`.

[endsect]
[endsect]


[section:arith `time_point` non-member arithmetic]


[section:time_point__op_plus_1 Non-Member Function `operator+(time_point,duration)`]

    template <class __Clock, class __Duration1, class Rep2, class Period2>
    constexpr
    __time_point<__Clock, typename __common_type<Duration1, duration<Rep2, Period2> >::type>
    operator+(const __time_point<__Clock, __Duration1>& lhs,
              const duration<Rep2, Period2>& rhs);

__returns `CT(lhs.time_since_epoch() + rhs)` where `CT` is the type of the return value.

[endsect]
[section:time_point__op_plus_2 Non-Member Function `operator+(duration,time_point)`]

    template <class Rep1, class Period1, class __Clock, class __Duration2>
    constexpr
    __time_point<__Clock, typename __common_type<duration<Rep1, Period1>, __Duration2>::type>
    operator+(const duration<Rep1, Period1>& lhs,
              const __time_point<__Clock, __Duration2>& rhs);

__returns `rhs + lhs`.

[endsect]
[section:time_point__op_minus_1 Non-Member Function `operator-(time_point,duration)`]

    template <class __Clock, class __Duration1, class Rep2, class Period2>
    constexpr
    __time_point<__Clock, typename __common_type<Duration1, duration<Rep2, Period2> >::type>
    operator-(const __time_point<__Clock, __Duration1>& lhs,
              const duration<Rep2, Period2>& rhs);

__returns `lhs + (-rhs)`.

[endsect]
[section:time_point__op_minus_2 Non-Member Function `operator-(time_point,time_point)`]

    template <class __Clock, class __Duration1, class __Duration2>
    constexpr
    typename __common_type<Duration1, __Duration2>::type
    operator-(const __time_point<__Clock, __Duration1>& lhs,
              const __time_point<__Clock, __Duration2>& rhs);

__returns `lhs.time_since_epoch() - rhs.time_since_epoch()`.

[endsect]
[endsect]

[section:cmp `time_point` non-member comparisons]
[section:time_point__op_eq Non-Member Function `operator==(time_point,time_point)`]

    template <class __Clock, class __Duration1, class __Duration2>
    constexpr
    bool operator==(const __time_point<__Clock, __Duration1>& lhs,
                    const __time_point<__Clock, __Duration2>& rhs);

__returns `lhs.time_since_epoch() == rhs.time_since_epoch()`.

[endsect]
[section:time_point__op_neq Non-Member Function `operator!=(time_point,time_point)`]

   template <class __Clock, class __Duration1, class __Duration2>
   constexpr
   bool operator!=(const __time_point<__Clock, __Duration1>& lhs,
                   const __time_point<__Clock, __Duration2>& rhs);

__returns `!(lhs == rhs)`.

[endsect]
[section:time_point__op_lt Non-Member Function `operator<(time_point,time_point)`]

    template <class __Clock, class __Duration1, class __Duration2>
    constexpr
    bool operator< (const __time_point<__Clock, __Duration1>& lhs,
                    const __time_point<__Clock, __Duration2>& rhs);

__returns lhs.time_since_epoch() < rhs.time_since_epoch().

[endsect]
[section:time_point__op_leq Non-Member Function `operator<=(time_point,time_point)`]

    template <class __Clock, class __Duration1, class __Duration2>
    constexpr
    bool operator<=(const __time_point<__Clock, __Duration1>& lhs,
                    const __time_point<__Clock, __Duration2>& rhs);

__returns `!(rhs < lhs)`.

[endsect]
[section:time_point__op_gt Non-Member Function `operator>(time_point,time_point)`]

   template <class __Clock, class __Duration1, class __Duration2>
   constexpr
   bool operator>(const __time_point<__Clock, __Duration1>& lhs,
                  const __time_point<__Clock, __Duration2>& rhs);

__returns `rhs < lhs`.

[endsect]
[section:time_point__op_geq Non-Member Function `operator>=(time_point,time_point)`]

    template <class __Clock, class __Duration1, class __Duration2>
    constexpr
    bool operator>=(const __time_point<__Clock, __Duration1>& lhs,
                    const __time_point<__Clock, __Duration2>& rhs);

__returns `!(lhs < rhs)`.

[endsect]
[endsect]


[section:time_point_cast Non-Member Function `time_point_cast(time_point)`]

    template <class __ToDuration, class __Clock, class __Duration>
    constexpr
    __time_point<__Clock, ToDuration> __time_point_cast(const __time_point<__Clock, __Duration>& t);

__requires This function will not participate in overload resolution unless `ToDuration` is an instantiation of __duration.

__returns `__time_point<__Clock, ToDuration>(__duration_cast<ToDuration>(t.time_since_epoch()))`.

[endsect]

[endsect]

[/=============================================]
[section:system_clocks_hpp Header `<boost/chrono/system_clocks.hpp>`]
[/=============================================]

This file contains the standard clock classes. The types defined in this section satisfy the __TrivialClock requirements

    namespace boost {
      namespace chrono {

        // Clocks
        class __system_clock;
        class __steady_clock;
        class __high_resolution_clock;

        template <class CharT>
        struct clock_string<system_clock, CharT>;
        template <class CharT>
        struct clock_string<steady_clock, CharT>;

      }
    }


[section:system_clock Class `system_clock`]

The __system_clock class provides a means of obtaining the current wall-clock time from the system-wide real-time clock. The current time can be obtained by calling `system_clock::now()`. Instances of `system_clock::time_point`  can be converted to and from time_t  with the `system_clock::to_time_t()`  and `system_clock::from_time_t()`  functions. If system clock is not steady, a subsequent call to `system_clock::now()`  may return an earlier time than a previous call (e.g. if the operating system clock is manually adjusted, or synchronized with an external clock).

The current implementation of __system_clock is related an epoch (midnight UTC of January 1, 1970), but this is not in the contract. You need to use the static function static

    std::time_t to_time_t(const time_point& t);

which returns a `time_t` type that is based on midnight UTC of January 1, 1970.

    class system_clock {
    public:
        typedef __see_bellow__          duration;
        typedef duration::rep                        rep;
        typedef duration::period                     period;
        typedef chrono::__time_point<system_clock>     time_point;
        static constexpr bool is_steady =            false;


        static time_point  now() noexcept;
        static time_point  now(system::error_code & ec);

        // Map to C API
        static std::time_t to_time_t(const time_point& t) noexcept;
        static time_point  from_time_t(std::time_t t) noexcept;
    };

__system_clock satisfy the __clock_req:

* `system_clock::duration::min() < system_clock::duration::zero()` is `true`.

* The nested `duration` typedef has a resolution that depends on the one provided by the platform.

[section:to_time_t Static Member Function `to_time_t(time_point)`]

    time_t to_time_t(const time_point& t) noexcept;

__returns A `time_t` such that the `time_t` and `t` represent the same point in time, truncated to the coarser of the precisions among `time_t` and `time_point`.

[endsect]
[section:from_time_t Static Member Function `from_time_t(time_t)`]

    time_point from_time_t(time_t t) noexcept;

__returns A __time_point such that the __time_point and `t` represent the same point in time, truncated to the coarser of the precisions among __time_point and `time_t`.

[endsect]
[endsect]

[section:BOOST_CHRONO_HAS_CLOCK_STEADY Macro `BOOST_CHRONO_HAS_CLOCK_STEADY`]

Defined if the platform support steady clocks.

[endsect]

[section:steady_clock Class `steady_clock`]

__steady_clock satisfy the __clock_req.

__steady_clock class provides access to the system-wide steady clock. The current time can be obtained by calling `steady_clock::now()`. There is no fixed relationship between values returned by `steady_clock::now()` and wall-clock time.

    #ifdef BOOST_HAS_CLOCK_STEADY
        class steady_clock {
        public:
            typedef __nanoseconds                          duration;
            typedef duration::rep                        rep;
            typedef duration::period                     period;
            typedef chrono::__time_point<steady_clock>     time_point;
            static constexpr bool is_steady =            true;

            static time_point  now() noexcept;
            static time_point  now(system::error_code & ec);
        };
    #endif

[endsect]

[section:high_resolution_clock Class `high_resolution_clock`]

__high_resolution_clock satisfy the __clock_req.

    #ifdef __BOOST_CHRONO_HAS_CLOCK_STEADY
      typedef __steady_clock high_resolution_clock;  // as permitted by [time.clock.hires]
    #else
      typedef __system_clock high_resolution_clock;  // as permitted by [time.clock.hires]
    #endif

[endsect]

[section:clock_string_system_clock `clock_string<system_clock>` Specialization]

    template <class CharT>
    struct clock_string<system_clock, CharT>
    {
        static std::basic_string<CharT> name();
        static std::basic_string<CharT> since();
    };

`clock_string<>::name()` returns "system_clock".

`clock_string<>::since()` returns " since Jan 1, 1970"


[endsect]

[section:clock_string_steady_clock `clock_string<steady_clock>` Specialization]

    #ifdef BOOST_CHRONO_HAS_CLOCK_STEADY

    template <class CharT>
    struct clock_string<steady_clock, CharT>
    {
        static std::basic_string<CharT> name();
        static std::basic_string<CharT> since();
    };
    #endif

`clock_string<>::name()` returns "steady_clock".

`clock_string<>::since()` returns " since boot"

[endsect]

[endsect]

[/==================================================================]
[section:clock_strings_hpp Header `<boost/chrono/clock_strings.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {

        template <class __Clock, class CharT>
        struct clock_string;

      }
    }

[section:clock_string Template Class `clock_string<>`]

    template <class __Clock, class CharT>
    struct clock_string;

This template must be specialized for specific clocks. The specialization must define the following functions

        static std::basic_string<CharT> name();
        static std::basic_string<CharT> since();

`clock_string<>::name()` return the clock name, which usually corresponds to the class name.

`clock_string<>::since()` return the textual format of the clock epoch.


[endsect]

[endsect] [/section:clock_strings_hpp Header `<boost/chrono/clock_strings.hpp>`]

[/=============================================]
[section:chrono_typeof_hpp Header `<boost/chrono/typeof/boost/chrono/chrono.hpp>`]
[/=============================================]

Register __duration`<>` and __time_point`<>` class templates to [*Boost.Typeof].

[endsect]
[endsect]

[section:io_v1 Chrono I/O V1]
[/==================================================================]
[section:chrono_io_hpp Header `<boost/chrono/chrono_io.hpp>`]
[/==================================================================]



    namespace boost {
    namespace chrono {

        template <class CharT>
        class duration_punct;

        template <class CharT, class Traits>
            std::basic_ostream<CharT, Traits>&
            duration_short(std::basic_ostream<CharT, Traits>& os);

        template <class CharT, class Traits>
            std::basic_ostream<CharT, Traits>&
            duration_long(std::basic_ostream<CharT, Traits>& os);

        template <class CharT, class Traits, class Rep, class Period>
            std::basic_ostream<CharT, Traits>&
            operator<<(std::basic_ostream<CharT, Traits>& os, const duration<Rep, Period>& d);

        template <class CharT, class Traits, class Rep, class Period>
            std::basic_istream<CharT, Traits>&
            operator>>(std::basic_istream<CharT, Traits>& is, duration<Rep, Period>& d)

        template <class CharT, class Traits, class Clock, class Duration>
            std::basic_ostream<CharT, Traits>&
            operator<<(std::basic_ostream<CharT, Traits>& os,
                   const time_point<Clock, Duration>& tp);

        template <class CharT, class Traits, class Clock, class Duration>
            std::basic_istream<CharT, Traits>&
            operator>>(std::basic_istream<CharT, Traits>& is,
                   time_point<Clock, Duration>& tp);

    }
    }

[section:duration_punct Template Class `duration_punct<>`]

The __duration unit names can be customized through the facet: __duration_punct. __duration unit names come in two varieties: long and short. The default constructed __duration_punct provides names in the long format. These names are English descriptions. Other languages are supported by constructing a __duration_punct with the proper spellings for "hours", "minutes" and "seconds", and their abbreviations (for the short format).

        template <class CharT>
        class duration_punct
            : public std::locale::facet
        {
        public:
            typedef std::basic_string<CharT> string_type;
            enum {use_long, use_short};

            static std::locale::id id;

            explicit duration_punct(int use = use_long);

            duration_punct(int use,
                const string_type& long_seconds, const string_type& long_minutes,
                const string_type& long_hours, const string_type& short_seconds,
                const string_type& short_minutes, const string_type& short_hours);

            duration_punct(int use, const duration_punct& d);

            template <class Period> string_type short_name() const;
            template <class Period> string_type long_name() const;
            template <class Period> string_type name() const;

            bool is_short_name() const;
            bool is_long_name() const;
        };

[endsect]

[section:manipulators I/O Manipulators]

The short or long format can be easily chosen by streaming a `duration_short` or `duration_long` manipulator respectively.

        template <class CharT, class Traits>
            std::basic_ostream<CharT, Traits>&
            duration_short(std::basic_ostream<CharT, Traits>& os);

__effects Set the __duration_punct facet to stream __durations and __time_points as abbreviations.

__returns the output stream

        template <class CharT, class Traits>
            std::basic_ostream<CharT, Traits>&
            duration_long(std::basic_ostream<CharT, Traits>& os);


__effects Set the __duration_punct facet to stream durations and time_points as long text.

__returns the output stream


[endsect]

[section:streams I/O Streams Operations]

Any __duration can be streamed out to a `basic_ostream`. The run-time value of the __duration is formatted according to the rules and current format settings for __duration`::rep`. This is followed by a single space and then the compile-time unit name of the __duration. This unit name is built on the string returned from `ratio_string<>` and the data used to construct the __duration_punct which was inserted into the stream's locale. If a __duration_punct has not been inserted into the stream's locale, a default constructed __duration_punct will be added to the stream's locale.

A __time_point is formatted by outputting its internal __duration followed by a string that describes the __time_point`::clock` epoch. This string will vary for each distinct clock, and for each implementation of the supplied clocks.

        template <class CharT, class Traits, class Rep, class Period>
            std::basic_ostream<CharT, Traits>&
            operator<<(std::basic_ostream<CharT, Traits>& os, const duration<Rep, Period>& d);

__effects outputs the __duration as an abbreviated or long text format depending on the state of the __duration_punct facet.

__returns the output stream

        template <class CharT, class Traits, class Rep, class Period>
            std::basic_istream<CharT, Traits>&
            operator>>(std::basic_istream<CharT, Traits>& is, duration<Rep, Period>& d)

__effects reads a __duration from the input stream. If a format error is found, the input stream state will be set to `failbit`.

__returns the  input stream

        template <class CharT, class Traits, class Clock, class Duration>
            std::basic_ostream<CharT, Traits>&
            operator<<(std::basic_ostream<CharT, Traits>& os,
                   const time_point<Clock, Duration>& tp);

__effects outputs the __time_point as an abbreviated or long text format depending on the state of the __duration_punct facet.

__returns the output stream


        template <class CharT, class Traits, class Clock, class Duration>
            std::basic_istream<CharT, Traits>&
            operator>>(std::basic_istream<CharT, Traits>& is,
                   time_point<Clock, Duration>& tp);

__effects reads a __time_point from the input stream. If a format error is found, the input stream state will be set to `failbit`.

__returns the input stream

[endsect]


[endsect]
[endsect]

[section:io Chrono I/O V2]

[/==================================================================]
[section:chrono_io_hpp Header `<boost/chrono/chrono_io.hpp>`]
[/==================================================================]


This file includes the i/o of the two major components, duration and time_point.

        #include <boost/chrono/io/duration_style.hpp>
        #include <boost/chrono/io/timezone.hpp>
        #include <boost/chrono/io/ios_base_state.hpp>
        #include <boost/chrono/io/duration_get.hpp>
        #include <boost/chrono/io/duration_put.hpp>
        #include <boost/chrono/io/duration_units.hpp>
        #include <boost/chrono/io/duration_io.hpp>
        #include <boost/chrono/io/time_point_get.hpp>
        #include <boost/chrono/io/time_point_put.hpp>
        #include <boost/chrono/io/time_point_units.hpp>
        #include <boost/chrono/io/time_point_io.hpp>


[endsect] [/section:chrono_io_hpp Header `<boost/chrono/chrono_io.hpp>`]

[/==================================================================]
[section:duration_style_hpp Header `<boost/chrono/io/duration_style.hpp>`]
[/==================================================================]


    namespace boost {
      namespace chrono {

        // typedefs
        enum class __duration_style {
          __prefix, __symbol
        };
      }
    }
        
[section:duration_style Scoped enum `duration_style`]

          enum class duration_style {
            prefix, symbol
          };

__duration unit names come in two varieties: prefix and symbol.

[endsect] [/section:duration_style Scoped enum `duration_style`]

[endsect] [/section:duration_style_hpp Header `<boost/chrono/io/duration_style.hpp>`]

[/==================================================================]
[section:timezone_hpp Header `<boost/chrono/io/timezone.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {

        struct __timezone
        {
          enum type {
            __utc, __local
          };
        };
      }
    }
        
[section:timezone Scoped enum `timezone`]

          enum class timezone {
            utc, local
          };

[endsect] [/section:timezone Scoped enum `timezone`]
        
        
[endsect] [/section:timezone_hpp Header `<boost/chrono/io/timezone.hpp>`]


[/==================================================================]
[section:ios_state_hpp Header `<boost/chrono/io/ios_base_state.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {

        // setters and getters
        duration_style __get_duration_style(std::ios_base & ios);
        void __set_duration_style(std::ios_base& ios, duration_style style);

        timezone __get_timezone(std::ios_base & ios);
        void __set_timezone(std::ios_base& ios, timezone tz);
        
        template<typename CharT>
        std::basic_string<CharT> __get_time_fmt(std::ios_base & ios);
        template<typename CharT>
        void __set_time_fmt(std::ios_base& ios, std::basic_string<CharT> const& fmt);
               
        // i/o state savers
        struct __duration_style_io_saver;
        template<typename CharT = char, typename Traits = std::char_traits<CharT> >
        struct __timezone_io_saver;
        template<typename CharT = char, typename Traits = std::char_traits<CharT> >
        struct __time_fmt_io_saver;
      }
    }
    
[section:sag Setter and getters]

[section:get_duration_style Non Member Function `get_duration_style(std::ios_base &)`]

        duration_style get_duration_style(std::ios_base & ios);
        
__returns The stream's __duration_style attribute associated to `ios`.
        
[endsect]

[section:set_duration_style Non Member Function `set_duration_style(std::ios_base &,duration_style)`]

        void set_duration_style(std::ios_base& ios, duration_style style);
        
__effects Set the stream's __duration_style attribute associated to `ios` with the `style` parameter.
        
[endsect]

[section:get_timezone Non Member Function `get_timezone(std::ios_base&)`]

        timezone get_timezone(std::ios_base & ios);
        
__returns The stream's __timezone attribute associated to `ios`.
        
[endsect]

[section:set_timezone Non Member Function `set_timezone(std::ios_base&,duration_style)`]

        void set_timezone(std::ios_base& ios, timezone tz);
        
__effects Set the stream's __timezone attribute associated to `ios` with the `tz` parameter.
        
[endsect]

[section:get_time_fmt Non Member Function `get_time_fmt(std::ios_base &)`]

        template<typename CharT>
        std::basic_string<CharT> get_time_fmt(std::ios_base & ios);
                       
__returns The stream's time format attribute associated to `ios`.
        
[endsect]

[section:set_time_fmt Non Member Function `set_time_fmt(std::ios_base&, std::basic_string<CharT> const&)`]

        template<typename CharT>
        void set_time_fmt(std::ios_base& ios, std::basic_string<CharT> const& fmt);
        
__effects Set the stream's time format attribute associated to `ios` with the `fmt` parameter.

The format is composed of zero or more directives. 
Each directive is composed of one of the following: 

 * one or more white-space characters (as specified by isspace()); 
 * an ordinary character (neither '%' nor a white-space character); 
 * or a conversion specification. 

Each conversion specification is composed of a '%' character followed by a conversion character which specifies the replacement required. 
The application shall ensure that there is white-space or other non-alphanumeric characters between any two conversion specifications. 
The following conversion specifications are supported:

[table Format tags
    [[Format Specifier]        [Description ]  [Example]]
    [[`%%`]        [Replaced by `%`.]  [.]]
    [[`%a %A`]        [The day of the week, using the locale's weekday names; either the abbreviated or full name may be specified.]  ["Monday".]]
    [[`%b %B %h`]        [The month, using the locale's month names; either the abbreviated or full name may be specified.]  ["February".]]
    [[`%c`]        [Not supported.]  [.]]
    [[`%d %e`]        [The day of the month `[01,31]`; leading zeros are permitted but not required..]  [.]]
    [[`%D`]        [The date as `%m/%d/%y`.]  [.]]
    [[`%F`]        [The date as `%Y/%m/%d`.]  [.]]
    [[`%H`]        [The hour (24-hour clock) `[00,23]`; leading zeros are permitted but not required.]  [.]]
    [[`%I`]        [The hour (12-hour clock) `[01,12]`; leading zeros are permitted but not required.]  [.]]
    [[`%j`]        [The day number of the year `[001,366]`; leading zeros are permitted but not required.]  ["060" => Feb-29.]]
    [[`%m`]        [The month number `[01,12]`; leading zeros are permitted but not required..]  ["01" => January.]]
    [[`%M`]        [The minute `[00,59]`; leading zeros are permitted but not required.]  [.]]
    [[`%n %t`]        [Any white space..]  [.]]
    [[`%p`]        [Not supported.]  [.]]
    [[`%r`]        [Not supported.]  [.]]
    [[`%R`]        [The time as `%H:%M`.]  [.]]
    [[`%S`]        [The seconds `[00,60]`; leading zeros are permitted but not required.]  [.]]
    [[`%T`]        [The time as `%H:%M:%S`.]  [.]]
    [[`%U`]        [Not supported.]  [.]]
    [[`%w`]        [The weekday as a decimal number `[0,6]`, with 0 representing Sunday; leading zeros are permitted but not required..]  ["0" => Sunday.]]
    [[`%W`]        [Not supported.]  [.]]
    [[`%x`]        [The date, using the locale's date format..]  [.]]
    [[`%X`]        [Not supported.]  [.]]
    [[`%y`]        [Not supported.]  ["2005".]]
    [[`%Y`]        [The year, including the century (for example, 1988).]  [.]]
]

        
[endsect]

[endsect]
[section:ioss I/O State Savers]
[section:duration_style_io_saver Template Class `duration_style_io_saver`]

        // i/o state savers

        struct duration_style_io_saver
        {
          typedef std::ios_base state_type; // the state type is ios_base
          typedef __duration_style aspect_type; // the aspect type is the __duration_style

          explicit __duration_style_io_saver__c1(state_type &s);
          __duration_style_io_saver__c2(state_type &s, aspect_type new_value);
          __duration_style_io_saver__d();
          void __duration_style_io_saver__restore();
        };

The `state_type` is a version of the IOStreams base class `std::ios_base`. 

[section:c1 Constructor `duration_style_io_saver(state_type&)`]

This constructor takes a stream object and saves a reference to the stream and 
the current value of a particular stream attribute.

          explicit duration_style_io_saver(state_type &s);

__effects Constructs a __duration_style_io_saver by storing `s`.

[endsect]

[section:c2 Constructor `duration_style_io_saver(state_type&, aspect_type)`]

This constructor works like the previous one, and in addition
uses its second argument to change the stream's attribute to the new `aspect_type` value given.

          explicit duration_style_io_saver(state_type &s, aspect_type new_value);

__effects Constructs a __duration_style_io_saver by storing `s`. Sets the `state_type` `aspect_type` with the value `new_value`.

[endsect]

[section:d Destructor `~duration_style_io_saver()`]

The destructor restores the stream's attribute to the saved value.

          ~duration_style_io_saver();

__effects As if __duration_style_io_saver__restore().

[endsect]
[section:restore Member Function `restore()`]

The restoration can be activated early (and often) with the restore member function.

          void restore();

__effects Restores the stream's __duration_style attribute to the saved value.

[endsect]


[endsect]
[section:timezone_io_saver Template Class `timezone_io_saver<>`]


        template<typename CharT = char, typename Traits = std::char_traits<CharT> >
        struct __timezone_io_saver
        {
          typedef std::basic_ios<CharT, Traits> state_type;
          typedef __timezone aspect_type;

          explicit __timezone_io_saver__c1(state_type &s);
          __timezone_io_saver__c2(state_type &s, aspect_type new_value);
          __timezone_io_saver__d();
          void timezone_io_saver__restore();
        };


The `state_type` is a version of the IOStreams base class template `std::basic_ios<CharT, Traits>`, where `CharT` is a character type and `Traits` is a character traits class. The user would usually place an actual input, output, or combined stream object for the state-type parameter, and not a base class object.

[section:c1 Constructor `timezone_io_saver(state_type&)`]

This constructor takes a stream object and saves a reference to the stream and the current value of a particular stream attribute.

          explicit timezone_io_saver(state_type &s);

__effects Constructs a __timezone_io_saver by storing `s`.

[endsect]

[section:c2 Constructor `timezone_io_saver(state_type&, aspect_type)`]

This constructor works like the previous one, and 
uses its second argument to change the stream's attribute to the new aspect_type value given.

          explicit timezone_io_saver(state_type &s, aspect_type new_value);

__effects Constructs a __timezone_io_saver by storing `s`. Sets the `state_type` `aspect_type` with the value `new_value`.


[endsect]

[section:d Destructor `~timezone_io_saver()`]

The destructor restores the stream's attribute to the saved value.

          ~timezone_io_saver();

__effects As if __timezone_io_saver__restore().

[endsect]
[section:restore Member Function `restore()`]

The restoration can be activated early (and often) with the restore member function.

          void restore();

__effects Restores the stream's __timezone attribute to the saved value.

[endsect]


[endsect]

[section:time_fmt_io_saver Template Class `time_fmt_io_saver<>`]

        template<typename CharT = char, typename Traits = std::char_traits<CharT> >
        struct __time_fmt_io_saver
        {
          typedef std::basic_ios<CharT, Traits> state_type;

          explicit __time_fmt_io_saver__c1(state_type &s);
          __time_fmt_io_saver__c2(state_type &s, basic_string<CharT> const& new_value);
          __time_fmt_io_saver__d();
          void __time_fmt_io_saver__restore();
        };


The `state_type` is a version of the IOStreams base class template `std::basic_ios<CharT, Traits>`, where `CharT` is a character type and `Traits` is a character traits class. The user would usually place an actual input, output, or combined stream object for the state-type parameter, and not a base class object.

[section:c1 Constructor `time_fmt_io_saver(state_type&)`]

This constructor takes a stream object and saves a reference to the stream and the current value of a particular stream attribute.

          explicit time_fmt_io_saver(state_type &s);

__effects Constructs a __time_fmt_io_saver by storing `s`.

[endsect]

[section:c2 Constructor `time_fmt_io_saver(state_type&, aspect_type)`]

This constructor works like the previous one, and 
uses its second argument to change the stream's attribute to the new aspect_type value given.

          explicit time_fmt_io_saver(state_type &s, aspect_type new_value);

__effects Constructs a __time_fmt_io_saver by storing `s`. Sets the `state_type` `aspect_type` with the value `new_value`.


[endsect]

[section:d Destructor `~time_fmt_io_saver()`]

The destructor restores the stream's attribute to the saved value.

          ~time_fmt_io_saver();

__effects As if __time_fmt_io_saver__restore().

[endsect]
[section:restore Member Function `restore()`]

The restoration can be activated early (and often) with the restore member function.

          void restore();

__effects Restores the stream's time format attribute to the saved value.

[endsect]

[endsect]
[endsect]

[endsect] [/section:ios_state_hpp Header `<boost/chrono/io/ios_sate.hpp>`]

[/==================================================================]
[section:duration_get_hpp Header `<boost/chrono/io/duration_get.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {

        template <class CharT, class InputIterator = std::istreambuf_iterator<CharT> >
        class __duration_get;

      }
    }

[section:duration_get Template Class `duration_get`]

    template <class CharT, class InputIterator = std::istreambuf_iterator<CharT> >
    class duration_get: public std::locale::facet
    {
    public:
      typedef CharT char_type; // Type of character the facet is instantiated on
      typedef std::basic_string<CharT> string_type; // Type of character string passed to member functions.
      typedef InputIterator iter_type; // Type of iterator used to scan the character buffer.

      explicit __duration_get_c(size_t refs = 0);
      
      template <typename Rep, typename Period>
      iter_type __duration_get_get(
        iter_type s, 
        iter_type end, 
        std::ios_base& ios, 
        std::ios_base::iostate& err,
        duration<Rep, Period>& d, 
        const char_type* pattern, 
        const char_type* pat_end
      ) const;
          
      template <typename Rep, typename Period>
      iter_type __duration_get_get2(
        iter_type s, 
        iter_type end, 
        std::ios_base& ios, 
        std::ios_base::iostate& err,
        duration<Rep, Period>& d
      ) const;
          
      template <typename Rep>
      iter_type __duration_get_get_value(
        iter_type s, 
        iter_type end, 
        std::ios_base& ios, 
        std::ios_base::iostate& err, 
        Rep& r
      ) const;
      
      iter_type __duration_get_get_unit(
        iter_type i, 
        iter_type e, 
        std::ios_base& is, 
        std::ios_base::iostate& err, 
        detail::rt_ratio &rt
      ) const
      
      static std::locale::id id; // Unique identifier for this type of facet.

      __duration_get_d()
      {
      }
                    
    };


The __duration_get facet extracts duration from a character string and stores the resulting value in a class 
duration d argument. The facet parses the string using a specific format as a guide. 
If the string does not fit the format, then the facet will indicate an error by setting the err argument to 
iosbase::failbit.
In other words, user confirmation is required for reliable parsing of
user-entered durations, but machine-generated formats can be parsed
reliably. This allows parsers to be aggressive about interpreting user
variations on standard formats.

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:c Constructor `duration_get(size_t)`]

      explicit duration_get(size_t refs);

Constructs a __duration_get facet.

__params

* [param refs] references

__effects Constructs a __duration_get facet.
If the `refs` argument is `0` then destruction of the object is
delegated to the locale, or locales, containing it. This allows
the user to ignore lifetime management issues. On the other had,
if `refs` is `1` then the object must be explicitly deleted;
`locale` will not do so. In this case, the object can be
maintained across the lifetime of multiple locales.

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:d Destructor `~duration_get()`]

      ~duration_get();

Destructs a __duration_get facet.

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:get Template Member Function `get(iter_type,iter_type,std::ios_base&,std::ios_base::iostate&,duration<Rep,Period>&,const char_type*, const char_type*) const`]

      template <typename Rep, typename Period>
      iter_type get(iter_type s, iter_type end, std::ios_base& ios, std::ios_base::iostate& err,
          duration<Rep, Period> &d, const char_type *pattern, const char_type *pat_end) const;
      
Extracts a duration from the range `[s,end)` following the pattern `[pattern,pat_end)`.
       
__params
      
* [param s] start input stream iterator
* [param end] end input stream iterator
* [param ios] a reference to a ios_base
* [param err] the ios_base state
* [param d] the duration
* [param pattern] begin of the formatting pattern
* [param pat_end] end of the formatting pattern

__requires `[s, end) and [pattern, pat_end)` shall be valid ranges.

__effects The function starts by evaluating `err = std::ios_base::goodbit`.

Then it computes an intermediate representation based on `Rep` according to the following rules:

* If `Rep` is a floating point type, the intermediate representation is `long double`.
* Else if `Rep` is a signed integral type, the intermediate representation is `long long`.
* Else if `Rep` is an unsigned integral type, the intermediate representation is unsigned long long.
* Else intermediate representation is `Rep`.

Next the following local variable `r` of type intermediate representation and `rt` of type `rt_ratio` are default constructed.

It then enters a loop, reading zero or more characters from `s` at
each iteration. Unless otherwise specified below, the loop
terminates when the first of the following conditions holds:

* The expression `pattern == pat_end` evaluates to `true`.
* The expression `err == std::ios_base::goodbit` evaluates to false.
* The expression `s == end` evaluates to `true`, in which case the
function evaluates `err = std::ios_base::eofbit | std::ios_base::failbit`.
* The next element of pattern is equal to `'%'`, followed by a conversion
specifier character, format.
If the number of elements in the range `[pattern,pat_end)` is not
sufficient to unambiguously determine whether the conversion
specification is complete and valid, the function evaluates
`err = std::ios_base::failbit`. Otherwise, the function evaluates
`s = get_value(s, end, ios, err, r)` when the conversion specification is 'v' and
`s = get_value(s, end, ios, err, rt)` when the conversion specification is 'u'.
If `err == std::ios_base::goodbit` holds after
the evaluation of the expression, the function increments pattern to
point just past the end of the conversion specification and continues
looping.
* The expression `isspace(*pattern, ios.getloc())` evaluates to `true`, in
which case the function first increments pattern until
`pattern == pat_end || !isspace(*pattern, ios.getloc())` evaluates to `true`,
then advances `s` until `s == end || !isspace(*s, ios.getloc())` is `true`,
and finally resumes looping.
* The next character read from `s` matches the element pointed to by
pattern in a case-insensitive comparison, in which case the function
evaluates `++pattern, ++s` and continues looping. 
Otherwise, the function evaluates `err = std::ios_base::failbit`.

If a duration representation value and a unit specifier have successfully been parsed, compute 
`(rt.num/rt.den)/(Period::num/Period::den)` reduced to lowest terms. 
If this ratio can not be stored without overflow, evaluates `err = std::ios_base::failbit`. 
Otherwise store the result of this division in `num` and `den`.

If the division did not result in overflow, then compute `r * num / den` in such a way as to avoid intermediate overflow. 
If `r` has integral type and this computation would not be exact, evaluates `err = std::ios_base::failbit`. 
If the result of the computation would overflow `Rep`, evaluates `err = std::ios_base::failbit`. 
Otherwise the result of `r * num / den` is used to construct a `duration<Rep, Period>` which is assigned to `d`.

__returns `s`, an iterator pointing just beyond the last character that can be determined to be part of a valid duration.
         
[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:get2 Template Member Function `get(iter_type,iter_type,std::ios_base&,std::ios_base::iostate&,duration<Rep,Period>&) const`]

      template <typename Rep, typename Period>
      iter_type get(
        iter_type s, 
        iter_type end, 
        std::ios_base& ios, 
        std::ios_base::iostate& err,
        duration<Rep, Period>& d
      ) const;

Extracts a duration from the range `[s,end)` following the default pattern.

__params
      
* [param s] start input stream iterator
* [param end] end input stream iterator
* [param ios] a reference to a ios_base
* [param err] the ios_base state
* [param d] the duration


__effects
Stores the duration pattern from the __duration_unit facet associated to 'ios` in let say `str`. Last as if

  return get(s, end, ios, err, ios, d, str.data(), str.data() + str.size());
  
__returns `s`, an iterator pointing just beyond the last character that can be determined to be part of a valid duration.
       

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:get_value Template Member Function `get_value(iter_type,iter_type,std::ios_base&,std::ios_base::iostate&,Rep&) const`]

      template <typename Rep>
      iter_type get_value(iter_type s, iter_type end, std::ios_base& ios, std::ios_base::iostate& err, Rep& r) const;

Extracts a duration representation from the range `[s,end)`.

__params
      
* [param s] start input stream iterator
* [param end] end input stream iterator
* [param ios] a reference to a ios_base
* [param err] the ios_base state
* [param r] a reference to the duration representation

__effects As if

  return std::use_facet<std::num_get<char_type, iter_type>>(ios.getloc()).get(s, end, ios, err, r);

__returns `s`, an iterator pointing just beyond the last character that can be determined to be part of a valid `Rep` value.

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:get_unit  Member Function `get_unit(iter_type,iter_type,std::ios_base&,std::ios_base::iostate&,rt_ratio&) const`]

      iter_type get_unit(iter_type i, iter_type e, std::ios_base& is, std::ios_base::iostate& err, detail::rt_ratio &rt) const;

Extracts a duration unit from the range `[s,end)`.

__params

* [param s] start input stream iterator
* [param end] end input stream iterator
* [param ios] a reference to a ios_base
* [param err] the ios_base state
* [param rt] a reference to the duration run-time ratio.

__effects 

* If the first character is `'['`, an attempt is made to consume a pattern of the form 
`"[N/D]"` where `N` and `D` have type `unsigned long long`. 
  * If successful, rt records the values of `N` and `D`, otherwise evaluates `err = std::ios_base::failbit` and return `i`.
  * Return the parse the longest string possible matching one of the durations units that can follow the pattern `"[N/D]"`, as if
  
   return do_get_n_d_prefix_unit(facet, i, e, is, err); 
   
* Otherwise the next character is not `'['`. Return the parse the longest string possible matching one of the 
durations units, as if
  
   return do_get_prefix_unit(facet, i, e, is, err, rt);

__returns `i`, an iterator pointing just beyond the last character that can be determined to be part of a valid duration unit.      
      
[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:do_get_n_d_prefix_unit Protected Member Function `do_get_n_d_prefix_unit(duration_units<CharT> const&,iter_type,iter_type,std::ios_base&,std::ios_base::iostate&) const`]

      virtual iter_type do_get_n_d_prefix_unit(
        duration_units<CharT> const &facet, 
        iter_type i, 
        iter_type e,
        std::ios_base&, 
        std::ios_base::iostate& err 
      ) const;

Extracts the run-time ratio associated to the duration when it is given in [N/D] form.

This is an extension point of this facet so that we can take in account other periods that can have a useful
translation in other contexts, as e.g. days and weeks.

__params

* [param s] start input stream iterator
* [param end] end input stream iterator
* [param ios] a reference to a ios_base
* [param err] the ios_base state
* [param rt] a reference to the duration run-time ratio.


__effects Scan `s` for the longest of all the plural forms associated with the duration units. 
If successful, sets the matched ratio in `rt`. Otherwise evaluates `err = std::ios_base::failbit`.

__returns `s`, an iterator pointing just beyond the last character that can be determined to be part of a valid name.

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:do_get_prefix_unit  Protected Member Function `do_get_prefix_unit(duration_units<CharT> const&,iter_type,iter_type,std::ios_base&,std::ios_base::iostate&,detail::rt_ratio&) const`]

      virtual iter_type do_get_prefix_unit(
        duration_units<CharT> const &facet, 
        iter_type i, 
        iter_type e,
        std::ios_base&, 
        std::ios_base::iostate& err, 
        detail::rt_ratio &rt
      ) const;

Extracts the run-time ratio associated to the duration when it is given in prefix form.

This is an extension point of this facet so that we can take in account other periods that can have a useful
translation in other contexts, as e.g. days and weeks.

__params

* [param s] start input stream iterator
* [param end] end input stream iterator
* [param ios] a reference to a ios_base
* [param err] the ios_base state
* [param rt] a reference to the duration run-time ratio.


__effects Scan `s` for the longest of all the plural forms associated with the duration units. 
If successful, sets the matched ratio in `rt`. Otherwise evaluates `err = std::ios_base::failbit`.

__returns `s`, an iterator pointing just beyond the last character that can be determined to be part of a valid name.

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]

[endsect]

[endsect] [/section:duration_get_hpp]

[/==================================================================]
[section:duration_put_hpp Header `<boost/chrono/io/duration_put.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {

        template <class CharT, class OutputIterator = std::ostreambuf_iterator<CharT> >
        class __duration_put;
    
      }
    }
    
[section:duration_put Template Class `duration_put`]
    
    template <class CharT, class OutputIterator = std::ostreambuf_iterator<CharT> >
    class duration_put: public std::locale::facet
    {
    public:
      typedef CharT char_type; // Type of character the facet is instantiated on.
      typedef std::basic_string<CharT> string_type; // Type of character string passed to member functions.
      typedef OutputIterator iter_type; // Type of iterator used to write in the character buffer.

      explicit __duration_put_c(size_t refs = 0);
      
      template <typename Rep, typename Period>
      iter_type __duration_put_put(iter_type s, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d, const CharT* pattern,
          const CharT* pat_end) const;
          
      template <typename Rep, typename Period>
      iter_type __duration_put_put2(iter_type s, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d) const;

      template <typename Rep, typename Period>
      iter_type __duration_put_put_value(iter_type s, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d) const;

      template <typename Rep, typename Period>
      iter_type __duration_put_put_unit(iter_type s, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d) const;


      static std::locale::id id; // Unique identifier for this type of facet.

      __duration_put_d();
    };

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:c Constructor `duration_put(size_t)`]

      explicit duration_put(size_t refs);

Constructs a __duration_put facet.

__params

* [param refs] references

__effects Constructs a __duration_put facet.
If the `refs` argument is `0` then destruction of the object is
delegated to the locale, or locales, containing it. This allows
the user to ignore lifetime management issues. On the other had,
if `refs` is `1` then the object must be explicitly deleted;
`locale` will not do so. In this case, the object can be
maintained across the lifetime of multiple locales.

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:d Destructor `~duration_put()`]

      ~duration_put();

Destructs a __duration_put facet.

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:put  Member Function `put(iter_type,std::ios_base&,char_type,duration<Rep,Period> const&, const CharT*,const CharT*)`]

      template <typename Rep, typename Period>
      iter_type put(iter_type s, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d, const CharT* pattern,
          const CharT* pat_end) const;

__params

* [param s] an output stream iterator
* [param ios] a reference to a ios_base
* [param d] the duration
* [param pattern] begin of the formatting pattern
* [param pat_end] end of the formatting pattern

__effects Steps through the sequence from `pattern` to `pat_end`,
identifying characters that are part of a pattern sequence. Each character
that is not part of a pattern sequence is written to `s` immediately, and
each pattern sequence, as it is identified, results in a call to `put_value` or `put_unit`;
thus, pattern elements and other characters are interleaved in the output
in the order in which they appear in the pattern. Pattern sequences are
identified by converting each character `c` to a `char` value as if by
`ct.narrow(c,0)`, where `ct` is a reference to `ctype<charT>` obtained from
`ios.getloc()`. The first character of each sequence is equal to `'%'`,
followed by a pattern specifier character specifier, which can be `'v'` for
the duration value or `'u'` for the duration unit. .
For each valid pattern sequence identified, calls
`put_value(s, ios, d)` or `put_unit(s, ios, d)`.

__returns An iterator pointing immediately after the last character produced.


[endsect]
[section:put2  Member Function `put(iter_type,std::ios_base&,char_type fill, duration<Rep, Period> const&)`]

      template <typename Rep, typename Period>
      iter_type put(iter_type s, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d) const;

__params

* [param s] an output stream iterator
* [param ios] a reference to a ios_base
* [param d] the duration

Retrieves Stores the duration pattern from the __duration_unit facet in let say `str`. Last as if

  return put(s, ios, d, str.data(), str.data() + str.size());

__returns An iterator pointing immediately after the last character produced.

[endsect]
[section:put_value  Member Function `put_value(iter_type, std::ios_base&, char_type, duration<Rep,Period> const&)`]

      template <typename Rep, typename Period>
      iter_type put_value(iter_type s, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d) const;

__params

* [param s] an output stream iterator
* [param ios] a reference to a ios_base
* [param d] the duration

__effects As if std::use_facet<std::num_put<CharT, iter_type> >(ios.getloc()).put(s, ios, ' ', static_cast<long int> (d.count())).

__returns An iterator pointing immediately after the last character produced.

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section:put_unit  Member Function `put_unit(iter_type,std::ios_base&,char_type,duration<Rep, Period> const&)`]

      template <typename Rep, typename Period>
      iter_type put_unit(iter_type s, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d) const;

__params

* [param s] an output stream iterator
* [param ios] a reference to a ios_base
* [param fill] the fill character
* [param d] the duration

__effects Let `facet` be the `duration_units<CharT>` facet associated to `ios`. If the associated unit is named, as if

          string_type str = facet.get_unit(get_duration_style(ios), d);
          s=std::copy(str.begin(), str.end(), s);

Otherwise, format the unit as `"[Period::num/Period::den]"` followed by the unit associated to [N/D] obtained using `facet.get_n_d_unit(get_duration_style(ios), d)`.

__returns s,  iterator pointing immediately after the last character produced.

[endsect]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[endsect] 

[endsect] [/section:duration_put_hpp]

[/==================================================================]
[section:duration_units_hpp Header `<boost/chrono/io/duration_units.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {
        class rt_ratio;
        template <typename CharT = char>
        class duration_units;

      }
    }


[section:rt_ratio Class `rt_ratio`]

    class rt_ratio
    {
    public:
      template <typename Period>
      rt_ratio(Period const&) :
        num(Period::type::num), den(Period::type::den)
      {
      }

      rt_ratio(intmax_t n = 0, intmax_t d = 1) :
        num(n), den(d)
      {
      }

      intmax_t num;
      intmax_t den;
    };
    
[endsect]
[section:duration_units Template Class `duration_units`]
    
    template <typename CharT = char>
    class duration_units: public std::locale::facet
    {
    public:
      typedef CharT char_type; // Type of character the facet is instantiated on.
      typedef std::basic_string<CharT> string_type; // Type of character string passed to member functions.
      static std::locale::id id; // Unique identifier for this type of facet.

      explicit __duration_units_c(size_t refs = 0);
      virtual const string_type* __duration_units_get_n_d_valid_units_start() const =0;
      virtual const string_type* __duration_units_get_n_d_valid_units_end() const=0;
      virtual const string_type* __duration_units_get_valid_units_start() const=0;
      virtual const string_type* __duration_units_get_valid_units_end() const=0;
      virtual bool __duration_units_match_n_d_valid_unit(const string_type* k) const = 0;
      virtual bool __duration_units_match_valid_unit(const string_type* k, rt_ratio& rt) const = 0;
      virtual string_type __duration_units_get_pattern() const=0;
      template <typename Rep, typename Period>
      string_type __duration_units_get_unit(duration_style style, duration<Rep, Period> const& d) const;
      template <typename Rep, typename Period>
      string_type __duration_units_get_n_d_unit(duration_style style, duration<Rep, Period> const& d) const;
      template <typename Period>
      bool __duration_units_is_named_unit() const;

    protected:
      virtual __duration_units_d();
      virtual string_type __duration_units_do_get_n_d_unit(duration_style style, rt_ratio rt, intmax_t v) const = 0;
      virtual string_type __duration_units_do_get_unit(duration_style style,rt_ratio rt, intmax_t v) const = 0;
      virtual bool __duration_units_do_is_named_unit(rt_ratio rt) const =0;
    };

__duration_units facet gives useful information about the duration units,
as the number of plural forms, the plural form associated to a duration,
the text associated to a plural form and a duration's period,
    
[section:c Constructor `duration_units()`]

      explicit duration_units(size_t refs = 0);

Construct a __duration_units facet.

__params

* [param refs] references

__effects Construct a __duration_units facet.
If the `refs` argument is `0` then destruction of the object is
delegated to the locale, or locales, containing it. This allows
the user to ignore lifetime management issues. On the other had,
if `refs` is `1` then the object must be explicitly deleted;
the locale will not do so. In this case, the object can be
maintained across the lifetime of multiple locales.

[endsect]
[section:d Destructor `~duration_units()`]

      virtual ~duration_units();

__effects Destroys the facet.

[endsect]
[section:get_n_d_valid_units_start Member Function `get_n_d_valid_units_start()`]

      virtual const string_type* get_n_d_valid_units_start() const =0;

__returns pointer to the start of valid [N/D] units.

      
[endsect]
[section:get_n_d_valid_units_end Member Function `get_n_d_valid_units_end()`]

      virtual const string_type* get_n_d_valid_units_end() const=0;

__returns pointer to the end of valid [N/D] units.

[endsect]
[section:get_valid_units_start Member Function `get_valid_units_start()`]

      virtual const string_type* get_valid_units_start() const=0;

__returns pointer to the start of valid units, symbol or prefix with its different plural forms.

[endsect]
[section:get_valid_units_end Member Function `get_valid_units_end()`]

      virtual const string_type* get_valid_units_end() const=0;

__returns pointer to the end of valid units.

[endsect]
[section:match_n_d_valid_unit Member Function `match_n_d_valid_unit(const string_type*)`]

      virtual bool match_n_d_valid_unit(const string_type* k) const = 0;

__params

* [param k] the found pointer to the [N/D] unit.

__returns `true` if `k` matches a valid unit.

[endsect]
[section:match_valid_unit Member Function `match_valid_unit(const string_type*,rt_ratio&)`]

      virtual bool match_valid_unit(const string_type* k, rt_ratio& rt) const = 0;

__params

* [param k] the found pointer to the unit.

__effects `rt` is set to the valid Period when the `k` matches a valid unit.
__returns `true` if `k` matches a valid unit.

[endsect]
[section:get_pattern Member Function `get_pattern()`]

      virtual string_type get_pattern() const=0;

__returns the pattern to be used by default.

[endsect]
[section:get_unit Template Member Function `get_unit(duration_style,duration<Rep,Period> const&)`]

      template <typename Rep, typename Period>
      string_type get_unit(duration_style style, duration<Rep, Period> const& d) const;


__returns `get_unit(style, d.count(), rt_ratio(Period()))`, the unit associated to this duration.

[endsect]
        
[section:get_n_d_unit Template Member Function `get_n_d_unit(duration_style,duration<Rep,Period> const&)`]

      template <typename Rep, typename Period>
      string_type get_n_d_unit(duration_style style, duration<Rep, Period> const& d) const;

__returns get_n_d_unit(style, d.count(), rt_ratio(Period())), i.e. the [N/D] suffix unit associated to this duration.

[endsect]
        
[section:is_named_unit Template Member Function `is_named_unit()`]

      template <typename Period>
      bool is_named_unit() const;

__returns `do_is_named_unit(rt_ratio(Period()))`, true if the unit associated to the given Period is named, false otherwise.

[endsect]
        
[section:do_get_n_d_unit Protected Member Function `do_get_n_d_unit(duration_style,rt_ratio,intmax_t)`]

      virtual string_type do_get_n_d_unit(duration_style style, rt_ratio rt, intmax_t v) const = 0;

__returns the [N/D] suffix unit associated to this duration.

[endsect]
        
[section:do_get_unit Protected Member Function `do_get_unit(duration_style,rt_ratio,intmax_t)`]

      virtual string_type do_get_unit(duration_style style,rt_ratio rt, intmax_t v) const = 0;

__returns the unit associated to this duration.
      
[endsect]
        
[section:do_is_named_unit Protected Member Function `do_is_named_unit(rt_ratio)`]

      virtual bool do_is_named_unit(rt_ratio rt) const =0;

__returns true if the unit associated to the given Period is named, false otherwise.

[endsect]
        
[endsect] [/section:duration_units]
    
[section:duration_units_default Template Class `duration_units_default`]

    template <typename CharT = char>
    class duration_units_default: public duration_units<CharT>
    {
    protected:
      static const std::size_t pfs_ = 2; // The default English facet has two plural forms.

    public:
      typedef CharT char_type;
      typedef std::basic_string<CharT> string_type;

      explicit duration_units_default(size_t refs = 0);
      ~duration_units_default();

      bool match_n_d_valid_unit(const string_type* k) const;
      bool match_valid_unit(const string_type* k, rt_ratio& rt) const;
      const string_type* get_n_d_valid_units_start()const;
      const string_type* get_n_d_valid_units_end()const;
      string_type* get_valid_units_start() const;
      string_type* get_valid_units_end() const;
      string_type get_pattern() const;
    protected:
      bool do_is_named_unit(rt_ratio rt) const;
      string_type do_get_n_d_unit(duration_style style, rt_ratio, intmax_t v) const;
      string_type do_get_unit(duration_style style, rt_ratio rt, intmax_t v) const;
      
      virtual std::size_t do_get_plural_forms() const;
      virtual std::size_t do_get_plural_form(int_least64_t value) const;

      virtual string_type do_get_unit(duration_style style, ratio<1> u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, ratio<60> u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, ratio<3600> u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, atto u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, femto u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, pico u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, nano u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, micro u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, milli u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, centi u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, deci u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, deca u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, hecto u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, kilo u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, mega u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, giga u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, tera u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, peta u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, exa u, std::size_t pf) const;

      virtual string_type do_get_ratio_prefix(duration_style style, atto u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, femto u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, pico u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, nano u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, micro u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, milli u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, centi u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, deci u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, deca u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, hecto u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, kilo u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, mega u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, giga u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, tera u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, peta u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, exa u) const;
    };

This class is used to define the strings for the default English.
This facet names the units associated to the following periods:

* __atto,
* __femto,
* __pico,
* __nano,
* __micro,
* __milli,
* __centi,
* __deci,
* ratio<1>,
* __deca,
* __hecto,
* __kilo,
* __mega,
* __giga,
* __tera,
* __peta,
* __exa,
* ratio<60> and 
* ratio<3600>.


[section:c Constructor `duration_units_default()`]

      explicit duration_units_default(size_t refs = 0);

Construct a duration_units_default facet.

__params

* [param refs] references

__effects Construct a duration_units_default facet.
If the `refs` argument is `0` then destruction of the object is
delegated to the locale, or locales, containing it. This allows
the user to ignore lifetime management issues. On the other had,
if `refs` is `1` then the object must be explicitly deleted;
the locale will not do so. In this case, the object can be
maintained across the lifetime of multiple locales.

[endsect]
[section:d Destructor `~duration_units_default()`]

      virtual ~duration_units_default();

__effects Destroys the facet.

[endsect]
[section:get_n_d_valid_units_start Member Function `get_n_d_valid_units_start()`]

      virtual const string_type* get_n_d_valid_units_start() const;

__returns pointer to the start of valid [N/D] units.

      
[endsect]
[section:get_n_d_valid_units_end Member Function `get_n_d_valid_units_end()`]

      virtual const string_type* get_n_d_valid_units_end() const;

__returns pointer to the end of valid [N/D] units.

[endsect]
[section:get_valid_units_start Member Function `get_valid_units_start()`]

      virtual const string_type* get_valid_units_start() const;

__returns pointer to the start of valid units, symbol or prefix with its different plural forms.

[endsect]
[section:get_valid_units_end Member Function `get_valid_units_end()`]

      virtual const string_type* get_valid_units_end() const;

__returns pointer to the end of valid units.

[endsect]
[section:match_n_d_valid_unit Member Function `match_n_d_valid_unit(const string_type*)`]

      virtual bool match_n_d_valid_unit(const string_type* k) const;

__params

* [param k] the found pointer to the [N/D] unit.

__returns `true` if `k` matches a valid unit.

[endsect]
[section:match_valid_unit Member Function `match_valid_unit(const string_type*,rt_ratio&)`]

      virtual bool match_valid_unit(const string_type* k, rt_ratio& rt) const;

__params

* [param k] the found pointer to the unit.

__effects `rt` is set to the valid Period when the `k` matches a valid unit.
__returns `true` if `k` matches a valid unit.

[endsect]
[section:get_pattern Member Function `get_pattern()`]

      virtual string_type get_pattern() const;

__returns the pattern to be used by default.

[endsect]
        
[section:do_get_n_d_unit Protected Member Function `do_get_n_d_unit(duration_style,rt_ratio,intmax_t)`]

      virtual string_type do_get_n_d_unit(duration_style style, rt_ratio rt, intmax_t v) const;

__returns the [N/D] suffix unit associated to this duration.

[endsect]
        
[section:do_get_unit Protected Member Function `do_get_unit(duration_style,rt_ratio,intmax_t)`]

      virtual string_type do_get_unit(duration_style style,rt_ratio rt, intmax_t v) const;

__returns the unit associated to this duration.
      
[endsect]
        
[section:do_is_named_unit Protected Member Function `do_is_named_unit(rt_ratio)`]

      virtual bool do_is_named_unit(rt_ratio rt) const;

__returns true if the unit associated to the given Period is named, false otherwise.

[endsect]

[section:do_get_plural_forms Protected Member Function `do_get_plural_forms()`]

      virtual std::size_t do_get_plural_forms() const;

__returns the number of associated plural forms this facet manages.

[endsect]

[section:do_get_plural_form Protected Member Function `do_get_plural_form(int_least64_t)`]

      virtual std::size_t do_get_plural_form(int_least64_t value) const;

Gets the associated plural form.

__params

[param value] the duration representation

__returns the plural form associated to the `value` parameter. In English there are 2 plural forms

* 0 singular (-1 or 1)
* 1 plural for all others

[endsect]

[section:do_get_unit_seconds Protected Member Function `do_get_unit(duration_style,ratio<1>,std::size_t)`]

      virtual string_type do_get_unit(duration_style style, ratio<1> u, std::size_t pf) const;

__params

* [param style] the duration style.
* [param period] the period associated to the duration hours.
* [param pf] the requested plural form.

__returns if style is symbol returns "s", otherwise if pf is 0 return "second", if pf is 1 "seconds"

[endsect]

[section:do_get_unit_minutes Protected Member Function `do_get_unit(duration_style style,ratio<60>,std::size_t)`]

      virtual string_type do_get_unit(duration_style style, ratio<60> u, std::size_t pf) const;

__params

* [param style] the duration style.
* [param period] the period associated to the duration hours.
* [param pf] the requested plural form.

__returns if style is symbol returns "min", otherwise if pf is 0 return "minute", if pf is 1 "minutes"

[endsect]

[section:do_get_unit_hours Protected Member Function `do_get_unit(duration_style,ratio<3600>,std::size_t)`]

      virtual string_type do_get_unit(duration_style style, ratio<3600> u, std::size_t pf) const;

__params

* [param style] the duration style.
* [param period] the period associated to the duration hours.
* [param pf] the requested plural form.

__returns if style is symbol returns "h", otherwise if pf is 0 return "hour", if pf is 1 "hours"

[endsect]

[section:do_get_unit_xxx Protected Member Function `do_get_unit(duration_style,Period,std::size_t)`]

      virtual string_type do_get_unit(duration_style style, atto u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, femto u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, pico u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, nano u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, micro u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, milli u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, centi u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, deci u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, deca u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, hecto u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, kilo u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, mega u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, giga u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, tera u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, peta u, std::size_t pf) const;
      virtual string_type do_get_unit(duration_style style, exa u, std::size_t pf) const;
      
__params

* [param style] the duration style.
* [param u] the period tag atto.
* [param pf] the requested plural form.

__returns the concatenation of the prefix associated to period `u` + the one associated to seconds.

[endsect]

[section:do_get_ratio_prefix_xxx Protected Member Function `do_get_ratio_prefix(duration_style, Period)`]

      virtual string_type do_get_ratio_prefix(duration_style style, atto u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, femto u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, pico u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, nano u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, micro u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, milli u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, centi u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, deci u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, deca u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, hecto u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, kilo u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, mega u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, giga u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, tera u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, peta u) const;
      virtual string_type do_get_ratio_prefix(duration_style style, exa u) const;

__params

* [param style] the duration style.
* [param u] the period tag atto.

__returns depending on the value of `style` return the ratio_string symbol or prefix.

[endsect]


[endsect] [/section:duration_units_default]

[endsect] [/section:duration_units_hpp]

[/==================================================================]
[section:duration_io_hpp Header `<boost/chrono/io/duration_io.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {

        // manipulators

        std::ios_base& __symbol_format(ios_base& ios);
        std::ios_base& __name_format(ios_base& ios);
        class __duration_fmt;
        template<class CharT, class Traits>
        std::basic_ostream<CharT, Traits>&
        __duration_fmt__op_out(std::basic_ostream<CharT, Traits>& os, duration_fmt d);

        template<class CharT, class Traits>
        std::basic_istream<CharT, Traits>&
        __duration_fmt__op_in(std::basic_istream<CharT, Traits>& is, duration_fmt d);


        // duration I/O

        template <class CharT, class Traits, class Rep, class Period>
            std::basic_ostream<CharT, Traits>&
            __duration__op_out(std::basic_ostream<CharT, Traits>& os, const __duration<Rep, Period>& d);

        template <class CharT, class Traits, class Rep, class Period>
            std::basic_istream<CharT, Traits>&
            __duration__op_in(std::basic_istream<CharT, Traits>& is, __duration<Rep, Period>& d)

      }
    }


[section:manipulators I/O Manipulators]

There is a parameterized manipulator that takes the duration_style as parameter.
The symbol or name format can be easily chosen by streaming a `symbol_format` or `name_format` manipulators respectively.

[section:duration_fmt Template Class `duration_fmt`]

        class duration_fmt
        {
        public:

          explicit __duration_fmt__c1(__duration_style style) noexcept;

        #ifndef BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS
          explicit
          __duration_fmt__op_duration_style() const noexcept;
        #endif
          __duration_style __duration_fmt__get_duration_style() const noexcept;
        };

        template<class CharT, class Traits>
        std::basic_ostream<CharT, Traits>&
        __duration_fmt__op_out(std::basic_ostream<CharT, Traits>& os, duration_fmt d);

        template<class CharT, class Traits>
        std::basic_istream<CharT, Traits>&
        __duration_fmt__op_in(std::basic_istream<CharT, Traits>& is, duration_fmt d);


[section:c1 Constructor `duration_fmt(duration_style)`]

  explicit duration_fmt(duration_style f) noexcept;

__effects Constructs a __duration_fmt by storing `f`.

__postcondition `static_cast<duration_style>(*this) == f`.

[endsect]
[section:op_duration_style Member Function `operator duration_style()`]

  explicit operator duration_style() const noexcept;
  duration_style get_duration_style() const noexcept;

__returns: The stored __duration_fmt `f`.

[endsect]
[section:op_out Member Function `operator <<(basic_ostream<>&, duration_fmt)`]

  template<class CharT, class Traits>
    basic_ostream<CharT, Traits>&
    operator<<(basic_ostream<CharT, Traits>& s, duration_fmt d);

__effects: `__set_duration_style(s, static_cast<__duration_style>(d))))`.

__returns: `s`.

[endsect]
[section:op_in Member Function `operator >>(basic_istream<>&, duration_fmt)`]

  template<class CharT, class Traits>
    basic_istream<CharT, Traits>&
    operator>>(basic_istream<CharT, Traits>& s, duration_fmt d);

__effects: `__set_duration_style(s, static_cast<__duration_style>(d))))`.

__returns: `s`.

[endsect]
[endsect]

[section:symbol_format Non Member Function `symbol_format(ios_base&)`]

        std::ios_base& __symbol_format(ios_base& ios);

__effects: `__set_duration_style(s, __duration_style::symbol)`.

__returns `ios`

[endsect]
[section:name_format Non Member Function `name_format(ios_base&)`]

        std::ios_base& __name_format(ios_base& ios);

__effects: `__set_duration_style(s, __duration_style::prefix)`.

__returns `ios`


[endsect]
[endsect]


[section:streams I/O Streams Operations]

[section:duration__op_out Member Function `operator <<(basic_ostream<>&, const duration<>& d)`]

Any __duration can be streamed out to a `basic_ostream`. 
The run-time value of the __duration is formatted according to the rules and current format settings for __duration`::rep` and the __duration_units facet.


        template <class CharT, class Traits, class Rep, class Period>
            std::basic_ostream<CharT, Traits>&
            operator<<(std::basic_ostream<CharT, Traits>& os, const duration<Rep, Period>& d);

__effects Behaves as a formatted output function. 
After constructing a sentry object, if the sentry converts to true, 
calls to `facet.__duration_put_put(os,os,os.fill(),d)` where `facet` is the `__duration_put<CharT>`
facet associated to `os` or a new created instance of the default `__duration_put<CharT>` facet.

__returns `os`.

[endsect]
[section:duration__op_in Member Function `operator <<(basic_istream<>&, const duration<>& d)`]

        template <class CharT, class Traits, class Rep, class Period>
            std::basic_istream<CharT, Traits>&
            operator>>(std::basic_istream<CharT, Traits>& is, duration<Rep, Period>& d)

__effects Behaves as a formatted input function. After constructing a `sentry` object, if the `sentry`
converts to `true`, calls to `facet.__duration_get_get(is,std::istreambuf_iterator<CharT, Traits>(), is, err, d)` where `facet` is the `__duration_get<CharT>`
facet associated to `is` or a new created instance of the default `__duration_get<CharT>` facet.

If any step fails, calls `os.setstate(std::ios_base::failbit | std::ios_base::badbit)`.

__returns `is`
[endsect]

[endsect]


[endsect] [/section:duration_io_hpp Header `<boost/chrono/io/duration_io.hpp>`]

[/==================================================================]
[section:time_point_get_hpp Header `<boost/chrono/io/time_point_get.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {
        template <class CharT, class InputIterator = std::istreambuf_iterator<CharT> >
        class time_point_get;
      }
    }

[section:time_point_get Template Class `time_point_get`]
    
    template <class CharT, class InputIterator = std::istreambuf_iterator<CharT> >
    class time_point_get: public std::locale::facet
    {
    public:
      typedef CharT char_type; // Type of character the facet is instantiated on.
      typedef InputIterator iter_type; // Type of iterator used to scan the character buffer.

      explicit __time_point_get_c(size_t refs = 0);

      template <class Clock, class Duration>
      iter_type __time_point_get_get(iter_type i, iter_type e, std::ios_base& is, std::ios_base::iostate& err,
          time_point<Clock, Duration> &tp, const char_type *pattern, const char_type *pat_end) const;

      template <class Clock, class Duration>
      iter_type __time_point_get_get2(iter_type i, iter_type e, std::ios_base& is, std::ios_base::iostate& err,
          time_point<Clock, Duration> &tp) const;
          
      template <typename Rep, typename Period>
      iter_type __time_point_get_get_duration(iter_type i, iter_type e, std::ios_base& is, std::ios_base::iostate& err,
          duration<Rep, Period>& d) const;

      template <class Clock>
      iter_type __time_point_get_get_epoch(iter_type i, iter_type e, std::ios_base& is, std::ios_base::iostate& err) const;

      static std::locale::id id; // Unique identifier for this type of facet.

      __time_point_get_d();
    };


`time_point_get` is used to parse a character sequence, extracting
the duration and the epoch into a class `time_point`.

The pattern can contain the format specifiers `%d` and `%e` in any order.

User confirmation is required for reliable parsing of
user-entered durations, but machine-generated formats can be parsed
reliably. This allows parsers to be aggressive about interpreting user
variations on standard formats.

If the end iterator is reached during parsing the member function sets `std::ios_base::eofbit` in `err`.

[section:c Constructor `time_point_get(size_t)`]

      explicit time_point_get(size_t refs);

Constructs a __time_point_get facet.

__params

* [param refs] references

__effects Constructs a __duration_put facet.
If the `refs` argument is `0` then destruction of the object is
delegated to the locale, or locales, containing it. This allows
the user to ignore lifetime management issues. On the other had,
if `refs` is `1` then the object must be explicitly deleted;
`locale` will not do so. In this case, the object can be
maintained across the lifetime of multiple locales.

[endsect]

[section:d Destructor `~time_point_get()`]

      ~time_point_get();

__effects Destroy the facet.
      
[endsect]
[section:get Template Member Function `get(iter_type,iter_type,std::ios_base&,std::ios_base::iostate&,time_point<Clock, Duration>&,const CharT*,const CharT*)`]

      template <class Clock, class Duration>
      iter_type get(iter_type s, iter_type end, std::ios_base& ios, std::ios_base::iostate& err,
          time_point<Clock, Duration> &tp, const char_type *pattern, const char_type *pat_end) const;

__params

* [param s] start input stream iterator
* [param end] end input stream iterator
* [param ios] a reference to a `ios_base`
* [param err] the `ios_base::iostate`
* [param tp] the __time_point
* [param pattern] begin of the formatting pattern
* [param pat_end] end of the formatting pattern

__requires `[pattern,pat_end)` must be a valid range.

__effects: The function starts by evaluating `err = std::ios_base::goodbit`.
It then enters a loop, reading zero or more characters from `s` at
each iteration. Unless otherwise specified below, the loop
terminates when the first of the following conditions holds:

* The expression `pattern == pat_end` evaluates to `true`.
* The expression `err == std::ios_base::goodbit` evaluates to `false`.
* The expression `s == end` evaluates to `true`, in which case the
function evaluates `err = std::ios_base::eofbit | std::ios_base::failbit`.
* The next element of pattern is equal to `'%'`, followed by a conversion
specifier character, the functions `get_duration` or `get_epoch` are called depending on
whether the format is `'d'` or `'e'`.
If the number of elements in the range `[pattern,pat_end)` is not
sufficient to unambiguously determine whether the conversion
specification is complete and valid, the function evaluates
`err |= std::ios_base::failbit`. Otherwise, the function evaluates
`s = do_get(s, end, ios, err, d)`. If `err == std::ios_base::goodbit` holds after
the evaluation of the expression, the function increments pattern to
point just past the end of the conversion specification and continues
looping.
* The expression `isspace(*pattern, ios.getloc())` evaluates to `true`, in
which case the function first increments `pattern` until
`pattern == pat_end || !isspace(*pattern, ios.getloc())` evaluates to `true`,
then advances `s` until `s == end || !isspace(*s, ios.getloc())` is `true`,
and finally resumes looping.
* The next character read from `s` matches the element pointed to by
pattern in a case-insensitive comparison, in which case the function
evaluates `++pattern`, `++s` and continues looping. Otherwise, the function
evaluates `err = std::ios_base::failbit`.

__returns An iterator pointing just beyond the last character that can be determined to be part of a valid time_point.


[endsect]
[section:get2 Template Member Function `get(iter_type,iter_type,std::ios_base&,std::ios_base::iostate&,time_point<Clock,Duration>&)`]

      template <class Clock, class Duration>
      iter_type get(iter_type s, iter_type end, std::ios_base& ios, std::ios_base::iostate& err,
          time_point<Clock, Duration> &tp) const;
          
__params

* [param s] start input stream iterator
* [param end] end input stream iterator
* [param ios] a reference to a `ios_base`
* [param err] the `ios_base::iostate`
* [param tp] the __time_point

__effects Stores the duration pattern from the `duration_unit` facet in let say `str`. Last as if

  return get(s, end, ios, err, ios, d, str.data(), str.data() + str.size());

__returns An iterator pointing just beyond the last character that can be determined to be part of a valid name.

[endsect]
[section:get_duration Template Member Function `get_duration(iter_type,iter_type,std::ios_base&,std::ios_base::iostate&,duration<Rep,Period>&)`]

      template <typename Rep, typename Period>
      iter_type get_duration(iter_type s, iter_type end, std::ios_base& ios, std::ios_base::iostate& err,
          duration<Rep, Period>& d) const;

__effects As if  

  return facet.get(s, end, ios, err, d);

where `facet` is either the `duration_get` facet associated to the `ios` or an instance of the default `duration_get` facet.

__returns An iterator pointing just beyond the last character that can be determined to be part of a valid duration.

[endsect]
[section:get_epoch Template Member Function `get_epoch(iter_type,iter_type,std::ios_base&,std::ios_base::iostate&)`]

      template <class Clock>
      iter_type get_epoch(iter_type s, iter_type end, std::ios_base& ios, std::ios_base::iostate& err) const;
      
__effects  Let `facet` be the __time_point_units facet associated to `ios` or a new instance of the default __time_point_units_default facet.
Let `epoch` be the epoch string associated to the `Clock` using this facet.
Scans `s` to match `epoch` or `end` is reached.

If not match before the `end` is reached `std::ios_base::failbit` is set in `err`.
If `end` is reached `std::ios_base::failbit` is set in `err`.
       
__returns An iterator pointing just beyond the last character that can be determined to be part of a epoch.

[endsect]
    
[endsect] [/section:time_point_get]


[endsect] [/section:time_point_get_hpp]

[/==================================================================]
[section:time_point_put_hpp Header `<boost/chrono/io/time_point_put.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {
        template <class CharT, class OutputIterator = std::ostreambuf_iterator<CharT> >
        class time_point_put;
      }
    }


[section:time_point_put Template Class `time_point_put`]


The __time_point_put facet provides facilities for formatted output of __time_point values.
The member function of __time_point_put take a __time_point and format it into character string representation.
     
tparam ChatT a character type
tparam OutputIterator a model of `OutputIterator`
     
     
    template <class CharT, class OutputIterator = std::ostreambuf_iterator<CharT> >
    class time_point_put: public std::locale::facet
    {
    public:
      typedef CharT char_type; // Type of character the facet is instantiated on.
      typedef std::basic_string<CharT> string_type; // Type of character string passed to member functions.
      typedef OutputIterator iter_type; // Type of iterator used to write in the character buffer.

      explicit time_point_put(size_t refs = 0);
      ~time_point_put();

      template <class Clock, class Duration>
      iter_type put(iter_type i, std::ios_base& ios, char_type fill, time_point<Clock, Duration> const& tp, const CharT* pattern,
          const CharT* pat_end) const;
      template <class Clock, class Duration>
      iter_type put(iter_type i, std::ios_base& ios, char_type fill, time_point<Clock, Duration> const& tp) const;
      template <typename Rep, typename Period>
      iter_type put_duration(iter_type i, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d) const;
      template <typename Clock>
      iter_type put_epoch(iter_type i, std::ios_base& os) const;
      
      static std::locale::id id; // Unique identifier for this type of facet.


    };


[section:c Constructor `time_point_get(size_t)`]

      explicit time_point_put(size_t refs = 0);


Construct a time_point_put facet.

__effects Construct a time_point_put facet.
If the `refs` argument is `0` then destruction of the object is
delegated to the locale, or locales, containing it. This allows
the user to ignore lifetime management issues. On the other had,
if `refs` is `1` then the object must be explicitly deleted;
the locale will not do so. In this case, the object can be
maintained across the lifetime of multiple locales.

__params

* [param refs] references

[endsect]

[section:put1 Member Function `put()`]

      template <class Clock, class Duration>
      iter_type put(iter_type i, std::ios_base& ios, char_type fill, time_point<Clock, Duration> const& tp, const CharT* pattern,
          const CharT* pat_end) const;


__params

* [param i] an output stream iterator
* [param ios] a reference to a ios_base
* [param fill] the character used as filler
* [param tp] the __time_point
* [param pattern] begin of the formatting pattern
* [param pat_end] end of the formatting pattern

__effects Steps through the sequence from `pattern` to `pat_end`,
identifying characters that are part of a pattern sequence. Each character
that is not part of a pattern sequence is written to `s` immediately, and
each pattern sequence, as it is identified, results in a call to
__put_duration or __put_epoch;
thus, pattern elements and other characters are interleaved in the output
in the order in which they appear in the pattern. Pattern sequences are
identified by converting each character `c` to a `char` value as if by
`ct.narrow(c,0)`, where `ct` is a reference to `ctype<charT>` obtained from
`ios.getloc()`. The first character of each sequence is equal to `'%'`,
followed by a pattern specifier character spec, which can be `'d'` for
the duration value or `'e'` for the epoch.
For each valid pattern sequence identified, calls
`put_duration(s, ios, fill, tp.time_since_epoch())` or `put_epoch(s, ios)`.

__returns An iterator pointing immediately after the last character produced.

[endsect]

[section:put2 Member Function `put()`]

      template <class Clock, class Duration>
      iter_type put(iter_type i, std::ios_base& ios, char_type fill, time_point<Clock, Duration> const& tp) const;

__params

* [param i] an output stream iterator
* [param ios] a reference to a ios_base
* [param fill] the character used as filler
* [param tp] the __time_point
* [param tern] begin of the formatting pattern
* [param pat_end] end of the formatting pattern

__effects Stores the time_point pattern from the __time_point_unit facet in let say `str`. Last as if

  return put(s, ios, fill, tp, str.data(), str.data() + str.size());

__returns An iterator pointing immediately after the last character produced.

[endsect]

[section:put_duration Member Function `put_duration()`]

      template <typename Rep, typename Period>
      iter_type put_duration(iter_type i, std::ios_base& ios, char_type fill, duration<Rep, Period> const& d) const;

__params

* [param i] an output stream iterator
* [param ios] a reference to a ios_base
* [param fill] the character used as filler
* [param d] the __duration

__effects As if `facet.put(s, ios, fill, d)` where facet is the `__duration_put<CharT>` facet associated
to the `ios` or a new instance of `__duration_put<CharT>`.

__returns An iterator pointing immediately after the last character produced.

[endsect]

[section:put_epoch Member Function `put_epoch()`]

      template <typename Clock>
      iter_type put_epoch(iter_type i, std::ios_base& os) const;

__params

* [param i] an output stream iterator
* [param ios] a reference to a ios_base

__effects As if

  string_type str = facet.template get_epoch<Clock>();
  s=std::copy(str.begin(), str.end(), s);


where facet is the `__time_point_units<CharT>` facet associated
to the `ios` or a new instance of `__time_point_units_default<CharT>`.

__returns s, iterator pointing immediately after the last character produced.

[endsect]

[endsect]

[endsect] [/section:time_point_put_hpp]

[/==================================================================]
[section:time_point_units_hpp Header `<boost/chrono/io/time_point_units.hpp>`]
[/==================================================================]

    namespace boost {
      namespace chrono {
        template <typename CharT, typename Clock, typename TPUFacet>
        std::basic_string<CharT> get_epoch_custom(Clock, TPUFacet& f);
        template <typename CharT=char>
        class time_point_units;
        template <typename CharT=char>
        class time_point_units_default,
      }
    }

[section:get_epoch_custom Template Function `get_epoch_custom`]

        template <typename CharT, typename Clock, typename TPUFacet>
        std::basic_string<CharT> get_epoch_custom(Clock, TPUFacet& f);
        

Customization point to the epoch associated to the clock `Clock`
The default calls `f.do_get_epoch(Clock())`. 

The user can overload this function.
    
        
__returns Forwards the call to the facet as if 

      return f.do_get_epoch(Clock());


[endsect]
[//////////////////////////////////////////////////////////]
[section:time_point_units Template Class `time_point_units`]


__time_point_units facet gives useful information about the time_point pattern,
the text associated to a time_point's epoch,

    template <typename CharT=char>
    class time_point_units: public std::locale::facet
    {
    public:
      typedef CharT char_type; // Type of character the facet is instantiated on.
      typedef std::basic_string<char_type> string_type; // Type of character string used by member functions.
      static std::locale::id id; // Unique identifier for this type of facet.

      explicit time_point_units(size_t refs = 0);

      virtual string_type get_pattern() const =0;
      template <typename Clock>
      string_type get_epoch() const;

    protected:
      virtual ~time_point_units();
      virtual string_type do_get_epoch(system_clock) const=0;
      virtual string_type do_get_epoch(steady_clock) const=0;

  #if defined(BOOST_CHRONO_HAS_PROCESS_CLOCKS)
      virtual string_type do_get_epoch(process_real_cpu_clock) const=0;
      virtual string_type do_get_epoch(process_user_cpu_clock) const=0;
      virtual string_type do_get_epoch(process_system_cpu_clock) const=0;
      virtual string_type do_get_epoch(process_cpu_clock) const=0;
  #endif
  #if defined(BOOST_CHRONO_HAS_THREAD_CLOCK)
      virtual string_type do_get_epoch(thread_clock) const=0;
  #endif

    };
    
    
[section:c Constructor `time_point_units()`]

      explicit time_point_units(size_t refs = 0);

Construct a __time_point_units facet.

__params

* [param refs] references

__effects Construct a __time_point_units facet.
If the `refs` argument is `0` then destruction of the object is
delegated to the locale, or locales, containing it. This allows
the user to ignore lifetime management issues. On the other had,
if `refsv is `1` then the object must be explicitly deleted;
the locale will not do so. In this case, the object can be
maintained across the lifetime of multiple locales.

[endsect]
    
[section:get_pattern Member Function `get_pattern()`]

      virtual string_type get_pattern() const =0;

__returns the pattern to be used by default.

[endsect]
    
[section:get_epoch Member Function `get_epoch()`]

      template <typename Clock>
      string_type get_epoch() const;

__returns the epoch associated to the clock `Clock` as if `return get_epoch_custom<CharT>(Clock(), *this);
`

[endsect]
    
[section:d Destructor `time_point_units()`]

      virtual ~time_point_units();

Destroy the facet.

[endsect]

[section:do_get_epoch_system_clock Member Function `do_get_epoch(system_clock)`]

      virtual string_type do_get_epoch(system_clock) const=0;

__params

* [param c] a dummy instance of __system_clock.

__returns The epoch string associated to the __system_clock.

[endsect]

[section:do_get_epoch_steady_clock Member Function `do_get_epoch(steady_clock)`]

      virtual string_type do_get_epoch(steady_clock) const=0;

__params

* [param c] a dummy instance of __steady_clock.

__returns The epoch string associated to the __steady_clock.

[endsect]

[section:do_get_epoch_process_real_cpu_clock Member Function `do_get_epoch(process_real_cpu_clock)`]

  #if defined(BOOST_CHRONO_HAS_PROCESS_CLOCKS)
      virtual string_type do_get_epoch(process_real_cpu_clock) const=0;
  #endif
  
__params

* [param c] a dummy instance of __process_real_cpu_clock.

__returns The epoch string associated to the __process_real_cpu_clock.

[endsect]

[section:do_get_epoch_process_user_cpu_clock Member Function `do_get_epoch(process_user_cpu_clock)`]

  #if defined(BOOST_CHRONO_HAS_PROCESS_CLOCKS)
      virtual string_type do_get_epoch(process_user_cpu_clock) const=0;
  #endif
  
__params

* [param c] a dummy instance of __process_real_cpu_clock.

__returns The epoch string associated to the process_user_cpu_clock.

[endsect]

[section:do_get_epoch_process_system_cpu_clock Member Function `do_get_epoch(process_system_cpu_clock)`]

  #if defined(BOOST_CHRONO_HAS_PROCESS_CLOCKS)
      virtual string_type do_get_epoch(process_system_cpu_clock) const=0;
  #endif
  
__params

* [param c] a dummy instance of __process_system_cpu_clock.

__returns The epoch string associated to the process_user_cpu_clock.

[endsect]

[section:do_get_epoch_process_process_cpu_clock Member Function `do_get_epoch(process_cpu_clock)`]

  #if defined(BOOST_CHRONO_HAS_PROCESS_CLOCKS)
      virtual string_type do_get_epoch(process_cpu_clock) const=0;
  #endif
  
__params

* [param c] a dummy instance of __process_cpu_clock.

__returns The epoch string associated to the process_cpu_clock.

[endsect]

[section:do_get_epoch_process_thread_clock Member Function `do_get_epoch(thread_clock)`]

  #if defined(BOOST_CHRONO_HAS_THREAD_CLOCK)
      virtual string_type do_get_epoch(thread_clock) const=0;
  #endif
  
__params

* [param c] a dummy instance of __thread_clock.

__returns The epoch string associated to the __thread_clock.

[endsect]


[endsect]
[section:time_point_units_default Template Class `time_point_units_default`]

    // This class is used to define the strings for the default English
    template <typename CharT=char>
    class time_point_units_default: public time_point_units<CharT>
    {
    public:
      typedef CharT char_type; // Type of character the facet is instantiated on.
      typedef std::basic_string<char_type> string_type; // Type of character string used by member functions.

      explicit time_point_units_default(size_t refs = 0);
      ~time_point_units_default();

      /**
       * __returns the default pattern "%d%e".
       */
      string_type get_pattern() const;
      
    protected:
      /**
       * [param c a dummy instance of __system_clock.
       * __returns The epoch string returned by `clock_string<system_clock,CharT>::since()`.
       */
      string_type do_get_epoch(system_clock ) const;
      /**
       * [param c a dummy instance of __steady_clock.
       * __returns The epoch string returned by `clock_string<steady_clock,CharT>::since()`.
       */
      string_type do_get_epoch(steady_clock ) const;
  #if defined(BOOST_CHRONO_HAS_PROCESS_CLOCKS)
      /**
       * [param c a dummy instance of __process_real_cpu_clock.
       * __returns The epoch string returned by `clock_string<process_real_cpu_clock,CharT>::since()`.
       */
      string_type do_get_epoch(process_real_cpu_clock ) const;
      /**
       * [param c a dummy instance of __process_user_cpu_clock.
       * __returns The epoch string returned by `clock_string<process_user_cpu_clock,CharT>::since()`.
       */
      string_type do_get_epoch(process_user_cpu_clock ) const;
      /**
       * [param c a dummy instance of __process_system_cpu_clock.
       * __returns The epoch string returned by `clock_string<process_system_cpu_clock,CharT>::since()`.
       */
      string_type do_get_epoch(process_system_cpu_clock ) const;
      /**
       * [param c a dummy instance of __process_cpu_clock.
       * __returns The epoch string returned by `clock_string<process_cpu_clock,CharT>::since()`.
       */
      string_type do_get_epoch(process_cpu_clock ) const;

  #endif
  #if defined(BOOST_CHRONO_HAS_THREAD_CLOCK)
      /**
       * [param c a dummy instance of __thread_clock.
       * __returns The epoch string returned by `clock_string<thread_clock,CharT>::since()`.
       */
      string_type do_get_epoch(thread_clock ) const;
  #endif

    };

[endsect]
[endsect] [/section:time_point_units_hpp]


[/==================================================================]
[section:time_point_io_hpp Header `<boost/chrono/io/time_point_io.hpp>`]
[/==================================================================]


    namespace boost {
    namespace chrono {


        // manipulators

        unspecified __time_fmt1(timezone tz);
        template<class CharT>
        unspecified __time_fmt2(timezone tz, basic_string<CharT> f);
        template<class CharT>
        unspecified __time_fmt2(timezone tz, const CharT* f);

        // i/o state savers

        template<typename CharT = char, typename Traits = std::char_traits<CharT> >
        struct __timezone_io_saver
        {
          typedef std::basic_ios<CharT, Traits> state_type;
          typedef timezone aspect_type;

          explicit __timezone_io_saver__c1(state_type &s);
          __timezone_io_saver__c2(state_type &s, aspect_type new_value);
          __timezone_io_saver__d();
          void timezone_io_saver__restore();
        };

        template<typename CharT = char, typename Traits = std::char_traits<CharT> >
        struct __time_fmt_io_saver
        {
          typedef std::basic_ios<CharT, Traits> state_type;

          explicit time_fmt_io_saver(state_type &s);
          time_fmt_io_saver(state_type &s, basic_string<CharT> const& new_value);
          ~ time_fmt_io_saver();
          void restore();
        };

        // system_clock I/O
  
        template <class CharT, class Traits, class __Duration>
          basic_ostream<CharT, Traits>&
          operator<<(basic_ostream<CharT, Traits>& os,
                     const time_point<system_clock, __Duration>& tp);

        template <class CharT, class Traits, class __Duration>
          basic_istream<CharT, Traits>&
          operator>>(basic_istream<CharT, Traits>& is,
                     time_point<system_clock, __Duration>& tp);
    
        // Other Clocks I/O

        template <class CharT, class Traits, class __Clock, class __Duration>
            std::basic_ostream<CharT, Traits>&
            operator<<(std::basic_ostream<CharT, Traits>& os,
                   const time_point<__Clock, __Duration>& tp);

        template <class CharT, class Traits, class __Clock, class __Duration>
            std::basic_istream<CharT, Traits>&
            operator>>(std::basic_istream<CharT, Traits>& is,
                   time_point<__Clock, __Duration>& tp);

    }
    }



[section:manip I/O Manipulators]
[section:time_fmt1 Non Member Function `time_fmt(__timezone)` ]

  unspecified time_fmt(__timezone tz);

__returns: An unspecified object that when streamed to a `basic_ostream<CharT, Traits>` or `basic_istream<CharT, Traits>` `s` will have the effects of:

          __set_timezone(s, tz);

[endsect]

[section:time_fmt2 Non Member Function `time_fmt(__timezone, string)` ]

  template<class CharT>
    unspecified time_fmt(timezone tz, basic_string<CharT> f);
  template<class CharT>
    unspecified time_fmt(timezone tz, const CharT* f);

__returns: An unspecified object that when streamed to a `basic_ostream<CharT, Traits>` or `basic_istream<CharT, Traits>` `s` will have the effects of:

          __set_timezone(s, tz);
          __set_time_fmt<CharT>(s, f);

[endsect]
[endsect]



[section:streams I/O Streams Operations]

[section:system_clock `system_clock`]

[/warning

The time_point<system_clock,D> formatter/parser specializations don't work yet. It has been disable defining `BOOST_CHRONO_DONT_PROVIDE_DATE_IO_FOR_SYSTEM_CLOCK_TIME_POINT` until a fix for the following ticket is found:

- [@http://svn.boost.org/trac/boost/ticket/7547 #7547] time_point<system_clock> input version 2 fails to compile 

- [@http://svn.boost.org/trac/boost/ticket/7546 #7546] time_point<system_clock> output version 2 fails to compile 

In this case the io operators behave like any time_point as if the specialization was removed.

In order to enable this features on compilers working with, you will need to define `BOOST_CHRONO_PROVIDES_DATE_IO_FOR_SYSTEM_CLOCK_TIME_POINT`.

]

[section:op_out Non Member Function `operator<<()`]

        template <class CharT, class Traits, class __Duration>
            std::basic_ostream<CharT, Traits>&
            operator<<(std::basic_ostream<CharT, Traits>& os,
                   const time_point<system_clock, __Duration>& tp);

__effects Behaves as a formatted output function. After constructing a `sentry` object, if the `sentry` converts to 
`true`, sets a local variable `tz` of type __timezone to `get_timezone(os)`. 
Additionally the format string obtained with `__get_time_fmt()` is recorded as a pair of `const CharT*`. 
If the stream has no time_punct facet, then this pair of `const CharT*` that represent an empty range.

Next `tp` is converted to a `time_t`, and this `time_t` is converted to a `tm`. 
The conversion to `tm` use `gmtime` (when available) if the timezone is __utc, else it use localtime (if available).

Using the `std::time_put` facet stored in `os`, this inserter writes characters to the stream using the `tm` and the 
formatting string stored in the `time_punct` facet, unless that facet was missing, or unless it provided an empty string.

If the formatting string is empty, then output as follows:

First output `tm` using `"%F %H:%M:"`

Next output a `double` representing the number of seconds stored in the `tm` plus the fractional seconds represented 
in `tp`. The format shall be `ios::fixed` and the precision shall be sufficient to represent `system_clock::duration` 
exactly (e.g. if `system_clock::period` is micro, the precision should be sufficient to output six digits following 
the decimal point). If the number of seconds is less than `10`, the output shall be prefixed by `'0'`.

Finally if `tz` is __local, output the `tm` using the pattern " %z". Else append the sequence " +0000" to the stream.

If any step fails, calls `os.setstate(ios_base::failbit | ios_base::badbit)`.

__returns `os`

[endsect]
[section:op_in Non Member Function `operator>>()`]

        template <class CharT, class Traits, class __Duration>
            std::basic_istream<CharT, Traits>&
            operator>>(std::basic_istream<CharT, Traits>& is,
                   time_point<system_clock, __Duration>& tp);

__effects Behaves as a formatted input function. After constructing a sentry object, if the sentry converts to true, 
obtains a `std::time_get` facet from `is`, and obtains a formatting string in the same manner as described for 
insertion operator. Extract a `tm` using the supplied formatting string, or if empty, defaulted as described for the 
insertion operator. Note that extraction does not use the __timezone data stored in the `is` for the 
defaulted string as the __timezone information is stored in the stream.

Any `time_point<system_clock, __Duration>` inserted, and then extracted should result in an equal 
`time_point<system_clock, __Duration>`, excepting any precision that did not get inserted.

__example

    void test(std::chrono::system_clock::time_point tp)
    {
        std::stringstream s;
        s << tp;
        boost::chrono::system_clock::time_point tp2;
        s >> tp2;
        assert(tp == tp2);
    }

__returns is

[endsect]

[endsect]


[section:other_clocks Other Clocks]
[section:op_out Non Member Function `operator<<()`]


        template <class CharT, class Traits, class __Clock, class __Duration>
            std::basic_ostream<CharT, Traits>&
            operator<<(std::basic_ostream<CharT, Traits>& os,
                   const time_point<__Clock, __Duration>& tp);

__effects Behaves as a formatted output function. After constructing a `sentry` object, if the `sentry`
converts to `true`, calls to `facet.put(os,os,os.fill(),tp)` where `facet` is the `time_point_put<CharT>`
facet associated to `os` or a new created instance of the default `time_point_put<CharT>` facet.

__returns `os`.

__example

    22644271279698 nanoseconds since boot


[endsect]
[section:op_in Non Member Function `operator>>()`]

        template <class CharT, class Traits, class __Clock, class __Duration>
            std::basic_istream<CharT, Traits>&
            operator>>(std::basic_istream<CharT, Traits>& is,
                   time_point<__Clock, __Duration>& tp);

Extracts `tp` from the stream `is`.

__effects Behaves as a formatted input function. After constructing a `sentry` object, if the `sentry`
converts to `true`, calls to `facet.get(is,std::istreambuf_iterator<CharT, Traits>(), is, err, tp)` where `facet` is the `time_point_get<CharT>`
facet associated to `is` or a new created instance of the default `time_point_get<CharT>` facet.

If any step fails, calls `os.setstate(std::ios_base::failbit | std::ios_base::badbit)`.

__returns `is`.

[endsect]

[endsect]
[endsect]



[endsect] [/section:time_point_io_hpp Header `<boost/chrono/io/time_point_io.hpp>`]



[/
[/==================================================================]
[section:duration_unit_string_hpp Header `<boost/chrono/io/duration_unit_string.hpp>`]
[/==================================================================]

    namespace boost { namespace chrono {

      template <class Period, class CharT>
      struct duration_period_strings
      {
        static std::basic_string<CharT> plural();
        static std::basic_string<CharT> singular();
        static std::basic_string<CharT> symbol();
      };

      template <class Period, class CharT>
      struct duration_period
      {
        static std::basic_string<CharT> prefix(std::basic_string<CharT> const& translation);
        static std::basic_string<CharT> symbol();
      };

      template <class CharT, class Rep, class Period>
      std::basic_string<CharT> duration_unit_string(
        bool is_prefix,
        duration<Rep,Period> const& d);

    } }

[section:duration_period_suffix Template Class `duration_period_suffix<>`]

[endsect]

[section: duration_unit_string Non Member Function `duration_unit_string()`]

      template <class CharT, class Rep, class Period>
      std::basic_string<CharT> duration_unit_string(
        bool is_prefix,
        duration<Rep,Period> const& d);

First units are obtained from `Period::type` and stored in two `basic_string<CharT>` (e.g. singular/plural) according to the following rules:

* If `is_prefix` is `true`

  * If `Period::type` is `ratio<3600>`, `singular` is set to `"hour"` and  `plural` is set to `"hours"`.
  * Else if `Period::type` is `ratio<60>`, `singular` is set to `"minute"` and `plural ` is set to `"minutes"`.
  * Else if `Period::type` is `ratio<1>` `singular` is set to `"second"` and `plural ` is set to `"seconds"`.
  * Else `singular ` is set to `ratio_string<Period, CharT>::prefix() + "second"` and `plural ` is set to `ratio_string<Period, CharT>::prefix() + "seconds"`.

Finally the intermediary unit string obtained by calling __duration_prefix_translate function with the `singular`, `plural` and the duration `d` as parameters.

* If `is_prefix` is `true`

  * If `Period::type` is `ratio<3600>` `symbol` is set to `"h"`.
  * Else if `Period::type` is `ratio<60>` `symbol` is set to `"min"`.
  * Else if `Period::type` is `ratio<1>` `symbol` is set to `"s"`.
  * Else `symbol` is set to `ratio_string<Period, CharT>::symbol() + "s"`.

Finally the intermediary unit string is obtained by calling duration_symbol_translate function

Note: The string literals above need to be converted to the correct literal type to be assigned to a `basic_string<CharT>`.

[endsect]

[endsect] [/section:duration_units_string_hpp Header `<boost/chrono/io/duration_uinit_string.hpp>`]

[/==================================================================]
[section:translate_hpp Header `<boost/chrono/io/translate.hpp>`]
[/==================================================================]

    namespace boost { namespace chrono {

    #if !defined BOOST_CHRONO_SPECIFIC_TRANSLATE
      template <class CharT, class Rep>
      std::basic_string<CharT> duration_prefix_translate(
        std::basic_string<CharT> const &singular,
        std::basic_string<CharT> const &plural,
        Rep v);

      template <class CharT>
      std::basic_string<CharT> duration_symbol_translate(
        std::basic_string<CharT> const &symbol);

      template <class CharT>
      std::basic_string<CharT> epoch_translate(
        std::basic_string<CharT> const &epoch);

    #endif


    } }

The library provides a default implementation of these translation function if BOOST_CHRONO_SPECIFIC_TRANSLATE is not defined.

The user can provide its single specific implementation by ensuring that equivalent prototypes are provided before their use is needed and BOOST_CHRONO_SPECIFIC_TRANSLATE is defined.

Next follow the behavior of the default implementation.

[section:duration_prefix_translate Non Member Function `duration_prefix_translate()`]

      template <class CharT, class Rep>
      std::basic_string<CharT> duration_prefix_translate(
        std::basic_string<CharT> const &singular,
        std::basic_string<CharT> const &plural,
        Rep r);

__returns If `r` is `-1` or `1` returns `singular`, else `plural`.

[endsect]

[section:duration_symbol_translate Non Member Function `duration_symbol_translate()`]

      template <class CharT>
      std::basic_string<CharT> duration_symbol_translate(
        std::basic_string<CharT> const &symbol);

__returns `symbol`.

[endsect]
[section:epoch_translate Non Member Function `epoch_translate()`]

      template <class CharT>
      std::basic_string<CharT> epoch_translate(
        std::basic_string<CharT> const &epoch);

__returns `epoch`.

[endsect]

[endsect] [/section:translate_hpp Header `<boost/chrono/io/translate.hpp>`]
]

[endsect] [/section:io Chrono I/O]


[section:round Chrono Rounding Utilities]
[/==================================================================]
[section:floor_hpp Header `<boost/chrono/floor.hpp>`]
[/==================================================================]

  namespace boost { namespace chrono {
    template <class To, class Rep, class Period>
    To floor(const duration<Rep, Period>& d);
  } }

This function round down the given parameter.

[endsect]
[/==================================================================]
[section:round_hpp Header `<boost/chrono/round.hpp>`]
[/==================================================================]

  namespace boost { namespace chrono {
    template <class To, class Rep, class Period>
    To round(const duration<Rep, Period>& d);
  } }

This function round to nearest, to even on tie the given parameter.


[endsect]
[/==================================================================]
[section:ceil_hpp Header `<boost/chrono/ceil.hpp>`]
[/==================================================================]

  namespace boost { namespace chrono {
    template <class To, class Rep, class Period>
    To ceil(const duration<Rep, Period>& d);
  } }

This function round up the given parameter.

[endsect]
[endsect]


[section:other_clocks Other Clocks]

[/==================================================================]
[section:process_cpu_clocks_hpp Header `<boost/chrono/process_cpu_clocks.hpp>`]
[/==================================================================]

Knowing how long a program takes to execute is useful in both test and production environments. It is also helpful if such timing information is broken down into real (wall clock) time, CPU time spent by the user, and CPU time spent by the operating system servicing user requests.

Process clocks don't include the time spent by the child process.

    #define BOOST_CHRONO_HAS_PROCESS_CLOCKS

    namespace boost { namespace chrono {

        class process_real_cpu_clock;
        class process_user_cpu_clock;
        class process_system_cpu_clock;
        class process_cpu_clock;

        template <typename Rep>
        struct process_times;
        template <class CharT, class Traits, class Rep>
        std::basic_ostream<CharT, Traits>&
        operator<<(std::basic_ostream<CharT, Traits>& os,
                process_times<Rep> const& rhs);

        template <class CharT, class Traits, class Rep>
        std::basic_istream<CharT, Traits>&
        operator>>(std::basic_istream<CharT, Traits>& is,
                process_times<Rep> const& rhs);

        template <class Rep>
        struct duration_values<process_times<Rep> >;

        template <class CharT>
        struct clock_string<process_real_cpu_clock, CharT>;
        struct clock_string<process_user_cpu_clock, CharT>;
        struct clock_string<process_system_cpu_clock, CharT>;
        struct clock_string<process_cpu_clock, CharT>;

    } }
    namespace std {
        template <class Rep>
        class numeric_limits<boost::chrono::process_times<Rep> >;
    }

[section: BOOST_CHRONO_HAS_PROCESS_CLOCKS Macro `BOOST_CHRONO_HAS_PROCESS_CLOCKS `]

This macro is defined if the platform supports process clocks.

[endsect]

[section:process_real_cpu_clock Class `process_real_cpu_clock`]

__process_real_cpu_clock satisfy the __clock_req.

__process_real_cpu_clock class provides access to the real process wall-clock steady clock, i.e. the real CPU-time clock of the calling process. The process relative current time can be obtained by calling `process_real_cpu_clock::now()`.

        class process_real_cpu_clock {
        public:
            typedef __nanoseconds                          duration;
            typedef duration::rep                        rep;
            typedef duration::period                     period;
            typedef chrono::__time_point<process_real_cpu_clock>    time_point;
            static constexpr bool is_steady =            true;

            static time_point now(  ) noexcept;
            static time_point now( system::error_code & ec );
        };


[endsect]
[section:process_user_cpu_clock Class `process_user_cpu_clock`]

__process_user_cpu_clock satisfy the __clock_req.

__process_user_cpu_clock class provides access to the user CPU-time steady clock of the calling process. The process relative user current time can be obtained by calling `process_user_cpu_clock::now()`.

        class process_user_cpu_clock {
        public:
            typedef __nanoseconds                          duration;
            typedef duration::rep                        rep;
            typedef duration::period                     period;
            typedef chrono::__time_point<process_user_cpu_clock>    time_point;
            static constexpr bool is_steady =            true;

            static time_point now(  ) noexcept;
            static time_point now( system::error_code & ec );
        };


[endsect]

[section:process_system_cpu_clock Class `process_system_cpu_clock`]

__process_system_cpu_clock satisfy the __clock_req.

__process_system_cpu_clock class provides access to the system CPU-time steady clock of the calling process. The process relative system current time can be obtained by calling `process_system_cpu_clock::now()`.

        class process_system_cpu_clock {
        public:
            typedef __nanoseconds                          duration;
            typedef duration::rep                        rep;
            typedef duration::period                     period;
            typedef chrono::__time_point<process_system_cpu_clock>    time_point;
            static constexpr bool is_steady =            true;

            static time_point now(  ) noexcept;
            static time_point now( system::error_code & ec );
        };


[endsect]

[section:process_cpu_clock Class `process_cpu_clock`]

`process_cpu_clock` can be considered as a `tuple<process_real_cpu_clock, process_user_cpu_clock, process_system_cpu_clock>`.

`process_cpu_clock` provides a thin wrapper around the operating system's process time API. For POSIX-like systems, that's the times() function, while for Windows, it's the `GetProcessTimes()` function.

The process relative real, user and system current time can be obtained at once by calling `process_clocks::now()`.

    class process_cpu_clock
    {
    public:
        typedef process_times<nanoseconds::rep> times ;

        typedef __duration<times,  nano>                  duration;
        typedef duration::rep                           rep;
        typedef duration::period                        period;
        typedef chrono::__time_point<process_cpu_clock>   time_point;
        static constexpr bool is_steady =               true;

        static time_point now(  ) noexcept;
        static time_point now( system::error_code & ec );
    };

[endsect]

[section:times Template Class `process_times`]

This class is the representation of the `process_cpu_clock::duration` class. As such it needs to implements the arithmetic operators.

        template <typename Rep>
        struct process_times : arithmetic<process_times<Rep>,
            multiplicative<process_times<Rep>, Rep,
            less_than_comparable<process_times<Rep> > > >
        {
            Rep real;    // real (i.e wall clock) time
            Rep user;    // user cpu time
            Rep system;  // system cpu time

            times();
            times(
                process_real_cpu_clock::rep r,
                process_user_cpu_clock::rep u,
                process_system_cpu_clock::rep s);

            template <typename Rep2>
            explicit process_times(
                Rep2 r);
            template <typename Rep2>
            explicit process_times(
                process_times<Rep2> const& rhs);
            operator rep() const;

            bool operator==(process_times const& rhs);
            template <typename Rep2>
            bool operator==(process_times<Rep2> const& rhs);

            times operator+=(process_times const& rhs);
            times operator-=(process_times const& rhs);
            times operator*=(process_times const& rhs);
            times operator/=(process_times const& rhs);
            bool operator<(process_times const & rhs) const;
        };

[endsect]

[section:times_io `process_times` Input/Output]

    template <class CharT, class Traits, class Rep>
    std::basic_ostream<CharT, Traits>&
    operator<<(std::basic_ostream<CharT, Traits>& os,
            process_times<Rep> const& rhs);

__effects Output each part separated by ';' and surrounded by '{', '}'.

__throws None.

    template <class CharT, class Traits, class Rep>
    std::basic_istream<CharT, Traits>&
    operator>>(std::basic_istream<CharT, Traits>& is,
            process_times<Rep> const& rhs);

__effects overrides the value of rhs if the input stream has the format "{r;u;s}". Otherwise, set the input stream state as failbit | eofbit.

__throws None.


[endsect]

[section:times_duration_values `duration_values` Specialization for `process_times<>`]

    template <class Rep>
    struct __duration_values<process_times<Rep> >
    {
        static process_times<Rep> zero();
        static process_times<Rep> max();
        static process_times<Rep> min();
    };

The `times` specific functions `zero()`, `max()` and `min()` uses the relative functions on the representation of each component.

[endsect]

[section:clock_string_process_real_cpu_clock `clock_string<process_real_cpu_clock>` Specialization]

    template <class CharT>
    struct clock_string<process_real_cpu_clock, CharT>
    {
        static std::basic_string<CharT> name();
        static std::basic_string<CharT> since();
    };

`clock_string<>::name()` returns "process_real_cpu_clock".

`clock_string<>::since()` returns " since process start-up"

[endsect]

[section:clock_string_process_user_cpu_clock `clock_string<process_user_cpu_clock>` Specialization]

    template <class CharT>
    struct clock_string<process_user_cpu_clock, CharT>
    {
        static std::basic_string<CharT> name();
        static std::basic_string<CharT> since();
    };

`clock_string<>::name()` returns "process_user_cpu_clock".

`clock_string<>::since()` returns " since process start-up"


[endsect]

[section:clock_string_process_system_cpu_clock `clock_string<process_system_cpu_clock>` Specialization]

    template <class CharT>
    struct clock_string<process_system_cpu_clock, CharT>
    {
        static std::basic_string<CharT> name();
        static std::basic_string<CharT> since();
    };

`clock_string<>::name()` returns "process_system_cpu_clock".

`clock_string<>::since()` returns " since process start-up"


[endsect]

[section:clock_string_process_cpu_clock `clock_string<process_cpu_clock>` Specialization]

    template <class CharT>
    struct clock_string<process_cpu_clock, CharT>
    {
        static std::basic_string<CharT> name();
        static std::basic_string<CharT> since();
    };

`clock_string<>::name()` returns "process_cpu_clock".

`clock_string<>::since()` returns " since process start-up"

[endsect]

[section:times_numeric_limits `numeric_limits` Specialization for `process_times<>`]

    namespace std {
        template <>
        class numeric_limits<boost::chrono::process_times<Rep>> {
            typedef boost::chrono::process_times<Rep> Res;

        public:
            static const bool is_specialized = true;
            static Res min();
            static Res max();
            static Res lowest();
            static const int digits;
            static const int digits10;
            static const bool is_signed = false;
            static const bool is_integer = true;
            static const bool is_exact = true;
            static const int radix = 0;
        };
    }

The `process_times<Rep>` specialization functions `min()`, `max()` and `lowest()` uses the relative functions on the representation of each component.

Notes

* `min()` returns the tuple of mins.
* `max()` returns the tuple of maxs.
* `lowest()` returns the tuple of lowests.
* `digits` is the sum of (binary) digits.
* `digits10` is the sum of digits10s.

[endsect]

[endsect]

[/==================================================================]
[section:thread_clock_hpp Header `<boost/chrono/thread_clock.hpp>`]
[/==================================================================]

Knowing the time a thread takes to execute is useful in both test and production environments.

    #define BOOST_CHRONO_HAS_THREAD_CLOCK
    #define BOOST_CHRONO_THREAD_CLOCK_IS_STEADY
    namespace boost { namespace chrono {

        class thread_clock;
        template <class CharT>
        struct clock_string<thread_clock, CharT>;

    } }

[section:BOOST_CHRONO_HAS_THREAD_CLOCK Macro `BOOST_CHRONO_HAS_THREAD_CLOCK`]

This macro is defined if the platform supports thread clocks.

[endsect]

[section:BOOST_CHRONO_THREAD_CLOCK_IS_STEADY Macro `BOOST_CHRONO_THREAD_CLOCK_IS_STEADY`]

This macro is defined if the platform has a thread clock. Its value is true if it is steady and false otherwise.

[endsect]

[section:thread_clock Class `thread_clock`]

__thread_clock satisfy the __clock_req.

__thread_clock class provides access to the real thread wall-clock, i.e. the real CPU-time clock of the calling thread. The thread relative current time can be obtained by calling `thread_clock::now()`.

        class thread_clock {
        public:
            typedef __nanoseconds                          duration;
            typedef duration::rep                        rep;
            typedef duration::period                     period;
            typedef chrono::__time_point<thread_clock>     time_point;
            static constexpr bool is_steady =            BOOST_CHRONO_THREAD_CLOCK_IS_STEADY;

            static time_point now(  ) noexcept;
            static time_point now( system::error_code & ec );
        };


[endsect]


[section:clock_string_thread_clock `clock_string<thread_clock>` Specialization]

    #if defined(BOOST_CHRONO_HAS_THREAD_CLOCK)
    template <class CharT>
    struct clock_string<thread_clock, CharT>
    {
        static std::basic_string<CharT> name();
        static std::basic_string<CharT> since();
    };
    #endif

`clock_string<>::name()` returns "thread_clock".

`clock_string<>::since()` returns " since thread start-up"


[endsect]

[endsect]

[endsect]

[endsect]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[/=================]
[section Appendices]
[/=================]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[/==================================]
[section:history Appendix: History]
[/==================================]

[section [*Version 2.0.8, , 2018 - 1.70] ]

[*Fixes:]

* Remove Stopwatches, which was never be delivered officially.

[endsect] [/section [*Version 2.0.7] ]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section [*Version 2.0.7, August 18, 2016 - 1.62] ]

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/11630 #11630] boost chrono documentation about boost chrono version & io API is wrong.
* [@http://svn.boost.org/trac/boost/ticket/12176 #12176] Chrono without linking to Boost.System 
* [@http://svn.boost.org/trac/boost/ticket/12260 #12260] Bug: time_fmt does not support for wchar_t on windows

[endsect] [/section [*Version 2.0.7] ]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section [*Version 2.0.6, December 18, 2015 - 1.60] ]

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/11330 #11330] boost::chrono::duration default constructor doesn't initialize rep_
* [@http://svn.boost.org/trac/boost/ticket/11618 #11618] Chrono IO V2 doc ios_state.hpp does not exist
* [@http://svn.boost.org/trac/boost/ticket/11631 #11631] boost chrono io v2 does not let you support custom clocks  
  
[endsect] [/section [*Version 2.0.6] ]
[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section [*Version 2.0.5, February 18, 2015 - 1.58] ]

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/10778 #10778]	VC14 CTP4 Atomic don't compile with boost::chrono durations which are not TriviallyCopyable
* [@http://svn.boost.org/trac/boost/ticket/10840 #10840] 	Missing std:: qualifier for puts call in test_7868.cpp
* [@http://svn.boost.org/trac/boost/ticket/10851 #10851] 	Missing std:: qualifier for puts call in time_point_output.cpp
* [@http://svn.boost.org/trac/boost/ticket/10893 #10893] 	Minor doc typo in Boost.Chrono
* [@http://svn.boost.org/trac/boost/ticket/10992 #10992] 	Chrono IO state savers inconsistent with Boost.IO state savers
* [@http://svn.boost.org/trac/boost/ticket/10995 #10995] 	duration_put::put_value truncates fractional part
* [@http://svn.boost.org/trac/boost/ticket/11006 #11006] 	Impossible to instantiate time_fmt_io_saver due to several errors.
* [@http://svn.boost.org/trac/boost/ticket/11012 #11012] 	chrono_io v2 fail to compile with boost::chrono::duration< boost::rational<int> >

[endsect] [/section [*Version 2.0.5] ]
[section [*Version 2.0.4, July 12, 2014 - 1.56] ]

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/6918 #6918] Boost Chrono compilation failure on HP uX due to undefined pthread_getcpuclockid
* [@http://svn.boost.org/trac/boost/ticket/8006 #8006] Boost::Chrono Assertion at startup - steady_clock::now() - Windows

* [@http://svn.boost.org/trac/boost/ticket/9337 #9337] chrono::process_cpu_clock time points wrong by factor 1000 on Linux
* [@http://svn.boost.org/trac/boost/ticket/9342 #9342] Regression on process_cpu_clock::timepoint io on V2
* [@http://svn.boost.org/trac/boost/ticket/9419 #9419] boost::chrono::floor()/round() and negative durations is wrong
* [@http://svn.boost.org/trac/boost/ticket/9698 #9698] boost::chrono::thread_clock not declared in OSX
* [@http://svn.boost.org/trac/boost/ticket/9720 #9720] boost::this_thread::sleep_for() sometimes returns immediately on win32
* [@http://svn.boost.org/trac/boost/ticket/9859 #9859] Remove references to gcc-mingw
* [@http://svn.boost.org/trac/boost/ticket/9918 #9918] chrono compilation error on Solaris, function timegm
* [@http://svn.boost.org/trac/boost/ticket/9811 #9811] boost/boost/chrono/duration.hpp:355:56: error: call to non-constexpr function 'static std::numeric_limits<float>::_Ty std::numeric_limits<float>::max()' /home/<USER>/input_service/inputservices-core-service/other/boost/boost/chrono/duration.hpp: In static member function 'static constexpr double boost::chrono::detail::chrono_numeric_limits<double, true>::lowest()':
* [@http://svn.boost.org/trac/boost/ticket/10069 #10069] Overflow in chrono clocks on 32bit
* [@http://svn.boost.org/trac/boost/ticket/10151 #10151] timegm function not available on QNX

[endsect] [/section [*Version 2.0.4] ]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section [*Version 2.0.3, September 29, 2013 - 1.55] ]

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/7868 #7868] chrono_io parses time incorrectly (1.53 and 1.52)   
* [@http://svn.boost.org/trac/boost/ticket/9028 #9028] Typo in boost/chrono/stopwatches/formatters/base_formatter.hpp  
* [@http://svn.boost.org/trac/boost/ticket/9147 #9147] uninitialized std::tm
* [@http://svn.boost.org/trac/boost/ticket/9274 #9274] lost of precision on system_clock input.
* [@http://svn.boost.org/trac/boost/ticket/9276 #9276] output from a system_clock::time_point get a time_point that is one day later than expected.

[endsect] [/section [*Version 2.0.3] ]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section [*Version 2.0.2, June 15, 2013 - 1.54] ]


[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/8079 #8079] Chrono memory leak
* [@http://svn.boost.org/trac/boost/ticket/8318 #8318] BOOST_FORCEINLINE constructors of time_point and duration
* [@http://svn.boost.org/trac/boost/ticket/8367 #8367] chrono does not compile with clang from XCode 4.5.2 with -std=c++11 -stdlib=libc++ and -arch armv7
* [@http://svn.boost.org/trac/boost/ticket/8370 #8370] typo in chrono reference
* [@http://svn.boost.org/trac/boost/ticket/8435 #8435] Can't compile Chrono on HP-UX due to no CLOCK_REALTIME macro.
* [@http://svn.boost.org/trac/boost/ticket/8690 #8690] duration_units_default - lost return types constness in overridden methods.
* [@http://svn.boost.org/trac/boost/ticket/8691 #8691] iostate is not checked after scan_keyword call.
* [@http://svn.boost.org/trac/boost/ticket/8696 #8696] chrono compilation error on Solaris/gcc.

[endsect] [/section [*Version 2.0.2] ]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section [*Version 2.0.1, January 18, 2013 - 1.53] ]

[*Deprecated:]

* The chrono i/o version included in Boost.Chrono 1.2.x has been completly refactored in version 2.0.0 
* chrono I/O: The manipulators __duration_short, __duration_long are deprecated. You should use the parameterized form __duration_fmt or the renamed manipulators __duration_symbol and __duration_prefix  instead.
* chrono I/O: The __duration_punct<> facet is deprecated. You should use the __get_duration_style free function to get the informations and use the __duration_units facet for localization purposes.

When BOOST_CHRONO_VERSION==2 the preceding deprecated functions are not available.

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/7546 #7546] time_point<system_clock> output version 2 fails to compile  assigned  viboes  Bugs  Boost 1.53.0  --
* [@http://svn.boost.org/trac/boost/ticket/7547 #7547] time_point<system_clock> input version 2 fails to compile   assigned  viboes  Bugs  Boost 1.53.0  --
* [@http://svn.boost.org/trac/boost/ticket/7868 #7868] chrono_io parses time incorrectly (1.53 and 1.52)


[endsect] [/section [*Version 2.0.1] ]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section [*Version 2.0.0, October 23, 2012 - 1.52] ]

[*New Features:]

* Enhance chrono I/O
    * [@http://svn.boost.org/trac/boost/ticket/5980 #5980] Enhance chrono I/O with H. Hinnant proposal [@http://home.roadrunner.com/~hinnant/bloomington/chrono_io.html proposal] which has the advantage to provide I/O for system clocks using the Gregorian Calendar.
    * [@http://svn.boost.org/trac/boost/ticket/5981 #5981] Add i/o state savers for duration and time_point formatting state.
    * [@http://svn.boost.org/trac/boost/ticket/7059 #7059] Add low level i/o facilities.

[*Deprecated:]

* The chrono i/o version included in Boost.Chrono 1.2.x has been completly refactored in version 2.0.0 
* chrono I/O: The manipulators __duration_short, __duration_long are deprecated. You should use the parameterized form __duration_fmt or the renamed manipulators __duration_symbol and __duration_prefix  instead.
* chrono I/O: The __duration_punct<> facet is deprecated. You should use the __get_duration_style free function to get the informations and use the __duration_units facet for localization purposes.

When BOOST_CHRONO_VERSION==2 the preceding deprecated functions are not available.

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/7381 #7381] C++11 compliance: unresolved symbol when assigning a constexpr duration to a non-const local variable.
* [@http://svn.boost.org/trac/boost/ticket/7479 #7479] Compiles fails with compilers supporting constexpr fails if the standard library doesn't provides the constexpr interface
* [@http://svn.boost.org/trac/boost/ticket/7493 #7493] compile fail on intel-linux-12.1.3.0x because of bug on explicit bool conversion
* [@http://svn.boost.org/trac/boost/ticket/7542 #7542] Missing -lpthread in chrono/io tester Sandia-clang-trunk
   
[*Would not fix:]

* [@http://svn.boost.org/trac/boost/ticket/6871 #6871] chrono_io.hpp: operator<<(ostream& os, ...) modifies the state of os.

  * The new io interface provided in version 2 solves this issue. You should move to the new version. 

[*Known bugs not fixed yet:]

* [@http://svn.boost.org/trac/boost/ticket/7525 #7525] Wrong clock_string<system_clock>::since() on Windows 

[/warning

The time_point<system_clock,D> formatter/parser specializations don't work yet. It has been disable defining `BOOST_CHRONO_DONT_PROVIDE_DATE_IO_FOR_SYSTEM_CLOCK_TIME_POINT` until a fix for the following ticket is found:

- [@http://svn.boost.org/trac/boost/ticket/7547 #7547] time_point<system_clock> input version 2 fails to compile 

- [@http://svn.boost.org/trac/boost/ticket/7546 #7546] time_point<system_clock> output version 2 fails to compile 

In this case the io operators behave like any time_point as if the specialization was removed.

In order to enable this features on compilers working with, you will need to define `BOOST_CHRONO_PROVIDES_DATE_IO_FOR_SYSTEM_CLOCK_TIME_POINT`.

]

[endsect] [/section [*Version 2.0.0] ]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section [*Version 1.2.3, August 1, 2012 - 1.51] ]

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/6918 #6918] Boost Chrono compilation failure on HP uX due to undefined pthread_getcpuclockid.  
* [@http://svn.boost.org/trac/boost/ticket/6241 #6241] boost::chrono compilation problems without std::wstring support.
* [@http://svn.boost.org/trac/boost/ticket/6987 #6987] Documentation & C++11.
* [@http://svn.boost.org/trac/boost/ticket/7041 #7041] time_point.hpp depends on Boost.System.
* [@http://svn.boost.org/trac/boost/ticket/7042 #7042] Avoiding time_point and duration dependency on time.h and CLOCK_REALTIME.
* [@http://svn.boost.org/trac/boost/ticket/7058 #7058] Make it work when BOOST_NO_EXCEPTIONS is defined.
* [@http://svn.boost.org/trac/boost/ticket/7069 #7069] Misspellings in clock_string<thread_clock>.
* [@http://svn.boost.org/trac/boost/ticket/7081 #7081] WinError.h capitalization in boost/detail/win/basic_types.hpp.

[endsect] [/section [*Version 1.2.3] ]

[//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////]
[section [*Version 1.2.2, April 1, 2012 - 1.50] ]

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/6361 #6361] integer overflow in boost::chrono::process_real_cpu_clock::now() under Windows 32bits.
* [@http://svn.boost.org/trac/boost/ticket/6628 #6628] compiler warning in process_cpu_clocks.hpp.
* [@http://svn.boost.org/trac/boost/ticket/6666 #6666] thread_clock.hpp needs pthread.h.

[endsect] [/section [*Version 1.2.2] ]

[section [*Version 1.2.1, February 1, 2012 - 1.49] ]

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/6092 #6092] Input from non integral durations makes the compiler fail.
* [@http://svn.boost.org/trac/boost/ticket/6093 #6093] [1/3]second fails as valid duration input.
* [@http://svn.boost.org/trac/boost/ticket/6113 #6113] duplicate symbol when BOOST_CHRONO_HEADER_ONLY is defined.
* [@http://svn.boost.org/trac/boost/ticket/6243 #6243] Sandia-pgi-11.9: more than one instance of overloaded function "min" matches.
* [@http://svn.boost.org/trac/boost/ticket/6257 #6257] process_cpu_clock::now() on linux gives time_points 1/1000 times.

[endsect] [/section [*Version 1.2.1] ]


[section [*Version 1.2.0, October 17, 2011] ]
[*New Features:]

* [@http://svn.boost.org/trac/boost/ticket/5979 #5979] Added chrono rounding utilities as defined By Howard Hinnant [@http://home.roadrunner.com/~hinnant/duration_io/chrono_util.html here].
* [@http://svn.boost.org/trac/boost/ticket/5978 #5978] Added BOOST_CHRONO_HAS_PROCESS_CLOCKS to know if process clocks are available.
* [@http://svn.boost.org/trac/boost/ticket/5998 #5998] Make possible to don't provide hybrid error handling.
* [@http://svn.boost.org/trac/boost/ticket/5906 #5906] Take in account the constexpr as defined in the standard.
* [@http://svn.boost.org/trac/boost/ticket/5907 #5907] Take in account noexcept for compilers supporting it.

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/2114 #2114] Enable visibility support (Boost.Chorno part)
* [@http://svn.boost.org/trac/boost/ticket/5669 #5669] Intel compiler failure to compile duration.hpp
* [@http://svn.boost.org/trac/boost/ticket/5909 #5909] process_cpu_clock::now() on MAC gives time_points 1/1000 times.
* [@http://svn.boost.org/trac/boost/ticket/5946 #5946] Process real cpu clock returns the system steady clock (windows).
* [@http://svn.boost.org/trac/boost/ticket/5974 #5974] Process real cpu clock should use clock() instead of times() in MAC which is twice faster and have better resolution.

[*Cleanup:]

* [@http://svn.boost.org/trac/boost/ticket/5975 #5975] Reduce the combinations of header-only, shared, static link to reduce test time by 50%.
* [@http://svn.boost.org/trac/boost/ticket/5976 #5976] chrono_accuracy_test is not deterministic and should be removed from the regression tests
* [@http://svn.boost.org/trac/boost/ticket/5977 #5977] Remove old files from Beman's version. Some old files included in the Beman's version and not documented in the reviewed version that have been definitely removed from the repository as
  * boost/chrono/timer.hpp,
  * boost/chrono/process_times.hpp
  * boost/chrono/detail/process_clock.hpp,
  * boost/chrono/detail/mac/process_clock.hpp,
  * boost/chrono/detail/posix/process_clock.hpp,
  * boost/chrono/detail/win/process_clock.hpp,
  * boost/chrono/detail/run_timer.hpp,
  * boost/chrono/detail/run_timer_static.hpp,



[endsect] [/section [*Version 1.2.0] ]


[section [*Version 1.1.0, Mars 17, 2011] ]

[*New Features:]

* [@http://svn.boost.org/trac/boost/ticket/???? #????] Added time_point unary operators +,-,++,-- and binary operators +=,-= with Rep al RHS.
* [@http://svn.boost.org/trac/boost/ticket/5323 #5323] Add Associated type difference_type for chrono::time_point.

[*Fixes:]

* [@http://svn.boost.org/trac/boost/ticket/5322 #5322] Explicit default constructed chrono::durations are uninitialized


[endsect] [/section [*Version 1.1.0] ]

[section [*Version 1.0.0, January 6, 2011] ]

* Moved chrono to trunk taking in account the review remarks.
* Documentation revision.

[*Features:]

* Boost_Chrono is now a configurable header-only library version (that also allows the user to choose if the `windows.h` file is included or not).
* Added clock_string<> traits.
* Define chrono-io for all the clocks.
* Add input of process_times representation.

[*Implementation:]

* Use of detail/win files to avoid the use of windows.h file.
* Completed the error_code handling.
* Works now with BOOST_SYSTEM_NO_DEPRECATED.

[*Fixes:]

* Fix some warnings.
* Fix original errors on Mac
* Don't fix the link with boost_system to static.

[*Test:]

* Added test on process and thread clocks.
* Moved to lightweight_test.hpp.
* Able to test multiple configurations.

[*Doc:]

* Removed some not useful parts as the test and the tickets.

[endsect] [/section [*Version 1.0.0] ]

[/
[section [*Version 0.6.0, September 22, 2010] ]

[*Features:]

* Added experimental chrono_io.

[*Fixes:]

* Fix duration values min implementation.
* Fix some warnings

[*Test:]

* Adapted test from libc++/chrono

[endsect]

[section [*Version 0.5.0, September 10, 2010] ]

[*Features:]

* Stopwatches, Ratio and CommonType have been moved to separated libraries: Boost.Stopwatches, Boost.Ratio and Boost.TypeTraits.

[endsect]

[section [*Version 0.4.7, September 1, 2010] ]

[*New Features:]

* Added __lightweight_stopwatch__.

[endsect]

[section [*Version 0.4.6, August 28, 2010] ]

[*New Features:]

* Implementation of __common_type without using Boost.TypeOf.
* Added __stopwatch_accumulator_time_formatter__ class.

[*Old Features:]

* Type reporter removed from Stopwatches as well as the get_reporter metafunction.

[*Bug Fixes]

* __process_cpu_clock is now a valid model of __Clock that can be used with __stopclocks_accumulator__.
* eliminate or suppress a lot of warnings appearing with warnings=all -Wextra
* improve the error code handling

[endsect]

[section [*Version 0.4.5, July 6, 2010] ['Documentation update]]

[*Documentation]

* Overview rewriting
* Added missing __thread_clock reference.
* How to implement a __thread_clock tutorial removed.
* References section renamed to External Resources.
* Added links to source examples.
* Added links between Models and Concepts.
* Added macros descriptions.
* Cleanup.

[*Bug Fixes]

* Valgrind fixes: "Conditional jump or move depends on uninitialised value(s)"
* Take care of Boost.System break on version 1.44
* gcc.4.4 "warning: suggest parentheses around '&&' within '||' " removal.

[endsect]

[section [*Version 0.4.4, February 22, 2010] [' Warning fixes]]

[*Bug Fixes]

* `scoped_suspend` warning removal
* `error_code` management completed

[endsect]

[section [*Version 0.4.3, June 18, 2010] ['Missing file fixe]]

[*Bug Fixes]

* boost/thread/detail/cv_status.hpp file was not committed.

[endsect]

[section [*Version 0.4.2, June 18, 2010] ['Packaging fixe]]

* Boost.Conversion library, used by Boost.Thread porting to Boost.Chrono was not packaged.

[endsect]

[section [*Version 0.4.1, June 17, 2010] ['Added thread clock implementation on Windows]]

[*New Features:]

* Added __thread_clock implementation on Windows.
* Added *Boost.Thread* using *Boost.Chrono*.

[endsect]

[section [*Version 0.4, February 28, 2010] ['New thread clock and Suspendible clock ]]

[*New Features:]

* __SuspendibleClock__ concept + template class _suspendible_clock__.
* Added `scope_suspend` which do `suspend`/`resume` if the __Clock is a model of __SuspendibleClock__ concept, and nothing otherwise.
* __thread_clock support on platforms providing it natively.
* Added support for wide character for __stopwatch_reporter, `stopclock`, and `stopclock_accumulator`.
* `digital_time` renamed `t24_hours`.

[*Perf]

Added performances measures.

[*Bug Fixes]

* Bug on timeval_demo.

    time_point t(duration(xtime(0)));  // this was taken as a function declaration
    gettimeofday((timeval*)&t, 0);
    return t;
by

    timeval tv;
    gettimeofday(&tv, 0);
    xtime xt( tv.tv_sec, tv.tv_usec);
    return time_point(duration(xt));

* Bug on run_timer_test (add a global variable to avoid optimization that removes completely the code to be  measured

[endsect]

[section [*Version 0.3.2, January 25, 2010] ['New frequency, lifetime and percentage stopwatch_accumulator features]]
[*Features:]

* Added overloading for `operator/(Integer/Duration)`
* Added frequency, lifetime and percentage to the default `stopwatch_accumulator_formatter`.

[*Bug Fixes]
* Specific formatters didn't work completely.
* Replace `duration(0)` by `duration::zero()` on template classes.
* `suspend` doesn't works: `partial_` not initialized neither taken in account by the elapsed function.

[endsect]

[section [*Version 0.3.1, January 20, 2010] ['New support for wide characters]]
[*Features:]

* Support for wide characters on formatters and stopclocks
* added `chrono.hpp` and `stopwatches.hpp` at the boost level


[endsect]

[section [*Version 0.3.0, January 17, 2010] ['New stopwatch/stopclock feature + Bug fixes]]
[*Features:]

* Added independent process cpu clocks for real, user, system process CPU time
* Added global process cpu clock for real, user, system process CPU time
* Added `digital_time` (looking for a better name)
* Added new __Stopwatch__ concept measuring elapsed time between different points in time associated to the operations `start`, `stop`, `suspend` and `resume`.
* Added __stopwatch__ is a model __Stopwatch__ measuring the elapsed time between the `start` and the `stop` operations.
* Added __laps_stopwatch is a model __Stopwatch__ allowing to accumulate several time samples and gives the average, ...
* Added scoped helper classes allowing to pairwise `start`/`stop` operations, `suspend`/`resume` and `resume`/`suspend` a __Stopwatch__.
* Added new stopwatch __Formatter__ concept
    * Added stopwatch formatter "%ds\\n"
    * Added stopwatch accumulator formatter "%c times, sum=%ss, min=%ms, max=%Ms, mean=%as\\n"
    * Added time formatter "real %rs, cpu %cs (%p%), user %us, system %ss\\n"
    * Added digital_time formatter "%d days(s) %h:%m:%s.%n\\n"
* __stopwatch_reporter is a convenient generic class reporting elapsed time for the Stopwatch concept.
* Added `stopclock<__Clock>` shortcut `stopwatch_reporter<stopwatch<__Clock>>`
* Added __scoped_stopclock__ which trace at the constructor and destructor.
* Added `typeof` registration for classes __duration and __time_point

* The old classes `process_times`, `process_clock`, `process_timer`, `timer` and `run_timer` are deprecated as the preceding additions are more generic. However for backward compatibility they preserved until inclusion of the library in Boost. Next follows the equivalences:
    * `timer<>` ~ `stopwatch<>`
    * `process_timer` ~ `stopwatch<process_cpu_clock>`
    * `run_timer` ~ `stopclock<>`

[*Bug Fixes]

* Try to correct warning "C4251: 'boost::chrono::run_timer::m_format' : class 'std::basic_string<_Elem,_Traits,_Ax>' needs to have dll-interface to be used by clients of class 'boost::chrono::run_timer'", by don't including inline functions using the std::string `m_format`.

[endsect]

[section [*Version 0.2.1, December 13, 2009] ['Bug fixes]]

[*Bug Fixes]

* Replace `INTMAX_C` by `BOOST_INTMAX_C` until `boost/cstdint.hpp` ensures `INTMAX_C` is always defined.
* Define __BOOST_CHRONO_HAS_CLOCK_STEADY__ when `BOOST_CHRONO_WINDOWS_API`
* Commenting invalid operator declarations
* Take care of Boost `min`/`max` recommendations
* Complete qualification when defining nested typedef duration on clocks to avoid the following compile error:

    ./boost/chrono/chrono.hpp:945: error: declaration of 'typedef class boost::chrono::duration<long long int, boost::__ratio<1ll, 10000000ll> > boost::chrono::system_clock::duration'
    ./boost/chrono/chrono.hpp:458: error: changes meaning of 'duration' from 'class boost::chrono::duration<long long int, boost::__ratio<1ll, 10000000ll> >'

* cleanup of simulated...
* warning removal on `posix/process_clock.cpp`.
* disable VC++ foolishness.
* Update Jamfiles to manage with dll.
* removal of conversion warning in test_duration.
* manage with MSVC reporting a warning instead of an error when there is an integral constant overflow.
* Use `STATIC_ASSERT` specific macro to solve the compile failing issue.
* Qualify with `boost::detail` `boost::chrono::detail` to avoid ambiguities with MSVC.


[*Documentation:]

More updated documentation.

[endsect]

[section [*Version 0.2.0, December 8, 2009] ['+ Features + Bug fixes + Updated documentation]]

[*Features:]

* Added __ratio construction and assignment from an equivalent __ratio ([@http://home.roadrunner.com/~hinnant/issue_review/lwg-active.html#1281 [*LWG 1281. CopyConstruction and Assignment between ratios having the same normalized form]])
* Added nested __ratio typedef type ([@http://home.roadrunner.com/~hinnant/issue_review/lwg-active.html#1281 [*LWG 1281. CopyConstruction and Assignment between ratios having the same normalized form]])
* Added __BOOST_CHRONO_HAS_CLOCK_STEADY__ macro to state if __steady_clock is provided on this platform.
* Added __duration `operator%` ([@http://home.roadrunner.com/~hinnant/issue_review/lwg-defects.html#934 [*LGW 934. duration is missing operator%]])
* Added constexpr when constexpr should be used.
* Complete __duration `operator*` and `operator/`.


[*Implementation:]

* Use `INTMAC_C` to name `intmax_t` constants instead of `LL`.
* Separate `chrono.cpp` on # files `win/chrono.cpp`, `mac/chrono.cpp` and `posix/chrono.cpp` to make easier the maintenance on different platforms.
* Separate `process_clock.cpp` on # files `win/process_clock.cpp`, `mac/process_clock.cpp` and `posix/process_clock.cpp` to make easier the maintenance on different platforms.
* Added the `error_code` prototype for `steady_clock::now` for `mac/chrono.cpp`.
* Fully implement `mac/chrono.cpp` with error handling.
* Take care on POSIX systems when `CLOCK_STEADY` is not defined.

[*Documentation:]

* The documentation is written now using quick-book using as base [@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2661.htm [*N2661 - A Foundation to Sleep On]] .

[*Bug Fixes]

* `operator/` was ambiguous: Disambiguate duration `operator/`.
* `CLOCK_STEADY` is not defined with cygwin/gcc 3.4: Disable code when __BOOST_CHRONO_HAS_CLOCK_STEADY__ is not defined.
* result of metafunctions `ratio_multiply` and `ratio_divide` were not normalized ratios: Use of the nested __ratio typedef type on __ratio arithmetic operations.
* Copy constructor from similar __duration masked the defaulted operations: Added duration defaulted implementations


[endsect]
[section [*Version 0.1.0, April 29, 2009] ['Beman's boostified version Chrono]]

[*Features:]

*  The C++11 Standard Library's __common_type.
*  The C++11 Standard Library's compile-time rational arithmetic.
*  The C++11 Standard Library's time utilities, including:
    * Class template __duration
    * Class template __time_point
    * Clocks:
        * __system_clock
        * __steady_clock
        * `high_resolution_clock`

* Class template timer, with typedefs:
    * `system_timer`
    * `steady_timer`
    * `high_resolution_timer`

* Process clocks and timers:
    * `process_clock`, capturing real, user-CPU, and system-CPU times.
    * `process_timer`, capturing elapsed real, user-CPU, and system-CPU times.
    * `run_timer`, convenient reporting of process_timer results.


[endsect]
]
[endsect]

[/======================================]
[section:rationale Appendix: Rationale]

See [@http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2661.htm [*N2661 - A Foundation to Sleep On]] which is very informative and provides motivation for key design decisions. This section contains some extracts from this document.

[heading Why duration needs operator%]

This operator is convenient for computing where in a time frame a given duration lies. A motivating example is converting a duration into a "broken-down" time duration such as hours::minutes::seconds:

    class ClockTime
    {
        typedef boost::chrono::hours hours;
        typedef boost::chrono::minutes minutes;
        typedef boost::chrono::seconds seconds;
    public:
        hours hours_;
        minutes minutes_;
        seconds seconds_;

        template <class Rep, class Period>
          explicit ClockTime(const boost::chrono::duration<Rep, Period>& d)
            : hours_  (boost::chrono::duration_cast<hours>  (d)),
              minutes_(boost::chrono::duration_cast<minutes>(d % hours(1))),
              seconds_(boost::chrono::duration_cast<seconds>(d % minutes(1)))
              {}
    };

[endsect]


[/======================================================]
[section:implementation Appendix: Implementation Notes]

[heading Which APIs have been chosen to implement each clock on each platform?]

The following table presents a resume of which API is used for each clock on each platform
[table Clock API correspondence
    [[Clock]                                [Windows Platform]          [Posix Platform]                 [Mac Platform]  ]
    [[__system_clock]                     [GetSystemTimeAsFileTime]   [clock_gettime( CLOCK_REALTIME)] [gettimeofday]          ]
    [[__steady_clock]                  [QueryPerformanceCounter and QueryPerformanceFrequency]
                                        [clock_gettime( CLOCK_STEADY)] [mach_timebase_info,mach_absolute_time]          ]
    [[__process_real_cpu_clock]           [GetProcessTimes]           [times]                         [times]  ]
    [[__process_system_cpu_clock]         [GetProcessTimes]           [times]                         [times]    ]
    [[__process_user_cpu_clock]           [GetProcessTimes]           [times]                         [times]  ]
    [[__process_cpu_clock]                [GetProcessTimes]           [times]                         [times]   ]
    [[__thread_clock]                     [GetThreadTimes]            [clock_gettime(pthread_getcpuclockid)]         [clock_gettime(pthread_getcpuclockid)]       ]
]

[endsect]

[/======================================================]
[section:faq Appendix: FAQ]

[heading Why does process_cpu_clock sometimes give more cpu seconds than real seconds?]

Ask your operating system supplier. The results have been inspected with a debugger, and both for Windows and Linux, that's what the OS appears to be reporting at times.


[heading Are integer overflows in the duration arithmetic detected and reported?]

[*Boost.Ratio] avoids all kind of overflow that could result of arithmetic operation and that can be simplified. The typedefs durations don't detect overflow. You will need a duration representation that handles overflow.

[heading Which clocks should be used to benchmarking?]

Each clock has his own features. It depends on what do you need to benchmark. Most of the time, you could be interested in using a thread clock, but if you need to measure code subject to synchronization a process clock would be better. If you have a multi-process application, a system-wide clock could be needed.

[heading Which clocks should be used for watching?]

For trace purposes, it is probably best to use a system-wide clock.

[endsect] [/section:faq Appendix: FAQ]

[/====================================================]
[section:acknowledgements Appendix: Acknowledgements]

The library's code was derived from Howard Hinnant's time2_demo prototype. Many thanks to Howard for making his code available under the Boost license. The original code was modified by Beman Dawes to conform to Boost conventions.

time2_demo contained this comment:

Much thanks to Andrei Alexandrescu, Walter Brown, Peter Dimov, Jeff Garland, Terry Golubiewski, Daniel Krugler, Anthony Williams.

The file <boost/chrono_io.hpp> has been adapted from the experimental header `<chrono_io>` from Howard Hinnant. Thanks for all Howard.

Howard Hinnant, who is the real author of the library, has provided valuable feedback and suggestions during the development of the library. In particular, The chrono_io_io.hpp source has been adapted from the experimental header `<chrono_io>` from Howard Hinnant.

The acceptance review of Boost.Ratio took place between November 5th and 15th 2010. Many thanks to Anthony Williams, the review manager, and to all the reviewers: David Deakins, John Bytheway, Roland Bock and Paul A. Bristow.

Thanks to Ronald Bock, Andrew Chinoff, Paul A. Bristow and John Bytheway for his help polishing the documentation.

Thanks to Tom Tan for reporting some compiler issues with MSVC V10 beta and MinGW-gcc-4.4.0 and for the many pushing for an homogeneous `process_cpu_clock` clock.

Thanks to Ronald Bock for reporting Valgind issues and for the many suggestions he made concerning the documentation.

[endsect]

[/=====================================]
[section:todo  Appendix: Future plans]
[/=====================================]

[heading For later releases]

* Add User defined literals for some durations.
* Include chrono::date as defined by Howard Hinnant [@http://home.roadrunner.com/~hinnant/bloomington/date.html here].

[endsect]

[endsect]
