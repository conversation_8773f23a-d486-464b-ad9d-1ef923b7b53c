<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>datetime::time_point</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.MySQL">
<link rel="up" href="../boost__mysql__datetime.html" title="datetime">
<link rel="prev" href="../boost__mysql__datetime.html" title="datetime">
<link rel="next" href="datetime.html" title="datetime::datetime">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../boost__mysql__datetime.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__mysql__datetime.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="datetime.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="mysql.ref.boost__mysql__datetime.time_point"></a><a class="link" href="time_point.html" title="datetime::time_point">datetime::time_point</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm20029"></a>
        </p>
<p>
          A <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">time_point</span></code> that can represent any valid
          datetime.
        </p>
<h6>
<a name="mysql.ref.boost__mysql__datetime.time_point.h0"></a>
          <span class="phrase"><a name="mysql.ref.boost__mysql__datetime.time_point.synopsis"></a></span><a class="link" href="time_point.html#mysql.ref.boost__mysql__datetime.time_point.synopsis">Synopsis</a>
        </h6>
<pre class="programlisting"><span class="keyword">using</span> <span class="identifier">time_point</span> <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">time_point</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">system_clock</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">int64_t</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">micro</span> <span class="special">&gt;</span> <span class="special">&gt;;</span>
</pre>
<h6>
<a name="mysql.ref.boost__mysql__datetime.time_point.h1"></a>
          <span class="phrase"><a name="mysql.ref.boost__mysql__datetime.time_point.description"></a></span><a class="link" href="time_point.html#mysql.ref.boost__mysql__datetime.time_point.description">Description</a>
        </h6>
<p>
          Represents microseconds since the UNIX epoch, with the same precision for
          all architectures.
        </p>
</div>
<div class="copyright-footer">Copyright © 2019-2023 Ruben Perez<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../boost__mysql__datetime.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__mysql__datetime.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="datetime.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
