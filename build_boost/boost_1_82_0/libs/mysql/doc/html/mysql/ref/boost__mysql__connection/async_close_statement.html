<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>connection::async_close_statement</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.MySQL">
<link rel="up" href="../boost__mysql__connection.html" title="connection">
<link rel="prev" href="close_statement/overload2.html" title="connection::close_statement (2 of 2 overloads)">
<link rel="next" href="async_close_statement/overload1.html" title="connection::async_close_statement (1 of 2 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="close_statement/overload2.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__mysql__connection.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="async_close_statement/overload1.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="mysql.ref.boost__mysql__connection.async_close_statement"></a><a class="link" href="async_close_statement.html" title="connection::async_close_statement">connection::async_close_statement</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm17914"></a>
        </p>
<p>
          Closes a statement, deallocating it from the server.
        </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span> <a href="../../../../../../../doc/html/boost_asio/reference/asynchronous_operations.html#boost_asio.reference.asynchronous_operations.completion_tokens_and_handlers" target="_top"><span class="emphasis"><em>CompletionToken</em></span></a><span class="special">&gt;</span>
<span class="keyword">auto</span>
<a class="link" href="async_close_statement/overload1.html" title="connection::async_close_statement (1 of 2 overloads)">async_close_statement</a><span class="special">(</span>
    <span class="keyword">const</span> <span class="identifier">statement</span><span class="special">&amp;</span> <span class="identifier">stmt</span><span class="special">,</span>
    <span class="identifier">CompletionToken</span><span class="special">&amp;&amp;</span> <span class="identifier">token</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="async_close_statement/overload1.html" title="connection::async_close_statement (1 of 2 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>

<span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span> <a href="../../../../../../../doc/html/boost_asio/reference/asynchronous_operations.html#boost_asio.reference.asynchronous_operations.completion_tokens_and_handlers" target="_top"><span class="emphasis"><em>CompletionToken</em></span></a><span class="special">&gt;</span>
<span class="keyword">auto</span>
<a class="link" href="async_close_statement/overload2.html" title="connection::async_close_statement (2 of 2 overloads)">async_close_statement</a><span class="special">(</span>
    <span class="keyword">const</span> <span class="identifier">statement</span><span class="special">&amp;</span> <span class="identifier">stmt</span><span class="special">,</span>
    <span class="identifier">diagnostics</span><span class="special">&amp;</span> <span class="identifier">diag</span><span class="special">,</span>
    <span class="identifier">CompletionToken</span><span class="special">&amp;&amp;</span> <span class="identifier">token</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="async_close_statement/overload2.html" title="connection::async_close_statement (2 of 2 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2019-2023 Ruben Perez<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="close_statement/overload2.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__mysql__connection.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="async_close_statement/overload1.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
