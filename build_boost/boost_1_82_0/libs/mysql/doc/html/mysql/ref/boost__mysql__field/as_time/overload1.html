<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>field::as_time (1 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Boost.MySQL">
<link rel="up" href="../as_time.html" title="field::as_time">
<link rel="prev" href="../as_time.html" title="field::as_time">
<link rel="next" href="overload2.html" title="field::as_time (2 of 2 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../as_time.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../as_time.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="mysql.ref.boost__mysql__field.as_time.overload1"></a><a class="link" href="overload1.html" title="field::as_time (1 of 2 overloads)">field::as_time
          (1 of 2 overloads)</a>
</h6></div></div></div>
<p>
            Retrieves a reference to the underlying <code class="computeroutput"><span class="identifier">time</span></code>
            value or throws an exception.
          </p>
<h6>
<a name="mysql.ref.boost__mysql__field.as_time.overload1.h0"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__field.as_time.overload1.synopsis"></a></span><a class="link" href="overload1.html#mysql.ref.boost__mysql__field.as_time.overload1.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="keyword">const</span> <span class="identifier">time</span><span class="special">&amp;</span>
<span class="identifier">as_time</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<h6>
<a name="mysql.ref.boost__mysql__field.as_time.overload1.h1"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__field.as_time.overload1.exception_safety"></a></span><a class="link" href="overload1.html#mysql.ref.boost__mysql__field.as_time.overload1.exception_safety">Exception
            safety</a>
          </h6>
<p>
            Strong guarantee. Throws on type mismatch.
          </p>
<h6>
<a name="mysql.ref.boost__mysql__field.as_time.overload1.h2"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__field.as_time.overload1.exceptions"></a></span><a class="link" href="overload1.html#mysql.ref.boost__mysql__field.as_time.overload1.exceptions">Exceptions</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Type
                    </p>
                  </th>
<th>
                    <p>
                      Thrown On
                    </p>
                  </th>
</tr></thead>
<tbody><tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">bad_field_access</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      If <code class="computeroutput"><span class="special">!</span><span class="keyword">this</span><span class="special">-&gt;</span><span class="identifier">is_time</span><span class="special">()</span></code>
                    </p>
                  </td>
</tr></tbody>
</table></div>
<h6>
<a name="mysql.ref.boost__mysql__field.as_time.overload1.h3"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__field.as_time.overload1.object_lifetimes"></a></span><a class="link" href="overload1.html#mysql.ref.boost__mysql__field.as_time.overload1.object_lifetimes">Object
            lifetimes</a>
          </h6>
<p>
            The returned reference is valid as long as <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code> is alive and no function that invalidates
            references is called on <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>.
          </p>
</div>
<div class="copyright-footer">Copyright © 2019-2023 Ruben Perez<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../as_time.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../as_time.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
