<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>common_server_errc</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.MySQL">
<link rel="up" href="../../pt01.html" title="Part Two: Reference. ">
<link rel="prev" href="boost__mysql__field_kind.html" title="field_kind">
<link rel="next" href="boost__mysql__column_type.html" title="column_type">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost__mysql__field_kind.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../pt01.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost__mysql__column_type.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="mysql.ref.boost__mysql__common_server_errc"></a><a class="link" href="boost__mysql__common_server_errc.html" title="common_server_errc">common_server_errc</a>
</h4></div></div></div>
<p>
        <a class="indexterm" name="idm42617"></a>
      </p>
<p>
        Server-defined error codes, shared between MySQL and MariaDB.
      </p>
<h5>
<a name="mysql.ref.boost__mysql__common_server_errc.h0"></a>
        <span class="phrase"><a name="mysql.ref.boost__mysql__common_server_errc.synopsis"></a></span><a class="link" href="boost__mysql__common_server_errc.html#mysql.ref.boost__mysql__common_server_errc.synopsis">Synopsis</a>
      </h5>
<p>
        Defined in header <code class="literal">&lt;<a href="https://github.com/boostorg/mysql/blob/master/include/boost/mysql/common_server_errc.hpp" target="_top">boost/mysql/common_server_errc.hpp</a>&gt;</code>
      </p>
<pre class="programlisting"><span class="keyword">enum</span> <span class="identifier">common_server_errc</span>
</pre>
<h5>
<a name="mysql.ref.boost__mysql__common_server_errc.h1"></a>
        <span class="phrase"><a name="mysql.ref.boost__mysql__common_server_errc.values"></a></span><a class="link" href="boost__mysql__common_server_errc.html#mysql.ref.boost__mysql__common_server_errc.values">Values</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_hashchk</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1000, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_hashchk" target="_top">ER_HASHCHK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_nisamchk</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1001, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_nisamchk" target="_top">ER_NISAMCHK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1002, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no" target="_top">ER_NO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_yes</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1003, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_yes" target="_top">ER_YES</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_create_file</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1004, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_create_file" target="_top">ER_CANT_CREATE_FILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_create_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1005, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_create_table" target="_top">ER_CANT_CREATE_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_create_db</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1006, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_create_db" target="_top">ER_CANT_CREATE_DB</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_db_create_exists</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1007, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_db_create_exists" target="_top">ER_DB_CREATE_EXISTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_db_drop_exists</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1008, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_db_drop_exists" target="_top">ER_DB_DROP_EXISTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_db_drop_delete</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1009, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_db_drop_delete" target="_top">ER_DB_DROP_DELETE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_db_drop_rmdir</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1010, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_db_drop_rmdir" target="_top">ER_DB_DROP_RMDIR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_delete_file</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1011, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_delete_file" target="_top">ER_CANT_DELETE_FILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_find_system_rec</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1012, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_find_system_rec" target="_top">ER_CANT_FIND_SYSTEM_REC</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_get_stat</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1013, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_get_stat" target="_top">ER_CANT_GET_STAT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_get_wd</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1014, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_get_wd" target="_top">ER_CANT_GET_WD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_lock</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1015, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_lock" target="_top">ER_CANT_LOCK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_open_file</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1016, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_open_file" target="_top">ER_CANT_OPEN_FILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_file_not_found</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1017, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_file_not_found" target="_top">ER_FILE_NOT_FOUND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_read_dir</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1018, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_read_dir" target="_top">ER_CANT_READ_DIR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_set_wd</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1019, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_set_wd" target="_top">ER_CANT_SET_WD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_checkread</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1020, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_checkread" target="_top">ER_CHECKREAD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_disk_full</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1021, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_disk_full" target="_top">ER_DISK_FULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1022, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_key" target="_top">ER_DUP_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_on_close</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1023, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_on_close" target="_top">ER_ERROR_ON_CLOSE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_on_read</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1024, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_on_read" target="_top">ER_ERROR_ON_READ</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_on_rename</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1025, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_on_rename" target="_top">ER_ERROR_ON_RENAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_on_write</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1026, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_on_write" target="_top">ER_ERROR_ON_WRITE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_file_used</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1027, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_file_used" target="_top">ER_FILE_USED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_filsort_abort</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1028, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_filsort_abort" target="_top">ER_FILSORT_ABORT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_form_not_found</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1029, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_form_not_found" target="_top">ER_FORM_NOT_FOUND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_get_errno</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1030, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_get_errno" target="_top">ER_GET_ERRNO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_illegal_ha</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1031, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_illegal_ha" target="_top">ER_ILLEGAL_HA</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_key_not_found</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1032, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_key_not_found" target="_top">ER_KEY_NOT_FOUND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_not_form_file</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1033, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_not_form_file" target="_top">ER_NOT_FORM_FILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_not_keyfile</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1034, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_not_keyfile" target="_top">ER_NOT_KEYFILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_old_keyfile</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1035, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_old_keyfile" target="_top">ER_OLD_KEYFILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_open_as_readonly</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1036, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_open_as_readonly" target="_top">ER_OPEN_AS_READONLY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_outofmemory</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1037, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_outofmemory" target="_top">ER_OUTOFMEMORY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_out_of_sortmemory</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1038, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_out_of_sortmemory" target="_top">ER_OUT_OF_SORTMEMORY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unexpected_eof</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1039, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unexpected_eof" target="_top">ER_UNEXPECTED_EOF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_con_count_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1040, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_con_count_error" target="_top">ER_CON_COUNT_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_out_of_resources</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1041, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_out_of_resources" target="_top">ER_OUT_OF_RESOURCES</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_host_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1042, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_host_error" target="_top">ER_BAD_HOST_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_handshake_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1043, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_handshake_error" target="_top">ER_HANDSHAKE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dbaccess_denied_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1044, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dbaccess_denied_error" target="_top">ER_DBACCESS_DENIED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_access_denied_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1045, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_access_denied_error" target="_top">ER_ACCESS_DENIED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_db_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1046, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_db_error" target="_top">ER_NO_DB_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_com_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1047, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_com_error" target="_top">ER_UNKNOWN_COM_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_null_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1048, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_null_error" target="_top">ER_BAD_NULL_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_db_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1049, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_db_error" target="_top">ER_BAD_DB_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_exists_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1050, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_exists_error" target="_top">ER_TABLE_EXISTS_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_table_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1051, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_table_error" target="_top">ER_BAD_TABLE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_non_uniq_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1052, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_non_uniq_error" target="_top">ER_NON_UNIQ_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_server_shutdown</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1053, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_server_shutdown" target="_top">ER_SERVER_SHUTDOWN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_field_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1054, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_field_error" target="_top">ER_BAD_FIELD_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_field_with_group</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1055, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_field_with_group" target="_top">ER_WRONG_FIELD_WITH_GROUP</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_group_field</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1056, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_group_field" target="_top">ER_WRONG_GROUP_FIELD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_sum_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1057, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_sum_select" target="_top">ER_WRONG_SUM_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_value_count</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1058, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_value_count" target="_top">ER_WRONG_VALUE_COUNT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_long_ident</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1059, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_long_ident" target="_top">ER_TOO_LONG_IDENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_fieldname</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1060, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_fieldname" target="_top">ER_DUP_FIELDNAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_keyname</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1061, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_keyname" target="_top">ER_DUP_KEYNAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_entry</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1062, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_entry" target="_top">ER_DUP_ENTRY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_field_spec</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1063, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_field_spec" target="_top">ER_WRONG_FIELD_SPEC</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_parse_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1064, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_parse_error" target="_top">ER_PARSE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_empty_query</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1065, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_empty_query" target="_top">ER_EMPTY_QUERY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_nonuniq_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1066, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_nonuniq_table" target="_top">ER_NONUNIQ_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_invalid_default</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1067, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_invalid_default" target="_top">ER_INVALID_DEFAULT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_multiple_pri_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1068, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_multiple_pri_key" target="_top">ER_MULTIPLE_PRI_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_keys</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1069, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_keys" target="_top">ER_TOO_MANY_KEYS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_key_parts</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1070, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_key_parts" target="_top">ER_TOO_MANY_KEY_PARTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_long_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1071, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_long_key" target="_top">ER_TOO_LONG_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_key_column_does_not_exits</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1072, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_key_column_does_not_exits" target="_top">ER_KEY_COLUMN_DOES_NOT_EXITS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_blob_used_as_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1073, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_blob_used_as_key" target="_top">ER_BLOB_USED_AS_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_big_fieldlength</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1074, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_big_fieldlength" target="_top">ER_TOO_BIG_FIELDLENGTH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_auto_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1075, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_auto_key" target="_top">ER_WRONG_AUTO_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_normal_shutdown</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1077, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_normal_shutdown" target="_top">ER_NORMAL_SHUTDOWN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_got_signal</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1078, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_got_signal" target="_top">ER_GOT_SIGNAL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_shutdown_complete</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1079, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_shutdown_complete" target="_top">ER_SHUTDOWN_COMPLETE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_forcing_close</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1080, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_forcing_close" target="_top">ER_FORCING_CLOSE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_ipsock_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1081, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_ipsock_error" target="_top">ER_IPSOCK_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_such_index</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1082, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_such_index" target="_top">ER_NO_SUCH_INDEX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_field_terminators</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1083, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_field_terminators" target="_top">ER_WRONG_FIELD_TERMINATORS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_blobs_and_no_terminated</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1084, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_blobs_and_no_terminated" target="_top">ER_BLOBS_AND_NO_TERMINATED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_textfile_not_readable</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1085, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_textfile_not_readable" target="_top">ER_TEXTFILE_NOT_READABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_file_exists_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1086, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_file_exists_error" target="_top">ER_FILE_EXISTS_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_load_info</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1087, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_load_info" target="_top">ER_LOAD_INFO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_info</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1088, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_info" target="_top">ER_ALTER_INFO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_sub_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1089, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_sub_key" target="_top">ER_WRONG_SUB_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_remove_all_fields</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1090, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_remove_all_fields" target="_top">ER_CANT_REMOVE_ALL_FIELDS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_drop_field_or_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1091, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_drop_field_or_key" target="_top">ER_CANT_DROP_FIELD_OR_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_insert_info</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1092, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_insert_info" target="_top">ER_INSERT_INFO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_update_table_used</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1093, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_update_table_used" target="_top">ER_UPDATE_TABLE_USED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_such_thread</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1094, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_such_thread" target="_top">ER_NO_SUCH_THREAD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_kill_denied_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1095, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_kill_denied_error" target="_top">ER_KILL_DENIED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_tables_used</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1096, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_tables_used" target="_top">ER_NO_TABLES_USED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_big_set</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1097, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_big_set" target="_top">ER_TOO_BIG_SET</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_unique_logfile</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1098, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_unique_logfile" target="_top">ER_NO_UNIQUE_LOGFILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_not_locked_for_write</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1099, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_not_locked_for_write" target="_top">ER_TABLE_NOT_LOCKED_FOR_WRITE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_not_locked</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1100, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_not_locked" target="_top">ER_TABLE_NOT_LOCKED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_db_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1102, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_db_name" target="_top">ER_WRONG_DB_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_table_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1103, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_table_name" target="_top">ER_WRONG_TABLE_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_big_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1104, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_big_select" target="_top">ER_TOO_BIG_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1105, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_error" target="_top">ER_UNKNOWN_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_procedure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1106, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_procedure" target="_top">ER_UNKNOWN_PROCEDURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_paramcount_to_procedure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1107, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_paramcount_to_procedure" target="_top">ER_WRONG_PARAMCOUNT_TO_PROCEDURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_parameters_to_procedure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1108, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_parameters_to_procedure" target="_top">ER_WRONG_PARAMETERS_TO_PROCEDURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1109, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_table" target="_top">ER_UNKNOWN_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_field_specified_twice</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1110, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_field_specified_twice" target="_top">ER_FIELD_SPECIFIED_TWICE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_invalid_group_func_use</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1111, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_invalid_group_func_use" target="_top">ER_INVALID_GROUP_FUNC_USE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unsupported_extension</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1112, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unsupported_extension" target="_top">ER_UNSUPPORTED_EXTENSION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_must_have_columns</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1113, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_must_have_columns" target="_top">ER_TABLE_MUST_HAVE_COLUMNS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_record_file_full</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1114, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_record_file_full" target="_top">ER_RECORD_FILE_FULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_character_set</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1115, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_character_set" target="_top">ER_UNKNOWN_CHARACTER_SET</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_tables</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1116, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_tables" target="_top">ER_TOO_MANY_TABLES</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_fields</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1117, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_fields" target="_top">ER_TOO_MANY_FIELDS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_big_rowsize</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1118, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_big_rowsize" target="_top">ER_TOO_BIG_ROWSIZE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stack_overrun</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1119, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stack_overrun" target="_top">ER_STACK_OVERRUN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_null_column_in_index</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1121, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_null_column_in_index" target="_top">ER_NULL_COLUMN_IN_INDEX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_find_udf</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1122, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_find_udf" target="_top">ER_CANT_FIND_UDF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_initialize_udf</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1123, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_initialize_udf" target="_top">ER_CANT_INITIALIZE_UDF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_udf_no_paths</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1124, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_udf_no_paths" target="_top">ER_UDF_NO_PATHS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_udf_exists</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1125, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_udf_exists" target="_top">ER_UDF_EXISTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_open_library</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1126, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_open_library" target="_top">ER_CANT_OPEN_LIBRARY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_find_dl_entry</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1127, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_find_dl_entry" target="_top">ER_CANT_FIND_DL_ENTRY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_function_not_defined</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1128, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_function_not_defined" target="_top">ER_FUNCTION_NOT_DEFINED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_host_is_blocked</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1129, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_host_is_blocked" target="_top">ER_HOST_IS_BLOCKED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_host_not_privileged</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1130, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_host_not_privileged" target="_top">ER_HOST_NOT_PRIVILEGED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_password_anonymous_user</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1131, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_password_anonymous_user" target="_top">ER_PASSWORD_ANONYMOUS_USER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_password_not_allowed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1132, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_password_not_allowed" target="_top">ER_PASSWORD_NOT_ALLOWED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_password_no_match</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1133, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_password_no_match" target="_top">ER_PASSWORD_NO_MATCH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_update_info</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1134, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_update_info" target="_top">ER_UPDATE_INFO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_create_thread</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1135, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_create_thread" target="_top">ER_CANT_CREATE_THREAD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_value_count_on_row</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1136, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_value_count_on_row" target="_top">ER_WRONG_VALUE_COUNT_ON_ROW</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_reopen_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1137, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_reopen_table" target="_top">ER_CANT_REOPEN_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_invalid_use_of_null</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1138, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_invalid_use_of_null" target="_top">ER_INVALID_USE_OF_NULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_regexp_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1139, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_regexp_error" target="_top">ER_REGEXP_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mix_of_group_func_and_fields</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1140, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mix_of_group_func_and_fields" target="_top">ER_MIX_OF_GROUP_FUNC_AND_FIELDS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_nonexisting_grant</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1141, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_nonexisting_grant" target="_top">ER_NONEXISTING_GRANT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_tableaccess_denied_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1142, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_tableaccess_denied_error" target="_top">ER_TABLEACCESS_DENIED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_columnaccess_denied_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1143, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_columnaccess_denied_error" target="_top">ER_COLUMNACCESS_DENIED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_illegal_grant_for_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1144, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_illegal_grant_for_table" target="_top">ER_ILLEGAL_GRANT_FOR_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_grant_wrong_host_or_user</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1145, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_grant_wrong_host_or_user" target="_top">ER_GRANT_WRONG_HOST_OR_USER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_such_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1146, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_such_table" target="_top">ER_NO_SUCH_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_nonexisting_table_grant</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1147, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_nonexisting_table_grant" target="_top">ER_NONEXISTING_TABLE_GRANT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_not_allowed_command</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1148, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_not_allowed_command" target="_top">ER_NOT_ALLOWED_COMMAND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_syntax_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1149, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_syntax_error" target="_top">ER_SYNTAX_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_aborting_connection</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1152, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_aborting_connection" target="_top">ER_ABORTING_CONNECTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_net_packet_too_large</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1153, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_net_packet_too_large" target="_top">ER_NET_PACKET_TOO_LARGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_net_read_error_from_pipe</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1154, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_net_read_error_from_pipe" target="_top">ER_NET_READ_ERROR_FROM_PIPE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_net_fcntl_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1155, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_net_fcntl_error" target="_top">ER_NET_FCNTL_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_net_packets_out_of_order</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1156, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_net_packets_out_of_order" target="_top">ER_NET_PACKETS_OUT_OF_ORDER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_net_uncompress_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1157, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_net_uncompress_error" target="_top">ER_NET_UNCOMPRESS_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_net_read_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1158, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_net_read_error" target="_top">ER_NET_READ_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_net_read_interrupted</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1159, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_net_read_interrupted" target="_top">ER_NET_READ_INTERRUPTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_net_error_on_write</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1160, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_net_error_on_write" target="_top">ER_NET_ERROR_ON_WRITE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_net_write_interrupted</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1161, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_net_write_interrupted" target="_top">ER_NET_WRITE_INTERRUPTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_long_string</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1162, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_long_string" target="_top">ER_TOO_LONG_STRING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_cant_handle_blob</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1163, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_cant_handle_blob" target="_top">ER_TABLE_CANT_HANDLE_BLOB</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_cant_handle_auto_increment</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1164, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_cant_handle_auto_increment" target="_top">ER_TABLE_CANT_HANDLE_AUTO_INCREMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_column_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1166, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_column_name" target="_top">ER_WRONG_COLUMN_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_key_column</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1167, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_key_column" target="_top">ER_WRONG_KEY_COLUMN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_mrg_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1168, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_mrg_table" target="_top">ER_WRONG_MRG_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_unique</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1169, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_unique" target="_top">ER_DUP_UNIQUE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_blob_key_without_length</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1170, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_blob_key_without_length" target="_top">ER_BLOB_KEY_WITHOUT_LENGTH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_primary_cant_have_null</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1171, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_primary_cant_have_null" target="_top">ER_PRIMARY_CANT_HAVE_NULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_rows</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1172, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_rows" target="_top">ER_TOO_MANY_ROWS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_requires_primary_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1173, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_requires_primary_key" target="_top">ER_REQUIRES_PRIMARY_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_raid_compiled</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1174, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_raid_compiled" target="_top">ER_NO_RAID_COMPILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_update_without_key_in_safe_mode</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1175, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_update_without_key_in_safe_mode" target="_top">ER_UPDATE_WITHOUT_KEY_IN_SAFE_MODE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_check_no_such_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1177, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_check_no_such_table" target="_top">ER_CHECK_NO_SUCH_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_check_not_implemented</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1178, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_check_not_implemented" target="_top">ER_CHECK_NOT_IMPLEMENTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_do_this_during_an_transaction</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1179, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_do_this_during_an_transaction" target="_top">ER_CANT_DO_THIS_DURING_AN_TRANSACTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_during_commit</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1180, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_during_commit" target="_top">ER_ERROR_DURING_COMMIT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_during_rollback</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1181, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_during_rollback" target="_top">ER_ERROR_DURING_ROLLBACK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_during_flush_logs</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1182, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_during_flush_logs" target="_top">ER_ERROR_DURING_FLUSH_LOGS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_during_checkpoint</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1183, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_during_checkpoint" target="_top">ER_ERROR_DURING_CHECKPOINT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_new_aborting_connection</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1184, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_new_aborting_connection" target="_top">ER_NEW_ABORTING_CONNECTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_flush_master_binlog_closed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1186, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_flush_master_binlog_closed" target="_top">ER_FLUSH_MASTER_BINLOG_CLOSED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_index_rebuild</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1187, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_index_rebuild" target="_top">ER_INDEX_REBUILD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_master</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1188, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_master" target="_top">ER_MASTER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_master_net_read</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1189, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_master_net_read" target="_top">ER_MASTER_NET_READ</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_master_net_write</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1190, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_master_net_write" target="_top">ER_MASTER_NET_WRITE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_ft_matching_key_not_found</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1191, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_ft_matching_key_not_found" target="_top">ER_FT_MATCHING_KEY_NOT_FOUND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_lock_or_active_transaction</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1192, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_lock_or_active_transaction" target="_top">ER_LOCK_OR_ACTIVE_TRANSACTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_system_variable</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1193, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_system_variable" target="_top">ER_UNKNOWN_SYSTEM_VARIABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_crashed_on_usage</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1194, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_crashed_on_usage" target="_top">ER_CRASHED_ON_USAGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_crashed_on_repair</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1195, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_crashed_on_repair" target="_top">ER_CRASHED_ON_REPAIR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warning_not_complete_rollback</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1196, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warning_not_complete_rollback" target="_top">ER_WARNING_NOT_COMPLETE_ROLLBACK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trans_cache_full</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1197, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trans_cache_full" target="_top">ER_TRANS_CACHE_FULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_must_stop</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1198, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_must_stop" target="_top">ER_SLAVE_MUST_STOP</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_not_running</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1199, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_not_running" target="_top">ER_SLAVE_NOT_RUNNING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_slave</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1200, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_slave" target="_top">ER_BAD_SLAVE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_master_info</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1201, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_master_info" target="_top">ER_MASTER_INFO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_thread</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1202, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_thread" target="_top">ER_SLAVE_THREAD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_user_connections</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1203, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_user_connections" target="_top">ER_TOO_MANY_USER_CONNECTIONS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_set_constants_only</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1204, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_set_constants_only" target="_top">ER_SET_CONSTANTS_ONLY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_lock_wait_timeout</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1205, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_lock_wait_timeout" target="_top">ER_LOCK_WAIT_TIMEOUT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_lock_table_full</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1206, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_lock_table_full" target="_top">ER_LOCK_TABLE_FULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_read_only_transaction</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1207, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_read_only_transaction" target="_top">ER_READ_ONLY_TRANSACTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_drop_db_with_read_lock</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1208, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_drop_db_with_read_lock" target="_top">ER_DROP_DB_WITH_READ_LOCK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_create_db_with_read_lock</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1209, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_create_db_with_read_lock" target="_top">ER_CREATE_DB_WITH_READ_LOCK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_arguments</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1210, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_arguments" target="_top">ER_WRONG_ARGUMENTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_permission_to_create_user</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1211, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_permission_to_create_user" target="_top">ER_NO_PERMISSION_TO_CREATE_USER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_union_tables_in_different_dir</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1212, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_union_tables_in_different_dir" target="_top">ER_UNION_TABLES_IN_DIFFERENT_DIR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_lock_deadlock</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1213, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_lock_deadlock" target="_top">ER_LOCK_DEADLOCK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_cant_handle_ft</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1214, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_cant_handle_ft" target="_top">ER_TABLE_CANT_HANDLE_FT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cannot_add_foreign</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1215, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cannot_add_foreign" target="_top">ER_CANNOT_ADD_FOREIGN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_referenced_row</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1216, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_referenced_row" target="_top">ER_NO_REFERENCED_ROW</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_row_is_referenced</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1217, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_row_is_referenced" target="_top">ER_ROW_IS_REFERENCED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_connect_to_master</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1218, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_connect_to_master" target="_top">ER_CONNECT_TO_MASTER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_query_on_master</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1219, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_query_on_master" target="_top">ER_QUERY_ON_MASTER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_when_executing_command</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1220, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_when_executing_command" target="_top">ER_ERROR_WHEN_EXECUTING_COMMAND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_usage</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1221, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_usage" target="_top">ER_WRONG_USAGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_number_of_columns_in_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1222, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_number_of_columns_in_select" target="_top">ER_WRONG_NUMBER_OF_COLUMNS_IN_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_update_with_readlock</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1223, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_update_with_readlock" target="_top">ER_CANT_UPDATE_WITH_READLOCK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mixing_not_allowed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1224, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mixing_not_allowed" target="_top">ER_MIXING_NOT_ALLOWED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_argument</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1225, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_argument" target="_top">ER_DUP_ARGUMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_user_limit_reached</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1226, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_user_limit_reached" target="_top">ER_USER_LIMIT_REACHED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_specific_access_denied_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1227, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_specific_access_denied_error" target="_top">ER_SPECIFIC_ACCESS_DENIED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_local_variable</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1228, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_local_variable" target="_top">ER_LOCAL_VARIABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_global_variable</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1229, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_global_variable" target="_top">ER_GLOBAL_VARIABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_default</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1230, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_default" target="_top">ER_NO_DEFAULT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_value_for_var</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1231, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_value_for_var" target="_top">ER_WRONG_VALUE_FOR_VAR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_type_for_var</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1232, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_type_for_var" target="_top">ER_WRONG_TYPE_FOR_VAR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_var_cant_be_read</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1233, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_var_cant_be_read" target="_top">ER_VAR_CANT_BE_READ</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_use_option_here</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1234, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_use_option_here" target="_top">ER_CANT_USE_OPTION_HERE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_not_supported_yet</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1235, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_not_supported_yet" target="_top">ER_NOT_SUPPORTED_YET</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_master_fatal_error_reading_binlog</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1236, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_master_fatal_error_reading_binlog" target="_top">ER_MASTER_FATAL_ERROR_READING_BINLOG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_ignored_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1237, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_ignored_table" target="_top">ER_SLAVE_IGNORED_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_incorrect_global_local_var</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1238, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_incorrect_global_local_var" target="_top">ER_INCORRECT_GLOBAL_LOCAL_VAR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_fk_def</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1239, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_fk_def" target="_top">ER_WRONG_FK_DEF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_key_ref_do_not_match_table_ref</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1240, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_key_ref_do_not_match_table_ref" target="_top">ER_KEY_REF_DO_NOT_MATCH_TABLE_REF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_operand_columns</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1241, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_operand_columns" target="_top">ER_OPERAND_COLUMNS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_subquery_no_1_row</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1242, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_subquery_no_1_row" target="_top">ER_SUBQUERY_NO_1_ROW</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_stmt_handler</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1243, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_stmt_handler" target="_top">ER_UNKNOWN_STMT_HANDLER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_corrupt_help_db</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1244, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_corrupt_help_db" target="_top">ER_CORRUPT_HELP_DB</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cyclic_reference</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1245, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cyclic_reference" target="_top">ER_CYCLIC_REFERENCE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_auto_convert</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1246, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_auto_convert" target="_top">ER_AUTO_CONVERT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_illegal_reference</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1247, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_illegal_reference" target="_top">ER_ILLEGAL_REFERENCE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_derived_must_have_alias</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1248, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_derived_must_have_alias" target="_top">ER_DERIVED_MUST_HAVE_ALIAS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_select_reduced</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1249, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_select_reduced" target="_top">ER_SELECT_REDUCED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_tablename_not_allowed_here</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1250, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_tablename_not_allowed_here" target="_top">ER_TABLENAME_NOT_ALLOWED_HERE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_not_supported_auth_mode</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1251, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_not_supported_auth_mode" target="_top">ER_NOT_SUPPORTED_AUTH_MODE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_spatial_cant_have_null</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1252, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_spatial_cant_have_null" target="_top">ER_SPATIAL_CANT_HAVE_NULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_collation_charset_mismatch</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1253, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_collation_charset_mismatch" target="_top">ER_COLLATION_CHARSET_MISMATCH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_was_running</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1254, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_was_running" target="_top">ER_SLAVE_WAS_RUNNING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_was_not_running</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1255, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_was_not_running" target="_top">ER_SLAVE_WAS_NOT_RUNNING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_big_for_uncompress</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1256, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_big_for_uncompress" target="_top">ER_TOO_BIG_FOR_UNCOMPRESS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_zlib_z_mem_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1257, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_zlib_z_mem_error" target="_top">ER_ZLIB_Z_MEM_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_zlib_z_buf_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1258, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_zlib_z_buf_error" target="_top">ER_ZLIB_Z_BUF_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_zlib_z_data_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1259, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_zlib_z_data_error" target="_top">ER_ZLIB_Z_DATA_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cut_value_group_concat</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1260, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cut_value_group_concat" target="_top">ER_CUT_VALUE_GROUP_CONCAT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_too_few_records</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1261, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_too_few_records" target="_top">ER_WARN_TOO_FEW_RECORDS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_too_many_records</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1262, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_too_many_records" target="_top">ER_WARN_TOO_MANY_RECORDS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_null_to_notnull</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1263, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_null_to_notnull" target="_top">ER_WARN_NULL_TO_NOTNULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_data_out_of_range</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1264, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_data_out_of_range" target="_top">ER_WARN_DATA_OUT_OF_RANGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">warn_data_truncated</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1265, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_warn_data_truncated" target="_top">WARN_DATA_TRUNCATED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_using_other_handler</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1266, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_using_other_handler" target="_top">ER_WARN_USING_OTHER_HANDLER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_aggregate_2collations</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1267, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_aggregate_2collations" target="_top">ER_CANT_AGGREGATE_2COLLATIONS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_drop_user</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1268, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_drop_user" target="_top">ER_DROP_USER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_revoke_grants</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1269, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_revoke_grants" target="_top">ER_REVOKE_GRANTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_aggregate_3collations</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1270, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_aggregate_3collations" target="_top">ER_CANT_AGGREGATE_3COLLATIONS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_aggregate_ncollations</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1271, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_aggregate_ncollations" target="_top">ER_CANT_AGGREGATE_NCOLLATIONS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_variable_is_not_struct</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1272, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_variable_is_not_struct" target="_top">ER_VARIABLE_IS_NOT_STRUCT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_collation</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1273, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_collation" target="_top">ER_UNKNOWN_COLLATION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_ignored_ssl_params</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1274, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_ignored_ssl_params" target="_top">ER_SLAVE_IGNORED_SSL_PARAMS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_server_is_in_secure_auth_mode</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1275, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_server_is_in_secure_auth_mode" target="_top">ER_SERVER_IS_IN_SECURE_AUTH_MODE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_field_resolved</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1276, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_field_resolved" target="_top">ER_WARN_FIELD_RESOLVED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_slave_until_cond</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1277, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_slave_until_cond" target="_top">ER_BAD_SLAVE_UNTIL_COND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_missing_skip_slave</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1278, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_missing_skip_slave" target="_top">ER_MISSING_SKIP_SLAVE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_until_cond_ignored</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1279, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_until_cond_ignored" target="_top">ER_UNTIL_COND_IGNORED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_name_for_index</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1280, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_name_for_index" target="_top">ER_WRONG_NAME_FOR_INDEX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_name_for_catalog</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1281, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_name_for_catalog" target="_top">ER_WRONG_NAME_FOR_CATALOG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_qc_resize</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1282, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_qc_resize" target="_top">ER_WARN_QC_RESIZE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_ft_column</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1283, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_ft_column" target="_top">ER_BAD_FT_COLUMN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_key_cache</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1284, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_key_cache" target="_top">ER_UNKNOWN_KEY_CACHE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_hostname_wont_work</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1285, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_hostname_wont_work" target="_top">ER_WARN_HOSTNAME_WONT_WORK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_storage_engine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1286, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_storage_engine" target="_top">ER_UNKNOWN_STORAGE_ENGINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_deprecated_syntax</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1287, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_deprecated_syntax" target="_top">ER_WARN_DEPRECATED_SYNTAX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_non_updatable_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1288, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_non_updatable_table" target="_top">ER_NON_UPDATABLE_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_feature_disabled</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1289, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_feature_disabled" target="_top">ER_FEATURE_DISABLED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_option_prevents_statement</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1290, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_option_prevents_statement" target="_top">ER_OPTION_PREVENTS_STATEMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_duplicated_value_in_type</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1291, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_duplicated_value_in_type" target="_top">ER_DUPLICATED_VALUE_IN_TYPE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_truncated_wrong_value</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1292, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_truncated_wrong_value" target="_top">ER_TRUNCATED_WRONG_VALUE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_much_auto_timestamp_cols</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1293, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_much_auto_timestamp_cols" target="_top">ER_TOO_MUCH_AUTO_TIMESTAMP_COLS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_invalid_on_update</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1294, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_invalid_on_update" target="_top">ER_INVALID_ON_UPDATE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unsupported_ps</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1295, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unsupported_ps" target="_top">ER_UNSUPPORTED_PS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_get_errmsg</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1296, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_get_errmsg" target="_top">ER_GET_ERRMSG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_get_temporary_errmsg</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1297, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_get_temporary_errmsg" target="_top">ER_GET_TEMPORARY_ERRMSG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_time_zone</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1298, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_time_zone" target="_top">ER_UNKNOWN_TIME_ZONE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_invalid_timestamp</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1299, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_invalid_timestamp" target="_top">ER_WARN_INVALID_TIMESTAMP</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_invalid_character_string</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1300, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_invalid_character_string" target="_top">ER_INVALID_CHARACTER_STRING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_allowed_packet_overflowed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1301, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_allowed_packet_overflowed" target="_top">ER_WARN_ALLOWED_PACKET_OVERFLOWED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_conflicting_declarations</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1302, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_conflicting_declarations" target="_top">ER_CONFLICTING_DECLARATIONS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_no_recursive_create</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1303, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_no_recursive_create" target="_top">ER_SP_NO_RECURSIVE_CREATE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_already_exists</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1304, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_already_exists" target="_top">ER_SP_ALREADY_EXISTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_does_not_exist</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1305, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_does_not_exist" target="_top">ER_SP_DOES_NOT_EXIST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_drop_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1306, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_drop_failed" target="_top">ER_SP_DROP_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_store_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1307, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_store_failed" target="_top">ER_SP_STORE_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_lilabel_mismatch</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1308, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_lilabel_mismatch" target="_top">ER_SP_LILABEL_MISMATCH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_label_redefine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1309, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_label_redefine" target="_top">ER_SP_LABEL_REDEFINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_label_mismatch</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1310, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_label_mismatch" target="_top">ER_SP_LABEL_MISMATCH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_uninit_var</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1311, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_uninit_var" target="_top">ER_SP_UNINIT_VAR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_badselect</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1312, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_badselect" target="_top">ER_SP_BADSELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_badreturn</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1313, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_badreturn" target="_top">ER_SP_BADRETURN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_badstatement</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1314, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_badstatement" target="_top">ER_SP_BADSTATEMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_update_log_deprecated_ignored</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1315, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_update_log_deprecated_ignored" target="_top">ER_UPDATE_LOG_DEPRECATED_IGNORED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_update_log_deprecated_translated</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1316, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_update_log_deprecated_translated" target="_top">ER_UPDATE_LOG_DEPRECATED_TRANSLATED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_query_interrupted</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1317, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_query_interrupted" target="_top">ER_QUERY_INTERRUPTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_wrong_no_of_args</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1318, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_wrong_no_of_args" target="_top">ER_SP_WRONG_NO_OF_ARGS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_cond_mismatch</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1319, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_cond_mismatch" target="_top">ER_SP_COND_MISMATCH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_noreturn</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1320, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_noreturn" target="_top">ER_SP_NORETURN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_noreturnend</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1321, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_noreturnend" target="_top">ER_SP_NORETURNEND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_bad_cursor_query</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1322, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_bad_cursor_query" target="_top">ER_SP_BAD_CURSOR_QUERY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_bad_cursor_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1323, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_bad_cursor_select" target="_top">ER_SP_BAD_CURSOR_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_cursor_mismatch</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1324, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_cursor_mismatch" target="_top">ER_SP_CURSOR_MISMATCH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_cursor_already_open</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1325, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_cursor_already_open" target="_top">ER_SP_CURSOR_ALREADY_OPEN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_cursor_not_open</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1326, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_cursor_not_open" target="_top">ER_SP_CURSOR_NOT_OPEN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_undeclared_var</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1327, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_undeclared_var" target="_top">ER_SP_UNDECLARED_VAR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_wrong_no_of_fetch_args</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1328, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_wrong_no_of_fetch_args" target="_top">ER_SP_WRONG_NO_OF_FETCH_ARGS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_fetch_no_data</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1329, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_fetch_no_data" target="_top">ER_SP_FETCH_NO_DATA</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_dup_param</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1330, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_dup_param" target="_top">ER_SP_DUP_PARAM</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_dup_var</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1331, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_dup_var" target="_top">ER_SP_DUP_VAR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_dup_cond</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1332, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_dup_cond" target="_top">ER_SP_DUP_COND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_dup_curs</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1333, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_dup_curs" target="_top">ER_SP_DUP_CURS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_cant_alter</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1334, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_cant_alter" target="_top">ER_SP_CANT_ALTER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_subselect_nyi</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1335, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_subselect_nyi" target="_top">ER_SP_SUBSELECT_NYI</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stmt_not_allowed_in_sf_or_trg</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1336, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stmt_not_allowed_in_sf_or_trg" target="_top">ER_STMT_NOT_ALLOWED_IN_SF_OR_TRG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_varcond_after_curshndlr</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1337, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_varcond_after_curshndlr" target="_top">ER_SP_VARCOND_AFTER_CURSHNDLR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_cursor_after_handler</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1338, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_cursor_after_handler" target="_top">ER_SP_CURSOR_AFTER_HANDLER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_case_not_found</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1339, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_case_not_found" target="_top">ER_SP_CASE_NOT_FOUND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fparser_too_big_file</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1340, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fparser_too_big_file" target="_top">ER_FPARSER_TOO_BIG_FILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fparser_bad_header</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1341, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fparser_bad_header" target="_top">ER_FPARSER_BAD_HEADER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fparser_eof_in_comment</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1342, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fparser_eof_in_comment" target="_top">ER_FPARSER_EOF_IN_COMMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fparser_error_in_parameter</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1343, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fparser_error_in_parameter" target="_top">ER_FPARSER_ERROR_IN_PARAMETER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fparser_eof_in_unknown_parameter</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1344, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fparser_eof_in_unknown_parameter" target="_top">ER_FPARSER_EOF_IN_UNKNOWN_PARAMETER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_no_explain</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1345, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_no_explain" target="_top">ER_VIEW_NO_EXPLAIN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_frm_unknown_type</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1346, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_frm_unknown_type" target="_top">ER_FRM_UNKNOWN_TYPE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_object</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1347, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_object" target="_top">ER_WRONG_OBJECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_nonupdateable_column</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1348, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_nonupdateable_column" target="_top">ER_NONUPDATEABLE_COLUMN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_select_clause</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1350, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_select_clause" target="_top">ER_VIEW_SELECT_CLAUSE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_select_variable</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1351, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_select_variable" target="_top">ER_VIEW_SELECT_VARIABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_select_tmptable</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1352, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_select_tmptable" target="_top">ER_VIEW_SELECT_TMPTABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_wrong_list</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1353, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_wrong_list" target="_top">ER_VIEW_WRONG_LIST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_view_merge</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1354, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_view_merge" target="_top">ER_WARN_VIEW_MERGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_view_without_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1355, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_view_without_key" target="_top">ER_WARN_VIEW_WITHOUT_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_invalid</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1356, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_invalid" target="_top">ER_VIEW_INVALID</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_no_drop_sp</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1357, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_no_drop_sp" target="_top">ER_SP_NO_DROP_SP</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_goto_in_hndlr</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1358, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_goto_in_hndlr" target="_top">ER_SP_GOTO_IN_HNDLR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_already_exists</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1359, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_already_exists" target="_top">ER_TRG_ALREADY_EXISTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_does_not_exist</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1360, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_does_not_exist" target="_top">ER_TRG_DOES_NOT_EXIST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_on_view_or_temp_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1361, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_on_view_or_temp_table" target="_top">ER_TRG_ON_VIEW_OR_TEMP_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_cant_change_row</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1362, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_cant_change_row" target="_top">ER_TRG_CANT_CHANGE_ROW</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_no_such_row_in_trg</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1363, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_no_such_row_in_trg" target="_top">ER_TRG_NO_SUCH_ROW_IN_TRG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_default_for_field</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1364, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_default_for_field" target="_top">ER_NO_DEFAULT_FOR_FIELD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_division_by_zero</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1365, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_division_by_zero" target="_top">ER_DIVISION_BY_ZERO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_truncated_wrong_value_for_field</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1366, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_truncated_wrong_value_for_field" target="_top">ER_TRUNCATED_WRONG_VALUE_FOR_FIELD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_illegal_value_for_type</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1367, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_illegal_value_for_type" target="_top">ER_ILLEGAL_VALUE_FOR_TYPE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_nonupd_check</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1368, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_nonupd_check" target="_top">ER_VIEW_NONUPD_CHECK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_check_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1369, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_check_failed" target="_top">ER_VIEW_CHECK_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_procaccess_denied_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1370, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_procaccess_denied_error" target="_top">ER_PROCACCESS_DENIED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_relay_log_fail</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1371, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_relay_log_fail" target="_top">ER_RELAY_LOG_FAIL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_passwd_length</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1372, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_passwd_length" target="_top">ER_PASSWD_LENGTH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_target_binlog</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1373, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_target_binlog" target="_top">ER_UNKNOWN_TARGET_BINLOG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_io_err_log_index_read</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1374, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_io_err_log_index_read" target="_top">ER_IO_ERR_LOG_INDEX_READ</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_purge_prohibited</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1375, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_purge_prohibited" target="_top">ER_BINLOG_PURGE_PROHIBITED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fseek_fail</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1376, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fseek_fail" target="_top">ER_FSEEK_FAIL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_purge_fatal_err</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1377, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_purge_fatal_err" target="_top">ER_BINLOG_PURGE_FATAL_ERR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_log_in_use</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1378, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_log_in_use" target="_top">ER_LOG_IN_USE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_log_purge_unknown_err</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1379, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_log_purge_unknown_err" target="_top">ER_LOG_PURGE_UNKNOWN_ERR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_relay_log_init</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1380, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_relay_log_init" target="_top">ER_RELAY_LOG_INIT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_binary_logging</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1381, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_binary_logging" target="_top">ER_NO_BINARY_LOGGING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_reserved_syntax</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1382, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_reserved_syntax" target="_top">ER_RESERVED_SYNTAX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wsas_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1383, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wsas_failed" target="_top">ER_WSAS_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_diff_groups_proc</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1384, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_diff_groups_proc" target="_top">ER_DIFF_GROUPS_PROC</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_group_for_proc</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1385, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_group_for_proc" target="_top">ER_NO_GROUP_FOR_PROC</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_order_with_proc</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1386, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_order_with_proc" target="_top">ER_ORDER_WITH_PROC</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_logging_prohibit_changing_of</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1387, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_logging_prohibit_changing_of" target="_top">ER_LOGGING_PROHIBIT_CHANGING_OF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_file_mapping</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1388, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_file_mapping" target="_top">ER_NO_FILE_MAPPING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_magic</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1389, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_magic" target="_top">ER_WRONG_MAGIC</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_ps_many_param</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1390, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_ps_many_param" target="_top">ER_PS_MANY_PARAM</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_key_part_0</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1391, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_key_part_0" target="_top">ER_KEY_PART_0</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_checksum</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1392, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_checksum" target="_top">ER_VIEW_CHECKSUM</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_multiupdate</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1393, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_multiupdate" target="_top">ER_VIEW_MULTIUPDATE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_no_insert_field_list</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1394, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_no_insert_field_list" target="_top">ER_VIEW_NO_INSERT_FIELD_LIST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_delete_merge_view</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1395, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_delete_merge_view" target="_top">ER_VIEW_DELETE_MERGE_VIEW</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cannot_user</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1396, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cannot_user" target="_top">ER_CANNOT_USER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_xaer_nota</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1397, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_xaer_nota" target="_top">ER_XAER_NOTA</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_xaer_inval</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1398, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_xaer_inval" target="_top">ER_XAER_INVAL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_xaer_rmfail</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1399, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_xaer_rmfail" target="_top">ER_XAER_RMFAIL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_xaer_outside</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1400, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_xaer_outside" target="_top">ER_XAER_OUTSIDE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_xaer_rmerr</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1401, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_xaer_rmerr" target="_top">ER_XAER_RMERR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_xa_rbrollback</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1402, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_xa_rbrollback" target="_top">ER_XA_RBROLLBACK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_nonexisting_proc_grant</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1403, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_nonexisting_proc_grant" target="_top">ER_NONEXISTING_PROC_GRANT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_proc_auto_grant_fail</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1404, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_proc_auto_grant_fail" target="_top">ER_PROC_AUTO_GRANT_FAIL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_proc_auto_revoke_fail</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1405, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_proc_auto_revoke_fail" target="_top">ER_PROC_AUTO_REVOKE_FAIL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_data_too_long</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1406, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_data_too_long" target="_top">ER_DATA_TOO_LONG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_bad_sqlstate</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1407, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_bad_sqlstate" target="_top">ER_SP_BAD_SQLSTATE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_startup</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1408, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_startup" target="_top">ER_STARTUP</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_load_from_fixed_size_rows_to_var</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1409, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_load_from_fixed_size_rows_to_var" target="_top">ER_LOAD_FROM_FIXED_SIZE_ROWS_TO_VAR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_create_user_with_grant</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1410, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_create_user_with_grant" target="_top">ER_CANT_CREATE_USER_WITH_GRANT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_value_for_type</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1411, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_value_for_type" target="_top">ER_WRONG_VALUE_FOR_TYPE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_def_changed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1412, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_def_changed" target="_top">ER_TABLE_DEF_CHANGED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_dup_handler</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1413, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_dup_handler" target="_top">ER_SP_DUP_HANDLER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_not_var_arg</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1414, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_not_var_arg" target="_top">ER_SP_NOT_VAR_ARG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_no_retset</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1415, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_no_retset" target="_top">ER_SP_NO_RETSET</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_create_geometry_object</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1416, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_create_geometry_object" target="_top">ER_CANT_CREATE_GEOMETRY_OBJECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_failed_routine_break_binlog</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1417, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_failed_routine_break_binlog" target="_top">ER_FAILED_ROUTINE_BREAK_BINLOG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_routine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1418, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_routine" target="_top">ER_BINLOG_UNSAFE_ROUTINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_create_routine_need_super</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1419, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_create_routine_need_super" target="_top">ER_BINLOG_CREATE_ROUTINE_NEED_SUPER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_exec_stmt_with_open_cursor</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1420, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_exec_stmt_with_open_cursor" target="_top">ER_EXEC_STMT_WITH_OPEN_CURSOR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stmt_has_no_open_cursor</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1421, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stmt_has_no_open_cursor" target="_top">ER_STMT_HAS_NO_OPEN_CURSOR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_commit_not_allowed_in_sf_or_trg</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1422, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_commit_not_allowed_in_sf_or_trg" target="_top">ER_COMMIT_NOT_ALLOWED_IN_SF_OR_TRG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_default_for_view_field</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1423, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_default_for_view_field" target="_top">ER_NO_DEFAULT_FOR_VIEW_FIELD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_no_recursion</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1424, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_no_recursion" target="_top">ER_SP_NO_RECURSION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_big_scale</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1425, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_big_scale" target="_top">ER_TOO_BIG_SCALE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_big_precision</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1426, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_big_precision" target="_top">ER_TOO_BIG_PRECISION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_m_bigger_than_d</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1427, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_m_bigger_than_d" target="_top">ER_M_BIGGER_THAN_D</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_lock_of_system_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1428, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_lock_of_system_table" target="_top">ER_WRONG_LOCK_OF_SYSTEM_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_connect_to_foreign_data_source</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1429, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_connect_to_foreign_data_source" target="_top">ER_CONNECT_TO_FOREIGN_DATA_SOURCE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_query_on_foreign_data_source</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1430, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_query_on_foreign_data_source" target="_top">ER_QUERY_ON_FOREIGN_DATA_SOURCE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_foreign_data_source_doesnt_exist</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1431, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_foreign_data_source_doesnt_exist" target="_top">ER_FOREIGN_DATA_SOURCE_DOESNT_EXIST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_foreign_data_string_invalid_cant_create</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1432, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_foreign_data_string_invalid_cant_create" target="_top">ER_FOREIGN_DATA_STRING_INVALID_CANT_CREATE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_foreign_data_string_invalid</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1433, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_foreign_data_string_invalid" target="_top">ER_FOREIGN_DATA_STRING_INVALID</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_create_federated_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1434, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_create_federated_table" target="_top">ER_CANT_CREATE_FEDERATED_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_in_wrong_schema</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1435, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_in_wrong_schema" target="_top">ER_TRG_IN_WRONG_SCHEMA</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stack_overrun_need_more</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1436, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stack_overrun_need_more" target="_top">ER_STACK_OVERRUN_NEED_MORE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_long_body</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1437, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_long_body" target="_top">ER_TOO_LONG_BODY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_cant_drop_default_keycache</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1438, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_cant_drop_default_keycache" target="_top">ER_WARN_CANT_DROP_DEFAULT_KEYCACHE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_big_displaywidth</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1439, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_big_displaywidth" target="_top">ER_TOO_BIG_DISPLAYWIDTH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_xaer_dupid</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1440, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_xaer_dupid" target="_top">ER_XAER_DUPID</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_datetime_function_overflow</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1441, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_datetime_function_overflow" target="_top">ER_DATETIME_FUNCTION_OVERFLOW</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_update_used_table_in_sf_or_trg</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1442, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_update_used_table_in_sf_or_trg" target="_top">ER_CANT_UPDATE_USED_TABLE_IN_SF_OR_TRG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_prevent_update</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1443, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_prevent_update" target="_top">ER_VIEW_PREVENT_UPDATE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_ps_no_recursion</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1444, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_ps_no_recursion" target="_top">ER_PS_NO_RECURSION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_cant_set_autocommit</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1445, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_cant_set_autocommit" target="_top">ER_SP_CANT_SET_AUTOCOMMIT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_malformed_definer</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1446, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_malformed_definer" target="_top">ER_MALFORMED_DEFINER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_frm_no_user</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1447, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_frm_no_user" target="_top">ER_VIEW_FRM_NO_USER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_other_user</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1448, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_other_user" target="_top">ER_VIEW_OTHER_USER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_such_user</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1449, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_such_user" target="_top">ER_NO_SUCH_USER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_forbid_schema_change</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1450, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_forbid_schema_change" target="_top">ER_FORBID_SCHEMA_CHANGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_row_is_referenced_2</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1451, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_row_is_referenced_2" target="_top">ER_ROW_IS_REFERENCED_2</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_referenced_row_2</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1452, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_referenced_row_2" target="_top">ER_NO_REFERENCED_ROW_2</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_bad_var_shadow</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1453, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_bad_var_shadow" target="_top">ER_SP_BAD_VAR_SHADOW</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_no_definer</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1454, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_no_definer" target="_top">ER_TRG_NO_DEFINER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_old_file_format</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1455, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_old_file_format" target="_top">ER_OLD_FILE_FORMAT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_recursion_limit</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1456, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_recursion_limit" target="_top">ER_SP_RECURSION_LIMIT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_proc_table_corrupt</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1457, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_proc_table_corrupt" target="_top">ER_SP_PROC_TABLE_CORRUPT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_wrong_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1458, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_wrong_name" target="_top">ER_SP_WRONG_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_needs_upgrade</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1459, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_needs_upgrade" target="_top">ER_TABLE_NEEDS_UPGRADE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sp_no_aggregate</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1460, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sp_no_aggregate" target="_top">ER_SP_NO_AGGREGATE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_max_prepared_stmt_count_reached</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1461, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_max_prepared_stmt_count_reached" target="_top">ER_MAX_PREPARED_STMT_COUNT_REACHED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_recursive</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1462, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_recursive" target="_top">ER_VIEW_RECURSIVE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_non_grouping_field_used</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1463, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_non_grouping_field_used" target="_top">ER_NON_GROUPING_FIELD_USED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_cant_handle_spkeys</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1464, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_cant_handle_spkeys" target="_top">ER_TABLE_CANT_HANDLE_SPKEYS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_triggers_on_system_schema</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1465, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_triggers_on_system_schema" target="_top">ER_NO_TRIGGERS_ON_SYSTEM_SCHEMA</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_removed_spaces</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1466, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_removed_spaces" target="_top">ER_REMOVED_SPACES</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_autoinc_read_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1467, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_autoinc_read_failed" target="_top">ER_AUTOINC_READ_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_username</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1468, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_username" target="_top">ER_USERNAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_hostname</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1469, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_hostname" target="_top">ER_HOSTNAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_string_length</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1470, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_string_length" target="_top">ER_WRONG_STRING_LENGTH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_non_insertable_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1471, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_non_insertable_table" target="_top">ER_NON_INSERTABLE_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_admin_wrong_mrg_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1472, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_admin_wrong_mrg_table" target="_top">ER_ADMIN_WRONG_MRG_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_high_level_of_nesting_for_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1473, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_high_level_of_nesting_for_select" target="_top">ER_TOO_HIGH_LEVEL_OF_NESTING_FOR_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_name_becomes_empty</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1474, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_name_becomes_empty" target="_top">ER_NAME_BECOMES_EMPTY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_ambiguous_field_term</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1475, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_ambiguous_field_term" target="_top">ER_AMBIGUOUS_FIELD_TERM</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_foreign_server_exists</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1476, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_foreign_server_exists" target="_top">ER_FOREIGN_SERVER_EXISTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_foreign_server_doesnt_exist</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1477, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_foreign_server_doesnt_exist" target="_top">ER_FOREIGN_SERVER_DOESNT_EXIST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_illegal_ha_create_option</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1478, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_illegal_ha_create_option" target="_top">ER_ILLEGAL_HA_CREATE_OPTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_requires_values_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1479, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_requires_values_error" target="_top">ER_PARTITION_REQUIRES_VALUES_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_wrong_values_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1480, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_wrong_values_error" target="_top">ER_PARTITION_WRONG_VALUES_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_maxvalue_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1481, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_maxvalue_error" target="_top">ER_PARTITION_MAXVALUE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_subpartition_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1482, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_subpartition_error" target="_top">ER_PARTITION_SUBPARTITION_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_subpart_mix_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1483, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_subpart_mix_error" target="_top">ER_PARTITION_SUBPART_MIX_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_wrong_no_part_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1484, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_wrong_no_part_error" target="_top">ER_PARTITION_WRONG_NO_PART_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_wrong_no_subpart_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1485, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_wrong_no_subpart_error" target="_top">ER_PARTITION_WRONG_NO_SUBPART_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_expr_in_partition_func_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1486, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_expr_in_partition_func_error" target="_top">ER_WRONG_EXPR_IN_PARTITION_FUNC_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_field_not_found_part_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1488, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_field_not_found_part_error" target="_top">ER_FIELD_NOT_FOUND_PART_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_list_of_fields_only_in_hash_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1489, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_list_of_fields_only_in_hash_error" target="_top">ER_LIST_OF_FIELDS_ONLY_IN_HASH_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_inconsistent_partition_info_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1490, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_inconsistent_partition_info_error" target="_top">ER_INCONSISTENT_PARTITION_INFO_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_func_not_allowed_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1491, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_func_not_allowed_error" target="_top">ER_PARTITION_FUNC_NOT_ALLOWED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partitions_must_be_defined_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1492, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partitions_must_be_defined_error" target="_top">ER_PARTITIONS_MUST_BE_DEFINED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_range_not_increasing_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1493, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_range_not_increasing_error" target="_top">ER_RANGE_NOT_INCREASING_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_inconsistent_type_of_functions_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1494, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_inconsistent_type_of_functions_error" target="_top">ER_INCONSISTENT_TYPE_OF_FUNCTIONS_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_multiple_def_const_in_list_part_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1495, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_multiple_def_const_in_list_part_error" target="_top">ER_MULTIPLE_DEF_CONST_IN_LIST_PART_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_entry_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1496, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_entry_error" target="_top">ER_PARTITION_ENTRY_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mix_handler_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1497, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mix_handler_error" target="_top">ER_MIX_HANDLER_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_not_defined_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1498, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_not_defined_error" target="_top">ER_PARTITION_NOT_DEFINED_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_partitions_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1499, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_partitions_error" target="_top">ER_TOO_MANY_PARTITIONS_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_subpartition_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1500, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_subpartition_error" target="_top">ER_SUBPARTITION_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_create_handler_file</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1501, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_create_handler_file" target="_top">ER_CANT_CREATE_HANDLER_FILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_blob_field_in_part_func_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1502, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_blob_field_in_part_func_error" target="_top">ER_BLOB_FIELD_IN_PART_FUNC_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unique_key_need_all_fields_in_pf</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1503, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unique_key_need_all_fields_in_pf" target="_top">ER_UNIQUE_KEY_NEED_ALL_FIELDS_IN_PF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_parts_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1504, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_parts_error" target="_top">ER_NO_PARTS_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_mgmt_on_nonpartitioned</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1505, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_mgmt_on_nonpartitioned" target="_top">ER_PARTITION_MGMT_ON_NONPARTITIONED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_drop_partition_non_existent</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1507, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_drop_partition_non_existent" target="_top">ER_DROP_PARTITION_NON_EXISTENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_drop_last_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1508, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_drop_last_partition" target="_top">ER_DROP_LAST_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_coalesce_only_on_hash_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1509, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_coalesce_only_on_hash_partition" target="_top">ER_COALESCE_ONLY_ON_HASH_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_reorg_hash_only_on_same_no</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1510, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_reorg_hash_only_on_same_no" target="_top">ER_REORG_HASH_ONLY_ON_SAME_NO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_reorg_no_param_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1511, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_reorg_no_param_error" target="_top">ER_REORG_NO_PARAM_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_only_on_range_list_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1512, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_only_on_range_list_partition" target="_top">ER_ONLY_ON_RANGE_LIST_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_add_partition_subpart_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1513, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_add_partition_subpart_error" target="_top">ER_ADD_PARTITION_SUBPART_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_add_partition_no_new_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1514, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_add_partition_no_new_partition" target="_top">ER_ADD_PARTITION_NO_NEW_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_coalesce_partition_no_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1515, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_coalesce_partition_no_partition" target="_top">ER_COALESCE_PARTITION_NO_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_reorg_partition_not_exist</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1516, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_reorg_partition_not_exist" target="_top">ER_REORG_PARTITION_NOT_EXIST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_same_name_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1517, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_same_name_partition" target="_top">ER_SAME_NAME_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_binlog_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1518, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_binlog_error" target="_top">ER_NO_BINLOG_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_consecutive_reorg_partitions</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1519, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_consecutive_reorg_partitions" target="_top">ER_CONSECUTIVE_REORG_PARTITIONS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_reorg_outside_range</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1520, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_reorg_outside_range" target="_top">ER_REORG_OUTSIDE_RANGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_function_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1521, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_function_failure" target="_top">ER_PARTITION_FUNCTION_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_part_state_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1522, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_part_state_error" target="_top">ER_PART_STATE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_limited_part_range</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1523, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_limited_part_range" target="_top">ER_LIMITED_PART_RANGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_plugin_is_not_loaded</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1524, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_plugin_is_not_loaded" target="_top">ER_PLUGIN_IS_NOT_LOADED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_value</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1525, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_value" target="_top">ER_WRONG_VALUE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_partition_for_given_value</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1526, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_partition_for_given_value" target="_top">ER_NO_PARTITION_FOR_GIVEN_VALUE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_filegroup_option_only_once</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1527, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_filegroup_option_only_once" target="_top">ER_FILEGROUP_OPTION_ONLY_ONCE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_create_filegroup_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1528, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_create_filegroup_failed" target="_top">ER_CREATE_FILEGROUP_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_drop_filegroup_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1529, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_drop_filegroup_failed" target="_top">ER_DROP_FILEGROUP_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_tablespace_auto_extend_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1530, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_tablespace_auto_extend_error" target="_top">ER_TABLESPACE_AUTO_EXTEND_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_size_number</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1531, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_size_number" target="_top">ER_WRONG_SIZE_NUMBER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_size_overflow_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1532, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_size_overflow_error" target="_top">ER_SIZE_OVERFLOW_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_filegroup_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1533, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_filegroup_failed" target="_top">ER_ALTER_FILEGROUP_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_row_logging_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1534, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_row_logging_failed" target="_top">ER_BINLOG_ROW_LOGGING_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_row_wrong_table_def</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1535, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_row_wrong_table_def" target="_top">ER_BINLOG_ROW_WRONG_TABLE_DEF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_row_rbr_to_sbr</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1536, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_row_rbr_to_sbr" target="_top">ER_BINLOG_ROW_RBR_TO_SBR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_already_exists</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1537, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_already_exists" target="_top">ER_EVENT_ALREADY_EXISTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_store_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1538, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_store_failed" target="_top">ER_EVENT_STORE_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_does_not_exist</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1539, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_does_not_exist" target="_top">ER_EVENT_DOES_NOT_EXIST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_cant_alter</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1540, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_cant_alter" target="_top">ER_EVENT_CANT_ALTER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_drop_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1541, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_drop_failed" target="_top">ER_EVENT_DROP_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_interval_not_positive_or_too_big</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1542, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_interval_not_positive_or_too_big" target="_top">ER_EVENT_INTERVAL_NOT_POSITIVE_OR_TOO_BIG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_ends_before_starts</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1543, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_ends_before_starts" target="_top">ER_EVENT_ENDS_BEFORE_STARTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_exec_time_in_the_past</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1544, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_exec_time_in_the_past" target="_top">ER_EVENT_EXEC_TIME_IN_THE_PAST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_open_table_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1545, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_open_table_failed" target="_top">ER_EVENT_OPEN_TABLE_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_neither_m_expr_nor_m_at</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1546, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_neither_m_expr_nor_m_at" target="_top">ER_EVENT_NEITHER_M_EXPR_NOR_M_AT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_cannot_delete</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1549, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_cannot_delete" target="_top">ER_EVENT_CANNOT_DELETE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_compile_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1550, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_compile_error" target="_top">ER_EVENT_COMPILE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_same_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1551, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_same_name" target="_top">ER_EVENT_SAME_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_data_too_long</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1552, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_data_too_long" target="_top">ER_EVENT_DATA_TOO_LONG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_drop_index_fk</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1553, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_drop_index_fk" target="_top">ER_DROP_INDEX_FK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_deprecated_syntax_with_ver</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1554, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_deprecated_syntax_with_ver" target="_top">ER_WARN_DEPRECATED_SYNTAX_WITH_VER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_write_lock_log_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1555, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_write_lock_log_table" target="_top">ER_CANT_WRITE_LOCK_LOG_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_lock_log_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1556, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_lock_log_table" target="_top">ER_CANT_LOCK_LOG_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_col_count_doesnt_match_please_update</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1558, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_col_count_doesnt_match_please_update" target="_top">ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_temp_table_prevents_switch_out_of_rbr</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1559, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_temp_table_prevents_switch_out_of_rbr" target="_top">ER_TEMP_TABLE_PREVENTS_SWITCH_OUT_OF_RBR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stored_function_prevents_switch_binlog_format</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1560, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stored_function_prevents_switch_binlog_format" target="_top">ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_FORMAT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_no_temporary</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1562, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_no_temporary" target="_top">ER_PARTITION_NO_TEMPORARY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_const_domain_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1563, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_const_domain_error" target="_top">ER_PARTITION_CONST_DOMAIN_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_function_is_not_allowed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1564, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_function_is_not_allowed" target="_top">ER_PARTITION_FUNCTION_IS_NOT_ALLOWED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_ddl_log_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1565, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_ddl_log_error" target="_top">ER_DDL_LOG_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_null_in_values_less_than</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1566, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_null_in_values_less_than" target="_top">ER_NULL_IN_VALUES_LESS_THAN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_partition_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1567, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_partition_name" target="_top">ER_WRONG_PARTITION_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_change_tx_characteristics</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1568, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_change_tx_characteristics" target="_top">ER_CANT_CHANGE_TX_CHARACTERISTICS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_entry_autoincrement_case</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1569, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_entry_autoincrement_case" target="_top">ER_DUP_ENTRY_AUTOINCREMENT_CASE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_modify_queue_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1570, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_modify_queue_error" target="_top">ER_EVENT_MODIFY_QUEUE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_set_var_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1571, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_set_var_error" target="_top">ER_EVENT_SET_VAR_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_merge_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1572, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_merge_error" target="_top">ER_PARTITION_MERGE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_activate_log</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1573, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_activate_log" target="_top">ER_CANT_ACTIVATE_LOG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_rbr_not_available</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1574, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_rbr_not_available" target="_top">ER_RBR_NOT_AVAILABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_base64_decode_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1575, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_base64_decode_error" target="_top">ER_BASE64_DECODE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_recursion_forbidden</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1576, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_recursion_forbidden" target="_top">ER_EVENT_RECURSION_FORBIDDEN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_events_db_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1577, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_events_db_error" target="_top">ER_EVENTS_DB_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_only_integers_allowed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1578, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_only_integers_allowed" target="_top">ER_ONLY_INTEGERS_ALLOWED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unsuported_log_engine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1579, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unsuported_log_engine" target="_top">ER_UNSUPORTED_LOG_ENGINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_log_statement</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1580, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_log_statement" target="_top">ER_BAD_LOG_STATEMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_rename_log_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1581, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_rename_log_table" target="_top">ER_CANT_RENAME_LOG_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_paramcount_to_native_fct</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1582, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_paramcount_to_native_fct" target="_top">ER_WRONG_PARAMCOUNT_TO_NATIVE_FCT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_parameters_to_native_fct</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1583, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_parameters_to_native_fct" target="_top">ER_WRONG_PARAMETERS_TO_NATIVE_FCT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_parameters_to_stored_fct</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1584, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_parameters_to_stored_fct" target="_top">ER_WRONG_PARAMETERS_TO_STORED_FCT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_native_fct_name_collision</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1585, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_native_fct_name_collision" target="_top">ER_NATIVE_FCT_NAME_COLLISION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_entry_with_key_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1586, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_entry_with_key_name" target="_top">ER_DUP_ENTRY_WITH_KEY_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_purge_emfile</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1587, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_purge_emfile" target="_top">ER_BINLOG_PURGE_EMFILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_cannot_create_in_the_past</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1588, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_cannot_create_in_the_past" target="_top">ER_EVENT_CANNOT_CREATE_IN_THE_PAST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_cannot_alter_in_the_past</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1589, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_cannot_alter_in_the_past" target="_top">ER_EVENT_CANNOT_ALTER_IN_THE_PAST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_incident</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1590, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_incident" target="_top">ER_SLAVE_INCIDENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_partition_for_given_value_silent</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1591, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_partition_for_given_value_silent" target="_top">ER_NO_PARTITION_FOR_GIVEN_VALUE_SILENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_statement</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1592, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_statement" target="_top">ER_BINLOG_UNSAFE_STATEMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_relay_log_read_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1594, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_relay_log_read_failure" target="_top">ER_SLAVE_RELAY_LOG_READ_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_relay_log_write_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1595, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_relay_log_write_failure" target="_top">ER_SLAVE_RELAY_LOG_WRITE_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_create_event_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1596, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_create_event_failure" target="_top">ER_SLAVE_CREATE_EVENT_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_master_com_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1597, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_master_com_failure" target="_top">ER_SLAVE_MASTER_COM_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_logging_impossible</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1598, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_logging_impossible" target="_top">ER_BINLOG_LOGGING_IMPOSSIBLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_no_creation_ctx</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1599, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_no_creation_ctx" target="_top">ER_VIEW_NO_CREATION_CTX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_view_invalid_creation_ctx</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1600, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_view_invalid_creation_ctx" target="_top">ER_VIEW_INVALID_CREATION_CTX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sr_invalid_creation_ctx</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1601, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sr_invalid_creation_ctx" target="_top">ER_SR_INVALID_CREATION_CTX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_corrupted_file</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1602, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_corrupted_file" target="_top">ER_TRG_CORRUPTED_FILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_no_creation_ctx</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1603, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_no_creation_ctx" target="_top">ER_TRG_NO_CREATION_CTX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_invalid_creation_ctx</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1604, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_invalid_creation_ctx" target="_top">ER_TRG_INVALID_CREATION_CTX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_event_invalid_creation_ctx</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1605, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_event_invalid_creation_ctx" target="_top">ER_EVENT_INVALID_CREATION_CTX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_trg_cant_open_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1606, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_trg_cant_open_table" target="_top">ER_TRG_CANT_OPEN_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_create_sroutine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1607, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_create_sroutine" target="_top">ER_CANT_CREATE_SROUTINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_format_description_event_before_binlog_statement</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1609, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_format_description_event_before_binlog_statement" target="_top">ER_NO_FORMAT_DESCRIPTION_EVENT_BEFORE_BINLOG_STATEMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_corrupt_event</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1610, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_corrupt_event" target="_top">ER_SLAVE_CORRUPT_EVENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_log_purge_no_file</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1612, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_log_purge_no_file" target="_top">ER_LOG_PURGE_NO_FILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_xa_rbtimeout</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1613, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_xa_rbtimeout" target="_top">ER_XA_RBTIMEOUT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_xa_rbdeadlock</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1614, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_xa_rbdeadlock" target="_top">ER_XA_RBDEADLOCK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_need_reprepare</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1615, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_need_reprepare" target="_top">ER_NEED_REPREPARE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_delayed_not_supported</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1616, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_delayed_not_supported" target="_top">ER_DELAYED_NOT_SUPPORTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">warn_no_master_info</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1617, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_warn_no_master_info" target="_top">WARN_NO_MASTER_INFO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">warn_option_ignored</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1618, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_warn_option_ignored" target="_top">WARN_OPTION_IGNORED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_plugin_delete_builtin</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1619, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_plugin_delete_builtin" target="_top">ER_PLUGIN_DELETE_BUILTIN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">warn_plugin_busy</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1620, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_warn_plugin_busy" target="_top">WARN_PLUGIN_BUSY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_variable_is_readonly</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1621, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_variable_is_readonly" target="_top">ER_VARIABLE_IS_READONLY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_engine_transaction_rollback</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1622, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_engine_transaction_rollback" target="_top">ER_WARN_ENGINE_TRANSACTION_ROLLBACK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_heartbeat_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1623, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_heartbeat_failure" target="_top">ER_SLAVE_HEARTBEAT_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_heartbeat_value_out_of_range</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1624, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_heartbeat_value_out_of_range" target="_top">ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_conflict_fn_parse_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1626, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_conflict_fn_parse_error" target="_top">ER_CONFLICT_FN_PARSE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_exceptions_write_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1627, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_exceptions_write_error" target="_top">ER_EXCEPTIONS_WRITE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_long_table_comment</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1628, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_long_table_comment" target="_top">ER_TOO_LONG_TABLE_COMMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_long_field_comment</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1629, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_long_field_comment" target="_top">ER_TOO_LONG_FIELD_COMMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_func_inexistent_name_collision</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1630, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_func_inexistent_name_collision" target="_top">ER_FUNC_INEXISTENT_NAME_COLLISION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_database_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1631, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_database_name" target="_top">ER_DATABASE_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1632, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_name" target="_top">ER_TABLE_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1633, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_name" target="_top">ER_PARTITION_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_subpartition_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1634, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_subpartition_name" target="_top">ER_SUBPARTITION_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_temporary_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1635, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_temporary_name" target="_top">ER_TEMPORARY_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_renamed_name</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1636, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_renamed_name" target="_top">ER_RENAMED_NAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_concurrent_trxs</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1637, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_concurrent_trxs" target="_top">ER_TOO_MANY_CONCURRENT_TRXS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">warn_non_ascii_separator_not_implemented</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1638, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_warn_non_ascii_separator_not_implemented" target="_top">WARN_NON_ASCII_SEPARATOR_NOT_IMPLEMENTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_debug_sync_timeout</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1639, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_debug_sync_timeout" target="_top">ER_DEBUG_SYNC_TIMEOUT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_debug_sync_hit_limit</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1640, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_debug_sync_hit_limit" target="_top">ER_DEBUG_SYNC_HIT_LIMIT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_signal_set</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1641, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_signal_set" target="_top">ER_DUP_SIGNAL_SET</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_signal_warn</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1642, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_signal_warn" target="_top">ER_SIGNAL_WARN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_signal_not_found</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1643, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_signal_not_found" target="_top">ER_SIGNAL_NOT_FOUND</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_signal_exception</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1644, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_signal_exception" target="_top">ER_SIGNAL_EXCEPTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_resignal_without_active_handler</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1645, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_resignal_without_active_handler" target="_top">ER_RESIGNAL_WITHOUT_ACTIVE_HANDLER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_signal_bad_condition_type</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1646, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_signal_bad_condition_type" target="_top">ER_SIGNAL_BAD_CONDITION_TYPE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">warn_cond_item_truncated</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1647, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_warn_cond_item_truncated" target="_top">WARN_COND_ITEM_TRUNCATED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cond_item_too_long</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1648, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cond_item_too_long" target="_top">ER_COND_ITEM_TOO_LONG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_locale</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1649, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_locale" target="_top">ER_UNKNOWN_LOCALE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_ignore_server_ids</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1650, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_ignore_server_ids" target="_top">ER_SLAVE_IGNORE_SERVER_IDS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_query_cache_disabled</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1651, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_query_cache_disabled" target="_top">ER_QUERY_CACHE_DISABLED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_same_name_partition_field</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1652, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_same_name_partition_field" target="_top">ER_SAME_NAME_PARTITION_FIELD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_column_list_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1653, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_column_list_error" target="_top">ER_PARTITION_COLUMN_LIST_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_type_column_value_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1654, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_type_column_value_error" target="_top">ER_WRONG_TYPE_COLUMN_VALUE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_partition_func_fields_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1655, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_partition_func_fields_error" target="_top">ER_TOO_MANY_PARTITION_FUNC_FIELDS_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_maxvalue_in_values_in</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1656, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_maxvalue_in_values_in" target="_top">ER_MAXVALUE_IN_VALUES_IN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_many_values_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1657, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_many_values_error" target="_top">ER_TOO_MANY_VALUES_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_row_single_partition_field_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1658, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_row_single_partition_field_error" target="_top">ER_ROW_SINGLE_PARTITION_FIELD_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_field_type_not_allowed_as_partition_field</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1659, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_field_type_not_allowed_as_partition_field" target="_top">ER_FIELD_TYPE_NOT_ALLOWED_AS_PARTITION_FIELD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_fields_too_long</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1660, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_fields_too_long" target="_top">ER_PARTITION_FIELDS_TOO_LONG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_row_engine_and_stmt_engine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1661, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_row_engine_and_stmt_engine" target="_top">ER_BINLOG_ROW_ENGINE_AND_STMT_ENGINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_row_mode_and_stmt_engine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1662, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_row_mode_and_stmt_engine" target="_top">ER_BINLOG_ROW_MODE_AND_STMT_ENGINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_and_stmt_engine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1663, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_and_stmt_engine" target="_top">ER_BINLOG_UNSAFE_AND_STMT_ENGINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_row_injection_and_stmt_engine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1664, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_row_injection_and_stmt_engine" target="_top">ER_BINLOG_ROW_INJECTION_AND_STMT_ENGINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_stmt_mode_and_row_engine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1665, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_stmt_mode_and_row_engine" target="_top">ER_BINLOG_STMT_MODE_AND_ROW_ENGINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_row_injection_and_stmt_mode</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1666, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_row_injection_and_stmt_mode" target="_top">ER_BINLOG_ROW_INJECTION_AND_STMT_MODE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_multiple_engines_and_self_logging_engine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1667, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_multiple_engines_and_self_logging_engine" target="_top">ER_BINLOG_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_limit</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1668, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_limit" target="_top">ER_BINLOG_UNSAFE_LIMIT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_system_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1670, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_system_table" target="_top">ER_BINLOG_UNSAFE_SYSTEM_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_autoinc_columns</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1671, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_autoinc_columns" target="_top">ER_BINLOG_UNSAFE_AUTOINC_COLUMNS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_udf</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1672, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_udf" target="_top">ER_BINLOG_UNSAFE_UDF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_system_variable</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1673, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_system_variable" target="_top">ER_BINLOG_UNSAFE_SYSTEM_VARIABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_system_function</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1674, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_system_function" target="_top">ER_BINLOG_UNSAFE_SYSTEM_FUNCTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_nontrans_after_trans</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1675, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_nontrans_after_trans" target="_top">ER_BINLOG_UNSAFE_NONTRANS_AFTER_TRANS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_message_and_statement</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1676, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_message_and_statement" target="_top">ER_MESSAGE_AND_STATEMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_conversion_failed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1677, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_conversion_failed" target="_top">ER_SLAVE_CONVERSION_FAILED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_cant_create_conversion</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1678, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_cant_create_conversion" target="_top">ER_SLAVE_CANT_CREATE_CONVERSION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_inside_transaction_prevents_switch_binlog_format</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1679, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_inside_transaction_prevents_switch_binlog_format" target="_top">ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_FORMAT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_path_length</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1680, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_path_length" target="_top">ER_PATH_LENGTH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_deprecated_syntax_no_replacement</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1681, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_deprecated_syntax_no_replacement" target="_top">ER_WARN_DEPRECATED_SYNTAX_NO_REPLACEMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_native_table_structure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1682, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_native_table_structure" target="_top">ER_WRONG_NATIVE_TABLE_STRUCTURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_perfschema_usage</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1683, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_perfschema_usage" target="_top">ER_WRONG_PERFSCHEMA_USAGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_i_s_skipped_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1684, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_i_s_skipped_table" target="_top">ER_WARN_I_S_SKIPPED_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_inside_transaction_prevents_switch_binlog_direct</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1685, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_inside_transaction_prevents_switch_binlog_direct" target="_top">ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_DIRECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stored_function_prevents_switch_binlog_direct</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1686, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stored_function_prevents_switch_binlog_direct" target="_top">ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_DIRECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_spatial_must_have_geom_col</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1687, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_spatial_must_have_geom_col" target="_top">ER_SPATIAL_MUST_HAVE_GEOM_COL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_long_index_comment</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1688, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_long_index_comment" target="_top">ER_TOO_LONG_INDEX_COMMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_lock_aborted</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1689, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_lock_aborted" target="_top">ER_LOCK_ABORTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_data_out_of_range</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1690, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_data_out_of_range" target="_top">ER_DATA_OUT_OF_RANGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_wrong_spvar_type_in_limit</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1691, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_wrong_spvar_type_in_limit" target="_top">ER_WRONG_SPVAR_TYPE_IN_LIMIT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_multiple_engines_and_self_logging_engine</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1692, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_multiple_engines_and_self_logging_engine" target="_top">ER_BINLOG_UNSAFE_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_mixed_statement</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1693, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_mixed_statement" target="_top">ER_BINLOG_UNSAFE_MIXED_STATEMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_inside_transaction_prevents_switch_sql_log_bin</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1694, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_inside_transaction_prevents_switch_sql_log_bin" target="_top">ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_SQL_LOG_BIN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stored_function_prevents_switch_sql_log_bin</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1695, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stored_function_prevents_switch_sql_log_bin" target="_top">ER_STORED_FUNCTION_PREVENTS_SWITCH_SQL_LOG_BIN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_failed_read_from_par_file</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1696, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_failed_read_from_par_file" target="_top">ER_FAILED_READ_FROM_PAR_FILE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_values_is_not_int_type_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1697, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_values_is_not_int_type_error" target="_top">ER_VALUES_IS_NOT_INT_TYPE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_access_denied_no_password_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1698, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_access_denied_no_password_error" target="_top">ER_ACCESS_DENIED_NO_PASSWORD_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_set_password_auth_plugin</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1699, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_set_password_auth_plugin" target="_top">ER_SET_PASSWORD_AUTH_PLUGIN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_grant_plugin_user_exists</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1700, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_grant_plugin_user_exists" target="_top">ER_GRANT_PLUGIN_USER_EXISTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_truncate_illegal_fk</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1701, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_truncate_illegal_fk" target="_top">ER_TRUNCATE_ILLEGAL_FK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_plugin_is_permanent</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1702, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_plugin_is_permanent" target="_top">ER_PLUGIN_IS_PERMANENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_heartbeat_value_out_of_range_min</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1703, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_heartbeat_value_out_of_range_min" target="_top">ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MIN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_heartbeat_value_out_of_range_max</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1704, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_heartbeat_value_out_of_range_max" target="_top">ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MAX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stmt_cache_full</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1705, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stmt_cache_full" target="_top">ER_STMT_CACHE_FULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_multi_update_key_conflict</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1706, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_multi_update_key_conflict" target="_top">ER_MULTI_UPDATE_KEY_CONFLICT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_needs_rebuild</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1707, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_needs_rebuild" target="_top">ER_TABLE_NEEDS_REBUILD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">warn_option_below_limit</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1708, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_warn_option_below_limit" target="_top">WARN_OPTION_BELOW_LIMIT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_index_column_too_long</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1709, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_index_column_too_long" target="_top">ER_INDEX_COLUMN_TOO_LONG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_in_trigger_body</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1710, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_in_trigger_body" target="_top">ER_ERROR_IN_TRIGGER_BODY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_error_in_unknown_trigger_body</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1711, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_error_in_unknown_trigger_body" target="_top">ER_ERROR_IN_UNKNOWN_TRIGGER_BODY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_index_corrupt</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1712, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_index_corrupt" target="_top">ER_INDEX_CORRUPT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_undo_record_too_big</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1713, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_undo_record_too_big" target="_top">ER_UNDO_RECORD_TOO_BIG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_insert_ignore_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1714, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_insert_ignore_select" target="_top">ER_BINLOG_UNSAFE_INSERT_IGNORE_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_insert_select_update</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1715, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_insert_select_update" target="_top">ER_BINLOG_UNSAFE_INSERT_SELECT_UPDATE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_replace_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1716, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_replace_select" target="_top">ER_BINLOG_UNSAFE_REPLACE_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_create_ignore_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1717, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_create_ignore_select" target="_top">ER_BINLOG_UNSAFE_CREATE_IGNORE_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_create_replace_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1718, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_create_replace_select" target="_top">ER_BINLOG_UNSAFE_CREATE_REPLACE_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_update_ignore</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1719, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_update_ignore" target="_top">ER_BINLOG_UNSAFE_UPDATE_IGNORE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_write_autoinc_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1722, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_write_autoinc_select" target="_top">ER_BINLOG_UNSAFE_WRITE_AUTOINC_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_create_select_autoinc</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1723, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_create_select_autoinc" target="_top">ER_BINLOG_UNSAFE_CREATE_SELECT_AUTOINC</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_insert_two_keys</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1724, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_insert_two_keys" target="_top">ER_BINLOG_UNSAFE_INSERT_TWO_KEYS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_unsafe_autoinc_not_first</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1727, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_unsafe_autoinc_not_first" target="_top">ER_BINLOG_UNSAFE_AUTOINC_NOT_FIRST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cannot_load_from_table_v2</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1728, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cannot_load_from_table_v2" target="_top">ER_CANNOT_LOAD_FROM_TABLE_V2</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_master_delay_value_out_of_range</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1729, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_master_delay_value_out_of_range" target="_top">ER_MASTER_DELAY_VALUE_OUT_OF_RANGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_only_fd_and_rbr_events_allowed_in_binlog_statement</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1730, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_only_fd_and_rbr_events_allowed_in_binlog_statement" target="_top">ER_ONLY_FD_AND_RBR_EVENTS_ALLOWED_IN_BINLOG_STATEMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_exchange_different_option</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1731, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_exchange_different_option" target="_top">ER_PARTITION_EXCHANGE_DIFFERENT_OPTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_exchange_part_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1732, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_exchange_part_table" target="_top">ER_PARTITION_EXCHANGE_PART_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_exchange_temp_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1733, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_exchange_temp_table" target="_top">ER_PARTITION_EXCHANGE_TEMP_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_instead_of_subpartition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1734, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_instead_of_subpartition" target="_top">ER_PARTITION_INSTEAD_OF_SUBPARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1735, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_partition" target="_top">ER_UNKNOWN_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_tables_different_metadata</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1736, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_tables_different_metadata" target="_top">ER_TABLES_DIFFERENT_METADATA</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_row_does_not_match_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1737, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_row_does_not_match_partition" target="_top">ER_ROW_DOES_NOT_MATCH_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_cache_size_greater_than_max</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1738, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_cache_size_greater_than_max" target="_top">ER_BINLOG_CACHE_SIZE_GREATER_THAN_MAX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_index_not_applicable</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1739, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_index_not_applicable" target="_top">ER_WARN_INDEX_NOT_APPLICABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_exchange_foreign_key</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1740, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_exchange_foreign_key" target="_top">ER_PARTITION_EXCHANGE_FOREIGN_KEY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_no_such_key_value</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1741, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_no_such_key_value" target="_top">ER_NO_SUCH_KEY_VALUE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_network_read_event_checksum_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1743, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_network_read_event_checksum_failure" target="_top">ER_NETWORK_READ_EVENT_CHECKSUM_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_read_event_checksum_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1744, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_read_event_checksum_failure" target="_top">ER_BINLOG_READ_EVENT_CHECKSUM_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_stmt_cache_size_greater_than_max</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1745, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_stmt_cache_size_greater_than_max" target="_top">ER_BINLOG_STMT_CACHE_SIZE_GREATER_THAN_MAX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_update_table_in_create_table_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1746, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_update_table_in_create_table_select" target="_top">ER_CANT_UPDATE_TABLE_IN_CREATE_TABLE_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_partition_clause_on_nonpartitioned</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1747, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_partition_clause_on_nonpartitioned" target="_top">ER_PARTITION_CLAUSE_ON_NONPARTITIONED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_row_does_not_match_given_partition_set</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1748, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_row_does_not_match_given_partition_set" target="_top">ER_ROW_DOES_NOT_MATCH_GIVEN_PARTITION_SET</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_change_rpl_info_repository_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1750, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_change_rpl_info_repository_failure" target="_top">ER_CHANGE_RPL_INFO_REPOSITORY_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warning_not_complete_rollback_with_created_temp_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1751, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warning_not_complete_rollback_with_created_temp_table" target="_top">ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_CREATED_TEMP_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warning_not_complete_rollback_with_dropped_temp_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1752, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warning_not_complete_rollback_with_dropped_temp_table" target="_top">ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_DROPPED_TEMP_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mts_feature_is_not_supported</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1753, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mts_feature_is_not_supported" target="_top">ER_MTS_FEATURE_IS_NOT_SUPPORTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mts_updated_dbs_greater_max</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1754, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mts_updated_dbs_greater_max" target="_top">ER_MTS_UPDATED_DBS_GREATER_MAX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mts_cant_parallel</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1755, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mts_cant_parallel" target="_top">ER_MTS_CANT_PARALLEL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mts_inconsistent_data</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1756, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mts_inconsistent_data" target="_top">ER_MTS_INCONSISTENT_DATA</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fulltext_not_supported_with_partitioning</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1757, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fulltext_not_supported_with_partitioning" target="_top">ER_FULLTEXT_NOT_SUPPORTED_WITH_PARTITIONING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_da_invalid_condition_number</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1758, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_da_invalid_condition_number" target="_top">ER_DA_INVALID_CONDITION_NUMBER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_insecure_plain_text</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1759, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_insecure_plain_text" target="_top">ER_INSECURE_PLAIN_TEXT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_insecure_change_master</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1760, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_insecure_change_master" target="_top">ER_INSECURE_CHANGE_MASTER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_foreign_duplicate_key_with_child_info</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1761, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_foreign_duplicate_key_with_child_info" target="_top">ER_FOREIGN_DUPLICATE_KEY_WITH_CHILD_INFO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_foreign_duplicate_key_without_child_info</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1762, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_foreign_duplicate_key_without_child_info" target="_top">ER_FOREIGN_DUPLICATE_KEY_WITHOUT_CHILD_INFO</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sqlthread_with_secure_slave</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1763, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sqlthread_with_secure_slave" target="_top">ER_SQLTHREAD_WITH_SECURE_SLAVE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_has_no_ft</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1764, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_has_no_ft" target="_top">ER_TABLE_HAS_NO_FT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_variable_not_settable_in_sf_or_trigger</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1765, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_variable_not_settable_in_sf_or_trigger" target="_top">ER_VARIABLE_NOT_SETTABLE_IN_SF_OR_TRIGGER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_variable_not_settable_in_transaction</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1766, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_variable_not_settable_in_transaction" target="_top">ER_VARIABLE_NOT_SETTABLE_IN_TRANSACTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gtid_next_is_not_in_gtid_next_list</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1767, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gtid_next_is_not_in_gtid_next_list" target="_top">ER_GTID_NEXT_IS_NOT_IN_GTID_NEXT_LIST</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_set_statement_cannot_invoke_function</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1769, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_set_statement_cannot_invoke_function" target="_top">ER_SET_STATEMENT_CANNOT_INVOKE_FUNCTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gtid_next_cant_be_automatic_if_gtid_next_list_is_non_null</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1770, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gtid_next_cant_be_automatic_if_gtid_next_list_is_non_null" target="_top">ER_GTID_NEXT_CANT_BE_AUTOMATIC_IF_GTID_NEXT_LIST_IS_NON_NULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_skipping_logged_transaction</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1771, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_skipping_logged_transaction" target="_top">ER_SKIPPING_LOGGED_TRANSACTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_malformed_gtid_set_specification</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1772, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_malformed_gtid_set_specification" target="_top">ER_MALFORMED_GTID_SET_SPECIFICATION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_malformed_gtid_set_encoding</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1773, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_malformed_gtid_set_encoding" target="_top">ER_MALFORMED_GTID_SET_ENCODING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_malformed_gtid_specification</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1774, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_malformed_gtid_specification" target="_top">ER_MALFORMED_GTID_SPECIFICATION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gno_exhausted</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1775, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gno_exhausted" target="_top">ER_GNO_EXHAUSTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_bad_slave_auto_position</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1776, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_bad_slave_auto_position" target="_top">ER_BAD_SLAVE_AUTO_POSITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_do_implicit_commit_in_trx_when_gtid_next_is_set</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1778, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_do_implicit_commit_in_trx_when_gtid_next_is_set" target="_top">ER_CANT_DO_IMPLICIT_COMMIT_IN_TRX_WHEN_GTID_NEXT_IS_SET</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gtid_mode_requires_binlog</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1780, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gtid_mode_requires_binlog" target="_top">ER_GTID_MODE_REQUIRES_BINLOG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_set_gtid_next_to_gtid_when_gtid_mode_is_off</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1781, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_set_gtid_next_to_gtid_when_gtid_mode_is_off" target="_top">ER_CANT_SET_GTID_NEXT_TO_GTID_WHEN_GTID_MODE_IS_OFF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_set_gtid_next_to_anonymous_when_gtid_mode_is_on</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1782, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_set_gtid_next_to_anonymous_when_gtid_mode_is_on" target="_top">ER_CANT_SET_GTID_NEXT_TO_ANONYMOUS_WHEN_GTID_MODE_IS_ON</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_set_gtid_next_list_to_non_null_when_gtid_mode_is_off</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1783, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_set_gtid_next_list_to_non_null_when_gtid_mode_is_off" target="_top">ER_CANT_SET_GTID_NEXT_LIST_TO_NON_NULL_WHEN_GTID_MODE_IS_OFF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gtid_unsafe_non_transactional_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1785, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gtid_unsafe_non_transactional_table" target="_top">ER_GTID_UNSAFE_NON_TRANSACTIONAL_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gtid_unsafe_create_select</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1786, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gtid_unsafe_create_select" target="_top">ER_GTID_UNSAFE_CREATE_SELECT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gtid_unsafe_create_drop_temporary_table_in_transaction</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1787, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gtid_unsafe_create_drop_temporary_table_in_transaction" target="_top">ER_GTID_UNSAFE_CREATE_DROP_TEMPORARY_TABLE_IN_TRANSACTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gtid_mode_can_only_change_one_step_at_a_time</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1788, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gtid_mode_can_only_change_one_step_at_a_time" target="_top">ER_GTID_MODE_CAN_ONLY_CHANGE_ONE_STEP_AT_A_TIME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_master_has_purged_required_gtids</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1789, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_master_has_purged_required_gtids" target="_top">ER_MASTER_HAS_PURGED_REQUIRED_GTIDS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_set_gtid_next_when_owning_gtid</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1790, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_set_gtid_next_when_owning_gtid" target="_top">ER_CANT_SET_GTID_NEXT_WHEN_OWNING_GTID</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_explain_format</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1791, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_explain_format" target="_top">ER_UNKNOWN_EXPLAIN_FORMAT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_execute_in_read_only_transaction</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1792, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_execute_in_read_only_transaction" target="_top">ER_CANT_EXECUTE_IN_READ_ONLY_TRANSACTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_too_long_table_partition_comment</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1793, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_too_long_table_partition_comment" target="_top">ER_TOO_LONG_TABLE_PARTITION_COMMENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_configuration</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1794, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_configuration" target="_top">ER_SLAVE_CONFIGURATION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_ft_limit</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1795, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_ft_limit" target="_top">ER_INNODB_FT_LIMIT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_no_ft_temp_table</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1796, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_no_ft_temp_table" target="_top">ER_INNODB_NO_FT_TEMP_TABLE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_ft_wrong_docid_column</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1797, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_ft_wrong_docid_column" target="_top">ER_INNODB_FT_WRONG_DOCID_COLUMN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_ft_wrong_docid_index</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1798, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_ft_wrong_docid_index" target="_top">ER_INNODB_FT_WRONG_DOCID_INDEX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_online_log_too_big</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1799, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_online_log_too_big" target="_top">ER_INNODB_ONLINE_LOG_TOO_BIG</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_alter_algorithm</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1800, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_alter_algorithm" target="_top">ER_UNKNOWN_ALTER_ALGORITHM</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_unknown_alter_lock</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1801, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_unknown_alter_lock" target="_top">ER_UNKNOWN_ALTER_LOCK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mts_change_master_cant_run_with_gaps</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1802, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mts_change_master_cant_run_with_gaps" target="_top">ER_MTS_CHANGE_MASTER_CANT_RUN_WITH_GAPS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mts_recovery_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1803, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mts_recovery_failure" target="_top">ER_MTS_RECOVERY_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mts_reset_workers</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1804, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mts_reset_workers" target="_top">ER_MTS_RESET_WORKERS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_col_count_doesnt_match_corrupted_v2</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1805, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_col_count_doesnt_match_corrupted_v2" target="_top">ER_COL_COUNT_DOESNT_MATCH_CORRUPTED_V2</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_silent_retry_transaction</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1806, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_silent_retry_transaction" target="_top">ER_SLAVE_SILENT_RETRY_TRANSACTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_schema_mismatch</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1808, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_schema_mismatch" target="_top">ER_TABLE_SCHEMA_MISMATCH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_in_system_tablespace</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1809, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_in_system_tablespace" target="_top">ER_TABLE_IN_SYSTEM_TABLESPACE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_io_read_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1810, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_io_read_error" target="_top">ER_IO_READ_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_io_write_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1811, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_io_write_error" target="_top">ER_IO_WRITE_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_tablespace_missing</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1812, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_tablespace_missing" target="_top">ER_TABLESPACE_MISSING</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_tablespace_exists</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1813, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_tablespace_exists" target="_top">ER_TABLESPACE_EXISTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_tablespace_discarded</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1814, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_tablespace_discarded" target="_top">ER_TABLESPACE_DISCARDED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_internal_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1815, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_internal_error" target="_top">ER_INTERNAL_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_import_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1816, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_import_error" target="_top">ER_INNODB_IMPORT_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_index_corrupt</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1817, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_index_corrupt" target="_top">ER_INNODB_INDEX_CORRUPT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_invalid_year_column_length</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1818, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_invalid_year_column_length" target="_top">ER_INVALID_YEAR_COLUMN_LENGTH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_not_valid_password</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1819, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_not_valid_password" target="_top">ER_NOT_VALID_PASSWORD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_must_change_password</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1820, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_must_change_password" target="_top">ER_MUST_CHANGE_PASSWORD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_no_index_child</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1821, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_no_index_child" target="_top">ER_FK_NO_INDEX_CHILD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_no_index_parent</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1822, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_no_index_parent" target="_top">ER_FK_NO_INDEX_PARENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_fail_add_system</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1823, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_fail_add_system" target="_top">ER_FK_FAIL_ADD_SYSTEM</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_cannot_open_parent</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1824, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_cannot_open_parent" target="_top">ER_FK_CANNOT_OPEN_PARENT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_incorrect_option</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1825, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_incorrect_option" target="_top">ER_FK_INCORRECT_OPTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_password_format</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1827, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_password_format" target="_top">ER_PASSWORD_FORMAT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_column_cannot_drop</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1828, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_column_cannot_drop" target="_top">ER_FK_COLUMN_CANNOT_DROP</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_column_cannot_drop_child</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1829, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_column_cannot_drop_child" target="_top">ER_FK_COLUMN_CANNOT_DROP_CHILD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_column_not_null</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1830, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_column_not_null" target="_top">ER_FK_COLUMN_NOT_NULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_index</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1831, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_index" target="_top">ER_DUP_INDEX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_column_cannot_change</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1832, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_column_cannot_change" target="_top">ER_FK_COLUMN_CANNOT_CHANGE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_fk_column_cannot_change_child</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1833, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_fk_column_cannot_change_child" target="_top">ER_FK_COLUMN_CANNOT_CHANGE_CHILD</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_malformed_packet</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1835, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_malformed_packet" target="_top">ER_MALFORMED_PACKET</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_read_only_mode</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1836, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_read_only_mode" target="_top">ER_READ_ONLY_MODE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_variable_not_settable_in_sp</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1838, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_variable_not_settable_in_sp" target="_top">ER_VARIABLE_NOT_SETTABLE_IN_SP</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_set_gtid_purged_when_gtid_mode_is_off</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1839, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_set_gtid_purged_when_gtid_mode_is_off" target="_top">ER_CANT_SET_GTID_PURGED_WHEN_GTID_MODE_IS_OFF</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_set_gtid_purged_when_gtid_executed_is_not_empty</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1840, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_set_gtid_purged_when_gtid_executed_is_not_empty" target="_top">ER_CANT_SET_GTID_PURGED_WHEN_GTID_EXECUTED_IS_NOT_EMPTY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_cant_set_gtid_purged_when_owned_gtids_is_not_empty</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1841, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_cant_set_gtid_purged_when_owned_gtids_is_not_empty" target="_top">ER_CANT_SET_GTID_PURGED_WHEN_OWNED_GTIDS_IS_NOT_EMPTY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gtid_purged_was_changed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1842, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gtid_purged_was_changed" target="_top">ER_GTID_PURGED_WAS_CHANGED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_gtid_executed_was_changed</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1843, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_gtid_executed_was_changed" target="_top">ER_GTID_EXECUTED_WAS_CHANGED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_stmt_mode_and_no_repl_tables</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1844, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_stmt_mode_and_no_repl_tables" target="_top">ER_BINLOG_STMT_MODE_AND_NO_REPL_TABLES</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1845, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1846, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_copy</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1847, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_copy" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COPY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1848, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_partition" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_fk_rename</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1849, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_fk_rename" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_RENAME</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_column_type</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1850, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_column_type" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COLUMN_TYPE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_fk_check</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1851, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_fk_check" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_CHECK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_nopk</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1853, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_nopk" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOPK</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_autoinc</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1854, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_autoinc" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_AUTOINC</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_hidden_fts</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1855, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_hidden_fts" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_HIDDEN_FTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_change_fts</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1856, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_change_fts" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_CHANGE_FTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_fts</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1857, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_fts" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FTS</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_sql_slave_skip_counter_not_settable_in_gtid_mode</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1858, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_sql_slave_skip_counter_not_settable_in_gtid_mode" target="_top">ER_SQL_SLAVE_SKIP_COUNTER_NOT_SETTABLE_IN_GTID_MODE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_dup_unknown_in_index</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1859, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_dup_unknown_in_index" target="_top">ER_DUP_UNKNOWN_IN_INDEX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_ident_causes_too_long_path</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1860, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_ident_causes_too_long_path" target="_top">ER_IDENT_CAUSES_TOO_LONG_PATH</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_alter_operation_not_supported_reason_not_null</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1861, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_alter_operation_not_supported_reason_not_null" target="_top">ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOT_NULL</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_must_change_password_login</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1862, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_must_change_password_login" target="_top">ER_MUST_CHANGE_PASSWORD_LOGIN</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_row_in_wrong_partition</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1863, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_row_in_wrong_partition" target="_top">ER_ROW_IN_WRONG_PARTITION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_mts_event_bigger_pending_jobs_size_max</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1864, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_mts_event_bigger_pending_jobs_size_max" target="_top">ER_MTS_EVENT_BIGGER_PENDING_JOBS_SIZE_MAX</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_no_ft_uses_parser</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1865, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_no_ft_uses_parser" target="_top">ER_INNODB_NO_FT_USES_PARSER</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_binlog_logical_corruption</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1866, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_binlog_logical_corruption" target="_top">ER_BINLOG_LOGICAL_CORRUPTION</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_purge_log_in_use</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1867, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_purge_log_in_use" target="_top">ER_WARN_PURGE_LOG_IN_USE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_warn_purge_log_is_active</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1868, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_warn_purge_log_is_active" target="_top">ER_WARN_PURGE_LOG_IS_ACTIVE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_auto_increment_conflict</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1869, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_auto_increment_conflict" target="_top">ER_AUTO_INCREMENT_CONFLICT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">warn_on_blockhole_in_rbr</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1870, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_warn_on_blockhole_in_rbr" target="_top">WARN_ON_BLOCKHOLE_IN_RBR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_mi_init_repository</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1871, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_mi_init_repository" target="_top">ER_SLAVE_MI_INIT_REPOSITORY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_slave_rli_init_repository</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1872, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_slave_rli_init_repository" target="_top">ER_SLAVE_RLI_INIT_REPOSITORY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_access_denied_change_user_error</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1873, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_access_denied_change_user_error" target="_top">ER_ACCESS_DENIED_CHANGE_USER_ERROR</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_read_only</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1874, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_read_only" target="_top">ER_INNODB_READ_ONLY</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stop_slave_sql_thread_timeout</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1875, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stop_slave_sql_thread_timeout" target="_top">ER_STOP_SLAVE_SQL_THREAD_TIMEOUT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_stop_slave_io_thread_timeout</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1876, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_stop_slave_io_thread_timeout" target="_top">ER_STOP_SLAVE_IO_THREAD_TIMEOUT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_table_corrupt</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1877, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_table_corrupt" target="_top">ER_TABLE_CORRUPT</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_temp_file_write_failure</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1878, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_temp_file_write_failure" target="_top">ER_TEMP_FILE_WRITE_FAILURE</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">er_innodb_ft_aux_not_hex_id</span></code>
                </p>
              </td>
<td>
                <p>
                  Common server error. Error number: 1879, symbol: <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html#error_er_innodb_ft_aux_not_hex_id" target="_top">ER_INNODB_FT_AUX_NOT_HEX_ID</a>.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h5>
<a name="mysql.ref.boost__mysql__common_server_errc.h2"></a>
        <span class="phrase"><a name="mysql.ref.boost__mysql__common_server_errc.description"></a></span><a class="link" href="boost__mysql__common_server_errc.html#mysql.ref.boost__mysql__common_server_errc.description">Description</a>
      </h5>
<p>
        The numeric value and semantics match the ones described in the MySQL documentation.
        For more info, consult the error reference for <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html" target="_top">MySQL
        8.0</a>, <a href="https://dev.mysql.com/doc/mysql-errors/5.7/en/server-error-reference.html" target="_top">MySQL
        5.7</a>, <a href="https://mariadb.com/kb/en/mariadb-error-codes/" target="_top">MariaDB</a>.
      </p>
<p>
        Convenience header <code class="literal">&lt;<a href="https://github.com/boostorg/mysql/blob/master/include/boost/mysql.hpp" target="_top">boost/mysql.hpp</a>&gt;</code>
      </p>
</div>
<div class="copyright-footer">Copyright © 2019-2023 Ruben Perez<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost__mysql__field_kind.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../pt01.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost__mysql__column_type.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
