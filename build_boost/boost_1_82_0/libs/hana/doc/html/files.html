<!--
Copyright <PERSON> 2013-2017
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
-->
<!-- boost-no-inspect -->
<!-- HTML header for doxygen *******-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<title>Boost.Hana: Headers</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
// Copyright Louis Dionne 2013-2017
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
MathJax.Hub.Config({
    "HTML-CSS": {
        linebreaks: {
            automatic: true,
            width: "75% container"
        }
    }
});
</script>
<script type="text/javascript" async="async" src="https://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<!-- Additional javascript for drawing charts. -->
<script type="text/javascript" src="highcharts.js"></script>
<script type="text/javascript" src="highcharts-data.js"></script>
<script type="text/javascript" src="highcharts-exporting.js"></script>
<script type="text/javascript" src="chart.js"></script>
<script type="text/javascript" src="hana.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="Boost.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">Boost.Hana
   &#160;<span id="projectnumber">1.7.1</span>
   </div>
   <div id="projectbrief">Your standard library for metaprogramming</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('files.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Headers</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"> This is a list of all the headers provided by the library.</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span><span onclick="javascript:toggleLevel(3);">3</span><span onclick="javascript:toggleLevel(4);">4</span><span onclick="javascript:toggleLevel(5);">5</span><span onclick="javascript:toggleLevel(6);">6</span><span onclick="javascript:toggleLevel(7);">7</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9660;</span><span id="img_0_" class="iconfopen" onclick="toggleFolder('0_')">&#160;</span><a class="el" href="dir_1878a3f4746a95c6aad317458cc7ef80.html" target="_self">boost</a></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_0_" class="arrow" onclick="toggleFolder('0_0_')">&#9660;</span><span id="img_0_0_" class="iconfopen" onclick="toggleFolder('0_0_')">&#160;</span><a class="el" href="dir_daf74c896eae580804ddb7810f485dad.html" target="_self">hana</a></td><td class="desc"></td></tr>
<tr id="row_0_0_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_0_0_0_" class="arrow" onclick="toggleFolder('0_0_0_')">&#9660;</span><span id="img_0_0_0_" class="iconfopen" onclick="toggleFolder('0_0_0_')">&#160;</span><a class="el" href="dir_a6bcc969367a2b1bb27eb51c9ff4f3b4.html" target="_self">concept</a></td><td class="desc"></td></tr>
<tr id="row_0_0_0_0_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2applicative_8hpp.html" target="_self">applicative.hpp</a></td><td class="desc">Defines <code>boost::hana::Applicative</code> </td></tr>
<tr id="row_0_0_0_1_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2comonad_8hpp.html" target="_self">comonad.hpp</a></td><td class="desc">Defines <code>boost::hana::Comonad</code> </td></tr>
<tr id="row_0_0_0_2_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2comparable_8hpp.html" target="_self">comparable.hpp</a></td><td class="desc">Defines <code>boost::hana::Comparable</code> </td></tr>
<tr id="row_0_0_0_3_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2constant_8hpp.html" target="_self">constant.hpp</a></td><td class="desc">Defines <code>boost::hana::Constant</code> </td></tr>
<tr id="row_0_0_0_4_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2euclidean__ring_8hpp.html" target="_self">euclidean_ring.hpp</a></td><td class="desc">Defines <code>boost::hana::EuclideanRing</code> </td></tr>
<tr id="row_0_0_0_5_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2foldable_8hpp.html" target="_self">foldable.hpp</a></td><td class="desc">Defines <code>boost::hana::Foldable</code> </td></tr>
<tr id="row_0_0_0_6_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2functor_8hpp.html" target="_self">functor.hpp</a></td><td class="desc">Defines <code>boost::hana::Functor</code> </td></tr>
<tr id="row_0_0_0_7_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2group_8hpp.html" target="_self">group.hpp</a></td><td class="desc">Defines <code>boost::hana::Group</code> </td></tr>
<tr id="row_0_0_0_8_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2hashable_8hpp.html" target="_self">hashable.hpp</a></td><td class="desc">Defines <code>boost::hana::Hashable</code> </td></tr>
<tr id="row_0_0_0_9_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2integral__constant_8hpp.html" target="_self">integral_constant.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">boost::hana::IntegralConstant</a></code> </td></tr>
<tr id="row_0_0_0_10_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2iterable_8hpp.html" target="_self">iterable.hpp</a></td><td class="desc">Defines <code>boost::hana::Iterable</code> </td></tr>
<tr id="row_0_0_0_11_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2logical_8hpp.html" target="_self">logical.hpp</a></td><td class="desc">Defines <code>boost::hana::Logical</code> </td></tr>
<tr id="row_0_0_0_12_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2metafunction_8hpp.html" target="_self">metafunction.hpp</a></td><td class="desc">Defines <code>boost::hana::Metafunction</code> </td></tr>
<tr id="row_0_0_0_13_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2monad_8hpp.html" target="_self">monad.hpp</a></td><td class="desc">Defines <code>boost::hana::Monad</code> </td></tr>
<tr id="row_0_0_0_14_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2monad__plus_8hpp.html" target="_self">monad_plus.hpp</a></td><td class="desc">Defines <code>boost::hana::MonadPlus</code> </td></tr>
<tr id="row_0_0_0_15_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2monoid_8hpp.html" target="_self">monoid.hpp</a></td><td class="desc">Defines <code>boost::hana::Monoid</code> </td></tr>
<tr id="row_0_0_0_16_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2orderable_8hpp.html" target="_self">orderable.hpp</a></td><td class="desc">Defines <code>boost::hana::Orderable</code> </td></tr>
<tr id="row_0_0_0_17_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2product_8hpp.html" target="_self">product.hpp</a></td><td class="desc">Defines <code>boost::hana::Product</code> </td></tr>
<tr id="row_0_0_0_18_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2ring_8hpp.html" target="_self">ring.hpp</a></td><td class="desc">Defines <code>boost::hana::Ring</code> </td></tr>
<tr id="row_0_0_0_19_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2searchable_8hpp.html" target="_self">searchable.hpp</a></td><td class="desc">Defines <code>boost::hana::Searchable</code> </td></tr>
<tr id="row_0_0_0_20_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2sequence_8hpp.html" target="_self">sequence.hpp</a></td><td class="desc">Defines <code>boost::hana::Sequence</code> </td></tr>
<tr id="row_0_0_0_21_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_2struct_8hpp.html" target="_self">struct.hpp</a></td><td class="desc">Defines <code>boost::hana::Struct</code> </td></tr>
<tr id="row_0_0_1_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_0_0_1_" class="arrow" onclick="toggleFolder('0_0_1_')">&#9660;</span><span id="img_0_0_1_" class="iconfopen" onclick="toggleFolder('0_0_1_')">&#160;</span><a class="el" href="dir_579efcf19f0a51e7b529b5b94ff4dd6f.html" target="_self">core</a></td><td class="desc"></td></tr>
<tr id="row_0_0_1_0_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="core_2common_8hpp.html" target="_self">common.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1common.html" title="Metafunction returning the common data type between two data types.">boost::hana::common</a></code> and <code><a class="el" href="group__group-core.html#ga4da46c97755c0f430b063711b66ca05b" title="Alias to common&lt;T, U&gt;::type, provided for convenience.">boost::hana::common_t</a></code> </td></tr>
<tr id="row_0_0_1_1_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="core_2default_8hpp.html" target="_self">default.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1default__.html" title="Mark a tag-dispatched method implementation as a default implementation.">boost::hana::default_</a></code> and <code><a class="el" href="structboost_1_1hana_1_1is__default.html" title="Returns whether a tag-dispatched method implementation is a default implementation.">boost::hana::is_default</a></code> </td></tr>
<tr id="row_0_0_1_2_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="dispatch_8hpp.html" target="_self">dispatch.hpp</a></td><td class="desc">Includes all the headers needed to setup tag-dispatching </td></tr>
<tr id="row_0_0_1_3_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="core_2is__a_8hpp.html" target="_self">is_a.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-core.html#ga38cf78e1e3e262f7f1c71ddd9ca70cd9" title="Returns whether the tag of an object matches a given tag.">boost::hana::is_a</a></code> and <code><a class="el" href="group__group-core.html#ga7fdbde52f5fe384a816c6f39ff272df9" title="Equivalent to is_a; provided for consistency with the rules of the English language.">boost::hana::is_an</a></code> </td></tr>
<tr id="row_0_0_1_4_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="core_2make_8hpp.html" target="_self">make.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-core.html#ga1d92480f0af1029878e773dafa3e2f60" title="Create an object of the given tag with the given arguments.">boost::hana::make</a></code> </td></tr>
<tr id="row_0_0_1_5_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="core_2tag__of_8hpp.html" target="_self">tag_of.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1tag__of.html" title="Metafunction returning the tag associated to T.">boost::hana::tag_of</a></code> and <code><a class="el" href="group__group-core.html#ga686d1236161b5690ab302500077988e1" title="Alias to tag_of&lt;T&gt;::type, provided for convenience.">boost::hana::tag_of_t</a></code> </td></tr>
<tr id="row_0_0_1_6_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="core_2to_8hpp.html" target="_self">to.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-core.html#gadc70755c1d059139297814fb3bfeb91e" title="Converts an object from one data type to another.">boost::hana::to</a></code> and related utilities </td></tr>
<tr id="row_0_0_1_7_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="core_2when_8hpp.html" target="_self">when.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1when.html" title="Enable a partial specialization only if a boolean condition is true.">boost::hana::when</a></code> and <code><a class="el" href="group__group-core.html#ga0f5d717bbf6646619bb6219b104384dc" title="Variant of when allowing specializations to be enabled only if an expression is well-formed.">boost::hana::when_valid</a></code> </td></tr>
<tr id="row_0_0_2_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_0_0_2_" class="arrow" onclick="toggleFolder('0_0_2_')">&#9660;</span><span id="img_0_0_2_" class="iconfopen" onclick="toggleFolder('0_0_2_')">&#160;</span><a class="el" href="dir_0a4844ac9cb2026bd07faf42b116b549.html" target="_self">detail</a></td><td class="desc"></td></tr>
<tr id="row_0_0_2_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span id="arr_0_0_2_0_" class="arrow" onclick="toggleFolder('0_0_2_0_')">&#9660;</span><span id="img_0_0_2_0_" class="iconfopen" onclick="toggleFolder('0_0_2_0_')">&#160;</span><a class="el" href="dir_0cd2b357ffff5ecfb0310070703d859b.html" target="_self">operators</a></td><td class="desc"></td></tr>
<tr id="row_0_0_2_0_0_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="adl_8hpp.html" target="_self">adl.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1operators_1_1adl.html" title="Enables ADL in the hana::detail::operators namespace.">boost::hana::detail::operators::adl</a></code> </td></tr>
<tr id="row_0_0_2_0_1_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="arithmetic_8hpp.html" target="_self">arithmetic.hpp</a></td><td class="desc">Defines arithmetic operators </td></tr>
<tr id="row_0_0_2_0_2_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2operators_2comparable_8hpp.html" target="_self">comparable.hpp</a></td><td class="desc">Defines operators for Comparables </td></tr>
<tr id="row_0_0_2_0_3_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2operators_2iterable_8hpp.html" target="_self">iterable.hpp</a></td><td class="desc">Defines operators for Iterables </td></tr>
<tr id="row_0_0_2_0_4_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2operators_2logical_8hpp.html" target="_self">logical.hpp</a></td><td class="desc">Defines logical operators </td></tr>
<tr id="row_0_0_2_0_5_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2operators_2monad_8hpp.html" target="_self">monad.hpp</a></td><td class="desc">Defines operators for Monads </td></tr>
<tr id="row_0_0_2_0_6_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2operators_2orderable_8hpp.html" target="_self">orderable.hpp</a></td><td class="desc">Defines operators for Orderables </td></tr>
<tr id="row_0_0_2_0_7_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2operators_2searchable_8hpp.html" target="_self">searchable.hpp</a></td><td class="desc">Defines operators for Searchables </td></tr>
<tr id="row_0_0_2_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span id="arr_0_0_2_1_" class="arrow" onclick="toggleFolder('0_0_2_1_')">&#9660;</span><span id="img_0_0_2_1_" class="iconfopen" onclick="toggleFolder('0_0_2_1_')">&#160;</span><a class="el" href="dir_3a2d86f21a1d869e2ec4e510547bf681.html" target="_self">variadic</a></td><td class="desc"></td></tr>
<tr id="row_0_0_2_1_0_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span id="arr_0_0_2_1_0_" class="arrow" onclick="toggleFolder('0_0_2_1_0_')">&#9660;</span><span id="img_0_0_2_1_0_" class="iconfopen" onclick="toggleFolder('0_0_2_1_0_')">&#160;</span><a class="el" href="dir_cd91aed0e5b3a0fe3db2eb07a1431dc6.html" target="_self">reverse_apply</a></td><td class="desc"></td></tr>
<tr id="row_0_0_2_1_0_0_" class="even"><td class="entry"><span style="width:96px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="flat_8hpp.html" target="_self">flat.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::variadic::reverse_apply_flat</code> </td></tr>
<tr id="row_0_0_2_1_0_1_"><td class="entry"><span style="width:96px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="unrolled_8hpp.html" target="_self">unrolled.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::variadic::reverse_apply_unrolled</code> </td></tr>
<tr id="row_0_0_2_1_1_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2variadic_2at_8hpp.html" target="_self">at.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::variadic::at</code> </td></tr>
<tr id="row_0_0_2_1_2_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="drop__into_8hpp.html" target="_self">drop_into.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::variadic::drop_into</code> </td></tr>
<tr id="row_0_0_2_1_3_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="foldl1_8hpp.html" target="_self">foldl1.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::variadic::foldl1</code> </td></tr>
<tr id="row_0_0_2_1_4_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="foldr1_8hpp.html" target="_self">foldr1.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::variadic::foldr1</code> </td></tr>
<tr id="row_0_0_2_1_5_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="reverse__apply_8hpp.html" target="_self">reverse_apply.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::variadic::reverse_apply</code> </td></tr>
<tr id="row_0_0_2_1_6_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="split__at_8hpp.html" target="_self">split_at.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::variadic::split_at</code> </td></tr>
<tr id="row_0_0_2_1_7_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="take_8hpp.html" target="_self">take.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::variadic::take</code> </td></tr>
<tr id="row_0_0_2_2_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="algorithm_8hpp.html" target="_self">algorithm.hpp</a></td><td class="desc">Defines several <code>constexpr</code> algorithms </td></tr>
<tr id="row_0_0_2_3_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2any__of_8hpp.html" target="_self">any_of.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1any__of.html" title="Returns whether the Predicate is satisfied by any of the T....">boost::hana::detail::any_of</a></code> </td></tr>
<tr id="row_0_0_2_4_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2array_8hpp.html" target="_self">array.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1array.html" title="A minimal std::array with better constexpr support.">boost::hana::detail::array</a></code> </td></tr>
<tr id="row_0_0_2_5_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="canonical__constant_8hpp.html" target="_self">canonical_constant.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1_canonical_constant.html" title="Tag representing a canonical Constant.">boost::hana::detail::CanonicalConstant</a></code> </td></tr>
<tr id="row_0_0_2_6_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concepts_8hpp.html" target="_self">concepts.hpp</a></td><td class="desc">Defines concepts from the Standard library </td></tr>
<tr id="row_0_0_2_7_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="create_8hpp.html" target="_self">create.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1create.html" title="Implementation of the generic std::make_xxx pattern for arbitrary xxxs.">boost::hana::detail::create</a></code> </td></tr>
<tr id="row_0_0_2_8_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="decay_8hpp.html" target="_self">decay.hpp</a></td><td class="desc">Defines a replacement for <code>std::decay</code>, which is sometimes too slow at compile-time </td></tr>
<tr id="row_0_0_2_9_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="dispatch__if_8hpp.html" target="_self">dispatch_if.hpp</a></td><td class="desc">Defines <code>BOOST_HANA_DISPATCH_IF</code> </td></tr>
<tr id="row_0_0_2_10_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ebo_8hpp.html" target="_self">ebo.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::ebo</code> </td></tr>
<tr id="row_0_0_2_11_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fast__and_8hpp.html" target="_self">fast_and.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::fast_and</code> </td></tr>
<tr id="row_0_0_2_12_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="first__unsatisfied__index_8hpp.html" target="_self">first_unsatisfied_index.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1first__unsatisfied__index.html" title="Returns the index of the first element which does not satisfy Pred, or sizeof...(Xs) if no such eleme...">boost::hana::detail::first_unsatisfied_index</a></code> </td></tr>
<tr id="row_0_0_2_13_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="has__common__embedding_8hpp.html" target="_self">has_common_embedding.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::has_[nontrivial_]common_embedding</code> </td></tr>
<tr id="row_0_0_2_14_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="has__duplicates_8hpp.html" target="_self">has_duplicates.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1has__duplicates.html" title="Returns whether any of the Ts are duplicate w.r.t. hana::equal.">boost::hana::detail::has_duplicates</a></code> </td></tr>
<tr id="row_0_0_2_15_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="hash__table_8hpp.html" target="_self">hash_table.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::hash_table</code> </td></tr>
<tr id="row_0_0_2_16_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2index__if_8hpp.html" target="_self">index_if.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::index_if</code> </td></tr>
<tr id="row_0_0_2_17_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="detail_2integral__constant_8hpp.html" target="_self">integral_constant.hpp</a></td><td class="desc">Defines the barebones <code><a class="el" href="structboost_1_1hana_1_1integral__constant.html" title="Compile-time value of an integral type.">boost::hana::integral_constant</a></code> template, but no operations on it </td></tr>
<tr id="row_0_0_2_18_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="intrinsics_8hpp.html" target="_self">intrinsics.hpp</a></td><td class="desc">Defines macros for commonly used type traits </td></tr>
<tr id="row_0_0_2_19_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="nested__by_8hpp.html" target="_self">nested_by.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1nested__by.html" title="Provides a .by static constexpr function object.">boost::hana::detail::nested_by</a></code> </td></tr>
<tr id="row_0_0_2_20_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="nested__by__fwd_8hpp.html" target="_self">nested_by_fwd.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1detail_1_1nested__by.html" title="Provides a .by static constexpr function object.">boost::hana::detail::nested_by</a></code> </td></tr>
<tr id="row_0_0_2_21_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="nested__than_8hpp.html" target="_self">nested_than.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1nested__than.html" title="Provides a .than static constexpr function object.">boost::hana::detail::nested_than</a></code> </td></tr>
<tr id="row_0_0_2_22_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="nested__than__fwd_8hpp.html" target="_self">nested_than_fwd.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1detail_1_1nested__than.html" title="Provides a .than static constexpr function object.">boost::hana::detail::nested_than</a></code> </td></tr>
<tr id="row_0_0_2_23_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="nested__to_8hpp.html" target="_self">nested_to.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1nested__to.html" title="Provides a .to static constexpr function object.">boost::hana::detail::nested_to</a></code> </td></tr>
<tr id="row_0_0_2_24_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="nested__to__fwd_8hpp.html" target="_self">nested_to_fwd.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1detail_1_1nested__to.html" title="Provides a .to static constexpr function object.">boost::hana::detail::nested_to</a></code> </td></tr>
<tr id="row_0_0_2_25_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="preprocessor_8hpp.html" target="_self">preprocessor.hpp</a></td><td class="desc">Defines generally useful preprocessor macros </td></tr>
<tr id="row_0_0_2_26_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="std__common__type_8hpp.html" target="_self">std_common_type.hpp</a></td><td class="desc">Defines a SFINAE-friendly version of <code>std::common_type</code> </td></tr>
<tr id="row_0_0_2_27_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="struct__macros_8hpp.html" target="_self">struct_macros.hpp</a></td><td class="desc">Defines the <code>BOOST_HANA_DEFINE_STRUCT</code>, <code>BOOST_HANA_ADAPT_STRUCT</code>, and <code>BOOST_HANA_ADAPT_ADT</code> macros </td></tr>
<tr id="row_0_0_2_28_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="type__at_8hpp.html" target="_self">type_at.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1type__at.html" title="Classic MPL-style metafunction returning the nth element of a type parameter pack.">boost::hana::detail::type_at</a></code> </td></tr>
<tr id="row_0_0_2_29_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="type__foldl1_8hpp.html" target="_self">type_foldl1.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::type_foldl1</code> </td></tr>
<tr id="row_0_0_2_30_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="type__foldr1_8hpp.html" target="_self">type_foldr1.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::type_foldr1</code> </td></tr>
<tr id="row_0_0_2_31_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="unpack__flatten_8hpp.html" target="_self">unpack_flatten.hpp</a></td><td class="desc">Defines <code>boost::hana::detail::unpack_flatten</code> </td></tr>
<tr id="row_0_0_2_32_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="void__t_8hpp.html" target="_self">void_t.hpp</a></td><td class="desc">Defines an equivalent to the proposed <code>std::void_t</code> </td></tr>
<tr id="row_0_0_2_33_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="wrong_8hpp.html" target="_self">wrong.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1detail_1_1wrong.html" title="Equivalent to a type-dependent std::false_type.">boost::hana::detail::wrong</a></code> </td></tr>
<tr id="row_0_0_3_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_0_0_3_" class="arrow" onclick="toggleFolder('0_0_3_')">&#9660;</span><span id="img_0_0_3_" class="iconfopen" onclick="toggleFolder('0_0_3_')">&#160;</span><a class="el" href="dir_323f3a97cf58df541572718162fe1793.html" target="_self">experimental</a></td><td class="desc"></td></tr>
<tr id="row_0_0_3_0_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="types_8hpp.html" target="_self">types.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1experimental_1_1types.html" title="Container optimized for holding types.">boost::hana::experimental::types</a></code> </td></tr>
<tr id="row_0_0_4_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_0_0_4_" class="arrow" onclick="toggleFolder('0_0_4_')">&#9660;</span><span id="img_0_0_4_" class="iconfopen" onclick="toggleFolder('0_0_4_')">&#160;</span><a class="el" href="dir_97491a7940b2b44461a547afe712abd2.html" target="_self">ext</a></td><td class="desc"></td></tr>
<tr id="row_0_0_4_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span id="arr_0_0_4_0_" class="arrow" onclick="toggleFolder('0_0_4_0_')">&#9660;</span><span id="img_0_0_4_0_" class="iconfopen" onclick="toggleFolder('0_0_4_0_')">&#160;</span><a class="el" href="dir_8414a6d7fc3b38da6a6da863ae030f2c.html" target="_self">boost</a></td><td class="desc"></td></tr>
<tr id="row_0_0_4_0_0_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span id="arr_0_0_4_0_0_" class="arrow" onclick="toggleFolder('0_0_4_0_0_')">&#9660;</span><span id="img_0_0_4_0_0_" class="iconfopen" onclick="toggleFolder('0_0_4_0_0_')">&#160;</span><a class="el" href="dir_666cbe1241d83a4f7d9cad90f7b86490.html" target="_self">fusion</a></td><td class="desc"></td></tr>
<tr id="row_0_0_4_0_0_0_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span id="arr_0_0_4_0_0_0_" class="arrow" onclick="toggleFolder('0_0_4_0_0_0_')">&#9660;</span><span id="img_0_0_4_0_0_0_" class="iconfopen" onclick="toggleFolder('0_0_4_0_0_0_')">&#160;</span><a class="el" href="dir_7042a2e59d7efa2568e3581036b964b0.html" target="_self">detail</a></td><td class="desc"></td></tr>
<tr id="row_0_0_4_0_0_0_0_"><td class="entry"><span style="width:112px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ext_2boost_2fusion_2detail_2common_8hpp.html" target="_self">common.hpp</a></td><td class="desc">Defines common methods for all Boost.Fusion sequences </td></tr>
<tr id="row_0_0_4_0_0_1_" class="even"><td class="entry"><span style="width:96px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="deque_8hpp.html" target="_self">deque.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structboost_1_1fusion_1_1deque.html" title="Adapter for Boost.Fusion deques.">boost::fusion::deque</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_0_0_2_"><td class="entry"><span style="width:96px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fusion_2list_8hpp.html" target="_self">list.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structboost_1_1fusion_1_1list.html" title="Adapter for Boost.Fusion lists.">boost::fusion::list</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_0_0_3_" class="even"><td class="entry"><span style="width:96px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ext_2boost_2fusion_2tuple_8hpp.html" target="_self">tuple.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structboost_1_1fusion_1_1tuple.html" title="Adapter for Boost.Fusion tuples.">boost::fusion::tuple</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_0_0_4_"><td class="entry"><span style="width:96px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="boost_2fusion_2vector_8hpp.html" target="_self">vector.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structboost_1_1fusion_1_1vector.html" title="Adapter for Boost.Fusion vectors.">boost::fusion::vector</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_0_1_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span id="arr_0_0_4_0_1_" class="arrow" onclick="toggleFolder('0_0_4_0_1_')">&#9660;</span><span id="img_0_0_4_0_1_" class="iconfopen" onclick="toggleFolder('0_0_4_0_1_')">&#160;</span><a class="el" href="dir_aa8bf510119a03cbd5af87806db73281.html" target="_self">mpl</a></td><td class="desc"></td></tr>
<tr id="row_0_0_4_0_1_0_"><td class="entry"><span style="width:96px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="integral__c_8hpp.html" target="_self">integral_c.hpp</a></td><td class="desc">Adapts Boost.MPL IntegralConstants for use with Hana </td></tr>
<tr id="row_0_0_4_0_1_1_" class="even"><td class="entry"><span style="width:96px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="mpl_2list_8hpp.html" target="_self">list.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structboost_1_1mpl_1_1list.html" title="Adapter for Boost.MPL lists.">boost::mpl::list</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_0_1_2_"><td class="entry"><span style="width:96px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="boost_2mpl_2vector_8hpp.html" target="_self">vector.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structboost_1_1mpl_1_1vector.html" title="Adapter for Boost.MPL vectors.">boost::mpl::vector</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_0_2_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fusion_8hpp.html" target="_self">fusion.hpp</a></td><td class="desc">Includes all the adaptors for the Boost.Fusion library </td></tr>
<tr id="row_0_0_4_0_3_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="mpl_8hpp.html" target="_self">mpl.hpp</a></td><td class="desc">Includes all the adaptors for the Boost.MPL library </td></tr>
<tr id="row_0_0_4_0_4_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ext_2boost_2tuple_8hpp.html" target="_self">tuple.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structboost_1_1tuple.html" title="Adapter for boost::tuples.">boost::tuple</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span id="arr_0_0_4_1_" class="arrow" onclick="toggleFolder('0_0_4_1_')">&#9660;</span><span id="img_0_0_4_1_" class="iconfopen" onclick="toggleFolder('0_0_4_1_')">&#160;</span><a class="el" href="dir_f021aaf8cb4047f6c82c8c8a57a9e0c7.html" target="_self">std</a></td><td class="desc"></td></tr>
<tr id="row_0_0_4_1_0_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ext_2std_2array_8hpp.html" target="_self">array.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structstd_1_1array.html" title="Adaptation of std::array for Hana.">std::array</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_1_1_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="integer__sequence_8hpp.html" target="_self">integer_sequence.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structstd_1_1integer__sequence.html" title="Adaptation of std::integer_sequence for Hana.">std::integer_sequence</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_1_2_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ext_2std_2integral__constant_8hpp.html" target="_self">integral_constant.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structstd_1_1integral__constant.html" title="Adapter for std::integral_constants.">std::integral_constant</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_1_3_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ext_2std_2pair_8hpp.html" target="_self">pair.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structstd_1_1pair.html" title="Adaptation of std::pair for Hana.">std::pair</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_1_4_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ratio_8hpp.html" target="_self">ratio.hpp</a></td><td class="desc">Adapts <code><a class="el" href="classstd_1_1ratio.html" title="Adaptation of std::ratio for Hana.">std::ratio</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_1_5_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ext_2std_2tuple_8hpp.html" target="_self">tuple.hpp</a></td><td class="desc">Adapts <code><a class="el" href="structstd_1_1tuple.html" title="Adapter for std::tuples.">std::tuple</a></code> for use with Hana </td></tr>
<tr id="row_0_0_4_1_6_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="std_2vector_8hpp.html" target="_self">vector.hpp</a></td><td class="desc">Adapts <code>std::vector</code> for use with Hana </td></tr>
<tr id="row_0_0_4_2_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="boost_8hpp.html" target="_self">boost.hpp</a></td><td class="desc">Includes all the adaptors for external Boost libraries </td></tr>
<tr id="row_0_0_4_3_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="std_8hpp.html" target="_self">std.hpp</a></td><td class="desc">Includes all the adaptors for the standard library </td></tr>
<tr id="row_0_0_5_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_0_0_5_" class="arrow" onclick="toggleFolder('0_0_5_')">&#9660;</span><span id="img_0_0_5_" class="iconfopen" onclick="toggleFolder('0_0_5_')">&#160;</span><a class="el" href="dir_cf196044773ad9db3b539387dd944c9e.html" target="_self">functional</a></td><td class="desc"></td></tr>
<tr id="row_0_0_5_0_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="always_8hpp.html" target="_self">always.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga835970cb25a0c8dc200f1e5f8943538b" title="Return a constant function returning x regardless of the argument(s) it is invoked with.">boost::hana::always</a></code> </td></tr>
<tr id="row_0_0_5_1_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="apply_8hpp.html" target="_self">apply.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga30027c383676084be151ef3c6cf2829f" title="Invokes a Callable with the given arguments.">boost::hana::apply</a></code> </td></tr>
<tr id="row_0_0_5_2_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="arg_8hpp.html" target="_self">arg.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga6acc765a35c4dc85f0deab4785831a3d" title="Return the nth passed argument.">boost::hana::arg</a></code> </td></tr>
<tr id="row_0_0_5_3_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="capture_8hpp.html" target="_self">capture.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga41ada6b336e9d5bcb101ff0c737acbd0" title="Create a function capturing the given variables.">boost::hana::capture</a></code> </td></tr>
<tr id="row_0_0_5_4_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="compose_8hpp.html" target="_self">compose.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga3b16146e53efcdf9ecbb9a7b21f8cd0b" title="Return the composition of two functions or more.">boost::hana::compose</a></code> </td></tr>
<tr id="row_0_0_5_5_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="curry_8hpp.html" target="_self">curry.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga49ea872ade5ac8f6c10052c495302e89" title="Curry a function up to the given number of arguments.">boost::hana::curry</a></code> </td></tr>
<tr id="row_0_0_5_6_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="demux_8hpp.html" target="_self">demux.hpp</a></td><td class="desc">Defines <code>boost::hana::demux</code> </td></tr>
<tr id="row_0_0_5_7_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fix_8hpp.html" target="_self">fix.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga1393f40da2e8da6e0c12fce953e56a6c" title="Return a function computing the fixed point of a function.">boost::hana::fix</a></code> </td></tr>
<tr id="row_0_0_5_8_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="flip_8hpp.html" target="_self">flip.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga004f884cdbb85c2efe3383c1db450094" title="Invoke a function with its two first arguments reversed.">boost::hana::flip</a></code> </td></tr>
<tr id="row_0_0_5_9_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="id_8hpp.html" target="_self">id.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#gaef38cf34324c8edbd3597ae71811d00d" title="The identity function – returns its argument unchanged.">boost::hana::id</a></code> </td></tr>
<tr id="row_0_0_5_10_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="infix_8hpp.html" target="_self">infix.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga7bdafba6dc801f1d2d83731ad9714557" title="Return an equivalent function that can also be applied in infix notation.">boost::hana::infix</a></code> </td></tr>
<tr id="row_0_0_5_11_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="iterate_8hpp.html" target="_self">iterate.hpp</a></td><td class="desc">Defines <code>boost::hana::iterate</code> </td></tr>
<tr id="row_0_0_5_12_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="lockstep_8hpp.html" target="_self">lockstep.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#gafca60c09e1f7a32a2b52baaf6515c279" title="Invoke a function with the result of invoking other functions on its arguments, in lockstep.">boost::hana::lockstep</a></code> </td></tr>
<tr id="row_0_0_5_13_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="on_8hpp.html" target="_self">on.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga35c4fc3c5677b9f558150b90e74d3ab1" title="Invoke a function with the result of invoking another function on each argument.">boost::hana::on</a></code> </td></tr>
<tr id="row_0_0_5_14_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="overload_8hpp.html" target="_self">overload.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga83e71bae315e299f9f5f9de77b012139" title="Pick one of several functions to call based on overload resolution.">boost::hana::overload</a></code> </td></tr>
<tr id="row_0_0_5_15_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="overload__linearly_8hpp.html" target="_self">overload_linearly.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#gaa46de6f618d9f14edb1589b36b6e75ec" title="Call the first function that produces a valid call expression.">boost::hana::overload_linearly</a></code> </td></tr>
<tr id="row_0_0_5_16_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="partial_8hpp.html" target="_self">partial.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga778b2daa27882e71d28b6f2b38982ddf" title="Partially apply a function to some arguments.">boost::hana::partial</a></code> </td></tr>
<tr id="row_0_0_5_17_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="placeholder_8hpp.html" target="_self">placeholder.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#gaefe9fd152cba94be71c2b5b9de689d23" title="Create simple functions representing C++ operators inline.">boost::hana::_</a></code> </td></tr>
<tr id="row_0_0_5_18_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="reverse__partial_8hpp.html" target="_self">reverse_partial.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-functional.html#ga6e648f0d3fc0209ec024e9d759a5e8f8" title="Partially apply a function to some arguments.">boost::hana::reverse_partial</a></code> </td></tr>
<tr id="row_0_0_6_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_0_0_6_" class="arrow" onclick="toggleFolder('0_0_6_')">&#9660;</span><span id="img_0_0_6_" class="iconfopen" onclick="toggleFolder('0_0_6_')">&#160;</span><a class="el" href="dir_cc4d96287a8e6ea2980c75f79e8c5cd4.html" target="_self">fwd</a></td><td class="desc"></td></tr>
<tr id="row_0_0_6_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span id="arr_0_0_6_0_" class="arrow" onclick="toggleFolder('0_0_6_0_')">&#9660;</span><span id="img_0_0_6_0_" class="iconfopen" onclick="toggleFolder('0_0_6_0_')">&#160;</span><a class="el" href="dir_9c824c28346f35ae3c8d4f56bd6b4593.html" target="_self">concept</a></td><td class="desc"></td></tr>
<tr id="row_0_0_6_0_0_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2applicative_8hpp.html" target="_self">applicative.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Applicative</code> </td></tr>
<tr id="row_0_0_6_0_1_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2comonad_8hpp.html" target="_self">comonad.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Comonad</code> </td></tr>
<tr id="row_0_0_6_0_2_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2comparable_8hpp.html" target="_self">comparable.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Comparable</code> </td></tr>
<tr id="row_0_0_6_0_3_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2constant_8hpp.html" target="_self">constant.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Constant</code> </td></tr>
<tr id="row_0_0_6_0_4_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2euclidean__ring_8hpp.html" target="_self">euclidean_ring.hpp</a></td><td class="desc">Forward declares <code>boost::hana::EuclideanRing</code> </td></tr>
<tr id="row_0_0_6_0_5_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2foldable_8hpp.html" target="_self">foldable.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Foldable</code> </td></tr>
<tr id="row_0_0_6_0_6_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2functor_8hpp.html" target="_self">functor.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Functor</code> </td></tr>
<tr id="row_0_0_6_0_7_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2group_8hpp.html" target="_self">group.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Group</code> </td></tr>
<tr id="row_0_0_6_0_8_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2hashable_8hpp.html" target="_self">hashable.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Hashable</code> </td></tr>
<tr id="row_0_0_6_0_9_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2integral__constant_8hpp.html" target="_self">integral_constant.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">boost::hana::IntegralConstant</a></code> </td></tr>
<tr id="row_0_0_6_0_10_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2iterable_8hpp.html" target="_self">iterable.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Iterable</code> </td></tr>
<tr id="row_0_0_6_0_11_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2logical_8hpp.html" target="_self">logical.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Logical</code> </td></tr>
<tr id="row_0_0_6_0_12_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2metafunction_8hpp.html" target="_self">metafunction.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Metafunction</code> </td></tr>
<tr id="row_0_0_6_0_13_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2monad_8hpp.html" target="_self">monad.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Monad</code> </td></tr>
<tr id="row_0_0_6_0_14_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2monad__plus_8hpp.html" target="_self">monad_plus.hpp</a></td><td class="desc">Forward declares <code>boost::hana::MonadPlus</code> </td></tr>
<tr id="row_0_0_6_0_15_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2monoid_8hpp.html" target="_self">monoid.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Monoid</code> </td></tr>
<tr id="row_0_0_6_0_16_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2orderable_8hpp.html" target="_self">orderable.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Orderable</code> </td></tr>
<tr id="row_0_0_6_0_17_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2product_8hpp.html" target="_self">product.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Product</code> </td></tr>
<tr id="row_0_0_6_0_18_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2ring_8hpp.html" target="_self">ring.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Ring</code> </td></tr>
<tr id="row_0_0_6_0_19_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2searchable_8hpp.html" target="_self">searchable.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Searchable</code> </td></tr>
<tr id="row_0_0_6_0_20_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2sequence_8hpp.html" target="_self">sequence.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Sequence</code> </td></tr>
<tr id="row_0_0_6_0_21_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concept_2struct_8hpp.html" target="_self">struct.hpp</a></td><td class="desc">Forward declares <code>boost::hana::Struct</code> </td></tr>
<tr id="row_0_0_6_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span id="arr_0_0_6_1_" class="arrow" onclick="toggleFolder('0_0_6_1_')">&#9660;</span><span id="img_0_0_6_1_" class="iconfopen" onclick="toggleFolder('0_0_6_1_')">&#160;</span><a class="el" href="dir_b00751b7bd933c58cff85542f43b8f16.html" target="_self">core</a></td><td class="desc"></td></tr>
<tr id="row_0_0_6_1_0_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2core_2common_8hpp.html" target="_self">common.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1common.html" title="Metafunction returning the common data type between two data types.">boost::hana::common</a></code> and <code><a class="el" href="group__group-core.html#ga4da46c97755c0f430b063711b66ca05b" title="Alias to common&lt;T, U&gt;::type, provided for convenience.">boost::hana::common_t</a></code> </td></tr>
<tr id="row_0_0_6_1_1_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2core_2default_8hpp.html" target="_self">default.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1default__.html" title="Mark a tag-dispatched method implementation as a default implementation.">boost::hana::default_</a></code> and <code><a class="el" href="structboost_1_1hana_1_1is__default.html" title="Returns whether a tag-dispatched method implementation is a default implementation.">boost::hana::is_default</a></code> </td></tr>
<tr id="row_0_0_6_1_2_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2core_2is__a_8hpp.html" target="_self">is_a.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-core.html#ga38cf78e1e3e262f7f1c71ddd9ca70cd9" title="Returns whether the tag of an object matches a given tag.">boost::hana::is_a</a></code> and <code><a class="el" href="group__group-core.html#ga7fdbde52f5fe384a816c6f39ff272df9" title="Equivalent to is_a; provided for consistency with the rules of the English language.">boost::hana::is_an</a></code> </td></tr>
<tr id="row_0_0_6_1_3_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2core_2make_8hpp.html" target="_self">make.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-core.html#ga1d92480f0af1029878e773dafa3e2f60" title="Create an object of the given tag with the given arguments.">boost::hana::make</a></code> </td></tr>
<tr id="row_0_0_6_1_4_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2core_2tag__of_8hpp.html" target="_self">tag_of.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1tag__of.html" title="Metafunction returning the tag associated to T.">boost::hana::tag_of</a></code> and <code><a class="el" href="group__group-core.html#ga686d1236161b5690ab302500077988e1" title="Alias to tag_of&lt;T&gt;::type, provided for convenience.">boost::hana::tag_of_t</a></code> </td></tr>
<tr id="row_0_0_6_1_5_"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2core_2to_8hpp.html" target="_self">to.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-core.html#gadc70755c1d059139297814fb3bfeb91e" title="Converts an object from one data type to another.">boost::hana::to</a></code> and related utilities </td></tr>
<tr id="row_0_0_6_1_6_" class="even"><td class="entry"><span style="width:80px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2core_2when_8hpp.html" target="_self">when.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1when.html" title="Enable a partial specialization only if a boolean condition is true.">boost::hana::when</a></code> and <code><a class="el" href="group__group-core.html#ga0f5d717bbf6646619bb6219b104384dc" title="Variant of when allowing specializations to be enabled only if an expression is well-formed.">boost::hana::when_valid</a></code> </td></tr>
<tr id="row_0_0_6_2_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2accessors_8hpp.html" target="_self">accessors.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_struct.html#ga983a55dbd93d766fd37689ea32e4ddfb" title="Returns a Sequence of pairs representing the accessors of the data structure.">boost::hana::accessors</a></code> </td></tr>
<tr id="row_0_0_6_3_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2adapt__adt_8hpp.html" target="_self">adapt_adt.hpp</a></td><td class="desc">Documents the <code>BOOST_HANA_ADAPT_ADT</code> macro </td></tr>
<tr id="row_0_0_6_4_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2adapt__struct_8hpp.html" target="_self">adapt_struct.hpp</a></td><td class="desc">Documents the <code>BOOST_HANA_ADAPT_STRUCT</code> macro </td></tr>
<tr id="row_0_0_6_5_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2adjust_8hpp.html" target="_self">adjust.hpp</a></td><td class="desc">Forward declares <code>boost::hana::adjust</code> </td></tr>
<tr id="row_0_0_6_6_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2adjust__if_8hpp.html" target="_self">adjust_if.hpp</a></td><td class="desc">Forward declares <code>boost::hana::adjust_if</code> </td></tr>
<tr id="row_0_0_6_7_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2all_8hpp.html" target="_self">all.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga81ae9764dd7818ad36270c6419fb1082" title="Returns whether all the keys of the structure are true-valued.">boost::hana::all</a></code> </td></tr>
<tr id="row_0_0_6_8_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2all__of_8hpp.html" target="_self">all_of.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga3a168950082f38afd9edf256f336c8ba" title="Returns whether all the keys of the structure satisfy the predicate.">boost::hana::all_of</a></code> </td></tr>
<tr id="row_0_0_6_9_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2and_8hpp.html" target="_self">and.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_logical.html#ga14066f5672867c123524e0e0978069eb" title="Return whether all the arguments are true-valued.">boost::hana::and_</a></code> </td></tr>
<tr id="row_0_0_6_10_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2any_8hpp.html" target="_self">any.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#gab7d632b9319b10b1eb7e98f9e1cf8a28" title="Returns whether any key of the structure is true-valued.">boost::hana::any</a></code> </td></tr>
<tr id="row_0_0_6_11_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2any__of_8hpp.html" target="_self">any_of.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga5f7ff0125c448983e1b96c3ffb84f646" title="Returns whether any key of the structure satisfies the predicate.">boost::hana::any_of</a></code> </td></tr>
<tr id="row_0_0_6_12_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2ap_8hpp.html" target="_self">ap.hpp</a></td><td class="desc">Forward declares <code>boost::hana::ap</code> </td></tr>
<tr id="row_0_0_6_13_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2append_8hpp.html" target="_self">append.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad_plus.html#ga08624924fe05f0cfbfbd6e439db01873" title="Append an element to a monadic structure.">boost::hana::append</a></code> </td></tr>
<tr id="row_0_0_6_14_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2at_8hpp.html" target="_self">at.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129" title="Returns the nth element of an iterable.">boost::hana::at</a></code> and <code><a class="el" href="group__group-_iterable.html#ga4cb99cfbef936cb267e76f66f40f529c" title="Equivalent to at; provided for convenience.">boost::hana::at_c</a></code> </td></tr>
<tr id="row_0_0_6_15_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2at__key_8hpp.html" target="_self">at_key.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga3c1826aee6c6eb577810bb99c5c3e53d" title="Returns the value associated to the given key in a structure, or fail.">boost::hana::at_key</a></code> </td></tr>
<tr id="row_0_0_6_16_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2back_8hpp.html" target="_self">back.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_iterable.html#gab3f4d0035345a453284e46303862d463" title="Returns the last element of a non-empty and finite iterable.">boost::hana::back</a></code> </td></tr>
<tr id="row_0_0_6_17_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2basic__tuple_8hpp.html" target="_self">basic_tuple.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1basic__tuple.html" title="Stripped down version of hana::tuple.">boost::hana::basic_tuple</a></code> </td></tr>
<tr id="row_0_0_6_18_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2bool_8hpp.html" target="_self">bool.hpp</a></td><td class="desc">Includes <a class="el" href="fwd_2integral__constant_8hpp.html" title="Forward declares boost::hana::integral_constant.">boost/hana/fwd/integral_constant.hpp</a> </td></tr>
<tr id="row_0_0_6_19_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2cartesian__product_8hpp.html" target="_self">cartesian_product.hpp</a></td><td class="desc">Forward declares <code>boost::hana::cartesian_product</code> </td></tr>
<tr id="row_0_0_6_20_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2chain_8hpp.html" target="_self">chain.hpp</a></td><td class="desc">Forward declares <code>boost::hana::chain</code> </td></tr>
<tr id="row_0_0_6_21_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2comparing_8hpp.html" target="_self">comparing.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_comparable.html#ga9c2ffe2e51780e57a38d9e7e31b87cdc" title="Returns a function performing equal after applying a transformation to both arguments.">boost::hana::comparing</a></code> </td></tr>
<tr id="row_0_0_6_22_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2concat_8hpp.html" target="_self">concat.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad_plus.html#ga1946e96c3b4c178c7ae8703724c29c37" title="Combine two monadic structures together.">boost::hana::concat</a></code> </td></tr>
<tr id="row_0_0_6_23_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2contains_8hpp.html" target="_self">contains.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga38e7748956cbc9f3d9bb035ac8577906" title="Returns whether the key occurs in the structure.">boost::hana::contains</a></code> and <code><a class="el" href="group__group-_searchable.html#ga0d9456ceda38b6ca664998e79d7c45b7" title="Return whether the key occurs in the structure.">boost::hana::in</a></code> </td></tr>
<tr id="row_0_0_6_24_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2core_8hpp.html" target="_self">core.hpp</a></td><td class="desc">Forward declares the <a class="el" href="group__group-core.html">Core</a> module </td></tr>
<tr id="row_0_0_6_25_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2count_8hpp.html" target="_self">count.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#ga3159cfa41be18a396926741b0a3fdefd" title="Return the number of elements in the structure that compare equal to a given value.">boost::hana::count</a></code> </td></tr>
<tr id="row_0_0_6_26_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2count__if_8hpp.html" target="_self">count_if.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#ga39d71be65d5b98e7d035a3e5c607e1b4" title="Return the number of elements in the structure for which the predicate is satisfied.">boost::hana::count_if</a></code> </td></tr>
<tr id="row_0_0_6_27_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2cycle_8hpp.html" target="_self">cycle.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad_plus.html#gaaf46c168f721da9effcc7336a997f5d6" title="Combine a monadic structure with itself n times.">boost::hana::cycle</a></code> </td></tr>
<tr id="row_0_0_6_28_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2define__struct_8hpp.html" target="_self">define_struct.hpp</a></td><td class="desc">Documents the <code>BOOST_HANA_DEFINE_STRUCT</code> macro </td></tr>
<tr id="row_0_0_6_29_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2difference_8hpp.html" target="_self">difference.hpp</a></td><td class="desc">Forward declares <code>boost::hana::difference</code> </td></tr>
<tr id="row_0_0_6_30_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2div_8hpp.html" target="_self">div.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_euclidean_ring.html#ga4225a7988ce98903228913dde53762df" title="Generalized integer division.">boost::hana::div</a></code> </td></tr>
<tr id="row_0_0_6_31_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2drop__back_8hpp.html" target="_self">drop_back.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7" title="Drop the last n elements of a finite sequence, and return the rest.">boost::hana::drop_back</a></code> </td></tr>
<tr id="row_0_0_6_32_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2drop__front_8hpp.html" target="_self">drop_front.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757" title="Drop the first n elements of an iterable, and return the rest.">boost::hana::drop_front</a></code> </td></tr>
<tr id="row_0_0_6_33_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2drop__front__exactly_8hpp.html" target="_self">drop_front_exactly.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4" title="Drop the first n elements of an iterable, and return the rest.">boost::hana::drop_front_exactly</a></code> </td></tr>
<tr id="row_0_0_6_34_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2drop__while_8hpp.html" target="_self">drop_while.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_iterable.html#ga9f1d02c74a6bdc1db260e0d6a8f1ee56" title="Drop elements from an iterable up to, but excluding, the first element for which the predicate is not...">boost::hana::drop_while</a></code> </td></tr>
<tr id="row_0_0_6_35_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2duplicate_8hpp.html" target="_self">duplicate.hpp</a></td><td class="desc">Forward declares <code>boost::hana::duplicate</code> </td></tr>
<tr id="row_0_0_6_36_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2empty_8hpp.html" target="_self">empty.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad_plus.html#gaa6be1e83ad72b9d69b43b4bada0f3a75" title="Identity of the monadic combination concat.">boost::hana::empty</a></code> </td></tr>
<tr id="row_0_0_6_37_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2equal_8hpp.html" target="_self">equal.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_comparable.html#gacaf1ebea6b3ab96ac9dcb82f0e64e547" title="Returns a Logical representing whether x is equal to y.">boost::hana::equal</a></code> </td></tr>
<tr id="row_0_0_6_38_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2erase__key_8hpp.html" target="_self">erase_key.hpp</a></td><td class="desc">Forward declares <code>boost::hana::erase_key</code> </td></tr>
<tr id="row_0_0_6_39_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2eval_8hpp.html" target="_self">eval.hpp</a></td><td class="desc">Forward declares <code>boost::hana::eval</code> </td></tr>
<tr id="row_0_0_6_40_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2eval__if_8hpp.html" target="_self">eval_if.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_logical.html#gab64636f84de983575aac0208f5fa840c" title="Conditionally execute one of two branches based on a condition.">boost::hana::eval_if</a></code> </td></tr>
<tr id="row_0_0_6_41_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2extend_8hpp.html" target="_self">extend.hpp</a></td><td class="desc">Forward declares <code>boost::hana::extend</code> </td></tr>
<tr id="row_0_0_6_42_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2extract_8hpp.html" target="_self">extract.hpp</a></td><td class="desc">Forward declares <code>boost::hana::extract</code> </td></tr>
<tr id="row_0_0_6_43_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2fill_8hpp.html" target="_self">fill.hpp</a></td><td class="desc">Forward declares <code>boost::hana::fill</code> </td></tr>
<tr id="row_0_0_6_44_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2filter_8hpp.html" target="_self">filter.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad_plus.html#ga65cc6d9f522fb9e8e3b28d80ee5c822a" title="Filter a monadic structure using a custom predicate.">boost::hana::filter</a></code> </td></tr>
<tr id="row_0_0_6_45_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2find_8hpp.html" target="_self">find.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga6b6cdd69942b0fe3bf5254247f9c861e" title="Finds the value associated to the given key in a structure.">boost::hana::find</a></code> </td></tr>
<tr id="row_0_0_6_46_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2find__if_8hpp.html" target="_self">find_if.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f" title="Finds the value associated to the first key satisfying a predicate.">boost::hana::find_if</a></code> </td></tr>
<tr id="row_0_0_6_47_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2first_8hpp.html" target="_self">first.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_product.html#ga34bbf4281de06dc3540441e8b2bd24f4" title="Returns the first element of a pair.">boost::hana::first</a></code> </td></tr>
<tr id="row_0_0_6_48_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2flatten_8hpp.html" target="_self">flatten.hpp</a></td><td class="desc">Forward declares <code>boost::hana::flatten</code> </td></tr>
<tr id="row_0_0_6_49_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2fold_8hpp.html" target="_self">fold.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#gaa0fde17f3b947a0678a1c0c01232f2cc" title="Equivalent to fold_left; provided for convenience.">boost::hana::fold</a></code> </td></tr>
<tr id="row_0_0_6_50_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2fold__left_8hpp.html" target="_self">fold_left.hpp</a></td><td class="desc">Forward declares <code>boost::hana::fold_left</code> </td></tr>
<tr id="row_0_0_6_51_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2fold__right_8hpp.html" target="_self">fold_right.hpp</a></td><td class="desc">Forward declares <code>boost::hana::fold_right</code> </td></tr>
<tr id="row_0_0_6_52_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2for__each_8hpp.html" target="_self">for_each.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#ga2af382f7e644ce3707710bbad313e9c2" title="Perform an action on each element of a foldable, discarding the result each time.">boost::hana::for_each</a></code> </td></tr>
<tr id="row_0_0_6_53_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2front_8hpp.html" target="_self">front.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444" title="Returns the first element of a non-empty iterable.">boost::hana::front</a></code> </td></tr>
<tr id="row_0_0_6_54_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2fuse_8hpp.html" target="_self">fuse.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#ga19fcf61d8d1179903952c0f564c538aa" title="Transform a function taking multiple arguments into a function that can be called with a compile-time...">boost::hana::fuse</a></code> </td></tr>
<tr id="row_0_0_6_55_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2greater_8hpp.html" target="_self">greater.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_orderable.html#gaf9a073eafebbe514fb19dff82318f198" title="Returns a Logical representing whether x is greater than y.">boost::hana::greater</a></code> </td></tr>
<tr id="row_0_0_6_56_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2greater__equal_8hpp.html" target="_self">greater_equal.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_orderable.html#ga6023631e7d0a01e16dc3fa4221fbd703" title="Returns a Logical representing whether x is greater than or equal to y.">boost::hana::greater_equal</a></code> </td></tr>
<tr id="row_0_0_6_57_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2group_8hpp.html" target="_self">group.hpp</a></td><td class="desc">Forward declares <code>boost::hana::group</code> </td></tr>
<tr id="row_0_0_6_58_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2hash_8hpp.html" target="_self">hash.hpp</a></td><td class="desc">Forward declares <code>boost::hana::hash</code> </td></tr>
<tr id="row_0_0_6_59_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2if_8hpp.html" target="_self">if.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_logical.html#gafd655d2222367131e7a63616e93dd080" title="Conditionally return one of two values based on a condition.">boost::hana::if_</a></code> </td></tr>
<tr id="row_0_0_6_60_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2index__if_8hpp.html" target="_self">index_if.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87" title="Finds the value associated to the first key satisfying a predicate.">boost::hana::index_if</a></code> </td></tr>
<tr id="row_0_0_6_61_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2insert_8hpp.html" target="_self">insert.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#gae22a1a184b1b2dd550fa4fa619bed2e9" title="Insert a value at a given index in a sequence.">boost::hana::insert</a></code> </td></tr>
<tr id="row_0_0_6_62_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2insert__range_8hpp.html" target="_self">insert_range.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga3410ba833cf1ff1d929fcfda4df2eae1" title="Insert several values at a given index in a sequence.">boost::hana::insert_range</a></code> </td></tr>
<tr id="row_0_0_6_63_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2integral__constant_8hpp.html" target="_self">integral_constant.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1integral__constant.html" title="Compile-time value of an integral type.">boost::hana::integral_constant</a></code> </td></tr>
<tr id="row_0_0_6_64_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2intersection_8hpp.html" target="_self">intersection.hpp</a></td><td class="desc">Forward declares <code>boost::hana::intersection</code> </td></tr>
<tr id="row_0_0_6_65_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2intersperse_8hpp.html" target="_self">intersperse.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#gaa18061cd0f63cfaae89abf43ff92b79e" title="Insert a value between each pair of elements in a finite sequence.">boost::hana::intersperse</a></code> </td></tr>
<tr id="row_0_0_6_66_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2is__disjoint_8hpp.html" target="_self">is_disjoint.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga3b8269d4f5cdd6dd549fae32280795a0" title="Returns whether two Searchables are disjoint.">boost::hana::is_disjoint</a></code> </td></tr>
<tr id="row_0_0_6_67_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2is__empty_8hpp.html" target="_self">is_empty.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394" title="Returns whether the iterable is empty.">boost::hana::is_empty</a></code> </td></tr>
<tr id="row_0_0_6_68_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2is__subset_8hpp.html" target="_self">is_subset.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#gadccfc79f1acdd8043d2baa16df16ec9f" title="Returns whether a structure contains a subset of the keys of another structure.">boost::hana::is_subset</a></code> </td></tr>
<tr id="row_0_0_6_69_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2keys_8hpp.html" target="_self">keys.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_struct.html#gaf8c7199742581e6e66c8397def68e2d3" title="Returns a Sequence containing the name of the members of the data structure.">boost::hana::keys</a></code> </td></tr>
<tr id="row_0_0_6_70_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2lazy_8hpp.html" target="_self">lazy.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1lazy.html" title="hana::lazy implements superficial laziness via a monadic interface.">boost::hana::lazy</a></code> </td></tr>
<tr id="row_0_0_6_71_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2length_8hpp.html" target="_self">length.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#gaf0f8f717245620dc28cd7d7fa44d7475" title="Return the number of elements in a foldable structure.">boost::hana::length</a></code> </td></tr>
<tr id="row_0_0_6_72_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2less_8hpp.html" target="_self">less.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_orderable.html#gad510011602bdb14686f1c4ec145301c9" title="Returns a Logical representing whether x is less than y.">boost::hana::less</a></code> </td></tr>
<tr id="row_0_0_6_73_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2less__equal_8hpp.html" target="_self">less_equal.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_orderable.html#ga9917dd82beb67151bf5657245d37b851" title="Returns a Logical representing whether x is less than or equal to y.">boost::hana::less_equal</a></code> </td></tr>
<tr id="row_0_0_6_74_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2lexicographical__compare_8hpp.html" target="_self">lexicographical_compare.hpp</a></td><td class="desc">Forward declares <code>boost::hana::lexicographical_compare</code> </td></tr>
<tr id="row_0_0_6_75_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2lift_8hpp.html" target="_self">lift.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_applicative.html#ga712038d7abbc7159f8792788f7cd0c73" title="Lift a value into an Applicative structure.">boost::hana::lift</a></code> </td></tr>
<tr id="row_0_0_6_76_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2map_8hpp.html" target="_self">map.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1map.html" title="Basic associative container requiring unique, Comparable and Hashable keys.">boost::hana::map</a></code> </td></tr>
<tr id="row_0_0_6_77_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2max_8hpp.html" target="_self">max.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_orderable.html#ga999eee8ca8750f9b1afa0d7a1db28030" title="Returns the greatest of its arguments according to the less ordering.">boost::hana::max</a></code> </td></tr>
<tr id="row_0_0_6_78_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2maximum_8hpp.html" target="_self">maximum.hpp</a></td><td class="desc">Forward declares <code>boost::hana::maximum</code> </td></tr>
<tr id="row_0_0_6_79_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2members_8hpp.html" target="_self">members.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_struct.html#gad301dd8e9fb4639d7874619c97d6d427" title="Returns a Sequence containing the members of a Struct.">boost::hana::members</a></code> </td></tr>
<tr id="row_0_0_6_80_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2min_8hpp.html" target="_self">min.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_orderable.html#ga2d54f189ea6f57fb2c0d772169440c5c" title="Returns the smallest of its arguments according to the less ordering.">boost::hana::min</a></code> </td></tr>
<tr id="row_0_0_6_81_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2minimum_8hpp.html" target="_self">minimum.hpp</a></td><td class="desc">Forward declares <code>boost::hana::minimum</code> </td></tr>
<tr id="row_0_0_6_82_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2minus_8hpp.html" target="_self">minus.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_group.html#ga2020c526324f361a2b990fe8d1b07c20" title="Subtract two elements of a group.">boost::hana::minus</a></code> </td></tr>
<tr id="row_0_0_6_83_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2mod_8hpp.html" target="_self">mod.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_euclidean_ring.html#ga9b47b223d5b02db933b3c93b5bd1a062" title="Generalized integer modulus.">boost::hana::mod</a></code> </td></tr>
<tr id="row_0_0_6_84_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2monadic__compose_8hpp.html" target="_self">monadic_compose.hpp</a></td><td class="desc">Forward declares <code>boost::hana::monadic_compose</code> </td></tr>
<tr id="row_0_0_6_85_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2monadic__fold__left_8hpp.html" target="_self">monadic_fold_left.hpp</a></td><td class="desc">Forward declares <code>boost::hana::monadic_fold_left</code> </td></tr>
<tr id="row_0_0_6_86_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2monadic__fold__right_8hpp.html" target="_self">monadic_fold_right.hpp</a></td><td class="desc">Forward declares <code>boost::hana::monadic_fold_right</code> </td></tr>
<tr id="row_0_0_6_87_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2mult_8hpp.html" target="_self">mult.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_ring.html#ga052d31c269a6a438cc8004c9ad1efdfa" title="Associative operation of a Ring.">boost::hana::mult</a></code> </td></tr>
<tr id="row_0_0_6_88_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2negate_8hpp.html" target="_self">negate.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_group.html#ga02e81002f40ba52eac4cf1974c7e0cdb" title="Return the inverse of an element of a group.">boost::hana::negate</a></code> </td></tr>
<tr id="row_0_0_6_89_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2none_8hpp.html" target="_self">none.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga614ff1e575806f59246b17006e19d479" title="Returns whether all of the keys of the structure are false-valued.">boost::hana::none</a></code> </td></tr>
<tr id="row_0_0_6_90_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2none__of_8hpp.html" target="_self">none_of.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_searchable.html#ga43954c791b5b1351fb009e2a643d00f5" title="Returns whether none of the keys of the structure satisfy the predicate.">boost::hana::none_of</a></code> </td></tr>
<tr id="row_0_0_6_91_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2not_8hpp.html" target="_self">not.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_logical.html#ga4a7c9d7037601d5e553fd20777958980" title="Negates a Logical.">boost::hana::not_</a></code> </td></tr>
<tr id="row_0_0_6_92_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2not__equal_8hpp.html" target="_self">not_equal.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_comparable.html#gae33be2e0d5e04f19082f4b7740dfc9cd" title="Returns a Logical representing whether x is not equal to y.">boost::hana::not_equal</a></code> </td></tr>
<tr id="row_0_0_6_93_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2one_8hpp.html" target="_self">one.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_ring.html#gadea531feb3b0a1c5c3d777f7ab45e932" title="Identity of the Ring multiplication.">boost::hana::one</a></code> </td></tr>
<tr id="row_0_0_6_94_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2optional_8hpp.html" target="_self">optional.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1optional.html" title="Optional value whose optional-ness is known at compile-time.">boost::hana::optional</a></code> </td></tr>
<tr id="row_0_0_6_95_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2or_8hpp.html" target="_self">or.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_logical.html#ga68c00efbeb69339bfa157a78ebdd3f87" title="Return whether any of the arguments is true-valued.">boost::hana::or_</a></code> </td></tr>
<tr id="row_0_0_6_96_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2ordering_8hpp.html" target="_self">ordering.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_orderable.html#gaf7e94ba859710cd6ba6152e5dc18977d" title="Returns a function performing less after applying a transformation to both arguments.">boost::hana::ordering</a></code> </td></tr>
<tr id="row_0_0_6_97_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2pair_8hpp.html" target="_self">pair.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1pair.html" title="Generic container for two elements.">boost::hana::pair</a></code> </td></tr>
<tr id="row_0_0_6_98_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2partition_8hpp.html" target="_self">partition.hpp</a></td><td class="desc">Forward declares <code>boost::hana::partition</code> </td></tr>
<tr id="row_0_0_6_99_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2permutations_8hpp.html" target="_self">permutations.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#gac1e182ac088f1990edd739424d30ea07" title="Return a sequence of all the permutations of the given sequence.">boost::hana::permutations</a></code> </td></tr>
<tr id="row_0_0_6_100_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2plus_8hpp.html" target="_self">plus.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monoid.html#gaeb5d4a1e967e319712f9e4791948896c" title="Associative binary operation on a Monoid.">boost::hana::plus</a></code> </td></tr>
<tr id="row_0_0_6_101_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2power_8hpp.html" target="_self">power.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_ring.html#ga0ee3cff9ec646bcc7217f00ee6099b72" title="Elevate a ring element to its nth power.">boost::hana::power</a></code> </td></tr>
<tr id="row_0_0_6_102_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2prefix_8hpp.html" target="_self">prefix.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad_plus.html#ga3022fdfe454dc9bc1f79b5dfeba13b5e" title="Inserts a value before each element of a monadic structure.">boost::hana::prefix</a></code> </td></tr>
<tr id="row_0_0_6_103_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2prepend_8hpp.html" target="_self">prepend.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad_plus.html#ga69afbfd4e91125e3e52fcb409135ca7c" title="Prepend an element to a monadic structure.">boost::hana::prepend</a></code> </td></tr>
<tr id="row_0_0_6_104_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2product_8hpp.html" target="_self">product.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#ga17fe9c1982c882807f3358b4138c5744" title="Compute the product of the numbers of a structure.">boost::hana::product</a></code> </td></tr>
<tr id="row_0_0_6_105_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2range_8hpp.html" target="_self">range.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1range.html" title="Compile-time half-open interval of hana::integral_constants.">boost::hana::range</a></code> </td></tr>
<tr id="row_0_0_6_106_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2remove_8hpp.html" target="_self">remove.hpp</a></td><td class="desc">Forward declares <code>boost::hana::remove</code> </td></tr>
<tr id="row_0_0_6_107_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2remove__at_8hpp.html" target="_self">remove_at.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga80724ec8ecf319a1e695988a69e22f87" title="Remove the element at a given index from a sequence.">boost::hana::remove_at</a></code> and <code><a class="el" href="group__group-_sequence.html#gae70b0815645c7d81bb636a1eed1a65c6" title="Equivalent to remove_at; provided for convenience.">boost::hana::remove_at_c</a></code> </td></tr>
<tr id="row_0_0_6_108_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2remove__if_8hpp.html" target="_self">remove_if.hpp</a></td><td class="desc">Forward declares <code>boost::hana::remove_if</code> </td></tr>
<tr id="row_0_0_6_109_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2remove__range_8hpp.html" target="_self">remove_range.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga6f6d5c1f335780c91d29626fde615c78" title="Remove the elements inside a given range of indices from a sequence.">boost::hana::remove_range</a></code> and <code><a class="el" href="group__group-_sequence.html#ga4696efcdee7d95ab4a391bb896a840b5" title="Equivalent to remove_range; provided for convenience.">boost::hana::remove_range_c</a></code> </td></tr>
<tr id="row_0_0_6_110_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2repeat_8hpp.html" target="_self">repeat.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="namespaceboost_1_1hana.html#a405f3dd84fc6f5003e64f8da104a1b54" title="Invokes a nullary function n times.">boost::hana::repeat</a></code> </td></tr>
<tr id="row_0_0_6_111_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2replace_8hpp.html" target="_self">replace.hpp</a></td><td class="desc">Forward declares <code>boost::hana::replace</code> </td></tr>
<tr id="row_0_0_6_112_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2replace__if_8hpp.html" target="_self">replace_if.hpp</a></td><td class="desc">Forward declares <code>boost::hana::replace_if</code> </td></tr>
<tr id="row_0_0_6_113_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2replicate_8hpp.html" target="_self">replicate.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad_plus.html#gad5f48c79d11923d6c1d70b18b7dd3f19" title="Create a monadic structure by combining a lifted value with itself n times.">boost::hana::replicate</a></code> </td></tr>
<tr id="row_0_0_6_114_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2reverse_8hpp.html" target="_self">reverse.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga28037560e8f224c53cf6ac168d03a067" title="Reverse a sequence.">boost::hana::reverse</a></code> </td></tr>
<tr id="row_0_0_6_115_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2reverse__fold_8hpp.html" target="_self">reverse_fold.hpp</a></td><td class="desc">Forward declares <code>boost::hana::reverse_fold</code> </td></tr>
<tr id="row_0_0_6_116_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2scan__left_8hpp.html" target="_self">scan_left.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd" title="Fold a Sequence to the left and return a list containing the successive reduction states.">boost::hana::scan_left</a></code> </td></tr>
<tr id="row_0_0_6_117_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2scan__right_8hpp.html" target="_self">scan_right.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab" title="Fold a Sequence to the right and return a list containing the successive reduction states.">boost::hana::scan_right</a></code> </td></tr>
<tr id="row_0_0_6_118_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2second_8hpp.html" target="_self">second.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_product.html#ga7bb979d59ffc3ab862cb7d9dc7730077" title="Returns the second element of a pair.">boost::hana::second</a></code> </td></tr>
<tr id="row_0_0_6_119_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2set_8hpp.html" target="_self">set.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1set.html" title="Basic unordered container requiring unique, Comparable and Hashable keys.">boost::hana::set</a></code> </td></tr>
<tr id="row_0_0_6_120_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2size_8hpp.html" target="_self">size.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#ga8ec3ac9a6f5014db943f61ebc9e1e36e" title="Equivalent to length; provided for consistency with the standard library.">boost::hana::size</a></code> </td></tr>
<tr id="row_0_0_6_121_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2slice_8hpp.html" target="_self">slice.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081" title="Extract the elements of a Sequence at the given indices.">boost::hana::slice</a></code> and <code><a class="el" href="group__group-_sequence.html#gae1f6a2a9cb70564d43c6b3c663b25dd7" title="Shorthand to slice a contiguous range of elements.">boost::hana::slice_c</a></code> </td></tr>
<tr id="row_0_0_6_122_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2sort_8hpp.html" target="_self">sort.hpp</a></td><td class="desc">Forward declares <code>boost::hana::sort</code> </td></tr>
<tr id="row_0_0_6_123_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2span_8hpp.html" target="_self">span.hpp</a></td><td class="desc">Forward declares <code>boost::hana::span</code> </td></tr>
<tr id="row_0_0_6_124_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2string_8hpp.html" target="_self">string.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1string.html" title="Compile-time string.">boost::hana::string</a></code> </td></tr>
<tr id="row_0_0_6_125_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2suffix_8hpp.html" target="_self">suffix.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad_plus.html#ga61dab15f6ecf379121d4096fe0c8ab13" title="Inserts a value after each element of a monadic structure.">boost::hana::suffix</a></code> </td></tr>
<tr id="row_0_0_6_126_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2sum_8hpp.html" target="_self">sum.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#ga650def4b2e98f4273d8b9b7aa5a2fc28" title="Compute the sum of the numbers of a structure.">boost::hana::sum</a></code> </td></tr>
<tr id="row_0_0_6_127_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2symmetric__difference_8hpp.html" target="_self">symmetric_difference.hpp</a></td><td class="desc">Forward declares <code>boost::hana::symmetric_difference</code> </td></tr>
<tr id="row_0_0_6_128_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2take__back_8hpp.html" target="_self">take_back.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920" title="Returns the last n elements of a sequence, or the whole sequence if the sequence has less than n elem...">boost::hana::take_back</a></code> </td></tr>
<tr id="row_0_0_6_129_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2take__front_8hpp.html" target="_self">take_front.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316" title="Returns the first n elements of a sequence, or the whole sequence if the sequence has less than n ele...">boost::hana::take_front</a></code> and <code><a class="el" href="group__group-_sequence.html#ga3779f62fea92af00113a9290f1c680eb" title="Equivalent to take_front; provided for convenience.">boost::hana::take_front_c</a></code> </td></tr>
<tr id="row_0_0_6_130_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2take__while_8hpp.html" target="_self">take_while.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga2d4db4ec5ec5bc16fe74f57de12697fd" title="Take elements from a sequence while the predicate is satisfied.">boost::hana::take_while</a></code> </td></tr>
<tr id="row_0_0_6_131_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2tap_8hpp.html" target="_self">tap.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad.html#ga5e0735de01a24f681c55aedfeb6d13bf" title="Tap inside a monadic chain.">boost::hana::tap</a></code> </td></tr>
<tr id="row_0_0_6_132_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2then_8hpp.html" target="_self">then.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monad.html#gaaddd3789de43cf989babb10cdc0b447a" title="Sequentially compose two monadic actions, discarding any value produced by the first but not its effe...">boost::hana::then</a></code> </td></tr>
<tr id="row_0_0_6_133_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2transform_8hpp.html" target="_self">transform.hpp</a></td><td class="desc">Forward declares <code>boost::hana::transform</code> </td></tr>
<tr id="row_0_0_6_134_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2tuple_8hpp.html" target="_self">tuple.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1tuple.html" title="General purpose index-based heterogeneous sequence with a fixed length.">boost::hana::tuple</a></code> </td></tr>
<tr id="row_0_0_6_135_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2type_8hpp.html" target="_self">type.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="structboost_1_1hana_1_1type.html" title="C++ type in value-level representation.">boost::hana::type</a></code> and related utilities </td></tr>
<tr id="row_0_0_6_136_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2unfold__left_8hpp.html" target="_self">unfold_left.hpp</a></td><td class="desc">Forward declares <code>boost::hana::unfold_left</code> </td></tr>
<tr id="row_0_0_6_137_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2unfold__right_8hpp.html" target="_self">unfold_right.hpp</a></td><td class="desc">Forward declares <code>boost::hana::unfold_right</code> </td></tr>
<tr id="row_0_0_6_138_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2union_8hpp.html" target="_self">union.hpp</a></td><td class="desc">Forward declares <code>boost::hana::union_</code> </td></tr>
<tr id="row_0_0_6_139_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2unique_8hpp.html" target="_self">unique.hpp</a></td><td class="desc">Forward declares <code>boost::hana::unique</code> </td></tr>
<tr id="row_0_0_6_140_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2unpack_8hpp.html" target="_self">unpack.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_foldable.html#ga7b0c23944364ce61136e10b978ae2170" title="Invoke a function with the elements of a Foldable as arguments.">boost::hana::unpack</a></code> </td></tr>
<tr id="row_0_0_6_141_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2value_8hpp.html" target="_self">value.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_constant.html#ga1687520692a6b0c49e3a69de2980f388" title="Return the compile-time value associated to a constant.">boost::hana::value</a></code> </td></tr>
<tr id="row_0_0_6_142_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2while_8hpp.html" target="_self">while.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_logical.html#ga08a767b86c330cac67daa891406d2730" title="Apply a function to an initial state while some predicate is satisfied.">boost::hana::while_</a></code> </td></tr>
<tr id="row_0_0_6_143_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2zero_8hpp.html" target="_self">zero.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_monoid.html#gad459ac17b6bab8ead1cae7de0032f3c6" title="Identity of plus.">boost::hana::zero</a></code> </td></tr>
<tr id="row_0_0_6_144_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2zip_8hpp.html" target="_self">zip.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#gaa5a378d4e71a91e0d6cd3959d9818e8a" title="Zip one sequence or more.">boost::hana::zip</a></code> </td></tr>
<tr id="row_0_0_6_145_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2zip__shortest_8hpp.html" target="_self">zip_shortest.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#gade78593b3ff51fc5479e1da97142fef5" title="Zip one sequence or more.">boost::hana::zip_shortest</a></code> </td></tr>
<tr id="row_0_0_6_146_"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2zip__shortest__with_8hpp.html" target="_self">zip_shortest_with.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#gae7a51104a77db79a0407d7d67b034667" title="Zip one sequence or more with a given function.">boost::hana::zip_shortest_with</a></code> </td></tr>
<tr id="row_0_0_6_147_" class="even"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fwd_2zip__with_8hpp.html" target="_self">zip_with.hpp</a></td><td class="desc">Forward declares <code><a class="el" href="group__group-_sequence.html#ga6a4bf8549ce69b5b5b7377aec225a0e3" title="Zip one sequence or more with a given function.">boost::hana::zip_with</a></code> </td></tr>
<tr id="row_0_0_7_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="accessors_8hpp.html" target="_self">accessors.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_struct.html#ga983a55dbd93d766fd37689ea32e4ddfb" title="Returns a Sequence of pairs representing the accessors of the data structure.">boost::hana::accessors</a></code> </td></tr>
<tr id="row_0_0_8_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="adapt__adt_8hpp.html" target="_self">adapt_adt.hpp</a></td><td class="desc">Defines the <code>BOOST_HANA_ADAPT_ADT</code> macro </td></tr>
<tr id="row_0_0_9_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="adapt__struct_8hpp.html" target="_self">adapt_struct.hpp</a></td><td class="desc">Defines the <code>BOOST_HANA_ADAPT_STRUCT</code> macro </td></tr>
<tr id="row_0_0_10_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="adjust_8hpp.html" target="_self">adjust.hpp</a></td><td class="desc">Defines <code>boost::hana::adjust</code> </td></tr>
<tr id="row_0_0_11_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="adjust__if_8hpp.html" target="_self">adjust_if.hpp</a></td><td class="desc">Defines <code>boost::hana::adjust_if</code> </td></tr>
<tr id="row_0_0_12_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="all_8hpp.html" target="_self">all.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga81ae9764dd7818ad36270c6419fb1082" title="Returns whether all the keys of the structure are true-valued.">boost::hana::all</a></code> </td></tr>
<tr id="row_0_0_13_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="all__of_8hpp.html" target="_self">all_of.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga3a168950082f38afd9edf256f336c8ba" title="Returns whether all the keys of the structure satisfy the predicate.">boost::hana::all_of</a></code> </td></tr>
<tr id="row_0_0_14_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="and_8hpp.html" target="_self">and.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_logical.html#ga14066f5672867c123524e0e0978069eb" title="Return whether all the arguments are true-valued.">boost::hana::and_</a></code> </td></tr>
<tr id="row_0_0_15_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="any_8hpp.html" target="_self">any.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#gab7d632b9319b10b1eb7e98f9e1cf8a28" title="Returns whether any key of the structure is true-valued.">boost::hana::any</a></code> </td></tr>
<tr id="row_0_0_16_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="any__of_8hpp.html" target="_self">any_of.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga5f7ff0125c448983e1b96c3ffb84f646" title="Returns whether any key of the structure satisfies the predicate.">boost::hana::any_of</a></code> </td></tr>
<tr id="row_0_0_17_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ap_8hpp.html" target="_self">ap.hpp</a></td><td class="desc">Defines <code>boost::hana::ap</code> </td></tr>
<tr id="row_0_0_18_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="append_8hpp.html" target="_self">append.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad_plus.html#ga08624924fe05f0cfbfbd6e439db01873" title="Append an element to a monadic structure.">boost::hana::append</a></code> </td></tr>
<tr id="row_0_0_19_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="assert_8hpp.html" target="_self">assert.hpp</a></td><td class="desc">Defines macros to perform different kinds of assertions </td></tr>
<tr id="row_0_0_20_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="at_8hpp.html" target="_self">at.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129" title="Returns the nth element of an iterable.">boost::hana::at</a></code> and <code><a class="el" href="group__group-_iterable.html#ga4cb99cfbef936cb267e76f66f40f529c" title="Equivalent to at; provided for convenience.">boost::hana::at_c</a></code> </td></tr>
<tr id="row_0_0_21_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="at__key_8hpp.html" target="_self">at_key.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga3c1826aee6c6eb577810bb99c5c3e53d" title="Returns the value associated to the given key in a structure, or fail.">boost::hana::at_key</a></code> </td></tr>
<tr id="row_0_0_22_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="back_8hpp.html" target="_self">back.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_iterable.html#gab3f4d0035345a453284e46303862d463" title="Returns the last element of a non-empty and finite iterable.">boost::hana::back</a></code> </td></tr>
<tr id="row_0_0_23_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="basic__tuple_8hpp.html" target="_self">basic_tuple.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1basic__tuple.html" title="Stripped down version of hana::tuple.">boost::hana::basic_tuple</a></code> </td></tr>
<tr id="row_0_0_24_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="bool_8hpp.html" target="_self">bool.hpp</a></td><td class="desc">Defines the <code>Logical</code> and <code>Comparable</code> models of <code><a class="el" href="structboost_1_1hana_1_1integral__constant.html" title="Compile-time value of an integral type.">boost::hana::integral_constant</a></code> </td></tr>
<tr id="row_0_0_25_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="cartesian__product_8hpp.html" target="_self">cartesian_product.hpp</a></td><td class="desc">Defines <code>boost::hana::cartesian_product</code> </td></tr>
<tr id="row_0_0_26_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="chain_8hpp.html" target="_self">chain.hpp</a></td><td class="desc">Defines <code>boost::hana::chain</code> </td></tr>
<tr id="row_0_0_27_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="comparing_8hpp.html" target="_self">comparing.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_comparable.html#ga9c2ffe2e51780e57a38d9e7e31b87cdc" title="Returns a function performing equal after applying a transformation to both arguments.">boost::hana::comparing</a></code> </td></tr>
<tr id="row_0_0_28_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concat_8hpp.html" target="_self">concat.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad_plus.html#ga1946e96c3b4c178c7ae8703724c29c37" title="Combine two monadic structures together.">boost::hana::concat</a></code> </td></tr>
<tr id="row_0_0_29_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="concept_8hpp.html" target="_self">concept.hpp</a></td><td class="desc">Master header for the <code>boost/hana/concept/</code> subdirectory </td></tr>
<tr id="row_0_0_30_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="config_8hpp.html" target="_self">config.hpp</a></td><td class="desc">Defines configuration macros used throughout the library </td></tr>
<tr id="row_0_0_31_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="contains_8hpp.html" target="_self">contains.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga38e7748956cbc9f3d9bb035ac8577906" title="Returns whether the key occurs in the structure.">boost::hana::contains</a></code> and <code><a class="el" href="group__group-_searchable.html#ga0d9456ceda38b6ca664998e79d7c45b7" title="Return whether the key occurs in the structure.">boost::hana::in</a></code> </td></tr>
<tr id="row_0_0_32_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="core_8hpp.html" target="_self">core.hpp</a></td><td class="desc">Defines the <a class="el" href="group__group-core.html">Core</a> module </td></tr>
<tr id="row_0_0_33_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="count_8hpp.html" target="_self">count.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#ga3159cfa41be18a396926741b0a3fdefd" title="Return the number of elements in the structure that compare equal to a given value.">boost::hana::count</a></code> </td></tr>
<tr id="row_0_0_34_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="count__if_8hpp.html" target="_self">count_if.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#ga39d71be65d5b98e7d035a3e5c607e1b4" title="Return the number of elements in the structure for which the predicate is satisfied.">boost::hana::count_if</a></code> </td></tr>
<tr id="row_0_0_35_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="cycle_8hpp.html" target="_self">cycle.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad_plus.html#gaaf46c168f721da9effcc7336a997f5d6" title="Combine a monadic structure with itself n times.">boost::hana::cycle</a></code> </td></tr>
<tr id="row_0_0_36_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="define__struct_8hpp.html" target="_self">define_struct.hpp</a></td><td class="desc">Defines the <code>BOOST_HANA_DEFINE_STRUCT</code> macro </td></tr>
<tr id="row_0_0_37_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="difference_8hpp.html" target="_self">difference.hpp</a></td><td class="desc">Defines <code>boost::hana::difference</code> </td></tr>
<tr id="row_0_0_38_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="div_8hpp.html" target="_self">div.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_euclidean_ring.html#ga4225a7988ce98903228913dde53762df" title="Generalized integer division.">boost::hana::div</a></code> </td></tr>
<tr id="row_0_0_39_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="drop__back_8hpp.html" target="_self">drop_back.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7" title="Drop the last n elements of a finite sequence, and return the rest.">boost::hana::drop_back</a></code> </td></tr>
<tr id="row_0_0_40_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="drop__front_8hpp.html" target="_self">drop_front.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757" title="Drop the first n elements of an iterable, and return the rest.">boost::hana::drop_front</a></code> </td></tr>
<tr id="row_0_0_41_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="drop__front__exactly_8hpp.html" target="_self">drop_front_exactly.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4" title="Drop the first n elements of an iterable, and return the rest.">boost::hana::drop_front_exactly</a></code> </td></tr>
<tr id="row_0_0_42_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="drop__while_8hpp.html" target="_self">drop_while.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_iterable.html#ga9f1d02c74a6bdc1db260e0d6a8f1ee56" title="Drop elements from an iterable up to, but excluding, the first element for which the predicate is not...">boost::hana::drop_while</a></code> </td></tr>
<tr id="row_0_0_43_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="duplicate_8hpp.html" target="_self">duplicate.hpp</a></td><td class="desc">Defines <code>boost::hana::duplicate</code> </td></tr>
<tr id="row_0_0_44_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="empty_8hpp.html" target="_self">empty.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad_plus.html#gaa6be1e83ad72b9d69b43b4bada0f3a75" title="Identity of the monadic combination concat.">boost::hana::empty</a></code> </td></tr>
<tr id="row_0_0_45_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="equal_8hpp.html" target="_self">equal.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_comparable.html#gacaf1ebea6b3ab96ac9dcb82f0e64e547" title="Returns a Logical representing whether x is equal to y.">boost::hana::equal</a></code> </td></tr>
<tr id="row_0_0_46_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="erase__key_8hpp.html" target="_self">erase_key.hpp</a></td><td class="desc">Defines <code>boost::hana::erase_key</code> </td></tr>
<tr id="row_0_0_47_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="eval_8hpp.html" target="_self">eval.hpp</a></td><td class="desc">Defines <code>boost::hana::eval</code> </td></tr>
<tr id="row_0_0_48_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="eval__if_8hpp.html" target="_self">eval_if.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_logical.html#gab64636f84de983575aac0208f5fa840c" title="Conditionally execute one of two branches based on a condition.">boost::hana::eval_if</a></code> </td></tr>
<tr id="row_0_0_49_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="extend_8hpp.html" target="_self">extend.hpp</a></td><td class="desc">Defines <code>boost::hana::extend</code> </td></tr>
<tr id="row_0_0_50_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="extract_8hpp.html" target="_self">extract.hpp</a></td><td class="desc">Defines <code>boost::hana::extract</code> </td></tr>
<tr id="row_0_0_51_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fill_8hpp.html" target="_self">fill.hpp</a></td><td class="desc">Defines <code>boost::hana::fill</code> </td></tr>
<tr id="row_0_0_52_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="filter_8hpp.html" target="_self">filter.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad_plus.html#ga65cc6d9f522fb9e8e3b28d80ee5c822a" title="Filter a monadic structure using a custom predicate.">boost::hana::filter</a></code> </td></tr>
<tr id="row_0_0_53_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="find_8hpp.html" target="_self">find.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga6b6cdd69942b0fe3bf5254247f9c861e" title="Finds the value associated to the given key in a structure.">boost::hana::find</a></code> </td></tr>
<tr id="row_0_0_54_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="find__if_8hpp.html" target="_self">find_if.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f" title="Finds the value associated to the first key satisfying a predicate.">boost::hana::find_if</a></code> </td></tr>
<tr id="row_0_0_55_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="first_8hpp.html" target="_self">first.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_product.html#ga34bbf4281de06dc3540441e8b2bd24f4" title="Returns the first element of a pair.">boost::hana::first</a></code> </td></tr>
<tr id="row_0_0_56_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="flatten_8hpp.html" target="_self">flatten.hpp</a></td><td class="desc">Defines <code>boost::hana::flatten</code> </td></tr>
<tr id="row_0_0_57_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fold_8hpp.html" target="_self">fold.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#gaa0fde17f3b947a0678a1c0c01232f2cc" title="Equivalent to fold_left; provided for convenience.">boost::hana::fold</a></code> </td></tr>
<tr id="row_0_0_58_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fold__left_8hpp.html" target="_self">fold_left.hpp</a></td><td class="desc">Defines <code>boost::hana::fold_left</code> </td></tr>
<tr id="row_0_0_59_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fold__right_8hpp.html" target="_self">fold_right.hpp</a></td><td class="desc">Defines <code>boost::hana::fold_right</code> </td></tr>
<tr id="row_0_0_60_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="for__each_8hpp.html" target="_self">for_each.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#ga2af382f7e644ce3707710bbad313e9c2" title="Perform an action on each element of a foldable, discarding the result each time.">boost::hana::for_each</a></code> </td></tr>
<tr id="row_0_0_61_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="front_8hpp.html" target="_self">front.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444" title="Returns the first element of a non-empty iterable.">boost::hana::front</a></code> </td></tr>
<tr id="row_0_0_62_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="functional_8hpp.html" target="_self">functional.hpp</a></td><td class="desc">Defines the <a class="el" href="group__group-functional.html">Functional</a> module </td></tr>
<tr id="row_0_0_63_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="fuse_8hpp.html" target="_self">fuse.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#ga19fcf61d8d1179903952c0f564c538aa" title="Transform a function taking multiple arguments into a function that can be called with a compile-time...">boost::hana::fuse</a></code> </td></tr>
<tr id="row_0_0_64_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="greater_8hpp.html" target="_self">greater.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_orderable.html#gaf9a073eafebbe514fb19dff82318f198" title="Returns a Logical representing whether x is greater than y.">boost::hana::greater</a></code> </td></tr>
<tr id="row_0_0_65_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="greater__equal_8hpp.html" target="_self">greater_equal.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_orderable.html#ga6023631e7d0a01e16dc3fa4221fbd703" title="Returns a Logical representing whether x is greater than or equal to y.">boost::hana::greater_equal</a></code> </td></tr>
<tr id="row_0_0_66_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="group_8hpp.html" target="_self">group.hpp</a></td><td class="desc">Defines <code>boost::hana::group</code> </td></tr>
<tr id="row_0_0_67_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="hash_8hpp.html" target="_self">hash.hpp</a></td><td class="desc">Defines <code>boost::hana::hash</code> </td></tr>
<tr id="row_0_0_68_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="if_8hpp.html" target="_self">if.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_logical.html#gafd655d2222367131e7a63616e93dd080" title="Conditionally return one of two values based on a condition.">boost::hana::if_</a></code> </td></tr>
<tr id="row_0_0_69_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="index__if_8hpp.html" target="_self">index_if.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87" title="Finds the value associated to the first key satisfying a predicate.">boost::hana::index_if</a></code> </td></tr>
<tr id="row_0_0_70_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="insert_8hpp.html" target="_self">insert.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#gae22a1a184b1b2dd550fa4fa619bed2e9" title="Insert a value at a given index in a sequence.">boost::hana::insert</a></code> </td></tr>
<tr id="row_0_0_71_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="insert__range_8hpp.html" target="_self">insert_range.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga3410ba833cf1ff1d929fcfda4df2eae1" title="Insert several values at a given index in a sequence.">boost::hana::insert_range</a></code> </td></tr>
<tr id="row_0_0_72_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="integral__constant_8hpp.html" target="_self">integral_constant.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1integral__constant.html" title="Compile-time value of an integral type.">boost::hana::integral_constant</a></code> </td></tr>
<tr id="row_0_0_73_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="intersection_8hpp.html" target="_self">intersection.hpp</a></td><td class="desc">Defines <code>boost::hana::intersection</code> </td></tr>
<tr id="row_0_0_74_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="intersperse_8hpp.html" target="_self">intersperse.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#gaa18061cd0f63cfaae89abf43ff92b79e" title="Insert a value between each pair of elements in a finite sequence.">boost::hana::intersperse</a></code> </td></tr>
<tr id="row_0_0_75_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="is__disjoint_8hpp.html" target="_self">is_disjoint.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga3b8269d4f5cdd6dd549fae32280795a0" title="Returns whether two Searchables are disjoint.">boost::hana::is_disjoint</a></code> </td></tr>
<tr id="row_0_0_76_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="is__empty_8hpp.html" target="_self">is_empty.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394" title="Returns whether the iterable is empty.">boost::hana::is_empty</a></code> </td></tr>
<tr id="row_0_0_77_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="is__subset_8hpp.html" target="_self">is_subset.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#gadccfc79f1acdd8043d2baa16df16ec9f" title="Returns whether a structure contains a subset of the keys of another structure.">boost::hana::is_subset</a></code> </td></tr>
<tr id="row_0_0_78_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="keys_8hpp.html" target="_self">keys.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_struct.html#gaf8c7199742581e6e66c8397def68e2d3" title="Returns a Sequence containing the name of the members of the data structure.">boost::hana::keys</a></code> </td></tr>
<tr id="row_0_0_79_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="lazy_8hpp.html" target="_self">lazy.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1lazy.html" title="hana::lazy implements superficial laziness via a monadic interface.">boost::hana::lazy</a></code> </td></tr>
<tr id="row_0_0_80_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="length_8hpp.html" target="_self">length.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#gaf0f8f717245620dc28cd7d7fa44d7475" title="Return the number of elements in a foldable structure.">boost::hana::length</a></code> </td></tr>
<tr id="row_0_0_81_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="less_8hpp.html" target="_self">less.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_orderable.html#gad510011602bdb14686f1c4ec145301c9" title="Returns a Logical representing whether x is less than y.">boost::hana::less</a></code> </td></tr>
<tr id="row_0_0_82_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="less__equal_8hpp.html" target="_self">less_equal.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_orderable.html#ga9917dd82beb67151bf5657245d37b851" title="Returns a Logical representing whether x is less than or equal to y.">boost::hana::less_equal</a></code> </td></tr>
<tr id="row_0_0_83_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="lexicographical__compare_8hpp.html" target="_self">lexicographical_compare.hpp</a></td><td class="desc">Defines <code>boost::hana::lexicographical_compare</code> </td></tr>
<tr id="row_0_0_84_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="lift_8hpp.html" target="_self">lift.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_applicative.html#ga712038d7abbc7159f8792788f7cd0c73" title="Lift a value into an Applicative structure.">boost::hana::lift</a></code> </td></tr>
<tr id="row_0_0_85_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="map_8hpp.html" target="_self">map.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1map.html" title="Basic associative container requiring unique, Comparable and Hashable keys.">boost::hana::map</a></code> </td></tr>
<tr id="row_0_0_86_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="max_8hpp.html" target="_self">max.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_orderable.html#ga999eee8ca8750f9b1afa0d7a1db28030" title="Returns the greatest of its arguments according to the less ordering.">boost::hana::max</a></code> </td></tr>
<tr id="row_0_0_87_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="maximum_8hpp.html" target="_self">maximum.hpp</a></td><td class="desc">Defines <code>boost::hana::maximum</code> </td></tr>
<tr id="row_0_0_88_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="members_8hpp.html" target="_self">members.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_struct.html#gad301dd8e9fb4639d7874619c97d6d427" title="Returns a Sequence containing the members of a Struct.">boost::hana::members</a></code> </td></tr>
<tr id="row_0_0_89_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="min_8hpp.html" target="_self">min.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_orderable.html#ga2d54f189ea6f57fb2c0d772169440c5c" title="Returns the smallest of its arguments according to the less ordering.">boost::hana::min</a></code> </td></tr>
<tr id="row_0_0_90_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="minimum_8hpp.html" target="_self">minimum.hpp</a></td><td class="desc">Defines <code>boost::hana::minimum</code> </td></tr>
<tr id="row_0_0_91_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="minus_8hpp.html" target="_self">minus.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_group.html#ga2020c526324f361a2b990fe8d1b07c20" title="Subtract two elements of a group.">boost::hana::minus</a></code> </td></tr>
<tr id="row_0_0_92_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="mod_8hpp.html" target="_self">mod.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_euclidean_ring.html#ga9b47b223d5b02db933b3c93b5bd1a062" title="Generalized integer modulus.">boost::hana::mod</a></code> </td></tr>
<tr id="row_0_0_93_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="monadic__compose_8hpp.html" target="_self">monadic_compose.hpp</a></td><td class="desc">Defines <code>boost::hana::monadic_compose</code> </td></tr>
<tr id="row_0_0_94_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="monadic__fold__left_8hpp.html" target="_self">monadic_fold_left.hpp</a></td><td class="desc">Defines <code>boost::hana::monadic_fold_left</code> </td></tr>
<tr id="row_0_0_95_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="monadic__fold__right_8hpp.html" target="_self">monadic_fold_right.hpp</a></td><td class="desc">Defines <code>boost::hana::monadic_fold_right</code> </td></tr>
<tr id="row_0_0_96_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="mult_8hpp.html" target="_self">mult.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_ring.html#ga052d31c269a6a438cc8004c9ad1efdfa" title="Associative operation of a Ring.">boost::hana::mult</a></code> </td></tr>
<tr id="row_0_0_97_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="negate_8hpp.html" target="_self">negate.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_group.html#ga02e81002f40ba52eac4cf1974c7e0cdb" title="Return the inverse of an element of a group.">boost::hana::negate</a></code> </td></tr>
<tr id="row_0_0_98_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="none_8hpp.html" target="_self">none.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga614ff1e575806f59246b17006e19d479" title="Returns whether all of the keys of the structure are false-valued.">boost::hana::none</a></code> </td></tr>
<tr id="row_0_0_99_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="none__of_8hpp.html" target="_self">none_of.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_searchable.html#ga43954c791b5b1351fb009e2a643d00f5" title="Returns whether none of the keys of the structure satisfy the predicate.">boost::hana::none_of</a></code> </td></tr>
<tr id="row_0_0_100_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="not_8hpp.html" target="_self">not.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_logical.html#ga4a7c9d7037601d5e553fd20777958980" title="Negates a Logical.">boost::hana::not_</a></code> </td></tr>
<tr id="row_0_0_101_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="not__equal_8hpp.html" target="_self">not_equal.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_comparable.html#gae33be2e0d5e04f19082f4b7740dfc9cd" title="Returns a Logical representing whether x is not equal to y.">boost::hana::not_equal</a></code> </td></tr>
<tr id="row_0_0_102_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="one_8hpp.html" target="_self">one.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_ring.html#gadea531feb3b0a1c5c3d777f7ab45e932" title="Identity of the Ring multiplication.">boost::hana::one</a></code> </td></tr>
<tr id="row_0_0_103_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="optional_8hpp.html" target="_self">optional.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1optional.html" title="Optional value whose optional-ness is known at compile-time.">boost::hana::optional</a></code> </td></tr>
<tr id="row_0_0_104_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="or_8hpp.html" target="_self">or.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_logical.html#ga68c00efbeb69339bfa157a78ebdd3f87" title="Return whether any of the arguments is true-valued.">boost::hana::or_</a></code> </td></tr>
<tr id="row_0_0_105_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="ordering_8hpp.html" target="_self">ordering.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_orderable.html#gaf7e94ba859710cd6ba6152e5dc18977d" title="Returns a function performing less after applying a transformation to both arguments.">boost::hana::ordering</a></code> </td></tr>
<tr id="row_0_0_106_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="pair_8hpp.html" target="_self">pair.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1pair.html" title="Generic container for two elements.">boost::hana::pair</a></code> </td></tr>
<tr id="row_0_0_107_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="partition_8hpp.html" target="_self">partition.hpp</a></td><td class="desc">Defines <code>boost::hana::partition</code> </td></tr>
<tr id="row_0_0_108_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="permutations_8hpp.html" target="_self">permutations.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#gac1e182ac088f1990edd739424d30ea07" title="Return a sequence of all the permutations of the given sequence.">boost::hana::permutations</a></code> </td></tr>
<tr id="row_0_0_109_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="plus_8hpp.html" target="_self">plus.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monoid.html#gaeb5d4a1e967e319712f9e4791948896c" title="Associative binary operation on a Monoid.">boost::hana::plus</a></code> </td></tr>
<tr id="row_0_0_110_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="power_8hpp.html" target="_self">power.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_ring.html#ga0ee3cff9ec646bcc7217f00ee6099b72" title="Elevate a ring element to its nth power.">boost::hana::power</a></code> </td></tr>
<tr id="row_0_0_111_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="prefix_8hpp.html" target="_self">prefix.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad_plus.html#ga3022fdfe454dc9bc1f79b5dfeba13b5e" title="Inserts a value before each element of a monadic structure.">boost::hana::prefix</a></code> </td></tr>
<tr id="row_0_0_112_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="prepend_8hpp.html" target="_self">prepend.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad_plus.html#ga69afbfd4e91125e3e52fcb409135ca7c" title="Prepend an element to a monadic structure.">boost::hana::prepend</a></code> </td></tr>
<tr id="row_0_0_113_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="product_8hpp.html" target="_self">product.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#ga17fe9c1982c882807f3358b4138c5744" title="Compute the product of the numbers of a structure.">boost::hana::product</a></code> </td></tr>
<tr id="row_0_0_114_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="range_8hpp.html" target="_self">range.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1range.html" title="Compile-time half-open interval of hana::integral_constants.">boost::hana::range</a></code> </td></tr>
<tr id="row_0_0_115_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="remove_8hpp.html" target="_self">remove.hpp</a></td><td class="desc">Defines <code>boost::hana::remove</code> </td></tr>
<tr id="row_0_0_116_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="remove__at_8hpp.html" target="_self">remove_at.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga80724ec8ecf319a1e695988a69e22f87" title="Remove the element at a given index from a sequence.">boost::hana::remove_at</a></code> and <code><a class="el" href="group__group-_sequence.html#gae70b0815645c7d81bb636a1eed1a65c6" title="Equivalent to remove_at; provided for convenience.">boost::hana::remove_at_c</a></code> </td></tr>
<tr id="row_0_0_117_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="remove__if_8hpp.html" target="_self">remove_if.hpp</a></td><td class="desc">Defines <code>boost::hana::remove_if</code> </td></tr>
<tr id="row_0_0_118_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="remove__range_8hpp.html" target="_self">remove_range.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga6f6d5c1f335780c91d29626fde615c78" title="Remove the elements inside a given range of indices from a sequence.">boost::hana::remove_range</a></code> and <code><a class="el" href="group__group-_sequence.html#ga4696efcdee7d95ab4a391bb896a840b5" title="Equivalent to remove_range; provided for convenience.">boost::hana::remove_range_c</a></code> </td></tr>
<tr id="row_0_0_119_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="repeat_8hpp.html" target="_self">repeat.hpp</a></td><td class="desc">Defines <code><a class="el" href="namespaceboost_1_1hana.html#a405f3dd84fc6f5003e64f8da104a1b54" title="Invokes a nullary function n times.">boost::hana::repeat</a></code> </td></tr>
<tr id="row_0_0_120_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="replace_8hpp.html" target="_self">replace.hpp</a></td><td class="desc">Defines <code>boost::hana::replace</code> </td></tr>
<tr id="row_0_0_121_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="replace__if_8hpp.html" target="_self">replace_if.hpp</a></td><td class="desc">Defines <code>boost::hana::replace_if</code> </td></tr>
<tr id="row_0_0_122_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="replicate_8hpp.html" target="_self">replicate.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad_plus.html#gad5f48c79d11923d6c1d70b18b7dd3f19" title="Create a monadic structure by combining a lifted value with itself n times.">boost::hana::replicate</a></code> </td></tr>
<tr id="row_0_0_123_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="reverse_8hpp.html" target="_self">reverse.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga28037560e8f224c53cf6ac168d03a067" title="Reverse a sequence.">boost::hana::reverse</a></code> </td></tr>
<tr id="row_0_0_124_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="reverse__fold_8hpp.html" target="_self">reverse_fold.hpp</a></td><td class="desc">Defines <code>boost::hana::reverse_fold</code> </td></tr>
<tr id="row_0_0_125_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="scan__left_8hpp.html" target="_self">scan_left.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd" title="Fold a Sequence to the left and return a list containing the successive reduction states.">boost::hana::scan_left</a></code> </td></tr>
<tr id="row_0_0_126_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="scan__right_8hpp.html" target="_self">scan_right.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab" title="Fold a Sequence to the right and return a list containing the successive reduction states.">boost::hana::scan_right</a></code> </td></tr>
<tr id="row_0_0_127_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="second_8hpp.html" target="_self">second.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_product.html#ga7bb979d59ffc3ab862cb7d9dc7730077" title="Returns the second element of a pair.">boost::hana::second</a></code> </td></tr>
<tr id="row_0_0_128_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="set_8hpp.html" target="_self">set.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1set.html" title="Basic unordered container requiring unique, Comparable and Hashable keys.">boost::hana::set</a></code> </td></tr>
<tr id="row_0_0_129_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="size_8hpp.html" target="_self">size.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#ga8ec3ac9a6f5014db943f61ebc9e1e36e" title="Equivalent to length; provided for consistency with the standard library.">boost::hana::size</a></code> </td></tr>
<tr id="row_0_0_130_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="slice_8hpp.html" target="_self">slice.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081" title="Extract the elements of a Sequence at the given indices.">boost::hana::slice</a></code> and <code><a class="el" href="group__group-_sequence.html#gae1f6a2a9cb70564d43c6b3c663b25dd7" title="Shorthand to slice a contiguous range of elements.">boost::hana::slice_c</a></code> </td></tr>
<tr id="row_0_0_131_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="sort_8hpp.html" target="_self">sort.hpp</a></td><td class="desc">Defines <code>boost::hana::sort</code> </td></tr>
<tr id="row_0_0_132_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="span_8hpp.html" target="_self">span.hpp</a></td><td class="desc">Defines <code>boost::hana::span</code> </td></tr>
<tr id="row_0_0_133_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="string_8hpp.html" target="_self">string.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1string.html" title="Compile-time string.">boost::hana::string</a></code> </td></tr>
<tr id="row_0_0_134_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="suffix_8hpp.html" target="_self">suffix.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad_plus.html#ga61dab15f6ecf379121d4096fe0c8ab13" title="Inserts a value after each element of a monadic structure.">boost::hana::suffix</a></code> </td></tr>
<tr id="row_0_0_135_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="sum_8hpp.html" target="_self">sum.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#ga650def4b2e98f4273d8b9b7aa5a2fc28" title="Compute the sum of the numbers of a structure.">boost::hana::sum</a></code> </td></tr>
<tr id="row_0_0_136_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="symmetric__difference_8hpp.html" target="_self">symmetric_difference.hpp</a></td><td class="desc">Defines <code>boost::hana::symmetric_difference</code> </td></tr>
<tr id="row_0_0_137_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="take__back_8hpp.html" target="_self">take_back.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920" title="Returns the last n elements of a sequence, or the whole sequence if the sequence has less than n elem...">boost::hana::take_back</a></code> </td></tr>
<tr id="row_0_0_138_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="take__front_8hpp.html" target="_self">take_front.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316" title="Returns the first n elements of a sequence, or the whole sequence if the sequence has less than n ele...">boost::hana::take_front</a></code> and <code><a class="el" href="group__group-_sequence.html#ga3779f62fea92af00113a9290f1c680eb" title="Equivalent to take_front; provided for convenience.">boost::hana::take_front_c</a></code> </td></tr>
<tr id="row_0_0_139_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="take__while_8hpp.html" target="_self">take_while.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga2d4db4ec5ec5bc16fe74f57de12697fd" title="Take elements from a sequence while the predicate is satisfied.">boost::hana::take_while</a></code> </td></tr>
<tr id="row_0_0_140_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="tap_8hpp.html" target="_self">tap.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad.html#ga5e0735de01a24f681c55aedfeb6d13bf" title="Tap inside a monadic chain.">boost::hana::tap</a></code> </td></tr>
<tr id="row_0_0_141_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="then_8hpp.html" target="_self">then.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monad.html#gaaddd3789de43cf989babb10cdc0b447a" title="Sequentially compose two monadic actions, discarding any value produced by the first but not its effe...">boost::hana::then</a></code> </td></tr>
<tr id="row_0_0_142_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="traits_8hpp.html" target="_self">traits.hpp</a></td><td class="desc">Defines function-like equivalents to the standard <code>&lt;type_traits&gt;</code>, and also to some utilities like <code>std::declval</code> </td></tr>
<tr id="row_0_0_143_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="transform_8hpp.html" target="_self">transform.hpp</a></td><td class="desc">Defines <code>boost::hana::transform</code> </td></tr>
<tr id="row_0_0_144_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="tuple_8hpp.html" target="_self">tuple.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1tuple.html" title="General purpose index-based heterogeneous sequence with a fixed length.">boost::hana::tuple</a></code> </td></tr>
<tr id="row_0_0_145_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="type_8hpp.html" target="_self">type.hpp</a></td><td class="desc">Defines <code><a class="el" href="structboost_1_1hana_1_1type.html" title="C++ type in value-level representation.">boost::hana::type</a></code> and related utilities </td></tr>
<tr id="row_0_0_146_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="unfold__left_8hpp.html" target="_self">unfold_left.hpp</a></td><td class="desc">Defines <code>boost::hana::unfold_left</code> </td></tr>
<tr id="row_0_0_147_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="unfold__right_8hpp.html" target="_self">unfold_right.hpp</a></td><td class="desc">Defines <code>boost::hana::unfold_right</code> </td></tr>
<tr id="row_0_0_148_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="union_8hpp.html" target="_self">union.hpp</a></td><td class="desc">Defines <code>boost::hana::union</code> </td></tr>
<tr id="row_0_0_149_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="unique_8hpp.html" target="_self">unique.hpp</a></td><td class="desc">Defines <code>boost::hana::unique</code> </td></tr>
<tr id="row_0_0_150_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="unpack_8hpp.html" target="_self">unpack.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_foldable.html#ga7b0c23944364ce61136e10b978ae2170" title="Invoke a function with the elements of a Foldable as arguments.">boost::hana::unpack</a></code> </td></tr>
<tr id="row_0_0_151_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="value_8hpp.html" target="_self">value.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_constant.html#ga1687520692a6b0c49e3a69de2980f388" title="Return the compile-time value associated to a constant.">boost::hana::value</a></code> </td></tr>
<tr id="row_0_0_152_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="version_8hpp.html" target="_self">version.hpp</a></td><td class="desc">Defines macros for tracking the version of the library </td></tr>
<tr id="row_0_0_153_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="while_8hpp.html" target="_self">while.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_logical.html#ga08a767b86c330cac67daa891406d2730" title="Apply a function to an initial state while some predicate is satisfied.">boost::hana::while_</a></code> </td></tr>
<tr id="row_0_0_154_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="zero_8hpp.html" target="_self">zero.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_monoid.html#gad459ac17b6bab8ead1cae7de0032f3c6" title="Identity of plus.">boost::hana::zero</a></code> </td></tr>
<tr id="row_0_0_155_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="zip_8hpp.html" target="_self">zip.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#gaa5a378d4e71a91e0d6cd3959d9818e8a" title="Zip one sequence or more.">boost::hana::zip</a></code> </td></tr>
<tr id="row_0_0_156_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="zip__shortest_8hpp.html" target="_self">zip_shortest.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#gade78593b3ff51fc5479e1da97142fef5" title="Zip one sequence or more.">boost::hana::zip_shortest</a></code> </td></tr>
<tr id="row_0_0_157_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="zip__shortest__with_8hpp.html" target="_self">zip_shortest_with.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#gae7a51104a77db79a0407d7d67b034667" title="Zip one sequence or more with a given function.">boost::hana::zip_shortest_with</a></code> </td></tr>
<tr id="row_0_0_158_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="zip__with_8hpp.html" target="_self">zip_with.hpp</a></td><td class="desc">Defines <code><a class="el" href="group__group-_sequence.html#ga6a4bf8549ce69b5b5b7377aec225a0e3" title="Zip one sequence or more with a given function.">boost::hana::zip_with</a></code> </td></tr>
<tr id="row_0_1_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icondoc"></span><a class="el" href="hana_8hpp.html" target="_self">hana.hpp</a></td><td class="desc">Includes all the library components except the adapters for external libraries </td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
</div><!-- doc-content -->
<!--
Copyright Louis Dionne 2013-2017
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
-->
<!-- boost-no-inspect -->
<!-- HTML footer for doxygen *******-->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
  </ul>
</div>
</body>
</html>
