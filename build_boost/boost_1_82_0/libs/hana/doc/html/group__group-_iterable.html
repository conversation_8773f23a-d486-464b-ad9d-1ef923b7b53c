<!--
Copyright <PERSON> 2013-2017
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
-->
<!-- boost-no-inspect -->
<!-- HTML header for doxygen *******-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<title>Boost.Hana: Iterable</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
// Copyright Louis Dionne 2013-2017
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
MathJax.Hub.Config({
    "HTML-CSS": {
        linebreaks: {
            automatic: true,
            width: "75% container"
        }
    }
});
</script>
<script type="text/javascript" async="async" src="https://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<!-- Additional javascript for drawing charts. -->
<script type="text/javascript" src="highcharts.js"></script>
<script type="text/javascript" src="highcharts-data.js"></script>
<script type="text/javascript" src="highcharts-exporting.js"></script>
<script type="text/javascript" src="chart.js"></script>
<script type="text/javascript" src="hana.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="Boost.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">Boost.Hana
   &#160;<span id="projectnumber">1.7.1</span>
   </div>
   <div id="projectbrief">Your standard library for metaprogramming</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('group__group-_iterable.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">Iterable<div class="ingroups"><a class="el" href="group__group-concepts.html">Concepts</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>The <code>Iterable</code> concept represents data structures supporting external iteration. </p>
<p>Intuitively, an <code>Iterable</code> can be seen as a kind of container whose elements can be pulled out one at a time. An <code>Iterable</code> also provides a way to know when the <em>container</em> is empty, i.e. when there are no more elements to pull out.</p>
<p>Whereas <code>Foldable</code> represents data structures supporting internal iteration with the ability to accumulate a result, the <code>Iterable</code> concept allows inverting the control of the iteration. This is more flexible than <code>Foldable</code>, since it allows iterating over only some part of the structure. This, in turn, allows <code>Iterable</code> to work on infinite structures, while trying to fold such a structure would never finish.</p>
<h2><a class="anchor" id="autotoc_md123"></a>
Minimal complete definition</h2>
<p><code>at</code>, <code>drop_front</code> and <code>is_empty</code></p>
<p><a class="anchor" id="Iterable-lin"></a></p>
<h2><a class="anchor" id="autotoc_md124"></a>
The linearization of an &lt;tt&gt;Iterable&lt;/tt&gt;</h2>
<p>Intuitively, for an <code>Iterable</code> structure <code>xs</code>, the <em>linearization</em> of <code>xs</code> is the sequence of all the elements in <code>xs</code> as if they had been put in a (possibly infinite) list: </p><div class="fragment"><div class="line">linearization(xs) = [x1, x2, x3, ...]</div>
</div><!-- fragment --><p>The <code>n</code>th element of the linearization of an <code>Iterable</code> can be accessed with the <code>at</code> function. In other words, <code>at(xs, n) == xn</code>.</p>
<p>Note that this notion is precisely the extension of the <a class="el" href="group__group-_foldable.html#Foldable-lin">linearization</a> notion of <code>Foldable</code>s to the infinite case. This notion is useful for expressing various properties of <code>Iterable</code>s, and is used for that elsewhere in the documentation.</p>
<h2><a class="anchor" id="autotoc_md125"></a>
Compile-time &lt;tt&gt;Iterable&lt;/tt&gt;s</h2>
<p>A <em>compile-time</em> <code>Iterable</code> is an <code>Iterable</code> for which <code>is_empty</code> returns a compile-time <code>Logical</code>. These structures allow iteration to be done at compile-time, in the sense that the "loop" doing the iteration can be unrolled because the total length of the structure is kown at compile-time.</p>
<p>In particular, note that being a compile-time <code>Iterable</code> has nothing to do with being finite or infinite. For example, it would be possible to create a sequence representing the Pythagorean triples as <code><a class="el" href="structboost_1_1hana_1_1integral__constant.html" title="Compile-time value of an integral type.">integral_constant</a></code>s. Such a sequence would be infinite, but iteration on the sequence would still be done at compile-time. However, if one tried to iterate over <em>all</em> the elements of the sequence, the compiler would loop indefinitely, in contrast to your program looping indefinitely if the sequence was a runtime one.</p>
<p><b>In the current version of the library, only compile-time <code>Iterable</code>s are supported.</b> While it would be possible in theory to support runtime <code>Iterable</code>s, doing it efficiently is the subject of some research. In particular, follow <a href="https://github.com/boostorg/hana/issues/40">this issue</a> for the current status of runtime <code>Iterable</code>s.</p>
<h2><a class="anchor" id="autotoc_md126"></a>
Laws</h2>
<p>First, we require the equality of two <code>Iterable</code>s to be related to the equality of the elements in their linearizations. More specifically, if <code>xs</code> and <code>ys</code> are two <code>Iterable</code>s of data type <code>It</code>, then </p><div class="fragment"><div class="line">xs == ys  =&gt;  <a class="code" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">at</a>(xs, i) == <a class="code" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">at</a>(ys, i)   <span class="keywordflow">for</span> <a class="code" href="group__group-_searchable.html#ga81ae9764dd7818ad36270c6419fb1082">all</a> i</div>
<div class="ttc" id="agroup__group-_iterable_html_ga8a484304380eae38f3d9663d98860129"><div class="ttname"><a href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">boost::hana::at</a></div><div class="ttdeci">constexpr auto at</div><div class="ttdoc">Returns the nth element of an iterable.</div><div class="ttdef"><b>Definition:</b> at.hpp:50</div></div>
<div class="ttc" id="agroup__group-_searchable_html_ga81ae9764dd7818ad36270c6419fb1082"><div class="ttname"><a href="group__group-_searchable.html#ga81ae9764dd7818ad36270c6419fb1082">boost::hana::all</a></div><div class="ttdeci">constexpr auto all</div><div class="ttdoc">Returns whether all the keys of the structure are true-valued.</div><div class="ttdef"><b>Definition:</b> all.hpp:30</div></div>
</div><!-- fragment --><p>This conveys that two <code>Iterable</code>s must have the same linearization in order to be considered equal.</p>
<p>Secondly, since every <code>Iterable</code> is also a <code>Searchable</code>, we require the models of <code>Iterable</code> and <code>Searchable</code> to be consistent. This is made precise by the following laws. For any <code>Iterable</code> <code>xs</code> with a linearization of <code>[x1, x2, x3, ...]</code>, </p><div class="fragment"><div class="line"><a class="code" href="group__group-_searchable.html#ga5f7ff0125c448983e1b96c3ffb84f646">any_of</a>(xs, <a class="code" href="group__group-_comparable.html#gacaf1ebea6b3ab96ac9dcb82f0e64e547">equal</a>.to(z))  &lt;=&gt;  xi == z</div>
<div class="ttc" id="agroup__group-_comparable_html_gacaf1ebea6b3ab96ac9dcb82f0e64e547"><div class="ttname"><a href="group__group-_comparable.html#gacaf1ebea6b3ab96ac9dcb82f0e64e547">boost::hana::equal</a></div><div class="ttdeci">constexpr auto equal</div><div class="ttdoc">Returns a Logical representing whether x is equal to y.</div><div class="ttdef"><b>Definition:</b> equal.hpp:64</div></div>
<div class="ttc" id="agroup__group-_searchable_html_ga5f7ff0125c448983e1b96c3ffb84f646"><div class="ttname"><a href="group__group-_searchable.html#ga5f7ff0125c448983e1b96c3ffb84f646">boost::hana::any_of</a></div><div class="ttdeci">constexpr auto any_of</div><div class="ttdoc">Returns whether any key of the structure satisfies the predicate.</div><div class="ttdef"><b>Definition:</b> any_of.hpp:37</div></div>
</div><!-- fragment --><p> for some <em>finite</em> index <code>i</code>. Furthermore, </p><div class="fragment"><div class="line"><a class="code" href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f">find_if</a>(xs, pred) == just(the <a class="code" href="group__group-_product.html#ga34bbf4281de06dc3540441e8b2bd24f4">first</a> xi such that pred(xi) is satisfied)</div>
<div class="ttc" id="agroup__group-_product_html_ga34bbf4281de06dc3540441e8b2bd24f4"><div class="ttname"><a href="group__group-_product.html#ga34bbf4281de06dc3540441e8b2bd24f4">boost::hana::first</a></div><div class="ttdeci">constexpr auto first</div><div class="ttdoc">Returns the first element of a pair.</div><div class="ttdef"><b>Definition:</b> first.hpp:33</div></div>
<div class="ttc" id="agroup__group-_searchable_html_ga7f99b80672aa80a7eb8b223955ce546f"><div class="ttname"><a href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f">boost::hana::find_if</a></div><div class="ttdeci">constexpr auto find_if</div><div class="ttdoc">Finds the value associated to the first key satisfying a predicate.</div><div class="ttdef"><b>Definition:</b> find_if.hpp:41</div></div>
</div><!-- fragment --><p> or <code>nothing</code> if no such <code>xi</code> exists.</p>
<h2><a class="anchor" id="autotoc_md127"></a>
Refined concepts</h2>
<ol type="1">
<li><code>Searchable</code> (free model)<br  />
Any <code>Iterable</code> gives rise to a model of <code>Searchable</code>, where the keys and the values are both the elements in the structure. Searching for a key is just doing a linear search through the elements of the structure. <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="ext_2std_2integral__constant_8hpp.html">boost/hana/ext/std/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="find_8hpp.html">boost/hana/find.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="find__if_8hpp.html">boost/hana/find_if.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="compose_8hpp.html">boost/hana/functional/compose.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="optional_8hpp.html">boost/hana/optional.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="type_8hpp.html">boost/hana/type.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;type_traits&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">// First get the type of the object, and then call the trait on it.</span></div>
<div class="line">constexpr <span class="keyword">auto</span> is_integral = <a class="code" href="group__group-functional.html#ga3b16146e53efcdf9ecbb9a7b21f8cd0b">hana::compose</a>(hana::trait&lt;std::is_integral&gt;, hana::typeid_);</div>
<div class="line">constexpr <span class="keyword">auto</span> is_class = <a class="code" href="group__group-functional.html#ga3b16146e53efcdf9ecbb9a7b21f8cd0b">hana::compose</a>(hana::trait&lt;std::is_class&gt;, hana::typeid_);</div>
<div class="line"> </div>
<div class="line">static_assert(</div>
<div class="line">    <a class="code" href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f">hana::find_if</a>(hana::make_tuple(1.0, 2, <span class="charliteral">&#39;3&#39;</span>), is_integral)</div>
<div class="line">        ==</div>
<div class="line">    hana::just(2)</div>
<div class="line">, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(</div>
<div class="line">    <a class="code" href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f">hana::find_if</a>(hana::make_tuple(1.0, 2, <span class="charliteral">&#39;3&#39;</span>), is_class)</div>
<div class="line">        ==</div>
<div class="line">    hana::nothing</div>
<div class="line">);</div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(</div>
<div class="line">    <a class="code" href="group__group-_searchable.html#ga6b6cdd69942b0fe3bf5254247f9c861e">hana::find</a>(hana::make_tuple(hana::int_c&lt;1&gt;, hana::char_c&lt;&#39;c&#39;&gt;, hana::type_c&lt;void&gt;), hana::type_c&lt;void&gt;)</div>
<div class="line">        ==</div>
<div class="line">    hana::just(hana::type_c&lt;void&gt;)</div>
<div class="line">);</div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(</div>
<div class="line">    <a class="code" href="group__group-_searchable.html#ga6b6cdd69942b0fe3bf5254247f9c861e">hana::find</a>(hana::make_tuple(hana::int_c&lt;1&gt;, hana::char_c&lt;&#39;c&#39;&gt;, hana::type_c&lt;void&gt;), hana::type_c&lt;int&gt;)</div>
<div class="line">        ==</div>
<div class="line">    hana::nothing</div>
<div class="line">);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="aassert_8hpp_html"><div class="ttname"><a href="assert_8hpp.html">assert.hpp</a></div><div class="ttdoc">Defines macros to perform different kinds of assertions.</div></div>
<div class="ttc" id="acompose_8hpp_html"><div class="ttname"><a href="compose_8hpp.html">compose.hpp</a></div><div class="ttdoc">Defines boost::hana::compose.</div></div>
<div class="ttc" id="aext_2std_2integral__constant_8hpp_html"><div class="ttname"><a href="ext_2std_2integral__constant_8hpp.html">integral_constant.hpp</a></div><div class="ttdoc">Adapts std::integral_constant for use with Hana.</div></div>
<div class="ttc" id="afind_8hpp_html"><div class="ttname"><a href="find_8hpp.html">find.hpp</a></div><div class="ttdoc">Defines boost::hana::find.</div></div>
<div class="ttc" id="afind__if_8hpp_html"><div class="ttname"><a href="find__if_8hpp.html">find_if.hpp</a></div><div class="ttdoc">Defines boost::hana::find_if.</div></div>
<div class="ttc" id="agroup__group-_searchable_html_ga6b6cdd69942b0fe3bf5254247f9c861e"><div class="ttname"><a href="group__group-_searchable.html#ga6b6cdd69942b0fe3bf5254247f9c861e">boost::hana::find</a></div><div class="ttdeci">constexpr auto find</div><div class="ttdoc">Finds the value associated to the given key in a structure.</div><div class="ttdef"><b>Definition:</b> find.hpp:44</div></div>
<div class="ttc" id="agroup__group-assertions_html_ga2c5006540936d9f8880e3a39f4fcc035"><div class="ttname"><a href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a></div><div class="ttdeci">#define BOOST_HANA_CONSTANT_CHECK(...)</div><div class="ttdoc">Equivalent to BOOST_HANA_CONSTANT_ASSERT, but not influenced by the BOOST_HANA_CONFIG_DISABLE_ASSERTI...</div><div class="ttdef"><b>Definition:</b> assert.hpp:239</div></div>
<div class="ttc" id="agroup__group-functional_html_ga3b16146e53efcdf9ecbb9a7b21f8cd0b"><div class="ttname"><a href="group__group-functional.html#ga3b16146e53efcdf9ecbb9a7b21f8cd0b">boost::hana::compose</a></div><div class="ttdeci">constexpr auto compose</div><div class="ttdoc">Return the composition of two functions or more.</div><div class="ttdef"><b>Definition:</b> compose.hpp:52</div></div>
<div class="ttc" id="aintegral__constant_8hpp_html"><div class="ttname"><a href="integral__constant_8hpp.html">integral_constant.hpp</a></div><div class="ttdoc">Defines boost::hana::integral_constant.</div></div>
<div class="ttc" id="anamespaceboost_1_1hana_html"><div class="ttname"><a href="namespaceboost_1_1hana.html">boost::hana</a></div><div class="ttdoc">Namespace containing everything in the library.</div><div class="ttdef"><b>Definition:</b> accessors.hpp:20</div></div>
<div class="ttc" id="aoptional_8hpp_html"><div class="ttname"><a href="optional_8hpp.html">optional.hpp</a></div><div class="ttdoc">Defines boost::hana::optional.</div></div>
<div class="ttc" id="atuple_8hpp_html"><div class="ttname"><a href="tuple_8hpp.html">tuple.hpp</a></div><div class="ttdoc">Defines boost::hana::tuple.</div></div>
<div class="ttc" id="atype_8hpp_html"><div class="ttname"><a href="type_8hpp.html">type.hpp</a></div><div class="ttdoc">Defines boost::hana::type and related utilities.</div></div>
</div><!-- fragment --></li>
<li><code>Foldable</code> for finite <code>Iterable</code>s<br  />
Every finite <code>Iterable</code> gives rise to a model of <code>Foldable</code>. For these models to be consistent, we require the models of both <code>Foldable</code> and <code>Iterable</code> to have the same linearization.</li>
</ol>
<dl class="section note"><dt>Note</dt><dd>As explained above, <code>Iterable</code>s are also <code>Searchable</code>s and their models have to be consistent. By the laws presented here, it also means that the <code>Foldable</code> model for finite <code>Iterable</code>s has to be consistent with the <code>Searchable</code> model.</dd></dl>
<p>For convenience, finite <code>Iterable</code>s must only provide a definition of <code>length</code> to model the <code>Foldable</code> concept; defining the more powerful <code>unpack</code> or <code>fold_left</code> is not necessary (but still possible). The default implementation of <code>unpack</code> derived from <code>Iterable</code> + <code>length</code> uses the fact that <code>at(xs, i)</code> denotes the <code>i</code>th element of <code>xs</code>'s linearization, and that the linearization of a finite <code>Iterable</code> must be the same as its linearization as a <code>Foldable</code>.</p>
<h2><a class="anchor" id="autotoc_md128"></a>
Concrete models</h2>
<p><code><a class="el" href="structboost_1_1hana_1_1tuple.html" title="General purpose index-based heterogeneous sequence with a fixed length.">hana::tuple</a></code>, <code><a class="el" href="structboost_1_1hana_1_1string.html" title="Compile-time string.">hana::string</a></code>, <code><a class="el" href="structboost_1_1hana_1_1range.html" title="Compile-time half-open interval of hana::integral_constants.">hana::range</a></code> </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ga8a484304380eae38f3d9663d98860129"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">boost::hana::at</a></td></tr>
<tr class="memdesc:ga8a484304380eae38f3d9663d98860129"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>n</code>th element of an iterable.  <a href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">More...</a><br /></td></tr>
<tr class="separator:ga8a484304380eae38f3d9663d98860129"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4cb99cfbef936cb267e76f66f40f529c"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:ga4cb99cfbef936cb267e76f66f40f529c"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga4cb99cfbef936cb267e76f66f40f529c">boost::hana::at_c</a></td></tr>
<tr class="memdesc:ga4cb99cfbef936cb267e76f66f40f529c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>at</code>; provided for convenience.  <a href="group__group-_iterable.html#ga4cb99cfbef936cb267e76f66f40f529c">More...</a><br /></td></tr>
<tr class="separator:ga4cb99cfbef936cb267e76f66f40f529c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3f4d0035345a453284e46303862d463"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#gab3f4d0035345a453284e46303862d463">boost::hana::back</a></td></tr>
<tr class="memdesc:gab3f4d0035345a453284e46303862d463"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the last element of a non-empty and finite iterable.  <a href="group__group-_iterable.html#gab3f4d0035345a453284e46303862d463">More...</a><br /></td></tr>
<tr class="separator:gab3f4d0035345a453284e46303862d463"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad23ce0a4906e2bb0a52f38837b134757"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">boost::hana::drop_front</a></td></tr>
<tr class="memdesc:gad23ce0a4906e2bb0a52f38837b134757"><td class="mdescLeft">&#160;</td><td class="mdescRight">Drop the first <code>n</code> elements of an iterable, and return the rest.  <a href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">More...</a><br /></td></tr>
<tr class="separator:gad23ce0a4906e2bb0a52f38837b134757"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4dbc6a82f03ca35b7ac418ca30889cc4"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4">boost::hana::drop_front_exactly</a></td></tr>
<tr class="memdesc:ga4dbc6a82f03ca35b7ac418ca30889cc4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Drop the first <code>n</code> elements of an iterable, and return the rest.  <a href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4">More...</a><br /></td></tr>
<tr class="separator:ga4dbc6a82f03ca35b7ac418ca30889cc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f1d02c74a6bdc1db260e0d6a8f1ee56"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga9f1d02c74a6bdc1db260e0d6a8f1ee56">boost::hana::drop_while</a></td></tr>
<tr class="memdesc:ga9f1d02c74a6bdc1db260e0d6a8f1ee56"><td class="mdescLeft">&#160;</td><td class="mdescRight">Drop elements from an iterable up to, but excluding, the first element for which the <code>predicate</code> is not satisfied.  <a href="group__group-_iterable.html#ga9f1d02c74a6bdc1db260e0d6a8f1ee56">More...</a><br /></td></tr>
<tr class="separator:ga9f1d02c74a6bdc1db260e0d6a8f1ee56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a67ea10e8082dbe6705e573fa978444"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444">boost::hana::front</a></td></tr>
<tr class="memdesc:ga8a67ea10e8082dbe6705e573fa978444"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the first element of a non-empty iterable.  <a href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444">More...</a><br /></td></tr>
<tr class="separator:ga8a67ea10e8082dbe6705e573fa978444"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5332fd1dd82edf08379958ba21d57a87"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87">boost::hana::index_if</a></td></tr>
<tr class="memdesc:ga5332fd1dd82edf08379958ba21d57a87"><td class="mdescLeft">&#160;</td><td class="mdescRight">Finds the value associated to the first key satisfying a predicate.  <a href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87">More...</a><br /></td></tr>
<tr class="separator:ga5332fd1dd82edf08379958ba21d57a87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a05f564f8a7e4afa04fcbc07ad8f394"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">boost::hana::is_empty</a></td></tr>
<tr class="memdesc:ga2a05f564f8a7e4afa04fcbc07ad8f394"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the iterable is empty.  <a href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">More...</a><br /></td></tr>
<tr class="separator:ga2a05f564f8a7e4afa04fcbc07ad8f394"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="ga8a484304380eae38f3d9663d98860129"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8a484304380eae38f3d9663d98860129">&#9670;&nbsp;</a></span>at</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::at</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2at_8hpp.html">boost/hana/fwd/at.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span> <span class="keyword">const</span>&amp; n) -&gt; decltype(<span class="keyword">auto</span>) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Returns the <code>n</code>th element of an iterable. </p>
<p>Given an <code>Iterable</code> and an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> index, <code>at</code> returns the element located at the index in the linearization of the iterable. Specifically, given an iterable <code>xs</code> with a linearization of <code>[x1, ..., xN]</code>, <code>at(xs, k)</code> is equivalent to <code>xk</code>.</p>
<p>If the <code>Iterable</code> actually stores the elements it contains, <code>at</code> is required to return a lvalue reference, a lvalue reference to const or a rvalue reference to the matching element, where the type of reference must match that of the iterable passed to <code>at</code>. If the <code>Iterable</code> does not store the elements it contains (i.e. it generates them on demand), this requirement is dropped.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The iterable in which an element is retrieved. The iterable must contain at least <code>n + 1</code> elements.</td></tr>
    <tr><td class="paramname">n</td><td>A non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> representing the 0-based index of the element to return. It is an error to call <code>at</code> with an index that out of bounds of the iterable.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md59"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="at_8hpp.html">boost/hana/at.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.0);</div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">hana::at</a>(xs, hana::size_t&lt;0&gt;{}) == 0, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">hana::at</a>(xs, hana::size_t&lt;1&gt;{}) == <span class="charliteral">&#39;1&#39;</span>, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">hana::at</a>(xs, hana::size_t&lt;2&gt;{}) == 2.0, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="aat_8hpp_html"><div class="ttname"><a href="at_8hpp.html">at.hpp</a></div><div class="ttdoc">Defines boost::hana::at and boost::hana::at_c.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga4cb99cfbef936cb267e76f66f40f529c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4cb99cfbef936cb267e76f66f40f529c">&#9670;&nbsp;</a></span>at_c</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;std::size_t n&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::at_c</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2at_8hpp.html">boost/hana/fwd/at.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) {</div>
<div class="line">        <span class="keywordflow">return</span> <a class="code" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">hana::at</a>(forwarded(xs), hana::size_c&lt;n&gt;);</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Equivalent to <code>at</code>; provided for convenience. </p>
<dl class="section note"><dt>Note</dt><dd><code><a class="el" href="group__group-_iterable.html#ga4cb99cfbef936cb267e76f66f40f529c" title="Equivalent to at; provided for convenience.">hana::at_c</a>&lt;n&gt;</code> is an overloaded function, not a function object. Hence, it can't be passed to higher-order algorithms. This is done for compile-time performance reasons.</dd></dl>
<h2><a class="anchor" id="autotoc_md60"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="at_8hpp.html">boost/hana/at.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.0);</div>
<div class="line"> </div>
<div class="line">static_assert(hana::at_c&lt;0&gt;(xs) == 0, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(hana::at_c&lt;1&gt;(xs) == <span class="charliteral">&#39;1&#39;</span>, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(hana::at_c&lt;2&gt;(xs) == 2.0, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
</div><!-- fragment --> 
</div>
</div>
<a id="gab3f4d0035345a453284e46303862d463"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab3f4d0035345a453284e46303862d463">&#9670;&nbsp;</a></span>back</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::back</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2back_8hpp.html">boost/hana/fwd/back.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) -&gt; decltype(<span class="keyword">auto</span>) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Returns the last element of a non-empty and finite iterable. </p>
<p>Given a non-empty and finite iterable <code>xs</code> with a linearization of <code>[x1, ..., xN]</code>, <code>back(xs)</code> is equal to <code>xN</code>. Equivalently, <code>back(xs)</code> must be equivalent to <code>at_c&lt;N-1&gt;(xs)</code>, and that regardless of the value category of <code>xs</code> (<code>back</code> must respect the reference semantics of <code>at</code>).</p>
<h2><a class="anchor" id="autotoc_md62"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="back_8hpp.html">boost/hana/back.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#gab3f4d0035345a453284e46303862d463">hana::back</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3)) == 3.3, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#gab3f4d0035345a453284e46303862d463">hana::back</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3, <span class="keyword">nullptr</span>)) == <span class="keyword">nullptr</span>, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="aback_8hpp_html"><div class="ttname"><a href="back_8hpp.html">back.hpp</a></div><div class="ttdoc">Defines boost::hana::back.</div></div>
<div class="ttc" id="agroup__group-_iterable_html_gab3f4d0035345a453284e46303862d463"><div class="ttname"><a href="group__group-_iterable.html#gab3f4d0035345a453284e46303862d463">boost::hana::back</a></div><div class="ttdeci">constexpr auto back</div><div class="ttdoc">Returns the last element of a non-empty and finite iterable.</div><div class="ttdef"><b>Definition:</b> back.hpp:32</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gad23ce0a4906e2bb0a52f38837b134757"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad23ce0a4906e2bb0a52f38837b134757">&#9670;&nbsp;</a></span>drop_front</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::drop_front</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2drop__front_8hpp.html">boost/hana/fwd/drop_front.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs[, <span class="keyword">auto</span> <span class="keyword">const</span>&amp; n]) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Drop the first <code>n</code> elements of an iterable, and return the rest. </p>
<p>Given an <code>Iterable</code> <code>xs</code> with a linearization of <code>[x1, x2, ...]</code> and a non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> <code>n</code>, <code>drop_front(xs, n)</code> is an iterable with the same tag as <code>xs</code> whose linearization is <code>[xn+1, xn+2, ...]</code>. In particular, note that this function does not mutate the original iterable in any way. If <code>n</code> is not given, it defaults to an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> with a value equal to <code>1</code>.</p>
<p>In case <code>length(xs) &lt;= n</code>, <code>drop_front</code> will simply drop the whole iterable without failing, thus returning an empty iterable. This is different from <code>drop_front_exactly</code>, which expects <code>n &lt;= length(xs)</code> but can be better optimized because of this additional guarantee.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The iterable from which elements are dropped.</td></tr>
    <tr><td class="paramname">n</td><td>A non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> representing the number of elements to be dropped from the iterable. If <code>n</code> is not given, it defaults to an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> with a value equal to <code>1</code>.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md206"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="drop__front_8hpp.html">boost/hana/drop_front.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.0);</div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">hana::drop_front</a>(xs, hana::size_c&lt;0&gt;) == xs, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">hana::drop_front</a>(xs, hana::size_c&lt;1&gt;) == hana::make_tuple(<span class="charliteral">&#39;1&#39;</span>, 2.0), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">hana::drop_front</a>(xs, hana::size_c&lt;2&gt;) == hana::make_tuple(2.0), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(<a class="code" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">hana::drop_front</a>(xs, hana::size_c&lt;3&gt;) == hana::make_tuple());</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(<a class="code" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">hana::drop_front</a>(xs, hana::size_c&lt;4&gt;) == hana::make_tuple());</div>
<div class="line"> </div>
<div class="line"><span class="comment">// drop_front(xs) is equivalent to drop_front(xs, size_t&lt;1&gt;)</span></div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">hana::drop_front</a>(xs) == hana::make_tuple(<span class="charliteral">&#39;1&#39;</span>, 2.0), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="adrop__front_8hpp_html"><div class="ttname"><a href="drop__front_8hpp.html">drop_front.hpp</a></div><div class="ttdoc">Defines boost::hana::drop_front.</div></div>
<div class="ttc" id="aequal_8hpp_html"><div class="ttname"><a href="equal_8hpp.html">equal.hpp</a></div><div class="ttdoc">Defines boost::hana::equal.</div></div>
<div class="ttc" id="agroup__group-_iterable_html_gad23ce0a4906e2bb0a52f38837b134757"><div class="ttname"><a href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">boost::hana::drop_front</a></div><div class="ttdeci">constexpr auto drop_front</div><div class="ttdoc">Drop the first n elements of an iterable, and return the rest.</div><div class="ttdef"><b>Definition:</b> drop_front.hpp:47</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga4dbc6a82f03ca35b7ac418ca30889cc4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4dbc6a82f03ca35b7ac418ca30889cc4">&#9670;&nbsp;</a></span>drop_front_exactly</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::drop_front_exactly</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2drop__front__exactly_8hpp.html">boost/hana/fwd/drop_front_exactly.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs[, <span class="keyword">auto</span> <span class="keyword">const</span>&amp; n]) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Drop the first <code>n</code> elements of an iterable, and return the rest. </p>
<p>Given an <code>Iterable</code> <code>xs</code> with a linearization of <code>[x1, x2, ...]</code> and a non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> <code>n</code>, <code>drop_front_exactly(xs, n)</code> is an iterable with the same tag as <code>xs</code> whose linearization is <code>[xn+1, xn+2, ...]</code>. In particular, note that this function does not mutate the original iterable in any way. If <code>n</code> is not given, it defaults to an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> with a value equal to <code>1</code>.</p>
<p>It is an error to use <code>drop_front_exactly</code> with <code>n &gt; length(xs)</code>. This additional guarantee allows <code>drop_front_exactly</code> to be better optimized than the <code>drop_front</code> function, which allows <code>n &gt; length(xs)</code>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The iterable from which elements are dropped.</td></tr>
    <tr><td class="paramname">n</td><td>A non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> representing the number of elements to be dropped from the iterable. In addition to being non-negative, <code>n</code> must be less than or equal to the number of elements in <code>xs</code>. If <code>n</code> is not given, it defaults to an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> with a value equal to <code>1</code>.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md207"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="drop__front__exactly_8hpp.html">boost/hana/drop_front_exactly.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.0);</div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4">hana::drop_front_exactly</a>(xs, hana::size_c&lt;1&gt;) == hana::make_tuple(<span class="charliteral">&#39;1&#39;</span>, 2.0), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4">hana::drop_front_exactly</a>(xs, hana::size_c&lt;2&gt;) == hana::make_tuple(2.0), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(<a class="code" href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4">hana::drop_front_exactly</a>(xs, hana::size_c&lt;3&gt;) == hana::make_tuple());</div>
<div class="line"> </div>
<div class="line"><span class="comment">// drop_front_exactly(xs) is equivalent to drop_front_exactly(xs, size_t&lt;1&gt;)</span></div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4">hana::drop_front_exactly</a>(xs) == hana::make_tuple(<span class="charliteral">&#39;1&#39;</span>, 2.0), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="adrop__front__exactly_8hpp_html"><div class="ttname"><a href="drop__front__exactly_8hpp.html">drop_front_exactly.hpp</a></div><div class="ttdoc">Defines boost::hana::drop_front_exactly.</div></div>
<div class="ttc" id="agroup__group-_iterable_html_ga4dbc6a82f03ca35b7ac418ca30889cc4"><div class="ttname"><a href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4">boost::hana::drop_front_exactly</a></div><div class="ttdeci">constexpr auto drop_front_exactly</div><div class="ttdoc">Drop the first n elements of an iterable, and return the rest.</div><div class="ttdef"><b>Definition:</b> drop_front_exactly.hpp:48</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga9f1d02c74a6bdc1db260e0d6a8f1ee56"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9f1d02c74a6bdc1db260e0d6a8f1ee56">&#9670;&nbsp;</a></span>drop_while</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::drop_while</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2drop__while_8hpp.html">boost/hana/fwd/drop_while.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; iterable, <span class="keyword">auto</span>&amp;&amp; predicate) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Drop elements from an iterable up to, but excluding, the first element for which the <code>predicate</code> is not satisfied. </p>
<p>Specifically, <code>drop_while</code> returns an iterable containing all the elements of the original iterable except for those in the range delimited by [<code>head</code>, <code>e</code>), where <code>head</code> is the first element and <code>e</code> is the first element for which the <code>predicate</code> is not satisfied. If the iterable is not finite, the <code>predicate</code> has to return a false- valued <code>Logical</code> at a finite index for this method to return.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">iterable</td><td>The iterable from which elements are dropped.</td></tr>
    <tr><td class="paramname">predicate</td><td>A function called as <code>predicate(x)</code>, where <code>x</code> is an element of the structure, and returning a <code>Logical</code> representing whether <code>x</code> should be dropped from the structure. In the current version of the library, <code>predicate</code> should return a compile-time <code>Logical</code>.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md208"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="drop__while_8hpp.html">boost/hana/drop_while.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="less_8hpp.html">boost/hana/less.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="negate_8hpp.html">boost/hana/negate.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="range_8hpp.html">boost/hana/range.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"><span class="keyword">using namespace </span>hana::literals;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> negative = [](<span class="keyword">auto</span> x) {</div>
<div class="line">    <span class="keywordflow">return</span> x &lt; hana::int_c&lt;0&gt;;</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(</div>
<div class="line">    <a class="code" href="group__group-_iterable.html#ga9f1d02c74a6bdc1db260e0d6a8f1ee56">hana::drop_while</a>(hana::make_range(hana::int_c&lt;-3&gt;, hana::int_c&lt;6&gt;), negative)</div>
<div class="line">        ==</div>
<div class="line">    hana::make_range(hana::int_c&lt;0&gt;, hana::int_c&lt;6&gt;)</div>
<div class="line">);</div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(</div>
<div class="line">    <a class="code" href="group__group-_iterable.html#ga9f1d02c74a6bdc1db260e0d6a8f1ee56">hana::drop_while</a>(hana::make_tuple(1_c, -2_c, 4_c, 5_c), negative)</div>
<div class="line">        ==</div>
<div class="line">    hana::make_tuple(1_c, -2_c, 4_c, 5_c)</div>
<div class="line">);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="adrop__while_8hpp_html"><div class="ttname"><a href="drop__while_8hpp.html">drop_while.hpp</a></div><div class="ttdoc">Defines boost::hana::drop_while.</div></div>
<div class="ttc" id="agroup__group-_iterable_html_ga9f1d02c74a6bdc1db260e0d6a8f1ee56"><div class="ttname"><a href="group__group-_iterable.html#ga9f1d02c74a6bdc1db260e0d6a8f1ee56">boost::hana::drop_while</a></div><div class="ttdeci">constexpr auto drop_while</div><div class="ttdoc">Drop elements from an iterable up to, but excluding, the first element for which the predicate is not...</div><div class="ttdef"><b>Definition:</b> drop_while.hpp:44</div></div>
<div class="ttc" id="aless_8hpp_html"><div class="ttname"><a href="less_8hpp.html">less.hpp</a></div><div class="ttdoc">Defines boost::hana::less.</div></div>
<div class="ttc" id="anegate_8hpp_html"><div class="ttname"><a href="negate_8hpp.html">negate.hpp</a></div><div class="ttdoc">Defines boost::hana::negate.</div></div>
<div class="ttc" id="arange_8hpp_html"><div class="ttname"><a href="range_8hpp.html">range.hpp</a></div><div class="ttdoc">Defines boost::hana::range.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga8a67ea10e8082dbe6705e573fa978444"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8a67ea10e8082dbe6705e573fa978444">&#9670;&nbsp;</a></span>front</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::front</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2front_8hpp.html">boost/hana/fwd/front.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) -&gt; decltype(<span class="keyword">auto</span>) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Returns the first element of a non-empty iterable. </p>
<p>Given a non-empty Iterable <code>xs</code> with a linearization of <code>[x1, ..., xN]</code>, <code>front(xs)</code> is equal to <code>x1</code>. If <code>xs</code> is empty, it is an error to use this function. Equivalently, <code>front(xs)</code> must be equivalent to <code>at_c&lt;0&gt;(xs)</code>, and that regardless of the value category of <code>xs</code> (<code>front</code> must respect the reference semantics of <code>at</code>).</p>
<h2><a class="anchor" id="autotoc_md230"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="front_8hpp.html">boost/hana/front.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444">hana::front</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3, <span class="keyword">nullptr</span>)) == 1, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="afront_8hpp_html"><div class="ttname"><a href="front_8hpp.html">front.hpp</a></div><div class="ttdoc">Defines boost::hana::front.</div></div>
<div class="ttc" id="agroup__group-_iterable_html_ga8a67ea10e8082dbe6705e573fa978444"><div class="ttname"><a href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444">boost::hana::front</a></div><div class="ttdeci">constexpr auto front</div><div class="ttdoc">Returns the first element of a non-empty iterable.</div><div class="ttdef"><b>Definition:</b> front.hpp:32</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga5332fd1dd82edf08379958ba21d57a87"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5332fd1dd82edf08379958ba21d57a87">&#9670;&nbsp;</a></span>index_if</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::index_if</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2index__if_8hpp.html">boost/hana/fwd/index_if.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span>&amp;&amp; predicate) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Finds the value associated to the first key satisfying a predicate. </p>
<p>Given an <code>Iterable</code> structure <code>xs</code> and a predicate <code>pred</code>, <code>index_if(xs, pred)</code> returns a <code><a class="el" href="structboost_1_1hana_1_1optional.html" title="Optional value whose optional-ness is known at compile-time.">hana::optional</a></code> containing an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> of the index of the first element that satisfies the predicate or nothing if no element satisfies the predicate.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The structure to be searched.</td></tr>
    <tr><td class="paramname">predicate</td><td>A function called as <code>predicate(x)</code>, where <code>x</code> is an element of the <code>Iterable</code> structure and returning whether <code>x</code> is the element being searched for. In the current version of the library, the predicate has to return an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> holding a value that can be converted to <code>bool</code>.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md239"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Copyright Jason Rice 2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="core_2is__a_8hpp.html">boost/hana/core/is_a.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="index__if_8hpp.html">boost/hana/index_if.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="optional_8hpp.html">boost/hana/optional.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.0);</div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87">hana::index_if</a>(xs, hana::is_an&lt;int&gt;)   == hana::just(hana::size_c&lt;0&gt;), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87">hana::index_if</a>(xs, hana::is_a&lt;char&gt;)   == hana::just(hana::size_c&lt;1&gt;), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87">hana::index_if</a>(xs, hana::is_a&lt;double&gt;) == hana::just(hana::size_c&lt;2&gt;), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87">hana::index_if</a>(xs, hana::is_a&lt;hana::tuple_tag&gt;) == hana::nothing, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="acore_2is__a_8hpp_html"><div class="ttname"><a href="core_2is__a_8hpp.html">is_a.hpp</a></div><div class="ttdoc">Defines boost::hana::is_a and boost::hana::is_an.</div></div>
<div class="ttc" id="agroup__group-_iterable_html_ga5332fd1dd82edf08379958ba21d57a87"><div class="ttname"><a href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87">boost::hana::index_if</a></div><div class="ttdeci">constexpr auto index_if</div><div class="ttdoc">Finds the value associated to the first key satisfying a predicate.</div><div class="ttdef"><b>Definition:</b> index_if.hpp:43</div></div>
<div class="ttc" id="aindex__if_8hpp_html"><div class="ttname"><a href="index__if_8hpp.html">index_if.hpp</a></div><div class="ttdoc">Defines boost::hana::index_if.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga2a05f564f8a7e4afa04fcbc07ad8f394"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2a05f564f8a7e4afa04fcbc07ad8f394">&#9670;&nbsp;</a></span>is_empty</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::is_empty</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2is__empty_8hpp.html">boost/hana/fwd/is_empty.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span> <span class="keyword">const</span>&amp; xs) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Returns whether the iterable is empty. </p>
<p>Given an <code>Iterable</code> <code>xs</code>, <code>is_empty</code> returns whether <code>xs</code> contains no more elements. In other words, it returns whether trying to extract the tail of <code>xs</code> would be an error. In the current version of the library, <code>is_empty</code> must return an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> holding a value convertible to <code>bool</code>. This is because only compile-time <code>Iterable</code>s are supported right now.</p>
<h2><a class="anchor" id="autotoc_md246"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="is__empty_8hpp.html">boost/hana/is_empty.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="not_8hpp.html">boost/hana/not.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(!<a class="code" href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">hana::is_empty</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>)));</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>( <a class="code" href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">hana::is_empty</a>(hana::make_tuple()));</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_iterable_html_ga2a05f564f8a7e4afa04fcbc07ad8f394"><div class="ttname"><a href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">boost::hana::is_empty</a></div><div class="ttdeci">constexpr auto is_empty</div><div class="ttdoc">Returns whether the iterable is empty.</div><div class="ttdef"><b>Definition:</b> is_empty.hpp:33</div></div>
<div class="ttc" id="ais__empty_8hpp_html"><div class="ttname"><a href="is__empty_8hpp.html">is_empty.hpp</a></div><div class="ttdoc">Defines boost::hana::is_empty.</div></div>
<div class="ttc" id="anot_8hpp_html"><div class="ttname"><a href="not_8hpp.html">not.hpp</a></div><div class="ttdoc">Defines boost::hana::not_.</div></div>
</div><!-- fragment --> 
</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!--
Copyright Louis Dionne 2013-2017
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
-->
<!-- boost-no-inspect -->
<!-- HTML footer for doxygen *******-->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
  </ul>
</div>
</body>
</html>
