<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>stream_parser::write (1 of 6 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Boost.JSON">
<link rel="up" href="../write.html" title="stream_parser::write">
<link rel="prev" href="../write.html" title="stream_parser::write">
<link rel="next" href="overload2.html" title="stream_parser::write (2 of 6 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../write.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../write.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="json.ref.boost__json__stream_parser.write.overload1"></a><a class="link" href="overload1.html" title="stream_parser::write (1 of 6 overloads)">stream_parser::write
          (1 of 6 overloads)</a>
</h6></div></div></div>
<p>
            Parse a buffer containing all or part of a complete JSON text.
          </p>
<h6>
<a name="json.ref.boost__json__stream_parser.write.overload1.h0"></a>
            <span class="phrase"><a name="json.ref.boost__json__stream_parser.write.overload1.synopsis"></a></span><a class="link" href="overload1.html#json.ref.boost__json__stream_parser.write.overload1.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span>
<span class="identifier">write</span><span class="special">(</span>
    <span class="keyword">char</span> <span class="keyword">const</span><span class="special">*</span> <span class="identifier">data</span><span class="special">,</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">size</span><span class="special">,</span>
    <span class="identifier">error_code</span><span class="special">&amp;</span> <span class="identifier">ec</span><span class="special">);</span>
</pre>
<h6>
<a name="json.ref.boost__json__stream_parser.write.overload1.h1"></a>
            <span class="phrase"><a name="json.ref.boost__json__stream_parser.write.overload1.description"></a></span><a class="link" href="overload1.html#json.ref.boost__json__stream_parser.write.overload1.description">Description</a>
          </h6>
<p>
            This function parses a all or part of a JSON text contained in the specified
            character buffer. The entire buffer must be consumed; if there are additional
            characters past the end of the complete JSON text, the parse fails and
            an error is returned.
          </p>
<h6>
<a name="json.ref.boost__json__stream_parser.write.overload1.h2"></a>
            <span class="phrase"><a name="json.ref.boost__json__stream_parser.write.overload1.example"></a></span><a class="link" href="overload1.html#json.ref.boost__json__stream_parser.write.overload1.example">Example</a>
          </h6>
<pre class="programlisting"><span class="identifier">stream_parser</span> <span class="identifier">p</span><span class="special">;</span>                                <span class="comment">// construct a parser</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">n</span><span class="special">;</span>                                  <span class="comment">// number of characters used</span>
<span class="identifier">n</span> <span class="special">=</span> <span class="identifier">p</span><span class="special">.</span><span class="identifier">write</span><span class="special">(</span> <span class="string">"[1,2"</span> <span class="special">);</span>                          <span class="comment">// parse some of the JSON text</span>
<span class="identifier">assert</span><span class="special">(</span> <span class="identifier">n</span> <span class="special">==</span> <span class="number">4</span> <span class="special">);</span>                               <span class="comment">// all characters consumed</span>
<span class="identifier">n</span> <span class="special">=</span> <span class="identifier">p</span><span class="special">.</span><span class="identifier">write</span><span class="special">(</span> <span class="string">"3,4]"</span> <span class="special">);</span>                          <span class="comment">// parse the rest of the JSON text</span>
<span class="identifier">assert</span><span class="special">(</span> <span class="identifier">n</span> <span class="special">==</span> <span class="number">4</span> <span class="special">);</span>                               <span class="comment">// all characters consumed</span>
<span class="identifier">value</span> <span class="identifier">jv</span> <span class="special">=</span> <span class="identifier">p</span><span class="special">.</span><span class="identifier">release</span><span class="special">();</span>                         <span class="comment">// take ownership of the value</span>
</pre>
<h6>
<a name="json.ref.boost__json__stream_parser.write.overload1.h3"></a>
            <span class="phrase"><a name="json.ref.boost__json__stream_parser.write.overload1.remarks"></a></span><a class="link" href="overload1.html#json.ref.boost__json__stream_parser.write.overload1.remarks">Remarks</a>
          </h6>
<p>
            To indicate there are no more character buffers, such as when <a class="link" href="../done.html" title="stream_parser::done"><code class="computeroutput"><span class="identifier">done</span></code></a> returns <code class="computeroutput"><span class="keyword">false</span></code>
            after writing, call <a class="link" href="../finish.html" title="stream_parser::finish"><code class="computeroutput"><span class="identifier">finish</span></code></a>.
          </p>
<h6>
<a name="json.ref.boost__json__stream_parser.write.overload1.h4"></a>
            <span class="phrase"><a name="json.ref.boost__json__stream_parser.write.overload1.complexity"></a></span><a class="link" href="overload1.html#json.ref.boost__json__stream_parser.write.overload1.complexity">Complexity</a>
          </h6>
<p>
            Linear in <code class="computeroutput"><span class="identifier">size</span></code>.
          </p>
<h6>
<a name="json.ref.boost__json__stream_parser.write.overload1.h5"></a>
            <span class="phrase"><a name="json.ref.boost__json__stream_parser.write.overload1.exception_safety"></a></span><a class="link" href="overload1.html#json.ref.boost__json__stream_parser.write.overload1.exception_safety">Exception
            Safety</a>
          </h6>
<p>
            Basic guarantee. Calls to <code class="computeroutput"><span class="identifier">memory_resource</span><span class="special">::</span><span class="identifier">allocate</span></code>
            may throw. Upon error or exception, subsequent calls will fail until
            <a class="link" href="../reset.html" title="stream_parser::reset"><code class="computeroutput"><span class="identifier">reset</span></code></a> is called to parse a new
            JSON text.
          </p>
<h6>
<a name="json.ref.boost__json__stream_parser.write.overload1.h6"></a>
            <span class="phrase"><a name="json.ref.boost__json__stream_parser.write.overload1.return_value"></a></span><a class="link" href="overload1.html#json.ref.boost__json__stream_parser.write.overload1.return_value">Return
            Value</a>
          </h6>
<p>
            The number of characters consumed from the buffer.
          </p>
<h6>
<a name="json.ref.boost__json__stream_parser.write.overload1.h7"></a>
            <span class="phrase"><a name="json.ref.boost__json__stream_parser.write.overload1.parameters"></a></span><a class="link" href="overload1.html#json.ref.boost__json__stream_parser.write.overload1.parameters">Parameters</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Name
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
</tr></thead>
<tbody>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">data</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      A pointer to a buffer of <code class="computeroutput"><span class="identifier">size</span></code>
                      characters to parse.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">size</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      The number of characters pointed to by <code class="computeroutput"><span class="identifier">data</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">ec</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Set to the error, if any occurred.
                    </p>
                  </td>
</tr>
</tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2019, 2020 Vinnie Falco<br>Copyright © 2020 Krystian Stasiowski<br>Copyright © 2022 Dmitry Arkhipov<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../write.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../write.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
