<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>object::operator= (1 of 3 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Boost.JSON">
<link rel="up" href="../operator_eq_.html" title="object::operator=">
<link rel="prev" href="../operator_eq_.html" title="object::operator=">
<link rel="next" href="overload2.html" title="object::operator= (2 of 3 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../operator_eq_.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../operator_eq_.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="json.ref.boost__json__object.operator_eq_.overload1"></a><a class="link" href="overload1.html" title="object::operator= (1 of 3 overloads)">object::operator=
          (1 of 3 overloads)</a>
</h6></div></div></div>
<p>
            Copy assignment.
          </p>
<h6>
<a name="json.ref.boost__json__object.operator_eq_.overload1.h0"></a>
            <span class="phrase"><a name="json.ref.boost__json__object.operator_eq_.overload1.synopsis"></a></span><a class="link" href="overload1.html#json.ref.boost__json__object.operator_eq_.overload1.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="identifier">object</span><span class="special">&amp;</span>
<span class="keyword">operator</span><span class="special">=(</span>
    <span class="identifier">object</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">);</span>
</pre>
<h6>
<a name="json.ref.boost__json__object.operator_eq_.overload1.h1"></a>
            <span class="phrase"><a name="json.ref.boost__json__object.operator_eq_.overload1.description"></a></span><a class="link" href="overload1.html#json.ref.boost__json__object.operator_eq_.overload1.description">Description</a>
          </h6>
<p>
            The contents of the object are replaced with an element-wise copy of
            <code class="computeroutput"><span class="identifier">other</span></code>.
          </p>
<h6>
<a name="json.ref.boost__json__object.operator_eq_.overload1.h2"></a>
            <span class="phrase"><a name="json.ref.boost__json__object.operator_eq_.overload1.complexity"></a></span><a class="link" href="overload1.html#json.ref.boost__json__object.operator_eq_.overload1.complexity">Complexity</a>
          </h6>
<p>
            Linear in <a class="link" href="../size.html" title="object::size"><code class="computeroutput"><span class="identifier">size</span><span class="special">()</span></code></a>
            plus <code class="computeroutput"><span class="identifier">other</span><span class="special">.</span><span class="identifier">size</span><span class="special">()</span></code>.
          </p>
<h6>
<a name="json.ref.boost__json__object.operator_eq_.overload1.h3"></a>
            <span class="phrase"><a name="json.ref.boost__json__object.operator_eq_.overload1.exception_safety"></a></span><a class="link" href="overload1.html#json.ref.boost__json__object.operator_eq_.overload1.exception_safety">Exception
            Safety</a>
          </h6>
<p>
            Strong guarantee. Calls to <code class="computeroutput"><span class="identifier">memory_resource</span><span class="special">::</span><span class="identifier">allocate</span></code>
            may throw.
          </p>
<h6>
<a name="json.ref.boost__json__object.operator_eq_.overload1.h4"></a>
            <span class="phrase"><a name="json.ref.boost__json__object.operator_eq_.overload1.parameters"></a></span><a class="link" href="overload1.html#json.ref.boost__json__object.operator_eq_.overload1.parameters">Parameters</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Name
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
</tr></thead>
<tbody><tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">other</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      The object to copy.
                    </p>
                  </td>
</tr></tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2019, 2020 Vinnie Falco<br>Copyright © 2020 Krystian Stasiowski<br>Copyright © 2022 Dmitry Arkhipov<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../operator_eq_.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../operator_eq_.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
