<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>string::insert (4 of 4 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Boost.JSON">
<link rel="up" href="../insert.html" title="string::insert">
<link rel="prev" href="overload3.html" title="string::insert (3 of 4 overloads)">
<link rel="next" href="../erase.html" title="string::erase">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../insert.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../erase.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="json.ref.boost__json__string.insert.overload4"></a><a class="link" href="overload4.html" title="string::insert (4 of 4 overloads)">string::insert
          (4 of 4 overloads)</a>
</h6></div></div></div>
<p>
            Insert a range of characters.
          </p>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h0"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.synopsis"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">InputIt</span><span class="special">&gt;</span>
<span class="identifier">string</span><span class="special">&amp;</span>
<span class="identifier">insert</span><span class="special">(</span>
    <span class="identifier">size_type</span> <span class="identifier">pos</span><span class="special">,</span>
    <span class="identifier">InputIt</span> <span class="identifier">first</span><span class="special">,</span>
    <span class="identifier">InputIt</span> <span class="identifier">last</span><span class="special">);</span>
</pre>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h1"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.description"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.description">Description</a>
          </h6>
<p>
            Inserts characters from the range <code class="computeroutput"><span class="special">{</span><span class="identifier">first</span><span class="special">,</span> <span class="identifier">last</span><span class="special">)</span></code>
            before the character at index <code class="computeroutput"><span class="identifier">pos</span></code>.
          </p>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h2"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.precondition"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.precondition">Precondition</a>
          </h6>
<p>
            <code class="computeroutput"><span class="special">{</span><span class="identifier">first</span><span class="special">,</span> <span class="identifier">last</span><span class="special">)</span></code>is a valid range.
          </p>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h3"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.exception_safety"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.exception_safety">Exception
            Safety</a>
          </h6>
<p>
            Strong guarantee.
          </p>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h4"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.remarks"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.remarks">Remarks</a>
          </h6>
<p>
            All references, pointers, or iterators referring to contained elements
            are invalidated. Any past-the-end iterators are also invalidated.
          </p>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h5"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.template_parameters"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.template_parameters">Template
            Parameters</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Type
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
</tr></thead>
<tbody><tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">InputIt</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      The type of the iterators.
                    </p>
                  </td>
</tr></tbody>
</table></div>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h6"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.constraints"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.constraints">Constraints</a>
          </h6>
<p>
            <code class="computeroutput"><span class="identifier">InputIt</span></code>satisfies <span class="bold"><strong>InputIterator</strong></span>.
          </p>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h7"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.return_value"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.return_value">Return
            Value</a>
          </h6>
<p>
            <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
          </p>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h8"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.parameters"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.parameters">Parameters</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Name
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
</tr></thead>
<tbody>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">pos</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      The index to insert at.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">first</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      The beginning of the character range.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">last</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      The end of the character range.
                    </p>
                  </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="json.ref.boost__json__string.insert.overload4.h9"></a>
            <span class="phrase"><a name="json.ref.boost__json__string.insert.overload4.exceptions"></a></span><a class="link" href="overload4.html#json.ref.boost__json__string.insert.overload4.exceptions">Exceptions</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Type
                    </p>
                  </th>
<th>
                    <p>
                      Thrown On
                    </p>
                  </th>
</tr></thead>
<tbody>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">length_error</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">size</span><span class="special">()</span>
                      <span class="special">+</span> <span class="identifier">insert_count</span>
                      <span class="special">&gt;</span> <span class="identifier">max_size</span><span class="special">()</span></code>
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">out_of_range</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">pos</span> <span class="special">&gt;</span>
                      <span class="identifier">size</span><span class="special">()</span></code>
                    </p>
                  </td>
</tr>
</tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2019, 2020 Vinnie Falco<br>Copyright © 2020 Krystian Stasiowski<br>Copyright © 2022 Dmitry Arkhipov<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../insert.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../erase.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
