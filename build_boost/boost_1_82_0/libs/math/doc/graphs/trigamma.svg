<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="600" height ="400" version="1.1"
xmlns:svg ="http://www.w3.org/2000/svg"
xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
xmlns:cc="http://web.resource.org/cc/"
xmlns:dc="http://purl.org/dc/elements/1.1/"
xmlns ="http://www.w3.org/2000/svg"
>
<!-- SVG plot written using Boost.Plot program (C<PERSON>) --> 
<!-- Use, modification and distribution of Boost.Plot subject to the --> 
<!-- Boost Software License, Version 1.0.--> 
<!-- (See accompanying file LICENSE_1_0.txt --> 
<!-- or copy at http://www.boost.org/LICENSE_1_0.txt) --> 

<!-- SVG Plot Copyright <PERSON> 2008 --> 
<meta name="copyright" content="<PERSON>" />
<meta name="date" content="2008" />
<!-- Use, modification and distribution of this Scalable Vector Graphic file -->
<!-- are subject to the Boost Software License, Version 1.0. -->
<!-- (See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt) -->

<clipPath id="plot_window"><rect x="76.8" y="59" width="496.2" height="281"/></clipPath>
<g id="imageBackground" stroke="rgb(119,136,153)" fill="rgb(255,255,255)" stroke-width="2"><rect x="0" y="0" width="600" height="400"/></g>
<g id="plotBackground" stroke="rgb(119,136,153)" fill="rgb(255,255,255)" stroke-width="2"><rect x="75.8" y="58" width="498.2" height="283"/></g>
<g id="yMinorGrid" stroke="rgb(200,220,255)" stroke-width="0.5"></g>
<g id="yMajorGrid" stroke="rgb(200,220,255)" stroke-width="1"></g>
<g id="xMinorGrid" stroke="rgb(200,220,255)" stroke-width="0.5"></g>
<g id="xMajorGrid" stroke="rgb(200,220,255)" stroke-width="1"></g>
<g id="yAxis" stroke="rgb(0,0,0)" stroke-width="1"><line x1="315.2" y1="58" x2="315.2" y2="341"/><line x1="75.8" y1="58" x2="75.8" y2="341"/></g>
<g id="xAxis" stroke="rgb(0,0,0)" stroke-width="1"><line x1="75.8" y1="341" x2="574" y2="341"/></g>
<g id="yMinorTicks" stroke="rgb(0,0,0)" stroke-width="1"><path d="M73.8,331.4 L75.8,331.4 M73.8,319.8 L75.8,319.8 M73.8,308.1 L75.8,308.1 M73.8,284.8 L75.8,284.8 M73.8,273.2 L75.8,273.2 M73.8,261.5 L75.8,261.5 M73.8,238.2 L75.8,238.2 M73.8,226.6 L75.8,226.6 M73.8,215 L75.8,215 M73.8,191.7 L75.8,191.7 M73.8,180 L75.8,180 M73.8,168.4 L75.8,168.4 M73.8,145.1 L75.8,145.1 M73.8,133.4 L75.8,133.4 M73.8,121.8 L75.8,121.8 M73.8,98.49 L75.8,98.49 M73.8,86.84 L75.8,86.84 M73.8,75.2 L75.8,75.2 " fill="none"/></g>
<g id="xMinorTicks" stroke="rgb(0,0,0)" stroke-width="1"><path d="M327.6,341 L327.6,343 M340.1,341 L340.1,343 M352.5,341 L352.5,343 M377.5,341 L377.5,343 M389.9,341 L389.9,343 M402.4,341 L402.4,343 M427.3,341 L427.3,343 M439.7,341 L439.7,343 M452.2,341 L452.2,343 M477.1,341 L477.1,343 M489.5,341 L489.5,343 M502,341 L502,343 M526.9,341 L526.9,343 M539.3,341 L539.3,343 M551.8,341 L551.8,343 M302.7,341 L302.7,343 M290.3,341 L290.3,343 M277.8,341 L277.8,343 M252.9,341 L252.9,343 M240.5,341 L240.5,343 M228,341 L228,343 M203.1,341 L203.1,343 M190.7,341 L190.7,343 M178.2,341 L178.2,343 M153.3,341 L153.3,343 M140.9,341 L140.9,343 M128.4,341 L128.4,343 M103.5,341 L103.5,343 M91.05,341 L91.05,343 M78.6,341 L78.6,343 " fill="none"/></g>
<g id="yMajorTicks" stroke="rgb(0,0,0)" stroke-width="2"><path d="M70.8,296.5 L75.8,296.5 M70.8,249.9 L75.8,249.9 M70.8,203.3 L75.8,203.3 M70.8,156.7 L75.8,156.7 M70.8,110.1 L75.8,110.1 M70.8,63.55 L75.8,63.55 " fill="none"/></g>
<g id="xMajorTicks" stroke="rgb(0,0,0)" stroke-width="2"><path d="M315.2,341 L315.2,346 M365,341 L365,346 M414.8,341 L414.8,346 M464.6,341 L464.6,346 M514.4,341 L514.4,346 M564.2,341 L564.2,346 M315.2,341 L315.2,346 M265.4,341 L265.4,346 M215.6,341 L215.6,346 M165.8,341 L165.8,346 M116,341 L116,346 " fill="none"/></g>
<g id="xTicksValues">
<text x="315.2" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">0</text>
<text x="365" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">1</text>
<text x="414.8" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">2</text>
<text x="464.6" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">3</text>
<text x="514.4" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">4</text>
<text x="564.2" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">5</text>
<text x="315.2" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">0</text>
<text x="265.4" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">-1</text>
<text x="215.6" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">-2</text>
<text x="165.8" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">-3</text>
<text x="116" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">-4</text></g>
<g id="yTicksValues">
<text x="64.8" y="298.9" text-anchor="end" font-size="12" font-family="Verdana">5</text>
<text x="64.8" y="252.3" text-anchor="end" font-size="12" font-family="Verdana">10</text>
<text x="64.8" y="205.7" text-anchor="end" font-size="12" font-family="Verdana">15</text>
<text x="64.8" y="159.1" text-anchor="end" font-size="12" font-family="Verdana">20</text>
<text x="64.8" y="112.5" text-anchor="end" font-size="12" font-family="Verdana">25</text>
<text x="64.8" y="65.95" text-anchor="end" font-size="12" font-family="Verdana">30</text></g>
<g id="yLabel">
<text x="42.9" y="199.5" text-anchor="middle" transform = "rotate(-90 42.9 199.5 )" font-size="14" font-family="Verdana">trigamma(x)</text></g>
<g id="xLabel">
<text x="324.9" y="376.7" text-anchor="middle" font-size="14" font-family="Verdana">x</text></g>
<g id="plotLines" stroke-width="2"><g clip-path="url(#plot_window)" stroke="rgb(0,0,139)" stroke-width="1"><path d="M324.5,63.55 L325.7,121.5 L326.9,162.7 L328.1,192.9 L329.3,215.9 L330.5,233.7 L331.7,247.8 L332.9,259.2 L334.1,268.5 L335.3,276.2 L336.5,282.7 L337.7,288.2 L338.9,292.9 L340.1,297 L341.3,300.5 L342.5,303.6 L343.7,306.4 L344.9,308.8 L346.1,310.9 L347.3,312.9 L348.5,314.6 L349.7,316.2 L350.9,317.6 L352.1,318.9 L353.3,320.1 L354.5,321.1 L355.7,322.1 L356.9,323.1 L358.1,323.9 L359.2,324.7 L360.4,325.4 L361.6,326.1 L362.8,326.7 L364,327.3 L365.2,327.8 L366.4,328.4 L367.6,328.8 L368.8,329.3 L370,329.7 L371.2,330.1 L372.4,330.5 L373.6,330.9 L374.8,331.2 L376,331.5 L377.2,331.9 L378.4,332.1 L379.6,332.4 L380.8,332.7 L382,332.9 L383.2,333.2 L384.4,333.4 L385.6,333.6 L386.8,333.8 L388,334.1 L389.2,334.2 L390.4,334.4 L391.6,334.6 L392.8,334.8 L394,334.9 L395.2,335.1 L396.4,335.3 L397.6,335.4 L398.8,335.6 L400,335.7 L401.2,335.8 L402.4,335.9 L403.6,336.1 L404.8,336.2 L406,336.3 L407.2,336.4 L408.4,336.5 L409.6,336.6 L410.8,336.7 L412,336.8 L413.2,336.9 L414.4,337 L415.6,337.1 L416.8,337.2 L418,337.3 L419.2,337.4 L420.4,337.4 L421.6,337.5 L422.8,337.6 L424,337.7 L425.2,337.7 L426.4,337.8 L427.6,337.9 L428.8,338 L430,338 L431.2,338.1 L432.4,338.1 L433.6,338.2 L434.8,338.3 L436,338.3 L437.2,338.4 L438.4,338.4 L439.6,338.5 L440.8,338.5 L442,338.6 L443.2,338.6 L444.4,338.7 L445.6,338.7 L446.8,338.8 L448,338.8 L449.2,338.9 L450.4,338.9 L451.6,339 L452.7,339 L453.9,339 L455.1,339.1 L456.3,339.1 L457.5,339.2 L458.7,339.2 L459.9,339.2 L461.1,339.3 L462.3,339.3 L463.5,339.4 L464.7,339.4 L465.9,339.4 L467.1,339.5 L468.3,339.5 L469.5,339.5 L470.7,339.6 L471.9,339.6 L473.1,339.6 L474.3,339.6 L475.5,339.7 L476.7,339.7 L477.9,339.7 L479.1,339.8 L480.3,339.8 L481.5,339.8 L482.7,339.8 L483.9,339.9 L485.1,339.9 L486.3,339.9 L487.5,339.9 L488.7,340 L489.9,340 L491.1,340 L492.3,340 L493.5,340.1 L494.7,340.1 L495.9,340.1 L497.1,340.1 L498.3,340.2 L499.5,340.2 L500.7,340.2 L501.9,340.2 L503.1,340.2 L504.3,340.3 L505.5,340.3 L506.7,340.3 L507.9,340.3 L509.1,340.3 L510.3,340.4 L511.5,340.4 L512.7,340.4 L513.9,340.4 L515.1,340.4 L516.3,340.4 L517.5,340.5 L518.7,340.5 L519.9,340.5 L521.1,340.5 L522.3,340.5 L523.5,340.5 L524.7,340.6 L525.9,340.6 L527.1,340.6 L528.3,340.6 L529.5,340.6 L530.7,340.6 L531.9,340.7 L533.1,340.7 L534.3,340.7 L535.5,340.7 L536.7,340.7 L537.9,340.7 L539.1,340.7 L540.3,340.8 L541.5,340.8 L542.7,340.8 L543.9,340.8 L545.1,340.8 L546.3,340.8 L547.4,340.8 L548.6,340.8 L549.8,340.9 L551,340.9 L552.2,340.9 L553.4,340.9 L554.6,340.9 L555.8,340.9 L557,340.9 L558.2,340.9 L559.4,341 L560.6,341 L561.8,341 L563,341 L564.2,341 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(0,0,139)" stroke-width="1"><path d="M274.9,63.55 L275.1,71.46 L275.2,78.99 L275.4,86.18 L275.6,93.04 L275.7,99.59 L275.9,105.8 L276,111.8 L276.2,117.5 L276.3,123 L276.5,128.3 L276.6,133.3 L276.8,138.1 L276.9,142.7 L277.1,147.2 L277.2,151.4 L277.4,155.5 L277.6,159.5 L277.7,163.2 L277.9,166.9 L278,170.4 L278.2,173.7 L278.3,177 L278.5,180.1 L278.6,183.1 L278.8,186 L278.9,188.8 L279.1,191.5 L279.2,194.1 L279.4,196.6 L279.6,199 L279.7,201.4 L279.9,203.6 L280,205.8 L280.2,207.9 L280.3,210 L280.5,211.9 L280.6,213.8 L280.8,215.7 L280.9,217.4 L281.1,219.2 L281.2,220.8 L281.4,222.4 L281.6,224 L281.7,225.5 L281.9,226.9 L282,228.3 L282.2,229.7 L282.3,231 L282.5,232.3 L282.6,233.5 L282.8,234.7 L282.9,235.9 L283.1,237 L283.3,238.1 L283.4,239.1 L283.6,240.1 L283.7,241.1 L283.9,242 L284,242.9 L284.2,243.8 L284.3,244.7 L284.5,245.5 L284.6,246.3 L284.8,247 L284.9,247.8 L285.1,248.5 L285.3,249.2 L285.4,249.8 L285.6,250.5 L285.7,251.1 L285.9,251.7 L286,252.2 L286.2,252.8 L286.3,253.3 L286.5,253.8 L286.6,254.2 L286.8,254.7 L286.9,255.1 L287.1,255.5 L287.3,255.9 L287.4,256.3 L287.6,256.6 L287.7,257 L287.9,257.3 L288,257.6 L288.2,257.9 L288.3,258.1 L288.5,258.3 L288.6,258.6 L288.8,258.8 L288.9,259 L289.1,259.1 L289.3,259.3 L289.4,259.4 L289.6,259.5 L289.7,259.6 L289.9,259.7 L290,259.8 L290.2,259.8 L290.3,259.8 L290.5,259.8 L290.6,259.8 L290.8,259.8 L291,259.8 L291.1,259.7 L291.3,259.6 L291.4,259.5 L291.6,259.4 L291.7,259.3 L291.9,259.1 L292,259 L292.2,258.8 L292.3,258.6 L292.5,258.4 L292.6,258.1 L292.8,257.9 L293,257.6 L293.1,257.3 L293.3,257 L293.4,256.7 L293.6,256.3 L293.7,255.9 L293.9,255.5 L294,255.1 L294.2,254.7 L294.3,254.2 L294.5,253.7 L294.6,253.2 L294.8,252.7 L295,252.1 L295.1,251.6 L295.3,251 L295.4,250.3 L295.6,249.7 L295.7,249 L295.9,248.3 L296,247.6 L296.2,246.8 L296.3,246 L296.5,245.2 L296.6,244.4 L296.8,243.5 L297,242.6 L297.1,241.6 L297.3,240.6 L297.4,239.6 L297.6,238.6 L297.7,237.5 L297.9,236.4 L298,235.2 L298.2,234 L298.3,232.7 L298.5,231.5 L298.7,230.1 L298.8,228.7 L299,227.3 L299.1,225.8 L299.3,224.3 L299.4,222.7 L299.6,221 L299.7,219.3 L299.9,217.6 L300,215.7 L300.2,213.9 L300.3,211.9 L300.5,209.9 L300.7,207.8 L300.8,205.6 L301,203.3 L301.1,201 L301.3,198.6 L301.4,196.1 L301.6,193.5 L301.7,190.8 L301.9,188 L302,185 L302.2,182 L302.3,178.9 L302.5,175.6 L302.7,172.2 L302.8,168.7 L303,165 L303.1,161.2 L303.3,157.2 L303.4,153.1 L303.6,148.8 L303.7,144.3 L303.9,139.6 L304,134.7 L304.2,129.6 L304.3,124.3 L304.5,118.7 L304.7,112.9 L304.8,106.8 L305,100.4 L305.1,93.7 L305.3,86.7 L305.4,79.35 L305.6,71.64 L305.7,63.55 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(0,0,139)" stroke-width="1"><path d="M225.2,63.55 L225.3,71.27 L225.5,78.63 L225.6,85.65 L225.8,92.35 L225.9,98.76 L226.1,104.9 L226.3,110.7 L226.4,116.3 L226.6,121.7 L226.7,126.8 L226.9,131.8 L227,136.5 L227.2,141 L227.3,145.4 L227.5,149.5 L227.6,153.6 L227.8,157.4 L227.9,161.1 L228.1,164.7 L228.2,168.1 L228.4,171.4 L228.5,174.6 L228.7,177.7 L228.9,180.6 L229,183.5 L229.2,186.2 L229.3,188.9 L229.5,191.4 L229.6,193.9 L229.8,196.3 L229.9,198.6 L230.1,200.8 L230.2,203 L230.4,205 L230.5,207 L230.7,209 L230.8,210.8 L231,212.6 L231.2,214.4 L231.3,216.1 L231.5,217.7 L231.6,219.3 L231.8,220.8 L231.9,222.3 L232.1,223.7 L232.2,225.1 L232.4,226.4 L232.5,227.7 L232.7,229 L232.8,230.2 L233,231.4 L233.1,232.5 L233.3,233.6 L233.4,234.6 L233.6,235.7 L233.8,236.7 L233.9,237.6 L234.1,238.5 L234.2,239.4 L234.4,240.3 L234.5,241.1 L234.7,241.9 L234.8,242.7 L235,243.4 L235.1,244.2 L235.3,244.8 L235.4,245.5 L235.6,246.2 L235.7,246.8 L235.9,247.4 L236.1,247.9 L236.2,248.5 L236.4,249 L236.5,249.5 L236.7,250 L236.8,250.4 L237,250.9 L237.1,251.3 L237.3,251.7 L237.4,252.1 L237.6,252.4 L237.7,252.7 L237.9,253.1 L238,253.4 L238.2,253.6 L238.3,253.9 L238.5,254.1 L238.7,254.4 L238.8,254.6 L239,254.8 L239.1,254.9 L239.3,255.1 L239.4,255.2 L239.6,255.3 L239.7,255.4 L239.9,255.5 L240,255.6 L240.2,255.6 L240.3,255.7 L240.5,255.7 L240.6,255.7 L240.8,255.7 L241,255.6 L241.1,255.6 L241.3,255.5 L241.4,255.4 L241.6,255.3 L241.7,255.2 L241.9,255 L242,254.9 L242.2,254.7 L242.3,254.5 L242.5,254.3 L242.6,254 L242.8,253.8 L242.9,253.5 L243.1,253.2 L243.2,252.9 L243.4,252.6 L243.6,252.3 L243.7,251.9 L243.9,251.5 L244,251.1 L244.2,250.7 L244.3,250.2 L244.5,249.8 L244.6,249.3 L244.8,248.8 L244.9,248.2 L245.1,247.7 L245.2,247.1 L245.4,246.5 L245.5,245.8 L245.7,245.2 L245.9,244.5 L246,243.8 L246.2,243.1 L246.3,242.3 L246.5,241.5 L246.6,240.7 L246.8,239.8 L246.9,238.9 L247.1,238 L247.2,237.1 L247.4,236.1 L247.5,235.1 L247.7,234 L247.8,233 L248,231.8 L248.1,230.7 L248.3,229.5 L248.5,228.2 L248.6,226.9 L248.8,225.6 L248.9,224.2 L249.1,222.8 L249.2,221.3 L249.4,219.8 L249.5,218.2 L249.7,216.6 L249.8,214.9 L250,213.2 L250.1,211.4 L250.3,209.5 L250.4,207.6 L250.6,205.6 L250.7,203.5 L250.9,201.4 L251.1,199.1 L251.2,196.8 L251.4,194.5 L251.5,192 L251.7,189.4 L251.8,186.8 L252,184 L252.1,181.2 L252.3,178.2 L252.4,175.1 L252.6,172 L252.7,168.6 L252.9,165.2 L253,161.6 L253.2,157.9 L253.4,154 L253.5,150 L253.7,145.8 L253.8,141.4 L254,136.9 L254.1,132.1 L254.3,127.2 L254.4,122 L254.6,116.6 L254.7,111 L254.9,105.1 L255,98.97 L255.2,92.53 L255.3,85.79 L255.5,78.73 L255.6,71.32 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(0,0,139)" stroke-width="1"><path d="M175.4,63.55 L175.5,71.2 L175.7,78.49 L175.9,85.44 L176,92.09 L176.2,98.43 L176.3,104.5 L176.5,110.3 L176.6,115.9 L176.8,121.2 L176.9,126.3 L177.1,131.2 L177.2,135.9 L177.4,140.3 L177.5,144.7 L177.7,148.8 L177.8,152.8 L178,156.6 L178.1,160.3 L178.3,163.9 L178.5,167.3 L178.6,170.6 L178.8,173.7 L178.9,176.8 L179.1,179.7 L179.2,182.5 L179.4,185.3 L179.5,187.9 L179.7,190.4 L179.8,192.9 L180,195.2 L180.1,197.5 L180.3,199.7 L180.4,201.9 L180.6,203.9 L180.7,205.9 L180.9,207.8 L181,209.7 L181.2,211.5 L181.4,213.2 L181.5,214.9 L181.7,216.5 L181.8,218.1 L182,219.6 L182.1,221.1 L182.3,222.5 L182.4,223.9 L182.6,225.2 L182.7,226.5 L182.9,227.7 L183,228.9 L183.2,230.1 L183.3,231.2 L183.5,232.3 L183.6,233.4 L183.8,234.4 L184,235.3 L184.1,236.3 L184.3,237.2 L184.4,238.1 L184.6,239 L184.7,239.8 L184.9,240.6 L185,241.3 L185.2,242.1 L185.3,242.8 L185.5,243.5 L185.6,244.1 L185.8,244.8 L185.9,245.4 L186.1,246 L186.2,246.5 L186.4,247.1 L186.6,247.6 L186.7,248.1 L186.9,248.6 L187,249 L187.2,249.5 L187.3,249.9 L187.5,250.3 L187.6,250.6 L187.8,251 L187.9,251.3 L188.1,251.6 L188.2,251.9 L188.4,252.2 L188.5,252.5 L188.7,252.7 L188.8,252.9 L189,253.1 L189.1,253.3 L189.3,253.5 L189.5,253.6 L189.6,253.7 L189.8,253.9 L189.9,254 L190.1,254 L190.2,254.1 L190.4,254.1 L190.5,254.2 L190.7,254.2 L190.8,254.2 L191,254.2 L191.1,254.1 L191.3,254.1 L191.4,254 L191.6,253.9 L191.7,253.8 L191.9,253.7 L192.1,253.5 L192.2,253.3 L192.4,253.2 L192.5,253 L192.7,252.8 L192.8,252.5 L193,252.3 L193.1,252 L193.3,251.7 L193.4,251.4 L193.6,251.1 L193.7,250.7 L193.9,250.4 L194,250 L194.2,249.6 L194.3,249.1 L194.5,248.7 L194.6,248.2 L194.8,247.7 L195,247.2 L195.1,246.7 L195.3,246.1 L195.4,245.5 L195.6,244.9 L195.7,244.3 L195.9,243.6 L196,243 L196.2,242.2 L196.3,241.5 L196.5,240.7 L196.6,240 L196.8,239.1 L196.9,238.3 L197.1,237.4 L197.2,236.5 L197.4,235.5 L197.6,234.6 L197.7,233.6 L197.9,232.5 L198,231.4 L198.2,230.3 L198.3,229.1 L198.5,227.9 L198.6,226.7 L198.8,225.4 L198.9,224.1 L199.1,222.7 L199.2,221.3 L199.4,219.8 L199.5,218.3 L199.7,216.8 L199.8,215.1 L200,213.5 L200.2,211.7 L200.3,209.9 L200.5,208.1 L200.6,206.2 L200.8,204.2 L200.9,202.1 L201.1,200 L201.2,197.8 L201.4,195.5 L201.5,193.1 L201.7,190.7 L201.8,188.1 L202,185.5 L202.1,182.8 L202.3,179.9 L202.4,177 L202.6,174 L202.7,170.8 L202.9,167.5 L203.1,164.1 L203.2,160.5 L203.4,156.8 L203.5,153 L203.7,149 L203.8,144.9 L204,140.5 L204.1,136 L204.3,131.3 L204.4,126.4 L204.6,121.3 L204.7,116 L204.9,110.4 L205,104.6 L205.2,98.53 L205.3,92.17 L205.5,85.51 L205.7,78.53 L205.8,71.22 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(0,0,139)" stroke-width="1"><path d="M125.6,63.55 L125.8,71.16 L125.9,78.41 L126.1,85.34 L126.2,91.95 L126.4,98.27 L126.5,104.3 L126.7,110.1 L126.8,115.6 L127,120.9 L127.1,126 L127.3,130.9 L127.4,135.5 L127.6,140 L127.7,144.3 L127.9,148.4 L128,152.4 L128.2,156.2 L128.3,159.9 L128.5,163.4 L128.7,166.8 L128.8,170.1 L129,173.2 L129.1,176.3 L129.3,179.2 L129.4,182 L129.6,184.7 L129.7,187.4 L129.9,189.9 L130,192.3 L130.2,194.7 L130.3,197 L130.5,199.2 L130.6,201.3 L130.8,203.3 L130.9,205.3 L131.1,207.2 L131.2,209.1 L131.4,210.9 L131.6,212.6 L131.7,214.3 L131.9,215.9 L132,217.5 L132.2,219 L132.3,220.4 L132.5,221.9 L132.6,223.2 L132.8,224.6 L132.9,225.8 L133.1,227.1 L133.2,228.3 L133.4,229.4 L133.5,230.5 L133.7,231.6 L133.8,232.7 L134,233.7 L134.1,234.7 L134.3,235.6 L134.5,236.5 L134.6,237.4 L134.8,238.3 L134.9,239.1 L135.1,239.9 L135.2,240.6 L135.4,241.4 L135.5,242.1 L135.7,242.8 L135.8,243.4 L136,244.1 L136.1,244.7 L136.3,245.3 L136.4,245.8 L136.6,246.4 L136.7,246.9 L136.9,247.4 L137,247.8 L137.2,248.3 L137.4,248.7 L137.5,249.1 L137.7,249.5 L137.8,249.9 L138,250.3 L138.1,250.6 L138.3,250.9 L138.4,251.2 L138.6,251.5 L138.7,251.7 L138.9,252 L139,252.2 L139.2,252.4 L139.3,252.6 L139.5,252.7 L139.6,252.9 L139.8,253 L139.9,253.1 L140.1,253.2 L140.3,253.3 L140.4,253.3 L140.6,253.4 L140.7,253.4 L140.9,253.4 L141,253.4 L141.2,253.4 L141.3,253.4 L141.5,253.3 L141.6,253.2 L141.8,253.1 L141.9,253 L142.1,252.9 L142.2,252.7 L142.4,252.6 L142.5,252.4 L142.7,252.2 L142.8,252 L143,251.8 L143.2,251.5 L143.3,251.2 L143.5,250.9 L143.6,250.6 L143.8,250.3 L143.9,250 L144.1,249.6 L144.2,249.2 L144.4,248.8 L144.5,248.4 L144.7,247.9 L144.8,247.4 L145,247 L145.1,246.4 L145.3,245.9 L145.4,245.3 L145.6,244.8 L145.8,244.2 L145.9,243.5 L146.1,242.9 L146.2,242.2 L146.4,241.5 L146.5,240.7 L146.7,240 L146.8,239.2 L147,238.4 L147.1,237.5 L147.3,236.6 L147.4,235.7 L147.6,234.8 L147.7,233.8 L147.9,232.8 L148,231.7 L148.2,230.7 L148.3,229.5 L148.5,228.4 L148.7,227.2 L148.8,226 L149,224.7 L149.1,223.4 L149.3,222 L149.4,220.6 L149.6,219.1 L149.7,217.6 L149.9,216 L150,214.4 L150.2,212.7 L150.3,211 L150.5,209.2 L150.6,207.4 L150.8,205.5 L150.9,203.5 L151.1,201.4 L151.2,199.3 L151.4,197.1 L151.6,194.8 L151.7,192.5 L151.9,190 L152,187.5 L152.2,184.9 L152.3,182.2 L152.5,179.3 L152.6,176.4 L152.8,173.4 L152.9,170.2 L153.1,166.9 L153.2,163.5 L153.4,160 L153.5,156.3 L153.7,152.5 L153.8,148.5 L154,144.4 L154.1,140.1 L154.3,135.6 L154.5,130.9 L154.6,126.1 L154.8,121 L154.9,115.7 L155.1,110.2 L155.2,104.4 L155.4,98.32 L155.5,91.99 L155.7,85.37 L155.8,78.44 L156,71.17 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(0,0,139)" stroke-width="1"><path d="M75.8,63.55 L75.95,71.13 L76.11,78.37 L76.26,85.27 L76.41,91.86 L76.56,98.16 L76.72,104.2 L76.87,109.9 L77.02,115.5 L77.17,120.7 L77.33,125.8 L77.48,130.7 L77.63,135.3 L77.78,139.8 L77.94,144.1 L78.09,148.2 L78.24,152.2 L78.39,156 L78.55,159.6 L78.7,163.1 L78.85,166.5 L79,169.8 L79.16,172.9 L79.31,176 L79.46,178.9 L79.61,181.7 L79.77,184.4 L79.92,187 L80.07,189.6 L80.22,192 L80.38,194.3 L80.53,196.6 L80.68,198.8 L80.83,200.9 L80.99,203 L81.14,205 L81.29,206.9 L81.44,208.7 L81.6,210.5 L81.75,212.2 L81.9,213.9 L82.05,215.5 L82.21,217.1 L82.36,218.6 L82.51,220.1 L82.67,221.5 L82.82,222.8 L82.97,224.2 L83.12,225.4 L83.28,226.7 L83.43,227.9 L83.58,229 L83.73,230.1 L83.89,231.2 L84.04,232.3 L84.19,233.3 L84.34,234.3 L84.5,235.2 L84.65,236.1 L84.8,237 L84.95,237.8 L85.11,238.7 L85.26,239.5 L85.41,240.2 L85.56,241 L85.72,241.7 L85.87,242.3 L86.02,243 L86.17,243.6 L86.33,244.2 L86.48,244.8 L86.63,245.4 L86.78,245.9 L86.94,246.4 L87.09,246.9 L87.24,247.4 L87.39,247.9 L87.55,248.3 L87.7,248.7 L87.85,249.1 L88,249.5 L88.16,249.8 L88.31,250.1 L88.46,250.4 L88.62,250.7 L88.77,251 L88.92,251.3 L89.07,251.5 L89.23,251.7 L89.38,251.9 L89.53,252.1 L89.68,252.3 L89.84,252.4 L89.99,252.5 L90.14,252.7 L90.29,252.7 L90.45,252.8 L90.6,252.9 L90.75,252.9 L90.9,253 L91.06,253 L91.21,253 L91.36,252.9 L91.51,252.9 L91.67,252.8 L91.82,252.8 L91.97,252.7 L92.12,252.6 L92.28,252.4 L92.43,252.3 L92.58,252.1 L92.73,251.9 L92.89,251.7 L93.04,251.5 L93.19,251.3 L93.34,251 L93.5,250.8 L93.65,250.5 L93.8,250.2 L93.95,249.8 L94.11,249.5 L94.26,249.1 L94.41,248.7 L94.56,248.3 L94.72,247.9 L94.87,247.5 L95.02,247 L95.18,246.5 L95.33,246 L95.48,245.4 L95.63,244.9 L95.79,244.3 L95.94,243.7 L96.09,243.1 L96.24,242.4 L96.4,241.7 L96.55,241 L96.7,240.3 L96.85,239.5 L97.01,238.7 L97.16,237.9 L97.31,237.1 L97.46,236.2 L97.62,235.3 L97.77,234.3 L97.92,233.4 L98.07,232.3 L98.23,231.3 L98.38,230.2 L98.53,229.1 L98.68,227.9 L98.84,226.7 L98.99,225.5 L99.14,224.2 L99.29,222.9 L99.45,221.5 L99.6,220.1 L99.75,218.7 L99.9,217.2 L100.1,215.6 L100.2,214 L100.4,212.3 L100.5,210.6 L100.7,208.8 L100.8,207 L101,205.1 L101.1,203.1 L101.3,201 L101.4,198.9 L101.6,196.7 L101.7,194.4 L101.9,192.1 L102,189.6 L102.2,187.1 L102.3,184.5 L102.5,181.8 L102.7,179 L102.8,176.1 L103,173 L103.1,169.9 L103.3,166.6 L103.4,163.2 L103.6,159.7 L103.7,156 L103.9,152.2 L104,148.3 L104.2,144.1 L104.3,139.9 L104.5,135.4 L104.6,130.7 L104.8,125.9 L104.9,120.8 L105.1,115.5 L105.2,110 L105.4,104.2 L105.5,98.2 L105.7,91.89 L105.9,85.29 L106,78.38 L106.2,71.14 " fill="none"/></g>
</g>
<g id="plotPoints" clip-path="url(#plot_window)"></g>
<g id="title">
<text x="300" y="40" text-anchor="middle" font-size="20" font-family="Verdana">Trigamma</text></g>
<g id="plotXValues"></g>
<g id="plotYValues"></g>
</svg>
