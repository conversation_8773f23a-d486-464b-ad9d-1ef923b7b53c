<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="750" height ="400" version="1.1"
xmlns:svg ="http://www.w3.org/2000/svg"
xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
xmlns:cc="http://web.resource.org/cc/"
xmlns:dc="http://purl.org/dc/elements/1.1/"
xmlns ="http://www.w3.org/2000/svg"
>
<!-- SVG plot written using Boost.Plot program (C<PERSON>) --> 
<!-- Use, modification and distribution of Boost.Plot subject to the --> 
<!-- Boost Software License, Version 1.0.--> 
<!-- (See accompanying file LICENSE_1_0.txt --> 
<!-- or copy at http://www.boost.org/LICENSE_1_0.txt) --> 

<clipPath id="plot_window"><rect x="71.2" y="63" width="423.2" height="271.4"/></clipPath>
<g id="imageBackground" stroke="rgb(119,136,153)" fill="rgb(255,255,255)" stroke-width="2"><rect x="0" y="0" width="750" height="400"/></g>
<g id="plotBackground" stroke="rgb(119,136,153)" fill="rgb(255,255,255)" stroke-width="2"><rect x="70.2" y="62" width="425.2" height="273.4"/></g>
<g id="yMinorGrid" stroke="rgb(200,220,255)" stroke-width="0.5"></g>
<g id="yMajorGrid" stroke="rgb(200,220,255)" stroke-width="1"></g>
<g id="xMinorGrid" stroke="rgb(200,220,255)" stroke-width="0.5"></g>
<g id="xMajorGrid" stroke="rgb(200,220,255)" stroke-width="1"></g>
<g id="yAxis" stroke="rgb(0,0,0)" stroke-width="1"><line x1="282.8" y1="62" x2="282.8" y2="340.4"/><line x1="70.2" y1="62" x2="70.2" y2="335.4"/></g>
<g id="xAxis" stroke="rgb(0,0,0)" stroke-width="1"><line x1="65.2" y1="335.4" x2="495.4" y2="335.4"/><line x1="65.2" y1="335.4" x2="495.4" y2="335.4"/></g>
<g id="yMinorTicks" stroke="rgb(0,0,0)" stroke-width="1"><path d="M68.2,325.5 L70.2,325.5 M68.2,315.5 L70.2,315.5 M68.2,305.6 L70.2,305.6 M68.2,295.6 L70.2,295.6 M68.2,275.7 L70.2,275.7 M68.2,265.8 L70.2,265.8 M68.2,255.9 L70.2,255.9 M68.2,245.9 L70.2,245.9 M68.2,226 L70.2,226 M68.2,216.1 L70.2,216.1 M68.2,206.2 L70.2,206.2 M68.2,196.2 L70.2,196.2 M68.2,176.3 L70.2,176.3 M68.2,166.4 L70.2,166.4 M68.2,156.4 L70.2,156.4 M68.2,146.5 L70.2,146.5 M68.2,126.6 L70.2,126.6 M68.2,116.7 L70.2,116.7 M68.2,106.7 L70.2,106.7 M68.2,96.8 L70.2,96.8 M68.2,76.91 L70.2,76.91 M68.2,66.97 L70.2,66.97 M68.2,335.4 L70.2,335.4 " fill="none"/></g>
<g id="xMinorTicks" stroke="rgb(0,0,0)" stroke-width="1"><path d="M299,335.4 L299,337.4 M315.3,335.4 L315.3,337.4 M331.6,335.4 L331.6,337.4 M347.8,335.4 L347.8,337.4 M380.4,335.4 L380.4,337.4 M396.6,335.4 L396.6,337.4 M412.9,335.4 L412.9,337.4 M429.2,335.4 L429.2,337.4 M461.7,335.4 L461.7,337.4 M478,335.4 L478,337.4 M494.2,335.4 L494.2,337.4 M266.5,335.4 L266.5,337.4 M250.3,335.4 L250.3,337.4 M234,335.4 L234,337.4 M217.7,335.4 L217.7,337.4 M185.2,335.4 L185.2,337.4 M168.9,335.4 L168.9,337.4 M152.7,335.4 L152.7,337.4 M136.4,335.4 L136.4,337.4 M103.9,335.4 L103.9,337.4 M87.6,335.4 L87.6,337.4 M71.33,335.4 L71.33,337.4 " fill="none"/></g>
<g id="yMajorTicks" stroke="rgb(0,0,0)" stroke-width="2"><path d="M65.2,335.4 L70.2,335.4 M65.2,285.7 L70.2,285.7 M65.2,236 L70.2,236 M65.2,186.3 L70.2,186.3 M65.2,136.6 L70.2,136.6 M65.2,86.85 L70.2,86.85 M65.2,335.4 L70.2,335.4 " fill="none"/></g>
<g id="xMajorTicks" stroke="rgb(0,0,0)" stroke-width="2"><path d="M282.8,335.4 L282.8,340.4 M364.1,335.4 L364.1,340.4 M445.4,335.4 L445.4,340.4 M282.8,335.4 L282.8,340.4 M201.5,335.4 L201.5,340.4 M120.1,335.4 L120.1,340.4 " fill="none"/></g>
<g id="plotLabels">
<text x="282.8" y="357.2" text-anchor="middle" font-size="14" font-family="Verdana">0</text>
<text x="364.1" y="357.2" text-anchor="middle" font-size="14" font-family="Verdana">5</text>
<text x="445.4" y="357.2" text-anchor="middle" font-size="14" font-family="Verdana">10</text>
<text x="282.8" y="357.2" text-anchor="middle" font-size="14" font-family="Verdana">0</text>
<text x="201.5" y="357.2" text-anchor="middle" font-size="14" font-family="Verdana">-5</text>
<text x="120.1" y="357.2" text-anchor="middle" font-size="14" font-family="Verdana">-10</text>
<text x="59.2" y="337.8" text-anchor="end" font-size="12" font-family="Verdana">0</text>
<text x="59.2" y="288.1" text-anchor="end" font-size="12" font-family="Verdana">0.1</text>
<text x="59.2" y="238.4" text-anchor="end" font-size="12" font-family="Verdana">0.2</text>
<text x="59.2" y="188.7" text-anchor="end" font-size="12" font-family="Verdana">0.3</text>
<text x="59.2" y="139" text-anchor="end" font-size="12" font-family="Verdana">0.4</text>
<text x="59.2" y="89.25" text-anchor="end" font-size="12" font-family="Verdana">0.5</text>
<text x="59.2" y="337.8" text-anchor="end" font-size="12" font-family="Verdana">0</text></g>
<g id="yLabel">
<text x="30.2" y="198.7" text-anchor="middle" transform = "rotate(-90 30.2 198.7 )" font-size="14" font-family="Verdana">Probability</text></g>
<g id="xLabel">
<text x="282.8" y="388" text-anchor="middle" font-size="14" font-family="Verdana">Random Variable</text></g>
<g id="plotLines" stroke-width="2"><g clip-path="url(#plot_window)" stroke="rgb(0,0,139)" stroke-width="1"><path d="M70.2,335.4 L72.33,335.4 L74.45,335.4 L76.58,335.4 L78.7,335.4 L80.83,335.4 L82.95,335.4 L85.08,335.4 L87.21,335.4 L89.33,335.4 L91.46,335.4 L93.58,335.4 L95.71,335.4 L97.84,335.4 L99.96,335.4 L102.1,335.4 L104.2,335.4 L106.3,335.4 L108.5,335.4 L110.6,335.4 L112.7,335.4 L114.8,335.4 L117,335.4 L119.1,335.4 L121.2,335.4 L123.3,335.4 L125.5,335.4 L127.6,335.4 L129.7,335.4 L131.8,335.4 L134,335.3 L136.1,335.3 L138.2,335.3 L140.4,335.3 L142.5,335.3 L144.6,335.3 L146.7,335.3 L148.9,335.3 L151,335.2 L153.1,335.2 L155.2,335.2 L157.4,335.2 L159.5,335.1 L161.6,335.1 L163.7,335.1 L165.9,335 L168,335 L170.1,334.9 L172.2,334.8 L174.4,334.8 L176.5,334.7 L178.6,334.6 L180.7,334.5 L182.9,334.3 L185,334.2 L187.1,334 L189.2,333.8 L191.4,333.6 L193.5,333.4 L195.6,333.1 L197.7,332.8 L199.9,332.4 L202,332 L204.1,331.5 L206.3,331 L208.4,330.4 L210.5,329.7 L212.6,328.9 L214.8,328 L216.9,327 L219,325.9 L221.1,324.7 L223.3,323.2 L225.4,321.6 L227.5,319.8 L229.6,317.8 L231.8,315.6 L233.9,313.1 L236,310.3 L238.1,307.2 L240.3,303.8 L242.4,300.1 L244.5,296 L246.6,291.5 L248.8,286.8 L250.9,281.6 L253,276.2 L255.1,270.4 L257.3,264.5 L259.4,258.3 L261.5,252.1 L263.6,245.8 L265.8,239.7 L267.9,233.9 L270,228.4 L272.2,223.5 L274.3,219.2 L276.4,215.8 L278.5,213.2 L280.7,211.7 L282.8,211.1 L284.9,211.7 L287,213.2 L289.2,215.8 L291.3,219.2 L293.4,223.5 L295.5,228.4 L297.7,233.9 L299.8,239.7 L301.9,245.8 L304,252.1 L306.2,258.3 L308.3,264.5 L310.4,270.4 L312.5,276.2 L314.7,281.6 L316.8,286.7 L318.9,291.5 L321,296 L323.2,300.1 L325.3,303.8 L327.4,307.2 L329.5,310.3 L331.7,313.1 L333.8,315.6 L335.9,317.8 L338.1,319.8 L340.2,321.6 L342.3,323.2 L344.4,324.7 L346.6,325.9 L348.7,327 L350.8,328 L352.9,328.9 L355.1,329.7 L357.2,330.4 L359.3,331 L361.4,331.5 L363.6,332 L365.7,332.4 L367.8,332.8 L369.9,333.1 L372.1,333.4 L374.2,333.6 L376.3,333.8 L378.4,334 L380.6,334.2 L382.7,334.3 L384.8,334.5 L386.9,334.6 L389.1,334.7 L391.2,334.8 L393.3,334.8 L395.4,334.9 L397.6,335 L399.7,335 L401.8,335.1 L404,335.1 L406.1,335.1 L408.2,335.2 L410.3,335.2 L412.5,335.2 L414.6,335.2 L416.7,335.3 L418.8,335.3 L421,335.3 L423.1,335.3 L425.2,335.3 L427.3,335.3 L429.5,335.3 L431.6,335.3 L433.7,335.4 L435.8,335.4 L438,335.4 L440.1,335.4 L442.2,335.4 L444.3,335.4 L446.5,335.4 L448.6,335.4 L450.7,335.4 L452.8,335.4 L455,335.4 L457.1,335.4 L459.2,335.4 L461.3,335.4 L463.5,335.4 L465.6,335.4 L467.7,335.4 L469.9,335.4 L472,335.4 L474.1,335.4 L476.2,335.4 L478.4,335.4 L480.5,335.4 L482.6,335.4 L484.7,335.4 L486.9,335.4 L489,335.4 L491.1,335.4 L493.2,335.4 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(139,0,0)" stroke-width="1"><path d="M70.2,333.3 L72.33,333.2 L74.45,333.1 L76.58,333 L78.7,332.9 L80.83,332.8 L82.95,332.7 L85.08,332.6 L87.21,332.5 L89.33,332.4 L91.46,332.2 L93.58,332.1 L95.71,332 L97.84,331.8 L99.96,331.7 L102.1,331.5 L104.2,331.3 L106.3,331.2 L108.5,331 L110.6,330.8 L112.7,330.6 L114.8,330.4 L117,330.2 L119.1,330 L121.2,329.8 L123.3,329.5 L125.5,329.3 L127.6,329.1 L129.7,328.8 L131.8,328.5 L134,328.2 L136.1,328 L138.2,327.7 L140.4,327.3 L142.5,327 L144.6,326.7 L146.7,326.4 L148.9,326 L151,325.6 L153.1,325.3 L155.2,324.9 L157.4,324.5 L159.5,324 L161.6,323.6 L163.7,323.2 L165.9,322.7 L168,322.3 L170.1,321.8 L172.2,321.3 L174.4,320.8 L176.5,320.3 L178.6,319.7 L180.7,319.2 L182.9,318.6 L185,318.1 L187.1,317.5 L189.2,316.9 L191.4,316.3 L193.5,315.7 L195.6,315 L197.7,314.4 L199.9,313.7 L202,313.1 L204.1,312.4 L206.3,311.8 L208.4,311.1 L210.5,310.4 L212.6,309.7 L214.8,309 L216.9,308.3 L219,307.6 L221.1,306.9 L223.3,306.2 L225.4,305.5 L227.5,304.9 L229.6,304.2 L231.8,303.5 L233.9,302.9 L236,302.2 L238.1,301.6 L240.3,300.9 L242.4,300.3 L244.5,299.7 L246.6,299.2 L248.8,298.6 L250.9,298.1 L253,297.6 L255.1,297.1 L257.3,296.7 L259.4,296.3 L261.5,295.9 L263.6,295.5 L265.8,295.2 L267.9,294.9 L270,294.7 L272.2,294.5 L274.3,294.3 L276.4,294.2 L278.5,294.1 L280.7,294 L282.8,294 L284.9,294 L287,294.1 L289.2,294.2 L291.3,294.3 L293.4,294.5 L295.5,294.7 L297.7,294.9 L299.8,295.2 L301.9,295.5 L304,295.9 L306.2,296.3 L308.3,296.7 L310.4,297.1 L312.5,297.6 L314.7,298.1 L316.8,298.6 L318.9,299.2 L321,299.7 L323.2,300.3 L325.3,300.9 L327.4,301.6 L329.5,302.2 L331.7,302.9 L333.8,303.5 L335.9,304.2 L338.1,304.9 L340.2,305.5 L342.3,306.2 L344.4,306.9 L346.6,307.6 L348.7,308.3 L350.8,309 L352.9,309.7 L355.1,310.4 L357.2,311.1 L359.3,311.8 L361.4,312.4 L363.6,313.1 L365.7,313.7 L367.8,314.4 L369.9,315 L372.1,315.7 L374.2,316.3 L376.3,316.9 L378.4,317.5 L380.6,318.1 L382.7,318.6 L384.8,319.2 L386.9,319.7 L389.1,320.3 L391.2,320.8 L393.3,321.3 L395.4,321.8 L397.6,322.3 L399.7,322.7 L401.8,323.2 L404,323.6 L406.1,324 L408.2,324.5 L410.3,324.9 L412.5,325.3 L414.6,325.6 L416.7,326 L418.8,326.4 L421,326.7 L423.1,327 L425.2,327.3 L427.3,327.7 L429.5,328 L431.6,328.2 L433.7,328.5 L435.8,328.8 L438,329.1 L440.1,329.3 L442.2,329.5 L444.3,329.8 L446.5,330 L448.6,330.2 L450.7,330.4 L452.8,330.6 L455,330.8 L457.1,331 L459.2,331.2 L461.3,331.3 L463.5,331.5 L465.6,331.7 L467.7,331.8 L469.9,332 L472,332.1 L474.1,332.2 L476.2,332.4 L478.4,332.5 L480.5,332.6 L482.6,332.7 L484.7,332.8 L486.9,332.9 L489,333 L491.1,333.1 L493.2,333.2 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(0,100,0)" stroke-width="1"><path d="M70.2,335.4 L72.33,335.4 L74.45,335.4 L76.58,335.4 L78.7,335.4 L80.83,335.4 L82.95,335.4 L85.08,335.4 L87.21,335.4 L89.33,335.4 L91.46,335.4 L93.58,335.4 L95.71,335.4 L97.84,335.4 L99.96,335.4 L102.1,335.4 L104.2,335.4 L106.3,335.4 L108.5,335.4 L110.6,335.4 L112.7,335.4 L114.8,335.4 L117,335.4 L119.1,335.4 L121.2,335.4 L123.3,335.4 L125.5,335.4 L127.6,335.4 L129.7,335.4 L131.8,335.4 L134,335.4 L136.1,335.4 L138.2,335.4 L140.4,335.4 L142.5,335.4 L144.6,335.4 L146.7,335.4 L148.9,335.4 L151,335.4 L153.1,335.4 L155.2,335.4 L157.4,335.4 L159.5,335.4 L161.6,335.4 L163.7,335.4 L165.9,335.4 L168,335.4 L170.1,335.4 L172.2,335.4 L174.4,335.4 L176.5,335.4 L178.6,335.4 L180.7,335.4 L182.9,335.4 L185,335.4 L187.1,335.4 L189.2,335.4 L191.4,335.4 L193.5,335.4 L195.6,335.4 L197.7,335.4 L199.9,335.4 L202,335.4 L204.1,335.3 L206.3,335.3 L208.4,335.3 L210.5,335.3 L212.6,335.2 L214.8,335.2 L216.9,335.1 L219,335 L221.1,334.9 L223.3,334.7 L225.4,334.5 L227.5,334.3 L229.6,334 L231.8,333.5 L233.9,333 L236,332.3 L238.1,331.3 L240.3,330.1 L242.4,328.6 L244.5,326.6 L246.6,324 L248.8,320.7 L250.9,316.5 L253,311.1 L255.1,304.3 L257.3,295.7 L259.4,285.2 L261.5,272.2 L263.6,256.5 L265.8,238.1 L267.9,217 L270,193.5 L272.2,168.8 L274.3,144.1 L276.4,121.5 L278.5,103.1 L280.7,91.06 L282.8,86.85 L284.9,91.05 L287,103.1 L289.2,121.5 L291.3,144.1 L293.4,168.7 L295.5,193.5 L297.7,216.9 L299.8,238.1 L301.9,256.5 L304,272.2 L306.2,285.2 L308.3,295.7 L310.4,304.3 L312.5,311.1 L314.7,316.4 L316.8,320.7 L318.9,324 L321,326.6 L323.2,328.6 L325.3,330.1 L327.4,331.3 L329.5,332.3 L331.7,333 L333.8,333.5 L335.9,334 L338.1,334.3 L340.2,334.5 L342.3,334.7 L344.4,334.9 L346.6,335 L348.7,335.1 L350.8,335.2 L352.9,335.2 L355.1,335.3 L357.2,335.3 L359.3,335.3 L361.4,335.3 L363.6,335.4 L365.7,335.4 L367.8,335.4 L369.9,335.4 L372.1,335.4 L374.2,335.4 L376.3,335.4 L378.4,335.4 L380.6,335.4 L382.7,335.4 L384.8,335.4 L386.9,335.4 L389.1,335.4 L391.2,335.4 L393.3,335.4 L395.4,335.4 L397.6,335.4 L399.7,335.4 L401.8,335.4 L404,335.4 L406.1,335.4 L408.2,335.4 L410.3,335.4 L412.5,335.4 L414.6,335.4 L416.7,335.4 L418.8,335.4 L421,335.4 L423.1,335.4 L425.2,335.4 L427.3,335.4 L429.5,335.4 L431.6,335.4 L433.7,335.4 L435.8,335.4 L438,335.4 L440.1,335.4 L442.2,335.4 L444.3,335.4 L446.5,335.4 L448.6,335.4 L450.7,335.4 L452.8,335.4 L455,335.4 L457.1,335.4 L459.2,335.4 L461.3,335.4 L463.5,335.4 L465.6,335.4 L467.7,335.4 L469.9,335.4 L472,335.4 L474.1,335.4 L476.2,335.4 L478.4,335.4 L480.5,335.4 L482.6,335.4 L484.7,335.4 L486.9,335.4 L489,335.4 L491.1,335.4 L493.2,335.4 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(255,140,0)" stroke-width="1"><path d="M70.2,335.4 L72.33,335.4 L74.45,335.4 L76.58,335.4 L78.7,335.4 L80.83,335.4 L82.95,335.4 L85.08,335.4 L87.21,335.4 L89.33,335.4 L91.46,335.4 L93.58,335.4 L95.71,335.4 L97.84,335.4 L99.96,335.4 L102.1,335.4 L104.2,335.4 L106.3,335.4 L108.5,335.4 L110.6,335.4 L112.7,335.4 L114.8,335.4 L117,335.4 L119.1,335.4 L121.2,335.4 L123.3,335.4 L125.5,335.4 L127.6,335.4 L129.7,335.4 L131.8,335.4 L134,335.4 L136.1,335.4 L138.2,335.4 L140.4,335.4 L142.5,335.4 L144.6,335.4 L146.7,335.4 L148.9,335.4 L151,335.4 L153.1,335.4 L155.2,335.4 L157.4,335.4 L159.5,335.4 L161.6,335.4 L163.7,335.4 L165.9,335.4 L168,335.4 L170.1,335.4 L172.2,335.4 L174.4,335.4 L176.5,335.4 L178.6,335.4 L180.7,335.4 L182.9,335.4 L185,335.4 L187.1,335.4 L189.2,335.4 L191.4,335.4 L193.5,335.4 L195.6,335.4 L197.7,335.4 L199.9,335.4 L202,335.4 L204.1,335.4 L206.3,335.4 L208.4,335.4 L210.5,335.4 L212.6,335.4 L214.8,335.3 L216.9,335.3 L219,335.3 L221.1,335.3 L223.3,335.3 L225.4,335.3 L227.5,335.3 L229.6,335.3 L231.8,335.3 L233.9,335.2 L236,335.2 L238.1,335.2 L240.3,335.2 L242.4,335.1 L244.5,335.1 L246.6,335 L248.8,335 L250.9,334.9 L253,334.9 L255.1,334.8 L257.3,334.7 L259.4,334.6 L261.5,334.5 L263.6,334.4 L265.8,334.2 L267.9,334.1 L270,333.9 L272.2,333.7 L274.3,333.4 L276.4,333.2 L278.5,332.8 L280.7,332.5 L282.8,332.1 L284.9,331.6 L287,331.1 L289.2,330.5 L291.3,329.9 L293.4,329.1 L295.5,328.3 L297.7,327.3 L299.8,326.2 L301.9,325 L304,323.6 L306.2,322.1 L308.3,320.3 L310.4,318.4 L312.5,316.2 L314.7,313.7 L316.8,311 L318.9,308 L321,304.7 L323.2,301 L325.3,297.1 L327.4,292.7 L329.5,288 L331.7,283 L333.8,277.6 L335.9,271.9 L338.1,266 L340.2,259.9 L342.3,253.7 L344.4,247.4 L346.6,241.3 L348.7,235.4 L350.8,229.8 L352.9,224.7 L355.1,220.3 L357.2,216.6 L359.3,213.8 L361.4,212 L363.6,211.2 L365.7,211.4 L367.8,212.7 L369.9,215 L372.1,218.3 L374.2,222.3 L376.3,227.1 L378.4,232.4 L380.6,238.2 L382.7,244.3 L384.8,250.5 L386.9,256.7 L389.1,262.9 L391.2,268.9 L393.3,274.7 L395.4,280.2 L397.6,285.5 L399.7,290.3 L401.8,294.9 L404,299 L406.1,302.9 L408.2,306.3 L410.3,309.5 L412.5,312.4 L414.6,315 L416.7,317.3 L418.8,319.3 L421,321.2 L423.1,322.8 L425.2,324.3 L427.3,325.6 L429.5,326.8 L431.6,327.8 L433.7,328.7 L435.8,329.5 L438,330.2 L440.1,330.8 L442.2,331.4 L444.3,331.9 L446.5,332.3 L448.6,332.7 L450.7,333 L452.8,333.3 L455,333.6 L457.1,333.8 L459.2,334 L461.3,334.1 L463.5,334.3 L465.6,334.4 L467.7,334.6 L469.9,334.7 L472,334.7 L474.1,334.8 L476.2,334.9 L478.4,335 L480.5,335 L482.6,335.1 L484.7,335.1 L486.9,335.1 L489,335.2 L491.1,335.2 L493.2,335.2 " fill="none"/></g>
</g>
<g id="plotPoints" clip-path="url(#plot_window)"></g>
<g id="legendBackground" stroke="rgb(119,136,153)" fill="rgb(255,255,255)" stroke-width="1"><rect x="509.4" y="62" width="228.6" height="135"/><rect x="509.4" y="62" width="228.6" height="135"/></g>
<g id="legendPoints"><g stroke="rgb(0,0,139)" stroke-width="1"><line x1="524.4" y1="92" x2="539.4" y2="92"/></g>
<g stroke="rgb(139,0,0)" stroke-width="1"><line x1="524.4" y1="122" x2="539.4" y2="122"/></g>
<g stroke="rgb(0,100,0)" stroke-width="1"><line x1="524.4" y1="152" x2="539.4" y2="152"/></g>
<g stroke="rgb(255,140,0)" stroke-width="1"><line x1="524.4" y1="182" x2="539.4" y2="182"/></g>
</g>
<g id="legendText">
<text x="546.9" y="92" font-size="15" font-family="Verdana">location=0, scale=1</text>
<text x="546.9" y="122" font-size="15" font-family="Verdana">location=0, scale=3</text>
<text x="546.9" y="152" font-size="15" font-family="Verdana">location=0, scale=0.5</text>
<text x="546.9" y="182" font-size="15" font-family="Verdana">location=5, scale=1</text></g>
<g id="title">
<text x="375" y="40" text-anchor="middle" font-size="20" font-family="Verdana">Logistic Distribution PDF</text></g>
<g id="plotXValues"></g>
<g id="plotYValues"></g>
</svg>
