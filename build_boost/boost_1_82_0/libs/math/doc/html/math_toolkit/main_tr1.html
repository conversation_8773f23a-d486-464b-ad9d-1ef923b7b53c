<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>C99 and TR1 C Functions Overview</title>
<link rel="stylesheet" href="../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../extern_c.html" title='Chapter 9. TR1 and C99 external "C" Functions'>
<link rel="prev" href="../extern_c.html" title='Chapter 9. TR1 and C99 external "C" Functions'>
<link rel="next" href="c99.html" title="C99 C Functions">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../extern_c.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../extern_c.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="c99.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="math_toolkit.main_tr1"></a><a class="link" href="main_tr1.html" title="C99 and TR1 C Functions Overview">C99 and TR1 C Functions Overview</a>
</h2></div></div></div>
<p>
      Many of the special functions included in this library are also a part of the
      either the <a href="http://www.open-std.org/JTC1/SC22/WG14/www/docs/n1256.pdf" target="_top">C99
      Standard ISO/IEC 9899:1999</a> or the <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2005/n1836.pdf" target="_top">Technical
      Report on C++ Library Extensions</a>. Therefore this library includes a
      thin wrapper header <code class="computeroutput"><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">tr1</span><span class="special">.</span><span class="identifier">hpp</span></code> that
      provides compatibility with these two standards.
    </p>
<p>
      There are various pros and cons to using the library in this way:
    </p>
<p>
      Pros:
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          The header to include is lightweight (i.e. fast to compile).
        </li>
<li class="listitem">
          The functions have extern "C" linkage, and so are usable from
          other languages (not just C and C++).
        </li>
<li class="listitem">
          C99 and C++ TR1 Standard compatibility.
        </li>
</ul></div>
<p>
      Cons:
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          You will need to compile and link to the external Boost.Math libraries.
        </li>
<li class="listitem">
          Limited to support for the types, <code class="computeroutput"><span class="keyword">float</span></code>,
          <code class="computeroutput"><span class="keyword">double</span></code> and <code class="computeroutput"><span class="keyword">long</span>
          <span class="keyword">double</span></code>.
        </li>
<li class="listitem">
          Error handling is handled via setting ::errno and returning NaN's and infinities:
          this may be less flexible than an C++ exception based approach.
        </li>
</ul></div>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
        The separate libraries are required <span class="bold"><strong>only</strong></span>
        if you choose to use boost/math/tr1.hpp rather than some other Boost.Math
        header, the rest of Boost.Math remains header-only.
      </p></td></tr>
</table></div>
<p>
      The separate libraries required in order to use tr1.hpp can be compiled using
      bjam from within the libs/math/build directory, or from the Boost root directory
      using the usual Boost-wide install procedure. Alternatively the source files
      are located in libs/math/src and each have the same name as the function they
      implement. The various libraries are named as follows:
    </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Name
              </p>
            </th>
<th>
              <p>
                Type
              </p>
            </th>
<th>
              <p>
                Functions
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                boost_math_c99f-&lt;suffix&gt;
              </p>
            </td>
<td>
              <p>
                float
              </p>
            </td>
<td>
              <p>
                C99 Functions
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                boost_math_c99-&lt;suffix&gt;
              </p>
            </td>
<td>
              <p>
                double
              </p>
            </td>
<td>
              <p>
                C99 Functions
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                boost_math_c99l-&lt;suffix&gt;
              </p>
            </td>
<td>
              <p>
                long double
              </p>
            </td>
<td>
              <p>
                C99 Functions
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                boost_math_tr1f-&lt;suffix&gt;
              </p>
            </td>
<td>
              <p>
                float
              </p>
            </td>
<td>
              <p>
                TR1 Functions
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                boost_math_tr1-&lt;suffix&gt;
              </p>
            </td>
<td>
              <p>
                double
              </p>
            </td>
<td>
              <p>
                TR1 Functions
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                boost_math_tr1l-&lt;suffix&gt;
              </p>
            </td>
<td>
              <p>
                long double
              </p>
            </td>
<td>
              <p>
                TR1 Functions
              </p>
            </td>
</tr>
</tbody>
</table></div>
<p>
      Where <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">suffix</span><span class="special">&gt;</span></code> encodes the compiler and build options
      used to build the libraries: for example "libboost_math_tr1-vc80-mt-gd.lib"
      would be the statically linked TR1 library to use with Visual C++ 8.0, in multithreading
      debug mode, with the DLL VC++ runtime, where as "boost_math_tr1-vc80-mt.lib"
      would be import library for the TR1 DLL to be used with Visual C++ 8.0 with
      the release multithreaded DLL VC++ runtime. Refer to the getting started guide
      for a <a href="http://www.boost.org/doc/libs/1_35_0/more/getting_started/windows.html#library-naming" target="_top">full
      explanation of the <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">suffix</span><span class="special">&gt;</span></code> meanings</a>.
    </p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top">
<p>
        Visual C++ users will typically have the correct library variant to link
        against selected for them by boost/math/tr1.hpp based on your compiler settings.
      </p>
<p>
        Users will need to define BOOST_MATH_TR1_DYN_LINK when building their code
        if they want to link against the DLL versions of these libraries rather than
        the static versions.
      </p>
<p>
        Users can disable auto-linking by defining BOOST_MATH_TR1_NO_LIB when building:
        this is typically only used when linking against a customised build of the
        libraries.
      </p>
</td></tr>
</table></div>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
        Linux and Unix users will generally only have one variant of these libraries
        installed, and can generally just link against -lboost_math_tr1 etc.
      </p></td></tr>
</table></div>
<h5>
<a name="math_toolkit.main_tr1.h0"></a>
      <span class="phrase"><a name="math_toolkit.main_tr1.usage_recommendations"></a></span><a class="link" href="main_tr1.html#math_toolkit.main_tr1.usage_recommendations">Usage
      Recommendations</a>
    </h5>
<p>
      This library now presents the user with a choice:
    </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
          To include the header only versions of the functions and have an easier
          time linking, but a longer compile time.
        </li>
<li class="listitem">
          To include the TR1 headers and link against an external library.
        </li>
</ul></div>
<p>
      Which option you choose depends largely on how you prefer to work and how your
      system is set up.
    </p>
<p>
      For example a casual user who just needs the acosh function, would probably
      be better off including <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">acosh</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code> and using <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">acosh</span><span class="special">(</span><span class="identifier">x</span><span class="special">)</span></code> in their code.
    </p>
<p>
      However, for large scale software development where compile times are significant,
      and where the Boost libraries are already built and installed on the system,
      then including <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">tr1</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code> and using <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">tr1</span><span class="special">::</span><span class="identifier">acosh</span><span class="special">(</span><span class="identifier">x</span><span class="special">)</span></code>
      will speed up compile times, reduce object files sizes (since there are no
      templates being instantiated any more), and also speed up debugging runtimes
      - since the externally compiled libraries can be compiler optimised, rather
      than built using full settings - the difference in performance between <a class="link" href="getting_best.html" title="Getting the Best Performance from this Library: Compiler and Compiler Options">release and debug builds can be as much
      as 20 times</a>, so for complex applications this can be a big win.
    </p>
<h5>
<a name="math_toolkit.main_tr1.h1"></a>
      <span class="phrase"><a name="math_toolkit.main_tr1.supported_c99_functions"></a></span><a class="link" href="main_tr1.html#math_toolkit.main_tr1.supported_c99_functions">Supported
      C99 Functions</a>
    </h5>
<p>
      See also the <a class="link" href="c99.html" title="C99 C Functions">quick reference guide for these
      functions</a>.
    </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">tr1</span><span class="special">{</span> <span class="keyword">extern</span> <span class="string">"C"</span><span class="special">{</span>

<span class="keyword">typedef</span> <span class="identifier">unspecified</span> <span class="identifier">float_t</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">unspecified</span> <span class="identifier">double_t</span><span class="special">;</span>

<span class="keyword">double</span> <span class="identifier">acosh</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">acoshf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">acoshl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">asinh</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">asinhf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">asinhl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">atanh</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">atanhf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">atanhl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">cbrt</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">cbrtf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">cbrtl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">copysign</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">copysignf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">copysignl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">erf</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">erff</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">erfl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">erfc</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">erfcf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">erfcl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">expm1</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">expm1f</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">expm1l</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">fmax</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">fmaxf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">fmaxl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">fmin</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">fminf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">fminl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">hypot</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">hypotf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">hypotl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">lgamma</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">lgammaf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">lgammal</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">llround</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">llroundf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">llroundl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">log1p</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">log1pf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">log1pl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">long</span> <span class="identifier">lround</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="identifier">lroundf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="identifier">lroundl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">nextafter</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">nextafterf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nextafterl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">nexttoward</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">nexttowardf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nexttowardl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">round</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">roundf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">roundl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">tgamma</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">tgammaf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">tgammal</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">trunc</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">truncf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">truncl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="special">}}}}</span> <span class="comment">// namespaces</span>
</pre>
<h5>
<a name="math_toolkit.main_tr1.h2"></a>
      <span class="phrase"><a name="math_toolkit.main_tr1.supported_tr1_functions"></a></span><a class="link" href="main_tr1.html#math_toolkit.main_tr1.supported_tr1_functions">Supported
      TR1 Functions</a>
    </h5>
<p>
      See also the <a class="link" href="main_tr1.html" title="C99 and TR1 C Functions Overview">quick reference guide for
      these functions</a>.
    </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">tr1</span><span class="special">{</span> <span class="keyword">extern</span> <span class="string">"C"</span><span class="special">{</span>

<span class="comment">// [5.2.1.1] associated Laguerre polynomials:</span>
<span class="keyword">double</span> <span class="identifier">assoc_laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">assoc_laguerref</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">assoc_laguerrel</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.2] associated Legendre functions:</span>
<span class="keyword">double</span> <span class="identifier">assoc_legendre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">assoc_legendref</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">assoc_legendrel</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.3] beta function:</span>
<span class="keyword">double</span> <span class="identifier">beta</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">betaf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">betal</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>

<span class="comment">// [*******] (complete) elliptic integral of the first kind:</span>
<span class="keyword">double</span> <span class="identifier">comp_ellint_1</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">k</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">comp_ellint_1f</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">k</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">comp_ellint_1l</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">k</span><span class="special">);</span>

<span class="comment">// [*******] (complete) elliptic integral of the second kind:</span>
<span class="keyword">double</span> <span class="identifier">comp_ellint_2</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">k</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">comp_ellint_2f</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">k</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">comp_ellint_2l</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">k</span><span class="special">);</span>

<span class="comment">// [*******] (complete) elliptic integral of the third kind:</span>
<span class="keyword">double</span> <span class="identifier">comp_ellint_3</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">nu</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">comp_ellint_3f</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">nu</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">comp_ellint_3l</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nu</span><span class="special">);</span>

<span class="comment">// [5.2.1.8] regular modified cylindrical Bessel functions:</span>
<span class="keyword">double</span> <span class="identifier">cyl_bessel_i</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">cyl_bessel_if</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">cyl_bessel_il</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.9] cylindrical Bessel functions (of the first kind):</span>
<span class="keyword">double</span> <span class="identifier">cyl_bessel_j</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">cyl_bessel_jf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">cyl_bessel_jl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.10] irregular modified cylindrical Bessel functions:</span>
<span class="keyword">double</span> <span class="identifier">cyl_bessel_k</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">cyl_bessel_kf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">cyl_bessel_kl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.11] cylindrical Neumann functions;</span>
<span class="comment">// cylindrical Bessel functions (of the second kind):</span>
<span class="keyword">double</span> <span class="identifier">cyl_neumann</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">cyl_neumannf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">cyl_neumannl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [********] (incomplete) elliptic integral of the first kind:</span>
<span class="keyword">double</span> <span class="identifier">ellint_1</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">phi</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">ellint_1f</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">phi</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">ellint_1l</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">phi</span><span class="special">);</span>

<span class="comment">// [********] (incomplete) elliptic integral of the second kind:</span>
<span class="keyword">double</span> <span class="identifier">ellint_2</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">phi</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">ellint_2f</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">phi</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">ellint_2l</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">phi</span><span class="special">);</span>

<span class="comment">// [********] (incomplete) elliptic integral of the third kind:</span>
<span class="keyword">double</span> <span class="identifier">ellint_3</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">phi</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">ellint_3f</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">phi</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">ellint_3l</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">k</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nu</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">phi</span><span class="special">);</span>

<span class="comment">// [5.2.1.15] exponential integral:</span>
<span class="keyword">double</span> <span class="identifier">expint</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">expintf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">expintl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.16] Hermite polynomials:</span>
<span class="keyword">double</span> <span class="identifier">hermite</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">hermitef</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">hermitel</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.18] Laguerre polynomials:</span>
<span class="keyword">double</span> <span class="identifier">laguerre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">laguerref</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">laguerrel</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.19] Legendre polynomials:</span>
<span class="keyword">double</span> <span class="identifier">legendre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">legendref</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">legendrel</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.20] Riemann zeta function:</span>
<span class="keyword">double</span> <span class="identifier">riemann_zeta</span><span class="special">(</span><span class="keyword">double</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">riemann_zetaf</span><span class="special">(</span><span class="keyword">float</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">riemann_zetal</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span><span class="special">);</span>

<span class="comment">// [5.2.1.21] spherical Bessel functions (of the first kind):</span>
<span class="keyword">double</span> <span class="identifier">sph_bessel</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">sph_besself</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">sph_bessell</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.22] spherical associated Legendre functions:</span>
<span class="keyword">double</span> <span class="identifier">sph_legendre</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">theta</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">sph_legendref</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">theta</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">sph_legendrel</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">l</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="identifier">m</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">theta</span><span class="special">);</span>

<span class="comment">// [5.2.1.23] spherical Neumann functions;</span>
<span class="comment">// spherical Bessel functions (of the second kind):</span>
<span class="keyword">double</span> <span class="identifier">sph_neumann</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">sph_neumannf</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">sph_neumannl</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="identifier">n</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="special">}}}}</span> <span class="comment">// namespaces</span>
</pre>
<p>
      In addition sufficient additional overloads of the <code class="computeroutput"><span class="keyword">double</span></code>
      versions of the above functions are provided, so that calling the function
      with any mixture of <code class="computeroutput"><span class="keyword">float</span></code>, <code class="computeroutput"><span class="keyword">double</span></code>, <code class="computeroutput"><span class="keyword">long</span>
      <span class="keyword">double</span></code>, or <span class="emphasis"><em>integer</em></span>
      arguments is supported, with the return type determined by the <a class="link" href="result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
      type calculation rules</em></span></a>.
    </p>
<h5>
<a name="math_toolkit.main_tr1.h3"></a>
      <span class="phrase"><a name="math_toolkit.main_tr1.currently_unsupported_c99_functi"></a></span><a class="link" href="main_tr1.html#math_toolkit.main_tr1.currently_unsupported_c99_functi">Currently
      Unsupported C99 Functions</a>
    </h5>
<pre class="programlisting"><span class="keyword">double</span> <span class="identifier">exp2</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">exp2f</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">exp2l</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">fdim</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">fdimf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">fdiml</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">fma</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">z</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">fmaf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">z</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">fmal</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">z</span><span class="special">);</span>

<span class="keyword">int</span> <span class="identifier">ilogb</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">int</span> <span class="identifier">ilogbf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">int</span> <span class="identifier">ilogbl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">llrint</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">llrintf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">llrintl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">log2</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">log2f</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">log2l</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">logb</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">logbf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">logbl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">long</span> <span class="identifier">lrint</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="identifier">lrintf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="identifier">lrintl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">nan</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="identifier">str</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">nanf</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="identifier">str</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nanl</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="identifier">str</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">nearbyint</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">nearbyintf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">nearbyintl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">remainder</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">remainderf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">remainderl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">remquo</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">,</span> <span class="keyword">int</span> <span class="special">*</span><span class="identifier">pquo</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">remquof</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">y</span><span class="special">,</span> <span class="keyword">int</span> <span class="special">*</span><span class="identifier">pquo</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">remquol</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">y</span><span class="special">,</span> <span class="keyword">int</span> <span class="special">*</span><span class="identifier">pquo</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">rint</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">rintf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">rintl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">scalbln</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="identifier">ex</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">scalblnf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="identifier">ex</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">scalblnl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">long</span> <span class="identifier">ex</span><span class="special">);</span>

<span class="keyword">double</span> <span class="identifier">scalbn</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">int</span> <span class="identifier">ex</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">scalbnf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">int</span> <span class="identifier">ex</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">scalbnl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">int</span> <span class="identifier">ex</span><span class="special">);</span>
</pre>
<h5>
<a name="math_toolkit.main_tr1.h4"></a>
      <span class="phrase"><a name="math_toolkit.main_tr1.currently_unsupported_tr1_functi"></a></span><a class="link" href="main_tr1.html#math_toolkit.main_tr1.currently_unsupported_tr1_functi">Currently
      Unsupported TR1 Functions</a>
    </h5>
<pre class="programlisting"><span class="comment">// [5.2.1.7] confluent hypergeometric functions:</span>
<span class="keyword">double</span> <span class="identifier">conf_hyperg</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">a</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">c</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">conf_hypergf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">a</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">c</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">conf_hypergl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">a</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">c</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>

<span class="comment">// [5.2.1.17] hypergeometric functions:</span>
<span class="keyword">double</span> <span class="identifier">hyperg</span><span class="special">(</span><span class="keyword">double</span> <span class="identifier">a</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">b</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">c</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">float</span> <span class="identifier">hypergf</span><span class="special">(</span><span class="keyword">float</span> <span class="identifier">a</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">b</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">c</span><span class="special">,</span> <span class="keyword">float</span> <span class="identifier">x</span><span class="special">);</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">hypergl</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">a</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">b</span><span class="special">,</span> <span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">c</span><span class="special">,</span>
<span class="keyword">long</span> <span class="keyword">double</span> <span class="identifier">x</span><span class="special">);</span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../extern_c.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../extern_c.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="c99.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
