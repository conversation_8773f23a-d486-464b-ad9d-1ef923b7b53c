<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Tables of Error Rates for all Functions</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../logs_and_tables.html" title="Error logs and tables">
<link rel="prev" href="../logs_and_tables.html" title="Error logs and tables">
<link rel="next" href="logs.html" title="Error Logs For Error Rate Tables">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../logs_and_tables.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../logs_and_tables.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="logs.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.logs_and_tables.all_table"></a><a class="link" href="all_table.html" title="Tables of Error Rates for all Functions">Tables of Error
      Rates for all Functions</a>
</h3></div></div></div>
<div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_beta"></a><p class="title"><b>Table 23.3. Error rates for beta</b></p>
<div class="table-contents"><table class="table" summary="Error rates for beta">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> <span class="red">Max = +INFε (Mean = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_beta_GSL_2_1_Beta_Function_Small_Values">And
                  other failures.</a>)</span><br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 1.14ε (Mean = 0.574ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.86ε (Mean = 1.22ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 364ε (Mean = 76.2ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.86ε (Mean = 1.22ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.23ε (Mean = 1.14ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.978ε (Mean = 0.0595ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.18e+03ε (Mean = 238ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 1.09e+03ε (Mean = 265ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 61.4ε (Mean = 19.4ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.07e+03ε (Mean = 264ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 107ε (Mean = 24.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 96.5ε (Mean = 22.4ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Beta Function: Divergent Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 12.1ε (Mean = 1.99ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 176ε (Mean = 28ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.99ε (Mean = 2.44ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 128ε (Mean = 23.8ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.8ε (Mean = 2.71ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.4ε (Mean = 2.19ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_beta_incomplete_"></a><p class="title"><b>Table 23.4. Error rates for beta (incomplete)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for beta (incomplete)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.1ε (Mean = 2.32ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.7ε (Mean = 3.19ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.94ε (Mean = 2.17ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.568ε (Mean = 0.0254ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 69.2ε (Mean = 13.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 174ε (Mean = 25ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 90ε (Mean = 12.7ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Large and Diverse Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.0325ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.84e+04ε (Mean = 2.76e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.86e+04ε (Mean = 2.79e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 633ε (Mean = 29.7ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Integer Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.0323ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.6ε (Mean = 3.6ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 51.8ε (Mean = 11ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 26ε (Mean = 6.28ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_betac"></a><p class="title"><b>Table 23.5. Error rates for betac</b></p>
<div class="table-contents"><table class="table" summary="Error rates for betac">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.676ε (Mean = 0.0302ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.92ε (Mean = 2.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.2ε (Mean = 2.94ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.94ε (Mean = 2.06ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.949ε (Mean = 0.098ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 63.5ε (Mean = 13.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 97.6ε (Mean = 24.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 90.6ε (Mean = 14.8ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Large and Diverse Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.12ε (Mean = 0.0458ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.05e+05ε (Mean = 5.45e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.04e+05ε (Mean = 5.46e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.72e+03ε (Mean = 113ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Integer Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.586ε (Mean = 0.0314ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.1ε (Mean = 3.65ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 103ε (Mean = 17.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 26.2ε (Mean = 6.36ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_binomial_coefficient"></a><p class="title"><b>Table 23.6. Error rates for binomial_coefficient</b></p>
<div class="table-contents"><table class="table" summary="Error rates for binomial_coefficient">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Binomials: small arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.369ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.5ε (Mean = 0.339ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.5ε (Mean = 0.339ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.369ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Binomials: large arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.939ε (Mean = 0.314ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 26.6ε (Mean = 6.13ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 53.2ε (Mean = 10.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 37.2ε (Mean = 7.4ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_boost_math_powm1"></a><p class="title"><b>Table 23.7. Error rates for boost::math::powm1</b></p>
<div class="table-contents"><table class="table" summary="Error rates for boost::math::powm1">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  powm1
                </p>
              </td>
<td>
                <p>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 2.04ε (Mean = 0.493ε))<br>
                  <br> <span class="blue">Max = 2.04ε (Mean = 0.493ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.04ε (Mean = 0.493ε))
                </p>
              </td>
<td>
                <p>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 1.06ε (Mean = 0.425ε))<br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.06ε (Mean = 0.425ε))<br>
                  <br> <span class="blue">Max = 1.06ε (Mean = 0.425ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.88ε (Mean = 0.49ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.88ε (Mean = 0.49ε))
                </p>
              </td>
<td>
                <p>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.84ε (Mean = 0.486ε))<br>
                  <br> <span class="blue">Max = 1.84ε (Mean = 0.486ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cbrt"></a><p class="title"><b>Table 23.8. Error rates for cbrt</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cbrt">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  cbrt Function
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.34ε (Mean = 0.471ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.34ε (Mean = 0.471ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.34ε (Mean = 0.471ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.34ε (Mean = 0.471ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.34ε (Mean = 0.471ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.7ε (Mean = 0.565ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.7ε (Mean = 0.565ε))
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cos_pi"></a><p class="title"><b>Table 23.9. Error rates for cos_pi</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cos_pi">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  sin_pi and cos_pi
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.991ε (Mean = 0.302ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.991ε (Mean = 0.302ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.996ε (Mean = 0.284ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  sin_pi and cos_pi near integers and half integers
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.976ε (Mean = 0.28ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.976ε (Mean = 0.28ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.996ε (Mean = 0.298ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_i"></a><p class="title"><b>Table 23.10. Error rates for cyl_bessel_i</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_i">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel I0: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 270ε (Mean = 91.6ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_GSL_2_1_Bessel_I0_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 1.52ε (Mean = 0.622ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_Rmath_3_2_3_Bessel_I0_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.95ε (Mean = 0.738ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 8.49ε (Mean = 3.46ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_i__cmath__Bessel_I0_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.95ε (Mean = 0.661ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.762ε (Mean = 0.329ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I1: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 128ε (Mean = 41ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_GSL_2_1_Bessel_I1_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 1.53ε (Mean = 0.483ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_Rmath_3_2_3_Bessel_I1_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.64ε (Mean = 0.202ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 5ε (Mean = 2.15ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_i__cmath__Bessel_I1_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.64ε (Mean = 0.202ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.767ε (Mean = 0.398ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel In: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.31ε (Mean = 0.838ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_GSL_2_1_Bessel_In_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 1.73ε (Mean = 0.601ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_Rmath_3_2_3_Bessel_In_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.8ε (Mean = 1.33ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 430ε (Mean = 163ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_i__cmath__Bessel_In_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 463ε (Mean = 140ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.46ε (Mean = 1.32ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel Iv: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 5.95ε (Mean = 2.08ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_GSL_2_1_Bessel_Iv_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 3.53ε (Mean = 1.39ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.12ε (Mean = 1.85ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 616ε (Mean = 221ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_i__cmath__Bessel_Iv_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.12ε (Mean = 1.95ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.97ε (Mean = 1.24ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel In: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 261ε (Mean = 53.2ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_GSL_2_1_Bessel_In_Random_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 7.37ε (Mean = 2.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.62ε (Mean = 1.06ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 645ε (Mean = 132ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 176ε (Mean = 39.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.67ε (Mean = 1.88ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel Iv: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.661ε (Mean = 0.0441ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 6.18e+03ε (Mean = 1.55e+03ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_GSL_2_1_Bessel_Iv_Random_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  <span class="red">Max = 4.28e+08ε (Mean = 2.85e+07ε))</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.35ε (Mean = 1.62ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.05e+03ε (Mean = 224ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_i__cmath__Bessel_Iv_Random_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 283ε (Mean = 88.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.46ε (Mean = 1.71ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel Iv: Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 37ε (Mean = 18ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_GSL_2_1_Bessel_Iv_Mathworld_Data_large_values_">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  <span class="red">Max = 3.77e+168ε (Mean = 2.39e+168ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_Rmath_3_2_3_Bessel_Iv_Mathworld_Data_large_values_">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.7ε (Mean = 6.66ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 118ε (Mean = 57.2ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_i__cmath__Bessel_Iv_Mathworld_Data_large_values_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.7ε (Mean = 6.59ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.67ε (Mean = 1.64ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_i_integer_orders_"></a><p class="title"><b>Table 23.11. Error rates for cyl_bessel_i (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_i (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel I0: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.79ε (Mean = 0.482ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1.52ε (Mean = 0.622ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_integer_orders__Rmath_3_2_3_Bessel_I0_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.95ε (Mean = 0.738ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 8.49ε (Mean = 3.46ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_i_integer_orders___cmath__Bessel_I0_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.95ε (Mean = 0.661ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.762ε (Mean = 0.329ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I1: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.82ε (Mean = 0.456ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1.53ε (Mean = 0.483ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_integer_orders__Rmath_3_2_3_Bessel_I1_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.64ε (Mean = 0.202ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 5ε (Mean = 2.15ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_i_integer_orders___cmath__Bessel_I1_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.64ε (Mean = 0.202ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.767ε (Mean = 0.398ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel In: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 5.15ε (Mean = 2.13ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_integer_orders__GSL_2_1_Bessel_In_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 1.73ε (Mean = 0.601ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_i_integer_orders__Rmath_3_2_3_Bessel_In_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.8ε (Mean = 1.33ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 430ε (Mean = 163ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_i_integer_orders___cmath__Bessel_In_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 463ε (Mean = 140ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.46ε (Mean = 1.32ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_i_prime"></a><p class="title"><b>Table 23.12. Error rates for cyl_bessel_i_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_i_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel I'0: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.259ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.259ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.354ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'1: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.757ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.757ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.36ε (Mean = 0.782ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'n: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.31ε (Mean = 1.41ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 701ε (Mean = 212ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.61ε (Mean = 1.22ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'v: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.62ε (Mean = 0.512ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.89e+03ε (Mean = 914ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.89e+03ε (Mean = 914ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.76e+03ε (Mean = 1.19e+03ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'n: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.95ε (Mean = 1.06ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 195ε (Mean = 37.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.85ε (Mean = 1.82ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'v: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.1ε (Mean = 2.93ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 336ε (Mean = 68.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14ε (Mean = 2.5ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'v: Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.6ε (Mean = 20.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.6ε (Mean = 20.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 59.5ε (Mean = 26.6ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_i_prime_integer_orders_"></a><p class="title"><b>Table 23.13. Error rates for cyl_bessel_i_prime (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_i_prime (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel I'0: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.259ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.259ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.82ε (Mean = 0.354ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'1: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.757ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.757ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.36ε (Mean = 0.782ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel I'n: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.31ε (Mean = 1.41ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 701ε (Mean = 212ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.61ε (Mean = 1.22ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_j"></a><p class="title"><b>Table 23.14. Error rates for cyl_bessel_j</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_j">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel J0: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.55ε (Mean = 2.86ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 5.04ε (Mean = 1.78ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_j__cmath__Bessel_J0_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.629ε (Mean = 0.223ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_GSL_2_1_Bessel_J0_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 0.629ε (Mean = 0.223ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_Rmath_3_2_3_Bessel_J0_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.55ε (Mean = 2.86ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.52ε (Mean = 1.2ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J0: Mathworld Data (Tricky cases)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.64e+08ε (Mean = 6.69e+07ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 4.79e+08ε (Mean
                  = 1.96e+08ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8e+04ε (Mean = 3.27e+04ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 6.5e+07ε (Mean = 2.66e+07ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 1.04e+07ε (Mean = 4.29e+06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.64e+08ε (Mean = 6.69e+07ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1e+07ε (Mean = 4.09e+06ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.59ε (Mean = 1.33ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 6.1ε (Mean = 2.95ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_j__cmath__Bessel_J1_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 6.62ε (Mean = 2.35ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_GSL_2_1_Bessel_J1_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 0.946ε (Mean = 0.39ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_Rmath_3_2_3_Bessel_J1_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.44ε (Mean = 0.637ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.73ε (Mean = 0.976ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1: Mathworld Data (tricky cases)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.18e+05ε (Mean = 9.76e+04ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.15e+06ε (Mean
                  = 1.58e+06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 106ε (Mean = 47.5ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 8.75e+05ε (Mean = 5.32e+05ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 2.93e+06ε (Mean = 1.7e+06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.18e+05ε (Mean = 9.76e+04ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.23e+04ε (Mean = 1.45e+04ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel JN: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.85ε (Mean = 3.35ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 2.13e+19ε (Mean
                  = 5.16e+18ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_j__cmath__Bessel_JN_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 6.9e+05ε (Mean = 2.15e+05ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_GSL_2_1_Bessel_JN_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  <span class="red">Max = +INFε (Mean = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_Rmath_3_2_3_Bessel_JN_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 463ε (Mean = 112ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.7ε (Mean = 5.4ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.7ε (Mean = 4.11ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 3.49e+05ε (Mean = 8.09e+04ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_j__cmath__Bessel_J_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10ε (Mean = 2.24ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.39e+05ε (Mean = 5.37e+04ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_GSL_2_1_Bessel_J_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  <span class="red">Max = +INFε (Mean = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_Rmath_3_2_3_Bessel_J_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.7ε (Mean = 4.22ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.9ε (Mean = 3.89ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J: Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 607ε (Mean = 305ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 34.9ε (Mean = 17.4ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_j__cmath__Bessel_J_Mathworld_Data_large_values_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.536ε (Mean = 0.268ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 4.91e+03ε (Mean = 2.46e+03ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_GSL_2_1_Bessel_J_Mathworld_Data_large_values_">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 5.9ε (Mean = 3.76ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 607ε (Mean = 305ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.31ε (Mean = 5.52ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel JN: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 50.8ε (Mean = 3.69ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.12e+03ε (Mean = 88.7ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 75.7ε (Mean = 5.36ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 3.93ε (Mean = 1.22ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 99.6ε (Mean = 22ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 17.5ε (Mean = 1.46ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.4ε (Mean = 1.68ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 501ε (Mean = 52.3ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 15.5ε (Mean = 3.33ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_GSL_2_1_Bessel_J_Random_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 6.74ε (Mean = 1.3ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 260ε (Mean = 34ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.24ε (Mean = 1.17ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J: Random Data (Tricky large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 785ε (Mean = 94.2ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 5.01e+17ε (Mean
                  = 6.23e+16ε))</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.48e+05ε (Mean = 5.11e+04ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 71.6ε (Mean = 11.7ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 785ε (Mean = 97.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 59.2ε (Mean = 8.67ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_j_integer_orders_"></a><p class="title"><b>Table 23.15. Error rates for cyl_bessel_j (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_j (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel J0: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.55ε (Mean = 2.86ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 5.04ε (Mean = 1.78ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_j_integer_orders___cmath__Bessel_J0_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.12ε (Mean = 0.488ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 0.629ε (Mean = 0.223ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_integer_orders__Rmath_3_2_3_Bessel_J0_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.55ε (Mean = 2.86ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.52ε (Mean = 1.2ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.89ε (Mean = 0.988ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J0: Mathworld Data (Tricky cases) (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.64e+08ε (Mean = 6.69e+07ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 4.79e+08ε (Mean
                  = 1.96e+08ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8e+04ε (Mean = 3.27e+04ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1e+07ε (Mean = 4.11e+06ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 1.04e+07ε (Mean = 4.29e+06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.64e+08ε (Mean = 6.69e+07ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1e+07ε (Mean = 4.09e+06ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> <span class="red">Max
                  = 2.54e+08ε (Mean = 1.04e+08ε))</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.59ε (Mean = 1.33ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 6.1ε (Mean = 2.95ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_j_integer_orders___cmath__Bessel_J1_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.89ε (Mean = 0.721ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 0.946ε (Mean = 0.39ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_integer_orders__Rmath_3_2_3_Bessel_J1_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.44ε (Mean = 0.637ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.73ε (Mean = 0.976ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 11.4ε (Mean = 4.15ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1: Mathworld Data (tricky cases) (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.18e+05ε (Mean = 9.76e+04ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.15e+06ε (Mean
                  = 1.58e+06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 106ε (Mean = 47.5ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.26e+06ε (Mean = 6.28e+05ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 2.93e+06ε (Mean = 1.7e+06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.18e+05ε (Mean = 9.76e+04ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.23e+04ε (Mean = 1.45e+04ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.44e+07ε (Mean
                  = 6.5e+06ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel JN: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.85ε (Mean = 3.35ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 2.13e+19ε (Mean
                  = 5.16e+18ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_j_integer_orders___cmath__Bessel_JN_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 6.9e+05ε (Mean = 2.53e+05ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_integer_orders__GSL_2_1_Bessel_JN_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  <span class="red">Max = +INFε (Mean = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_j_integer_orders__Rmath_3_2_3_Bessel_JN_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 463ε (Mean = 112ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.7ε (Mean = 5.4ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> <span class="red">Max =
                  +INFε (Mean = +INFε) <a class="link" href="logs.html#errors_Microsoft_Visual_C_version_14_1_Win32_double_cyl_bessel_j_integer_orders___math_h__Bessel_JN_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_j_prime"></a><p class="title"><b>Table 23.16. Error rates for cyl_bessel_j_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_j_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel J0': Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.9ε (Mean = 6.82ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.9ε (Mean = 6.72ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.62ε (Mean = 2.55ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J0': Mathworld Data (Tricky cases)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.44ε (Mean = 3.34ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.44ε (Mean = 3.31ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.67ε (Mean = 1.74ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1': Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.9ε (Mean = 3.37ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.9ε (Mean = 3.37ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.627ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1': Mathworld Data (tricky cases)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 287ε (Mean = 129ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.88e+05ε (Mean = 2.63e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.88e+05ε (Mean = 2.63e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 288ε (Mean = 129ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel JN': Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.527ε (Mean = 0.128ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.29e+03ε (Mean = 312ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.29e+03ε (Mean = 355ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14ε (Mean = 6.13ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J': Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 21.5ε (Mean = 4.7ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.5ε (Mean = 9.31ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.5ε (Mean = 9.32ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 23.7ε (Mean = 8ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J': Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 989ε (Mean = 495ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 989ε (Mean = 495ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.9ε (Mean = 1.61ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel JN': Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.593ε (Mean = 0.0396ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.3ε (Mean = 1.85ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 79.4ε (Mean = 16.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.34ε (Mean = 0.999ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J': Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.885ε (Mean = 0.033ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 139ε (Mean = 6.47ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 279ε (Mean = 27.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 176ε (Mean = 9.75ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J': Random Data (Tricky large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 474ε (Mean = 62.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 474ε (Mean = 64.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 379ε (Mean = 45.4ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_j_prime_integer_orders_"></a><p class="title"><b>Table 23.17. Error rates for cyl_bessel_j_prime (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_j_prime (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel J0': Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.9ε (Mean = 6.82ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.9ε (Mean = 6.72ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.62ε (Mean = 2.55ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J0': Mathworld Data (Tricky cases) (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.44ε (Mean = 3.34ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.44ε (Mean = 3.31ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.67ε (Mean = 1.74ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1': Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.9ε (Mean = 3.37ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.9ε (Mean = 3.37ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.627ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel J1': Mathworld Data (tricky cases) (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 287ε (Mean = 129ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.88e+05ε (Mean = 2.63e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.88e+05ε (Mean = 2.63e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 288ε (Mean = 129ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel JN': Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.527ε (Mean = 0.128ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.29e+03ε (Mean = 312ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.29e+03ε (Mean = 355ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14ε (Mean = 6.13ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_k"></a><p class="title"><b>Table 23.18. Error rates for cyl_bessel_k</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_k">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel K0: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.833ε (Mean = 0.436ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 9.33ε (Mean = 3.25ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 6.04ε (Mean = 2.16ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 0.833ε (Mean = 0.601ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.833ε (Mean = 0.436ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.833ε (Mean = 0.552ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K1: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 8.94ε (Mean = 3.19ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 6.26ε (Mean = 2.21ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 0.894ε (Mean = 0.516ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.39ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel Kn: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.6ε (Mean = 1.21ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 12.9ε (Mean = 4.91ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_k__cmath__Bessel_Kn_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 3.36ε (Mean = 1.43ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_k_GSL_2_1_Bessel_Kn_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 8.48ε (Mean = 2.98ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.6ε (Mean = 1.21ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.63ε (Mean = 1.46ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel Kv: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.58ε (Mean = 2.39ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 13ε (Mean = 4.81ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_k__cmath__Bessel_Kv_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 5.47ε (Mean = 2.04ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_k_GSL_2_1_Bessel_Kv_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 3.15ε (Mean = 1.35ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.21ε (Mean = 2.53ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.78ε (Mean = 2.19ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel Kv: Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.3ε (Mean = 21ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 42.3ε (Mean = 19.8ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_k__cmath__Bessel_Kv_Mathworld_Data_large_values_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 308ε (Mean = 142ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_k_GSL_2_1_Bessel_Kv_Mathworld_Data_large_values_">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 84.6ε (Mean = 37.8ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.3ε (Mean = 21ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 59.8ε (Mean = 26.9ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel Kn: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.55ε (Mean = 1.12ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 13.9ε (Mean = 2.91ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.764ε (Mean = 0.0348ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 8.71ε (Mean = 1.76ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_k_GSL_2_1_Bessel_Kn_Random_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 7.47ε (Mean = 1.34ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.55ε (Mean = 1.12ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.34ε (Mean = 1.7ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel Kv: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.88ε (Mean = 1.48ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 13.6ε (Mean = 2.68ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_k__cmath__Bessel_Kv_Random_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.507ε (Mean = 0.0313ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 9.71ε (Mean = 1.47ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_bessel_k_GSL_2_1_Bessel_Kv_Random_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 7.37ε (Mean = 1.49ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.88ε (Mean = 1.47ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.33ε (Mean = 1.62ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_k_integer_orders_"></a><p class="title"><b>Table 23.19. Error rates for cyl_bessel_k (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_k (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel K0: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.833ε (Mean = 0.436ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 9.33ε (Mean = 3.25ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.2ε (Mean = 0.733ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 0.833ε (Mean = 0.601ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.833ε (Mean = 0.436ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.833ε (Mean = 0.552ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K1: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 8.94ε (Mean = 3.19ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.626ε (Mean = 0.333ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 0.894ε (Mean = 0.516ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.39ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel Kn: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.6ε (Mean = 1.21ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 12.9ε (Mean = 4.91ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_bessel_k_integer_orders___cmath__Bessel_Kn_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 168ε (Mean = 59.5ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 8.48ε (Mean = 2.98ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.6ε (Mean = 1.21ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.63ε (Mean = 1.46ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_k_prime"></a><p class="title"><b>Table 23.20. Error rates for cyl_bessel_k_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_k_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel K'0: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.39ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'1: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.736ε (Mean = 0.389ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.736ε (Mean = 0.389ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.761ε (Mean = 0.444ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'n: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 1.08ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 1.08ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.17ε (Mean = 1.75ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'v: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.94ε (Mean = 2.44ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.94ε (Mean = 2.34ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.94ε (Mean = 1.47ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'v: Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 59.2ε (Mean = 42.9ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 58.7ε (Mean = 42.6ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.6ε (Mean = 11.8ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'n: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.45ε (Mean = 1.19ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.45ε (Mean = 1.19ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.67ε (Mean = 1.73ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'v: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.95ε (Mean = 1.53ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.95ε (Mean = 1.52ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.32ε (Mean = 1.65ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_bessel_k_prime_integer_orders_"></a><p class="title"><b>Table 23.21. Error rates for cyl_bessel_k_prime (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_bessel_k_prime (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Bessel K'0: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.329ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.39ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'1: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.736ε (Mean = 0.389ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.736ε (Mean = 0.389ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.761ε (Mean = 0.444ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Bessel K'n: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 1.08ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 1.08ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.17ε (Mean = 1.75ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_neumann"></a><p class="title"><b>Table 23.22. Error rates for cyl_neumann</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_neumann">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Y0: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.53ε (Mean = 2.4ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.05e+05ε (Mean = 6.87e+04ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 60.9ε (Mean = 20.4ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 167ε (Mean = 56.5ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.53ε (Mean = 2.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.61ε (Mean = 2.29ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y1: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.33ε (Mean = 2.25ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 9.71e+03ε (Mean = 4.08e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 23.4ε (Mean = 8.1ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 193ε (Mean = 64.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.33ε (Mean = 2.29ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.75ε (Mean = 1.72ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Yn: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 55.2ε (Mean = 17.8ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 2.2e+20ε (Mean
                  = 6.97e+19ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_neumann__cmath__Yn_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.993ε (Mean = 0.314ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.41e+05ε (Mean = 7.62e+04ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_neumann_GSL_2_1_Yn_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 1.24e+04ε (Mean = 4e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 55.2ε (Mean = 17.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 35ε (Mean = 11.9ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Yv: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.7ε (Mean = 4.93ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 3.49e+15ε (Mean
                  = 1.05e+15ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_neumann__cmath__Yv_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10ε (Mean = 3.02ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.07e+05ε (Mean = 3.22e+04ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_neumann_GSL_2_1_Yv_Mathworld_Data">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 243ε (Mean = 73.9ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.7ε (Mean = 5.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.89ε (Mean = 3.27ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Yv: Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.7ε (Mean = 1.33ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 43.2ε (Mean = 16.3ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_neumann__cmath__Yv_Mathworld_Data_large_values_">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 60.8ε (Mean = 23ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_cyl_neumann_GSL_2_1_Yv_Mathworld_Data_large_values_">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 0.682ε (Mean = 0.335ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.7ε (Mean = 1.33ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.682ε (Mean = 0.423ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y0 and Y1: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.8ε (Mean = 3.04ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.59e+03ε (Mean = 500ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 34.4ε (Mean = 8.9ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 83ε (Mean = 14.2ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.8ε (Mean = 3.04ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.17ε (Mean = 1.24ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Yn: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 338ε (Mean = 27.5ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 4.01e+03ε (Mean = 348ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 500ε (Mean = 47.8ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 691ε (Mean = 67.9ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 338ε (Mean = 27.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 117ε (Mean = 10.2ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Yv: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.08e+03ε (Mean = 149ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max
                  = +INFε (Mean = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_neumann__cmath__Yv_Random_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.53ε (Mean = 0.102ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.41e+06ε (Mean = 7.67e+04ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 1.79e+05ε (Mean = 9.64e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.08e+03ε (Mean = 149ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.23e+03ε (Mean = 69.9ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_neumann_integer_orders_"></a><p class="title"><b>Table 23.23. Error rates for cyl_neumann (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_neumann (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Y0: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.53ε (Mean = 2.4ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.05e+05ε (Mean = 6.87e+04ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 6.46ε (Mean = 2.38ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 167ε (Mean = 56.5ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.53ε (Mean = 2.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.61ε (Mean = 2.29ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 5.37e+03ε (Mean = 1.81e+03ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y1: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.33ε (Mean = 2.25ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 9.71e+03ε (Mean = 4.08e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.51ε (Mean = 0.839ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 193ε (Mean = 64.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.33ε (Mean = 2.29ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.75ε (Mean = 1.72ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.86e+04ε (Mean = 6.2e+03ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Yn: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 55.2ε (Mean = 17.8ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 2.2e+20ε (Mean
                  = 6.97e+19ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_cyl_neumann_integer_orders___cmath__Yn_Mathworld_Data_Integer_Version_">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.993ε (Mean = 0.314ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.41e+05ε (Mean = 7.62e+04ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 1.24e+04ε (Mean = 4e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 55.2ε (Mean = 17.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 35ε (Mean = 11.9ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 2.49e+05ε (Mean = 8.14e+04ε))
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_neumann_prime"></a><p class="title"><b>Table 23.24. Error rates for cyl_neumann_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_neumann_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Y'0: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.33ε (Mean = 3.12ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.33ε (Mean = 3.14ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.75ε (Mean = 1.75ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y'1: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.58ε (Mean = 0.193ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 37.1ε (Mean = 12.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 34ε (Mean = 11.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.08ε (Mean = 1.2ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y'n: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.05ε (Mean = 0.677ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 56ε (Mean = 18.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 56ε (Mean = 21.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 563ε (Mean = 178ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y'v: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 21.5ε (Mean = 6.49ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.5ε (Mean = 13.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 42.5ε (Mean = 13.6ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 23.7ε (Mean = 10.1ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y'v: Mathworld Data (large values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.57ε (Mean = 1.24ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.57ε (Mean = 1.24ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.627ε (Mean = 0.237ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y'0 and Y'1: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 23.8ε (Mean = 3.69ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 23.8ε (Mean = 3.69ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.95ε (Mean = 1.36ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y'n: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.53ε (Mean = 0.0885ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.35e+03ε (Mean = 136ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.35e+03ε (Mean = 136ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 621ε (Mean = 36ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y'v: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 56.8ε (Mean = 2.59ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.16e+05ε (Mean = 5.28e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.16e+05ε (Mean = 5.28e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.23e+04ε (Mean = 1.13e+03ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_cyl_neumann_prime_integer_orders_"></a><p class="title"><b>Table 23.25. Error rates for cyl_neumann_prime (integer orders)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for cyl_neumann_prime (integer orders)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Y'0: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.33ε (Mean = 3.12ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.33ε (Mean = 3.14ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.75ε (Mean = 1.75ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y'1: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.58ε (Mean = 0.193ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 37.1ε (Mean = 12.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 34ε (Mean = 11.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.08ε (Mean = 1.2ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Y'n: Mathworld Data (Integer Version)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.05ε (Mean = 0.677ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 56ε (Mean = 18.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 56ε (Mean = 21.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 563ε (Mean = 178ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_digamma"></a><p class="title"><b>Table 23.26. Error rates for digamma</b></p>
<div class="table-contents"><table class="table" summary="Error rates for digamma">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Digamma Function: Large Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.84ε (Mean = 0.71ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1.18ε (Mean = 0.331ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.39ε (Mean = 0.413ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.39ε (Mean = 0.413ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.98ε (Mean = 0.369ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Digamma Function: Near the Positive Root
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.891ε (Mean = 0.0995ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 135ε (Mean = 11.9ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 2.02e+03ε (Mean = 256ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.37ε (Mean = 0.477ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.31ε (Mean = 0.471ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.997ε (Mean = 0.527ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Digamma Function: Near Zero
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.953ε (Mean = 0.348ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1.17ε (Mean = 0.564ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.984ε (Mean = 0.361ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.984ε (Mean = 0.361ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.953ε (Mean = 0.337ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Digamma Function: Negative Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 4.56e+04ε (Mean = 3.91e+03ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 4.6e+04ε (Mean = 3.94e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 180ε (Mean = 13ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 180ε (Mean = 13ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 214ε (Mean = 16.1ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Digamma Function: Values near 0
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.866ε (Mean = 0.387ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 3.58e+05ε (Mean = 1.6e+05ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.592ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.592ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Digamma Function: Integer arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.992ε (Mean = 0.215ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.18ε (Mean = 0.607ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 4.33ε (Mean = 0.982ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.888ε (Mean = 0.403ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.888ε (Mean = 0.403ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.992ε (Mean = 0.452ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Digamma Function: Half integer arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.09ε (Mean = 0.531ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 46.2ε (Mean = 7.24ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.906ε (Mean = 0.409ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.906ε (Mean = 0.409ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.78ε (Mean = 0.314ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_1"></a><p class="title"><b>Table 23.27. Error rates for ellint_1</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_1">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral F: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.94ε (Mean = 0.509ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = +INFε (Mean
                  = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_ellint_1__cmath__Elliptic_Integral_F_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.919ε (Mean = 0.544ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.94ε (Mean = 0.509ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.919ε (Mean = 0.542ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral F: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.57ε (Mean = 0.56ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.56ε (Mean = 0.816ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.99ε (Mean = 0.797ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.57ε (Mean = 0.561ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.26ε (Mean = 0.631ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_1_complete_"></a><p class="title"><b>Table 23.28. Error rates for ellint_1 (complete)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_1 (complete)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral K: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.887ε (Mean = 0.296ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.19ε (Mean = 0.765ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.623ε (Mean = 0.393ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.887ε (Mean = 0.296ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.915ε (Mean = 0.547ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral K: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.27ε (Mean = 0.473ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.19ε (Mean = 0.694ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.851ε (Mean = 0.0851ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.32ε (Mean = 0.688ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.27ε (Mean = 0.473ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.958ε (Mean = 0.408ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_2"></a><p class="title"><b>Table 23.29. Error rates for ellint_2</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_2">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral E: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.63ε (Mean = 0.325ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.656ε (Mean = 0.317ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = +INFε (Mean
                  = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_ellint_2__cmath__Elliptic_Integral_E_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.656ε (Mean = 0.317ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.31ε (Mean = 0.727ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral E: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 4.4ε (Mean = 1.16ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.05ε (Mean = 0.632ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 3.08e+04ε (Mean = 3.84e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.05ε (Mean = 0.632ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.23ε (Mean = 0.639ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral E: Small Angles
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.5ε (Mean = 0.118ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.283ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2ε (Mean = 0.333ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.283ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.421ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_2_complete_"></a><p class="title"><b>Table 23.30. Error rates for ellint_2 (complete)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_2 (complete)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral E: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 3.09ε (Mean = 1.04ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_ellint_2_complete__GSL_2_1_Elliptic_Integral_E_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.836ε (Mean = 0.469ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 170ε (Mean = 55.1ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.836ε (Mean = 0.469ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.3ε (Mean = 0.615ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral E: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 4.34ε (Mean = 1.18ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.629ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.49e+04ε (Mean = 3.39e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.629ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.71ε (Mean = 0.553ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_3"></a><p class="title"><b>Table 23.31. Error rates for ellint_3</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_3">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral PI: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 475ε (Mean = 86.3ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = +INFε (Mean
                  = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_ellint_3__cmath__Elliptic_Integral_PI_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.48e+05ε (Mean = 2.54e+04ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_ellint_3_GSL_2_1_Elliptic_Integral_PI_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 475ε (Mean = 86.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 565ε (Mean = 102ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral PI: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.54ε (Mean = 0.895ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 3.37e+20ε (Mean
                  = 3.47e+19ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_ellint_3__cmath__Elliptic_Integral_PI_Random_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 633ε (Mean = 50.1ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.49ε (Mean = 0.885ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.33ε (Mean = 0.971ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral PI: Large Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.7ε (Mean = 0.893ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 2.52e+18ε (Mean
                  = 4.83e+17ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_ellint_3__cmath__Elliptic_Integral_PI_Large_Random_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.557ε (Mean = 0.0389ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 40.1ε (Mean = 7.77ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.7ε (Mean = 0.892ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.86ε (Mean = 0.944ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_3_complete_"></a><p class="title"><b>Table 23.32. Error rates for ellint_3 (complete)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_3 (complete)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Complete Elliptic Integral PI: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.4ε (Mean = 0.575ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 6.31e+20ε (Mean
                  = 1.53e+20ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_ellint_3_complete___cmath__Complete_Elliptic_Integral_PI_Mathworld_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 6.33e+04ε (Mean = 1.54e+04ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_ellint_3_complete__GSL_2_1_Complete_Elliptic_Integral_PI_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.4ε (Mean = 0.575ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.971ε (Mean = 0.464ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Complete Elliptic Integral PI: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.45ε (Mean = 0.696ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> <span class="red">Max = 8.78e+20ε (Mean
                  = 1.02e+20ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_ellint_3_complete___cmath__Complete_Elliptic_Integral_PI_Random_Data">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 24ε (Mean = 2.99ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.4ε (Mean = 0.677ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.46ε (Mean = 0.657ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_d"></a><p class="title"><b>Table 23.33. Error rates for ellint_d</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_d">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral E: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.862ε (Mean = 0.568ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.3ε (Mean = 0.813ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.3ε (Mean = 0.813ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.862ε (Mean = 0.457ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral D: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 3.01ε (Mean = 0.928ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.51ε (Mean = 0.883ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.51ε (Mean = 0.883ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.87ε (Mean = 0.805ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_d_complete_"></a><p class="title"><b>Table 23.34. Error rates for ellint_d (complete)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_d (complete)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral E: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.637ε (Mean = 0.368ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.27ε (Mean = 0.735ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.27ε (Mean = 0.735ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.637ε (Mean = 0.368ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral D: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.27ε (Mean = 0.334ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.27ε (Mean = 0.334ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.27ε (Mean = 0.355ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_rc"></a><p class="title"><b>Table 23.35. Error rates for ellint_rc</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rc">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  RC: Random data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.4ε (Mean = 0.624ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.995ε (Mean = 0.433ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.962ε (Mean = 0.407ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_rd"></a><p class="title"><b>Table 23.36. Error rates for ellint_rd</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rd">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  RD: Random data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.59ε (Mean = 0.878ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.73ε (Mean = 0.831ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.16ε (Mean = 0.803ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.896ε (Mean = 0.022ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.88ε (Mean = 0.839ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.65ε (Mean = 0.82ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 16.5ε (Mean = 0.843ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: x = y
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.824ε (Mean = 0.0272ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 3.74ε (Mean = 0.84ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.85ε (Mean = 0.865ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.51ε (Mean = 0.816ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: x = 0, y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2ε (Mean = 0.656ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.19ε (Mean = 0.522ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.16ε (Mean = 0.497ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: x = y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.03ε (Mean = 0.418ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.387ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.03ε (Mean = 0.418ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RD: x = 0
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.85ε (Mean = 0.781ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.79ε (Mean = 0.883ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.64ε (Mean = 0.894ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_rf"></a><p class="title"><b>Table 23.37. Error rates for ellint_rf</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rf">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  RF: Random data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.73ε (Mean = 0.804ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.54ε (Mean = 0.674ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.02ε (Mean = 0.677ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RF: x = y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.999ε (Mean = 0.34ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.991ε (Mean = 0.345ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.34ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RF: x = y or y = z or x = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.536ε (Mean = 0.00658ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.89ε (Mean = 0.749ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.95ε (Mean = 0.418ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.21ε (Mean = 0.394ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RF: x = 0, y = z
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.29ε (Mean = 0.527ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.894ε (Mean = 0.338ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.407ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RF: z = 0
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.54ε (Mean = 0.781ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.7ε (Mean = 0.539ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.89ε (Mean = 0.587ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_rg"></a><p class="title"><b>Table 23.38. Error rates for ellint_rg</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rg">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  RG: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.983ε (Mean = 0.0172ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 0.983ε (Mean = 0.0172ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.95ε (Mean = 0.951ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.65ε (Mean = 0.929ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RG: two values 0
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RG: All values the same or zero
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.992ε (Mean = 0.288ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.06ε (Mean = 0.348ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RG: two values the same
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.594ε (Mean = 0.0103ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 0.594ε (Mean = 0.0103ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.51ε (Mean = 0.404ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.96ε (Mean = 0.374ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RG: one value zero
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.14ε (Mean = 0.722ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.96ε (Mean = 0.674ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ellint_rj"></a><p class="title"><b>Table 23.39. Error rates for ellint_rj</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ellint_rj">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  RJ: Random data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.52ε (Mean = 0.0184ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 3.57ε (Mean = 0.704ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_ellint_rj_GSL_2_1_RJ_Random_data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 186ε (Mean = 6.67ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 215ε (Mean = 7.66ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RJ: 4 Equal Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.03ε (Mean = 0.418ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.387ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.03ε (Mean = 0.418ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RJ: 3 Equal Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 3.96ε (Mean = 1.06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 20.8ε (Mean = 0.986ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 39.9ε (Mean = 1.17ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RJ: 2 Equal Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.6ε (Mean = 0.0228ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.57ε (Mean = 0.754ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 220ε (Mean = 6.64ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 214ε (Mean = 5.28ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  RJ: Equal z and p
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.742ε (Mean = 0.0166ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.62ε (Mean = 0.699ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 17.2ε (Mean = 1.16ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 16.1ε (Mean = 1.14ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_erf"></a><p class="title"><b>Table 23.40. Error rates for erf</b></p>
<div class="table-contents"><table class="table" summary="Error rates for erf">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Erf Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.925ε (Mean = 0.193ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.944ε (Mean = 0.191ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.944ε (Mean = 0.191ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.841ε (Mean = 0.0687ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.06ε (Mean = 0.319ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.925ε (Mean = 0.193ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.944ε (Mean = 0.194ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.996ε (Mean = 0.182ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.57ε (Mean = 0.317ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Erf Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.5ε (Mean = 0.193ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.921ε (Mean = 0.0723ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.921ε (Mean = 0.0723ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.119ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.31ε (Mean = 0.368ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.5ε (Mean = 0.197ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.921ε (Mean = 0.071ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.171ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.19ε (Mean = 0.244ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Erf Function: Large Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span>
                  Max = 0ε (Mean = 0ε))<br> (<span class="emphasis"><em>&lt;math.h&gt;:</em></span>
                  Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>&lt;math.h&gt;:</em></span>
                  Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>&lt;math.h&gt;:</em></span>
                  Max = 0ε (Mean = 0ε))
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_erf_inv"></a><p class="title"><b>Table 23.41. Error rates for erf_inv</b></p>
<div class="table-contents"><table class="table" summary="Error rates for erf_inv">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Inverse Erf Function
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.996ε (Mean = 0.389ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.08ε (Mean = 0.395ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.09ε (Mean = 0.502ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_erfc"></a><p class="title"><b>Table 23.42. Error rates for erfc</b></p>
<div class="table-contents"><table class="table" summary="Error rates for erfc">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Erf Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span>
                  Max = 0ε (Mean = 0ε))<br> (<span class="emphasis"><em>&lt;math.h&gt;:</em></span>
                  Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.658ε (Mean = 0.0537ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.01ε (Mean = 0.485ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>&lt;math.h&gt;:</em></span>
                  Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>&lt;math.h&gt;:</em></span>
                  Max = 0ε (Mean = 0ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Erf Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.76ε (Mean = 0.365ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.35ε (Mean = 0.307ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.35ε (Mean = 0.307ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.983ε (Mean = 0.213ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.64ε (Mean = 0.662ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.76ε (Mean = 0.38ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 2.81ε (Mean = 0.739ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.65ε (Mean = 0.373ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 2.36ε (Mean = 0.539ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Erf Function: Large Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.57ε (Mean = 0.542ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.26ε (Mean = 0.441ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.26ε (Mean = 0.441ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.868ε (Mean = 0.147ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 3.9ε (Mean = 0.472ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.57ε (Mean = 0.564ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 4.91ε (Mean = 1.54ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.14ε (Mean = 0.248ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.84ε (Mean = 0.331ε))
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_erfc_inv"></a><p class="title"><b>Table 23.43. Error rates for erfc_inv</b></p>
<div class="table-contents"><table class="table" summary="Error rates for erfc_inv">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Inverse Erfc Function
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.996ε (Mean = 0.397ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.08ε (Mean = 0.403ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.491ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Inverse Erfc Function: extreme values
                </p>
              </td>
<td>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.62ε (Mean = 0.383ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.62ε (Mean = 0.383ε)</span>
                </p>
              </td>
<td>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_expint_Ei_"></a><p class="title"><b>Table 23.44. Error rates for expint (Ei)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for expint (Ei)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Exponential Integral Ei
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.05ε (Mean = 0.821ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 14.1ε (Mean = 2.43ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_expint_Ei___cmath__Exponential_Integral_Ei">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.994ε (Mean = 0.142ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 8.96ε (Mean = 0.703ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.05ε (Mean = 0.835ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.43ε (Mean = 0.54ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Exponential Integral Ei: double exponent range
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.72ε (Mean = 0.593ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 3.11ε (Mean = 1.13ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.156ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.5ε (Mean = 0.612ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.72ε (Mean = 0.607ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.7ε (Mean = 0.66ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Exponential Integral Ei: long exponent range
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.98ε (Mean = 0.595ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.93ε (Mean = 0.855ε))
                </p>
              </td>
<td>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.98ε (Mean = 0.575ε)</span>
                </p>
              </td>
<td>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_expint_En_"></a><p class="title"><b>Table 23.45. Error rates for expint (En)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for expint (En)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Exponential Integral En
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.589ε (Mean = 0.0331ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 58.5ε (Mean = 17.1ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.97ε (Mean = 2.13ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.97ε (Mean = 2.13ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.16ε (Mean = 1.85ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Exponential Integral En: small z values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 115ε (Mean = 23.6ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.99ε (Mean = 0.559ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.99ε (Mean = 0.559ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.62ε (Mean = 0.531ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Exponential Integral E1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.556ε (Mean = 0.0625ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 0.988ε (Mean = 0.469ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.965ε (Mean = 0.414ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.965ε (Mean = 0.408ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.988ε (Mean = 0.486ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_expm1"></a><p class="title"><b>Table 23.46. Error rates for expm1</b></p>
<div class="table-contents"><table class="table" summary="Error rates for expm1">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Random test data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.992ε (Mean = 0.402ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.992ε (Mean = 0.402ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.992ε (Mean = 0.402ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.793ε (Mean = 0.126ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 0.793ε (Mean = 0.126ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.31ε (Mean = 0.428ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.996ε (Mean = 0.426ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.31ε (Mean = 0.496ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.31ε (Mean = 0.496ε))
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_gamma_p"></a><p class="title"><b>Table 23.47. Error rates for gamma_p</b></p>
<div class="table-contents"><table class="table" summary="Error rates for gamma_p">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  tgamma(a, z) medium values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.955ε (Mean = 0.05ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 342ε (Mean = 45.8ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 389ε (Mean = 44ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 41.6ε (Mean = 8.09ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 239ε (Mean = 30.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 35.1ε (Mean = 6.98ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) small values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 4.82ε (Mean = 0.758ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1.01ε (Mean = 0.306ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2ε (Mean = 0.464ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2ε (Mean = 0.461ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.54ε (Mean = 0.439ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) large values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.02e+03ε (Mean = 105ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1.11e+03ε (Mean = 67.5ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.08e+04ε (Mean = 1.86e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.02e+04ε (Mean = 1.91e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 243ε (Mean = 20.2ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) integer and half integer values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 128ε (Mean = 22.6ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 66.2ε (Mean = 12.2ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.8ε (Mean = 2.66ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 71.6ε (Mean = 9.47ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 13ε (Mean = 2.97ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_gamma_p_inv"></a><p class="title"><b>Table 23.48. Error rates for gamma_p_inv</b></p>
<div class="table-contents"><table class="table" summary="Error rates for gamma_p_inv">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  incomplete gamma inverse(a, z) medium values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.993ε (Mean = 0.15ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 4.88ε (Mean = 0.868ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.8ε (Mean = 0.406ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.89ε (Mean = 0.466ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.71ε (Mean = 0.34ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  incomplete gamma inverse(a, z) large values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 0.816ε (Mean = 0.0874ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.509ε (Mean = 0.0447ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.509ε (Mean = 0.0447ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.924ε (Mean = 0.108ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  incomplete gamma inverse(a, z) small values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 441ε (Mean = 53.9ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 547ε (Mean = 61.6ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.17e+03ε (Mean = 1.45e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.09e+04ε (Mean = 1.3e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.1e+03ε (Mean = 131ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_gamma_p_inva"></a><p class="title"><b>Table 23.49. Error rates for gamma_p_inva</b></p>
<div class="table-contents"><table class="table" summary="Error rates for gamma_p_inva">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Incomplete gamma inverses.
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.87ε (Mean = 1.15ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.08ε (Mean = 1.12ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.92ε (Mean = 1.03ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_gamma_q"></a><p class="title"><b>Table 23.50. Error rates for gamma_q</b></p>
<div class="table-contents"><table class="table" summary="Error rates for gamma_q">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  tgamma(a, z) medium values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.927ε (Mean = 0.035ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 201ε (Mean = 13.5ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 131ε (Mean = 12.7ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 32.3ε (Mean = 6.61ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 199ε (Mean = 26.6ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 23.7ε (Mean = 4ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) small values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> <span class="red">Max = 1.38e+10ε (Mean = 1.05e+09ε))</span><br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 65.6ε (Mean = 11ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.45ε (Mean = 0.885ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.45ε (Mean = 0.819ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.26ε (Mean = 0.74ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) large values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.71e+04ε (Mean = 2.16e+03ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1.02e+03ε (Mean = 62.7ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.82e+03ε (Mean = 414ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.15e+04ε (Mean = 733ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 469ε (Mean = 31.5ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) integer and half integer values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 118ε (Mean = 12.5ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 138ε (Mean = 16.9ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.1ε (Mean = 2.07ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 54.7ε (Mean = 6.16ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.72ε (Mean = 1.48ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_gamma_q_inv"></a><p class="title"><b>Table 23.51. Error rates for gamma_q_inv</b></p>
<div class="table-contents"><table class="table" summary="Error rates for gamma_q_inv">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  incomplete gamma inverse(a, z) medium values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.912ε (Mean = 0.154ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 4.66ε (Mean = 0.792ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.2ε (Mean = 0.627ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.2ε (Mean = 0.683ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.88ε (Mean = 0.469ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  incomplete gamma inverse(a, z) large values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.894ε (Mean = 0.0915ε)</span><br>
                  <br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 0.894ε (Mean = 0.106ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.814ε (Mean = 0.0856ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  incomplete gamma inverse(a, z) small values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 292ε (Mean = 36.4ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 415ε (Mean = 48.7ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.28e+03ε (Mean = 1.09e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.98e+03ε (Mean = 877ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 451ε (Mean = 64.7ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_gamma_q_inva"></a><p class="title"><b>Table 23.52. Error rates for gamma_q_inva</b></p>
<div class="table-contents"><table class="table" summary="Error rates for gamma_q_inva">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Incomplete gamma inverses.
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.42ε (Mean = 1.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.86ε (Mean = 1.24ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.05ε (Mean = 1.08ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_hermite"></a><p class="title"><b>Table 23.53. Error rates for hermite</b></p>
<div class="table-contents"><table class="table" summary="Error rates for hermite">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Hermite Polynomials
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.24ε (Mean = 2.07ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.24ε (Mean = 2.07ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.46ε (Mean = 1.41ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_heuman_lambda"></a><p class="title"><b>Table 23.54. Error rates for heuman_lambda</b></p>
<div class="table-contents"><table class="table" summary="Error rates for heuman_lambda">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral Jacobi Zeta: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.89ε (Mean = 0.887ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.89ε (Mean = 0.887ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.08ε (Mean = 0.734ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral Heuman Lambda: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.82ε (Mean = 0.609ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.82ε (Mean = 0.608ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.12ε (Mean = 0.588ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ibeta"></a><p class="title"><b>Table 23.55. Error rates for ibeta</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibeta">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 682ε (Mean = 32.6ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 22.9ε (Mean = 3.35ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.97ε (Mean = 2.09ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 21.3ε (Mean = 2.75ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.4ε (Mean = 1.93ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 690ε (Mean = 151ε))<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 232ε (Mean = 27.9ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 50ε (Mean = 12.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 124ε (Mean = 18.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 106ε (Mean = 16.3ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Large and Diverse Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.26ε (Mean = 0.063ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 3.9e+05ε (Mean = 1.82e+04ε)
                  <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_ibeta_GSL_2_1_Incomplete_Beta_Function_Large_and_Diverse_Values">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 574ε (Mean = 49.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.96e+04ε (Mean = 997ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.98e+04ε (Mean = 2.07e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.32e+03ε (Mean = 68.5ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Integer Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 254ε (Mean = 50.9ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 62.2ε (Mean = 8.95ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.45ε (Mean = 0.814ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 44.5ε (Mean = 10.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.85ε (Mean = 0.791ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ibeta_inv"></a><p class="title"><b>Table 23.56. Error rates for ibeta_inv</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibeta_inv">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Inverse incomplete beta
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11ε (Mean = 0.345ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max = 1.14e+121ε (Mean
                  = 3.28e+119ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_ibeta_inv_Rmath_3_2_3_Inverse_incomplete_beta">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.8e+04ε (Mean = 2.66e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.07e+04ε (Mean = 2.86e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.59e+03ε (Mean = 277ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ibeta_inva"></a><p class="title"><b>Table 23.57. Error rates for ibeta_inva</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibeta_inva">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Inverse incomplete beta
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.602ε (Mean = 0.0239ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 377ε (Mean = 24.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 438ε (Mean = 31.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 242ε (Mean = 22.9ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ibeta_invb"></a><p class="title"><b>Table 23.58. Error rates for ibeta_invb</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibeta_invb">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Inverse incomplete beta
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.765ε (Mean = 0.0422ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 407ε (Mean = 27.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 407ε (Mean = 24.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 409ε (Mean = 19.3ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ibetac"></a><p class="title"><b>Table 23.59. Error rates for ibetac</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibetac">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 22.4ε (Mean = 3.67ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.6ε (Mean = 2.22ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 13.8ε (Mean = 2.68ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.94ε (Mean = 1.71ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 204ε (Mean = 25.8ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 73.9ε (Mean = 11.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 132ε (Mean = 19.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 56.7ε (Mean = 14.3ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Large and Diverse Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.981ε (Mean = 0.0573ε)</span><br>
                  <br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 889ε (Mean = 68.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.45e+04ε (Mean = 1.32e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.31e+04ε (Mean = 2.04e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.88e+03ε (Mean = 82.7ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Integer Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 84.6ε (Mean = 18ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.34ε (Mean = 1.11ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 107ε (Mean = 17.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.37ε (Mean = 1.03ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ibetac_inv"></a><p class="title"><b>Table 23.60. Error rates for ibetac_inv</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibetac_inv">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Inverse incomplete beta
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.977ε (Mean = 0.0976ε)</span><br>
                  <br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max
                  = 3.01e+132ε (Mean = 8.65e+130ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_ibetac_inv_Rmath_3_2_3_Inverse_incomplete_beta">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.88e+04ε (Mean = 3.16e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.05e+04ε (Mean = 3.33e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.93e+03ε (Mean = 198ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ibetac_inva"></a><p class="title"><b>Table 23.61. Error rates for ibetac_inva</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibetac_inva">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Inverse incomplete beta
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.683ε (Mean = 0.0314ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 382ε (Mean = 22.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 315ε (Mean = 23.7ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 408ε (Mean = 26.7ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_ibetac_invb"></a><p class="title"><b>Table 23.62. Error rates for ibetac_invb</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibetac_invb">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Inverse incomplete beta
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.724ε (Mean = 0.0303ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 317ε (Mean = 19.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 369ε (Mean = 22.6ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 271ε (Mean = 16.4ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_jacobi_cn"></a><p class="title"><b>Table 23.63. Error rates for jacobi_cn</b></p>
<div class="table-contents"><table class="table" summary="Error rates for jacobi_cn">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 17.3ε (Mean = 4.29ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_jacobi_cn_GSL_2_1_Jacobi_Elliptic_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 71.6ε (Mean = 19.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 71.6ε (Mean = 19.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 45.8ε (Mean = 11.4ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.816ε (Mean = 0.0563ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 2.43ε (Mean = 0.803ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.68ε (Mean = 0.443ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.68ε (Mean = 0.454ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.83ε (Mean = 0.455ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Random Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 55.2ε (Mean = 1.64ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_jacobi_cn_GSL_2_1_Jacobi_Elliptic_Random_Small_Values">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.4ε (Mean = 0.594ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.4ε (Mean = 0.602ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 26.2ε (Mean = 1.17ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Modulus near 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.919ε (Mean = 0.127ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 0ε (Mean = 0ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_jacobi_cn_GSL_2_1_Jacobi_Elliptic_Modulus_near_1">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 675ε (Mean = 87.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 675ε (Mean = 86.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 513ε (Mean = 126ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Large Phi
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.2ε (Mean = 0.927ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 5.92e+03ε (Mean = 477ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.97e+04ε (Mean = 1.9e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.97e+04ε (Mean = 1.9e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.27e+04ε (Mean = 1.93e+03ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_jacobi_dn"></a><p class="title"><b>Table 23.64. Error rates for jacobi_dn</b></p>
<div class="table-contents"><table class="table" summary="Error rates for jacobi_dn">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.82ε (Mean = 1.18ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_jacobi_dn_GSL_2_1_Jacobi_Elliptic_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 49ε (Mean = 14ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 49ε (Mean = 14ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 34.3ε (Mean = 8.71ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 3ε (Mean = 0.61ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.53ε (Mean = 0.473ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.53ε (Mean = 0.481ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.52ε (Mean = 0.466ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Random Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.5ε (Mean = 0.0122ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.5ε (Mean = 0.391ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_jacobi_dn_GSL_2_1_Jacobi_Elliptic_Random_Small_Values">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 22.4ε (Mean = 0.777ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 22.4ε (Mean = 0.763ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 16.1ε (Mean = 0.685ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Modulus near 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.28ε (Mean = 0.194ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 0ε (Mean = 0ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_jacobi_dn_GSL_2_1_Jacobi_Elliptic_Modulus_near_1">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.75e+03ε (Mean = 293ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.75e+03ε (Mean = 293ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.24e+03ε (Mean = 482ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Large Phi
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 14.1ε (Mean = 0.897ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 121ε (Mean = 22ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.82e+04ε (Mean = 1.79e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.82e+04ε (Mean = 1.79e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.67e+04ε (Mean = 1e+03ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_jacobi_sn"></a><p class="title"><b>Table 23.65. Error rates for jacobi_sn</b></p>
<div class="table-contents"><table class="table" summary="Error rates for jacobi_sn">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 588ε (Mean = 146ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_jacobi_sn_GSL_2_1_Jacobi_Elliptic_Mathworld_Data">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 341ε (Mean = 80.7ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 341ε (Mean = 80.7ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 481ε (Mean = 113ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 4.02ε (Mean = 1.07ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.01ε (Mean = 0.584ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.01ε (Mean = 0.593ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.92ε (Mean = 0.567ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Random Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 11.7ε (Mean = 1.65ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_jacobi_sn_GSL_2_1_Jacobi_Elliptic_Random_Small_Values">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.99ε (Mean = 0.347ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.99ε (Mean = 0.347ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.11ε (Mean = 0.385ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Modulus near 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_jacobi_sn_GSL_2_1_Jacobi_Elliptic_Modulus_near_1">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 109ε (Mean = 7.35ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 109ε (Mean = 7.38ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 23.2ε (Mean = 1.85ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Jacobi Elliptic: Large Phi
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 12ε (Mean = 0.771ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 4.54e+04ε (Mean = 2.63e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.45e+04ε (Mean = 1.51e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.45e+04ε (Mean = 1.51e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.36e+04ε (Mean = 2.54e+03ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_jacobi_zeta"></a><p class="title"><b>Table 23.66. Error rates for jacobi_zeta</b></p>
<div class="table-contents"><table class="table" summary="Error rates for jacobi_zeta">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Elliptic Integral Jacobi Zeta: Mathworld Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.66ε (Mean = 0.48ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.66ε (Mean = 0.48ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.52ε (Mean = 0.357ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral Jacobi Zeta: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.99ε (Mean = 0.824ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.96ε (Mean = 1.06ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.89ε (Mean = 0.824ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Elliptic Integral Jacobi Zeta: Large Phi Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.92ε (Mean = 0.951ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.05ε (Mean = 1.13ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.52ε (Mean = 0.977ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_laguerre_n_m_x_"></a><p class="title"><b>Table 23.67. Error rates for laguerre(n, m, x)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for laguerre(n, m, x)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Associated Laguerre Polynomials
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.84ε (Mean = 0.0358ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 434ε (Mean = 10.7ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 167ε (Mean = 6.38ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 206ε (Mean = 6.86ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 167ε (Mean = 6.38ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 434ε (Mean = 11.1ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_laguerre_n_x_"></a><p class="title"><b>Table 23.68. Error rates for laguerre(n, x)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for laguerre(n, x)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Laguerre Polynomials
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.82ε (Mean = 0.408ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 3.1e+03ε (Mean = 185ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.39e+04ε (Mean = 828ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 4.2e+03ε (Mean
                  = 251ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.39e+04ε (Mean = 828ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.1e+03ε (Mean = 185ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_legendre_p"></a><p class="title"><b>Table 23.69. Error rates for legendre_p</b></p>
<div class="table-contents"><table class="table" summary="Error rates for legendre_p">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Legendre Polynomials: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.732ε (Mean = 0.0619ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 211ε (Mean = 20.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 69.2ε (Mean = 9.58ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 124ε (Mean = 13.2ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 69.2ε (Mean = 9.58ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 211ε (Mean = 20.4ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Legendre Polynomials: Large Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.632ε (Mean = 0.0693ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 300ε (Mean = 33.2ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 699ε (Mean = 59.6ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 343ε (Mean = 32.1ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 699ε (Mean = 59.6ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 300ε (Mean = 33.2ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_legendre_p_associated_"></a><p class="title"><b>Table 23.70. Error rates for legendre_p (associated)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for legendre_p (associated)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Associated Legendre Polynomials: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.05ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 121ε (Mean = 6.75ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_legendre_p_associated__GSL_2_1_Associated_Legendre_Polynomials_Small_Values">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 175ε (Mean = 9.88ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 175ε (Mean = 9.36ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_long_double_legendre_p_associated___cmath__Associated_Legendre_Polynomials_Small_Values">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 77.7ε (Mean = 5.59ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 121ε (Mean = 7.14ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_legendre_q"></a><p class="title"><b>Table 23.71. Error rates for legendre_q</b></p>
<div class="table-contents"><table class="table" summary="Error rates for legendre_q">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Legendre Polynomials: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.612ε (Mean = 0.0517ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 46.4ε (Mean = 7.46ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 50.9ε (Mean = 9ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 50.9ε (Mean = 8.98ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 46.4ε (Mean = 7.32ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Legendre Polynomials: Large Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.49ε (Mean = 0.202ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 4.6e+03ε (Mean = 366ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.98e+03ε (Mean = 478ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.98e+03ε (Mean = 478ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.6e+03ε (Mean = 366ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_lgamma"></a><p class="title"><b>Table 23.72. Error rates for lgamma</b></p>
<div class="table-contents"><table class="table" summary="Error rates for lgamma">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  factorials
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 33.6ε (Mean = 2.78ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1.55ε (Mean = 0.592ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.991ε (Mean = 0.308ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.67ε (Mean = 0.487ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.67ε (Mean = 0.487ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.991ε (Mean = 0.383ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.36ε (Mean = 0.476ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.914ε (Mean = 0.175ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.958ε (Mean = 0.38ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near 0
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 5.21ε (Mean = 1.57ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.42ε (Mean = 0.566ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.964ε (Mean = 0.543ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.964ε (Mean = 0.543ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.42ε (Mean = 0.566ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.964ε (Mean = 0.543ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.964ε (Mean = 0.462ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.962ε (Mean = 0.372ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 442ε (Mean = 88.8ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 7.99e+04ε (Mean = 1.68e+04ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.948ε (Mean = 0.36ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.615ε (Mean = 0.096ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.615ε (Mean = 0.096ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.948ε (Mean = 0.36ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.71ε (Mean = 0.581ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.867ε (Mean = 0.468ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.906ε (Mean = 0.565ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near 2
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.17e+03ε (Mean = 274ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 2.63e+05ε (Mean = 5.84e+04ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.878ε (Mean = 0.242ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.741ε (Mean = 0.263ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.741ε (Mean = 0.263ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.878ε (Mean = 0.242ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.598ε (Mean = 0.235ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.591ε (Mean = 0.159ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.741ε (Mean = 0.473ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near -10
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 24.9ε (Mean = 4.6ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 4.22ε (Mean = 1.26ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.81ε (Mean = 1.01ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.997ε (Mean = 0.412ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.997ε (Mean = 0.412ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.81ε (Mean = 1.01ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 3.04ε (Mean = 1.01ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.22ε (Mean = 1.33ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.997ε (Mean = 0.444ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near -55
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 7.02ε (Mean = 1.47ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 250ε (Mean = 60.9ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.821ε (Mean = 0.513ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.58ε (Mean = 0.672ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.58ε (Mean = 0.672ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.59ε (Mean = 0.587ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.821ε (Mean = 0.674ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.821ε (Mean = 0.419ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 249ε (Mean = 43.1ε))
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_log1p"></a><p class="title"><b>Table 23.73. Error rates for log1p</b></p>
<div class="table-contents"><table class="table" summary="Error rates for log1p">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Random test data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.818ε (Mean = 0.227ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.818ε (Mean = 0.227ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.818ε (Mean = 0.227ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.846ε (Mean = 0.153ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 0.846ε (Mean = 0.153ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.3ε (Mean = 0.66ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.818ε (Mean = 0.249ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.509ε (Mean = 0.057ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.509ε (Mean = 0.057ε))
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_non_central_beta_CDF"></a><p class="title"><b>Table 23.74. Error rates for non central beta CDF</b></p>
<div class="table-contents"><table class="table" summary="Error rates for non central beta CDF">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Non Central Beta, medium parameters
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.0649ε)</span><br>
                  <br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max
                  = 1.46e+26ε (Mean = 3.5e+24ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_non_central_beta_CDF_Rmath_3_2_3_Non_Central_Beta_medium_parameters">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 824ε (Mean = 27.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 832ε (Mean = 38.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 242ε (Mean = 31ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Non Central Beta, large parameters
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.18ε (Mean = 0.175ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max = 1.01e+36ε (Mean
                  = 1.19e+35ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_non_central_beta_CDF_Rmath_3_2_3_Non_Central_Beta_large_parameters">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.5e+04ε (Mean = 3.78e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.57e+04ε (Mean = 4.45e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.66e+03ε (Mean = 500ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_non_central_beta_CDF_complement"></a><p class="title"><b>Table 23.75. Error rates for non central beta CDF complement</b></p>
<div class="table-contents"><table class="table" summary="Error rates for non central beta CDF complement">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Non Central Beta, medium parameters
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.0936ε)</span><br>
                  <br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max
                  = 7.5e+97ε (Mean = 1.37e+96ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_non_central_beta_CDF_complement_Rmath_3_2_3_Non_Central_Beta_medium_parameters">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 396ε (Mean = 50.7ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 554ε (Mean = 57.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 624ε (Mean = 62.7ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Non Central Beta, large parameters
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.986ε (Mean = 0.188ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max = +INFε (Mean
                  = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_non_central_beta_CDF_complement_Rmath_3_2_3_Non_Central_Beta_large_parameters">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.83e+03ε (Mean = 993ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.56e+03ε (Mean = 707ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.25e+04ε (Mean = 1.49e+03ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_non_central_chi_squared_CDF"></a><p class="title"><b>Table 23.76. Error rates for non central chi squared CDF</b></p>
<div class="table-contents"><table class="table" summary="Error rates for non central chi squared CDF">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Non Central Chi Squared, medium parameters
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.99ε (Mean = 0.0544ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 727ε (Mean = 121ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 46.5ε (Mean = 10.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 115ε (Mean = 13.9ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 48.9ε (Mean = 10ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Non Central Chi Squared, large parameters
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.07ε (Mean = 0.102ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max = 3.27e+08ε (Mean
                  = 2.23e+07ε))</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.07e+03ε (Mean = 336ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.17e+03ε (Mean = 677ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.79e+03ε (Mean = 723ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_non_central_chi_squared_CDF_complement"></a><p class="title"><b>Table 23.77. Error rates for non central chi squared CDF complement</b></p>
<div class="table-contents"><table class="table" summary="Error rates for non central chi squared CDF complement">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Non Central Chi Squared, medium parameters
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.96ε (Mean = 0.0635ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max = +INFε (Mean
                  = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_non_central_chi_squared_CDF_complement_Rmath_3_2_3_Non_Central_Chi_Squared_medium_parameters">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 107ε (Mean = 17.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 171ε (Mean = 22.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 98.6ε (Mean = 15.8ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Non Central Chi Squared, large parameters
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.11ε (Mean = 0.278ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max = +INFε (Mean
                  = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_non_central_chi_squared_CDF_complement_Rmath_3_2_3_Non_Central_Chi_Squared_large_parameters">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.02e+03ε (Mean = 630ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.1e+03ε (Mean = 577ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.43e+03ε (Mean = 705ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_non_central_t_CDF"></a><p class="title"><b>Table 23.78. Error rates for non central t CDF</b></p>
<div class="table-contents"><table class="table" summary="Error rates for non central t CDF">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Non Central T
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.796ε (Mean = 0.0691ε)</span><br>
                  <br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max
                  = 5.28e+15ε (Mean = 8.49e+14ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_non_central_t_CDF_Rmath_3_2_3_Non_Central_T">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 139ε (Mean = 31ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 145ε (Mean = 30.9ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 135ε (Mean = 32.1ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Non Central T (small non-centrality)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 2.09e+03ε (Mean = 244ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.86ε (Mean = 1.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.15ε (Mean = 2.13ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.17ε (Mean = 1.45ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Non Central T (large parameters)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 257ε (Mean = 72.1ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 2.46ε (Mean = 0.657ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.26e+05ε (Mean = 1.48e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.24e+05ε (Mean = 1.47e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 286ε (Mean = 62.8ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_non_central_t_CDF_complement"></a><p class="title"><b>Table 23.79. Error rates for non central t CDF complement</b></p>
<div class="table-contents"><table class="table" summary="Error rates for non central t CDF complement">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Non Central T
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.707ε (Mean = 0.0497ε)</span><br>
                  <br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span> <span class="red">Max
                  = 6.19e+15ε (Mean = 6.72e+14ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_non_central_t_CDF_complement_Rmath_3_2_3_Non_Central_T">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 201ε (Mean = 31.7ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 340ε (Mean = 43.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 154ε (Mean = 32.1ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Non Central T (small non-centrality)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1.87e+03ε (Mean = 263ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.5ε (Mean = 2.13ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.5ε (Mean = 2.39ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.6ε (Mean = 1.63ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Non Central T (large parameters)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 478ε (Mean = 96.3ε)</span><br> <br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 2.24ε (Mean = 0.945ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.79e+05ε (Mean = 1.97e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.79e+05ε (Mean = 1.97e+05ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 227ε (Mean = 50.4ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_owens_t"></a><p class="title"><b>Table 23.80. Error rates for owens_t</b></p>
<div class="table-contents"><table class="table" summary="Error rates for owens_t">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Owens T (medium small values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.34ε (Mean = 0.944ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.34ε (Mean = 0.911ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.37ε (Mean = 0.98ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Owens T (large and diverse values)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 49ε (Mean = 2.16ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 24.5ε (Mean = 1.39ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.78ε (Mean = 0.621ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_polygamma"></a><p class="title"><b>Table 23.81. Error rates for polygamma</b></p>
<div class="table-contents"><table class="table" summary="Error rates for polygamma">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Mathematica Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.824ε (Mean = 0.0574ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 62.9ε (Mean = 12.8ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 108ε (Mean = 15.2ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.38ε (Mean = 1.84ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 34.3ε (Mean = 7.65ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.32ε (Mean = 1.95ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Mathematica Data - large arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.0592ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 244ε (Mean = 32.8ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_polygamma_GSL_2_1_Mathematica_Data_large_arguments">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  <span class="red">Max = 1.71e+56ε (Mean = 1.01e+55ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_polygamma_Rmath_3_2_3_Mathematica_Data_large_arguments">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.23ε (Mean = 0.323ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.1ε (Mean = 0.848ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 150ε (Mean = 13.9ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Mathematica Data - negative arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.516ε (Mean = 0.022ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 36.6ε (Mean = 3.04ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_polygamma_GSL_2_1_Mathematica_Data_negative_arguments">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 0ε (Mean = 0ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_polygamma_Rmath_3_2_3_Mathematica_Data_negative_arguments">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 269ε (Mean = 87.7ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 269ε (Mean = 88.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 497ε (Mean = 129ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Mathematica Data - large negative arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.79ε (Mean = 0.197ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_polygamma_GSL_2_1_Mathematica_Data_large_negative_arguments">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 0ε (Mean = 0ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_polygamma_Rmath_3_2_3_Mathematica_Data_large_negative_arguments">And
                  other failures.</a>)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 155ε (Mean = 96.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 155ε (Mean = 96.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 162ε (Mean = 101ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Mathematica Data - small arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 15.2ε (Mean = 5.03ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 106ε (Mean = 20ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.33ε (Mean = 0.75ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.33ε (Mean = 0.75ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3ε (Mean = 0.496ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Mathematica Data - Large orders and other bug cases
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 151ε (Mean = 39.3ε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_polygamma_GSL_2_1_Mathematica_Data_Large_orders_and_other_bug_cases">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  <span class="red">Max = +INFε (Mean = +INFε) <a class="link" href="logs.html#errors_GNU_C_version_7_1_0_linux_double_polygamma_Rmath_3_2_3_Mathematica_Data_Large_orders_and_other_bug_cases">And
                  other failures.</a>)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 54.5ε (Mean = 13.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 145ε (Mean = 55.9ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 200ε (Mean = 57.2ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_powm1"></a><p class="title"><b>Table 23.82. Error rates for powm1</b></p>
<div class="table-contents"><table class="table" summary="Error rates for powm1">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  powm1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.06ε (Mean = 0.425ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.04ε (Mean = 0.493ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.88ε (Mean = 0.49ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.84ε (Mean = 0.486ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_sin_pi"></a><p class="title"><b>Table 23.83. Error rates for sin_pi</b></p>
<div class="table-contents"><table class="table" summary="Error rates for sin_pi">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  sin_pi and cos_pi
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.996ε (Mean = 0.335ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.996ε (Mean = 0.336ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.99ε (Mean = 0.328ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  sin_pi and cos_pi near integers and half integers
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.976ε (Mean = 0.293ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.976ε (Mean = 0.293ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.996ε (Mean = 0.343ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_sph_bessel"></a><p class="title"><b>Table 23.84. Error rates for sph_bessel</b></p>
<div class="table-contents"><table class="table" summary="Error rates for sph_bessel">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Bessel j: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 243ε (Mean = 13.3ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.91e+06ε (Mean = 1.09e+05ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.978ε (Mean = 0.0445ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.79e+03ε (Mean = 107ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 243ε (Mean = 33.7ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 245ε (Mean = 16.3ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_sph_bessel_prime"></a><p class="title"><b>Table 23.85. Error rates for sph_bessel_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for sph_bessel_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Bessel j': Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.753ε (Mean = 0.0343ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 167ε (Mean = 12ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 167ε (Mean = 33.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 307ε (Mean = 25.2ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_sph_neumann"></a><p class="title"><b>Table 23.86. Error rates for sph_neumann</b></p>
<div class="table-contents"><table class="table" summary="Error rates for sph_neumann">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  y: Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 234ε (Mean = 19.5ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.6e+06ε (Mean = 1.4e+05ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.995ε (Mean = 0.0665ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 8.5e+04ε (Mean = 5.33e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 234ε (Mean = 19.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 281ε (Mean = 31.1ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_sph_neumann_prime"></a><p class="title"><b>Table 23.87. Error rates for sph_neumann_prime</b></p>
<div class="table-contents"><table class="table" summary="Error rates for sph_neumann_prime">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  y': Random Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.988ε (Mean = 0.0869ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 158ε (Mean = 18.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 158ε (Mean = 20.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 296ε (Mean = 25.6ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_spherical_harmonic_i"></a><p class="title"><b>Table 23.88. Error rates for spherical_harmonic_i</b></p>
<div class="table-contents"><table class="table" summary="Error rates for spherical_harmonic_i">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Spherical Harmonics
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.36ε (Mean = 0.0765ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.89e+03ε (Mean = 108ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.03e+04ε (Mean = 327ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.27e+04ε (Mean = 725ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_spherical_harmonic_r"></a><p class="title"><b>Table 23.89. Error rates for spherical_harmonic_r</b></p>
<div class="table-contents"><table class="table" summary="Error rates for spherical_harmonic_r">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Spherical Harmonics
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.58ε (Mean = 0.0707ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.89e+03ε (Mean = 108ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.03e+04ε (Mean = 327ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.27e+04ε (Mean = 725ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_sqrt1pm1"></a><p class="title"><b>Table 23.90. Error rates for sqrt1pm1</b></p>
<div class="table-contents"><table class="table" summary="Error rates for sqrt1pm1">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  sqrt1pm1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.3ε (Mean = 0.404ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.33ε (Mean = 0.404ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.54ε (Mean = 0.563ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.35ε (Mean = 0.497ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_tgamma"></a><p class="title"><b>Table 23.91. Error rates for tgamma</b></p>
<div class="table-contents"><table class="table" summary="Error rates for tgamma">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  factorials
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 3.95ε (Mean = 0.783ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 314ε (Mean = 93.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.67ε (Mean = 0.617ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.66ε (Mean = 0.584ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.66ε (Mean = 0.584ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 172ε (Mean = 41ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.85ε (Mean = 0.566ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 3.17ε (Mean = 0.928ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near 0
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 4.51ε (Mean = 1.92ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1ε (Mean = 0.335ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2ε (Mean = 0.608ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1ε (Mean = 0.376ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1ε (Mean = 0.376ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2ε (Mean = 0.647ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.5ε (Mean = 0.0791ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.5ε (Mean = 0.635ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1ε (Mean = 0.405ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 4.41ε (Mean = 1.81ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1ε (Mean = 0.32ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.51ε (Mean = 1.02ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.918ε (Mean = 0.203ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.918ε (Mean = 0.203ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.01ε (Mean = 1.06ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1ε (Mean = 0.175ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.1ε (Mean = 0.59ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1ε (Mean = 0.4ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near 2
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 7.95ε (Mean = 3.12ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 1ε (Mean = 0.191ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.1ε (Mean = 1.55ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 0.558ε (Mean = 0.298ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.558ε (Mean = 0.298ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.01ε (Mean = 1.89ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2ε (Mean = 0.733ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near -10
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 2.6ε (Mean = 1.05ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 34.9ε (Mean = 9.2ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.75ε (Mean = 0.895ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 2.26ε (Mean = 1.08ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 2.26ε (Mean = 1.08ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.75ε (Mean = 0.819ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.86ε (Mean = 0.881ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0.866ε (Mean = 0.445ε))
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  near -55
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 1.8ε (Mean = 0.782ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 3.89e+04ε (Mean = 9.52e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.69ε (Mean = 1.09ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.79ε (Mean = 0.75ε))<br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 1.79ε (Mean = 0.75ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 98.5ε (Mean = 53.4ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.7ε (Mean = 1.35ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;math.h&gt;:</em></span> Max = 3.87e+04ε (Mean = 6.71e+03ε))
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_tgamma1pm1"></a><p class="title"><b>Table 23.92. Error rates for tgamma1pm1</b></p>
<div class="table-contents"><table class="table" summary="Error rates for tgamma1pm1">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  tgamma1pm1(dz)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.12ε (Mean = 0.49ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.61ε (Mean = 0.84ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.31ε (Mean = 0.517ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_tgamma_delta_ratio"></a><p class="title"><b>Table 23.93. Error rates for tgamma_delta_ratio</b></p>
<div class="table-contents"><table class="table" summary="Error rates for tgamma_delta_ratio">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  tgamma + small delta ratios
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.83ε (Mean = 1.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 15.4ε (Mean = 2.09ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.56ε (Mean = 1.31ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma + small delta ratios (negative delta)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.94ε (Mean = 1.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.3ε (Mean = 2.03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.43ε (Mean = 1.42ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma + small integer ratios
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.96ε (Mean = 0.677ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.96ε (Mean = 0.677ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.74ε (Mean = 0.736ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma + small integer ratios (negative delta)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.62ε (Mean = 0.451ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.62ε (Mean = 0.451ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.15ε (Mean = 0.685ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  integer tgamma ratios
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.997ε (Mean = 0.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.997ε (Mean = 0.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.968ε (Mean = 0.386ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  integer tgamma ratios (negative delta)
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.853ε (Mean = 0.176ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.853ε (Mean = 0.176ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.974ε (Mean = 0.175ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_tgamma_incomplete_"></a><p class="title"><b>Table 23.94. Error rates for tgamma (incomplete)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for tgamma (incomplete)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  tgamma(a, z) medium values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 200ε (Mean = 13.3ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.47ε (Mean = 1.9ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 412ε (Mean = 95.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.14ε (Mean = 1.76ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) small values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.753ε (Mean = 0.0474ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> <span class="red">Max =
                  1.38e+10ε (Mean = 1.05e+09ε))</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.31ε (Mean = 0.775ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.13ε (Mean = 0.717ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.53ε (Mean = 0.66ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) integer and half integer values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 117ε (Mean = 12.5ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.52ε (Mean = 1.48ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 79.6ε (Mean = 20.9ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.16ε (Mean = 1.33ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_tgamma_lower"></a><p class="title"><b>Table 23.95. Error rates for tgamma_lower</b></p>
<div class="table-contents"><table class="table" summary="Error rates for tgamma_lower">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  tgamma(a, z) medium values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.833ε (Mean = 0.0315ε)</span><br>
                  <br> (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 0.833ε (Mean = 0.0315ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.79ε (Mean = 1.46ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 363ε (Mean = 63.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.62ε (Mean = 1.49ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) small values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.555ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.97ε (Mean = 0.558ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.57ε (Mean = 0.525ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  tgamma(a, z) integer and half integer values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0ε (Mean = 0ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.83ε (Mean = 1.15ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 84.7ε (Mean = 17.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.69ε (Mean = 0.849ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_tgamma_ratio"></a><p class="title"><b>Table 23.96. Error rates for tgamma_ratio</b></p>
<div class="table-contents"><table class="table" summary="Error rates for tgamma_ratio">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  tgamma ratios
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.694ε (Mean = 0.0347ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 2.99ε (Mean = 1.15ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 174ε (Mean = 61.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.28ε (Mean = 1.12ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_trigamma"></a><p class="title"><b>Table 23.97. Error rates for trigamma</b></p>
<div class="table-contents"><table class="table" summary="Error rates for trigamma">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  Mathematica Data
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.105ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 1.34e+04ε (Mean = 1.49e+03ε))<br>
                  (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 1.34e+04ε (Mean = 1.51e+03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.28ε (Mean = 0.449ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.28ε (Mean = 0.449ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1ε (Mean = 0.382ε)</span>
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.logs_and_tables.all_table.table_zeta"></a><p class="title"><b>Table 23.98. Error rates for zeta</b></p>
<div class="table-contents"><table class="table" summary="Error rates for zeta">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Zeta: Random values greater than 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.846ε (Mean = 0.0833ε)</span><br>
                  <br> (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 5.45ε (Mean = 1ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 8.69ε (Mean = 1.03ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.846ε (Mean = 0.0833ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.836ε (Mean = 0.093ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Zeta: Random values less than 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 7.03ε (Mean = 2.93ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 538ε (Mean = 59.3ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 137ε (Mean = 13.8ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 70.1ε (Mean = 17.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.84ε (Mean = 3.12ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Zeta: Values close to and greater than 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.995ε (Mean = 0.5ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 1.9e+06ε (Mean = 5.11e+05ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 7.73ε (Mean = 4.07ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.995ε (Mean = 0.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.994ε (Mean = 0.421ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Zeta: Values close to and less than 1
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.508ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 8.53e+06ε (Mean = 1.87e+06ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 0.991ε (Mean = 0.28ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.998ε (Mean = 0.508ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.991ε (Mean = 0.375ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Zeta: Integer arguments
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9ε (Mean = 3.06ε)</span><br> <br>
                  (<span class="emphasis"><em>&lt;cmath&gt;:</em></span> Max = 70.3ε (Mean = 17.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 3.75ε (Mean = 1.1ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 28ε (Mean = 5.62ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9ε (Mean = 3ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../logs_and_tables.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../logs_and_tables.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="logs.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
