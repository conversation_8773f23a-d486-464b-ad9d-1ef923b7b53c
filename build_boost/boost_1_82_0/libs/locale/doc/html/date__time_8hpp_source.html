<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.15"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Boost.Locale: boost/locale/date_time.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(initResizable);
/* @license-end */</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="section-basic.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="boost-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Boost.Locale
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('date__time_8hpp_source.html','');});
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">date_time.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// Copyright (c) 2009-2011 Artyom Beilis (Tonkikh)</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">// https://www.boost.org/LICENSE_1_0.txt</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;</div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#ifndef BOOST_LOCALE_DATE_TIME_HPP_INCLUDED</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#define BOOST_LOCALE_DATE_TIME_HPP_INCLUDED</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;</div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &lt;boost/locale/date_time_facet.hpp&gt;</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#include &lt;boost/locale/formatting.hpp&gt;</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &lt;boost/locale/hold_ptr.hpp&gt;</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &lt;boost/locale/time_zone.hpp&gt;</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &lt;locale&gt;</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &lt;stdexcept&gt;</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &lt;vector&gt;</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#ifdef BOOST_MSVC</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#    pragma warning(push)</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#    pragma warning(disable : 4275 4251 4231 4660)</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">namespace </span>boost { <span class="keyword">namespace </span>locale {</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__error.html">   30</a></span>&#160;    <span class="keyword">class </span>BOOST_SYMBOL_VISIBLE <a class="code" href="classboost_1_1locale_1_1date__time__error.html">date_time_error</a> : <span class="keyword">public</span> std::runtime_error {</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    <span class="keyword">public</span>:</div><div class="line"><a name="l00033"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__error.html#a25580392651d96c752a67f01ea853542">   33</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time__error.html#a25580392651d96c752a67f01ea853542">date_time_error</a>(<span class="keyword">const</span> std::string&amp; e) : std::runtime_error(e) {}</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    };</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno"><a class="line" href="structboost_1_1locale_1_1date__time__period.html">   42</a></span>&#160;    <span class="keyword">struct </span><a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> {</div><div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="structboost_1_1locale_1_1date__time__period.html#aa6511600eb5264c8597f700668e9c628">   43</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> <a class="code" href="structboost_1_1locale_1_1date__time__period.html#aa6511600eb5264c8597f700668e9c628">type</a>; </div><div class="line"><a name="l00044"></a><span class="lineno"><a class="line" href="structboost_1_1locale_1_1date__time__period.html#aec776b16ed46a22833308a4112886ca4">   44</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html#aec776b16ed46a22833308a4112886ca4">value</a>;                </div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="structboost_1_1locale_1_1date__time__period.html#a4db4cc5a2c415dc54ab5d67185881900">operator+</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> *<span class="keyword">this</span>; }</div><div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="structboost_1_1locale_1_1date__time__period.html#a42337ef4ab1008fc5deeb548c01905ca">   48</a></span>&#160;        <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="structboost_1_1locale_1_1date__time__period.html#a42337ef4ab1008fc5deeb548c01905ca">operator-</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html#a61b11b2243098412dddd804ca7e104af">date_time_period</a>(<a class="code" href="structboost_1_1locale_1_1date__time__period.html#aa6511600eb5264c8597f700668e9c628">type</a>, -<a class="code" href="structboost_1_1locale_1_1date__time__period.html#aec776b16ed46a22833308a4112886ca4">value</a>); }</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="structboost_1_1locale_1_1date__time__period.html#a61b11b2243098412dddd804ca7e104af">   51</a></span>&#160;        <a class="code" href="structboost_1_1locale_1_1date__time__period.html#a61b11b2243098412dddd804ca7e104af">date_time_period</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f = <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a>(), <span class="keywordtype">int</span> v = 1) : <a class="code" href="structboost_1_1locale_1_1date__time__period.html#aa6511600eb5264c8597f700668e9c628">type</a>(f), <a class="code" href="structboost_1_1locale_1_1date__time__period.html#aec776b16ed46a22833308a4112886ca4">value</a>(v) {}</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    };</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html">   54</a></span>&#160;    <span class="keyword">namespace </span>period {</div><div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a6468414599fd81815943e8d9e868fd1b">   56</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a6468414599fd81815943e8d9e868fd1b">invalid</a>()</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        {</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aab24556edbe246e0bc3b4b0d3f2e627eb">marks::invalid</a>);</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        }</div><div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">   61</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">era</a>()</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        {</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aacc399d4cddd6d129a7d36cc2ce94b47a">marks::era</a>);</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        }</div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">   66</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">year</a>()</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        {</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa57de42317988a555460cf98be583addc">marks::year</a>);</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        }</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">   71</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">extended_year</a>()</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        {</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa827e0e9673cbb1e0983191ea42c88c88">marks::extended_year</a>);</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        }</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">   76</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>()</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        {</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa1fe197c6bcc18839b75fc550857ed89a">marks::month</a>);</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        }</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">   81</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">day</a>()</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        {</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aac944d440d0afd38cc75c1011319f790e">marks::day</a>);</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        }</div><div class="line"><a name="l00086"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">   86</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">day_of_year</a>()</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;        {</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa6e0f5f21ddfe455d27dfdafb5e58796b">marks::day_of_year</a>);</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        }</div><div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">   96</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>()</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        {</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aaefcf18fd66bcbbac887950c40d5e039b">marks::day_of_week</a>);</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        }</div><div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">  102</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">day_of_week_in_month</a>()</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        {</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aada04317134ebd8c192c431fdc6776756">marks::day_of_week_in_month</a>);</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        }</div><div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">  107</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">day_of_week_local</a>()</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        {</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa16f1add720e3211b165609cc8bf49edf">marks::day_of_week_local</a>);</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        }</div><div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">  112</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">hour</a>()</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;        {</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa889701bc6cf11a9ee39d7223691966e1">marks::hour</a>);</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        }</div><div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">  117</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">hour_12</a>()</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;        {</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa33760c5f41e6cb76695f18d65f5d0a9d">marks::hour_12</a>);</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        }</div><div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">  122</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a>()</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        {</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa8c4f7415895e44b3c1c9484a8eadcf06">marks::am_pm</a>);</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        }</div><div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">  127</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">minute</a>()</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;        {</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa1fec41808558d9452161712537717906">marks::minute</a>);</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        }</div><div class="line"><a name="l00132"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">  132</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">second</a>()</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;        {</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa3afc5b74b9e5e23bdfeab56a297bb7c7">marks::second</a>);</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        }</div><div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">  137</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">week_of_year</a>()</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        {</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa779ca20cc3ae59a2421293f5d8023c2e">marks::week_of_year</a>);</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        }</div><div class="line"><a name="l00142"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">  142</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">week_of_month</a>()</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        {</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa91d7d091168f60438e08ecfb72a47aed">marks::week_of_month</a>);</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        }</div><div class="line"><a name="l00147"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">  147</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">first_day_of_week</a>()</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        {</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>(<a class="code" href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa8cc9f718b5bdf9c30cff3846118039bd">marks::first_day_of_week</a>);</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        }</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00153"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a9d37769736a55787e6f1b82b620b5ef4">  153</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">era</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;        {</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">era</a>(), v);</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;        }</div><div class="line"><a name="l00158"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab3a1f7646bf3496bde51942509d848d5">  158</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">year</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        {</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">year</a>(), v);</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        }</div><div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a484606afd8fb5bc756b97d009052c809">  163</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">extended_year</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;        {</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">extended_year</a>(), v);</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;        }</div><div class="line"><a name="l00168"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a8d5844402628043891f77fccab0f6c16">  168</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        {</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), v);</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;        }</div><div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a0170a70ce66c0cd31bd64e9be1662591">  173</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">day</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        {</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">day</a>(), v);</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;        }</div><div class="line"><a name="l00178"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aad2e66d5af580de384b94f8c71ba697c">  178</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">day_of_year</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        {</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">day_of_year</a>(), v);</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;        }</div><div class="line"><a name="l00188"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a754856ee5d0492db9367bdc3e1f4fb88">  188</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        {</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(), v);</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;        }</div><div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#af60490df8e226368cea043cc6046db19">  194</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">day_of_week_in_month</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;        {</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">day_of_week_in_month</a>(), v);</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;        }</div><div class="line"><a name="l00199"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a673f97f4427f8a3f0e6453a5ed6aaf1f">  199</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">day_of_week_local</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;        {</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">day_of_week_local</a>(), v);</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;        }</div><div class="line"><a name="l00204"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a0acb70742e1b2f17cc13152c544ac242">  204</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">hour</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;        {</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">hour</a>(), v);</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;        }</div><div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ad805b92b111e4fd7dc00d911b07f6ab8">  209</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">hour_12</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;        {</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">hour_12</a>(), v);</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;        }</div><div class="line"><a name="l00214"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab1288da3e9e6cff57a4d964abe463ab1">  214</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;        {</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a>(), v);</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;        }</div><div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a0490daa370b1e8c89b14d0af2db48073">  219</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">minute</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;        {</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">minute</a>(), v);</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;        }</div><div class="line"><a name="l00224"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aa75c0462a5cada6fc3661e48ca0ae016">  224</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">second</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        {</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">second</a>(), v);</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;        }</div><div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a9e8cc55821326edd169fbd90af7304ec">  229</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">week_of_year</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;        {</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">week_of_year</a>(), v);</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;        }</div><div class="line"><a name="l00234"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aeb20ca7d1a9fbba527939202dcd8a1a8">  234</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">week_of_month</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;        {</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">week_of_month</a>(), v);</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;        }</div><div class="line"><a name="l00239"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab1a52ed7a8042fa428d14c4f87642bc4">  239</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">first_day_of_week</a>(<span class="keywordtype">int</span> v)</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;        {</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">first_day_of_week</a>(), v);</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;        }</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;</div><div class="line"><a name="l00245"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a6073ebcf60bf690662c3a9d113b49e9b">  245</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a6073ebcf60bf690662c3a9d113b49e9b">january</a>()</div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;        {</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 0);</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;        }</div><div class="line"><a name="l00250"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab0610583a720120e8dcf90d0fe01cb01">  250</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ab0610583a720120e8dcf90d0fe01cb01">february</a>()</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;        {</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 1);</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;        }</div><div class="line"><a name="l00255"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ac0d844780c28dc783879d82eaa192961">  255</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ac0d844780c28dc783879d82eaa192961">march</a>()</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;        {</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 2);</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;        }</div><div class="line"><a name="l00260"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a1b4663ee24f7687e592f0b790e8df494">  260</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a1b4663ee24f7687e592f0b790e8df494">april</a>()</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;        {</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 3);</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;        }</div><div class="line"><a name="l00265"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a420d04f471ef28107ba1bd6a8edae263">  265</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a420d04f471ef28107ba1bd6a8edae263">may</a>()</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;        {</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 4);</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;        }</div><div class="line"><a name="l00270"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab9269cd77b2d055022a587c9c0d13673">  270</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ab9269cd77b2d055022a587c9c0d13673">june</a>()</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;        {</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 5);</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;        }</div><div class="line"><a name="l00275"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a41e228841e03b61187660fb3e9692c7d">  275</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a41e228841e03b61187660fb3e9692c7d">july</a>()</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;        {</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 6);</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;        }</div><div class="line"><a name="l00280"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab5d2e72c7d5b842c26af29af4c96a853">  280</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ab5d2e72c7d5b842c26af29af4c96a853">august</a>()</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;        {</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 7);</div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;        }</div><div class="line"><a name="l00285"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ad325c929fb0a1173097cb9195367b209">  285</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ad325c929fb0a1173097cb9195367b209">september</a>()</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;        {</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 8);</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;        }</div><div class="line"><a name="l00290"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ad0e376bf63fb32da0dad13b0c4a6fef1">  290</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ad0e376bf63fb32da0dad13b0c4a6fef1">october</a>()</div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;        {</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 9);</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;        }</div><div class="line"><a name="l00295"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab2810f9718b18b77e47b4d23f94589ae">  295</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ab2810f9718b18b77e47b4d23f94589ae">november</a>()</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;        {</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 10);</div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;        }</div><div class="line"><a name="l00300"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aad0c72b6aa0ade2e71a71223eefab6fd">  300</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#aad0c72b6aa0ade2e71a71223eefab6fd">december</a>()</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;        {</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(), 11);</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;        }</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;</div><div class="line"><a name="l00306"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a12e3cce2dc169b65062c7fadd1143b0b">  306</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a12e3cce2dc169b65062c7fadd1143b0b">sunday</a>()</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;        {</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(), 1);</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;        }</div><div class="line"><a name="l00311"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a45ad1aeaf7a0e62ef26a42adca38da70">  311</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a45ad1aeaf7a0e62ef26a42adca38da70">monday</a>()</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;        {</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(), 2);</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;        }</div><div class="line"><a name="l00316"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a2426159fe1f6cb25f2598f35adf14267">  316</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a2426159fe1f6cb25f2598f35adf14267">tuesday</a>()</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;        {</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(), 3);</div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;        }</div><div class="line"><a name="l00321"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a856d680245af08ff3bd1618817a90ef1">  321</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a856d680245af08ff3bd1618817a90ef1">wednesday</a>()</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;        {</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(), 4);</div><div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;        }</div><div class="line"><a name="l00326"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab03ddec166d072a7465e87dbaccc1389">  326</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ab03ddec166d072a7465e87dbaccc1389">thursday</a>()</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;        {</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(), 5);</div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;        }</div><div class="line"><a name="l00331"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a26ef9fa6f6df065606a36c9b42e165eb">  331</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a26ef9fa6f6df065606a36c9b42e165eb">friday</a>()</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;        {</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(), 6);</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;        }</div><div class="line"><a name="l00336"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ac16b397e27e29fe6483540910e8ade3a">  336</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ac16b397e27e29fe6483540910e8ade3a">saturday</a>()</div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;        {</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(), 7);</div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;        }</div><div class="line"><a name="l00341"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a683b393abc6276f3d77289af0f5d6404">  341</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a683b393abc6276f3d77289af0f5d6404">am</a>()</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;        {</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a>(), 0);</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;        }</div><div class="line"><a name="l00346"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a5ee88cdab049350f1346466b2da6ccf0">  346</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a5ee88cdab049350f1346466b2da6ccf0">pm</a>()</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;        {</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a>(), 1);</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;        }</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;</div><div class="line"><a name="l00352"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a1399317fdf67b50ec11aa9298176ab70">  352</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a1399317fdf67b50ec11aa9298176ab70">operator+</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)</div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;        {</div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f);</div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;        }</div><div class="line"><a name="l00357"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a9c5588c1059eff9e9ab8034f7442bf00">  357</a></span>&#160;        <span class="keyword">inline</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a9c5588c1059eff9e9ab8034f7442bf00">operator-</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;        {</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f, -1);</div><div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;        }</div><div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;</div><div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00364"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a64a8e83513d867a5356cacc596d03962">  364</a></span>&#160;        <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a64a8e83513d867a5356cacc596d03962">operator*</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f, T v)</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;        {</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f, v);</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;        }</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00371"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a854429d3488950dbf68104f3ae4dd68c">  371</a></span>&#160;        <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a64a8e83513d867a5356cacc596d03962">operator*</a>(T v, <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;        {</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f, v);</div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;        }</div><div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00377"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a28a491e49e553332bebc92c3c7c6e917">  377</a></span>&#160;        <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a64a8e83513d867a5356cacc596d03962">operator*</a>(T v, <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> f)</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;        {</div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f.<a class="code" href="structboost_1_1locale_1_1date__time__period.html#aa6511600eb5264c8597f700668e9c628">type</a>, f.<a class="code" href="structboost_1_1locale_1_1date__time__period.html#aec776b16ed46a22833308a4112886ca4">value</a> * v);</div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;        }</div><div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;</div><div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00384"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a6cf5ca5046426b079e8949944c265c72">  384</a></span>&#160;        <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a64a8e83513d867a5356cacc596d03962">operator*</a>(<a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> f, T v)</div><div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;        {</div><div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;            <span class="keywordflow">return</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f.<a class="code" href="structboost_1_1locale_1_1date__time__period.html#aa6511600eb5264c8597f700668e9c628">type</a>, f.<a class="code" href="structboost_1_1locale_1_1date__time__period.html#aec776b16ed46a22833308a4112886ca4">value</a> * v);</div><div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;        }</div><div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;</div><div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;    } <span class="comment">// namespace period</span></div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;</div><div class="line"><a name="l00395"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__period__set.html">  395</a></span>&#160;    <span class="keyword">class </span><a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a> {</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;    <span class="keyword">public</span>:</div><div class="line"><a name="l00398"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__period__set.html#a635b8d91c4c8da99857810e42a0aff65">  398</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a635b8d91c4c8da99857810e42a0aff65">date_time_period_set</a>() {}</div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;</div><div class="line"><a name="l00401"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__period__set.html#a0966962f5da78fc06121d1455efcbbae">  401</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a0966962f5da78fc06121d1455efcbbae">date_time_period_set</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) { basic_[0] = <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f); }</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;</div><div class="line"><a name="l00404"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__period__set.html#a5867088d009104d8d3725d52ad0ac2bd">  404</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a5867088d009104d8d3725d52ad0ac2bd">date_time_period_set</a>(<span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; fl) { basic_[0] = fl; }</div><div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;</div><div class="line"><a name="l00407"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__period__set.html#a0e70247c1738dbf6869e6d8c04461893">  407</a></span>&#160;        <span class="keywordtype">void</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a0e70247c1738dbf6869e6d8c04461893">add</a>(<a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> f)</div><div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;        {</div><div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;            <span class="keywordtype">size_t</span> n = <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a14b5c2e199b3458d5faee0dc572b2f28">size</a>();</div><div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;            <span class="keywordflow">if</span>(n &lt; 4)</div><div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;                basic_[n] = f;</div><div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;            <span class="keywordflow">else</span></div><div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;                periods_.push_back(f);</div><div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;        }</div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;</div><div class="line"><a name="l00417"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__period__set.html#a14b5c2e199b3458d5faee0dc572b2f28">  417</a></span>&#160;        <span class="keywordtype">size_t</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a14b5c2e199b3458d5faee0dc572b2f28">size</a>()<span class="keyword"> const</span></div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;<span class="keyword">        </span>{</div><div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;            <span class="keywordflow">if</span>(basic_[0].type == <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a>())</div><div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;                <span class="keywordflow">return</span> 0;</div><div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;            <span class="keywordflow">if</span>(basic_[1].type == <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a>())</div><div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;                <span class="keywordflow">return</span> 1;</div><div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;            <span class="keywordflow">if</span>(basic_[2].type == <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a>())</div><div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;                <span class="keywordflow">return</span> 2;</div><div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;            <span class="keywordflow">if</span>(basic_[3].type == <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a>())</div><div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;                <span class="keywordflow">return</span> 3;</div><div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;            <span class="keywordflow">return</span> 4 + periods_.size();</div><div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;        }</div><div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;</div><div class="line"><a name="l00431"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__period__set.html#a47b0a2759cbacd296d91929ddd453dcb">  431</a></span>&#160;        <span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a47b0a2759cbacd296d91929ddd453dcb">operator[]</a>(<span class="keywordtype">size_t</span> n)<span class="keyword"> const</span></div><div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;<span class="keyword">        </span>{</div><div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;            <span class="keywordflow">if</span>(n &gt;= <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a14b5c2e199b3458d5faee0dc572b2f28">size</a>())</div><div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;                <span class="keywordflow">throw</span> std::out_of_range(<span class="stringliteral">&quot;Invalid index to date_time_period&quot;</span>);</div><div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;            <span class="keywordflow">if</span>(n &lt; 4)</div><div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;                <span class="keywordflow">return</span> basic_[n];</div><div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;            <span class="keywordflow">else</span></div><div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;                <span class="keywordflow">return</span> periods_[n - 4];</div><div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;        }</div><div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;</div><div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;    <span class="keyword">private</span>:</div><div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;        <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> basic_[4];</div><div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;        std::vector&lt;date_time_period&gt; periods_;</div><div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;    };</div><div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;</div><div class="line"><a name="l00447"></a><span class="lineno"><a class="line" href="group__date__time.html#ga5fb5bf4b94dcd67f45564e8015b57c20">  447</a></span>&#160;    <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a> <a class="code" href="group__date__time.html#ga5fb5bf4b94dcd67f45564e8015b57c20">operator+</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; a, <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; b)</div><div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;    {</div><div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a> s(a);</div><div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;        <span class="keywordflow">for</span>(<span class="keywordtype">unsigned</span> i = 0; i &lt; b.<a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a14b5c2e199b3458d5faee0dc572b2f28">size</a>(); i++)</div><div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;            s.<a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a0e70247c1738dbf6869e6d8c04461893">add</a>(b[i]);</div><div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;        <span class="keywordflow">return</span> s;</div><div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;    }</div><div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;</div><div class="line"><a name="l00456"></a><span class="lineno"><a class="line" href="group__date__time.html#ga0079bd8236eee75c4b5baa8497959b78">  456</a></span>&#160;    <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a> <a class="code" href="group__date__time.html#ga0079bd8236eee75c4b5baa8497959b78">operator-</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; a, <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; b)</div><div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;    {</div><div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a> s(a);</div><div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;        <span class="keywordflow">for</span>(<span class="keywordtype">unsigned</span> i = 0; i &lt; b.<a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a14b5c2e199b3458d5faee0dc572b2f28">size</a>(); i++)</div><div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;            s.<a class="code" href="classboost_1_1locale_1_1date__time__period__set.html#a0e70247c1738dbf6869e6d8c04461893">add</a>(-b[i]);</div><div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;        <span class="keywordflow">return</span> s;</div><div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;    }</div><div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;</div><div class="line"><a name="l00469"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1calendar.html">  469</a></span>&#160;    <span class="keyword">class </span>BOOST_LOCALE_DECL <a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a> {</div><div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;    <span class="keyword">public</span>:</div><div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;        <a class="code" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113">calendar</a>(std::ios_base&amp; ios);</div><div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;</div><div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;        <a class="code" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113">calendar</a>(<span class="keyword">const</span> std::locale&amp; l, <span class="keyword">const</span> std::string&amp; zone);</div><div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;</div><div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;        <a class="code" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113">calendar</a>(<span class="keyword">const</span> std::locale&amp; l);</div><div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;</div><div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;</div><div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;        <a class="code" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113">calendar</a>(<span class="keyword">const</span> std::string&amp; zone);</div><div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;</div><div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;        <a class="code" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113">calendar</a>();</div><div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;        ~<a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a>();</div><div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;</div><div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;        <a class="code" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113">calendar</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a>&amp; other);</div><div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;        <a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a>&amp; operator=(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a>&amp; other);</div><div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;</div><div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;        <span class="keywordtype">int</span> minimum(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) <span class="keyword">const</span>;</div><div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;        <span class="keywordtype">int</span> greatest_minimum(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) <span class="keyword">const</span>;</div><div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;        <span class="keywordtype">int</span> maximum(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) <span class="keyword">const</span>;</div><div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;        <span class="keywordtype">int</span> least_maximum(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) <span class="keyword">const</span>;</div><div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;</div><div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;        <span class="keywordtype">int</span> first_day_of_week() <span class="keyword">const</span>;</div><div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;</div><div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;        std::locale get_locale() <span class="keyword">const</span>;</div><div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;        std::string get_time_zone() <span class="keyword">const</span>;</div><div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;</div><div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;        <span class="keywordtype">bool</span> is_gregorian() <span class="keyword">const</span>;</div><div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;</div><div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;        <span class="keywordtype">bool</span> <a class="code" href="group__boundary.html#ga9ae9b79530f58d95aebde8a221461130">operator==</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a>&amp; other) <span class="keyword">const</span>;</div><div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;        <span class="keywordtype">bool</span> <a class="code" href="group__boundary.html#ga64c5f3099bc9f79d468b3e9b91d9cb0e">operator!=</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a>&amp; other) <span class="keyword">const</span>;</div><div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;</div><div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;    <span class="keyword">private</span>:</div><div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;        <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>;</div><div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;        std::locale locale_;</div><div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;        std::string tz_;</div><div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;        <a class="code" href="classboost_1_1locale_1_1hold__ptr.html">hold_ptr&lt;abstract_calendar&gt;</a> impl_;</div><div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;    };</div><div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;</div><div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;</div><div class="line"><a name="l00561"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html">  561</a></span>&#160;    <span class="keyword">class </span>BOOST_LOCALE_DECL <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> {</div><div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;    <span class="keyword">public</span>:</div><div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>();</div><div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;</div><div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other);</div><div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;        <span class="comment">// Move construct a new date_time</span></div><div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>(<a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp;&amp;) = <span class="keywordflow">default</span>;</div><div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;</div><div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other, <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; set);</div><div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;</div><div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator=(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other);</div><div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;        <span class="comment">// Move assign a date_time</span></div><div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator=(<a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp;&amp;) = <span class="keywordflow">default</span>;</div><div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;</div><div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>(<span class="keywordtype">double</span> <a class="code" href="group__manipulators.html#gae669b101cbeaed6f6d246ebdcaa8f39c">time</a>);</div><div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;</div><div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>(<span class="keywordtype">double</span> <a class="code" href="group__manipulators.html#gae669b101cbeaed6f6d246ebdcaa8f39c">time</a>, <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a>&amp; cal);</div><div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;</div><div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a>&amp; cal);</div><div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;</div><div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; set);</div><div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;</div><div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; set, <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1calendar.html">calendar</a>&amp; cal);</div><div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;</div><div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator=(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; f);</div><div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;</div><div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;        <span class="keywordtype">void</span> set(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f, <span class="keywordtype">int</span> v);</div><div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;</div><div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;        <span class="keywordtype">int</span> get(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) <span class="keyword">const</span>;</div><div class="line"><a name="l00609"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html#a57fc9acb86b8e89bc20278d510f096f5">  609</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="classboost_1_1locale_1_1date__time.html#a57fc9acb86b8e89bc20278d510f096f5">operator/</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> get(f); }</div><div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;</div><div class="line"><a name="l00612"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html#a3d66a4e6adfcc369c03626c8cd7429b7">  612</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="classboost_1_1locale_1_1date__time.html#a3d66a4e6adfcc369c03626c8cd7429b7">operator+</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> *<span class="keyword">this</span> + <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f); }</div><div class="line"><a name="l00614"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html#aefda6042554834d3455e680de596d577">  614</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="classboost_1_1locale_1_1date__time.html#aefda6042554834d3455e680de596d577">operator-</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> *<span class="keyword">this</span> - <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f); }</div><div class="line"><a name="l00616"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html#a4af6593b60bf965b9138a564644ab66a">  616</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; <a class="code" href="classboost_1_1locale_1_1date__time.html#a4af6593b60bf965b9138a564644ab66a">operator+=</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) { <span class="keywordflow">return</span> *<span class="keyword">this</span> += <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f); }</div><div class="line"><a name="l00618"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html#a04f0b2a8b8444e8b603ebbf58ef3a4e3">  618</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; <a class="code" href="classboost_1_1locale_1_1date__time.html#a04f0b2a8b8444e8b603ebbf58ef3a4e3">operator-=</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) { <span class="keywordflow">return</span> *<span class="keyword">this</span> -= <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f); }</div><div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;</div><div class="line"><a name="l00621"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html#ac538193b90000b90b20792b87a42ab72">  621</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="classboost_1_1locale_1_1date__time.html#ac538193b90000b90b20792b87a42ab72">operator&lt;&lt;</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> *<span class="keyword">this</span> &lt;&lt; <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f); }</div><div class="line"><a name="l00623"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html#a8865698029284b6c7729ea393ad2ebfb">  623</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="classboost_1_1locale_1_1date__time.html#a8865698029284b6c7729ea393ad2ebfb">operator&gt;&gt;</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> *<span class="keyword">this</span> &gt;&gt; <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f); }</div><div class="line"><a name="l00625"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html#ab249f9229b2de6fc9d1afc818bb1d0dc">  625</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; <a class="code" href="classboost_1_1locale_1_1date__time.html#ab249f9229b2de6fc9d1afc818bb1d0dc">operator&lt;&lt;=</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) { <span class="keywordflow">return</span> *<span class="keyword">this</span> &lt;&lt;= <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f); }</div><div class="line"><a name="l00627"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time.html#abb1fb403d1571d7973186916cc76686e">  627</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; <a class="code" href="classboost_1_1locale_1_1date__time.html#abb1fb403d1571d7973186916cc76686e">operator&gt;&gt;=</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) { <span class="keywordflow">return</span> *<span class="keyword">this</span> &gt;&gt;= <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>(f); }</div><div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;</div><div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="group__date__time.html#ga5fb5bf4b94dcd67f45564e8015b57c20">operator+</a>(<span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; v) <span class="keyword">const</span>;</div><div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="group__date__time.html#ga0079bd8236eee75c4b5baa8497959b78">operator-</a>(<span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; v) <span class="keyword">const</span>;</div><div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator+=(<span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; v);</div><div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator-=(<span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; v);</div><div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;</div><div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="group__date__time.html#gadac52301b5dbc9383cc35610417802a3">operator&lt;&lt;</a>(<span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; v) <span class="keyword">const</span>;</div><div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="group__date__time.html#gaf6e30351fc67e887e37853723c228484">operator&gt;&gt;</a>(<span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; v) <span class="keyword">const</span>;</div><div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator&lt;&lt;=(<span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; v);</div><div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator&gt;&gt;=(<span class="keyword">const</span> <a class="code" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&amp; v);</div><div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;</div><div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="group__date__time.html#ga5fb5bf4b94dcd67f45564e8015b57c20">operator+</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; v) <span class="keyword">const</span>;</div><div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="group__date__time.html#ga0079bd8236eee75c4b5baa8497959b78">operator-</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; v) <span class="keyword">const</span>;</div><div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator+=(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; v);</div><div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator-=(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; v);</div><div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;</div><div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="group__date__time.html#gadac52301b5dbc9383cc35610417802a3">operator&lt;&lt;</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; v) <span class="keyword">const</span>;</div><div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a> <a class="code" href="group__date__time.html#gaf6e30351fc67e887e37853723c228484">operator&gt;&gt;</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; v) <span class="keyword">const</span>;</div><div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator&lt;&lt;=(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; v);</div><div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; operator&gt;&gt;=(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&amp; v);</div><div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;</div><div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;        <span class="keywordtype">double</span> <a class="code" href="group__manipulators.html#gae669b101cbeaed6f6d246ebdcaa8f39c">time</a>() <span class="keyword">const</span>;</div><div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160;        <span class="keywordtype">void</span> <a class="code" href="group__manipulators.html#gae669b101cbeaed6f6d246ebdcaa8f39c">time</a>(<span class="keywordtype">double</span> v);</div><div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;</div><div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;        <span class="keywordtype">bool</span> <a class="code" href="group__boundary.html#ga9ae9b79530f58d95aebde8a221461130">operator==</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other) <span class="keyword">const</span>;</div><div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;        <span class="keywordtype">bool</span> <a class="code" href="group__boundary.html#ga64c5f3099bc9f79d468b3e9b91d9cb0e">operator!=</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other) <span class="keyword">const</span>;</div><div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;        <span class="keywordtype">bool</span> <a class="code" href="group__boundary.html#ga1730e6426d9030c42f8d4987f2591aae">operator&lt;</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other) <span class="keyword">const</span>;</div><div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;        <span class="keywordtype">bool</span> <a class="code" href="group__boundary.html#gaaf63b3de94f3146ba8c15f0374a100db">operator&gt;</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other) <span class="keyword">const</span>;</div><div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;        <span class="keywordtype">bool</span> <a class="code" href="group__boundary.html#ga07d3ee374afea084c1bc8efa5fb5aba9">operator&lt;=</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other) <span class="keyword">const</span>;</div><div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;        <span class="keywordtype">bool</span> <a class="code" href="group__boundary.html#ga45c63976f13515c35b514cd0871a7f32">operator&gt;=</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other) <span class="keyword">const</span>;</div><div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;</div><div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;        <span class="keywordtype">void</span> swap(<a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other);</div><div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;</div><div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;        <span class="keywordtype">int</span> difference(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; other, <a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) <span class="keyword">const</span>;</div><div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;</div><div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;        <span class="keywordtype">int</span> minimum(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) <span class="keyword">const</span>;</div><div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;        <span class="keywordtype">int</span> maximum(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f) <span class="keyword">const</span>;</div><div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;</div><div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;        <span class="keywordtype">bool</span> is_in_daylight_saving_time() <span class="keyword">const</span>;</div><div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;</div><div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;    <span class="keyword">private</span>:</div><div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;        <a class="code" href="classboost_1_1locale_1_1hold__ptr.html">hold_ptr&lt;abstract_calendar&gt;</a> impl_;</div><div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;    };</div><div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;</div><div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;    <span class="keyword">template</span>&lt;<span class="keyword">typename</span> CharType&gt;</div><div class="line"><a name="l00719"></a><span class="lineno"><a class="line" href="group__date__time.html#gadac52301b5dbc9383cc35610417802a3">  719</a></span>&#160;    std::basic_ostream&lt;CharType&gt;&amp; <a class="code" href="group__date__time.html#gadac52301b5dbc9383cc35610417802a3">operator&lt;&lt;</a>(std::basic_ostream&lt;CharType&gt;&amp; out, <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; t)</div><div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;    {</div><div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;        <span class="keywordtype">double</span> time_point = t.<a class="code" href="classboost_1_1locale_1_1date__time.html#a3bd340d8382b034ac1c90c9447ca7713">time</a>();</div><div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;        uint64_t display_flags = <a class="code" href="classboost_1_1locale_1_1ios__info.html#a02f6979dffc2df97c3612d72b7c7241b">ios_info::get</a>(out).<a class="code" href="classboost_1_1locale_1_1ios__info.html#a18b06a5cf88e25361bf188f64216edf2">display_flags</a>();</div><div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;        <span class="keywordflow">if</span>(display_flags == flags::date || display_flags == flags::time || display_flags == flags::datetime</div><div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;           || display_flags == flags::strftime)</div><div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;        {</div><div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;            out &lt;&lt; time_point;</div><div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;        } <span class="keywordflow">else</span> {</div><div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;            <a class="code" href="classboost_1_1locale_1_1ios__info.html#a02f6979dffc2df97c3612d72b7c7241b">ios_info::get</a>(out).<a class="code" href="classboost_1_1locale_1_1ios__info.html#a18b06a5cf88e25361bf188f64216edf2">display_flags</a>(flags::datetime);</div><div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;            out &lt;&lt; time_point;</div><div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;            <a class="code" href="classboost_1_1locale_1_1ios__info.html#a02f6979dffc2df97c3612d72b7c7241b">ios_info::get</a>(out).<a class="code" href="classboost_1_1locale_1_1ios__info.html#a18b06a5cf88e25361bf188f64216edf2">display_flags</a>(display_flags);</div><div class="line"><a name="l00731"></a><span class="lineno">  731</span>&#160;        }</div><div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;        <span class="keywordflow">return</span> out;</div><div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;    }</div><div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;</div><div class="line"><a name="l00738"></a><span class="lineno">  738</span>&#160;    <span class="keyword">template</span>&lt;<span class="keyword">typename</span> CharType&gt;</div><div class="line"><a name="l00739"></a><span class="lineno"><a class="line" href="group__date__time.html#gaf6e30351fc67e887e37853723c228484">  739</a></span>&#160;    std::basic_istream&lt;CharType&gt;&amp; <a class="code" href="group__date__time.html#gaf6e30351fc67e887e37853723c228484">operator&gt;&gt;</a>(std::basic_istream&lt;CharType&gt;&amp; in, <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; t)</div><div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;    {</div><div class="line"><a name="l00741"></a><span class="lineno">  741</span>&#160;        <span class="keywordtype">double</span> v;</div><div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;        uint64_t display_flags = <a class="code" href="classboost_1_1locale_1_1ios__info.html#a02f6979dffc2df97c3612d72b7c7241b">ios_info::get</a>(in).<a class="code" href="classboost_1_1locale_1_1ios__info.html#a18b06a5cf88e25361bf188f64216edf2">display_flags</a>();</div><div class="line"><a name="l00743"></a><span class="lineno">  743</span>&#160;        <span class="keywordflow">if</span>(display_flags == flags::date || display_flags == flags::time || display_flags == flags::datetime</div><div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;           || display_flags == flags::strftime)</div><div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;        {</div><div class="line"><a name="l00746"></a><span class="lineno">  746</span>&#160;            in &gt;&gt; v;</div><div class="line"><a name="l00747"></a><span class="lineno">  747</span>&#160;        } <span class="keywordflow">else</span> {</div><div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;            <a class="code" href="classboost_1_1locale_1_1ios__info.html#a02f6979dffc2df97c3612d72b7c7241b">ios_info::get</a>(in).<a class="code" href="classboost_1_1locale_1_1ios__info.html#a18b06a5cf88e25361bf188f64216edf2">display_flags</a>(flags::datetime);</div><div class="line"><a name="l00749"></a><span class="lineno">  749</span>&#160;            in &gt;&gt; v;</div><div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160;            <a class="code" href="classboost_1_1locale_1_1ios__info.html#a02f6979dffc2df97c3612d72b7c7241b">ios_info::get</a>(in).<a class="code" href="classboost_1_1locale_1_1ios__info.html#a18b06a5cf88e25361bf188f64216edf2">display_flags</a>(display_flags);</div><div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160;        }</div><div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;        <span class="keywordflow">if</span>(!in.fail())</div><div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;            t.<a class="code" href="classboost_1_1locale_1_1date__time.html#a3bd340d8382b034ac1c90c9447ca7713">time</a>(v);</div><div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;        <span class="keywordflow">return</span> in;</div><div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;    }</div><div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;</div><div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;<span class="preprocessor">#ifdef BOOST_MSVC</span></div><div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;<span class="preprocessor">#    pragma warning(push)</span></div><div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160;<span class="preprocessor">#    pragma warning(disable : 4512) // assignment operator could not be generated</span></div><div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;</div><div class="line"><a name="l00768"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__duration.html">  768</a></span>&#160;    <span class="keyword">class </span><a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> {</div><div class="line"><a name="l00769"></a><span class="lineno">  769</span>&#160;    <span class="keyword">public</span>:</div><div class="line"><a name="l00772"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__duration.html#a4556447fc56ce359e2e207da2bbd25bf">  772</a></span>&#160;        <a class="code" href="classboost_1_1locale_1_1date__time__duration.html#a4556447fc56ce359e2e207da2bbd25bf">date_time_duration</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; first, <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; second) : s_(first), e_(second) {}</div><div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;</div><div class="line"><a name="l00775"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">  775</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html#a8570e450ad9c900a4dd39235b8e61909">start</a>().<a class="code" href="classboost_1_1locale_1_1date__time.html#a8d00584d1f1b2dc0f9b44ef53e2a15e9">difference</a>(<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#a77e0946de974eeeb60d8aa3f6d174c93">end</a>(), f); }</div><div class="line"><a name="l00777"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__duration.html#aac2e7c14858dcde54baca11d26d2dd54">  777</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html#aac2e7c14858dcde54baca11d26d2dd54">operator/</a>(<a class="code" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html#a8570e450ad9c900a4dd39235b8e61909">start</a>().<a class="code" href="classboost_1_1locale_1_1date__time.html#a8d00584d1f1b2dc0f9b44ef53e2a15e9">difference</a>(<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#a77e0946de974eeeb60d8aa3f6d174c93">end</a>(), f); }</div><div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;</div><div class="line"><a name="l00780"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__duration.html#a8570e450ad9c900a4dd39235b8e61909">  780</a></span>&#160;        <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; <a class="code" href="classboost_1_1locale_1_1date__time__duration.html#a8570e450ad9c900a4dd39235b8e61909">start</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> s_; }</div><div class="line"><a name="l00782"></a><span class="lineno"><a class="line" href="classboost_1_1locale_1_1date__time__duration.html#a77e0946de974eeeb60d8aa3f6d174c93">  782</a></span>&#160;        <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; <a class="code" href="classboost_1_1locale_1_1date__time__duration.html#a77e0946de974eeeb60d8aa3f6d174c93">end</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> e_; }</div><div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;</div><div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;    <span class="keyword">private</span>:</div><div class="line"><a name="l00785"></a><span class="lineno">  785</span>&#160;        <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; s_;</div><div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;        <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; e_;</div><div class="line"><a name="l00787"></a><span class="lineno">  787</span>&#160;    };</div><div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;<span class="preprocessor">#ifdef BOOST_MSVC</span></div><div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160;<span class="preprocessor">#    pragma warning(pop)</span></div><div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;</div><div class="line"><a name="l00794"></a><span class="lineno"><a class="line" href="group__date__time.html#ga04d9cf366abf96ba0a208f618fc9fb06">  794</a></span>&#160;    <span class="keyword">inline</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> <a class="code" href="group__date__time.html#ga0079bd8236eee75c4b5baa8497959b78">operator-</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; later, <span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; earlier)</div><div class="line"><a name="l00795"></a><span class="lineno">  795</span>&#160;    {</div><div class="line"><a name="l00796"></a><span class="lineno">  796</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>(earlier, later);</div><div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;    }</div><div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;</div><div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;    <span class="keyword">namespace </span>period {</div><div class="line"><a name="l00801"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#af372c670a3979be4d5b8525ac224bae3">  801</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">era</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;        {</div><div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">era</a>());</div><div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160;        }</div><div class="line"><a name="l00807"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a87e2ab6badc92d4541cc8651fb1401e0">  807</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">year</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160;        {</div><div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">year</a>());</div><div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160;        }</div><div class="line"><a name="l00813"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a51f370518c43c31e219a67b927bbeb91">  813</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">extended_year</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00814"></a><span class="lineno">  814</span>&#160;        {</div><div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">extended_year</a>());</div><div class="line"><a name="l00816"></a><span class="lineno">  816</span>&#160;        }</div><div class="line"><a name="l00818"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a8bacd973219a934023c5c0acd901b987">  818</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160;        {</div><div class="line"><a name="l00820"></a><span class="lineno">  820</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>());</div><div class="line"><a name="l00821"></a><span class="lineno">  821</span>&#160;        }</div><div class="line"><a name="l00823"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a31d0373ed7e81d7ceb5b55472b65b47c">  823</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">day</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160;        {</div><div class="line"><a name="l00825"></a><span class="lineno">  825</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">day</a>());</div><div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160;        }</div><div class="line"><a name="l00828"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a4223191400174ceb748a0a500f04c85b">  828</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">day_of_year</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;        {</div><div class="line"><a name="l00830"></a><span class="lineno">  830</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">day_of_year</a>());</div><div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;        }</div><div class="line"><a name="l00838"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a0ad06c97a5719507b62c73eb21a1d7d8">  838</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160;        {</div><div class="line"><a name="l00840"></a><span class="lineno">  840</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>());</div><div class="line"><a name="l00841"></a><span class="lineno">  841</span>&#160;        }</div><div class="line"><a name="l00845"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aff80ffdef5ae43dad1ae0b93102a58f2">  845</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">day_of_week_in_month</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00846"></a><span class="lineno">  846</span>&#160;        {</div><div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">day_of_week_in_month</a>());</div><div class="line"><a name="l00848"></a><span class="lineno">  848</span>&#160;        }</div><div class="line"><a name="l00851"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a9922218132f453d43446e82554e97cb4">  851</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">day_of_week_local</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00852"></a><span class="lineno">  852</span>&#160;        {</div><div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">day_of_week_local</a>());</div><div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;        }</div><div class="line"><a name="l00856"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#af52108fa564fd9191b1191dd368256d5">  856</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">hour</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00857"></a><span class="lineno">  857</span>&#160;        {</div><div class="line"><a name="l00858"></a><span class="lineno">  858</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">hour</a>());</div><div class="line"><a name="l00859"></a><span class="lineno">  859</span>&#160;        }</div><div class="line"><a name="l00861"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a7ff9ca3b6be4eede78e367603c16ae4c">  861</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">hour_12</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00862"></a><span class="lineno">  862</span>&#160;        {</div><div class="line"><a name="l00863"></a><span class="lineno">  863</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">hour_12</a>());</div><div class="line"><a name="l00864"></a><span class="lineno">  864</span>&#160;        }</div><div class="line"><a name="l00866"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ad36d2f5e0b8df78a8f13bbe5ab0446e9">  866</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;        {</div><div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a>());</div><div class="line"><a name="l00869"></a><span class="lineno">  869</span>&#160;        }</div><div class="line"><a name="l00871"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a48d45a9609e9d015f756da56d5edcdff">  871</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">minute</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;        {</div><div class="line"><a name="l00873"></a><span class="lineno">  873</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">minute</a>());</div><div class="line"><a name="l00874"></a><span class="lineno">  874</span>&#160;        }</div><div class="line"><a name="l00876"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a7b929adb5728ae6b2eb1abbd3140d21c">  876</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">second</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160;        {</div><div class="line"><a name="l00878"></a><span class="lineno">  878</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">second</a>());</div><div class="line"><a name="l00879"></a><span class="lineno">  879</span>&#160;        }</div><div class="line"><a name="l00881"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ad87b4b80ff5b22373ecbf62debc1c8cf">  881</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">week_of_year</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00882"></a><span class="lineno">  882</span>&#160;        {</div><div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">week_of_year</a>());</div><div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;        }</div><div class="line"><a name="l00886"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a68b0dca7c16e8325ecb96c4ee1d1b4d8">  886</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">week_of_month</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;        {</div><div class="line"><a name="l00888"></a><span class="lineno">  888</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">week_of_month</a>());</div><div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;        }</div><div class="line"><a name="l00892"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aad79f5a6fc953cf2e65a9b8ece18bb0e">  892</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">first_day_of_week</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time.html">date_time</a>&amp; dt)</div><div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160;        {</div><div class="line"><a name="l00894"></a><span class="lineno">  894</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">first_day_of_week</a>());</div><div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;        }</div><div class="line"><a name="l00896"></a><span class="lineno">  896</span>&#160;</div><div class="line"><a name="l00899"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aa8ca4022eb5465964ac6feba2e4bda68">  899</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">era</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00900"></a><span class="lineno">  900</span>&#160;        {</div><div class="line"><a name="l00901"></a><span class="lineno">  901</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">era</a>());</div><div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160;        }</div><div class="line"><a name="l00904"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a305d934f23f06b16330c41ca9cc47637">  904</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">year</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00905"></a><span class="lineno">  905</span>&#160;        {</div><div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">year</a>());</div><div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;        }</div><div class="line"><a name="l00910"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a2a27eb8aaf25c260c9403d8ab62e52bc">  910</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">extended_year</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;        {</div><div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">extended_year</a>());</div><div class="line"><a name="l00913"></a><span class="lineno">  913</span>&#160;        }</div><div class="line"><a name="l00915"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab46e501097767030605584bc733b3b28">  915</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;        {</div><div class="line"><a name="l00917"></a><span class="lineno">  917</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a>());</div><div class="line"><a name="l00918"></a><span class="lineno">  918</span>&#160;        }</div><div class="line"><a name="l00920"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ae19429b6c84a3b6816299182a10c8efa">  920</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">day</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00921"></a><span class="lineno">  921</span>&#160;        {</div><div class="line"><a name="l00922"></a><span class="lineno">  922</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">day</a>());</div><div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;        }</div><div class="line"><a name="l00925"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#af5c34cbf5ed47ed873440b9ab583133b">  925</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">day_of_year</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00926"></a><span class="lineno">  926</span>&#160;        {</div><div class="line"><a name="l00927"></a><span class="lineno">  927</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">day_of_year</a>());</div><div class="line"><a name="l00928"></a><span class="lineno">  928</span>&#160;        }</div><div class="line"><a name="l00930"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ab24c7a6767638388c6ec73aadb8ef279">  930</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00931"></a><span class="lineno">  931</span>&#160;        {</div><div class="line"><a name="l00932"></a><span class="lineno">  932</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a>());</div><div class="line"><a name="l00933"></a><span class="lineno">  933</span>&#160;        }</div><div class="line"><a name="l00936"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a2322c3643f94039534dbe51b0cae0d78">  936</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">day_of_week_in_month</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00937"></a><span class="lineno">  937</span>&#160;        {</div><div class="line"><a name="l00938"></a><span class="lineno">  938</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">day_of_week_in_month</a>());</div><div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;        }</div><div class="line"><a name="l00941"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#adf15b4ddef13c97527625e4529b064aa">  941</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">day_of_week_local</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00942"></a><span class="lineno">  942</span>&#160;        {</div><div class="line"><a name="l00943"></a><span class="lineno">  943</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">day_of_week_local</a>());</div><div class="line"><a name="l00944"></a><span class="lineno">  944</span>&#160;        }</div><div class="line"><a name="l00946"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a76076ff327a9253b50c184c6bce49467">  946</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">hour</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00947"></a><span class="lineno">  947</span>&#160;        {</div><div class="line"><a name="l00948"></a><span class="lineno">  948</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">hour</a>());</div><div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;        }</div><div class="line"><a name="l00951"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aff94a06785624362b343353d0a155d6d">  951</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">hour_12</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00952"></a><span class="lineno">  952</span>&#160;        {</div><div class="line"><a name="l00953"></a><span class="lineno">  953</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">hour_12</a>());</div><div class="line"><a name="l00954"></a><span class="lineno">  954</span>&#160;        }</div><div class="line"><a name="l00956"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a73ac683dcb58698a46e28413b13c43e5">  956</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;        {</div><div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a>());</div><div class="line"><a name="l00959"></a><span class="lineno">  959</span>&#160;        }</div><div class="line"><a name="l00961"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#aa2109020ceca6fd45f144f3fa0878dae">  961</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">minute</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;        {</div><div class="line"><a name="l00963"></a><span class="lineno">  963</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">minute</a>());</div><div class="line"><a name="l00964"></a><span class="lineno">  964</span>&#160;        }</div><div class="line"><a name="l00966"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a1c2981b3d0ec30691695cb1d5f527ae0">  966</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">second</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00967"></a><span class="lineno">  967</span>&#160;        {</div><div class="line"><a name="l00968"></a><span class="lineno">  968</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">second</a>());</div><div class="line"><a name="l00969"></a><span class="lineno">  969</span>&#160;        }</div><div class="line"><a name="l00971"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#acaf9188e417af350cc43d6ae5418b768">  971</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">week_of_year</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;        {</div><div class="line"><a name="l00973"></a><span class="lineno">  973</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">week_of_year</a>());</div><div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;        }</div><div class="line"><a name="l00976"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#a0f1af74a7a6f537fa4b63d49138a0719">  976</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">week_of_month</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;        {</div><div class="line"><a name="l00978"></a><span class="lineno">  978</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">week_of_month</a>());</div><div class="line"><a name="l00979"></a><span class="lineno">  979</span>&#160;        }</div><div class="line"><a name="l00981"></a><span class="lineno"><a class="line" href="namespaceboost_1_1locale_1_1period.html#ad24c3c782bd5844aa116ebcbe0457794">  981</a></span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">first_day_of_week</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&amp; dt)</div><div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;        {</div><div class="line"><a name="l00983"></a><span class="lineno">  983</span>&#160;            <span class="keywordflow">return</span> dt.<a class="code" href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">get</a>(<a class="code" href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">first_day_of_week</a>());</div><div class="line"><a name="l00984"></a><span class="lineno">  984</span>&#160;        }</div><div class="line"><a name="l00985"></a><span class="lineno">  985</span>&#160;</div><div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;    } <span class="comment">// namespace period</span></div><div class="line"><a name="l00987"></a><span class="lineno">  987</span>&#160;</div><div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;</div><div class="line"><a name="l00990"></a><span class="lineno">  990</span>&#160;}} <span class="comment">// namespace boost::locale</span></div><div class="line"><a name="l00991"></a><span class="lineno">  991</span>&#160;</div><div class="line"><a name="l00992"></a><span class="lineno">  992</span>&#160;<span class="preprocessor">#ifdef BOOST_MSVC</span></div><div class="line"><a name="l00993"></a><span class="lineno">  993</span>&#160;<span class="preprocessor">#    pragma warning(pop)</span></div><div class="line"><a name="l00994"></a><span class="lineno">  994</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;</div><div class="line"><a name="l01001"></a><span class="lineno"> 1001</span>&#160;</div><div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="classboost_1_1locale_1_1date__time_html_abb1fb403d1571d7973186916cc76686e"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#abb1fb403d1571d7973186916cc76686e">boost::locale::date_time::operator&gt;&gt;=</a></div><div class="ttdeci">date_time &amp; operator&gt;&gt;=(period::period_type f)</div><div class="ttdoc">roll backward a date by single period f.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:627</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a61b2390a32e15c6aa2c26bc06d21a20c"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">boost::locale::period::am_pm</a></div><div class="ttdeci">period_type am_pm()</div><div class="ttdoc">Get period_type for: am or pm marker [0..1].</div><div class="ttdef"><b>Definition:</b> date_time.hpp:122</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_abc5b3fccdb3b2d2c673b48380a2434bb"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#abc5b3fccdb3b2d2c673b48380a2434bb">boost::locale::date_time::get</a></div><div class="ttdeci">int get(period::period_type f) const</div><div class="ttdoc">get specific period f value</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a4cb28c5353004068b73d0f12136bbfe9"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">boost::locale::period::week_of_year</a></div><div class="ttdeci">period_type week_of_year()</div><div class="ttdoc">Get period_type for: The week number in the year.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:137</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a0076efc591f6341ef785f49422b6fa89"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">boost::locale::period::minute</a></div><div class="ttdeci">period_type minute()</div><div class="ttdoc">Get period_type for: minute [0..59].</div><div class="ttdef"><b>Definition:</b> date_time.hpp:127</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa91d7d091168f60438e08ecfb72a47aed"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa91d7d091168f60438e08ecfb72a47aed">boost::locale::period::marks::week_of_month</a></div><div class="ttdoc">The week number within current month.</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:50</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__error_html_a25580392651d96c752a67f01ea853542"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__error.html#a25580392651d96c752a67f01ea853542">boost::locale::date_time_error::date_time_error</a></div><div class="ttdeci">date_time_error(const std::string &amp;e)</div><div class="ttdoc">Constructor of date_time_error class.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:33</div></div>
<div class="ttc" id="structboost_1_1locale_1_1date__time__period_html_aa6511600eb5264c8597f700668e9c628"><div class="ttname"><a href="structboost_1_1locale_1_1date__time__period.html#aa6511600eb5264c8597f700668e9c628">boost::locale::date_time_period::type</a></div><div class="ttdeci">period::period_type type</div><div class="ttdoc">The type of period, i.e. era, year, day etc.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:43</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aada04317134ebd8c192c431fdc6776756"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aada04317134ebd8c192c431fdc6776756">boost::locale::period::marks::day_of_week_in_month</a></div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:41</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_aeb2dfdc73c6d16796360eca141654aba"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">boost::locale::period::extended_year</a></div><div class="ttdeci">period_type extended_year()</div><div class="ttdoc">Get period_type for: Extended year for Gregorian/Julian calendars, where 1 BC == 0,...</div><div class="ttdef"><b>Definition:</b> date_time.hpp:71</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ac16b397e27e29fe6483540910e8ade3a"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ac16b397e27e29fe6483540910e8ade3a">boost::locale::period::saturday</a></div><div class="ttdeci">date_time_period saturday()</div><div class="ttdoc">Get predefined constant for Saturday.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:336</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__period__set_html_a635b8d91c4c8da99857810e42a0aff65"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__period__set.html#a635b8d91c4c8da99857810e42a0aff65">boost::locale::date_time_period_set::date_time_period_set</a></div><div class="ttdeci">date_time_period_set()</div><div class="ttdoc">Default constructor - empty set.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:398</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_a4af6593b60bf965b9138a564644ab66a"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#a4af6593b60bf965b9138a564644ab66a">boost::locale::date_time::operator+=</a></div><div class="ttdeci">date_time &amp; operator+=(period::period_type f)</div><div class="ttdoc">add single period f to the current date_time</div><div class="ttdef"><b>Definition:</b> date_time.hpp:616</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__duration_html_af18b87186d2a9343069b1186fd062e2f"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__duration.html#af18b87186d2a9343069b1186fd062e2f">boost::locale::date_time_duration::get</a></div><div class="ttdeci">int get(period::period_type f) const</div><div class="ttdoc">find a difference in terms of period_type f</div><div class="ttdef"><b>Definition:</b> date_time.hpp:775</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aaefcf18fd66bcbbac887950c40d5e039b"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aaefcf18fd66bcbbac887950c40d5e039b">boost::locale::period::marks::day_of_week</a></div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:36</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa57de42317988a555460cf98be583addc"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa57de42317988a555460cf98be583addc">boost::locale::period::marks::year</a></div><div class="ttdoc">Year, it is calendar specific, for example 2011 in Gregorian calendar.</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:31</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__period__set_html"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__period__set.html">boost::locale::date_time_period_set</a></div><div class="ttdoc">this class that represents a set of periods,</div><div class="ttdef"><b>Definition:</b> date_time.hpp:395</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_a3bd340d8382b034ac1c90c9447ca7713"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#a3bd340d8382b034ac1c90c9447ca7713">boost::locale::date_time::time</a></div><div class="ttdeci">double time() const</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a6073ebcf60bf690662c3a9d113b49e9b"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a6073ebcf60bf690662c3a9d113b49e9b">boost::locale::period::january</a></div><div class="ttdeci">date_time_period january()</div><div class="ttdoc">Get predefined constant for January.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:245</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_ab249f9229b2de6fc9d1afc818bb1d0dc"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#ab249f9229b2de6fc9d1afc818bb1d0dc">boost::locale::date_time::operator&lt;&lt;=</a></div><div class="ttdeci">date_time &amp; operator&lt;&lt;=(period::period_type f)</div><div class="ttdoc">roll forward a date by single period f.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:625</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ab2810f9718b18b77e47b4d23f94589ae"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ab2810f9718b18b77e47b4d23f94589ae">boost::locale::period::november</a></div><div class="ttdeci">date_time_period november()</div><div class="ttdoc">Get predefined constant for November.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:295</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__duration_html"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__duration.html">boost::locale::date_time_duration</a></div><div class="ttdoc">This class represents a period: a pair of two date_time objects.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:768</div></div>
<div class="ttc" id="group__date__time_html_ga5fb5bf4b94dcd67f45564e8015b57c20"><div class="ttname"><a href="group__date__time.html#ga5fb5bf4b94dcd67f45564e8015b57c20">boost::locale::operator+</a></div><div class="ttdeci">date_time_period_set operator+(const date_time_period_set &amp;a, const date_time_period_set &amp;b)</div><div class="ttdoc">Append two periods sets. Note this operator is not commutative.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:447</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__period__set_html_a47b0a2759cbacd296d91929ddd453dcb"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__period__set.html#a47b0a2759cbacd296d91929ddd453dcb">boost::locale::date_time_period_set::operator[]</a></div><div class="ttdeci">const date_time_period &amp; operator[](size_t n) const</div><div class="ttdoc">Get item at position n the set, n should be in range [0,size)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:431</div></div>
<div class="ttc" id="namespaceboost_1_1locale_html_aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113"><div class="ttname"><a href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113">boost::locale::category_t::calendar</a></div><div class="ttdoc">Generate boundary analysis facet.</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ac0d844780c28dc783879d82eaa192961"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ac0d844780c28dc783879d82eaa192961">boost::locale::period::march</a></div><div class="ttdeci">date_time_period march()</div><div class="ttdoc">Get predefined constant for March.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:255</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ac1b424cae5ed4ab32aed3c3aedc306e9"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">boost::locale::period::day_of_week</a></div><div class="ttdeci">period_type day_of_week()</div><div class="ttdef"><b>Definition:</b> date_time.hpp:96</div></div>
<div class="ttc" id="group__boundary_html_gaaf63b3de94f3146ba8c15f0374a100db"><div class="ttname"><a href="group__boundary.html#gaaf63b3de94f3146ba8c15f0374a100db">boost::locale::boundary::operator&gt;</a></div><div class="ttdeci">bool operator&gt;(const segment&lt; IteratorL &gt; &amp;l, const segment&lt; IteratorR &gt; &amp;r)</div><div class="ttdoc">Compare two segments.</div><div class="ttdef"><b>Definition:</b> segment.hpp:178</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa6e0f5f21ddfe455d27dfdafb5e58796b"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa6e0f5f21ddfe455d27dfdafb5e58796b">boost::locale::period::marks::day_of_year</a></div><div class="ttdoc">The number of day in year, starting from 1, in Gregorian [1..366].</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:35</div></div>
<div class="ttc" id="group__boundary_html_ga9ae9b79530f58d95aebde8a221461130"><div class="ttname"><a href="group__boundary.html#ga9ae9b79530f58d95aebde8a221461130">boost::locale::boundary::operator==</a></div><div class="ttdeci">bool operator==(const BaseIterator &amp;l, const boundary_point&lt; BaseIterator &gt; &amp;r)</div><div class="ttdoc">Check if the boundary point r points to same location as an iterator l.</div><div class="ttdef"><b>Definition:</b> boundary_point.hpp:88</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a45ad1aeaf7a0e62ef26a42adca38da70"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a45ad1aeaf7a0e62ef26a42adca38da70">boost::locale::period::monday</a></div><div class="ttdeci">date_time_period monday()</div><div class="ttdoc">Get predefined constant for Monday.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:311</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ab0610583a720120e8dcf90d0fe01cb01"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ab0610583a720120e8dcf90d0fe01cb01">boost::locale::period::february</a></div><div class="ttdeci">date_time_period february()</div><div class="ttdoc">Get predefined constant for February.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:250</div></div>
<div class="ttc" id="group__boundary_html_ga1730e6426d9030c42f8d4987f2591aae"><div class="ttname"><a href="group__boundary.html#ga1730e6426d9030c42f8d4987f2591aae">boost::locale::boundary::operator&lt;</a></div><div class="ttdeci">bool operator&lt;(const segment&lt; IteratorL &gt; &amp;l, const segment&lt; IteratorR &gt; &amp;r)</div><div class="ttdoc">Compare two segments.</div><div class="ttdef"><b>Definition:</b> segment.hpp:166</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a1b4663ee24f7687e592f0b790e8df494"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a1b4663ee24f7687e592f0b790e8df494">boost::locale::period::april</a></div><div class="ttdeci">date_time_period april()</div><div class="ttdoc">Get predefined constant for April.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:260</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a1569da2e8680ef5d3409361069905541"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">boost::locale::period::first_day_of_week</a></div><div class="ttdeci">period_type first_day_of_week()</div><div class="ttdoc">Get period_type for: First day of week, constant, for example Sunday in US = 1, Monday in France = 2.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:147</div></div>
<div class="ttc" id="classboost_1_1locale_1_1calendar_html"><div class="ttname"><a href="classboost_1_1locale_1_1calendar.html">boost::locale::calendar</a></div><div class="ttdoc">this class provides an access to general calendar information.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:469</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ab5d2e72c7d5b842c26af29af4c96a853"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ab5d2e72c7d5b842c26af29af4c96a853">boost::locale::period::august</a></div><div class="ttdeci">date_time_period august()</div><div class="ttdoc">Get predefined constant for August.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:280</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__duration_html_aac2e7c14858dcde54baca11d26d2dd54"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__duration.html#aac2e7c14858dcde54baca11d26d2dd54">boost::locale::date_time_duration::operator/</a></div><div class="ttdeci">int operator/(period::period_type f) const</div><div class="ttdoc">Syntactic sugar for get(f)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:777</div></div>
<div class="ttc" id="structboost_1_1locale_1_1date__time__period_html_a4db4cc5a2c415dc54ab5d67185881900"><div class="ttname"><a href="structboost_1_1locale_1_1date__time__period.html#a4db4cc5a2c415dc54ab5d67185881900">boost::locale::date_time_period::operator+</a></div><div class="ttdeci">date_time_period operator+() const</div><div class="ttdoc">Operator + returns copy of itself.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:46</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a683b393abc6276f3d77289af0f5d6404"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a683b393abc6276f3d77289af0f5d6404">boost::locale::period::am</a></div><div class="ttdeci">date_time_period am()</div><div class="ttdoc">Get predefined constant for AM (Ante Meridiem)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:341</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ad0e376bf63fb32da0dad13b0c4a6fef1"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ad0e376bf63fb32da0dad13b0c4a6fef1">boost::locale::period::october</a></div><div class="ttdeci">date_time_period october()</div><div class="ttdoc">Get predefined constant for October.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:290</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_ac538193b90000b90b20792b87a42ab72"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#ac538193b90000b90b20792b87a42ab72">boost::locale::date_time::operator&lt;&lt;</a></div><div class="ttdeci">date_time operator&lt;&lt;(period::period_type f) const</div><div class="ttdoc">roll forward a date by single period f.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:621</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__period__set_html_a0e70247c1738dbf6869e6d8c04461893"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__period__set.html#a0e70247c1738dbf6869e6d8c04461893">boost::locale::date_time_period_set::add</a></div><div class="ttdeci">void add(date_time_period f)</div><div class="ttdoc">Append date_time_period f to the set.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:407</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_a3d66a4e6adfcc369c03626c8cd7429b7"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#a3d66a4e6adfcc369c03626c8cd7429b7">boost::locale::date_time::operator+</a></div><div class="ttdeci">date_time operator+(period::period_type f) const</div><div class="ttdoc">add single period f to the current date_time</div><div class="ttdef"><b>Definition:</b> date_time.hpp:612</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_a8d00584d1f1b2dc0f9b44ef53e2a15e9"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#a8d00584d1f1b2dc0f9b44ef53e2a15e9">boost::locale::date_time::difference</a></div><div class="ttdeci">int difference(const date_time &amp;other, period::period_type f) const</div><div class="ttdoc">calculate the distance from this date_time to other in terms of periods f</div></div>
<div class="ttc" id="group__boundary_html_ga45c63976f13515c35b514cd0871a7f32"><div class="ttname"><a href="group__boundary.html#ga45c63976f13515c35b514cd0871a7f32">boost::locale::boundary::operator&gt;=</a></div><div class="ttdeci">bool operator&gt;=(const segment&lt; IteratorL &gt; &amp;l, const segment&lt; IteratorR &gt; &amp;r)</div><div class="ttdoc">Compare two segments.</div><div class="ttdef"><b>Definition:</b> segment.hpp:184</div></div>
<div class="ttc" id="group__boundary_html_ga64c5f3099bc9f79d468b3e9b91d9cb0e"><div class="ttname"><a href="group__boundary.html#ga64c5f3099bc9f79d468b3e9b91d9cb0e">boost::locale::boundary::operator!=</a></div><div class="ttdeci">bool operator!=(const BaseIterator &amp;l, const boundary_point&lt; BaseIterator &gt; &amp;r)</div><div class="ttdoc">Check if the boundary point r points to different location from an iterator l.</div><div class="ttdef"><b>Definition:</b> boundary_point.hpp:94</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_aad0c72b6aa0ade2e71a71223eefab6fd"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#aad0c72b6aa0ade2e71a71223eefab6fd">boost::locale::period::december</a></div><div class="ttdeci">date_time_period december()</div><div class="ttdoc">Get predefined constant for December.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:300</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a62d0745dbb555066a7281b9c805ceab7"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">boost::locale::period::era</a></div><div class="ttdeci">period_type era()</div><div class="ttdoc">Get period_type for: Era i.e. AC, BC in Gregorian and Julian calendar, range [0,1].</div><div class="ttdef"><b>Definition:</b> date_time.hpp:61</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a35f2ca900e3cda757c4598b686ca5969"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">boost::locale::period::hour</a></div><div class="ttdeci">period_type hour()</div><div class="ttdoc">Get period_type for: 24 clock hour [0..23].</div><div class="ttdef"><b>Definition:</b> date_time.hpp:112</div></div>
<div class="ttc" id="classboost_1_1locale_1_1hold__ptr_html"><div class="ttname"><a href="classboost_1_1locale_1_1hold__ptr.html">boost::locale::hold_ptr</a></div><div class="ttdoc">a smart pointer similar to std::unique_ptr but the underlying object has the same constness as the po...</div><div class="ttdef"><b>Definition:</b> hold_ptr.hpp:16</div></div>
<div class="ttc" id="group__date__time_html_ga0079bd8236eee75c4b5baa8497959b78"><div class="ttname"><a href="group__date__time.html#ga0079bd8236eee75c4b5baa8497959b78">boost::locale::operator-</a></div><div class="ttdeci">date_time_period_set operator-(const date_time_period_set &amp;a, const date_time_period_set &amp;b)</div><div class="ttdoc">Append two period sets when all periods of set change their sign.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:456</div></div>
<div class="ttc" id="classboost_1_1locale_1_1ios__info_html_a18b06a5cf88e25361bf188f64216edf2"><div class="ttname"><a href="classboost_1_1locale_1_1ios__info.html#a18b06a5cf88e25361bf188f64216edf2">boost::locale::ios_info::display_flags</a></div><div class="ttdeci">void display_flags(uint64_t flags)</div><div class="ttdoc">Set flags that define how to format data, e.g. number, spell, currency etc.</div></div>
<div class="ttc" id="classboost_1_1locale_1_1ios__info_html_a02f6979dffc2df97c3612d72b7c7241b"><div class="ttname"><a href="classboost_1_1locale_1_1ios__info.html#a02f6979dffc2df97c3612d72b7c7241b">boost::locale::ios_info::get</a></div><div class="ttdeci">static ios_info &amp; get(std::ios_base &amp;ios)</div><div class="ttdoc">Get ios_info instance for specific stream object.</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa779ca20cc3ae59a2421293f5d8023c2e"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa779ca20cc3ae59a2421293f5d8023c2e">boost::locale::period::marks::week_of_year</a></div><div class="ttdoc">The week number in the year.</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:49</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa889701bc6cf11a9ee39d7223691966e1"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa889701bc6cf11a9ee39d7223691966e1">boost::locale::period::marks::hour</a></div><div class="ttdoc">24 clock hour [0..23]</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:44</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aab24556edbe246e0bc3b4b0d3f2e627eb"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aab24556edbe246e0bc3b4b0d3f2e627eb">boost::locale::period::marks::invalid</a></div><div class="ttdoc">Special invalid value, should not be used directly.</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:29</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aac944d440d0afd38cc75c1011319f790e"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aac944d440d0afd38cc75c1011319f790e">boost::locale::period::marks::day</a></div><div class="ttdoc">The day of month, calendar specific, in Gregorian [1..31].</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:34</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a57d6b8f97bd6604e13c6982ed0953678"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">boost::locale::period::day_of_week_local</a></div><div class="ttdeci">period_type day_of_week_local()</div><div class="ttdoc">Get period_type for: Local day of week, for example in France Monday is 1, in US Sunday is 1,...</div><div class="ttdef"><b>Definition:</b> date_time.hpp:107</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa1fec41808558d9452161712537717906"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa1fec41808558d9452161712537717906">boost::locale::period::marks::minute</a></div><div class="ttdoc">minute [0..59]</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:47</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa1fe197c6bcc18839b75fc550857ed89a"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa1fe197c6bcc18839b75fc550857ed89a">boost::locale::period::marks::month</a></div><div class="ttdoc">The month of year, calendar specific, in Gregorian [0..11].</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:33</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa16f1add720e3211b165609cc8bf49edf"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa16f1add720e3211b165609cc8bf49edf">boost::locale::period::marks::day_of_week_local</a></div><div class="ttdoc">Local day of week, for example in France Monday is 1, in US Sunday is 1, [1..7].</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:43</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a9c5588c1059eff9e9ab8034f7442bf00"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a9c5588c1059eff9e9ab8034f7442bf00">boost::locale::period::operator-</a></div><div class="ttdeci">date_time_period operator-(period::period_type f)</div><div class="ttdoc">convert period_type to date_time_period(f,-1)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:357</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a64a8e83513d867a5356cacc596d03962"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a64a8e83513d867a5356cacc596d03962">boost::locale::period::operator *</a></div><div class="ttdeci">date_time_period operator *(period::period_type f, T v)</div><div class="ttdoc">Create date_time_period of type f with value v.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:364</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a083c271b3bbd29e4644b59fb3e34a4d7"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">boost::locale::period::day_of_week_in_month</a></div><div class="ttdeci">period_type day_of_week_in_month()</div><div class="ttdef"><b>Definition:</b> date_time.hpp:102</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa33760c5f41e6cb76695f18d65f5d0a9d"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa33760c5f41e6cb76695f18d65f5d0a9d">boost::locale::period::marks::hour_12</a></div><div class="ttdoc">12 clock hour [0..11]</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:45</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_aefda6042554834d3455e680de596d577"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#aefda6042554834d3455e680de596d577">boost::locale::date_time::operator-</a></div><div class="ttdeci">date_time operator-(period::period_type f) const</div><div class="ttdoc">subtract single period f from the current date_time</div><div class="ttdef"><b>Definition:</b> date_time.hpp:614</div></div>
<div class="ttc" id="group__date__time_html_gadac52301b5dbc9383cc35610417802a3"><div class="ttname"><a href="group__date__time.html#gadac52301b5dbc9383cc35610417802a3">boost::locale::operator&lt;&lt;</a></div><div class="ttdeci">std::basic_ostream&lt; CharType &gt; &amp; operator&lt;&lt;(std::basic_ostream&lt; CharType &gt; &amp;out, const date_time &amp;t)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:719</div></div>
<div class="ttc" id="structboost_1_1locale_1_1date__time__period_html"><div class="ttname"><a href="structboost_1_1locale_1_1date__time__period.html">boost::locale::date_time_period</a></div><div class="ttdoc">This class represents a pair of period_type and the integer values that describes its amount....</div><div class="ttdef"><b>Definition:</b> date_time.hpp:42</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a5ee88cdab049350f1346466b2da6ccf0"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a5ee88cdab049350f1346466b2da6ccf0">boost::locale::period::pm</a></div><div class="ttdeci">date_time_period pm()</div><div class="ttdoc">Get predefined constant for PM (Post Meridiem)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:346</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a72438d8d7da8493457e043aa442f0d9d"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">boost::locale::period::day</a></div><div class="ttdeci">period_type day()</div><div class="ttdoc">Get period_type for: The day of month, calendar specific, in Gregorian [1..31].</div><div class="ttdef"><b>Definition:</b> date_time.hpp:81</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a9ea8d1453bed512ee16bea3199fd92af"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">boost::locale::period::year</a></div><div class="ttdeci">period_type year()</div><div class="ttdoc">Get period_type for: Year, it is calendar specific, for example 2011 in Gregorian calendar.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:66</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ab01f299cdb64c780cadca7d64f87cd5f"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">boost::locale::period::week_of_month</a></div><div class="ttdeci">period_type week_of_month()</div><div class="ttdoc">Get period_type for: The week number within current month.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:142</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a856d680245af08ff3bd1618817a90ef1"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a856d680245af08ff3bd1618817a90ef1">boost::locale::period::wednesday</a></div><div class="ttdeci">date_time_period wednesday()</div><div class="ttdoc">Get predefined constant for Wednesday.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:321</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__error_html"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__error.html">boost::locale::date_time_error</a></div><div class="ttdoc">This error is thrown in case of invalid state that occurred.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:30</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ab03ddec166d072a7465e87dbaccc1389"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ab03ddec166d072a7465e87dbaccc1389">boost::locale::period::thursday</a></div><div class="ttdeci">date_time_period thursday()</div><div class="ttdoc">Get predefined constant for Thursday.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:326</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa8c4f7415895e44b3c1c9484a8eadcf06"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa8c4f7415895e44b3c1c9484a8eadcf06">boost::locale::period::marks::am_pm</a></div><div class="ttdoc">am or pm marker [0..1]</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:46</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__period__set_html_a0966962f5da78fc06121d1455efcbbae"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__period__set.html#a0966962f5da78fc06121d1455efcbbae">boost::locale::date_time_period_set::date_time_period_set</a></div><div class="ttdeci">date_time_period_set(period::period_type f)</div><div class="ttdoc">Create a set of single period with value 1.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:401</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__duration_html_a4556447fc56ce359e2e207da2bbd25bf"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__duration.html#a4556447fc56ce359e2e207da2bbd25bf">boost::locale::date_time_duration::date_time_duration</a></div><div class="ttdeci">date_time_duration(const date_time &amp;first, const date_time &amp;second)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:772</div></div>
<div class="ttc" id="group__boundary_html_ga07d3ee374afea084c1bc8efa5fb5aba9"><div class="ttname"><a href="group__boundary.html#ga07d3ee374afea084c1bc8efa5fb5aba9">boost::locale::boundary::operator&lt;=</a></div><div class="ttdeci">bool operator&lt;=(const segment&lt; IteratorL &gt; &amp;l, const segment&lt; IteratorR &gt; &amp;r)</div><div class="ttdoc">Compare two segments.</div><div class="ttdef"><b>Definition:</b> segment.hpp:172</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__duration_html_a8570e450ad9c900a4dd39235b8e61909"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__duration.html#a8570e450ad9c900a4dd39235b8e61909">boost::locale::date_time_duration::start</a></div><div class="ttdeci">const date_time &amp; start() const</div><div class="ttdoc">Get starting point.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:780</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_a04f0b2a8b8444e8b603ebbf58ef3a4e3"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#a04f0b2a8b8444e8b603ebbf58ef3a4e3">boost::locale::date_time::operator-=</a></div><div class="ttdeci">date_time &amp; operator-=(period::period_type f)</div><div class="ttdoc">subtract single period f from the current date_time</div><div class="ttdef"><b>Definition:</b> date_time.hpp:618</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a12e3cce2dc169b65062c7fadd1143b0b"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a12e3cce2dc169b65062c7fadd1143b0b">boost::locale::period::sunday</a></div><div class="ttdeci">date_time_period sunday()</div><div class="ttdoc">Get predefined constant for Sunday.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:306</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__period__set_html_a14b5c2e199b3458d5faee0dc572b2f28"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__period__set.html#a14b5c2e199b3458d5faee0dc572b2f28">boost::locale::date_time_period_set::size</a></div><div class="ttdeci">size_t size() const</div><div class="ttdoc">Get number if items in list.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:417</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a26ef9fa6f6df065606a36c9b42e165eb"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a26ef9fa6f6df065606a36c9b42e165eb">boost::locale::period::friday</a></div><div class="ttdeci">date_time_period friday()</div><div class="ttdoc">Get predefined constant for Friday.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:331</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__duration_html_a77e0946de974eeeb60d8aa3f6d174c93"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__duration.html#a77e0946de974eeeb60d8aa3f6d174c93">boost::locale::date_time_duration::end</a></div><div class="ttdeci">const date_time &amp; end() const</div><div class="ttdoc">Get ending point.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:782</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a2426159fe1f6cb25f2598f35adf14267"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a2426159fe1f6cb25f2598f35adf14267">boost::locale::period::tuesday</a></div><div class="ttdeci">date_time_period tuesday()</div><div class="ttdoc">Get predefined constant for Tuesday.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:316</div></div>
<div class="ttc" id="structboost_1_1locale_1_1date__time__period_html_aec776b16ed46a22833308a4112886ca4"><div class="ttname"><a href="structboost_1_1locale_1_1date__time__period.html#aec776b16ed46a22833308a4112886ca4">boost::locale::date_time_period::value</a></div><div class="ttdeci">int value</div><div class="ttdef"><b>Definition:</b> date_time.hpp:44</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a1399317fdf67b50ec11aa9298176ab70"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a1399317fdf67b50ec11aa9298176ab70">boost::locale::period::operator+</a></div><div class="ttdeci">date_time_period operator+(period::period_type f)</div><div class="ttdoc">convert period_type to date_time_period(f,1)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:352</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a33e67d3354486021fa1e7076d30d51a4"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">boost::locale::period::day_of_year</a></div><div class="ttdeci">period_type day_of_year()</div><div class="ttdoc">Get period_type for: The number of day in year, starting from 1, in Gregorian [1.....</div><div class="ttdef"><b>Definition:</b> date_time.hpp:86</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time__period__set_html_a5867088d009104d8d3725d52ad0ac2bd"><div class="ttname"><a href="classboost_1_1locale_1_1date__time__period__set.html#a5867088d009104d8d3725d52ad0ac2bd">boost::locale::date_time_period_set::date_time_period_set</a></div><div class="ttdeci">date_time_period_set(const date_time_period &amp;fl)</div><div class="ttdoc">Create a set of single period fl.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:404</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aacc399d4cddd6d129a7d36cc2ce94b47a"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aacc399d4cddd6d129a7d36cc2ce94b47a">boost::locale::period::marks::era</a></div><div class="ttdoc">Era i.e. AC, BC in Gregorian and Julian calendar, range [0,1].</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:30</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa8cc9f718b5bdf9c30cff3846118039bd"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa8cc9f718b5bdf9c30cff3846118039bd">boost::locale::period::marks::first_day_of_week</a></div><div class="ttdoc">First day of week, constant, for example Sunday in US = 1, Monday in France = 2.</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:51</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_af96ab88b9d168801bfde95c7ad24613a"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">boost::locale::period::hour_12</a></div><div class="ttdeci">period_type hour_12()</div><div class="ttdoc">Get period_type for: 12 clock hour [0..11].</div><div class="ttdef"><b>Definition:</b> date_time.hpp:117</div></div>
<div class="ttc" id="group__date__time_html_gaf6e30351fc67e887e37853723c228484"><div class="ttname"><a href="group__date__time.html#gaf6e30351fc67e887e37853723c228484">boost::locale::operator&gt;&gt;</a></div><div class="ttdeci">std::basic_istream&lt; CharType &gt; &amp; operator&gt;&gt;(std::basic_istream&lt; CharType &gt; &amp;in, date_time &amp;t)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:739</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa827e0e9673cbb1e0983191ea42c88c88"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa827e0e9673cbb1e0983191ea42c88c88">boost::locale::period::marks::extended_year</a></div><div class="ttdoc">Extended year for Gregorian/Julian calendars, where 1 BC == 0, 2 BC == -1.</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:32</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a6468414599fd81815943e8d9e868fd1b"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a6468414599fd81815943e8d9e868fd1b">boost::locale::period::invalid</a></div><div class="ttdeci">period_type invalid()</div><div class="ttdoc">Get period_type for: special invalid value, should not be used directly.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:56</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ab9269cd77b2d055022a587c9c0d13673"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ab9269cd77b2d055022a587c9c0d13673">boost::locale::period::june</a></div><div class="ttdeci">date_time_period june()</div><div class="ttdoc">Get predefined constant for June.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:270</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a420d04f471ef28107ba1bd6a8edae263"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a420d04f471ef28107ba1bd6a8edae263">boost::locale::period::may</a></div><div class="ttdeci">date_time_period may()</div><div class="ttdoc">Get predefined constant for May.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:265</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html">boost::locale::date_time</a></div><div class="ttdoc">this class represents a date time and allows to perform various operation according to the locale set...</div><div class="ttdef"><b>Definition:</b> date_time.hpp:561</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_a8865698029284b6c7729ea393ad2ebfb"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#a8865698029284b6c7729ea393ad2ebfb">boost::locale::date_time::operator&gt;&gt;</a></div><div class="ttdeci">date_time operator&gt;&gt;(period::period_type f) const</div><div class="ttdoc">roll backward a date by single period f.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:623</div></div>
<div class="ttc" id="structboost_1_1locale_1_1date__time__period_html_a42337ef4ab1008fc5deeb548c01905ca"><div class="ttname"><a href="structboost_1_1locale_1_1date__time__period.html#a42337ef4ab1008fc5deeb548c01905ca">boost::locale::date_time_period::operator-</a></div><div class="ttdeci">date_time_period operator-() const</div><div class="ttdoc">Operator -, switches the sign of period.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:48</div></div>
<div class="ttc" id="group__manipulators_html_gae669b101cbeaed6f6d246ebdcaa8f39c"><div class="ttname"><a href="group__manipulators.html#gae669b101cbeaed6f6d246ebdcaa8f39c">boost::locale::as::time</a></div><div class="ttdeci">std::ios_base &amp; time(std::ios_base &amp;ios)</div><div class="ttdoc">Format a time, number is treated as POSIX time.</div><div class="ttdef"><b>Definition:</b> formatting.hpp:238</div></div>
<div class="ttc" id="structboost_1_1locale_1_1date__time__period_html_a61b11b2243098412dddd804ca7e104af"><div class="ttname"><a href="structboost_1_1locale_1_1date__time__period.html#a61b11b2243098412dddd804ca7e104af">boost::locale::date_time_period::date_time_period</a></div><div class="ttdeci">date_time_period(period::period_type f=period::period_type(), int v=1)</div><div class="ttdoc">Constructor that creates date_time_period from period_type f and a value v – default 1.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:51</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_adee2c2dcbbea3653604cb6d514cb9a9b"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">boost::locale::period::second</a></div><div class="ttdeci">period_type second()</div><div class="ttdoc">Get period_type for: second [0..59].</div><div class="ttdef"><b>Definition:</b> date_time.hpp:132</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_1_1marks_html_a1536eaff4b2a880782128fc127f40d3aa3afc5b74b9e5e23bdfeab56a297bb7c7"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period_1_1marks.html#a1536eaff4b2a880782128fc127f40d3aa3afc5b74b9e5e23bdfeab56a297bb7c7">boost::locale::period::marks::second</a></div><div class="ttdoc">second [0..59]</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:48</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_aa6aafbd111a9c729266f2aa28b895d68"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">boost::locale::period::month</a></div><div class="ttdeci">period_type month()</div><div class="ttdoc">Get period_type for: The month of year, calendar specific, in Gregorian [0..11].</div><div class="ttdef"><b>Definition:</b> date_time.hpp:76</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_a41e228841e03b61187660fb3e9692c7d"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#a41e228841e03b61187660fb3e9692c7d">boost::locale::period::july</a></div><div class="ttdeci">date_time_period july()</div><div class="ttdoc">Get predefined constant for July.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:275</div></div>
<div class="ttc" id="namespaceboost_1_1locale_1_1period_html_ad325c929fb0a1173097cb9195367b209"><div class="ttname"><a href="namespaceboost_1_1locale_1_1period.html#ad325c929fb0a1173097cb9195367b209">boost::locale::period::september</a></div><div class="ttdeci">date_time_period september()</div><div class="ttdoc">Get predefined constant for September.</div><div class="ttdef"><b>Definition:</b> date_time.hpp:285</div></div>
<div class="ttc" id="classboost_1_1locale_1_1date__time_html_a57fc9acb86b8e89bc20278d510f096f5"><div class="ttname"><a href="classboost_1_1locale_1_1date__time.html#a57fc9acb86b8e89bc20278d510f096f5">boost::locale::date_time::operator/</a></div><div class="ttdeci">int operator/(period::period_type f) const</div><div class="ttdoc">syntactic sugar for get(f)</div><div class="ttdef"><b>Definition:</b> date_time.hpp:609</div></div>
<div class="ttc" id="classboost_1_1locale_1_1period_1_1period__type_html"><div class="ttname"><a href="classboost_1_1locale_1_1period_1_1period__type.html">boost::locale::period::period_type</a></div><div class="ttdoc">This class holds a type that represents certain period of time like year, hour, second and so on.</div><div class="ttdef"><b>Definition:</b> date_time_facet.hpp:65</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->

    <li class="footer">
&copy; Copyright 2009-2012 Artyom Beilis,  Distributed under the <a href="https://www.boost.org/LICENSE_1_0.txt">Boost Software License</a>, Version 1.0.
    </li>
   </ul>
 </div>
</body>
</html>
