<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.15"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Boost.Locale: boost::locale::period Namespace Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(initResizable);
/* @license-end */</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="section-basic.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="boost-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Boost.Locale
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('namespaceboost_1_1locale_1_1period.html','');});
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#namespaces">Namespaces</a> &#124;
<a href="#nested-classes">Classes</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">boost::locale::period Namespace Reference<div class="ingroups"><a class="el" href="group__date__time.html">Date, Time, Timezone and Calendar manipulations</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Namespace that contains various types for manipulation with dates.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1period_1_1marks"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period_1_1marks.html">marks</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1period_1_1marks"><td class="mdescLeft">&#160;</td><td class="mdescRight">This namespace holds a enum of various period types like era, year, month, etc.. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">This class holds a type that represents certain period of time like year, hour, second and so on.  <a href="classboost_1_1locale_1_1period_1_1period__type.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a6468414599fd81815943e8d9e868fd1b"><td class="memItemLeft" align="right" valign="top"><a id="a6468414599fd81815943e8d9e868fd1b"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a6468414599fd81815943e8d9e868fd1b">invalid</a> ()</td></tr>
<tr class="memdesc:a6468414599fd81815943e8d9e868fd1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: special invalid value, should not be used directly. <br /></td></tr>
<tr class="separator:a6468414599fd81815943e8d9e868fd1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62d0745dbb555066a7281b9c805ceab7"><td class="memItemLeft" align="right" valign="top"><a id="a62d0745dbb555066a7281b9c805ceab7"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a62d0745dbb555066a7281b9c805ceab7">era</a> ()</td></tr>
<tr class="memdesc:a62d0745dbb555066a7281b9c805ceab7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: Era i.e. AC, BC in Gregorian and Julian calendar, range [0,1]. <br /></td></tr>
<tr class="separator:a62d0745dbb555066a7281b9c805ceab7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ea8d1453bed512ee16bea3199fd92af"><td class="memItemLeft" align="right" valign="top"><a id="a9ea8d1453bed512ee16bea3199fd92af"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a9ea8d1453bed512ee16bea3199fd92af">year</a> ()</td></tr>
<tr class="memdesc:a9ea8d1453bed512ee16bea3199fd92af"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: Year, it is calendar specific, for example 2011 in Gregorian calendar. <br /></td></tr>
<tr class="separator:a9ea8d1453bed512ee16bea3199fd92af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb2dfdc73c6d16796360eca141654aba"><td class="memItemLeft" align="right" valign="top"><a id="aeb2dfdc73c6d16796360eca141654aba"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aeb2dfdc73c6d16796360eca141654aba">extended_year</a> ()</td></tr>
<tr class="memdesc:aeb2dfdc73c6d16796360eca141654aba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: Extended year for Gregorian/Julian calendars, where 1 BC == 0, 2 BC == -1. <br /></td></tr>
<tr class="separator:aeb2dfdc73c6d16796360eca141654aba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6aafbd111a9c729266f2aa28b895d68"><td class="memItemLeft" align="right" valign="top"><a id="aa6aafbd111a9c729266f2aa28b895d68"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aa6aafbd111a9c729266f2aa28b895d68">month</a> ()</td></tr>
<tr class="memdesc:aa6aafbd111a9c729266f2aa28b895d68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: The month of year, calendar specific, in Gregorian [0..11]. <br /></td></tr>
<tr class="separator:aa6aafbd111a9c729266f2aa28b895d68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72438d8d7da8493457e043aa442f0d9d"><td class="memItemLeft" align="right" valign="top"><a id="a72438d8d7da8493457e043aa442f0d9d"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a72438d8d7da8493457e043aa442f0d9d">day</a> ()</td></tr>
<tr class="memdesc:a72438d8d7da8493457e043aa442f0d9d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: The day of month, calendar specific, in Gregorian [1..31]. <br /></td></tr>
<tr class="separator:a72438d8d7da8493457e043aa442f0d9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33e67d3354486021fa1e7076d30d51a4"><td class="memItemLeft" align="right" valign="top"><a id="a33e67d3354486021fa1e7076d30d51a4"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a33e67d3354486021fa1e7076d30d51a4">day_of_year</a> ()</td></tr>
<tr class="memdesc:a33e67d3354486021fa1e7076d30d51a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: The number of day in year, starting from 1, in Gregorian [1..366]. <br /></td></tr>
<tr class="separator:a33e67d3354486021fa1e7076d30d51a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1b424cae5ed4ab32aed3c3aedc306e9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ac1b424cae5ed4ab32aed3c3aedc306e9">day_of_week</a> ()</td></tr>
<tr class="separator:ac1b424cae5ed4ab32aed3c3aedc306e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a083c271b3bbd29e4644b59fb3e34a4d7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a083c271b3bbd29e4644b59fb3e34a4d7">day_of_week_in_month</a> ()</td></tr>
<tr class="separator:a083c271b3bbd29e4644b59fb3e34a4d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57d6b8f97bd6604e13c6982ed0953678"><td class="memItemLeft" align="right" valign="top"><a id="a57d6b8f97bd6604e13c6982ed0953678"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a57d6b8f97bd6604e13c6982ed0953678">day_of_week_local</a> ()</td></tr>
<tr class="memdesc:a57d6b8f97bd6604e13c6982ed0953678"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: Local day of week, for example in France Monday is 1, in US Sunday is 1, [1..7]. <br /></td></tr>
<tr class="separator:a57d6b8f97bd6604e13c6982ed0953678"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35f2ca900e3cda757c4598b686ca5969"><td class="memItemLeft" align="right" valign="top"><a id="a35f2ca900e3cda757c4598b686ca5969"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a35f2ca900e3cda757c4598b686ca5969">hour</a> ()</td></tr>
<tr class="memdesc:a35f2ca900e3cda757c4598b686ca5969"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: 24 clock hour [0..23]. <br /></td></tr>
<tr class="separator:a35f2ca900e3cda757c4598b686ca5969"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af96ab88b9d168801bfde95c7ad24613a"><td class="memItemLeft" align="right" valign="top"><a id="af96ab88b9d168801bfde95c7ad24613a"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#af96ab88b9d168801bfde95c7ad24613a">hour_12</a> ()</td></tr>
<tr class="memdesc:af96ab88b9d168801bfde95c7ad24613a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: 12 clock hour [0..11]. <br /></td></tr>
<tr class="separator:af96ab88b9d168801bfde95c7ad24613a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a61b2390a32e15c6aa2c26bc06d21a20c"><td class="memItemLeft" align="right" valign="top"><a id="a61b2390a32e15c6aa2c26bc06d21a20c"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a61b2390a32e15c6aa2c26bc06d21a20c">am_pm</a> ()</td></tr>
<tr class="memdesc:a61b2390a32e15c6aa2c26bc06d21a20c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: am or pm marker [0..1]. <br /></td></tr>
<tr class="separator:a61b2390a32e15c6aa2c26bc06d21a20c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0076efc591f6341ef785f49422b6fa89"><td class="memItemLeft" align="right" valign="top"><a id="a0076efc591f6341ef785f49422b6fa89"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a0076efc591f6341ef785f49422b6fa89">minute</a> ()</td></tr>
<tr class="memdesc:a0076efc591f6341ef785f49422b6fa89"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: minute [0..59]. <br /></td></tr>
<tr class="separator:a0076efc591f6341ef785f49422b6fa89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adee2c2dcbbea3653604cb6d514cb9a9b"><td class="memItemLeft" align="right" valign="top"><a id="adee2c2dcbbea3653604cb6d514cb9a9b"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#adee2c2dcbbea3653604cb6d514cb9a9b">second</a> ()</td></tr>
<tr class="memdesc:adee2c2dcbbea3653604cb6d514cb9a9b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: second [0..59]. <br /></td></tr>
<tr class="separator:adee2c2dcbbea3653604cb6d514cb9a9b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4cb28c5353004068b73d0f12136bbfe9"><td class="memItemLeft" align="right" valign="top"><a id="a4cb28c5353004068b73d0f12136bbfe9"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a4cb28c5353004068b73d0f12136bbfe9">week_of_year</a> ()</td></tr>
<tr class="memdesc:a4cb28c5353004068b73d0f12136bbfe9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: The week number in the year. <br /></td></tr>
<tr class="separator:a4cb28c5353004068b73d0f12136bbfe9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab01f299cdb64c780cadca7d64f87cd5f"><td class="memItemLeft" align="right" valign="top"><a id="ab01f299cdb64c780cadca7d64f87cd5f"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab01f299cdb64c780cadca7d64f87cd5f">week_of_month</a> ()</td></tr>
<tr class="memdesc:ab01f299cdb64c780cadca7d64f87cd5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: The week number within current month. <br /></td></tr>
<tr class="separator:ab01f299cdb64c780cadca7d64f87cd5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1569da2e8680ef5d3409361069905541"><td class="memItemLeft" align="right" valign="top"><a id="a1569da2e8680ef5d3409361069905541"></a>
<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a1569da2e8680ef5d3409361069905541">first_day_of_week</a> ()</td></tr>
<tr class="memdesc:a1569da2e8680ef5d3409361069905541"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: First day of week, constant, for example Sunday in US = 1, Monday in France = 2. <br /></td></tr>
<tr class="separator:a1569da2e8680ef5d3409361069905541"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d37769736a55787e6f1b82b620b5ef4"><td class="memItemLeft" align="right" valign="top"><a id="a9d37769736a55787e6f1b82b620b5ef4"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a9d37769736a55787e6f1b82b620b5ef4">era</a> (int v)</td></tr>
<tr class="memdesc:a9d37769736a55787e6f1b82b620b5ef4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: Era i.e. AC, BC in Gregorian and Julian calendar, range [0,1]. <br /></td></tr>
<tr class="separator:a9d37769736a55787e6f1b82b620b5ef4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3a1f7646bf3496bde51942509d848d5"><td class="memItemLeft" align="right" valign="top"><a id="ab3a1f7646bf3496bde51942509d848d5"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab3a1f7646bf3496bde51942509d848d5">year</a> (int v)</td></tr>
<tr class="memdesc:ab3a1f7646bf3496bde51942509d848d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: Year, it is calendar specific, for example 2011 in Gregorian calendar. <br /></td></tr>
<tr class="separator:ab3a1f7646bf3496bde51942509d848d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a484606afd8fb5bc756b97d009052c809"><td class="memItemLeft" align="right" valign="top"><a id="a484606afd8fb5bc756b97d009052c809"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a484606afd8fb5bc756b97d009052c809">extended_year</a> (int v)</td></tr>
<tr class="memdesc:a484606afd8fb5bc756b97d009052c809"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: Extended year for Gregorian/Julian calendars, where 1 BC == 0, 2 BC == -1. <br /></td></tr>
<tr class="separator:a484606afd8fb5bc756b97d009052c809"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d5844402628043891f77fccab0f6c16"><td class="memItemLeft" align="right" valign="top"><a id="a8d5844402628043891f77fccab0f6c16"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a8d5844402628043891f77fccab0f6c16">month</a> (int v)</td></tr>
<tr class="memdesc:a8d5844402628043891f77fccab0f6c16"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: The month of year, calendar specific, in Gregorian [0..11]. <br /></td></tr>
<tr class="separator:a8d5844402628043891f77fccab0f6c16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0170a70ce66c0cd31bd64e9be1662591"><td class="memItemLeft" align="right" valign="top"><a id="a0170a70ce66c0cd31bd64e9be1662591"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a0170a70ce66c0cd31bd64e9be1662591">day</a> (int v)</td></tr>
<tr class="memdesc:a0170a70ce66c0cd31bd64e9be1662591"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: The day of month, calendar specific, in Gregorian [1..31]. <br /></td></tr>
<tr class="separator:a0170a70ce66c0cd31bd64e9be1662591"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad2e66d5af580de384b94f8c71ba697c"><td class="memItemLeft" align="right" valign="top"><a id="aad2e66d5af580de384b94f8c71ba697c"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aad2e66d5af580de384b94f8c71ba697c">day_of_year</a> (int v)</td></tr>
<tr class="memdesc:aad2e66d5af580de384b94f8c71ba697c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: The number of day in year, starting from 1, in Gregorian [1..366]. <br /></td></tr>
<tr class="separator:aad2e66d5af580de384b94f8c71ba697c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a754856ee5d0492db9367bdc3e1f4fb88"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a754856ee5d0492db9367bdc3e1f4fb88">day_of_week</a> (int v)</td></tr>
<tr class="separator:a754856ee5d0492db9367bdc3e1f4fb88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af60490df8e226368cea043cc6046db19"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#af60490df8e226368cea043cc6046db19">day_of_week_in_month</a> (int v)</td></tr>
<tr class="separator:af60490df8e226368cea043cc6046db19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a673f97f4427f8a3f0e6453a5ed6aaf1f"><td class="memItemLeft" align="right" valign="top"><a id="a673f97f4427f8a3f0e6453a5ed6aaf1f"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a673f97f4427f8a3f0e6453a5ed6aaf1f">day_of_week_local</a> (int v)</td></tr>
<tr class="memdesc:a673f97f4427f8a3f0e6453a5ed6aaf1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: Local day of week, for example in France Monday is 1, in US Sunday is 1, [1..7]. <br /></td></tr>
<tr class="separator:a673f97f4427f8a3f0e6453a5ed6aaf1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0acb70742e1b2f17cc13152c544ac242"><td class="memItemLeft" align="right" valign="top"><a id="a0acb70742e1b2f17cc13152c544ac242"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a0acb70742e1b2f17cc13152c544ac242">hour</a> (int v)</td></tr>
<tr class="memdesc:a0acb70742e1b2f17cc13152c544ac242"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: 24 clock hour [0..23]. <br /></td></tr>
<tr class="separator:a0acb70742e1b2f17cc13152c544ac242"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad805b92b111e4fd7dc00d911b07f6ab8"><td class="memItemLeft" align="right" valign="top"><a id="ad805b92b111e4fd7dc00d911b07f6ab8"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ad805b92b111e4fd7dc00d911b07f6ab8">hour_12</a> (int v)</td></tr>
<tr class="memdesc:ad805b92b111e4fd7dc00d911b07f6ab8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: 12 clock hour [0..11]. <br /></td></tr>
<tr class="separator:ad805b92b111e4fd7dc00d911b07f6ab8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1288da3e9e6cff57a4d964abe463ab1"><td class="memItemLeft" align="right" valign="top"><a id="ab1288da3e9e6cff57a4d964abe463ab1"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab1288da3e9e6cff57a4d964abe463ab1">am_pm</a> (int v)</td></tr>
<tr class="memdesc:ab1288da3e9e6cff57a4d964abe463ab1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: am or pm marker [0..1]. <br /></td></tr>
<tr class="separator:ab1288da3e9e6cff57a4d964abe463ab1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0490daa370b1e8c89b14d0af2db48073"><td class="memItemLeft" align="right" valign="top"><a id="a0490daa370b1e8c89b14d0af2db48073"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a0490daa370b1e8c89b14d0af2db48073">minute</a> (int v)</td></tr>
<tr class="memdesc:a0490daa370b1e8c89b14d0af2db48073"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: minute [0..59]. <br /></td></tr>
<tr class="separator:a0490daa370b1e8c89b14d0af2db48073"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa75c0462a5cada6fc3661e48ca0ae016"><td class="memItemLeft" align="right" valign="top"><a id="aa75c0462a5cada6fc3661e48ca0ae016"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aa75c0462a5cada6fc3661e48ca0ae016">second</a> (int v)</td></tr>
<tr class="memdesc:aa75c0462a5cada6fc3661e48ca0ae016"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: second [0..59]. <br /></td></tr>
<tr class="separator:aa75c0462a5cada6fc3661e48ca0ae016"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9e8cc55821326edd169fbd90af7304ec"><td class="memItemLeft" align="right" valign="top"><a id="a9e8cc55821326edd169fbd90af7304ec"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a9e8cc55821326edd169fbd90af7304ec">week_of_year</a> (int v)</td></tr>
<tr class="memdesc:a9e8cc55821326edd169fbd90af7304ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: The week number in the year. <br /></td></tr>
<tr class="separator:a9e8cc55821326edd169fbd90af7304ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb20ca7d1a9fbba527939202dcd8a1a8"><td class="memItemLeft" align="right" valign="top"><a id="aeb20ca7d1a9fbba527939202dcd8a1a8"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aeb20ca7d1a9fbba527939202dcd8a1a8">week_of_month</a> (int v)</td></tr>
<tr class="memdesc:aeb20ca7d1a9fbba527939202dcd8a1a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: The week number within current month. <br /></td></tr>
<tr class="separator:aeb20ca7d1a9fbba527939202dcd8a1a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1a52ed7a8042fa428d14c4f87642bc4"><td class="memItemLeft" align="right" valign="top"><a id="ab1a52ed7a8042fa428d14c4f87642bc4"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab1a52ed7a8042fa428d14c4f87642bc4">first_day_of_week</a> (int v)</td></tr>
<tr class="memdesc:ab1a52ed7a8042fa428d14c4f87642bc4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: First day of week, constant, for example Sunday in US = 1, Monday in France = 2. <br /></td></tr>
<tr class="separator:ab1a52ed7a8042fa428d14c4f87642bc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6073ebcf60bf690662c3a9d113b49e9b"><td class="memItemLeft" align="right" valign="top"><a id="a6073ebcf60bf690662c3a9d113b49e9b"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a6073ebcf60bf690662c3a9d113b49e9b">january</a> ()</td></tr>
<tr class="memdesc:a6073ebcf60bf690662c3a9d113b49e9b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for January. <br /></td></tr>
<tr class="separator:a6073ebcf60bf690662c3a9d113b49e9b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0610583a720120e8dcf90d0fe01cb01"><td class="memItemLeft" align="right" valign="top"><a id="ab0610583a720120e8dcf90d0fe01cb01"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab0610583a720120e8dcf90d0fe01cb01">february</a> ()</td></tr>
<tr class="memdesc:ab0610583a720120e8dcf90d0fe01cb01"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for February. <br /></td></tr>
<tr class="separator:ab0610583a720120e8dcf90d0fe01cb01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0d844780c28dc783879d82eaa192961"><td class="memItemLeft" align="right" valign="top"><a id="ac0d844780c28dc783879d82eaa192961"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ac0d844780c28dc783879d82eaa192961">march</a> ()</td></tr>
<tr class="memdesc:ac0d844780c28dc783879d82eaa192961"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for March. <br /></td></tr>
<tr class="separator:ac0d844780c28dc783879d82eaa192961"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1b4663ee24f7687e592f0b790e8df494"><td class="memItemLeft" align="right" valign="top"><a id="a1b4663ee24f7687e592f0b790e8df494"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a1b4663ee24f7687e592f0b790e8df494">april</a> ()</td></tr>
<tr class="memdesc:a1b4663ee24f7687e592f0b790e8df494"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for April. <br /></td></tr>
<tr class="separator:a1b4663ee24f7687e592f0b790e8df494"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a420d04f471ef28107ba1bd6a8edae263"><td class="memItemLeft" align="right" valign="top"><a id="a420d04f471ef28107ba1bd6a8edae263"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a420d04f471ef28107ba1bd6a8edae263">may</a> ()</td></tr>
<tr class="memdesc:a420d04f471ef28107ba1bd6a8edae263"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for May. <br /></td></tr>
<tr class="separator:a420d04f471ef28107ba1bd6a8edae263"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab9269cd77b2d055022a587c9c0d13673"><td class="memItemLeft" align="right" valign="top"><a id="ab9269cd77b2d055022a587c9c0d13673"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab9269cd77b2d055022a587c9c0d13673">june</a> ()</td></tr>
<tr class="memdesc:ab9269cd77b2d055022a587c9c0d13673"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for June. <br /></td></tr>
<tr class="separator:ab9269cd77b2d055022a587c9c0d13673"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41e228841e03b61187660fb3e9692c7d"><td class="memItemLeft" align="right" valign="top"><a id="a41e228841e03b61187660fb3e9692c7d"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a41e228841e03b61187660fb3e9692c7d">july</a> ()</td></tr>
<tr class="memdesc:a41e228841e03b61187660fb3e9692c7d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for July. <br /></td></tr>
<tr class="separator:a41e228841e03b61187660fb3e9692c7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5d2e72c7d5b842c26af29af4c96a853"><td class="memItemLeft" align="right" valign="top"><a id="ab5d2e72c7d5b842c26af29af4c96a853"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab5d2e72c7d5b842c26af29af4c96a853">august</a> ()</td></tr>
<tr class="memdesc:ab5d2e72c7d5b842c26af29af4c96a853"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for August. <br /></td></tr>
<tr class="separator:ab5d2e72c7d5b842c26af29af4c96a853"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad325c929fb0a1173097cb9195367b209"><td class="memItemLeft" align="right" valign="top"><a id="ad325c929fb0a1173097cb9195367b209"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ad325c929fb0a1173097cb9195367b209">september</a> ()</td></tr>
<tr class="memdesc:ad325c929fb0a1173097cb9195367b209"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for September. <br /></td></tr>
<tr class="separator:ad325c929fb0a1173097cb9195367b209"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0e376bf63fb32da0dad13b0c4a6fef1"><td class="memItemLeft" align="right" valign="top"><a id="ad0e376bf63fb32da0dad13b0c4a6fef1"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ad0e376bf63fb32da0dad13b0c4a6fef1">october</a> ()</td></tr>
<tr class="memdesc:ad0e376bf63fb32da0dad13b0c4a6fef1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for October. <br /></td></tr>
<tr class="separator:ad0e376bf63fb32da0dad13b0c4a6fef1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab2810f9718b18b77e47b4d23f94589ae"><td class="memItemLeft" align="right" valign="top"><a id="ab2810f9718b18b77e47b4d23f94589ae"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab2810f9718b18b77e47b4d23f94589ae">november</a> ()</td></tr>
<tr class="memdesc:ab2810f9718b18b77e47b4d23f94589ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for November. <br /></td></tr>
<tr class="separator:ab2810f9718b18b77e47b4d23f94589ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad0c72b6aa0ade2e71a71223eefab6fd"><td class="memItemLeft" align="right" valign="top"><a id="aad0c72b6aa0ade2e71a71223eefab6fd"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aad0c72b6aa0ade2e71a71223eefab6fd">december</a> ()</td></tr>
<tr class="memdesc:aad0c72b6aa0ade2e71a71223eefab6fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for December. <br /></td></tr>
<tr class="separator:aad0c72b6aa0ade2e71a71223eefab6fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12e3cce2dc169b65062c7fadd1143b0b"><td class="memItemLeft" align="right" valign="top"><a id="a12e3cce2dc169b65062c7fadd1143b0b"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a12e3cce2dc169b65062c7fadd1143b0b">sunday</a> ()</td></tr>
<tr class="memdesc:a12e3cce2dc169b65062c7fadd1143b0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for Sunday. <br /></td></tr>
<tr class="separator:a12e3cce2dc169b65062c7fadd1143b0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45ad1aeaf7a0e62ef26a42adca38da70"><td class="memItemLeft" align="right" valign="top"><a id="a45ad1aeaf7a0e62ef26a42adca38da70"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a45ad1aeaf7a0e62ef26a42adca38da70">monday</a> ()</td></tr>
<tr class="memdesc:a45ad1aeaf7a0e62ef26a42adca38da70"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for Monday. <br /></td></tr>
<tr class="separator:a45ad1aeaf7a0e62ef26a42adca38da70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2426159fe1f6cb25f2598f35adf14267"><td class="memItemLeft" align="right" valign="top"><a id="a2426159fe1f6cb25f2598f35adf14267"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a2426159fe1f6cb25f2598f35adf14267">tuesday</a> ()</td></tr>
<tr class="memdesc:a2426159fe1f6cb25f2598f35adf14267"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for Tuesday. <br /></td></tr>
<tr class="separator:a2426159fe1f6cb25f2598f35adf14267"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a856d680245af08ff3bd1618817a90ef1"><td class="memItemLeft" align="right" valign="top"><a id="a856d680245af08ff3bd1618817a90ef1"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a856d680245af08ff3bd1618817a90ef1">wednesday</a> ()</td></tr>
<tr class="memdesc:a856d680245af08ff3bd1618817a90ef1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for Wednesday. <br /></td></tr>
<tr class="separator:a856d680245af08ff3bd1618817a90ef1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab03ddec166d072a7465e87dbaccc1389"><td class="memItemLeft" align="right" valign="top"><a id="ab03ddec166d072a7465e87dbaccc1389"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab03ddec166d072a7465e87dbaccc1389">thursday</a> ()</td></tr>
<tr class="memdesc:ab03ddec166d072a7465e87dbaccc1389"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for Thursday. <br /></td></tr>
<tr class="separator:ab03ddec166d072a7465e87dbaccc1389"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26ef9fa6f6df065606a36c9b42e165eb"><td class="memItemLeft" align="right" valign="top"><a id="a26ef9fa6f6df065606a36c9b42e165eb"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a26ef9fa6f6df065606a36c9b42e165eb">friday</a> ()</td></tr>
<tr class="memdesc:a26ef9fa6f6df065606a36c9b42e165eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for Friday. <br /></td></tr>
<tr class="separator:a26ef9fa6f6df065606a36c9b42e165eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac16b397e27e29fe6483540910e8ade3a"><td class="memItemLeft" align="right" valign="top"><a id="ac16b397e27e29fe6483540910e8ade3a"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ac16b397e27e29fe6483540910e8ade3a">saturday</a> ()</td></tr>
<tr class="memdesc:ac16b397e27e29fe6483540910e8ade3a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for Saturday. <br /></td></tr>
<tr class="separator:ac16b397e27e29fe6483540910e8ade3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a683b393abc6276f3d77289af0f5d6404"><td class="memItemLeft" align="right" valign="top"><a id="a683b393abc6276f3d77289af0f5d6404"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a683b393abc6276f3d77289af0f5d6404">am</a> ()</td></tr>
<tr class="memdesc:a683b393abc6276f3d77289af0f5d6404"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for AM (Ante Meridiem) <br /></td></tr>
<tr class="separator:a683b393abc6276f3d77289af0f5d6404"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ee88cdab049350f1346466b2da6ccf0"><td class="memItemLeft" align="right" valign="top"><a id="a5ee88cdab049350f1346466b2da6ccf0"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a5ee88cdab049350f1346466b2da6ccf0">pm</a> ()</td></tr>
<tr class="memdesc:a5ee88cdab049350f1346466b2da6ccf0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get predefined constant for PM (Post Meridiem) <br /></td></tr>
<tr class="separator:a5ee88cdab049350f1346466b2da6ccf0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1399317fdf67b50ec11aa9298176ab70"><td class="memItemLeft" align="right" valign="top"><a id="a1399317fdf67b50ec11aa9298176ab70"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a1399317fdf67b50ec11aa9298176ab70">operator+</a> (<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)</td></tr>
<tr class="memdesc:a1399317fdf67b50ec11aa9298176ab70"><td class="mdescLeft">&#160;</td><td class="mdescRight">convert <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> to date_time_period(f,1) <br /></td></tr>
<tr class="separator:a1399317fdf67b50ec11aa9298176ab70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c5588c1059eff9e9ab8034f7442bf00"><td class="memItemLeft" align="right" valign="top"><a id="a9c5588c1059eff9e9ab8034f7442bf00"></a>
<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a9c5588c1059eff9e9ab8034f7442bf00">operator-</a> (<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)</td></tr>
<tr class="memdesc:a9c5588c1059eff9e9ab8034f7442bf00"><td class="mdescLeft">&#160;</td><td class="mdescRight">convert <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> to <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a>(f,-1) <br /></td></tr>
<tr class="separator:a9c5588c1059eff9e9ab8034f7442bf00"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a64a8e83513d867a5356cacc596d03962"><td class="memTemplParams" colspan="2"><a id="a64a8e83513d867a5356cacc596d03962"></a>
template&lt;typename T &gt; </td></tr>
<tr class="memitem:a64a8e83513d867a5356cacc596d03962"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a64a8e83513d867a5356cacc596d03962">operator *</a> (<a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f, T v)</td></tr>
<tr class="memdesc:a64a8e83513d867a5356cacc596d03962"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> of type <em>f</em> with value <em>v</em>. <br /></td></tr>
<tr class="separator:a64a8e83513d867a5356cacc596d03962"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a854429d3488950dbf68104f3ae4dd68c"><td class="memTemplParams" colspan="2"><a id="a854429d3488950dbf68104f3ae4dd68c"></a>
template&lt;typename T &gt; </td></tr>
<tr class="memitem:a854429d3488950dbf68104f3ae4dd68c"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a854429d3488950dbf68104f3ae4dd68c">operator *</a> (T v, <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period::period_type</a> f)</td></tr>
<tr class="memdesc:a854429d3488950dbf68104f3ae4dd68c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> of type <em>f</em> with value <em>v</em>. <br /></td></tr>
<tr class="separator:a854429d3488950dbf68104f3ae4dd68c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28a491e49e553332bebc92c3c7c6e917"><td class="memTemplParams" colspan="2"><a id="a28a491e49e553332bebc92c3c7c6e917"></a>
template&lt;typename T &gt; </td></tr>
<tr class="memitem:a28a491e49e553332bebc92c3c7c6e917"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a28a491e49e553332bebc92c3c7c6e917">operator *</a> (T v, <a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> f)</td></tr>
<tr class="memdesc:a28a491e49e553332bebc92c3c7c6e917"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> of type <em>f</em> with value <em>v</em>. <br /></td></tr>
<tr class="separator:a28a491e49e553332bebc92c3c7c6e917"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6cf5ca5046426b079e8949944c265c72"><td class="memTemplParams" colspan="2"><a id="a6cf5ca5046426b079e8949944c265c72"></a>
template&lt;typename T &gt; </td></tr>
<tr class="memitem:a6cf5ca5046426b079e8949944c265c72"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a>&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a6cf5ca5046426b079e8949944c265c72">operator *</a> (<a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> f, T v)</td></tr>
<tr class="memdesc:a6cf5ca5046426b079e8949944c265c72"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> of type <em>f</em> with value <em>v</em>. <br /></td></tr>
<tr class="separator:a6cf5ca5046426b079e8949944c265c72"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af372c670a3979be4d5b8525ac224bae3"><td class="memItemLeft" align="right" valign="top"><a id="af372c670a3979be4d5b8525ac224bae3"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#af372c670a3979be4d5b8525ac224bae3">era</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:af372c670a3979be4d5b8525ac224bae3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of Era i.e. AC, BC in Gregorian and Julian calendar, range [0,1]. <br /></td></tr>
<tr class="separator:af372c670a3979be4d5b8525ac224bae3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a87e2ab6badc92d4541cc8651fb1401e0"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a87e2ab6badc92d4541cc8651fb1401e0">year</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="separator:a87e2ab6badc92d4541cc8651fb1401e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51f370518c43c31e219a67b927bbeb91"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a51f370518c43c31e219a67b927bbeb91">extended_year</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="separator:a51f370518c43c31e219a67b927bbeb91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8bacd973219a934023c5c0acd901b987"><td class="memItemLeft" align="right" valign="top"><a id="a8bacd973219a934023c5c0acd901b987"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a8bacd973219a934023c5c0acd901b987">month</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:a8bacd973219a934023c5c0acd901b987"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of The month of year, calendar specific, in Gregorian [0..11]. <br /></td></tr>
<tr class="separator:a8bacd973219a934023c5c0acd901b987"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31d0373ed7e81d7ceb5b55472b65b47c"><td class="memItemLeft" align="right" valign="top"><a id="a31d0373ed7e81d7ceb5b55472b65b47c"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a31d0373ed7e81d7ceb5b55472b65b47c">day</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:a31d0373ed7e81d7ceb5b55472b65b47c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of The day of month, calendar specific, in Gregorian [1..31]. <br /></td></tr>
<tr class="separator:a31d0373ed7e81d7ceb5b55472b65b47c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4223191400174ceb748a0a500f04c85b"><td class="memItemLeft" align="right" valign="top"><a id="a4223191400174ceb748a0a500f04c85b"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a4223191400174ceb748a0a500f04c85b">day_of_year</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:a4223191400174ceb748a0a500f04c85b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of The number of day in year, starting from 1, in Gregorian [1..366]. <br /></td></tr>
<tr class="separator:a4223191400174ceb748a0a500f04c85b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ad06c97a5719507b62c73eb21a1d7d8"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a0ad06c97a5719507b62c73eb21a1d7d8">day_of_week</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="separator:a0ad06c97a5719507b62c73eb21a1d7d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff80ffdef5ae43dad1ae0b93102a58f2"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aff80ffdef5ae43dad1ae0b93102a58f2">day_of_week_in_month</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="separator:aff80ffdef5ae43dad1ae0b93102a58f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9922218132f453d43446e82554e97cb4"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a9922218132f453d43446e82554e97cb4">day_of_week_local</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="separator:a9922218132f453d43446e82554e97cb4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af52108fa564fd9191b1191dd368256d5"><td class="memItemLeft" align="right" valign="top"><a id="af52108fa564fd9191b1191dd368256d5"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#af52108fa564fd9191b1191dd368256d5">hour</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:af52108fa564fd9191b1191dd368256d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of 24 clock hour [0..23]. <br /></td></tr>
<tr class="separator:af52108fa564fd9191b1191dd368256d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ff9ca3b6be4eede78e367603c16ae4c"><td class="memItemLeft" align="right" valign="top"><a id="a7ff9ca3b6be4eede78e367603c16ae4c"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a7ff9ca3b6be4eede78e367603c16ae4c">hour_12</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:a7ff9ca3b6be4eede78e367603c16ae4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of 12 clock hour [0..11]. <br /></td></tr>
<tr class="separator:a7ff9ca3b6be4eede78e367603c16ae4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad36d2f5e0b8df78a8f13bbe5ab0446e9"><td class="memItemLeft" align="right" valign="top"><a id="ad36d2f5e0b8df78a8f13bbe5ab0446e9"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ad36d2f5e0b8df78a8f13bbe5ab0446e9">am_pm</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:ad36d2f5e0b8df78a8f13bbe5ab0446e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of am or pm marker [0..1]. <br /></td></tr>
<tr class="separator:ad36d2f5e0b8df78a8f13bbe5ab0446e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a48d45a9609e9d015f756da56d5edcdff"><td class="memItemLeft" align="right" valign="top"><a id="a48d45a9609e9d015f756da56d5edcdff"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a48d45a9609e9d015f756da56d5edcdff">minute</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:a48d45a9609e9d015f756da56d5edcdff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of minute [0..59]. <br /></td></tr>
<tr class="separator:a48d45a9609e9d015f756da56d5edcdff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b929adb5728ae6b2eb1abbd3140d21c"><td class="memItemLeft" align="right" valign="top"><a id="a7b929adb5728ae6b2eb1abbd3140d21c"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a7b929adb5728ae6b2eb1abbd3140d21c">second</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:a7b929adb5728ae6b2eb1abbd3140d21c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of second [0..59]. <br /></td></tr>
<tr class="separator:a7b929adb5728ae6b2eb1abbd3140d21c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad87b4b80ff5b22373ecbf62debc1c8cf"><td class="memItemLeft" align="right" valign="top"><a id="ad87b4b80ff5b22373ecbf62debc1c8cf"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ad87b4b80ff5b22373ecbf62debc1c8cf">week_of_year</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:ad87b4b80ff5b22373ecbf62debc1c8cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of The week number in the year. <br /></td></tr>
<tr class="separator:ad87b4b80ff5b22373ecbf62debc1c8cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68b0dca7c16e8325ecb96c4ee1d1b4d8"><td class="memItemLeft" align="right" valign="top"><a id="a68b0dca7c16e8325ecb96c4ee1d1b4d8"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a68b0dca7c16e8325ecb96c4ee1d1b4d8">week_of_month</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="memdesc:a68b0dca7c16e8325ecb96c4ee1d1b4d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of The week number within current month. <br /></td></tr>
<tr class="separator:a68b0dca7c16e8325ecb96c4ee1d1b4d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad79f5a6fc953cf2e65a9b8ece18bb0e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aad79f5a6fc953cf2e65a9b8ece18bb0e">first_day_of_week</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;dt)</td></tr>
<tr class="separator:aad79f5a6fc953cf2e65a9b8ece18bb0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8ca4022eb5465964ac6feba2e4bda68"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aa8ca4022eb5465964ac6feba2e4bda68">era</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="separator:aa8ca4022eb5465964ac6feba2e4bda68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a305d934f23f06b16330c41ca9cc47637"><td class="memItemLeft" align="right" valign="top"><a id="a305d934f23f06b16330c41ca9cc47637"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a305d934f23f06b16330c41ca9cc47637">year</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:a305d934f23f06b16330c41ca9cc47637"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in years. <br /></td></tr>
<tr class="separator:a305d934f23f06b16330c41ca9cc47637"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a27eb8aaf25c260c9403d8ab62e52bc"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a2a27eb8aaf25c260c9403d8ab62e52bc">extended_year</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="separator:a2a27eb8aaf25c260c9403d8ab62e52bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab46e501097767030605584bc733b3b28"><td class="memItemLeft" align="right" valign="top"><a id="ab46e501097767030605584bc733b3b28"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab46e501097767030605584bc733b3b28">month</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:ab46e501097767030605584bc733b3b28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in months. <br /></td></tr>
<tr class="separator:ab46e501097767030605584bc733b3b28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae19429b6c84a3b6816299182a10c8efa"><td class="memItemLeft" align="right" valign="top"><a id="ae19429b6c84a3b6816299182a10c8efa"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ae19429b6c84a3b6816299182a10c8efa">day</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:ae19429b6c84a3b6816299182a10c8efa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in days of month. <br /></td></tr>
<tr class="separator:ae19429b6c84a3b6816299182a10c8efa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5c34cbf5ed47ed873440b9ab583133b"><td class="memItemLeft" align="right" valign="top"><a id="af5c34cbf5ed47ed873440b9ab583133b"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#af5c34cbf5ed47ed873440b9ab583133b">day_of_year</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:af5c34cbf5ed47ed873440b9ab583133b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in days of year. <br /></td></tr>
<tr class="separator:af5c34cbf5ed47ed873440b9ab583133b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab24c7a6767638388c6ec73aadb8ef279"><td class="memItemLeft" align="right" valign="top"><a id="ab24c7a6767638388c6ec73aadb8ef279"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ab24c7a6767638388c6ec73aadb8ef279">day_of_week</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:ab24c7a6767638388c6ec73aadb8ef279"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in days of week. <br /></td></tr>
<tr class="separator:ab24c7a6767638388c6ec73aadb8ef279"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2322c3643f94039534dbe51b0cae0d78"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a2322c3643f94039534dbe51b0cae0d78">day_of_week_in_month</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="separator:a2322c3643f94039534dbe51b0cae0d78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf15b4ddef13c97527625e4529b064aa"><td class="memItemLeft" align="right" valign="top"><a id="adf15b4ddef13c97527625e4529b064aa"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#adf15b4ddef13c97527625e4529b064aa">day_of_week_local</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:adf15b4ddef13c97527625e4529b064aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in local day of week. <br /></td></tr>
<tr class="separator:adf15b4ddef13c97527625e4529b064aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76076ff327a9253b50c184c6bce49467"><td class="memItemLeft" align="right" valign="top"><a id="a76076ff327a9253b50c184c6bce49467"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a76076ff327a9253b50c184c6bce49467">hour</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:a76076ff327a9253b50c184c6bce49467"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in hours. <br /></td></tr>
<tr class="separator:a76076ff327a9253b50c184c6bce49467"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff94a06785624362b343353d0a155d6d"><td class="memItemLeft" align="right" valign="top"><a id="aff94a06785624362b343353d0a155d6d"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aff94a06785624362b343353d0a155d6d">hour_12</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:aff94a06785624362b343353d0a155d6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in 12 clock hours. <br /></td></tr>
<tr class="separator:aff94a06785624362b343353d0a155d6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73ac683dcb58698a46e28413b13c43e5"><td class="memItemLeft" align="right" valign="top"><a id="a73ac683dcb58698a46e28413b13c43e5"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a73ac683dcb58698a46e28413b13c43e5">am_pm</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:a73ac683dcb58698a46e28413b13c43e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in am or pm markers. <br /></td></tr>
<tr class="separator:a73ac683dcb58698a46e28413b13c43e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa2109020ceca6fd45f144f3fa0878dae"><td class="memItemLeft" align="right" valign="top"><a id="aa2109020ceca6fd45f144f3fa0878dae"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#aa2109020ceca6fd45f144f3fa0878dae">minute</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:aa2109020ceca6fd45f144f3fa0878dae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in minutes. <br /></td></tr>
<tr class="separator:aa2109020ceca6fd45f144f3fa0878dae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c2981b3d0ec30691695cb1d5f527ae0"><td class="memItemLeft" align="right" valign="top"><a id="a1c2981b3d0ec30691695cb1d5f527ae0"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a1c2981b3d0ec30691695cb1d5f527ae0">second</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:a1c2981b3d0ec30691695cb1d5f527ae0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in seconds. <br /></td></tr>
<tr class="separator:a1c2981b3d0ec30691695cb1d5f527ae0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acaf9188e417af350cc43d6ae5418b768"><td class="memItemLeft" align="right" valign="top"><a id="acaf9188e417af350cc43d6ae5418b768"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#acaf9188e417af350cc43d6ae5418b768">week_of_year</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:acaf9188e417af350cc43d6ae5418b768"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in the week number in the year. <br /></td></tr>
<tr class="separator:acaf9188e417af350cc43d6ae5418b768"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0f1af74a7a6f537fa4b63d49138a0719"><td class="memItemLeft" align="right" valign="top"><a id="a0f1af74a7a6f537fa4b63d49138a0719"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#a0f1af74a7a6f537fa4b63d49138a0719">week_of_month</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:a0f1af74a7a6f537fa4b63d49138a0719"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in The week number within current month. <br /></td></tr>
<tr class="separator:a0f1af74a7a6f537fa4b63d49138a0719"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad24c3c782bd5844aa116ebcbe0457794"><td class="memItemLeft" align="right" valign="top"><a id="ad24c3c782bd5844aa116ebcbe0457794"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html#ad24c3c782bd5844aa116ebcbe0457794">first_day_of_week</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;dt)</td></tr>
<tr class="memdesc:ad24c3c782bd5844aa116ebcbe0457794"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in the first day of week. <br /></td></tr>
<tr class="separator:ad24c3c782bd5844aa116ebcbe0457794"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Namespace that contains various types for manipulation with dates. </p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="ac1b424cae5ed4ab32aed3c3aedc306e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac1b424cae5ed4ab32aed3c3aedc306e9">&#9670;&nbsp;</a></span>day_of_week() <span class="overload">[1/3]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> boost::locale::period::day_of_week </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: Day of week, Sunday=1, Monday=2,..., Saturday=7.</p>
<p>Note that updating this value respects local day of week, so for example, If first day of week is Monday and the current day is Tuesday then setting the value to Sunday (1) would forward the date by 5 days forward and not backward by two days as it could be expected if the numbers were taken as is. </p>
<dl class="section examples"><dt>Examples</dt><dd><a class="el" href="calendar_8cpp-example.html#a12">calendar.cpp</a>.</dd>
</dl>

</div>
</div>
<a id="a754856ee5d0492db9367bdc3e1f4fb88"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a754856ee5d0492db9367bdc3e1f4fb88">&#9670;&nbsp;</a></span>day_of_week() <span class="overload">[2/3]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> boost::locale::period::day_of_week </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: Day of week, Sunday=1, Monday=2,..., Saturday=7.</p>
<p>Note that updating this value respects local day of week, so for example, If first day of week is Monday and the current day is Tuesday then setting the value to Sunday (1) would forward the date by 5 days forward and not backward by two days as it could be expected if the numbers were taken as is. </p>

</div>
</div>
<a id="a0ad06c97a5719507b62c73eb21a1d7d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0ad06c97a5719507b62c73eb21a1d7d8">&#9670;&nbsp;</a></span>day_of_week() <span class="overload">[3/3]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int boost::locale::period::day_of_week </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;&#160;</td>
          <td class="paramname"><em>dt</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of Day of week, Sunday=1, Monday=2,..., Saturday=7.</p>
<p>Note that updating this value respects local day of week, so for example, If first day of week is Monday and the current day is Tuesday then setting the value to Sunday (1) would forward the date by 5 days forward and not backward by two days as it could be expected if the numbers were taken as is. </p>

</div>
</div>
<a id="a083c271b3bbd29e4644b59fb3e34a4d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a083c271b3bbd29e4644b59fb3e34a4d7">&#9670;&nbsp;</a></span>day_of_week_in_month() <span class="overload">[1/4]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html">period_type</a> boost::locale::period::day_of_week_in_month </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get <a class="el" href="classboost_1_1locale_1_1period_1_1period__type.html" title="This class holds a type that represents certain period of time like year, hour, second and so on.">period_type</a> for: Original number of the day of the week in month. For example 1st Sunday, 2nd Sunday, etc. in Gregorian [1..5] </p>

</div>
</div>
<a id="af60490df8e226368cea043cc6046db19"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af60490df8e226368cea043cc6046db19">&#9670;&nbsp;</a></span>day_of_week_in_month() <span class="overload">[2/4]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a> boost::locale::period::day_of_week_in_month </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get <a class="el" href="structboost_1_1locale_1_1date__time__period.html" title="This class represents a pair of period_type and the integer values that describes its amount....">date_time_period</a> for: Original number of the day of the week in month. For example 1st Sunday, 2nd Sunday, etc. in Gregorian [1..5] </p>

</div>
</div>
<a id="aff80ffdef5ae43dad1ae0b93102a58f2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff80ffdef5ae43dad1ae0b93102a58f2">&#9670;&nbsp;</a></span>day_of_week_in_month() <span class="overload">[3/4]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int boost::locale::period::day_of_week_in_month </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;&#160;</td>
          <td class="paramname"><em>dt</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of Original number of the day of the week in month. For example 1st Sunday, 2nd Sunday, etc. in Gregorian [1..5] </p>

</div>
</div>
<a id="a2322c3643f94039534dbe51b0cae0d78"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2322c3643f94039534dbe51b0cae0d78">&#9670;&nbsp;</a></span>day_of_week_in_month() <span class="overload">[4/4]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int boost::locale::period::day_of_week_in_month </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;&#160;</td>
          <td class="paramname"><em>dt</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in original number of the day of the week in month </p>

</div>
</div>
<a id="a9922218132f453d43446e82554e97cb4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9922218132f453d43446e82554e97cb4">&#9670;&nbsp;</a></span>day_of_week_local()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int boost::locale::period::day_of_week_local </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;&#160;</td>
          <td class="paramname"><em>dt</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of Local day of week, for example in France Monday is 1, in US Sunday is 1, [1..7] </p>

</div>
</div>
<a id="aa8ca4022eb5465964ac6feba2e4bda68"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8ca4022eb5465964ac6feba2e4bda68">&#9670;&nbsp;</a></span>era()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int boost::locale::period::era </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;&#160;</td>
          <td class="paramname"><em>dt</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in Era i.e. AC, BC in Gregorian and Julian calendar, range [0,1] </p>

</div>
</div>
<a id="a51f370518c43c31e219a67b927bbeb91"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a51f370518c43c31e219a67b927bbeb91">&#9670;&nbsp;</a></span>extended_year() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int boost::locale::period::extended_year </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;&#160;</td>
          <td class="paramname"><em>dt</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of Extended year for Gregorian/Julian calendars, where 1 BC == 0, 2 BC == -1. </p>

</div>
</div>
<a id="a2a27eb8aaf25c260c9403d8ab62e52bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a27eb8aaf25c260c9403d8ab62e52bc">&#9670;&nbsp;</a></span>extended_year() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int boost::locale::period::extended_year </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a> &amp;&#160;</td>
          <td class="paramname"><em>dt</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Extract from <a class="el" href="classboost_1_1locale_1_1date__time__duration.html" title="This class represents a period: a pair of two date_time objects.">date_time_duration</a> numerical value of duration in extended years (for Gregorian/Julian calendars, where 1 BC == 0, 2 BC == -1). </p>

</div>
</div>
<a id="aad79f5a6fc953cf2e65a9b8ece18bb0e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad79f5a6fc953cf2e65a9b8ece18bb0e">&#9670;&nbsp;</a></span>first_day_of_week()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int boost::locale::period::first_day_of_week </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;&#160;</td>
          <td class="paramname"><em>dt</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of First day of week, constant, for example Sunday in US = 1, Monday in France = 2 </p>

</div>
</div>
<a id="a87e2ab6badc92d4541cc8651fb1401e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a87e2ab6badc92d4541cc8651fb1401e0">&#9670;&nbsp;</a></span>year()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int boost::locale::period::year </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;&#160;</td>
          <td class="paramname"><em>dt</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Extract from <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> numerical value of Year, it is calendar specific, for example 2011 in Gregorian calendar. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->

    <li class="footer">
&copy; Copyright 2009-2012 Artyom Beilis,  Distributed under the <a href="https://www.boost.org/LICENSE_1_0.txt">Boost Software License</a>, Version 1.0.
    </li>
   </ul>
 </div>
</body>
</html>
