<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>is_void</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.TypeTraits">
<link rel="up" href="../reference.html" title="Alphabetical Reference">
<link rel="prev" href="is_virtual_base_of.html" title="is_virtual_base_of">
<link rel="next" href="is_volatile.html" title="is_volatile">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_virtual_base_of.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../reference.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="is_volatile.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_typetraits.reference.is_void"></a><a class="link" href="is_void.html" title="is_void">is_void</a>
</h3></div></div></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">is_void</span> <span class="special">:</span> <span class="keyword">public</span> <em class="replaceable"><code><a class="link" href="integral_constant.html" title="integral_constant">true_type</a>-or-<a class="link" href="integral_constant.html" title="integral_constant">false_type</a></code></em> <span class="special">{};</span>
</pre>
<p>
        <span class="bold"><strong>Inherits:</strong></span> If T is a (possibly cv-qualified)
        void type then inherits from <a class="link" href="integral_constant.html" title="integral_constant">true_type</a>,
        otherwise inherits from <a class="link" href="integral_constant.html" title="integral_constant">false_type</a>.
      </p>
<p>
        <span class="bold"><strong>C++ Standard Reference:</strong></span> 3.9.1p9.
      </p>
<p>
        <span class="bold"><strong>Compiler Compatibility:</strong></span> All current compilers
        are supported by this trait.
      </p>
<p>
        <span class="bold"><strong>Header:</strong></span> <code class="computeroutput"> <span class="preprocessor">#include</span>
        <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">type_traits</span><span class="special">/</span><span class="identifier">is_void</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
        or <code class="computeroutput"> <span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">type_traits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<p>
        <span class="bold"><strong>Examples:</strong></span>
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <code class="computeroutput"><span class="identifier">is_void</span><span class="special">&lt;</span><span class="keyword">void</span><span class="special">&gt;</span></code>
          inherits from <code class="computeroutput"><a class="link" href="integral_constant.html" title="integral_constant">true_type</a></code>.
        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <code class="computeroutput"><span class="identifier">is_void</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="keyword">void</span><span class="special">&gt;::</span><span class="identifier">type</span></code>
          is the type <code class="computeroutput"><a class="link" href="integral_constant.html" title="integral_constant">true_type</a></code>.
        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <code class="computeroutput"><span class="identifier">is_void</span><span class="special">&lt;</span><span class="keyword">void</span><span class="special">&gt;::</span><span class="identifier">value</span></code> is an integral constant expression
          that evaluates to <span class="emphasis"><em>true</em></span>.
        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <code class="computeroutput"><span class="identifier">is_void</span><span class="special">&lt;</span><span class="keyword">void</span><span class="special">*&gt;::</span><span class="identifier">value</span></code> is an integral constant expression
          that evaluates to <span class="emphasis"><em>false</em></span>.
        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <code class="computeroutput"><span class="identifier">is_void</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">value_type</span></code> is the type <code class="computeroutput"><span class="keyword">bool</span></code>.
        </p></blockquote></div>
</div>
<div class="copyright-footer">Copyright © 2000, 2011 Adobe Systems Inc, David Abrahams,
      Frederic Bron, Steve Cleary, Beman Dawes, Glen Fernandes, Aleksey Gurtovoy,
      Howard Hinnant, Jesse Jones, Mat Marcus, Itay Maman, John Maddock, Alexander
      Nasonov, Thorsten Ottosen, Roman Perepelitsa, Robert Ramey, Jeremy Siek, Robert
      Stewart and Steven Watanabe<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_virtual_base_of.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../reference.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="is_volatile.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
