<?xml version="1.0" standalone="yes"?>
<library-reference id="shared_library_reference"><title>Shared Library Reference</title><header name="boost/dll/alias.hpp">
<para>Includes alias methods and macro. You can include this header or boost/dll/shared_library.hpp to reduce dependencies in case you do not use the refcountable functions. </para><namespace name="boost">
<namespace name="dll">












</namespace>
</namespace>
<macro name="BOOST_DLL_FORCE_ALIAS_INSTANTIATION"><description><para>Define this macro to explicitly specify translation unit in which alias must be instantiated. See section 'Limitations' for more info. You may find usage examples in source codes of almost each tutorial. Must be used in code, when <link linkend="BOOST_DLL_FORCE_NO_WEAK_EXPORTS">BOOST_DLL_FORCE_NO_WEAK_EXPORTS</link> is defined </para></description></macro>
<macro name="BOOST_DLL_FORCE_NO_WEAK_EXPORTS"><description><para>Define this macro to disable exporting weak symbols and start using the <link linkend="BOOST_DLL_FORCE_ALIAS_INSTANTIATION">BOOST_DLL_FORCE_ALIAS_INSTANTIATION</link>. This may be useful for working around linker problems or to test your program for compatibility with linkers that do not support export of weak symbols. </para></description></macro>
<macro name="BOOST_DLL_SELECTANY"><purpose>Macro that allows linker to select any occurrence of this symbol instead of failing with 'multiple definitions' error at linktime. </purpose><description><para>This macro does not work on Android, IBM XL C/C++ and MinGW+Windows because of linker problems with exporting weak symbols (See <ulink url="https://code.google.com/p/android/issues/detail?id=70206,">https://code.google.com/p/android/issues/detail?id=70206,</ulink> <ulink url="https://sourceware.org/bugzilla/show_bug.cgi?id=17480">https://sourceware.org/bugzilla/show_bug.cgi?id=17480</ulink>) </para></description></macro>
<macro name="BOOST_DLL_SECTION" kind="functionlike"><macro-parameter name="SectionName"><description><para>Name of the section. Must be a valid C identifier without quotes not longer than 8 bytes. </para></description></macro-parameter><macro-parameter name="Permissions"><description><para>Can be "read" or "write" (without quotes!). </para></description></macro-parameter><purpose>Macro that puts symbol to a specific section. On MacOS all the sections are put into "__DATA" segment. </purpose><description><para>
</para></description></macro>
<macro name="BOOST_DLL_ALIAS" kind="functionlike"><macro-parameter name="FunctionOrVar"><description><para>Function or variable for which an alias must be made. </para></description></macro-parameter><macro-parameter name="AliasName"><description><para>Name of the alias. Must be a valid C identifier.</para></description></macro-parameter><purpose>Makes an alias name for exported function or variable. </purpose><description><para>This macro is useful in cases of long mangled C++ names. For example some <computeroutput>void boost::foo(std::string)</computeroutput> function name will change to something like <computeroutput>N5boostN3foosE</computeroutput> after mangling. Importing function by <computeroutput>N5boostN3foosE</computeroutput> name does not looks user friendly, especially assuming the fact that different compilers have different mangling schemes. AliasName is the name that won't be mangled and can be used as a portable import name.</para><para>Can be used in any namespace, including global. FunctionOrVar must be fully qualified, so that address of it could be taken. Multiple different aliases for a single variable/function are allowed.</para><para>Make sure that AliasNames are unique per library/executable. Functions or variables in global namespace must not have names same as AliasNames.</para><para>Same AliasName in different translation units must point to the same FunctionOrVar.</para><para>Puts all the aliases into the <emphasis role="bold">"boostdll"</emphasis> read only section of the binary. Equal to  <link linkend="BOOST_DLL_ALIAS_SECTIONED">BOOST_DLL_ALIAS_SECTIONED</link>(FunctionOrVar, AliasName, boostdll).</para><para>
<emphasis role="bold">Example:</emphasis> <programlisting language="c++">namespace foo {
  void bar(std::string&amp;);

  BOOST_DLL_ALIAS(foo::bar, foo_bar)
}

BOOST_DLL_ALIAS(foo::bar, foo_bar_another_alias_name)
</programlisting></para><para><emphasis role="bold">See: <link linkend="BOOST_DLL_ALIAS_SECTIONED">BOOST_DLL_ALIAS_SECTIONED</link></emphasis> for making alias in a specific section. </para></description></macro>
<macro name="BOOST_DLL_ALIAS_SECTIONED" kind="functionlike"><macro-parameter name="FunctionOrVar"><description><para>Function or variable for which an alias must be made. </para></description></macro-parameter><macro-parameter name="AliasName"><description><para>Name of the alias. Must be a valid C identifier. </para></description></macro-parameter><macro-parameter name="SectionName"><description><para>Name of the section. Must be a valid C identifier without quotes not longer than 8 bytes.</para></description></macro-parameter><purpose>Same as <link linkend="BOOST_DLL_ALIAS">BOOST_DLL_ALIAS</link> but puts alias name into the user specified section. </purpose><description><para>
<emphasis role="bold">Example:</emphasis> <programlisting language="c++">namespace foo {
  void bar(std::string&amp;);

  BOOST_DLL_ALIAS_SECTIONED(foo::bar, foo_bar, sect_1) // section "sect_1" now exports "foo_bar"
}
</programlisting> </para></description></macro>
<macro name="BOOST_DLL_AUTO_ALIAS" kind="functionlike"><macro-parameter name="FunctionOrVar"><description><para>Function or variable for which an unmangled alias must be made.</para></description></macro-parameter><purpose>Exports variable or function with unmangled alias name. </purpose><description><para>This macro is useful in cases of long mangled C++ names. For example some <computeroutput>void boost::foo(std::string)</computeroutput> function name will change to something like <computeroutput>N5boostN3foosE</computeroutput> after mangling. Importing function by <computeroutput>N5boostN3foosE</computeroutput> name does not looks user friendly, especially assuming the fact that different compilers have different mangling schemes.*</para><para>Must be used in scope where FunctionOrVar declared. FunctionOrVar must be a valid C name, which means that it must not contain <computeroutput>::</computeroutput>.</para><para>Functions or variables in global namespace must not have names same as FunctionOrVar.</para><para>Puts all the aliases into the <emphasis role="bold">"boostdll"</emphasis> read only section of the binary. Almost same as  <link linkend="BOOST_DLL_ALIAS">BOOST_DLL_ALIAS</link>(FunctionOrVar, FunctionOrVar).</para><para>
<emphasis role="bold">Example:</emphasis> <programlisting language="c++">namespace foo {
  void bar(std::string&amp;);
  BOOST_DLL_AUTO_ALIAS(bar)
}
</programlisting></para><para><emphasis role="bold">See: <link linkend="BOOST_DLL_ALIAS">BOOST_DLL_ALIAS</link></emphasis> for making an alias with different names. </para></description></macro>
</header>
<header name="boost/dll/config.hpp">
<para>Imports filesystem, error_code, errc, system_error, make_error_code from Boost or C++17 into <computeroutput>boost::dll::fs</computeroutput> namespace. </para><namespace name="boost">
<namespace name="dll">
<namespace name="fs">
<typedef name="path"><description><para>Alias to <computeroutput>std::filesystem::path</computeroutput> if <link linkend="BOOST_DLL_USE_STD_FS">BOOST_DLL_USE_STD_FS</link> is defined by user. Alias to <computeroutput>boost::filesystem::path</computeroutput> otherwise. </para></description><type>std::conditional_t&lt; BOOST_DLL_USE_STD_FS, std::filesystem::path, boost::filesystem::path &gt;</type></typedef>
<typedef name="error_code"><description><para>Alias to <computeroutput>std::error_code</computeroutput> if <link linkend="BOOST_DLL_USE_STD_FS">BOOST_DLL_USE_STD_FS</link> is defined by user. boost::system::error_code otherwise. </para></description><type>std::conditional_t&lt; BOOST_DLL_USE_STD_FS, std::error_code, boost::system::error_code &gt;</type></typedef>
<typedef name="system_error"><description><para>Alias to <computeroutput>std::system_error</computeroutput> if <link linkend="BOOST_DLL_USE_STD_FS">BOOST_DLL_USE_STD_FS</link> is defined by user. Alias to <computeroutput>boost::system::system_error</computeroutput> otherwise. </para></description><type>std::conditional_t&lt; BOOST_DLL_USE_STD_FS, std::system_error, boost::system::system_error &gt;</type></typedef>
</namespace>












</namespace>
</namespace>
<macro name="BOOST_DLL_USE_STD_FS"><purpose>Define this macro to make Boost.DLL use C++17's std::filesystem::path, std::system_error and std::error_code. </purpose></macro>
</header>
<header name="boost/dll/library_info.hpp">
<para>Contains only the <classname alt="boost::dll::library_info">boost::dll::library_info</classname> class that is capable of extracting different information from binaries. </para><namespace name="boost">
<namespace name="dll">
<class name="library_info"><inherit access="private">noncopyable</inherit><purpose>Class that is capable of extracting different information from a library or binary file. Currently understands ELF, MACH-O and PE formats on all the platforms. </purpose><method-group name="public member functions">
<method name="sections"><type>std::vector&lt; std::string &gt;</type><description><para>
</para></description><returns><para>List of sections that exist in binary file. </para>
</returns></method>
<method name="symbols"><type>std::vector&lt; std::string &gt;</type><description><para>
</para></description><returns><para>List of all the exportable symbols from all the sections that exist in binary file. </para>
</returns></method>
<method name="symbols"><type>std::vector&lt; std::string &gt;</type><parameter name="section_name"><paramtype>const char *</paramtype><description><para>Name of the section from which symbol names must be returned. </para></description></parameter><description><para>

</para></description><returns><para>List of symbols from the specified section. </para>
</returns></method>
<method name="symbols"><type>std::vector&lt; std::string &gt;</type><parameter name="section_name"><paramtype>const std::string &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="library_path"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>Path to the binary file from which the info must be extracted. </para></description></parameter><parameter name="throw_if_not_native_format"><paramtype>bool</paramtype><default>true</default><description><para>Throw an exception if this file format is not supported by OS. </para></description></parameter><description><para>Opens file with specified path and prepares for information extraction. 
</para></description></constructor>
</class>











</namespace>
</namespace>
</header>
<header name="boost/dll/runtime_symbol_info.hpp">
<para>Provides methods for getting acceptable by <classname alt="boost::dll::shared_library">boost::dll::shared_library</classname> location of symbol, source line or program. </para><namespace name="boost">
<namespace name="dll">




<overloaded-function name="symbol_location_ptr"><signature><type>boost::dll::fs::path</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="ptr_to_symbol"><paramtype>T</paramtype><description><para>Pointer to symbol which location is to be determined. </para></description></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter></signature><signature><type>boost::dll::fs::path</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="ptr_to_symbol"><paramtype>T</paramtype></parameter></signature><description><para>On success returns full path and name to the binary object that holds symbol pointed by ptr_to_symbol.</para><para>


<emphasis role="bold">Examples:</emphasis> <programlisting language="c++">int main() {
   dll::symbol_location_ptr(std::set_terminate(0));       // returns "/some/path/libmy_terminate_handler.so"
   dll::symbol_location_ptr(::signal(SIGSEGV, SIG_DFL));  // returns "/some/path/libmy_symbol_handler.so"
}
</programlisting> </para></description><returns><para>Path to the binary object that holds symbol or empty path in case error. </para>
</returns><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. Overload that does not accept <link linkend="boost.dll.fs.error_code">boost::dll::fs::error_code</link> also throws <link linkend="boost.dll.fs.system_error">boost::dll::fs::system_error</link>.</simpara></throws></overloaded-function>

<overloaded-function name="symbol_location"><signature><type>boost::dll::fs::path</type><template>
          <template-type-parameter name="T"><purpose><para>Type of the symbol, must not be explicitly specified. </para></purpose></template-type-parameter>
        </template><parameter name="symbol"><paramtype>const T &amp;</paramtype><description><para>Symbol which location is to be determined. </para></description></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter></signature><signature><type>boost::dll::fs::path</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="symbol"><paramtype>const T &amp;</paramtype></parameter></signature><description><para>On success returns full path and name of the binary object that holds symbol.</para><para>



<emphasis role="bold">Examples:</emphasis> <programlisting language="c++">int var;
void foo() {}

int main() {
   dll::symbol_location(var);                     // returns program location
   dll::symbol_location(foo);                     // returns program location
   dll::symbol_location(std::cerr);               // returns location of libstdc++: "/usr/lib/x86_64-linux-gnu/libstdc++.so.6"
   dll::symbol_location(std::placeholders::_1);   // returns location of libstdc++: "/usr/lib/x86_64-linux-gnu/libstdc++.so.6"
   dll::symbol_location(std::puts);               // returns location of libc: "/lib/x86_64-linux-gnu/libc.so.6"
}
</programlisting> </para></description><returns><para>Path to the binary object that holds symbol or empty path in case error. </para>
</returns><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. Overload that does not accept <link linkend="boost.dll.fs.error_code">boost::dll::fs::error_code</link> also throws <link linkend="boost.dll.fs.system_error">boost::dll::fs::system_error</link>.</simpara></throws></overloaded-function>

<overloaded-function name="this_line_location"><signature><type>boost::dll::fs::path</type><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter></signature><signature><type>boost::dll::fs::path</type></signature><description><para>On success returns full path and name of the binary object that holds the current line of code (the line in which the <computeroutput>this_line_location()</computeroutput> method was called).</para><para>

</para></description><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. Overload that does not accept <link linkend="boost.dll.fs.error_code">boost::dll::fs::error_code</link> also throws <link linkend="boost.dll.fs.system_error">boost::dll::fs::system_error</link>. </simpara></throws></overloaded-function>

<overloaded-function name="program_location"><signature><type>boost::dll::fs::path</type><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter></signature><signature><type>boost::dll::fs::path</type></signature><description><para>On success returns full path and name of the currently running program (the one which contains the <computeroutput>main()</computeroutput> function).</para><para>Return value can be used as a parameter for <classname alt="boost::dll::shared_library">shared_library</classname>. See Tutorial "Linking plugin into the executable" for usage example. Flag '-rdynamic' must be used when linking the plugin into the executable on Linux OS.</para><para>

</para></description><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. Overload that does not accept <link linkend="boost.dll.fs.error_code">boost::dll::fs::error_code</link> also throws <link linkend="boost.dll.fs.system_error">boost::dll::fs::system_error</link>. </simpara></throws></overloaded-function>

</namespace>
</namespace>
</header>
<header name="boost/dll/shared_library.hpp">
<para>Contains the <classname alt="boost::dll::shared_library">boost::dll::shared_library</classname> class, core class for all the DLL/DSO operations. </para><namespace name="boost">
<namespace name="dll">
<class name="shared_library"><purpose>This class can be used to load a Dynamic link libraries (DLL's) or Shared Libraries, also know as dynamic shared objects (DSO's) and get their exported symbols (functions and variables). </purpose><description><para><classname alt="boost::dll::shared_library">shared_library</classname> instances share reference count to an actual loaded DLL/DSO, so it is safe and memory efficient to have multiple instances of <classname alt="boost::dll::shared_library">shared_library</classname> referencing the same DLL/DSO even if those instances were loaded using different paths (relative + absolute) referencing the same object.</para><para>On Linux/POSIX link with library "dl". "-fvisibility=hidden" flag is also recommended for use on Linux/POSIX. </para></description><typedef name="native_handle_t"><type>platform_specific</type></typedef>
<method-group name="public member functions">
<method name="assign"><type><classname>shared_library</classname> &amp;</type><parameter name="lib"><paramtype>const <classname>shared_library</classname> &amp;</paramtype><description><para>A library to copy. </para></description></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter><description><para>Makes *this share the same shared object as lib. If *this is loaded, then unloads it.</para><para>


</para></description><postconditions><para>lib.location() == this-&gt;location(), lib == *this </para>
</postconditions><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. </simpara></throws></method>
<method name="assign"><type><classname>shared_library</classname> &amp;</type><parameter name="lib"><paramtype>const <classname>shared_library</classname> &amp;</paramtype><description><para>A library instance to assign from. </para></description></parameter><description><para>Makes *this share the same shared object as lib. If *this is loaded, then unloads it.</para><para>


</para></description><postconditions><para>lib.location() == this-&gt;location() </para>
</postconditions><throws><simpara><classname/> </simpara></throws></method>
<method name="load"><type>void</type><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>Library file name. Can handle std::string, const char*, std::wstring, const wchar_t* or <link linkend="boost.dll.fs.path">boost::dll::fs::path</link>. </para></description></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype><default>load_mode::default_mode</default><description><para>A mode that will be used on library load. </para></description></parameter><description><para>Loads a library by specified path with a specified mode.</para><para>Note that if some library is already loaded in this instance, load will call unload() and then load the new provided library.</para><para>

</para></description><throws><simpara><classname/> </simpara></throws></method>
<method name="load"><type>void</type><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>Library file name. Can handle std::string, const char*, std::wstring, const wchar_t* or <link linkend="boost.dll.fs.path">boost::dll::fs::path</link>. </para></description></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype><default>load_mode::default_mode</default><description><para>A mode that will be used on library load. </para></description></parameter><description><para>Loads a library by specified path with a specified mode.</para><para>Note that if some library is already loaded in this instance, load will call unload() and then load the new provided library.</para><para>

</para></description><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. </simpara></throws></method>
<method name="load"><type>void</type><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="unload" cv="noexcept"><type>void</type><description><para>Unloads a shared library. If library was loaded multiple times by different instances, the actual DLL/DSO won't be unloaded until there is at least one instance that references the DLL/DSO.</para><para>

</para></description><postconditions><para>this-&gt;is_loaded() returns false. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="is_loaded" cv="const noexcept"><type>bool</type><description><para>Check if an library is loaded.</para><para>

</para></description><returns><para>true if a library has been loaded. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="operator!" cv="const noexcept"><type>bool</type><description><para>Check if an library is not loaded.</para><para>

</para></description><returns><para>true if a library has not been loaded. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>bool</type><description><para>Check if an library is loaded.</para><para>

</para></description><returns><para>true if a library has been loaded. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="has" cv="const noexcept"><type>bool</type><parameter name="symbol_name"><paramtype>const char *</paramtype><description><para>Null-terminated symbol name. Can handle std::string, char*, const char*. </para></description></parameter><description><para>Search for a given symbol on loaded library. Works for all symbols, including alias names.</para><para>


</para></description><returns><para><computeroutput>true</computeroutput> if the loaded library contains a symbol with a given name. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="has" cv="const noexcept"><type>bool</type><parameter name="symbol_name"><paramtype>const std::string &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="get" cv="const"><type>boost::enable_if_c&lt; boost::is_member_pointer&lt; T &gt;::value||boost::is_reference&lt; T &gt;::value, T &gt;::type</type><template>
          <template-type-parameter name="T"><purpose><para>Type of the symbol that we are going to import. Must be explicitly specified. </para></purpose></template-type-parameter>
        </template><parameter name="symbol_name"><paramtype>const std::string &amp;</paramtype><description><para>Null-terminated symbol name. Can handle std::string, char*, const char*. </para></description></parameter><description><para>Returns reference to the symbol (function or variable) with the given name from the loaded library. This call will always succeed and throw nothing if call to <computeroutput>has(const char* )</computeroutput> member function with the same symbol name returned <computeroutput>true</computeroutput>.</para><para><emphasis role="bold">Example:</emphasis> <programlisting language="c++">int&amp; i0 = lib.get&lt;int&gt;("integer_name");
int&amp; i1 = *lib.get&lt;int*&gt;("integer_alias_name");
</programlisting></para><para>



</para></description><returns><para>Reference to the symbol. </para>
</returns><throws><simpara><classname/> </simpara></throws></method>
<method name="get" cv="const"><type>boost::disable_if_c&lt; boost::is_member_pointer&lt; T &gt;::value||boost::is_reference&lt; T &gt;::value, T &amp; &gt;::type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="symbol_name"><paramtype>const std::string &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="get" cv="const"><type>boost::enable_if_c&lt; boost::is_member_pointer&lt; T &gt;::value||boost::is_reference&lt; T &gt;::value, T &gt;::type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="symbol_name"><paramtype>const char *</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="get" cv="const"><type>boost::disable_if_c&lt; boost::is_member_pointer&lt; T &gt;::value||boost::is_reference&lt; T &gt;::value, T &amp; &gt;::type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="symbol_name"><paramtype>const char *</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="get_alias" cv="const"><type>T &amp;</type><template>
          <template-type-parameter name="T"><purpose><para>Type of the symbol that we are going to import. Must be explicitly specified.. </para></purpose></template-type-parameter>
        </template><parameter name="alias_name"><paramtype>const char *</paramtype><description><para>Null-terminated alias symbol name. Can handle std::string, char*, const char*. </para></description></parameter><description><para>Returns a symbol (function or variable) from a shared library by alias name of the symbol.</para><para><emphasis role="bold">Example:</emphasis> <programlisting language="c++">int&amp; i = lib.get_alias&lt;int&gt;("integer_alias_name");
</programlisting></para><para>


</para></description><throws><simpara><classname/> </simpara></throws></method>
<method name="get_alias" cv="const"><type>T &amp;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="alias_name"><paramtype>const std::string &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="native" cv="const noexcept"><type>native_handle_t</type><description><para>Returns the native handler of the loaded library.</para><para>
</para></description><returns><para>Platform-specific handle. </para>
</returns></method>
<method name="location" cv="const"><type>boost::dll::fs::path</type><description><para>Returns full path and name of this shared object.</para><para><emphasis role="bold">Example:</emphasis> <programlisting language="c++">shared_library lib("test_lib.dll");
filesystem::path full_path = lib.location(); // C:\Windows\System32\test_lib.dll
</programlisting></para><para>

</para></description><returns><para>Full path to the shared library. </para>
</returns><throws><simpara><classname/> </simpara></throws></method>
<method name="location" cv="const"><type>boost::dll::fs::path</type><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter><description><para>Returns full path and name of shared module.</para><para><emphasis role="bold">Example:</emphasis> <programlisting language="c++">shared_library lib("test_lib.dll");
filesystem::path full_path = lib.location(); // C:\Windows\System32\test_lib.dll
</programlisting></para><para>


</para></description><returns><para>Full path to the shared library. </para>
</returns><throws><simpara><classname>std::bad_alloc.</classname> </simpara></throws></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="rhs"><paramtype><classname>shared_library</classname> &amp;</paramtype><description><para>Library to swap with. </para></description></parameter><description><para>Swaps two libraries. Does not invalidate existing symbols and functions loaded from libraries.</para><para>

</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
</method-group>
<constructor cv="noexcept"><description><para>Creates in anstance that does not reference any DLL/DSO.</para><para>

</para></description><postconditions><para>this-&gt;is_loaded() returns false. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor><parameter name="lib"><paramtype>const <classname>shared_library</classname> &amp;</paramtype><description><para>A library to copy. </para></description></parameter><description><para>Copy constructor that increments the reference count of an underlying shared library. Same as calling constructor with <computeroutput>lib.location()</computeroutput> parameter.</para><para>


</para></description><postconditions><para>lib == *this </para>
</postconditions><throws><simpara><classname/> </simpara></throws></constructor>
<constructor><parameter name="lib"><paramtype>const <classname>shared_library</classname> &amp;</paramtype><description><para>A shared library to copy. </para></description></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter><description><para>Copy constructor that increments the reference count of an underlying shared library. Same as calling constructor with <computeroutput>lib.location(), ec</computeroutput> parameters.</para><para>


</para></description><postconditions><para>lib == *this </para>
</postconditions><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. </simpara></throws></constructor>
<constructor cv="noexcept"><parameter name="lib"><paramtype><classname>shared_library</classname> &amp;&amp;</paramtype><description><para>A shared library to move from. </para></description></parameter><description><para>Move constructor. Does not invalidate existing symbols and functions loaded from lib.</para><para>


</para></description><postconditions><para>lib.is_loaded() returns false, this-&gt;is_loaded() return true. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor specifiers="explicit"><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>Library file name. Can handle std::string, const char*, std::wstring, const wchar_t* or <link linkend="boost.dll.fs.path">boost::dll::fs::path</link>. </para></description></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype><default>load_mode::default_mode</default><description><para>A mode that will be used on library load. </para></description></parameter><description><para>Loads a library by specified path with a specified mode.</para><para>

</para></description><throws><simpara><classname/> </simpara></throws></constructor>
<constructor><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>Library file name. Can handle std::string, const char*, std::wstring, const wchar_t* or <link linkend="boost.dll.fs.path">boost::dll::fs::path</link>. </para></description></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype><default>load_mode::default_mode</default><description><para>A mode that will be used on library load. </para></description></parameter><description><para>Loads a library by specified path with a specified mode.</para><para>

</para></description><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. </simpara></throws></constructor>
<constructor><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></constructor>
<copy-assignment><type><classname>shared_library</classname> &amp;</type><parameter name="lib"><paramtype>const <classname>shared_library</classname> &amp;</paramtype><description><para>A shared library to assign from. </para></description></parameter><description><para>Assignment operator. If this-&gt;is_loaded() then calls this-&gt;unload(). Does not invalidate existing symbols and functions loaded from lib.</para><para>


</para></description><postconditions><para>lib == *this </para>
</postconditions><throws><simpara><classname/> </simpara></throws></copy-assignment>
<copy-assignment cv="noexcept"><type><classname>shared_library</classname> &amp;</type><parameter name="lib"><paramtype><classname>shared_library</classname> &amp;&amp;</paramtype><description><para>A library to move from. </para></description></parameter><description><para>Move assignment operator. If this-&gt;is_loaded() then calls this-&gt;unload(). Does not invalidate existing symbols and functions loaded from lib.</para><para>


</para></description><postconditions><para>lib.is_loaded() returns false. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></copy-assignment>
<destructor><description><para>Destroys the object by calling <computeroutput>unload()</computeroutput>. If library was loaded multiple times by different instances, the actual DLL/DSO won't be unloaded until there is at least one instance that references the DLL/DSO.</para><para>
</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></destructor>
<method-group name="public static functions">
<method name="suffix" specifiers="static"><type>boost::dll::fs::path</type><description><para>Returns suffix of shared module: in a call to load() or the constructor/load.</para><para>
</para></description><returns><para>The suffix od shared module: ".dll" (Windows), ".so" (Unix/Linux/BSD), ".dylib" (MacOS/IOS) </para>
</returns></method>
<method name="decorate" specifiers="static"><type>boost::dll::fs::path</type><parameter name="sl"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>the module name and path to decorate - for instance : /usr/lib/boost</para></description></parameter><description><para>Returns the decorated path to a shared module name, i.e. with needed prefix/suffix added.</para><para><emphasis role="bold">Recommendations:</emphasis> Use <computeroutput>load</computeroutput> with <computeroutput>load_mode::append_decorations</computeroutput> instead of constructing the decorated path via <computeroutput>decorate()</computeroutput> and loading by it.</para><para>For instance, for a path like "path/to/boost" it returns :<itemizedlist>
<listitem><para>path/to/libboost.so on posix platforms</para>
</listitem><listitem><para>path/to/libboost.dylib on OSX</para>
</listitem><listitem><para>path/to/boost.dll on Windows</para>
</listitem></itemizedlist>
</para><para>Method handles both relative and absolute paths.</para><para><itemizedlist>
<listitem><para>Windows note: <computeroutput>decorate()</computeroutput> does not prepend "lib" to the decorated path. Use <computeroutput>load</computeroutput> with <computeroutput>load_mode::append_decorations</computeroutput> for MinGW compatibility purpose.</para>
</listitem><listitem><para>Posix note: if the initial module name is already prepended with lib, only the suffix() is appended to the path</para>
</listitem></itemizedlist>
</para><para>

</para></description><returns><para>The decorated unportable path that may not exists in the filesystem or could be wrong due to platform specifics. </para>
</returns></method>
</method-group>
</class><function name="operator=="><type>bool</type><parameter name="lhs"><paramtype>const <classname>shared_library</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>shared_library</classname> &amp;</paramtype></parameter><purpose>Very fast equality check that compares the actual DLL/DSO objects. Throws nothing. </purpose></function>
<function name="operator!="><type>bool</type><parameter name="lhs"><paramtype>const <classname>shared_library</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>shared_library</classname> &amp;</paramtype></parameter><purpose>Very fast inequality check that compares the actual DLL/DSO objects. Throws nothing. </purpose></function>
<function name="operator&lt;"><type>bool</type><parameter name="lhs"><paramtype>const <classname>shared_library</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>shared_library</classname> &amp;</paramtype></parameter><purpose>Compare the actual DLL/DSO objects without any guarantee to be stable between runs. Throws nothing. </purpose></function>
<function name="swap"><type>void</type><parameter name="lhs"><paramtype><classname>shared_library</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype><classname>shared_library</classname> &amp;</paramtype></parameter><purpose>Swaps two shared libraries. Does not invalidate symbols and functions loaded from libraries. Throws nothing. </purpose></function>








</namespace>
</namespace>
</header>
<header name="boost/dll/shared_library_load_mode.hpp">
<para>Contains only the boost::dll::load_mode::type enum and operators related to it. </para><namespace name="boost">
<namespace name="dll">
<namespace name="load_mode">
<enum name="type"><enumvalue name="default_mode"><description><para>Default open mode. See the <emphasis role="bold">Default:</emphasis> comments below to find out the flags that are enabled by default. </para></description></enumvalue><enumvalue name="dont_resolve_dll_references"><description><para><emphasis role="bold">Platforms:</emphasis> Windows</para><para><emphasis role="bold">Default:</emphasis> disabled</para><para>If this value is used, and the executable module is a DLL, the system does not call DllMain for process and thread initialization and termination. Also, the system does not load additional executable modules that are referenced by the specified module.</para><para>Note Do not use this value; it is provided only for backward compatibility. If you are planning to access only data or resources in the DLL, use LOAD_LIBRARY_AS_DATAFILE_EXCLUSIVE or LOAD_LIBRARY_AS_IMAGE_RESOURCE or both. </para></description></enumvalue><enumvalue name="load_ignore_code_authz_level"><description><para><emphasis role="bold">Platforms:</emphasis> Windows</para><para><emphasis role="bold">Default:</emphasis> disabled</para><para>If this value is used, the system does not check AppLocker rules or apply Software Restriction Policies for the DLL. </para></description></enumvalue><enumvalue name="load_with_altered_search_path"><description><para><emphasis role="bold">Platforms:</emphasis> Windows</para><para><emphasis role="bold">Default:</emphasis> disabled</para><para>If this value is used and lpFileName specifies an absolute path, the system uses the alternate file search strategy.</para><para>This value cannot be combined with any LOAD_LIBRARY_SEARCH flag. </para></description></enumvalue><enumvalue name="rtld_lazy"><description><para><emphasis role="bold">Platforms:</emphasis> POSIX</para><para><emphasis role="bold">Default:</emphasis> enabled</para><para>Relocations shall be performed at an implementation-defined time, ranging from the time of the dlopen() call until the first reference to a given symbol occurs.</para><para>Specifying RTLD_LAZY should improve performance on implementations supporting dynamic symbol binding as a process may not reference all of the functions in any given object. And, for systems supporting dynamic symbol resolution for normal process execution, this behavior mimics the normal handling of process execution. </para></description></enumvalue><enumvalue name="rtld_now"><description><para><emphasis role="bold">Platforms:</emphasis> POSIX</para><para><emphasis role="bold">Default:</emphasis> disabled</para><para>All necessary relocations shall be performed when the object is first loaded. This may waste some processing if relocations are performed for functions that are never referenced. This behavior may be useful for plugins that need to know as soon as an object is loaded that all symbols referenced during execution are available. </para></description></enumvalue><enumvalue name="rtld_global"><description><para><emphasis role="bold">Platforms:</emphasis> POSIX</para><para><emphasis role="bold">Default:</emphasis> disabled</para><para>The object's symbols shall be made available for the relocation processing of any other object. In addition, symbol lookup using dlopen(0, mode) and an associated dlsym() allows objects loaded with this mode to be searched. </para></description></enumvalue><enumvalue name="rtld_local"><description><para><emphasis role="bold">Platforms:</emphasis> POSIX</para><para><emphasis role="bold">Default:</emphasis> enabled</para><para>The object's symbols shall not be made available for the relocation processing of any other object.</para><para>This is a default Windows behavior that can not be changed. </para></description></enumvalue><enumvalue name="rtld_deepbind"><description><para><emphasis role="bold">Platforms:</emphasis> POSIX (requires glibc &gt;= 2.3.4)</para><para><emphasis role="bold">Default:</emphasis> disabled</para><para>The object will use its own symbols in preference to global symbols with the same name contained in libraries that have already been loaded. This flag is not specified in POSIX.1-2001. </para></description></enumvalue><enumvalue name="append_decorations"><description><para><emphasis role="bold">Platforms:</emphasis> Windows, POSIX</para><para><emphasis role="bold">Default:</emphasis> disabled</para><para>Append a platform specific extension and prefix to shared library filename before trying to load it. If load attempt fails, try to load with exactly specified name.</para><para><emphasis role="bold">Example:</emphasis> <programlisting language="c++">// Opens `./my_plugins/plugin1.dll` on Windows, `./my_plugins/libplugin1.so` on Linux, `./my_plugins/libplugin1.dylib` on MacOS.
// If that fails, loads `./my_plugins/plugin1`
boost::dll::shared_library lib("./my_plugins/plugin1", load_mode::append_decorations);
</programlisting> </para></description></enumvalue><enumvalue name="search_system_folders"><description><para><emphasis role="bold">Platforms:</emphasis> Windows, POSIX</para><para><emphasis role="bold">Default:</emphasis> disabled</para><para>Allow loading from system folders if path to library contains no parent path. </para></description></enumvalue><description><para>Library load modes.</para><para>Each of system family provides own modes. Flags not supported by a particular platform will be silently ignored.</para><para>For a detailed description of platform specific options see: <ulink url="http://msdn.microsoft.com/en-us/library/windows/desktop/ms684179(v=vs.85).aspx">Windows specific options</ulink>, <ulink url="http://pubs.opengroup.org/onlinepubs/000095399/functions/dlopen.html">POSIX specific options</ulink>. </para></description></enum>
<function name="operator|"><type>BOOST_CONSTEXPR type</type><parameter name="left"><paramtype>type</paramtype></parameter><parameter name="right"><paramtype>type</paramtype></parameter><purpose>Free operators for load_mode::type flag manipulation. </purpose></function>
<function name="operator|="><type>BOOST_CXX14_CONSTEXPR type &amp;</type><parameter name="left"><paramtype>type &amp;</paramtype></parameter><parameter name="right"><paramtype>type</paramtype></parameter></function>
<function name="operator&amp;"><type>BOOST_CONSTEXPR type</type><parameter name="left"><paramtype>type</paramtype></parameter><parameter name="right"><paramtype>type</paramtype></parameter></function>
<function name="operator&amp;="><type>BOOST_CXX14_CONSTEXPR type &amp;</type><parameter name="left"><paramtype>type &amp;</paramtype></parameter><parameter name="right"><paramtype>type</paramtype></parameter></function>
<function name="operator^"><type>BOOST_CONSTEXPR type</type><parameter name="left"><paramtype>type</paramtype></parameter><parameter name="right"><paramtype>type</paramtype></parameter></function>
<function name="operator^="><type>BOOST_CXX14_CONSTEXPR type &amp;</type><parameter name="left"><paramtype>type &amp;</paramtype></parameter><parameter name="right"><paramtype>type</paramtype></parameter></function>
<function name="operator~"><type>BOOST_CONSTEXPR type</type><parameter name="left"><paramtype>type</paramtype></parameter></function>
</namespace>












</namespace>
</namespace>
</header>
<header name="boost/dll/smart_library.hpp">
<para>Contains the <classname alt="boost::dll::experimental::smart_library">boost::dll::experimental::smart_library</classname> class for loading mangled symbols. </para><para><warning><para>Extremely experimental! Requires C++11! Will change in next version of Boost! boost/dll/smart_library.hpp is not included in boost/dll.hpp </para>
</warning>
</para><namespace name="boost">
<namespace name="dll">
<namespace name="experimental">
<class name="smart_library"><purpose>This class is an extension of <classname alt="boost::dll::shared_library">shared_library</classname>, which allows to load C++ symbols. </purpose><description><para>This class allows type safe loading of overloaded functions, member-functions, constructors and variables. It also allows to overwrite classes so they can be loaded, while being declared with different names.</para><para><warning><para>Is still very experimental.</para>
</warning>
Currently known limitations:</para><para>Member functions must be defined outside of the class to be exported. That is: <programlisting language="c++">//not exported:
struct BOOST_SYMBOL_EXPORT my_class { void func() {}};
//exported
struct BOOST_SYMBOL_EXPORT my_class { void func();};
void my_class::func() {};
</programlisting></para><para>With the current analysis, the first version does get exported in MSVC. MinGW also does export it, BOOST_SYMBOL_EXPORT is written before it. To allow this on windows one can use BOOST_DLL_MEMBER_EXPORT for this, so that MinGW and MSVC can provide those functions. This does however not work with gcc on linux.</para><para>Direct initialization of members. On linux the following member variable i will not be initialized when using the allocating constructor: <programlisting language="c++">struct BOOST_SYMBOL_EXPORT my_class { int i; my_class() : i(42) {} };
</programlisting></para><para>This does however not happen when the value is set inside the constructor function. </para></description><typedef name="mangled_storage"><type><emphasis>unspecified</emphasis></type></typedef>
<method-group name="public member functions">
<method name="shared_lib" cv="const"><type>const <classname>shared_library</classname> &amp;</type><description><para>Get the underlying <classname alt="boost::dll::shared_library">shared_library</classname> </para></description></method>
<method name="symbol_storage" cv="const"><type>const mangled_storage &amp;</type><description><para>Access to the mangled storage, which is created on construction.</para><para>
</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="symbol_storage"><type>mangled_storage &amp;</type><purpose>Overload, for current development. </purpose></method>
<method name="load"><type>void</type><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>Library file name. Can handle std::string, const char*, std::wstring, const wchar_t* or <link linkend="boost.dll.fs.path">boost::dll::fs::path</link>. </para></description></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype><default>load_mode::default_mode</default><description><para>A mode that will be used on library load. </para></description></parameter><description><para>Loads a library by specified path with a specified mode.</para><para>Note that if some library is already loaded in this instance, load will call unload() and then load the new provided library.</para><para>

</para></description><throws><simpara><classname/> </simpara></throws></method>
<method name="load"><type>void</type><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>Library file name. Can handle std::string, const char*, std::wstring, const wchar_t* or <link linkend="boost.dll.fs.path">boost::dll::fs::path</link>. </para></description></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype><default>load_mode::default_mode</default><description><para>A mode that will be used on library load. </para></description></parameter><description><para>Loads a library by specified path with a specified mode.</para><para>Note that if some library is already loaded in this instance, load will call unload() and then load the new provided library.</para><para>

</para></description><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. </simpara></throws></method>
<method name="load"><type>void</type><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="get_variable" cv="const"><type>T &amp;</type><template>
          <template-type-parameter name="T"><purpose><para>Type of the variable </para></purpose></template-type-parameter>
        </template><parameter name="name"><paramtype>const std::string &amp;</paramtype><description><para>Name of the variable </para></description></parameter><description><para>Load a variable from the referenced library.</para><para>Unlinke shared_library::get this function will also load scoped variables, which also includes static class members.</para><para><note><para>When mangled, MSVC will also check the type.</para>
</note>




</para></description><returns><para>A reference to the variable of type T.</para>
</returns><throws><simpara><classname/> </simpara></throws></method>
<method name="get_function" cv="const"><type>Func &amp;</type><template>
          <template-type-parameter name="Func"><purpose><para>Type of the function, required for determining the overload </para></purpose></template-type-parameter>
        </template><parameter name="name"><paramtype>const std::string &amp;</paramtype><description><para>Name of the function. </para></description></parameter><description><para>Load a function from the referenced library.</para><para><emphasis role="bold">Example:</emphasis> </para><para><programlisting language="c++">smart_library lib("test_lib.so");
typedef int      (&amp;add_ints)(int, int);
typedef double (&amp;add_doubles)(double, double);
add_ints     f1 = lib.get_function&lt;int(int, int)&gt;         ("func_name");
add_doubles  f2 = lib.get_function&lt;double(double, double)&gt;("func_name");
</programlisting></para><para><note><para>When mangled, MSVC will also check the return type.</para>
</note>




</para></description><returns><para>A reference to the function of type F.</para>
</returns><throws><simpara><classname/> </simpara></throws></method>
<method name="get_mem_fn" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="Class"><purpose><para>The class the function is a member of. If Class is const, the function will be assumed as taking a const this-pointer. The same applies for volatile. </para></purpose></template-type-parameter>
          <template-type-parameter name="Func"><purpose><para>Signature of the function, required for determining the overload </para></purpose></template-type-parameter>
        </template><parameter name="name"><paramtype>const std::string &amp;</paramtype><description><para>Name of the function. </para></description></parameter><description><para>Load a member-function from the referenced library.</para><para><emphasis role="bold">Example</emphasis> (import class is MyClass, which is available inside the library and the host):</para><para><programlisting language="c++">smart_library lib("test_lib.so");

typedef int      MyClass(*func)(int);
typedef int   MyClass(*func_const)(int) const;

add_ints     f1 = lib.get_mem_fn&lt;MyClass, int(int)&gt;              ("MyClass::function");
add_doubles  f2 = lib.get_mem_fn&lt;const MyClass, double(double)&gt;("MyClass::function");
</programlisting></para><para><note><para>When mangled, MSVC will also check the return type.</para>
</note>




</para></description><returns><para>A pointer to the member-function with the signature provided</para>
</returns><throws><simpara><classname/> </simpara></throws></method>
<method name="get_constructor" cv="const"><type>constructor&lt; Signature &gt;</type><template>
          <template-type-parameter name="Signature"><purpose><para>Signature of the function, required for determining the overload. The return type is the class which this is the constructor of. </para></purpose></template-type-parameter>
        </template><description><para>Load a constructor from the referenced library.</para><para><emphasis role="bold">Example</emphasis> (import class is MyClass, which is available inside the library and the host):</para><para><programlisting language="c++">smart_library lib("test_lib.so");

constructor&lt;MyClass(int)    f1 = lib.get_mem_fn&lt;MyClass(int)&gt;();
</programlisting></para><para>


</para></description><returns><para>A constructor object.</para>
</returns><throws><simpara><classname/> </simpara></throws></method>
<method name="get_destructor" cv="const"><type>destructor&lt; Class &gt;</type><template>
          <template-type-parameter name="Class"><purpose><para>The class whose destructor shall be loaded </para></purpose></template-type-parameter>
        </template><description><para>Load a destructor from the referenced library.</para><para><emphasis role="bold">Example</emphasis> (import class is MyClass, which is available inside the library and the host):</para><para><programlisting language="c++">smart_library lib("test_lib.so");

destructor&lt;MyClass&gt;     f1 = lib.get_mem_fn&lt;MyClass&gt;();
</programlisting></para><para>


</para></description><returns><para>A destructor object.</para>
</returns><throws><simpara><classname/> </simpara></throws></method>
<method name="get_type_info" cv="const"><type>const std::type_info &amp;</type><template>
          <template-type-parameter name="Class"><purpose><para>The class whose typeinfo shall be loaded </para></purpose></template-type-parameter>
        </template><description><para>Load the typeinfo of the given type.</para><para><emphasis role="bold">Example</emphasis> (import class is MyClass, which is available inside the library and the host):</para><para><programlisting language="c++">smart_library lib("test_lib.so");

std::type_info &amp;ti = lib.get_Type_info&lt;MyClass&gt;();
</programlisting></para><para>


</para></description><returns><para>A reference to a type_info object.</para>
</returns><throws><simpara><classname/> </simpara></throws></method>
<method name="add_type_alias"><type>void</type><template>
          <template-type-parameter name="Alias"/>
        </template><parameter name="name"><paramtype>const std::string &amp;</paramtype><description><para>Name of the class the alias is for.</para></description></parameter><description><para>This function can be used to add a type alias.</para><para>This is to be used, when a class shall be imported, which is not declared on the host side.</para><para>Example: <programlisting language="c++">smart_library lib("test_lib.so");

lib.add_type_alias&lt;MyAlias&gt;("MyClass"); //when using MyAlias, the library will look for MyClass

//get the destructor of MyClass
destructor&lt;MyAlias&gt; dtor = lib.get_destructor&lt;MyAlias&gt;();
</programlisting></para><para>
<note><para>If the alias-type is not large enough for the imported class, it will result in undefined behaviour. </para>
</note>
<warning><para>The alias will only be applied for the type signature, it will not replace the token in the scoped name. </para>
</warning>
</para></description></method>
<method name="unload" cv="noexcept"><type>void</type><description><para>Unloads a shared library. If library was loaded multiple times by different instances, the actual DLL/DSO won't be unloaded until there is at least one instance that references the DLL/DSO.</para><para>

</para></description><postconditions><para>this-&gt;is_loaded() returns false. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="is_loaded" cv="const noexcept"><type>bool</type><description><para>Check if an library is loaded.</para><para>

</para></description><returns><para>true if a library has been loaded. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="operator!" cv="const noexcept"><type>bool</type><description><para>Check if an library is not loaded.</para><para>

</para></description><returns><para>true if a library has not been loaded. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>bool</type><purpose>bool() const </purpose><description><para>bool() const  </para></description></method>
<method name="has" cv="const noexcept"><type>bool</type><parameter name="symbol_name"><paramtype>const char *</paramtype><description><para>Null-terminated symbol name. Can handle std::string, char*, const char*. </para></description></parameter><description><para>Search for a given symbol on loaded library. Works for all symbols, including alias names.</para><para>


</para></description><returns><para><computeroutput>true</computeroutput> if the loaded library contains a symbol with a given name. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="has" cv="const noexcept"><type>bool</type><parameter name="symbol_name"><paramtype>const std::string &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="assign"><type><classname>smart_library</classname> &amp;</type><parameter name="lib"><paramtype>const <classname>smart_library</classname> &amp;</paramtype><description><para>A library instance to assign from. </para></description></parameter><description><para>Makes *this share the same shared object as lib. If *this is loaded, then unloads it.</para><para>


</para></description><postconditions><para>lib.location() == this-&gt;location() </para>
</postconditions><throws><simpara><classname/> </simpara></throws></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="rhs"><paramtype><classname>smart_library</classname> &amp;</paramtype><description><para>Library to swap with. </para></description></parameter><description><para>Swaps two libraries. Does not invalidate existing symbols and functions loaded from libraries.</para><para>

</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
</method-group>
<constructor cv="noexcept"><description><para>Creates in anstance that does not reference any DLL/DSO.</para><para>

</para></description><postconditions><para>this-&gt;is_loaded() returns false. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>Library file name. Can handle std::string, const char*, std::wstring, const wchar_t* or <link linkend="boost.dll.fs.path">boost::dll::fs::path</link>. </para></description></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype><default>load_mode::default_mode</default><description><para>A mode that will be used on library load. </para></description></parameter><description><para>Loads a library by specified path with a specified mode.</para><para>

</para></description><throws><simpara><classname/> </simpara></throws></constructor>
<constructor><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype><description><para>Library file name. Can handle std::string, const char*, std::wstring, const wchar_t* or <link linkend="boost.dll.fs.path">boost::dll::fs::path</link>. </para></description></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype><description><para>Variable that will be set to the result of the operation. </para></description></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype><default>load_mode::default_mode</default><description><para>A mode that will be used on library load. </para></description></parameter><description><para>Loads a library by specified path with a specified mode.</para><para>

</para></description><throws><simpara><classname>std::bad_alloc</classname> in case of insufficient memory. </simpara></throws></constructor>
<constructor><parameter name="lib_path"><paramtype>const boost::dll::fs::path &amp;</paramtype></parameter><parameter name="mode"><paramtype>load_mode::type</paramtype></parameter><parameter name="ec"><paramtype>boost::dll::fs::error_code &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></constructor>
<constructor cv="noexcept"><parameter name="lib"><paramtype>const <classname>smart_library</classname> &amp;</paramtype><description><para>A <classname alt="boost::dll::experimental::smart_library">smart_library</classname> to move from.</para></description></parameter><description><para>copy a <classname alt="boost::dll::experimental::smart_library">smart_library</classname> object.</para><para>

</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor cv="noexcept"><parameter name="lib"><paramtype><classname>smart_library</classname> &amp;&amp;</paramtype><description><para>A <classname alt="boost::dll::experimental::smart_library">smart_library</classname> to move from.</para></description></parameter><description><para>Move a <classname alt="boost::dll::experimental::smart_library">smart_library</classname> object.</para><para>

</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor specifiers="explicit" cv="noexcept"><parameter name="lib"><paramtype>const <classname>shared_library</classname> &amp;</paramtype><description><para>A <classname alt="boost::dll::shared_library">shared_library</classname> to move from.</para></description></parameter><description><para>Construct from a <classname alt="boost::dll::shared_library">shared_library</classname> object.</para><para>

</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor specifiers="explicit" cv="noexcept"><parameter name="lib"><paramtype><classname>shared_library</classname> &amp;&amp;</paramtype><description><para>A <classname alt="boost::dll::shared_library">shared_library</classname> to move from.</para></description></parameter><description><para>Construct from a <classname alt="boost::dll::shared_library">shared_library</classname> object.</para><para>

</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<destructor><description><para>Destroys the <classname alt="boost::dll::experimental::smart_library">smart_library</classname>. <computeroutput>unload()</computeroutput> is called if the DLL/DSO was loaded. If library was loaded multiple times by different instances of <classname alt="boost::dll::shared_library">shared_library</classname>, the actual DLL/DSO won't be unloaded until there is at least one instance of <classname alt="boost::dll::shared_library">shared_library</classname>.</para><para>
</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></destructor>
</class><function name="operator=="><type>bool</type><parameter name="lhs"><paramtype>const <classname>smart_library</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>smart_library</classname> &amp;</paramtype></parameter><purpose>Very fast equality check that compares the actual DLL/DSO objects. Throws nothing. </purpose></function>
<function name="operator!="><type>bool</type><parameter name="lhs"><paramtype>const <classname>smart_library</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>smart_library</classname> &amp;</paramtype></parameter><purpose>Very fast inequality check that compares the actual DLL/DSO objects. Throws nothing. </purpose></function>
<function name="operator&lt;"><type>bool</type><parameter name="lhs"><paramtype>const <classname>smart_library</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>smart_library</classname> &amp;</paramtype></parameter><purpose>Compare the actual DLL/DSO objects without any guarantee to be stable between runs. Throws nothing. </purpose></function>
<function name="swap"><type>void</type><parameter name="lhs"><paramtype><classname>smart_library</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype><classname>smart_library</classname> &amp;</paramtype></parameter><purpose>Swaps two shared libraries. Does not invalidate symbols and functions loaded from libraries. Throws nothing. </purpose></function>
<function name="get"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="T2"/>
        </template><parameter name="sm"><paramtype>const <classname>smart_library</classname> &amp;</paramtype><description><para>A reference to the <classname alt="boost::dll::experimental::smart_library">smart_library</classname> </para></description></parameter><parameter name="name"><paramtype>const std::string &amp;</paramtype><description><para>The name of the entity to import </para></description></parameter><description><para>Helper functions for overloads.</para><para>Gets either a variable, function or member-function, depending on the signature.</para><para><programlisting language="c++">smart_library sm("lib.so");
get&lt;int&gt;(sm, "space::value"); //import a variable
get&lt;void(int)&gt;(sm, "space::func"); //import a function
get&lt;some_class, void(int)&gt;(sm, "space::class_::mem_fn"); //import a member function
</programlisting></para><para>
</para></description></function>
<function name="get"><type>boost::enable_if&lt; boost::is_object&lt; T &gt;, T &amp; &gt;::type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="sm"><paramtype>const <classname>smart_library</classname> &amp;</paramtype><description><para>A reference to the <classname alt="boost::dll::experimental::smart_library">smart_library</classname> </para></description></parameter><parameter name="name"><paramtype>const std::string &amp;</paramtype><description><para>The name of the entity to import </para></description></parameter><description><para>Helper functions for overloads.</para><para>Gets either a variable, function or member-function, depending on the signature.</para><para><programlisting language="c++">smart_library sm("lib.so");
get&lt;int&gt;(sm, "space::value"); //import a variable
get&lt;void(int)&gt;(sm, "space::func"); //import a function
get&lt;some_class, void(int)&gt;(sm, "space::class_::mem_fn"); //import a member function
</programlisting></para><para>
</para></description></function>
<function name="get"><type>boost::enable_if&lt; boost::is_function&lt; T &gt;, T &amp; &gt;::type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="sm"><paramtype>const <classname>smart_library</classname> &amp;</paramtype><description><para>A reference to the <classname alt="boost::dll::experimental::smart_library">smart_library</classname> </para></description></parameter><parameter name="name"><paramtype>const std::string &amp;</paramtype><description><para>The name of the entity to import </para></description></parameter><description><para>Helper functions for overloads.</para><para>Gets either a variable, function or member-function, depending on the signature.</para><para><programlisting language="c++">smart_library sm("lib.so");
get&lt;int&gt;(sm, "space::value"); //import a variable
get&lt;void(int)&gt;(sm, "space::func"); //import a function
get&lt;some_class, void(int)&gt;(sm, "space::class_::mem_fn"); //import a member function
</programlisting></para><para>
</para></description></function>
<function name="get"><type>auto</type><template>
          <template-type-parameter name="Class"/>
          <template-type-parameter name="Signature"/>
        </template><parameter name="sm"><paramtype>const <classname>smart_library</classname> &amp;</paramtype><description><para>A reference to the <classname alt="boost::dll::experimental::smart_library">smart_library</classname> </para></description></parameter><parameter name="name"><paramtype>const std::string &amp;</paramtype><description><para>The name of the entity to import </para></description></parameter><description><para>Helper functions for overloads.</para><para>Gets either a variable, function or member-function, depending on the signature.</para><para><programlisting language="c++">smart_library sm("lib.so");
get&lt;int&gt;(sm, "space::value"); //import a variable
get&lt;void(int)&gt;(sm, "space::func"); //import a function
get&lt;some_class, void(int)&gt;(sm, "space::class_::mem_fn"); //import a member function
</programlisting></para><para>
</para></description></function>
</namespace>












</namespace>
</namespace>
</header>
</library-reference>