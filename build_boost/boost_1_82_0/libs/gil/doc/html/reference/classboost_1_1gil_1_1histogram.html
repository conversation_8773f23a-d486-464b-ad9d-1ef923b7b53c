<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: histogram&lt; T &gt; Class Template Reference</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceboost.html">boost</a></li><li class="navelem"><b>gil</b></li><li class="navelem"><a class="el" href="classboost_1_1gil_1_1histogram.html">histogram</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="classboost_1_1gil_1_1histogram-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">histogram&lt; T &gt; Class Template Reference<div class="ingroups"><a class="el" href="group___histogram.html">Histogram</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Default histogram class provided by boost::gil.  
 <a href="classboost_1_1gil_1_1histogram.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="histogram_8hpp_source.html">histogram.hpp</a>&gt;</code></p>

<p>Inherits unordered_map&lt; std::tuple&lt; T... &gt;, double, detail::hash_tuple&lt; T... &gt; &gt;.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a383075e79dfce648ead785b87a7f0463"><td class="memItemLeft" align="right" valign="top"><a id="a383075e79dfce648ead785b87a7f0463"></a>
mapped_t &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#a383075e79dfce648ead785b87a7f0463">operator()</a> (T... indices)</td></tr>
<tr class="memdesc:a383075e79dfce648ead785b87a7f0463"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns bin value corresponding to specified tuple. <br /></td></tr>
<tr class="separator:a383075e79dfce648ead785b87a7f0463"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af92795152d964a6032e959435c828434"><td class="memTemplParams" colspan="2"><a id="af92795152d964a6032e959435c828434"></a>
template&lt;typename OtherType &gt; </td></tr>
<tr class="memitem:af92795152d964a6032e959435c828434"><td class="memTemplItemLeft" align="right" valign="top">bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#af92795152d964a6032e959435c828434">equals</a> (OtherType const &amp;otherhist) const</td></tr>
<tr class="memdesc:af92795152d964a6032e959435c828434"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if 2 histograms are equal. Ignores type, and checks if the keys (after type casting) match. <br /></td></tr>
<tr class="separator:af92795152d964a6032e959435c828434"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4fb7132988f0d964a632b88e34347d8"><td class="memTemplParams" colspan="2"><a id="ae4fb7132988f0d964a632b88e34347d8"></a>
template&lt;typename Tuple &gt; </td></tr>
<tr class="memitem:ae4fb7132988f0d964a632b88e34347d8"><td class="memTemplItemLeft" align="right" valign="top">bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#ae4fb7132988f0d964a632b88e34347d8">is_tuple_compatible</a> (Tuple const &amp;)</td></tr>
<tr class="memdesc:ae4fb7132988f0d964a632b88e34347d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the histogram class is compatible to be used with the specified tuple type. <br /></td></tr>
<tr class="separator:ae4fb7132988f0d964a632b88e34347d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1326d1132f56f08297dce325b4b4ff7a"><td class="memTemplParams" colspan="2"><a id="a1326d1132f56f08297dce325b4b4ff7a"></a>
template&lt;std::size_t... Dimensions, typename Tuple &gt; </td></tr>
<tr class="memitem:a1326d1132f56f08297dce325b4b4ff7a"><td class="memTemplItemLeft" align="right" valign="top">key_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#a1326d1132f56f08297dce325b4b4ff7a">key_from_tuple</a> (Tuple const &amp;t) const</td></tr>
<tr class="memdesc:a1326d1132f56f08297dce325b4b4ff7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a key compatible to be used as the histogram key from the input tuple. <br /></td></tr>
<tr class="separator:a1326d1132f56f08297dce325b4b4ff7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72e5f7159495f2b7a131acc41d686c23"><td class="memTemplParams" colspan="2"><a id="a72e5f7159495f2b7a131acc41d686c23"></a>
template&lt;std::size_t... Dimensions, typename Pixel &gt; </td></tr>
<tr class="memitem:a72e5f7159495f2b7a131acc41d686c23"><td class="memTemplItemLeft" align="right" valign="top">key_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#a72e5f7159495f2b7a131acc41d686c23">key_from_pixel</a> (Pixel const &amp;p) const</td></tr>
<tr class="memdesc:a72e5f7159495f2b7a131acc41d686c23"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a histogram compatible key from the input pixel which can be directly used. <br /></td></tr>
<tr class="separator:a72e5f7159495f2b7a131acc41d686c23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac5b0f6998ccb8ac1e5fb735acf8735b0"><td class="memItemLeft" align="right" valign="top"><a id="ac5b0f6998ccb8ac1e5fb735acf8735b0"></a>
key_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#ac5b0f6998ccb8ac1e5fb735acf8735b0">nearest_key</a> (key_t const &amp;k) const</td></tr>
<tr class="memdesc:ac5b0f6998ccb8ac1e5fb735acf8735b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return nearest smaller key to specified histogram key. <br /></td></tr>
<tr class="separator:ac5b0f6998ccb8ac1e5fb735acf8735b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad383819035232b2c4a664b0a9c025989"><td class="memTemplParams" colspan="2"><a id="ad383819035232b2c4a664b0a9c025989"></a>
template&lt;std::size_t... Dimensions, typename SrcView &gt; </td></tr>
<tr class="memitem:ad383819035232b2c4a664b0a9c025989"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#ad383819035232b2c4a664b0a9c025989">fill</a> (SrcView const &amp;srcview, std::size_t bin_width=1, bool applymask=false, std::vector&lt; std::vector&lt; bool &gt;&gt; mask={}, key_t lower=key_t(), key_t upper=key_t(), bool setlimits=false)</td></tr>
<tr class="memdesc:ad383819035232b2c4a664b0a9c025989"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fills the histogram with the input image view. <br /></td></tr>
<tr class="separator:ad383819035232b2c4a664b0a9c025989"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51a9548281df868dbe64edf2616d025d"><td class="memTemplParams" colspan="2"><a id="a51a9548281df868dbe64edf2616d025d"></a>
template&lt;std::size_t... Dimensions, typename Tuple &gt; </td></tr>
<tr class="memitem:a51a9548281df868dbe64edf2616d025d"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1gil_1_1histogram.html">histogram</a>&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#a51a9548281df868dbe64edf2616d025d">sub_histogram</a> (Tuple const &amp;t1, Tuple const &amp;t2)</td></tr>
<tr class="memdesc:a51a9548281df868dbe64edf2616d025d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Can return a subset or a mask over the current histogram. <br /></td></tr>
<tr class="separator:a51a9548281df868dbe64edf2616d025d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e7dfad30cdf935978cea64a236fb6e0"><td class="memTemplParams" colspan="2"><a id="a1e7dfad30cdf935978cea64a236fb6e0"></a>
template&lt;std::size_t... Dimensions&gt; </td></tr>
<tr class="memitem:a1e7dfad30cdf935978cea64a236fb6e0"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1gil_1_1histogram.html">histogram</a>&lt; boost::mp11::mp_at&lt; bin_t, boost::mp11::mp_size_t&lt; Dimensions &gt; &gt;... &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#a1e7dfad30cdf935978cea64a236fb6e0">sub_histogram</a> ()</td></tr>
<tr class="memdesc:a1e7dfad30cdf935978cea64a236fb6e0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a sub-histogram over specified axes. <br /></td></tr>
<tr class="separator:a1e7dfad30cdf935978cea64a236fb6e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd0de676568888d848beb97dcc53ae47"><td class="memItemLeft" align="right" valign="top"><a id="acd0de676568888d848beb97dcc53ae47"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#acd0de676568888d848beb97dcc53ae47">normalize</a> ()</td></tr>
<tr class="memdesc:acd0de676568888d848beb97dcc53ae47"><td class="mdescLeft">&#160;</td><td class="mdescRight">Normalize this histogram class. <br /></td></tr>
<tr class="separator:acd0de676568888d848beb97dcc53ae47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af31d07521e522dfbd377eebf1652053f"><td class="memItemLeft" align="right" valign="top"><a id="af31d07521e522dfbd377eebf1652053f"></a>
double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#af31d07521e522dfbd377eebf1652053f">sum</a> () const</td></tr>
<tr class="memdesc:af31d07521e522dfbd377eebf1652053f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the sum count of all bins. <br /></td></tr>
<tr class="separator:af31d07521e522dfbd377eebf1652053f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69855316534ac65806a71253ac424688"><td class="memItemLeft" align="right" valign="top"><a id="a69855316534ac65806a71253ac424688"></a>
key_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#a69855316534ac65806a71253ac424688">min_key</a> () const</td></tr>
<tr class="memdesc:a69855316534ac65806a71253ac424688"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the minimum key in histogram. <br /></td></tr>
<tr class="separator:a69855316534ac65806a71253ac424688"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6c108814c2d6da6d55b18dbb4d51f4e"><td class="memItemLeft" align="right" valign="top"><a id="af6c108814c2d6da6d55b18dbb4d51f4e"></a>
key_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#af6c108814c2d6da6d55b18dbb4d51f4e">max_key</a> () const</td></tr>
<tr class="memdesc:af6c108814c2d6da6d55b18dbb4d51f4e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the maximum key in histogram. <br /></td></tr>
<tr class="separator:af6c108814c2d6da6d55b18dbb4d51f4e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0607d4ba35a4d535c4bf1ff5e32db8b"><td class="memItemLeft" align="right" valign="top"><a id="ab0607d4ba35a4d535c4bf1ff5e32db8b"></a>
std::vector&lt; key_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#ab0607d4ba35a4d535c4bf1ff5e32db8b">sorted_keys</a> () const</td></tr>
<tr class="memdesc:ab0607d4ba35a4d535c4bf1ff5e32db8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return sorted keys in a vector. <br /></td></tr>
<tr class="separator:ab0607d4ba35a4d535c4bf1ff5e32db8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a4806809cec84980fac0a10596414e960"><td class="memItemLeft" align="right" valign="top"><a id="a4806809cec84980fac0a10596414e960"></a>
static constexpr std::size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#a4806809cec84980fac0a10596414e960">dimension</a> ()</td></tr>
<tr class="memdesc:a4806809cec84980fac0a10596414e960"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the number of dimensions(axes) the class supports. <br /></td></tr>
<tr class="separator:a4806809cec84980fac0a10596414e960"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0dae242b2c1bf16bde24c124d3097c8a"><td class="memItemLeft" align="right" valign="top"><a id="a0dae242b2c1bf16bde24c124d3097c8a"></a>
static constexpr bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1gil_1_1histogram.html#a0dae242b2c1bf16bde24c124d3097c8a">is_pixel_compatible</a> ()</td></tr>
<tr class="memdesc:a0dae242b2c1bf16bde24c124d3097c8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the histogram class is compatible to be used with a GIL image type. <br /></td></tr>
<tr class="separator:a0dae242b2c1bf16bde24c124d3097c8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename... T&gt;<br />
class boost::gil::histogram&lt; T &gt;</h3>

<p>Default histogram class provided by boost::gil. </p>
<p>The class inherits over the std::unordered_map provided by STL. A complete example/tutorial of how to use the class resides in the docs. Simple calling syntax for a 3D dimensional histogram : </p><div class="fragment"><div class="line">histogram&lt;int, int , int&gt; h;</div><div class="line">h(1, 1, 1) = 0;</div></div><!-- fragment --><p> This is just a starter to what all can be achieved with it, refer to the docs for the full demo. </p>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="histogram_8hpp_source.html">histogram.hpp</a></li>
</ul>
</div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
