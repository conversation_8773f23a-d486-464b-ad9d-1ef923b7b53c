<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: read_and_convert_view.hpp Source File</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_1878a3f4746a95c6aad317458cc7ef80.html">boost</a></li><li class="navelem"><a class="el" href="dir_df4750f408086f9b9c1b5ee4251365ff.html">gil</a></li><li class="navelem"><a class="el" href="dir_80930c1173f2c0438c68e99be5d8d1e3.html">io</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">read_and_convert_view.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// Copyright 2007-2012 Christian Henning, Andreas Pokorny, Lubomir Bourdev</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">// Distributed under the Boost Software License, Version 1.0</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">// See accompanying file LICENSE_1_0.txt or copy at</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">// http://www.boost.org/LICENSE_1_0.txt</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#ifndef BOOST_GIL_IO_READ_AND_CONVERT_VIEW_HPP</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#define BOOST_GIL_IO_READ_AND_CONVERT_VIEW_HPP</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;</div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#include &lt;boost/gil/io/base.hpp&gt;</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &lt;boost/gil/io/conversion_policies.hpp&gt;</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &lt;boost/gil/io/device.hpp&gt;</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &lt;boost/gil/io/get_reader.hpp&gt;</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &lt;boost/gil/io/path_spec.hpp&gt;</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &lt;boost/gil/detail/mp11.hpp&gt;</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &lt;type_traits&gt;</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceboost.html">boost</a>{ <span class="keyword">namespace </span>gil {</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Reader, <span class="keyword">typename</span> View&gt;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">inline</span></div><div class="line"><a name="l00032"></a><span class="lineno"><a class="line" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">   32</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(Reader&amp; reader, View <span class="keyword">const</span>&amp; <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>,</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;    <span class="keyword">typename</span> std::enable_if</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    &lt;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        mp11::mp_and</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        &lt;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;            detail::is_reader&lt;Reader&gt;,</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;            is_format_tag&lt;typename Reader::format_tag_t&gt;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        &gt;::value</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;    &gt;::type* <span class="comment">/*dummy*/</span> = <span class="keyword">nullptr</span>)</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;{</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;    reader.check_image_size(<a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>.dimensions());</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;    reader.init_view(<a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>, reader._settings);</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;    reader.apply(<a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>);</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;}</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Device, <span class="keyword">typename</span> View, <span class="keyword">typename</span> ColorConverter, <span class="keyword">typename</span> FormatTag&gt;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">inline</span></div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="keywordtype">void</span> <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;    Device&amp; device,</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    View <span class="keyword">const</span>&amp; <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>,</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    image_read_settings&lt;FormatTag&gt; <span class="keyword">const</span>&amp; settings,</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    ColorConverter <span class="keyword">const</span>&amp; cc,</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    <span class="keyword">typename</span> std::enable_if</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    &lt;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        mp11::mp_and</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        &lt;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;            detail::is_read_device&lt;FormatTag, Device&gt;,</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;            is_format_tag&lt;FormatTag&gt;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        &gt;::value</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;    &gt;::type* <span class="comment">/*dummy*/</span> = <span class="keyword">nullptr</span>)</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;{</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;    <span class="keyword">using</span> read_and_convert_t = detail::read_and_convert&lt;ColorConverter&gt;;</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    <span class="keyword">using</span> reader_t = <span class="keyword">typename</span> get_reader&lt;Device, FormatTag, read_and_convert_t&gt;::type;</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;    reader_t reader = make_reader(device, settings, read_and_convert_t{cc});</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;    <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(reader, <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>);</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;}</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> String, <span class="keyword">typename</span> View, <span class="keyword">typename</span> ColorConverter, <span class="keyword">typename</span> FormatTag&gt;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="keyword">inline</span></div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="keywordtype">void</span> <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    String <span class="keyword">const</span>&amp; file_name,</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    View <span class="keyword">const</span>&amp; <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>,</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    image_read_settings&lt;FormatTag&gt; <span class="keyword">const</span>&amp; settings,</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    ColorConverter <span class="keyword">const</span>&amp; cc,</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    <span class="keyword">typename</span> std::enable_if</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    &lt;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;        mp11::mp_and</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;        &lt;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;            is_format_tag&lt;FormatTag&gt;,</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;            detail::is_supported_path_spec&lt;String&gt;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        &gt;::value</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;    &gt;::type* <span class="comment">/*dummy*/</span> = <span class="keyword">nullptr</span>)</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;{</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    <span class="keyword">using</span> read_and_convert_t = detail::read_and_convert&lt;ColorConverter&gt;;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    <span class="keyword">using</span> reader_t = <span class="keyword">typename</span> get_reader&lt;String, FormatTag, read_and_convert_t&gt;::type;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    reader_t reader = make_reader(file_name, settings, read_and_convert_t{cc});</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(reader, <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>);</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;}</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> String, <span class="keyword">typename</span> View, <span class="keyword">typename</span> ColorConverter, <span class="keyword">typename</span> FormatTag&gt;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="keyword">inline</span></div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="keywordtype">void</span> <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    String <span class="keyword">const</span>&amp; file_name,</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;    View <span class="keyword">const</span>&amp; <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>,</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;    ColorConverter <span class="keyword">const</span>&amp; cc,</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;    FormatTag <span class="keyword">const</span>&amp; tag,</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;    <span class="keyword">typename</span> std::enable_if</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    &lt;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        mp11::mp_and</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;        &lt;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;            is_format_tag&lt;FormatTag&gt;,</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;            detail::is_supported_path_spec&lt;String&gt;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        &gt;::value</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    &gt;::type* <span class="comment">/*dummy*/</span> = <span class="keyword">nullptr</span>)</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;{</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    <span class="keyword">using</span> read_and_convert_t = detail::read_and_convert&lt;ColorConverter&gt;;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    <span class="keyword">using</span> reader_t = <span class="keyword">typename</span> get_reader&lt;String, FormatTag, read_and_convert_t&gt;::type;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;    reader_t reader = make_reader(file_name, tag, read_and_convert_t{cc});</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(reader, <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>);</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;}</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Device, <span class="keyword">typename</span> View, <span class="keyword">typename</span> ColorConverter, <span class="keyword">typename</span> FormatTag&gt;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;<span class="keyword">inline</span></div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;<span class="keywordtype">void</span> <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    Device&amp; device,</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    View <span class="keyword">const</span>&amp; <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>,</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;    ColorConverter <span class="keyword">const</span>&amp; cc,</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    FormatTag <span class="keyword">const</span>&amp; tag,</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    <span class="keyword">typename</span> std::enable_if</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    &lt;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        mp11::mp_and</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        &lt;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;            detail::is_read_device&lt;FormatTag, Device&gt;,</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;            is_format_tag&lt;FormatTag&gt;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        &gt;::value</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    &gt;::type* <span class="comment">/*dummy*/</span> = <span class="keyword">nullptr</span>)</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;{</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    <span class="keyword">using</span> read_and_convert_t = detail::read_and_convert&lt;ColorConverter&gt;;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <span class="keyword">using</span> reader_t = <span class="keyword">typename</span> get_reader&lt;Device, FormatTag, read_and_convert_t&gt;::type;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;    reader_t reader = make_reader(device, tag, read_and_convert_t{cc});</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(reader, <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>);</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;}</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> String, <span class="keyword">typename</span> View, <span class="keyword">typename</span> FormatTag&gt;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;<span class="keyword">inline</span></div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;<span class="keywordtype">void</span> <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    String <span class="keyword">const</span>&amp; file_name,</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    View <span class="keyword">const</span>&amp; <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>,</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;    image_read_settings&lt;FormatTag&gt; <span class="keyword">const</span>&amp; settings,</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;    <span class="keyword">typename</span> std::enable_if</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    &lt;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;        mp11::mp_and</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;        &lt;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;            is_format_tag&lt;FormatTag&gt;,</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;            detail::is_supported_path_spec&lt;String&gt;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        &gt;::value</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    &gt;::type* <span class="comment">/*dummy*/</span> = <span class="keyword">nullptr</span>)</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;{</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    <span class="keyword">using</span> read_and_convert_t = detail::read_and_convert&lt;default_color_converter&gt;;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    <span class="keyword">using</span> reader_t = <span class="keyword">typename</span> get_reader&lt;String, FormatTag, read_and_convert_t&gt;::type;</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;    reader_t reader = make_reader(file_name, settings, read_and_convert_t{});</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(reader, <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>);</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;}</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Device, <span class="keyword">typename</span> View, <span class="keyword">typename</span> FormatTag&gt;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="keyword">inline</span></div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;<span class="keywordtype">void</span> <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;    Device&amp; device,</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    View <span class="keyword">const</span>&amp; <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>,</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;    image_read_settings&lt;FormatTag&gt; <span class="keyword">const</span>&amp; settings,</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;    <span class="keyword">typename</span> std::enable_if</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;    &lt;</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;        mp11::mp_and</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;        &lt;</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;            detail::is_read_device&lt;FormatTag, Device&gt;,</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;            is_format_tag&lt;FormatTag&gt;</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;        &gt;::value</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;    &gt;::type* <span class="comment">/*dummy*/</span> = <span class="keyword">nullptr</span>)</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;{</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;    <span class="keyword">using</span> read_and_convert_t = detail::read_and_convert&lt;default_color_converter&gt;;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;    <span class="keyword">using</span> reader_t = <span class="keyword">typename</span> get_reader&lt;Device, FormatTag, read_and_convert_t&gt;::type;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;    reader_t reader = make_reader(device, settings, read_and_convert_t{});</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;    <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(reader, <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>);</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;}</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> String, <span class="keyword">typename</span> View, <span class="keyword">typename</span> FormatTag&gt;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;<span class="keyword">inline</span></div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;<span class="keywordtype">void</span> <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;    String <span class="keyword">const</span>&amp; file_name,</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;    View <span class="keyword">const</span>&amp; <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>,</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;    FormatTag <span class="keyword">const</span>&amp; tag,</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;    <span class="keyword">typename</span> std::enable_if</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;    &lt;</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;        mp11::mp_and</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;        &lt;</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;            is_format_tag&lt;FormatTag&gt;,</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;            detail::is_supported_path_spec&lt;String&gt;</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;        &gt;::value</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;    &gt;::type* <span class="comment">/*dummy*/</span> = <span class="keyword">nullptr</span>)</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;{</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;    <span class="keyword">using</span> read_and_convert_t = detail::read_and_convert&lt;default_color_converter&gt;;</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;    <span class="keyword">using</span> reader_t = <span class="keyword">typename</span> get_reader&lt;String, FormatTag, read_and_convert_t&gt;::type;</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;    reader_t reader = make_reader(file_name, tag, read_and_convert_t{});</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;    <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(reader, <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>);</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;}</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Device, <span class="keyword">typename</span> View, <span class="keyword">typename</span> FormatTag&gt;</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;<span class="keyword">inline</span></div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;<span class="keywordtype">void</span> <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;    Device&amp; device,</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;    View <span class="keyword">const</span>&amp; <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>,</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;    FormatTag <span class="keyword">const</span>&amp; tag,</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;    <span class="keyword">typename</span> std::enable_if</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;    &lt;</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;        mp11::mp_and</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;        &lt;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;            detail::is_read_device&lt;FormatTag, Device&gt;,</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;            is_format_tag&lt;FormatTag&gt;</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;        &gt;::value</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    &gt;::type* <span class="comment">/*dummy*/</span> = <span class="keyword">nullptr</span>)</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;{</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;    <span class="keyword">using</span> read_and_convert_t = detail::read_and_convert&lt;default_color_converter&gt;;</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;    <span class="keyword">using</span> reader_t = <span class="keyword">typename</span> get_reader&lt;Device, FormatTag, read_and_convert_t&gt;::type;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;    reader_t reader = make_reader(device, tag, read_and_convert_t{});</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;    <a class="code" href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">read_and_convert_view</a>(reader, <a class="code" href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">view</a>);</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;}</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;}} <span class="comment">// namespace boost::gill</span></div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="group___image_model_html_gae885a4a33370b2c78aa5584ad0290371"><div class="ttname"><a href="group___image_model.html#gae885a4a33370b2c78aa5584ad0290371">boost::gil::view</a></div><div class="ttdeci">auto view(image&lt; Pixel, IsPlanar, Alloc &gt; &amp;img) -&gt; typename image&lt; Pixel, IsPlanar, Alloc &gt;::view_t const &amp;</div><div class="ttdoc">Returns the non-constant-pixel view of an image.</div><div class="ttdef"><b>Definition:</b> image.hpp:565</div></div>
<div class="ttc" id="namespaceboost_html"><div class="ttname"><a href="namespaceboost.html">boost</a></div><div class="ttdoc">defined(BOOST_NO_CXX17_HDR_MEMORY_RESOURCE)</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:36</div></div>
<div class="ttc" id="group___i_o_html_gaf30e030fab17abdd159c08478008cde1"><div class="ttname"><a href="group___i_o.html#gaf30e030fab17abdd159c08478008cde1">boost::gil::read_and_convert_view</a></div><div class="ttdeci">void read_and_convert_view(Reader &amp;reader, View const &amp;view, typename std::enable_if&lt; mp11::mp_and&lt; detail::is_reader&lt; Reader &gt;, is_format_tag&lt; typename Reader::format_tag_t &gt; &gt;::value &gt;::type *=nullptr)</div><div class="ttdoc">Reads and color-converts an image view. No memory is allocated.</div><div class="ttdef"><b>Definition:</b> read_and_convert_view.hpp:32</div></div>
</div><!-- fragment --></div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
