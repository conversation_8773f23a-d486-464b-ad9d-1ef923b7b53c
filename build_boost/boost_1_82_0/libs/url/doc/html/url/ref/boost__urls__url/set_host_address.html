<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>url::set_host_address</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../boost__urls__url.html" title="url">
<link rel="prev" href="set_encoded_host.html" title="url::set_encoded_host">
<link rel="next" href="set_encoded_host_address.html" title="url::set_encoded_host_address">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="set_encoded_host.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__url.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="set_encoded_host_address.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="url.ref.boost__urls__url.set_host_address"></a><a class="link" href="set_host_address.html" title="url::set_host_address">url::set_host_address</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm101996"></a>
        </p>
<p>
          Set the host to an address.
        </p>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h0"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.synopsis"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.synopsis">Synopsis</a>
        </h6>
<pre class="programlisting"><span class="identifier">url</span><span class="special">&amp;</span>
<span class="identifier">set_host_address</span><span class="special">(</span>
    <span class="identifier">string_view</span> <span class="identifier">s</span><span class="special">);</span>
</pre>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h1"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.description"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.description">Description</a>
        </h6>
<p>
          Depending on the contents of the passed string, this function sets the
          host:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              If the string is a valid IPv4 address, then the host is set to the
              address. The host type is <a class="link" href="../boost__urls__host_type.html" title="host_type"><code class="computeroutput"><span class="identifier">host_type</span><span class="special">::</span><span class="identifier">ipv4</span></code></a>.
            </li>
<li class="listitem">
              If the string is a valid IPv6 address, then the host is set to that
              address. The host type is <a class="link" href="../boost__urls__host_type.html" title="host_type"><code class="computeroutput"><span class="identifier">host_type</span><span class="special">::</span><span class="identifier">ipv6</span></code></a>.
            </li>
<li class="listitem">
              If the string is a valid IPvFuture, then the host is set to that address.
              The host type is <a class="link" href="../boost__urls__host_type.html" title="host_type"><code class="computeroutput"><span class="identifier">host_type</span><span class="special">::</span><span class="identifier">ipvfuture</span></code></a>.
            </li>
<li class="listitem">
              Otherwise, the host name is set to the string, which may be empty.
              Reserved characters in the string are percent-escaped in the result.
              The host type is <a class="link" href="../boost__urls__host_type.html" title="host_type"><code class="computeroutput"><span class="identifier">host_type</span><span class="special">::</span><span class="identifier">name</span></code></a>.
            </li>
</ul></div>
<p>
          In all cases, when this function returns, the URL contains an authority.
        </p>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h2"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.example"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.example">Example</a>
        </h6>
<pre class="programlisting"><span class="identifier">assert</span><span class="special">(</span> <span class="identifier">url</span><span class="special">(</span> <span class="string">"http://www.example.com"</span> <span class="special">).</span><span class="identifier">set_host_address</span><span class="special">(</span> <span class="string">"127.0.0.1"</span> <span class="special">).</span><span class="identifier">buffer</span><span class="special">()</span> <span class="special">==</span> <span class="string">"http://127.0.0.1"</span> <span class="special">);</span>
</pre>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h3"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.postconditions"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.postconditions">Postconditions</a>
        </h6>
<pre class="programlisting"><span class="keyword">this</span><span class="special">-&gt;</span><span class="identifier">has_authority</span><span class="special">()</span> <span class="special">==</span> <span class="keyword">true</span>
</pre>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h4"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.complexity"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.complexity">Complexity</a>
        </h6>
<p>
          Linear in <code class="computeroutput"><span class="identifier">s</span><span class="special">.</span><span class="identifier">size</span><span class="special">()</span></code>.
        </p>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h5"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.exception_safety"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.exception_safety">Exception
          Safety</a>
        </h6>
<p>
          Strong guarantee. Calls to allocate may throw.
        </p>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h6"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.parameters"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.parameters">Parameters</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody><tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">s</span></code>
                  </p>
                </td>
<td>
                  <p>
                    The string to set.
                  </p>
                </td>
</tr></tbody>
</table></div>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h7"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.bnf"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.bnf">BNF</a>
        </h6>
<pre class="programlisting"><span class="identifier">IPv4address</span> <span class="special">=</span> <span class="identifier">dec</span><span class="special">-</span><span class="identifier">octet</span> <span class="string">"."</span> <span class="identifier">dec</span><span class="special">-</span><span class="identifier">octet</span> <span class="string">"."</span> <span class="identifier">dec</span><span class="special">-</span><span class="identifier">octet</span> <span class="string">"."</span> <span class="identifier">dec</span><span class="special">-</span><span class="identifier">octet</span>

<span class="identifier">dec</span><span class="special">-</span><span class="identifier">octet</span>   <span class="special">=</span> <span class="identifier">DIGIT</span>                 <span class="special">;</span> <span class="number">0</span><span class="special">-</span><span class="number">9</span>
            <span class="special">/</span> <span class="special">%</span><span class="identifier">x31</span><span class="special">-</span><span class="number">39</span> <span class="identifier">DIGIT</span>         <span class="special">;</span> <span class="number">10</span><span class="special">-</span><span class="number">99</span>
            <span class="special">/</span> <span class="string">"1"</span> <span class="number">2D</span><span class="identifier">IGIT</span>            <span class="special">;</span> <span class="number">100</span><span class="special">-</span><span class="number">199</span>
            <span class="special">/</span> <span class="string">"2"</span> <span class="special">%</span><span class="identifier">x30</span><span class="special">-</span><span class="number">34</span> <span class="identifier">DIGIT</span>     <span class="special">;</span> <span class="number">200</span><span class="special">-</span><span class="number">249</span>
            <span class="special">/</span> <span class="string">"25"</span> <span class="special">%</span><span class="identifier">x30</span><span class="special">-</span><span class="number">35</span>          <span class="special">;</span> <span class="number">250</span><span class="special">-</span><span class="number">255</span>

<span class="identifier">IPv6address</span> <span class="special">=</span>                            <span class="number">6</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">ls32</span>
            <span class="special">/</span>                       <span class="string">"::"</span> <span class="number">5</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">ls32</span>
            <span class="special">/</span> <span class="special">[</span>               <span class="identifier">h16</span> <span class="special">]</span> <span class="string">"::"</span> <span class="number">4</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">ls32</span>
            <span class="special">/</span> <span class="special">[</span> <span class="special">*</span><span class="number">1</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">h16</span> <span class="special">]</span> <span class="string">"::"</span> <span class="number">3</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">ls32</span>
            <span class="special">/</span> <span class="special">[</span> <span class="special">*</span><span class="number">2</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">h16</span> <span class="special">]</span> <span class="string">"::"</span> <span class="number">2</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">ls32</span>
            <span class="special">/</span> <span class="special">[</span> <span class="special">*</span><span class="number">3</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">h16</span> <span class="special">]</span> <span class="string">"::"</span>    <span class="identifier">h16</span> <span class="string">":"</span>   <span class="identifier">ls32</span>
            <span class="special">/</span> <span class="special">[</span> <span class="special">*</span><span class="number">4</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">h16</span> <span class="special">]</span> <span class="string">"::"</span>              <span class="identifier">ls32</span>
            <span class="special">/</span> <span class="special">[</span> <span class="special">*</span><span class="number">5</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">h16</span> <span class="special">]</span> <span class="string">"::"</span>              <span class="identifier">h16</span>
            <span class="special">/</span> <span class="special">[</span> <span class="special">*</span><span class="number">6</span><span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="special">)</span> <span class="identifier">h16</span> <span class="special">]</span> <span class="string">"::"</span>

<span class="identifier">ls32</span>        <span class="special">=</span> <span class="special">(</span> <span class="identifier">h16</span> <span class="string">":"</span> <span class="identifier">h16</span> <span class="special">)</span> <span class="special">/</span> <span class="identifier">IPv4address</span>
            <span class="special">;</span> <span class="identifier">least</span><span class="special">-</span><span class="identifier">significant</span> <span class="number">32</span> <span class="identifier">bits</span> <span class="identifier">of</span> <span class="identifier">address</span>

<span class="identifier">h16</span>         <span class="special">=</span> <span class="number">1</span><span class="special">*</span><span class="number">4</span><span class="identifier">HEXDIG</span>
            <span class="special">;</span> <span class="number">16</span> <span class="identifier">bits</span> <span class="identifier">of</span> <span class="identifier">address</span> <span class="identifier">represented</span> <span class="identifier">in</span> <span class="identifier">hexadecimal</span>

<span class="identifier">IPvFuture</span>     <span class="special">=</span> <span class="string">"v"</span> <span class="number">1</span><span class="special">*</span><span class="identifier">HEXDIG</span> <span class="string">"."</span> <span class="number">1</span><span class="special">*(</span> <span class="identifier">unreserved</span> <span class="special">/</span> <span class="identifier">sub</span><span class="special">-</span><span class="identifier">delims</span> <span class="special">/</span> <span class="string">":"</span> <span class="special">)</span>

<span class="identifier">reg</span><span class="special">-</span><span class="identifier">name</span>    <span class="special">=</span> <span class="special">*(</span> <span class="identifier">unreserved</span> <span class="special">/</span> <span class="identifier">pct</span><span class="special">-</span><span class="identifier">encoded</span> <span class="special">/</span> <span class="string">"-"</span> <span class="special">/</span> <span class="string">"."</span><span class="special">)</span>
</pre>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h8"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.specification"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.specification">Specification</a>
        </h6>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <a href="https://en.wikipedia.org/wiki/IPv4" target="_top">IPv4 (Wikipedia)</a>
            </li>
<li class="listitem">
              <a href="https://datatracker.ietf.org/doc/html/rfc4291" target="_top">IP Version
              6 Addressing Architecture (rfc4291)</a>
            </li>
<li class="listitem">
              <a href="https://datatracker.ietf.org/doc/html/rfc3986#section-3.2.2" target="_top">3.2.2.
              Host (rfc3986)</a>
            </li>
</ul></div>
<h6>
<a name="url.ref.boost__urls__url.set_host_address.h9"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url.set_host_address.see_also"></a></span><a class="link" href="set_host_address.html#url.ref.boost__urls__url.set_host_address.see_also">See Also</a>
        </h6>
<p>
          <a class="link" href="set_encoded_host.html" title="url::set_encoded_host"><code class="computeroutput"><span class="identifier">set_encoded_host</span></code></a>, <a class="link" href="set_encoded_host_address.html" title="url::set_encoded_host_address"><code class="computeroutput"><span class="identifier">set_encoded_host_address</span></code></a>, <a class="link" href="set_encoded_host_name.html" title="url::set_encoded_host_name"><code class="computeroutput"><span class="identifier">set_encoded_host_name</span></code></a>, <a class="link" href="set_host.html" title="url::set_host"><code class="computeroutput"><span class="identifier">set_host</span></code></a>, <a class="link" href="set_host_address.html" title="url::set_host_address"><code class="computeroutput"><span class="identifier">set_host_address</span></code></a>, <a class="link" href="set_host_ipv4.html" title="url::set_host_ipv4"><code class="computeroutput"><span class="identifier">set_host_ipv4</span></code></a>, <a class="link" href="set_host_ipv6.html" title="url::set_host_ipv6"><code class="computeroutput"><span class="identifier">set_host_ipv6</span></code></a>, <a class="link" href="set_host_ipvfuture.html" title="url::set_host_ipvfuture"><code class="computeroutput"><span class="identifier">set_host_ipvfuture</span></code></a>, <a class="link" href="set_host_name.html" title="url::set_host_name"><code class="computeroutput"><span class="identifier">set_host_name</span></code></a>.
        </p>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="set_encoded_host.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__url.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="set_encoded_host_address.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
