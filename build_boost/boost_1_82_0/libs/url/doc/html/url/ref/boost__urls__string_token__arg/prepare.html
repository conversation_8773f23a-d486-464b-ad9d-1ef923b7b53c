<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>string_token::arg::prepare</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../boost__urls__string_token__arg.html" title="string_token::arg">
<link rel="prev" href="../boost__urls__string_token__arg.html" title="string_token::arg">
<link rel="next" href="_dtor_arg.html" title="string_token::arg::~arg">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../boost__urls__string_token__arg.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__string_token__arg.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="_dtor_arg.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="url.ref.boost__urls__string_token__arg.prepare"></a><a class="link" href="prepare.html" title="string_token::arg::prepare">string_token::arg::prepare</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm16230"></a>
        </p>
<p>
          Return a modifiable character buffer.
        </p>
<h6>
<a name="url.ref.boost__urls__string_token__arg.prepare.h0"></a>
          <span class="phrase"><a name="url.ref.boost__urls__string_token__arg.prepare.synopsis"></a></span><a class="link" href="prepare.html#url.ref.boost__urls__string_token__arg.prepare.synopsis">Synopsis</a>
        </h6>
<pre class="programlisting"><span class="keyword">char</span><span class="special">*</span>
<span class="identifier">prepare</span><span class="special">(</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">n</span><span class="special">);</span>
</pre>
<h6>
<a name="url.ref.boost__urls__string_token__arg.prepare.h1"></a>
          <span class="phrase"><a name="url.ref.boost__urls__string_token__arg.prepare.description"></a></span><a class="link" href="prepare.html#url.ref.boost__urls__string_token__arg.prepare.description">Description</a>
        </h6>
<p>
          This function attempts to obtain a character buffer with space for at least
          <code class="computeroutput"><span class="identifier">n</span></code> characters. Upon success,
          a pointer to the beginning of the buffer is returned. Ownership is not
          transferred; the caller should not attempt to free the storage. The buffer
          shall remain valid until <code class="computeroutput"><span class="keyword">this</span></code>
          is destroyed.
        </p>
<h6>
<a name="url.ref.boost__urls__string_token__arg.prepare.h2"></a>
          <span class="phrase"><a name="url.ref.boost__urls__string_token__arg.prepare.remarks"></a></span><a class="link" href="prepare.html#url.ref.boost__urls__string_token__arg.prepare.remarks">Remarks</a>
        </h6>
<p>
          This function may only be called once. After invoking the function, the
          only valid operation is destruction.
        </p>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../boost__urls__string_token__arg.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__string_token__arg.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="_dtor_arg.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
