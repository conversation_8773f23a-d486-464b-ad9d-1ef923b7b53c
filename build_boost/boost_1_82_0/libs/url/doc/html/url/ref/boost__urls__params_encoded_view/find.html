<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>params_encoded_view::find</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../boost__urls__params_encoded_view.html" title="params_encoded_view">
<link rel="prev" href="count.html" title="params_encoded_view::count">
<link rel="next" href="find/overload1.html" title="params_encoded_view::find (1 of 2 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="count.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__params_encoded_view.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="find/overload1.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="url.ref.boost__urls__params_encoded_view.find"></a><a class="link" href="find.html" title="params_encoded_view::find">params_encoded_view::find</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm36681"></a>
        </p>
<p>
          Find a matching key.
        </p>
<pre class="programlisting"><span class="identifier">iterator</span>
<a class="link" href="find/overload1.html" title="params_encoded_view::find (1 of 2 overloads)">find</a><span class="special">(</span>
    <span class="identifier">pct_string_view</span> <span class="identifier">key</span><span class="special">,</span>
    <span class="identifier">ignore_case_param</span> <span class="identifier">ic</span> <span class="special">=</span> <span class="special">{})</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="emphasis"><em>» <a class="link" href="find/overload1.html" title="params_encoded_view::find (1 of 2 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>

<span class="identifier">iterator</span>
<a class="link" href="find/overload2.html" title="params_encoded_view::find (2 of 2 overloads)">find</a><span class="special">(</span>
    <span class="identifier">iterator</span> <span class="identifier">from</span><span class="special">,</span>
    <span class="identifier">pct_string_view</span> <span class="identifier">key</span><span class="special">,</span>
    <span class="identifier">ignore_case_param</span> <span class="identifier">ic</span> <span class="special">=</span> <span class="special">{})</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="emphasis"><em>» <a class="link" href="find/overload2.html" title="params_encoded_view::find (2 of 2 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="count.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__params_encoded_view.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="find/overload1.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
