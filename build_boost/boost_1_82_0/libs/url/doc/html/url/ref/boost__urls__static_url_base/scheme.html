<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>static_url_base::scheme</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../boost__urls__static_url_base.html" title="static_url_base">
<link rel="prev" href="has_scheme.html" title="static_url_base::has_scheme">
<link rel="next" href="scheme_id.html" title="static_url_base::scheme_id">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="has_scheme.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__static_url_base.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="scheme_id.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="url.ref.boost__urls__static_url_base.scheme"></a><a class="link" href="scheme.html" title="static_url_base::scheme">static_url_base::scheme</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm89325"></a>
        </p>
<p>
          (Inherited from <a class="link" href="../boost__urls__url_view_base.html" title="url_view_base"><code class="computeroutput"><span class="identifier">url_view_base</span></code></a>)
        </p>
<p>
          Return the scheme.
        </p>
<h6>
<a name="url.ref.boost__urls__static_url_base.scheme.h0"></a>
          <span class="phrase"><a name="url.ref.boost__urls__static_url_base.scheme.synopsis"></a></span><a class="link" href="scheme.html#url.ref.boost__urls__static_url_base.scheme.synopsis">Synopsis</a>
        </h6>
<pre class="programlisting"><span class="identifier">string_view</span>
<span class="identifier">scheme</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<h6>
<a name="url.ref.boost__urls__static_url_base.scheme.h1"></a>
          <span class="phrase"><a name="url.ref.boost__urls__static_url_base.scheme.description"></a></span><a class="link" href="scheme.html#url.ref.boost__urls__static_url_base.scheme.description">Description</a>
        </h6>
<p>
          This function returns the scheme if it exists, without a trailing colon
          (':'). Otherwise it returns an empty string. Note that schemes are case-insensitive,
          and the canonical form is lowercased.
        </p>
<h6>
<a name="url.ref.boost__urls__static_url_base.scheme.h2"></a>
          <span class="phrase"><a name="url.ref.boost__urls__static_url_base.scheme.example"></a></span><a class="link" href="scheme.html#url.ref.boost__urls__static_url_base.scheme.example">Example</a>
        </h6>
<pre class="programlisting"><span class="identifier">assert</span><span class="special">(</span> <span class="identifier">url_view</span><span class="special">(</span> <span class="string">"http://www.example.com"</span> <span class="special">).</span><span class="identifier">scheme</span><span class="special">()</span> <span class="special">==</span> <span class="string">"http"</span> <span class="special">);</span>
</pre>
<h6>
<a name="url.ref.boost__urls__static_url_base.scheme.h3"></a>
          <span class="phrase"><a name="url.ref.boost__urls__static_url_base.scheme.exception_safety"></a></span><a class="link" href="scheme.html#url.ref.boost__urls__static_url_base.scheme.exception_safety">Exception
          Safety</a>
        </h6>
<p>
          Throws nothing.
        </p>
<h6>
<a name="url.ref.boost__urls__static_url_base.scheme.h4"></a>
          <span class="phrase"><a name="url.ref.boost__urls__static_url_base.scheme.bnf"></a></span><a class="link" href="scheme.html#url.ref.boost__urls__static_url_base.scheme.bnf">BNF</a>
        </h6>
<pre class="programlisting"><span class="identifier">scheme</span>          <span class="special">=</span> <span class="identifier">ALPHA</span> <span class="special">*(</span> <span class="identifier">ALPHA</span> <span class="special">/</span> <span class="identifier">DIGIT</span> <span class="special">/</span> <span class="string">"+"</span> <span class="special">/</span> <span class="string">"-"</span> <span class="special">/</span> <span class="string">"."</span> <span class="special">)</span>

<span class="identifier">URI</span>             <span class="special">=</span> <span class="identifier">scheme</span> <span class="string">":"</span> <span class="identifier">hier</span><span class="special">-</span><span class="identifier">part</span> <span class="special">[</span> <span class="string">"?"</span> <span class="identifier">query</span> <span class="special">]</span> <span class="special">[</span> <span class="string">"#"</span> <span class="identifier">fragment</span> <span class="special">]</span>

<span class="identifier">absolute</span><span class="special">-</span><span class="identifier">URI</span>    <span class="special">=</span> <span class="identifier">scheme</span> <span class="string">":"</span> <span class="identifier">hier</span><span class="special">-</span><span class="identifier">part</span> <span class="special">[</span> <span class="string">"?"</span> <span class="identifier">query</span> <span class="special">]</span>
</pre>
<h6>
<a name="url.ref.boost__urls__static_url_base.scheme.h5"></a>
          <span class="phrase"><a name="url.ref.boost__urls__static_url_base.scheme.specification"></a></span><a class="link" href="scheme.html#url.ref.boost__urls__static_url_base.scheme.specification">Specification</a>
        </h6>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
              <a href="https://datatracker.ietf.org/doc/html/rfc3986#section-3.1" target="_top">3.1.
              Scheme (rfc3986)</a>
            </li></ul></div>
<h6>
<a name="url.ref.boost__urls__static_url_base.scheme.h6"></a>
          <span class="phrase"><a name="url.ref.boost__urls__static_url_base.scheme.see_also"></a></span><a class="link" href="scheme.html#url.ref.boost__urls__static_url_base.scheme.see_also">See Also</a>
        </h6>
<p>
          <a class="link" href="has_scheme.html" title="static_url_base::has_scheme"><code class="computeroutput"><span class="identifier">has_scheme</span></code></a>, <a class="link" href="scheme_id.html" title="static_url_base::scheme_id"><code class="computeroutput"><span class="identifier">scheme_id</span></code></a>.
        </p>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="has_scheme.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__static_url_base.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="scheme_id.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
