<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>static_url::static_url (4 of 4 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../static_url.html" title="static_url::static_url">
<link rel="prev" href="overload3.html" title="static_url::static_url (3 of 4 overloads)">
<link rel="next" href="../operator_eq_.html" title="static_url::operator=">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../static_url.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../operator_eq_.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="url.ref.boost__urls__static_url.static_url.overload4"></a><a class="link" href="overload4.html" title="static_url::static_url (4 of 4 overloads)">static_url::static_url
          (4 of 4 overloads)</a>
</h6></div></div></div>
<p>
            Constructor.
          </p>
<h6>
<a name="url.ref.boost__urls__static_url.static_url.overload4.h0"></a>
            <span class="phrase"><a name="url.ref.boost__urls__static_url.static_url.overload4.synopsis"></a></span><a class="link" href="overload4.html#url.ref.boost__urls__static_url.static_url.overload4.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="identifier">static_url</span><span class="special">(</span>
    <span class="identifier">url_view_base</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">u</span><span class="special">);</span>
</pre>
<h6>
<a name="url.ref.boost__urls__static_url.static_url.overload4.h1"></a>
            <span class="phrase"><a name="url.ref.boost__urls__static_url.static_url.overload4.description"></a></span><a class="link" href="overload4.html#url.ref.boost__urls__static_url.static_url.overload4.description">Description</a>
          </h6>
<p>
            The newly constructed object contains a copy of <code class="computeroutput"><span class="identifier">u</span></code>.
          </p>
<h6>
<a name="url.ref.boost__urls__static_url.static_url.overload4.h2"></a>
            <span class="phrase"><a name="url.ref.boost__urls__static_url.static_url.overload4.postconditions"></a></span><a class="link" href="overload4.html#url.ref.boost__urls__static_url.static_url.overload4.postconditions">Postconditions</a>
          </h6>
<pre class="programlisting"><span class="keyword">this</span><span class="special">-&gt;</span><span class="identifier">buffer</span><span class="special">()</span> <span class="special">==</span> <span class="identifier">u</span><span class="special">.</span><span class="identifier">buffer</span><span class="special">()</span> <span class="special">&amp;&amp;</span> <span class="keyword">this</span><span class="special">-&gt;</span><span class="identifier">buffer</span><span class="special">.</span><span class="identifier">data</span><span class="special">()</span> <span class="special">!=</span> <span class="identifier">u</span><span class="special">.</span><span class="identifier">buffer</span><span class="special">().</span><span class="identifier">data</span><span class="special">()</span>
</pre>
<h6>
<a name="url.ref.boost__urls__static_url.static_url.overload4.h3"></a>
            <span class="phrase"><a name="url.ref.boost__urls__static_url.static_url.overload4.complexity"></a></span><a class="link" href="overload4.html#url.ref.boost__urls__static_url.static_url.overload4.complexity">Complexity</a>
          </h6>
<p>
            Linear in <code class="computeroutput"><span class="identifier">u</span><span class="special">.</span><span class="identifier">size</span><span class="special">()</span></code>.
          </p>
<h6>
<a name="url.ref.boost__urls__static_url.static_url.overload4.h4"></a>
            <span class="phrase"><a name="url.ref.boost__urls__static_url.static_url.overload4.exception_safety"></a></span><a class="link" href="overload4.html#url.ref.boost__urls__static_url.static_url.overload4.exception_safety">Exception
            Safety</a>
          </h6>
<p>
            Exception thrown if capacity exceeded.
          </p>
<h6>
<a name="url.ref.boost__urls__static_url.static_url.overload4.h5"></a>
            <span class="phrase"><a name="url.ref.boost__urls__static_url.static_url.overload4.exceptions"></a></span><a class="link" href="overload4.html#url.ref.boost__urls__static_url.static_url.overload4.exceptions">Exceptions</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Type
                    </p>
                  </th>
<th>
                    <p>
                      Thrown On
                    </p>
                  </th>
</tr></thead>
<tbody><tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">system_error</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Capacity would be exceeded.
                    </p>
                  </td>
</tr></tbody>
</table></div>
<h6>
<a name="url.ref.boost__urls__static_url.static_url.overload4.h6"></a>
            <span class="phrase"><a name="url.ref.boost__urls__static_url.static_url.overload4.parameters"></a></span><a class="link" href="overload4.html#url.ref.boost__urls__static_url.static_url.overload4.parameters">Parameters</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Name
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
</tr></thead>
<tbody><tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">u</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      The url to copy.
                    </p>
                  </td>
</tr></tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../static_url.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../operator_eq_.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
