<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.16">
<meta name="author" content="Quaternion / Vector / Matrix Library written in C++03 | <PERSON>">
<title>QVM</title>
<link rel="stylesheet" href="./zajo-dark.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="./rouge-github.css">
<meta name="keywords" content="c++,quaternion,vector,matrix,open source">
<meta name="description" content="Generic C++ library for working with Quaternions, Vectors and Matrices">
<link rel="stylesheet" href="./zajo-light.css" disabled=true>
<script>
function switch_style()
{
	var i, tag;
	for( i=0, tag=document.getElementsByTagName("link"); i<tag.length; i++ )
		if( tag[i].rel.indexOf("stylesheet")!=-1 && tag[i].href.includes("zajo-") )
			tag[i].disabled = !tag[i].disabled;
}
</script>
</head>
<body class="article toc2 toc-left">
<div id="header">
<h1>QVM <div style="z-index: 3; bottom:-9px; right:4px; position:fixed"><input width="32" height="32" type="image" alt="Skin" src="./skin.png" onclick="this.blur();switch_style();return false;"/></div></h1>
<div class="details">
<span id="author" class="author">Quaternion / Vector / Matrix Library written in C++03 | Emil Dotchevski</span><br>
</div>
<div id="toc" class="toc2">
<div id="toctitle"></div>
<ul class="sectlevel1">
<li><a href="#_abstract">Abstract</a></li>
<li><a href="#support">Support</a></li>
<li><a href="#_portability">Portability</a></li>
<li><a href="#_distribution">Distribution</a></li>
<li><a href="#tutorial">Tutorial</a>
<ul class="sectlevel2">
<li><a href="#_quaternions_vectors_matrices">Quaternions, Vectors, Matrices</a></li>
<li><a href="#_cc_arrays">C/C&#43;&#43; Arrays</a></li>
<li><a href="#view_proxy">View proxies</a></li>
<li><a href="#_swizzling">Swizzling</a></li>
<li><a href="#enable_if">SFINAE/enable_if</a></li>
<li><a href="#_interoperability">Interoperability</a>
<ul class="sectlevel3">
<li><a href="#_specifying_return_types_for_binary_operations">Specifying return types for binary operations</a></li>
<li><a href="#_specifying_return_types_for_unary_operations">Specifying return types for unary operations</a></li>
<li><a href="#_converting_between_different_quaternion_vector_and_matrix_types">Converting between different quaternion, vector and matrix types</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#reference">Reference</a>
<ul class="sectlevel2">
<li><a href="#_header_files">Header Files</a></li>
<li><a href="#type_traits">Type Traits System</a>
<ul class="sectlevel3">
<li><a href="#scalar_requirements">Scalar Requirements</a></li>
<li><a href="#is_scalar"><code>is_scalar</code></a></li>
<li><a href="#scalar_traits"><code>scalar_traits</code></a></li>
<li><a href="#deduce_scalar"><code>deduce_scalar</code></a></li>
<li><a href="#scalar"><code>scalar</code></a></li>
<li><a href="#is_quat"><code>is_quat</code></a></li>
<li><a href="#quat_traits"><code>quat_traits</code></a></li>
<li><a href="#quat_traits_defaults"><code>quat_traits_defaults</code></a></li>
<li><a href="#deduce_quat"><code>deduce_quat</code></a></li>
<li><a href="#deduce_quat2"><code>deduce_quat2</code></a></li>
<li><a href="#is_vec"><code>is_vec</code></a></li>
<li><a href="#vec_traits"><code>vec_traits</code></a></li>
<li><a href="#vec_traits_defaults"><code>vec_traits_defaults</code></a></li>
<li><a href="#deduce_vec"><code>deduce_vec</code></a></li>
<li><a href="#deduce_vec2"><code>deduce_vec2</code></a></li>
<li><a href="#is_mat"><code>is_mat</code></a></li>
<li><a href="#mat_traits"><code>mat_traits</code></a></li>
<li><a href="#mat_traits_defaults"><code>mat_traits_defaults</code></a></li>
<li><a href="#deduce_mat"><code>deduce_mat</code></a></li>
<li><a href="#deduce_mat2"><code>deduce_mat2</code></a></li>
</ul>
</li>
<li><a href="#_built_in_quaternion_vector_and_matrix_types">Built-in Quaternion, Vector and Matrix Types</a>
<ul class="sectlevel3">
<li><a href="#quat"><code>quat</code></a></li>
<li><a href="#vec"><code>vec</code></a></li>
<li><a href="#mat"><code>mat</code></a></li>
</ul>
</li>
<li><a href="#_element_access">Element Access</a>
<ul class="sectlevel3">
<li><a href="#quat_access">Quaternions</a></li>
<li><a href="#vec_access">Vectors</a></li>
<li><a href="#swizzling">Vector Element Swizzling</a></li>
<li><a href="#mat_access">Matrices</a></li>
</ul>
</li>
<li><a href="#_quaternion_operations">Quaternion Operations</a>
<ul class="sectlevel3">
<li><a href="#quat_assign"><code>assign</code></a></li>
<li><a href="#quat_convert_to"><code>convert_to</code></a></li>
<li><a href="#quat_minus_eq"><code>operator-=</code></a></li>
<li><a href="#quat_minus_unary"><code>operator-</code> (unary)</a></li>
<li><a href="#quat_minus"><code>operator-</code> (binary)</a></li>
<li><a href="#quat_plus_eq"><code>operator+=</code></a></li>
<li><a href="#quat_plus"><code>operator+</code></a></li>
<li><a href="#quat_div_eq_scalar"><code>operator/=</code> (scalar)</a></li>
<li><a href="#quat_div_scalar"><code>operator/</code> (scalar)</a></li>
<li><a href="#quat_mul_eq_scalar"><code>operator*=</code> (scalar)</a></li>
<li><a href="#quat_mul_eq"><code>operator*=</code></a></li>
<li><a href="#quat_mul_scalar"><code>operator*</code> (scalar)</a></li>
<li><a href="#quat_mul"><code>operator*</code></a></li>
<li><a href="#quat_eq"><code>operator==</code></a></li>
<li><a href="#quat_neq"><code>operator!=</code></a></li>
<li><a href="#quat_cmp"><code>cmp</code></a></li>
<li><a href="#quat_mag_sqr"><code>mag_sqr</code></a></li>
<li><a href="#quat_mag"><code>mag</code></a></li>
<li><a href="#quat_normalized"><code>normalized</code></a></li>
<li><a href="#quat_normalize"><code>normalize</code></a></li>
<li><a href="#quat_dot"><code>dot</code></a></li>
<li><a href="#conjugate"><code>conjugate</code></a></li>
<li><a href="#quat_inverse"><code>inverse</code></a></li>
<li><a href="#slerp"><code>slerp180</code> / <code>slerp360</code></a></li>
<li><a href="#zero_quat"><code>zero_quat</code></a></li>
<li><a href="#quat_set_zero"><code>set_zero</code></a></li>
<li><a href="#identity_quat"><code>identity_quat</code></a></li>
<li><a href="#quat_set_identity"><code>set_identity</code></a></li>
<li><a href="#rot_quat"><code>rot_quat</code></a></li>
<li><a href="#quat_set_rot"><code>set_rot</code></a></li>
<li><a href="#quat_rotate"><code>rotate</code></a></li>
<li><a href="#rotx_quat"><code>rotx_quat</code></a></li>
<li><a href="#quat_set_rotx"><code>set_rotx</code></a></li>
<li><a href="#quat_rotate_x"><code>rotate_x</code></a></li>
<li><a href="#roty_quat"><code>roty_quat</code></a></li>
<li><a href="#quat_set_roty"><code>set_roty</code></a></li>
<li><a href="#quat_rotate_y"><code>rotate_y</code></a></li>
<li><a href="#rotz_quat"><code>rotz_quat</code></a></li>
<li><a href="#quat_set_rotz"><code>set_rotz</code></a></li>
<li><a href="#quat_rotate_z"><code>rotate_z</code></a></li>
<li><a href="#quat_scalar_cast"><code>scalar_cast</code></a></li>
<li><a href="#qref"><code>qref</code></a></li>
</ul>
</li>
<li><a href="#_vector_operations">Vector Operations</a>
<ul class="sectlevel3">
<li><a href="#vec_assign"><code>assign</code></a></li>
<li><a href="#vec_convert_to"><code>convert_to</code></a></li>
<li><a href="#vec_minus_eq"><code>operator-=</code></a></li>
<li><a href="#vec_minus_unary"><code>operator-</code> (unary)</a></li>
<li><a href="#vec_minus"><code>operator-</code> (binary)</a></li>
<li><a href="#vec_plus_eq"><code>operator+=</code></a></li>
<li><a href="#vec_plus"><code>operator+</code></a></li>
<li><a href="#vec_div_eq_scalar"><code>operator/=</code> (scalar)</a></li>
<li><a href="#vec_div_scalar"><code>operator/</code></a></li>
<li><a href="#vec_mul_eq_scalar"><code>operator*=</code></a></li>
<li><a href="#vec_mul_scalar"><code>operator*</code></a></li>
<li><a href="#vec_eq"><code>operator==</code></a></li>
<li><a href="#vec_neq"><code>operator!=</code></a></li>
<li><a href="#vec_cmp"><code>cmp</code></a></li>
<li><a href="#vec_mag_sqr"><code>mag_sqr</code></a></li>
<li><a href="#vec_mag"><code>mag</code></a></li>
<li><a href="#vec_normalized"><code>normalized</code></a></li>
<li><a href="#vec_normalize"><code>normalize</code></a></li>
<li><a href="#vec_dot"><code>dot</code></a></li>
<li><a href="#vec_cross"><code>cross</code></a></li>
<li><a href="#zero_vec"><code>zero_vec</code></a></li>
<li><a href="#vec_set_zero"><code>set_zero</code></a></li>
<li><a href="#vec_scalar_cast"><code>scalar_cast</code></a></li>
<li><a href="#vref"><code>vref</code></a></li>
</ul>
</li>
<li><a href="#_matrix_operations">Matrix Operations</a>
<ul class="sectlevel3">
<li><a href="#mat_assign"><code>assign</code></a></li>
<li><a href="#mat_convert_to"><code>convert_to</code></a></li>
<li><a href="#mat_minus_eq_scalar"><code>operator-=</code></a></li>
<li><a href="#mat_minus_unary"><code>operator-</code> (unary)</a></li>
<li><a href="#mat_minus"><code>operator-</code></a></li>
<li><a href="#mat_plus_eq_scalar"><code>operator+=</code></a></li>
<li><a href="#mat_plus"><code>operator+</code></a></li>
<li><a href="#mat_div_eq_scalar"><code>operator/=</code> (scalar)</a></li>
<li><a href="#mat_div_scalar"><code>operator/</code> (scalar)</a></li>
<li><a href="#mat_mul_eq"><code>operator*=</code></a></li>
<li><a href="#mat_mul_eq_scalar"><code>operator*=</code> (scalar)</a></li>
<li><a href="#mat_mul"><code>operator*</code></a></li>
<li><a href="#mat_mul_scalar"><code>operator*</code> (scalar)</a></li>
<li><a href="#mat_eq"><code>operator==</code></a></li>
<li><a href="#mat_neq"><code>operator!=</code></a></li>
<li><a href="#mat_cmp"><code>cmp</code></a></li>
<li><a href="#mat_inverse"><code>inverse</code></a></li>
<li><a href="#zero_mat"><code>zero_mat</code></a></li>
<li><a href="#mat_set_zero"><code>set_zero</code></a></li>
<li><a href="#identity_mat"><code>identity_mat</code></a></li>
<li><a href="#mat_set_identity"><code>set_identity</code></a></li>
<li><a href="#rot_mat"><code>rot_mat</code> / Euler angles</a></li>
<li><a href="#mat_set_rot"><code>set_rot</code> / Euler angles</a></li>
<li><a href="#mat_rotate"><code>rotate</code> / Euler angles</a></li>
<li><a href="#rotx_mat"><code>rotx_mat</code></a></li>
<li><a href="#mat_set_rotx"><code>set_rotx</code></a></li>
<li><a href="#mat_rotate_x"><code>rotate_x</code></a></li>
<li><a href="#roty_mat"><code>roty_mat</code></a></li>
<li><a href="#mat_set_roty"><code>set_roty</code></a></li>
<li><a href="#mat_rotate_y"><code>rotate_y</code></a></li>
<li><a href="#rotz_mat"><code>rotz_mat</code></a></li>
<li><a href="#mat_set_rotz"><code>set_rotz</code></a></li>
<li><a href="#mat_rotate_z"><code>rotate_z</code></a></li>
<li><a href="#determinant"><code>determinant</code></a></li>
<li><a href="#perspective_lh"><code>perspective_lh</code></a></li>
<li><a href="#perspective_rh"><code>perspective_rh</code></a></li>
<li><a href="#mat_scalar_cast"><code>scalar_cast</code></a></li>
<li><a href="#mref"><code>mref</code></a></li>
</ul>
</li>
<li><a href="#_quaternion_vector_operations">Quaternion-Vector Operations</a>
<ul class="sectlevel3">
<li><a href="#quat_vec_mul"><code>operator*</code></a></li>
</ul>
</li>
<li><a href="#_matrix_vector_operations">Matrix-Vector Operations</a>
<ul class="sectlevel3">
<li><a href="#mat_vec_mul"><code>operator*</code></a></li>
<li><a href="#transform_vector"><code>transform_vector</code></a></li>
<li><a href="#transform_point"><code>transform_point</code></a></li>
</ul>
</li>
<li><a href="#_matrix_to_matrix_view_proxies">Matrix-to-Matrix View Proxies</a>
<ul class="sectlevel3">
<li><a href="#del_row"><code>del_row</code></a></li>
<li><a href="#del_col"><code>del_col</code></a></li>
<li><a href="#del_row_col"><code>del_row_col</code></a></li>
<li><a href="#neg_row"><code>neg_row</code></a></li>
<li><a href="#neg_col"><code>neg_col</code></a></li>
<li><a href="#swap_rows"><code>swap_rows</code></a></li>
<li><a href="#swap_cols"><code>swap_cols</code></a></li>
<li><a href="#transposed"><code>transposed</code></a></li>
</ul>
</li>
<li><a href="#_vector_to_matrix_view_proxies">Vector-to-Matrix View Proxies</a>
<ul class="sectlevel3">
<li><a href="#col_mat"><code>col_mat</code></a></li>
<li><a href="#row_mat"><code>row_mat</code></a></li>
<li><a href="#translation_mat"><code>translation_mat</code></a></li>
<li><a href="#diag_mat"><code>diag_mat</code></a></li>
</ul>
</li>
<li><a href="#_matrix_to_vector_view_proxies">Matrix-to-Vector View Proxies</a>
<ul class="sectlevel3">
<li><a href="#col"><code>col</code></a></li>
<li><a href="#row"><code>row</code></a></li>
<li><a href="#diag"><code>diag</code></a></li>
<li><a href="#translation"><code>translation</code></a></li>
</ul>
</li>
<li><a href="#_exceptions">Exceptions</a>
<ul class="sectlevel3">
<li><a href="#error"><code>error</code></a></li>
<li><a href="#zero_magnitude_error"><code>zero_magnitude_error</code></a></li>
<li><a href="#zero_determinant_error"><code>zero_determinant_error</code></a></li>
</ul>
</li>
<li><a href="#_macros_and_configuration_boost_qvm">Macros and Configuration: BOOST_QVM_</a>
<ul class="sectlevel3">
<li><a href="#BOOST_QVM_INLINE"><code>INLINE</code></a></li>
<li><a href="#BOOST_QVM_FORCE_INLINE"><code>FORCE_INLINE</code></a></li>
<li><a href="#BOOST_QVM_INLINE_TRIVIAL"><code>INLINE_TRIVIAL</code></a></li>
<li><a href="#BOOST_QVM_INLINE_CRITICAL"><code>INLINE_CRITICAL</code></a></li>
<li><a href="#BOOST_QVM_INLINE_OPERATIONS"><code>INLINE_OPERATIONS</code></a></li>
<li><a href="#BOOST_QVM_INLINE_RECURSION"><code>INLINE_RECURSION</code></a></li>
<li><a href="#BOOST_QVM_ASSERT"><code>ASSERT</code></a></li>
<li><a href="#BOOST_QVM_STATIC_ASSERT"><code>STATIC_ASSERT</code></a></li>
<li><a href="#BOOST_QVM_THROW_EXCEPTION"><code>THROW_EXCEPTION</code></a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#rationale">Design Rationale</a></li>
<li><a href="#_code_generator">Code Generator</a></li>
<li><a href="#_known_quirks_and_issues">Known Quirks and Issues</a>
<ul class="sectlevel2">
<li><a href="#_capturing_view_proxies_with_auto">Capturing View Proxies with <code>auto</code></a></li>
<li><a href="#_binding_qvm_overloads_from_an_unrelated_namespace">Binding QVM Overloads From an Unrelated Namespace</a></li>
<li><a href="#_link_errors_when_calling_math_functions_with_int_arguments">Link Errors When Calling Math Functions with <code>int</code> Arguments</a></li>
</ul>
</li>
<li><a href="#_qa">Q&amp;A</a></li>
</ul>
</div>
</div>
<div id="content">
<div id="preamble">
<div class="sectionbody">
<div class="paragraph text-right">
<p><a href="https://github.com/boostorg/qvm">GitHub</a> | <a href="https://boostorg.github.io/qvm/qvm.pdf">PDF</a></p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_abstract">Abstract</h2>
<div class="sectionbody">
<div class="paragraph">
<p>QVM is a generic library for working with Quaternions, Vectors and Matrices of static size. Features:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="ulist">
<ul>
<li>
<p>Emphasis on 2, 3 and 4-dimensional operations needed in graphics, video games and simulation applications.</p>
</li>
<li>
<p>Free function templates operate on any compatible user-defined Quaternion, Vector or Matrix type.</p>
</li>
<li>
<p>Enables Quaternion, Vector and Matrix types from different libraries to be safely mixed in the same expression.</p>
</li>
<li>
<p>Type-safe mapping between compatible lvalue types with no temporary objects; f.ex. transpose remaps the access to the elements, rather than transforming the matrix.</p>
</li>
<li>
<p>Requires only C&#43;&#43;03.</p>
</li>
<li>
<p>Zero dependencies.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="paragraph text-right">
<p><a href="#tutorial">Tutorial</a> | <a href="#reference">Reference</a> | <a href="#rationale">Design Rationale</a></p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="support">Support</h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p><a href="https://Cpplang.slack.com">cpplang on Slack</a> (use the <code>#boost</code> channel)</p>
</li>
<li>
<p><a href="https://lists.boost.org/mailman/listinfo.cgi/boost-users">Boost Users Mailing List</a></p>
</li>
<li>
<p><a href="https://lists.boost.org/mailman/listinfo.cgi/boost">Boost Developers Mailing List</a></p>
</li>
<li>
<p><a href="https://github.com/boostorg/qvm/issues">Report an issue</a> on GitHub</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_portability">Portability</h2>
<div class="sectionbody">
<div class="paragraph">
<p>QVM requires only C&#43;&#43;03 but is tested on many compiler versions and C&#43;&#43; standards.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_distribution">Distribution</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Copyright 2008-2023 Emil Dotchevski. Distributed under the <a href="http://www.boost.org/LICENSE_1_0.txt">Boost Software License, Version 1.0</a>.</p>
</div>
<div class="paragraph">
<p>There are three distribution channels:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>QVM is included in official <a href="https://www.boost.org/">Boost</a> releases.</p>
</li>
<li>
<p>The source code is hosted on <a href="https://github.com/boostorg/qvm">GitHub</a>.</p>
</li>
<li>
<p>For maximum portability, the latest release of the library is also available in single-header format, in two variants (direct download links):</p>
<div class="ulist">
<ul>
<li>
<p><a href="https://raw.githubusercontent.com/boostorg/qvm/gh-pages/qvm.hpp">qvm.hpp</a>: single header containing the complete QVM source, including the complete set of <a href="#swizzling">swizzling</a> overloads.</p>
</li>
<li>
<p><a href="https://raw.githubusercontent.com/boostorg/qvm/gh-pages/qvm_lite.hpp">qvm_lite.hpp</a>: single header containing everything except for the swizzling overloads.</p>
</li>
</ul>
</div>
</li>
</ul>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
QVM does not depend on Boost or other libraries.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="tutorial">Tutorial</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="_quaternions_vectors_matrices">Quaternions, Vectors, Matrices</h3>
<div class="paragraph">
<p>Out of the box QVM defines generic yet simple <a href="#quat"><code>quat</code></a>, <a href="#vec"><code>vec</code></a> and <a href="#mat"><code>mat</code></a> types. For example, the following snippet creates a quaternion object that rotates around the X axis:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">quat</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span> <span class="n">rx</span> <span class="o">=</span> <span class="n">rotx_quat</span><span class="p">(</span><span class="mf">3.14159</span><span class="n">f</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Similarly, a matrix that translates by a given vector can be created as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">vec</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span><span class="mi">3</span><span class="o">&gt;</span> <span class="n">v</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">7</span><span class="p">};</span>
<span class="n">mat</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span><span class="mi">4</span><span class="p">,</span><span class="mi">4</span><span class="o">&gt;</span> <span class="n">tr</span> <span class="o">=</span> <span class="n">translation_mat</span><span class="p">(</span><span class="n">v</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The usual quaternion, vector and matrix operations work on these QVM types, however the operations are decoupled from any specific type: they work on any suitable type that has been registered by specializing the <a href="#quat_traits"><code>quat_traits</code></a>, <a href="#vec_traits"><code>vec_traits</code></a> and <a href="#mat_traits"><code>mat_traits</code></a> templates.</p>
</div>
<div class="paragraph">
<p>For example, a user-defined 3D vector type <code>float3</code> can be introduced to QVM as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">struct</span> <span class="nc">float3</span> <span class="p">{</span> <span class="kt">float</span> <span class="n">a</span><span class="p">[</span><span class="mi">3</span><span class="p">];</span> <span class="p">};</span>

<span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">vec_traits</span><span class="o">&lt;</span><span class="n">float3</span><span class="o">&gt;</span> <span class="p">{</span>

    <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">dim</span><span class="o">=</span><span class="mi">3</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="kt">float</span> <span class="n">scalar_type</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">float3</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="n">read_element</span><span class="p">(</span> <span class="n">float3</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">float3</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
    <span class="p">}</span> <span class="c1">//optional</span>

    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="n">read_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">float3</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
    <span class="p">}</span> <span class="c1">//optional</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Equivalently, using the <a href="#vec_traits_defaults"><code>vec_traits_defaults</code></a> template the above can be shortened to:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">vec_traits</span><span class="o">&lt;</span><span class="n">float3</span><span class="o">&gt;:</span> <span class="n">vec_traits_defaults</span><span class="o">&lt;</span><span class="n">float3</span><span class="p">,</span><span class="kt">float</span><span class="p">,</span><span class="mi">3</span><span class="o">&gt;</span> <span class="p">{</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">float3</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">float3</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
    <span class="p">}</span> <span class="c1">//optional</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>After a similar specialization of the <a href="#mat_traits"><code>mat_traits</code></a> template for a user-defined 3x3 matrix type <code>float33</code>, the full range of vector and matrix operations defined by QVM headers becomes available automatically:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">float3</span> <span class="n">v</span><span class="p">;</span>
<span class="n">X</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
<span class="n">Y</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
<span class="n">Z</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">=</span> <span class="mi">7</span><span class="p">;</span>
<span class="kt">float</span> <span class="n">vmag</span> <span class="o">=</span> <span class="n">mag</span><span class="p">(</span><span class="n">v</span><span class="p">);</span>
<span class="n">float33</span> <span class="n">m</span> <span class="o">=</span> <span class="n">rotx_mat</span><span class="o">&lt;</span><span class="mi">3</span><span class="o">&gt;</span><span class="p">(</span><span class="mf">3.14159</span><span class="n">f</span><span class="p">);</span>
<span class="n">float3</span> <span class="n">vrot</span> <span class="o">=</span> <span class="n">m</span> <span class="o">*</span> <span class="n">v</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>User-defined quaternion types are similarly introduced to QVM by specializing the <a href="#quat_traits"><code>quat_traits</code></a> template.</p>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<div class="paragraph">
<p>In order to support vector types that can not provide access to elements by reference, the <code>write_element</code> / <code>write_element_idx</code> static member functions can be defined as <code>void</code> functions taking a scalar to write into an element, for example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
<span class="k">static</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="nf">write_element</span><span class="p">(</span> <span class="n">float3</span> <span class="o">&amp;</span> <span class="n">v</span><span class="p">,</span> <span class="n">scalar_type</span> <span class="n">s</span> <span class="p">)</span> <span class="p">{</span>
  <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">]</span> <span class="o">=</span> <span class="n">s</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Boost QVM automatically detects which alternative is provided.</p>
</div>
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect2">
<h3 id="_cc_arrays">C/C&#43;&#43; Arrays</h3>
<div class="paragraph">
<p>In <code>boost/qvm/quat_traits_array.hpp</code>, <code>vec_traits_array.hpp</code> and <code>mat_traits_array.hpp</code>, QVM defines appropriate <a href="#quat_traits"><code>quat_traits</code></a>, <a href="#vec_traits"><code>vec_traits</code></a> and <a href="#mat_traits"><code>mat_traits</code></a> specializations that allow QVM functions to operate directly on plain old C arrays:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="kt">float</span> <span class="n">v</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">7</span><span class="p">};</span>
<span class="n">float3</span> <span class="n">vrot</span> <span class="o">=</span> <span class="n">rotx_mat</span><span class="o">&lt;</span><span class="mi">3</span><span class="o">&gt;</span><span class="p">(</span><span class="mf">3.14159</span><span class="n">f</span><span class="p">)</span> <span class="o">*</span> <span class="n">v</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Naturally, operator overloads cannot kick in if all elements of an expression are of built-in types. The following is still illegal:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="kt">float</span> <span class="n">v</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">7</span><span class="p">};</span>
<span class="n">v</span> <span class="o">*=</span> <span class="mi">42</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The <a href="#vref"><code>vref</code></a> and <a href="#mref"><code>mref</code></a> function templates can be used to work around this issue:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="kt">float</span> <span class="n">v</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">7</span><span class="p">};</span>
<span class="n">vref</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">*=</span> <span class="mi">42</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>If C&#43;&#43;-11 is available, the same headers define appropriate <code>quat_traits</code>, <code>vec_traits</code> and <code>mat_traits</code> for <code>std::array</code> objects.</p>
</div>
<hr>
</div>
<div class="sect2">
<h3 id="view_proxy">View proxies</h3>
<div class="paragraph">
<p>QVM defines various function templates that provide static mapping between (possibly user-defined) quaternion, vector and matrix types. The example below multiplies column 1 (QVM indexes are always zero-based) of the matrix <code>m</code> by a scalar:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="kt">void</span> <span class="nf">multiply_column1</span><span class="p">(</span> <span class="n">float33</span> <span class="o">&amp;</span> <span class="n">m</span><span class="p">,</span> <span class="kt">float</span> <span class="n">scalar</span> <span class="p">)</span> <span class="p">{</span>
  <span class="n">col</span><span class="o">&lt;</span><span class="mi">1</span><span class="o">&gt;</span><span class="p">(</span><span class="n">m</span><span class="p">)</span> <span class="o">*=</span> <span class="n">scalar</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <a href="#col"><code>col&lt;1&gt;(m)</code></a> is an lvalue of an unspecified 3D vector type that refers to column 1 of <code>m</code>. Note however that this does not create any temporary objects; instead <code>operator*=</code> above works directly with a reference to <code>m</code>.</p>
</div>
<div class="paragraph">
<p>Here is another example, multiplying a transposed view of a matrix by a vector of some user-defined type <code>float3</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">float3</span> <span class="n">v</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">7</span><span class="p">};</span>
<span class="n">float3</span> <span class="n">vrot</span> <span class="o">=</span> <span class="n">transposed</span><span class="p">(</span><span class="n">rotx_mat</span><span class="o">&lt;</span><span class="mi">3</span><span class="o">&gt;</span><span class="p">(</span><span class="mf">3.14159</span><span class="n">f</span><span class="p">))</span> <span class="o">*</span> <span class="n">v</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>In general, the various view proxy functions return references of unspecified, non-copyable types that refer to the original object. They can be assigned from or converted to any compatible vector or matrix type.</p>
</div>
<hr>
</div>
<div class="sect2">
<h3 id="_swizzling">Swizzling</h3>
<div class="paragraph">
<p>QVM allows accessing vector elements by swizzling, exposing vector views of different dimensions, and/or views with reordered elements. The example below rotates <code>v</code> around the X axis, and stores the resulting vector back in <code>v</code> but with the X and Y elements swapped:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">float3</span> <span class="n">v</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">7</span><span class="p">};</span>
<span class="n">YXZ</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">=</span> <span class="n">rotx_mat</span><span class="o">&lt;</span><span class="mi">3</span><span class="o">&gt;</span><span class="p">(</span><span class="mf">3.14159</span><span class="n">f</span><span class="p">)</span> <span class="o">*</span> <span class="n">v</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>A special case of swizzling provides next-dimension-view of a vector object, adding either 0 or 1 as its last component. Assuming <code>float3</code> is a 3D vector type, and <code>float4</code> is a 4D vector type, the following statements are valid:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">float3</span> <span class="n">v</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">7</span><span class="p">};</span>
<span class="n">float4</span> <span class="n">point</span> <span class="o">=</span> <span class="n">XYZ1</span><span class="p">(</span><span class="n">v</span><span class="p">);</span> <span class="c1">//{0,0,7,1}</span>
<span class="n">float4</span> <span class="n">vector</span> <span class="o">=</span> <span class="n">XYZ0</span><span class="p">(</span><span class="n">v</span><span class="p">);</span> <span class="c1">//{0,0,7,0}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>It is also valid for swizzling to address vector elements more than once:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">float3</span> <span class="n">v</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">7</span><span class="p">};</span>
<span class="n">float4</span> <span class="n">v1</span> <span class="o">=</span> <span class="n">ZZZZ</span><span class="p">(</span><span class="n">v</span><span class="p">);</span> <span class="c1">//{7,7,7,7}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>QVM defines all permutations of <code>X</code>, <code>Y</code>, <code>Z</code>, <code>W</code> for 1D, 2D, 3D and 4D swizzling, plus each dimension defines variants with 0 or 1 used at any position (if 0 or 1 appear at the first position, the swizzling function name begins with underscore, e.g. <code>_1XY</code>).</p>
</div>
<div class="paragraph">
<p>The swizzling syntax can also be used to bind scalars as vectors. For example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">float3</span> <span class="n">v</span> <span class="o">=</span> <span class="n">_00X</span><span class="p">(</span><span class="mf">42.0</span><span class="n">f</span><span class="p">);</span> <span class="c1">//{0,0,42}</span></code></pre>
</div>
</div>
<hr>
</div>
<div class="sect2">
<h3 id="enable_if">SFINAE/enable_if</h3>
<div class="paragraph">
<p>SFINAE stands for Substitution Failure Is Not An Error. This refers to a situation in C&#43;&#43; where an invalid substitution of template parameters (including when those parameters are deduced implicitly as a result of an unqualified call) is not in itself an error.</p>
</div>
<div class="paragraph">
<p>In absence of concepts support, SFINAE can be used to disable function template overloads that would otherwise present a signature that is too generic. More formally, this is supported by the Boost <code>enable_if</code> library.</p>
</div>
<div class="paragraph">
<p>For example, QVM defines <code>operator*</code> overload which works with any user-defined matrix and vector types. The naive approach would be to declare this overload as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Matrix</span><span class="p">,</span><span class="k">class</span> <span class="nc">Vector</span><span class="p">&gt;</span>
<span class="n">Vector</span> <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">Matrix</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">m</span><span class="p">,</span> <span class="n">Vector</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Even if the function definition might contain code that would compile only for <code>Matrix</code> and <code>Vector</code> types, because the function declaration itself is valid, it will participate in overload rezolutions when multiplying objects of any two types whatsoever. This typically renders overload resolutions ambiguous and the compiler (correctly) issues an error.</p>
</div>
<div class="paragraph">
<p>Using <code>enable_if</code>, QVM declares such overloads in a way that preserves their generic signature but only participate in overload resolutions if the passed parameters make sense depending on the semantics of the operation being defined:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
<span class="k">typename</span> <span class="n">enable_if_c</span><span class="o">&lt;</span>
  <span class="n">is_mat</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">value</span> <span class="o">&amp;&amp;</span> <span class="n">is_vec</span><span class="o">&lt;</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">value</span> <span class="o">&amp;&amp;</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">==</span><span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">dim</span><span class="p">,</span> <span class="c1">//Condition</span>
  <span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span> <span class="c1">//Return type</span>
<span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>For brevity, function declarations throughout this documentation specify the condition which controls whether they are enabled or not without specifying exactly what <code>enable_if</code> construct is used to achieve this effect.</p>
</div>
<hr>
</div>
<div class="sect2">
<h3 id="_interoperability">Interoperability</h3>
<div class="paragraph">
<p>An important design goal of QVM is that it works seamlessly with 3rd-party quaternion, vector and matrix types and libraries. Even when such libraries overload the same C&#43;&#43; operators as QVM, it is safe to bring the entire <code>boost::qvm</code> namespace in scope by specifying:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">using</span> <span class="k">namespace</span> <span class="n">boost</span><span class="o">::</span><span class="n">qvm</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The above using directive does not introduce ambiguities with function and operator overloads defined by a 3rd-party library because:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Most <code>boost::qvm</code> function overloads and all operator overloads use SFINAE/<code>enable_if</code>, which makes them "disappear" unless an expression uses types that have the appropriate QVM-specific type traits defined;</p>
</li>
<li>
<p>Whenever such overloads are compatible with a given expression, their signature is extremely generic, which means that any other (user-defined) compatible overload will be a better match in any overload resolution.</p>
</li>
</ul>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Bringing the entire boost::qvm namespace in scope may introduce ambiguities when accessing types (as opposed to functions) defined by 3rd-party libraries. In that case, you can safely bring namespace <code>boost::qvm::sfinae</code> in scope instead, which contains only function and operator overloads that use SFINAE/<code>enable_if</code>.
</td>
</tr>
</table>
</div>
<div class="sect3">
<h4 id="_specifying_return_types_for_binary_operations">Specifying return types for binary operations</h4>
<div class="paragraph">
<p>Bringing the <code>boost::qvm</code> namespace in scope lets you mix vector and matrix types that come from different APIs into a common, type-safe framework. In this case however, it should be considered what types should be returned by binary operations that return an object by value. For example, if you multiply a 3x3 matrix <code>m1</code> of type <code>user_matrix1</code> by a 3x3 matrix <code>m2</code> of type <code>user_matrix2</code>, what type should that operation return?</p>
</div>
<div class="paragraph">
<p>The answer is that by default, QVM returns some kind of compatible matrix type, so it is always safe to write:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">auto</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="o">=</span> <span class="n">m1</span> <span class="o">*</span> <span class="n">m2</span><span class="p">;</span> <span class="c1">// auto requires C++11</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>However, the type deduced by default converts implicitly to any compatible matrix type, so the following is also valid, at the cost of a temporary:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">user_matrix1</span> <span class="n">m</span> <span class="o">=</span> <span class="n">m1</span> <span class="o">*</span> <span class="n">m2</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>While the temporary object can be optimized away by many compilers, it can be avoided altogether by specializing the <a href="#deduce_mat2"><code>deduce_mat2</code></a> template. For example, to specify that multiplying a <code>user_matrix1</code> by a <code>user_matrix2</code> should always produce a <code>user_matrix1</code> object, you could write:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">deduce_mat2</span><span class="o">&lt;</span><span class="n">user_matrix1</span><span class="p">,</span><span class="n">user_matrix2</span><span class="p">,</span><span class="mi">3</span><span class="p">,</span><span class="mi">3</span><span class="o">&gt;</span> <span class="p">{</span>
    <span class="k">typedef</span> <span class="n">user_matrix1</span> <span class="n">type</span><span class="p">;</span>
  <span class="p">};</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">deduce_mat2</span><span class="o">&lt;</span><span class="n">user_matrix2</span><span class="p">,</span><span class="n">user_matrix1</span><span class="p">,</span><span class="mi">3</span><span class="p">,</span><span class="mi">3</span><span class="o">&gt;</span> <span class="p">{</span>
    <span class="k">typedef</span> <span class="n">user_matrix1</span> <span class="n">type</span><span class="p">;</span>
  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="admonitionblock warning">
<table>
<tr>
<td class="icon">
<i class="fa icon-warning" title="Warning"></i>
</td>
<td class="content">
<div class="paragraph">
<p>Be mindful of potential ODR violation when using <a href="#deduce_quat2"><code>deduce_quat2</code></a>, <a href="#deduce_vec2"><code>deduce_vec2</code></a> and <a href="#deduce_mat2"><code>deduce_mat2</code></a> in independent libraries. For example, this could happen if <code>lib1</code> defines <code>deduce_vec2&lt;lib1::vec,lib2::vec&gt;::type</code> as <code>lib1::vec</code> and in the same program <code>lib2</code> defines <code>deduce_vec2&lt;lib1::vec,lib2::vec&gt;::type</code> as <code>lib2::vec</code>.</p>
</div>
<div class="paragraph">
<p>It is best to keep such specializations out of <code>lib1</code> and <code>lib2</code>. Of course, it is always safe for <code>lib1</code> and <code>lib2</code> to use <a href="#convert_to"><code>convert_to</code></a> to convert between the <code>lib1::vec</code> and <code>lib2::vec</code> types as needed.</p>
</div>
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="_specifying_return_types_for_unary_operations">Specifying return types for unary operations</h4>
<div class="paragraph">
<p>Perhaps surprisingly, unary operations that return an object by value have a similar, though simpler issue. That&#8217;s because the argument they&#8217;re called with may not be copyable, as in:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="kt">float</span> <span class="n">m</span><span class="p">[</span><span class="mi">3</span><span class="p">][</span><span class="mi">3</span><span class="p">];</span>
<span class="k">auto</span> <span class="o">&amp;</span> <span class="n">inv</span> <span class="o">=</span> <span class="n">inverse</span><span class="p">(</span><span class="n">m</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Above, the object returned by <a href="#mat_inverse"><code>inverse</code></a> and captured by <code>inv</code> can not be of type <code>float[3][3]</code>, because that type isn&#8217;t copyable. By default, QVM "just works", returning an object of suitable matrix type that is copyable. This deduction process can be controlled, by specializing the <a href="#deduce_mat"><code>deduce_mat</code></a> template.</p>
</div>
</div>
<div class="sect3">
<h4 id="_converting_between_different_quaternion_vector_and_matrix_types">Converting between different quaternion, vector and matrix types</h4>
<div class="paragraph">
<p>Any time you need to create a matrix of a particular C&#43;&#43; type from any other compatible matrix type, you can use the <a href="#convert_to"><code>convert_to</code></a> function:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">user_matrix2</span> <span class="n">m</span><span class="o">=</span><span class="n">convert_to</span><span class="o">&lt;</span><span class="n">user_matrix2</span><span class="o">&gt;</span><span class="p">(</span><span class="n">m1</span> <span class="o">*</span> <span class="n">m2</span><span class="p">);</span></code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="reference">Reference</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="_header_files">Header Files</h3>
<div class="paragraph">
<p>QVM is split into multiple headers to allow different compilation units to <code>#include</code> only the components they need. Each function in this document specifies the exact header that must be <code>#included</code> in order to use it.</p>
</div>
<div class="paragraph">
<p>The tables below list commonly used components and the headers they&#8217;re found in. Header names containing a number define functions that only work with objects of that dimension; e.g. <code>vec_operations2.hpp</code> contains only functions for working with 2D vectors.</p>
</div>
<div class="paragraph">
<p>The header <code>boost/qvm/all.hpp</code> is provided for convenience. It includes all other QVM headers.</p>
</div>
<div class="paragraph">
<p>In addition, Boost QVM is available in single-header format for maximum portability. See <a href="#_distribution">Distribution</a>.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 1. Quaternion header files</caption>
<colgroup>
<col style="width: 33.3333%;">
<col style="width: 66.6667%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Quaternion traits</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/quat_traits.hpp&gt;
#include &lt;boost/qvm/quat_traits_array.hpp&gt;
#include &lt;boost/qvm/deduce_quat.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Quaternion element access</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/quat_access.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Quaternion operations</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/quat_operations.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#quat"><code>quat</code></a> class template</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/quat.hpp&gt;</pre></div></td>
</tr>
</tbody>
</table>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 2. Vector header files</caption>
<colgroup>
<col style="width: 33.3333%;">
<col style="width: 66.6667%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Vector traits</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/vec_traits.hpp&gt;
#include &lt;boost/qvm/vec_traits_array.hpp&gt;
#include &lt;boost/qvm/deduce_vec.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Vector element access</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/vec_access.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Vector <a href="#swizzling">swizzling</a></p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/swizzle.hpp&gt;
#include &lt;boost/qvm/swizzle2.hpp&gt;
#include &lt;boost/qvm/swizzle3.hpp&gt;
#include &lt;boost/qvm/swizzle4.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Vector operations</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/vec_operations.hpp&gt;
#include &lt;boost/qvm/vec_operations2.hpp&gt;
#include &lt;boost/qvm/vec_operations3.hpp&gt;
#include &lt;boost/qvm/vec_operations4.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Quaternion-vector operations</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/quat_vec_operations.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Vector-matrix operations</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/vec_mat_operations.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Vector-matrix <a href="#view_proxy">view proxies</a></p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/map_vec_mat.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#vec"><code>vec</code></a> class template</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/vec.hpp&gt;</pre></div></td>
</tr>
</tbody>
</table>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 3. Matrix header files</caption>
<colgroup>
<col style="width: 33.3333%;">
<col style="width: 66.6667%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Matrix traits</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/mat_traits.hpp&gt;
#include &lt;boost/qvm/mat_traits_array.hpp&gt;
#include &lt;boost/qvm/deduce_mat.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Matrix element access</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/mat_access.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Matrix operations</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/mat_operations.hpp&gt;
#include &lt;boost/qvm/mat_operations2.hpp&gt;
#include &lt;boost/qvm/mat_operations3.hpp&gt;
#include &lt;boost/qvm/mat_operations4.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Matrix-matrix <a href="#view_proxy">view proxies</a></p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/map_mat_mat.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">Matrix-vector <a href="#view_proxy">view proxies</a></p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/map_mat_vec.hpp&gt;</pre></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><a href="#mat"><code>mat</code></a> class template</p></td>
<td class="tableblock halign-left valign-top"><div class="literal"><pre>#include &lt;boost/qvm/mat.hpp&gt;</pre></div></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="type_traits">Type Traits System</h3>
<div class="paragraph">
<p>QVM is designed to work with user-defined quaternion, vector and matrix types, as well as user-defined scalar types. This section formally defines the way such types can be integrated.</p>
</div>
<hr>
<div class="sect3">
<h4 id="scalar_requirements">Scalar Requirements</h4>
<div class="paragraph">
<p>A valid scalar type <code>S</code> must have accessible destructor, default constructor, copy constructor and assignment operator, and must support the following operations:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">S</span> <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">S</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span>
<span class="n">S</span> <span class="k">operator</span><span class="o">/</span><span class="p">(</span> <span class="n">S</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span>
<span class="n">S</span> <span class="k">operator</span><span class="o">+</span><span class="p">(</span> <span class="n">S</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span>
<span class="n">S</span> <span class="k">operator</span><span class="o">-</span><span class="p">(</span> <span class="n">S</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span>

<span class="n">S</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">*=</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span>
<span class="n">S</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">/=</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span>
<span class="n">S</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">+=</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span>
<span class="n">S</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">-=</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span>

<span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">S</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span>
<span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">S</span><span class="p">,</span> <span class="n">S</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>In addition, the expression <code>S(0)</code> should construct a scalar of value zero, and <code>S(1)</code> should construct a scalar of value one, or else the <a href="#scalar_traits"><code>scalar_traits</code></a> template must be specialized appropriately.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="is_scalar"><code>is_scalar</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/scalar_traits.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">is_scalar</span> <span class="p">{</span>
    <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">false</span><span class="p">;</span>
  <span class="p">};</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">char</span><span class="o">&gt;</span>           <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">signed</span> <span class="kt">char</span><span class="o">&gt;</span>    <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">unsigned</span> <span class="kt">char</span><span class="o">&gt;</span>  <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">signed</span> <span class="kt">short</span><span class="o">&gt;</span>   <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">unsigned</span> <span class="kt">short</span><span class="o">&gt;</span> <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">signed</span> <span class="kt">int</span><span class="o">&gt;</span>     <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">unsigned</span> <span class="kt">int</span><span class="o">&gt;</span>   <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">signed</span> <span class="kt">long</span><span class="o">&gt;</span>    <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">unsigned</span> <span class="kt">long</span><span class="o">&gt;</span>  <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span>          <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">double</span><span class="o">&gt;</span>         <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_scalar</span><span class="o">&lt;</span><span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span>    <span class="p">{</span> <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span><span class="o">=</span><span class="nb">true</span><span class="p">;</span> <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This template defines a compile-time boolean constant value which can be used to determine whether a type <code>T</code> is a valid scalar type. It must be specialized together with the <a href="#scalar_traits"><code>scalar_traits</code></a> template in order to introduce a user-defined scalar type to QVM. Such types must satisfy the <a href="#scalar_requirements">scalar requirements</a>.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="scalar_traits"><code>scalar_traits</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/scalar_traits.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Scalar</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">scalar_traits</span> <span class="p">{</span>

    <span class="n">BOOST_QVM_INLINE_CRITICAL</span>
    <span class="k">static</span> <span class="n">Scalar</span> <span class="n">value</span><span class="p">(</span> <span class="kt">int</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">Scalar</span><span class="p">(</span><span class="n">v</span><span class="p">);</span>
    <span class="p">}</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This template may be specialized for user-defined scalar types to define the appropriate conversion from <code>int</code>; this is primarily used whenever QVM needs to deduce a zero or one value.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="deduce_scalar"><code>deduce_scalar</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/deduce_scalar.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">deduce_scalar</span>
  <span class="p">{</span>
    <span class="k">typedef</span> <span class="k">typename</span> <span class="cm">/*exact definition unspecified*/</span> <span class="n">type</span><span class="p">;</span>
  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>A</code> and <code>B</code> satisfy the <a href="#scalar_requirements">scalar requirements</a>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If <code>A</code> and <code>B</code> are the same type, <code>scalar_traits&lt;A,B&gt;::type</code> is defined as that type. Otherwise for the following types:</p>
<div class="ulist">
<ul>
<li>
<p><code>signed</code>/<code>unsigned char</code>,</p>
</li>
<li>
<p><code>signed</code>/<code>unsigned short</code>,</p>
</li>
<li>
<p><code>signed</code>/<code>unsigned int</code>,</p>
</li>
<li>
<p><code>signed</code>/<code>unsigned long</code>,</p>
</li>
<li>
<p><code>float</code>,</p>
</li>
<li>
<p><code>double</code>,</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>the deduction logic is as follows:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>if either of <code>A</code> and <code>B</code> is <code>double</code>, the result is <code>double</code>;</p>
</li>
<li>
<p>else, if one of <code>A</code> or <code>B</code> is an integer type and the other is <code>float</code>, the result is <code>float</code>;</p>
</li>
<li>
<p>else, if one of <code>A</code> or <code>B</code> is a signed integer and the other type is unsigned integer, the signed type is changed to unsigned, and then the lesser of the two integers is promoted to the other.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>For any other types <code>scalar_traits&lt;A,B&gt;::type</code> is defined as <code>void</code>. It can be specialized for user-defined scalar types.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
This template is used by generic binary operations that return a scalar, to deduce the return type based on the (possibly different) scalars of their arguments.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="scalar"><code>scalar</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/scalar_traits.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">scalar</span> <span class="p">{</span>
    <span class="k">typedef</span> <span class="cm">/*exact definition unspecified*/</span> <span class="n">type</span><span class="p">;</span>
  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <a href="#quat_traits"><code>quat_traits&lt;T&gt;::scalar_type</code></a> evaluates to the scalar type of the quaternion type <code>T</code> (if <a href="#is_quat"><code>is_quat&lt;T&gt;::value</code></a> is <code>true</code>).</p>
</div>
<div class="paragraph">
<p>The expression <a href="#vec_traits"><code>vec_traits&lt;T&gt;::scalar_type</code></a> evaluates to the scalar type of the vector type <code>T</code> (if <a href="#is_vec"><code>is_vec&lt;T&gt;::value</code></a> is <code>true</code>).</p>
</div>
<div class="paragraph">
<p>The expression <a href="#mat_traits"><code>mat_traits&lt;T&gt;::scalar_type</code></a> evaluates to the scalar type of the matrix type <code>T</code> (if <a href="#is_mat"><code>is_mat&lt;T&gt;::value</code></a> is <code>true</code>).</p>
</div>
<div class="paragraph">
<p>The expression <code>scalar&lt;T&gt;::type</code> is similar, except that it automatically detects whether <code>T</code> is a vector or a matrix or a quaternion type.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="is_quat"><code>is_quat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_traits.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">is_quat</span> <span class="p">{</span>

    <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span> <span class="o">=</span> <span class="nb">false</span><span class="p">;</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This type template defines a compile-time boolean constant value which can be used to determine whether a type <code>T</code> is a quaternion type. For quaternion types, the <a href="#quat_traits"><code>quat_traits</code></a> template can be used to access their elements generically, or to obtain their <code>scalar type</code>.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_traits"><code>quat_traits</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_traits.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Q</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">quat_traits</span> <span class="p">{</span>

    <span class="cm">/*main template members unspecified*/</span>

  <span class="p">};</span>

  <span class="cm">/*
  User-defined (possibly partial) specializations:

  template &lt;&gt;
  struct quat_traits&lt;Q&gt; {

    typedef &lt;&lt;user-defined&gt;&gt; scalar_type;

    template &lt;int I&gt;
    static inline scalar_type read_element( Quaternion const &amp; q );

    template &lt;int I&gt;
    static inline scalar_type &amp; write_element( Quaternion &amp; q );

    // Alternative signature for write_element:

    template &lt;int I&gt;
    static inline void write_element( Quaternion &amp; q, scalar_type s );

  };
  */</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The <code>quat_traits</code> template must be specialized for (user-defined) quaternion types in order to enable quaternion operations defined in QVM headers for objects of those types.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
QVM quaternion operations do not require that quaternion types are copyable.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>The main <code>quat_traits</code> template members are not specified. Valid specializations are required to define the following members:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>scalar_type</code>: the expression <code>quat_traits&lt;Quaternion&gt;::scalar_type</code> must be a value type which satisfies the <a href="#scalar_requirements"><code>scalar requirements</code></a>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>In addition, valid specializations of the <code>quat_traits</code> template must define at least one of the following access functions as static members, where <code>q</code> is an object of type <code>Quaternion</code>, and <code>I</code> is compile-time integer constant:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>read_element</code>: the expression <code>quat_traits&lt;Quaternion&gt;::read_element&lt;I&gt;(q)</code> returns either a copy of or a <code>const</code> reference to the <code>I</code>-th element of <code>q</code>.</p>
</li>
<li>
<p><code>write_element</code>: the expression <code>quat_traits&lt;Quaternion&gt;::write_element&lt;I&gt;(q)</code> returns mutable reference to the <code>I</code>-th
element of <code>q</code>.</p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Alternatively, <code>write_element</code> may be defined as <code>void</code> function that takes (as the last argument) a scalar to write into the specified element. QVM automatically detects this case by examining the signature of the <code>write_element</code> template.
</td>
</tr>
</table>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
For the quaternion <code>a + bi + cj + dk</code>, the elements are assumed to be in the following order: <code>a</code>, <code>b</code>, <code>c</code>, <code>d</code>; that is, <code>I</code>=<code>0</code>/<code>1</code>/<code>2</code>/<code>3</code> would access <code>a</code>/<code>b</code>/<code>c</code>/<code>d</code>.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>It is illegal to call any of the above functions unless <code>is_quat&lt;Quaternion&gt;::value</code> is true. Even then, quaternion types are allowed to define only a subset of the access functions.</p>
</div>
<div class="paragraph">
<p>Below is an example of a user-defined quaternion type, and its corresponding specialization of the quat_traits template:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="cp">#include &lt;boost/qvm/quat_traits.hpp&gt;
</span>
<span class="k">struct</span> <span class="nc">fquat</span> <span class="p">{</span> <span class="kt">float</span> <span class="n">a</span><span class="p">[</span><span class="mi">4</span><span class="p">];</span> <span class="p">};</span>

<span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">quat_traits</span><span class="o">&lt;</span><span class="n">fquat</span><span class="o">&gt;</span> <span class="p">{</span>

    <span class="k">typedef</span> <span class="kt">float</span> <span class="n">scalar_type</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">fquat</span> <span class="o">&amp;</span> <span class="n">q</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">q</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="n">read_element</span><span class="p">(</span> <span class="n">fquat</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">q</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">q</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
    <span class="p">}</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Equivalently, using the <a href="#quat_traits_defaults"><code>quat_traits_defaults</code></a> template the above can be shortened to:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">quat_traits</span><span class="o">&lt;</span><span class="n">fquat</span><span class="o">&gt;:</span> <span class="n">quat_traits_defaults</span><span class="o">&lt;</span><span class="n">fquat</span><span class="p">,</span><span class="kt">float</span><span class="o">&gt;</span> <span class="p">{</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">fquat</span> <span class="o">&amp;</span> <span class="n">q</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">q</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
    <span class="p">}</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_traits_defaults"><code>quat_traits_defaults</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_traits_defaults.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">QuatType</span><span class="p">,</span><span class="k">class</span> <span class="nc">ScalarType</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">quat_traits_defaults</span> <span class="p">{</span>

    <span class="k">typedef</span> <span class="n">QuatType</span> <span class="n">quat_type</span><span class="p">;</span>

    <span class="k">typedef</span> <span class="n">ScalarType</span> <span class="n">scalar_type</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="n">BOOST_QVM_INLINE_CRITICAL</span>
    <span class="n">scalar_type</span> <span class="n">read_element</span><span class="p">(</span> <span class="n">quat_type</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">quat_type</span><span class="o">&gt;::</span><span class="k">template</span>
        <span class="n">write_element</span><span class="o">&lt;</span><span class="n">I</span><span class="p">&gt;(</span><span class="k">const_cast</span><span class="o">&lt;</span><span class="n">quat_type</span> <span class="o">&amp;&gt;</span><span class="p">(</span><span class="n">x</span><span class="p">));</span>
    <span class="p">}</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The <code>quat_traits_defaults</code> template is designed to be used as a public base for user-defined specializations of the <a href="#quat_traits"><code>quat_traits</code></a> template, to easily define the required members. If it is used, the only member that must be defined by the user in a <code>quat_traits</code> specialization is <code>write_element</code>; the <code>quat_traits_defaults</code> base will define <code>read_element</code>, as well as <code>scalar_type</code> automatically.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="deduce_quat"><code>deduce_quat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/deduce_quat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Q</span><span class="p">,</span>
    <span class="k">class</span> <span class="nc">S</span><span class="o">=</span><span class="k">typename</span> <span class="nc">quat_traits</span><span class="o">&lt;</span><span class="n">Q</span><span class="p">&gt;</span><span class="o">::</span><span class="n">scalar_type</span><span class="o">&gt;</span>
  <span class="k">struct</span> <span class="nc">deduce_quat</span> <span class="p">{</span>
    <span class="k">typedef</span> <span class="n">Q</span> <span class="n">type</span><span class="p">;</span>
  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p><code><a href="#is_quat">is_quat</a>&lt;Q&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code><a href="#is_scalar">is_scalar</a>&lt;S&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code><a href="#is_quat">is_quat</a>&lt;deduce_quat&lt;Q,S&gt;::type&gt;::value</code> must be <code>true</code>;</p>
</li>
<li>
<p><code><a href="#quat_traits">quat_traits</a>&lt;deduce_quat&lt;Q,S&gt;::type&gt;::scalar_type</code> must be the same type as <code>S</code>;</p>
</li>
<li>
<p><code>deduce_quat&lt;Q,S&gt;::type</code> must be copyable.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>This template is used by QVM whenever it needs to deduce a copyable quaternion type from the quaternion type <code>Q</code>, with a scalar type <code>S</code>. Note that <code>Q</code> itself may be non-copyable.</p>
</div>
<div class="paragraph">
<p>The main template definition returns an unspecified quaternion type, except if <code>S</code> is the same type as <code><a href="#quat_traits">quat_traits</a>&lt;Q&gt;::scalar_type</code>, in which case it returns <code>Q</code>, which is only suitable if <code>Q</code> is copyable. QVM also defines (partial) specializations for the non-copyable quaternion types it produces. Users can define other (partial) specializations for their own types.</p>
</div>
<div class="paragraph">
<p>A typical use of the <code>deduce_quat</code> template is for specifying the preferred quaternion type to be returned by the generic function template overloads in QVM depending on the type of their arguments.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="deduce_quat2"><code>deduce_quat2</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/deduce_quat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span>
    <span class="k">class</span> <span class="nc">S</span><span class="o">=</span><span class="k">typename</span> <span class="nc">deduce_scalar</span><span class="o">&lt;</span>
      <span class="k">typename</span> <span class="nc">scalar</span><span class="o">&lt;</span><span class="n">A</span><span class="p">&gt;</span><span class="o">::</span><span class="n">type</span><span class="p">,</span>
      <span class="k">typename</span> <span class="n">scalar</span><span class="o">&lt;</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span><span class="o">&gt;::</span><span class="n">type</span><span class="o">&gt;</span>
  <span class="k">struct</span> <span class="nc">deduce_quat2</span> <span class="p">{</span>
    <span class="k">typedef</span> <span class="cm">/*unspecified*/</span> <span class="n">type</span><span class="p">;</span>
  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Both <code><a href="#scalar">scalar</a>&lt;A&gt;::type</code> and <code>scalar&lt;B&gt;::type</code> are well defined;</p>
</li>
<li>
<p><code><a href="#is_quat">is_quat</a>&lt;A&gt;::value</code> || <code>is_quat&lt;B&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code><a href="#is_scalar">is_scalar</a>&lt;S&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code>is_quat&lt;deduce_quat2&lt;A,B,S&gt;::type&gt;::value</code> must be <code>true</code>;</p>
</li>
<li>
<p><code><a href="#quat_traits">quat_traits</a>&lt;deduce_quat2&lt;A,B,S&gt;::type&gt;::scalar_type</code> must be the same type as <code>S</code>;</p>
</li>
<li>
<p><code>deduce_quat2&lt;A,B,S&gt;::type</code> must be copyable.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>This template is used by QVM whenever it needs to deduce a quaternion type from the types of two user-supplied function parameters, with scalar type <code>S</code>. The returned type must have accessible copy constructor (the <code>A</code> and <code>B</code> types themselves could be non-copyable, and either one of them may not be a quaternion type.)</p>
</div>
<div class="paragraph">
<p>The main template definition returns an unspecified quaternion type with <a href="#quat_traits"><code>scalar_type</code></a> <code>S</code>, except if <code>A</code> and <code>B</code> are the same quaternion type <code>Q</code>, in which case <code>Q</code> is returned, which is only suitable for copyable types. QVM also defines (partial) specializations for the non-copyable quaternion types it produces. Users can define other (partial) specializations for their own types.</p>
</div>
<div class="paragraph">
<p>A typical use of the <code>deduce_quat2</code> template is for specifying the preferred quaternion type to be returned by the generic function template overloads in QVM depending on the type of their arguments.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="is_vec"><code>is_vec</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_traits.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">is_vec</span> <span class="p">{</span>

    <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span> <span class="o">=</span> <span class="nb">false</span><span class="p">;</span>

  <span class="p">};</span>

 <span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This type template defines a compile-time boolean constant value which can be used to determine whether a type <code>T</code> is a vector type. For vector types, the <a href="#vec_traits"><code>vec_traits</code></a> template can be used to access their elements generically, or to obtain their dimension and <code>scalar type</code>.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_traits"><code>vec_traits</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_traits.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">vec_traits</span> <span class="p">{</span>

    <span class="cm">/*main template members unspecified*/</span>

  <span class="p">};</span>

  <span class="cm">/*
  User-defined (possibly partial) specializations:

  template &lt;&gt;
  struct vec_traits&lt;V&gt; {

    static int const dim = &lt;&lt;user-defined&gt;&gt;;

    typedef &lt;&lt;user-defined&gt;&gt; scalar_type;

    template &lt;int I&gt;
    static inline scalar_type read_element( Vector const &amp; v );

    static inline scalar_type read_element_idx( int i, Vector const &amp; v );

    template &lt;int I&gt;
    static inline scalar_type &amp; write_element( Vector &amp; v );

    static inline scalar_type &amp; write_element_idx( int i, Vector &amp; v );

    // Alternative signature for write_element and write_element_idx:

    template &lt;int I&gt;
    static inline void write_element( Vector &amp; v, scalar_type s );

    static inline void write_element_idx( int i, Vector &amp; v, scalar_type s );

  };
  */</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The <code>vec_traits</code> template must be specialized for (user-defined) vector types in order to enable vector and matrix operations defined in QVM headers for objects of those types.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
QVM vector operations do not require that vector types are copyable.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>The main <code>vec_traits</code> template members are not specified. Valid specializations are required to define the following members:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>dim</code>: the expression <code>vec_traits&lt;Vector&gt;::dim</code> must evaluate to a compile-time integer constant greater than 0 that specifies the vector size.</p>
</li>
<li>
<p><code>scalar_type</code>: the expression <code>vec_traits&lt;Vector&gt;::scalar_type</code> must be a value type which satisfies the <a href="#scalar_requirements"><code>scalar requirements</code></a>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>In addition, valid specializations of the <code>vec_traits</code> template may define the following access functions as static members, where <code>v</code> is an object of type <code>Vector</code>, <code>I</code> is a compile-time integer constant, and <code>i</code> is a variable of type <code>int</code>:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>read_element</code>: the expression <code>vec_traits&lt;Vector&gt;::read_element&lt;I&gt;(v)</code> returns either a copy of or a const reference to the <code>I</code>-th element of <code>v</code>.</p>
</li>
<li>
<p><code>read_element_idx</code>: the expression <code>vec_traits&lt;Vector&gt;::read_element_idx(i,v)</code> returns either a copy of or a <code>const</code> reference to the <code>i</code>-th element of <code>v</code>.</p>
</li>
<li>
<p><code>write_element</code>: the expression <code>vec_traits&lt;Vector&gt;::write_element&lt;I&gt;(v)</code> returns mutable reference to the <code>I</code>-th element of <code>v</code>.</p>
</li>
<li>
<p><code>write_element_idx</code>: the expression <code>vec_traits&lt;Vector&gt;::write_element_idx(i,v)</code> returns mutable reference to the <code>i</code>-th element of <code>v</code>.</p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Alternatively, <code>write_element</code> and <code>write_element_idx</code> may be defined as <code>void</code> functions that take (as the last argument) a scalar to write into the specified element. QVM automatically detects this case by examining the signature of the <code>write_element</code> template.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>It is illegal to call any of the above functions unless <code>is_vec&lt;Vector&gt;::value</code> is true. Even then, vector types are allowed to define only a subset of the access functions. The general requirements are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>At least one of <code>read_element</code> or <code>write_element</code> must be defined;</p>
</li>
<li>
<p>If <code>read_element_idx</code> is defined, <code>read_element</code> must also be defined;</p>
</li>
<li>
<p>If <code>write_element_idx</code> is defined, <code>write_element</code> must also be defined.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Below is an example of a user-defined 3D vector type, and its corresponding specialization of the <code>vec_traits</code> template:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="cp">#include &lt;boost/qvm/vec_traits.hpp&gt;
</span>
<span class="k">struct</span> <span class="nc">float3</span> <span class="p">{</span> <span class="kt">float</span> <span class="n">a</span><span class="p">[</span><span class="mi">3</span><span class="p">];</span> <span class="p">};</span>

<span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">vec_traits</span><span class="o">&lt;</span><span class="n">float3</span><span class="o">&gt;</span> <span class="p">{</span>

    <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">dim</span><span class="o">=</span><span class="mi">3</span><span class="p">;</span>

    <span class="k">typedef</span> <span class="kt">float</span> <span class="n">scalar_type</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">float3</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="n">read_element</span><span class="p">(</span> <span class="n">float3</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">float3</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
    <span class="p">}</span> <span class="c1">//optional</span>

    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="n">read_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">float3</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
    <span class="p">}</span> <span class="c1">//optional</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Equivalently, using the <a href="#vec_traits_defaults"><code>vec_traits_defaults</code></a> template the above can be shortened to:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">vec_traits</span><span class="o">&lt;</span><span class="n">float3</span><span class="o">&gt;:</span> <span class="n">vec_traits_defaults</span><span class="o">&lt;</span><span class="n">float3</span><span class="p">,</span><span class="kt">float</span><span class="p">,</span><span class="mi">3</span><span class="o">&gt;</span>
  <span class="p">{</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">float3</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">float3</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">v</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
    <span class="p">}</span> <span class="c1">//optional</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_traits_defaults"><code>vec_traits_defaults</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_traits_defaults.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">VecType</span><span class="p">,</span><span class="k">class</span> <span class="nc">ScalarType</span><span class="p">,</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">vec_traits_defaults</span> <span class="p">{</span>

    <span class="k">typedef</span> <span class="n">VecType</span> <span class="n">vec_type</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">ScalarType</span> <span class="n">scalar_type</span><span class="p">;</span>
    <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">dim</span><span class="o">=</span><span class="n">Dim</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="n">BOOST_QVM_INLINE_CRITICAL</span>
    <span class="n">scalar_type</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">vec_type</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">vec_type</span><span class="o">&gt;::</span><span class="k">template</span> <span class="n">write_element</span><span class="o">&lt;</span><span class="n">I</span><span class="p">&gt;(</span><span class="k">const_cast</span><span class="o">&lt;</span><span class="n">vec_type</span> <span class="o">&amp;&gt;</span><span class="p">(</span><span class="n">x</span><span class="p">));</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="n">BOOST_QVM_INLINE_CRITICAL</span>
    <span class="n">scalar_type</span> <span class="n">read_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">vec_type</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">vec_type</span><span class="o">&gt;::</span><span class="n">write_element_idx</span><span class="p">(</span><span class="n">i</span><span class="p">,</span><span class="k">const_cast</span><span class="o">&lt;</span><span class="n">vec_type</span> <span class="o">&amp;&gt;</span><span class="p">(</span><span class="n">x</span><span class="p">));</span>
    <span class="p">}</span>

    <span class="nl">protected:</span>

    <span class="k">static</span> <span class="n">BOOST_QVM_INLINE_TRIVIAL</span>
    <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">vec_type</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">)</span> <span class="p">{</span>
      <span class="cm">/* unspecified */</span>
    <span class="p">}</span>
  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The <code>vec_traits_defaults</code> template is designed to be used as a public base for user-defined specializations of the <a href="#vec_traits"><code>vec_traits</code></a> template, to easily define the required members. If it is used, the only member that must be defined by the user in a <code>vec_traits</code> specialization is <code>write_element</code>; the <code>vec_traits_defaults</code> base will define <code>read_element</code>, as well as <code>scalar_type</code> and <code>dim</code> automatically.</p>
</div>
<div class="paragraph">
<p>Optionally, the user may also define <code>write_element_idx</code>, in which case the <code>vec_traits_defaults</code> base will provide a suitable <code>read_element_idx</code> definition automatically. If not, <code>vec_traits_defaults</code> defines a protected implementation of <code>write_element_idx</code> which may be made publicly available by the deriving <code>vec_traits</code> specialization in case the vector type for which it is being specialized can not be indexed efficiently. This <code>write_element_idx</code> function is less efficient (using meta-programming), implemented in terms of the required user-defined <code>write_element</code>.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="deduce_vec"><code>deduce_vec</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/deduce_vec.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">,</span>
    <span class="kt">int</span> <span class="n">D</span><span class="o">=</span><span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">Vector</span><span class="p">&gt;</span><span class="o">::</span><span class="n">dim</span><span class="p">,</span>
    <span class="k">class</span> <span class="nc">S</span><span class="o">=</span><span class="k">typename</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;::</span><span class="n">scalar_type</span><span class="o">&gt;</span>
  <span class="k">struct</span> <span class="nc">deduce_vec</span> <span class="p">{</span>

    <span class="k">typedef</span> <span class="cm">/*unspecified*/</span> <span class="n">type</span><span class="p">;</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p><code><a href="#is_vec">is_vec</a>&lt;V&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code><a href="#is_scalar">is_scalar</a>&lt;S&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code>is_vec&lt;deduce_vec&lt;V,D,S&gt;::type&gt;::value</code> must be <code>true</code>;</p>
</li>
<li>
<p><code>deduce_vec&lt;V,D,S&gt;::type</code> must be copyable;</p>
</li>
<li>
<p><code>vec_traits&lt;deduce_vec&lt;V,D,S&gt;::type&gt;::dim==D</code>;</p>
</li>
<li>
<p><code>vec_traits&lt;deduce_vec&lt;V,D,S&gt;::type&gt;::scalar_type</code> is the same type as <code>S</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>This template is used by QVM whenever it needs to deduce a copyable vector type of certain dimension from a single user-supplied function parameter of vector type. The returned type must have accessible copy constructor. Note that <code>V</code> may be non-copyable.</p>
</div>
<div class="paragraph">
<p>The main template definition returns an unspecified copyable vector type of size <code>D</code> and scalar type <code>S</code>, except if <code><a href="#vec_traits">vec_traits</a>&lt;V&gt;::dim==D</code> and <code><a href="#vec_traits">vec_traits</a>&lt;V&gt;::scalar_type</code> is the same type as <code>S</code>, in which case it returns <code>V</code>, which is suitable only if <code>V</code> is a copyable type. QVM also defines (partial) specializations for the non-copyable vector types it produces. Users can define other (partial) specializations for their own types.</p>
</div>
<div class="paragraph">
<p>A typical use of the <code>deduce_vec</code> template is for specifying the preferred vector type to be returned by the generic function template overloads in QVM depending on the type of their arguments.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="deduce_vec2"><code>deduce_vec2</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/deduce_vec.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span><span class="kt">int</span> <span class="n">D</span><span class="p">,</span>
    <span class="k">class</span> <span class="nc">S</span><span class="o">=</span><span class="k">typename</span> <span class="nc">deduce_scalar</span><span class="o">&lt;</span>
      <span class="k">typename</span> <span class="nc">scalar</span><span class="o">&lt;</span><span class="n">A</span><span class="p">&gt;</span><span class="o">::</span><span class="n">type</span><span class="p">,</span>
      <span class="k">typename</span> <span class="n">scalar</span><span class="o">&lt;</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span><span class="o">&gt;::</span><span class="n">type</span><span class="o">&gt;</span>
  <span class="k">struct</span> <span class="nc">deduce_vec2</span> <span class="p">{</span>
    <span class="k">typedef</span> <span class="cm">/*unspecified*/</span> <span class="n">type</span><span class="p">;</span>
  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Both <code><a href="#scalar">scalar</a>&lt;A&gt;::type</code> and <code>scalar&lt;B&gt;::type</code> are well defined;</p>
</li>
<li>
<p><code><a href="#is_vec">is_vec</a>&lt;A&gt;::value || is_vec&lt;B&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code><a href="#is_scalar">is_scalar</a>&lt;S&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code>is_vec&lt;deduce_vec2&lt;A,B,D,S&gt;::type&gt;::value</code> must be <code>true</code>;</p>
</li>
<li>
<p><code>deduce_vec2&lt;A,B,D,S&gt;::type</code> must be copyable;</p>
</li>
<li>
<p><code>vec_traits&lt;deduce_vec2&lt;A,B,D,S&gt;::type&gt;::dim==D</code>.</p>
</li>
<li>
<p><code>vec_traits&lt;deduce_vec2&lt;A,B,D,S&gt;::type&gt;::scalar_type</code> is the same type as <code>S</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>This template is used by QVM whenever it needs to deduce a vector type of certain dimension from the types of two user-supplied function parameters. The returned type must have accessible copy constructor (the <code>A</code> and <code>B</code> types themselves could be non-copyable, and either one of them may be a non-vector type.)</p>
</div>
<div class="paragraph">
<p>The main template definition returns an unspecified vector type of the requested dimension with <a href="#vec_traits"><code>scalar_type</code></a> <code>S</code>, except if <code>A</code> and <code>B</code> are the same vector type <code>V</code>, in which case <code>V</code> is returned, which is only suitable for copyable types. QVM also defines (partial) specializations for the non-copyable vector types it produces. Users can define other (partial) specializations for their own types.</p>
</div>
<div class="paragraph">
<p>A typical use of the <code>deduce_vec2</code> template is for specifying the preferred vector type to be returned by the generic function template overloads in QVM depending on the type of their arguments.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="is_mat"><code>is_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_traits.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">is_mat</span> <span class="p">{</span>

    <span class="k">static</span> <span class="kt">bool</span> <span class="k">const</span> <span class="n">value</span> <span class="o">=</span> <span class="nb">false</span><span class="p">;</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This type template defines a compile-time boolean constant value which can be used to determine whether a type <code>T</code> is a matrix type. For matrix types, the <a href="#mat_traits"><code>mat_traits</code></a> template can be used to access their elements generically, or to obtain their dimensions and scalar type.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_traits"><code>mat_traits</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_traits.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">M</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">mat_traits</span> <span class="p">{</span>

    <span class="cm">/*main template members unspecified*/</span>

  <span class="p">};</span>

  <span class="cm">/*
  User-defined (possibly partial) specializations:

  template &lt;&gt;
  struct mat_traits&lt;M&gt; {

    static int const rows = &lt;&lt;user-defined&gt;&gt;;
    static int const cols = &lt;&lt;user-defined&gt;&gt;;
    typedef &lt;&lt;user-defined&gt;&gt; scalar_type;

    template &lt;int R,int C&gt;
    static inline scalar_type read_element( Matrix const &amp; m );

    static inline scalar_typeread_element_idx( int r, int c, Matrix const &amp; m );

    template &lt;int R,int C&gt;
    static inline scalar_type &amp; write_element( Matrix &amp; m );

    static inline scalar_type &amp; write_element_idx( int r, int c, Matrix &amp; m );

    // Alternative signature for write_element and write_element_idx:

    template &lt;int R,int C&gt;
    static inline void write_element( Matrix &amp; m, scalar_type s );

    static inline void write_element_idx( int r, int c, Matrix &amp; m, scalar_type s );

  };
  */</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The <code>mat_traits</code> template must be specialized for (user-defined) matrix types in order to enable vector and matrix operations defined in QVM headers for objects of those types.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The matrix operations defined by QVM do not require matrix types to be copyable.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>The main <code>mat_traits</code> template members are not specified. Valid specializations are required to define the following members:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>rows</code>: the expression <code>mat_traits&lt;Matrix&gt;::rows</code> must evaluate to a compile-time integer constant greater than 0 that specifies the number of rows in a matrix.</p>
</li>
<li>
<p><code>cols</code> must evaluate to a compile-time integer constant greater than 0 that specifies the number of columns in a matrix.</p>
</li>
<li>
<p><code>scalar_type</code>: the expression <code>mat_traits&lt;Matrix&gt;::scalar_type</code> must be a value type which satisfies the scalar requirements.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>In addition, valid specializations of the <code>mat_traits</code> template may define the following access functions as static members, where <code>m</code> is an object of type <code>Matrix</code>, <code>R</code> and <code>C</code> are compile-time integer constants, and <code>r</code> and <code>c</code> are variables of type <code>int</code>:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>read_element</code>: the expression <code>mat_traits&lt;Matrix&gt;::read_element&lt;R,C&gt;(m)</code> returns either a copy of or a const reference to the element at row <code>R</code> and column <code>C</code> of <code>m</code>.</p>
</li>
<li>
<p><code>read_element_idx</code>: the expression <code>mat_traits&lt;Matrix&gt;::read_element_idx(r,c,m)</code> returns either a copy of or a const reference to the element at row <code>r</code> and column <code>c</code> of <code>m</code>.</p>
</li>
<li>
<p><code>write_element</code>: the expression <code>mat_traits&lt;Matrix&gt;::write_element&lt;R,C&gt;(m)</code> returns mutable reference to the element at row <code>R</code> and column <code>C</code> of <code>m</code>.</p>
</li>
<li>
<p><code>write_element_idx</code>: the expression <code>mat_traits&lt;Matrix&gt;::write_element_idx(r,c,m)</code> returns mutable reference to the element at row <code>r</code> and column <code>c</code> of <code>m</code>.</p>
</li>
</ul>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
Alternatively, <code>write_element</code> and <code>write_element_idx</code> may be defined as <code>void</code> functions that take (as the last argument) a scalar to write into the specified element. QVM automatically detects this case by examining the signature of the <code>write_element</code> template.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>It is illegal to call any of the above functions unless <code>is_mat&lt;Matrix&gt;::value</code> is true. Even then, matrix types are allowed to define only a subset of the access functions. The general requirements are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>At least one of <code>read_element</code> or <code>write_element</code> must be defined;</p>
</li>
<li>
<p>If <code>read_element_idx</code> is defined, <code>read_element</code> must also be defined;</p>
</li>
<li>
<p>If <code>write_element_idx</code> is defined, <code>write_element</code> must also be defined.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Below is an example of a user-defined 3x3 matrix type, and its corresponding specialization of the <code>mat_traits</code> template:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="cp">#include &lt;boost/qvm/mat_traits.hpp&gt;
</span>
<span class="k">struct</span> <span class="nc">float33</span> <span class="p">{</span> <span class="kt">float</span> <span class="n">a</span><span class="p">[</span><span class="mi">3</span><span class="p">][</span><span class="mi">3</span><span class="p">];</span> <span class="p">};</span>

<span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">mat_traits</span><span class="o">&lt;</span><span class="n">float33</span><span class="o">&gt;</span> <span class="p">{</span>

    <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">rows</span><span class="o">=</span><span class="mi">3</span><span class="p">;</span>
    <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">cols</span><span class="o">=</span><span class="mi">3</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="kt">float</span> <span class="n">scalar_type</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">R</span><span class="p">,</span><span class="kt">int</span> <span class="n">C</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">float33</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">m</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">R</span><span class="p">][</span><span class="n">C</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">R</span><span class="p">,</span><span class="kt">int</span> <span class="n">C</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="n">read_element</span><span class="p">(</span> <span class="n">float33</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">m</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">R</span><span class="p">][</span><span class="n">C</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">r</span><span class="p">,</span> <span class="kt">int</span> <span class="n">c</span><span class="p">,</span> <span class="n">float33</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">m</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">r</span><span class="p">][</span><span class="n">c</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="n">read_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">r</span><span class="p">,</span> <span class="kt">int</span> <span class="n">c</span><span class="p">,</span> <span class="n">float33</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">m</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">r</span><span class="p">][</span><span class="n">c</span><span class="p">];</span>
    <span class="p">}</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Equivalently, we could use the &lt;&lt;mat_traits_defaults,<code>mat_traits_defaults</code> template to shorten the above to:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">mat_traits</span><span class="o">&lt;</span><span class="n">float33</span><span class="o">&gt;:</span> <span class="n">mat_traits_defaults</span><span class="o">&lt;</span><span class="n">float33</span><span class="p">,</span><span class="kt">float</span><span class="p">,</span><span class="mi">3</span><span class="p">,</span><span class="mi">3</span><span class="o">&gt;</span> <span class="p">{</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">R</span><span class="p">,</span><span class="kt">int</span> <span class="n">C</span><span class="p">&gt;</span> <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">float33</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">)</span> <span class="p">{</span> <span class="k">return</span> <span class="n">m</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">R</span><span class="p">][</span><span class="n">C</span><span class="p">];</span> <span class="p">}</span>

    <span class="k">static</span> <span class="kr">inline</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">r</span><span class="p">,</span> <span class="kt">int</span> <span class="n">c</span><span class="p">,</span> <span class="n">float33</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">m</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">r</span><span class="p">][</span><span class="n">c</span><span class="p">];</span>
    <span class="p">}</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_traits_defaults"><code>mat_traits_defaults</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_traits_defaults.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">MatType</span><span class="p">,</span><span class="k">class</span> <span class="nc">ScalarType</span><span class="p">,</span><span class="kt">int</span> <span class="n">Rows</span><span class="p">,</span><span class="kt">int</span> <span class="n">Cols</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">mat_traits_defaults</span>
  <span class="p">{</span>
    <span class="k">typedef</span> <span class="n">MatType</span> <span class="n">mat_type</span><span class="p">;</span>
    <span class="k">typedef</span> <span class="n">ScalarType</span> <span class="n">scalar_type</span><span class="p">;</span>
    <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">rows</span><span class="o">=</span><span class="n">Rows</span><span class="p">;</span>
    <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">cols</span><span class="o">=</span><span class="n">Cols</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Row</span><span class="p">,</span><span class="kt">int</span> <span class="n">Col</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="n">BOOST_QVM_INLINE_CRITICAL</span>
    <span class="n">scalar_type</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">mat_type</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">mat_type</span><span class="o">&gt;::</span><span class="k">template</span> <span class="n">write_element</span><span class="o">&lt;</span><span class="n">Row</span><span class="p">,</span><span class="n">Col</span><span class="p">&gt;(</span><span class="k">const_cast</span><span class="o">&lt;</span><span class="n">mat_type</span> <span class="o">&amp;&gt;</span><span class="p">(</span><span class="n">x</span><span class="p">));</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="n">BOOST_QVM_INLINE_CRITICAL</span>
    <span class="n">scalar_type</span> <span class="n">read_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">r</span><span class="p">,</span> <span class="kt">int</span> <span class="n">c</span><span class="p">,</span> <span class="n">mat_type</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">mat_type</span><span class="o">&gt;::</span><span class="n">write_element_idx</span><span class="p">(</span><span class="n">r</span><span class="p">,</span><span class="n">c</span><span class="p">,</span><span class="k">const_cast</span><span class="o">&lt;</span><span class="n">mat_type</span> <span class="o">&amp;&gt;</span><span class="p">(</span><span class="n">x</span><span class="p">));</span>
    <span class="p">}</span>

    <span class="nl">protected:</span>

    <span class="k">static</span> <span class="n">BOOST_QVM_INLINE_TRIVIAL</span>
    <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">r</span><span class="p">,</span> <span class="kt">int</span> <span class="n">c</span><span class="p">,</span> <span class="n">mat_type</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">)</span> <span class="p">{</span>
      <span class="cm">/* unspecified */</span>
    <span class="p">}</span>
  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The <code>mat_traits_defaults</code> template is designed to be used as a public base for user-defined specializations of the <a href="#mat_traits"><code>mat_traits</code></a> template, to easily define the required members. If it is used, the only member that must be defined by the user in a <code>mat_traits</code> specialization is <code>write_element</code>; the <code>mat_traits_defaults</code> base will define <code>read_element</code>, as well as <code>scalar_type</code>, <code>rows</code> and <code>cols</code> automatically.</p>
</div>
<div class="paragraph">
<p>Optionally, the user may also define <code>write_element_idx</code>, in which case the <code>mat_traits_defaults</code> base will provide a suitable <code>read_element_idx</code> definition automatically. Otherwise, <code>mat_traits_defaults</code> defines a protected implementation of <code>write_element_idx</code> which may be made publicly available by the deriving <code>mat_traits</code> specialization in case the matrix type for which it is being specialized can not be indexed efficiently. This <code>write_element_idx</code> function is less efficient (using meta-programming), implemented in terms of the required user-defined <code>write_element</code>.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="deduce_mat"><code>deduce_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/deduce_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span>
    <span class="k">class</span> <span class="nc">M</span><span class="p">,</span>
    <span class="kt">int</span> <span class="n">R</span><span class="o">=</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">Matrix</span><span class="p">&gt;</span><span class="o">::</span><span class="n">rows</span><span class="p">,</span>
    <span class="kt">int</span> <span class="n">C</span><span class="o">=</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">Matrix</span><span class="o">&gt;::</span><span class="n">cols</span><span class="p">,</span>
    <span class="k">class</span> <span class="nc">S</span><span class="o">=</span><span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">M</span><span class="o">&gt;::</span><span class="n">scalar_type</span><span class="o">&gt;</span>
  <span class="k">struct</span> <span class="nc">deduce_mat</span> <span class="p">{</span>

    <span class="k">typedef</span> <span class="cm">/*unspecified*/</span> <span class="n">type</span><span class="p">;</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p><code><a href="#is_mat">is_mat</a>&lt;M,R,C,S&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code>is_mat&lt;deduce_mat&lt;M,R,C,S&gt;::type&gt;::value</code> must be <code>true</code>;</p>
</li>
<li>
<p><code>deduce_mat&lt;M,R,C,S&gt;::type</code> must be copyable;</p>
</li>
<li>
<p><code><a href="#mat_traits">mat_traits</a>&lt;deduce_mat&lt;M,R,C,S&gt;::type&gt;::rows==R</code>;</p>
</li>
<li>
<p><code>mat_traits&lt;deduce_mat&lt;M,R,C,S&gt;::type&gt;::cols==C</code>,</p>
</li>
<li>
<p><code>mat_traits&lt;deduce_mat&lt;M,R,C,S&gt;::type::scalar_type</code> is the same type as <code>S</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>This template is used by QVM whenever it needs to deduce a copyable matrix type of certain dimensions from a single user-supplied function parameter of matrix type. The returned type must have accessible copy constructor. Note that M itself may be non-copyable.</p>
</div>
<div class="paragraph">
<p>The main template definition returns an unspecified copyable matrix type of size <code>R</code> x <code>C</code> and scalar type <code>S</code>, except if <code><a href="#mat_traits">mat_traits</a>&lt;M&gt;::rows==R</code> and <code>mat_traits&lt;M&gt;::cols==Cols</code> and <code>mat_traits&lt;M&gt;::scalar_type</code> is S, in which case it returns <code>M</code>, which is suitable only if <code>M</code> is a copyable type. QVM also defines (partial) specializations for the non-copyable matrix types it produces. Users can define other (partial) specializations for their own types.</p>
</div>
<div class="paragraph">
<p>A typical use of the deduce_mat template is for specifying the preferred matrix type to be returned by the generic function template overloads in QVM depending on the type of their arguments.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="deduce_mat2"><code>deduce_mat2</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/deduce_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span><span class="kt">int</span> <span class="n">R</span><span class="p">,</span><span class="kt">int</span> <span class="n">C</span><span class="p">,</span>
    <span class="k">class</span> <span class="nc">S</span> <span class="o">=</span> <span class="k">typename</span> <span class="nc">deduce_scalar</span><span class="o">&lt;</span>
      <span class="k">typename</span> <span class="nc">scalar</span><span class="o">&lt;</span><span class="n">A</span><span class="p">&gt;</span><span class="o">::</span><span class="n">type</span><span class="p">,</span>
      <span class="k">typename</span> <span class="n">scalar</span><span class="o">&lt;</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">struct</span> <span class="nc">deduce_mat2</span> <span class="p">{</span>

    <span class="k">typedef</span> <span class="cm">/*unspecified*/</span> <span class="n">type</span><span class="p">;</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Both <code><a href="#scalar">scalar</a>&lt;A&gt;::type</code> and <code>scalar&lt;B&gt;::type</code> are well defined;</p>
</li>
<li>
<p><code><a href="#is_mat">is_mat</a>&lt;A&gt;::value || is_mat&lt;B&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code><a href="#is_scalar">is_scalar</a>&lt;S&gt;::value</code> is <code>true</code>;</p>
</li>
<li>
<p><code>is_mat&lt;deduce_mat2&lt;A,B&gt;::type&gt;::value</code> must be <code>true</code>;</p>
</li>
<li>
<p><code>deduce_mat2&lt;A,B,R,C,S&gt;::type</code> must be copyable;</p>
</li>
<li>
<p><code><a href="#mat_traits">mat_traits</a>&lt;deduce_mat2&lt;A,B,R,C,S&gt;::type&gt;::rows==R</code>;</p>
</li>
<li>
<p><code>mat_traits&lt;deduce_mat2&lt;A,B,R,C,S&gt;::type&gt;::cols==C</code>;</p>
</li>
<li>
<p><code>mat_traits&lt;deduce_mat2&lt;A,B,R,C,S&gt;::type&gt;::scalar_type</code> is the same type as <code>S</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>This template is used by QVM whenever it needs to deduce a matrix type of certain dimensions from the types of two user-supplied function parameters. The returned type must have accessible copy constructor (the <code>A</code> and <code>B</code> types themselves could be non-copyable, and either one of them may be a non-matrix type.)</p>
</div>
<div class="paragraph">
<p>The main template definition returns an unspecified matrix type of the requested dimensions with <a href="#mat_traits"><code>scalar_type</code></a> <code>S</code>, except if <code>A</code> and <code>B</code> are the same matrix type <code>M</code>, in which case <code>M</code> is returned, which is only suitable for copyable types. QVM also defines (partial) specializations for the non-copyable matrix types it produces. Users can define other (partial) specializations for their own types.</p>
</div>
<div class="paragraph">
<p>A typical use of the <code>deduce_mat2</code> template is for specifying the preferred matrix type to be returned by the generic function template overloads in QVM depending on the type of their arguments.</p>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_built_in_quaternion_vector_and_matrix_types">Built-in Quaternion, Vector and Matrix Types</h3>
<div class="paragraph">
<p>QVM defines several class templates (together with appropriate specializations of <a href="#quat_traits"><code>quat_traits</code></a>, <a href="#vec_traits"><code>vec_traits</code></a> and <a href="#mat_traits"><code>mat_traits</code></a> templates) which can be used as generic quaternion, vector and matrix types. Using these types directly wouldn&#8217;t be typical though, the main design goal of QVM is to allow users to plug in their own quaternion, vector and matrix types.</p>
</div>
<div class="sect3">
<h4 id="quat"><code>quat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
    <span class="k">struct</span> <span class="nc">quat</span> <span class="p">{</span>

      <span class="n">T</span> <span class="n">a</span><span class="p">[</span><span class="mi">4</span><span class="p">];</span>

      <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span><span class="p">&gt;</span>
      <span class="k">operator</span> <span class="n">R</span><span class="p">()</span> <span class="k">const</span> <span class="p">{</span>
        <span class="n">R</span> <span class="n">r</span><span class="p">;</span>
        <span class="n">assign</span><span class="p">(</span><span class="n">r</span><span class="p">,</span><span class="o">*</span><span class="k">this</span><span class="p">);</span>
        <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
      <span class="p">}</span>

    <span class="p">};</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Quaternion</span><span class="p">&gt;</span>
    <span class="k">struct</span> <span class="nc">quat_traits</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
    <span class="k">struct</span> <span class="nc">quat_traits</span><span class="o">&lt;</span> <span class="n">quat</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span> <span class="o">&gt;</span> <span class="p">{</span>

      <span class="k">typedef</span> <span class="n">T</span> <span class="n">scalar_type</span><span class="p">;</span>

      <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
      <span class="k">static</span> <span class="n">scalar_type</span> <span class="n">read_element</span><span class="p">(</span> <span class="n">quat</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
        <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
      <span class="p">}</span>

      <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
      <span class="k">static</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">quat</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
        <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
      <span class="p">}</span>

    <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This is a simple quaternion type. It converts to any other quaternion type.</p>
</div>
<div class="paragraph">
<p>The partial specialization of the <a href="#quat_traits"><code>quat_traits</code></a> template makes the <code>quat</code> template compatible with the generic operations defined by QVM.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec"><code>vec</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">&gt;</span>
    <span class="k">struct</span> <span class="nc">vec</span> <span class="p">{</span>

      <span class="n">T</span> <span class="n">a</span><span class="p">[</span><span class="n">Dim</span><span class="p">];</span>

      <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span><span class="p">&gt;</span>
      <span class="k">operator</span> <span class="n">R</span><span class="p">()</span> <span class="k">const</span> <span class="p">{</span>
        <span class="n">R</span> <span class="n">r</span><span class="p">;</span>
        <span class="n">assign</span><span class="p">(</span><span class="n">r</span><span class="p">,</span><span class="o">*</span><span class="k">this</span><span class="p">);</span>
        <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
      <span class="p">}</span>

    <span class="p">};</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Vector</span><span class="p">&gt;</span>
    <span class="k">struct</span> <span class="nc">vec_traits</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">&gt;</span>
    <span class="k">struct</span> <span class="nc">vec_traits</span><span class="o">&lt;</span> <span class="n">vec</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Dim</span><span class="o">&gt;</span> <span class="o">&gt;</span> <span class="p">{</span>

      <span class="k">typedef</span> <span class="n">T</span> <span class="n">scalar_type</span><span class="p">;</span>
      <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">dim</span><span class="o">=</span><span class="n">Dim</span><span class="p">;</span>

      <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
      <span class="k">static</span> <span class="n">scalar_type</span> <span class="n">read_element</span><span class="p">(</span> <span class="n">vec</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Dim</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
        <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
      <span class="p">}</span>

      <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">&gt;</span>
      <span class="k">static</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">vec</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Dim</span><span class="o">&gt;</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
        <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">I</span><span class="p">];</span>
      <span class="p">}</span>

      <span class="k">static</span> <span class="n">scalar_type</span> <span class="n">read_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">vec</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Dim</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
        <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
      <span class="p">}</span>

      <span class="k">static</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">i</span><span class="p">,</span> <span class="n">vec</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Dim</span><span class="o">&gt;</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
        <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
      <span class="p">}</span>
    <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This is a simple vector type. It converts to any other vector type of compatible size.</p>
</div>
<div class="paragraph">
<p>The partial specialization of the <a href="#vec_traits"><code>vec_traits</code></a> template makes the <code>vec</code> template compatible with the generic operations defined by QVM.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat"><code>mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span><span class="kt">int</span> <span class="n">Rows</span><span class="p">,</span><span class="kt">int</span> <span class="n">Cols</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">mat</span> <span class="p">{</span>

    <span class="n">T</span> <span class="n">a</span><span class="p">[</span><span class="n">Rows</span><span class="p">][</span><span class="n">Cols</span><span class="p">];</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span><span class="p">&gt;</span>
    <span class="k">operator</span> <span class="n">R</span><span class="p">()</span> <span class="k">const</span> <span class="p">{</span>
      <span class="n">R</span> <span class="n">r</span><span class="p">;</span>
      <span class="n">assign</span><span class="p">(</span><span class="n">r</span><span class="p">,</span><span class="o">*</span><span class="k">this</span><span class="p">);</span>
      <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
    <span class="p">}</span>

  <span class="p">};</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Matrix</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">mat_traits</span><span class="p">;</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span><span class="kt">int</span> <span class="n">Rows</span><span class="p">,</span><span class="kt">int</span> <span class="n">Cols</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">mat_traits</span><span class="o">&lt;</span> <span class="n">mat</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Rows</span><span class="p">,</span><span class="n">Cols</span><span class="o">&gt;</span> <span class="o">&gt;</span> <span class="p">{</span>

    <span class="k">typedef</span> <span class="n">T</span> <span class="n">scalar_type</span><span class="p">;</span>
    <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">rows</span><span class="o">=</span><span class="n">Rows</span><span class="p">;</span>
    <span class="k">static</span> <span class="kt">int</span> <span class="k">const</span> <span class="n">cols</span><span class="o">=</span><span class="n">Cols</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Row</span><span class="p">,</span><span class="kt">int</span> <span class="n">Col</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="n">scalar_type</span> <span class="n">read_element</span><span class="p">(</span> <span class="n">mat</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Rows</span><span class="p">,</span><span class="n">Cols</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">Row</span><span class="p">][</span><span class="n">Col</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Row</span><span class="p">,</span><span class="kt">int</span> <span class="n">Col</span><span class="p">&gt;</span>
    <span class="k">static</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element</span><span class="p">(</span> <span class="n">mat</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Rows</span><span class="p">,</span><span class="n">Cols</span><span class="o">&gt;</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">Row</span><span class="p">][</span><span class="n">Col</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="n">scalar_type</span> <span class="n">read_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">row</span><span class="p">,</span> <span class="kt">int</span> <span class="n">col</span><span class="p">,</span> <span class="n">mat</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Rows</span><span class="p">,</span><span class="n">Cols</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">row</span><span class="p">][</span><span class="n">col</span><span class="p">];</span>
    <span class="p">}</span>

    <span class="k">static</span> <span class="n">scalar_type</span> <span class="o">&amp;</span> <span class="n">write_element_idx</span><span class="p">(</span> <span class="kt">int</span> <span class="n">row</span><span class="p">,</span> <span class="kt">int</span> <span class="n">col</span><span class="p">,</span> <span class="n">mat</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span><span class="n">Rows</span><span class="p">,</span><span class="n">Cols</span><span class="o">&gt;</span> <span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span> <span class="n">x</span><span class="p">.</span><span class="n">a</span><span class="p">[</span><span class="n">row</span><span class="p">][</span><span class="n">col</span><span class="p">];</span>
    <span class="p">}</span>

  <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This is a simple matrix type. It converts to any other matrix type of compatible size.</p>
</div>
<div class="paragraph">
<p>The partial specialization of the <a href="#mat_traits"><code>mat_traits</code></a> template makes the <code>mat</code> template compatible with the generic operations defined by QVM.</p>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_element_access">Element Access</h3>
<div class="sect3">
<h4 id="quat_access">Quaternions</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_access.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;Q&gt;::value</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Q</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">S</span><span class="p">(</span> <span class="n">Q</span> <span class="o">&amp;</span> <span class="n">q</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Q</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">V</span><span class="p">(</span> <span class="n">Q</span> <span class="o">&amp;</span> <span class="n">q</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Q</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X</span><span class="p">(</span> <span class="n">Q</span> <span class="o">&amp;</span> <span class="n">q</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Q</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">Y</span><span class="p">(</span> <span class="n">Q</span> <span class="o">&amp;</span> <span class="n">q</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Q</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">Z</span><span class="p">(</span> <span class="n">Q</span> <span class="o">&amp;</span> <span class="n">q</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>An expression of the form <code>S(q)</code> can be used to access the scalar component of the quaternion <code>q</code>. For example,</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">S</span><span class="p">(</span><span class="n">q</span><span class="p">)</span> <span class="o">*=</span> <span class="mi">42</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>multiplies the scalar component of <code>q</code> by the scalar 42.</p>
</div>
<div class="paragraph">
<p>An expression of the form <code>V(q)</code> can be used to access the vector component of the quaternion <code>q</code>. For example,</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">V</span><span class="p">(</span><span class="n">q</span><span class="p">)</span> <span class="o">*=</span> <span class="mi">42</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>multiplies the vector component of <code>q</code> by the scalar 42.</p>
</div>
<div class="paragraph">
<p>The <code>X</code>, <code>Y</code> and <code>Z</code> elements of the vector component can also be accessed directly using <code>X(q)</code>, <code>Y(q)</code> and <code>Z(q)</code>.</p>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The return types are lvalues.
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="vec_access">Vectors</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_access.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_vec&lt;V&gt;::value</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">I</span><span class="p">,</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A0</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A9</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">Y</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">Z</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">W</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>An expression of the form of <code>A&lt;I&gt;(v)</code> can be used to access the <code>I</code>-th element a vector object <code>v</code>. For example, the expression:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">A</span><span class="o">&lt;</span><span class="mi">1</span><span class="o">&gt;</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">*=</span> <span class="mi">42</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>can be used to multiply the element at index 1 (indexing in QVM is always zero-based) of a vector <code>v</code> by 42.</p>
</div>
<div class="paragraph">
<p>For convenience, there are also non-template overloads for <code>I</code> from 0 to 9; an alternative way to write the above expression is:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">A1</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">*=</span> <span class="mi">42</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p><code>X</code>, <code>Y</code>, <code>Z</code> and <code>W</code> act the same as <code>A0</code>/<code>A1</code>/<code>A2</code>/<code>A3</code>; yet another alternative way to write the above expression is:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">Y</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">*=</span> <span class="mi">42</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The return types are lvalues.
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="swizzling">Vector Element Swizzling</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/swizzle.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//*** Accessing vector elements by swizzling ***</span>

  <span class="c1">//2D view proxies, only enabled if:</span>
  <span class="c1">//  is_vec&lt;V&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XX</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XY</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XZ</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XW</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X0</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">YX</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">YY</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">YZ</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">YW</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">Y0</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">Y1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">ZX</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">ZY</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">ZZ</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">ZW</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">Z0</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">Z1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">WX</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">WY</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">WZ</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">WW</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">W0</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">W1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="c1">//2D view proxies, only enabled if:</span>
  <span class="c1">//  is_scalar&lt;S&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X0</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X1</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XX</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_00</span><span class="p">();</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_01</span><span class="p">();</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_10</span><span class="p">();</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">2</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_11</span><span class="p">();</span>

  <span class="c1">//3D view proxies, only enabled if:</span>
  <span class="c1">//  is_vec&lt;V&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXX</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXW</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XX0</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XX1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XYX</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XY1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">WW1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="c1">//3D view proxies, only enabled if:</span>
  <span class="c1">//  is_scalar&lt;S&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X00</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X01</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXX</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XX0</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_000</span><span class="p">();</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_001</span><span class="p">();</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_010</span><span class="p">();</span>
  <span class="p">...</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">3</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_111</span><span class="p">();</span>

  <span class="c1">//4D view proxies, only enabled if:</span>
  <span class="c1">//  is_vec&lt;V&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXXX</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXXW</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXX0</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXX1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXYX</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXY1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">WWW1</span><span class="p">(</span> <span class="n">V</span> <span class="o">&amp;</span> <span class="n">v</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="c1">//4D view proxies, only enabled if:</span>
  <span class="c1">//  is_scalar&lt;S&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X000</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">X001</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XXXX</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">XX00</span><span class="p">(</span> <span class="n">S</span> <span class="o">&amp;</span> <span class="n">s</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_0000</span><span class="p">();</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_0001</span><span class="p">();</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_0010</span><span class="p">();</span>
  <span class="p">...</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="mi">4</span><span class="n">D</span><span class="o">-</span><span class="n">vector</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">_1111</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Swizzling allows zero-overhead direct access to a (possibly rearranged) subset of the elements of 2D, 3D and 4D vectors. For example, if <code>v</code> is a 4D vector, the expression <code>YX(v) is a 2D view proxy whose `X</code> element refers to the <code>Y</code> element of <code>v</code>, and whose <code>Y</code> element refers to the <code>X</code> element of <code>v</code>. Like other view proxies <code>YX</code> is an lvalue, that is, if <code>v2</code> is a 2D vector, one could write:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">YX</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">=</span> <span class="n">v2</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The above will leave the <code>Z</code> and <code>W</code> elements of <code>v</code> unchanged but assign the <code>Y</code> element of <code>v2</code> to the <code>X</code> element of <code>v</code> and the <code>X</code> element of <code>v2</code> to the <code>Y</code> element of <code>v</code>.</p>
</div>
<div class="paragraph">
<p>All permutations of <code>X</code>, <code>Y</code>, <code>Z</code>, <code>W</code>, <code>0</code>, <code>1</code> for 2D, 3D and 4D swizzling are available (if the first character of the swizzle identifier is <code>0</code> or <code>1</code>, it is preceded by a <code>_</code>, for example <code>_11XY</code>).</p>
</div>
<div class="paragraph">
<p>It is valid to use the same vector element more than once: the expression <code>ZZZ(v)</code> is a 3D vector whose <code>X</code>, <code>Y</code> and <code>Z</code> elements all refer to the <code>Z</code> element of <code>v</code>.</p>
</div>
<div class="paragraph">
<p>Finally, scalars can be "swizzled" to access them as vectors: the expression <code>_0X01(42.0f)</code> is a 4D vector with <code>X</code>=0, <code>Y</code>=42.0, <code>Z</code>=0, <code>W</code>=1.</p>
</div>
</div>
<div class="sect3">
<h4 id="mat_access">Matrices</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_access.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;Q&gt;::value</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">R</span><span class="p">,</span><span class="kt">int</span> <span class="n">C</span><span class="p">,</span><span class="k">class</span> <span class="nc">M</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A</span><span class="p">(</span> <span class="n">M</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">M</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A00</span><span class="p">(</span> <span class="n">M</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">M</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A01</span><span class="p">(</span> <span class="n">M</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">M</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A09</span><span class="p">(</span> <span class="n">M</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">);</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">M</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A10</span><span class="p">(</span> <span class="n">M</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">);</span>
  <span class="p">...</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">M</span><span class="p">&gt;</span> <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">A99</span><span class="p">(</span> <span class="n">M</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>An expression of the form <code>A&lt;R,C&gt;(m)</code> can be used to access the element at row <code>R</code> and column <code>C</code> of a matrix object <code>m</code>. For example, the expression:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">A</span><span class="o">&lt;</span><span class="mi">4</span><span class="p">,</span><span class="mi">2</span><span class="o">&gt;</span><span class="p">(</span><span class="n">m</span><span class="p">)</span> <span class="o">*=</span> <span class="mi">42</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>can be used to multiply the element at row 4 and column 2 of a matrix <code>m</code> by 42.</p>
</div>
<div class="paragraph">
<p>For convenience, there are also non-template overloads for <code>R</code> from <code>0</code> to <code>9</code> and <code>C</code> from <code>0</code> to <code>9</code>; an alternative way to write the above expression is:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">A42</span><span class="p">(</span><span class="n">m</span><span class="p">)</span> <span class="o">*=</span> <span class="mi">42</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
The return types are lvalues.
</td>
</tr>
</table>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_quaternion_operations">Quaternion Operations</h3>
<div class="sect3">
<h4 id="quat_assign"><code>assign</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="n">assign</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Copies all elements of the quaternion <code>b</code> to the quaternion <code>a</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_convert_to"><code>convert_to</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;R&gt;::value &amp;&amp; is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="n">R</span> <span class="n">convert_to</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;R&gt;::value &amp;&amp; is_mat&lt;A&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==3 &amp;&amp; mat_traits&lt;A&gt;::cols==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="n">R</span> <span class="n">convert_to</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">m</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>R</code> must be copyable.</p>
</dd>
<dt class="hdlist1">Effects: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>The first overload is equivalent to: <code>R r; assign(r,a); return r;</code></p>
</li>
<li>
<p>The second overload assumes that <code>m</code> is an orthonormal rotation matrix and converts it to a quaternion that performs the same rotation.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_minus_eq"><code>operator-=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">-=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Subtracts the elements of <code>b</code> from the corresponding elements of <code>a</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_minus_unary"><code>operator-</code> (unary)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

   <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">-</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A quaternion of the negated elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat"><code>deduce_quat</code></a> template can be specialized to deduce the desired return type from the type <code>A</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_minus"><code>operator-</code> (binary)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">-</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A quaternion with elements equal to the elements of <code>b</code> subtracted from the corresponding elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat2"><code>deduce_quat2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_plus_eq"><code>operator+=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">+=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Adds the elements of <code>b</code> to the corresponding elements of <code>a</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_plus"><code>operator+</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value &amp;&amp;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">+</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A quaternion with elements equal to the elements of <code>a</code> added to the corresponding elements of <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat2"><code>deduce_quat2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_div_eq_scalar"><code>operator/=</code> (scalar)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">/=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>This operation divides a quaternion by a scalar.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_div_scalar"><code>operator/</code> (scalar)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">/</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A quaternion that is the result of dividing the quaternion <code>a</code> by the scalar <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat2"><code>deduce_quat2</code></a> template can be specialized to deduce the desired return type from the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_mul_eq_scalar"><code>operator*=</code> (scalar)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">*=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>This operation multiplies the quaternion <code>a</code> by the scalar <code>b</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_mul_eq"><code>operator*=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">*=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">A</span> <span class="nf">tmp</span><span class="p">(</span><span class="n">a</span><span class="p">);</span>
<span class="n">a</span> <span class="o">=</span> <span class="n">tmp</span> <span class="o">*</span> <span class="n">b</span><span class="p">;</span>
<span class="k">return</span> <span class="n">a</span><span class="p">;</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_mul_scalar"><code>operator*</code> (scalar)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A quaternion that is the result of multiplying the quaternion <code>a</code> by the scalar <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat2"><code>deduce_quat2</code></a> template can be specialized to deduce the desired return type from the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_mul"><code>operator*</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The result of multiplying the quaternions <code>a</code> and <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat2"><code>deduce_quat2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_eq"><code>operator==</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>true</code> if each element of <code>a</code> compares equal to its corresponding element of <code>b</code>, <code>false</code> otherwise.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_neq"><code>operator!=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!(a == b)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_cmp"><code>cmp</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span><span class="k">class</span> <span class="nc">Cmp</span><span class="p">&gt;</span>
  <span class="kt">bool</span> <span class="n">cmp</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span><span class="p">,</span> <span class="n">Cmp</span> <span class="n">pred</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>Similar to <a href="#quat_eq"><code>operator==</code></a>, except that it uses the binary predicate <code>pred</code> to compare the individual quaternion elements.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_mag_sqr"><code>mag_sqr</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span>
  <span class="n">mag_sqr</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The squared magnitude of the quaternion <code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_mag"><code>mag</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span>
  <span class="n">mag</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The magnitude of the quaternion <code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_normalized"><code>normalized</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">normalized</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">typename</span> <span class="n">deduce_quat</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span> <span class="n">tmp</span><span class="p">;</span>
<span class="n">assign</span><span class="p">(</span><span class="n">tmp</span><span class="p">,</span><span class="n">a</span><span class="p">);</span>
<span class="n">normalize</span><span class="p">(</span><span class="n">tmp</span><span class="p">);</span>
<span class="k">return</span> <span class="n">tmp</span><span class="p">;</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat"><code>deduce_quat</code></a> template can be specialized to deduce the desired return type from the type <code>A</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_normalize"><code>normalize</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">normalize</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Normalizes <code>a</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>mag(a)==scalar_traits&lt;typename quat_traits&lt;A&gt;::scalar_type&gt;::value(1).</code></p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>If the magnitude of <code>a</code> is zero, throws <a href="#zero_magnitude_error"><code>zero_magnitude_error</code></a>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_dot"><code>dot</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_scalar</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">dot</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The dot product of the quaternions <code>a</code> and <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_scalar"><code>deduce_scalar</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="conjugate"><code>conjugate</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">conjugate</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>Computes the conjugate of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat"><code>deduce_quat</code></a> template can be specialized to deduce the desired return type from the type <code>A</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_inverse"><code>inverse</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">inverse</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>Computes the multiplicative inverse of <code>a</code>, or the conjugate-to-norm ratio.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>If the magnitude of <code>a</code> is zero, throws <a href="#zero_magnitude_error"><code>zero_magnitude_error</code></a>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If <code>a</code> is known to be unit length, <code>conjugate</code> is equivalent to <a href="#quat_inverse"><code>inverse</code></a>, yet it is faster to compute.
</td>
</tr>
</table>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat"><code>deduce_quat</code></a> template can be specialized to deduce the desired return type from the type <code>A</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="slerp"><code>slerp180</code> / <code>slerp360</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value &amp;&amp; is_scalar&lt;C&gt;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span><span class="k">class</span> <span class="nc">C</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;</span> <span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">slerp180</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span><span class="p">,</span> <span class="n">C</span> <span class="n">c</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value &amp;&amp; is_scalar&lt;C&gt;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span><span class="k">class</span> <span class="nc">C</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;</span> <span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">slerp360</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span><span class="p">,</span> <span class="n">C</span> <span class="n">c</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_quat&lt;B&gt;::value &amp;&amp; is_scalar&lt;C&gt;</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span><span class="k">class</span> <span class="nc">C</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_quat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;</span> <span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">slerp</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span><span class="p">,</span> <span class="n">C</span> <span class="n">c</span> <span class="p">)</span> <span class="c1">// Deprecated</span>
    <span class="p">{</span>
    <span class="k">return</span> <span class="n">slerp360</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">t</span><span class="p">);</span>
    <span class="p">}</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Preconditions: </dt>
<dd>
<p><code>t&gt;=0 &amp;&amp; t&lt;=1</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A quaternion that is the result of Spherical Linear Interpolation of the quaternions <code>a</code> and <code>b</code> and the interpolation parameter <code>c</code>. When <code>slerp</code> is applied to unit quaternions, the quaternion path maps to a path through 3D rotations in a standard way. The effect is a rotation with uniform angular velocity around a fixed rotation axis.</p>
</dd>
</dl>
</div>
<div class="paragraph">
<p>When rotations represented by <code>a</code> and <code>b</code> are separated by more than 180 degrees, <code>slerp180</code> always takes the shorter path on the sphere, while <code>slerp360</code> does not. There are use cases for both behaviors, since each introduces a discontinuity under different conditions.</p>
</div>
<div class="paragraph">
<p>Mathematically:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">auto</span> <span class="n">q</span> <span class="o">=</span> <span class="n">slerp180</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">t</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>is equivalent to</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">auto</span> <span class="n">q</span> <span class="o">=</span> <span class="n">slerp360</span><span class="p">(</span><span class="n">dot</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">)</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="o">?</span> <span class="o">-</span><span class="n">a</span> <span class="o">:</span> <span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">t</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Previous versions of QVM implemented only the <code>slerp360</code> behavior, but under the name <code>slerp</code>. This is preserved for compatibility, but <code>slerp</code> is now deprecated. Please use either <code>slerp180</code> or <code>slerp360</code>.
</td>
</tr>
</table>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_quat2"><code>deduce_quat2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="zero_quat"><code>zero_quat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">zero_quat</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A read-only quaternion of unspecified type with <a href="#scalar_traits"><code>scalar_type</code></a> <code>T</code>, with all elements equal to <a href="#scalar_traits"><code>scalar_traits&lt;T&gt;::value(0)</code></a>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_set_zero"><code>set_zero</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_zero</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span><span class="n">a</span><span class="p">,</span>
  <span class="n">zero_quat</span><span class="o">&lt;</span><span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span><span class="o">&gt;</span><span class="p">());</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="identity_quat"><code>identity_quat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">S</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">identity_quat</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>An identity quaternion with scalar type <code>S</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_set_identity"><code>set_identity</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_identity</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span>
  <span class="n">a</span><span class="p">,</span>
  <span class="n">identity_quat</span><span class="o">&lt;</span><span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span><span class="o">&gt;</span><span class="p">());</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="rot_quat"><code>rot_quat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; vec_traits&lt;A&gt;::dim==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">rot_quat</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">axis</span><span class="p">,</span> <span class="k">typename</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A quaternion of unspecified type which performs a rotation around the <code>axis</code> at <code>angle</code> radians.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>In case the axis vector has zero magnitude, throws <a href="#zero_magnitude_error"><code>zero_magnitude_error</code></a>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <code>rot_quat</code> function is not a <a href="#view_proxy">view proxy</a>; it returns a temp object.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_set_rot"><code>set_rot</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp;</span>
  <span class="c1">//  is_vec&lt;B&gt;::value &amp;&amp; vec_traits&lt;B&gt;::dim==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">axis</span><span class="p">,</span> <span class="k">typename</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span>
  <span class="n">a</span><span class="p">,</span>
  <span class="n">rot_quat</span><span class="p">(</span><span class="n">axis</span><span class="p">,</span><span class="n">angle</span><span class="p">));</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_rotate"><code>rotate</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp;</span>
  <span class="c1">//  is_vec&lt;B&gt;::value &amp;&amp; vec_traits&lt;B&gt;::dim==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">axis</span><span class="p">,</span> <span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>a *= <a href="#rot_quat">rot_quat</a>(axis,angle)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="rotx_quat"><code>rotx_quat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">rotx_quat</span><span class="p">(</span> <span class="n">Angle</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A <a href="#view_proxy">view proxy</a> quaternion of unspecified type and scalar type <code>Angle</code>, which performs a rotation around the X axis at <code>angle</code> radians.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_set_rotx"><code>set_rotx</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rotx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span>
  <span class="n">a</span><span class="p">,</span>
  <span class="n">rotx_quat</span><span class="p">(</span><span class="n">angle</span><span class="p">));</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_rotate_x"><code>rotate_x</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_x</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>a *= <a href="#rotx_quat">rotx_quat</a>(angle)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="roty_quat"><code>roty_quat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">roty_quat</span><span class="p">(</span> <span class="n">Angle</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A <a href="#view_proxy">view proxy</a> quaternion of unspecified type and scalar type <code>Angle</code>, which performs a rotation around the Y axis at <code>angle</code> radians.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_set_roty"><code>set_roty</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rotz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span>
  <span class="n">a</span><span class="p">,</span>
  <span class="n">roty_quat</span><span class="p">(</span><span class="n">angle</span><span class="p">));</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_rotate_y"><code>rotate_y</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_y</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>a *= <a href="#roty_quat">roty_quat</a>(angle)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="rotz_quat"><code>rotz_quat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
    <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">rotz_quat</span><span class="p">(</span> <span class="n">Angle</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A <a href="#view_proxy">view proxy</a> quaternion of unspecified type and scalar type <code>Angle</code>, which performs a rotation around the Z axis at <code>angle</code> radians.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_set_rotz"><code>set_rotz</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rotz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span>
  <span class="n">a</span><span class="p">,</span>
  <span class="n">rotz_quat</span><span class="p">(</span><span class="n">angle</span><span class="p">));</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_rotate_z"><code>rotate_z</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_z</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">quat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>a *= <a href="#rotz_quat">rotz_quat</a>(angle)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="quat_scalar_cast"><code>scalar_cast</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Scalar</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="n">return_type</span><span class="o">-</span> <span class="n">scalar_cast</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A read-only <a href="#view_proxy">view proxy</a> of <code>a</code> that looks like a quaternion of the same dimensions as <code>a</code>, but with <a href="#quat_traits"><code>scalar_type</code></a> <code>Scalar</code> and elements constructed from the corresponding elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="qref"><code>qref</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_quat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">qref</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>An identity view proxy of <code>a</code>; that is, it simply accesses the elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<code>qref</code> allows calling QVM operations when <code>a</code> is of built-in type, for example a plain old C array.
</td>
</tr>
</table>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_vector_operations">Vector Operations</h3>
<div class="sect3">
<h4 id="vec_assign"><code>assign</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==vec_traits&lt;B&gt;::dim</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="n">A</span> <span class="o">&amp;</span> <span class="n">assign</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Copies all elements of the vector <code>b</code> to the vector <code>a</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_convert_to"><code>convert_to</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;R&gt;::value &amp;&amp; is_vec&lt;A&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;R&gt;::dim==vec_traits&lt;A&gt;::dim</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="n">R</span> <span class="n">convert_to</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>R</code> must be copyable.</p>
</dd>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>R r; assign(r,a); return r;</code></p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_minus_eq"><code>operator-=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==vec_traits&lt;B&gt;::dim</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">-=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Subtracts the elements of <code>b</code> from the corresponding elements of <code>a</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_minus_unary"><code>operator-</code> (unary)</h4>
<div class="paragraph">
<p>operator-(vec)</p>
</div>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_vec</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="k">operator</span><span class="o">-</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A vector of the negated elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_vec"><code>deduce_vec</code></a> template can be specialized to deduce the desired return type from the type <code>A</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_minus"><code>operator-</code> (binary)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==vec_traits&lt;B&gt;::dim</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">dim</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="k">operator</span><span class="o">-</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A vector of the same size as <code>a</code> and <code>b</code>, with elements the elements of <code>b</code> subtracted from the corresponding elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_vec2"><code>deduce_vec2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_plus_eq"><code>operator+=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==vec_traits&lt;B&gt;::dim</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">+=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Adds the elements of <code>b</code> to the corresponding elements of <code>a</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_plus"><code>operator+</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==vec_traits&lt;B&gt;::dim</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">dim</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="k">operator</span><span class="o">+</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A vector of the same size as <code>a</code> and <code>b</code>, with elements the elements of <code>b</code> added to the corresponding elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_vec2"><code>deduce_vec2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_div_eq_scalar"><code>operator/=</code> (scalar)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">/=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>This operation divides a vector by a scalar.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_div_scalar"><code>operator/</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">dim</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="k">operator</span><span class="o">/</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A vector that is the result of dividing the vector <code>a</code> by the scalar <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_vec2"><code>deduce_vec2</code></a> template can be specialized to deduce the desired return type from the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_mul_eq_scalar"><code>operator*=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">*=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>This operation multiplies the vector <code>a</code> by the scalar <code>b</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_mul_scalar"><code>operator*</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">dim</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

    <span class="c1">//Only enabled if: is_scalar&lt;B&gt;::value &amp;&amp; is_vec&lt;A&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">dim</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">B</span> <span class="n">b</span><span class="p">,</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A vector that is the result of multiplying the vector <code>a</code> by the scalar <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_vec2"><code>deduce_vec2</code></a> template can be specialized to deduce the desired return type from the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_eq"><code>operator==</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==vec_traits&lt;B&gt;::dim</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>true</code> if each element of <code>a</code> compares equal to its corresponding element of <code>b</code>, <code>false</code> otherwise.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_neq"><code>operator!=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==vec_traits&lt;B&gt;::dim</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!(a == b)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_cmp"><code>cmp</code></h4>
<div class="listingblock">
<div class="content">
<pre class="nowrap">.#include &lt;boost/qvm/mat_operations.hpp&gt;

namespace boost
{
  namespace qvm
  {
    //Only enabled if:
    //  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;
    //  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;
    //  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols
    template &lt;class A,class B,class Cmp&gt;
    bool cmp( A const &amp; a, B const &amp; b, Cmp pred );

} }</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>Similar to <a href="#vec_eq"><code>operator==</code></a>, except that the individual elements of <code>a</code> and <code>b</code> are passed to the binary predicate <code>pred</code> for comparison.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_mag_sqr"><code>mag_sqr</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span>
    <span class="n">mag_sqr</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The squared magnitude of the vector <code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_mag"><code>mag</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span>
    <span class="n">mag</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The magnitude of the vector <code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_normalized"><code>normalized</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_vec</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="n">normalized</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">typename</span> <span class="n">deduce_vec</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span> <span class="n">tmp</span><span class="p">;</span>
<span class="n">assign</span><span class="p">(</span><span class="n">tmp</span><span class="p">,</span><span class="n">a</span><span class="p">);</span>
<span class="n">normalize</span><span class="p">(</span><span class="n">tmp</span><span class="p">);</span>
<span class="k">return</span> <span class="n">tmp</span><span class="p">;</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_vec"><code>deduce_vec</code></a> template can be specialized to deduce the desired return type from the type <code>A</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_normalize"><code>normalize</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">normalize</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Normalizes <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="paragraph">
<p>Ensures:</p>
</div>
<div class="paragraph">
<p><code>mag(a)==<a href="#scalar_traits">scalar_traits</a>&lt;typename <a href="#vec_traits">vec_traits&lt;A&gt;::scalar_type</a>&gt;::value(1)</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>If the magnitude of <code>a</code> is zero, throws <a href="#zero_magnitude_error"><code>zero_magnitude_error</code></a>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_dot"><code>dot</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==vec_traits&lt;B&gt;::dim</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_scalar</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="n">dot</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The dot product of the vectors <code>a</code> and <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_scalar"><code>deduce_scalar</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_cross"><code>cross</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==3 &amp;&amp; vec_traits&lt;B&gt;::dim==3</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="mi">3</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="n">cross</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
    <span class="c1">//  vec_traits&lt;A&gt;::dim==2 &amp;&amp; vec_traits&lt;B&gt;::dim==2</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
    <span class="k">typename</span> <span class="n">deduce_scalar</span><span class="o">&lt;</span>
      <span class="k">typename</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span><span class="p">,</span>
      <span class="k">typename</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">scalar_type</span><span class="o">&gt;::</span><span class="n">type</span>
    <span class="n">cross</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The cross product of the vectors <code>a</code> and <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_vec2"><code>deduce_vec2</code></a> (and <a href="#deduce_scalar"><code>deduce_scalar</code></a>) templates can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="zero_vec"><code>zero_vec</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span><span class="kt">int</span> <span class="n">S</span><span class="p">&gt;</span>
    <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">zero_vec</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A read-only vector of unspecified type with <a href="#vec_traits"><code>scalar_type</code></a> <code>T</code> and size <code>S</code>, with all elements equal to <a href="#scalar_traits"><code>scalar_traits&lt;T&gt;::value(0)</code></a>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_set_zero"><code>set_zero</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if:</span>
    <span class="c1">//  is_vec&lt;A&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="kt">void</span> <span class="n">set_zero</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span><span class="n">a</span><span class="p">,</span>
  <span class="n">zero_vec</span><span class="o">&lt;</span>
    <span class="k">typename</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span><span class="p">,</span>
    <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">dim</span><span class="o">&gt;</span><span class="p">());</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vec_scalar_cast"><code>scalar_cast</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Scalar</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="n">return_type</span><span class="o">-</span> <span class="n">scalar_cast</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A read-only <a href="#view_proxy">view proxy</a> of <code>a</code> that looks like a vector of the same dimensions as <code>a</code>, but with <a href="#vec_traits"><code>scalar_type</code></a> <code>Scalar</code> and elements constructed from the corresponding elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="vref"><code>vref</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

    <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value</span>
    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
    <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">vref</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>An identity <a href="#view_proxy">view proxy</a> of <code>a</code>; that is, it simply accesses the elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<code>vref</code> allows calling QVM operations when <code>a</code> is of built-in type, for example a plain old C array.
</td>
</tr>
</table>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_matrix_operations">Matrix Operations</h3>
<div class="sect3">
<h4 id="mat_assign"><code>assign</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="n">assign</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Copies all elements of the matrix <code>b</code> to the matrix <code>a</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_convert_to"><code>convert_to</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;R&gt;::value &amp;&amp; is_mat&lt;A&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;R&gt;::rows==mat_traits&lt;A&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;R&gt;::cols==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="n">R</span> <span class="n">convert_to</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>R</code> must be copyable.</p>
</dd>
</dl>
</div>
<div class="paragraph">
<p>Effects:</p>
</div>
<div class="paragraph">
<p>As if: <code>R r; <a href="#mat_assign">assign</a>(r,a); return r;</code></p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_minus_eq_scalar"><code>operator-=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">-=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Subtracts the elements of <code>b</code> from the corresponding elements of <code>a</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_minus_unary"><code>operator-</code> (unary)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_mat</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">-</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A matrix of the negated elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_mat"><code>deduce_mat</code></a> template can be specialized to deduce the desired return type from the type <code>A</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_minus"><code>operator-</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_mat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">-</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A matrix of the same size as <code>a</code> and <code>b</code>, with elements the elements of <code>b</code> subtracted from the corresponding elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_mat2"><code>deduce_mat2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_plus_eq_scalar"><code>operator+=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">+=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Adds the elements of <code>b</code> to the corresponding elements of <code>a</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_plus"><code>operator+</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_mat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">+</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A matrix of the same size as <code>a</code> and <code>b</code>, with elements the elements of <code>b</code> added to the corresponding elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_mat2"><code>deduce_mat2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_div_eq_scalar"><code>operator/=</code> (scalar)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">/=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>This operation divides a matrix by a scalar.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_div_scalar"><code>operator/</code> (scalar)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_mat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">/</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A matrix that is the result of dividing the matrix <code>a</code> by the scalar <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_mat2"><code>deduce_mat2</code></a> template can be specialized to deduce the desired return type from the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_mul_eq"><code>operator*=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">*=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">A</span> <span class="nf">tmp</span><span class="p">(</span><span class="n">a</span><span class="p">);</span>
<span class="n">a</span> <span class="o">=</span> <span class="n">tmp</span> <span class="o">*</span> <span class="n">b</span><span class="p">;</span>
<span class="k">return</span> <span class="n">a</span><span class="p">;</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_mul_eq_scalar"><code>operator*=</code> (scalar)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">A</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">*=</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>This operation multiplies the matrix <code>a</code> matrix by the scalar <code>b</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_mul"><code>operator*</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::rows</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_mat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The result of <a href="https://en.wikipedia.org/wiki/Matrix_multiplication">multiplying</a> the matrices <code>a</code> and <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_mat2"><code>deduce_mat2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_mul_scalar"><code>operator*</code> (scalar)</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_mat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">b</span> <span class="p">);</span>

  <span class="c1">//Only enabled if: is_scalar&lt;B&gt;::value &amp;&amp; is_mat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_mat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">B</span> <span class="n">b</span><span class="p">,</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A matrix that is the result of multiplying the matrix <code>a</code> by the scalar <code>b</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_mat2"><code>deduce_mat2</code></a> template can be specialized to deduce the desired return type from the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_eq"><code>operator==</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>true</code> if each element of <code>a</code> compares equal to its corresponding element of <code>b</code>, <code>false</code> otherwise.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_neq"><code>operator!=</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( a <a href="#mat_eq">==</a> b )</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_cmp"><code>cmp</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_mat&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;B&gt;::rows &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;B&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">,</span><span class="k">class</span> <span class="nc">Cmp</span><span class="p">&gt;</span>
  <span class="kt">bool</span> <span class="n">cmp</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span><span class="p">,</span> <span class="n">Cmp</span> <span class="n">pred</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>Similar to <a href="#mat_eq"><code>operator==</code></a>, except that the individual elements of <code>a</code> and <code>b</code> are passed to the binary predicate <code>pred</code> for comparison.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_inverse"><code>inverse</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_scalar&lt;B&gt;::value</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_mat2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">inverse</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="n">det</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_mat</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">inverse</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Preconditions: </dt>
<dd>
<p><code>det!=0</code></p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>Both overloads compute the inverse of <code>a</code>. The first overload takes the pre-computed determinant of <code>a</code>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>The second overload computes the determinant automatically and throws <a href="#zero_determinant_error"><code>zero_determinant_error</code></a> if the computed determinant is zero.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_mat"><code>deduce_mat</code></a> (and <a href="#deduce_mat2"><code>deduce_mat2</code></a>) templates can be specialized to deduce the desired return type from the type <code>A</code> (and <code>B</code>).
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="zero_mat"><code>zero_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span><span class="kt">int</span> <span class="n">D</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">zero_mat</span><span class="p">();</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span><span class="kt">int</span> <span class="n">R</span><span class="p">,</span><span class="kt">int</span> <span class="n">C</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">zero_mat</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A read-only matrix of unspecified type with <a href="#mat_traits"><code>scalar_type</code></a> <code>T</code>, <code>R</code> rows and <code>C</code> columns (or <code>D</code> rows and <code>D</code> columns), with all elements equal to <a href="#scalar_traits"><code>scalar_traits&lt;T&gt;::value(0)</code></a>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_set_zero"><code>set_zero</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_zero</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span><span class="n">a</span><span class="p">,</span>
  <span class="n">zero_mat</span><span class="o">&lt;</span>
    <span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span><span class="p">,</span>
    <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="p">,</span>
    <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">&gt;</span><span class="p">());</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="identity_mat"><code>identity_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="nowrap">namespace boost { namespace qvm {

  template &lt;class S,int D&gt;
  -unspecified-return-type- identity_mat();

} }</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>An identity matrix of size <code>D</code> x <code>D</code> and scalar type <code>S</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_set_identity"><code>set_identity</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==mat_traits&lt;A&gt;::rows</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_identity</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span>
  <span class="n">a</span><span class="p">,</span>
  <span class="n">identity_mat</span><span class="o">&lt;</span>
    <span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span><span class="p">,</span>
    <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="p">,</span>
    <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">cols</span><span class="o">&gt;</span><span class="p">());</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="rot_mat"><code>rot_mat</code> / Euler angles</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_vec&lt;A&gt;::value &amp;&amp; vec_traits&lt;A&gt;::dim==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">axis</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">angle</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_xzy</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_xyz</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_yxz</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_yzx</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_zyx</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_zxy</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_xzx</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_xyx</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_yxy</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_yzy</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_zyz</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">rot_mat_zxz</span><span class="p">(</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A matrix of unspecified type, of <code>Dim</code> rows and <code>Dim</code> columns parameter, which performs a rotation around the <code>axis</code> at <code>angle</code> radians, or Tait–Bryan angles (x-y-z, y-z-x, z-x-y, x-z-y, z-y-x, y-x-z), or proper Euler angles (z-x-z, x-y-x, y-z-y, z-y-z, x-z-x, y-x-y). See <a href="https://en.wikipedia.org/wiki/Euler_angles">Euler angles</a>.</p>
</dd>
<dt class="hdlist1">Throws: </dt>
<dd>
<p>In case the axis vector has zero magnitude, throws <a href="#zero_magnitude_error"><code>zero_magnitude_error</code></a>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
These functions are not view proxies; they return a temp object.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_set_rot"><code>set_rot</code> / Euler angles</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols &amp;&amp;</span>
  <span class="c1">//  is_vec&lt;B&gt;::value &amp;&amp; vec_traits&lt;B&gt;::dim==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">axis</span><span class="p">,</span> <span class="k">typename</span> <span class="n">vec_traits</span><span class="o">&lt;</span><span class="n">B</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_xzy</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_xyz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_yxz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_yzx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_zyx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_zxy</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_xzx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_xyx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_yxy</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_yzy</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_zyz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_zxz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rot_xzy</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Assigns the return value of the corresponding <a href="#rot_mat"><code>rot_mat</code></a> function to <code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_rotate"><code>rotate</code> / Euler angles</h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols &amp;&amp;</span>
  <span class="c1">//  is_vec&lt;B&gt;::value &amp;&amp; vec_traits&lt;B&gt;::dim==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">axis</span><span class="p">,</span> <span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_xzy</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_xyz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_yxz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_yzx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_zyx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_zxy</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_xzx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_xyx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_yxy</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_yzy</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_zyz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">y2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_zxz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z1</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">x2</span><span class="p">,</span> <span class="n">Angle</span> <span class="n">z3</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Multiplies the matrix <code>a</code> in-place by the return value of the corresponding <a href="#rot_mat"><code>rot_mat</code></a> function.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="rotx_mat"><code>rotx_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">rotx_mat</span><span class="p">(</span> <span class="n">Angle</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A <a href="#view_proxy">view proxy</a> matrix of unspecified type, of <code>Dim</code> rows and <code>Dim</code> columns and scalar type <code>Angle</code>, which performs a rotation around the <code>X</code> axis at <code>angle</code> radians.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_set_rotx"><code>set_rotx</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rotx</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span>
  <span class="n">a</span><span class="p">,</span>
  <span class="n">rotx_mat</span><span class="o">&lt;</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="o">&gt;</span><span class="p">(</span><span class="n">angle</span><span class="p">));</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_rotate_x"><code>rotate_x</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_x</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>a <a href="#mat_mul_eq">*=</a> <a href="#rotx_mat">rotx_mat</a>&lt;<a href="#mat_traits">mat_traits&lt;A&gt;::rows</a>&gt;(angle)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="roty_mat"><code>roty_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">roty_mat</span><span class="p">(</span> <span class="n">Angle</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A <a href="#view_proxy">view proxy</a> matrix of unspecified type, of <code>Dim</code> rows and <code>Dim</code> columns and scalar type <code>Angle</code>, which performs a rotation around the <code>Y</code> axis at <code>angle</code> radians.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_set_roty"><code>set_roty</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_roty</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span>
  <span class="n">a</span><span class="p">,</span>
  <span class="n">roty_mat</span><span class="o">&lt;</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="o">&gt;</span><span class="p">(</span><span class="n">angle</span><span class="p">));</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_rotate_y"><code>rotate_y</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_y</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>a <a href="#mat_mul_eq">*=</a> <a href="#roty_mat">roty_mat</a>&lt;<a href="#mat_traits">mat_traits&lt;A&gt;::rows</a>&gt;(angle)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="rotz_mat"><code>rotz_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">Dim</span><span class="p">,</span><span class="k">class</span> <span class="nc">Angle</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">rotz_mat</span><span class="p">(</span> <span class="n">Angle</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A <a href="#view_proxy">view proxy</a> matrix of unspecified type, of <code>Dim</code> rows and <code>Dim</code> columns and scalar type <code>Angle</code>, which performs a rotation around the <code>Z</code> axis at <code>angle</code> radians.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_set_rotz"><code>set_rotz</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">set_rotz</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if:</p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="n">assign</span><span class="p">(</span>
  <span class="n">a</span><span class="p">,</span>
  <span class="n">rotz_mat</span><span class="o">&lt;</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="o">&gt;</span><span class="p">(</span><span class="n">angle</span><span class="p">));</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_rotate_z"><code>rotate_z</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows&gt;=3 &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="kt">void</span> <span class="n">rotate_z</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="k">typename</span> <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span> <span class="n">angle</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>a <a href="#mat_mul_eq">*=</a> <a href="#rotz_mat">rotz_mat</a>&lt;<a href="#mat_traits">mat_traits&lt;A&gt;::rows</a>&gt;(angle)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="determinant"><code>determinant</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">scalar_type</span>
  <span class="n">determinant</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This function computes the <a href="https://en.wikipedia.org/wiki/Determinant">determinant</a> of the square matrix <code>a</code>.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="perspective_lh"><code>perspective_lh</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">perspective_lh</span><span class="p">(</span> <span class="n">T</span> <span class="n">fov_y</span><span class="p">,</span> <span class="n">T</span> <span class="n">aspect</span><span class="p">,</span> <span class="n">T</span> <span class="n">zn</span><span class="p">,</span> <span class="n">T</span> <span class="n">zf</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A 4x4 projection matrix of unspecified type of the following form:</p>
<table class="tableblock frame-all grid-all" style="width: 50%;">
<colgroup>
<col style="width: 25%;">
<col style="width: 25%;">
<col style="width: 25%;">
<col style="width: 25%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-center valign-top"><p class="tableblock"><code>xs</code></p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
</tr>
<tr>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock"><code>ys</code></p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
</tr>
<tr>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock"><code>zf</code>/(<code>zf</code>-<code>zn</code>)</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">-<code>zn</code>*<code>zf</code>/(<code>zf</code>-<code>zn</code>)</p></td>
</tr>
<tr>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">1</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>where <code>ys</code> = cot(<code>fov_y</code>/2) and <code>xs</code> = <code>ys</code>/<code>aspect</code>.</p>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="perspective_rh"><code>perspective_rh</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span>
  <span class="n">perspective_rh</span><span class="p">(</span> <span class="n">T</span> <span class="n">fov_y</span><span class="p">,</span> <span class="n">T</span> <span class="n">aspect</span><span class="p">,</span> <span class="n">T</span> <span class="n">zn</span><span class="p">,</span> <span class="n">T</span> <span class="n">zf</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A 4x4 projection matrix of unspecified type of the following form:</p>
<table class="tableblock frame-all grid-all" style="width: 50%;">
<colgroup>
<col style="width: 25%;">
<col style="width: 25%;">
<col style="width: 25%;">
<col style="width: 25%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-center valign-top"><p class="tableblock"><code>xs</code></p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
</tr>
<tr>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock"><code>ys</code></p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
</tr>
<tr>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock"><code>zf</code>/(<code>zn</code>-<code>zf</code>)</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock"><code>zn</code>*<code>zf</code>/(<code>zn</code>-<code>zf</code>)</p></td>
</tr>
<tr>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">-1</p></td>
<td class="tableblock halign-center valign-top"><p class="tableblock">0</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>where <code>ys</code> = cot(<code>fov_y</code>/2), and <code>xs</code> = <code>ys</code>/<code>aspect</code>.</p>
</div>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mat_scalar_cast"><code>scalar_cast</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">Scalar</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="n">return_type</span><span class="o">-</span> <span class="n">scalar_cast</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A read-only <a href="#view_proxy">view proxy</a> of <code>a</code> that looks like a matrix of the same dimensions as <code>a</code>, but with <a href="#mat_traits"><code>scalar_type</code></a> <code>Scalar</code> and elements constructed from the corresponding elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="mref"><code>mref</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">mref</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>An identity view proxy of <code>a</code>; that is, it simply accesses the elements of <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
<code>mref</code> allows calling QVM operations when <code>a</code> is of built-in type, for example a plain old C array.
</td>
</tr>
</table>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_quaternion_vector_operations">Quaternion-Vector Operations</h3>
<div class="sect3">
<h4 id="quat_vec_mul"><code>operator*</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/quat_vec_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_quat&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  is_vec&lt;B&gt;::value &amp;&amp; vec_traits&lt;B&gt;::dim==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The result of transforming the vector <code>b</code> by the quaternion <code>a</code>.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_vec2"><code>deduce_vec2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_matrix_vector_operations">Matrix-Vector Operations</h3>
<div class="sect3">
<h4 id="mat_vec_mul"><code>operator*</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols==vec_traits&lt;B&gt;::dim</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="k">typename</span> <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="n">mat_traits</span><span class="o">&lt;</span><span class="n">A</span><span class="o">&gt;::</span><span class="n">rows</span><span class="o">&gt;::</span><span class="n">type</span>
  <span class="k">operator</span><span class="o">*</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The result of multiplying the matrix <code>a</code> and the vector <code>b</code>, where <code>b</code> is interpreted as a matrix-column. The resulting matrix-row is returned as a vector type.</p>
</dd>
</dl>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
The <a href="#deduce_vec2"><code>deduce_vec2</code></a> template can be specialized to deduce the desired return type, given the types <code>A</code> and <code>B</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="transform_vector"><code>transform_vector</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==4 &amp;&amp; mat_traits&lt;A&gt;::cols==4 &amp;&amp;</span>
  <span class="c1">//  vec_traits&lt;B&gt;::dim==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="mi">3</span><span class="o">&gt;</span> <span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">transform_vector</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>return a <a href="#mat_vec_mul">*</a> <a href="#swizzling">XYZ0</a>(b)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="transform_point"><code>transform_point</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/vec_mat_operations.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp; is_vec&lt;B&gt;::value &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::rows==4 &amp;&amp; mat_traits&lt;A&gt;::cols==4 &amp;&amp;</span>
  <span class="c1">//  vec_traits&lt;B&gt;::dim==3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">,</span><span class="k">class</span> <span class="nc">B</span><span class="p">&gt;</span>
  <span class="n">deduce_vec2</span><span class="o">&lt;</span><span class="n">A</span><span class="p">,</span><span class="n">B</span><span class="p">,</span><span class="mi">3</span><span class="o">&gt;</span> <span class="o">&gt;::</span><span class="n">type</span>
  <span class="n">transform_point</span><span class="p">(</span> <span class="n">A</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">a</span><span class="p">,</span> <span class="n">B</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">b</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>As if: <code>return a <a href="#mat_vec_mul">*</a> <a href="#swizzling">XYZ1</a>(b)</code>.</p>
</dd>
</dl>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_matrix_to_matrix_view_proxies">Matrix-to-Matrix View Proxies</h3>
<div class="sect3">
<h4 id="del_row"><code>del_row</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">R</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">del_row</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>del_row&lt;R&gt;(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that looks like the matrix <code>m</code> with row <code>R</code> deleted.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="del_col"><code>del_col</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">C</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">del_col</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>del_col&lt;C&gt;(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that looks like the matrix <code>m</code> with column <code>C</code> deleted.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="del_row_col"><code>del_row_col</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">R</span><span class="p">,</span><span class="kt">int</span> <span class="n">C</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">del_row_col</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>del_row_col&lt;R,C&gt;(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that looks like the matrix <code>m</code> with row <code>R</code> and column <code>C</code> deleted.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="neg_row"><code>neg_row</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">R</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">neg_row</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>neg_row&lt;R&gt;(m)</code> returns a read-only <a href="#view_proxy">view proxy</a> that looks like the matrix <code>m</code> with row <code>R</code> negated.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="neg_col"><code>neg_col</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">C</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">neg_col</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre class="nowrap">The expression `neg_col&lt;C&gt;(m)` returns a read-only &lt;&lt;view_proxy,`view proxy`&gt;&gt; that looks like the matrix `m` with column `C` negated.</pre>
</div>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="swap_rows"><code>swap_rows</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">R1</span><span class="p">,</span><span class="kt">int</span> <span class="n">R2</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">swap_rows</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>swap_rows&lt;R1,R2&gt;(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that looks like the matrix <code>m</code> with rows <code>R1</code> and <code>R2</code> swapped.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="swap_cols"><code>swap_cols</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">C1</span><span class="p">,</span><span class="kt">int</span> <span class="n">C2</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">swap_cols</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>swap_cols&lt;C1,C2&gt;(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that looks like the matrix <code>m</code> with columns <code>C1</code> and <code>C2</code> swapped.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="transposed"><code>transposed</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">transposed</span><span class="p">();</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>transposed(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that transposes the matrix <code>m</code>.</p>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_vector_to_matrix_view_proxies">Vector-to-Matrix View Proxies</h3>
<div class="sect3">
<h4 id="col_mat"><code>col_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_vec_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="n">iclass</span> <span class="n">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">col_mat</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>col_mat(v)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that accesses the vector <code>v</code> as a matrix-column.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="row_mat"><code>row_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_vec_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="n">iclass</span> <span class="n">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">row_mat</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>row_mat(v)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that accesses the vector <code>v</code> as a matrix-row.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="translation_mat"><code>translation_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_vec_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="n">iclass</span> <span class="n">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">translation_mat</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>translation_mat(v)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that accesses the vector <code>v</code> as translation matrix of size 1 + <a href="#vec_traits"><code>vec_traits&lt;A&gt;::dim</code></a>.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="diag_mat"><code>diag_mat</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_vec_mat.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_vec&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="n">iclass</span> <span class="n">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">diag_mat</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>diag_mat(v)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that accesses the vector <code>v</code> as a square matrix of the same dimensions in which the elements of <code>v</code> appear as the main diagonal and all other elements are zero.</p>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If <code>v</code> is a 3D vector, the expression <code>diag_mat(XYZ1(v))</code> can be used as a scaling 4D matrix.
</td>
</tr>
</table>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_matrix_to_vector_view_proxies">Matrix-to-Vector View Proxies</h3>
<div class="sect3">
<h4 id="col"><code>col</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_vec.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">C</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">col</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>col&lt;C&gt;(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that accesses column <code>C</code> of the matrix <code>m</code> as a vector.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="row"><code>row</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_vec.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="kt">int</span> <span class="n">C</span><span class="p">,</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">row</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>row&lt;R&gt;(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that accesses row <code>R</code> of the matrix <code>m</code> as a vector.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="diag"><code>diag</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_vec.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if: is_mat&lt;A&gt;::value</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">diag</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>diag(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that accesses the main diagonal of the matrix <code>m</code> as a vector.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="translation"><code>translation</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/map_mat_vec.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="c1">//Only enabled if:</span>
  <span class="c1">//  is_mat&lt;A&gt;::value &amp;&amp;</span>
  <span class="c1">//  (mat_traits&lt;A&gt;::rows==mat_traits&lt;A&gt;::cols || mat_traits&lt;A&gt;::rows+1==mat_traits&lt;A&gt;::cols) &amp;&amp;</span>
  <span class="c1">//  mat_traits&lt;A&gt;::cols&gt;=3</span>
  <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="o">-</span><span class="n">unspecified</span><span class="o">-</span><span class="k">return</span><span class="o">-</span><span class="n">type</span><span class="o">-</span> <span class="n">translation</span><span class="p">(</span> <span class="n">A</span> <span class="o">&amp;</span> <span class="n">a</span> <span class="p">);</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression <code>translation(m)</code> returns an lvalue <a href="#view_proxy">view proxy</a> that accesses the translation component of the square matrix <code>m</code>, which is a vector of size <code>C</code>-1, where <code>C</code> is the number of column in <code>m</code>.</p>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_exceptions">Exceptions</h3>
<div class="sect3">
<h4 id="error"><code>error</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/error.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">struct</span> <span class="nc">error</span><span class="o">:</span> <span class="k">virtual</span> <span class="n">boost</span><span class="o">::</span><span class="n">exception</span><span class="p">,</span> <span class="k">virtual</span> <span class="n">std</span><span class="o">::</span><span class="n">exception</span> <span class="p">{</span> <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This is the base for all exceptions thorwn by QVM.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="zero_magnitude_error"><code>zero_magnitude_error</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/error.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">struct</span> <span class="nc">zero_magnitude_error</span><span class="o">:</span> <span class="k">virtual</span> <span class="n">error</span> <span class="p">{</span> <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This exception indicates that an operation requires a vector or a quaternion with non-zero magnitude, but the computed magnitude is zero.</p>
</div>
<hr>
</div>
<div class="sect3">
<h4 id="zero_determinant_error"><code>zero_determinant_error</code></h4>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/error.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="k">struct</span> <span class="nc">zero_determinant_error</span><span class="o">:</span> <span class="k">virtual</span> <span class="n">error</span> <span class="p">{</span> <span class="p">};</span>

<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This exception indicates that an operation requires a matrix with non-zero determinant, but the computed determinant is zero.</p>
</div>
<hr>
</div>
</div>
<div class="sect2">
<h3 id="_macros_and_configuration_boost_qvm">Macros and Configuration: BOOST_QVM_</h3>
<div class="sect3">
<h4 id="BOOST_QVM_INLINE"><code>INLINE</code></h4>
<div class="sect4">
<h5 id="_boost_qvm_inline"><code>BOOST_QVM_INLINE</code></h5>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/config.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="cp">#ifndef BOOST_QVM_INLINE
</span>  <span class="cp">#define BOOST_QVM_INLINE inline
</span>  <span class="cp">#endif
</span>
<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This macro is not used directly by QVM, except as the default value of other macros from <code>&lt;boost/qvm/config.hpp&gt;</code>. A user-defined <code>BOOST_QVM_INLINE</code> should expand to a value that is valid substitution of the <code>inline</code> keyword in function definitions.</p>
</div>
<hr>
</div>
</div>
<div class="sect3">
<h4 id="BOOST_QVM_FORCE_INLINE"><code>FORCE_INLINE</code></h4>
<div class="sect4">
<h5 id="_boost_qvm_force_inline"><code>BOOST_QVM_FORCE_INLINE</code></h5>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/config.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="cp">#ifndef BOOST_QVM_FORCE_INLINE
</span>  <span class="cp">#define BOOST_QVM_FORCE_INLINE </span><span class="cm">/*platform-specific*/</span><span class="cp">
</span>  <span class="cp">#endif
</span>
<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This macro is not used directly by QVM, except as the default value of other macros from <code>&lt;boost/qvm/config.hpp&gt;</code>. A user-defined <code>BOOST_QVM_FORCE_INLINE</code> should expand to a value that is valid substitution of the <code>inline</code> keyword in function definitions, to indicate that the compiler must inline the function. Of course, actual inlining may or may not occur.</p>
</div>
<hr>
</div>
</div>
<div class="sect3">
<h4 id="BOOST_QVM_INLINE_TRIVIAL"><code>INLINE_TRIVIAL</code></h4>
<div class="sect4">
<h5 id="_boost_qvm_inline_trivial"><code>BOOST_QVM_INLINE_TRIVIAL</code></h5>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/config.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="cp">#ifndef BOOST_QVM_INLINE_TRIVIAL
</span>  <span class="cp">#define BOOST_QVM_INLINE_TRIVIAL BOOST_QVM_FORCE_INLINE
</span>  <span class="cp">#endif
</span>
<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>QVM uses <code>BOOST_QVM_INLINE_TRIVIAL</code> in definitions of functions that are not critical for the overall performance of the library but are extremely simple (such as one-liners) and therefore should always be inlined.</p>
</div>
<hr>
</div>
</div>
<div class="sect3">
<h4 id="BOOST_QVM_INLINE_CRITICAL"><code>INLINE_CRITICAL</code></h4>
<div class="sect4">
<h5 id="_boost_qvm_inline_critical"><code>BOOST_QVM_INLINE_CRITICAL</code></h5>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/config.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="cp">#ifndef BOOST_QVM_INLINE_CRITICAL
</span>  <span class="cp">#define BOOST_QVM_INLINE_CRITICAL BOOST_QVM_FORCE_INLINE
</span>  <span class="cp">#endif
</span>
<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>QVM uses <code>BOOST_QVM_INLINE_CRITICAL</code> in definitions of functions that are critical for the overall performance of the library, such as functions that access individual vector and matrix elements.</p>
</div>
<hr>
</div>
</div>
<div class="sect3">
<h4 id="BOOST_QVM_INLINE_OPERATIONS"><code>INLINE_OPERATIONS</code></h4>
<div class="sect4">
<h5 id="_boost_qvm_inline_operations"><code>BOOST_QVM_INLINE_OPERATIONS</code></h5>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/config.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="cp">#ifndef BOOST_QVM_INLINE_OPERATIONS
</span>  <span class="cp">#define BOOST_QVM_INLINE_OPERATIONS BOOST_QVM_INLINE
</span>  <span class="cp">#endif
</span>
<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>QVM uses <code>BOOST_QVM_INLINE_OPERATIONS</code> in definitions of functions that implement various high-level operations, such as matrix multiplication, computing the magnitude of a vector, etc.</p>
</div>
<hr>
</div>
</div>
<div class="sect3">
<h4 id="BOOST_QVM_INLINE_RECURSION"><code>INLINE_RECURSION</code></h4>
<div class="sect4">
<h5 id="_boost_qvm_inline_recursion"><code>BOOST_QVM_INLINE_RECURSION</code></h5>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/config.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="cp">#ifndef BOOST_QVM_INLINE_RECURSION
</span>  <span class="cp">#define BOOST_QVM_INLINE_RECURSION BOOST_QVM_INLINE_OPERATIONS
</span>  <span class="cp">#endif
</span>
<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>QVM uses <code>BOOST_QVM_INLINE_RECURSION</code> in definitions of recursive functions that are not critical for the overall performance of the library (definitions of all critical functions, including critical recursive functions, use <a href="#BOOST_QVM_INLINE_CRITICAL"><code>BOOST_QVM_INLINE_CRITICAL</code></a>).</p>
</div>
<hr>
</div>
</div>
<div class="sect3">
<h4 id="BOOST_QVM_ASSERT"><code>ASSERT</code></h4>
<div class="sect4">
<h5 id="_boost_qvm_assert"><code>BOOST_QVM_ASSERT</code></h5>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/assert.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

<span class="cp">#ifndef BOOST_QVM_ASSERT
#include &lt;boost/assert.hpp&gt;
#define BOOST_QVM_ASSERT BOOST_ASSERT
#endif
</span>
<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This is the macro QVM uses to assert on precondition violations and logic errors. A user-defined <code>BOOST_QVM_ASSERT</code> should have the semantics of the standard <code>assert</code>.</p>
</div>
<hr>
</div>
</div>
<div class="sect3">
<h4 id="BOOST_QVM_STATIC_ASSERT"><code>STATIC_ASSERT</code></h4>
<div class="sect4">
<h5 id="_boost_qvm_static_assert"><code>BOOST_QVM_STATIC_ASSERT</code></h5>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/static_assert.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="cp">#ifndef BOOST_QVM_STATIC_ASSERT
</span>  <span class="cp">#include &lt;boost/static_assert.hpp&gt;
</span>  <span class="cp">#define BOOST_QVM_STATIC_ASSERT BOOST_STATIC_ASSERT
</span>  <span class="cp">#endif
</span>
<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>All static assertions in QVM use the <code>BOOST_QVM_STATIC_ASSERT</code> macro.</p>
</div>
<hr>
</div>
</div>
<div class="sect3">
<h4 id="BOOST_QVM_THROW_EXCEPTION"><code>THROW_EXCEPTION</code></h4>
<div class="sect4">
<h5 id="_boost_qvm_throw_exception"><code>BOOST_QVM_THROW_EXCEPTION</code></h5>
<div class="listingblock">
<div class="title">#include &lt;boost/qvm/throw_exception.hpp&gt;</div>
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span> <span class="k">namespace</span> <span class="n">qvm</span> <span class="p">{</span>

  <span class="cp">#ifndef BOOST_QVM_THROW_EXCEPTION
</span>  <span class="cp">#include &lt;boost/throw_exception.hpp&gt;
</span>  <span class="cp">#define BOOST_QVM_THROW_EXCEPTION BOOST_THROW_EXCEPTION
</span>  <span class="cp">#endif
</span>
<span class="p">}</span> <span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This macro is used whenever QVM throws an exception. Users who override the standard <code>BOOST_QVM_THROW_EXCEPTION</code> behavior must ensure that when invoked, the substituted implementation does not return control to the caller. Below is a list of all QVM functions that invoke <code>BOOST_QVM_THROW_EXCEPTION</code>:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Quaternion operations:</p>
<div class="ulist">
<ul>
<li>
<p><a href="#quat_inverse"><code>inverse</code></a></p>
</li>
<li>
<p><a href="#rot_quat"><code>rot_quat</code></a></p>
</li>
<li>
<p><a href="#quat_normalize"><code>normalize</code></a></p>
</li>
<li>
<p><a href="#quat_normalized"><code>normalized</code></a></p>
</li>
</ul>
</div>
</li>
<li>
<p>Vector operations:</p>
<div class="ulist">
<ul>
<li>
<p><a href="#vec_normalize"><code>normalize</code></a></p>
</li>
<li>
<p><a href="#vec_normalized"><code>normalized</code></a></p>
</li>
</ul>
</div>
</li>
<li>
<p>Matrix operations:</p>
<div class="ulist">
<ul>
<li>
<p><a href="#mat_inverse"><code>inverse</code></a></p>
</li>
<li>
<p><a href="#rot_mat"><code>rot_mat</code></a></p>
</li>
</ul>
</div>
</li>
</ul>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="rationale">Design Rationale</h2>
<div class="sectionbody">
<div class="paragraph">
<p>C&#43;&#43; is ideal for 3D graphics and other domains that require 3D transformations: define vector and matrix types and then overload the appropriate operators to implement the standard algebraic operations. Because this is relatively straight-forward, there are many libraries that do this, each providing custom vector and matrix types, and then defining the same operations (e.g. matrix multiply) for these types.</p>
</div>
<div class="paragraph">
<p>Often these libraries are part of a higher level system. For example, video game programmers typically use one set of vector/matrix types with the rendering engine, and another with the physics simulation engine.</p>
</div>
<div class="paragraph">
<p>QVM proides interoperability between all these different types and APIs by decoupling the standard algebraic functions from the types they operate on&#8201;&#8212;&#8201;without compromising type safety. The operations work on any type for which proper traits have been specialized. Using QVM, there is no need to translate between the different quaternion, vector or matrix types; they can be mixed in the same expression safely and efficiently.</p>
</div>
<div class="paragraph">
<p>This design enables QVM to generate types and adaptors at compile time, compatible with any other QVM or user-defined type. For example, transposing a matrix needs not store the result: rather than modifying its argument or returning a new object, it simply binds the original matrix object through a generated type which remaps element access on the fly.</p>
</div>
<div class="paragraph">
<p>In addition, QVM can be helpful in selectively optimizing individual types or operations for maximum performance where that matters. For example, users can overload a specific operation for specific types, or define highly optimized, possibly platform-specific or for some reason cumbersome to use types, then mix and match them with more user-friendly types in parts of the program where performance isn&#8217;t critical.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_code_generator">Code Generator</h2>
<div class="sectionbody">
<div class="paragraph">
<p>While QVM defines generic functions that operate on matrix and vector types of arbitrary static dimensions, it also provides a code generator that can be used to create compatible header files that define much simpler specializations of these functions for specific dimensions. This is useful during debugging since the generated code is much easier to read than the template metaprogramming-heavy generic implementations. It is also potentially friendlier to the optimizer.</p>
</div>
<div class="paragraph">
<p>The code generator is a command-line utility program. Its source code can be found in the <code>boost/libs/qvm/gen</code> directory. It was used to generate the following headers that ship with QVM:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>2D, 3D and 4D matrix operations:</p>
<div class="ulist">
<ul>
<li>
<p><code>boost/qvm/gen/mat_operations2.hpp</code> (matrices of size 2x2, 2x1 and 1x2, included by <code>boost/qvm/mat_operations2.hpp</code>)</p>
</li>
<li>
<p><code>boost/qvm/gen/mat_operations3.hpp</code> (matrices of size 3x3, 3x1 and 1x3, included by <code>boost/qvm/mat_operations3.hpp</code>)</p>
</li>
<li>
<p><code>boost/qvm/gen/mat_operations4.hpp</code> (matrices of size 4x4, 4x1 and 1x4, included by <code>boost/qvm/mat_operations4.hpp</code>)</p>
</li>
</ul>
</div>
</li>
<li>
<p>2D, 3D and 4D vector operations:</p>
<div class="ulist">
<ul>
<li>
<p><code>boost/qvm/gen/v2.hpp</code> (included by <code>boost/qvm/vec_operations2.hpp</code>)</p>
</li>
<li>
<p><code>boost/qvm/gen/v3.hpp</code> (included by <code>boost/qvm/vec_operations3.hpp</code>)</p>
</li>
<li>
<p><code>boost/qvm/gen/v4.hpp</code> (included by <code>boost/qvm/vec_operations4.hpp</code>)</p>
</li>
</ul>
</div>
</li>
<li>
<p>2D, 3D and 4D vector-matrix operations:</p>
<div class="ulist">
<ul>
<li>
<p><code>boost/qvm/gen/vm2.hpp</code> (included by <code>boost/qvm/vec_mat_operations2.hpp</code>)</p>
</li>
<li>
<p><code>boost/qvm/gen/vm3.hpp</code> (included by <code>boost/qvm/vec_mat_operations3.hpp</code>)</p>
</li>
<li>
<p><code>boost/qvm/gen/vm4.hpp</code> (included by <code>boost/qvm/vec_mat_operations4.hpp</code>)</p>
</li>
</ul>
</div>
</li>
<li>
<p>2D, 3D and 4D vector swizzling operations:</p>
<div class="ulist">
<ul>
<li>
<p><code>boost/qvm/gen/sw2.hpp</code> (included by <code>boost/qvm/swizzle2.hpp</code>)</p>
</li>
<li>
<p><code>boost/qvm/gen/sw3.hpp</code> (included by <code>boost/qvm/swizzle3.hpp</code>)</p>
</li>
<li>
<p><code>boost/qvm/gen/sw4.hpp</code> (included by <code>boost/qvm/swizzle4.hpp</code>)</p>
</li>
</ul>
</div>
</li>
</ul>
</div>
<div class="paragraph">
<p>Any such generated headers must be included before the corresponding generic header file is included. For example, if one creates a header <code>boost/qvm/gen/m5.hpp</code>, it must be included before <code>boost/qvm/mat_operations.hpp</code> in included. However, the generic headers (<code>boost/qvm/mat_operations.hpp</code>, <code>boost/qvm/vec_operations.hpp</code>, <code>boost/qvm/vec_mat_operations.hpp</code> and <code>boost/qvm/swizzle.hpp</code>) already include the generated headers from the list above, so the generated headers don&#8217;t need to be included manually.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
headers under <code>boost/qvm/gen</code> are not part of the public interface of QVM. For example, <code>boost/qvm/gen/mat_operations2.hpp</code> should not be included directly; <code>#include &lt;boost/qvm/mat_operations2.hpp&gt;</code> instead.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_known_quirks_and_issues">Known Quirks and Issues</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="_capturing_view_proxies_with_auto">Capturing View Proxies with <code>auto</code></h3>
<div class="paragraph">
<p>By design, <a href="#view_proxy">view proxies</a> must not return temporary objects. They return reference to an argument they take by (<code>const</code>) reference, cast to reference of unspecified type that is not copyable. Because of this, the return value of a view proxy can not be captured by value with <code>auto</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">auto</span> <span class="n">tr</span> <span class="o">=</span> <span class="n">transposed</span><span class="p">(</span><span class="n">m</span><span class="p">);</span> <span class="c1">//Error: the return type of transposed can not be copied.</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The correct use of auto with view proxies is:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">auto</span> <span class="o">&amp;</span> <span class="n">tr</span> <span class="o">=</span> <span class="n">transposed</span><span class="p">(</span><span class="n">m</span><span class="p">);</span></code></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Many view proxies are not read-only, that is, they&#8217;re lvalues; changes made on the view proxy operate on the original object. This is another reason why they can not be captured by value with  <code>auto</code>.
</td>
</tr>
</table>
</div>
<hr>
</div>
<div class="sect2">
<h3 id="_binding_qvm_overloads_from_an_unrelated_namespace">Binding QVM Overloads From an Unrelated Namespace</h3>
<div class="paragraph">
<p>The operator overloads in namespace <code>boost::qvm</code> are designed to work with user-defined types. Typically it is sufficient to make these operators available in the namespace where the operator is used, by <code>using namespace boost::qvm</code>. A problem arises if the scope that uses the operator is not controlled by the user. For example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">ns1</span> <span class="p">{</span>

  <span class="k">struct</span> <span class="nc">float2</span> <span class="p">{</span> <span class="kt">float</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">;</span> <span class="p">};</span>

<span class="p">}</span>

<span class="k">namespace</span> <span class="n">ns2</span> <span class="p">{</span>

  <span class="k">using</span> <span class="k">namespace</span> <span class="n">boost</span><span class="o">::</span><span class="n">qvm</span><span class="p">;</span>

  <span class="kt">void</span> <span class="n">f</span><span class="p">()</span> <span class="p">{</span>
    <span class="n">ns1</span><span class="o">::</span><span class="n">float2</span> <span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">;</span>
    <span class="n">a</span><span class="o">==</span><span class="n">b</span><span class="p">;</span> <span class="c1">//OK</span>
    <span class="n">ns1</span><span class="o">::</span><span class="n">float2</span> <span class="n">arr1</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="n">arr2</span><span class="p">[</span><span class="mi">2</span><span class="p">];</span>
    <span class="n">std</span><span class="o">::</span><span class="n">equal</span><span class="p">(</span><span class="n">arr1</span><span class="p">,</span><span class="n">arr1</span><span class="o">+</span><span class="mi">2</span><span class="p">,</span><span class="n">arr2</span><span class="p">);</span> <span class="c1">//Error: operator== is inaccessible from namespace std</span>
  <span class="p">}</span>

<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>In the <code>std::equal</code> expression above, even though <code>boost::qvm::operator==</code> is made visible in namespace <code>ns2</code> by <code>using namespace boost::qvm</code>, the call originates from namespace <code>std</code>. In this case the compiler can&#8217;t bind <code>boost::qvm::operator==</code> because only namespace <code>ns1</code> is visible through ADL, and it does not contain a suitable declaration. The solution is to declare <code>operator==</code> in namespace ns1, which can be done like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight nowrap"><code data-lang="c++"><span class="k">namespace</span> <span class="n">ns1</span> <span class="p">{</span>

  <span class="k">using</span> <span class="n">boost</span><span class="o">::</span><span class="n">qvm</span><span class="o">::</span><span class="k">operator</span><span class="o">==</span><span class="p">;</span>

<span class="p">}</span></code></pre>
</div>
</div>
<hr>
</div>
<div class="sect2">
<h3 id="_link_errors_when_calling_math_functions_with_int_arguments">Link Errors When Calling Math Functions with <code>int</code> Arguments</h3>
<div class="paragraph">
<p>QVM does not call standard math functions (e.g. sin, cos, etc.) directly. Instead, it calls function templates declared in <code>boost/qvm/math.hpp</code> in namespace <code>boost::qvm</code>. This allows the user to specialize these templates for user-defined scalar types.</p>
</div>
<div class="paragraph">
<p>QVM itself defines specializations of the math function templates only for <code>float</code> and <code>double</code>, but it does not provide generic definitions. This is done to protect the user from unintentionally writing code that binds standard math functions that take <code>double</code> when passing arguments of lesser types, which would be suboptimal.</p>
</div>
<div class="paragraph">
<p>Because of this, a call to e.g. <code><a href="#rot_mat">rot_mat</a>(axis,1)</code> will compile successfully but fail to link, since it calls e.g. <code>boost::qvm::sin&lt;int&gt;</code>, which is undefined. Because rotations by integer number of radians are rarely needed, in QVM there is no protection against such errors. In such cases the solution is to use <code>rot_mat(axis,1.0f)</code> instead.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_qa">Q&amp;A</h2>
<div class="sectionbody">
<div class="qlist qanda">
<ol>
<li>
<p><em>What is the motivation behind QVM? Why not just use uBLAS/Eigen/CML/GLM/etc?</em></p>
<p>The primary domain of QVM is realtime graphics and simulation applications, so it is not a complete linear algebra library. While (naturally) there is some overlap with such libraries, QVM puts the emphasis on 2, 3 and 4 dimensional zero-overhead operations (hence domain-specific features like Swizzling).</p>
</li>
<li>
<p><em>How does the <code>qvm::<a href="#vec">vec</a></code> (or <code>qvm::<a href="#mat">mat</a></code>, or <code>qvm::<a href="#quat">quat</a></code>) template compare to vector types from other libraries?</em></p>
<p>The <code>qvm::vec</code> template is not in any way central to the vector operations defined by QVM. The operations are designed to work with any user-defined vector type or with 3rd-party vector types (e.g. <code>D3DVECTOR</code>), while the <code>qvm::vec</code> template is simply a default return type for expressions that use arguments of different types that would be incompatible outside of QVM. For example, if the <a href="#deduce_mat2"><code>deduce_mat2</code></a> hasn&#8217;t been specialized, calling <a href="#cross"><code>cross</code></a> with a user-defined type <code>vec3</code> and a user-defined type <code>float3</code> returns a <code>qvm::vec</code>.</p>
</li>
<li>
<p><em>Why doesn&#8217;t QVM use [] or () to access vector and matrix elements?</em></p>
<p>Because it&#8217;s designed to work with user-defined types, and the C&#43;&#43; standard requires these operators to be members. Of course if a user-defined type defines <code>operator[]</code> or <code>operator()</code> they are available for use with other QVM functions, but QVM defines its own mechanisms for <a href="#quat_access">accessing quaternion elements</a>, <a href="#vec_access">accessing vector elements</a> (as well as <a href="#swizzling">swizzling</a>), and <a href="#mat_access">accessing matrix elements</a>.</p>
</li>
</ol>
</div>
<hr>
<div class="paragraph text-right">
<p>Documentation rendered by <a href="https://asciidoctor.org/">Asciidoctor</a> with <a href="https://github.com/zajo/asciidoctor_skin">these customizations</a>.</p>
</div>
<div class="paragraph text-right">
<p>Copyright 2008-2023 Emil Dotchevski and Reverge Studios, Inc.</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
</div>
</div>
</body>
</html>