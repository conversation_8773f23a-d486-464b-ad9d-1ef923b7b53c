<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Backend Requirements</title>
<link rel="stylesheet" href="../../multiprecision.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Multiprecision">
<link rel="up" href="../ref.html" title="Reference">
<link rel="prev" href="internals.html" title="Internal Support Code">
<link rel="next" href="headers.html" title="Header File Structure">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="internals.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ref.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="headers.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_multiprecision.ref.backendconc"></a><a class="link" href="backendconc.html" title="Backend Requirements">Backend Requirements</a>
</h3></div></div></div>
<p>
        The requirements on the <code class="computeroutput"><span class="identifier">Backend</span></code>
        template argument to <code class="computeroutput"><span class="identifier">number</span></code>
        are split up into sections: compulsory and optional.
      </p>
<p>
        Compulsory requirements have no default implementation in the library, therefore
        if the feature they implement is to be supported at all, then they must be
        implemented by the backend.
      </p>
<p>
        Optional requirements have default implementations that are called if the
        backend doesn't provide its own. Typically the backend will implement these
        to improve performance.
      </p>
<p>
        In the following tables, type B is the <code class="computeroutput"><span class="identifier">Backend</span></code>
        template argument to <code class="computeroutput"><span class="identifier">number</span></code>,
        <code class="computeroutput"><span class="identifier">b</span></code> and <code class="computeroutput"><span class="identifier">b2</span></code>
        are a variables of type B, <code class="computeroutput"><span class="identifier">pb</span></code>
        is a variable of type B*, <code class="computeroutput"><span class="identifier">cb</span></code>,
        <code class="computeroutput"><span class="identifier">cb2</span></code> and <code class="computeroutput"><span class="identifier">cb3</span></code>
        are constant variables of type <code class="computeroutput"><span class="keyword">const</span>
        <span class="identifier">B</span></code>, <code class="computeroutput"><span class="identifier">rb</span></code>
        is a variable of type <code class="computeroutput"><span class="identifier">B</span><span class="special">&amp;&amp;</span></code>,
        <code class="computeroutput"><span class="identifier">a</span></code> and <code class="computeroutput"><span class="identifier">a2</span></code>
        are variables of Arithmetic type, <code class="computeroutput"><span class="identifier">s</span></code>
        is a variable of type <code class="computeroutput"><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span></code>, <code class="computeroutput"><span class="identifier">ui</span></code> is a variable of type <code class="computeroutput"><span class="keyword">unsigned</span></code>, <code class="computeroutput"><span class="identifier">bb</span></code>
        is a variable of type <code class="computeroutput"><span class="keyword">bool</span></code>,
        <code class="computeroutput"><span class="identifier">pa</span></code> is a variable of type
        pointer-to-arithmetic-type, <code class="computeroutput"><span class="identifier">exp</span></code>
        is a variable of type <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">exp_type</span></code>, <code class="computeroutput"><span class="identifier">pexp</span></code>
        is a variable of type <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">exp_type</span><span class="special">*</span></code>,
        <code class="computeroutput"><span class="identifier">i</span></code> is a variable of type
        <code class="computeroutput"><span class="keyword">int</span></code>, <code class="computeroutput"><span class="identifier">pi</span></code>
        pointer to a variable of type <code class="computeroutput"><span class="keyword">int</span></code>,
        B2 is another type that meets these requirements, b2 is a variable of type
        B2, <code class="computeroutput"><span class="identifier">ss</span></code> is variable of type
        <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span></code> and <code class="computeroutput"><span class="identifier">ff</span></code>
        is a variable of type <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">ios_base</span><span class="special">::</span><span class="identifier">fmtflags</span></code>.
      </p>
<div class="table">
<a name="boost_multiprecision.ref.backendconc.compulsory_requirements_on_the_b"></a><p class="title"><b>Table 1.8. Compulsory Requirements on the Backend type.</b></p>
<div class="table-contents"><table class="table" summary="Compulsory Requirements on the Backend type.">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Expression
                </p>
              </th>
<th>
                <p>
                  Return Type
                </p>
              </th>
<th>
                <p>
                  Comments
                </p>
              </th>
<th>
                <p>
                  Throws
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple</span><span class="special">&lt;</span><span class="identifier">type</span><span class="special">-</span><span class="identifier">list</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  A list of signed integral types that can be assigned to type B.
                  The types shall be listed in order of size, smallest first, and
                  shall terminate in the type that is <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">intmax_t</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple</span><span class="special">&lt;</span><span class="identifier">type</span><span class="special">-</span><span class="identifier">list</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  A list of unsigned integral types that can be assigned to type
                  B. The types shall be listed in order of size, smallest first,
                  and shall terminate in the type that is <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">uintmax_t</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple</span><span class="special">&lt;</span><span class="identifier">type</span><span class="special">-</span><span class="identifier">list</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  A list of floating-point types that can be assigned to type B.The
                  types shall be listed in order of size, smallest first, and shall
                  terminate in type <code class="computeroutput"><span class="keyword">long</span> <span class="keyword">double</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">exponent_type</span></code>
                </p>
              </td>
<td>
                <p>
                  A signed integral type.
                </p>
              </td>
<td>
                <p>
                  The type of the exponent of type B. This type is required only
                  for floating-point types.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">()</span></code>
                </p>
              </td>
<td>
              </td>
<td>
                <p>
                  Default constructor.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
              </td>
<td>
                <p>
                  Copy Constructor.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                  <span class="identifier">b</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">&amp;</span></code>
                </p>
              </td>
<td>
                <p>
                  Assignment operator.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                  <span class="identifier">a</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">&amp;</span></code>
                </p>
              </td>
<td>
                <p>
                  Assignment from an Arithmetic type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                  <span class="identifier">s</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">&amp;</span></code>
                </p>
              </td>
<td>
                <p>
                  Assignment from a string.
                </p>
              </td>
<td>
                <p>
                  Throws a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">runtime_error</span></code> if the string could
                  not be interpreted as a valid number.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">b</span><span class="special">.</span><span class="identifier">swap</span><span class="special">(</span><span class="identifier">b</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Swaps the contents of its arguments.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">noexcept</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">cb</span><span class="special">.</span><span class="identifier">str</span><span class="special">(</span><span class="identifier">ss</span><span class="special">,</span>
                  <span class="identifier">ff</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns the string representation of <code class="computeroutput"><span class="identifier">b</span></code>
                  with <code class="computeroutput"><span class="identifier">ss</span></code> digits
                  and formatted according to the flags set in <code class="computeroutput"><span class="identifier">ff</span></code>.
                  If <code class="computeroutput"><span class="identifier">ss</span></code> is zero,
                  then returns as many digits as are required to reconstruct the
                  original value.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">b</span><span class="special">.</span><span class="identifier">negate</span><span class="special">()</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Negates <code class="computeroutput"><span class="identifier">b</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">cb</span><span class="special">.</span><span class="identifier">compare</span><span class="special">(</span><span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">int</span></code>
                </p>
              </td>
<td>
                <p>
                  Compares <code class="computeroutput"><span class="identifier">cb</span></code> and
                  <code class="computeroutput"><span class="identifier">cb2</span></code>, returns a
                  value less than zero if <code class="computeroutput"><span class="identifier">cb</span>
                  <span class="special">&lt;</span> <span class="identifier">cb2</span></code>,
                  a value greater than zero if <code class="computeroutput"><span class="identifier">cb</span>
                  <span class="special">&gt;</span> <span class="identifier">cb2</span></code>
                  and zero if <code class="computeroutput"><span class="identifier">cb</span> <span class="special">==</span> <span class="identifier">cb2</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">noexcept</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">cb</span><span class="special">.</span><span class="identifier">compare</span><span class="special">(</span><span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">int</span></code>
                </p>
              </td>
<td>
                <p>
                  Compares <code class="computeroutput"><span class="identifier">cb</span></code> and
                  <code class="computeroutput"><span class="identifier">a</span></code>, returns a value
                  less than zero if <code class="computeroutput"><span class="identifier">cb</span>
                  <span class="special">&lt;</span> <span class="identifier">a</span></code>,
                  a value greater than zero if <code class="computeroutput"><span class="identifier">cb</span>
                  <span class="special">&gt;</span> <span class="identifier">a</span></code>
                  and zero if <code class="computeroutput"><span class="identifier">cb</span> <span class="special">==</span> <span class="identifier">a</span></code>.
                  The type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Adds <code class="computeroutput"><span class="identifier">cb</span></code> to <code class="computeroutput"><span class="identifier">b</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Subtracts <code class="computeroutput"><span class="identifier">cb</span></code> from
                  <code class="computeroutput"><span class="identifier">b</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">b</span></code> by
                  <code class="computeroutput"><span class="identifier">cb</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_divide</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Divides <code class="computeroutput"><span class="identifier">b</span></code> by <code class="computeroutput"><span class="identifier">cb</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if cb has the
                  value zero, and <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">number</span><span class="special">&lt;</span><span class="identifier">B</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">has_infinity</span> <span class="special">==</span>
                  <span class="keyword">false</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_modulus</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">%=</span>
                  <span class="identifier">cb</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if cb has the
                  value zero.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_and</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">&amp;=</span>
                  <span class="identifier">cb</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_or</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">|=</span>
                  <span class="identifier">cb</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_xor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">^=</span>
                  <span class="identifier">cb</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_complement</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes the ones-complement of <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>,
                  only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an integer type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_left_shift</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">ui</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">&lt;&lt;=</span>
                  <span class="identifier">ui</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_right_shift</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">ui</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">&gt;&gt;=</span>
                  <span class="identifier">ui</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_convert_to</span><span class="special">(</span><span class="identifier">pa</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Converts <code class="computeroutput"><span class="identifier">cb</span></code> to
                  the type of <code class="computeroutput"><span class="special">*</span><span class="identifier">pa</span></code>
                  and store the result in <code class="computeroutput"><span class="special">*</span><span class="identifier">pa</span></code>. Type <code class="computeroutput"><span class="identifier">B</span></code>
                  shall support conversion to at least types <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">intmax_t</span></code>,
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">uintmax_t</span></code> and <code class="computeroutput"><span class="keyword">long</span>
                  <span class="keyword">long</span></code>. Conversion to other
                  arithmetic types can then be synthesised using other operations.
                  Conversions to other types are entirely optional.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_frexp</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">pexp</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Stores values in <code class="computeroutput"><span class="identifier">b</span></code>
                  and <code class="computeroutput"><span class="special">*</span><span class="identifier">pexp</span></code>
                  such that the value of <code class="computeroutput"><span class="identifier">cb</span></code>
                  is b * 2<sup>*pexp</sup>, only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is a floating-point type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_ldexp</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">exp</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Stores a value in <code class="computeroutput"><span class="identifier">b</span></code>
                  that is cb * 2<sup>exp</sup>, only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is a floating-point type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_frexp</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">pi</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Stores values in <code class="computeroutput"><span class="identifier">b</span></code>
                  and <code class="computeroutput"><span class="special">*</span><span class="identifier">pi</span></code>
                  such that the value of <code class="computeroutput"><span class="identifier">cb</span></code>
                  is b * 2<sup>*pi</sup>, only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is a floating-point type.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">runtime_error</span></code> if the exponent
                  of cb is too large to be stored in an <code class="computeroutput"><span class="keyword">int</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_ldexp</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">i</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Stores a value in <code class="computeroutput"><span class="identifier">b</span></code>
                  that is cb * 2<sup>i</sup>, only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is a floating-point type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_floor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Stores the floor of <code class="computeroutput"><span class="identifier">cb</span></code>
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is a floating-point
                  type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_ceil</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Stores the ceiling of <code class="computeroutput"><span class="identifier">cb</span></code>
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is a floating-point
                  type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_sqrt</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Stores the square root of <code class="computeroutput"><span class="identifier">cb</span></code>
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is a floating-point
                  type.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">multiprecision</span><span class="special">::</span><span class="identifier">number_category</span><span class="special">&lt;</span><span class="identifier">B</span><span class="special">&gt;::</span><span class="identifier">type</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">integral_constant</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">,</span> <span class="identifier">N</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">N</span></code> is one of the
                  values <code class="computeroutput"><span class="identifier">number_kind_integer</span></code>,
                  <code class="computeroutput"><span class="identifier">number_kind_floating_point</span></code>,
                  <code class="computeroutput"><span class="identifier">number_kind_complex</span></code>,
                  <code class="computeroutput"><span class="identifier">number_kind_rational</span></code>
                  or <code class="computeroutput"><span class="identifier">number_kind_fixed_point</span></code>.
                  Defaults to <code class="computeroutput"><span class="identifier">number_kind_floating_point</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_conj</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the complex
                  conjugate of <code class="computeroutput"><span class="identifier">cb</span></code>.
                  Required for complex types only - other types have a sensible default.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_proj</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the Riemann
                  projection of <code class="computeroutput"><span class="identifier">cb</span></code>.
                  Required for complex types only - other types have a sensible default.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_real</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the real
                  part of <code class="computeroutput"><span class="identifier">cb</span></code>. Required
                  for complex types only - other types have a sensible default.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_imag</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the imaginary
                  of <code class="computeroutput"><span class="identifier">cb</span></code>. Required
                  for complex types only - other types have a sensible default.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_set_real</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets the real part of <code class="computeroutput"><span class="identifier">b</span></code>
                  to <code class="computeroutput"><span class="identifier">cb</span></code>. Required
                  for complex types only - other types have a sensible default.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_set_imag</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets the imaginary part of <code class="computeroutput"><span class="identifier">b</span></code>
                  to <code class="computeroutput"><span class="identifier">cb</span></code>. Required
                  for complex types only - other types have a sensible default.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="boost_multiprecision.ref.backendconc.optional_requirements_on_the_bac"></a><p class="title"><b>Table 1.9. Optional Requirements on the Backend Type</b></p>
<div class="table-contents"><table class="table" summary="Optional Requirements on the Backend Type">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Expression
                </p>
              </th>
<th>
                <p>
                  Returns
                </p>
              </th>
<th>
                <p>
                  Comments
                </p>
              </th>
<th>
                <p>
                  Throws
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <span class="emphasis"><em>Construct and assign:</em></span>
                </p>
              </td>
<td class="auto-generated"> </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">(</span><span class="identifier">rb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span></code>
                </p>
              </td>
<td>
                <p>
                  Move constructor. Afterwards variable <code class="computeroutput"><span class="identifier">rb</span></code>
                  shall be in sane state, albeit with unspecified value. Only destruction
                  and assignment to the moved-from variable <code class="computeroutput"><span class="identifier">rb</span></code>
                  need be supported after the operation.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">noexcept</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                  <span class="identifier">rb</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">&amp;</span></code>
                </p>
              </td>
<td>
                <p>
                  Move-assign. Afterwards variable <code class="computeroutput"><span class="identifier">rb</span></code>
                  shall be in sane state, albeit with unspecified value. Only destruction
                  and assignment to the moved-from variable <code class="computeroutput"><span class="identifier">rb</span></code>
                  need be supported after the operation.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">noexcept</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span></code>
                </p>
              </td>
<td>
                <p>
                  Direct construction from an arithmetic type. The type of <code class="computeroutput"><span class="identifier">a</span></code> shall be listed in one of the
                  type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>, <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>
                  or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  this operation is simulated using default-construction followed
                  by assignment.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">(</span><span class="identifier">b2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span></code>
                </p>
              </td>
<td>
                <p>
                  Copy constructor from a different back-end type. When not provided,
                  a generic interconversion routine is used. This constructor may
                  be <code class="computeroutput"><span class="keyword">explicit</span></code> if the
                  corresponding frontend constructor should also be <code class="computeroutput"><span class="keyword">explicit</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                  <span class="identifier">b2</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">b</span><span class="special">&amp;</span></code>
                </p>
              </td>
<td>
                <p>
                  Assignment operator from a different back-end type. When not provided,
                  a generic interconversion routine is used.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">assign_components</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Assigns to <code class="computeroutput"><span class="identifier">b</span></code> the
                  two components in the following arguments. Only applies to rational
                  and complex number types. When not provided, arithmetic operations
                  are used to synthesise the result from the two values.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">assign_components</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">b2</span><span class="special">,</span> <span class="identifier">b2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Assigns to <code class="computeroutput"><span class="identifier">b</span></code> the
                  two components in the following arguments. Only applies to rational
                  and complex number types. When not provided, arithmetic operations
                  are used to synthesise the result from the two values.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="emphasis"><em>Comparisons:</em></span>
                </p>
              </td>
<td class="auto-generated"> </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_eq</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">cb</span></code> and <code class="computeroutput"><span class="identifier">cb2</span></code>
                  are equal in value. When not provided, the default implementation
                  returns <code class="computeroutput"><span class="identifier">cb</span><span class="special">.</span><span class="identifier">compare</span><span class="special">(</span><span class="identifier">cb2</span><span class="special">)</span>
                  <span class="special">==</span> <span class="number">0</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">noexcept</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_eq</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">cb</span></code> and <code class="computeroutput"><span class="identifier">a</span></code>
                  are equal in value. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  return the equivalent of <code class="computeroutput"><span class="identifier">eval_eq</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_eq</span><span class="special">(</span><span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">cb</span></code> and <code class="computeroutput"><span class="identifier">a</span></code>
                  are equal in value. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default version returns <code class="computeroutput"><span class="identifier">eval_eq</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_lt</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">cb</span></code> is less than <code class="computeroutput"><span class="identifier">cb2</span></code> in value. When not provided,
                  the default implementation returns <code class="computeroutput"><span class="identifier">cb</span><span class="special">.</span><span class="identifier">compare</span><span class="special">(</span><span class="identifier">cb2</span><span class="special">)</span> <span class="special">&lt;</span>
                  <span class="number">0</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">noexcept</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_lt</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">cb</span></code> is less than <code class="computeroutput"><span class="identifier">a</span></code> in value. The type of <code class="computeroutput"><span class="identifier">a</span></code> shall be listed in one of the
                  type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>, <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>
                  or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default implementation returns <code class="computeroutput"><span class="identifier">eval_lt</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_lt</span><span class="special">(</span><span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> is less than <code class="computeroutput"><span class="identifier">cb</span></code> in value. The type of <code class="computeroutput"><span class="identifier">a</span></code> shall be listed in one of the
                  type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>, <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>
                  or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default implementation returns <code class="computeroutput"><span class="identifier">eval_gt</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_gt</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">cb</span></code> is greater than <code class="computeroutput"><span class="identifier">cb2</span></code> in value. When not provided,
                  the default implementation returns <code class="computeroutput"><span class="identifier">cb</span><span class="special">.</span><span class="identifier">compare</span><span class="special">(</span><span class="identifier">cb2</span><span class="special">)</span> <span class="special">&gt;</span>
                  <span class="number">0</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">noexcept</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_gt</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">cb</span></code> is greater than <code class="computeroutput"><span class="identifier">a</span></code> in value. The type of <code class="computeroutput"><span class="identifier">a</span></code> shall be listed in one of the
                  type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>, <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>
                  or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default implementation returns <code class="computeroutput"><span class="identifier">eval_gt</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_gt</span><span class="special">(</span><span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> is greater than <code class="computeroutput"><span class="identifier">cb</span></code> in value. The type of <code class="computeroutput"><span class="identifier">a</span></code> shall be listed in one of the
                  type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>, <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>
                  or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default implementation returns <code class="computeroutput"><span class="identifier">eval_lt</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_is_zero</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns <code class="computeroutput"><span class="keyword">true</span></code> if <code class="computeroutput"><span class="identifier">cb</span></code> is zero, otherwise <code class="computeroutput"><span class="keyword">false</span></code>. The default version of this
                  function returns <code class="computeroutput"><span class="identifier">cb</span><span class="special">.</span><span class="identifier">compare</span><span class="special">(</span><span class="identifier">ui_type</span><span class="special">(</span><span class="number">0</span><span class="special">))</span> <span class="special">==</span>
                  <span class="number">0</span></code>, where <code class="computeroutput"><span class="identifier">ui_type</span></code>
                  is <code class="computeroutput"><span class="identifier">ui_type</span></code> is
                  <code class="computeroutput"><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple_element</span><span class="special">&lt;</span><span class="number">0</span><span class="special">,</span> <span class="keyword">typename</span>
                  <span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span><span class="special">&gt;::</span><span class="identifier">type</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_get_sign</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">int</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns a value &lt; zero if <code class="computeroutput"><span class="identifier">cb</span></code>
                  is negative, a value &gt; zero if <code class="computeroutput"><span class="identifier">cb</span></code>
                  is positive, and zero if <code class="computeroutput"><span class="identifier">cb</span></code>
                  is zero. The default version of this function returns <code class="computeroutput"><span class="identifier">cb</span><span class="special">.</span><span class="identifier">compare</span><span class="special">(</span><span class="identifier">ui_type</span><span class="special">(</span><span class="number">0</span><span class="special">))</span></code>,
                  where <code class="computeroutput"><span class="identifier">ui_type</span></code> is
                  <code class="computeroutput"><span class="identifier">ui_type</span></code> is <code class="computeroutput"><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple_element</span><span class="special">&lt;</span><span class="number">0</span><span class="special">,</span> <span class="keyword">typename</span>
                  <span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span><span class="special">&gt;::</span><span class="identifier">type</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="emphasis"><em>Basic arithmetic:</em></span>
                </p>
              </td>
<td class="auto-generated"> </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Adds <code class="computeroutput"><span class="identifier">a</span></code> to <code class="computeroutput"><span class="identifier">b</span></code>. The type of <code class="computeroutput"><span class="identifier">a</span></code> shall be listed in one of the
                  type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>, <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>
                  or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default version calls <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Add <code class="computeroutput"><span class="identifier">cb</span></code> to <code class="computeroutput"><span class="identifier">cb2</span></code> and stores the result in
                  <code class="computeroutput"><span class="identifier">b</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Add <code class="computeroutput"><span class="identifier">cb</span></code> to <code class="computeroutput"><span class="identifier">a</span></code> and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>. The type of <code class="computeroutput"><span class="identifier">a</span></code> shall be listed in one of the
                  type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>, <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>
                  or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Add <code class="computeroutput"><span class="identifier">a</span></code> to <code class="computeroutput"><span class="identifier">cb</span></code> and stores the result in
                  <code class="computeroutput"><span class="identifier">b</span></code>. The type of
                  <code class="computeroutput"><span class="identifier">a</span></code> shall be listed
                  in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Subtracts <code class="computeroutput"><span class="identifier">a</span></code> from
                  <code class="computeroutput"><span class="identifier">b</span></code>. The type of
                  <code class="computeroutput"><span class="identifier">a</span></code> shall be listed
                  in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default version calls <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Subtracts <code class="computeroutput"><span class="identifier">cb2</span></code> from
                  <code class="computeroutput"><span class="identifier">cb</span></code> and stores the
                  result in <code class="computeroutput"><span class="identifier">b</span></code>. When
                  not provided, does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Subtracts <code class="computeroutput"><span class="identifier">a</span></code> from
                  <code class="computeroutput"><span class="identifier">cb</span></code> and stores the
                  result in <code class="computeroutput"><span class="identifier">b</span></code>. The
                  type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Subtracts <code class="computeroutput"><span class="identifier">cb</span></code> from
                  <code class="computeroutput"><span class="identifier">a</span></code> and stores the
                  result in <code class="computeroutput"><span class="identifier">b</span></code>. The
                  type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">);</span> <span class="identifier">b</span><span class="special">.</span><span class="identifier">negate</span><span class="special">();</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">b</span></code> by
                  <code class="computeroutput"><span class="identifier">a</span></code>. The type of
                  <code class="computeroutput"><span class="identifier">a</span></code> shall be listed
                  in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default version calls <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">cb</span></code> by
                  <code class="computeroutput"><span class="identifier">cb2</span></code> and stores
                  the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">cb</span></code> by
                  <code class="computeroutput"><span class="identifier">a</span></code> and stores the
                  result in <code class="computeroutput"><span class="identifier">b</span></code>. The
                  type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">a</span></code> by
                  <code class="computeroutput"><span class="identifier">cb</span></code> and stores the
                  result in <code class="computeroutput"><span class="identifier">b</span></code>. The
                  type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">cb</span></code> by
                  <code class="computeroutput"><span class="identifier">cb2</span></code> and adds the
                  result to <code class="computeroutput"><span class="identifier">b</span></code>. When
                  not provided does the equivalent of creating a temporary <code class="computeroutput"><span class="identifier">B</span> <span class="identifier">t</span></code>
                  and <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">t</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code> followed by <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">t</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">a</span></code> by
                  <code class="computeroutput"><span class="identifier">cb</span></code> and adds the
                  result to <code class="computeroutput"><span class="identifier">b</span></code>. The
                  type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided
                  does the equivalent of creating a temporary <code class="computeroutput"><span class="identifier">B</span>
                  <span class="identifier">t</span></code> and <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">t</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code> followed by <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">t</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">a</span></code> by
                  <code class="computeroutput"><span class="identifier">cb</span></code> and adds the
                  result to <code class="computeroutput"><span class="identifier">b</span></code>. The
                  type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_multiply_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">cb</span></code> by
                  <code class="computeroutput"><span class="identifier">cb2</span></code> and subtracts
                  the result from <code class="computeroutput"><span class="identifier">b</span></code>.
                  When not provided does the equivalent of creating a temporary
                  <code class="computeroutput"><span class="identifier">B</span> <span class="identifier">t</span></code>
                  and <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">t</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code> followed by <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">t</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">a</span></code> by
                  <code class="computeroutput"><span class="identifier">cb</span></code> and subtracts
                  the result from <code class="computeroutput"><span class="identifier">b</span></code>.
                  The type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided
                  does the equivalent of creating a temporary <code class="computeroutput"><span class="identifier">B</span>
                  <span class="identifier">t</span></code> and <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">t</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code> followed by <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">t</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">a</span></code> by
                  <code class="computeroutput"><span class="identifier">cb</span></code> and subtracts
                  the result from <code class="computeroutput"><span class="identifier">b</span></code>.
                  The type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_multiply_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">,</span> <span class="identifier">cb3</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">cb</span></code> by
                  <code class="computeroutput"><span class="identifier">cb2</span></code> and adds the
                  result to <code class="computeroutput"><span class="identifier">cb3</span></code> storing
                  the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  When not provided does the equivalent of <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code> followed by <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb3</span><span class="special">)</span></code>. For brevity, only a version showing
                  all arguments of type <code class="computeroutput"><span class="identifier">B</span></code>
                  is shown here, but you can replace up to any 2 of <code class="computeroutput"><span class="identifier">cb</span></code>, <code class="computeroutput"><span class="identifier">cb2</span></code>
                  and <code class="computeroutput"><span class="identifier">cb3</span></code> with any
                  type listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_multiply_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">,</span> <span class="identifier">cb3</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Multiplies <code class="computeroutput"><span class="identifier">cb</span></code> by
                  <code class="computeroutput"><span class="identifier">cb2</span></code> and subtracts
                  from the result <code class="computeroutput"><span class="identifier">cb3</span></code>
                  storing the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  When not provided does the equivalent of <code class="computeroutput"><span class="identifier">eval_multiply</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code> followed by <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb3</span><span class="special">)</span></code>. For brevity, only a version showing
                  all arguments of type <code class="computeroutput"><span class="identifier">B</span></code>
                  is shown here, but you can replace up to any 2 of <code class="computeroutput"><span class="identifier">cb</span></code>, <code class="computeroutput"><span class="identifier">cb2</span></code>
                  and <code class="computeroutput"><span class="identifier">cb3</span></code> with any
                  type listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_divide</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Divides <code class="computeroutput"><span class="identifier">b</span></code> by <code class="computeroutput"><span class="identifier">a</span></code>. The type of <code class="computeroutput"><span class="identifier">a</span></code> shall be listed in one of the
                  type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>, <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>
                  or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default version calls <code class="computeroutput"><span class="identifier">eval_divide</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> has the value zero, and <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">number</span><span class="special">&lt;</span><span class="identifier">B</span><span class="special">&gt;</span>
                  <span class="special">&gt;::</span><span class="identifier">has_infinity</span>
                  <span class="special">==</span> <span class="keyword">false</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_divide</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Divides <code class="computeroutput"><span class="identifier">cb</span></code> by
                  <code class="computeroutput"><span class="identifier">cb2</span></code> and stores
                  the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_divide</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if <code class="computeroutput"><span class="identifier">cb2</span></code> has the value zero, and
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">number</span><span class="special">&lt;</span><span class="identifier">B</span><span class="special">&gt;</span>
                  <span class="special">&gt;::</span><span class="identifier">has_infinity</span>
                  <span class="special">==</span> <span class="keyword">false</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_divide</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Divides <code class="computeroutput"><span class="identifier">cb</span></code> by
                  <code class="computeroutput"><span class="identifier">a</span></code> and stores the
                  result in <code class="computeroutput"><span class="identifier">b</span></code>. The
                  type of <code class="computeroutput"><span class="identifier">a</span></code> shall
                  be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_divide</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> has the value zero, and <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">number</span><span class="special">&lt;</span><span class="identifier">B</span><span class="special">&gt;</span>
                  <span class="special">&gt;::</span><span class="identifier">has_infinity</span>
                  <span class="special">==</span> <span class="keyword">false</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_divide</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Divides <code class="computeroutput"><span class="identifier">a</span></code> by <code class="computeroutput"><span class="identifier">cb</span></code> and stores the result in
                  <code class="computeroutput"><span class="identifier">b</span></code>. The type of
                  <code class="computeroutput"><span class="identifier">a</span></code> shall be listed
                  in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_divide</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">),</span> <span class="identifier">cb</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if cb has the
                  value zero, and <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">number</span><span class="special">&lt;</span><span class="identifier">B</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">has_infinity</span> <span class="special">==</span>
                  <span class="keyword">false</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_increment</span><span class="special">(</span><span class="identifier">b</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  void
                </p>
              </td>
<td>
                <p>
                  Increments the value of <code class="computeroutput"><span class="identifier">b</span></code>
                  by one. When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">eval_add</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">ui_type</span><span class="special">&gt;(</span><span class="number">1u</span><span class="special">))</span></code>.
                  Where <code class="computeroutput"><span class="identifier">ui_type</span></code> is
                  <code class="computeroutput"><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple_element</span><span class="special">&lt;</span><span class="number">0</span><span class="special">,</span> <span class="keyword">typename</span>
                  <span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span><span class="special">&gt;::</span><span class="identifier">type</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_decrement</span><span class="special">(</span><span class="identifier">b</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  void
                </p>
              </td>
<td>
                <p>
                  Decrements the value of <code class="computeroutput"><span class="identifier">b</span></code>
                  by one. When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">eval_subtract</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">ui_type</span><span class="special">&gt;(</span><span class="number">1u</span><span class="special">))</span></code>.
                  Where <code class="computeroutput"><span class="identifier">ui_type</span></code> is
                  <code class="computeroutput"><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple_element</span><span class="special">&lt;</span><span class="number">0</span><span class="special">,</span> <span class="keyword">typename</span>
                  <span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span><span class="special">&gt;::</span><span class="identifier">type</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="emphasis"><em>Integer specific operations:</em></span>
                </p>
              </td>
<td class="auto-generated"> </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_modulus</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">%=</span>
                  <span class="identifier">cb</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default version calls <code class="computeroutput"><span class="identifier">eval_modulus</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> has the value zero.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_modulus</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">%</span>
                  <span class="identifier">cb2</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_modulus</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> has the value zero.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_modulus</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">%</span>
                  <span class="identifier">a</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_modulus</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> has the value zero.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_modulus</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">%</span>
                  <span class="identifier">a</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_modulus</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">),</span> <span class="identifier">cb</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> has the value zero.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_and</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">&amp;=</span>
                  <span class="identifier">cb</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default version calls <code class="computeroutput"><span class="identifier">eval_bitwise_and</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_and</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">&amp;</span>
                  <span class="identifier">cb2</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_bitwise_and</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_and</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">&amp;</span>
                  <span class="identifier">a</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_bitwise_and</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_and</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">&amp;</span>
                  <span class="identifier">a</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_bitwise_and</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_or</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">|=</span>
                  <span class="identifier">cb</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default version calls <code class="computeroutput"><span class="identifier">eval_bitwise_or</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_or</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">|</span>
                  <span class="identifier">cb2</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_bitwise_or</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_or</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">|</span>
                  <span class="identifier">a</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_bitwise_or</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_or</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">|</span>
                  <span class="identifier">a</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_bitwise_or</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_xor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">b</span> <span class="special">^=</span>
                  <span class="identifier">cb</span></code>, only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  the default version calls <code class="computeroutput"><span class="identifier">eval_bitwise_xor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_xor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">^</span>
                  <span class="identifier">cb2</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_bitwise_xor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_xor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">^</span>
                  <span class="identifier">a</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_bitwise_xor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bitwise_xor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">a</span> <span class="special">^</span>
                  <span class="identifier">cb</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. When not provided,
                  does the equivalent of <code class="computeroutput"><span class="identifier">eval_bitwise_xor</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_left_shift</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">ui</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">&lt;&lt;</span>
                  <span class="identifier">ui</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_left_shift</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">);</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_right_shift</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">ui</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Computes <code class="computeroutput"><span class="identifier">cb</span> <span class="special">&gt;&gt;</span>
                  <span class="identifier">ui</span></code> and stores the result
                  in <code class="computeroutput"><span class="identifier">b</span></code>, only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. When not provided, does the equivalent of <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span><span class="special">;</span> <span class="identifier">eval_right_shift</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">);</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_qr</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">,</span>
                  <span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">b2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the result
                  of <code class="computeroutput"><span class="identifier">cb</span> <span class="special">/</span>
                  <span class="identifier">cb2</span></code> and <code class="computeroutput"><span class="identifier">b2</span></code> to the result of <code class="computeroutput"><span class="identifier">cb</span> <span class="special">%</span>
                  <span class="identifier">cb2</span></code>. Only required when
                  <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The default version of this function is synthesised from
                  other operations above.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> has the value zero.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_integer_modulus</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span> <span class="identifier">ui</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">unsigned</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns the result of <code class="computeroutput"><span class="identifier">cb</span>
                  <span class="special">%</span> <span class="identifier">ui</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an integer type. The default version of this function is synthesised
                  from other operations above.
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code> if <code class="computeroutput"><span class="identifier">a</span></code> has the value zero.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_lsb</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">unsigned</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns the index of the least significant bit that is set. Only
                  required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an integer type. The default version of this function is synthesised
                  from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_msb</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">unsigned</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns the index of the most significant bit that is set. Only
                  required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an integer type. The default version of this function is synthesised
                  from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bit_test</span><span class="special">(</span><span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">ui</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">bool</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns true if <code class="computeroutput"><span class="identifier">cb</span></code>
                  has bit <code class="computeroutput"><span class="identifier">ui</span></code> set.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an integer type. The default version of this function is synthesised
                  from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bit_set</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">ui</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets the bit at index <code class="computeroutput"><span class="identifier">ui</span></code>
                  in <code class="computeroutput"><span class="identifier">b</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The default version of this function is synthesised from
                  other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bit_unset</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">ui</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Unsets the bit at index <code class="computeroutput"><span class="identifier">ui</span></code>
                  in <code class="computeroutput"><span class="identifier">b</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The default version of this function is synthesised from
                  other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_bit_flip</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">ui</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Flips the bit at index <code class="computeroutput"><span class="identifier">ui</span></code>
                  in <code class="computeroutput"><span class="identifier">b</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The default version of this function is synthesised from
                  other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_gcd</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the greatest
                  common divisor of <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">cb2</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The default version of this function is synthesised from
                  other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_lcm</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the least
                  common multiple of <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">cb2</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The default version of this function is synthesised from
                  other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_gcd</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the greatest
                  common divisor of <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">cb2</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. The default version
                  of this function calls <code class="computeroutput"><span class="identifier">eval_gcd</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_lcm</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the least
                  common multiple of <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">cb2</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. The default version
                  of this function calls <code class="computeroutput"><span class="identifier">eval_lcm</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">B</span><span class="special">(</span><span class="identifier">a</span><span class="special">))</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_gcd</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the greatest
                  common divisor of <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">a</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. The default version
                  of this function calls <code class="computeroutput"><span class="identifier">eval_gcd</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_lcm</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the least
                  common multiple of <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">a</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an integer
                  type. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code> or <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">float_types</span></code>. The default version
                  of this function calls <code class="computeroutput"><span class="identifier">eval_lcm</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_powm</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">,</span>
                  <span class="identifier">cb3</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the result
                  of <span class="emphasis"><em>(cb^cb2)%cb3</em></span>. The default version of this
                  function is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_powm</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the result
                  of <span class="emphasis"><em>(cb^cb2)%a</em></span>. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>. The default version
                  of this function is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_powm</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the result
                  of <span class="emphasis"><em>(cb^a)%cb2</em></span>. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>. The default version
                  of this function is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_powm</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">a2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the result
                  of <span class="emphasis"><em>(cb^a)%a2</em></span>. The type of <code class="computeroutput"><span class="identifier">a</span></code>
                  shall be listed in one of the type lists <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">signed_types</span></code>,
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">unsigned_types</span></code>. The default version
                  of this function is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_integer_sqrt</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">b2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the largest
                  integer which when squared is less than <code class="computeroutput"><span class="identifier">cb</span></code>,
                  also sets <code class="computeroutput"><span class="identifier">b2</span></code> to
                  the remainder, ie to <span class="emphasis"><em>cb - b<sup>2</sup></em></span>. The default
                  version of this function is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="emphasis"><em>Sign manipulation:</em></span>
                </p>
              </td>
<td class="auto-generated"> </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_abs</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Set <code class="computeroutput"><span class="identifier">b</span></code> to the absolute
                  value of <code class="computeroutput"><span class="identifier">cb</span></code>. The
                  default version of this functions assigns <code class="computeroutput"><span class="identifier">cb</span></code>
                  to <code class="computeroutput"><span class="identifier">b</span></code>, and then
                  calls <code class="computeroutput"><span class="identifier">b</span><span class="special">.</span><span class="identifier">negate</span><span class="special">()</span></code>
                  if <code class="computeroutput"><span class="identifier">eval_get_sign</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span> <span class="special">&lt;</span>
                  <span class="number">0</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fabs</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Set <code class="computeroutput"><span class="identifier">b</span></code> to the absolute
                  value of <code class="computeroutput"><span class="identifier">cb</span></code>. The
                  default version of this functions assigns <code class="computeroutput"><span class="identifier">cb</span></code>
                  to <code class="computeroutput"><span class="identifier">b</span></code>, and then
                  calls <code class="computeroutput"><span class="identifier">b</span><span class="special">.</span><span class="identifier">negate</span><span class="special">()</span></code>
                  if <code class="computeroutput"><span class="identifier">eval_get_sign</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span> <span class="special">&lt;</span>
                  <span class="number">0</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="emphasis"><em>floating-point functions:</em></span>
                </p>
              </td>
<td class="auto-generated"> </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fpclassify</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">int</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns one of the same values returned by <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">fpclassify</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  will only test for zero <code class="computeroutput"><span class="identifier">cb</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_trunc</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">trunc</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_round</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">round</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_exp</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">exp</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_exp2</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">exp2</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is implemented in terms of <code class="computeroutput"><span class="identifier">eval_pow</span></code>.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_log</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">log</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_log10</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">log10</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_sin</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">sin</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_cos</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">cos</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_tan</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">exp</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_asin</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">asin</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_acos</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">acos</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_atan</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">atan</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_sinh</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">sinh</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_cosh</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">cosh</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_tanh</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">tanh</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>
                  and stores the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fmod</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">fmod</span></code>
                  on arguments <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">cb2</span></code>, and store
                  the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_modf</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">pb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">modf</span></code>
                  on argument <code class="computeroutput"><span class="identifier">cb</span></code>,
                  and store the integer result in <code class="computeroutput"><span class="special">*</span><span class="identifier">pb</span></code> and the fractional part in
                  <code class="computeroutput"><span class="identifier">b</span></code>. Only required
                  when <code class="computeroutput"><span class="identifier">B</span></code> is an floating-point
                  type. The default version of this function is synthesised from
                  other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_pow</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pow</span></code>
                  on arguments <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">cb2</span></code>, and store
                  the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_atan2</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Performs the equivalent operation to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">atan</span></code>
                  on arguments <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">cb2</span></code>, and store
                  the result in <code class="computeroutput"><span class="identifier">b</span></code>.
                  Only required when <code class="computeroutput"><span class="identifier">B</span></code>
                  is an floating-point type. The default version of this function
                  is synthesised from other operations above.
                </p>
              </td>
<td>
                <p>
                   
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_scalbn</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">e</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Scales value <code class="computeroutput"><span class="identifier">cb</span></code>
                  by <span class="emphasis"><em>r<sup>e</sup></em></span>, where <span class="emphasis"><em>r</em></span> is the
                  radix of the type. The default version of this function is implemented
                  in terms of eval_ldexp, consequently this function must be provided
                  for types with a radix other than 2.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_scalbln</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">e</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Calls <code class="computeroutput"><span class="identifier">eval_scalbn</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">e</span><span class="special">)</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_ilogb</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">exponent_type</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns the exponent <span class="emphasis"><em>e</em></span> of value <code class="computeroutput"><span class="identifier">cb</span></code> such that <span class="emphasis"><em>1 &lt;=
                  cb*r<sup>-e</sup> &lt; r</em></span>, where <span class="emphasis"><em>r</em></span> is the radix
                  of type B. The default version of this function is implemented
                  in terms of eval_frexp, consequently this function must be provided
                  for types with a radix other than 2.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_remquo</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">,</span>
                  <span class="identifier">pi</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                  <span class="identifier">cb</span> <span class="special">-</span>
                  <span class="identifier">n</span> <span class="special">*</span>
                  <span class="identifier">cb2</span></code> and stores <code class="computeroutput"><span class="identifier">n</span></code> in <code class="computeroutput"><span class="special">*</span><span class="identifier">pi</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_remquo</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">pi</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Default version converts a to type B and calls the overload above.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_remquo</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">pi</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Default version converts a to type B and calls the overload above.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_remainder</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Default version calls eval_remquo with a dummy final argument.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_remainder</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">,</span> <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Default version calls eval_remquo with a dummy final argument.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_remainder</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Default version calls eval_remquo with a dummy final argument.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fdim</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Default version sets <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span>
                  <span class="special">-</span> <span class="identifier">cb2</span></code>
                  if <code class="computeroutput"><span class="identifier">cb</span> <span class="special">&gt;</span>
                  <span class="identifier">cb2</span></code> and zero otherwise.
                  Special cases are handled as in the C99 annex.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fdim</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Default version sets <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span>
                  <span class="special">-</span> <span class="identifier">cb2</span></code>
                  if <code class="computeroutput"><span class="identifier">cb</span> <span class="special">&gt;</span>
                  <span class="identifier">cb2</span></code> and zero otherwise.
                  Special cases are handled as in the C99 annex.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fdim</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Default version sets <code class="computeroutput"><span class="identifier">b</span>
                  <span class="special">=</span> <span class="identifier">cb</span>
                  <span class="special">-</span> <span class="identifier">cb2</span></code>
                  if <code class="computeroutput"><span class="identifier">cb</span> <span class="special">&gt;</span>
                  <span class="identifier">cb2</span></code> and zero otherwise.
                  Special cases are handled as in the C99 annex.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fmax</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the larger
                  of <code class="computeroutput"><span class="identifier">cb</span></code> and <code class="computeroutput"><span class="identifier">cb2</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fmax</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the larger
                  of <code class="computeroutput"><span class="identifier">cb</span></code> and <code class="computeroutput"><span class="identifier">a</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fmax</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the larger
                  of <code class="computeroutput"><span class="identifier">cb</span></code> and <code class="computeroutput"><span class="identifier">a</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fmin</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the smaller
                  of <code class="computeroutput"><span class="identifier">cb</span></code> and <code class="computeroutput"><span class="identifier">cb2</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fmin</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the smaller
                  of <code class="computeroutput"><span class="identifier">cb</span></code> and <code class="computeroutput"><span class="identifier">a</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_fmin</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the smaller
                  of <code class="computeroutput"><span class="identifier">cb</span></code> and <code class="computeroutput"><span class="identifier">a</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_hypot</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">cb2</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the square
                  root of the sum of the squares of <code class="computeroutput"><span class="identifier">cb</span></code>
                  and <code class="computeroutput"><span class="identifier">cb2</span></code> without
                  undue over or under flow.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_hypot</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  As above.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_hypot</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">a</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  As above.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_logb</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">B</span><span class="special">::</span><span class="identifier">exponent_type</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the exponent
                  <span class="emphasis"><em>e</em></span> of value <code class="computeroutput"><span class="identifier">cb</span></code>
                  such that <span class="emphasis"><em>1 &lt;= cb*r<sup>-b</sup> &lt; r</em></span>, where <span class="emphasis"><em>r</em></span>
                  is the radix of type B. The default version of this function is
                  implemented in terms of <code class="computeroutput"><span class="identifier">eval_ilogb</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_nearbyint</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Calls <code class="computeroutput"><span class="identifier">eval_round</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_rint</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Calls <code class="computeroutput"><span class="identifier">eval_nearbyint</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span> <span class="identifier">cb</span><span class="special">)</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">eval_log2</span><span class="special">(</span><span class="identifier">b</span><span class="special">,</span>
                  <span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="keyword">void</span></code>
                </p>
              </td>
<td>
                <p>
                  Sets <code class="computeroutput"><span class="identifier">b</span></code> to the logarithm
                  base 2 of <code class="computeroutput"><span class="identifier">cb</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="emphasis"><em>hashing:</em></span>
                </p>
              </td>
<td class="auto-generated"> </td>
<td class="auto-generated"> </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">hash_value</span><span class="special">(</span><span class="identifier">cb</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span></code>
                </p>
              </td>
<td>
                <p>
                  Returns a hash value for the argument that is suitable for use
                  with <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">hash</span></code> etc. If not provided then
                  no automatic hashing support will be available for the number type.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
        When the tables above place no <span class="emphasis"><em>throws</em></span> requirements on
        an operation, then it is up to each type modelling this concept to decide
        when or whether throwing an exception is desirable. However, thrown exceptions
        should always either be the type, or inherit from the type <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">runtime_error</span></code>.
        For example, a floating-point type might choose to throw <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">overflow_error</span></code>
        whenever the result of an operation would be infinite, and <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">underflow_error</span></code>
        whenever it would round to zero.
      </p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
          The non-member functions are all named with an "eval_" prefix
          to avoid conflicts with template classes of the same name - in point of
          fact this naming convention shouldn't be necessary, but rather works around
          some compiler bugs.
        </p></td></tr>
</table></div>
<h5>
<a name="boost_multiprecision.ref.backendconc.h0"></a>
        <span class="phrase"><a name="boost_multiprecision.ref.backendconc.overloadable_functions"></a></span><a class="link" href="backendconc.html#boost_multiprecision.ref.backendconc.overloadable_functions">Overloadable
        Functions</a>
      </h5>
<p>
        Some of the C99 math functions do not have <code class="computeroutput"><span class="identifier">eval_</span></code>
        functions but must be overloaded directly: these functions are either trivial
        or are forwarded to the Boost.Math implementations by default. The full list
        of these functions is:
      </p>
<pre class="programlisting"><span class="keyword">int</span>           <span class="identifier">sign</span>       <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="keyword">bool</span>          <span class="identifier">signbit</span>    <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">changesign</span> <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">copysign</span>   <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;,</span> <span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">asinh</span>      <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">acosh</span>      <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">atanh</span>      <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">cbrt</span>       <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">erf</span>        <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">erfc</span>       <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">expm1</span>      <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">log1p</span>      <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">tgamma</span>     <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">lgamma</span>     <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="keyword">long</span>          <span class="identifier">lrint</span>      <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="keyword">long</span> <span class="keyword">long</span>     <span class="identifier">llrint</span>     <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">nextafter</span>  <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;,</span> <span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
<span class="emphasis"><em>number</em></span>        <span class="identifier">nexttoward</span> <span class="special">(</span><span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;,</span> <span class="keyword">const</span> <span class="emphasis"><em>number-or-expression-template-type</em></span><span class="special">&amp;);</span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2002-2020 John
      Maddock and Christopher Kormanyos<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="internals.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ref.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="headers.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
