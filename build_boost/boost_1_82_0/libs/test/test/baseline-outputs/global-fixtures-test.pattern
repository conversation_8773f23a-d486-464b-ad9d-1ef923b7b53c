***********************
*********************** GlobalFixtureWithCtor<&good_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithCtor: dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="45"><![CDATA[GlobalFixtureWithCtor: dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithCtor: dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="45"><![CDATA[GlobalFixtureWithCtor: dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithCtor<&almost_good_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy": condition 2>3 is not satisfied [2 <= 3]
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithCtor: dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="45"><![CDATA[GlobalFixtureWithCtor: dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors": condition 2>3 is not satisfied [2 <= 3]
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithCtor: dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="45"><![CDATA[GlobalFixtureWithCtor: dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithCtor<&bad_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy": non sense
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithCtor: dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="45"><![CDATA[GlobalFixtureWithCtor: dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 39
- message: GlobalFixtureWithCtor: ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 45
- message: GlobalFixtureWithCtor: dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy no errors": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy no errors": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy no errors": non sense
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithCtor: dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="45"><![CDATA[GlobalFixtureWithCtor: dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 39
- message: GlobalFixtureWithCtor: ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 45
- message: GlobalFixtureWithCtor: dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithCtor<&very_bad_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="3" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 39
- message: GlobalFixtureWithCtor: ctor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_exception" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_exception'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside/bad_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="3" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_exception" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_exception'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside/bad_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy no errors": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 39
- message: GlobalFixtureWithCtor: ctor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/almost_good_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/almost_good_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithCtor<&very_bad_exception>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="3" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 39
- message: GlobalFixtureWithCtor: ctor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_exception" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_exception'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside/bad_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="3" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_exception" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_exception'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside/bad_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithCtor: ctor
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy no errors": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy no errors": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="39"><![CDATA[GlobalFixtureWithCtor: ctor]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 39
- message: GlobalFixtureWithCtor: ctor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/almost_good_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/almost_good_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithSetup<&good_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
GlobalFixtureWithSetup::setup-calling function done
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="61"><![CDATA[GlobalFixtureWithSetup::setup-calling function done]]></Message><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
GlobalFixtureWithSetup::setup-calling function done
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="61"><![CDATA[GlobalFixtureWithSetup::setup-calling function done]]></Message><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithSetup<&almost_good_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy": condition 2>3 is not satisfied [2 <= 3]
GlobalFixtureWithSetup::setup-calling function done
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="xxx/test-macro-global-fixture.cpp" line="61"><![CDATA[GlobalFixtureWithSetup::setup-calling function done]]></Message><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors": condition 2>3 is not satisfied [2 <= 3]
GlobalFixtureWithSetup::setup-calling function done
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="xxx/test-macro-global-fixture.cpp" line="61"><![CDATA[GlobalFixtureWithSetup::setup-calling function done]]></Message><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithSetup<&bad_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy": non sense
GlobalFixtureWithSetup::setup-calling function done
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="61"><![CDATA[GlobalFixtureWithSetup::setup-calling function done]]></Message><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 52
- message: GlobalFixtureWithSetup ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 59
- message: GlobalFixtureWithSetup::setup-calling function

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 61
- message: GlobalFixtureWithSetup::setup-calling function done

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 55
- message: GlobalFixtureWithSetup dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy no errors": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy no errors": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy no errors": non sense
GlobalFixtureWithSetup::setup-calling function done
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="61"><![CDATA[GlobalFixtureWithSetup::setup-calling function done]]></Message><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 52
- message: GlobalFixtureWithSetup ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 59
- message: GlobalFixtureWithSetup::setup-calling function

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 61
- message: GlobalFixtureWithSetup::setup-calling function done

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 55
- message: GlobalFixtureWithSetup dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithSetup<&very_bad_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy": very_bad_foo is fatal
Failure occurred in a following context:
    some context
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="3" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 52
- message: GlobalFixtureWithSetup ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 59
- message: GlobalFixtureWithSetup::setup-calling function

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 55
- message: GlobalFixtureWithSetup dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_exception" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_exception'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside/bad_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="3" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_exception" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_exception'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside/bad_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy no errors": very_bad_foo is fatal
Failure occurred in a following context:
    some context
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 52
- message: GlobalFixtureWithSetup ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 59
- message: GlobalFixtureWithSetup::setup-calling function

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 55
- message: GlobalFixtureWithSetup dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/almost_good_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/almost_good_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithSetup<&very_bad_exception>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="3" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 52
- message: GlobalFixtureWithSetup ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 59
- message: GlobalFixtureWithSetup::setup-calling function

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 55
- message: GlobalFixtureWithSetup dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_exception" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_exception'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside/bad_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="3" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" name="very_bad_exception" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/very_bad_exception'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 test cases inside'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside/bad_foo'
- disabled test unit: 'Fake Test Suite Hierarchy/1 bad test case inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithSetup ctor
GlobalFixtureWithSetup::setup-calling function
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy no errors": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy no errors": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
GlobalFixtureWithSetup dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="52"><![CDATA[GlobalFixtureWithSetup ctor]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="59"><![CDATA[GlobalFixtureWithSetup::setup-calling function]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><Message file="xxx/test-macro-global-fixture.cpp" line="55"><![CDATA[GlobalFixtureWithSetup dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 52
- message: GlobalFixtureWithSetup ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 59
- message: GlobalFixtureWithSetup::setup-calling function

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 55
- message: GlobalFixtureWithSetup dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/almost_good_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/almost_good_foo'
- reason: '']]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<skipped/>
<system-out><![CDATA[Test case disabled because of the following chain of decision:
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo'
- disabled test unit: 'Fake Test Suite Hierarchy no errors/1 test cases inside'
- reason: '']]></system-out>
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithTeardown<&good_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithTeardown::teardown-calling function
GlobalFixtureWithTeardown::teardown-calling function done
GlobalFixtureWithTeardown dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="77"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function done]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="71"><![CDATA[GlobalFixtureWithTeardown dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithTeardown::teardown-calling function
GlobalFixtureWithTeardown::teardown-calling function done
GlobalFixtureWithTeardown dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="77"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function done]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="71"><![CDATA[GlobalFixtureWithTeardown dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithTeardown<&almost_good_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithTeardown::teardown-calling function
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy": condition 2>3 is not satisfied [2 <= 3]
GlobalFixtureWithTeardown::teardown-calling function done
GlobalFixtureWithTeardown dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="xxx/test-macro-global-fixture.cpp" line="77"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function done]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="71"><![CDATA[GlobalFixtureWithTeardown dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithTeardown::teardown-calling function
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors": condition 2>3 is not satisfied [2 <= 3]
GlobalFixtureWithTeardown::teardown-calling function done
GlobalFixtureWithTeardown dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="xxx/test-macro-global-fixture.cpp" line="77"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function done]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="71"><![CDATA[GlobalFixtureWithTeardown dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithTeardown<&bad_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithTeardown::teardown-calling function
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy": non sense
GlobalFixtureWithTeardown::teardown-calling function done
GlobalFixtureWithTeardown dtor
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="77"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function done]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="71"><![CDATA[GlobalFixtureWithTeardown dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 68
- message: GlobalFixtureWithTeardown ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 75
- message: GlobalFixtureWithTeardown::teardown-calling function

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 77
- message: GlobalFixtureWithTeardown::teardown-calling function done

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 71
- message: GlobalFixtureWithTeardown dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithTeardown::teardown-calling function
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy no errors": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy no errors": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy no errors": non sense
GlobalFixtureWithTeardown::teardown-calling function done
GlobalFixtureWithTeardown dtor
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="77"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function done]]></Message><Message file="xxx/test-macro-global-fixture.cpp" line="71"><![CDATA[GlobalFixtureWithTeardown dtor]]></Message></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 68
- message: GlobalFixtureWithTeardown ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 75
- message: GlobalFixtureWithTeardown::teardown-calling function

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 77
- message: GlobalFixtureWithTeardown::teardown-calling function done

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 71
- message: GlobalFixtureWithTeardown dtor

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="3" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithTeardown<&very_bad_foo>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithTeardown::teardown-calling function
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 68
- message: GlobalFixtureWithTeardown ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 75
- message: GlobalFixtureWithTeardown::teardown-calling function

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithTeardown::teardown-calling function
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy no errors": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 68
- message: GlobalFixtureWithTeardown ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 75
- message: GlobalFixtureWithTeardown::teardown-calling function

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="1" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
***********************
*********************** GlobalFixtureWithTeardown<&very_bad_exception>
***********************
* 1-format  *******************************************************************
Running 5 test cases...
xxx/test-macro-global-fixture.cpp:281: Entering test suite "Fake Test Suite Hierarchy"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:282: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:282: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:283: Entering test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:165: fatal error: in "Fake Test Suite Hierarchy/very_bad_foo": very_bad_foo is fatal
Failure occurred in a following context:
    some context
xxx/test-macro-global-fixture.cpp:283: Leaving test case "very_bad_foo"
xxx/test-macro-global-fixture.cpp:284: Entering test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy/very_bad_exception": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy/very_bad_exception": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:284: Leaving test case "very_bad_exception"
xxx/test-macro-global-fixture.cpp:275: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:276: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:276: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:275: Leaving test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:278: Entering test suite "1 bad test case inside"
xxx/test-macro-global-fixture.cpp:279: Entering test case "bad_foo"
xxx/test-macro-global-fixture.cpp:151: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": 
this is a message
xxx/test-macro-global-fixture.cpp:154: info: check true has passed
xxx/test-macro-global-fixture.cpp:158: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
xxx/test-macro-global-fixture.cpp:160: error: in "Fake Test Suite Hierarchy/1 bad test case inside/bad_foo": non sense
xxx/test-macro-global-fixture.cpp:279: Leaving test case "bad_foo"
xxx/test-macro-global-fixture.cpp:278: Leaving test suite "1 bad test case inside"
GlobalFixtureWithTeardown::teardown-calling function
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:281: Leaving test suite "Fake Test Suite Hierarchy"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy" file="xxx/test-macro-global-fixture.cpp" line="281"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="282"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_foo" file="xxx/test-macro-global-fixture.cpp" line="283"><FatalError file="xxx/test-macro-global-fixture.cpp" line="165"><![CDATA[very_bad_foo is fatal]]><Context><Frame><![CDATA[some context]]></Frame></Context></FatalError><TestingTime>ZZZ</TestingTime></TestCase><TestCase name="very_bad_exception" file="xxx/test-macro-global-fixture.cpp" line="284"><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="275"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="276"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><TestSuite name="1 bad test case inside" file="xxx/test-macro-global-fixture.cpp" line="278"><TestCase name="bad_foo" file="xxx/test-macro-global-fixture.cpp" line="279"><Error file="xxx/test-macro-global-fixture.cpp" line="151"><![CDATA[]]></Error><Message file="xxx/test-macro-global-fixture.cpp" line="153"><![CDATA[this is a message]]></Message><Info file="xxx/test-macro-global-fixture.cpp" line="154"><![CDATA[check true has passed]]></Info><Error file="xxx/test-macro-global-fixture.cpp" line="158"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Error file="xxx/test-macro-global-fixture.cpp" line="160"><![CDATA[non sense]]></Error><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 68
- message: GlobalFixtureWithTeardown ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 75
- message: GlobalFixtureWithTeardown::teardown-calling function

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 153
- message: this is a message

INFO:
- file   : test-macro-global-fixture.cpp
- line   : 154
- message: check true has passed

]]></system-out>
<system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="5" skipped="0" errors="2" failures="2" id="0" name="Fake_Test_Suite_Hierarchy" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="4" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/bad_foo
- file: test-macro-global-fixture.cpp
- line: 282
]]></system-err>
</testcase>
<testcase assertions="1" name="very_bad_foo" time="0.1234">
<failure message="failure" type="fatal error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 165
- message: very_bad_foo is fatal
- context:
  - 'some context'


]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_foo
- file: test-macro-global-fixture.cpp
- line: 283
]]></system-err>
</testcase>
<testcase assertions="2" name="very_bad_exception" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/very_bad_exception
- file: test-macro-global-fixture.cpp
- line: 284
]]></system-err>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
<testcase assertions="4" classname="1_bad_test_case_inside" name="bad_foo" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 151
- message: 

]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 158
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 160
- message: non sense

]]></failure><system-err><![CDATA[Failures detected in:
- test case: Fake Test Suite Hierarchy/1 bad test case inside/bad_foo
- file: test-macro-global-fixture.cpp
- line: 279
]]></system-err>
</testcase>
</testsuite>
* 1-format  *******************************************************************
Running 2 test cases...
xxx/test-macro-global-fixture.cpp:293: Entering test suite "Fake Test Suite Hierarchy no errors"
GlobalFixtureWithTeardown ctor
xxx/test-macro-global-fixture.cpp:289: Test suite "Fake Test Suite Hierarchy no errors/0 test cases inside" is skipped because disabled
xxx/test-macro-global-fixture.cpp:295: Entering test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:147: warning: in "Fake Test Suite Hierarchy no errors/almost_good_foo": condition 2>3 is not satisfied [2 <= 3]
Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:295: Leaving test case "almost_good_foo"
xxx/test-macro-global-fixture.cpp:290: Entering test suite "1 test cases inside"
xxx/test-macro-global-fixture.cpp:291: Entering test case "good_foo"
Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions
xxx/test-macro-global-fixture.cpp:291: Leaving test case "good_foo"
xxx/test-macro-global-fixture.cpp:290: Leaving test suite "1 test cases inside"
GlobalFixtureWithTeardown::teardown-calling function
xxx/test-macro-global-fixture.cpp:174: error: in "Fake Test Suite Hierarchy no errors": with some message
Failure occurred in a following context:
    Context value=something
    Context value2=something different
unknown location:0: fatal error: in "Fake Test Suite Hierarchy no errors": unknown type
xxx/test-macro-global-fixture.cpp:174: last checkpoint
Failure occurred in a following context:
    exception context should be shown
xxx/test-macro-global-fixture.cpp:293: Leaving test suite "Fake Test Suite Hierarchy no errors"

* 2-format  *******************************************************************
<TestLog><TestSuite name="Fake Test Suite Hierarchy no errors" file="xxx/test-macro-global-fixture.cpp" line="293"><Message file="xxx/test-macro-global-fixture.cpp" line="68"><![CDATA[GlobalFixtureWithTeardown ctor]]></Message><TestSuite name="0 test cases inside" skipped="yes" reason="disabled"/><TestCase name="almost_good_foo" file="xxx/test-macro-global-fixture.cpp" line="295"><Warning file="xxx/test-macro-global-fixture.cpp" line="147"><![CDATA[condition 2>3 is not satisfied [2 <= 3]]]></Warning><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase><TestSuite name="1 test cases inside" file="xxx/test-macro-global-fixture.cpp" line="290"><TestCase name="good_foo" file="xxx/test-macro-global-fixture.cpp" line="291"><Message file="boost.test framework" line="0"><![CDATA[Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions]]></Message><TestingTime>ZZZ</TestingTime></TestCase></TestSuite><Message file="xxx/test-macro-global-fixture.cpp" line="75"><![CDATA[GlobalFixtureWithTeardown::teardown-calling function]]></Message><Error file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[with some message]]><Context><Frame><![CDATA[Context value=something]]></Frame><Frame><![CDATA[Context value2=something different]]></Frame></Context></Error><Exception file="unknown location" line="0"><![CDATA[unknown type]]><LastCheckpoint file="xxx/test-macro-global-fixture.cpp" line="174"><![CDATA[]]></LastCheckpoint><Context><Frame><![CDATA[exception context should be shown]]></Frame></Context></Exception></TestSuite></TestLog>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-out><![CDATA[MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 68
- message: GlobalFixtureWithTeardown ctor

MESSAGE:
- file   : test-macro-global-fixture.cpp
- line   : 75
- message: GlobalFixtureWithTeardown::teardown-calling function

]]></system-out>
<system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
<system-out><![CDATA[WARNING:
- file   : test-macro-global-fixture.cpp
- line   : 147
- message: condition 2>3 is not satisfied [2 <= 3]

MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/almost_good_foo did not check any assertions

]]></system-out>
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
<system-out><![CDATA[MESSAGE:
- file   : boost.test framework
- line   : 0
- message: Test case Fake Test Suite Hierarchy no errors/1 test cases inside/good_foo did not check any assertions

]]></system-out>
</testcase>
</testsuite>
* 3-format  *******************************************************************
<?xml version="1.0" encoding="UTF-8"?>
<testsuite tests="1" skipped="0" errors="0" failures="0" id="0" name="Fake_Test_Suite_Hierarchy_no_errors" time="0.1234">
<testcase assertions="2" name="boost_test-setup-teardown" time="0.1234">
<failure message="failure" type="assertion error"><![CDATA[
ASSERTION FAILURE:
- file   : test-macro-global-fixture.cpp
- line   : 174
- message: with some message
- context:
  - 'Context value=something'
  - 'Context value2=something different'


]]></failure><error message="unexpected exception" type="uncaught exception"><![CDATA[
UNCAUGHT EXCEPTION:
- file: unknown location
- line: 0


EXCEPTION STACK TRACE: --------------
unknown type
-------------------------------------

Last checkpoint:
- message: ""
- file: test-macro-global-fixture.cpp
- line: 174


CONTEXT:
- 'exception context should be shown'
]]></error><system-err><![CDATA[Failures detected in:
 boost.test global setup/teardown
]]></system-err>
</testcase>
<testcase assertions="0" name="almost_good_foo" time="0.1234">
</testcase>
<testcase assertions="0" classname="1_test_cases_inside" name="good_foo" time="0.1234">
</testcase>
</testsuite>
