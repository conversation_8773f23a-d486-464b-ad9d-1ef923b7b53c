<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>

    <meta http-equiv="content-type" content="text/html; charset=windows-1252">
    <title>BOOST_PP_LIMIT_WHILE</title>
    <link rel="stylesheet" type="text/css" href="../styles.css">
  </head>
  <body>
    <div style="margin-left: 0px;"> The <b>BOOST_PP_LIMIT_WHILE</b>
      macro defines the maximum number of <b>BOOST_PP_WHILE</b>
      iterations. </div>
    <h4>Usage</h4>
    <div class="code"> <b>BOOST_PP_LIMIT_WHILE</b> </div>
    <h4>Remarks</h4>
    <div>This macro expands by default to <i>256</i>. The macro
      actually expands to the value of the <a
href="file:///E:/Programming/VersionControl/modular-boost/libs/preprocessor/doc/ref/limit_mag.html">BOOST_PP_LIMIT_MAG</a>
      macro and can not be changed.</div>
    <h4>Requirements</h4>
    <div> <b>Header:</b> &nbsp;<a href="../headers/config/limits.html">&lt;boost/preprocessor/config/limits.hpp&gt;</a>
    </div>
    <hr size="1">
    <div style="margin-left: 0px;"> <i>© Copyright <a
          href="http://www.housemarque.com" target="_top">Housemarque Oy</a>
        2002</i> <br>
      <i>© Copyright Paul Mensonides 2002</i> </div>
    <div style="margin-left: 0px;">
      <p><small>Distributed under the Boost Software License, Version
          1.0. (See accompanying file <a
            href="../../../../LICENSE_1_0.txt">LICENSE_1_0.txt</a> or
          copy at <a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a>)</small></p>
    </div>
  </body>
</html>
