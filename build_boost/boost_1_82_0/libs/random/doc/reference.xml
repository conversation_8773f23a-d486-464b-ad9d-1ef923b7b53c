<?xml version="1.0" standalone="yes"?>
<library-reference id="headers"><title>Headers</title><header name="boost/random.hpp">
</header>
<header name="boost/random/additive_combine.hpp">
<namespace name="boost">
<namespace name="random">
<class name="additive_combine_engine"><template>
      <template-type-parameter name="MLCG1"/>
      <template-type-parameter name="MLCG2"/>
    </template><description><para>An instantiation of class template <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput> models a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It combines two multiplicative  <classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname> number generators, i.e. those with <computeroutput>c</computeroutput> = 0. It is described in</para><para> <blockquote><para>  "Efficient and Portable Combined Random Number Generators", <PERSON>, Communications of the ACM, Vol. 31, No. 6, June 1988, pp. 742-749, 774  </para></blockquote> </para><para>The template parameters MLCG1 and MLCG2 shall denote two different  <classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname> number generators, each with c = 0. Each invocation returns a random number X(n) := (MLCG1(n) - MLCG2(n)) mod (m1 - 1), where m1 denotes the modulus of MLCG1. </para></description><typedef name="first_base"><type>MLCG1</type></typedef>
<typedef name="second_base"><type>MLCG2</type></typedef>
<typedef name="result_type"><type>MLCG1::result_type</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce </para></description></method>
</method-group>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Seeds an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput> using the default seeds of the two base generators. </para></description></method>
<method name="seed"><type>void</type><parameter name="seed"><paramtype>result_type</paramtype></parameter><description><para>Seeds an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput>, using <computeroutput>seed</computeroutput> as the seed for both base generators. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput>, using <computeroutput>seq</computeroutput> to seed both base generators.</para><para>See the warning on the corresponding constructor. </para></description></method>
<method name="seed"><type>void</type><parameter name="seed1"><paramtype>typename MLCG1::result_type</paramtype></parameter><parameter name="seed2"><paramtype>typename MLCG2::result_type</paramtype></parameter><description><para>Seeds an <computeroutput>additive_combine</computeroutput> generator, using <computeroutput>seed1</computeroutput> and <computeroutput>seed2</computeroutput> as the seeds to the first and second base generators, respectively. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Seeds an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput> with values from the range defined by the input iterators first and last. first will be modified to point to the element after the last one used.</para><para>Throws: <computeroutput>std::invalid_argument</computeroutput> if the input range is too small.</para><para>Exception Safety: Basic </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
</method-group>
<constructor><description><para>Constructs an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput> using the default constructors of the two base generators. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="seed"><paramtype>result_type</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput>, using seed as the constructor argument for both base generators. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput>, using seq as the constructor argument for both base generators.</para><para> <warning><para>  The semantics of this function are liable to change. A <computeroutput><classname alt="boost::random::seed_seq">seed_seq</classname></computeroutput> is designed to generate all the seeds in one shot, but this seeds the two base engines independantly and probably ends up giving the same sequence to both.  </para></warning>  </para></description></constructor>
<constructor><parameter name="seed1"><paramtype>typename MLCG1::result_type</paramtype></parameter><parameter name="seed2"><paramtype>typename MLCG2::result_type</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput>, using <computeroutput>seed1</computeroutput> and <computeroutput>seed2</computeroutput> as the constructor argument to the first and second base generators, respectively. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Contructs an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput> with values from the range defined by the input iterators first and last. first will be modified to point to the element after the last one used.</para><para>Throws: <computeroutput>std::invalid_argument</computeroutput> if the input range is too small.</para><para>Exception Safety: Basic </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="r"><paramtype>const <classname>additive_combine_engine</classname> &amp;</paramtype></parameter><description><para>Writes the state of an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. The textual representation of an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput> is the textual representation of the first base generator followed by the textual representation of the second base generator. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="r"><paramtype>const <classname>additive_combine_engine</classname> &amp;</paramtype></parameter><description><para>Reads the state of an <computeroutput><classname alt="boost::random::additive_combine_engine">additive_combine_engine</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>additive_combine_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>additive_combine_engine</classname> &amp;</paramtype></parameter><description><para>Returns: true iff the two <computeroutput>additive_combine_engines</computeroutput> will produce the same sequence of values. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>additive_combine_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>additive_combine_engine</classname> &amp;</paramtype></parameter><description><para>Returns: true iff the two <computeroutput>additive_combine_engines</computeroutput> will produce different sequences of values. </para></description></method>
</method-group>
</class><typedef name="ecuyer1988"><description><para>The specialization <classname alt="boost::random::ecuyer1988">ecuyer1988</classname>  was suggested in</para><para> <blockquote><para>  "Efficient and Portable Combined Random Number Generators", Pierre L'Ecuyer, Communications of the ACM, Vol. 31, No. 6, June 1988, pp. 742-749, 774  </para></blockquote>  </para></description><type><classname>additive_combine_engine</classname>&lt; <classname>linear_congruential_engine</classname>&lt; uint32_t, 40014, 0, 2147483563 &gt;, <classname>linear_congruential_engine</classname>&lt; uint32_t, 40692, 0, 2147483399 &gt;&gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/bernoulli_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="bernoulli_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::bernoulli_distribution">bernoulli_distribution</classname>  model a  <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . Such a random distribution produces bool values distributed with probabilities P(true) = p and P(false) = 1-p. p is the parameter of the distribution. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>bernoulli_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="p" cv="const"><type>RealType</type><description><para>Returns the p parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="p"><paramtype>RealType</paramtype><default>0.5</default></parameter><description><para>Constructs the parameters of the distribution.</para><para>Requires: 0 &lt;= p &lt;= 1 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a std::ostream. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a std::istream. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>int</type></typedef>
<typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="p" cv="const"><type>RealType</type><description><para>Returns: The "p" parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>bool</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>bool</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns: a random variate distributed according to the  <classname alt="boost::random::bernoulli_distribution">bernoulli_distribution</classname> . </para></description></method>
<method name="operator()" cv="const"><type>bool</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns: a random variate distributed according to the  <classname alt="boost::random::bernoulli_distribution">bernoulli_distribution</classname>  with parameters specified by param. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="p"><paramtype>const RealType &amp;</paramtype><default>0.5</default></parameter><description><para>Constructs a <classname alt="boost::random::bernoulli_distribution">bernoulli_distribution</classname>  object. p is the parameter of the distribution.</para><para>Requires: 0 &lt;= p &lt;= 1 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs <classname alt="boost::random::bernoulli_distribution">bernoulli_distribution</classname>  from its parameters </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="bd"><paramtype>const <classname>bernoulli_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="bd"><paramtype>const <classname>bernoulli_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>bernoulli_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>bernoulli_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true iff the two distributions will produce identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>bernoulli_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>bernoulli_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true iff the two distributions will produce different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/beta_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="beta_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The beta distribution is a real-valued distribution which produces values in the range [0, 1]. It has two parameters, alpha and beta.</para><para>It has <inlineequation><alt>$\displaystyle p(x) = \frac{x^{\alpha-1}(1-x)^{\beta-1}}{B(\alpha, \beta)}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_0.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \frac{x^{\alpha-1}(1-x)^{\beta-1}}{B(\alpha, \beta)}$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>beta_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="alpha" cv="const"><type>RealType</type><description><para>Returns the "alpha" parameter of the distribtuion. </para></description></method>
<method name="beta" cv="const"><type>RealType</type><description><para>Returns the "beta" parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="alpha"><paramtype>RealType</paramtype><default>1.0</default></parameter><parameter name="beta"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::beta_distribution::param_type">param_type</classname></computeroutput> from the "alpha" and "beta" parameters of the distribution.</para><para>Requires: alpha &gt; 0, beta &gt; 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::beta_distribution::param_type">param_type</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::beta_distribution::param_type">param_type</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the different. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>RealType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the beta distribution. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed accordint to the beta distribution with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="alpha" cv="const"><type>RealType</type><description><para>Returns the "alpha" parameter of the distribution. </para></description></method>
<method name="beta" cv="const"><type>RealType</type><description><para>Returns the "beta" parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="alpha"><paramtype>RealType</paramtype><default>1.0</default></parameter><parameter name="beta"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::beta_distribution">beta_distribution</classname></computeroutput> from its "alpha" and "beta" parameters.</para><para>Requires: alpha &gt; 0, beta &gt; 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::beta_distribution">beta_distribution</classname></computeroutput> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="wd"><paramtype>const <classname>beta_distribution</classname> &amp;</paramtype></parameter><description><para>Writes an <computeroutput><classname alt="boost::random::beta_distribution">beta_distribution</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="wd"><paramtype>const <classname>beta_distribution</classname> &amp;</paramtype></parameter><description><para>Reads an <computeroutput><classname alt="boost::random::beta_distribution">beta_distribution</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>beta_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>beta_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::beta_distribution">beta_distribution</classname></computeroutput> will return identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>beta_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>beta_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::beta_distribution">beta_distribution</classname></computeroutput> will return different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/binomial_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="binomial_distribution"><template>
      <template-type-parameter name="IntType"><default>int</default></template-type-parameter>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The binomial distribution is an integer valued distribution with two parameters, <computeroutput>t</computeroutput> and <computeroutput>p</computeroutput>. The values of the distribution are within the range [0,t].</para><para>The distribution function is <inlineequation><alt>$\displaystyle P(k) = {t \choose k}p^k(1-p)^{t-k}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_1.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle P(k) = {t \choose k}p^k(1-p)^{t-k}$</phrase></textobject></inlinemediaobject></inlineequation>.</para><para>The algorithm used is the BTRD algorithm described in</para><para> <blockquote><para>  "The generation of binomial random variates", Wolfgang Hormann, Journal of Statistical Computation and Simulation, Volume 46, Issue 1 &amp; 2 April 1993 , pages 101 - 110  </para></blockquote>  </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>binomial_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="t" cv="const"><type>IntType</type><description><para>Returns the <computeroutput>t</computeroutput> parameter of the distribution. </para></description></method>
<method name="p" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>p</computeroutput> parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="t"><paramtype>IntType</paramtype><default>1</default></parameter><parameter name="p"><paramtype>RealType</paramtype><default>0.5</default></parameter><description><para>Construct a <classname alt="boost::random::binomial_distribution::param_type">param_type</classname> object. <computeroutput>t</computeroutput> and <computeroutput>p</computeroutput> are the parameters of the distribution.</para><para>Requires: t &gt;=0 &amp;&amp; 0 &lt;= p &lt;= 1 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype><classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have the same values. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have different values. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>IntType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>IntType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the binomial distribution. </para></description></method>
<method name="operator()" cv="const"><type>IntType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the binomial distribution with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="t" cv="const"><type>IntType</type><description><para>Returns the <computeroutput>t</computeroutput> parameter of the distribution. </para></description></method>
<method name="p" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>p</computeroutput> parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>IntType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>IntType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="t"><paramtype>IntType</paramtype><default>1</default></parameter><parameter name="p"><paramtype>RealType</paramtype><default>0.5</default></parameter><description><para>Construct a <computeroutput><classname alt="boost::random::binomial_distribution">binomial_distribution</classname></computeroutput> object. <computeroutput>t</computeroutput> and <computeroutput>p</computeroutput> are the parameters of the distribution.</para><para>Requires: t &gt;=0 &amp;&amp; 0 &lt;= p &lt;= 1 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Construct an <computeroutput><classname alt="boost::random::binomial_distribution">binomial_distribution</classname></computeroutput> object from the parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="bd"><paramtype>const <classname>binomial_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="bd"><paramtype><classname>binomial_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>binomial_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>binomial_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce the same sequence of values, given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>binomial_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>binomial_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions could produce different sequences of values, given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/cauchy_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="cauchy_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The cauchy distribution is a continuous distribution with two parameters, median and sigma.</para><para>It has <inlineequation><alt>$\displaystyle p(x) = \frac{\sigma}{\pi(\sigma^2 + (x-m)^2)}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_2.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \frac{\sigma}{\pi(\sigma^2 + (x-m)^2)}$</phrase></textobject></inlinemediaobject></inlineequation> </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>cauchy_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="median" cv="const"><type>RealType</type><description><para>Returns the median of the distribution. </para></description></method>
<method name="sigma" cv="const"><type>RealType</type><description><para>Returns the sigma parameter of the distribution. </para></description></method>
<method name="a" cv="const"><type>RealType</type><description><para>Returns the median of the distribution. </para></description></method>
<method name="b" cv="const"><type>RealType</type><description><para>Returns the sigma parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="median"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="sigma"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs the parameters of the cauchy distribution. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a std::ostream. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a std::istream. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>RealType</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="median" cv="const"><type>RealType</type><description><para>Returns: the "median" parameter of the distribution </para></description></method>
<method name="sigma" cv="const"><type>RealType</type><description><para>Returns: the "sigma" parameter of the distribution </para></description></method>
<method name="a" cv="const"><type>RealType</type><description><para>Returns: the "median" parameter of the distribution </para></description></method>
<method name="b" cv="const"><type>RealType</type><description><para>Returns: the "sigma" parameter of the distribution </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns: A random variate distributed according to the cauchy distribution. </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns: A random variate distributed according to the cauchy distribution with parameters specified by param. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="median"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="sigma"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <classname alt="boost::random::cauchy_distribution">cauchy_distribution</classname>  with the paramters <computeroutput>median</computeroutput> and <computeroutput>sigma</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::cauchy_distribution">cauchy_distribution</classname>  from it's parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="cd"><paramtype>const <classname>cauchy_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="cd"><paramtype>const <classname>cauchy_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>cauchy_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>cauchy_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce identical sequences of values, given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>cauchy_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>cauchy_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may produce different sequences of values, given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/chi_squared_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="chi_squared_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The chi squared distribution is a real valued distribution with one parameter, <computeroutput>n</computeroutput>. The distribution produces values &gt; 0.</para><para>The distribution function is <inlineequation><alt>$\displaystyle P(x) = \frac{x^{(n/2)-1}e^{-x/2}}{\Gamma(n/2)2^{n/2}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_3.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle P(x) = \frac{x^{(n/2)-1}e^{-x/2}}{\Gamma(n/2)2^{n/2}}$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>chi_squared_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="n" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>n</computeroutput> parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="n"><paramtype>RealType</paramtype><default>1</default></parameter><description><para>Construct a <classname alt="boost::random::chi_squared_distribution::param_type">param_type</classname> object. <computeroutput>n</computeroutput> is the parameter of the distribution.</para><para>Requires: t &gt;=0 &amp;&amp; 0 &lt;= p &lt;= 1 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype><classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have the same values. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have different values. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>RealType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the chi squared distribution. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the chi squared distribution with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="n" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>n</computeroutput> parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="n"><paramtype>RealType</paramtype><default>1</default></parameter><description><para>Construct a <computeroutput><classname alt="boost::random::chi_squared_distribution">chi_squared_distribution</classname></computeroutput> object. <computeroutput>n</computeroutput> is the parameter of the distribution.</para><para>Requires: t &gt;=0 &amp;&amp; 0 &lt;= p &lt;= 1 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Construct an <computeroutput><classname alt="boost::random::chi_squared_distribution">chi_squared_distribution</classname></computeroutput> object from the parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="c2d"><paramtype>const <classname>chi_squared_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="c2d"><paramtype><classname>chi_squared_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>chi_squared_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>chi_squared_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce the same sequence of values, given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>chi_squared_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>chi_squared_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions could produce different sequences of values, given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/discard_block.hpp">
<namespace name="boost">
<namespace name="random">
<class name="discard_block_engine"><template>
      <template-type-parameter name="UniformRandomNumberGenerator"/>
      <template-nontype-parameter name="p"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="r"><type>std::size_t</type></template-nontype-parameter>
    </template><description><para>The class template <classname alt="boost::random::discard_block_engine">discard_block_engine</classname>  is a model of  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It modifies another generator by discarding parts of its output. Out of every block of <computeroutput>p</computeroutput> results, the first <computeroutput>r</computeroutput> will be returned and the rest discarded.</para><para>Requires: 0 &lt; p &lt;= r </para></description><typedef name="base_type"><type>UniformRandomNumberGenerator</type></typedef>
<typedef name="result_type"><type>base_type::result_type</type></typedef>
<data-member name="block_size" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="used_block" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="total_block" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="returned_block" specifiers="static"><type>const std::size_t</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>default seeds the underlying generator. </para></description></method>
<method name="seed"><type>void</type><parameter name="s"><paramtype>seed_type</paramtype></parameter><description><para>Seeds the underlying generator with s. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds the underlying generator with seq. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Seeds the underlying generator with first and last. </para></description></method>
<method name="base" cv="const"><type>const base_type &amp;</type><description><para>Returns the underlying engine. </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter></method>
</method-group>
<constructor><description><para>Uses the default seed for the base generator. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="rng"><paramtype>const base_type &amp;</paramtype></parameter><description><para>Constructs a new <classname alt="boost::random::discard_block_engine">discard_block_engine</classname>  with a copy of rng. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="rng"><paramtype>base_type &amp;&amp;</paramtype></parameter><description><para>Constructs a new <classname alt="boost::random::discard_block_engine">discard_block_engine</classname>  with rng. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="value"><paramtype>seed_type</paramtype></parameter><description><para>Creates a new <classname alt="boost::random::discard_block_engine">discard_block_engine</classname>  and seeds the underlying generator with <computeroutput>value</computeroutput> </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Creates a new <classname alt="boost::random::discard_block_engine">discard_block_engine</classname>  and seeds the underlying generator with <computeroutput>seq</computeroutput> </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Creates a new <classname alt="boost::random::discard_block_engine">discard_block_engine</classname>  and seeds the underlying generator with first and last. </para></description></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. This is the same as the minimum of the underlying generator. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce. This is the same as the maximum of the underlying generator. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>discard_block_engine</classname> &amp;</paramtype></parameter><description><para>Writes a <classname alt="boost::random::discard_block_engine">discard_block_engine</classname>  to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype><classname>discard_block_engine</classname> &amp;</paramtype></parameter><description><para>Reads a <classname alt="boost::random::discard_block_engine">discard_block_engine</classname>  from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>discard_block_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>discard_block_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="x"><paramtype>const <classname>discard_block_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>discard_block_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/discrete_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="discrete_distribution"><template>
      <template-type-parameter name="IntType"><default>int</default></template-type-parameter>
      <template-type-parameter name="WeightType"><default>double</default></template-type-parameter>
    </template><description><para>The class <computeroutput><classname alt="boost::random::discrete_distribution">discrete_distribution</classname></computeroutput> models a <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . It produces integers in the range [0, n) with the probability of producing each value is specified by the parameters of the distribution. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>discrete_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="probabilities" cv="const"><type>std::vector&lt; WeightType &gt;</type><description><para>Returns a vector containing the probabilities of each possible value of the distribution. </para></description></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput><classname alt="boost::random::discrete_distribution::param_type">param_type</classname></computeroutput> object, representing a distribution with <inlineequation><alt>$p(0) = 1$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_4.png"/></imageobject><textobject role="tex"><phrase>$p(0) = 1$</phrase></textobject></inlinemediaobject></inlineequation> and <inlineequation><alt>$p(k|k&gt;0) = 0$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_9.png"/></imageobject><textobject role="tex"><phrase>$p(k|k&gt;0) = 0$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>If <computeroutput>first</computeroutput> == <computeroutput>last</computeroutput>, equivalent to the default constructor. Otherwise, the values of the range represent weights for the possible values of the distribution. </para></description></constructor>
<constructor><parameter name="wl"><paramtype>const std::initializer_list&lt; WeightType &gt; &amp;</paramtype></parameter><description><para>If wl.size() == 0, equivalent to the default constructor. Otherwise, the values of the <computeroutput>initializer_list</computeroutput> represent weights for the possible values of the distribution. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="Range"/>
        </template><parameter name="range"><paramtype>const Range &amp;</paramtype></parameter><description><para>If the range is empty, equivalent to the default constructor. Otherwise, the elements of the range represent weights for the possible values of the distribution. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="Func"/>
        </template><parameter name="nw"><paramtype>std::size_t</paramtype></parameter><parameter name="xmin"><paramtype>double</paramtype></parameter><parameter name="xmax"><paramtype>double</paramtype></parameter><parameter name="fw"><paramtype>Func</paramtype></parameter><description><para>If nw is zero, equivalent to the default constructor. Otherwise, the range of the distribution is [0, nw), and the weights are found by calling fw with values evenly distributed between <inlineequation><alt>$\mbox{xmin} + \delta/2$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_6.png"/></imageobject><textobject role="tex"><phrase>$\mbox{xmin} + \delta/2$</phrase></textobject></inlinemediaobject></inlineequation> and <inlineequation><alt>$\mbox{xmax} - \delta/2$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_7.png"/></imageobject><textobject role="tex"><phrase>$\mbox{xmax} - \delta/2$</phrase></textobject></inlinemediaobject></inlineequation>, where <inlineequation><alt>$\delta = (\mbox{xmax} - \mbox{xmin})/\mbox{nw}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_8.png"/></imageobject><textobject role="tex"><phrase>$\delta = (\mbox{xmax} - \mbox{xmin})/\mbox{nw}$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>WeightType</type></typedef>
<typedef name="result_type"><type>IntType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>IntType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a value distributed according to the parameters of the <classname alt="boost::random::discrete_distribution">discrete_distribution</classname>. </para></description></method>
<method name="operator()" cv="const"><type>IntType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a value distributed according to the parameters specified by param. </para></description></method>
<method name="min" cv="const"><type>result_type</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>result_type</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="probabilities" cv="const"><type>std::vector&lt; WeightType &gt;</type><description><para>Returns a vector containing the probabilities of each value of the distribution. For example, given</para><para><programlisting language="c++">discrete_distribution&lt;&gt; dist = { 1, 4, 5 };
std::vector&lt;double&gt; p = dist.param();
</programlisting></para><para>the vector, p will contain {0.1, 0.4, 0.5}.</para><para>If <computeroutput>WeightType</computeroutput> is integral, then the weights will be returned unchanged. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor><description><para>Creates a new <computeroutput><classname alt="boost::random::discrete_distribution">discrete_distribution</classname></computeroutput> object that has <inlineequation><alt>$p(0) = 1$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_4.png"/></imageobject><textobject role="tex"><phrase>$p(0) = 1$</phrase></textobject></inlinemediaobject></inlineequation> and <inlineequation><alt>$p(i|i&gt;0) = 0$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_5.png"/></imageobject><textobject role="tex"><phrase>$p(i|i&gt;0) = 0$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::discrete_distribution">discrete_distribution</classname> from an iterator range. If <computeroutput>first</computeroutput> == <computeroutput>last</computeroutput>, equivalent to the default constructor. Otherwise, the values of the range represent weights for the possible values of the distribution. </para></description></constructor>
<constructor><parameter name="wl"><paramtype>std::initializer_list&lt; WeightType &gt;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::discrete_distribution">discrete_distribution</classname></computeroutput> from a <computeroutput>std::initializer_list</computeroutput>. If the <computeroutput>initializer_list</computeroutput> is empty, equivalent to the default constructor. Otherwise, the values of the <computeroutput>initializer_list</computeroutput> represent weights for the possible values of the distribution. For example, given the distribution</para><para><programlisting language="c++">discrete_distribution&lt;&gt; dist{1, 4, 5};
</programlisting></para><para>The probability of a 0 is 1/10, the probability of a 1 is 2/5, the probability of a 2 is 1/2, and no other values are possible. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="Range"/>
        </template><parameter name="range"><paramtype>const Range &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::discrete_distribution">discrete_distribution</classname> from a Boost.Range range. If the range is empty, equivalent to the default constructor. Otherwise, the values of the range represent weights for the possible values of the distribution. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="Func"/>
        </template><parameter name="nw"><paramtype>std::size_t</paramtype></parameter><parameter name="xmin"><paramtype>double</paramtype></parameter><parameter name="xmax"><paramtype>double</paramtype></parameter><parameter name="fw"><paramtype>Func</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::discrete_distribution">discrete_distribution</classname> that approximates a function. If nw is zero, equivalent to the default constructor. Otherwise, the range of the distribution is [0, nw), and the weights are found by calling fw with values evenly distributed between <inlineequation><alt>$\mbox{xmin} + \delta/2$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_6.png"/></imageobject><textobject role="tex"><phrase>$\mbox{xmin} + \delta/2$</phrase></textobject></inlinemediaobject></inlineequation> and <inlineequation><alt>$\mbox{xmax} - \delta/2$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_7.png"/></imageobject><textobject role="tex"><phrase>$\mbox{xmax} - \delta/2$</phrase></textobject></inlinemediaobject></inlineequation>, where <inlineequation><alt>$\delta = (\mbox{xmax} - \mbox{xmin})/\mbox{nw}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_8.png"/></imageobject><textobject role="tex"><phrase>$\delta = (\mbox{xmax} - \mbox{xmin})/\mbox{nw}$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::discrete_distribution">discrete_distribution</classname> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="dd"><paramtype>const <classname>discrete_distribution</classname> &amp;</paramtype></parameter><description><para>Writes a distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="dd"><paramtype>const <classname>discrete_distribution</classname> &amp;</paramtype></parameter><description><para>Reads a distribution from a <computeroutput>std::istream</computeroutput> </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>discrete_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>discrete_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will return the same sequence of values, when passed equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>discrete_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>discrete_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may return different sequences of values, when passed equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/exponential_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="exponential_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The exponential distribution is a model of <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link>  with a single parameter lambda.</para><para>It has <inlineequation><alt>$\displaystyle p(x) = \lambda e^{-\lambda x}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_10.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \lambda e^{-\lambda x}$</phrase></textobject></inlinemediaobject></inlineequation></para><para>The implementation uses the "ziggurat" algorithm, as described in</para><para> <blockquote><para>  "The Ziggurat Method for Generating Random Variables", George Marsaglia and Wai Wan Tsang, Journal of Statistical Software Volume 5, Number 8 (2000), 1-7.  </para></blockquote>  </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>exponential_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="lambda" cv="const"><type>RealType</type><description><para>Returns the lambda parameter of the distribution. </para></description></method>
</method-group>
<constructor><parameter name="lambda"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs parameters with a given lambda.</para><para>Requires: lambda &gt; 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>RealType</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="lambda" cv="const"><type>RealType</type><description><para>Returns the lambda parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the exponential distribution. </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the exponential distribution with parameters specified by param. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="lambda"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs an <classname alt="boost::random::exponential_distribution">exponential_distribution</classname> with a given lambda.</para><para>Requires: lambda &gt; 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs an <classname alt="boost::random::exponential_distribution">exponential_distribution</classname> from its parameters </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ed"><paramtype>const <classname>exponential_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the distribution to a std::ostream. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ed"><paramtype>const <classname>exponential_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the distribution from a std::istream. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>exponential_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>exponential_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true iff the two distributions will produce identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>exponential_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>exponential_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true iff the two distributions will produce different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/extreme_value_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="extreme_value_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The extreme value distribution is a real valued distribution with two parameters a and b.</para><para>It has <inlineequation><alt>$\displaystyle p(x) = \frac{1}{b}e^{\frac{a-x}{b} - e^\frac{a-x}{b}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_11.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \frac{1}{b}e^{\frac{a-x}{b} - e^\frac{a-x}{b}}$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>extreme_value_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>RealType</type><description><para>Returns the "a" parameter of the distribtuion. </para></description></method>
<method name="b" cv="const"><type>RealType</type><description><para>Returns the "b" parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a"><paramtype>RealType</paramtype><default>1.0</default></parameter><parameter name="b"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::extreme_value_distribution::param_type">param_type</classname></computeroutput> from the "a" and "b" parameters of the distribution.</para><para>Requires: b &gt; 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::extreme_value_distribution::param_type">param_type</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::extreme_value_distribution::param_type">param_type</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the different. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>RealType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the <computeroutput><classname alt="boost::random::extreme_value_distribution">extreme_value_distribution</classname></computeroutput>. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed accordint to the extreme value distribution with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="a" cv="const"><type>RealType</type><description><para>Returns the "a" parameter of the distribution. </para></description></method>
<method name="b" cv="const"><type>RealType</type><description><para>Returns the "b" parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a"><paramtype>RealType</paramtype><default>1.0</default></parameter><parameter name="b"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::extreme_value_distribution">extreme_value_distribution</classname></computeroutput> from its "a" and "b" parameters.</para><para>Requires: b &gt; 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::extreme_value_distribution">extreme_value_distribution</classname></computeroutput> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="wd"><paramtype>const <classname>extreme_value_distribution</classname> &amp;</paramtype></parameter><description><para>Writes an <computeroutput><classname alt="boost::random::extreme_value_distribution">extreme_value_distribution</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="wd"><paramtype>const <classname>extreme_value_distribution</classname> &amp;</paramtype></parameter><description><para>Reads an <computeroutput><classname alt="boost::random::extreme_value_distribution">extreme_value_distribution</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>extreme_value_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>extreme_value_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::extreme_value_distribution">extreme_value_distribution</classname></computeroutput> will return identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>extreme_value_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>extreme_value_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::extreme_value_distribution">extreme_value_distribution</classname></computeroutput> will return different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/faure.hpp">
<namespace name="boost">
<namespace name="random">
<class name="faure_engine"><template>
      <template-type-parameter name="RealType"/>
      <template-type-parameter name="SeqSizeT"/>
      <template-type-parameter name="PrimeTable"><default>default_faure_prime_table</default></template-type-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::faure_engine">faure_engine</classname> model a <link linkend="boost_random.reference.concepts.quasi_random_number_generator">quasi-random number generator</link> . The <classname alt="boost::random::faure_engine">faure_engine</classname> uses the algorithm described in  <blockquote><para>  Henri Faure, Discrepance de suites associees a un systeme de numeration (en dimension s), Acta Arithmetica, Volume 41, 1982, pages 337-351.  </para></blockquote>   <blockquote><para>  Bennett Fox, Algorithm 647: Implementation and Relative Efficiency of Quasirandom Sequence Generators, ACM Transactions on Mathematical Software, Volume 12, Number 4, December 1986, pages 362-376.  </para></blockquote> </para><para>In the following documentation <computeroutput>X</computeroutput> denotes the concrete class of the template <classname alt="boost::random::faure_engine">faure_engine</classname> returning objects of type <computeroutput>RealType</computeroutput>, u and v are the values of <computeroutput>X</computeroutput>.</para><para>Some member functions may throw exceptions of type <computeroutput>std::bad_alloc</computeroutput>. </para></description><typedef name="result_type"><type>RealType</type></typedef>
<method-group name="friend functions">
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>faure_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>faure_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences of outputs. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>faure_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>faure_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences of outputs. </para></description></method>
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>faure_engine</classname> &amp;</paramtype></parameter><description><para>Writes the textual representation of the generator to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>faure_engine</classname> &amp;</paramtype></parameter><description><para>Reads the textual representation of the generator from a <computeroutput>std::istream</computeroutput>. </para></description></method>
</method-group>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns: Tight lower bound on the set of values returned by operator().</para><para>Throws: nothing. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns: Tight upper bound on the set of values returned by operator().</para><para>Throws: nothing. </para></description></method>
</method-group>
<method-group name="public member functions">
<method name="seed"><type>void</type><parameter name="init"><paramtype>SeqSizeT</paramtype><default>0</default></parameter><description><para>Effects: Effectively sets the quasi-random number generator state to the <computeroutput>init</computeroutput>-th vector in the <computeroutput>s</computeroutput>-dimensional quasi-random domain, where <computeroutput>s</computeroutput> == X::dimension(). <programlisting language="c++">X u, v;
for(int i = 0; i &lt; N; ++i)
    for( std::size_t j = 0; j &lt; u.dimension(); ++j )
        u();
v.seed(N);
assert(u() == v());
</programlisting> Throws: bad_alloc. </para></description></method>
<method name="dimension" cv="const"><type>std::size_t</type><description><para>Returns: The dimension of of the quasi-random domain.</para><para>Throws: nothing. </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns: Returns a successive element of an <computeroutput>s</computeroutput>-dimensional (s = X::dimension()) vector at each invocation. When all elements are exhausted, X::operator() begins anew with the starting element of a subsequent <computeroutput>s</computeroutput>-dimensional vector.</para><para>Throws: range_error. </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Effects: Advances *this state as if <computeroutput>z</computeroutput> consecutive X::operator() invocations were executed. <programlisting language="c++">X u = v;
for(int i = 0; i &lt; N; ++i)
    u();
v.discard(N);
assert(u() == v());
</programlisting></para><para>Throws: range_error. Throws: bad_alloc. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="s"><paramtype>std::size_t</paramtype></parameter><description><para>Effects: Constructs the <computeroutput>s</computeroutput>-dimensional default Faure quasi-random number generator.</para><para>Throws: bad_alloc, invalid_argument. </para></description></constructor>
</class><typedef name="faure"><description><para><note><para>This specialization of <classname alt="boost::random::faure_engine">faure_engine</classname>  supports up to 1117 dimensions.</para>
</note>
However, it is possible to provide your own prime table to <classname alt="boost::random::faure_engine">faure_engine</classname>  should the default one be insufficient. </para></description><type><classname>faure_engine</classname>&lt; double, boost::uint_least64_t, default_faure_prime_table &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/fisher_f_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="fisher_f_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The Fisher F distribution is a real valued distribution with two parameters m and n.</para><para>It has <inlineequation><alt>$\displaystyle p(x) = \frac{\Gamma((m+n)/2)}{\Gamma(m/2)\Gamma(n/2)} \left(\frac{m}{n}\right)^{m/2} x^{(m/2)-1} \left(1+\frac{mx}{n}\right)^{-(m+n)/2} $</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_12.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \frac{\Gamma((m+n)/2)}{\Gamma(m/2)\Gamma(n/2)} \left(\frac{m}{n}\right)^{m/2} x^{(m/2)-1} \left(1+\frac{mx}{n}\right)^{-(m+n)/2} $</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>fisher_f_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="m" cv="const"><type>RealType</type><description><para>Returns the "m" parameter of the distribtuion. </para></description></method>
<method name="n" cv="const"><type>RealType</type><description><para>Returns the "n" parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="m"><paramtype>RealType</paramtype><default>1.0</default></parameter><parameter name="n"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::fisher_f_distribution::param_type">param_type</classname></computeroutput> from the "m" and "n" parameters of the distribution.</para><para>Requires: m &gt; 0 and n &gt; 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::fisher_f_distribution::param_type">param_type</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::fisher_f_distribution::param_type">param_type</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the different. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>RealType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the F distribution. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the F distribution with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="m" cv="const"><type>RealType</type><description><para>Returns the "m" parameter of the distribution. </para></description></method>
<method name="n" cv="const"><type>RealType</type><description><para>Returns the "n" parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="m"><paramtype>RealType</paramtype><default>1.0</default></parameter><parameter name="n"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::fisher_f_distribution">fisher_f_distribution</classname></computeroutput> from its "m" and "n" parameters.</para><para>Requires: m &gt; 0 and n &gt; 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::fisher_f_distribution">fisher_f_distribution</classname></computeroutput> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="fd"><paramtype>const <classname>fisher_f_distribution</classname> &amp;</paramtype></parameter><description><para>Writes an <computeroutput><classname alt="boost::random::fisher_f_distribution">fisher_f_distribution</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="fd"><paramtype>const <classname>fisher_f_distribution</classname> &amp;</paramtype></parameter><description><para>Reads an <computeroutput><classname alt="boost::random::fisher_f_distribution">fisher_f_distribution</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>fisher_f_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>fisher_f_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::fisher_f_distribution">fisher_f_distribution</classname></computeroutput> will return identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>fisher_f_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>fisher_f_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::fisher_f_distribution">fisher_f_distribution</classname></computeroutput> will return different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/gamma_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="gamma_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The gamma distribution is a continuous distribution with two parameters alpha and beta. It produces values &gt; 0.</para><para>It has <inlineequation><alt>$\displaystyle p(x) = x^{\alpha-1}\frac{e^{-x/\beta}}{\beta^\alpha\Gamma(\alpha)}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_13.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = x^{\alpha-1}\frac{e^{-x/\beta}}{\beta^\alpha\Gamma(\alpha)}$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>gamma_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="alpha" cv="const"><type>RealType</type><description><para>Returns the "alpha" parameter of the distribution. </para></description></method>
<method name="beta" cv="const"><type>RealType</type><description><para>Returns the "beta" parameter of the distribution. </para></description></method>
</method-group>
<constructor><parameter name="alpha"><paramtype>const RealType &amp;</paramtype><default>1.0</default></parameter><parameter name="beta"><paramtype>const RealType &amp;</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::gamma_distribution::param_type">param_type</classname></computeroutput> object from the "alpha" and "beta" parameters.</para><para>Requires: alpha &gt; 0 &amp;&amp; beta &gt; 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype><classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets fo parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>RealType</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="alpha" cv="const"><type>RealType</type><description><para>Returns the "alpha" paramter of the distribution. </para></description></method>
<method name="beta" cv="const"><type>RealType</type><description><para>Returns the "beta" parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the gamma distribution. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="alpha"><paramtype>const result_type &amp;</paramtype><default>1.0</default></parameter><parameter name="beta"><paramtype>const result_type &amp;</paramtype><default>1.0</default></parameter><description><para>Creates a new <classname alt="boost::random::gamma_distribution">gamma_distribution</classname> with parameters "alpha" and "beta".</para><para>Requires: alpha &gt; 0 &amp;&amp; beta &gt; 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::gamma_distribution">gamma_distribution</classname></computeroutput> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="gd"><paramtype>const <classname>gamma_distribution</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::gamma_distribution">gamma_distribution</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="gd"><paramtype><classname>gamma_distribution</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::gamma_distribution">gamma_distribution</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>gamma_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>gamma_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce identical sequences of random variates given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>gamma_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>gamma_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions can produce different sequences of random variates, given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/generate_canonical.hpp">
<namespace name="boost">
<namespace name="random">
<function name="generate_canonical"><type>RealType</type><template>
          <template-type-parameter name="RealType"/>
          <template-nontype-parameter name="bits"><type>std::size_t</type></template-nontype-parameter>
          <template-type-parameter name="URNG"/>
        </template><parameter name="g"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a value uniformly distributed in the range [0, 1) with at least <computeroutput>bits</computeroutput> random bits. </para></description></function>
</namespace>
</namespace>
</header>
<header name="boost/random/geometric_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="geometric_distribution"><template>
      <template-type-parameter name="IntType"><default>int</default></template-type-parameter>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>An instantiation of the class template <computeroutput><classname alt="boost::random::geometric_distribution">geometric_distribution</classname></computeroutput> models a <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . The distribution produces positive integers which are the number of bernoulli trials with probability <computeroutput>p</computeroutput> required to get one that fails.</para><para>For the geometric distribution, <inlineequation><alt>$p(i) = p(1-p)^{i}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_14.png"/></imageobject><textobject role="tex"><phrase>$p(i) = p(1-p)^{i}$</phrase></textobject></inlinemediaobject></inlineequation>.</para><para> <warning><para>  This distribution has been updated to match the C++ standard. Its behavior has changed from the original boost::geometric_distribution. A backwards compatible wrapper is provided in namespace boost.  </para></warning>  </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>geometric_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="p" cv="const"><type>RealType</type><description><para>Returns the p parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="p"><paramtype>RealType</paramtype><default>0.5</default></parameter><description><para>Constructs the parameters with p. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a std::ostream. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a std::istream. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>RealType</type></typedef>
<typedef name="result_type"><type>IntType</type></typedef>
<method-group name="public member functions">
<method name="p" cv="const"><type>RealType</type><description><para>Returns: the distribution parameter <computeroutput>p</computeroutput> </para></description></method>
<method name="min" cv="const"><type>IntType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>IntType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the <classname alt="boost::random::geometric_distribution">geometric_distribution</classname>. </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the geometric distribution with parameters specified by param. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="p"><paramtype>const RealType &amp;</paramtype><default>0.5</default></parameter><description><para>Contructs a new <classname alt="boost::random::geometric_distribution">geometric_distribution</classname> with the paramter <computeroutput>p</computeroutput>.</para><para>Requires: 0 &lt; p &lt; 1 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a new <classname alt="boost::random::geometric_distribution">geometric_distribution</classname> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="gd"><paramtype>const <classname>geometric_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="gd"><paramtype>const <classname>geometric_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>geometric_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>geometric_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>geometric_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>geometric_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may produce different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/hyperexponential_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="hyperexponential_distribution"><template>
      <template-type-parameter name="RealT"><default>double</default></template-type-parameter>
    </template><description><para>The hyperexponential distribution is a real-valued continuous distribution with two parameters, the <emphasis>phase probability vector</emphasis> <computeroutput>probs</computeroutput> and the <emphasis>rate vector</emphasis> <computeroutput>rates</computeroutput>.</para><para>A <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation>-phase hyperexponential distribution is a mixture of <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> exponential distributions. For this reason, it is also referred to as <emphasis>mixed exponential distribution</emphasis> or <emphasis>parallel <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation>-phase exponential distribution</emphasis>.</para><para>A <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation>-phase hyperexponential distribution is characterized by two parameters, namely a <emphasis>phase probability vector</emphasis> <inlineequation><alt>$\mathbf{\alpha}=(\alpha_1,\ldots,\alpha_k)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_16.png"/></imageobject><textobject role="tex"><phrase>$\mathbf{\alpha}=(\alpha_1,\ldots,\alpha_k)$</phrase></textobject></inlinemediaobject></inlineequation> and a <emphasis>rate vector</emphasis> <inlineequation><alt>$\mathbf{\lambda}=(\lambda_1,\ldots,\lambda_k)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_17.png"/></imageobject><textobject role="tex"><phrase>$\mathbf{\lambda}=(\lambda_1,\ldots,\lambda_k)$</phrase></textobject></inlinemediaobject></inlineequation>.</para><para>A <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation>-phase hyperexponential distribution is frequently used in <emphasis>queueing theory</emphasis> to model the distribution of the superposition of <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> independent events, like, for instance, the service time distribution of a queueing station with <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> servers in parallel where the <inlineequation><alt>$i$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_18.png"/></imageobject><textobject role="tex"><phrase>$i$</phrase></textobject></inlinemediaobject></inlineequation>-th server is chosen with probability <inlineequation><alt>$\alpha_i$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_19.png"/></imageobject><textobject role="tex"><phrase>$\alpha_i$</phrase></textobject></inlinemediaobject></inlineequation> and its service time distribution is an exponential distribution with rate <inlineequation><alt>$\lambda_i$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_20.png"/></imageobject><textobject role="tex"><phrase>$\lambda_i$</phrase></textobject></inlinemediaobject></inlineequation> (Allen,1990; Papadopolous et al.,1993; Trivedi,2002).</para><para>For instance, CPUs service-time distribution in a computing system has often been observed to possess such a distribution (Rosin,1965). Also, the arrival of different types of customer to a single queueing station is often modeled as a hyperexponential distribution (Papadopolous et al.,1993). Similarly, if a product manufactured in several parallel assemply lines and the outputs are merged, the failure density of the overall product is likely to be hyperexponential (Trivedi,2002).</para><para>Finally, since the hyperexponential distribution exhibits a high Coefficient of Variation (CoV), that is a CoV &gt; 1, it is especially suited to fit empirical data with large CoV (Feitelson,2014; Wolski et al.,2013) and to approximate <emphasis>long-tail probability distributions</emphasis> (Feldmann et al.,1998).</para><para>See (Boost,2014) for more information and examples.</para><para>A <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation>-phase hyperexponential distribution has a probability density function <equation><title/><alt>\[ f(x) = \sum_{i=1}^k \alpha_i \lambda_i e^{-x\lambda_i} \]</alt><mediaobject><imageobject role="html"><imagedata format="PNG" align="center" fileref="images/random//form_21.png"/></imageobject><textobject role="tex"><phrase>\[ f(x) = \sum_{i=1}^k \alpha_i \lambda_i e^{-x\lambda_i} \]</phrase></textobject></mediaobject></equation> where:<itemizedlist>
<listitem><para><inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> is the <emphasis>number of phases</emphasis> and also the size of the input vector parameters,</para>
</listitem><listitem><para><inlineequation><alt>$\mathbf{\alpha}=(\alpha_1,\ldots,\alpha_k)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_16.png"/></imageobject><textobject role="tex"><phrase>$\mathbf{\alpha}=(\alpha_1,\ldots,\alpha_k)$</phrase></textobject></inlinemediaobject></inlineequation> is the <emphasis>phase probability vector</emphasis> parameter, and</para>
</listitem><listitem><para><inlineequation><alt>$\mathbf{\lambda}=(\lambda_1,\ldots,\lambda_k)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_17.png"/></imageobject><textobject role="tex"><phrase>$\mathbf{\lambda}=(\lambda_1,\ldots,\lambda_k)$</phrase></textobject></inlinemediaobject></inlineequation> is the <emphasis>rate vector</emphasis> parameter.</para>
</listitem></itemizedlist>
Given a <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation>-phase hyperexponential distribution with phase probability vector <inlineequation><alt>$\mathbf{\alpha}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_22.png"/></imageobject><textobject role="tex"><phrase>$\mathbf{\alpha}$</phrase></textobject></inlinemediaobject></inlineequation> and rate vector <inlineequation><alt>$\mathbf{\lambda}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_23.png"/></imageobject><textobject role="tex"><phrase>$\mathbf{\lambda}$</phrase></textobject></inlinemediaobject></inlineequation>, the random variate generation algorithm consists of the following steps (Tyszer,1999):<orderedlist>
<listitem><para>Generate a random variable <inlineequation><alt>$U$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_24.png"/></imageobject><textobject role="tex"><phrase>$U$</phrase></textobject></inlinemediaobject></inlineequation> uniformly distribution on the interval <inlineequation><alt>$(0,1)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_25.png"/></imageobject><textobject role="tex"><phrase>$(0,1)$</phrase></textobject></inlinemediaobject></inlineequation>.</para>
</listitem><listitem><para>Use <inlineequation><alt>$U$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_24.png"/></imageobject><textobject role="tex"><phrase>$U$</phrase></textobject></inlinemediaobject></inlineequation> to select the appropriate <inlineequation><alt>$\lambda_i$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_20.png"/></imageobject><textobject role="tex"><phrase>$\lambda_i$</phrase></textobject></inlinemediaobject></inlineequation> (e.g., the <emphasis>alias method</emphasis> can possibly be used for this step).</para>
</listitem><listitem><para>Generate an exponentially distributed random variable <inlineequation><alt>$X$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_26.png"/></imageobject><textobject role="tex"><phrase>$X$</phrase></textobject></inlinemediaobject></inlineequation> with rate parameter <inlineequation><alt>$\lambda_i$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_20.png"/></imageobject><textobject role="tex"><phrase>$\lambda_i$</phrase></textobject></inlinemediaobject></inlineequation>.</para>
</listitem><listitem><para>Return <inlineequation><alt>$X$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_26.png"/></imageobject><textobject role="tex"><phrase>$X$</phrase></textobject></inlinemediaobject></inlineequation>.</para>
</listitem></orderedlist>
References:<orderedlist>
<listitem><para>A.O. Allen, <emphasis>Probability, Statistics, and Queuing Theory with Computer Science Applications, Second Edition</emphasis>, Academic Press, 1990.</para>
</listitem><listitem><para>Boost C++ Libraries, <emphasis>Boost.Math / Statistical Distributions: Hyperexponential Distribution</emphasis>, Online: <ulink url="http://www.boost.org/doc/libs/release/libs/math/doc/html/dist.html">http://www.boost.org/doc/libs/release/libs/math/doc/html/dist.html</ulink> , 2014.</para>
</listitem><listitem><para>D.G. Feitelson, <emphasis>Workload Modeling for Computer Systems Performance Evaluation</emphasis>, Cambridge University Press, 2014</para>
</listitem><listitem><para>A. Feldmann and W. Whitt, <emphasis>Fitting mixtures of exponentials to long-tail distributions to analyze network performance models</emphasis>, Performance Evaluation 31(3-4):245, doi:10.1016/S0166-5316(97)00003-5, 1998.</para>
</listitem><listitem><para>H.T. Papadopolous, C. Heavey and J. Browne, <emphasis>Queueing Theory in Manufacturing Systems Analysis and Design</emphasis>, Chapman &amp; Hall/CRC, 1993, p. 35.</para>
</listitem><listitem><para>R.F. Rosin, <emphasis>Determining a computing center environment</emphasis>, Communications of the ACM 8(7):463-468, 1965.</para>
</listitem><listitem><para>K.S. Trivedi, <emphasis>Probability and Statistics with Reliability, Queueing, and Computer Science Applications</emphasis>, John Wiley &amp; Sons, Inc., 2002.</para>
</listitem><listitem><para>J. Tyszer, <emphasis>Object-Oriented Computer Simulation of Discrete-Event Systems</emphasis>, Springer, 1999.</para>
</listitem><listitem><para>Wikipedia, <emphasis>Hyperexponential Distribution</emphasis>, Online: <ulink url="http://en.wikipedia.org/wiki/Hyperexponential_distribution">http://en.wikipedia.org/wiki/Hyperexponential_distribution</ulink> , 2014.</para>
</listitem><listitem><para>Wolfram Mathematica, <emphasis>Hyperexponential Distribution</emphasis>, Online: <ulink url="http://reference.wolfram.com/language/ref/HyperexponentialDistribution.html">http://reference.wolfram.com/language/ref/HyperexponentialDistribution.html</ulink> , 2014.</para>
</listitem></orderedlist>
<para>Marco Guazzone (<ulink url="mailto:<EMAIL>"><EMAIL></ulink>) </para>

</para></description><class name="param_type"><description><para>The parameters of a hyperexponential distribution.</para><para>Stores the <emphasis>phase probability vector</emphasis> and the <emphasis>rate vector</emphasis> of the hyperexponential distribution.</para><para><para>Marco Guazzone (<ulink url="mailto:<EMAIL>"><EMAIL></ulink>) </para>

</para></description><typedef name="distribution_type"><type><classname>hyperexponential_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="probabilities" cv="const"><type>std::vector&lt; RealT &gt;</type><description><para>Gets the <emphasis>phase probability vector</emphasis> parameter of the distribtuion.</para><para>
<note><para>The returned probabilities are the normalized version of the ones passed at construction time. </para>
</note>
</para></description><returns><para>The <emphasis>phase probability vector</emphasis> parameter of the distribution.</para>
</returns></method>
<method name="rates" cv="const"><type>std::vector&lt; RealT &gt;</type><description><para>Gets the <emphasis>rate vector</emphasis> parameter of the distribtuion.</para><para>
</para></description><returns><para>The <emphasis>rate vector</emphasis> parameter of the distribution. </para>
</returns></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> with the default parameters of the distribution. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="ProbIterT"><purpose><para>Must meet the requirements of <computeroutput>InputIterator</computeroutput> concept (ISO,2014,sec. 24.2.3 [input.iterators]). </para></purpose></template-type-parameter>
          <template-type-parameter name="RateIterT"><purpose><para>Must meet the requirements of <computeroutput>InputIterator</computeroutput> concept (ISO,2014,sec. 24.2.3 [input.iterators]).</para></purpose></template-type-parameter>
        </template><parameter name="prob_first"><paramtype>ProbIterT</paramtype><description><para>The iterator to the beginning of the range of non-negative real elements representing the phase probabilities; if elements don't sum to 1, they are normalized. </para></description></parameter><parameter name="prob_last"><paramtype>ProbIterT</paramtype><description><para>The iterator to the ending of the range of non-negative real elements representing the phase probabilities; if elements don't sum to 1, they are normalized. </para></description></parameter><parameter name="rate_first"><paramtype>RateIterT</paramtype><description><para>The iterator to the beginning of the range of non-negative real elements representing the rates. </para></description></parameter><parameter name="rate_last"><paramtype>RateIterT</paramtype><description><para>The iterator to the ending of the range of non-negative real elements representing the rates.</para></description></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> from the <emphasis>phase probability vector</emphasis> and <emphasis>rate vector</emphasis> parameters of the distribution.</para><para>The <emphasis>phase probability vector</emphasis> parameter is given by the range defined by [<emphasis>prob_first</emphasis>, <emphasis>prob_last</emphasis>) iterator pair, and the <emphasis>rate vector</emphasis> parameter is given by the range defined by [<emphasis>rate_first</emphasis>, <emphasis>rate_last</emphasis>) iterator pair.</para><para>

References:<orderedlist>
<listitem><para>ISO, <emphasis>ISO/IEC 14882-2014: Information technology - Programming languages - C++</emphasis>, 2014</para>
</listitem></orderedlist>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="ProbRangeT"><purpose><para>Must meet the requirements of <ulink url="boost:/libs/range/doc/html/range/concepts.html">Range</ulink> concept. </para></purpose></template-type-parameter>
          <template-type-parameter name="RateRangeT"><purpose><para>Must meet the requirements of <ulink url="boost:/libs/range/doc/html/range/concepts.html">Range</ulink> concept.</para></purpose></template-type-parameter>
        </template><parameter name="prob_range"><paramtype>ProbRangeT const &amp;</paramtype><description><para>The range of non-negative real elements representing the phase probabilities; if elements don't sum to 1, they are normalized. </para></description></parameter><parameter name="rate_range"><paramtype>RateRangeT const &amp;</paramtype><description><para>The range of positive real elements representing the rates.</para></description></parameter><parameter name=""><paramtype>typename boost::disable_if_c&lt; boost::has_pre_increment&lt; ProbRangeT &gt;::value||boost::has_pre_increment&lt; RateRangeT &gt;::value &gt;::type *</paramtype><default>0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> from the <emphasis>phase probability vector</emphasis> and <emphasis>rate vector</emphasis> parameters of the distribution.</para><para>The <emphasis>phase probability vector</emphasis> parameter is given by the range defined by <emphasis>prob_range</emphasis>, and the <emphasis>rate vector</emphasis> parameter is given by the range defined by <emphasis>rate_range</emphasis>.</para><para>

<note><para>The final <computeroutput>disable_if</computeroutput> parameter is an implementation detail that differentiates between this two argument constructor and the iterator-based two argument constructor described below. </para>
</note>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="RateIterT"><purpose><para>Must meet the requirements of <computeroutput>InputIterator</computeroutput> concept (ISO,2014,sec. 24.2.3 [input.iterators]). </para></purpose></template-type-parameter>
        </template><parameter name="rate_first"><paramtype>RateIterT</paramtype><description><para>The iterator to the beginning of the range of non-negative real elements representing the rates. </para></description></parameter><parameter name="rate_last"><paramtype>RateIterT</paramtype><description><para>The iterator to the ending of the range of non-negative real elements representing the rates.</para></description></parameter><parameter name=""><paramtype>typename boost::enable_if_c&lt; boost::has_pre_increment&lt; RateIterT &gt;::value &gt;::type *</paramtype><default>0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> from the <emphasis>rate vector</emphasis> parameter of the distribution and with equal phase probabilities.</para><para>The <emphasis>rate vector</emphasis> parameter is given by the range defined by [<emphasis>rate_first</emphasis>, <emphasis>rate_last</emphasis>) iterator pair, and the <emphasis>phase probability vector</emphasis> parameter is set to the equal phase probabilities (i.e., to a vector of the same length <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> of the <emphasis>rate vector</emphasis> and with each element set to <inlineequation><alt>$1.0/k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_27.png"/></imageobject><textobject role="tex"><phrase>$1.0/k$</phrase></textobject></inlinemediaobject></inlineequation>).</para><para>

<note><para>The final <computeroutput>disable_if</computeroutput> parameter is an implementation detail that differentiates between this two argument constructor and the range-based two argument constructor described above.</para>
</note>
References:<orderedlist>
<listitem><para>ISO, <emphasis>ISO/IEC 14882-2014: Information technology - Programming languages - C++</emphasis>, 2014</para>
</listitem></orderedlist>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="RateRangeT"><purpose><para>Must meet the requirements of <ulink url="boost:/libs/range/doc/html/range/concepts.html">Range</ulink> concept.</para></purpose></template-type-parameter>
        </template><parameter name="rate_range"><paramtype>RateRangeT const &amp;</paramtype><description><para>The range of positive real elements representing the rates. </para></description></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> from the "rates" parameters of the distribution and with equal phase probabilities.</para><para>The <emphasis>rate vector</emphasis> parameter is given by the range defined by <emphasis>rate_range</emphasis>, and the <emphasis>phase probability vector</emphasis> parameter is set to the equal phase probabilities (i.e., to a vector of the same length <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> of the <emphasis>rate vector</emphasis> and with each element set to <inlineequation><alt>$1.0/k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_27.png"/></imageobject><textobject role="tex"><phrase>$1.0/k$</phrase></textobject></inlinemediaobject></inlineequation>).</para><para>

</para></description></constructor>
<constructor><parameter name="l1"><paramtype>std::initializer_list&lt; RealT &gt;</paramtype><description><para>The initializer list for inizializing the phase probability vector. </para></description></parameter><parameter name="l2"><paramtype>std::initializer_list&lt; RealT &gt;</paramtype><description><para>The initializer list for inizializing the rate vector.</para></description></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> from the <emphasis>phase probability vector</emphasis> and <emphasis>rate vector</emphasis> parameters of the distribution.</para><para>The <emphasis>phase probability vector</emphasis> parameter is given by the <emphasis>brace-init-list</emphasis> (ISO,2014,sec. 8.5.4 [dcl.init.list]) defined by <emphasis>l1</emphasis>, and the <emphasis>rate vector</emphasis> parameter is given by the <emphasis>brace-init-list</emphasis> (ISO,2014,sec. 8.5.4 [dcl.init.list]) defined by <emphasis>l2</emphasis>.</para><para>
References:<orderedlist>
<listitem><para>ISO, <emphasis>ISO/IEC 14882-2014: Information technology - Programming languages - C++</emphasis>, 2014</para>
</listitem></orderedlist>
</para></description></constructor>
<constructor><parameter name="l1"><paramtype>std::initializer_list&lt; RealT &gt;</paramtype><description><para>The initializer list for inizializing the rate vector.</para></description></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> from the <emphasis>rate vector</emphasis> parameter of the distribution and with equal phase probabilities.</para><para>The <emphasis>rate vector</emphasis> parameter is given by the <emphasis>brace-init-list</emphasis> (ISO,2014,sec. 8.5.4 [dcl.init.list]) defined by <emphasis>l1</emphasis>, and the <emphasis>phase probability vector</emphasis> parameter is set to the equal phase probabilities (i.e., to a vector of the same length <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> of the <emphasis>rate vector</emphasis> and with each element set to <inlineequation><alt>$1.0/k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_27.png"/></imageobject><textobject role="tex"><phrase>$1.0/k$</phrase></textobject></inlinemediaobject></inlineequation>).</para><para>
References:<orderedlist>
<listitem><para>ISO, <emphasis>ISO/IEC 14882-2014: Information technology - Programming languages - C++</emphasis>, 2014</para>
</listitem></orderedlist>
</para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the different. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>RealT</type></typedef>
<typedef name="input_type"><type>RealT</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>RealT</type><template>
          <template-type-parameter name="URNG"><purpose><para>Must meet the requirements of <link linkend="boost_random.reference.concepts.uniform_random_number_generator">uniform random number generator</link> .</para></purpose></template-type-parameter>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype><description><para>A uniform random number generator object.</para></description></parameter><description><para>Gets a random variate distributed according to the hyperexponential distribution.</para><para>


</para></description><returns><para>A random variate distributed according to the hyperexponential distribution. </para>
</returns></method>
<method name="operator()" cv="const"><type>RealT</type><template>
          <template-type-parameter name="URNG"><purpose><para>Must meet the requirements of <link linkend="boost_random.reference.concepts.uniform_random_number_generator">uniform random number generator</link> .</para></purpose></template-type-parameter>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype><description><para>A uniform random number generator object. </para></description></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype><description><para>A distribution parameter object.</para></description></parameter><description><para>Gets a random variate distributed according to the hyperexponential distribution with parameters specified by <computeroutput>param</computeroutput>.</para><para>


</para></description><returns><para>A random variate distributed according to the hyperexponential distribution. distribution with parameters specified by <computeroutput>param</computeroutput>. </para>
</returns></method>
<method name="num_phases" cv="const"><type>std::size_t</type><description><para>Returns the number of phases of the distribution. </para></description></method>
<method name="probabilities" cv="const"><type>std::vector&lt; RealT &gt;</type><description><para>Returns the <emphasis>phase probability vector</emphasis> parameter of the distribution. </para></description></method>
<method name="rates" cv="const"><type>std::vector&lt; RealT &gt;</type><description><para>Returns the <emphasis>rate vector</emphasis> parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealT</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealT</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype><classname>param_type</classname> const &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor><description><para>Constructs a 1-phase <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> (i.e., an exponential distribution) with rate 1. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="ProbIterT"><purpose><para>Must meet the requirements of <computeroutput>InputIterator</computeroutput> concept (ISO,2014,sec. 24.2.3 [input.iterators]). </para></purpose></template-type-parameter>
          <template-type-parameter name="RateIterT"><purpose><para>Must meet the requirements of <computeroutput>InputIterator</computeroutput> concept (ISO,2014,sec. 24.2.3 [input.iterators]).</para></purpose></template-type-parameter>
        </template><parameter name="prob_first"><paramtype>ProbIterT</paramtype><description><para>The iterator to the beginning of the range of non-negative real elements representing the phase probabilities; if elements don't sum to 1, they are normalized. </para></description></parameter><parameter name="prob_last"><paramtype>ProbIterT</paramtype><description><para>The iterator to the ending of the range of non-negative real elements representing the phase probabilities; if elements don't sum to 1, they are normalized. </para></description></parameter><parameter name="rate_first"><paramtype>RateIterT</paramtype><description><para>The iterator to the beginning of the range of non-negative real elements representing the rates. </para></description></parameter><parameter name="rate_last"><paramtype>RateIterT</paramtype><description><para>The iterator to the ending of the range of non-negative real elements representing the rates.</para></description></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> from the <emphasis>phase probability vector</emphasis> and <emphasis>rate vector</emphasis> parameters of the distribution.</para><para>The <emphasis>phase probability vector</emphasis> parameter is given by the range defined by [<emphasis>prob_first</emphasis>, <emphasis>prob_last</emphasis>) iterator pair, and the <emphasis>rate vector</emphasis> parameter is given by the range defined by [<emphasis>rate_first</emphasis>, <emphasis>rate_last</emphasis>) iterator pair.</para><para>

References:<orderedlist>
<listitem><para>ISO, <emphasis>ISO/IEC 14882-2014: Information technology - Programming languages - C++</emphasis>, 2014</para>
</listitem></orderedlist>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="ProbRangeT"><purpose><para>Must meet the requirements of <ulink url="boost:/libs/range/doc/html/range/concepts.html">Range</ulink> concept. </para></purpose></template-type-parameter>
          <template-type-parameter name="RateRangeT"><purpose><para>Must meet the requirements of <ulink url="boost:/libs/range/doc/html/range/concepts.html">Range</ulink> concept.</para></purpose></template-type-parameter>
        </template><parameter name="prob_range"><paramtype>ProbRangeT const &amp;</paramtype><description><para>The range of non-negative real elements representing the phase probabilities; if elements don't sum to 1, they are normalized. </para></description></parameter><parameter name="rate_range"><paramtype>RateRangeT const &amp;</paramtype><description><para>The range of positive real elements representing the rates.</para></description></parameter><parameter name=""><paramtype>typename boost::disable_if_c&lt; boost::has_pre_increment&lt; ProbRangeT &gt;::value||boost::has_pre_increment&lt; RateRangeT &gt;::value &gt;::type *</paramtype><default>0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> from the <emphasis>phase probability vector</emphasis> and <emphasis>rate vector</emphasis> parameters of the distribution.</para><para>The <emphasis>phase probability vector</emphasis> parameter is given by the range defined by <emphasis>prob_range</emphasis>, and the <emphasis>rate vector</emphasis> parameter is given by the range defined by <emphasis>rate_range</emphasis>.</para><para>

<note><para>The final <computeroutput>disable_if</computeroutput> parameter is an implementation detail that differentiates between this two argument constructor and the iterator-based two argument constructor described below. </para>
</note>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="RateIterT"><purpose><para>Must meet the requirements of <computeroutput>InputIterator</computeroutput> concept (ISO,2014,sec. 24.2.3 [input.iterators]). </para></purpose></template-type-parameter>
        </template><parameter name="rate_first"><paramtype>RateIterT</paramtype><description><para>The iterator to the beginning of the range of non-negative real elements representing the rates. </para></description></parameter><parameter name="rate_last"><paramtype>RateIterT</paramtype><description><para>The iterator to the ending of the range of non-negative real elements representing the rates.</para></description></parameter><parameter name=""><paramtype>typename boost::enable_if_c&lt; boost::has_pre_increment&lt; RateIterT &gt;::value &gt;::type *</paramtype><default>0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> from the <emphasis>rate vector</emphasis> parameter of the distribution and with equal phase probabilities.</para><para>The <emphasis>rate vector</emphasis> parameter is given by the range defined by [<emphasis>rate_first</emphasis>, <emphasis>rate_last</emphasis>) iterator pair, and the <emphasis>phase probability vector</emphasis> parameter is set to the equal phase probabilities (i.e., to a vector of the same length <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> of the <emphasis>rate vector</emphasis> and with each element set to <inlineequation><alt>$1.0/k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_27.png"/></imageobject><textobject role="tex"><phrase>$1.0/k$</phrase></textobject></inlinemediaobject></inlineequation>).</para><para>

<note><para>The final <computeroutput>disable_if</computeroutput> parameter is an implementation detail that differentiates between this two argument constructor and the range-based two argument constructor described above.</para>
</note>
References:<orderedlist>
<listitem><para>ISO, <emphasis>ISO/IEC 14882-2014: Information technology - Programming languages - C++</emphasis>, 2014</para>
</listitem></orderedlist>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="RateRangeT"><purpose><para>Must meet the requirements of <ulink url="boost:/libs/range/doc/html/range/concepts.html">Range</ulink> concept.</para></purpose></template-type-parameter>
        </template><parameter name="rate_range"><paramtype>RateRangeT const &amp;</paramtype><description><para>The range of positive real elements representing the rates. </para></description></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution::param_type">param_type</classname></computeroutput> from the "rates" parameters of the distribution and with equal phase probabilities.</para><para>The <emphasis>rate vector</emphasis> parameter is given by the range defined by <emphasis>rate_range</emphasis>, and the <emphasis>phase probability vector</emphasis> parameter is set to the equal phase probabilities (i.e., to a vector of the same length <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> of the <emphasis>rate vector</emphasis> and with each element set to <inlineequation><alt>$1.0/k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_27.png"/></imageobject><textobject role="tex"><phrase>$1.0/k$</phrase></textobject></inlinemediaobject></inlineequation>).</para><para>

</para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype><classname>param_type</classname> const &amp;</paramtype><description><para>The parameters of the distribution. </para></description></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> from its parameters.</para><para>
</para></description></constructor>
<constructor><parameter name="l1"><paramtype>std::initializer_list&lt; RealT &gt; const &amp;</paramtype><description><para>The initializer list for inizializing the phase probability vector. </para></description></parameter><parameter name="l2"><paramtype>std::initializer_list&lt; RealT &gt; const &amp;</paramtype><description><para>The initializer list for inizializing the rate vector.</para></description></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> from the <emphasis>phase probability vector</emphasis> and <emphasis>rate vector</emphasis> parameters of the distribution.</para><para>The <emphasis>phase probability vector</emphasis> parameter is given by the <emphasis>brace-init-list</emphasis> (ISO,2014,sec. 8.5.4 [dcl.init.list]) defined by <emphasis>l1</emphasis>, and the <emphasis>rate vector</emphasis> parameter is given by the <emphasis>brace-init-list</emphasis> (ISO,2014,sec. 8.5.4 [dcl.init.list]) defined by <emphasis>l2</emphasis>.</para><para>
References:<orderedlist>
<listitem><para>ISO, <emphasis>ISO/IEC 14882-2014: Information technology - Programming languages - C++</emphasis>, 2014</para>
</listitem></orderedlist>
</para></description></constructor>
<constructor><parameter name="l1"><paramtype>std::initializer_list&lt; RealT &gt; const &amp;</paramtype><description><para>The initializer list for inizializing the rate vector.</para></description></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> from the <emphasis>rate vector</emphasis> parameter of the distribution and with equal phase probabilities.</para><para>The <emphasis>rate vector</emphasis> parameter is given by the <emphasis>brace-init-list</emphasis> (ISO,2014,sec. 8.5.4 [dcl.init.list]) defined by <emphasis>l1</emphasis>, and the <emphasis>phase probability vector</emphasis> parameter is set to the equal phase probabilities (i.e., to a vector of the same length <inlineequation><alt>$k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_15.png"/></imageobject><textobject role="tex"><phrase>$k$</phrase></textobject></inlinemediaobject></inlineequation> of the <emphasis>rate vector</emphasis> and with each element set to <inlineequation><alt>$1.0/k$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_27.png"/></imageobject><textobject role="tex"><phrase>$1.0/k$</phrase></textobject></inlinemediaobject></inlineequation>).</para><para>
References:<orderedlist>
<listitem><para>ISO, <emphasis>ISO/IEC 14882-2014: Information technology - Programming languages - C++</emphasis>, 2014</para>
</listitem></orderedlist>
</para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="hd"><paramtype>const <classname>hyperexponential_distribution</classname> &amp;</paramtype></parameter><description><para>Writes an <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="hd"><paramtype>const <classname>hyperexponential_distribution</classname> &amp;</paramtype></parameter><description><para>Reads an <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>hyperexponential_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>hyperexponential_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> will return identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>hyperexponential_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>hyperexponential_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::hyperexponential_distribution">hyperexponential_distribution</classname></computeroutput> will return different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/independent_bits.hpp">
<namespace name="boost">
<namespace name="random">
<class name="independent_bits_engine"><template>
      <template-type-parameter name="Engine"/>
      <template-nontype-parameter name="w"><type>std::size_t</type></template-nontype-parameter>
      <template-type-parameter name="UIntType"/>
    </template><description><para>An instantiation of class template <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput> model a <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It generates random numbers distributed between [0, 2^w) by combining one or more invocations of the base engine.</para><para>Requires: 0 &lt; w &lt;= std::numeric_limits&lt;UIntType&gt;::digits </para></description><typedef name="base_type"><type>Engine</type></typedef>
<typedef name="result_type"><type>UIntType</type></typedef>
<typedef name="base_result_type"><type>Engine::result_type</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce. </para></description></method>
</method-group>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Seeds an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput> using the default seed of the base generator. </para></description></method>
<method name="seed"><type>void</type><parameter name="seed"><paramtype>base_result_type</paramtype></parameter><description><para>Seeds an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput>, using <computeroutput>seed</computeroutput> as the seed for the base generator. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput>, using <computeroutput>seq</computeroutput> to seed the base generator. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Seeds an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput> with values from the range defined by the input iterators first and last. first will be modified to point to the element after the last one used.</para><para>Throws: <computeroutput>std::invalid_argument</computeroutput> if the input range is too small.</para><para>Exception Safety: Basic </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
<method name="base" cv="const"><type>const base_type &amp;</type></method>
</method-group>
<constructor><description><para>Constructs an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput> using the default constructor of the base generator. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="seed"><paramtype>base_result_type</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput>, using seed as the constructor argument for both base generators. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput>, using seq as the constructor argument for the base generator. </para></description></constructor>
<constructor><parameter name="base_arg"><paramtype>const base_type &amp;</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput> by copying <computeroutput>base</computeroutput>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Contructs an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput> with values from the range defined by the input iterators first and last. first will be modified to point to the element after the last one used.</para><para>Throws: <computeroutput>std::invalid_argument</computeroutput> if the input range is too small.</para><para>Exception Safety: Basic </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="r"><paramtype>const <classname>independent_bits_engine</classname> &amp;</paramtype></parameter><description><para>Writes the textual representation if the generator to a <computeroutput>std::ostream</computeroutput>. The textual representation of the engine is the textual representation of the base engine. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="r"><paramtype>const <classname>independent_bits_engine</classname> &amp;</paramtype></parameter><description><para>Reads the state of an <computeroutput><classname alt="boost::random::independent_bits_engine">independent_bits_engine</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>independent_bits_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>independent_bits_engine</classname> &amp;</paramtype></parameter><description><para>Returns: true iff the two <computeroutput>independent_bits_engines</computeroutput> will produce the same sequence of values. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>independent_bits_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>independent_bits_engine</classname> &amp;</paramtype></parameter><description><para>Returns: true iff the two <computeroutput>independent_bits_engines</computeroutput> will produce different sequences of values. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/inversive_congruential.hpp">
<namespace name="boost">
<namespace name="random">
<class name="inversive_congruential_engine"><template>
      <template-type-parameter name="IntType"/>
      <template-nontype-parameter name="a"><type>IntType</type></template-nontype-parameter>
      <template-nontype-parameter name="b"><type>IntType</type></template-nontype-parameter>
      <template-nontype-parameter name="p"><type>IntType</type></template-nontype-parameter>
    </template><description><para>Instantiations of class template <computeroutput><classname alt="boost::random::inversive_congruential_engine">inversive_congruential_engine</classname></computeroutput> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It uses the inversive congruential algorithm (ICG) described in</para><para> <blockquote><para>  "Inversive pseudorandom number generators: concepts, results and links", Peter Hellekalek, In: "Proceedings of the 1995 Winter Simulation
 Conference", C. Alexopoulos, K. Kang, W.R. Lilegdon, and D. Goldsman (editors), 1995, pp. 255-262. <ulink url="ftp://random.mat.sbg.ac.at/pub/data/wsc95.ps">ftp://random.mat.sbg.ac.at/pub/data/wsc95.ps</ulink>  </para></blockquote> </para><para>The output sequence is defined by x(n+1) = (a*inv(x(n)) - b) (mod p), where x(0), a, b, and the prime number p are parameters of the generator. The expression inv(k) denotes the multiplicative inverse of k in the field of integer numbers modulo p, with inv(0) := 0.</para><para>The template parameter IntType shall denote a signed integral type large enough to hold p; a, b, and p are the parameters of the generators. The template parameter val is the validation value checked by validation.</para><para> <note><para>  The implementation currently uses the Euclidian Algorithm to compute the multiplicative inverse. Therefore, the inversive generators are about 10-20 times slower than the others (see section"performance"). However, the paper talks of only 3x slowdown, so the Euclidian Algorithm is probably not optimal for calculating the multiplicative inverse.  </para></note>  </para></description><typedef name="result_type"><type>IntType</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="multiplier" specifiers="static"><type>const result_type</type></data-member>
<data-member name="increment" specifiers="static"><type>const result_type</type></data-member>
<data-member name="modulus" specifiers="static"><type>const result_type</type></data-member>
<data-member name="default_seed" specifiers="static"><type>const IntType</type></data-member>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type></method>
<method name="max" specifiers="static"><type>constexpr result_type</type></method>
</method-group>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Calls seed(default_seed) </para></description></method>
<method name="seed"><type>void</type><parameter name="x0"><paramtype>IntType</paramtype></parameter><description><para>If c mod m is zero and x0 mod m is zero, changes the current value of the generator to 1. Otherwise, changes it to x0 mod m. If c is zero, distinct seeds in the range [1,m) will leave the generator in distinct states. If c is not zero, the range is [0,m). </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds an <computeroutput><classname alt="boost::random::inversive_congruential_engine">inversive_congruential_engine</classname></computeroutput> using values from a SeedSeq. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>seeds an <computeroutput><classname alt="boost::random::inversive_congruential_engine">inversive_congruential_engine</classname></computeroutput> with values taken from the itrator range [first, last) and adjusts <computeroutput>first</computeroutput> to point to the element after the last one used. If there are not enough elements, throws <computeroutput>std::invalid_argument</computeroutput>.</para><para><computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> must be input iterators. </para></description></method>
<method name="operator()"><type>IntType</type><description><para>Returns the next output of the generator. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
</method-group>
<constructor><description><para>Constructs an <computeroutput><classname alt="boost::random::inversive_congruential_engine">inversive_congruential_engine</classname></computeroutput>, seeding it with the default seed. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="x0"><paramtype>IntType</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::inversive_congruential_engine">inversive_congruential_engine</classname></computeroutput>, seeding it with <computeroutput>x0</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::inversive_congruential_engine">inversive_congruential_engine</classname></computeroutput>, seeding it with values produced by a call to <computeroutput>seq.generate()</computeroutput>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::inversive_congruential_engine">inversive_congruential_engine</classname></computeroutput>, seeds it with values taken from the itrator range [first, last), and adjusts first to point to the element after the last one used. If there are not enough elements, throws <computeroutput>std::invalid_argument</computeroutput>.</para><para>first and last must be input iterators. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="x"><paramtype>const <classname>inversive_congruential_engine</classname> &amp;</paramtype></parameter><description><para>Writes the textual representation of the generator to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="x"><paramtype>const <classname>inversive_congruential_engine</classname> &amp;</paramtype></parameter><description><para>Reads the textual representation of the generator from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>inversive_congruential_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>inversive_congruential_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences of outputs. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>inversive_congruential_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>inversive_congruential_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences of outputs. </para></description></method>
</method-group>
</class><typedef name="hellekalek1995"><description><para>The specialization hellekalek1995 was suggested in</para><para> <blockquote><para>  "Inversive pseudorandom number generators: concepts, results and links", Peter Hellekalek, In: "Proceedings of the 1995 Winter Simulation
 Conference", C. Alexopoulos, K. Kang, W.R. Lilegdon, and D. Goldsman (editors), 1995, pp. 255-262. <ulink url="ftp://random.mat.sbg.ac.at/pub/data/wsc95.ps">ftp://random.mat.sbg.ac.at/pub/data/wsc95.ps</ulink>  </para></blockquote>  </para></description><type><classname>inversive_congruential_engine</classname>&lt; uint32_t, 9102, 2147483647-36884165, 2147483647 &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/lagged_fibonacci.hpp">
<namespace name="boost">
<namespace name="random">
<class name="lagged_fibonacci_01_engine"><template>
      <template-type-parameter name="RealType"/>
      <template-nontype-parameter name="w"><type>int</type></template-nontype-parameter>
      <template-nontype-parameter name="p"><type>unsigned int</type></template-nontype-parameter>
      <template-nontype-parameter name="q"><type>unsigned int</type></template-nontype-parameter>
    </template><description><para>Instantiations of class template <computeroutput>lagged_fibonacci_01</computeroutput> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It uses a lagged Fibonacci algorithm with two lags <computeroutput>p</computeroutput> and <computeroutput>q</computeroutput>, evaluated in floating-point arithmetic: x(i) = x(i-p) + x(i-q) (mod 1) with p &gt; q. See</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote> </para><para> <note><para>  The quality of the generator crucially depends on the choice of the parameters. User code should employ one of the sensibly parameterized generators such as <classname alt="boost::random::lagged_fibonacci607">lagged_fibonacci607</classname>  instead.  </para></note> </para><para>The generator requires considerable amounts of memory for the storage of its state array. For example, <classname alt="boost::random::lagged_fibonacci607">lagged_fibonacci607</classname>  requires about 4856 bytes and <classname alt="boost::random::lagged_fibonacci44497">lagged_fibonacci44497</classname>  requires about 350 KBytes. </para></description><typedef name="result_type"><type>RealType</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="word_size" specifiers="static"><type>const int</type></data-member>
<data-member name="long_lag" specifiers="static"><type>const unsigned int</type></data-member>
<data-member name="short_lag" specifiers="static"><type>const unsigned int</type></data-member>
<data-member name="default_seed" specifiers="static"><type>const boost::uint32_t</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Calls seed(default_seed). </para></description></method>
<method name="seed"><type>void</type><parameter name="value"><paramtype>boost::uint32_t</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::minstd_rand0">minstd_rand0</classname>  generator with the constructor parameter value and calls seed with it. Distinct seeds in the range [1, 2147483647) will produce generators with different states. Other seeds will be equivalent to some seed within this range. See  <classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname> for details. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds this <computeroutput><classname alt="boost::random::lagged_fibonacci_01_engine">lagged_fibonacci_01_engine</classname></computeroutput> using values produced by <computeroutput>seq.generate</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Seeds this <computeroutput><classname alt="boost::random::lagged_fibonacci_01_engine">lagged_fibonacci_01_engine</classname></computeroutput> using values from the iterator range [first, last). If there are not enough elements in the range, throws <computeroutput>std::invalid_argument</computeroutput>. </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput>lagged_fibonacci_01</computeroutput> generator and calls <computeroutput>seed()</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="value"><paramtype>uint32_t</paramtype></parameter><description><para>Constructs a <computeroutput>lagged_fibonacci_01</computeroutput> generator and calls <computeroutput>seed(value)</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Constructs a <computeroutput>lagged_fibonacci_01</computeroutput> generator and calls <computeroutput>seed(gen)</computeroutput>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the upper bound of the generators outputs. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>const <classname>lagged_fibonacci_01_engine</classname> &amp;</paramtype></parameter><description><para>Writes the textual representation of the generator to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>const <classname>lagged_fibonacci_01_engine</classname> &amp;</paramtype></parameter><description><para>Reads the textual representation of the generator from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x_"><paramtype>const <classname>lagged_fibonacci_01_engine</classname> &amp;</paramtype></parameter><parameter name="y_"><paramtype>const <classname>lagged_fibonacci_01_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences of outputs. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>lagged_fibonacci_01_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>lagged_fibonacci_01_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences of outputs. </para></description></method>
</method-group>
</class><class name="lagged_fibonacci_engine"><template>
      <template-type-parameter name="UIntType"/>
      <template-nontype-parameter name="w"><type>int</type></template-nontype-parameter>
      <template-nontype-parameter name="p"><type>unsigned int</type></template-nontype-parameter>
      <template-nontype-parameter name="q"><type>unsigned int</type></template-nontype-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::lagged_fibonacci_engine">lagged_fibonacci_engine</classname> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It uses a lagged Fibonacci algorithm with two lags <computeroutput>p</computeroutput> and <computeroutput>q:</computeroutput> x(i) = x(i-p) + x(i-q) (mod 2<superscript>w</superscript>) with p &gt; q. </para></description><typedef name="result_type"><type>UIntType</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="word_size" specifiers="static"><type>const int</type></data-member>
<data-member name="long_lag" specifiers="static"><type>const unsigned int</type></data-member>
<data-member name="short_lag" specifiers="static"><type>const unsigned int</type></data-member>
<data-member name="default_seed" specifiers="static"><type>const UIntType</type></data-member>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce. </para></description></method>
</method-group>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Calls <computeroutput>seed(default_seed)</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><parameter name="value"><paramtype>UIntType</paramtype></parameter><description><para>Sets the state of the generator to values produced by a <classname alt="boost::random::minstd_rand0">minstd_rand0</classname>  generator. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Sets the state of the generator using values produced by seq. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Sets the state of the generator to values from the iterator range [first, last). If there are not enough elements in the range [first, last) throws <computeroutput>std::invalid_argument</computeroutput>. </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
</method-group>
<constructor><description><para>Creates a new <computeroutput><classname alt="boost::random::lagged_fibonacci_engine">lagged_fibonacci_engine</classname></computeroutput> and calls <computeroutput>seed()</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="value"><paramtype>UIntType</paramtype></parameter><description><para>Creates a new <computeroutput><classname alt="boost::random::lagged_fibonacci_engine">lagged_fibonacci_engine</classname></computeroutput> and calls <computeroutput>seed(value)</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Creates a new <computeroutput><classname alt="boost::random::lagged_fibonacci_engine">lagged_fibonacci_engine</classname></computeroutput> and calls <computeroutput>seed(seq)</computeroutput>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Creates a new <computeroutput><classname alt="boost::random::lagged_fibonacci_engine">lagged_fibonacci_engine</classname></computeroutput> and calls <computeroutput>seed(first, last)</computeroutput>. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>const <classname>lagged_fibonacci_engine</classname> &amp;</paramtype></parameter><description><para>Writes the textual representation of the generator to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>const <classname>lagged_fibonacci_engine</classname> &amp;</paramtype></parameter><description><para>Reads the textual representation of the generator from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x_"><paramtype>const <classname>lagged_fibonacci_engine</classname> &amp;</paramtype></parameter><parameter name="y_"><paramtype>const <classname>lagged_fibonacci_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences of outputs. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>lagged_fibonacci_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>lagged_fibonacci_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences of outputs. </para></description></method>
</method-group>
</class><typedef name="lagged_fibonacci607"><description><para>The specializations lagged_fibonacci607 ... lagged_fibonacci44497 use well tested lags.</para><para>See</para><para> <blockquote><para>  "On the Periods of Generalized Fibonacci Recurrences", Richard P. Brent Computer Sciences Laboratory Australian National University, December 1992  </para></blockquote> </para><para>The lags used here can be found in</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote>  </para></description><type><classname>lagged_fibonacci_01_engine</classname>&lt; double, 48, 607, 273 &gt;</type></typedef>
<typedef name="lagged_fibonacci1279"><description><para>The specializations lagged_fibonacci607 ... lagged_fibonacci44497 use well tested lags.</para><para>See</para><para> <blockquote><para>  "On the Periods of Generalized Fibonacci Recurrences", Richard P. Brent Computer Sciences Laboratory Australian National University, December 1992  </para></blockquote> </para><para>The lags used here can be found in</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote>  </para></description><type><classname>lagged_fibonacci_01_engine</classname>&lt; double, 48, 1279, 418 &gt;</type></typedef>
<typedef name="lagged_fibonacci2281"><description><para>The specializations lagged_fibonacci607 ... lagged_fibonacci44497 use well tested lags.</para><para>See</para><para> <blockquote><para>  "On the Periods of Generalized Fibonacci Recurrences", Richard P. Brent Computer Sciences Laboratory Australian National University, December 1992  </para></blockquote> </para><para>The lags used here can be found in</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote>  </para></description><type><classname>lagged_fibonacci_01_engine</classname>&lt; double, 48, 2281, 1252 &gt;</type></typedef>
<typedef name="lagged_fibonacci3217"><description><para>The specializations lagged_fibonacci607 ... lagged_fibonacci44497 use well tested lags.</para><para>See</para><para> <blockquote><para>  "On the Periods of Generalized Fibonacci Recurrences", Richard P. Brent Computer Sciences Laboratory Australian National University, December 1992  </para></blockquote> </para><para>The lags used here can be found in</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote>  </para></description><type><classname>lagged_fibonacci_01_engine</classname>&lt; double, 48, 3217, 576 &gt;</type></typedef>
<typedef name="lagged_fibonacci4423"><description><para>The specializations lagged_fibonacci607 ... lagged_fibonacci44497 use well tested lags.</para><para>See</para><para> <blockquote><para>  "On the Periods of Generalized Fibonacci Recurrences", Richard P. Brent Computer Sciences Laboratory Australian National University, December 1992  </para></blockquote> </para><para>The lags used here can be found in</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote>  </para></description><type><classname>lagged_fibonacci_01_engine</classname>&lt; double, 48, 4423, 2098 &gt;</type></typedef>
<typedef name="lagged_fibonacci9689"><description><para>The specializations lagged_fibonacci607 ... lagged_fibonacci44497 use well tested lags.</para><para>See</para><para> <blockquote><para>  "On the Periods of Generalized Fibonacci Recurrences", Richard P. Brent Computer Sciences Laboratory Australian National University, December 1992  </para></blockquote> </para><para>The lags used here can be found in</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote>  </para></description><type><classname>lagged_fibonacci_01_engine</classname>&lt; double, 48, 9689, 5502 &gt;</type></typedef>
<typedef name="lagged_fibonacci19937"><description><para>The specializations lagged_fibonacci607 ... lagged_fibonacci44497 use well tested lags.</para><para>See</para><para> <blockquote><para>  "On the Periods of Generalized Fibonacci Recurrences", Richard P. Brent Computer Sciences Laboratory Australian National University, December 1992  </para></blockquote> </para><para>The lags used here can be found in</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote>  </para></description><type><classname>lagged_fibonacci_01_engine</classname>&lt; double, 48, 19937, 9842 &gt;</type></typedef>
<typedef name="lagged_fibonacci23209"><description><para>The specializations lagged_fibonacci607 ... lagged_fibonacci44497 use well tested lags.</para><para>See</para><para> <blockquote><para>  "On the Periods of Generalized Fibonacci Recurrences", Richard P. Brent Computer Sciences Laboratory Australian National University, December 1992  </para></blockquote> </para><para>The lags used here can be found in</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote>  </para></description><type><classname>lagged_fibonacci_01_engine</classname>&lt; double, 48, 23209, 13470 &gt;</type></typedef>
<typedef name="lagged_fibonacci44497"><description><para>The specializations lagged_fibonacci607 ... lagged_fibonacci44497 use well tested lags.</para><para>See</para><para> <blockquote><para>  "On the Periods of Generalized Fibonacci Recurrences", Richard P. Brent Computer Sciences Laboratory Australian National University, December 1992  </para></blockquote> </para><para>The lags used here can be found in</para><para> <blockquote><para>  "Uniform random number generators for supercomputers", Richard Brent, Proc. of Fifth Australian Supercomputer Conference, Melbourne, Dec. 1992, pp. 704-706.  </para></blockquote>  </para></description><type><classname>lagged_fibonacci_01_engine</classname>&lt; double, 48, 44497, 21034 &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/laplace_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="laplace_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The laplace distribution is a real-valued distribution with two parameters, mean and beta.</para><para>It has <inlineequation><alt>$\displaystyle p(x) = \frac{e^-{\frac{|x-\mu|}{\beta}}}{2\beta}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_28.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \frac{e^-{\frac{|x-\mu|}{\beta}}}{2\beta}$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>laplace_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="mean" cv="const"><type>RealType</type><description><para>Returns the "mean" parameter of the distribtuion. </para></description></method>
<method name="beta" cv="const"><type>RealType</type><description><para>Returns the "beta" parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="mean"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="beta"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::laplace_distribution::param_type">param_type</classname></computeroutput> from the "mean" and "beta" parameters of the distribution. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::laplace_distribution::param_type">param_type</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::laplace_distribution::param_type">param_type</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the different. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>RealType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the laplace distribution. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed accordint to the laplace distribution with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="mean" cv="const"><type>RealType</type><description><para>Returns the "mean" parameter of the distribution. </para></description></method>
<method name="beta" cv="const"><type>RealType</type><description><para>Returns the "beta" parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="mean"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="beta"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::laplace_distribution">laplace_distribution</classname></computeroutput> from its "mean" and "beta" parameters. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::laplace_distribution">laplace_distribution</classname></computeroutput> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="wd"><paramtype>const <classname>laplace_distribution</classname> &amp;</paramtype></parameter><description><para>Writes an <computeroutput><classname alt="boost::random::laplace_distribution">laplace_distribution</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="wd"><paramtype>const <classname>laplace_distribution</classname> &amp;</paramtype></parameter><description><para>Reads an <computeroutput><classname alt="boost::random::laplace_distribution">laplace_distribution</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>laplace_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>laplace_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::laplace_distribution">laplace_distribution</classname></computeroutput> will return identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>laplace_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>laplace_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::laplace_distribution">laplace_distribution</classname></computeroutput> will return different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/linear_congruential.hpp">
<namespace name="boost">
<namespace name="random">
<class name="linear_congruential_engine"><template>
      <template-type-parameter name="IntType"/>
      <template-nontype-parameter name="a"><type>IntType</type></template-nontype-parameter>
      <template-nontype-parameter name="c"><type>IntType</type></template-nontype-parameter>
      <template-nontype-parameter name="m"><type>IntType</type></template-nontype-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . Linear congruential pseudo-random number generators are described in:</para><para> <blockquote><para>  "Mathematical methods in large-scale computing units", D. H. Lehmer, Proc. 2nd Symposium on Large-Scale Digital Calculating Machines, Harvard University Press, 1951, pp. 141-146  </para></blockquote> </para><para>Let x(n) denote the sequence of numbers returned by some pseudo-random number generator. Then for the linear congruential generator, x(n+1) := (a * x(n) + c) mod m. Parameters for the generator are x(0), a, c, m. The template parameter IntType shall denote an integral type. It must be large enough to hold values a, c, and m. The template parameters a and c must be smaller than m.</para><para>Note: The quality of the generator crucially depends on the choice of the parameters. User code should use one of the sensibly parameterized generators such as minstd_rand instead. </para></description><typedef name="result_type"><type>IntType</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="multiplier" specifiers="static"><type>const IntType</type></data-member>
<data-member name="increment" specifiers="static"><type>const IntType</type></data-member>
<data-member name="modulus" specifiers="static"><type>const IntType</type></data-member>
<data-member name="default_seed" specifiers="static"><type>const IntType</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Calls seed(default_seed) </para></description></method>
<method name="seed"><type>void</type><parameter name="x0_"><paramtype>IntType</paramtype></parameter><description><para>If c mod m is zero and x0 mod m is zero, changes the current value of the generator to 1. Otherwise, changes it to x0 mod m. If c is zero, distinct seeds in the range [1,m) will leave the generator in distinct states. If c is not zero, the range is [0,m). </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds a <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput> using values from a SeedSeq. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>seeds a <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput> with values taken from the itrator range [first, last) and adjusts <computeroutput>first</computeroutput> to point to the element after the last one used. If there are not enough elements, throws <computeroutput>std::invalid_argument</computeroutput>.</para><para><computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> must be input iterators. </para></description></method>
<method name="operator()"><type>IntType</type><description><para>Returns the next value of the <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput>. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput>, using the default seed </para></description></constructor>
<constructor specifiers="explicit"><parameter name="x0"><paramtype>IntType</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput>, seeding it with <computeroutput>x0</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput>, seeding it with values produced by a call to <computeroutput>seq.generate()</computeroutput>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput> and seeds it with values taken from the itrator range [first, last) and adjusts first to point to the element after the last one used. If there are not enough elements, throws <computeroutput>std::invalid_argument</computeroutput>.</para><para>first and last must be input iterators. </para></description></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput> can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput> can produce. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="lcg"><paramtype>const <classname>linear_congruential_engine</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="lcg"><paramtype><classname>linear_congruential_engine</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
</method-group>
</class><class name="rand48"><description><para>Class <computeroutput><classname alt="boost::random::rand48">rand48</classname></computeroutput> models a <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It uses the linear congruential algorithm with the parameters a = 0x5DEECE66D, c = 0xB, m = 2**48. It delivers identical results to the <computeroutput>lrand48()</computeroutput> function available on some systems (assuming lcong48 has not been called).</para><para>It is only available on systems where <computeroutput>uint64_t</computeroutput> is provided as an integral type, so that for example static in-class constants and/or enum definitions with large <computeroutput>uint64_t</computeroutput> numbers work. </para></description><typedef name="result_type"><type>boost::uint32_t</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr uint32_t</type><description><para>Returns the smallest value that the generator can produce </para></description></method>
<method name="max" specifiers="static"><type>constexpr uint32_t</type><description><para>Returns the largest value that the generator can produce </para></description></method>
</method-group>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Seeds the generator with the default seed. </para></description></method>
<method name="seed"><type>void</type><parameter name="x0"><paramtype>result_type</paramtype></parameter><description><para>Changes the current value x(n) of the generator to (x0 &lt;&lt; 16) | 0x330e. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Seeds the generator using values from an iterator range, and updates first to point one past the last value consumed. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds the generator with values produced by <computeroutput>seq.generate()</computeroutput>. </para></description></method>
<method name="operator()"><type>uint32_t</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
</method-group>
<constructor><description><para>Seeds the generator with the default seed. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="x0"><paramtype>result_type</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::rand48">rand48</classname>  generator with x(0) := (x0 &lt;&lt; 16) | 0x330e. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds the generator with values produced by <computeroutput>seq.generate()</computeroutput>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Seeds the generator using values from an iterator range, and updates first to point one past the last value consumed. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="r"><paramtype>const <classname>rand48</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::rand48">rand48</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="r"><paramtype><classname>rand48</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::rand48">rand48</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>rand48</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>rand48</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences of values. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="x"><paramtype>const <classname>rand48</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>rand48</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences of values. </para></description></method>
</method-group>
</class><typedef name="minstd_rand0"><description><para>The specialization <classname alt="boost::random::minstd_rand0">minstd_rand0</classname>  was originally suggested in</para><para> <blockquote><para>  A pseudo-random number generator for the System/360, P.A. Lewis, A.S. Goodman, J.M. Miller, IBM Systems Journal, Vol. 8, No. 2, 1969, pp. 136-146  </para></blockquote> </para><para>It is examined more closely together with <classname alt="boost::random::minstd_rand">minstd_rand</classname>  in</para><para> <blockquote><para>  "Random Number Generators: Good ones are hard to find", Stephen K. Park and Keith W. Miller, Communications of the ACM, Vol. 31, No. 10, October 1988, pp. 1192-1201  </para></blockquote>  </para></description><type><classname>linear_congruential_engine</classname>&lt; uint32_t, 16807, 0, 2147483647 &gt;</type></typedef>
<typedef name="minstd_rand"><description><para>The specialization <classname alt="boost::random::minstd_rand">minstd_rand</classname>  was suggested in</para><para> <blockquote><para>  "Random Number Generators: Good ones are hard to find", Stephen K. Park and Keith W. Miller, Communications of the ACM, Vol. 31, No. 10, October 1988, pp. 1192-1201  </para></blockquote>  </para></description><type><classname>linear_congruential_engine</classname>&lt; uint32_t, 48271, 0, 2147483647 &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/linear_feedback_shift.hpp">
<namespace name="boost">
<namespace name="random">
<class name="linear_feedback_shift_engine"><template>
      <template-type-parameter name="UIntType"/>
      <template-nontype-parameter name="w"><type>int</type></template-nontype-parameter>
      <template-nontype-parameter name="k"><type>int</type></template-nontype-parameter>
      <template-nontype-parameter name="q"><type>int</type></template-nontype-parameter>
      <template-nontype-parameter name="s"><type>int</type></template-nontype-parameter>
    </template><description><para>Instatiations of <computeroutput>linear_feedback_shift</computeroutput> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It was originally proposed in</para><para> <blockquote><para>  "Random numbers generated by linear recurrence modulo two.", Tausworthe, R. C.(1965), Mathematics of Computation 19, 201-209.  </para></blockquote>  </para></description><typedef name="result_type"><type>UIntType</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="word_size" specifiers="static"><type>const int</type></data-member>
<data-member name="exponent1" specifiers="static"><type>const int</type></data-member>
<data-member name="exponent2" specifiers="static"><type>const int</type></data-member>
<data-member name="step_size" specifiers="static"><type>const int</type></data-member>
<data-member name="default_seed" specifiers="static"><type>const UIntType</type></data-member>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce. </para></description></method>
</method-group>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Seeds a <computeroutput><classname alt="boost::random::linear_feedback_shift_engine">linear_feedback_shift_engine</classname></computeroutput> with the default seed. </para></description></method>
<method name="seed"><type>void</type><parameter name="s0"><paramtype>UIntType</paramtype></parameter><description><para>Seeds a <computeroutput><classname alt="boost::random::linear_feedback_shift_engine">linear_feedback_shift_engine</classname></computeroutput> with <computeroutput>s0</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds a <computeroutput><classname alt="boost::random::linear_feedback_shift_engine">linear_feedback_shift_engine</classname></computeroutput> with values produced by <computeroutput>seq.generate()</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Seeds a <computeroutput><classname alt="boost::random::linear_feedback_shift_engine">linear_feedback_shift_engine</classname></computeroutput> with values from the range [first, last). </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput><classname alt="boost::random::linear_feedback_shift_engine">linear_feedback_shift_engine</classname></computeroutput>, using the default seed. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="s0"><paramtype>UIntType</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::linear_feedback_shift_engine">linear_feedback_shift_engine</classname></computeroutput>, seeding it with s0. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::linear_feedback_shift_engine">linear_feedback_shift_engine</classname></computeroutput>, seeding it with seq. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::linear_feedback_shift_engine">linear_feedback_shift_engine</classname></computeroutput>, seeding it with values from the range [first, last). </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="x"><paramtype>const <classname>linear_feedback_shift_engine</classname> &amp;</paramtype></parameter><description><para>Writes the textual representation of the generator to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="x"><paramtype>const <classname>linear_feedback_shift_engine</classname> &amp;</paramtype></parameter><description><para>Reads the textual representation of the generator from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>linear_feedback_shift_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>linear_feedback_shift_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences of outputs. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>linear_feedback_shift_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>linear_feedback_shift_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences of outputs. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/lognormal_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="lognormal_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::lognormal_distribution">lognormal_distribution</classname> model a  <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . Such a distribution produces random numbers with <inlineequation><alt>$\displaystyle p(x) = \frac{1}{x s \sqrt{2\pi}} e^{\frac{-\left(\log(x)-m\right)^2}{2s^2}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_29.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \frac{1}{x s \sqrt{2\pi}} e^{\frac{-\left(\log(x)-m\right)^2}{2s^2}}$</phrase></textobject></inlinemediaobject></inlineequation> for x &gt; 0.</para><para> <warning><para>  This distribution has been updated to match the C++ standard. Its behavior has changed from the original boost::lognormal_distribution. A backwards compatible version is provided in namespace boost.  </para></warning>  </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>lognormal_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="m" cv="const"><type>RealType</type><description><para>Returns the "m" parameter of the distribution. </para></description></method>
<method name="s" cv="const"><type>RealType</type><description><para>Returns the "s" parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="m"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="s"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs the parameters of a <classname alt="boost::random::lognormal_distribution">lognormal_distribution</classname>. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a std::ostream. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a std::istream. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type><classname>normal_distribution</classname>&lt; RealType &gt;::input_type</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="m" cv="const"><type>RealType</type><description><para>Returns the m parameter of the distribution. </para></description></method>
<method name="s" cv="const"><type>RealType</type><description><para>Returns the s parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the lognormal distribution. </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the lognormal distribution with parameters specified by param. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="m"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="s"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <classname alt="boost::random::lognormal_distribution">lognormal_distribution</classname>. <computeroutput>m</computeroutput> and <computeroutput>s</computeroutput> are the parameters of the distribution. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::lognormal_distribution">lognormal_distribution</classname> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ld"><paramtype>const <classname>lognormal_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ld"><paramtype>const <classname>lognormal_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>lognormal_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>lognormal_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>lognormal_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>lognormal_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may produce different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/mersenne_twister.hpp">
<namespace name="boost">
<namespace name="random">
<class name="mersenne_twister_engine"><template>
      <template-type-parameter name="UIntType"/>
      <template-nontype-parameter name="w"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="n"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="m"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="r"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="a"><type>UIntType</type></template-nontype-parameter>
      <template-nontype-parameter name="u"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="d"><type>UIntType</type></template-nontype-parameter>
      <template-nontype-parameter name="s"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="b"><type>UIntType</type></template-nontype-parameter>
      <template-nontype-parameter name="t"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="c"><type>UIntType</type></template-nontype-parameter>
      <template-nontype-parameter name="l"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="f"><type>UIntType</type></template-nontype-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::mersenne_twister_engine">mersenne_twister_engine</classname> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It uses the algorithm described in</para><para> <blockquote><para>  "Mersenne Twister: A 623-dimensionally equidistributed uniform
 pseudo-random number generator", Makoto Matsumoto and Takuji Nishimura, ACM Transactions on Modeling and Computer Simulation: Special Issue on Uniform Random Number Generation, Vol. 8, No. 1, January 1998, pp. 3-30.  </para></blockquote> </para><para> <note><para>  The boost variant has been implemented from scratch and does not derive from or use mt19937.c provided on the above WWW site. However, it was verified that both produce identical output.  </para></note> </para><para>The seeding from an integer was changed in April 2005 to address a <ulink url="http://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/MT2002/emt19937ar.html">weakness</ulink>.</para><para>The quality of the generator crucially depends on the choice of the parameters. User code should employ one of the sensibly parameterized generators such as <classname alt="boost::random::mt19937">mt19937</classname>  instead.</para><para>The generator requires considerable amounts of memory for the storage of its state array. For example, <classname alt="boost::random::mt11213b">mt11213b</classname>  requires about 1408 bytes and  <classname alt="boost::random::mt19937">mt19937</classname>  requires about 2496 bytes. </para></description><typedef name="result_type"><type>UIntType</type></typedef>
<data-member name="word_size" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="state_size" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="shift_size" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="mask_bits" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="xor_mask" specifiers="static"><type>const UIntType</type></data-member>
<data-member name="tempering_u" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="tempering_d" specifiers="static"><type>const UIntType</type></data-member>
<data-member name="tempering_s" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="tempering_b" specifiers="static"><type>const UIntType</type></data-member>
<data-member name="tempering_t" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="tempering_c" specifiers="static"><type>const UIntType</type></data-member>
<data-member name="tempering_l" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="initialization_multiplier" specifiers="static"><type>const UIntType</type></data-member>
<data-member name="default_seed" specifiers="static"><type>const UIntType</type></data-member>
<data-member name="parameter_a" specifiers="static"><type>const UIntType</type></data-member>
<data-member name="output_u" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="output_s" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="output_b" specifiers="static"><type>const UIntType</type></data-member>
<data-member name="output_t" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="output_c" specifiers="static"><type>const UIntType</type></data-member>
<data-member name="output_l" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Calls <computeroutput>seed(default_seed)</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><parameter name="value"><paramtype>UIntType</paramtype></parameter><description><para>Sets the state x(0) to v mod 2w. Then, iteratively, sets x(i) to (i + f * (x(i-1) xor (x(i-1) rshift w-2))) mod 2<superscript>w</superscript> for i = 1 .. n-1. x(n) is the first value to be returned by operator(). </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeeqSeq"/>
        </template><parameter name="seq"><paramtype>SeeqSeq &amp;</paramtype></parameter><description><para>Seeds a <classname alt="boost::random::mersenne_twister_engine">mersenne_twister_engine</classname> using values produced by seq.generate(). </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Sets the state of the generator using values from an iterator range. </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Produces the next value of the generator. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput> steps. Equivalent to</para><para><programlisting language="c++">for(unsigned long long i = 0; i &lt; z; ++i) {
    gen();
}
</programlisting> </para></description></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput><classname alt="boost::random::mersenne_twister_engine">mersenne_twister_engine</classname></computeroutput> and calls <computeroutput>seed()</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="value"><paramtype>UIntType</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::mersenne_twister_engine">mersenne_twister_engine</classname></computeroutput> and calls <computeroutput>seed(value)</computeroutput>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::mersenne_twister_engine">mersenne_twister_engine</classname> and calls <computeroutput>seed(gen)</computeroutput>.</para><para> <note><para>  The copy constructor will always be preferred over the templated constructor.  </para></note>  </para></description></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="mt"><paramtype>const <classname>mersenne_twister_engine</classname> &amp;</paramtype></parameter><description><para>Writes a <classname alt="boost::random::mersenne_twister_engine">mersenne_twister_engine</classname> to a <computeroutput>std::ostream</computeroutput> </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="mt"><paramtype><classname>mersenne_twister_engine</classname> &amp;</paramtype></parameter><description><para>Reads a <classname alt="boost::random::mersenne_twister_engine">mersenne_twister_engine</classname> from a <computeroutput>std::istream</computeroutput> </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x_"><paramtype>const <classname>mersenne_twister_engine</classname> &amp;</paramtype></parameter><parameter name="y_"><paramtype>const <classname>mersenne_twister_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators are in the same state, and will thus produce identical sequences. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="x_"><paramtype>const <classname>mersenne_twister_engine</classname> &amp;</paramtype></parameter><parameter name="y_"><paramtype>const <classname>mersenne_twister_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators are in different states. </para></description></method>
</method-group>
</class><typedef name="mt11213b"><description><para>The specializations <classname alt="boost::random::mt11213b">mt11213b</classname>  and <classname alt="boost::random::mt19937">mt19937</classname>  are from</para><para> <blockquote><para>  "Mersenne Twister: A 623-dimensionally equidistributed
 uniform pseudo-random number generator", Makoto Matsumoto and Takuji Nishimura, ACM Transactions on Modeling and Computer Simulation: Special Issue on Uniform Random Number Generation, Vol. 8, No. 1, January 1998, pp. 3-30.  </para></blockquote>  </para></description><type><classname>mersenne_twister_engine</classname>&lt; uint32_t, 32, 351, 175, 19, 0xccab8ee7, 11, 0xffffffff, 7, 0x31b6ab00, 15, 0xffe50000, 17, 1812433253 &gt;</type></typedef>
<typedef name="mt19937"><description><para>The specializations <classname alt="boost::random::mt11213b">mt11213b</classname>  and <classname alt="boost::random::mt19937">mt19937</classname>  are from</para><para> <blockquote><para>  "Mersenne Twister: A 623-dimensionally equidistributed
 uniform pseudo-random number generator", Makoto Matsumoto and Takuji Nishimura, ACM Transactions on Modeling and Computer Simulation: Special Issue on Uniform Random Number Generation, Vol. 8, No. 1, January 1998, pp. 3-30.  </para></blockquote>  </para></description><type><classname>mersenne_twister_engine</classname>&lt; uint32_t, 32, 624, 397, 31, 0x9908b0df, 11, 0xffffffff, 7, 0x9d2c5680, 15, 0xefc60000, 18, 1812433253 &gt;</type></typedef>
<typedef name="mt19937_64"><type><classname>mersenne_twister_engine</classname>&lt; uint64_t, 64, 312, 156, 31, 0xb5026f5aa96619e9ull, 29, 0x5555555555555555ull, 17, 0x71d67fffeda60000ull, 37, 0xfff7eee000000000ull, 43, 6364136223846793005ull &gt;</type></typedef>

</namespace>
</namespace>
<macro name="BOOST_RANDOM_MERSENNE_TWISTER_DISCARD_THRESHOLD"/>
</header>
<header name="boost/random/mixmax.hpp">
<namespace name="boost">
<namespace name="random">
<class name="mixmax_engine"><template>
      <template-nontype-parameter name="Ndim"><type>int</type></template-nontype-parameter>
      <template-nontype-parameter name="SPECIALMUL"><type>unsigned int</type></template-nontype-parameter>
      <template-nontype-parameter name="SPECIAL"><type>boost::int64_t</type></template-nontype-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::mixmax_engine">mixmax_engine</classname> model,  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link>  . It uses the MIXMAX generator algorithms from:</para><para> <blockquote><para>  G.K.Savvidy and N.G.Ter-Arutyunian, On the Monte Carlo simulation of physical systems, J.Comput.Phys. 97, 566 (1991); Preprint EPI-865-16-86, Yerevan, Jan. 1986 <ulink url="http://dx.doi.org/10.1016/0021-9991(91)90015-D">http://dx.doi.org/10.1016/0021-9991(91)90015-D</ulink></para>
<para>K.Savvidy The MIXMAX random number generator Comp. Phys. Commun. 196 (2015), pp 161–165 <ulink url="http://dx.doi.org/10.1016/j.cpc.2015.06.003">http://dx.doi.org/10.1016/j.cpc.2015.06.003</ulink></para>
<para>K.Savvidy and G.Savvidy Spectrum and Entropy of C-systems. MIXMAX random number generator Chaos, Solitons &amp; Fractals, Volume 91, (2016) pp. 33–38 <ulink url="http://dx.doi.org/10.1016/j.chaos.2016.05.003">http://dx.doi.org/10.1016/j.chaos.2016.05.003</ulink>  </para></blockquote> </para><para>The generator crucially depends on the choice of the parameters. The valid sets of parameters are from the published papers above. </para></description><struct name="rng_state_st"><data-member name="V"><type>boost::array&lt; boost::uint64_t, Ndim &gt;</type></data-member>
<data-member name="sumtot"><type>boost::uint64_t</type></data-member>
<data-member name="counter"><type>int</type></data-member>
</struct><typedef name="result_type"><type>boost::uint64_t</type></typedef>
<data-member name="mixmax_min" specifiers="static"><type>const boost::uint64_t</type></data-member>
<data-member name="mixmax_max" specifiers="static"><type>const boost::uint64_t</type></data-member>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="N" specifiers="static"><type>const int</type><purpose>The main internal parameter, size of the defining MIXMAX matrix. </purpose></data-member>
<method-group name="public member functions">
<method name="min"><type>BOOST_STATIC_CONSTEXPR result_type</type></method>
<method name="max"><type>BOOST_STATIC_CONSTEXPR result_type</type></method>
<method name="seed"><type>void</type><parameter name="seedval"><paramtype>boost::uint64_t</paramtype><default>default_seed</default></parameter><purpose>seed with one 64-bit seed </purpose></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Sets the state of the generator using values from an iterator range. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeeqSeq"/>
        </template><parameter name="seq"><paramtype>SeeqSeq &amp;</paramtype></parameter><description><para>Sets the state of the generator using values from a <classname alt="boost::random::seed_seq">seed_seq</classname>. </para></description></method>
<method name="operator()"><type>boost::uint64_t</type><description><para>return one uint64 between min=0 and max=2^61-1 </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="nsteps"><paramtype>boost::uint64_t</paramtype></parameter><purpose>discard n steps, required in boost::random </purpose></method>
</method-group>
<constructor specifiers="explicit"><purpose>Constructor, unit vector as initial state, acted on by A^2^512. </purpose></constructor>
<constructor specifiers="explicit"><parameter name=""><paramtype>boost::uint64_t</paramtype></parameter><purpose>Constructor, one 64-bit seed. </purpose><description><para>&lt; constructor, one uint64_t seed, random numbers are statistically independent from any two distinct seeds, e.g. consecutive seeds are ok </para></description></constructor>
<constructor specifiers="explicit"><parameter name="clusterID"><paramtype>uint32_t</paramtype></parameter><parameter name="machineID"><paramtype>uint32_t</paramtype></parameter><parameter name="runID"><paramtype>uint32_t</paramtype></parameter><parameter name="streamID"><paramtype>uint32_t</paramtype></parameter><purpose>Constructor, four 32-bit seeds for 128-bit seeding flexibility. </purpose></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="ost"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="me"><paramtype>const <classname>mixmax_engine</classname> &amp;</paramtype></parameter><description><para>save the state of the RNG to a stream </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="in"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="me"><paramtype><classname>mixmax_engine</classname> &amp;</paramtype></parameter><description><para>read the state of the RNG from a stream </para></description></method>
</method-group>
<method-group name="private member functions">
<method name="MOD_MERSENNE"><type>boost::uint64_t</type><parameter name="k"><paramtype>boost::uint64_t</paramtype></parameter></method>
<method name="MULWU"><type>boost::uint64_t</type><parameter name="k"><paramtype>boost::uint64_t</paramtype></parameter></method>
<method name="seed_vielbein"><type>void</type><parameter name="X"><paramtype>rng_state_t *</paramtype></parameter><parameter name="i"><paramtype>unsigned int</paramtype></parameter></method>
<method name="seed_uniquestream"><type>void</type><parameter name="Xin"><paramtype>rng_state_t *</paramtype></parameter><parameter name="clusterID"><paramtype>uint32_t</paramtype></parameter><parameter name="machineID"><paramtype>uint32_t</paramtype></parameter><parameter name="runID"><paramtype>uint32_t</paramtype></parameter><parameter name="streamID"><paramtype>uint32_t</paramtype></parameter></method>
<method name="iterate_raw_vec"><type>boost::uint64_t</type><parameter name="Y"><paramtype>boost::uint64_t *</paramtype></parameter><parameter name="sumtotOld"><paramtype>boost::uint64_t</paramtype></parameter></method>
<method name="apply_bigskip"><type>boost::uint64_t</type><parameter name="Vout"><paramtype>boost::uint64_t *</paramtype></parameter><parameter name="Vin"><paramtype>boost::uint64_t *</paramtype></parameter><parameter name="clusterID"><paramtype>uint32_t</paramtype></parameter><parameter name="machineID"><paramtype>uint32_t</paramtype></parameter><parameter name="runID"><paramtype>uint32_t</paramtype></parameter><parameter name="streamID"><paramtype>uint32_t</paramtype></parameter></method>
<method name="modadd"><type>boost::uint64_t</type><parameter name="foo"><paramtype>boost::uint64_t</paramtype></parameter><parameter name="bar"><paramtype>boost::uint64_t</paramtype></parameter></method>
<method name="fmodmulM61"><type>boost::uint64_t</type><parameter name="cum"><paramtype>boost::uint64_t</paramtype></parameter><parameter name="s"><paramtype>boost::uint64_t</paramtype></parameter><parameter name="a"><paramtype>boost::uint64_t</paramtype></parameter></method>
</method-group>
</class><typedef name="mixmax"><description><para>Instantiation with a valid parameter set. </para></description><type><classname>mixmax_engine</classname>&lt; 17, 36, 0 &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/negative_binomial_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="negative_binomial_distribution"><template>
      <template-type-parameter name="IntType"><default>int</default></template-type-parameter>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The negative binomial distribution is an integer valued distribution with two parameters, <computeroutput>k</computeroutput> and <computeroutput>p</computeroutput>. The distribution produces non-negative values.</para><para>The distribution function is <inlineequation><alt>$\displaystyle P(i) = {k+i-1\choose i}p^k(1-p)^i$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_30.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle P(i) = {k+i-1\choose i}p^k(1-p)^i$</phrase></textobject></inlinemediaobject></inlineequation>.</para><para>This implementation uses a gamma-poisson mixture. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>negative_binomial_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="k" cv="const"><type>IntType</type><description><para>Returns the <computeroutput>k</computeroutput> parameter of the distribution. </para></description></method>
<method name="p" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>p</computeroutput> parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="k"><paramtype>IntType</paramtype><default>1</default></parameter><parameter name="p"><paramtype>RealType</paramtype><default>0.5</default></parameter><description><para>Construct a <classname alt="boost::random::negative_binomial_distribution::param_type">param_type</classname> object. <computeroutput>k</computeroutput> and <computeroutput>p</computeroutput> are the parameters of the distribution.</para><para>Requires: k &gt;=0 &amp;&amp; 0 &lt;= p &lt;= 1 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype><classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have the same values. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have different values. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>IntType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>IntType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the negative binomial distribution. </para></description></method>
<method name="operator()" cv="const"><type>IntType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the negative binomial distribution with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="k" cv="const"><type>IntType</type><description><para>Returns the <computeroutput>k</computeroutput> parameter of the distribution. </para></description></method>
<method name="p" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>p</computeroutput> parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>IntType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>IntType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="k"><paramtype>IntType</paramtype><default>1</default></parameter><parameter name="p"><paramtype>RealType</paramtype><default>0.5</default></parameter><description><para>Construct a <computeroutput><classname alt="boost::random::negative_binomial_distribution">negative_binomial_distribution</classname></computeroutput> object. <computeroutput>k</computeroutput> and <computeroutput>p</computeroutput> are the parameters of the distribution.</para><para>Requires: k &gt;=0 &amp;&amp; 0 &lt;= p &lt;= 1 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Construct an <computeroutput><classname alt="boost::random::negative_binomial_distribution">negative_binomial_distribution</classname></computeroutput> object from the parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="bd"><paramtype>const <classname>negative_binomial_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="bd"><paramtype><classname>negative_binomial_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>negative_binomial_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>negative_binomial_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce the same sequence of values, given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>negative_binomial_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>negative_binomial_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions could produce different sequences of values, given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/niederreiter_base2.hpp">
<namespace name="boost">
<namespace name="random">
<class name="niederreiter_base2_engine"><template>
      <template-type-parameter name="UIntType"/>
      <template-nontype-parameter name="w"><type>unsigned</type></template-nontype-parameter>
      <template-type-parameter name="Nb2Table"><default>default_niederreiter_base2_table</default></template-type-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::niederreiter_base2_engine">niederreiter_base2_engine</classname> model a <link linkend="boost_random.reference.concepts.quasi_random_number_generator">quasi-random number generator</link> . The <classname alt="boost::random::niederreiter_base2_engine">niederreiter_base2_engine</classname> uses the algorithm described in  <blockquote><para>  Bratley, Fox, Niederreiter, ACM Trans. Model. Comp. Sim. 2, 195 (1992).  </para></blockquote> </para><para><note><para><classname alt="boost::random::niederreiter_base2_engine">niederreiter_base2_engine</classname> skips trivial zeroes at the start of the sequence. For example, the beginning of the 2-dimensional Niederreiter base 2 sequence in <computeroutput><classname alt="boost::random::uniform_01">uniform_01</classname></computeroutput> distribution will look like this: <programlisting language="c++">0.5, 0.5,
0.75, 0.25,
0.25, 0.75,
0.375, 0.375,
0.875, 0.875,
...
</programlisting></para>
</note>
In the following documentation <computeroutput>X</computeroutput> denotes the concrete class of the template <classname alt="boost::random::niederreiter_base2_engine">niederreiter_base2_engine</classname> returning objects of type <computeroutput>UIntType</computeroutput>, u and v are the values of <computeroutput>X</computeroutput>.</para><para>Some member functions may throw exceptions of type std::range_error. This happens when the quasi-random domain is exhausted and the generator cannot produce any more values. The length of the low discrepancy sequence is given by <inlineequation><alt>$L=Dimension \times (2^{w} - 1)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_31.png"/></imageobject><textobject role="tex"><phrase>$L=Dimension \times (2^{w} - 1)$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><typedef name="result_type"><type>UIntType</type></typedef>
<method-group name="public member functions">
<method name="dimension" cv="const"><type>std::size_t</type><description><para>Returns: The dimension of of the quasi-random domain.</para><para>Throws: nothing. </para></description></method>
<method name="seed"><type>void</type><purpose>Throws: nothing. </purpose><description><para>Effects: Resets the quasi-random number generator state to the one given by the default construction. Equivalent to u.seed(0). </para></description></method>
<method name="seed"><type>void</type><parameter name="init"><paramtype>UIntType</paramtype></parameter><purpose>Throws: range_error. </purpose><description><para>Effects: Effectively sets the quasi-random number generator state to the <computeroutput>init</computeroutput>-th vector in the <computeroutput>s</computeroutput>-dimensional quasi-random domain, where <computeroutput>s</computeroutput> == X::dimension(). <programlisting language="c++">X u, v;
for(int i = 0; i &lt; N; ++i)
    for( std::size_t j = 0; j &lt; u.dimension(); ++j )
        u();
v.seed(N);
assert(u() == v());
</programlisting> </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns: Returns a successive element of an <computeroutput>s</computeroutput>-dimensional (s = X::dimension()) vector at each invocation. When all elements are exhausted, X::operator() begins anew with the starting element of a subsequent <computeroutput>s</computeroutput>-dimensional vector.</para><para>Throws: range_error. </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Effects: Advances *this state as if <computeroutput>z</computeroutput> consecutive X::operator() invocations were executed. <programlisting language="c++">X u = v;
for(int i = 0; i &lt; N; ++i)
    u();
v.discard(N);
assert(u() == v());
</programlisting></para><para>Throws: range_error. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="s"><paramtype>std::size_t</paramtype></parameter><description><para>Effects: Constructs the default <computeroutput>s</computeroutput>-dimensional Niederreiter base 2 quasi-random number generator.</para><para>Throws: bad_alloc, invalid_argument, range_error. </para></description></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns: Tight lower bound on the set of values returned by operator().</para><para>Throws: nothing. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns: Tight upper bound on the set of values returned by operator().</para><para>Throws: nothing. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>niederreiter_base2_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>niederreiter_base2_engine</classname> &amp;</paramtype></parameter><purpose>Returns true if the two generators will produce identical sequences of outputs. </purpose></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>niederreiter_base2_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>niederreiter_base2_engine</classname> &amp;</paramtype></parameter><purpose>Returns true if the two generators will produce different sequences of outputs. </purpose></method>
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>niederreiter_base2_engine</classname> &amp;</paramtype></parameter><purpose>Writes the textual representation of the generator to a <computeroutput>std::ostream</computeroutput>. </purpose></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>niederreiter_base2_engine</classname> &amp;</paramtype></parameter><purpose>Reads the textual representation of the generator from a <computeroutput>std::istream</computeroutput>. </purpose></method>
</method-group>
</class><typedef name="niederreiter_base2"><description><para><note><para>This specialization of <classname alt="boost::random::niederreiter_base2_engine">niederreiter_base2_engine</classname>  supports up to 4720 dimensions.</para>
</note>
Binary irreducible polynomials (primes in the ring <computeroutput>GF(2)[X]</computeroutput>, evaluated at <computeroutput>X=2</computeroutput>) were generated while condition <computeroutput>max(prime)</computeroutput> &lt; 2<superscript>16</superscript> was satisfied.</para><para>There are exactly 4720 such primes, which yields a Niederreiter base 2 table for 4720 dimensions.</para><para>However, it is possible to provide your own table to <classname alt="boost::random::niederreiter_base2_engine">niederreiter_base2_engine</classname>  should the default one be insufficient. </para></description><type><classname>niederreiter_base2_engine</classname>&lt; boost::uint_least64_t, 64u, default_niederreiter_base2_table &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/non_central_chi_squared_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="non_central_chi_squared_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The noncentral chi-squared distribution is a real valued distribution with two parameter, <computeroutput>k</computeroutput> and <computeroutput>lambda</computeroutput>. The distribution produces values &gt; 0.</para><para>This is the distribution of the sum of squares of k Normal distributed variates each with variance one and <inlineequation><alt>$\lambda$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_32.png"/></imageobject><textobject role="tex"><phrase>$\lambda$</phrase></textobject></inlinemediaobject></inlineequation> the sum of squares of the normal means.</para><para>The distribution function is <inlineequation><alt>$\displaystyle P(x) = \frac{1}{2} e^{-(x+\lambda)/2} \left( \frac{x}{\lambda} \right)^{k/4-1/2} I_{k/2-1}( \sqrt{\lambda x} )$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_33.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle P(x) = \frac{1}{2} e^{-(x+\lambda)/2} \left( \frac{x}{\lambda} \right)^{k/4-1/2} I_{k/2-1}( \sqrt{\lambda x} )$</phrase></textobject></inlinemediaobject></inlineequation>. where <inlineequation><alt>$\displaystyle I_\nu(z)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_34.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle I_\nu(z)$</phrase></textobject></inlinemediaobject></inlineequation> is a modified Bessel function of the first kind.</para><para>The algorithm is taken from</para><para> <blockquote><para>  "Monte Carlo Methods in Financial Engineering", Paul Glasserman, 2003, XIII, 596 p, Stochastic Modelling and Applied Probability, Vol. 53, ISBN 978-0-387-21617-1, p 124, Fig. 3.5.  </para></blockquote>  </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>non_central_chi_squared_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="k" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>k</computeroutput> parameter of the distribution </para></description></method>
<method name="lambda" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>lambda</computeroutput> parameter of the distribution </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="k"><paramtype>RealType</paramtype><default>1</default></parameter><parameter name="lambda"><paramtype>RealType</paramtype><default>1</default></parameter><description><para>Constructs the parameters of a <classname alt="boost::random::non_central_chi_squared_distribution">non_central_chi_squared_distribution</classname>. <computeroutput>k</computeroutput> and <computeroutput>lambda</computeroutput> are the parameter of the distribution.</para><para>Requires: k &gt; 0 &amp;&amp; lambda &gt; 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have the same values. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have different values. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>RealType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="eng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the non central chi squared distribution specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="operator()"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="eng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the non central chi squared distribution. </para></description></method>
<method name="k" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>k</computeroutput> parameter of the distribution. </para></description></method>
<method name="lambda" cv="const"><type>RealType</type><description><para>Returns the <computeroutput>lambda</computeroutput> parameter of the distribution. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Resets the distribution, so that subsequent uses does not depend on values already produced by it. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="k"><paramtype>RealType</paramtype><default>1</default></parameter><parameter name="lambda"><paramtype>RealType</paramtype><default>1</default></parameter><description><para>Construct a <computeroutput><classname alt="boost::random::non_central_chi_squared_distribution">non_central_chi_squared_distribution</classname></computeroutput> object. <computeroutput>k</computeroutput> and <computeroutput>lambda</computeroutput> are the parameter of the distribution.</para><para>Requires: k &gt; 0 &amp;&amp; lambda &gt; 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Construct a <computeroutput><classname alt="boost::random::non_central_chi_squared_distribution">non_central_chi_squared_distribution</classname></computeroutput> object from the parameter. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="dist"><paramtype>const <classname>non_central_chi_squared_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="dist"><paramtype>const <classname>non_central_chi_squared_distribution</classname> &amp;</paramtype></parameter><description><para>reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>non_central_chi_squared_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>non_central_chi_squared_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if two distributions have the same parameters and produce the same sequence of random numbers given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>non_central_chi_squared_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>non_central_chi_squared_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if two distributions have different parameters and/or can produce different sequences of random numbers given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/normal_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="normal_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::normal_distribution">normal_distribution</classname> model a  <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . Such a distribution produces random numbers <computeroutput>x</computeroutput> distributed with probability density function <inlineequation><alt>$\displaystyle p(x) = \frac{1}{\sqrt{2\pi}\sigma} e^{-\frac{(x-\mu)^2}{2\sigma^2}} $</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_35.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \frac{1}{\sqrt{2\pi}\sigma} e^{-\frac{(x-\mu)^2}{2\sigma^2}} $</phrase></textobject></inlinemediaobject></inlineequation>, where mean and sigma are the parameters of the distribution.</para><para>The implementation uses the "ziggurat" algorithm, as described in</para><para> <blockquote><para>  "The Ziggurat Method for Generating Random Variables", George Marsaglia and Wai Wan Tsang, Journal of Statistical Software, Volume 5, Number 8 (2000), 1-7.  </para></blockquote>  </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>normal_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="mean" cv="const"><type>RealType</type><description><para>Returns the mean of the distribution. </para></description></method>
<method name="sigma" cv="const"><type>RealType</type><description><para>Returns the standand deviation of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="mean"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="sigma"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::normal_distribution::param_type">param_type</classname></computeroutput> with a given mean and standard deviation.</para><para>Requires: sigma &gt;= 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::normal_distribution::param_type">param_type</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::normal_distribution::param_type">param_type</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>RealType</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="mean" cv="const"><type>RealType</type><description><para>Returns the mean of the distribution. </para></description></method>
<method name="sigma" cv="const"><type>RealType</type><description><para>Returns the standard deviation of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns a normal variate. </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a normal variate with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="mean"><paramtype>const RealType &amp;</paramtype><default>0.0</default></parameter><parameter name="sigma"><paramtype>const RealType &amp;</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::normal_distribution">normal_distribution</classname></computeroutput> object. <computeroutput>mean</computeroutput> and <computeroutput>sigma</computeroutput> are the parameters for the distribution.</para><para>Requires: sigma &gt;= 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::normal_distribution">normal_distribution</classname></computeroutput> object from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="nd"><paramtype>const <classname>normal_distribution</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::normal_distribution">normal_distribution</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="nd"><paramtype>const <classname>normal_distribution</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::normal_distribution">normal_distribution</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>normal_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>normal_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::normal_distribution">normal_distribution</classname></computeroutput> will return identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>normal_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>normal_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::normal_distribution">normal_distribution</classname></computeroutput> will return different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/piecewise_constant_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="piecewise_constant_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
      <template-type-parameter name="WeightType"><default>double</default></template-type-parameter>
    </template><description><para>The class <computeroutput><classname alt="boost::random::piecewise_constant_distribution">piecewise_constant_distribution</classname></computeroutput> models a <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>piecewise_constant_distribution</classname></type></typedef>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
<method-group name="public member functions">
<method name="intervals" cv="const"><type>std::vector&lt; RealType &gt;</type><description><para>Returns a vector containing the interval boundaries. </para></description></method>
<method name="densities" cv="const"><type>std::vector&lt; RealType &gt;</type><description><para>Returns a vector containing the probability densities over all the intervals of the distribution. </para></description></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput><classname alt="boost::random::piecewise_constant_distribution::param_type">param_type</classname></computeroutput> object, representing a distribution that produces values uniformly distributed in the range [0, 1). </para></description></constructor>
<constructor><template>
          <template-type-parameter name="IntervalIter"/>
          <template-type-parameter name="WeightIter"/>
        </template><parameter name="intervals_first"><paramtype>IntervalIter</paramtype></parameter><parameter name="intervals_last"><paramtype>IntervalIter</paramtype></parameter><parameter name="weight_first"><paramtype>WeightIter</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::piecewise_constant_distribution::param_type">param_type</classname></computeroutput> object from two iterator ranges containing the interval boundaries and the interval weights. If there are less than two boundaries, then this is equivalent to the default constructor and creates a single interval, [0, 1).</para><para>The values of the interval boundaries must be strictly increasing, and the number of weights must be one less than the number of interval boundaries. If there are extra weights, they are ignored. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="F"/>
        </template><parameter name="il"><paramtype>const std::initializer_list&lt; T &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>F</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::piecewise_constant_distribution::param_type">param_type</classname></computeroutput> object from an initializer_list containing the interval boundaries and a unary function specifying the weights. Each weight is determined by calling the function at the midpoint of the corresponding interval.</para><para>If the initializer_list contains less than two elements, this is equivalent to the default constructor and the distribution will produce values uniformly distributed in the range [0, 1). </para></description></constructor>
<constructor><template>
          <template-type-parameter name="IntervalRange"/>
          <template-type-parameter name="WeightRange"/>
        </template><parameter name="intervals_arg"><paramtype>const IntervalRange &amp;</paramtype></parameter><parameter name="weights_arg"><paramtype>const WeightRange &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::piecewise_constant_distribution::param_type">param_type</classname></computeroutput> object from Boost.Range ranges holding the interval boundaries and the weights. If there are less than two interval boundaries, this is equivalent to the default constructor and the distribution will produce values uniformly distributed in the range [0, 1). The number of weights must be one less than the number of interval boundaries. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="F"/>
        </template><parameter name="nw"><paramtype>std::size_t</paramtype></parameter><parameter name="xmin"><paramtype>RealType</paramtype></parameter><parameter name="xmax"><paramtype>RealType</paramtype></parameter><parameter name="f"><paramtype>F</paramtype></parameter><description><para>Constructs the parameters for a distribution that approximates a function. The range of the distribution is [xmin, xmax). This range is divided into nw equally sized intervals and the weights are found by calling the unary function f on the midpoints of the intervals. </para></description></constructor>
</class><typedef name="input_type"><type>std::size_t</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a value distributed according to the parameters of the piecewist_constant_distribution. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a value distributed according to the parameters specified by param. </para></description></method>
<method name="min" cv="const"><type>result_type</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>result_type</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="densities" cv="const"><type>std::vector&lt; RealType &gt;</type><description><para>Returns a vector containing the probability density over each interval. </para></description></method>
<method name="intervals" cv="const"><type>std::vector&lt; RealType &gt;</type><description><para>Returns a vector containing the interval boundaries. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor><description><para>Creates a new <computeroutput><classname alt="boost::random::piecewise_constant_distribution">piecewise_constant_distribution</classname></computeroutput> with a single interval, [0, 1). </para></description></constructor>
<constructor><template>
          <template-type-parameter name="IntervalIter"/>
          <template-type-parameter name="WeightIter"/>
        </template><parameter name="first_interval"><paramtype>IntervalIter</paramtype></parameter><parameter name="last_interval"><paramtype>IntervalIter</paramtype></parameter><parameter name="first_weight"><paramtype>WeightIter</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_constant_distribution">piecewise_constant_distribution</classname> from two iterator ranges containing the interval boundaries and the interval weights. If there are less than two boundaries, then this is equivalent to the default constructor and creates a single interval, [0, 1).</para><para>The values of the interval boundaries must be strictly increasing, and the number of weights must be one less than the number of interval boundaries. If there are extra weights, they are ignored.</para><para>For example,</para><para><programlisting language="c++">double intervals[] = { 0.0, 1.0, 4.0 };
double weights[] = { 1.0, 1.0 };
piecewise_constant_distribution&lt;&gt; dist(
    &amp;intervals[0], &amp;intervals[0] + 3, &amp;weights[0]);
</programlisting></para><para>The distribution has a 50% chance of producing a value between 0 and 1 and a 50% chance of producing a value between 1 and 4. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="F"/>
        </template><parameter name="il"><paramtype>std::initializer_list&lt; T &gt;</paramtype></parameter><parameter name="f"><paramtype>F</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_constant_distribution">piecewise_constant_distribution</classname> from an initializer_list containing the interval boundaries and a unary function specifying the weights. Each weight is determined by calling the function at the midpoint of the corresponding interval.</para><para>If the initializer_list contains less than two elements, this is equivalent to the default constructor and the distribution will produce values uniformly distributed in the range [0, 1). </para></description></constructor>
<constructor><template>
          <template-type-parameter name="IntervalsRange"/>
          <template-type-parameter name="WeightsRange"/>
        </template><parameter name="intervals_arg"><paramtype>const IntervalsRange &amp;</paramtype></parameter><parameter name="weights_arg"><paramtype>const WeightsRange &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_constant_distribution">piecewise_constant_distribution</classname> from Boost.Range ranges holding the interval boundaries and the weights. If there are less than two interval boundaries, this is equivalent to the default constructor and the distribution will produce values uniformly distributed in the range [0, 1). The number of weights must be one less than the number of interval boundaries. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="F"/>
        </template><parameter name="nw"><paramtype>std::size_t</paramtype></parameter><parameter name="xmin"><paramtype>RealType</paramtype></parameter><parameter name="xmax"><paramtype>RealType</paramtype></parameter><parameter name="f"><paramtype>F</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_constant_distribution">piecewise_constant_distribution</classname> that approximates a function. The range of the distribution is [xmin, xmax). This range is divided into nw equally sized intervals and the weights are found by calling the unary function f on the midpoints of the intervals. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_constant_distribution">piecewise_constant_distribution</classname> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="pcd"><paramtype>const <classname>piecewise_constant_distribution</classname> &amp;</paramtype></parameter><description><para>Writes a distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="pcd"><paramtype>const <classname>piecewise_constant_distribution</classname> &amp;</paramtype></parameter><description><para>Reads a distribution from a <computeroutput>std::istream</computeroutput> </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>piecewise_constant_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>piecewise_constant_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will return the same sequence of values, when passed equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>piecewise_constant_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>piecewise_constant_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may return different sequences of values, when passed equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/piecewise_linear_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="piecewise_linear_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The class <computeroutput><classname alt="boost::random::piecewise_linear_distribution">piecewise_linear_distribution</classname></computeroutput> models a <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>piecewise_linear_distribution</classname></type></typedef>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
<method-group name="public member functions">
<method name="intervals" cv="const"><type>std::vector&lt; RealType &gt;</type><description><para>Returns a vector containing the interval boundaries. </para></description></method>
<method name="densities" cv="const"><type>std::vector&lt; RealType &gt;</type><description><para>Returns a vector containing the probability densities at all the interval boundaries. </para></description></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput><classname alt="boost::random::piecewise_linear_distribution::param_type">param_type</classname></computeroutput> object, representing a distribution that produces values uniformly distributed in the range [0, 1). </para></description></constructor>
<constructor><template>
          <template-type-parameter name="IntervalIter"/>
          <template-type-parameter name="WeightIter"/>
        </template><parameter name="intervals_first"><paramtype>IntervalIter</paramtype></parameter><parameter name="intervals_last"><paramtype>IntervalIter</paramtype></parameter><parameter name="weight_first"><paramtype>WeightIter</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::piecewise_linear_distribution::param_type">param_type</classname></computeroutput> object from two iterator ranges containing the interval boundaries and weights at the boundaries. If there are fewer than two boundaries, then this is equivalent to the default constructor and the distribution will produce values uniformly distributed in the range [0, 1).</para><para>The values of the interval boundaries must be strictly increasing, and the number of weights must be the same as the number of interval boundaries. If there are extra weights, they are ignored. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="F"/>
        </template><parameter name="il"><paramtype>const std::initializer_list&lt; T &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>F</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::piecewise_linear_distribution::param_type">param_type</classname></computeroutput> object from an initializer_list containing the interval boundaries and a unary function specifying the weights at the boundaries. Each weight is determined by calling the function at the corresponding point.</para><para>If the initializer_list contains fewer than two elements, this is equivalent to the default constructor and the distribution will produce values uniformly distributed in the range [0, 1). </para></description></constructor>
<constructor><template>
          <template-type-parameter name="IntervalRange"/>
          <template-type-parameter name="WeightRange"/>
        </template><parameter name="intervals_arg"><paramtype>const IntervalRange &amp;</paramtype></parameter><parameter name="weights_arg"><paramtype>const WeightRange &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::piecewise_linear_distribution::param_type">param_type</classname></computeroutput> object from Boost.Range ranges holding the interval boundaries and the weights at the boundaries. If there are fewer than two interval boundaries, this is equivalent to the default constructor and the distribution will produce values uniformly distributed in the range [0, 1). The number of weights must be equal to the number of interval boundaries. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="F"/>
        </template><parameter name="nw"><paramtype>std::size_t</paramtype></parameter><parameter name="xmin"><paramtype>RealType</paramtype></parameter><parameter name="xmax"><paramtype>RealType</paramtype></parameter><parameter name="f"><paramtype>F</paramtype></parameter><description><para>Constructs the parameters for a distribution that approximates a function. The range of the distribution is [xmin, xmax). This range is divided into nw equally sized intervals and the weights are found by calling the unary function f on the boundaries of the intervals. </para></description></constructor>
</class><typedef name="input_type"><type>std::size_t</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a value distributed according to the parameters of the <classname alt="boost::random::piecewise_linear_distribution">piecewise_linear_distribution</classname>. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a value distributed according to the parameters specified by param. </para></description></method>
<method name="min" cv="const"><type>result_type</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>result_type</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="densities" cv="const"><type>std::vector&lt; RealType &gt;</type><description><para>Returns a vector containing the probability densities at the interval boundaries. </para></description></method>
<method name="intervals" cv="const"><type>std::vector&lt; RealType &gt;</type><description><para>Returns a vector containing the interval boundaries. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor><description><para>Creates a new <computeroutput><classname alt="boost::random::piecewise_linear_distribution">piecewise_linear_distribution</classname></computeroutput> that produces values uniformly distributed in the range [0, 1). </para></description></constructor>
<constructor><template>
          <template-type-parameter name="IntervalIter"/>
          <template-type-parameter name="WeightIter"/>
        </template><parameter name="first_interval"><paramtype>IntervalIter</paramtype></parameter><parameter name="last_interval"><paramtype>IntervalIter</paramtype></parameter><parameter name="first_weight"><paramtype>WeightIter</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_linear_distribution">piecewise_linear_distribution</classname> from two iterator ranges containing the interval boundaries and the weights at the boundaries. If there are fewer than two boundaries, then this is equivalent to the default constructor and creates a distribution that produces values uniformly distributed in the range [0, 1).</para><para>The values of the interval boundaries must be strictly increasing, and the number of weights must be equal to the number of interval boundaries. If there are extra weights, they are ignored.</para><para>For example,</para><para><programlisting language="c++">double intervals[] = { 0.0, 1.0, 2.0 };
double weights[] = { 0.0, 1.0, 0.0 };
piecewise_constant_distribution&lt;&gt; dist(
    &amp;intervals[0], &amp;intervals[0] + 3, &amp;weights[0]);
</programlisting></para><para>produces a triangle distribution. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="F"/>
        </template><parameter name="il"><paramtype>std::initializer_list&lt; T &gt;</paramtype></parameter><parameter name="f"><paramtype>F</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_linear_distribution">piecewise_linear_distribution</classname> from an initializer_list containing the interval boundaries and a unary function specifying the weights. Each weight is determined by calling the function at the corresponding interval boundary.</para><para>If the initializer_list contains fewer than two elements, this is equivalent to the default constructor and the distribution will produce values uniformly distributed in the range [0, 1). </para></description></constructor>
<constructor><template>
          <template-type-parameter name="IntervalsRange"/>
          <template-type-parameter name="WeightsRange"/>
        </template><parameter name="intervals_arg"><paramtype>const IntervalsRange &amp;</paramtype></parameter><parameter name="weights_arg"><paramtype>const WeightsRange &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_linear_distribution">piecewise_linear_distribution</classname> from Boost.Range ranges holding the interval boundaries and the weights. If there are fewer than two interval boundaries, this is equivalent to the default constructor and the distribution will produce values uniformly distributed in the range [0, 1). The number of weights must be equal to the number of interval boundaries. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="F"/>
        </template><parameter name="nw"><paramtype>std::size_t</paramtype></parameter><parameter name="xmin"><paramtype>RealType</paramtype></parameter><parameter name="xmax"><paramtype>RealType</paramtype></parameter><parameter name="f"><paramtype>F</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_linear_distribution">piecewise_linear_distribution</classname> that approximates a function. The range of the distribution is [xmin, xmax). This range is divided into nw equally sized intervals and the weights are found by calling the unary function f on the interval boundaries. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::piecewise_linear_distribution">piecewise_linear_distribution</classname> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="pld"><paramtype>const <classname>piecewise_linear_distribution</classname> &amp;</paramtype></parameter><description><para>Writes a distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="pld"><paramtype>const <classname>piecewise_linear_distribution</classname> &amp;</paramtype></parameter><description><para>Reads a distribution from a <computeroutput>std::istream</computeroutput> </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>piecewise_linear_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>piecewise_linear_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will return the same sequence of values, when passed equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>piecewise_linear_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>piecewise_linear_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may return different sequences of values, when passed equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/poisson_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="poisson_distribution"><template>
      <template-type-parameter name="IntType"><default>int</default></template-type-parameter>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>An instantiation of the class template <computeroutput><classname alt="boost::random::poisson_distribution">poisson_distribution</classname></computeroutput> is a model of <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . The poisson distribution has <inlineequation><alt>$p(i) = \frac{e^{-\lambda}\lambda^i}{i!}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_36.png"/></imageobject><textobject role="tex"><phrase>$p(i) = \frac{e^{-\lambda}\lambda^i}{i!}$</phrase></textobject></inlinemediaobject></inlineequation></para><para>This implementation is based on the PTRD algorithm described</para><para> <blockquote><para>  "The transformed rejection method for generating Poisson random variables", Wolfgang Hormann, Insurance: Mathematics and Economics Volume 12, Issue 1, February 1993, Pages 39-45  </para></blockquote>  </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>poisson_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="mean" cv="const"><type>RealType</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="mean"><paramtype>RealType</paramtype><default>1</default></parameter><description><para>Construct a <classname alt="boost::random::poisson_distribution::param_type">param_type</classname> object with the parameter "mean"</para><para>Requires: mean &gt; 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype><classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have the same values. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the parameters have different values. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>IntType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>IntType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the poisson distribution. </para></description></method>
<method name="operator()" cv="const"><type>IntType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the poisson distribution with parameters specified by param. </para></description></method>
<method name="mean" cv="const"><type>RealType</type><description><para>Returns the "mean" parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>IntType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>IntType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="mean"><paramtype>RealType</paramtype><default>1</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::poisson_distribution">poisson_distribution</classname></computeroutput> with the parameter <computeroutput>mean</computeroutput>.</para><para>Requires: mean &gt; 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Construct an <computeroutput><classname alt="boost::random::poisson_distribution">poisson_distribution</classname></computeroutput> object from the parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="pd"><paramtype>const <classname>poisson_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the parameters of the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="pd"><paramtype><classname>poisson_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the parameters of the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>poisson_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>poisson_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce the same sequence of values, given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>poisson_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>poisson_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions could produce different sequences of values, given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/random_device.hpp">
<namespace name="boost">
<namespace name="random">
<class name="random_device"><inherit access="private">noncopyable</inherit><description><para>Class <classname alt="boost::random::random_device">random_device</classname> models a <link linkend="boost_random.reference.concepts.non_deterministic_uniform_random_number_generator">non-deterministic random number generator</link> . It uses one or more implementation-defined stochastic processes to generate a sequence of uniformly distributed non-deterministic random numbers. For those environments where a non-deterministic random number generator is not available, class <classname alt="boost::random::random_device">random_device</classname> must not be implemented. See</para><para> <blockquote><para>  "Randomness Recommendations for Security", D. Eastlake, S. Crocker, J. Schiller, Network Working Group, RFC 1750, December 1994  </para></blockquote> </para><para>for further discussions.</para><para> <note><para>  Some operating systems abstract the computer hardware enough to make it difficult to non-intrusively monitor stochastic processes. However, several do provide a special device for exactly this purpose. It seems to be impossible to emulate the functionality using Standard C++ only, so users should be aware that this class may not be available on all platforms.  </para></note> </para><para><emphasis role="bold">Implementation Note for Linux</emphasis></para><para>On the Linux operating system, token is interpreted as a filesystem path. It is assumed that this path denotes an operating system pseudo-device which generates a stream of non-deterministic random numbers. The pseudo-device should never signal an error or end-of-file. Otherwise, <computeroutput>std::ios_base::failure</computeroutput> is thrown. By default,  <classname alt="boost::random::random_device">random_device</classname> uses the /dev/urandom pseudo-device to retrieve the random numbers. Another option would be to specify the /dev/random pseudo-device, which blocks on reads if the entropy pool has no more random bits available.</para><para><emphasis role="bold">Implementation Note for Windows</emphasis></para><para>On the Windows operating system, token is interpreted as the name of a cryptographic service provider. By default <classname alt="boost::random::random_device">random_device</classname> uses MS_DEF_PROV.</para><para><emphasis role="bold">Performance</emphasis></para><para>The test program <ulink url="../../libs/random/performance/nondet_random_speed.cpp">nondet_random_speed.cpp</ulink> measures the execution times of the random_device.hpp implementation of the above algorithms in a tight loop. The performance has been evaluated on an Intel(R) Core(TM) i7 CPU Q 840 @ 1.87GHz, 1867 Mhz with Visual C++ 2010, Microsoft Windows 7 Professional and with gcc 4.4.5, Ubuntu Linux 2.6.35-25-generic.</para><para><informaltable><tgroup cols="2"><tbody><row>
<entry><emphasis role="bold">Platform</emphasis></entry><entry><emphasis role="bold">time per invocation [microseconds] </emphasis></entry></row>
<row>
<entry>Windows </entry><entry>2.9 </entry></row>
<row>
<entry>Linux </entry><entry>1.7 </entry></row>
</tbody></tgroup></informaltable>
</para><para>The measurement error is estimated at +/- 1 usec. </para></description><typedef name="result_type"><type>unsigned int</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the <classname alt="boost::random::random_device">random_device</classname> can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the <classname alt="boost::random::random_device">random_device</classname> can produce. </para></description></method>
</method-group>
<method-group name="public member functions">
<method name="entropy" cv="const"><type>double</type><description><para>Returns: An entropy estimate for the random numbers returned by operator(), in the range min() to log2( max()+1). A deterministic random number generator (e.g. a pseudo-random number engine) has entropy 0.</para><para>Throws: Nothing. </para></description></method>
<method name="operator()"><type>unsigned int</type><description><para>Returns a random value in the range [min, max]. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="begin"><paramtype>Iter</paramtype></parameter><parameter name="end"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random 32-bit values. </para></description></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput><classname alt="boost::random::random_device">random_device</classname></computeroutput>, optionally using the default device. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="token"><paramtype>const std::string &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::random_device">random_device</classname></computeroutput>, optionally using the given token as an access specification (for example, a URL) to some implementation-defined service for monitoring a stochastic process. </para></description></constructor>
<destructor/>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/random_number_generator.hpp">
<namespace name="boost">
<namespace name="random">
<class name="random_number_generator"><template>
      <template-type-parameter name="URNG"/>
      <template-type-parameter name="IntType"><default>long</default></template-type-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::random_number_generator">random_number_generator</classname> model a RandomNumberGenerator (std:25.2.11 [lib.alg.random.shuffle]). On each invocation, it returns a uniformly distributed integer in the range [0..n).</para><para>The template parameter IntType shall denote some integer-like value type. </para></description><typedef name="base_type"><type>URNG</type></typedef>
<typedef name="argument_type"><type>IntType</type></typedef>
<typedef name="result_type"><type>IntType</type></typedef>
<method-group name="public member functions">
<method name="operator()"><type>result_type</type><parameter name="n"><paramtype>argument_type</paramtype></parameter><description><para>Returns a value in the range [0, n) </para></description></method>
</method-group>
<constructor><parameter name="rng"><paramtype>base_type &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::random_number_generator">random_number_generator</classname> functor with the given  <link linkend="boost_random.reference.concepts.uniform_random_number_generator">uniform random number generator</link>  as the underlying source of random numbers. </para></description></constructor>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/ranlux.hpp">
<namespace name="boost">
<namespace name="random">
<typedef name="ranlux_base"><type><classname>subtract_with_carry_engine</classname>&lt; uint32_t, 24, 10, 24 &gt;</type></typedef>
<typedef name="ranlux_base_01"><type><classname>subtract_with_carry_01_engine</classname>&lt; float, 24, 10, 24 &gt;</type></typedef>
<typedef name="ranlux64_base_01"><type><classname>subtract_with_carry_01_engine</classname>&lt; double, 48, 10, 24 &gt;</type></typedef>
<typedef name="ranlux3"><description><para>The ranlux family of generators are described in</para><para> <blockquote><para>  "A portable high-quality random number generator for lattice field theory
 calculations", M. Luescher, Computer Physics Communications, 79 (1994) pp 100-110.  </para></blockquote> </para><para>The levels are given in</para><para> <blockquote><para>  "RANLUX: A Fortran implementation of the high-quality
 pseudorandom number generator of Luescher", F. James, Computer Physics Communications 79 (1994) 111-114  </para></blockquote>  </para></description><type><classname>discard_block_engine</classname>&lt; <classname>ranlux_base</classname>, 223, 24 &gt;</type></typedef>
<typedef name="ranlux4"><description><para>The ranlux family of generators are described in</para><para> <blockquote><para>  "A portable high-quality random number generator for lattice field theory
 calculations", M. Luescher, Computer Physics Communications, 79 (1994) pp 100-110.  </para></blockquote> </para><para>The levels are given in</para><para> <blockquote><para>  "RANLUX: A Fortran implementation of the high-quality
 pseudorandom number generator of Luescher", F. James, Computer Physics Communications 79 (1994) 111-114  </para></blockquote>  </para></description><type><classname>discard_block_engine</classname>&lt; <classname>ranlux_base</classname>, 389, 24 &gt;</type></typedef>
<typedef name="ranlux3_01"><description><para>The ranlux family of generators are described in</para><para> <blockquote><para>  "A portable high-quality random number generator for lattice field theory
 calculations", M. Luescher, Computer Physics Communications, 79 (1994) pp 100-110.  </para></blockquote> </para><para>The levels are given in</para><para> <blockquote><para>  "RANLUX: A Fortran implementation of the high-quality
 pseudorandom number generator of Luescher", F. James, Computer Physics Communications 79 (1994) 111-114  </para></blockquote>  </para></description><type><classname>discard_block_engine</classname>&lt; <classname>ranlux_base_01</classname>, 223, 24 &gt;</type></typedef>
<typedef name="ranlux4_01"><description><para>The ranlux family of generators are described in</para><para> <blockquote><para>  "A portable high-quality random number generator for lattice field theory
 calculations", M. Luescher, Computer Physics Communications, 79 (1994) pp 100-110.  </para></blockquote> </para><para>The levels are given in</para><para> <blockquote><para>  "RANLUX: A Fortran implementation of the high-quality
 pseudorandom number generator of Luescher", F. James, Computer Physics Communications 79 (1994) 111-114  </para></blockquote>  </para></description><type><classname>discard_block_engine</classname>&lt; <classname>ranlux_base_01</classname>, 389, 24 &gt;</type></typedef>
<typedef name="ranlux64_3_01"><description><para>The ranlux family of generators are described in</para><para> <blockquote><para>  "A portable high-quality random number generator for lattice field theory
 calculations", M. Luescher, Computer Physics Communications, 79 (1994) pp 100-110.  </para></blockquote> </para><para>The levels are given in</para><para> <blockquote><para>  "RANLUX: A Fortran implementation of the high-quality
 pseudorandom number generator of Luescher", F. James, Computer Physics Communications 79 (1994) 111-114  </para></blockquote>  </para></description><type><classname>discard_block_engine</classname>&lt; <classname>ranlux64_base_01</classname>, 223, 24 &gt;</type></typedef>
<typedef name="ranlux64_4_01"><description><para>The ranlux family of generators are described in</para><para> <blockquote><para>  "A portable high-quality random number generator for lattice field theory
 calculations", M. Luescher, Computer Physics Communications, 79 (1994) pp 100-110.  </para></blockquote> </para><para>The levels are given in</para><para> <blockquote><para>  "RANLUX: A Fortran implementation of the high-quality
 pseudorandom number generator of Luescher", F. James, Computer Physics Communications 79 (1994) 111-114  </para></blockquote>  </para></description><type><classname>discard_block_engine</classname>&lt; <classname>ranlux64_base_01</classname>, 389, 24 &gt;</type></typedef>
<typedef name="ranlux64_base"><type><classname>subtract_with_carry_engine</classname>&lt; uint64_t, 48, 10, 24 &gt;</type></typedef>
<typedef name="ranlux64_3"><description><para>The ranlux family of generators are described in</para><para> <blockquote><para>  "A portable high-quality random number generator for lattice field theory
 calculations", M. Luescher, Computer Physics Communications, 79 (1994) pp 100-110.  </para></blockquote> </para><para>The levels are given in</para><para> <blockquote><para>  "RANLUX: A Fortran implementation of the high-quality
 pseudorandom number generator of Luescher", F. James, Computer Physics Communications 79 (1994) 111-114  </para></blockquote>  </para></description><type><classname>discard_block_engine</classname>&lt; <classname>ranlux64_base</classname>, 223, 24 &gt;</type></typedef>
<typedef name="ranlux64_4"><description><para>The ranlux family of generators are described in</para><para> <blockquote><para>  "A portable high-quality random number generator for lattice field theory
 calculations", M. Luescher, Computer Physics Communications, 79 (1994) pp 100-110.  </para></blockquote> </para><para>The levels are given in</para><para> <blockquote><para>  "RANLUX: A Fortran implementation of the high-quality
 pseudorandom number generator of Luescher", F. James, Computer Physics Communications 79 (1994) 111-114  </para></blockquote>  </para></description><type><classname>discard_block_engine</classname>&lt; <classname>ranlux64_base</classname>, 389, 24 &gt;</type></typedef>
<typedef name="ranlux24_base"><type><classname>subtract_with_carry_engine</classname>&lt; uint32_t, 24, 10, 24 &gt;</type></typedef>
<typedef name="ranlux48_base"><type><classname>subtract_with_carry_engine</classname>&lt; uint64_t, 48, 5, 12 &gt;</type></typedef>
<typedef name="ranlux24"><type><classname>discard_block_engine</classname>&lt; <classname>ranlux24_base</classname>, 223, 23 &gt;</type></typedef>
<typedef name="ranlux48"><type><classname>discard_block_engine</classname>&lt; <classname>ranlux48_base</classname>, 389, 11 &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/seed_seq.hpp">
<namespace name="boost">
<namespace name="random">
<class name="seed_seq"><description><para>The class <computeroutput><classname alt="boost::random::seed_seq">seed_seq</classname></computeroutput> stores a sequence of 32-bit words for seeding a <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . These words will be combined to fill the entire state of the generator. </para></description><typedef name="result_type"><type>boost::uint_least32_t</type></typedef>
<method-group name="public member functions">
<method name="generate" cv="const"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with 32-bit values based on the stored sequence.</para><para>Requires: Iter must be a Random Access Iterator whose value type is an unsigned integral type at least 32 bits wide. </para></description></method>
<method name="size" cv="const"><type>std::size_t</type><description><para>Returns the size of the sequence. </para></description></method>
<method name="param"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="out"><paramtype>Iter</paramtype></parameter><description><para>Writes the stored sequence to iter. </para></description></method>
</method-group>
<constructor><description><para>Initializes a <classname alt="boost::random::seed_seq">seed_seq</classname> to hold an empty sequence. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="T"/>
        </template><parameter name="il"><paramtype>const std::initializer_list&lt; T &gt; &amp;</paramtype></parameter><description><para>Initializes the sequence from an initializer_list. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Initializes the sequence from an iterator range. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="Range"/>
        </template><parameter name="range"><paramtype>const Range &amp;</paramtype></parameter><description><para>Initializes the sequence from Boost.Range range. </para></description></constructor>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/shuffle_order.hpp">
<namespace name="boost">
<namespace name="random">
<class name="shuffle_order_engine"><template>
      <template-type-parameter name="UniformRandomNumberGenerator"/>
      <template-nontype-parameter name="k"><type>std::size_t</type></template-nontype-parameter>
    </template><description><para>Instatiations of class template <computeroutput><classname alt="boost::random::shuffle_order_engine">shuffle_order_engine</classname></computeroutput> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . It mixes the output of some (usually <classname alt="boost::random::linear_congruential_engine">linear_congruential_engine</classname>)  <link linkend="boost_random.reference.concepts.uniform_random_number_generator">uniform random number generator</link>  to get better statistical properties. The algorithm is described in</para><para> <blockquote><para>  "Improving a poor random number generator", Carter Bays and S.D. Durham, ACM Transactions on Mathematical Software, Vol 2, No. 1, March 1976, pp. 59-64. <ulink url="http://doi.acm.org/10.1145/355666.355670">http://doi.acm.org/10.1145/355666.355670</ulink>  </para></blockquote> </para><para>The output of the base generator is buffered in an array of length k. Every output X(n) has a second role: It gives an index into the array where X(n+1) will be retrieved. Used array elements are replaced with fresh output from the base generator.</para><para>Template parameters are the base generator and the array length k, which should be around 100. </para></description><typedef name="base_type"><type>UniformRandomNumberGenerator</type></typedef>
<typedef name="result_type"><type>base_type::result_type</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="buffer_size" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="table_size" specifiers="static"><type>const std::size_t</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type></method>
<method name="seed"><type>void</type><parameter name="seed"><paramtype>result_type</paramtype></parameter><description><para>Invokes the one-argument seed method of the base generator with the parameter seed and re-initializes the internal buffer array.</para><para>Complexity: Exactly k+1 invocations of the base generator. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Invokes the one-argument seed method of the base generator with the parameter seq and re-initializes the internal buffer array.</para><para>Complexity: Exactly k+1 invocations of the base generator. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter></method>
<method name="base" cv="const"><type>const base_type &amp;</type></method>
<method name="operator()"><type>result_type</type></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the generator by z steps. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with pseudo-random values. </para></description></method>
</method-group>
<constructor><description><para>Constructs a <computeroutput><classname alt="boost::random::shuffle_order_engine">shuffle_order_engine</classname></computeroutput> by invoking the default constructor of the base generator.</para><para>Complexity: Exactly k+1 invocations of the base generator. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="s"><paramtype>result_type</paramtype></parameter><description><para>Constructs a <computeroutput>shuffle_output_engine</computeroutput> by invoking the one-argument constructor of the base generator with the parameter seed.</para><para>Complexity: Exactly k+1 invocations of the base generator. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter></constructor>
<constructor specifiers="explicit"><parameter name="rng"><paramtype>const base_type &amp;</paramtype></parameter><description><para>Constructs a <computeroutput>shuffle_output_engine</computeroutput> by using a copy of the provided generator.</para><para>Precondition: The template argument UniformRandomNumberGenerator shall denote a CopyConstructible type.</para><para>Complexity: Exactly k+1 invocations of the base generator. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="rng"><paramtype>base_type &amp;&amp;</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>shuffle_order_engine</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::shuffle_order_engine">shuffle_order_engine</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>shuffle_order_engine</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::shuffle_order_engine">shuffle_order_engine</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>shuffle_order_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>shuffle_order_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>shuffle_order_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>shuffle_order_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences. </para></description></method>
</method-group>
</class><typedef name="kreutzer1986"><description><para>According to Harry Erwin (private e-mail), the specialization <computeroutput>kreutzer1986</computeroutput> was suggested in:</para><para> <blockquote><para>  "System Simulation: Programming Styles and Languages (International
Computer Science Series)", Wolfgang Kreutzer, Addison-Wesley, December 1986.  </para></blockquote>  </para></description><type><classname>shuffle_order_engine</classname>&lt; <classname>linear_congruential_engine</classname>&lt; uint32_t, 1366, 150889, 714025 &gt;, 97 &gt;</type></typedef>
<typedef name="knuth_b"><description><para>The specialization <computeroutput>knuth_b</computeroutput> is specified by the C++ standard. It is described in</para><para> <blockquote><para>  "The Art of Computer Programming, Second Edition, Volume 2,
Seminumerical Algorithms", Donald Knuth, Addison-Wesley, 1981.  </para></blockquote>  </para></description><type><classname>shuffle_order_engine</classname>&lt; <classname>minstd_rand0</classname>, 256 &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/sobol.hpp">
<namespace name="boost">
<namespace name="random">
<class name="sobol_engine"><template>
      <template-type-parameter name="UIntType"/>
      <template-nontype-parameter name="w"><type>unsigned</type></template-nontype-parameter>
      <template-type-parameter name="SobolTables"><default>default_sobol_table</default></template-type-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::sobol_engine">sobol_engine</classname> model a <link linkend="boost_random.reference.concepts.quasi_random_number_generator">quasi-random number generator</link> . The <classname alt="boost::random::sobol_engine">sobol_engine</classname> uses the algorithm described in  <blockquote><para>  [Bratley+Fox, TOMS 14, 88 (1988)] and [Antonov+Saleev, USSR Comput. Maths. Math. Phys. 19, 252 (1980)]  </para></blockquote> </para><para><note><para><classname alt="boost::random::sobol_engine">sobol_engine</classname> skips trivial zeroes at the start of the sequence. For example, the beginning of the 2-dimensional Sobol sequence in <computeroutput><classname alt="boost::random::uniform_01">uniform_01</classname></computeroutput> distribution will look like this: <programlisting language="c++">0.5, 0.5,
0.75, 0.25,
0.25, 0.75,
0.375, 0.375,
0.875, 0.875,
...
</programlisting></para>
</note>
In the following documentation <computeroutput>X</computeroutput> denotes the concrete class of the template <classname alt="boost::random::sobol_engine">sobol_engine</classname> returning objects of type <computeroutput>UIntType</computeroutput>, u and v are the values of <computeroutput>X</computeroutput>.</para><para>Some member functions may throw exceptions of type <computeroutput>std::range_error</computeroutput>. This happens when the quasi-random domain is exhausted and the generator cannot produce any more values. The length of the low discrepancy sequence is given by <inlineequation><alt>$L=Dimension \times (2^{w} - 1)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_31.png"/></imageobject><textobject role="tex"><phrase>$L=Dimension \times (2^{w} - 1)$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><typedef name="result_type"><type>UIntType</type></typedef>
<method-group name="public member functions">
<method name="dimension" cv="const"><type>std::size_t</type><description><para>Returns: The dimension of of the quasi-random domain.</para><para>Throws: nothing. </para></description></method>
<method name="seed"><type>void</type><purpose>Throws: nothing. </purpose><description><para>Effects: Resets the quasi-random number generator state to the one given by the default construction. Equivalent to u.seed(0). </para></description></method>
<method name="seed"><type>void</type><parameter name="init"><paramtype>UIntType</paramtype></parameter><purpose>Throws: range_error. </purpose><description><para>Effects: Effectively sets the quasi-random number generator state to the <computeroutput>init</computeroutput>-th vector in the <computeroutput>s</computeroutput>-dimensional quasi-random domain, where <computeroutput>s</computeroutput> == X::dimension(). <programlisting language="c++">X u, v;
for(int i = 0; i &lt; N; ++i)
    for( std::size_t j = 0; j &lt; u.dimension(); ++j )
        u();
v.seed(N);
assert(u() == v());
</programlisting> </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns: Returns a successive element of an <computeroutput>s</computeroutput>-dimensional (s = X::dimension()) vector at each invocation. When all elements are exhausted, X::operator() begins anew with the starting element of a subsequent <computeroutput>s</computeroutput>-dimensional vector.</para><para>Throws: range_error. </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Effects: Advances *this state as if <computeroutput>z</computeroutput> consecutive X::operator() invocations were executed. <programlisting language="c++">X u = v;
for(int i = 0; i &lt; N; ++i)
    u();
v.discard(N);
assert(u() == v());
</programlisting></para><para>Throws: range_error. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="s"><paramtype>std::size_t</paramtype></parameter><description><para>Effects: Constructs the default <computeroutput>s</computeroutput>-dimensional Sobol quasi-random number generator.</para><para>Throws: bad_alloc, invalid_argument, range_error. </para></description></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns: Tight lower bound on the set of values returned by operator().</para><para>Throws: nothing. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns: Tight upper bound on the set of values returned by operator().</para><para>Throws: nothing. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>sobol_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>sobol_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences of outputs. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>sobol_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>sobol_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences of outputs. </para></description></method>
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>sobol_engine</classname> &amp;</paramtype></parameter><description><para>Writes the textual representation of the generator to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>sobol_engine</classname> &amp;</paramtype></parameter><description><para>Reads the textual representation of the generator from a <computeroutput>std::istream</computeroutput>. </para></description></method>
</method-group>
</class><typedef name="sobol"><description><para><note><para>This specialization of <classname alt="boost::random::sobol_engine">sobol_engine</classname>  supports up to 3667 dimensions.</para>
</note>
Data on the primitive binary polynomials <computeroutput>a</computeroutput> and the corresponding starting values <computeroutput>m</computeroutput> for Sobol sequences in up to 21201 dimensions was taken from</para><para> <blockquote><para>  S. Joe and F. Y. Kuo, Constructing Sobol sequences with better two-dimensional projections, SIAM J. Sci. Comput. 30, 2635-2654 (2008).  </para></blockquote> </para><para>See the original tables up to dimension 21201: <ulink url="https://web.archive.org/web/20170802022909/http://web.maths.unsw.edu.au/~fkuo/sobol/new-joe-kuo-6.21201">https://web.archive.org/web/20170802022909/http://web.maths.unsw.edu.au/~fkuo/sobol/new-joe-kuo-6.21201</ulink></para><para>For practical reasons the default table uses only the subset of binary polynomials <computeroutput>a</computeroutput> &lt; 2<superscript>16</superscript>.</para><para>However, it is possible to provide your own table to <classname alt="boost::random::sobol_engine">sobol_engine</classname>  should the default one be insufficient. </para></description><type><classname>sobol_engine</classname>&lt; boost::uint_least64_t, 64u, default_sobol_table &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/student_t_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="student_t_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The Student t distribution is a real valued distribution with one parameter n, the number of degrees of freedom.</para><para>It has <inlineequation><alt>$\displaystyle p(x) = \frac{1}{\sqrt{n\pi}} \frac{\Gamma((n+1)/2)}{\Gamma(n/2)} \left(1+\frac{x^2}{n}\right)^{-(n+1)/2} $</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_37.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \frac{1}{\sqrt{n\pi}} \frac{\Gamma((n+1)/2)}{\Gamma(n/2)} \left(1+\frac{x^2}{n}\right)^{-(n+1)/2} $</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>student_t_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="n" cv="const"><type>RealType</type><description><para>Returns the number of degrees of freedom of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="n"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::student_t_distribution::param_type">param_type</classname></computeroutput> with "n" degrees of freedom.</para><para>Requires: n &gt; 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::student_t_distribution::param_type">param_type</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::student_t_distribution::param_type">param_type</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the different. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>RealType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the Student t distribution. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed accordint to the Student t distribution with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="n" cv="const"><type>RealType</type><description><para>Returns the number of degrees of freedom. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="n"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::student_t_distribution">student_t_distribution</classname></computeroutput> with "n" degrees of freedom.</para><para>Requires: n &gt; 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs an <computeroutput><classname alt="boost::random::student_t_distribution">student_t_distribution</classname></computeroutput> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="td"><paramtype>const <classname>student_t_distribution</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::student_t_distribution">student_t_distribution</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="td"><paramtype>const <classname>student_t_distribution</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::student_t_distribution">student_t_distribution</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>student_t_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>student_t_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::student_t_distribution">student_t_distribution</classname></computeroutput> will return identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>student_t_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>student_t_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::student_t_distribution">student_t_distribution</classname></computeroutput> will return different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/subtract_with_carry.hpp">
<namespace name="boost">
<namespace name="random">
<class name="subtract_with_carry_01_engine"><template>
      <template-type-parameter name="RealType"/>
      <template-nontype-parameter name="w"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="s"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="r"><type>std::size_t</type></template-nontype-parameter>
    </template><description><para>Instantiations of <classname alt="boost::random::subtract_with_carry_01_engine">subtract_with_carry_01_engine</classname> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . The algorithm is described in</para><para> <blockquote><para>  "A New Class of Random Number Generators", George Marsaglia and Arif Zaman, Annals of Applied Probability, Volume 1, Number 3 (1991), 462-480.  </para></blockquote>  </para></description><typedef name="result_type"><type>RealType</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="word_size" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="long_lag" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="short_lag" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="default_seed" specifiers="static"><type>const boost::uint32_t</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Seeds the generator with the default seed. </para></description></method>
<method name="seed"><type>void</type><parameter name="value"><paramtype>boost::uint32_t</paramtype></parameter><description><para>Seeds the generator with <computeroutput>value</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds the generator with values produced by <computeroutput>seq.generate()</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Seeds the generator with values from a range. Updates first to point one past the last consumed element. If there are not enough elements in the range to fill the entire state, throws <computeroutput>std::invalid_argument</computeroutput>. </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values. </para></description></method>
</method-group>
<constructor><description><para>Creates a new <classname alt="boost::random::subtract_with_carry_01_engine">subtract_with_carry_01_engine</classname> using the default seed. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="value"><paramtype>boost::uint32_t</paramtype></parameter><description><para>Creates a new <classname alt="boost::random::subtract_with_carry_01_engine">subtract_with_carry_01_engine</classname> and seeds it with value. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Creates a new <classname alt="boost::random::subtract_with_carry_01_engine">subtract_with_carry_01_engine</classname> and seeds with values produced by seq.generate(). </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Creates a new <classname alt="boost::random::subtract_with_carry_01_engine">subtract_with_carry_01_engine</classname> and seeds it with values from a range. Advances first to point one past the last consumed value. If the range does not contain enough elements to fill the entire state, throws <computeroutput>std::invalid_argument</computeroutput>. </para></description></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>const <classname>subtract_with_carry_01_engine</classname> &amp;</paramtype></parameter><description><para>Writes a <classname alt="boost::random::subtract_with_carry_01_engine">subtract_with_carry_01_engine</classname> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>const <classname>subtract_with_carry_01_engine</classname> &amp;</paramtype></parameter><description><para>Reads a <classname alt="boost::random::subtract_with_carry_01_engine">subtract_with_carry_01_engine</classname> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>subtract_with_carry_01_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>subtract_with_carry_01_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>subtract_with_carry_01_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>subtract_with_carry_01_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences. </para></description></method>
</method-group>
</class><class name="subtract_with_carry_engine"><template>
      <template-type-parameter name="IntType"/>
      <template-nontype-parameter name="w"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="s"><type>std::size_t</type></template-nontype-parameter>
      <template-nontype-parameter name="r"><type>std::size_t</type></template-nontype-parameter>
    </template><description><para>Instantiations of <computeroutput><classname alt="boost::random::subtract_with_carry_engine">subtract_with_carry_engine</classname></computeroutput> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . The algorithm is described in</para><para> <blockquote><para>  "A New Class of Random Number Generators", George Marsaglia and Arif Zaman, Annals of Applied Probability, Volume 1, Number 3 (1991), 462-480.  </para></blockquote>  </para></description><typedef name="result_type"><type>IntType</type></typedef>
<data-member name="word_size" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="long_lag" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="short_lag" specifiers="static"><type>const std::size_t</type></data-member>
<data-member name="default_seed" specifiers="static"><type>const uint32_t</type></data-member>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="modulus" specifiers="static"><type>const result_type</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Seeds the generator with the default seed. </para></description></method>
<method name="seed"><type>void</type><parameter name="value"><paramtype>IntType</paramtype></parameter></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Seeds the generator with values produced by <computeroutput>seq.generate()</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Seeds the generator with values from a range. Updates <computeroutput>first</computeroutput> to point one past the last consumed value. If the range does not contain enough elements to fill the entire state of the generator, throws <computeroutput>std::invalid_argument</computeroutput>. </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Fills a range with random values. </para></description></method>
</method-group>
<constructor><description><para>Constructs a new <computeroutput><classname alt="boost::random::subtract_with_carry_engine">subtract_with_carry_engine</classname></computeroutput> and seeds it with the default seed. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="value"><paramtype>IntType</paramtype></parameter><description><para>Constructs a new <computeroutput><classname alt="boost::random::subtract_with_carry_engine">subtract_with_carry_engine</classname></computeroutput> and seeds it with <computeroutput>value</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Constructs a new <computeroutput><classname alt="boost::random::subtract_with_carry_engine">subtract_with_carry_engine</classname></computeroutput> and seeds it with values produced by <computeroutput>seq.generate()</computeroutput>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Constructs a new <computeroutput><classname alt="boost::random::subtract_with_carry_engine">subtract_with_carry_engine</classname></computeroutput> and seeds it with values from a range. first is updated to point one past the last value consumed. If there are not enough elements in the range to fill the entire state of the generator, throws <computeroutput>std::invalid_argument</computeroutput>. </para></description></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>const <classname>subtract_with_carry_engine</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::subtract_with_carry_engine">subtract_with_carry_engine</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="f"><paramtype>const <classname>subtract_with_carry_engine</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::subtract_with_carry_engine">subtract_with_carry_engine</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>subtract_with_carry_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>subtract_with_carry_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences of values. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>subtract_with_carry_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>subtract_with_carry_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences of values. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/taus88.hpp">
<namespace name="boost">
<namespace name="random">
<typedef name="taus88"><description><para>The specialization taus88 was suggested in</para><para> <blockquote><para>  "Maximally Equidistributed Combined Tausworthe Generators", Pierre L'Ecuyer, Mathematics of Computation, Volume 65, Number 213, January 1996, Pages 203-213  </para></blockquote>  </para></description><type><classname>xor_combine_engine</classname>&lt; <classname>xor_combine_engine</classname>&lt; <classname>linear_feedback_shift_engine</classname>&lt; uint32_t, 32, 31, 13, 12 &gt;, 0, <classname>linear_feedback_shift_engine</classname>&lt; uint32_t, 32, 29, 2, 4 &gt;, 0 &gt;, 0, <classname>linear_feedback_shift_engine</classname>&lt; uint32_t, 32, 28, 3, 17 &gt;, 0 &gt;</type></typedef>

</namespace>
</namespace>
</header>
<header name="boost/random/traits.hpp">
<namespace name="boost">
<namespace name="random">
<namespace name="traits">
<struct name="is_integral"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">integral_constant&lt; bool, boost::is_integral&lt; T &gt;::value||(std::numeric_limits&lt; T &gt;::is_integer)&gt;</inherit><purpose>Traits class that indicates whether type T is an integer. </purpose></struct><struct name="is_signed"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">integral_constant&lt; bool, boost::is_signed&lt; T &gt;::value||(std::numeric_limits&lt; T &gt;::is_specialized &amp;&amp;std::numeric_limits&lt; T &gt;::is_integer &amp;&amp;std::numeric_limits&lt; T &gt;::is_signed)&gt;</inherit><purpose>Traits class that indicates whether type T is a signed integer. </purpose></struct><struct name="make_unsigned"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Converts the argument type T to an unsigned type. </purpose><description><para>This trait has a single member <computeroutput>type</computeroutput> which is the unsigned type corresponding to T. Note that if T is signed, then member <computeroutput>type</computeroutput> <emphasis>should define a type with one more bit precision than T</emphasis>. For built-in types this trait defaults to <computeroutput>boost::make_unsigned&lt;T&gt;::type</computeroutput>. For user defined types it simply asserts that the argument type T is an unsigned integer (using std::numeric_limits). User defined specializations may be provided for other cases. </para></description></struct><struct name="make_unsigned_or_unbounded"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Converts the argument type T to either an unsigned type or an unbounded integer type. </purpose><description><para>This trait has a single member <computeroutput>type</computeroutput> which is either the unsigned type corresponding to T or an unbounded integer type. This trait is used to generate types suitable for the calculation of a range: as a result if T is signed, then member <computeroutput>type</computeroutput> <emphasis>should define a type with one more bit precision than T</emphasis>. For built-in types this trait defaults to <computeroutput>boost::make_unsigned&lt;T&gt;::type</computeroutput>. For user defined types it simply asserts that the argument type T is either an unbounded integer, or an unsigned one (using std::numeric_limits). User defined specializations may be provided for other cases. </para></description></struct></namespace>

</namespace>
</namespace>
</header>
<header name="boost/random/triangle_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="triangle_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>Instantiations of <computeroutput><classname alt="boost::random::triangle_distribution">triangle_distribution</classname></computeroutput> model a <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . A <computeroutput><classname alt="boost::random::triangle_distribution">triangle_distribution</classname></computeroutput> has three parameters, <computeroutput>a</computeroutput>, <computeroutput>b</computeroutput>, and <computeroutput>c</computeroutput>, which are the smallest, the most probable and the largest values of the distribution respectively. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>triangle_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>RealType</type><description><para>Returns the minimum value of the distribution. </para></description></method>
<method name="b" cv="const"><type>RealType</type><description><para>Returns the mode of the distribution. </para></description></method>
<method name="c" cv="const"><type>RealType</type><description><para>Returns the maximum value of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="b"><paramtype>RealType</paramtype><default>0.5</default></parameter><parameter name="c"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs the parameters of a <computeroutput><classname alt="boost::random::triangle_distribution">triangle_distribution</classname></computeroutput>. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>RealType</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>result_type</type><description><para>Returns the <computeroutput>a</computeroutput> parameter of the distribution </para></description></method>
<method name="b" cv="const"><type>result_type</type><description><para>Returns the <computeroutput>b</computeroutput> parameter of the distribution </para></description></method>
<method name="c" cv="const"><type>result_type</type><description><para>Returns the <computeroutput>c</computeroutput> parameter of the distribution </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the triangle distribution. </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the triangle distribution with parameters specified by param. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="b"><paramtype>RealType</paramtype><default>0.5</default></parameter><parameter name="c"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::triangle_distribution">triangle_distribution</classname></computeroutput> with the parameters <computeroutput>a</computeroutput>, <computeroutput>b</computeroutput>, and <computeroutput>c</computeroutput>.</para><para>Preconditions: a &lt;= b &lt;= c. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::triangle_distribution">triangle_distribution</classname></computeroutput> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="td"><paramtype>const <classname>triangle_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="td"><paramtype>const <classname>triangle_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>triangle_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>triangle_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>triangle_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>triangle_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may produce different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/uniform_01.hpp">
<namespace name="boost">
<namespace name="random">
<class name="uniform_01"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The distribution function <classname alt="boost::random::uniform_01">uniform_01</classname> models a <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . On each invocation, it returns a random floating-point value uniformly distributed in the range [0..1).</para><para>The template parameter RealType shall denote a float-like value type with support for binary operators +, -, and /.</para><para>Note: The current implementation is buggy, because it may not fill all of the mantissa with random bits. I'm unsure how to fill a (to-be-invented) <computeroutput>boost::bigfloat</computeroutput> class with random bits efficiently. It's probably time for a traits class. </para></description><typedef name="input_type"><type>RealType</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="min" cv="const"><type>result_type</type></method>
<method name="max" cv="const"><type>result_type</type></method>
<method name="reset"><type>void</type></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/uniform_int_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="uniform_int_distribution"><template>
      <template-type-parameter name="IntType"><default>int</default></template-type-parameter>
    </template><description><para>The class template <classname alt="boost::random::uniform_int_distribution">uniform_int_distribution</classname> models a <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . On each invocation, it returns a random integer value uniformly distributed in the set of integers {min, min+1, min+2, ..., max}.</para><para>The template parameter IntType shall denote an integer-like value type. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>uniform_int_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>IntType</type><description><para>Returns the minimum value of the distribution. </para></description></method>
<method name="b" cv="const"><type>IntType</type><description><para>Returns the maximum value of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="min"><paramtype>IntType</paramtype><default>0</default></parameter><parameter name="max"><paramtype>IntType</paramtype><default>(std::numeric_limits&lt; IntType &gt;::max)()</default></parameter><description><para>Constructs the parameters of a <classname alt="boost::random::uniform_int_distribution">uniform_int_distribution</classname>.</para><para>Requires min &lt;= max </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>IntType</type></typedef>
<typedef name="result_type"><type>IntType</type></typedef>
<method-group name="public member functions">
<method name="min" cv="const"><type>IntType</type><description><para>Returns the minimum value of the distribution </para></description></method>
<method name="max" cv="const"><type>IntType</type><description><para>Returns the maximum value of the distribution </para></description></method>
<method name="a" cv="const"><type>IntType</type><description><para>Returns the minimum value of the distribution </para></description></method>
<method name="b" cv="const"><type>IntType</type><description><para>Returns the maximum value of the distribution </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns an integer uniformly distributed in the range [min, max]. </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns an integer uniformly distributed in the range [param.a(), param.b()]. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="min"><paramtype>IntType</paramtype><default>0</default></parameter><parameter name="max"><paramtype>IntType</paramtype><default>(std::numeric_limits&lt; IntType &gt;::max)()</default></parameter><description><para>Constructs a <classname alt="boost::random::uniform_int_distribution">uniform_int_distribution</classname>. <computeroutput>min</computeroutput> and <computeroutput>max</computeroutput> are the parameters of the distribution.</para><para>Requires: min &lt;= max </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::uniform_int_distribution">uniform_int_distribution</classname> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ud"><paramtype>const <classname>uniform_int_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ud"><paramtype>const <classname>uniform_int_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>uniform_int_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>uniform_int_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>uniform_int_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>uniform_int_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may produce different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/uniform_on_sphere.hpp">
<namespace name="boost">
<namespace name="random">
<class name="uniform_on_sphere"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
      <template-type-parameter name="Cont"><default>std::vector&lt;RealType&gt;</default></template-type-parameter>
    </template><description><para>Instantiations of class template <classname alt="boost::random::uniform_on_sphere">uniform_on_sphere</classname> model a  <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . Such a distribution produces random numbers uniformly distributed on the unit sphere of arbitrary dimension <computeroutput>dim</computeroutput>. The <computeroutput>Cont</computeroutput> template parameter must be a STL-like container type with begin and end operations returning non-const ForwardIterators of type <computeroutput>Cont::iterator</computeroutput>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>uniform_on_sphere</classname></type></typedef>
<method-group name="public member functions">
<method name="dim" cv="const"><type>int</type><description><para>Returns the dimension of the sphere. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="dim"><paramtype>int</paramtype><default>2</default></parameter><description><para>Constructs the parameters of a <classname alt="boost::random::uniform_on_sphere">uniform_on_sphere</classname> distribution, given the dimension of the sphere. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>RealType</type></typedef>
<typedef name="result_type"><type>Cont</type></typedef>
<method-group name="public member functions">
<method name="dim" cv="const"><type>int</type><description><para>Returns the dimension of the sphere. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="min" cv="const"><type>result_type</type><description><para>Returns the smallest value that the distribution can produce. Note that this is required to approximate the standard library's requirements. The behavior is defined according to lexicographical comparison so that for a container type of std::vector, dist.min() &lt;= x &lt;= dist.max() where x is any value produced by the distribution. </para></description></method>
<method name="max" cv="const"><type>result_type</type><description><para>Returns the largest value that the distribution can produce. Note that this is required to approximate the standard library's requirements. The behavior is defined according to lexicographical comparison so that for a container type of std::vector, dist.min() &lt;= x &lt;= dist.max() where x is any value produced by the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()"><type>const result_type &amp;</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns a point uniformly distributed over the surface of a sphere of dimension dim(). </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a point uniformly distributed over the surface of a sphere of dimension param.dim(). </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="dim"><paramtype>int</paramtype><default>2</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::uniform_on_sphere">uniform_on_sphere</classname></computeroutput> distribution. <computeroutput>dim</computeroutput> is the dimension of the sphere.</para><para>Requires: dim &gt;= 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::uniform_on_sphere">uniform_on_sphere</classname></computeroutput> distribution from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="sd"><paramtype>const <classname>uniform_on_sphere</classname> &amp;</paramtype></parameter><description><para>Writes the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="sd"><paramtype>const <classname>uniform_on_sphere</classname> &amp;</paramtype></parameter><description><para>Reads the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>uniform_on_sphere</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>uniform_on_sphere</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce identical sequences of values, given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>uniform_on_sphere</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>uniform_on_sphere</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may produce different sequences of values, given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/uniform_real_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="uniform_real_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The class template <classname alt="boost::random::uniform_real_distribution">uniform_real_distribution</classname> models a <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . On each invocation, it returns a random floating-point value uniformly distributed in the range [min..max). </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>uniform_real_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>RealType</type><description><para>Returns the minimum value of the distribution. </para></description></method>
<method name="b" cv="const"><type>RealType</type><description><para>Returns the maximum value of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="min"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="max"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs the parameters of a <classname alt="boost::random::uniform_real_distribution">uniform_real_distribution</classname>.</para><para>Requires min &lt;= max </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>RealType</type></typedef>
<typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="min" cv="const"><type>RealType</type><description><para>Returns the minimum value of the distribution </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the maximum value of the distribution </para></description></method>
<method name="a" cv="const"><type>RealType</type><description><para>Returns the minimum value of the distribution </para></description></method>
<method name="b" cv="const"><type>RealType</type><description><para>Returns the maximum value of the distribution </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns a value uniformly distributed in the range [min, max). </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a value uniformly distributed in the range [param.a(), param.b()). </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="min"><paramtype>RealType</paramtype><default>0.0</default></parameter><parameter name="max"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <classname alt="boost::random::uniform_real_distribution">uniform_real_distribution</classname>. <computeroutput>min</computeroutput> and <computeroutput>max</computeroutput> are the parameters of the distribution.</para><para>Requires: min &lt;= max </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <classname alt="boost::random::uniform_real_distribution">uniform_real_distribution</classname> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ud"><paramtype>const <classname>uniform_real_distribution</classname> &amp;</paramtype></parameter><description><para>Writes the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ud"><paramtype>const <classname>uniform_real_distribution</classname> &amp;</paramtype></parameter><description><para>Reads the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>uniform_real_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>uniform_real_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>uniform_real_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>uniform_real_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may produce different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/uniform_smallint.hpp">
<namespace name="boost">
<namespace name="random">
<class name="uniform_smallint"><template>
      <template-type-parameter name="IntType"><default>int</default></template-type-parameter>
    </template><description><para>The distribution function <classname alt="boost::random::uniform_smallint">uniform_smallint</classname> models a <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . On each invocation, it returns a random integer value uniformly distributed in the set of integer numbers {min, min+1, min+2, ..., max}. It assumes that the desired range (max-min+1) is small compared to the range of the underlying source of random numbers and thus makes no attempt to limit quantization errors.</para><para>Let <inlineequation><alt>$r_{\mathtt{out}} = (\mbox{max}-\mbox{min}+1)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_38.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{out}} = (\mbox{max}-\mbox{min}+1)$</phrase></textobject></inlinemediaobject></inlineequation> the desired range of integer numbers, and let <inlineequation><alt>$r_{\mathtt{base}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_39.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{base}}$</phrase></textobject></inlinemediaobject></inlineequation> be the range of the underlying source of random numbers. Then, for the uniform distribution, the theoretical probability for any number i in the range <inlineequation><alt>$r_{\mathtt{out}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_40.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{out}}$</phrase></textobject></inlinemediaobject></inlineequation> will be <inlineequation><alt>$\displaystyle p_{\mathtt{out}}(i) = \frac{1}{r_{\mathtt{out}}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_41.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p_{\mathtt{out}}(i) = \frac{1}{r_{\mathtt{out}}}$</phrase></textobject></inlinemediaobject></inlineequation>. Likewise, assume a uniform distribution on <inlineequation><alt>$r_{\mathtt{base}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_39.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{base}}$</phrase></textobject></inlinemediaobject></inlineequation> for the underlying source of random numbers, i.e. <inlineequation><alt>$\displaystyle p_{\mathtt{base}}(i) = \frac{1}{r_{\mathtt{base}}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_42.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p_{\mathtt{base}}(i) = \frac{1}{r_{\mathtt{base}}}$</phrase></textobject></inlinemediaobject></inlineequation>. Let <inlineequation><alt>$p_{\mathtt{out\_s}}(i)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_43.png"/></imageobject><textobject role="tex"><phrase>$p_{\mathtt{out\_s}}(i)$</phrase></textobject></inlinemediaobject></inlineequation> denote the random distribution generated by <computeroutput><classname alt="boost::random::uniform_smallint">uniform_smallint</classname></computeroutput>. Then the sum over all i in <inlineequation><alt>$r_{\mathtt{out}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_40.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{out}}$</phrase></textobject></inlinemediaobject></inlineequation> of <inlineequation><alt>$\displaystyle \left(\frac{p_{\mathtt{out\_s}}(i)}{p_{\mathtt{out}}(i)} - 1\right)^2$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_44.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle \left(\frac{p_{\mathtt{out\_s}}(i)}{p_{\mathtt{out}}(i)} - 1\right)^2$</phrase></textobject></inlinemediaobject></inlineequation> shall not exceed <inlineequation><alt>$\displaystyle \frac{r_{\mathtt{out}}}{r_{\mathtt{base}}^2} (r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}}) (r_{\mathtt{out}} - r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}})$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_45.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle \frac{r_{\mathtt{out}}}{r_{\mathtt{base}}^2} (r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}}) (r_{\mathtt{out}} - r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}})$</phrase></textobject></inlinemediaobject></inlineequation>.</para><para>The template parameter IntType shall denote an integer-like value type.</para><para> <note><para>  The property above is the square sum of the relative differences in probabilities between the desired uniform distribution <inlineequation><alt>$p_{\mathtt{out}}(i)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_46.png"/></imageobject><textobject role="tex"><phrase>$p_{\mathtt{out}}(i)$</phrase></textobject></inlinemediaobject></inlineequation> and the generated distribution <inlineequation><alt>$p_{\mathtt{out\_s}}(i)$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_43.png"/></imageobject><textobject role="tex"><phrase>$p_{\mathtt{out\_s}}(i)$</phrase></textobject></inlinemediaobject></inlineequation>. The property can be fulfilled with the calculation <inlineequation><alt>$(\mbox{base\_rng} \mbox{ mod } r_{\mathtt{out}})$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_47.png"/></imageobject><textobject role="tex"><phrase>$(\mbox{base\_rng} \mbox{ mod } r_{\mathtt{out}})$</phrase></textobject></inlinemediaobject></inlineequation>, as follows: Let <inlineequation><alt>$r = r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_48.png"/></imageobject><textobject role="tex"><phrase>$r = r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}}$</phrase></textobject></inlinemediaobject></inlineequation>. The base distribution on <inlineequation><alt>$r_{\mathtt{base}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_39.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{base}}$</phrase></textobject></inlinemediaobject></inlineequation> is folded onto the range <inlineequation><alt>$r_{\mathtt{out}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_40.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{out}}$</phrase></textobject></inlinemediaobject></inlineequation>. The numbers i &lt; r have assigned <inlineequation><alt>$\displaystyle \left\lfloor\frac{r_{\mathtt{base}}}{r_{\mathtt{out}}}\right\rfloor+1$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_49.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle \left\lfloor\frac{r_{\mathtt{base}}}{r_{\mathtt{out}}}\right\rfloor+1$</phrase></textobject></inlinemediaobject></inlineequation> numbers of the base distribution, the rest has only <inlineequation><alt>$\displaystyle \left\lfloor\frac{r_{\mathtt{base}}}{r_{\mathtt{out}}}\right\rfloor$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_50.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle \left\lfloor\frac{r_{\mathtt{base}}}{r_{\mathtt{out}}}\right\rfloor$</phrase></textobject></inlinemediaobject></inlineequation>. Therefore, <inlineequation><alt>$\displaystyle p_{\mathtt{out\_s}}(i) = \left(\left\lfloor\frac{r_{\mathtt{base}}} {r_{\mathtt{out}}}\right\rfloor+1\right) / r_{\mathtt{base}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_51.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p_{\mathtt{out\_s}}(i) = \left(\left\lfloor\frac{r_{\mathtt{base}}} {r_{\mathtt{out}}}\right\rfloor+1\right) / r_{\mathtt{base}}$</phrase></textobject></inlinemediaobject></inlineequation> for i &lt; r and <inlineequation><alt>$\displaystyle p_{\mathtt{out\_s}}(i) = \left\lfloor\frac{r_{\mathtt{base}}} {r_{\mathtt{out}}}\right\rfloor/r_{\mathtt{base}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_52.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p_{\mathtt{out\_s}}(i) = \left\lfloor\frac{r_{\mathtt{base}}} {r_{\mathtt{out}}}\right\rfloor/r_{\mathtt{base}}$</phrase></textobject></inlinemediaobject></inlineequation> otherwise. Substituting this in the above sum formula leads to the desired result.  </para></note> </para><para>Note: The upper bound for <inlineequation><alt>$(r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}}) (r_{\mathtt{out}} - r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}})$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_53.png"/></imageobject><textobject role="tex"><phrase>$(r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}}) (r_{\mathtt{out}} - r_{\mathtt{base}} \mbox{ mod } r_{\mathtt{out}})$</phrase></textobject></inlinemediaobject></inlineequation> is <inlineequation><alt>$\displaystyle \frac{r_{\mathtt{out}}^2}{4}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_54.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle \frac{r_{\mathtt{out}}^2}{4}$</phrase></textobject></inlinemediaobject></inlineequation>. Regarding the upper bound for the square sum of the relative quantization error of <inlineequation><alt>$\displaystyle \frac{r_\mathtt{out}^3}{4r_{\mathtt{base}}^2}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_55.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle \frac{r_\mathtt{out}^3}{4r_{\mathtt{base}}^2}$</phrase></textobject></inlinemediaobject></inlineequation>, it seems wise to either choose <inlineequation><alt>$r_{\mathtt{base}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_39.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{base}}$</phrase></textobject></inlinemediaobject></inlineequation> so that <inlineequation><alt>$r_{\mathtt{base}} &gt; 10r_{\mathtt{out}}^2$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_56.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{base}} &gt; 10r_{\mathtt{out}}^2$</phrase></textobject></inlinemediaobject></inlineequation> or ensure that <inlineequation><alt>$r_{\mathtt{base}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_39.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{base}}$</phrase></textobject></inlinemediaobject></inlineequation> is divisible by <inlineequation><alt>$r_{\mathtt{out}}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_40.png"/></imageobject><textobject role="tex"><phrase>$r_{\mathtt{out}}$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>uniform_smallint</classname></type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>IntType</type><description><para>Returns the minimum value. </para></description></method>
<method name="b" cv="const"><type>IntType</type><description><para>Returns the maximum value. </para></description></method>
</method-group>
<constructor><parameter name="min"><paramtype>IntType</paramtype><default>0</default></parameter><parameter name="max"><paramtype>IntType</paramtype><default>9</default></parameter><description><para>constructs the parameters of a <computeroutput><classname alt="boost::random::uniform_smallint">uniform_smallint</classname></computeroutput> distribution. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes the parameters to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads the parameters from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are equal. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are different. </para></description></method>
</method-group>
</class><typedef name="input_type"><type>IntType</type></typedef>
<typedef name="result_type"><type>IntType</type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>result_type</type><description><para>Returns the minimum value of the distribution. </para></description></method>
<method name="b" cv="const"><type>result_type</type><description><para>Returns the maximum value of the distribution. </para></description></method>
<method name="min" cv="const"><type>result_type</type><description><para>Returns the minimum value of the distribution. </para></description></method>
<method name="max" cv="const"><type>result_type</type><description><para>Returns the maximum value of the distribution. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><description><para>Returns a value uniformly distributed in the range [min(), max()]. </para></description></method>
<method name="operator()" cv="const"><type>result_type</type><template>
          <template-type-parameter name="Engine"/>
        </template><parameter name="eng"><paramtype>Engine &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a value uniformly distributed in the range [param.a(), param.b()]. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="min"><paramtype>IntType</paramtype><default>0</default></parameter><parameter name="max"><paramtype>IntType</paramtype><default>9</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::uniform_smallint">uniform_smallint</classname></computeroutput>. <computeroutput>min</computeroutput> and <computeroutput>max</computeroutput> are the lower and upper bounds of the output range, respectively. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::uniform_smallint">uniform_smallint</classname></computeroutput> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ud"><paramtype>const <classname>uniform_smallint</classname> &amp;</paramtype></parameter><description><para>Writes the distribution to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="ud"><paramtype>const <classname>uniform_smallint</classname> &amp;</paramtype></parameter><description><para>Reads the distribution from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>uniform_smallint</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>uniform_smallint</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions will produce identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>uniform_smallint</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>uniform_smallint</classname> &amp;</paramtype></parameter><description><para>Returns true if the two distributions may produce different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/variate_generator.hpp">
<namespace name="boost">
<class name="variate_generator"><template>
      <template-type-parameter name="Engine"/>
      <template-type-parameter name="Distribution"/>
    </template><description><para>A random variate generator is used to join a random number generator together with a random number distribution. Boost.Random provides a vast choice of <link linkend="boost_random.reference.generators">generators</link>  as well as <link linkend="boost_random.reference.distributions">distributions</link> .</para><para>The argument for the template parameter Engine shall be of the form U, U&amp;, or U*, where U models a  <link linkend="boost_random.reference.concepts.uniform_random_number_generator">uniform random number generator</link> . Then, the member engine_value_type names U (not the pointer or reference to U).</para><para>Specializations of <computeroutput><classname alt="boost::variate_generator">variate_generator</classname></computeroutput> satisfy the requirements of CopyConstructible. They also satisfy the requirements of Assignable unless the template parameter Engine is of the form U&amp;.</para><para>The complexity of all functions specified in this section is constant. No function described in this section except the constructor throws an exception. </para></description><typedef name="engine_value_type"><type>helper_type::value_type</type></typedef>
<typedef name="engine_type"><type>Engine</type></typedef>
<typedef name="distribution_type"><type>Distribution</type></typedef>
<typedef name="result_type"><type>Distribution::result_type</type></typedef>
<method-group name="public member functions">
<method name="operator()"><type>result_type</type><description><para>Returns: distribution()(engine()) </para></description></method>
<method name="operator()"><type>result_type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><description><para>Returns: distribution()(engine(), value). </para></description></method>
<method name="engine"><type>engine_value_type &amp;</type><description><para>Returns: A reference to the associated uniform random number generator. </para></description></method>
<method name="engine" cv="const"><type>const engine_value_type &amp;</type><description><para>Returns: A reference to the associated uniform random number generator. </para></description></method>
<method name="distribution"><type>distribution_type &amp;</type><description><para>Returns: A reference to the associated <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link> . </para></description></method>
<method name="distribution" cv="const"><type>const distribution_type &amp;</type><description><para>Returns: A reference to the associated random distribution. </para></description></method>
<method name="min" cv="const"><type>result_type</type><description><para>Precondition: distribution().min() is well-formed</para><para>Returns: distribution().min() </para></description></method>
<method name="max" cv="const"><type>result_type</type><description><para>Precondition: distribution().max() is well-formed</para><para>Returns: distribution().max() </para></description></method>
</method-group>
<constructor><parameter name="e"><paramtype>Engine</paramtype></parameter><parameter name="d"><paramtype>Distribution</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::variate_generator">variate_generator</classname></computeroutput> object with the associated  <link linkend="boost_random.reference.concepts.uniform_random_number_generator">uniform random number generator</link>  eng and the associated  <link linkend="boost_random.reference.concepts.random_distribution">random distribution</link>  d.</para><para>Throws: If and what the copy constructor of Engine or Distribution throws. </para></description></constructor>
</class></namespace>
</header>
<header name="boost/random/weibull_distribution.hpp">
<namespace name="boost">
<namespace name="random">
<class name="weibull_distribution"><template>
      <template-type-parameter name="RealType"><default>double</default></template-type-parameter>
    </template><description><para>The Weibull distribution is a real valued distribution with two parameters a and b, producing values &gt;= 0.</para><para>It has <inlineequation><alt>$\displaystyle p(x) = \frac{a}{b}\left(\frac{x}{b}\right)^{a-1}e^{-\left(\frac{x}{b}\right)^a}$</alt><inlinemediaobject><imageobject role="html"><imagedata format="PNG" fileref="images/random//form_57.png"/></imageobject><textobject role="tex"><phrase>$\displaystyle p(x) = \frac{a}{b}\left(\frac{x}{b}\right)^{a-1}e^{-\left(\frac{x}{b}\right)^a}$</phrase></textobject></inlinemediaobject></inlineequation>. </para></description><class name="param_type"><typedef name="distribution_type"><type><classname>weibull_distribution</classname></type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>RealType</type><description><para>Returns the "a" parameter of the distribtuion. </para></description></method>
<method name="b" cv="const"><type>RealType</type><description><para>Returns the "b" parameter of the distribution. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a"><paramtype>RealType</paramtype><default>1.0</default></parameter><parameter name="b"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::weibull_distribution::param_type">param_type</classname></computeroutput> from the "a" and "b" parameters of the distribution.</para><para>Requires: a &gt; 0 &amp;&amp; b &gt; 0 </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::weibull_distribution::param_type">param_type</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::weibull_distribution::param_type">param_type</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the same. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns true if the two sets of parameters are the different. </para></description></method>
</method-group>
</class><typedef name="result_type"><type>RealType</type></typedef>
<typedef name="input_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><description><para>Returns a random variate distributed according to the <computeroutput><classname alt="boost::random::weibull_distribution">weibull_distribution</classname></computeroutput>. </para></description></method>
<method name="operator()" cv="const"><type>RealType</type><template>
          <template-type-parameter name="URNG"/>
        </template><parameter name="urng"><paramtype>URNG &amp;</paramtype></parameter><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Returns a random variate distributed accordint to the Weibull distribution with parameters specified by <computeroutput>param</computeroutput>. </para></description></method>
<method name="a" cv="const"><type>RealType</type><description><para>Returns the "a" parameter of the distribution. </para></description></method>
<method name="b" cv="const"><type>RealType</type><description><para>Returns the "b" parameter of the distribution. </para></description></method>
<method name="min" cv="const"><type>RealType</type><description><para>Returns the smallest value that the distribution can produce. </para></description></method>
<method name="max" cv="const"><type>RealType</type><description><para>Returns the largest value that the distribution can produce. </para></description></method>
<method name="param" cv="const"><type><classname>param_type</classname></type><description><para>Returns the parameters of the distribution. </para></description></method>
<method name="param"><type>void</type><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Sets the parameters of the distribution. </para></description></method>
<method name="reset"><type>void</type><description><para>Effects: Subsequent uses of the distribution do not depend on values produced by any engine prior to invoking reset. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a"><paramtype>RealType</paramtype><default>1.0</default></parameter><parameter name="b"><paramtype>RealType</paramtype><default>1.0</default></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::weibull_distribution">weibull_distribution</classname></computeroutput> from its "a" and "b" parameters.</para><para>Requires: a &gt; 0 &amp;&amp; b &gt; 0 </para></description></constructor>
<constructor specifiers="explicit"><parameter name="param"><paramtype>const <classname>param_type</classname> &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::weibull_distribution">weibull_distribution</classname></computeroutput> from its parameters. </para></description></constructor>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="wd"><paramtype>const <classname>weibull_distribution</classname> &amp;</paramtype></parameter><description><para>Writes a <computeroutput><classname alt="boost::random::weibull_distribution">weibull_distribution</classname></computeroutput> to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="wd"><paramtype>const <classname>weibull_distribution</classname> &amp;</paramtype></parameter><description><para>Reads a <computeroutput><classname alt="boost::random::weibull_distribution">weibull_distribution</classname></computeroutput> from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>weibull_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>weibull_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::weibull_distribution">weibull_distribution</classname></computeroutput> will return identical sequences of values given equal generators. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>weibull_distribution</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>weibull_distribution</classname> &amp;</paramtype></parameter><description><para>Returns true if the two instances of <computeroutput><classname alt="boost::random::weibull_distribution">weibull_distribution</classname></computeroutput> will return different sequences of values given equal generators. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
<header name="boost/random/xor_combine.hpp">
<namespace name="boost">
<namespace name="random">
<class name="xor_combine_engine"><template>
      <template-type-parameter name="URNG1"/>
      <template-nontype-parameter name="s1"><type>int</type></template-nontype-parameter>
      <template-type-parameter name="URNG2"/>
      <template-nontype-parameter name="s2"><type>int</type></template-nontype-parameter>
    </template><description><para>Instantiations of <computeroutput><classname alt="boost::random::xor_combine_engine">xor_combine_engine</classname></computeroutput> model a  <link linkend="boost_random.reference.concepts.pseudo_random_number_generator">pseudo-random number generator</link> . To produce its output it invokes each of the base generators, shifts their results and xors them together. </para></description><typedef name="base1_type"><type>URNG1</type></typedef>
<typedef name="base2_type"><type>URNG2</type></typedef>
<typedef name="result_type"><type>base1_type::result_type</type></typedef>
<data-member name="has_fixed_range" specifiers="static"><type>const bool</type></data-member>
<data-member name="shift1" specifiers="static"><type>const int</type></data-member>
<data-member name="shift2" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><description><para>Calls <computeroutput>seed()</computeroutput> for both base generators. </para></description></method>
<method name="seed"><type>void</type><parameter name="v"><paramtype>result_type</paramtype></parameter><description><para><computeroutput>seeds</computeroutput> both base generators with <computeroutput>v</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para><computeroutput>seeds</computeroutput> both base generators with values produced by <computeroutput>seq</computeroutput>. </para></description></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>seeds both base generators with values from the iterator range [first, last) and changes first to point to the element after the last one used. If there are not enough elements in the range to seed both generators, throws <computeroutput>std::invalid_argument</computeroutput>. </para></description></method>
<method name="base1" cv="const"><type>const base1_type &amp;</type><description><para>Returns the first base generator. </para></description></method>
<method name="base2" cv="const"><type>const base2_type &amp;</type><description><para>Returns the second base generator. </para></description></method>
<method name="operator()"><type>result_type</type><description><para>Returns the next value of the generator. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="Iter"/>
        </template><parameter name="first"><paramtype>Iter</paramtype></parameter><parameter name="last"><paramtype>Iter</paramtype></parameter><description><para>Fills a range with random values </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>boost::uintmax_t</paramtype></parameter><description><para>Advances the state of the generator by <computeroutput>z</computeroutput>. </para></description></method>
</method-group>
<constructor><description><para>Constructors a <computeroutput><classname alt="boost::random::xor_combine_engine">xor_combine_engine</classname></computeroutput> by default constructing both base generators. </para></description></constructor>
<constructor><parameter name="rng1"><paramtype>const base1_type &amp;</paramtype></parameter><parameter name="rng2"><paramtype>const base2_type &amp;</paramtype></parameter><description><para>Constructs a <computeroutput>xor_combine</computeroutput> by copying two base generators. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="v"><paramtype>result_type</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::xor_combine_engine">xor_combine_engine</classname></computeroutput>, seeding both base generators with <computeroutput>v</computeroutput>.</para><para> <warning><para>  The exact algorithm used by this function may change in the future.  </para></warning>  </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SeedSeq"/>
        </template><parameter name="seq"><paramtype>SeedSeq &amp;</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::xor_combine_engine">xor_combine_engine</classname></computeroutput>, seeding both base generators with values produced by <computeroutput>seq</computeroutput>. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
        </template><parameter name="first"><paramtype>It &amp;</paramtype></parameter><parameter name="last"><paramtype>It</paramtype></parameter><description><para>Constructs a <computeroutput><classname alt="boost::random::xor_combine_engine">xor_combine_engine</classname></computeroutput>, seeding both base generators with values from the iterator range [first, last) and changes first to point to the element after the last one used. If there are not enough elements in the range to seed both generators, throws <computeroutput>std::invalid_argument</computeroutput>. </para></description></constructor>
<method-group name="public static functions">
<method name="min" specifiers="static"><type>constexpr result_type</type><description><para>Returns the smallest value that the generator can produce. </para></description></method>
<method name="max" specifiers="static"><type>constexpr result_type</type><description><para>Returns the largest value that the generator can produce. </para></description></method>
</method-group>
<method-group name="friend functions">
<method name="operator&lt;&lt;"><type>friend std::basic_ostream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>xor_combine_engine</classname> &amp;</paramtype></parameter><description><para>Writes the textual representation of the generator to a <computeroutput>std::ostream</computeroutput>. </para></description></method>
<method name="operator&gt;&gt;"><type>friend std::basic_istream&lt; CharT, Traits &gt; &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="is"><paramtype>std::basic_istream&lt; CharT, Traits &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype>const <classname>xor_combine_engine</classname> &amp;</paramtype></parameter><description><para>Reads the textual representation of the generator from a <computeroutput>std::istream</computeroutput>. </para></description></method>
<method name="operator=="><type>friend bool</type><parameter name="x"><paramtype>const <classname>xor_combine_engine</classname> &amp;</paramtype></parameter><parameter name="y"><paramtype>const <classname>xor_combine_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce identical sequences. </para></description></method>
<method name="operator!="><type>friend bool</type><parameter name="lhs"><paramtype>const <classname>xor_combine_engine</classname> &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>xor_combine_engine</classname> &amp;</paramtype></parameter><description><para>Returns true if the two generators will produce different sequences. </para></description></method>
</method-group>
</class>
</namespace>
</namespace>
</header>
</library-reference>