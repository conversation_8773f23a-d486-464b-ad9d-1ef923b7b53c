
[/ Copyright (C) 2008-2018 <PERSON>]
[/ Distributed under the Boost Software License, Version 1.0 (see accompanying]
[/ file LICENSE_1_0.txt or a copy at http://www.boost.org/LICENSE_1_0.txt).]
[/ See: http://www.boost.org/doc/libs/release/libs/contract/doc/html/index.html]

[library Boost.Contract
    [quickbook 1.5]
    [version 1.0.0]
    [purpose Contract programming for C++.]
    [authors [<PERSON><PERSON><PERSON> <email>lorca<PERSON><EMAIL></email>, <PERSON>]]
    [copyright 2008-2019 <PERSON>]
    [license Distributed under the Boost Software License, Version 1.0 (see accompanying file LICENSE_1_0.txt or a copy at [@http://www.boost.org/LICENSE_1_0.txt])]
    [source-mode c++]
]

[def __Introduction__ [link boost_contract.introduction Introduction]]
[def __Full_Table_of_Contents__ [link boost_contract.full_table_of_contents Full Table of Contents]]

[def __Getting_Started__ [link boost_contract.getting_started Getting Started]]
[def __This_Documentation__ [link boost_contract.getting_started.this_documentation This Documentation]]
[def __Compilers_and_Platforms__ [link boost_contract.getting_started.compilers_and_platforms Compilers and Platforms]]
[def __Code_Organization__ [link boost_contract.getting_started.code_organization Code Organization]]
[def __Build__ [link boost_contract.getting_started.build Build]]

[def __Contract_Programming_Overview__ [link boost_contract.contract_programming_overview Contract Programming Overview]]
[def __Assertions__ [link boost_contract.contract_programming_overview.assertions Assertions]]
[def __Benefits_and_Costs__ [link boost_contract.contract_programming_overview.benefits_and_costs Benefits and Costs]]
[def __Function_Calls__ [link boost_contract.contract_programming_overview.function_calls Function Calls]]
[def __Public_Function_Calls__ [link boost_contract.contract_programming_overview.public_function_calls Public Function Calls]]
[def __Constructor_Calls__ [link boost_contract.contract_programming_overview.constructor_calls Constructor Calls]]
[def __Destructor_Calls__ [link boost_contract.contract_programming_overview.destructor_calls Destructor Calls]]
[def __Constant_Correctness__ [link boost_contract.contract_programming_overview.constant_correctness Constant-Correctness]]
[def __Specifications_vs_Implementation__ [link boost_contract.contract_programming_overview.specifications_vs__implementation Specifications vs. Implementation]]
[def __On_Contract_Failures__ [link boost_contract.contract_programming_overview.on_contract_failures On Contract Failures]]
[def __Feature_Summary__ [link boost_contract.contract_programming_overview.feature_summary Feature Summary]]

[def __Tutorial__ [link boost_contract.tutorial Tutorial]]
[def __Non_Member_Functions__ [link boost_contract.tutorial.non_member_functions Non-Member Functions]]
[def __Preconditions__ [link boost_contract.tutorial.preconditions Preconditions]]
[def __Postconditions__ [link boost_contract.tutorial.postconditions Postconditions]]
[def __Return_Values__ [link boost_contract.tutorial.return_values Return Values]]
[def __Old_Values__ [link boost_contract.tutorial.old_values Old Values]]
[def __Exception_Guarantees__ [link boost_contract.tutorial.exception_guarantees Exception Guarantees]]
[def __Class_Invariants__ [link boost_contract.tutorial.class_invariants Class Invariants]]
[def __Constructors__ [link boost_contract.tutorial.constructors Constructors]]
[def __Destructors__ [link boost_contract.tutorial.destructors Destructors]]
[def __Public_Functions__ [link boost_contract.tutorial.public_functions Public Functions]]
[def __Virtual_Public_Functions__ [link boost_contract.tutorial.virtual_public_functions Virtual Public Functions]]
[def __Public_Function_Overrides__ [link boost_contract.tutorial.public_function_overrides__subcontracting_ Public Function Overrides]]
[def __Public_Function_Overrides_Subcontracting__ [link boost_contract.tutorial.public_function_overrides__subcontracting_ Public Function Overrides (Subcontracting)]]
[def __Base_Classes__ [link boost_contract.tutorial.base_classes__subcontracting_ Base Classes]]
[def __Base_Classes_Subcontracting__ [link boost_contract.tutorial.base_classes__subcontracting_ Base Classes (Subcontracting)]]
[def __Static_Public_Functions__ [link boost_contract.tutorial.static_public_functions Static Public Functions]]

[def __Advanced__ [link boost_contract.advanced Advanced]]
[def __Pure_Virtual_Public_Functions__ [link boost_contract.advanced.pure_virtual_public_functions Pure Virtual Public Functions]]
[def __Optional_Return_Values__ [link boost_contract.advanced.optional_return_values Optional Return Values]]
[def __Private_and_Protected_Functions__ [link boost_contract.advanced.private_and_protected_functions Private and Protected Functions]]
[def __Friend_Functions__ [link boost_contract.advanced.friend_functions Friend Functions]]
[def __Function_Overloads__ [link boost_contract.advanced.function_overloads Function Overloads]]
[def __Lambdas_Loops_Code_Blocks__ [link boost_contract.advanced.lambdas__loops__code_blocks__and__constexpr__ Lambdas, Loops, Code Blocks]]
[def __Lambdas_Loops_Code_Blocks_and_constexpr__ [link boost_contract.advanced.lambdas__loops__code_blocks__and__constexpr__ Lambdas, Loops, Code Blocks (and `constexpr`)]]
[def __Implementation_Checks__ [link boost_contract.advanced.implementation_checks Implementation Checks]]
[def __Old_Values_Copied_at_Body__ [link boost_contract.advanced.old_values_copied_at_body Old Values Copied at Body]]
[def __Named_Overrides__ [link boost_contract.advanced.named_overrides Named Overrides]]
[def __Access_Specifiers__ [link boost_contract.advanced.access_specifiers Access Specifiers]]
[def __Throw_on_Failures__ [link boost_contract.advanced.throw_on_failures__and__noexcept__ Throw on Failures]]
[def __Throw_on_Failures_and_noexcept__ [link boost_contract.advanced.throw_on_failures__and__noexcept__ Throw on Failures (and `noexcept`)]]

[def __Extras__ [link boost_contract.extras Extras]]
[def __Old_Value_Requirements__ [link boost_contract.extras.old_value_requirements__templates_ Old Value Requirements]]
[def __Old_Value_Requirements_Templates__ [link boost_contract.extras.old_value_requirements__templates_ Old Value Requirements (Templates)]]
[def __Assertion_Requirements__ [link boost_contract.extras.assertion_requirements__templates_ Assertion Requirements]]
[def __Assertion_Requirements_Templates__ [link boost_contract.extras.assertion_requirements__templates_ Assertion Requirements (Templates)]]
[def __Volatile_Public_Functions__ [link boost_contract.extras.volatile_public_functions Volatile Public Functions]]
[def __Move_Operations__ [link boost_contract.extras.move_operations Move Operations]]
[def __Unions__ [link boost_contract.extras.unions Unions]]
[def __Disable_Contract_Checking__ [link boost_contract.extras.disable_contract_checking Disable Contract Checking]]
[def __Assertion_Levels__ [link boost_contract.extras.assertion_levels Assertion Levels]]
[def __Disable_Contract_Compilation__ [link boost_contract.extras.disable_contract_compilation__macro_interface_ Disable Contract Compilation]]
[def __Disable_Contract_Compilation_Macro_Interface__ [link boost_contract.extras.disable_contract_compilation__macro_interface_ Disable Contract Compilation (Macro Interface)]]
[def __Separate_Body_Implementation__ [link boost_contract.extras.separate_body_implementation Separate Body Implementation]]
[def __No_Lambda_Functions__ [link boost_contract.extras.no_lambda_functions__no_c__11_ No Lambda Functions]]
[def __No_Lambda_Functions_No_CXX11__ [link boost_contract.extras.no_lambda_functions__no_c__11_ No Lambda Functions (No C++11)]]
[def __No_Macros__ [link boost_contract.extras.no_macros__and_no_variadic_macros_ No Macros]]
[def __No_Macros_and_No_Variadic_Macros__ [link boost_contract.extras.no_macros__and_no_variadic_macros_ No Macros (and No Variadic Macros)]]

[def __Reference__ [@reference.html Reference]]
[def __Examples__ [link boost_contract.examples Examples]]
[def __Release_Notes__ [link boost_contract.release_notes Release Notes]]
[def __Bibliography__ [link boost_contract.bibliography Bibliography]]
[def __Acknowledgments__ [link boost_contract.acknowledgments Acknowledgments]]

[def __AND__ [link and_anchor [^['AND]]]]
[def __OR__ [link or_anchor [^['OR]]]]

[def __Bright04__ [link Bright04_anchor \[Bright04\]]]
[def __Bright04b__ [link Bright04b_anchor \[Bright04b\]]]
[def __C2__ [link C2_anchor \[C2\]]]
[def __Chrome__ [link Chrome_anchor \[Chrome\]]]
[def __Cline90__ [link Cline90_anchor \[Cline90\]]]
[def __CodeContracts__ [link CodeContracts_anchor \[CodeContracts\]]]
[def __iContract__ [link iContract_anchor \[iContract\]]]
[def __Nana__ [link Nana_anchor \[Nana\]]]
[def __Hoare73__ [link Hoare73_anchor \[Hoare73\]]]
[def __Jcontract__ [link Jcontract_anchor \[Jcontract\]]]
[def __Lindrud04__ [link Lindrud04_anchor \[Lindrud04\]]]
[def __Maley99__ [link Maley99_anchor \[Maley99\]]]
[def __Meyer97__ [link Meyer97_anchor \[Meyer97\]]]
[def __Mitchell02__ [link Mitchell02_anchor \[Mitchell02\]]]
[def __N1613__ [link N1613_anchor \[N1613\]]]
[def __N1866__ [link N1866_anchor \[N1866\]]]
[def __N1962__ [link N1962_anchor \[N1962\]]]
[def __N2081__ [link N2081_anchor \[N2081\]]]
[def __N2914__ [link N2914_anchor \[N2914\]]]
[def __N3613__ [link N3613_anchor \[N3613\]]]
[def __P0380__ [link P0380_anchor \[P0380\]]]
[def __SPARKAda__ [link SPARKAda_anchor \[SPARKAda\]]]
[def __SpecSharp__ [link SpecSharp_anchor \[SpecSharp\]]]
[def __Stroustrup94__ [link Stroustrup94_anchor \[Stroustrup94\]]]
[def __Stroustrup13__ [link Stroustrup13_anchor \[Stroustrup13\]]]
[def __Tandin04__ [link Tandin04_anchor \[Tandin04\]]]

[def __substitution_principle__ [@http://en.wikipedia.org/wiki/Liskov_substitution_principle substitution principle]]

[:['["Our field needs more formality, but the profession has not realized it yet.]]]
[:['-- Bertrand Meyer (see __Meyer97__ page 400)]]

This library implements
[@http://en.wikipedia.org/wiki/Design_by_contract contract programming] (a.k.a., Design by Contract or DbC)
[footnote
Design by Contract (DbC) is a registered trademark of the Eiffel Software company and it was first introduced by the Eiffel programming language (see __Meyer97__).
]
for the C++ programming language.
All contract programming features are supported by this library: Subcontracting, class invariants (also for static and volatile member functions), postconditions (with old and return values), preconditions, customizable actions on assertion failure (e.g., terminate the program or throw exceptions), optional compilation of assertions, disable assertions while already checking other assertions (to avoid infinite recursion), and more (see __Feature_Summary__).

[include introduction.qbk]
[include full_table_of_contents.qbk]
[include getting_started.qbk]
[include contract_programming_overview.qbk]
[include tutorial.qbk]
[include advanced.qbk]
[include extras.qbk]
[include examples.qbk]
[xinclude reference.xml]
[include release_notes.qbk]
[include bibliography.qbk]
[include acknowledgments.qbk]

