<?xml version="1.0" standalone="yes"?>
<library-reference id="reference"><title>Reference</title><header name="boost/contract.hpp">
<para>Include all header files required by this library at once (for convenience). </para><para>All header files <computeroutput>boost/contract/*.hpp</computeroutput> are independent from one another and can be included one-by-one to reduce the amount of code to compile from this library in user code (but this was measured to not make an appreciable difference in compile-time so <computeroutput>boost/contract.hpp</computeroutput> can be included directly in most cases). Instead the headers <computeroutput>boost/contract/core/*.hpp</computeroutput> are not independent from other library headers and they are automatically included by the <computeroutput>boost/contract/*.hpp</computeroutput> headers (so the <computeroutput>boost/contract/core/*.hpp</computeroutput> headers are usually not directly included by programmers).</para><para>All files under the <computeroutput>boost/contract/detail/</computeroutput> directory, names within the <computeroutput>boost::contract::detail</computeroutput> namespace, names prefixed by <computeroutput>boost_contract_detail</computeroutput>... and <computeroutput>BOOST_CONTRACT_DETAIL</computeroutput>... (in any namesapce, including user's code) are reserved for internal use of this library and should never be used directly by programmers.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.getting_started"> Getting Started</link> </para>
</para>
</para></header>
<header name="boost/contract/assert.hpp">
<para>Assert contract conditions. </para><macro name="BOOST_CONTRACT_ASSERT" kind="functionlike"><macro-parameter name="cond"><description><para>Boolean contract condition to check. (This is not a variadic macro parameter so any comma it might contain must be protected by round parenthesis and <computeroutput>BOOST_CONTRACT_ASSERT((cond))</computeroutput> will always work.) </para></description></macro-parameter><purpose>Preferred way to assert contract conditions. </purpose><description><para>Any exception thrown from within a contract (preconditions, postconditions, exception guarantees, old value copies at body, class invariants, etc.) is interpreted by this library as a contract failure. Therefore, users can program contract assertions manually throwing an exception when an asserted condition is checked to be <computeroutput>false</computeroutput> (this library will then call the appropriate contract failure handler  <computeroutput><functionname alt="boost::contract::precondition_failure">boost::contract::precondition_failure</functionname></computeroutput>, etc.). However, it is preferred to use this macro because it expands to code that throws <computeroutput><classname alt="boost::contract::assertion_failure">boost::contract::assertion_failure</classname></computeroutput> with the correct assertion file name (using <computeroutput>__FILE__</computeroutput>), line number (using <computeroutput>__LINE__</computeroutput>), and asserted condition code so to produce informative error messages (C++11 <computeroutput>__func__</computeroutput> is not used here because in most cases it will simply expand to the internal compiler name of the lambda function used to program the contract conditions adding no specificity to the error message).</para><para> <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT_AUDIT">BOOST_CONTRACT_ASSERT_AUDIT</macroname></computeroutput>, and <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT_AXIOM">BOOST_CONTRACT_ASSERT_AXIOM</macroname></computeroutput> are the three assertion levels predefined by this library.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.preconditions"> Preconditions</link>,  <link linkend="boost_contract.tutorial.postconditions"> Postconditions</link>,  <link linkend="boost_contract.tutorial.exception_guarantees"> Exceptions Guarantees</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link></para>
</para>

</para></description></macro>
<macro name="BOOST_CONTRACT_ASSERT_AUDIT" kind="functionlike"><macro-parameter name="cond"><description><para>Boolean contract condition to check. (This is not a variadic macro parameter so any comma it might contain must be protected by round parenthesis and <computeroutput>BOOST_CONTRACT_ASSERT_AUDIT((cond))</computeroutput> will always work.) </para></description></macro-parameter><purpose>Preferred way to assert contract conditions that are computationally expensive, at least compared to the computational cost of executing the function body. </purpose><description><para>The asserted condition will always be compiled and validated syntactically, but it will not be checked at run-time unless  <computeroutput><macroname alt="BOOST_CONTRACT_AUDITS">BOOST_CONTRACT_AUDITS</macroname></computeroutput> is defined (undefined by default). This macro is defined by code equivalent to:</para><para><programlisting language="c++">#ifdef BOOST_CONTRACT_AUDITS
    #define BOOST_CONTRACT_ASSERT_AUDIT(cond) \
        BOOST_CONTRACT_ASSERT(cond)
#else
    #define BOOST_CONTRACT_ASSERT_AUDIT(cond) \
        BOOST_CONTRACT_ASSERT(true || cond)
#endif
</programlisting></para><para> <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT_AUDIT">BOOST_CONTRACT_ASSERT_AUDIT</macroname></computeroutput>, and <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT_AXIOM">BOOST_CONTRACT_ASSERT_AXIOM</macroname></computeroutput> are the three assertion levels predefined by this library. If there is a need, programmers are free to implement their own assertion levels defining macros similar to the one above.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_levels"> Assertion Levels</link>,  <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link></para>
</para>

</para></description></macro>
<macro name="BOOST_CONTRACT_ASSERT_AXIOM" kind="functionlike"><macro-parameter name="cond"><description><para>Boolean contract condition to check. (This is not a variadic macro parameter so any comma it might contain must be protected by round parenthesis and <computeroutput>BOOST_CONTRACT_ASSERT_AXIOM((cond))</computeroutput> will always work.) </para></description></macro-parameter><purpose>Preferred way to document in the code contract conditions that are computationally prohibitive, at least compared to the computational cost of executing the function body. </purpose><description><para>The asserted condition will always be compiled and validated syntactically, but it will never be checked at run-time. This macro is defined by code equivalent to:</para><para><programlisting language="c++">#define BOOST_CONTRACT_ASSERT_AXIOM(cond) \
    BOOST_CONTRACT_ASSERT(true || cond)
</programlisting></para><para> <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT_AUDIT">BOOST_CONTRACT_ASSERT_AUDIT</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT_AXIOM">BOOST_CONTRACT_ASSERT_AXIOM</macroname></computeroutput> are the three assertion levels predefined by this library. If there is a need, programmers are free to implement their own assertion levels defining macros similar to the one above.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_levels"> Assertion Levels</link>,  <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link></para>
</para>

</para></description></macro>
</header>
<header name="boost/contract/base_types.hpp">
<para>Specify inheritance form base classes (for subcontracting). </para><macro name="BOOST_CONTRACT_BASE_TYPES" kind="functionlike"><macro-parameter name="..."><description><para>Comma separated list of base classes. Each base must explicitly specify its access specifier <computeroutput>public</computeroutput>, <computeroutput>protected</computeroutput>, or <computeroutput>private</computeroutput>, and also <computeroutput>virtual</computeroutput> when present (this not always required in C++ instead). There is a limit of about 20 maximum bases that can be listed (because of similar limits in Boost.MPL internally used by this library). This is a variadic macro parameter, on compilers that do not support variadic macros, the <computeroutput>typedef</computeroutput> for base classes can be programmed manually without using this macro (see  <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link>). </para></description></macro-parameter><purpose>Used to program the <computeroutput>typedef</computeroutput> that lists the bases of a derived class. </purpose><description><para>In order to support subcontracting, a derived class that specifies contracts for one or more overriding public functions must declare a <computeroutput>typedef</computeroutput> named <computeroutput>base_types</computeroutput> (or <computeroutput><macroname alt="BOOST_CONTRACT_BASES_TYPEDEF">BOOST_CONTRACT_BASES_TYPEDEF</macroname></computeroutput>) using this macro:</para><para><programlisting language="c++">class u
    #define BASES public b, protected virtual w1, private w2
    : BASES
{
    friend class boost::contract:access;

    typedef BOOST_CONTRACT_BASE_TYPES(BASES) base_types;
    #undef BASES

    ...
};
</programlisting></para><para>This <computeroutput>typedef</computeroutput> must be <computeroutput>public</computeroutput> unless <computeroutput><classname alt="boost::contract::access">boost::contract::access</classname></computeroutput> is used.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.base_classes__subcontracting_"> Base Classes</link></para>
</para>

</para></description></macro>
</header>
<header name="boost/contract/call_if.hpp">
<para>Statically disable compilation and execution of functor calls. </para><para><note><para>These facilities allow to emulate C++17 <computeroutput>if constexpr</computeroutput> statements when used together with functor templates (and C++14 generic lambdas). Therefore, they are not useful on C++17 compilers where <computeroutput> if constexpr</computeroutput> can be directly used instead. </para>
</note>
</para><namespace name="boost">
<namespace name="contract">
<struct name="call_if_statement"><template>
      <template-nontype-parameter name="Pred"><type>bool</type><purpose><para>Static boolean predicate that selects which functor template call to compile and execute. </para></purpose></template-nontype-parameter>
      <template-type-parameter name="Then"><purpose><para>Type of the functor template to call if the static predicate <computeroutput>Pred</computeroutput> is <computeroutput>true</computeroutput>. </para></purpose></template-type-parameter>
      <template-type-parameter name="ThenResult"><default>internal_type</default><purpose><para>Return type of then-branch functor template call (this is usually automatically deduced by this library so it is never explicitly specified by the user, and that is why it is often marked as <computeroutput>internal_type</computeroutput> in this documentation). </para></purpose></template-type-parameter>
    </template><purpose>Select compilation and execution of functor template calls using a static boolean predicate (not needed on C++17 compilers, use <computeroutput>if constexpr</computeroutput> instead). </purpose><description><para>This class template has no members because it is never used directly, it is only used via its specializations. Usually this class template is instantiated only via the return value of  <computeroutput><functionname alt="boost::contract::call_if">boost::contract::call_if</functionname></computeroutput> and <computeroutput><functionname alt="boost::contract::call_if_c">boost::contract::call_if_c</functionname></computeroutput>.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_requirements__templates_">
        Assertion Requirements</link></para>
</para>

</para></description></struct><struct-specialization name="call_if_statement"><template>
      <template-type-parameter name="Then"><purpose><para>Type of functor template to call when the static predicate is <computeroutput>true</computeroutput> (never the case for this template specialization). </para></purpose></template-type-parameter>
    </template><specialization><template-arg>false</template-arg><template-arg>Then</template-arg><template-arg>internal_type</template-arg></specialization><purpose>Template specialization to handle static predicates that are <computeroutput>false</computeroutput> (not needed on C++17 compilers, use <computeroutput>if constexpr</computeroutput> instead). </purpose><description><para>This template specialization handles all else-branch functor template calls (whether they return void or not). Usually this class template is instantiated only via the return value of  <computeroutput><functionname alt="boost::contract::call_if">boost::contract::call_if</functionname></computeroutput> and <computeroutput><functionname alt="boost::contract::call_if_c">boost::contract::call_if_c</functionname></computeroutput>.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_requirements__templates_">
        Assertion Requirements</link></para>
</para>

</para></description><method-group name="public member functions">
<method name="else_" cv="const"><type>result_of&lt; Else()&gt;::type</type><template>
          <template-type-parameter name="Else"/>
        </template><parameter name="f"><paramtype>Else</paramtype><description><para>Else-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is actually compiled and executed for this template specialization (because the if-statement static predicate is <computeroutput>false</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be the same as (or implicitly convertible to) the return type of all other functor template calls specified for this call-if object.</para></description></parameter><purpose>Specify the else-branch functor template. </purpose><description><para><note><para>The <computeroutput>result_of&lt;Else()&gt;::type</computeroutput> expression needs be evaluated only when the static predicate is already checked to be <computeroutput>false</computeroutput> (because <computeroutput>Else()</computeroutput> is required to compile only in that case). Thus, this result-of expression is evaluated lazily and only in instantiations of this template specialization.</para>
</note>


</para></description><returns><para>A copy of the value returned by the call to the else-branch functor template <computeroutput>f()</computeroutput>. </para>
</returns></method>
<method name="else_if_c" cv="const"><type><classname>call_if_statement</classname>&lt; ElseIfPred, ElseIfThen &gt;</type><template>
          <template-nontype-parameter name="ElseIfPred"><type>bool</type><purpose><para>Static boolean predicate selecting which functor template call to compile and execute.</para></purpose></template-nontype-parameter>
          <template-type-parameter name="ElseIfThen"/>
        </template><parameter name="f"><paramtype>ElseIfThen</paramtype><description><para>Else-if-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is actually compiled and executed if and only if <computeroutput>ElseIfPred</computeroutput> is <computeroutput>true</computeroutput> (because the if-statement static predicate is already <computeroutput>false</computeroutput> for this template specialization). The return type of <computeroutput>f()</computeroutput> must be the same as (or implicitly convertible to) the return type of all other functor template calls specified for this call-if object.</para></description></parameter><purpose>Specify an else-if-branch functor template (using a static boolean predicate). </purpose><description><para>


</para></description><returns><para>A call-if statement so the else statement and additional else-if statements can be specified if needed. Eventually, this will be the return value of the one functor template call being compiled and executed. </para>
</returns></method>
<method name="else_if" cv="const"><type><classname>call_if_statement</classname>&lt; ElseIfPred::value, ElseIfThen &gt;</type><template>
          <template-type-parameter name="ElseIfPred"><purpose><para>Nullary boolean meta-function selecting which functor template call to compile and execute.</para></purpose></template-type-parameter>
          <template-type-parameter name="ElseIfThen"/>
        </template><parameter name="f"><paramtype>ElseIfThen</paramtype><description><para>Else-if-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is actually compiled and executed if and only if <computeroutput>ElseIfPred::value</computeroutput> is <computeroutput>true</computeroutput> (because the if-statement static predicate is already <computeroutput>false</computeroutput> for this template specialization). The return type of <computeroutput>f()</computeroutput> must be the same as (or implicitly convertible to) the return type of all other functor template calls specified for this call-if object.</para></description></parameter><purpose>Specify an else-if-branch functor template (using a nullary boolen meta-function). </purpose><description><para>


</para></description><returns><para>A call-if statement so the else statement and additional else-if statements can be specified if needed. Eventually, this will be the return value of the one functor template call being compiled and executed. </para>
</returns></method>
</method-group>
<constructor specifiers="explicit"><parameter name="f"><paramtype>Then const &amp;</paramtype><description><para>Then-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is never compiled or executed for this template specialization (because the if-statement static predicate is <computeroutput>false</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be the same as (or implicitly convertible to) the return type of all other functor template calls specified for this call-if object. </para></description></parameter><purpose>Construct this object with the then-branch functor template. </purpose><description><para>
</para></description></constructor>
</struct-specialization><struct-specialization name="call_if_statement"><template>
      <template-type-parameter name="Then"><purpose><para>Type of functor template to call when the static predicate is <computeroutput>true</computeroutput> (as it is for this template specialization). </para></purpose></template-type-parameter>
    </template><specialization><template-arg>true</template-arg><template-arg>Then</template-arg><template-arg>internal_type</template-arg></specialization><inherit access="public">boost::contract::call_if_statement&lt; true, Then, result_of&lt; Then()&gt;::type &gt;</inherit><purpose>Template specialization to dispatch between then-branch functor template calls that return void and the ones that return non-void (not needed on C++17 compilers, use <computeroutput>if constexpr</computeroutput> instead). </purpose><description><para>The base class is a call-if statement so the else and else-if statements can be specified if needed. Usually this class template is instantiated only via the return value of  <computeroutput><functionname alt="boost::contract::call_if">boost::contract::call_if</functionname></computeroutput> and <computeroutput><functionname alt="boost::contract::call_if_c">boost::contract::call_if_c</functionname></computeroutput>.</para><para><note><para>The <computeroutput>result_of&lt;Then()&gt;::type</computeroutput> expression needs be evaluated only when the static predicate is already checked to be <computeroutput>true</computeroutput> (because <computeroutput>Then()</computeroutput> is required to compile only in that case). Thus, this template specialization introduces an extra level of indirection necessary for proper lazy evaluation of this result-of expression.</para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_requirements__templates_">
        Assertion Requirements</link></para>
</para>

</para></description><method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="f"><paramtype>Then</paramtype><description><para>Then-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is compiled and called for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be the same as (or implicitly convertible to) the return type of all other functor template calls specified for this call-if object. </para></description></parameter><purpose>Construct this object with the then-branch functor template. </purpose><description><para>
</para></description></constructor>
</struct-specialization><struct-specialization name="call_if_statement"><template>
      <template-type-parameter name="Then"><purpose><para>Type of functor template to call when the static predicate is <computeroutput>true</computeroutput> (as it is for this template specialization). </para></purpose></template-type-parameter>
      <template-type-parameter name="ThenResult"><purpose><para>Non-void return type of the then-branch functor template call. </para></purpose></template-type-parameter>
    </template><specialization><template-arg>true</template-arg><template-arg>Then</template-arg><template-arg>ThenResult</template-arg></specialization><purpose>Template specialization to handle static predicates that are <computeroutput>true</computeroutput> for then-branch functor template calls that do not return void (not needed on C++17 compilers, use <computeroutput>if constexpr</computeroutput> instead). </purpose><description><para>Usually this class template is instantiated only via the return value of  <computeroutput><functionname alt="boost::contract::call_if">boost::contract::call_if</functionname></computeroutput> and <computeroutput><functionname alt="boost::contract::call_if_c">boost::contract::call_if_c</functionname></computeroutput>.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_requirements__templates_">
        Assertion Requirements</link></para>
</para>

</para></description><method-group name="public member functions">
<method name="conversion-operator" cv="const"><type>ThenResult</type><purpose>This implicit type conversion returns a copy of the value returned by the call to the then-branch functor template. </purpose></method>
<method name="else_" cv="const"><type>ThenResult</type><template>
          <template-type-parameter name="Else"/>
        </template><parameter name="f"><paramtype>Else const &amp;</paramtype><description><para>Else-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is never compiled or executed for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be the same as (or implicitly convertible to) the <computeroutput>ThenResult</computeroutput> type.</para></description></parameter><purpose>Specify the else-branch functor template. </purpose><description><para>

</para></description><returns><para>A copy of the value returned by the call to the then-branch functor template (because the else-branch functor template call is not executed). </para>
</returns></method>
<method name="else_if_c" cv="const"><type><classname>call_if_statement</classname>&lt; true, Then, ThenResult &gt;</type><template>
          <template-nontype-parameter name="ElseIfPred"><type>bool</type><purpose><para>Static boolean predicate selecting which functor template call to compile and execute.</para></purpose></template-nontype-parameter>
          <template-type-parameter name="ElseIfThen"/>
        </template><parameter name="f"><paramtype>ElseIfThen const &amp;</paramtype><description><para>Else-if-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is never compiled or executed for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be the same as (or implicitly convertible to) the <computeroutput>ThenResult</computeroutput> type.</para></description></parameter><purpose>Specify an else-if-branch functor template (using a static boolean predicate). </purpose><description><para>


</para></description><returns><para>A call-if statement so the else statement and additional else-if statements can be specified if needed. Eventually, it will be the return value of the then-branch functor template call for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). </para>
</returns></method>
<method name="else_if" cv="const"><type><classname>call_if_statement</classname>&lt; true, Then, ThenResult &gt;</type><template>
          <template-type-parameter name="ElseIfPred"><purpose><para>Nullary boolean meta-function selecting which functor template call to compile and execute.</para></purpose></template-type-parameter>
          <template-type-parameter name="ElseIfThen"/>
        </template><parameter name="f"><paramtype>ElseIfThen const &amp;</paramtype><description><para>Else-if-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is never compiled or executed for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be the same as (or implicitly convertible to) the <computeroutput>ThenResult</computeroutput> type.</para></description></parameter><purpose>Specify an else-if-branch functor template (using a nullary boolean meta-function). </purpose><description><para>


</para></description><returns><para>A call-if statement so the else statement and additional else-if statements can be specified if needed. Eventually, it will be the return value of the then-branch functor template call for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). </para>
</returns></method>
</method-group>
<constructor specifiers="explicit"><parameter name="f"><paramtype>Then</paramtype><description><para>Then-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is actually compiled and executed for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be the same as (or implicitly convertible to) the <computeroutput>ThenResult</computeroutput> type. </para></description></parameter><purpose>Construct this object with the then-branch functor template. </purpose><description><para>
</para></description></constructor>
</struct-specialization><struct-specialization name="call_if_statement"><template>
      <template-type-parameter name="Then"><purpose><para>Type of functor template to call when the static predicate if <computeroutput>true</computeroutput> (as it is for this template specialization). </para></purpose></template-type-parameter>
    </template><specialization><template-arg>true</template-arg><template-arg>Then</template-arg><template-arg>void</template-arg></specialization><purpose>Template specialization to handle static predicates that are <computeroutput>true</computeroutput> for then-branch functor template calls that return void (not needed on C++17 compilers, use <computeroutput>if constexpr</computeroutput> instead). </purpose><description><para>Usually this class template is instantiated only via the return value of  <computeroutput><functionname alt="boost::contract::call_if">boost::contract::call_if</functionname></computeroutput> and <computeroutput><functionname alt="boost::contract::call_if_c">boost::contract::call_if_c</functionname></computeroutput>.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_requirements__templates_">
        Assertion Requirements</link></para>
</para>

</para></description><method-group name="public member functions">
<method name="else_" cv="const"><type>void</type><template>
          <template-type-parameter name="Else"/>
        </template><parameter name="f"><paramtype>Else const &amp;</paramtype><description><para>Else-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is never compiled or executed for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be <computeroutput>void</computeroutput> for this template specialization (because the then-branch functor template calls return void). </para></description></parameter><purpose>Specify the else-branch functor template. </purpose><description><para>
</para></description></method>
<method name="else_if_c" cv="const"><type><classname>call_if_statement</classname>&lt; true, Then, void &gt;</type><template>
          <template-nontype-parameter name="ElseIfPred"><type>bool</type><purpose><para>Static boolean predicate selecting which functor template call to compile and execute.</para></purpose></template-nontype-parameter>
          <template-type-parameter name="ElseIfThen"/>
        </template><parameter name="f"><paramtype>ElseIfThen const &amp;</paramtype><description><para>Else-if-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is never compiled or executed for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be <computeroutput>void</computeroutput> for this template specialization (because the then-branch functor template calls return void).</para></description></parameter><purpose>Specify an else-if-branch functor template (using a static boolean predicate). </purpose><description><para>


</para></description><returns><para>A call-if statement so the else statement and additional else-if statements can be specified if needed. Eventually, it will return void for this template specialization (because the then-branch functor template calls return void). </para>
</returns></method>
<method name="else_if" cv="const"><type><classname>call_if_statement</classname>&lt; true, Then, void &gt;</type><template>
          <template-type-parameter name="ElseIfPred"><purpose><para>Nullary boolean meta-function selecting which functor template call to compile and execute.</para></purpose></template-type-parameter>
          <template-type-parameter name="ElseIfThen"/>
        </template><parameter name="f"><paramtype>ElseIfThen const &amp;</paramtype><description><para>Else-if-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is never compiled or executed for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be <computeroutput>void</computeroutput> for this template specialization (because the then-branch functor template calls return void).</para></description></parameter><purpose>Specify an else-if-branch functor template (using a nullary boolean meta-function). </purpose><description><para>


</para></description><returns><para>A call-if statement so the else statement and additional else-if statements can be specified if needed. Eventually, it will return void for this template specialization (because the then-branch functor template calls return void). </para>
</returns></method>
</method-group>
<constructor specifiers="explicit"><parameter name="f"><paramtype>Then</paramtype><description><para>Then-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is actually compiled and executed for this template specialization (because the if-statement static predicate is <computeroutput>true</computeroutput>). The return type of <computeroutput>f()</computeroutput> must be <computeroutput>void</computeroutput> for this template specialization (because the then-branch functor template calls return void). </para></description></parameter><purpose>Construct this object with the then-branch functor template. </purpose><description><para>
</para></description></constructor>
</struct-specialization><function name="call_if_c"><type><classname>call_if_statement</classname>&lt; Pred, Then &gt;</type><template>
          <template-nontype-parameter name="Pred"><type>bool</type><purpose><para>Static boolean predicate selecting which functor template call to compile and execute.</para></purpose></template-nontype-parameter>
          <template-type-parameter name="Then"/>
        </template><parameter name="f"><paramtype>Then</paramtype><description><para>Then-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is compiled and executed if and only if <computeroutput>Pred</computeroutput> is <computeroutput>true</computeroutput>. The return type of other functor template calls specified for this call-if statement (else-branch, else-if-branches, etc.) must be the same as (or implicitly convertible to) the return type of then-branch functor call <computeroutput>f()</computeroutput>.</para></description></parameter><purpose>Select compilation and execution of functor template calls using a static boolean predicate (not needed on C++17 compilers, use <computeroutput>if constexpr</computeroutput> instead). </purpose><description><para>Create a call-if object with the specified then-branch functor template:</para><para><programlisting language="c++">boost::contract::call_if_c&lt;Pred1&gt;(
    then_functor_template1
).template else_if_c&lt;Pred2&gt;(            // Optional.
    then_functor_template2
)                                       // Optionally, other `else_if_c` or
...                                     // `else_if`.
.else_(                                 // Optional for `void` functors,
    else_functor_template               // but required for non `void`.
)
</programlisting></para><para>Optional functor templates for else-if-branches and the else-branch can be specified as needed (the else-branch function template is required if <computeroutput>f</computeroutput> returns non-void).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_requirements__templates_">
        Assertion Requirements</link></para>
</para>



</para></description><returns><para>A call-if statement so else and else-if statements can be specified if needed. Eventually, this will be the return value of the one functor template call being compiled and executed (which could also be <computeroutput>void</computeroutput>). </para>
</returns></function>
<function name="call_if"><type><classname>call_if_statement</classname>&lt; Pred::value, Then &gt;</type><template>
          <template-type-parameter name="Pred"><purpose><para>Nullary boolean meta-function selecting which functor template call to compile and execute.</para></purpose></template-type-parameter>
          <template-type-parameter name="Then"/>
        </template><parameter name="f"><paramtype>Then</paramtype><description><para>Then-branch nullary functor template. The functor template call <computeroutput>f()</computeroutput> is compiled and executed if and only if <computeroutput>Pred::value</computeroutput> is <computeroutput>true</computeroutput>. The return type of other functor template calls specified for this call-if statement (else-branch, else-if-branches, etc.) must be the same as (or implicitly convertible to) the return type of then-branch functor template call <computeroutput>f()</computeroutput>.</para></description></parameter><purpose>Select compilation and execution of functor template calls using a nullary boolean meta-function (not needed on C++17 compilers, use <computeroutput>if constexpr</computeroutput> instead). </purpose><description><para>This is equivalent to <computeroutput>boost::contract::call_if_c&lt;Pred::value&gt;(f)</computeroutput>. Create a call-if object with the specified then-branch functor template:</para><para><programlisting language="c++">boost::contract::call_if&lt;Pred1&gt;(
    then_functor_template1
).template else_if&lt;Pred2&gt;(              // Optional.
    then_functor_template2
)                                       // Optionally, other `else_if` or
...                                     // `else_if_c`.
.else_(                                 // Optional for `void` functors,
    else_functor_template               // but required for non `void`.
)
</programlisting></para><para>Optional functor templates for else-if-branches and the else-branch can be specified as needed (the else-branch functor template is required if <computeroutput>f</computeroutput> returns non-void).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_requirements__templates_">
        Assertion Requirements</link></para>
</para>



</para></description><returns><para>A call-if statement so else and else-if statements can be specified if needed. Eventually, this will be the return value of the one functor template call being compiled and executed (which could also be <computeroutput>void</computeroutput>). </para>
</returns></function>
<function name="condition_if_c"><type>bool</type><template>
          <template-nontype-parameter name="Pred"><type>bool</type><purpose><para>Static boolean predicate selecting when the functor template call <computeroutput>f()</computeroutput> should be compiled and executed. </para></purpose></template-nontype-parameter>
          <template-type-parameter name="Then"/>
        </template><parameter name="f"><paramtype>Then</paramtype><description><para>Nullary boolean functor template. The functor template call <computeroutput>f()</computeroutput> is compiled and executed if and only if <computeroutput>Pred</computeroutput> is <computeroutput>true</computeroutput>.</para></description></parameter><parameter name="else_"><paramtype>bool</paramtype><default>true</default><description><para>Boolean value to return when <computeroutput>Pred</computeroutput> is <computeroutput>false</computeroutput> (instead of compiling and executing the functor template call <computeroutput>f()</computeroutput>).</para></description></parameter><purpose>Select compilation and execution of a boolean functor template condition using a static boolean predicate (not needed on C++17 compilers, use <computeroutput>if constexpr</computeroutput> instead). </purpose><description><para>Compile and execute the nullary boolean functor template call <computeroutput>f()</computeroutput> if and only if the specified static boolean predicate <computeroutput>Pred</computeroutput> is <computeroutput>true</computeroutput>, otherwise trivially return <computeroutput>else_</computeroutput> (<computeroutput>true</computeroutput> by default) at run-time.</para><para>A call to <computeroutput>boost::contract::condition_if_c&lt;Pred&gt;(f, else_)</computeroutput> is logically equivalent to <computeroutput>boost::contract::call_if_c&lt;Pred&gt;(f, [] { return else_; })</computeroutput> (but its internal implementation is optimized and it does not actually use <computeroutput>call_if_c</computeroutput>).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_requirements__templates_">
        Assertion Requirements</link></para>
</para>




</para></description><returns><para>Boolean value returned by <computeroutput>f()</computeroutput> if the static predicate <computeroutput>Pred</computeroutput> is <computeroutput>true</computeroutput>. Otherwise, trivially return <computeroutput>else_</computeroutput>. </para>
</returns></function>
<function name="condition_if"><type>bool</type><template>
          <template-type-parameter name="Pred"><purpose><para>Nullary boolean meta-function selecting when the functor template call <computeroutput>f()</computeroutput> should be compiled and executed.</para></purpose></template-type-parameter>
          <template-type-parameter name="Then"/>
        </template><parameter name="f"><paramtype>Then</paramtype><description><para>Nullary boolean functor template. The functor template call <computeroutput>f()</computeroutput> is compiled and executed if and only if <computeroutput>Pred::value</computeroutput> is <computeroutput>true</computeroutput>. </para></description></parameter><parameter name="else_"><paramtype>bool</paramtype><default>true</default><description><para>Boolean value to return when <computeroutput>Pred::value</computeroutput> is <computeroutput>false</computeroutput> (instead of compiling and executing the functor template call <computeroutput>f()</computeroutput>).</para></description></parameter><purpose>Select compilation and execution of a boolean functor template condition using a nullary boolean meta-function (not needed on C++17 compilers, use <computeroutput>if constexpr</computeroutput> instead). </purpose><description><para>This is equivalent to <computeroutput>boost::contract::condition_if_c&lt;Pred::value&gt;(f, else_)</computeroutput>. Compile and execute the nullary boolean functor template call <computeroutput>f()</computeroutput> if and only if the specified nullary boolean meta-function <computeroutput>Pred::value</computeroutput> is <computeroutput>true</computeroutput>, otherwise trivially return <computeroutput>else_</computeroutput> (<computeroutput>true</computeroutput> by default) at run-time.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_requirements__templates_">
        Assertion Requirements</link></para>
</para>



</para></description><returns><para>Boolean value returned by <computeroutput>f()</computeroutput> if the static predicate <computeroutput>Pred::value</computeroutput> is <computeroutput>true</computeroutput>. Otherwise, trivially return <computeroutput>else_</computeroutput>. </para>
</returns></function>




































</namespace>
</namespace>
</header>
<header name="boost/contract/check.hpp">
<para>RAII object that checks contracts. </para><namespace name="boost">
<namespace name="contract">
<class name="check"><purpose>RAII object that checks the contracts. </purpose><description><para>In general, when this object is constructed it checks class invariants at entry, preconditions, and makes old value copies at body. When it is destructed, it checks class invariants at exist, postconditions, and exception guarantees. This object enforces the following (see  <link linkend="boost_contract.contract_programming_overview"> Contract Programming Overview</link>):</para><para><itemizedlist>
<listitem><para>Postconditions are checked only if the body does not throw an exception. </para>
</listitem>
<listitem><para>Exceptions guarantees are checked only if the body throws an exception. </para>
</listitem>
<listitem><para>Constructor entry never checks class invariants. </para>
</listitem>
<listitem><para>Destructor exit checks class invariants only if the body throws an exception (even if destructors should usually not be programmed to throw exceptions in C++ and they are implicitly declared <computeroutput>noexcept</computeroutput> since C++11). </para>
</listitem>
<listitem><para>Static invariants are always checked at entry and exit (and regardless of the body throwing exceptions or not).</para>
</listitem>
</itemizedlist>
When used this way, this object is constructed and initialized to the return value of one of the contract functions <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput>,  <computeroutput><functionname alt="boost::contract::constructor">boost::contract::constructor</functionname></computeroutput>, <computeroutput><functionname alt="boost::contract::destructor">boost::contract::destructor</functionname></computeroutput>, or <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput>. In addition to that, this object can be constructed from a nullary functor when it is used to program implementation checks.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial"> Tutorial</link>,  <link linkend="boost_contract.advanced.implementation_checks"> Implementation Checks</link> </para>
</para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Nullary functor that asserts implementation checks. <computeroutput>f()</computeroutput> will be called as soon as this object is constructed at the point it is declared within the implementation code (see  <link linkend="boost_contract.advanced.implementation_checks">
            Implementation Checks</link>). </para></description></parameter><purpose>Construct this object for implementation checks. </purpose><description><para>This can be used to program checks within implementation code (body, etc.). This constructor is not declared <computeroutput>explicit</computeroutput> so initializations can use assignment syntax <computeroutput>=</computeroutput>.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify contract failure handlers that throw exceptions instead of terminating the program (see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>).</para><para>
</para></description></constructor>
<constructor><parameter name="other"><paramtype><classname>check</classname> const &amp;</paramtype><description><para>Copied-from object. </para></description></parameter><purpose>Construct this object copying it from the specified one. </purpose><description><para>This object will check the contract, the copied-from object will not (i.e., contract check ownership is transferred from the copied object to the new object being created by this constructor).</para><para>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="VirtualResult"><purpose><para>Return type of the enclosing function declaring the contract if that is either a virtual or an overriding public function, otherwise this is always <computeroutput>void</computeroutput>. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
        </template><parameter name="contract"><paramtype><classname>specify_precondition_old_postcondition_except</classname>&lt; VirtualResult &gt; const &amp;</paramtype><description><para>Contract to be checked (usually the return value of  <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput> or  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput>).</para></description></parameter><purpose>Construct this object to check the specified contract. </purpose><description><para>This checks class invariants at entry (if those were specified for the given contract). This constructor is not declared <computeroutput>explicit</computeroutput> so initializations can use assignment syntax <computeroutput>=</computeroutput>.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify contract failure handlers that throw exceptions instead of terminating the program (see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>).</para><para>

</para></description></constructor>
<constructor><template>
          <template-type-parameter name="VirtualResult"><purpose><para>Return type of the enclosing function declaring the contract if that is either a virtual or an overriding public function, otherwise this is always <computeroutput>void</computeroutput>. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
        </template><parameter name="contract"><paramtype><classname>specify_old_postcondition_except</classname>&lt; VirtualResult &gt; const &amp;</paramtype><description><para>Contract to be checked (usually the return value of  <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput>,  <computeroutput><functionname alt="boost::contract::constructor">boost::contract::constructor</functionname></computeroutput>,  <computeroutput><functionname alt="boost::contract::destructor">boost::contract::destructor</functionname></computeroutput>, or  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput>).</para></description></parameter><purpose>Construct this object to check the specified contract. </purpose><description><para>This checks class invariants at entry and preconditions (if any of those were specified for the given contract). This constructor is not declared <computeroutput>explicit</computeroutput> so initializations can use assignment syntax <computeroutput>=</computeroutput>.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify contract failure handlers that throw exceptions instead of terminating the program (see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>).</para><para>

</para></description></constructor>
<constructor><template>
          <template-type-parameter name="VirtualResult"><purpose><para>Return type of the enclosing function declaring the contract if that is either a virtual or an overriding public function, otherwise this is always <computeroutput>void</computeroutput>. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
        </template><parameter name="contract"><paramtype><classname>specify_postcondition_except</classname>&lt; VirtualResult &gt; const &amp;</paramtype><description><para>Contract to be checked (usually the return value of  <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput>,  <computeroutput><functionname alt="boost::contract::constructor">boost::contract::constructor</functionname></computeroutput>,  <computeroutput><functionname alt="boost::contract::destructor">boost::contract::destructor</functionname></computeroutput>, or  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput>).</para></description></parameter><purpose>Construct this object to check the specified contract. </purpose><description><para>This checks class invariants at entry and preconditions then it makes old value copies at body (if any of those were specified for the given contract). This constructor is not declared <computeroutput>explicit</computeroutput> so initializations can use assignment syntax <computeroutput>=</computeroutput>.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify contract failure handlers that throw exceptions instead of terminating te program (see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>).</para><para>

</para></description></constructor>
<constructor><parameter name="contract"><paramtype><classname>specify_except</classname> const &amp;</paramtype><description><para>Contract to be checked (usually the return value of  <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput>,  <computeroutput><functionname alt="boost::contract::constructor">boost::contract::constructor</functionname></computeroutput>,  <computeroutput><functionname alt="boost::contract::destructor">boost::contract::destructor</functionname></computeroutput>, or  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput>).</para></description></parameter><purpose>Construct this object to check the specified contract. </purpose><description><para>This checks class invariants at entry and preconditions then it makes old value copies at body, plus the destructor of this object will also check postconditions in this case (if any of those were specified for the given contract). This constructor is not declared <computeroutput>explicit</computeroutput> so initializations can use assignment syntax <computeroutput>=</computeroutput>.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify contract failure handlers that throw exceptions instead of terminating the program (see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>).</para><para>

</para></description></constructor>
<constructor><parameter name="contract"><paramtype><classname>specify_nothing</classname> const &amp;</paramtype><description><para>Contract to be checked (usually the return value of  <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput>,  <computeroutput><functionname alt="boost::contract::constructor">boost::contract::constructor</functionname></computeroutput>,  <computeroutput><functionname alt="boost::contract::destructor">boost::contract::destructor</functionname></computeroutput>, or  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput>).</para></description></parameter><purpose>Construct this object to check the specified contract. </purpose><description><para>This checks class invariants at entry and preconditions then it makes old value copies at body, plus the destructor of this object will also check postconditions and exception guarantees in this case (if any of those were specified for the given contract). This constructor is not declared <computeroutput>explicit</computeroutput> so initializations can use assignment syntax <computeroutput>=</computeroutput>.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify contract failure handlers that throw exceptions instead of terminating the program (see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>).</para><para>

</para></description></constructor>
<destructor><purpose>Destruct this object. </purpose><description><para>This checks class invariants at exit and either postconditions when the enclosing function body did not throw an exception, or exception guarantees when the function body threw an exception (if class invariants, postconditions, and exception guarantees respectively were specified for the enclosing class and the contract parameter given when constructing this object).</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify contract failure handlers that throw exceptions instead of terminating the program (see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>). (This is declared <computeroutput>noexcept(false)</computeroutput> since C++11.) </para></description></destructor>
</class>







































</namespace>
</namespace>
</header>
<header name="boost/contract/constructor.hpp">
<para>Program contracts for constructors. </para><namespace name="boost">
<namespace name="contract">




<function name="constructor"><type><classname>specify_old_postcondition_except</classname></type><template>
          <template-type-parameter name="Class"><purpose><para>The type of the class containing the constructor declaring the contract. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.)</para></purpose></template-type-parameter>
        </template><parameter name="obj"><paramtype>Class *</paramtype><description><para>The object <computeroutput>this</computeroutput> from the scope of the enclosing constructor declaring the contract. (Constructors check all class invariants, including static and volatile invariants, see <link linkend="boost_contract.tutorial.class_invariants">
            Class Invariants</link> and  <link linkend="boost_contract.extras.volatile_public_functions">
            Volatile Public Functions</link>).</para></description></parameter><purpose>Program contracts for constructors. </purpose><description><para>This is used to specify postconditions, exception guarantees, old value copies at body, and check class invariants for constructors (see  <computeroutput><classname alt="boost::contract::constructor_precondition">boost::contract::constructor_precondition</classname></computeroutput> to specify preconditions for constructors):</para><para><programlisting language="c++">class u {
    friend class boost::contract:access;

    void invariant() const { // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

public:
    u(...) {
        boost::contract::old_ptr&lt;old_type&gt; old_var;
        boost::contract::check c = boost::contract::constructor(this)
            // No `.precondition` (use `constructor_precondition` instead).
            .old([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(old_expr);
                ...
            })
            .postcondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .except([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ;

        ... // Constructor body.
    }

    ...
};
</programlisting></para><para>For optimization, this can be omitted for constructors that do not have postconditions and exception guarantees, within classes that have no invariants.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.constructors"> Constructors</link></para>
</para>



</para></description><returns><para>The result of this function must be assigned to a variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> declared explicitly (i.e., without using C++11 <computeroutput>auto</computeroutput> declarations) and locally just before the code of the constructor body (otherwise this library will generate a run-time error, see <computeroutput><macroname alt="BOOST_CONTRACT_ON_MISSING_CHECK_DECL">BOOST_CONTRACT_ON_MISSING_CHECK_DECL</macroname></computeroutput>). </para>
</returns></function>



































</namespace>
</namespace>
</header>
<header name="boost/contract/core/access.hpp">
<para>Allow to declare invariants, base types, etc all as private members. </para><namespace name="boost">
<namespace name="contract">
<class name="access"><purpose>Declare this class as friend to program invariants and base types as private members. </purpose><description><para>Declare this class a friend of the user-defined class specifying the contracts and then invariant functions and the base types <computeroutput>typedef</computeroutput> can be declared as non-public members:</para><para><programlisting language="c++">class u
    #define BASES public b, private w
    : BASES
{
    friend class boost::contract::access;

    typedef BOOST_CONTRACT_BASE_TYPES(BASES) base_types; // Private.
    #undef BASES

    void invariant() const { ... } // Private (same for static and volatile).

public:
    ...
};
</programlisting></para><para>In real code, programmers will likely chose to declare this class as friend so to fully control public interfaces of their user-defined classes (this is not extensively done in the examples of this documentation only for brevity). This class is not intended to be directly used by programmers a part from being declared as <computeroutput>friend</computeroutput> (and that is why this class does not have any public member and it is not copyable).</para><para><warning><para>Not declaring this class friend of user-defined classes will cause compiler errors on some compilers (e.g., MSVC) because the private members needed to check the contracts will not be accessible. On other compilers (e.g., GCC and CLang), the private access will instead fail SFINAE and no compiler error will be reported while invariants and subcontracting will be silently skipped at run-time. Therefore, programmers must make sure to either declare this class as friend or to always declare invariant functions and base types <computeroutput>typedef</computeroutput> as public members.</para>
</warning>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.access_specifiers"> Access Specifiers</link> </para>
</para>
</para></description></class>







































</namespace>
</namespace>
</header>
<header name="boost/contract/core/check_macro.hpp">
<para>Macros for implementation checks. </para><macro name="BOOST_CONTRACT_CHECK" kind="functionlike"><macro-parameter name="cond"><description><para>Boolean condition to check within implementation code (function body, etc.). (This is not a variadic macro parameter so any comma it might contain must be protected by round parenthesis and <computeroutput>BOOST_CONTRACT_CHECK((cond))</computeroutput> will always work.) </para></description></macro-parameter><purpose>Preferred way to assert implementation check conditions. </purpose><description><para>It is preferred to use this macro instead of programming implementation checks in a nullary functor passed to <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> constructor because this macro will completely remove implementation checks from the code when <computeroutput><macroname alt="BOOST_CONTRACT_NO_CHECKS">BOOST_CONTRACT_NO_CHECKS</macroname></computeroutput> is defined:</para><para><programlisting language="c++">void f() {
    ...
    BOOST_CONTRACT_CHECK(cond);
    ...
}
</programlisting></para><para> <computeroutput><macroname alt="BOOST_CONTRACT_CHECK">BOOST_CONTRACT_CHECK</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_CHECK_AUDIT">BOOST_CONTRACT_CHECK_AUDIT</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_CHECK_AXIOM">BOOST_CONTRACT_CHECK_AXIOM</macroname></computeroutput> are the three assertion levels predefined by this library for implementation checks.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.implementation_checks"> Implementation Checks</link></para>
</para>

</para></description></macro>
<macro name="BOOST_CONTRACT_CHECK_AUDIT" kind="functionlike"><macro-parameter name="cond"><description><para>Boolean condition to check within implementation code (function body, etc.). (This is not a variadic macro parameter so any comma it might contain must be protected by round parenthesis and <computeroutput>BOOST_CONTRACT_CHECK_AUDIT((cond))</computeroutput> will always work.) </para></description></macro-parameter><purpose>Preferred way to assert implementation check conditions that are computationally expensive, at least compared to the computational cost of executing the function body. </purpose><description><para>The specified condition will always be compiled and validated syntactically, but it will not be checked at run-time unless  <computeroutput><macroname alt="BOOST_CONTRACT_AUDITS">BOOST_CONTRACT_AUDITS</macroname></computeroutput> is defined (undefined by default). This macro is defined by code equivalent to:</para><para><programlisting language="c++">#ifdef BOOST_CONTRACT_AUDITS
    #define BOOST_CONTRACT_CHECK_AUDIT(cond) \
        BOOST_CONTRACT_CHECK(cond)
#else
    #define BOOST_CONTRACT_CHECK_AUDIT(cond) \
        BOOST_CONTRACT_CHECK(true || cond)
#endif
</programlisting></para><para> <computeroutput><macroname alt="BOOST_CONTRACT_CHECK">BOOST_CONTRACT_CHECK</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_CHECK_AUDIT">BOOST_CONTRACT_CHECK_AUDIT</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_CHECK_AXIOM">BOOST_CONTRACT_CHECK_AXIOM</macroname></computeroutput> are the three assertion levels predefined by this library for implementation checks. If there is a need, programmers are free to implement their own assertion levels defining macros similar to the one above.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_levels"> Assertion Levels</link></para>
</para>

</para></description></macro>
<macro name="BOOST_CONTRACT_CHECK_AXIOM" kind="functionlike"><macro-parameter name="cond"><description><para>Boolean condition to check within implementation code (function body, etc.). (This is not a variadic macro parameter so any comma it might contain must be protected by round parenthesis and <computeroutput>BOOST_CONTRACT_CHECK_AXIOM((cond))</computeroutput> will always work.) </para></description></macro-parameter><purpose>Preferred way to document in the code implementation check conditions that are computationally prohibitive, at least compared to the computational cost of executing the function body. </purpose><description><para>The specified condition will always be compiled and validated syntactically, but it will never be checked at run-time. This macro is defined by code equivalent to:</para><para><programlisting language="c++">#define BOOST_CONTRACT_CHECK_AXIOM(cond) \
    BOOST_CONTRACT_CHECK(true || cond)
</programlisting></para><para> <computeroutput><macroname alt="BOOST_CONTRACT_CHECK">BOOST_CONTRACT_CHECK</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_CHECK_AUDIT">BOOST_CONTRACT_CHECK_AUDIT</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_CHECK_AXIOM">BOOST_CONTRACT_CHECK_AXIOM</macroname></computeroutput> are the three assertion levels predefined by this library for implementation checks. If there is a need, programmers are free to implement their own assertion levels defining macros similar to the one above.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_levels"> Assertion Levels</link></para>
</para>

</para></description></macro>
</header>
<header name="boost/contract/core/config.hpp">
<para>Configure this library compile-time and run-time behaviours. </para><macro name="BOOST_CONTRACT_DYN_LINK"><purpose>Define this macro to compile this library as a shared library (recommended). </purpose><description><para>If this macro is defined, this library is compiled so it can be linked as a shared library (a.k.a., Dynamically Linked Library or DLL) to user code. This library will automatically define this macro when Boost libraries are built as shared libraries (e.g., defining <computeroutput>BOOST_ALL_DYN_LINK</computeroutput> or using <computeroutput>bjam link=shared ...</computeroutput>).</para><para><warning><para>In general this library will correctly check contracts at run-time only when compiled as a shared library, unless user code checks contracts in a single program unit (e.g., a single program with only statically linked libraries). Therefore, it is recommended to build and use this library as a shared library by defining this macro (or equivalently by building all Boost libraries as shared libraries).</para>
</warning>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.getting_started"> Getting Started</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_STATIC_LINK"><purpose>Define this macro to compile this library as a static library (not recommended). </purpose><description><para>If this macro is defined, this library is compiled so it can be linked statically to user code. This library will automatically define this macro when Boost libraries are built as static libraries.</para><para><warning><para>This library is not guaranteed to always work correctly at run-time when this macro is defined (define  <computeroutput><macroname alt="BOOST_CONTRACT_DYN_LINK">BOOST_CONTRACT_DYN_LINK</macroname></computeroutput> or <computeroutput>BOOST_ALL_DYN_LINK</computeroutput> instead). However, this macro can be defined and this library can be safely used as a static library for user code that checks contracts in a single program unit (e.g., a single program with only statically linked libraries).</para>
</warning>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.getting_started"> Getting Started</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_HEADER_ONLY"><purpose>Automatically defined by this library when it is being used as a header-only library (not recommended). </purpose><description><para>This macro is not a configuration macro and this library will generate a compile-time error if users try to define it directly. This library will automatically define this macro when users do not define  <computeroutput><macroname alt="BOOST_CONTRACT_DYN_LINK">BOOST_CONTRACT_DYN_LINK</macroname></computeroutput> (or <computeroutput>BOOST_ALL_DYN_LINK</computeroutput>) and  <computeroutput><macroname alt="BOOST_CONTRACT_STATIC_LINK">BOOST_CONTRACT_STATIC_LINK</macroname></computeroutput>. When used as a header-only library, this library code does not have to be compiled separately from user code, this library headers are simply included and compiled as part of the user program.</para><para><warning><para>This library is not guaranteed to always work correctly at run-time when this macro is defined (define  <computeroutput><macroname alt="BOOST_CONTRACT_DYN_LINK">BOOST_CONTRACT_DYN_LINK</macroname></computeroutput> or <computeroutput>BOOST_ALL_DYN_LINK</computeroutput> instead). However, this macro can be defined and this library can be safely used as a header-only library for user code that checks contracts in a single program unit (e.g., a single program with only statically linked libraries).</para>
</warning>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.getting_started"> Getting Started</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_DISABLE_THREADS"><purpose>Define this macro to not lock internal library data for thread safety (undefined by default). </purpose><description><para>Defining this macro will make the library implementation code not thread safe so this macro should not be defined unless the library is being used by single-threaded applications only. This library will automatically define this macro when Boost libraries are built without threads (e.g., defining <computeroutput>BOOST_DISABLE_THREADS</computeroutput>).</para><para><note><para>When this macro is left undefined this library needs to internally use some sort of global lock (to ensure contract checking is globally disabled when other contracts are being checked and also to safely access failure handler functors). That could introduce an undesired amount of synchronization in some multi-threaded applications.</para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.contract_programming_overview.assertions"> Assertions</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_MAX_ARGS"><purpose>Maximum number of arguments for public function overrides on compilers that do not support variadic templates (default to <computeroutput>10</computeroutput>). </purpose><description><para>On compilers that do not support C++11 variadic templates, this macro is defined to the maximum number of arguments that public function overrides can have and pass to <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput> (users can redefine this macro to a different value). On compilers that support variadic templates, this macro has no effect.</para><para><note><para>Regardless of the value of this macro and of compiler support for variadic templates, there might be an intrinsic limit of about 18 arguments for public function overrides (because of similar limits in Boost.MPL and Boost.FunctionTypes internally used by this library).</para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_BASES_TYPEDEF"><purpose>Define the name of the base type <computeroutput>typedef</computeroutput> (<computeroutput>base_types</computeroutput> by default). </purpose><description><para>This macro expands to the name of the <computeroutput>typedef</computeroutput> that lists the base classes for subcontracting via <computeroutput><macroname alt="BOOST_CONTRACT_BASE_TYPES">BOOST_CONTRACT_BASE_TYPES</macroname></computeroutput>:</para><para><programlisting language="c++">class u
    #define BASES public b, private w
    : BASES
{
    friend class boost::contract:access;

    typedef BOOST_CONTRACT_BASE_TYPES(BASES) BOOST_CONTRACT_TYPEDEF;
    #undef BASES

    ...
};
</programlisting></para><para>When used this way, users can redefine this macro if the <computeroutput>typedef</computeroutput> must have a name different from <computeroutput>base_types</computeroutput> (because of name clashes in user code, etc.).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.base_classes__subcontracting_"> Base Classes</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_INVARIANT_FUNC"><purpose>Define the name of the class invariant member function (<computeroutput>invariant</computeroutput> by default). </purpose><description><para>This macro expands to the name of the <computeroutput>const</computeroutput> and <computeroutput>const volatile</computeroutput> member functions that check class invariants and volatile class invariants respectively:</para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    void BOOST_CONTRACT_INVARIANT_FUNC() const {
        BOOST_CONTRACT_ASSERT(...);
        ...
    }
    
    void BOOST_CONTRACT_INVARIANT_FUNC() const volatile {
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

    ...
};
</programlisting></para><para>When used this way, users can redefine this macro if the invariant functions must have a name different from <computeroutput>invariant</computeroutput> (because of name clashes in user code, etc.).</para><para><note><para>C++ does not allow to overload member functions based on the <computeroutput>static</computeroutput> classifier, so this macro must always be defined to be different than the function name defined for  <computeroutput><macroname alt="BOOST_CONTRACT_STATIC_INVARIANT_FUNC">BOOST_CONTRACT_STATIC_INVARIANT_FUNC</macroname></computeroutput>.</para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_STATIC_INVARIANT_FUNC"><purpose>Define the name of the static invariant member function (<computeroutput>static_invariant</computeroutput> by default). </purpose><description><para>This macro expands to the name of the <computeroutput>static</computeroutput> member function that checks static class invariants:</para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    static void BOOST_CONTRACT_STATIC_INVARIANT_FUNC() {
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

    ...
};
</programlisting></para><para>When used this way, users can redefine this macro if the static invariant function must have a name different from <computeroutput>static_invariant</computeroutput> (because of name clashes in user code, etc.).</para><para><note><para>C++ does not allow to overload member functions based on the <computeroutput>static</computeroutput> classifier, so this macro must always be defined to be different than the function name defined for  <computeroutput><macroname alt="BOOST_CONTRACT_INVARIANT_FUNC">BOOST_CONTRACT_INVARIANT_FUNC</macroname></computeroutput>.</para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_PERMISSIVE"><purpose>Disable some compile-time errors generated by this library (undefined by default). </purpose><description><para>Defining this macro disables a number of static checks and related compile-time errors generated by this library, for example:</para><para><itemizedlist>
<listitem><para>The static invariant member function named as <computeroutput>BOOST_CONTRACT_STATIC_INVARIANT_FUNC</computeroutput> must be declared <computeroutput>static</computeroutput>. </para>
</listitem>
<listitem><para>Non-static invariant member functions named as <computeroutput>BOOST_CONTRACT_INVARIANT_FUNC</computeroutput> must be declared either <computeroutput>const</computeroutput>, <computeroutput>const volatile</computeroutput>, or <computeroutput>volatile const</computeroutput>. </para>
</listitem>
<listitem><para>Derived classes that program contracts for one or more public function overrides via <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput> must also define the <computeroutput><macroname alt="BOOST_CONTRACT_BASE_TYPES">BOOST_CONTRACT_BASE_TYPES</macroname></computeroutput> <computeroutput>typedef</computeroutput>.</para>
</listitem>
</itemizedlist>
In general, it is not recommended to define this macro because these compile-time checks can guard against misuses of this library.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.tutorial.base_classes__subcontracting_"> Base Classes</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_ON_MISSING_CHECK_DECL"><purpose>Code block to execute if contracts are not assigned to a  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> variable (undefined and executes <computeroutput>BOOST_ASSERT(false)</computeroutput> by default). </purpose><description><para>In general, there is a logic error in the program when contracts are not explicitly assigned to a local variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> and without using C++11 <computeroutput>auto</computeroutput> declarations (because that is a misuse of this library). Therefore, by default (i.e., when this macro is not defined) this library calls <computeroutput>BOOST_ASSERT(false)</computeroutput> in those cases. If this macro is defined, this library will execute the code expanded by this macro instead of calling <computeroutput>BOOST_ASSERT(false)</computeroutput> (if programmers prefer to throw an exception, etc.).</para><para>This macro can also be defined to be any block of code (and use empty curly brackets <computeroutput>{}</computeroutput> to generate no error, not recommended), for example (on GCC): <programlisting language="c++">gcc -DBOOST_CONTRACT_ON_MISSING_CHECK_DECL='{ throw std::logic_error("missing contract check declaration"); }' ...
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial"> Tutorial</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_PRECONDITIONS_DISABLE_NO_ASSERTION"><purpose>Define this macro to not disable other assertions while checking preconditions (undefined by default). </purpose><description><para>Not disabling other assertions while checking preconditions can lead to infinite recursion in user code so by default this macro is not defined.</para><para>However, the <link linkend="boost_contract.bibliography"> [N1962]</link> proposal does not disable assertions while checking preconditions because arguments can reach the function body unchecked if assertions are disabled while checking preconditions (e.g., when these same functions bodies are called to check the preconditions in question). This macro can be defined to obtain the behaviour specified in  <link linkend="boost_contract.bibliography"> [N1962]</link> (at the risk of infinite recursion).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.contract_programming_overview.feature_summary">
        Feature Summary</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_ALL_DISABLE_NO_ASSERTION"><purpose>Define this macro to not disable any assertion while checking other assertions (undefined by default). </purpose><description><para>Not disabling assertions while checking other assertions can lead to infinite recursion in user code so by default this macro is not defined. (Defining this macro automatically implies that other assertion checking is disabled while checking preconditions as if  <computeroutput><macroname alt="BOOST_CONTRACT_PRECONDITIONS_DISABLE_NO_ASSERTION">BOOST_CONTRACT_PRECONDITIONS_DISABLE_NO_ASSERTION</macroname></computeroutput> was also defined.)</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.contract_programming_overview.feature_summary">
        Feature Summary</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_AUDITS"><purpose>Define this macro to evaluate and check audit assertions at run-time (undefined by default). </purpose><description><para>Audit assertions and implementation checks programmed via  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT_AUDIT">BOOST_CONTRACT_ASSERT_AUDIT</macroname></computeroutput> and  <computeroutput><macroname alt="BOOST_CONTRACT_CHECK_AUDIT">BOOST_CONTRACT_CHECK_AUDIT</macroname></computeroutput> are always compiled and validated syntactically. However, they are not evaluated and checked at run-time unless this macro is defined (because these conditions can be computationally expensive, at least compared to the computational cost of executing the function body).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.assertion_levels"> Assertion Levels</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_CHECKS"><purpose>If defined, this library disables implementation checks (undefined by default). </purpose><description><para>If this macro is defined, this library internal code is also optimized to reduce compile-time (not just run-time) overhead associated with implementation checks. In addition, users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of implementation checks or use <computeroutput><macroname alt="BOOST_CONTRACT_CHECK">BOOST_CONTRACT_CHECK</macroname></computeroutput> (recommended).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.implementation_checks">
        Implementation Checks</link>,  <link linkend="boost_contract.extras.disable_contract_checking">
        Disable Contract Checking</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_PRECONDITIONS"><purpose>If defined, this library does not check preconditions (undefined by default). </purpose><description><para>If this macro is defined, this library internal code is also optimized to reduce compile-time (not just run-time) overhead associated with checking preconditions. In addition, users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of preconditions or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.preconditions"> Preconditions</link>,  <link linkend="boost_contract.extras.disable_contract_checking">
        Disable Contract Checking</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_POSTCONDITIONS"><purpose>If defined, this library does not check postconditions (undefined by default). </purpose><description><para>If this macro is defined, this library internal code is also optimized to reduce compile-time (not just run-time) overhead associated with checking postconditions. In addition, users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of postconditions or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para>It is necessary to disable both postconditions and exception guarantees defining <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput> and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput> in order to disable old value copies (see <computeroutput><macroname alt="BOOST_CONTRACT_NO_OLDS">BOOST_CONTRACT_NO_OLDS</macroname></computeroutput>).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.postconditions"> Postconditions</link>,  <link linkend="boost_contract.extras.disable_contract_checking">
        Disable Contract Checking</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_EXCEPTS"><purpose>If defined, this library does not check exception guarantees (undefined by default). </purpose><description><para>If this macro is defined, this library internal code is also optimized to reduce compile-time (not just run-time) overhead associated with checking exception guarantees. In addition, users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of exception guarantees or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para>It is necessary to disable both postconditions and exception guarantees defining <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput> and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput> in order to disable old value copies (see <computeroutput><macroname alt="BOOST_CONTRACT_NO_OLDS">BOOST_CONTRACT_NO_OLDS</macroname></computeroutput>).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.exception_guarantees"> Exception Guarantees</link>,  <link linkend="boost_contract.extras.disable_contract_checking">
        Disable Contract Checking</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_ENTRY_INVARIANTS"><purpose>If defined, this library does not check class invariants at entry (undefined by default). </purpose><description><para>If this macro is defined, this library internal code is also optimized to reduce compile-time (not just run-time) overhead associated with checking class invariants at entry. In addition, users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of entry class invariants or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para>This macro is automatically defined when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput> is defined.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.disable_contract_checking">
        Disable Contract Checking</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_EXIT_INVARIANTS"><purpose>If defined, this library does not check class invariants at exit (undefined by default). </purpose><description><para>If this macro is defined, this library internal code is also optimized to reduce compile-time (not just run-time) overhead associated with checking class invariants at exit. In addition, users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of exit class invariants or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para>This macro is automatically defined when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput> is defined.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.disable_contract_checking">
        Disable Contract Checking</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_INVARIANTS"><purpose>If defined, this library does not check class invariants (undefined by default). </purpose><description><para>If this macro is defined, this library internal code is also optimized to reduce compile-time (not just run-time) overhead associated with checking class invariants. In addition, users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of class invariants or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para>Defining this macro is equivalent to defining both  <computeroutput><macroname alt="BOOST_CONTRACT_NO_ENTRY_INVARIANTS">BOOST_CONTRACT_NO_ENTRY_INVARIANTS</macroname></computeroutput> and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXIT_INVARIANTS">BOOST_CONTRACT_NO_EXIT_INVARIANTS</macroname></computeroutput>.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.disable_contract_checking">
        Disable Contract Checking</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_OLDS"><purpose>Automatically defined by this library when old value copies are not to be performed. </purpose><description><para>This macro is not a configuration macro and this library will generate a compile-time error if users try to define it directly. This library will automatically define this macro when users define both  <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput> and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput>. Users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of old value copies or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.old_values"> Old Values</link>,  <link linkend="boost_contract.advanced.old_values_copied_at_body">
        Old Values Copied at Body</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_CONSTRUCTORS"><purpose>Automatically defined by this library when contracts are not checked for constructors. </purpose><description><para>This macro is not a configuration macro and this library will generate a compile-time error if users try to define it directly. This library will automatically define this macro when users define all  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput>. Users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of contracts for constructors or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para><note><para>Constructor preconditions are checked separately by  <computeroutput><classname alt="boost::contract::constructor_precondition">boost::contract::constructor_precondition</classname></computeroutput> so they are disabled by <computeroutput><macroname alt="BOOST_CONTRACT_NO_PRECONDITIONS">BOOST_CONTRACT_NO_PRECONDITIONS</macroname></computeroutput> instead.</para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.constructors"> Constructors</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_DESTRUCTORS"><purpose>Automatically defined by this library when contracts are not checked for destructors. </purpose><description><para>This macro is not a configuration macro and this library will generate a compile-time error if users try to define it directly. This library will automatically define this macro when users define all  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput>. Users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of contracts for destructors or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.destructors"> Destructors</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS"><purpose>Automatically defined by this library when contracts are not checked for public functions. </purpose><description><para>This macro is not a configuration macro and this library will generate a compile-time error if users try to define it directly. This library will automatically define this macro when users define all  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_PRECONDITIONS">BOOST_CONTRACT_NO_PRECONDITIONS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput>. Users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of contracts for public functions or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.public_functions"> Public Functions</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_FUNCTIONS"><purpose>Automatically defined by this library when contracts are not checked for non-member, private, or protected functions. </purpose><description><para>This macro is not a configuration macro and this library will generate a compile-time error if users try to define it directly. This library will automatically define this macro when users define all  <computeroutput><macroname alt="BOOST_CONTRACT_NO_PRECONDITIONS">BOOST_CONTRACT_NO_PRECONDITIONS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput>. Users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of contracts for non-member, private and protected functions, or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para>This macro is also used when contracts are not checked for private or protected functions, lambda functions, code blocks, loops, etc.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.non_member_functions"> Non-Member Functions</link>,  <link linkend="boost_contract.advanced.private_and_protected_functions">
        Private and Protected Functions</link>,  <link linkend="boost_contract.advanced.lambdas__loops__code_blocks__and__constexpr__">
        Lambdas, Loops, Code Blocks</link>,  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_CONDITIONS"><purpose>Automatically defined by this library when contracts are not checked for preconditions, postconditions, exceptions guarantees, and class invariants (excluding implementation checks). </purpose><description><para>This macro is not a configuration macro and this library will generate a compile-time error if users try to define it directly. This library will automatically define this macro when users define all  <computeroutput><macroname alt="BOOST_CONTRACT_NO_PRECONDITIONS">BOOST_CONTRACT_NO_PRECONDITIONS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput>. Users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to completely disable compilation of contracts within specifications (so excluding implementation checks which are contracts within implementations instead), or use the macros defined in <computeroutput>boost/contract_macro.hpp</computeroutput> (recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_NO_ALL"><purpose>Automatically defined by this library when contracts are not checked at all (neither for specifications nor for implementations). </purpose><description><para>This macro is not a configuration macro and this library will generate a compile-time error if users try to define it directly. This library will automatically define this macro when users define all  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_PRECONDITIONS">BOOST_CONTRACT_NO_PRECONDITIONS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_NO_CHECKS">BOOST_CONTRACT_NO_CHECKS</macroname></computeroutput>. For example, users can manually program <computeroutput>#ifndef</computeroutput> statements in their code using this macro to avoid including the <computeroutput>boost/contract.hpp</computeroutput> header all together:</para><para><programlisting language="c++">#include &lt;boost/contract/core/config.hpp&gt;
#ifndef BOOST_CONTRACT_NO_ALL
    #include &lt;boost/contract.hpp&gt;
#endif
</programlisting></para><para>Or, use the <computeroutput>boost/contract_macro.hpp</computeroutput> header and related macros instead (because the <computeroutput>boost/contract_macro.hpp</computeroutput> header is already optimized to not include other headers from this library when contracts are not checked, but recommended only for applications where it is truly necessary to completely remove contract code compilation from production code).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link> </para>
</para>
</para></description></macro>
</header>
<header name="boost/contract/core/constructor_precondition.hpp">
<para>Program preconditions for constructors. </para><namespace name="boost">
<namespace name="contract">
<class name="constructor_precondition"><template>
      <template-type-parameter name="Class"><purpose><para>The class type of the constructor for which preconditions are being programmed. </para></purpose></template-type-parameter>
    </template><purpose>Program preconditions for constructors. </purpose><description><para>This class must be the very first base of the class declaring the constructor for which preconditions are programmed (that way constructor arguments can be checked by preconditions even before they are used to initialize other base classes):</para><para><programlisting language="c++">class u
    #define BASES private boost::contract::constructor_precondition&lt;u&gt;, \
            public b
    : BASES
{
    friend class boost::contract::access;

    typedef BOOST_CONTRACT_BASE_TYPES(BASES) base_types;
    #undef BASES

public:
    explicit u(unsigned x) :
        boost::contract::constructor_precondition&lt;u&gt;([&amp;] {
            BOOST_CONTRACT_ASSERT(x != 0);
            ...
        }),
        b(1.0 / float(x))
    {
        ...
    }

    ...
};
</programlisting></para><para>User-defined classes should inherit privately from this class (to not alter the public interface of user-defined classes). In addition, this class should never be declared as a virtual base (because virtual bases are initialized only once across the entire inheritance hierarchy preventing preconditions of other base classes from being checked).</para><para>This class cannot be used this way in a <computeroutput>union</computeroutput> because unions cannot have base classes in C++. Instead, this class is used in a <computeroutput>union</computeroutput> to declare a local object within the constructor definition just before <computeroutput><functionname alt="boost::contract::constructor">boost::contract::constructor</functionname></computeroutput> is used (see <link linkend="boost_contract.extras.unions"> Unions</link>).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.constructors"> Constructors</link></para>
</para>

</para></description><method-group name="public member functions">
</method-group>
<constructor><purpose>Construct this object without specifying constructor preconditions. </purpose><description><para>This is implicitly called for those constructors of the contracted class that do not specify preconditions.</para><para><note><para>The implementation of this library is optimized so that calling this default constructor should amount to negligible compile-time and run-time overheads (likely to be optimized away completely by most compilers). </para>
</note>
</para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Nullary functor called by this library to check constructor preconditions <computeroutput>f()</computeroutput>. Assertions within this functor call are usually programmed using <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::precondition_failure">boost::contract::precondition_failure</functionname></computeroutput>). This functor should capture variables by (constant) value, or better by (constant) reference to avoid extra copies. </para></description></parameter><purpose>Construct this object specifying constructor preconditions. </purpose><description><para>
</para></description></constructor>
</class>







































</namespace>
</namespace>
</header>
<header name="boost/contract/core/exception.hpp">
<para>Handle contract assertion failures. </para><namespace name="boost">
<namespace name="contract">
<class name="assertion_failure"><inherit access="public">std::exception</inherit><inherit access="public">boost::contract::exception</inherit><purpose>Exception typically used to report a contract assertion failure. </purpose><description><para>This exception is thrown by code expanded by <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput> (but it can also be thrown by user code programmed manually without that macro). This exception is typically used to report contract assertion failures because it contains detailed information about the file name, line number, and source code of the asserted condition (so it can be used by this library to provide detailed error messages when handling contract assertion failures).</para><para>However, any other exception can be used to report a contract assertion failure (including user-defined exceptions). This library will call the appropriate contract failure handler function ( <computeroutput><functionname alt="boost::contract::precondition_failure">boost::contract::precondition_failure</functionname></computeroutput>, etc.) when this or any other exception is thrown while checking contracts (by default, these failure handler functions print an error message to <computeroutput>std::cerr</computeroutput> and terminate the program, but they can be customized to take any other action).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link> </para>
</para>
</para></description><method-group name="public member functions">
<method name="what" cv="const" specifiers="virtual"><type>char const  *</type><purpose>String describing the failed assertion. </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>
</para></description><returns><para>A string formatted similarly to the following: <computeroutput>assertion "`code()`" failed: file "`file()`", line `line()`</computeroutput> (where `` indicate execution quotes). File, line, and code will be omitted from this string if they were not specified when constructing this object. </para>
</returns></method>
<method name="file" cv="const"><type>char const  *</type><purpose>Name of the file containing the assertion. </purpose><description><para>
</para></description><returns><para>File name as specified at construction (or <computeroutput>""</computeroutput> if no file was specified). </para>
</returns></method>
<method name="line" cv="const"><type>unsigned long</type><purpose>Number of the line containing the assertion. </purpose><description><para>
</para></description><returns><para>Line number as specified at construction (or <computeroutput>0</computeroutput> if no line number was specified). </para>
</returns></method>
<method name="code" cv="const"><type>char const  *</type><purpose>Text listing the source code of the assertion condition. </purpose><description><para>
</para></description><returns><para>Assertion condition source code as specified at construction (or <computeroutput>""</computeroutput> if no source code text was specified). </para>
</returns></method>
</method-group>
<constructor specifiers="explicit"><parameter name="file"><paramtype>char const *</paramtype><default>""</default><description><para>Name of the file containing the assertion (usually set using <computeroutput>__FILE__</computeroutput>). </para></description></parameter><parameter name="line"><paramtype>unsigned long</paramtype><default>0</default><description><para>Number of the line containing the assertion (usually set using <computeroutput>__LINE__</computeroutput>). </para></description></parameter><parameter name="code"><paramtype>char const *</paramtype><default>""</default><description><para>Text listing the source code of the assertion condition. </para></description></parameter><purpose>Construct this object with file name, line number, and source code text of an assertion condition (all optional). </purpose><description><para>This constructor can also be used to specify no information (default constructor), or to specify only file name and line number but not source code text (because of the parameter default values).</para><para>
</para></description></constructor>
<constructor specifiers="explicit"><parameter name="code"><paramtype>char const *</paramtype><description><para>Text listing the source code of the assertion condition. </para></description></parameter><purpose>Construct this object only with the source code text of the assertion condition. </purpose><description><para>
</para></description></constructor>
<destructor><purpose>Destruct this object. </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11). </para></description></destructor>
</class><class name="bad_virtual_result_cast"><inherit access="public">std::bad_cast</inherit><inherit access="public">boost::contract::exception</inherit><purpose>Exception thrown when inconsistent return values are passed to overridden virtual public functions. </purpose><description><para>This exception is thrown when programmers pass to this library return value parameters for public function overrides in derived classes that are not consistent with the return type parameter passed for the virtual public function being overridden from the base classes. This allows this library to give more descriptive error messages in such cases of misuse.</para><para>This exception is internally thrown by this library and programmers should not need to throw it from user code.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.public_function_overrides__subcontracting_">
        Public Function Overrides</link> </para>
</para>
</para></description><method-group name="public member functions">
<method name="what" cv="const" specifiers="virtual"><type>char const  *</type><purpose>Description for this error (containing both from- and to- type names). </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11). </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="from_type_name"><paramtype>char const *</paramtype><description><para>Name of the from-type (source of the cast). </para></description></parameter><parameter name="to_type_name"><paramtype>char const *</paramtype><description><para>Name of the to-type (destination of the cast). </para></description></parameter><purpose>Construct this object with the name of the from- and to- result types. </purpose><description><para>
</para></description></constructor>
<destructor><purpose>Destruct this object. </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11). </para></description></destructor>
</class><class name="exception"><purpose>Public base class for all exceptions directly thrown by this library. </purpose><description><para>This class does not inherit from <computeroutput>std::exception</computeroutput> because exceptions deriving from this class will do that (inheriting from <computeroutput>std::exception</computeroutput>, <computeroutput>std::bad_cast</computeroutput>, etc.).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <computeroutput><classname alt="boost::contract::assertion_failure">boost::contract::assertion_failure</classname></computeroutput>,  <computeroutput><classname alt="boost::contract::bad_virtual_result_cast">boost::contract::bad_virtual_result_cast</classname></computeroutput>, etc. </para>
</para>
</para></description><method-group name="public member functions">
</method-group>
<destructor><purpose>Destruct this object. </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11). </para></description></destructor>
</class><enum name="from"><enumvalue name="from_constructor"><purpose>Assertion failed when checking contracts for constructors. </purpose></enumvalue><enumvalue name="from_destructor"><purpose>Assertion failed when checking contracts for destructors . </purpose></enumvalue><enumvalue name="from_function"><purpose>Assertion failed when checking contracts for functions (members or not, public or not). </purpose></enumvalue><purpose>Indicate the kind of operation where the contract assertion failed. </purpose><description><para>This is passed as a parameter to the assertion failure handler functions. For example, it might be necessary to know in which operation an assertion failed to make sure exceptions are never thrown from destructors, not even when contract failure handlers are programmed by users to throw exceptions instead of terminating the program.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link> </para>
</para>
</para></description></enum>
<typedef name="from_failure_handler"><purpose>Type of assertion failure handler functions (with <computeroutput>from</computeroutput> parameter). </purpose><description><para>Assertion failure handler functions specified by this type must be functors returning <computeroutput>void</computeroutput> and taking a single parameter of type  <computeroutput><enumname alt="boost::contract::from">boost::contract::from</enumname></computeroutput>. For example, this is used to specify contract failure handlers for class invariants, preconditions, postconditions, and exception guarantees.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link> </para>
</para>
</para></description><type>boost::function&lt; void(from)&gt;</type></typedef>
<typedef name="failure_handler"><purpose>Type of assertion failure handler functions (without <computeroutput>from</computeroutput> parameter). </purpose><description><para>Assertion failure handler functions specified by this type must be nullary functors returning <computeroutput>void</computeroutput>. For example, this is used to specify contract failure handlers for implementation checks.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link> </para>
</para>
</para></description><type>boost::function&lt; void()&gt;</type></typedef>


















<function name="set_check_failure"><type>failure_handler const  &amp;</type><parameter name="f"><paramtype>failure_handler const &amp;</paramtype><description><para>New failure handler functor to set.</para></description></parameter><purpose>Set failure handler for implementation checks. </purpose><description><para>Set a new failure handler and returns it.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>

<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.advanced.implementation_checks"> Implementation Checks</link> </para>
</para>
</para></description><returns><para>Same failure handler functor <computeroutput>f</computeroutput> passed as parameter (e.g., for concatenating function calls).</para>
</returns></function>
<function name="get_check_failure"><type>failure_handler</type><purpose>Return failure handler currently set for implementation checks. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.advanced.implementation_checks"> Implementation Checks</link> </para>
</para>
</para></description><returns><para>A copy of the failure handler currently set.</para>
</returns></function>
<function name="check_failure"><type>void</type><purpose>Call failure handler for implementation checks. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify a failure handler that throws exceptions on implementation check failures (not the default).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.advanced.implementation_checks"> Implementation Checks</link> </para>
</para>
</para></description></function>
<function name="set_precondition_failure"><type>from_failure_handler const  &amp;</type><parameter name="f"><paramtype>from_failure_handler const &amp;</paramtype><description><para>New failure handler functor to set.</para></description></parameter><purpose>Set failure handler for preconditions. </purpose><description><para>Set a new failure handler and returns it.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>

<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.preconditions"> Preconditions</link> </para>
</para>
</para></description><returns><para>Same failure handler functor <computeroutput>f</computeroutput> passed as parameter (e.g., for concatenating function calls).</para>
</returns></function>
<function name="get_precondition_failure"><type>from_failure_handler</type><purpose>Return failure handler currently set for preconditions. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.preconditions"> Preconditions</link> </para>
</para>
</para></description><returns><para>A copy of the failure handler currently set.</para>
</returns></function>
<function name="precondition_failure"><type>void</type><parameter name="where"><paramtype>from</paramtype><description><para>Operation that failed the contract assertion (when this function is called by this library, this parameter will never be <computeroutput>from_destructor</computeroutput> because destructors do not have preconditions).</para></description></parameter><purpose>Call failure handler for preconditions. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify a failure handler that throws exceptions on contract assertion failures (not the default).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.preconditions"> Preconditions</link> </para>
</para>
</para></description></function>
<function name="set_postcondition_failure"><type>from_failure_handler const  &amp;</type><parameter name="f"><paramtype>from_failure_handler const &amp;</paramtype><description><para>New failure handler functor to set.</para></description></parameter><purpose>Set failure handler for postconditions. </purpose><description><para>Set a new failure handler and returns it.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>

<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.postconditions"> Postconditions</link> </para>
</para>
</para></description><returns><para>Same failure handler functor <computeroutput>f</computeroutput> passed as parameter (e.g., for concatenating function calls).</para>
</returns></function>
<function name="get_postcondition_failure"><type>from_failure_handler</type><purpose>Return failure handler currently set for postconditions. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.postconditions"> Postconditions</link> </para>
</para>
</para></description><returns><para>A copy of the failure handler currently set.</para>
</returns></function>
<function name="postcondition_failure"><type>void</type><parameter name="where"><paramtype>from</paramtype><description><para>Operation that failed the contract assertion (e.g., this might be useful to program failure handler functors that never throw from destructors, not even when they are programmed by users to throw exceptions instead of terminating the program).</para></description></parameter><purpose>Call failure handler for postconditions. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify a failure handler that throws exceptions on contract assertion failures (not the default).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.postconditions"> Postconditions</link> </para>
</para>
</para></description></function>
<function name="set_except_failure"><type>from_failure_handler const  &amp;</type><parameter name="f"><paramtype>from_failure_handler const &amp;</paramtype><description><para>New failure handler functor to set.</para></description></parameter><purpose>Set failure handler for exception guarantees. </purpose><description><para>Set a new failure handler and returns it.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>

<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.exception_guarantees"> Exception Guarantees</link> </para>
</para>
</para></description><returns><para>Same failure handler functor <computeroutput>f</computeroutput> passed as parameter (e.g., for concatenating function calls).</para>
</returns></function>
<function name="get_except_failure"><type>from_failure_handler</type><purpose>Return failure handler currently set for exception guarantees. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.exception_guarantees"> Exception Guarantees</link> </para>
</para>
</para></description><returns><para>A copy of the failure handler currently set.</para>
</returns></function>
<function name="except_failure"><type>void</type><parameter name="where"><paramtype>from</paramtype><description><para>Operation that failed the contract assertion.</para></description></parameter><purpose>Call failure handler for exception guarantees. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify a failure handler that throws exceptions on contract assertion failures (not the default), however:</para><para><warning><para>When this failure handler is called there is already an active exception (the one that caused the exception guarantees to be checked in the first place). Therefore, programming this failure handler to throw yet another exception will force C++ to automatically terminate the program.</para>
</warning>

<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.exception_guarantees"> Exception Guarantees</link> </para>
</para>
</para></description></function>
<function name="set_old_failure"><type>from_failure_handler const  &amp;</type><parameter name="f"><paramtype>from_failure_handler const &amp;</paramtype><description><para>New failure handler functor to set.</para></description></parameter><purpose>Set failure handler for old values copied at body. </purpose><description><para>Set a new failure handler and returns it.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>

<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.advanced.old_values_copied_at_body"> Old Values Copied at Body</link> </para>
</para>
</para></description><returns><para>Same failure handler functor <computeroutput>f</computeroutput> passed as parameter (e.g., for concatenating function calls).</para>
</returns></function>
<function name="get_old_failure"><type>from_failure_handler</type><purpose>Return failure handler currently set for old values copied at body. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.advanced.old_values_copied_at_body"> Old Values Copied at Body</link> </para>
</para>
</para></description><returns><para>A copy of the failure handler currently set.</para>
</returns></function>
<function name="old_failure"><type>void</type><parameter name="where"><paramtype>from</paramtype><description><para>Operation that failed the old value copy (e.g., this might be useful to program failure handler functors that never throw from destructors, not even when they are programmed by users to throw exceptions instead of terminating the program).</para></description></parameter><purpose>Call failure handler for old values copied at body. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify a failure handler that throws exceptions on contract assertion failures (not the default).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.advanced.old_values_copied_at_body"> Old Values Copied at Body</link> </para>
</para>
</para></description></function>
<function name="set_entry_invariant_failure"><type>from_failure_handler const  &amp;</type><parameter name="f"><paramtype>from_failure_handler const &amp;</paramtype><description><para>New failure handler functor to set.</para></description></parameter><purpose>Set failure handler for class invariants at entry. </purpose><description><para>Set a new failure handler and returns it.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>

<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link> </para>
</para>
</para></description><returns><para>Same failure handler functor <computeroutput>f</computeroutput> passed as parameter (e.g., for concatenating function calls).</para>
</returns></function>
<function name="get_entry_invariant_failure"><type>from_failure_handler</type><purpose>Return failure handler currently set for class invariants at entry. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link> </para>
</para>
</para></description><returns><para>A copy of the failure handler currently set.</para>
</returns></function>
<function name="entry_invariant_failure"><type>void</type><parameter name="where"><paramtype>from</paramtype><description><para>Operation that failed the contract assertion (e.g., this might be useful to program failure handler functors that never throw from destructors, not even when they are programmed by users to throw exceptions instead of terminating the program).</para></description></parameter><purpose>Call failure handler for class invariants at entry. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify a failure handler that throws exceptions on contract assertion failures (not the default).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link> </para>
</para>
</para></description></function>
<function name="set_exit_invariant_failure"><type>from_failure_handler const  &amp;</type><parameter name="f"><paramtype>from_failure_handler const &amp;</paramtype><description><para>New failure handler functor to set.</para></description></parameter><purpose>Set failure handler for class invariants at exit. </purpose><description><para>Set a new failure handler and returns it.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>

<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link> </para>
</para>
</para></description><returns><para>Same failure handler functor <computeroutput>f</computeroutput> passed as parameter (e.g., for concatenating function calls).</para>
</returns></function>
<function name="get_exit_invariant_failure"><type>from_failure_handler</type><purpose>Return failure handler currently set for class invariants at exit. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link> </para>
</para>
</para></description><returns><para>A copy of the failure handler currently set.</para>
</returns></function>
<function name="exit_invariant_failure"><type>void</type><parameter name="where"><paramtype>from</paramtype><description><para>Operation that failed the contract assertion (e.g., this might be useful to program failure handler functors that never throw from destructors, not even when they are programmed by users to throw exceptions instead of terminating the program).</para></description></parameter><purpose>Call failure handler for class invariants at exit. </purpose><description><para>This is often called only internally by this library.</para><para><emphasis role="bold">Throws:</emphasis> This can throw in case programmers specify a failure handler that throws exceptions on contract assertion failures (not the default).</para><para>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link> </para>
</para>
</para></description></function>
<function name="set_invariant_failure"><type>from_failure_handler const  &amp;</type><parameter name="f"><paramtype>from_failure_handler const &amp;</paramtype><description><para>New failure handler functor to set for both entry and exit invariants.</para></description></parameter><purpose>Set failure handler for class invariants (at both entry and exit). </purpose><description><para>This is provided for convenience and it is equivalent to call both  <computeroutput><functionname alt="boost::contract::set_entry_invariant_failure">boost::contract::set_entry_invariant_failure</functionname></computeroutput> and  <computeroutput><functionname alt="boost::contract::set_exit_invariant_failure">boost::contract::set_exit_invariant_failure</functionname></computeroutput> with the same functor parameter <computeroutput>f</computeroutput>.</para><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept</computeroutput> (or <computeroutput>throw()</computeroutput> before C++11).</para><para>

<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__"> Throw on Failure</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link>,  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link> </para>
</para>
</para></description><returns><para>Same failure handler functor <computeroutput>f</computeroutput> passed as parameter (e.g., for concatenating function calls).</para>
</returns></function>
</namespace>
</namespace>
</header>
<header name="boost/contract/core/specify.hpp">
<para>Specify preconditions, old values copied at body, postconditions, and exception guarantees. </para><para>Preconditions, old values copied at body, postconditions, and exception guarantees are all optionals but, when they are specified, they need to be specified in that order. </para><namespace name="boost">
<namespace name="contract">
<class name="specify_except"><purpose>Allow to specify exception guarantees. </purpose><description><para>Allow to specify the functor this library will call to check exception guarantees. This object is internally constructed by the library when users specify contracts calling <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput> and similar functions (that is why this class does not have a public constructor).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.exception_guarantees"> Exception Guarantees</link> </para>
</para>
</para></description><method-group name="public member functions">
<method name="except"><type><classname>specify_nothing</classname></type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Nullary functor called by this library to check exception guarantees <computeroutput>f()</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::except_failure">boost::contract::except_failure</functionname></computeroutput>). This functor should capture variables by (constant) references (to access the values they will have at function exit).</para></description></parameter><purpose>Allow to specify exception guarantees. </purpose><description><para>

</para></description><returns><para>After exception guarantees have been specified, the object returned by this function does not allow to specify any additional contract. </para>
</returns></method>
</method-group>
<destructor><purpose>Destruct this object. </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept(false)</computeroutput> since C++11 to allow users to program failure handlers that throw exceptions on contract assertion failures (not the default, see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>). </para></description></destructor>
</class><class name="specify_nothing"><purpose>Used to prevent setting other contract conditions after exception guarantees. </purpose><description><para>This class has no member function so it is used to prevent specifying additional functors to check any other contract. This object is internally constructed by the library when users specify contracts calling <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput> and similar functions (that is why this class does not have a public constructor).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial"> Tutorial</link> </para>
</para>
</para></description><method-group name="public member functions">
</method-group>
<destructor><purpose>Destruct this object. </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept(false)</computeroutput> since C++11 to allow users to program failure handlers that throw exceptions on contract assertion failures (not the default, see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>). </para></description></destructor>
</class><class name="specify_old_postcondition_except"><template>
      <template-type-parameter name="VirtualResult"><default>void</default><purpose><para>Return type of the enclosing function declaring the contract if that is either a virtual or an overriding public function, otherwise this is always <computeroutput>void</computeroutput>. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
    </template><purpose>Allow to specify old values copied at body, postconditions, and exception guarantees. </purpose><description><para>Allow to specify functors this library will call to copy old values at body, check postconditions, and check exception guarantees. This object is internally constructed by the library when users specify contracts calling <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput> and similar functions (that is why this class does not have a public constructor).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.old_values_copied_at_body"> Old Values Copied at Body</link>,  <link linkend="boost_contract.tutorial.postconditions"> Postconditions</link>,  <link linkend="boost_contract.tutorial.exception_guarantees"> Exception Guarantees</link></para>
</para>

</para></description><method-group name="public member functions">
<method name="old"><type><classname>specify_postcondition_except</classname>&lt; VirtualResult &gt;</type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Nullary functor called by this library <computeroutput>f()</computeroutput> to assign old value copies just before the body is executed but after entry invariants (when they apply) and preconditions are checked. Old value pointers within this functor call are usually assigned using <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput>. Any exception thrown by a call to this functor will result in this library calling <computeroutput><functionname alt="boost::contract::old_failure">boost::contract::old_failure</functionname></computeroutput> (because old values could not be copied to check postconditions and exception guarantees). This functor should capture old value pointers by references so they can be assigned (all other variables needed to evaluate old value expressions can be captured by (constant) value, or better by (constant) reference to avoid extra copies).</para></description></parameter><purpose>Allow to specify old values copied at body. </purpose><description><para>It should often be sufficient to initialize old value pointers as soon as they are declared, without using this function (see  <link linkend="boost_contract.advanced.old_values_copied_at_body"> Old Values Copied at Body</link>).</para><para>

</para></description><returns><para>After old values copied at body have been specified, the object returned by this function allows to optionally specify postconditions and exception guarantees. </para>
</returns></method>
<method name="postcondition"><type><classname>specify_except</classname></type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Functor called by this library to check postconditions <computeroutput>f()</computeroutput> or <computeroutput>f(result)</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::postcondition_failure">boost::contract::postcondition_failure</functionname></computeroutput>). This functor should capture variables by (constant) references (to access the values they will have at function exit). This functor must be a nullary functor <computeroutput>f()</computeroutput> if <computeroutput>VirtualResult</computeroutput> is <computeroutput>void</computeroutput>, otherwise it must be a unary functor <computeroutput>f(result)</computeroutput> accepting the return value <computeroutput>result</computeroutput> as a parameter of type <computeroutput>VirtualResult const&amp;</computeroutput> (to avoid extra copies of the return value, or of type <computeroutput>VirtualResult</computeroutput> or <computeroutput>VirtualResult const</computeroutput> if extra copies of the return value are irrelevant).</para></description></parameter><purpose>Allow to specify postconditions. </purpose><description><para>

</para></description><returns><para>After postconditions have been specified, the object returned by this function allows to optionally specify exception guarantees. </para>
</returns></method>
<method name="except"><type><classname>specify_nothing</classname></type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Nullary functor called by this library to check exception guarantees <computeroutput>f()</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::except_failure">boost::contract::except_failure</functionname></computeroutput>). This functor should capture variables by (constant) references (to access the values they will have at function exit).</para></description></parameter><purpose>Allow to specify exception guarantees. </purpose><description><para>

</para></description><returns><para>After exception guarantees have been specified, the object returned by this function does not allow to specify any additional contract. </para>
</returns></method>
</method-group>
<destructor><purpose>Destruct this object. </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept(false)</computeroutput> since C++11 to allow users to program failure handlers that throw exceptions on contract assertion failures (not the default, see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>). </para></description></destructor>
</class><class name="specify_postcondition_except"><template>
      <template-type-parameter name="VirtualResult"><default>void</default><purpose><para>Return type of the enclosing function declaring the contract if that is either a virtual or an overriding public function, otherwise this is always <computeroutput>void</computeroutput>. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
    </template><purpose>Allow to specify postconditions or exception guarantees. </purpose><description><para>Allow to specify functors this library will call to check postconditions or exception guarantees. This object is internally constructed by the library when users specify contracts calling <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput> and similar functions (that is why this class does not have a public constructor).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.postconditions"> Postconditions</link>,  <link linkend="boost_contract.tutorial.exception_guarantees"> Exception Guarantees</link></para>
</para>

</para></description><method-group name="public member functions">
<method name="postcondition"><type><classname>specify_except</classname></type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Functor called by this library to check postconditions <computeroutput>f()</computeroutput> or <computeroutput>f(result)</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::postcondition_failure">boost::contract::postcondition_failure</functionname></computeroutput>). This functor should capture variables by (constant) references (to access the values they will have at function exit). This functor must be a nullary functor <computeroutput>f()</computeroutput> if <computeroutput>VirtualResult</computeroutput> is <computeroutput>void</computeroutput>, otherwise it must be a unary functor <computeroutput>f(result)</computeroutput> accepting the return value <computeroutput>result</computeroutput> as a parameter of type <computeroutput>VirtualResult const&amp;</computeroutput> (to avoid extra copies of the return value, or of type <computeroutput>VirtualResult</computeroutput> or <computeroutput>VirtualResult const</computeroutput> if extra copies of the return value are irrelevant).</para></description></parameter><purpose>Allow to specify postconditions. </purpose><description><para>

</para></description><returns><para>After postconditions have been specified, the object returned by this function allows to optionally specify exception guarantees. </para>
</returns></method>
<method name="except"><type><classname>specify_nothing</classname></type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Nullary functor called by this library to check exception guarantees <computeroutput>f()</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::except_failure">boost::contract::except_failure</functionname></computeroutput>). This functor should capture variables by (constant) references (to access the values they will have at function exit).</para></description></parameter><purpose>Allow to specify exception guarantees. </purpose><description><para>

</para></description><returns><para>After exception guarantees have been specified, the object returned by this function does not allow to specify any additional contract. </para>
</returns></method>
</method-group>
<destructor><purpose>Destruct this object. </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept(false)</computeroutput> since C++11 to allow users to program failure handlers that throw exceptions on contract assertion failures (not the default, see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>). </para></description></destructor>
</class><class name="specify_precondition_old_postcondition_except"><template>
      <template-type-parameter name="VirtualResult"><default>void</default><purpose><para>Return type of the enclosing function declaring the contract if that is either a virtual or an overriding public function, otherwise this is always <computeroutput>void</computeroutput>. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
    </template><purpose>Allow to specify preconditions, old values copied at body, postconditions, and exception guarantees. </purpose><description><para>Allow to specify functors this library will call to check preconditions, copy old values at body, check postconditions, and check exception guarantees. This object is internally constructed by the library when users specify contracts calling <computeroutput><functionname alt="boost::contract::function">boost::contract::function</functionname></computeroutput> and similar functions (that is why this class does not have a public constructor).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.preconditions"> Preconditions</link>,  <link linkend="boost_contract.advanced.old_values_copied_at_body"> Old Values Copied at Body</link>,  <link linkend="boost_contract.tutorial.postconditions"> Postconditions</link>,  <link linkend="boost_contract.tutorial.exception_guarantees"> Exception Guarantees</link></para>
</para>

</para></description><method-group name="public member functions">
<method name="precondition"><type><classname>specify_old_postcondition_except</classname>&lt; VirtualResult &gt;</type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Nullary functor called by this library to check preconditions <computeroutput>f()</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::precondition_failure">boost::contract::precondition_failure</functionname></computeroutput>). This functor should capture variables by (constant) value, or better by (constant) reference (to avoid extra copies).</para></description></parameter><purpose>Allow to specify preconditions. </purpose><description><para>

</para></description><returns><para>After preconditions have been specified, the object returned by this function allows to optionally specify old values copied at body, postconditions, and exception guarantees. </para>
</returns></method>
<method name="old"><type><classname>specify_postcondition_except</classname>&lt; VirtualResult &gt;</type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Nullary functor called by this library <computeroutput>f()</computeroutput> to assign old value copies just before the body is executed but after entry invariants (when they apply) and preconditions are checked. Old value pointers within this functor call are usually assigned using <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput>. Any exception thrown by a call to this functor will result in this library calling <computeroutput><functionname alt="boost::contract::old_failure">boost::contract::old_failure</functionname></computeroutput> (because old values could not be copied to check postconditions and exception guarantees). This functor should capture old value pointers by references so they can be assigned (all other variables needed to evaluate old value expressions can be captured by (constant) value, or better by (constant) reference to avoid extra copies).</para></description></parameter><purpose>Allow to specify old values copied at body. </purpose><description><para>It should often be sufficient to initialize old value pointers as soon as they are declared, without using this function (see  <link linkend="boost_contract.advanced.old_values_copied_at_body"> Old Values Copied at Body</link>).</para><para>

</para></description><returns><para>After old values copied at body have been specified, the object returned by this functions allows to optionally specify postconditions and exception guarantees. </para>
</returns></method>
<method name="postcondition"><type><classname>specify_except</classname></type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Functor called by this library to check postconditions <computeroutput>f()</computeroutput> or <computeroutput>f(result)</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::postcondition_failure">boost::contract::postcondition_failure</functionname></computeroutput>). This functor should capture variables by (constant) references (to access the values they will have at function exit). This functor must be a nullary functor <computeroutput>f()</computeroutput> if <computeroutput>VirtualResult</computeroutput> is <computeroutput>void</computeroutput>, otherwise it must be a unary functor <computeroutput>f(result)</computeroutput> accepting the return value <computeroutput>result</computeroutput> as a parameter of type <computeroutput>VirtualResult const&amp;</computeroutput> (to avoid extra copies of the return value, or of type <computeroutput>VirtualResult</computeroutput> or <computeroutput>VirtualResult const</computeroutput> if extra copies of the return value are irrelevant).</para></description></parameter><purpose>Allow to specify postconditions. </purpose><description><para>

</para></description><returns><para>After postconditions have been specified, the object returned by this function allows to optionally specify exception guarantees. </para>
</returns></method>
<method name="except"><type><classname>specify_nothing</classname></type><template>
          <template-type-parameter name="F"/>
        </template><parameter name="f"><paramtype>F const &amp;</paramtype><description><para>Nullary functor called by this library to check exception guarantees <computeroutput>f()</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::except_failure">boost::contract::except_failure</functionname></computeroutput>). This functor should capture variables by (constant) references (to access the values they will have at function exit).</para></description></parameter><purpose>Allow to specify exception guarantees. </purpose><description><para>

</para></description><returns><para>After exception guarantees have been specified, the object returned by this function does not allow to specify any additional contract. </para>
</returns></method>
</method-group>
<destructor><purpose>Destruct this object. </purpose><description><para><emphasis role="bold">Throws:</emphasis> This is declared <computeroutput>noexcept(false)</computeroutput> since C++11 to allow users to program failure handlers that throw exceptions on contract assertion failures (not the default, see  <link linkend="boost_contract.advanced.throw_on_failures__and__noexcept__">
            Throw on Failure</link>). </para></description></destructor>
</class>







































</namespace>
</namespace>
</header>
<header name="boost/contract/core/virtual.hpp">
<para>Handle virtual public functions with contracts (for subcontracting). </para><namespace name="boost">
<namespace name="contract">
<class name="virtual_"><purpose>Type of extra function parameter to handle contracts for virtual public functions (for subcontracting). </purpose><description><para>Virtual public functions (and therefore also public function overrides) declaring contracts using this library must specify an extra function parameter at the very end of their parameter list. This parameter must be a pointer to this class and it must have default value <computeroutput>0</computeroutput> or <computeroutput>nullptr</computeroutput> (this extra parameter is often named <computeroutput>v</computeroutput> in this documentation, but any name can be used):</para><para><programlisting language="c++">class u {
public:
    virtual void f(int x, boost::contract::virtual_* v = 0) { // Declare `v`.
        ... // Contract declaration (which will use `v`) and function body.
    }

    ...
};
</programlisting></para><para>In practice this extra parameter does not alter the calling interface of the enclosing function declaring the contract because it is always the very last parameter and it has a default value (so it can always be omitted when users call the function). This extra parameter must be passed to  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput>, and all other operations of this library that accept a pointer to  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput>. A part from that, this class is not intended to be directly used by programmers (and that is why this class does not have any public member and it is not copyable).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.virtual_public_functions"> Virtual Public Functions</link>,  <link linkend="boost_contract.tutorial.public_function_overrides__subcontracting_">
        Public Function Overrides</link> </para>
</para>
</para></description></class>







































</namespace>
</namespace>
</header>
<header name="boost/contract/destructor.hpp">
<para>Program contracts for destructors. </para><namespace name="boost">
<namespace name="contract">





<function name="destructor"><type><classname>specify_old_postcondition_except</classname></type><template>
          <template-type-parameter name="Class"><purpose><para>The type of the class containing the destructor declaring the contract. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.)</para></purpose></template-type-parameter>
        </template><parameter name="obj"><paramtype>Class *</paramtype><description><para>The object <computeroutput>this</computeroutput> from the scope of the enclosing destructor declaring the contract. (Destructors check all class invariants, including static and volatile invariants, see <link linkend="boost_contract.tutorial.class_invariants">
            Class Invariants</link> and  <link linkend="boost_contract.extras.volatile_public_functions">
            Volatile Public Functions</link>).</para></description></parameter><purpose>Program contracts for destructors. </purpose><description><para>This is used to specify postconditions, exception guarantees, old value copies at body, and check class invariants for destructors (destructors cannot have preconditions, see  <link linkend="boost_contract.contract_programming_overview.destructor_calls"> Destructor Calls</link>):</para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    void invariant() const { // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

public:
    ~u() {
        boost::contract::old_ptr&lt;old_type&gt; old_var;
        boost::contract::check c = boost::contract::destructor(this)
            // No `.precondition` (destructors have no preconditions).
            .old([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(old_expr);
                ...
            })
            .postcondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .except([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ;

        ... // Destructor body.
    }
    
    ...
};
</programlisting></para><para>For optimization, this can be omitted for destructors that do not have postconditions and exception guarantees, within classes that have no invariants.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.destructors"> Destructors</link></para>
</para>



</para></description><returns><para>The result of this function must be assigned to a variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> declared explicitly (i.e., without using C++11 <computeroutput>auto</computeroutput> declarations) and locally just before the code of the destructor body (otherwise this library will generate a run-time error, see <computeroutput><macroname alt="BOOST_CONTRACT_ON_MISSING_CHECK_DECL">BOOST_CONTRACT_ON_MISSING_CHECK_DECL</macroname></computeroutput>). </para>
</returns></function>


































</namespace>
</namespace>
</header>
<header name="boost/contract/function.hpp">
<para>Program contracts for (non-public) functions. </para><namespace name="boost">
<namespace name="contract">






<function name="function"><type><classname>specify_precondition_old_postcondition_except</classname></type><purpose>Program contracts for non-member, private and protected functions. </purpose><description><para>This is used to specify preconditions, postconditions, exception guarantees, and old value copies at body for non-member, private and protected functions (these functions never check class invariants, see  <link linkend="boost_contract.contract_programming_overview.function_calls"> Function Calls</link>):</para><para><programlisting language="c++">void f(...) {
    boost::contract::old_ptr&lt;old_type&gt; old_var;
    boost::contract::check c = boost::contract::function()
        .precondition([&amp;] { // Optional.
            BOOST_CONTRACT_ASSERT(...);
            ...
        })
        .old([&amp;] { // Optional.
            old_var = BOOST_CONTRACT_OLDOF(old_expr);  
            ...
        })
        .postcondition([&amp;] { // Optional.
            BOOST_CONTRACT_ASSERT(...);
            ...
        })
        .except([&amp;] { // Optional.
            BOOST_CONTRACT_ASSERT(...);
            ...
        })
    ;

    ... // Function body.
}
</programlisting></para><para>This can be used also to program contracts in implementation code for lambda functions, loops, and arbitrary blocks of code. For optimization, this can be omitted for code that does not have preconditions, postconditions, and exception guarantees.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.non_member_functions"> Non-Member Functions</link>,  <link linkend="boost_contract.advanced.private_and_protected_functions">
        Private and Protected Functions</link>,  <link linkend="boost_contract.advanced.lambdas__loops__code_blocks__and__constexpr__">
        Lambdas, Loops, Code Blocks</link></para>
</para>

</para></description><returns><para>The result of this function must be assigned to a variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> declared explicitly (i.e., without using C++11 <computeroutput>auto</computeroutput> declarations) and locally just before the code of the function body (otherwise this library will generate a run-time error, see <computeroutput><macroname alt="BOOST_CONTRACT_ON_MISSING_CHECK_DECL">BOOST_CONTRACT_ON_MISSING_CHECK_DECL</macroname></computeroutput>). </para>
</returns></function>

































</namespace>
</namespace>
</header>
<header name="boost/contract/old.hpp">
<para>Handle old values. </para><namespace name="boost">
<namespace name="contract">
<struct name="is_old_value_copyable"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">boost::is_copy_constructible&lt; T &gt;</inherit><purpose>Trait to check if an old value type can be copied or not. </purpose><description><para>By default, this unary boolean meta-function is equivalent to <computeroutput>boost::is_copy_constructible&lt;T&gt;</computeroutput> but programmers can chose to specialize it for user-defined types (in general some kind of specialization is also needed on compilers that do not support C++11, see <ulink url="http://www.boost.org/doc/libs/release/libs/type_traits/doc/html/boost_typetraits/reference/is_copy_constructible.html"><computeroutput>boost::is_copy_constructible</computeroutput></ulink>):</para><para><programlisting language="c++">class u; // Some user-defined type for which old values shall not be copied.

namespace boost { namespace contract {
    template&lt;&gt; // Specialization to not copy old values of type `u`.
    struct is_old_value_copyable&lt;u&gt; : boost::false_type {};
} } // namespace
</programlisting></para><para>A given old value type <computeroutput>T</computeroutput> is copied only if <computeroutput>boost::contract::is_old_value_copyable&lt;T&gt;::value</computeroutput> is <computeroutput>true</computeroutput>. A copyable old value type <computeroutput>V</computeroutput> is always copied using <computeroutput>boost::contract::old_value_copy&lt;V&gt;</computeroutput>. A non-copyable old value type <computeroutput>W</computeroutput> generates a compile-time error when <computeroutput>boost::contract::old_ptr&lt;W&gt;</computeroutput> is dereferenced, but instead leaves <computeroutput>boost::contract::old_ptr_if_copyable&lt;W&gt;</computeroutput> always null (without generating compile-time errors).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.old_value_requirements__templates_">
        Old Value Requirements</link> </para>
</para>
</para></description></struct><class name="old_pointer"><purpose>Convert old value copies into old value pointers. </purpose><description><para>This class is usually only implicitly used by this library and it does not explicitly appear in user code (that is why this class does not have public constructors, etc.). </para></description><method-group name="public member functions">
<method name="conversion-operator"><type>old_ptr_if_copyable&lt; T &gt;</type><template>
          <template-type-parameter name="T"><purpose><para>Type of the pointed old value. The old value pointer will always be null if this type is not copyable (see <computeroutput><classname alt="boost::contract::is_old_value_copyable">boost::contract::is_old_value_copyable</classname></computeroutput>), but this library will not generate a compile-time error. </para></purpose></template-type-parameter>
        </template><purpose>Convert this object to an actual old value pointer for which the old value type <computeroutput>T</computeroutput> might or not be copyable. </purpose><description><para>For example, this is implicitly called when assigning or initializing old value pointers of type <computeroutput><classname alt="boost::contract::old_ptr_if_copyable">boost::contract::old_ptr_if_copyable</classname></computeroutput>.</para><para>
</para></description></method>
<method name="conversion-operator"><type>old_ptr&lt; T &gt;</type><template>
          <template-type-parameter name="T"><purpose><para>Type of the pointed old value. This type must be copyable (see <computeroutput><classname alt="boost::contract::is_old_value_copyable">boost::contract::is_old_value_copyable</classname></computeroutput>), otherwise this library will generate a compile-time error when the old value pointer is dereferenced. </para></purpose></template-type-parameter>
        </template><purpose>Convert this object to an actual old value pointer for which the old value type <computeroutput>T</computeroutput> must be copyable. </purpose><description><para>For example, this is implicitly called when assigning or initializing old value pointers of type <computeroutput><classname alt="boost::contract::old_ptr">boost::contract::old_ptr</classname></computeroutput>.</para><para>
</para></description></method>
</method-group>
</class><class name="old_ptr"><template>
      <template-type-parameter name="T"><purpose><para>Type of the pointed old value. This type must be copyable (i.e., <computeroutput>boost::contract::is_old_value_copyable&lt;T&gt;::value</computeroutput> must be <computeroutput>true</computeroutput>), otherwise this pointer will always be null and this library will generate a compile-time error when the pointer is dereferenced. </para></purpose></template-type-parameter>
    </template><purpose>Old value pointer that requires the pointed old value type to be copyable. </purpose><description><para>This pointer can be set to point an actual old value copy using either  <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput> or <computeroutput><functionname alt="boost::contract::make_old">boost::contract::make_old</functionname></computeroutput> (that is why this class does not have public non-default constructors):</para><para><programlisting language="c++">class u {
public:
    virtual void f(..., boost::contract::virtual_* v = 0) {
        boost::contract::old_ptr&lt;old_type&gt; old_var = // For copyable `old_type`.
                BOOST_CONTRACT_OLDOF(v, old_expr);
        ...
    }

    ...
};
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.old_values"> Old Values</link></para>
</para>

</para></description><typedef name="element_type"><purpose>Pointed old value type. </purpose><type>T</type></typedef>
<method-group name="public member functions">
<method name="operator *" cv="const"><type>T const  &amp;</type><purpose>Dereference this old value pointer. </purpose><description><para>This will generate a run-time error if this pointer is null and a compile-time error if the pointed type <computeroutput>T</computeroutput> is not copyable (i.e., if <computeroutput>boost::contract::is_old_value_copyable&lt;T&gt;::value</computeroutput> is <computeroutput>false</computeroutput>).</para><para>
</para></description><returns><para>The pointed old value. Contract assertions should not change the state of the program so this member function is <computeroutput>const</computeroutput> and it returns the old value as a reference to a constant object (see  <link linkend="boost_contract.contract_programming_overview.constant_correctness">
        Constant Correctness</link>). </para>
</returns></method>
<method name="operator-&gt;" cv="const"><type>T const  *</type><purpose>Structure-dereference this old value pointer. </purpose><description><para>This will generate a compile-time error if the pointed type <computeroutput>T</computeroutput> is not copyable (i.e., if <computeroutput>boost::contract::is_old_value_copyable&lt;T&gt;::value</computeroutput> is <computeroutput>false</computeroutput>).</para><para>
</para></description><returns><para>A pointer to the old value (null if this old value pointer is null). Contract assertions should not change the state of the program so this member function is <computeroutput>const</computeroutput> and it returns the old value as a pointer to a constant object (see  <link linkend="boost_contract.contract_programming_overview.constant_correctness">
        Constant Correctness</link>). </para>
</returns></method>
<method name="conversion-operator" cv="const" specifiers="explicit"><type>bool</type><purpose>Query if this old value pointer is null or not (safe-bool operator). </purpose><description><para>(This is implemented using safe-bool emulation on compilers that do not support C++11 explicit type conversion operators.)</para><para>
</para></description><returns><para>True if this pointer is not null, false otherwise. </para>
</returns></method>
</method-group>
<constructor><purpose>Construct this old value pointer as null. </purpose></constructor>
</class><class name="old_ptr_if_copyable"><template>
      <template-type-parameter name="T"><purpose><para>Type of the pointed old value. If this type is not copyable (i.e., <computeroutput>boost::contract::is_old_value_copyable&lt;T&gt;::value</computeroutput> is <computeroutput>false</computeroutput>), this pointer will always be null (but this library will not generate a compile-time error when this pointer is dereferenced). </para></purpose></template-type-parameter>
    </template><purpose>Old value pointer that does not require the pointed old value type to be copyable. </purpose><description><para>This pointer can be set to point to an actual old value copy using either  <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput> or <computeroutput><functionname alt="boost::contract::make_old">boost::contract::make_old</functionname></computeroutput>:</para><para><programlisting language="c++">template&lt;typename T&gt; // Type `T` might or not be copyable.
class u {
public:
    virtual void f(..., boost::contract::virtual_* v = 0) {
        boost::contract::old_ptr_if_copyable&lt;T&gt; old_var =
                BOOST_CONTRACT_OLDOF(v, old_expr);
        ...
            if(old_var) ... // Always null for non-copyable types.
        ...
    }

    ...
};
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.old_value_requirements__templates_">
        Old Value Requirements</link></para>
</para>

</para></description><typedef name="element_type"><purpose>Pointed old value type. </purpose><type>T</type></typedef>
<method-group name="public member functions">
<method name="operator *" cv="const"><type>T const  &amp;</type><purpose>Dereference this old value pointer. </purpose><description><para>This will generate a run-time error if this pointer is null, but no compile-time error is generated if the pointed type <computeroutput>T</computeroutput> is not copyable (i.e., if <computeroutput>boost::contract::is_old_value_copyable&lt;T&gt;::value</computeroutput> is <computeroutput>false</computeroutput>).</para><para>
</para></description><returns><para>The pointed old value. Contract assertions should not change the state of the program so this member function is <computeroutput>const</computeroutput> and it returns the old value as a reference to a constant object (see  <link linkend="boost_contract.contract_programming_overview.constant_correctness">
        Constant Correctness</link>). </para>
</returns></method>
<method name="operator-&gt;" cv="const"><type>T const  *</type><purpose>Structure-dereference this old value pointer. </purpose><description><para>This will return null but will not generate a compile-time error if the pointed type <computeroutput>T</computeroutput> is not copyable (i.e., if <computeroutput>boost::contract::is_old_value_copyable&lt;T&gt;::value</computeroutput> is <computeroutput>false</computeroutput>).</para><para>
</para></description><returns><para>A pointer to the old value (null if this old value pointer is null). Contract assertions should not change the state of the program so this member function is <computeroutput>const</computeroutput> and it returns the old value as a pointer to a constant object (see  <link linkend="boost_contract.contract_programming_overview.constant_correctness">
        Constant Correctness</link>). </para>
</returns></method>
<method name="conversion-operator" cv="const" specifiers="explicit"><type>bool</type><purpose>Query if this old value pointer is null or not (safe-bool operator). </purpose><description><para>(This is implemented using safe-bool emulation on compilers that do not support C++11 explicit type conversion operators.)</para><para>
</para></description><returns><para>True if this pointer is not null, false otherwise. </para>
</returns></method>
</method-group>
<constructor><purpose>Construct this old value pointer as null. </purpose></constructor>
<constructor><parameter name="other"><paramtype><classname>old_ptr</classname>&lt; T &gt; const &amp;</paramtype><description><para>Old value pointer that requires the old value type to be copyable. </para></description></parameter><purpose>Construct this old value pointer from an old value pointer that requires the old value type to be copyable. </purpose><description><para>Ownership of the pointed value object is transferred to this pointer. This constructor is implicitly called by this library when assigning an object of this type using <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput> (this constructor is usually not explicitly called by user code).</para><para>
</para></description></constructor>
</class><class name="old_value"><purpose>Convert user-specified expressions to old values. </purpose><description><para>This class is usually only implicitly used by this library and it does not explicitly appear in user code.</para><para>On older compilers that cannot correctly deduce the <computeroutput><classname alt="boost::contract::is_old_value_copyable">boost::contract::is_old_value_copyable</classname></computeroutput> trait used in the declaration of this class, programmers can manually specialize that trait to make sure that only old value types that are copyable are actually copied.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.old_value_requirements__templates_">
        Old Value Requirements</link> </para>
</para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><template>
          <template-type-parameter name="T"><purpose><para>Old value type. </para></purpose></template-type-parameter>
        </template><parameter name="old"><paramtype>T const &amp;</paramtype><description><para>Old value to be copied.</para></description></parameter><parameter name=""><paramtype>typename boost::enable_if&lt; <classname>boost::contract::is_old_value_copyable</classname>&lt; T &gt; &gt;::type *</paramtype><default>0</default></parameter><purpose>Construct this object from the specified old value when the old value type is copy constructible. </purpose><description><para>The specified old value <computeroutput>old</computeroutput> is copied (one time only) using <computeroutput><classname alt="boost::contract::old_value_copy">boost::contract::old_value_copy</classname></computeroutput>, in which case the related old value pointer will not be null (but no copy is made if postconditions and exception guarantees are not being checked, see  <computeroutput><macroname alt="BOOST_CONTRACT_NO_OLDS">BOOST_CONTRACT_NO_OLDS</macroname></computeroutput>).</para><para>

</para></description></constructor>
<constructor><template>
          <template-type-parameter name="T"><purpose><para>Old value type. </para></purpose></template-type-parameter>
        </template><parameter name="old"><paramtype>T const &amp;</paramtype><description><para>Old value (that will not be copied in this case).</para></description></parameter><parameter name=""><paramtype>typename boost::disable_if&lt; <classname>boost::contract::is_old_value_copyable</classname>&lt; T &gt; &gt;::type *</paramtype><default>0</default></parameter><purpose>Construct this object from the specified old value when the old value type is not copyable. </purpose><description><para>The specified old value <computeroutput>old</computeroutput> cannot be copied in this case so it is not copied and the related old value pointer will always be null (thus calls to this constructor have no effect and they will likely be optimized away by most compilers).</para><para>

</para></description></constructor>
</class><struct name="old_value_copy"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Trait to copy an old value. </purpose><description><para>By default, the implementation of this trait uses <computeroutput>T's</computeroutput> copy constructor to make one single copy of the specified value. However, programmers can specialize this trait to copy old values using user-specific operations different from <computeroutput>T's</computeroutput> copy constructor. The default implementation of this trait is equivalent to:</para><para><programlisting language="c++">template&lt;typename T&gt;
class old_value_copy {
public:
    explicit old_value_copy(T const&amp; old) :
        old_(old) // One single copy of value using T's copy constructor.
    {}

    T const&amp; old() const { return old_; }

private:
    T const old_; // The old value copy.
};
</programlisting></para><para>This library will instantiate and use this trait only on old value types <computeroutput>T</computeroutput> that are copyable (i.e., for which <computeroutput>boost::contract::is_old_value_copyable&lt;T&gt;::value</computeroutput> is <computeroutput>true</computeroutput>).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.old_value_requirements__templates_">
        Old Value Requirements</link> </para>
</para>
</para></description><method-group name="public member functions">
<method name="old" cv="const"><type>T const  &amp;</type><purpose>Return a (constant) reference to the old value that was copied. </purpose><description><para>Contract assertions should not change the state of the program so the old value copy is returned as <computeroutput>const</computeroutput> (see  <link linkend="boost_contract.contract_programming_overview.constant_correctness">
Constant Correctness</link>). </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="old"><paramtype>T const &amp;</paramtype><description><para>The old value to copy. </para></description></parameter><purpose>Construct this object by making one single copy of the specified old value. </purpose><description><para>This is the only operation within this library that actually copies old values. This ensures this library makes one and only one copy of an old value (if they actually need to be copied, see <computeroutput><macroname alt="BOOST_CONTRACT_NO_OLDS">BOOST_CONTRACT_NO_OLDS</macroname></computeroutput>).</para><para>
</para></description></constructor>
</struct>






<function name="null_old"><type><classname>old_value</classname></type><purpose>Return a "null" old value. </purpose><description><para>The related old value pointer will also be null. This function is usually only called by the code expanded by  <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput>.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link></para>
</para>

</para></description><returns><para>Null old value. </para>
</returns></function>
<function name="make_old"><type><classname>old_pointer</classname></type><parameter name="old"><paramtype><classname>old_value</classname> const &amp;</paramtype><description><para>Old value which is usually implicitly constructed from the user old value expression to be copied (use the ternary operator <computeroutput>?:</computeroutput> to completely avoid to evaluate the old value expression when <computeroutput>boost::contract::copy_old()</computeroutput> is <computeroutput>false</computeroutput>).</para></description></parameter><purpose>Make an old value pointer (but not for virtual public functions and public functions overrides). </purpose><description><para>The related old value pointer will not be null if the specified old value was actually copied. This function is usually only called by code expanded by <computeroutput>BOOST_CONTRACT_OLDOF(old_expr)</computeroutput> as in:</para><para><programlisting language="c++">boost::contract::make_old(boost::contract::copy_old() ? old_expr :
        boost::contract::null_old())
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link></para>
</para>


</para></description><returns><para>Old value pointer (usually implicitly converted to either  <computeroutput><classname alt="boost::contract::old_ptr">boost::contract::old_ptr</classname></computeroutput> or  <computeroutput><classname alt="boost::contract::old_ptr_if_copyable">boost::contract::old_ptr_if_copyable</classname></computeroutput> in user code). </para>
</returns></function>
<function name="make_old"><type><classname>old_pointer</classname></type><parameter name="v"><paramtype><classname>virtual_</classname> *</paramtype><description><para>The trailing parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing virtual or overriding public function declaring the contract. </para></description></parameter><parameter name="old"><paramtype><classname>old_value</classname> const &amp;</paramtype><description><para>Old value which is usually implicitly constructed from the user old value expression to be copied (use the ternary operator <computeroutput>?:</computeroutput> to completely avoid to evaluate the old value expression when <computeroutput>boost::contract::copy_old(v)</computeroutput> is <computeroutput>false</computeroutput>).</para></description></parameter><purpose>Make an old value pointer (for virtual public functions and public functions overrides). </purpose><description><para>The related old value pointer will not be null if the specified old value was actually copied. This function is usually only called by code expanded by <computeroutput>BOOST_CONTRACT_OLDOF(v, old_expr)</computeroutput> as in:</para><para><programlisting language="c++">boost::contract::make_old(v, boost::contract::copy_old(v) ? old_expr :
        boost::contract::null_old())
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link></para>
</para>


</para></description><returns><para>Old value pointer (usually implicitly converted to either  <computeroutput><classname alt="boost::contract::old_ptr">boost::contract::old_ptr</classname></computeroutput> or  <computeroutput><classname alt="boost::contract::old_ptr_if_copyable">boost::contract::old_ptr_if_copyable</classname></computeroutput> in user code). </para>
</returns></function>
<function name="copy_old"><type>bool</type><purpose>Query if old values need to be copied (but not for virtual public functions and public function overrides). </purpose><description><para>For example, this function always returns false when both postconditions and exception guarantees are not being checked (see  <computeroutput><macroname alt="BOOST_CONTRACT_NO_OLDS">BOOST_CONTRACT_NO_OLDS</macroname></computeroutput>). This function is usually only called by the code expanded by  <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput>.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link></para>
</para>

</para></description><returns><para>True if old values need to be copied, false otherwise. </para>
</returns></function>
<function name="copy_old"><type>bool</type><parameter name="v"><paramtype><classname>virtual_</classname> *</paramtype><description><para>The trailing parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing virtual or overriding public function declaring the contract.</para></description></parameter><purpose>Query if old values need to be copied (for virtual public functions and public function overrides). </purpose><description><para>For example, this function always returns false when both postconditions and exception guarantees are not being checked (see  <computeroutput><macroname alt="BOOST_CONTRACT_NO_OLDS">BOOST_CONTRACT_NO_OLDS</macroname></computeroutput>). In addition, this function returns false when overridden functions are being called subsequent times by this library to support subcontracting. This function is usually only called by the code expanded by  <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput>.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link></para>
</para>


</para></description><returns><para>True if old values need to be copied, false otherwise. </para>
</returns></function>




























</namespace>
</namespace>
<macro name="BOOST_CONTRACT_OLDOF" kind="functionlike"><macro-parameter name="..."/><purpose>Macro typically used to copy an old value expression and assign it to an old value pointer. </purpose><description><para>The expression expanded by this macro should be assigned to an old value pointer of type <computeroutput><classname alt="boost::contract::old_ptr">boost::contract::old_ptr</classname></computeroutput> or  <computeroutput><classname alt="boost::contract::old_ptr_if_copyable">boost::contract::old_ptr_if_copyable</classname></computeroutput>. This is an overloaded variadic macro and it can be used in the following different ways.</para><para>1. From within virtual public functions and public functions overrides:</para><para><programlisting language="c++">BOOST_CONTRACT_OLDOF(v, old_expr)
</programlisting></para><para>2. From all other operations:</para><para><programlisting language="c++">BOOST_CONTRACT_OLDOF(old_expr)
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">v</emphasis></computeroutput> is the extra parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing virtual public function or public function overrides declaring the contract. </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">old_expr</emphasis></computeroutput> is the expression to be evaluated and copied into the old value pointer. (This is not a variadic macro parameter so any comma it might contain must be protected by round parenthesis and <computeroutput>BOOST_CONTRACT_OLDOF(v, (old_expr))</computeroutput> will always work.)</para>
</listitem>
</itemizedlist>
On compilers that do not support variadic macros, programmers can manually copy old value expressions without using this macro (see  <link linkend="boost_contract.extras.no_macros__and_no_variadic_macros_"> No Macros</link>).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.old_values"> Old Values</link> </para>
</para>
</para></description></macro>
</header>
<header name="boost/contract/override.hpp">
<para>Handle public function overrides (for subcontracting). </para><macro name="BOOST_CONTRACT_NAMED_OVERRIDE" kind="functionlike"><macro-parameter name="type_name"><description><para>Name of the override type trait this macro will declare. (This is not a variadic macro parameter but it should never contain commas because it is an identifier.) </para></description></macro-parameter><macro-parameter name="func_name"><description><para>Function name of the public function override. This macro is called just once even if the function name is overloaded (the same override type trait is used for all overloaded functions with the same name, see  <link linkend="boost_contract.advanced.function_overloads">
                    Function Overloads</link>). (This is not a variadic macro parameter but it should never contain commas because it is an identifier.) </para></description></macro-parameter><purpose>Declare an override type trait with an arbitrary name. </purpose><description><para>Declare the override type trait named <computeroutput>type_name</computeroutput> to pass as an explicit template parameter to <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput> for public function overrides.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.advanced.named_overrides"> Named Overrides</link></para>
</para>

</para></description></macro>
<macro name="BOOST_CONTRACT_OVERRIDE" kind="functionlike"><macro-parameter name="func_name"><description><para>Function name of the public function override. This macro is called just once even if the function name is overloaded (the same override type trait is used for all overloaded functions with the same name, see  <link linkend="boost_contract.advanced.function_overloads"> Function Overloads</link>). (This is not a variadic macro parameter but it should never contain any comma because it is an identifier.) </para></description></macro-parameter><purpose>Declare an override type trait named <computeroutput>override_<emphasis>func_name</emphasis></computeroutput>. </purpose><description><para>Declare the override type trait named <computeroutput>override_<emphasis>func_name</emphasis></computeroutput> to pass as an explicit template parameter to <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput> for public function overrides. Use <computeroutput><macroname alt="BOOST_CONTRACT_NAMED_OVERRIDE">BOOST_CONTRACT_NAMED_OVERRIDE</macroname></computeroutput> to generate an override type trait with a name different than <computeroutput>override_<emphasis>func_name</emphasis></computeroutput> (usually not needed).</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.public_function_overrides__subcontracting_">
        Public Function Overrides</link></para>
</para>

</para></description></macro>
<macro name="BOOST_CONTRACT_OVERRIDES" kind="functionlike"><macro-parameter name="..."><description><para>A comma separated list of one or more function names of public function overrides. (Each function name should never contain commas because it is an identifier.) </para></description></macro-parameter><purpose>Declare multiple override type traits at once naming them <computeroutput>override_...</computeroutput> (for convenience). </purpose><description><para>This variadic macro is provided for convenience as <computeroutput>BOOST_CONTRACT_OVERRIDES(f_1, f_2, ..., f_n)</computeroutput> expands to code equivalent to:</para><para><programlisting language="c++">BOOST_CONTRACT_OVERRIDE(f_1)
BOOST_CONTRACT_OVERRIDE(f_2)
...
BOOST_CONTRACT_OVERRIDE(f_n)
</programlisting></para><para>On compilers that do not support variadic macros, the override type traits can be equivalently programmed one-by-one calling  <computeroutput><macroname alt="BOOST_CONTRACT_OVERRIDE">BOOST_CONTRACT_OVERRIDE</macroname></computeroutput> for each function name as shown above.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.public_function_overrides__subcontracting_">
        Public Function Overrides</link></para>
</para>

</para></description></macro>
</header>
<header name="boost/contract/public_function.hpp">
<para>Program contracts for public functions (including subcontracting). </para><para>The different overloads handle public functions that are static, virtual void, virtual non-void, overriding void, and overriding non-void. </para><namespace name="boost">
<namespace name="contract">












<function name="public_function"><type><classname>specify_precondition_old_postcondition_except</classname></type><template>
          <template-type-parameter name="Class"><purpose><para>The type of the class containing the static public function declaring the contract. This template parameter must be explicitly specified for static public functions (because they have no object <computeroutput>this</computeroutput> so there is no function argument from which this type template parameter can be automatically deduced by C++).</para></purpose></template-type-parameter>
        </template><purpose>Program contracts for static public functions. </purpose><description><para>This is used to specify preconditions, postconditions, exception guarantees, old value copies at body, and check static class invariants for static public functions:</para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    static void static_invariant() { // Optional (as for non-static).
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

public:
    static void f(...) {
        boost::contract::old_ptr&lt;old_type&gt; old_var;
        boost::contract::check c = boost::contract::public_function&lt;u&gt;()
            .precondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .old([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(old_expr);
                ...
            })
            .postcondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .except([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ;

        ... // Function body.
    }
    
    ...
};
</programlisting></para><para>For optimization, this can be omitted for static public functions that do not have preconditions, postconditions and exception guarantees, within classes that have no static invariants.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.static_public_functions"> Static Public Functions</link></para>
</para>


</para></description><returns><para>The result of this function must be assigned to a variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> declared explicitly (i.e., without using C++11 <computeroutput>auto</computeroutput> declarations) and locally just before the code of the static public function body (otherwise this library will generate a run-time error, see <computeroutput><macroname alt="BOOST_CONTRACT_ON_MISSING_CHECK_DECL">BOOST_CONTRACT_ON_MISSING_CHECK_DECL</macroname></computeroutput>). </para>
</returns></function>
<function name="public_function"><type><classname>specify_precondition_old_postcondition_except</classname></type><template>
          <template-type-parameter name="Class"><purpose><para>The type of the class containing the public function declaring the contract. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.)</para></purpose></template-type-parameter>
        </template><parameter name="obj"><paramtype>Class *</paramtype><description><para>The object <computeroutput>this</computeroutput> from the scope of the enclosing public function declaring the contract. This object might be mutable, <computeroutput>const</computeroutput>, <computeroutput>volatile</computeroutput>, or <computeroutput>const volatile</computeroutput> depending on the cv-qualifier of the enclosing function (volatile public functions will check volatile class invariants, see  <link linkend="boost_contract.extras.volatile_public_functions">
            Volatile Public Functions</link>).</para></description></parameter><purpose>Program contracts for public functions that are not static, not virtual, and do not not override. </purpose><description><para>This is used to specify preconditions, postconditions, exception guarantees, old value copies at body, and check class invariants for public functions that are not static, not virtual, and do not override:</para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    void invariant() const { // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

public:
    void f(...) {
        boost::contract::old_ptr&lt;old_type&gt; old_var;
        boost::contract::check c = boost::contract::public_function(this)
            .precondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .old([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(old_expr);
                ...
            })
            .postcondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .except([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ;

        ... // Function body.
    }
    
    ...
};
</programlisting></para><para>For optimization, this can be omitted for public functions that do not have preconditions, postconditions and exception guarantees, within classes that have no invariants.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.public_functions"> Public Functions</link></para>
</para>



</para></description><returns><para>The result of this function must be assigned to a variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> declared explicitly (i.e., without using C++11 <computeroutput>auto</computeroutput> declarations) and locally just before the code of the public function body (otherwise this library will generate a run-time error, see <computeroutput><macroname alt="BOOST_CONTRACT_ON_MISSING_CHECK_DECL">BOOST_CONTRACT_ON_MISSING_CHECK_DECL</macroname></computeroutput>). </para>
</returns></function>
<function name="public_function"><type><classname>specify_precondition_old_postcondition_except</classname></type><template>
          <template-type-parameter name="Class"><purpose><para>The type of the class containing the virtual public function declaring the contract. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.)</para></purpose></template-type-parameter>
        </template><parameter name="v"><paramtype><classname>virtual_</classname> *</paramtype><description><para>The trailing parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing virtual public function. </para></description></parameter><parameter name="obj"><paramtype>Class *</paramtype><description><para>The object <computeroutput>this</computeroutput> from the scope of the enclosing virtual public function declaring the contract. This object might be mutable, <computeroutput>const</computeroutput>, <computeroutput>volatile</computeroutput>, or <computeroutput>const volatile</computeroutput> depending on the cv-qualifier of the enclosing function (volatile public functions will check volatile class invariants, see  <link linkend="boost_contract.extras.volatile_public_functions">
            Volatile Public Functions</link>).</para></description></parameter><purpose>Program contracts for void virtual public functions that do not override. </purpose><description><para>This is used to specify preconditions, postconditions, exception guarantees, old value copies at body, and check class invariants for public functions that are virtual, do not override, and return <computeroutput>void:</computeroutput> </para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    void invariant() const { // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

public:
    void f(..., boost::contract::virtual_* v = 0) {
        boost::contract::old_ptr&lt;old_type&gt; old_var;
        boost::contract::check c = boost::contract::public_function(v, this)
            .precondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .old([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(v, old_expr);
                ...
            })
            .postcondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .except([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ;

        ... // Function body.
    }
    
    ...
};
</programlisting></para><para>A virtual public function should always call  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput> otherwise this library will not be able to correctly use it for subcontracting.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.virtual_public_functions"> Virtual Public Functions</link></para>
</para>



</para></description><returns><para>The result of this function must be assigned to a variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> declared explicitly (i.e., without using C++11 <computeroutput>auto</computeroutput> declarations) and locally just before the code of the public function body (otherwise this library will generate a run-time error, see  <computeroutput><macroname alt="BOOST_CONTRACT_ON_MISSING_CHECK_DECL">BOOST_CONTRACT_ON_MISSING_CHECK_DECL</macroname></computeroutput>). </para>
</returns></function>
<function name="public_function"><type><classname>specify_precondition_old_postcondition_except</classname>&lt; VirtualResult &gt;</type><template>
          <template-type-parameter name="VirtualResult"><purpose><para>This type must be the same as, or compatible with, the return type of the enclosing virtual public function declaring the contract (this library might not be able to generate a compile-time error if these types mismatch, but in general that will cause run-time errors or undefined behaviour). Alternatively, <computeroutput>boost::optional&lt;<emphasis>return-type</emphasis>&gt;</computeroutput> can also be used (see  <link linkend="boost_contract.advanced.optional_return_values">
                        Optional Return Values</link>). (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
          <template-type-parameter name="Class"><purpose><para>The type of the class containing the virtual public function declaring the contract. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.)</para></purpose></template-type-parameter>
        </template><parameter name="v"><paramtype><classname>virtual_</classname> *</paramtype><description><para>The trailing parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing virtual public function. </para></description></parameter><parameter name="r"><paramtype>VirtualResult &amp;</paramtype><description><para>A reference to the return value of the enclosing virtual public function declaring the contract. This is usually a local variable declared by the enclosing virtual public function just before the contract, but programmers must set it to the actual value being returned by the function at each <computeroutput>return</computeroutput> statement. </para></description></parameter><parameter name="obj"><paramtype>Class *</paramtype><description><para>The object <computeroutput>this</computeroutput> from the scope of the enclosing virtual public function declaring the contract. This object might be mutable, <computeroutput>const</computeroutput>, <computeroutput>volatile</computeroutput>, or <computeroutput>const volatile</computeroutput> depending on the cv-qualifier of the enclosing function (volatile public functions will check volatile class invariants, see  <link linkend="boost_contract.extras.volatile_public_functions">
            Volatile Public Functions</link>).</para></description></parameter><purpose>Program contracts for non-void virtual public functions that do not override. </purpose><description><para>This is used to specify preconditions, postconditions, exception guarantees, old value copies at body, and check class invariants for public functions that are virtual, do not override, and do not return <computeroutput>void:</computeroutput> </para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    void invariant() const { // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

public:
    t f(..., boost::contract::virtual_* v = 0) {
        t result;
        boost::contract::old_ptr&lt;old_type&gt; old_var;
        boost::contract::check c = boost::contract::public_function(
                v, result, this)
            .precondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .old([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(v, old_expr);
                ...
            })
            .postcondition([&amp;] (t const&amp; result) { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .except([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ;

        ... // Function body (use `return result = return_expr`).
    }
    
    ...
};
</programlisting></para><para>A virtual public function should always call  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput> otherwise this library will not be able to correctly use it for subcontracting.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.virtual_public_functions"> Virtual Public Functions</link></para>
</para>



</para></description><returns><para>The result of this function must be assigned to a variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> declared explicitly (i.e., without using C++11 <computeroutput>auto</computeroutput> declarations) and locally just before the code of the public function body (otherwise this library will generate a run-time error, see  <computeroutput><macroname alt="BOOST_CONTRACT_ON_MISSING_CHECK_DECL">BOOST_CONTRACT_ON_MISSING_CHECK_DECL</macroname></computeroutput>). </para>
</returns></function>
<function name="public_function"><type><classname>specify_precondition_old_postcondition_except</classname></type><template>
          <template-type-parameter name="Override"><purpose><para>The type trait <computeroutput>override_<emphasis>function-name</emphasis></computeroutput> declared using the <computeroutput><macroname alt="BOOST_CONTRACT_OVERRIDE">BOOST_CONTRACT_OVERRIDE</macroname></computeroutput> or related macros. This template parameter must be explicitly specified (because there is no function argument from which it can be automatically deduced by C++). </para></purpose></template-type-parameter>
          <template-type-parameter name="F"><purpose><para>The function pointer type of the enclosing public function override declaring the contract. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
          <template-type-parameter name="Class"><purpose><para>The type of the class containing the virtual public function declaring the contract. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
          <template-nontype-parameter name="Args"><type>typename...</type><purpose><para>The types of all parameters passed to the enclosing public function override declaring the contract, but excluding the trailing parameter type <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname>*</computeroutput>. On compilers that do not support variadic templates, this library internally implements this function using preprocessor meta-programming (in this case, the maximum number of supported arguments is defined by  <computeroutput><macroname alt="BOOST_CONTRACT_MAX_ARGS">BOOST_CONTRACT_MAX_ARGS</macroname></computeroutput>). (Usually these template parameters are automatically deduced by C++ and they do not need to be explicitly specified by programmers.)</para></purpose></template-nontype-parameter>
        </template><parameter name="v"><paramtype><classname>virtual_</classname> *</paramtype><description><para>The trailing parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing public function override. </para></description></parameter><parameter name="f"><paramtype>F</paramtype><description><para>A pointer to the enclosing public function override declaring the contract (but see <link linkend="boost_contract.advanced.function_overloads">
            Function Overloads</link>). </para></description></parameter><parameter name="obj"><paramtype>Class *</paramtype><description><para>The object <computeroutput>this</computeroutput> from the scope of the enclosing public function override declaring the contract. This object might be mutable, <computeroutput>const</computeroutput>, <computeroutput>volatile</computeroutput>, or <computeroutput>const volatile</computeroutput> depending on the cv-qualifier of the enclosing function (volatile public functions will check volatile class invariants, see  <link linkend="boost_contract.extras.volatile_public_functions">
            Volatile Public Functions</link>). </para></description></parameter><parameter name="args"><paramtype>Args &amp;...</paramtype><description><para>All arguments passed to the enclosing public function override declaring the contract (by reference and in the order they appear in the enclosing function declaration), but excluding the trailing argument <computeroutput>v</computeroutput>.</para></description></parameter><purpose>Program contracts for void public functions overrides (virtual or not). </purpose><description><para>This is used to specify preconditions, postconditions, exception guarantees, old value copies at body, and check class invariants for public function overrides (virtual or not) that return <computeroutput>void:</computeroutput> </para><para><programlisting language="c++">class u
    #define BASES private boost::contract::constructor_precondition&lt;u&gt;, \
            public b, private w
    : BASES
{
    friend class boost::contract::access;

    typedef BOOST_CONTRACT_BASE_TYPES(BASES) base_types;
    #undef BASES

    void invariant() const { // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

    BOOST_CONTRACT_OVERRIDES(f)

public:
    // Override from `b::f`.
    void f(t_1 a_1, ..., t_n a_n, boost::contract::virtual_* v = 0) {
        boost::contract::old_ptr&lt;old_type&gt; old_var;
        boost::contract::check c = boost::contract::public_function&lt;
                override_f&gt;(v, &amp;u::f, this, a_1, ..., a_n)
            .precondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .old([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(v, old_expr);
                ...
            })
            .postcondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .except([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ;

        ... // Function body.
    }
    
    ...
};
</programlisting></para><para>A public function override should always call  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput> otherwise this library will not be able to correctly use it for subcontracting.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.public_function_overrides__subcontracting_">
        Public Function Overrides</link></para>
</para>



</para></description><returns><para>The result of this function must be assigned to a variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> declared explicitly (i.e., without using C++11 <computeroutput>auto</computeroutput> declarations) and locally just before the code of the public function body (otherwise this library will generate a run-time error, see  <computeroutput><macroname alt="BOOST_CONTRACT_ON_MISSING_CHECK_DECL">BOOST_CONTRACT_ON_MISSING_CHECK_DECL</macroname></computeroutput>). </para>
</returns></function>
<function name="public_function"><type><classname>specify_precondition_old_postcondition_except</classname>&lt; VirtualResult &gt;</type><template>
          <template-type-parameter name="Override"><purpose><para>The type trait <computeroutput>override_<emphasis>function-name</emphasis></computeroutput> declared using the <computeroutput><macroname alt="BOOST_CONTRACT_OVERRIDE">BOOST_CONTRACT_OVERRIDE</macroname></computeroutput> or related macros. This template parameter must be explicitly specified (because there is no function argument from which it can be automatically deduced by C++). </para></purpose></template-type-parameter>
          <template-type-parameter name="VirtualResult"><purpose><para>This type must be the same as, or compatible with, the return type of the enclosing public function override declaring the contract (this library might not be able to generate a compile-time error if these types mismatch, but in general that will cause run-time errors or undefined behaviour). Alternatively, <computeroutput>boost::optional&lt;<emphasis>return-type</emphasis>&gt;</computeroutput> can also be used (see  <link linkend="boost_contract.advanced.optional_return_values">
                        Optional Return Values</link>). (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
          <template-type-parameter name="F"><purpose><para>The function pointer type of the enclosing public function override declaring the contract. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
          <template-type-parameter name="Class"><purpose><para>The type of the class containing the virtual public function declaring the contract. (Usually this template parameter is automatically deduced by C++ and it does not need to be explicitly specified by programmers.) </para></purpose></template-type-parameter>
          <template-nontype-parameter name="Args"><type>typename...</type><purpose><para>The types of all parameters passed to the enclosing public function override declaring the contract, but excluding the trailing parameter type <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname>*</computeroutput>. On compilers that do not support variadic templates, this library internally implements this function using preprocessor meta-programming (in this case, the maximum number of supported arguments is defined by  <computeroutput><macroname alt="BOOST_CONTRACT_MAX_ARGS">BOOST_CONTRACT_MAX_ARGS</macroname></computeroutput>). (Usually these template parameters are automatically deduced by C++ and they do not need to be explicitly specified by programmers.)</para></purpose></template-nontype-parameter>
        </template><parameter name="v"><paramtype><classname>virtual_</classname> *</paramtype><description><para>The trailing parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing public function override. </para></description></parameter><parameter name="r"><paramtype>VirtualResult &amp;</paramtype><description><para>A reference to the return value of the enclosing public function override declaring the contract. This is usually a local variable declared by the enclosing public function override just before the contract, but programmers must set it to the actual value being returned by the function at each <computeroutput>return</computeroutput> statement. </para></description></parameter><parameter name="f"><paramtype>F</paramtype><description><para>A pointer to the enclosing public function override declaring the contract (but see <link linkend="boost_contract.advanced.function_overloads">
            Function Overloads</link>). </para></description></parameter><parameter name="obj"><paramtype>Class *</paramtype><description><para>The object <computeroutput>this</computeroutput> from the scope of the enclosing public function override declaring the contract. This object might be mutable, <computeroutput>const</computeroutput>, <computeroutput>volatile</computeroutput>, or <computeroutput>const volatile</computeroutput> depending on the cv-qualifier of the enclosing function (volatile public functions will check volatile class invariants, see  <link linkend="boost_contract.extras.volatile_public_functions">
            Volatile Public Functions</link>). </para></description></parameter><parameter name="args"><paramtype>Args &amp;...</paramtype><description><para>All arguments passed to the enclosing public function override declaring the contract (by reference and in the order they appear in the enclosing function declaration), but excluding the trailing argument <computeroutput>v</computeroutput>.</para></description></parameter><purpose>Program contracts for non-void public functions overrides (virtual or not). </purpose><description><para>This is used to specify preconditions, postconditions, exception guarantees, old value copies at body, and check class invariants for public function overrides (virtual or not) that do not return <computeroutput>void:</computeroutput> </para><para><programlisting language="c++">class u
    #define BASES private boost::contract::constructor_precondition&lt;u&gt;, \
            public b, private w
    : BASES
{
    friend class boost::contract::access;

    typedef BOOST_CONTRACT_BASE_TYPES(BASES) base_types;
    #undef BASES

    void invariant() const { // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    }

    BOOST_CONTRACT_OVERRIDES(f)

public:
    // Override from `b::f`.
    t f(t_1 a_1, ..., t_n a_n, boost::contract::virtual_* v = 0) {
        t result;
        boost::contract::old_ptr&lt;old_type&gt; old_var;
        boost::contract::check c = boost::contract::public_function&lt;
                override_f&gt;(v, result, &amp;u::f, this, a_1, ..., a_n)
            .precondition([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .old([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(v, old_expr);
                ...
            })
            .postcondition([&amp;] (t const&amp; result) { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            .except([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ;

        ... // Function body (use `return result = return_expr`).
    }
    
    ...
};
</programlisting></para><para>A public function override should always call  <computeroutput><functionname alt="boost::contract::public_function">boost::contract::public_function</functionname></computeroutput> otherwise this library will not be able to correctly use it for subcontracting.</para><para><para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.tutorial.public_function_overrides__subcontracting_">
        Public Function Overrides</link></para>
</para>



</para></description><returns><para>The result of this function must be assigned to a variable of type  <computeroutput><classname alt="boost::contract::check">boost::contract::check</classname></computeroutput> declared explicitly (i.e., without using C++11 <computeroutput>auto</computeroutput> declarations) and locally just before the code of the public function body (otherwise this library will generate a run-time error, see  <computeroutput><macroname alt="BOOST_CONTRACT_ON_MISSING_CHECK_DECL">BOOST_CONTRACT_ON_MISSING_CHECK_DECL</macroname></computeroutput>). </para>
</returns></function>






















</namespace>
</namespace>
</header>
<header name="boost/contract_macro.hpp">
<para>Allow to disable contracts to completely remove their compile-time and run-time overhead. </para><para>This header automatically includes all header files <computeroutput>boost/contract/*.hpp</computeroutput> necessary to use its macros.</para><para>Almost all the macros defined in this header file are variadic macros. On compilers that do not support variadic macros, programmers can manually code <computeroutput>#ifndef BOOST_CONTRACT_NO_...</computeroutput> statements instead (see  <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
Disable Contract Compilation</link>). </para><macro name="BOOST_CONTRACT_PRECONDITION" kind="functionlike"><macro-parameter name="..."/><purpose>Program preconditions that can be completely disabled at compile-time. </purpose><description><para><computeroutput>BOOST_CONTRACT_PRECONDITION(f)</computeroutput> expands to code equivalent to the following (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_PRECONDITIONS">BOOST_CONTRACT_NO_PRECONDITIONS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_PRECONDITIONS
    .precondition(f)
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">f</emphasis></computeroutput> is the nullay functor called by this library to check preconditions <computeroutput>f()</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::precondition_failure">boost::contract::precondition_failure</functionname></computeroutput>). This functor should capture variables by (constant) value, or better by (constant) reference (to avoid extra copies). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.preconditions"> Preconditions</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_POSTCONDITION" kind="functionlike"><macro-parameter name="..."/><purpose>Program postconditions that can be completely disabled at compile-time. </purpose><description><para><computeroutput>BOOST_CONTRACT_POSTCONDITION(f)</computeroutput> expands to code equivalent to the following (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_POSTCONDITIONS">BOOST_CONTRACT_NO_POSTCONDITIONS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_POSTCONDITIONS
    .postcondition(f)
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">f</emphasis></computeroutput> is the functor called by this library to check postconditions <computeroutput>f()</computeroutput> or <computeroutput>f(result)</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::postcondition_failure">boost::contract::postcondition_failure</functionname></computeroutput>). This functor should capture variables by (constant) references (to access the values they will have at function exit). This functor takes the return value (preferably by <computeroutput>const&amp;</computeroutput>) <computeroutput>result</computeroutput> as its one single parameter <computeroutput>f(result)</computeroutput> but only for virtual public functions and public functions overrides, otherwise it takes no parameter <computeroutput>f()</computeroutput>. (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.postconditions"> Postconditions</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_EXCEPT" kind="functionlike"><macro-parameter name="..."/><purpose>Program exception guarantees that can be completely disabled at compile-time. </purpose><description><para><computeroutput>BOOST_CONTRACT_EXCEPT(f)</computeroutput> expands to code equivalent to the following (note that no code is generated when <computeroutput><macroname alt="BOOST_CONTRACT_NO_EXCEPTS">BOOST_CONTRACT_NO_EXCEPTS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_EXCEPTS
    .except(f)
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">f</emphasis></computeroutput> is the nullary functor called by this library to check exception guarantees <computeroutput>f()</computeroutput>. Assertions within this functor are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract assertion failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::except_failure">boost::contract::except_failure</functionname></computeroutput>). This functor should capture variables by (constant) references (to access the values they will have at function exit). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.exception_guarantees"> Exception Guarantees</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_OLD" kind="functionlike"><macro-parameter name="..."/><purpose>Program old value copies at body that can be completely disabled at compile-time. </purpose><description><para><computeroutput>BOOST_CONTRACT_OLD(f)</computeroutput> expands to code equivalent to the following (note that no code is generated when <computeroutput><macroname alt="BOOST_CONTRACT_NO_OLDS">BOOST_CONTRACT_NO_OLDS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_OLDS
    .old(f)
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">f</emphasis></computeroutput> is the nullary functor called by this library <computeroutput>f()</computeroutput> to assign old value copies just before the body is execute but after entry invariants (when they apply) and preconditions are checked. Old value pointers within this functor call are usually assigned using <computeroutput><macroname alt="BOOST_CONTRACT_OLDOF">BOOST_CONTRACT_OLDOF</macroname></computeroutput>. Any exception thrown by a call to this functor will result in this library calling <computeroutput><functionname alt="boost::contract::old_failure">boost::contract::old_failure</functionname></computeroutput> (because old values could not be copied to check postconditions and exception guarantees). This functor should capture old value pointers by references so they can be assigned (all other variables needed to evaluate old value expressions can be captured by (constant) value, or better by (constant) reference to avoid extra copies). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.advanced.old_values_copied_at_body">
        Old Values Copied at Body</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_OLD_PTR" kind="functionlike"><macro-parameter name="..."/><purpose>Program old values that can be completely disabled at compile-time for old value types that are required to be copyable. </purpose><description><para>This is used to program old value copies for copyable types:</para><para><programlisting language="c++">class u {
public:
    void f(...) {
        BOOST_CONTRACT_OLD_PTR(old_type_a)(old_var_a); // Null...
        BOOST_CONTRACT_OLD_PTR(old_type_b)(old_var_b, old_expr_b); // Set.
        BOOST_CONTRACT_PUBLIC_FUNCTION(this)
            ...
            BOOST_CONTRACT_OLD([&amp;] {
                old_var_a = BOOST_CONTRACT_OLDOF(old_expr_a); // ...set.
                ...
            })
            ...
        ;

        ... // Function body.
    }

    virtual void g(..., boost::contract::virtual_* v = 0) {
        BOOST_CONTRACT_OLD_PTR(old_type_a)(old_var_a); // No `v`
        BOOST_CONTRACT_OLD_PTR(old_type_b)(v, old_var_b, old_expr_b); // `v`
        BOOST_CONTRACT_PUBLIC_FUNCTION(v, this)
            ...
            BOOST_CONTRACT_OLD([&amp;] {
                old_var_a = BOOST_CONTRACT_OLDOF(v, old_expr_a); // `v`
                ...
            })
            ...
        ;

        ... // Function body.
    }

    ...
};
</programlisting></para><para>This is an overloaded variadic macro and it can be used in the following different ways (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_OLDS">BOOST_CONTRACT_NO_OLDS</macroname></computeroutput> is defined).</para><para>1. <computeroutput>BOOST_CONTRACT_OLD_PTR(old_type)(old_var)</computeroutput> expands to code equivalent to the following (this leaves the old value pointer null):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_OLDS
    // This declaration does not need to use `v`.
    boost::contract::old_ptr&lt;old_type&gt; old_var
#endif
</programlisting></para><para>2. <computeroutput>BOOST_CONTRACT_OLD_PTR(old_type)(old_var, old_expr)</computeroutput> expands to code equivalent to the following (this initializes the pointer to the old value copy, but not to be used for virtual public functions and public function overrides):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_OLDS
    boost::contract::old_ptr&lt;old_type&gt; old_var =
            BOOST_CONTRACT_OLDOF(old_expr)
#endif
</programlisting></para><para>3. <computeroutput>BOOST_CONTRACT_OLD_PTR(old_type)(v, old_var, old_expr)</computeroutput> expands to code equivalent to the following (this initializes the pointer to the old value copy for virtual public functions and public function overrides):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_OLDS
    boost::contract::old_ptr&lt;old_type&gt; old_var =
            BOOST_CONTRACT_OLDOF(v, old_expr)
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">old_type</emphasis></computeroutput> is the type of the pointed old value. This type must be copyable (i.e., <computeroutput>boost::contract::is_old_value_copyable&lt;old_type&gt;::value</computeroutput> is <computeroutput>true</computeroutput>), otherwise this pointer will always be null and this library will generate a compile-time error when the pointer is dereferenced (see <computeroutput><macroname alt="BOOST_CONTRACT_OLD_PTR_IF_COPYABLE">BOOST_CONTRACT_OLD_PTR_IF_COPYABLE</macroname></computeroutput>). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.) (Rationale: Template parameters like this one are specified to this library macro interface using their own set of parenthesis <computeroutput>SOME_MACRO(template_params)(other_params)</computeroutput>.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">v</emphasis></computeroutput> is the extra training parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing virtual public function or public function override declaring the contract. (This is not a variadic macro parameter but it should never contain commas because it is an identifier.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">old_var</emphasis></computeroutput> is the name of the old value pointer variable. (This is not a variadic macro parameter but it should never contain commas because it is an identifier.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">old_expr</emphasis></computeroutput> is the expression to be evaluated and copied in the old value pointer. (This is not a variadic macro parameter so any comma it might contain must be protected by round parenthesis and <computeroutput>BOOST_CONTRACT_OLD_PTR(old_type)(v, old_var, (old_expr))</computeroutput> will always work.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.old_values"> Old Values</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_OLD_PTR_IF_COPYABLE" kind="functionlike"><macro-parameter name="..."/><purpose>Program old values that can be completely disabled at compile-time for old value types that are not required to be copyable. </purpose><description><para>This is used to program old value copies for types that might or might not be copyable:</para><para><programlisting language="c++">template&lt;typename T&gt; // Type `T` might or not be copyable.
class u {
public:
    void f(...) {
        BOOST_CONTRACT_OLD_PTR_IF_COPYABLE(old_type_a)(old_var_a);
        BOOST_CONTRACT_OLD_PTR_IF_COPYABLE(old_type_b)(old_var_b,
                old_expr_b);
        BOOST_CONTRACT_PUBLIC_FUNCTION(this)
            ...
            BOOST_CONTRACT_OLD([&amp;] {
                old_var_a = BOOST_CONTRACT_OLDOF(old_expr_a);
                ...
            })
            ... // In postconditions or exception guarantees:
                if(old_var_a) ... // Always null for non-copyable types.
                if(old_var_b) ... // Always null for non-copyable types.
            ...
        ;

        ... // Function body.
    }

    virtual void g(..., boost::contract::virtual_* v = 0) {
        BOOST_CONTRACT_OLD_PTR_IF_COPYABLE(old_type_a)(old_var_a);
        BOOST_CONTRACT_OLD_PTR_IF_COPYABLE(old_type_b)(v, old_var_b,
                old_expr_b);
        BOOST_CONTRACT_PUBLIC_FUNCTION(v, this)
            ...
            BOOST_CONTRACT_OLD([&amp;] {
                old_var_a = BOOST_CONTRACT_OLDOF(v, old_expr_a);
                ...
            })
            ... // In postconditions or exception guarantees:
                if(old_var_a) ... // Always null for non-copyable types.
                if(old_var_b) ... // Always null for non-copyable types.
            ...
        ;

        ... // Function body.
    }

    ...
};
</programlisting></para><para>This is an overloaded variadic macro and it can be used in the following different ways (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_OLDS">BOOST_CONTRACT_NO_OLDS</macroname></computeroutput> is defined).</para><para>1. <computeroutput>BOOST_CONTRACT_OLD_PTR_IF_COPYABLE(old_type)(old_var)</computeroutput> expands to code equivalent to the following (this leaves the old value pointer null):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_OLDS
    // This declaration does not need to use `v`.
    boost::contract::old_ptr_if_copyable&lt;old_type&gt; old_var
#endif
</programlisting></para><para>2. <computeroutput>BOOST_CONTRACT_OLD_PTR_IF_COPYABLE(old_type)(old_var, old_expr)</computeroutput> expands to code equivalent to the following (this initializes the pointer to the old value copy, but not to be used for virtual public functions and public function overrides):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_OLDS
    boost::contract::old_ptr_if_copyable&lt;old_type&gt; old_var =
            BOOST_CONTRACT_OLDOF(old_expr)
#endif
</programlisting></para><para>3. <computeroutput>BOOST_CONTRACT_OLD_PTR_IF_COPYABLE(old_type)(v, old_var, old_expr)</computeroutput> expands to code equivalent to the following (this initializes the pointer to the old value copy for virtual public functions and public function overrides):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_OLDS
    boost::contract::old_ptr_if_copyable&lt;old_type&gt; old_var =
            BOOST_CONTRACT_OLDOF(v, old_expr)
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">old_type</emphasis></computeroutput> is the type of the pointed old value. If this type is not copyable (i.e., <computeroutput>boost::contract::is_old_value_copyable&lt;old_type&gt;::value</computeroutput> is <computeroutput>false</computeroutput>), this pointer will always be null, but this library will not generate a compile-time error when this pointer is dereferenced (see <computeroutput><macroname alt="BOOST_CONTRACT_OLD_PTR">BOOST_CONTRACT_OLD_PTR</macroname></computeroutput>). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">v</emphasis></computeroutput> is the extra trailing parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing virtual public function or public function override declaring the contract. (This is not a variadic macro parameter but it should never contain commas because it is an identifier.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">old_var</emphasis></computeroutput> is the name of the old value pointer variable. (This is not a variadic macro parameter but it should never contain commas because it is an identifier.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">old_expr</emphasis></computeroutput> is the expression to be evaluated and copied in the old value pointer. (This is not a variadic macro parameter so any comma it might contain must be protected by round parenthesis and <computeroutput>BOOST_CONTRACT_OLD_PTR_IF_COPYABLE(old_type)(v, old_var, (old_expr))</computeroutput> will always work.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.extras.old_value_requirements__templates_">
        Old Value Requirements</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_INVARIANT" kind="functionlike"><macro-parameter name="..."/><purpose>Program (constant) class invariants that can be completely disabled at compile-time. </purpose><description><para><computeroutput>BOOST_CONTRACT_INVARIANT</computeroutput>({ ... }) expands to code equivalent to the following (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_INVARIANTS
    void BOOST_CONTRACT_INVARIANT_FUNC() const {
        ...
    }
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><emphasis role="bold">{ ... }</emphasis> is the definition of the function that checks class invariants for public functions that are not static and not volatile (see <computeroutput><macroname alt="BOOST_CONTRACT_STATIC_INVARIANT">BOOST_CONTRACT_STATIC_INVARIANT</macroname></computeroutput> and  <computeroutput><macroname alt="BOOST_CONTRACT_INVARIANT_VOLATILE">BOOST_CONTRACT_INVARIANT_VOLATILE</macroname></computeroutput>). The curly parenthesis are mandatory (rationale: this is so the syntax of this macro resembles mote the syntax of the lambda functions usually used to specify preconditions, etc.). Assertions within this function are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this function indicates a contract assertion failure (and will result in this library calling either  <computeroutput><functionname alt="boost::contract::entry_invariant_failure">boost::contract::entry_invariant_failure</functionname></computeroutput> or  <computeroutput><functionname alt="boost::contract::exit_invariant_failure">boost::contract::exit_invariant_failure</functionname></computeroutput>). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_INVARIANT_VOLATILE" kind="functionlike"><macro-parameter name="..."/><purpose>Program volatile class invariants that can be completely disabled at compile-time. </purpose><description><para><computeroutput>BOOST_CONTRACT_INVARIANT_VOLATILE</computeroutput>({ ... }) expands to code equivalent to the following (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_INVARIANTS
    void BOOST_CONTRACT_INVARIANT_FUNC() const volatile {
        ...
    }
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><emphasis role="bold">{ ... }</emphasis> is the definition of the function that checks class invariants for volatile public functions (see <computeroutput><macroname alt="BOOST_CONTRACT_INVARIANT">BOOST_CONTRACT_INVARIANT</macroname></computeroutput> and  <computeroutput><macroname alt="BOOST_CONTRACT_STATIC_INVARIANT">BOOST_CONTRACT_STATIC_INVARIANT</macroname></computeroutput>). The curly parenthesis are mandatory. Assertions within this function are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this function indicates a contract assertion failure (and will result in this library calling either  <computeroutput><functionname alt="boost::contract::entry_invariant_failure">boost::contract::entry_invariant_failure</functionname></computeroutput> or  <computeroutput><functionname alt="boost::contract::exit_invariant_failure">boost::contract::exit_invariant_failure</functionname></computeroutput>). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_STATIC_INVARIANT" kind="functionlike"><macro-parameter name="..."/><purpose>Program static class invariants that can be completely disabled at compile-time. </purpose><description><para><computeroutput>BOOST_CONTRACT_STATIC_INVARIANT</computeroutput>({ ... }) expands to code equivalent to the following (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_INVARIANTS">BOOST_CONTRACT_NO_INVARIANTS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_INVARIANTS
    static void BOOST_CONTRACT_STATIC_INVARIANT_FUNC() {
        ...
    }
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><emphasis role="bold">{ ... }</emphasis> is the definition of the function that checks class invariants for static public functions (see <computeroutput><macroname alt="BOOST_CONTRACT_INVARIANT">BOOST_CONTRACT_INVARIANT</macroname></computeroutput> and  <computeroutput><macroname alt="BOOST_CONTRACT_INVARIANT_VOLATILE">BOOST_CONTRACT_INVARIANT_VOLATILE</macroname></computeroutput>). The curly parenthesis are mandatory. Assertions within this function are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this function indicates a contract assertion failure (and will result in this library calling either  <computeroutput><functionname alt="boost::contract::entry_invariant_failure">boost::contract::entry_invariant_failure</functionname></computeroutput> or  <computeroutput><functionname alt="boost::contract::exit_invariant_failure">boost::contract::exit_invariant_failure</functionname></computeroutput>). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.class_invariants"> Class Invariants</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_CONSTRUCTOR" kind="functionlike"><macro-parameter name="..."/><purpose>Program contracts that can be completely disabled at compile-time for constructors. </purpose><description><para>This is used together with <computeroutput><macroname alt="BOOST_CONTRACT_POSTCONDITION">BOOST_CONTRACT_POSTCONDITION</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_EXCEPT">BOOST_CONTRACT_EXCEPT</macroname></computeroutput>, and <computeroutput><macroname alt="BOOST_CONTRACT_OLD">BOOST_CONTRACT_OLD</macroname></computeroutput> to specify postconditions, exception guarantees, and old value copies at body that can be completely disabled at compile-time for constructors (see  <computeroutput><macroname alt="BOOST_CONTRACT_CONSTRUCTOR_PRECONDITION">BOOST_CONTRACT_CONSTRUCTOR_PRECONDITION</macroname></computeroutput> to specify preconditions for constructors):</para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    BOOST_CONTRACT_INVARIANT({ // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    })

public:
    u(...) {
        BOOST_CONTRACT_OLD_PTR(old_type)(old_var);
        BOOST_CONTRACT_CONSTRUCTOR(this)
            // No `PRECONDITION` (use `CONSTRUCTOR_PRECONDITION` if needed).
            BOOST_CONTRACT_OLD([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(old_epxr);
                ...
            })
            BOOST_CONTRACT_POSTCONDITION([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            BOOST_CONTRACT_EXCEPT([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ; // Trailing `;` is required.

        ... // Constructor body.
    }

    ...
};
</programlisting></para><para>For optimization, this can be omitted for constructors that do not have postconditions and exception guarantees, within classes that have no invariants.</para><para><computeroutput>BOOST_CONTRACT_CONSTRUCTOR(obj)</computeroutput> expands to code equivalent to the following (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_CONSTRUCTORS">BOOST_CONTRACT_NO_CONSTRUCTORS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_CONSTRUCTORS
    boost::contract::check internal_var =
            boost::contract::constructor(obj)
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">obj</emphasis></computeroutput> is the object <computeroutput>this</computeroutput> from the scope of the enclosing constructor declaring the contract. Constructors check all class invariants, including static and volatile invariants (see <computeroutput><macroname alt="BOOST_CONTRACT_INVARIANT">BOOST_CONTRACT_INVARIANT</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_STATIC_INVARIANT">BOOST_CONTRACT_STATIC_INVARIANT</macroname></computeroutput>, and  <computeroutput><macroname alt="BOOST_CONTRACT_INVARIANT_VOLATILE">BOOST_CONTRACT_INVARIANT_VOLATILE</macroname></computeroutput>). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">internal_var</emphasis></computeroutput> is a variable name internally generated by this library (this name is unique but only on different line numbers so this macro cannot be expanded multiple times on the same line).</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.constructors"> Constructors</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_CONSTRUCTOR_PRECONDITION" kind="functionlike"><macro-parameter name="..."/><purpose>Program preconditions that can be disabled at compile-time for constructors. </purpose><description><para>This is used together with <computeroutput><macroname alt="BOOST_CONTRACT_CONSTRUCTOR">BOOST_CONTRACT_CONSTRUCTOR</macroname></computeroutput> to specify contracts for constructors. Constructors that do not have preconditions do not use this macro. When at least one of the class constructors uses this macro,  <computeroutput><classname alt="boost::contract::constructor_precondition">boost::contract::constructor_precondition</classname></computeroutput> must be the first and private base of the class declaring the constructor for which preconditions are programmed:</para><para><programlisting language="c++">class u
    #define BASES private boost::contract::constructor_precondition&lt;u&gt;, \
            public b
    : BASES
{
    friend class boost::contract::access;

    typedef BOOST_CONTRACT_BASE_TYPES(BASES) base_types;
    #undef BASES

    ...

public:
    explicit u(unsigned x) :
        BOOST_CONTRACT_CONSTRUCTOR_PRECONDITION(u)([&amp;] {
            BOOST_CONTRACT_ASSERT(x != 0);
        }),
        b(1 / x)
    {
        ...
    }

    ...
};
</programlisting></para><para><computeroutput>BOOST_CONTRACT_CONSTRUCTOR_PRECONDITION(class_type)(f)</computeroutput> expands to code equivalent to the following (note that when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_PRECONDITIONS">BOOST_CONTRACT_NO_PRECONDITIONS</macroname></computeroutput> is defined, this macro trivially expands to a default constructor call that is internally implemented to do nothing so this should have minimal to no overhead):</para><para><programlisting language="c++">// Guarded only by NO_PRECONDITIONS (and not also by NO_CONSTRUCTORS)
// because for constructor's preconditions (not for postconditions, etc.).
#ifndef BOOST_CONTRACT_NO_PRECONDITIONS
    boost::contract::constructor_precondition&lt;class_type&gt;(f)
#else // No-op call (likely optimized away, minimal to no overhead).
    boost::contract::constructor_precondition&lt;class_type&gt;()
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">class_type</emphasis></computeroutput> is the type of the class containing the constructor for which preconditions are being programmed. (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">f</emphasis></computeroutput> is the nullary functor called by this library to check constructor preconditions <computeroutput>f()</computeroutput>. Assertions within this functor call are usually programmed using  <computeroutput><macroname alt="BOOST_CONTRACT_ASSERT">BOOST_CONTRACT_ASSERT</macroname></computeroutput>, but any exception thrown by a call to this functor indicates a contract failure (and will result in this library calling  <computeroutput><functionname alt="boost::contract::precondition_failure">boost::contract::precondition_failure</functionname></computeroutput>). This functor should capture variables by (constant) value, or better by (constant) reference to avoid extra copies. (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.)</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.constructors"> Constructors</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_DESTRUCTOR" kind="functionlike"><macro-parameter name="..."/><purpose>Program contracts that can be completely disabled at compile-time for destructors. </purpose><description><para>This is used together with <computeroutput><macroname alt="BOOST_CONTRACT_POSTCONDITION">BOOST_CONTRACT_POSTCONDITION</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_EXCEPT">BOOST_CONTRACT_EXCEPT</macroname></computeroutput>, and <computeroutput><macroname alt="BOOST_CONTRACT_OLD">BOOST_CONTRACT_OLD</macroname></computeroutput> to specify postconditions, exception guarantees, and old value copies at body that can be completely disabled at compile-time for destructors (destructors cannot have preconditions, see  <link linkend="boost_contract.contract_programming_overview.destructor_calls"> Destructor Calls</link>):</para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    BOOST_CONTRACT_INVARIANT({ // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    })

public:
    ~u() {
        BOOST_CONTRACT_OLD_PTR(old_type)(old_var);
        BOOST_CONTRACT_DESTRUCTOR(this)
            // No `PRECONDITION` (destructors have no preconditions).
            BOOST_CONTRACT_OLD([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(old_expr);
                ...
            })
            BOOST_CONTRACT_POSTCONDITION([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            BOOST_CONTRACT_EXCEPT([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ; // Trailing `;` is required.

        ... // Destructor body.
    }
    
    ...
};
</programlisting></para><para>For optimization, this can be omitted for destructors that do not have postconditions and exception guarantees, within classes that have no invariants.</para><para><computeroutput>BOOST_CONTRACT_DESTRUCTOR(obj)</computeroutput> expands to code equivalent to the following (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_DESTRUCTORS">BOOST_CONTRACT_NO_DESTRUCTORS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_DESTRUCTORS
    boost::contract::check internal_var =
            boost::contract::destructor(obj)
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">obj</emphasis></computeroutput> is the object <computeroutput>this</computeroutput> from the scope of the enclosing destructor declaring the contract. Destructors check all class invariants, including static and volatile invariants (see <link linkend="boost_contract.tutorial.class_invariants">
        Class Invariants</link> and  <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link>). (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">internal_var</emphasis></computeroutput> is a variable name internally generated by this library (this name is unique but only on different line numbers so this macro cannot be expanded multiple times on the same line).</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.destructors"> Destructors</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_FUNCTION" kind="functionlike"><macro-parameter name=""/><purpose>Program contracts that can be completely disabled at compile-time for (non-public) functions. </purpose><description><para>This is used together with <computeroutput><macroname alt="BOOST_CONTRACT_PRECONDITION">BOOST_CONTRACT_PRECONDITION</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_POSTCONDITION">BOOST_CONTRACT_POSTCONDITION</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_EXCEPT">BOOST_CONTRACT_EXCEPT</macroname></computeroutput>, and <computeroutput><macroname alt="BOOST_CONTRACT_OLD">BOOST_CONTRACT_OLD</macroname></computeroutput> to specify preconditions, postconditions, exception guarantees, and old value copies at body that can be completely disabled at compile-time for (non-public) functions:</para><para><programlisting language="c++">void f(...) {
    BOOST_CONTRACT_OLD_PTR(old_type)(old_var);
    BOOST_CONTRACT_FUNCTION()
        BOOST_CONTRACT_PRECONDITION([&amp;] { // Optional.
            BOOST_CONTRACT_ASSERT(...);
            ...
        })
        BOOST_CONTRACT_OLD([&amp;] { // Optional.
            old_var = BOOST_CONTRACT_OLDOF(old_expr);  
            ...
        })
        BOOST_CONTRACT_POSTCONDITION([&amp;] { // Optional.
            BOOST_CONTRACT_ASSERT(...);
            ...
        })
        BOOST_CONTRACT_EXCEPT([&amp;] { // Optional.
            BOOST_CONTRACT_ASSERT(...);
            ...
        })
    ; // Trailing `;` is required.

    ... // Function body.
}
</programlisting></para><para>This can be used to program contracts for non-member functions but also for private and protected functions, lambda functions, loops, arbitrary blocks of code, etc. For optimization, this can be omitted for code that does not have preconditions, postconditions, and exception guarantees.</para><para><computeroutput>BOOST_CONTRACT_FUNCTION()</computeroutput> expands to code equivalent to the following (note that no code is generated when <computeroutput><macroname alt="BOOST_CONTRACT_NO_FUNCTIONS">BOOST_CONTRACT_NO_FUNCTIONS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_FUNCTIONS
    boost::contract::check internal_var =
            boost::contract::function()
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">internal_var</emphasis></computeroutput> is a variable name internally generated by this library (this name is unique but only on different line numbers so this macro cannot be expanded multiple times on the same line).</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.non_member_functions"> Non-Member Functions</link>,  <link linkend="boost_contract.advanced.private_and_protected_functions">
        Private and Protected Functions</link>,  <link linkend="boost_contract.advanced.lambdas__loops__code_blocks__and__constexpr__">
        Lambdas, Loops, Code Blocks</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_STATIC_PUBLIC_FUNCTION" kind="functionlike"><macro-parameter name="..."/><purpose>Program contracts that can be completely disabled at compile-time for static public functions. </purpose><description><para>This is used together with <computeroutput><macroname alt="BOOST_CONTRACT_PRECONDITION">BOOST_CONTRACT_PRECONDITION</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_POSTCONDITION">BOOST_CONTRACT_POSTCONDITION</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_EXCEPT">BOOST_CONTRACT_EXCEPT</macroname></computeroutput>, and <computeroutput><macroname alt="BOOST_CONTRACT_OLD">BOOST_CONTRACT_OLD</macroname></computeroutput> to specify preconditions, postconditions, exception guarantees, and old value copies at body that can be completely disabled at compile-time for static public functions:</para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    BOOST_CONTRACT_STATIC_INVARIANT({ // Optional (as for non-static).
        BOOST_CONTRACT_ASSERT(...);
        ...
    })

public:
    static void f(...) {
        BOOST_CONTRACT_OLD_PTR(old_type)(old_var);
        BOOST_CONTRACT_PUBLIC_FUNCTION(u)
            BOOST_CONTRACT_PRECONDITION([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            BOOST_CONTRACT_OLD([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(old_expr);
                ...
            })
            BOOST_CONTRACT_POSTCONDITION([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            BOOST_CONTRACT_EXCEPT([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ; // Trailing `;` is required.

        ... // Function body.
    }
    
    ...
};
</programlisting></para><para>For optimization, this can be omitted for static public functions that do not have preconditions, postconditions and exception guarantees, within classes that have no static invariants.</para><para><computeroutput>BOOST_CONTRACT_STATIC_PUBLIC_FUNCTION(class_type)</computeroutput> expands to code equivalent to the following (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS">BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS</macroname></computeroutput> is defined):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS
    boost::contract::check internal_var =
            boost::contract::public_function&lt;class_type&gt;()
#endif
</programlisting></para><para>Where:</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">class_type</emphasis></computeroutput> is the type of the class containing the static public function declaring the contract. (This is a variadic macro parameter so it can contain commas not protected by round parenthesis.) </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">internal_var</emphasis></computeroutput> is a variable name internally generated by this library (this name is unique but only on different line numbers so this macro cannot be expanded multiple times on the same line).</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.static_public_functions"> Static Public Functions</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_PUBLIC_FUNCTION" kind="functionlike"><macro-parameter name="..."/><purpose>Program contracts that can be completely disabled at compile-time for non-static public functions that do not override. </purpose><description><para>This is used together with <computeroutput><macroname alt="BOOST_CONTRACT_PRECONDITION">BOOST_CONTRACT_PRECONDITION</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_POSTCONDITION">BOOST_CONTRACT_POSTCONDITION</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_EXCEPT">BOOST_CONTRACT_EXCEPT</macroname></computeroutput>, and <computeroutput><macroname alt="BOOST_CONTRACT_OLD">BOOST_CONTRACT_OLD</macroname></computeroutput> to specify preconditions, postconditions, exception guarantees, and old value copies at body that can be completely disabled at compile-time for non-static public functions (virtual or not, void or not) that do not override:</para><para><programlisting language="c++">class u {
    friend class boost::contract::access;

    BOOST_CONTRACT_INVARIANT({ // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    })

public:
    // Non-virtual (same if void).
    t f(...) {
        t result;
        BOOST_CONTRACT_OLD_PTR(old_type)(old_var);
        BOOST_CONTRACT_PUBLIC_FUNCTION(this)
            BOOST_CONTRACT_PRECONDITION([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            BOOST_CONTRACT_OLD([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(old_expr);
                ...
            })
            BOOST_CONTRACT_POSTCONDITION([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            BOOST_CONTRACT_EXCEPT([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ; // Trailing `;` is required.

        ... // Function body (use `return result = return_expr`).
    }
    
    // Virtual and void.
    virtual void g(..., boost::contract::virtual_* v = 0) {
        BOOST_CONTRACT_OLD_PTR(old_type)(old_var);
        BOOST_CONTRACT_PUBLIC_FUNCTION(v, this)
            ...
            BOOST_CONTRACT_OLD([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(v, old_expr);
                ...
            })
            ...
        ; // Trailing `;` is required.
        
        ... // Function body.
    }
    
    // Virtual and non-void.
    virtual t h(..., boost::contract::virtual_* v = 0) {
        t result;
        BOOST_CONTRACT_OLD_PTR(old_type)(old_var);
        BOOST_CONTRACT_PUBLIC_FUNCTION(v, result, this)
            ...
            BOOST_CONTRACT_OLD([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(v, old_expr);
                ...
            })
            BOOST_CONTRACT_POSTCONDITION([&amp;] (t const&amp; result) { // Optional
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            ...
        ; // Trailing `;` is required.
        
        ... // Function body (use `return result = return_expr`).
    }
    
    ...
};
</programlisting></para><para>For optimization, this can be omitted for non-virtual public functions that do not have preconditions, postconditions and exception guarantees, within classes that have no invariants. Virtual public functions should always use  <computeroutput><macroname alt="BOOST_CONTRACT_PUBLIC_FUNCTION">BOOST_CONTRACT_PUBLIC_FUNCTION</macroname></computeroutput> otherwise this library will not be able to correctly use them for subcontracting.</para><para>This is an overloaded variadic macro and it can be used in the following different ways (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS">BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS</macroname></computeroutput> is defined).</para><para>1. <computeroutput>BOOST_CONTRACT_PUBLIC_FUNCTION(obj)</computeroutput> expands to code equivalent to the following (for non-virtual public functions that are not static and do not override, returning void or not):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS
    boost::contract::check internal_var =
            boost::contract::public_function(obj)
#endif
</programlisting></para><para>2. <computeroutput>BOOST_CONTRACT_PUBLIC_FUNCTION(v, obj)</computeroutput> expands to code equivalent to the following (for virtual public functions that do not override, returning void):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS
    boost::contract::check internal_var =
            boost::contract::public_function(v, obj)
#endif
</programlisting></para><para>3. <computeroutput>BOOST_CONTRACT_PUBLIC_FUNCTION(v, r, obj)</computeroutput> expands to code equivalent to the following (for virtual public functions that do not override, not returning void):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS
    boost::contract::check internal_var =
            boost::contract::public_function(v, r, obj)
#endif
</programlisting></para><para>Where (these are all variadic macro parameters so they can contain commas not protected by round parenthesis):</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">v</emphasis></computeroutput> is the extra parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing virtual public function declaring the contract. </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">r</emphasis></computeroutput> is a reference to the return value of the enclosing virtual public function declaring the contract. This is usually a local variable declared by the enclosing virtual public function just before the contract, but programmers must set it to the actual value being returned by the function at each <computeroutput>return</computeroutput> statement. </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">obj</emphasis></computeroutput> is the object <computeroutput>this</computeroutput> from the scope of the enclosing public function declaring the contract. This object might be mutable, <computeroutput>const</computeroutput>, <computeroutput>volatile</computeroutput>, or <computeroutput>const volatile</computeroutput> depending on the cv-qualifier of the enclosing function (volatile public functions will check volatile class invariants, see <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link>). </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">internal_var</emphasis></computeroutput> is a variable name internally generated by this library (this name is unique but only on different line numbers so this macro cannot be expanded multiple times on the same line).</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.public_functions"> Public Functions</link>,  <link linkend="boost_contract.tutorial.virtual_public_functions">
        Virtual Public Functions</link> </para>
</para>
</para></description></macro>
<macro name="BOOST_CONTRACT_PUBLIC_FUNCTION_OVERRIDE" kind="functionlike"><macro-parameter name="..."/><purpose>Program contracts that can be completely disabled at compile-time for public function overrides. </purpose><description><para>This is used together with <computeroutput><macroname alt="BOOST_CONTRACT_PRECONDITION">BOOST_CONTRACT_PRECONDITION</macroname></computeroutput>,  <computeroutput><macroname alt="BOOST_CONTRACT_POSTCONDITION">BOOST_CONTRACT_POSTCONDITION</macroname></computeroutput>, <computeroutput><macroname alt="BOOST_CONTRACT_EXCEPT">BOOST_CONTRACT_EXCEPT</macroname></computeroutput>, and <computeroutput><macroname alt="BOOST_CONTRACT_OLD">BOOST_CONTRACT_OLD</macroname></computeroutput> to specify preconditions, postconditions, exception guarantees, and old value copies at body that can be completely disabled at compile-time for public function overrides (virtual or not):</para><para><programlisting language="c++">class u
    #define BASES private boost::contract::constructor_precondition&lt;u&gt;, \
            public b, private w
    : BASES
{
    friend class boost::contract::access;

    typedef BOOST_CONTRACT_BASE_TYPES(BASES) base_types;
    #undef BASES

    BOOST_CONTRACT_INVARIANT({ // Optional (as for static and volatile).
        BOOST_CONTRACT_ASSERT(...);
        ...
    })

    BOOST_CONTRACT_OVERRIDES(f, g)

public:
    // Override from `b::f`, and void.
    void f(t_1 a_1, ..., t_n a_n, boost::contract::virtual_* v = 0) {
        BOOST_CONTRACT_OLD_PTR(old_type)(old_var);
        BOOST_CONTRACT_PUBLIC_FUNCTION_OVERRIDE(override_f)(
                v, &amp;u::f, this, a_1, ..., a_n)
            BOOST_CONTRACT_PRECONDITION([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            BOOST_CONTRACT_OLD([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(v, old_expr);
                ...
            })
            BOOST_CONTRACT_POSTCONDITION([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            BOOST_CONTRACT_EXCEPT([&amp;] { // Optional.
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
        ; // Trailing `;` is required.

        ... // Function body.
    }
    
    // Override from `b::g`, and void.
    t g(t_1 a_1, ..., t_n a_n, boost::contract::virtual_* v = 0) {
        t result;
        BOOST_CONTRACT_OLD_PTR(old_type)(old_var);
        BOOST_CONTRACT_PUBLIC_FUNCTION_OVERRIDE(override_g)(
                v, result, &amp;u::g, this, a_1, ..., a_n)
            ...
            BOOST_CONTRACT_OLD([&amp;] { // Optional.
                old_var = BOOST_CONTRACT_OLDOF(v, old_expr);
                ...
            })
            BOOST_CONTRACT_POSTCONDITION([&amp;] (t const&amp; result) { // Optional
                BOOST_CONTRACT_ASSERT(...);
                ...
            })
            ...
        ; // Trailing `;` is required.

        ... // Function body (use `return result = return_expr`).
    }
    
    ...
};
</programlisting></para><para>Public function overrides should always use  <computeroutput><macroname alt="BOOST_CONTRACT_PUBLIC_FUNCTION_OVERRIDE">BOOST_CONTRACT_PUBLIC_FUNCTION_OVERRIDE</macroname></computeroutput> otherwise this library will not be able to correctly use it for subcontracting.</para><para>This is an overloaded variadic macro and it can be used in the following different ways (note that no code is generated when  <computeroutput><macroname alt="BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS">BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS</macroname></computeroutput> is defined).</para><para>1. <computeroutput>BOOST_CONTRACT_PUBLIC_FUNCTION_OVERRIDE(override_type)(v, f, obj, ...)</computeroutput> expands to code equivalent to the following (for public function overrides that return void):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS
    boost::contract::check internal_var = boost::contract::
            public_function&lt;override_type&gt;(v, f, obj, ...)
#endif
</programlisting></para><para>2. <computeroutput>BOOST_CONTRACT_PUBLIC_FUNCTION_OVERRIDE(override_type)(v, r, f, obj, ...)</computeroutput> expands to code equivalent to the following (for public function overrides that do not return void):</para><para><programlisting language="c++">#ifndef BOOST_CONTRACT_NO_PUBLIC_FUNCTIONS
    boost::contract::check internal_var = boost::contract::
            public_function&lt;override_type&gt;(v, r, f, obj, ...)
#endif
</programlisting></para><para>Where (these are all variadic macro parameters so they can contain commas not protected by round parenthesis):</para><para><itemizedlist>
<listitem><para><computeroutput><emphasis role="bold">override_type</emphasis></computeroutput> is the type <computeroutput>override_<emphasis>function-name</emphasis></computeroutput> declared using the  <computeroutput><macroname alt="BOOST_CONTRACT_OVERRIDE">BOOST_CONTRACT_OVERRIDE</macroname></computeroutput> or related macros. </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">v</emphasis></computeroutput> is the extra parameter of type  <computeroutput><classname alt="boost::contract::virtual_">boost::contract::virtual_</classname></computeroutput><computeroutput>*</computeroutput> and default value <computeroutput>0</computeroutput> from the enclosing virtual public function declaring the contract. </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">r</emphasis></computeroutput> is a reference to the return value of the enclosing virtual public function declaring the contract. This is usually a local variable declared by the enclosing virtual public function just before the contract, but programmers must set it to the actual value being returned by the function at each <computeroutput>return</computeroutput> statement. </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">f</emphasis></computeroutput> is a pointer to the enclosing public function override declaring the contract. </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">obj</emphasis></computeroutput> is the object <computeroutput>this</computeroutput> from the scope of the enclosing public function declaring the contract. This object might be mutable, <computeroutput>const</computeroutput>, <computeroutput>volatile</computeroutput>, or <computeroutput>const volatile</computeroutput> depending on the cv-qualifier of the enclosing function (volatile public functions will check volatile class invariants, see <link linkend="boost_contract.extras.volatile_public_functions">
        Volatile Public Functions</link>). </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">...</emphasis></computeroutput> is a variadic macro parameter listing all the arguments passed to the enclosing public function override declaring the contract (by reference and in the order they appear in the enclosing function declaration), but excluding the trailing argument <computeroutput>v</computeroutput>. </para>
</listitem>
<listitem><para><computeroutput><emphasis role="bold">internal_var</emphasis></computeroutput> is a variable name internally generated by this library (this name is unique but only on different line numbers so this macro cannot be expanded multiple times on the same line).</para>
</listitem>
</itemizedlist>
<para><emphasis role="bold">See Also:</emphasis><para> <link linkend="boost_contract.extras.disable_contract_compilation__macro_interface_">
        Disable Contract Compilation</link>,  <link linkend="boost_contract.tutorial.public_function_overrides__subcontracting_">
        Public Function Overrides</link> </para>
</para>
</para></description></macro>
</header>
</library-reference>