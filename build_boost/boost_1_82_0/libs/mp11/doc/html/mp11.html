<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.16">
<meta name="author" content="Peter Dimov">
<title>Boost.Mp11: A C++11 metaprogramming library</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700">
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;-webkit-tap-highlight-color:transparent}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos{border-right:1px solid;opacity:.35;padding-right:.5em}
pre.pygments .lineno{border-right:1px solid;opacity:.35;display:inline-block;margin-right:.75em}
pre.pygments .lineno::before{content:"";margin-right:-.125em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all tr,table.stripes-odd tr:nth-of-type(odd),table.stripes-even tr:nth-of-type(even),table.stripes-hover tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
ol>li p,ul>li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
.gist .file-data>table{border:0;background:#fff;width:100%;margin-bottom:0}
.gist .file-data>table td.line-data{width:99%}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
<style>
pre.rouge table td { padding: 5px; }
pre.rouge table pre { margin: 0; }
pre.rouge .cm {
  color: #999988;
  font-style: italic;
}
pre.rouge .cp {
  color: #999999;
  font-weight: bold;
}
pre.rouge .c1 {
  color: #999988;
  font-style: italic;
}
pre.rouge .cs {
  color: #999999;
  font-weight: bold;
  font-style: italic;
}
pre.rouge .c, pre.rouge .ch, pre.rouge .cd, pre.rouge .cpf {
  color: #999988;
  font-style: italic;
}
pre.rouge .err {
  color: #a61717;
  background-color: #e3d2d2;
}
pre.rouge .gd {
  color: #000000;
  background-color: #ffdddd;
}
pre.rouge .ge {
  color: #000000;
  font-style: italic;
}
pre.rouge .gr {
  color: #aa0000;
}
pre.rouge .gh {
  color: #999999;
}
pre.rouge .gi {
  color: #000000;
  background-color: #ddffdd;
}
pre.rouge .go {
  color: #888888;
}
pre.rouge .gp {
  color: #555555;
}
pre.rouge .gs {
  font-weight: bold;
}
pre.rouge .gu {
  color: #aaaaaa;
}
pre.rouge .gt {
  color: #aa0000;
}
pre.rouge .kc {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kd {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kn {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kp {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kr {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kt {
  color: #445588;
  font-weight: bold;
}
pre.rouge .k, pre.rouge .kv {
  color: #000000;
  font-weight: bold;
}
pre.rouge .mf {
  color: #009999;
}
pre.rouge .mh {
  color: #009999;
}
pre.rouge .il {
  color: #009999;
}
pre.rouge .mi {
  color: #009999;
}
pre.rouge .mo {
  color: #009999;
}
pre.rouge .m, pre.rouge .mb, pre.rouge .mx {
  color: #009999;
}
pre.rouge .sa {
  color: #000000;
  font-weight: bold;
}
pre.rouge .sb {
  color: #d14;
}
pre.rouge .sc {
  color: #d14;
}
pre.rouge .sd {
  color: #d14;
}
pre.rouge .s2 {
  color: #d14;
}
pre.rouge .se {
  color: #d14;
}
pre.rouge .sh {
  color: #d14;
}
pre.rouge .si {
  color: #d14;
}
pre.rouge .sx {
  color: #d14;
}
pre.rouge .sr {
  color: #009926;
}
pre.rouge .s1 {
  color: #d14;
}
pre.rouge .ss {
  color: #990073;
}
pre.rouge .s, pre.rouge .dl {
  color: #d14;
}
pre.rouge .na {
  color: #008080;
}
pre.rouge .bp {
  color: #999999;
}
pre.rouge .nb {
  color: #0086B3;
}
pre.rouge .nc {
  color: #445588;
  font-weight: bold;
}
pre.rouge .no {
  color: #008080;
}
pre.rouge .nd {
  color: #3c5d5d;
  font-weight: bold;
}
pre.rouge .ni {
  color: #800080;
}
pre.rouge .ne {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nf, pre.rouge .fm {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nl {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nn {
  color: #555555;
}
pre.rouge .nt {
  color: #000080;
}
pre.rouge .vc {
  color: #008080;
}
pre.rouge .vg {
  color: #008080;
}
pre.rouge .vi {
  color: #008080;
}
pre.rouge .nv, pre.rouge .vm {
  color: #008080;
}
pre.rouge .ow {
  color: #000000;
  font-weight: bold;
}
pre.rouge .o {
  color: #000000;
  font-weight: bold;
}
pre.rouge .w {
  color: #bbbbbb;
}
pre.rouge {
  background-color: #f8f8f8;
}
</style>
</head>
<body class="article toc2 toc-left">
<div id="header">
<h1>Boost.Mp11: A C++11 metaprogramming library</h1>
<div class="details">
<span id="author" class="author">Peter Dimov</span><br>
</div>
<div id="toc" class="toc2">
<div id="toctitle">Table of Contents</div>
<ul class="sectlevel1">
<li><a href="#overview">Overview</a></li>
<li><a href="#definitions">Definitions</a></li>
<li><a href="#examples">Examples</a>
<ul class="sectlevel2">
<li><a href="#generating_test_cases">Generating Test Cases</a></li>
<li><a href="#writing_common_type_specializations">Writing common_type Specializations</a></li>
<li><a href="#fixing_tuple_cat">Fixing tuple_cat</a></li>
<li><a href="#computing_return_types">Computing Return Types</a></li>
</ul>
</li>
<li><a href="#changelog">Revision History</a>
<ul class="sectlevel2">
<li><a href="#changes_in_1_77_0">Changes in 1.77.0</a></li>
<li><a href="#changes_in_1_75_0">Changes in 1.75.0</a></li>
<li><a href="#changes_in_1_74_0">Changes in 1.74.0</a></li>
<li><a href="#changes_in_1_73_0">Changes in 1.73.0</a></li>
<li><a href="#changes_in_1_70_0">Changes in 1.70.0</a></li>
<li><a href="#changes_in_1_69_0">Changes in 1.69.0</a></li>
</ul>
</li>
<li><a href="#reference">Reference</a>
<ul class="sectlevel2">
<li><a href="#integral">Integral Constants, &lt;boost/mp11/integral.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#mp_boolb">mp_bool&lt;B&gt;</a></li>
<li><a href="#mp_true">mp_true</a></li>
<li><a href="#mp_false">mp_false</a></li>
<li><a href="#mp_to_boolt">mp_to_bool&lt;T&gt;</a></li>
<li><a href="#mp_nott">mp_not&lt;T&gt;</a></li>
<li><a href="#mp_inti">mp_int&lt;I&gt;</a></li>
<li><a href="#mp_size_tn">mp_size_t&lt;N&gt;</a></li>
</ul>
</li>
<li><a href="#list">List Operations, &lt;boost/mp11/list.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#mp_listt">mp_list&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_list_ct_i">mp_list_c&lt;T, I&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_is_listl">mp_is_list&lt;L&gt;</a></li>
<li><a href="#mp_sizel">mp_size&lt;L&gt;</a></li>
<li><a href="#mp_emptyl">mp_empty&lt;L&gt;</a></li>
<li><a href="#mp_assignl1_l2">mp_assign&lt;L1, L2&gt;</a></li>
<li><a href="#mp_clearl">mp_clear&lt;L&gt;</a></li>
<li><a href="#mp_frontl">mp_front&lt;L&gt;</a></li>
<li><a href="#mp_pop_frontl">mp_pop_front&lt;L&gt;</a></li>
<li><a href="#mp_firstl">mp_first&lt;L&gt;</a></li>
<li><a href="#mp_restl">mp_rest&lt;L&gt;</a></li>
<li><a href="#mp_secondl">mp_second&lt;L&gt;</a></li>
<li><a href="#mp_thirdl">mp_third&lt;L&gt;</a></li>
<li><a href="#mp_push_frontl_t">mp_push_front&lt;L, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_push_backl_t">mp_push_back&lt;L, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_renamel_y">mp_rename&lt;L, Y&gt;</a></li>
<li><a href="#mp_applyf_l">mp_apply&lt;F, L&gt;</a></li>
<li><a href="#mp_apply_qq_l">mp_apply_q&lt;Q, L&gt;</a></li>
<li><a href="#mp_appendl">mp_append&lt;L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_replace_frontl_t">mp_replace_front&lt;L, T&gt;</a></li>
<li><a href="#mp_replace_firstl_t">mp_replace_first&lt;L, T&gt;</a></li>
<li><a href="#mp_replace_secondl_t">mp_replace_second&lt;L, T&gt;</a></li>
<li><a href="#mp_replace_thirdl_t">mp_replace_third&lt;L, T&gt;</a></li>
<li><a href="#mp_transform_frontl_f">mp_transform_front&lt;L, F&gt;</a></li>
<li><a href="#mp_transform_front_ql_q">mp_transform_front_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_transform_firstl_f">mp_transform_first&lt;L, F&gt;</a></li>
<li><a href="#mp_transform_first_ql_q">mp_transform_first_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_transform_secondl_f">mp_transform_second&lt;L, F&gt;</a></li>
<li><a href="#mp_transform_second_ql_q">mp_transform_second_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_transform_thirdl_f">mp_transform_third&lt;L, F&gt;</a></li>
<li><a href="#mp_transform_third_ql_q">mp_transform_third_q&lt;L, Q&gt;</a></li>
</ul>
</li>
<li><a href="#utility">Utility Components, &lt;boost/mp11/utility.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#mp_identityt">mp_identity&lt;T&gt;</a></li>
<li><a href="#mp_identity_tt">mp_identity_t&lt;T&gt;</a></li>
<li><a href="#mp_inheritt">mp_inherit&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_if_cc_t_e">mp_if_c&lt;C, T, E&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_ifc_t_e">mp_if&lt;C, T, E&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_eval_if_cc_t_f_u">mp_eval_if_c&lt;C, T, F, U&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_eval_ifc_t_f_u">mp_eval_if&lt;C, T, F, U&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_eval_if_qc_t_q_u">mp_eval_if_q&lt;C, T, Q, U&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_eval_if_notc_t_f_u">mp_eval_if_not&lt;C, T, F, U&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_eval_if_not_qc_t_q_u">mp_eval_if_not_q&lt;C, T, Q, U&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_validf_t">mp_valid&lt;F, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_valid_qq_t">mp_valid_q&lt;Q, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_eval_ort_f_u">mp_eval_or&lt;T, F, U&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_eval_or_qt_q_u">mp_eval_or_q&lt;T, Q, U&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_valid_and_truef_t">mp_valid_and_true&lt;F, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_valid_and_true_qq_t">mp_valid_and_true_q&lt;Q, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_condc_t_r">mp_cond&lt;C, T, R&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_deferf_t">mp_defer&lt;F, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_quotef">mp_quote&lt;F&gt;</a></li>
<li><a href="#mp_quote_traitf">mp_quote_trait&lt;F&gt;</a></li>
<li><a href="#mp_invoke_qq_t">mp_invoke_q&lt;Q, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_not_fnp">mp_not_fn&lt;P&gt;</a></li>
<li><a href="#mp_not_fn_qq">mp_not_fn_q&lt;Q&gt;</a></li>
<li><a href="#mp_composef">mp_compose&lt;F&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_compose_qq">mp_compose_q&lt;Q&#8230;&#8203;&gt;</a></li>
</ul>
</li>
<li><a href="#algorithm">Algorithms, &lt;boost/mp11/algorithm.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#mp_transformf_l">mp_transform&lt;F, L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_transform_qq_l">mp_transform_q&lt;Q, L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_transform_ifp_f_l">mp_transform_if&lt;P, F, L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_transform_if_qqp_qf_l">mp_transform_if_q&lt;Qp, Qf, L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_filterp_l">mp_filter&lt;P, L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_filter_qqp_l">mp_filter_q&lt;Qp, L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_filll_v">mp_fill&lt;L, V&gt;</a></li>
<li><a href="#mp_countl_v">mp_count&lt;L, V&gt;</a></li>
<li><a href="#mp_count_ifl_p">mp_count_if&lt;L, P&gt;</a></li>
<li><a href="#mp_count_if_ql_q">mp_count_if_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_containsl_v">mp_contains&lt;L, V&gt;</a></li>
<li><a href="#mp_starts_withl1_l2">mp_starts_with&lt;L1, L2&gt;</a></li>
<li><a href="#mp_repeat_cl_n">mp_repeat_c&lt;L, N&gt;</a></li>
<li><a href="#mp_repeatl_n">mp_repeat&lt;L, N&gt;</a></li>
<li><a href="#mp_productf_l">mp_product&lt;F, L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_product_qq_l">mp_product_q&lt;Q, L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_power_setl">mp_power_set&lt;L&gt;</a></li>
<li><a href="#mp_drop_cl_n">mp_drop_c&lt;L, N&gt;</a></li>
<li><a href="#mp_dropl_n">mp_drop&lt;L, N&gt;</a></li>
<li><a href="#mp_from_sequences">mp_from_sequence&lt;S&gt;</a></li>
<li><a href="#mp_iota_cn">mp_iota_c&lt;N&gt;</a></li>
<li><a href="#mp_iotan">mp_iota&lt;N&gt;</a></li>
<li><a href="#mp_at_cl_i">mp_at_c&lt;L, I&gt;</a></li>
<li><a href="#mp_atl_i">mp_at&lt;L, I&gt;</a></li>
<li><a href="#mp_take_cl_n">mp_take_c&lt;L, N&gt;</a></li>
<li><a href="#mp_takel_n">mp_take&lt;L, N&gt;</a></li>
<li><a href="#mp_backl">mp_back&lt;L&gt;</a></li>
<li><a href="#mp_pop_backl">mp_pop_back&lt;L&gt;</a></li>
<li><a href="#mp_insert_cl_i_t">mp_insert_c&lt;L, I, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_insertl_i_t">mp_insert&lt;L, I, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_erase_cl_i_j">mp_erase_c&lt;L, I, J&gt;</a></li>
<li><a href="#mp_erasel_i_j">mp_erase&lt;L, I, J&gt;</a></li>
<li><a href="#mp_replacel_v_w">mp_replace&lt;L, V, W&gt;</a></li>
<li><a href="#mp_replace_ifl_p_w">mp_replace_if&lt;L, P, W&gt;</a></li>
<li><a href="#mp_replace_if_ql_q_w">mp_replace_if_q&lt;L, Q, W&gt;</a></li>
<li><a href="#mp_replace_at_cl_i_w">mp_replace_at_c&lt;L, I, W&gt;</a></li>
<li><a href="#mp_replace_atl_i_w">mp_replace_at&lt;L, I, W&gt;</a></li>
<li><a href="#mp_rotate_left_cl_n">mp_rotate_left_c&lt;L, N&gt;</a></li>
<li><a href="#mp_rotate_leftl_n">mp_rotate_left&lt;L, N&gt;</a></li>
<li><a href="#mp_rotate_right_cl_n">mp_rotate_right_c&lt;L, N&gt;</a></li>
<li><a href="#mp_rotate_rightl_n">mp_rotate_right&lt;L, N&gt;</a></li>
<li><a href="#mp_copy_ifl_p">mp_copy_if&lt;L, P&gt;</a></li>
<li><a href="#mp_copy_if_ql_q">mp_copy_if_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_removel_v">mp_remove&lt;L, V&gt;</a></li>
<li><a href="#mp_remove_ifl_p">mp_remove_if&lt;L, P&gt;</a></li>
<li><a href="#mp_remove_if_ql_q">mp_remove_if_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_flattenl">mp_flatten&lt;L&gt;</a></li>
<li><a href="#mp_interspersel_s">mp_intersperse&lt;L, S&gt;</a></li>
<li><a href="#mp_splitl_s">mp_split&lt;L, S&gt;</a></li>
<li><a href="#mp_joinl_s">mp_join&lt;L, S&gt;</a></li>
<li><a href="#mp_partitionl_p">mp_partition&lt;L, P&gt;</a></li>
<li><a href="#mp_partition_ql_q">mp_partition_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_sortl_p">mp_sort&lt;L, P&gt;</a></li>
<li><a href="#mp_sort_ql_q">mp_sort_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_nth_element_cl_i_p">mp_nth_element_c&lt;L, I, P&gt;</a></li>
<li><a href="#mp_nth_elementl_i_p">mp_nth_element&lt;L, I, P&gt;</a></li>
<li><a href="#mp_nth_element_ql_i_q">mp_nth_element_q&lt;L, I, Q&gt;</a></li>
<li><a href="#mp_min_elementl_p">mp_min_element&lt;L, P&gt;</a></li>
<li><a href="#mp_min_element_ql_q">mp_min_element_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_max_elementl_p">mp_max_element&lt;L, P&gt;</a></li>
<li><a href="#mp_max_element_ql_q">mp_max_element_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_findl_v">mp_find&lt;L, V&gt;</a></li>
<li><a href="#mp_find_ifl_p">mp_find_if&lt;L, P&gt;</a></li>
<li><a href="#mp_find_if_ql_q">mp_find_if_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_reversel">mp_reverse&lt;L&gt;</a></li>
<li><a href="#mp_foldl_v_f">mp_fold&lt;L, V, F&gt;</a></li>
<li><a href="#mp_fold_ql_v_q">mp_fold_q&lt;L, V, Q&gt;</a></li>
<li><a href="#mp_reverse_foldl_v_f">mp_reverse_fold&lt;L, V, F&gt;</a></li>
<li><a href="#mp_reverse_fold_ql_v_q">mp_reverse_fold_q&lt;L, V, Q&gt;</a></li>
<li><a href="#mp_partial_suml_v_f">mp_partial_sum&lt;L, V, F&gt;</a></li>
<li><a href="#mp_partial_sum_ql_v_q">mp_partial_sum_q&lt;L, V, Q&gt;</a></li>
<li><a href="#mp_pairwise_foldl_f">mp_pairwise_fold&lt;L, F&gt;</a></li>
<li><a href="#mp_pairwise_fold_ql_q">mp_pairwise_fold_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_iteratev_f_r">mp_iterate&lt;V, F, R&gt;</a></li>
<li><a href="#mp_iterate_qv_qf_qr">mp_iterate_q&lt;V, Qf, Qr&gt;</a></li>
<li><a href="#mp_uniquel">mp_unique&lt;L&gt;</a></li>
<li><a href="#mp_unique_ifl_p">mp_unique_if&lt;L, P&gt;</a></li>
<li><a href="#mp_unique_if_ql_q">mp_unique_if_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_all_ofl_p">mp_all_of&lt;L, P&gt;</a></li>
<li><a href="#mp_all_of_ql_q">mp_all_of_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_none_ofl_p">mp_none_of&lt;L, P&gt;</a></li>
<li><a href="#mp_none_of_ql_q">mp_none_of_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_any_ofl_p">mp_any_of&lt;L, P&gt;</a></li>
<li><a href="#mp_any_of_ql_q">mp_any_of_q&lt;L, Q&gt;</a></li>
<li><a href="#mp_for_eachlf">mp_for_each&lt;L&gt;(f)</a></li>
<li><a href="#mp_with_indexni_f">mp_with_index&lt;N&gt;(i, f)</a></li>
</ul>
</li>
<li><a href="#set">Set Operations, &lt;boost/mp11/set.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#mp_is_sets">mp_is_set&lt;S&gt;</a></li>
<li><a href="#mp_set_containss_v">mp_set_contains&lt;S, V&gt;</a></li>
<li><a href="#mp_set_push_backs_t">mp_set_push_back&lt;S, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_set_push_fronts_t">mp_set_push_front&lt;S, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_set_unionl">mp_set_union&lt;L&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_set_intersections">mp_set_intersection&lt;S&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_set_differencel_s">mp_set_difference&lt;L, S&#8230;&#8203;&gt;</a></li>
</ul>
</li>
<li><a href="#map">Map Operations, &lt;boost/mp11/map.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#mp_is_mapm">mp_is_map&lt;M&gt;</a></li>
<li><a href="#mp_map_findm_k">mp_map_find&lt;M, K&gt;</a></li>
<li><a href="#mp_map_containsm_k">mp_map_contains&lt;M, K&gt;</a></li>
<li><a href="#mp_map_insertm_t">mp_map_insert&lt;M, T&gt;</a></li>
<li><a href="#mp_map_replacem_t">mp_map_replace&lt;M, T&gt;</a></li>
<li><a href="#mp_map_updatem_t_f">mp_map_update&lt;M, T, F&gt;</a></li>
<li><a href="#mp_map_update_qm_t_q">mp_map_update_q&lt;M, T, Q&gt;</a></li>
<li><a href="#mp_map_erasem_k">mp_map_erase&lt;M, K&gt;</a></li>
<li><a href="#mp_map_keysm">mp_map_keys&lt;M&gt;</a></li>
</ul>
</li>
<li><a href="#function">Helper Metafunctions, &lt;boost/mp11/function.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#mp_voidt">mp_void&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_andt">mp_and&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_allt">mp_all&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_ort">mp_or&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_anyt">mp_any&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_samet">mp_same&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_similart">mp_similar&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_plust">mp_plus&lt;T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_lesst1_t2">mp_less&lt;T1, T2&gt;</a></li>
<li><a href="#mp_mint1_t">mp_min&lt;T1, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_maxt1_t">mp_max&lt;T1, T&#8230;&#8203;&gt;</a></li>
</ul>
</li>
<li><a href="#bind">Bind, &lt;boost/mp11/bind.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#mp_argi">mp_arg&lt;I&gt;</a></li>
<li><a href="#1_9">_1, &#8230;&#8203;, _9</a></li>
<li><a href="#mp_bindf_t">mp_bind&lt;F, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_bind_qq_t">mp_bind_q&lt;Q, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_bind_frontf_t">mp_bind_front&lt;F, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_bind_front_qq_t">mp_bind_front_q&lt;Q, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_bind_backf_t">mp_bind_back&lt;F, T&#8230;&#8203;&gt;</a></li>
<li><a href="#mp_bind_back_qq_t">mp_bind_back_q&lt;Q, T&#8230;&#8203;&gt;</a></li>
</ul>
</li>
<li><a href="#integer_sequence">Integer Sequences, &lt;boost/mp11/integer_sequence.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#integer_sequencet_i">integer_sequence&lt;T, I&#8230;&#8203;&gt;</a></li>
<li><a href="#make_integer_sequencet_n">make_integer_sequence&lt;T, N&gt;</a></li>
<li><a href="#index_sequencei">index_sequence&lt;I&#8230;&#8203;&gt;</a></li>
<li><a href="#make_index_sequencen">make_index_sequence&lt;N&gt;</a></li>
<li><a href="#index_sequence_fort">index_sequence_for&lt;T&#8230;&#8203;&gt;</a></li>
</ul>
</li>
<li><a href="#tuple">Tuple Operations, &lt;boost/mp11/tuple.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#tuple_applyf_tp">tuple_apply(f, tp)</a></li>
<li><a href="#construct_from_tuplettp">construct_from_tuple&lt;T&gt;(tp)</a></li>
<li><a href="#tuple_for_eachtp_f">tuple_for_each(tp, f)</a></li>
<li><a href="#tuple_transformf_tp">tuple_transform(f, tp&#8230;&#8203;)</a></li>
</ul>
</li>
<li><a href="#mp11">Convenience Header, &lt;boost/mp11.hpp&gt;</a></li>
<li><a href="#mpl">MPL Support, &lt;boost/mp11/mpl.hpp&gt;</a></li>
</ul>
</li>
<li><a href="#copyright_license_and_acknowledgments">Appendix A: Copyright, License, and Acknowledgments</a></li>
</ul>
</div>
</div>
<div id="content">
<div class="sect1">
<h2 id="overview">Overview</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Mp11 is a C++11 metaprogramming library for compile-time manipulation of data structures
that contain types. It&#8217;s based on template aliases and variadic templates and implements the
approach outlined in the article
<a href="simple_cxx11_metaprogramming.html">"Simple C&#43;&#43; metaprogramming"</a>
and <a href="simple_cxx11_metaprogramming_2.html">its sequel</a>. Reading these
articles before proceeding with this documentation is <em>highly</em> recommended.</p>
</div>
<div class="paragraph">
<p>The general principles upon which Mp11 is built are that algorithms and metafunctions are
template aliases of the form <code>F&lt;T&#8230;&#8203;&gt;</code> and data structures are lists of the form <code>L&lt;T&#8230;&#8203;&gt;</code>,
with the library placing no requirements on <code>L</code>. <code>mp_list&lt;T&#8230;&#8203;&gt;</code> is the built-in list type,
but <code>std::tuple&lt;T&#8230;&#8203;&gt;</code>, <code>std::pair&lt;T1, T2&gt;</code> and <code>std::variant&lt;T&#8230;&#8203;&gt;</code> are also perfectly
legitimate list types, although of course <code>std::pair&lt;T1, T2&gt;</code>, due to having exactly two elements,
is not resizeable and will consequently not work with algorithms that need to add or remove
elements.</p>
</div>
<div class="paragraph">
<p>Another distinguishing feature of this approach is that lists (<code>L&lt;T&#8230;&#8203;&gt;</code>) have the same form as
metafunctions (<code>F&lt;T&#8230;&#8203;&gt;</code>) and can therefore be used as such. For example, applying <code>std::add_pointer_t</code>
to the list <code>std::tuple&lt;int, float&gt;</code> by way of <code>mp_transform&lt;std::add_pointer_t, std::tuple&lt;int, float&gt;&gt;</code>
gives us <code>std::tuple&lt;int*, float*&gt;</code>, but we can also apply <code>mp_list</code> to the same tuple:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>using R = mp_transform&lt;mp_list, std::tuple&lt;int, float&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>and get <code>std::tuple&lt;mp_list&lt;int&gt;, mp_list&lt;float&gt;&gt;</code>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="definitions">Definitions</h2>
<div class="sectionbody">
<div class="paragraph">
<p>A <em>list</em> is a&#8201;&#8212;&#8201;usually but not necessarily variadic&#8201;&#8212;&#8201;template class whose parameters are all types,
for example <code>mp_list&lt;char[], void&gt;</code>, <code>mp_list&lt;&gt;</code>, <code>std::tuple&lt;int, float, char&gt;</code>, <code>std::pair&lt;int, float&gt;</code>, <code>std::shared_ptr&lt;X&gt;</code>.</p>
</div>
<div class="paragraph">
<p>A <em>metafunction</em> is a class template or a template alias whose parameters are all types, for example <code>std::add_pointer_t</code>,
<code>std::is_const</code>, <code>mp_second</code>, <code>mp_push_front</code>, <code>mp_list</code>, <code>std::tuple</code>, <code>std::pair</code>, <code>std::shared_ptr</code>, or</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">F1</span> <span class="o">=</span> <span class="kt">void</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">F2</span> <span class="o">=</span> <span class="n">T</span><span class="o">*</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">F3</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">integral_constant</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">...(</span><span class="n">T</span><span class="p">)</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>A <em>quoted metafunction</em> is a class with a public metafunction member called <code>fn</code>, for example</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">Q1</span> <span class="p">{</span> <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">fn</span> <span class="o">=</span> <span class="kt">void</span><span class="p">;</span> <span class="p">};</span>

<span class="k">struct</span> <span class="nc">Q2</span> <span class="p">{</span> <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">fn</span> <span class="o">=</span> <span class="n">T</span><span class="o">*</span><span class="p">;</span> <span class="p">};</span>

<span class="k">struct</span> <span class="nc">Q3</span> <span class="p">{</span> <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">fn</span> <span class="o">=</span>
  <span class="n">std</span><span class="o">::</span><span class="n">integral_constant</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">...(</span><span class="n">T</span><span class="p">)</span><span class="o">&gt;</span><span class="p">;</span> <span class="p">};</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>An <em>integral constant type</em> is a class with a public member <code>value</code> that is an integral constant in the C++ sense. For example,
<code>std::integral_constant&lt;int, 7&gt;</code>, or</p>
</div>
<div class="literalblock">
<div class="content">
<pre>struct N { static int constexpr value = 2; };</pre>
</div>
</div>
<div class="paragraph">
<p>A <em>set</em> is a list whose elements are unique.</p>
</div>
<div class="paragraph">
<p>A <em>map</em> is a list of lists, the inner lists having at least one element (the key.) The keys of the map must be unique. For example,</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">M1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">int</span><span class="o">*&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">float</span><span class="o">*&gt;</span><span class="p">,</span>
    <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">void</span><span class="o">*&gt;&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">M2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">int</span><span class="o">*&gt;</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">,</span> <span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span><span class="o">&gt;&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="examples">Examples</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="generating_test_cases">Generating Test Cases</h3>
<div class="paragraph">
<p>Let&#8217;s suppose that we have written a metafunction <code>result&lt;T, U&gt;</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">promote</span> <span class="o">=</span> <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">common_type</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span> <span class="kt">int</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">result</span> <span class="o">=</span>
    <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">common_type</span><span class="o">&lt;</span><span class="n">promote</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">promote</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;&gt;::</span><span class="n">type</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>that ought to represent the result of an arithmetic operation on the integer types <code>T</code> and <code>U</code>,
for example <code>t + u</code>. We want to test whether <code>result&lt;T, U&gt;</code> gives correct results for various combinations
of <code>T</code> and <code>U</code>, so we write the function</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T2</span><span class="p">&gt;</span> <span class="kt">void</span> <span class="nf">test_result</span><span class="p">()</span>
<span class="p">{</span>
    <span class="k">using</span> <span class="n">T3</span> <span class="o">=</span> <span class="k">decltype</span><span class="p">(</span> <span class="n">T1</span><span class="p">()</span> <span class="o">+</span> <span class="n">T2</span><span class="p">()</span> <span class="p">);</span>
    <span class="k">using</span> <span class="n">T4</span> <span class="o">=</span> <span class="n">result</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">,</span> <span class="n">T2</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">is_same</span><span class="o">&lt;</span><span class="n">T3</span><span class="p">,</span> <span class="n">T4</span><span class="o">&gt;::</span><span class="n">value</span><span class="o">?</span> <span class="s">"[PASS]"</span><span class="o">:</span> <span class="s">"[FAIL]"</span> <span class="p">)</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>and then need to call it a substantial number of times:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>int main()
{
    test_result&lt;char, char&gt;();
    test_result&lt;char, short&gt;();
    test_result&lt;char, int&gt;();
    test_result&lt;char, unsigned&gt;();
    // ...
}</pre>
</div>
</div>
<div class="paragraph">
<p>Writing all those type combinations by hand is unwieldy, error prone, and worst of all, boring. This is
how we can leverage Mp11 to automate the task:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#include &lt;boost/mp11.hpp&gt;
#include &lt;boost/core/demangle.hpp&gt;
#include &lt;type_traits&gt;
#include &lt;iostream&gt;
#include &lt;typeinfo&gt;
</span>
<span class="k">using</span> <span class="k">namespace</span> <span class="n">boost</span><span class="o">::</span><span class="n">mp11</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="nf">name</span><span class="p">()</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="n">boost</span><span class="o">::</span><span class="n">core</span><span class="o">::</span><span class="n">demangle</span><span class="p">(</span> <span class="k">typeid</span><span class="p">(</span><span class="n">T</span><span class="p">).</span><span class="n">name</span><span class="p">()</span> <span class="p">);</span>
<span class="p">}</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">promote</span> <span class="o">=</span> <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">common_type</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span> <span class="kt">int</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">result</span> <span class="o">=</span>
    <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">common_type</span><span class="o">&lt;</span><span class="n">promote</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">promote</span><span class="o">&lt;</span><span class="n">U</span><span class="o">&gt;&gt;::</span><span class="n">type</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T2</span><span class="p">&gt;</span> <span class="kt">void</span> <span class="nf">test_result</span><span class="p">(</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">,</span> <span class="n">T2</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">using</span> <span class="n">T3</span> <span class="o">=</span> <span class="k">decltype</span><span class="p">(</span> <span class="n">T1</span><span class="p">()</span> <span class="o">+</span> <span class="n">T2</span><span class="p">()</span> <span class="p">);</span>
    <span class="k">using</span> <span class="n">T4</span> <span class="o">=</span> <span class="n">result</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">,</span> <span class="n">T2</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">is_same</span><span class="o">&lt;</span><span class="n">T3</span><span class="p">,</span> <span class="n">T4</span><span class="o">&gt;::</span><span class="n">value</span><span class="o">?</span> <span class="s">"[PASS] "</span><span class="o">:</span> <span class="s">"[FAIL] "</span> <span class="p">)</span>
        <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="n">T1</span><span class="o">&gt;</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="s">" + "</span> <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="n">T2</span><span class="o">&gt;</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="s">" -&gt; "</span> <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="n">T3</span><span class="o">&gt;</span><span class="p">()</span>
        <span class="o">&lt;&lt;</span> <span class="s">", result: "</span> <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="n">T4</span><span class="o">&gt;</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="p">}</span>

<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
    <span class="k">using</span> <span class="n">L</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">,</span> <span class="kt">short</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">unsigned</span><span class="p">,</span> <span class="kt">long</span><span class="p">,</span> <span class="kt">unsigned</span> <span class="kt">long</span><span class="o">&gt;</span><span class="p">;</span>
    <span class="n">tuple_for_each</span><span class="p">(</span> <span class="n">mp_product</span><span class="o">&lt;</span><span class="n">mp_list</span><span class="p">,</span> <span class="n">L</span><span class="p">,</span> <span class="n">L</span><span class="o">&gt;</span><span class="p">(),</span> <span class="p">[](</span><span class="k">auto</span><span class="o">&amp;&amp;</span> <span class="n">x</span><span class="p">){</span> <span class="n">test_result</span><span class="p">(</span><span class="n">x</span><span class="p">);</span> <span class="p">}</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>How does it work?</p>
</div>
<div class="paragraph">
<p><code>mp_product&lt;F, L1, L2&gt;</code> calls <code>F&lt;T1, T2&gt;</code> where <code>T1</code> varies over the elements of <code>L1</code> and <code>T2</code> varies over
the elements of <code>L2</code>, as if by executing two nested loops. It then returns a list of these results, of the same
type as <code>L1</code>.</p>
</div>
<div class="paragraph">
<p>In our case, both lists are the same <code>std::tuple</code>, and <code>F</code> is <code>mp_list</code>, so <code>mp_product&lt;mp_list, L, L&gt;</code> will get us
<code>std::tuple&lt;mp_list&lt;char, char&gt;, mp_list&lt;char, short&gt;, mp_list&lt;char, int&gt;, &#8230;&#8203;, mp_list&lt;unsigned long, long&gt;, mp_list&lt;unsigned long, unsigned long&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>We then default-construct this tuple and pass it to <code>tuple_for_each</code>. <code>tuple_for_each(tp, f)</code> calls <code>f</code> for every
tuple element; we use a (C++14) lambda that calls <code>test_result</code>.</p>
</div>
<div class="paragraph">
<p>In pure C++11, we can&#8217;t use a lambda with an <code>auto&amp;&amp;</code> parameter, so we&#8217;ll have to make <code>test_result</code> a function object with
a templated <code>operator()</code> and pass that to <code>tuple_for_each</code> directly:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">test_result</span>
<span class="p">{</span>
    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T2</span><span class="p">&gt;</span> <span class="kt">void</span> <span class="k">operator</span><span class="p">()(</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">,</span> <span class="n">T2</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="p">)</span> <span class="k">const</span>
    <span class="p">{</span>
        <span class="k">using</span> <span class="n">T3</span> <span class="o">=</span> <span class="k">decltype</span><span class="p">(</span> <span class="n">T1</span><span class="p">()</span> <span class="o">+</span> <span class="n">T2</span><span class="p">()</span> <span class="p">);</span>
        <span class="k">using</span> <span class="n">T4</span> <span class="o">=</span> <span class="n">result</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">,</span> <span class="n">T2</span><span class="o">&gt;</span><span class="p">;</span>

        <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">is_same</span><span class="o">&lt;</span><span class="n">T3</span><span class="p">,</span> <span class="n">T4</span><span class="o">&gt;::</span><span class="n">value</span><span class="o">?</span> <span class="s">"[PASS] "</span><span class="o">:</span> <span class="s">"[FAIL] "</span> <span class="p">)</span>
            <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="n">T1</span><span class="o">&gt;</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="s">" + "</span> <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="n">T2</span><span class="o">&gt;</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="s">" -&gt; "</span> <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="n">T3</span><span class="o">&gt;</span><span class="p">()</span>
            <span class="o">&lt;&lt;</span> <span class="s">", result: "</span> <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="n">T4</span><span class="o">&gt;</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
    <span class="p">}</span>
<span class="p">};</span>

<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
    <span class="k">using</span> <span class="n">L</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">,</span> <span class="kt">short</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">unsigned</span><span class="p">,</span> <span class="kt">long</span><span class="p">,</span> <span class="kt">unsigned</span> <span class="kt">long</span><span class="o">&gt;</span><span class="p">;</span>
    <span class="n">tuple_for_each</span><span class="p">(</span> <span class="n">mp_product</span><span class="o">&lt;</span><span class="n">mp_list</span><span class="p">,</span> <span class="n">L</span><span class="p">,</span> <span class="n">L</span><span class="o">&gt;</span><span class="p">(),</span> <span class="n">test_result</span><span class="p">()</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="writing_common_type_specializations">Writing common_type Specializations</h3>
<div class="paragraph">
<p>The standard trait <code>std::common_type</code>, used to obtain a type to which all of its arguments can convert without
unnecessary loss of precision, can be user-specialized when its default implementation (based on the ternary <code>?:</code>
operator) is unsuitable.</p>
</div>
<div class="paragraph">
<p>Let&#8217;s write a <code>common_type</code> specialization for two <code>std::tuple</code> arguments. For that, we need a metafunction that
applies <code>std::common_type</code> to each pair of elements and gathers the results into a tuple:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">common_type_t</span> <span class="o">=</span>
    <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">common_type</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">;</span> <span class="c1">// standard in C++14</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">Tp1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tp2</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">common_tuple</span> <span class="o">=</span>
    <span class="n">mp_transform</span><span class="o">&lt;</span><span class="n">common_type_t</span><span class="p">,</span> <span class="n">Tp1</span><span class="p">,</span> <span class="n">Tp2</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>then specialize <code>common_type</code> to use it:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">std</span>
<span class="p">{</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T1</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">T2</span><span class="p">&gt;</span>
    <span class="k">struct</span> <span class="nc">common_type</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">...</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="n">T2</span><span class="p">...</span><span class="o">&gt;&gt;:</span>
        <span class="n">mp_defer</span><span class="o">&lt;</span><span class="n">common_tuple</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">...</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="n">T2</span><span class="p">...</span><span class="o">&gt;&gt;</span>
    <span class="p">{</span>
    <span class="p">};</span>

<span class="p">}</span> <span class="c1">// std</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>(There is no need to specialize <code>std::common_type</code> for more than two arguments - it takes care of synthesizing the appropriate semantics from
the binary case.)</p>
</div>
<div class="paragraph">
<p>The subtlety here is the use of <code>mp_defer</code>. We could have defined a nested <code>type</code> to <code>common_tuple&lt;std::tuple&lt;T1&#8230;&#8203;&gt;, std::tuple&lt;T2&#8230;&#8203;&gt;&gt;</code>,
and it would still have worked in all valid cases. By letting <code>mp_defer</code> define <code>type</code>, though, we make our specialization <em>SFINAE-friendly</em>.</p>
</div>
<div class="paragraph">
<p>That is, when our <code>common_tuple</code> causes a substitution failure instead of a hard error, <code>mp_defer</code> will not define a nested <code>type</code>,
and <code>common_type_t</code>, which is defined as <code>typename common_type&lt;&#8230;&#8203;&gt;::type</code>, will also cause a substitution failure.</p>
</div>
<div class="paragraph">
<p>As another example, consider the hypothetical type <code>expected&lt;T, E&#8230;&#8203;&gt;</code> that represents either a successful return with a value of <code>T</code>,
or an unsuccessful return with an error code of some type in the list <code>E&#8230;&#8203;</code>. The common type of <code>expected&lt;T1, E1, E2, E3&gt;</code> and
<code>expected&lt;T2, E1, E4, E5&gt;</code> is <code>expected&lt;common_type_t&lt;T1, T2&gt;, E1, E2, E3, E4, E5&gt;</code>. That is, the possible return values are
combined into their common type, and we take the union of the set of error types.</p>
</div>
<div class="paragraph">
<p>Therefore,</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T2</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E2</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">common_expected</span> <span class="o">=</span>
    <span class="n">mp_rename</span><span class="o">&lt;</span><span class="n">mp_push_front</span><span class="o">&lt;</span><span class="n">mp_unique</span><span class="o">&lt;</span><span class="n">mp_append</span><span class="o">&lt;</span><span class="n">E1</span><span class="p">,</span> <span class="n">E2</span><span class="o">&gt;&gt;</span><span class="p">,</span> <span class="n">common_type_t</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">,</span> <span class="n">T2</span><span class="o">&gt;&gt;</span><span class="p">,</span>
        <span class="n">expected</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">namespace</span> <span class="n">std</span>
<span class="p">{</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T1</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">E1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T2</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">E2</span><span class="p">&gt;</span>
    <span class="k">struct</span> <span class="nc">common_type</span><span class="o">&lt;</span><span class="n">expected</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">,</span> <span class="n">E1</span><span class="p">...</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">expected</span><span class="o">&lt;</span><span class="n">T2</span><span class="p">,</span> <span class="n">E2</span><span class="p">...</span><span class="o">&gt;&gt;:</span>
        <span class="n">mp_defer</span><span class="o">&lt;</span><span class="n">common_expected</span><span class="p">,</span> <span class="n">T1</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">E1</span><span class="p">...</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">T2</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">E2</span><span class="p">...</span><span class="o">&gt;&gt;</span>
    <span class="p">{</span>
    <span class="p">};</span>

<span class="p">}</span> <span class="c1">// std</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Here we&#8217;ve taken a different tack; instead of passing the <code>expected</code> types to <code>common_expected</code>, we&#8217;re passing the <code>T</code> types and lists of
the <code>E</code> types. This makes our job easier. <code>mp_unique&lt;mp_append&lt;E1, E2&gt;&gt;</code> gives us the concatenation of <code>E1</code> and <code>E2</code> with the duplicates
removed; we then add <code>common_type_t&lt;T1, T2&gt;</code> to the front via <code>mp_push_front</code>; and finally, we <code>mp_rename</code> the resultant <code>mp_list</code>
to <code>expected</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="fixing_tuple_cat">Fixing tuple_cat</h3>
<div class="paragraph">
<p>The article <a href="http://pdimov.com/cpp2/simple_cxx11_metaprogramming.html#">Simple C++11 metaprogramming</a> builds an
implementation of the standard function <code>tuple_cat</code>, with the end result given below:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">L</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">F</span> <span class="o">=</span> <span class="n">mp_iota</span><span class="o">&lt;</span><span class="n">mp_size</span><span class="o">&lt;</span><span class="n">L</span><span class="o">&gt;&gt;</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span><span class="nc">Is</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">Ks</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tp</span><span class="p">&gt;</span>
<span class="n">R</span> <span class="nf">tuple_cat_</span><span class="p">(</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">Is</span><span class="p">...</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">Ks</span><span class="p">...</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">Tp</span> <span class="n">tp</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="n">R</span><span class="p">{</span> <span class="n">std</span><span class="o">::</span><span class="n">get</span><span class="o">&lt;</span><span class="n">Ks</span><span class="o">::</span><span class="n">value</span><span class="o">&gt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">get</span><span class="o">&lt;</span><span class="n">Is</span><span class="o">::</span><span class="n">value</span><span class="o">&gt;</span><span class="p">(</span><span class="n">tp</span><span class="p">))...</span> <span class="p">};</span>
<span class="p">}</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">Tp</span><span class="p">,</span>
    <span class="k">class</span> <span class="nc">R</span> <span class="o">=</span> <span class="n">mp_append</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="p">&gt;,</span> <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">remove_reference</span><span class="o">&lt;</span><span class="n">Tp</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">...</span><span class="o">&gt;&gt;</span>
    <span class="n">R</span> <span class="nf">tuple_cat</span><span class="p">(</span> <span class="n">Tp</span> <span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">tp</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="k">const</span> <span class="n">N</span> <span class="o">=</span> <span class="k">sizeof</span><span class="p">...(</span><span class="n">Tp</span><span class="p">);</span>

    <span class="c1">// inner</span>

    <span class="k">using</span> <span class="n">list1</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span>
        <span class="n">mp_rename</span><span class="o">&lt;</span><span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">remove_reference</span><span class="o">&lt;</span><span class="n">Tp</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="k">using</span> <span class="n">list2</span> <span class="o">=</span> <span class="n">mp_iota_c</span><span class="o">&lt;</span><span class="n">N</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="k">using</span> <span class="n">list3</span> <span class="o">=</span> <span class="n">mp_transform</span><span class="o">&lt;</span><span class="n">mp_fill</span><span class="p">,</span> <span class="n">list1</span><span class="p">,</span> <span class="n">list2</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="k">using</span> <span class="n">inner</span> <span class="o">=</span> <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_append</span><span class="p">,</span> <span class="n">list3</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="c1">// outer</span>

    <span class="k">using</span> <span class="n">list4</span> <span class="o">=</span> <span class="n">mp_transform</span><span class="o">&lt;</span><span class="n">F</span><span class="p">,</span> <span class="n">list1</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="k">using</span> <span class="n">outer</span> <span class="o">=</span> <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_append</span><span class="p">,</span> <span class="n">list4</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="c1">//</span>

    <span class="k">return</span> <span class="n">tuple_cat_</span><span class="o">&lt;</span><span class="n">R</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">inner</span><span class="p">(),</span> <span class="n">outer</span><span class="p">(),</span>
        <span class="n">std</span><span class="o">::</span><span class="n">forward_as_tuple</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">forward</span><span class="o">&lt;</span><span class="n">Tp</span><span class="o">&gt;</span><span class="p">(</span><span class="n">tp</span><span class="p">)...</span> <span class="p">)</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This function, however, is not entirely correct, in that it doesn&#8217;t handle some cases properly. For example,
trying to concatenate tuples containing move-only elements such as <code>unique_ptr</code> fails:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">unique_ptr</span><span class="o">&lt;</span><span class="kt">int</span><span class="o">&gt;&gt;</span> <span class="n">t1</span><span class="p">;</span>
<span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">unique_ptr</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;&gt;</span> <span class="n">t2</span><span class="p">;</span>

<span class="k">auto</span> <span class="n">result</span> <span class="o">=</span> <span class="o">::</span><span class="n">tuple_cat</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">move</span><span class="p">(</span> <span class="n">t1</span> <span class="p">),</span> <span class="n">std</span><span class="o">::</span><span class="n">move</span><span class="p">(</span> <span class="n">t2</span> <span class="p">)</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Trying to concatenate <code>const</code> tuples fails:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">int</span><span class="o">&gt;</span> <span class="k">const</span> <span class="n">t1</span><span class="p">;</span>
<span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span> <span class="k">const</span> <span class="n">t2</span><span class="p">;</span>

<span class="k">auto</span> <span class="n">result</span> <span class="o">=</span> <span class="o">::</span><span class="n">tuple_cat</span><span class="p">(</span> <span class="n">t1</span><span class="p">,</span> <span class="n">t2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>And finally, the standard <code>tuple_cat</code> is specified to work on arbitrary tuple-like types (that is, all types
that support <code>tuple_size</code>, <code>tuple_element</code>, and <code>get</code>), while our implementation only works with <code>tuple</code> and
<code>pair</code>. <code>std::array</code>, for example, fails:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">array</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="mi">2</span><span class="o">&gt;</span> <span class="n">t1</span><span class="p">{</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span> <span class="p">};</span>
<span class="n">std</span><span class="o">::</span><span class="n">array</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="mi">3</span><span class="o">&gt;</span> <span class="n">t2</span><span class="p">{</span> <span class="mf">3.0</span><span class="n">f</span><span class="p">,</span> <span class="mf">4.0</span><span class="n">f</span><span class="p">,</span> <span class="mf">5.0</span><span class="n">f</span> <span class="p">};</span>

<span class="k">auto</span> <span class="n">result</span> <span class="o">=</span> <span class="o">::</span><span class="n">tuple_cat</span><span class="p">(</span> <span class="n">t1</span><span class="p">,</span> <span class="n">t2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Let&#8217;s fix these one by one. Support for move-only types is easy, if one knows where to look. The problem is
that <code>Tp</code> that we&#8217;re passing to the helper <code>tuple_cat_</code> is (correctly) <code>tuple&lt;unique_ptr&lt;int&gt;&amp;&amp;, unique_ptr&lt;float&gt;&amp;&amp;&gt;</code>,
but <code>std::get&lt;0&gt;(tp)</code> still returns <code>unique_ptr&lt;int&gt;&amp;</code>, because <code>tp</code> is an lvalue. This behavior is a bit
surprising, but is intended to prevent inadvertent double moves.</p>
</div>
<div class="paragraph">
<p>Long story short, we need <code>std::move(tp)</code> in <code>tuple_cat_</code> to make <code>tp</code> an rvalue:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>template&lt;class R, class...Is, class... Ks, class Tp&gt;
R tuple_cat_( mp_list&lt;Is...&gt;, mp_list&lt;Ks...&gt;, Tp tp )
{
    return R{ std::get&lt;Ks::value&gt;(std::get&lt;Is::value&gt;(std::move(tp)))... };
}</pre>
</div>
</div>
<div class="paragraph">
<p>Next, <code>const</code>-qualified tuples. The issue here is that we&#8217;re stripping references from the input tuples, but not
<code>const</code>. As a result, we&#8217;re trying to manipulate types such as <code>tuple&lt;int&gt; const</code> with Mp11 algorithms, and these
types do not fit the list concept. We just need to strip qualifiers as well, by defining the useful <code>remove_cv_ref</code>
primitive that is inexplicably missing from the standard library:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T&gt; using remove_cv_ref = typename std::remove_cv&lt;
    typename std::remove_reference&lt;T&gt;::type&gt;::type;</pre>
</div>
</div>
<div class="paragraph">
<p>and then by using <code>remove_cv_ref&lt;Tp&gt;</code> in place of <code>typename std::remove_reference&lt;Tp&gt;::type</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">Tp</span><span class="p">,</span>
    <span class="k">class</span> <span class="nc">R</span> <span class="o">=</span> <span class="n">mp_append</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="p">&gt;,</span> <span class="n">remove_cv_ref</span><span class="o">&lt;</span><span class="n">Tp</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;&gt;</span>
    <span class="n">R</span> <span class="nf">tuple_cat</span><span class="p">(</span> <span class="n">Tp</span> <span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">tp</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="k">const</span> <span class="n">N</span> <span class="o">=</span> <span class="k">sizeof</span><span class="p">...(</span><span class="n">Tp</span><span class="p">);</span>

    <span class="c1">// inner</span>

    <span class="k">using</span> <span class="n">list1</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">mp_rename</span><span class="o">&lt;</span><span class="n">remove_cv_ref</span><span class="o">&lt;</span><span class="n">Tp</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="c1">// ...</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Finally, tuple-like types. We&#8217;ve so far exploited the fact that <code>std::pair</code> and <code>std::tuple</code> are valid Mp11 lists,
but in general, arbitrary tuple-like types aren&#8217;t, so we need to convert them into such. For that, we&#8217;ll need to
define a metafunction <code>from_tuple_like</code> that will take an arbitrary tuple-like type and will return, in our case,
the corresponding <code>mp_list</code>.</p>
</div>
<div class="paragraph">
<p>Technically, a more principled approach would&#8217;ve been to return <code>std::tuple</code>, but here <code>mp_list</code> will prove more
convenient.</p>
</div>
<div class="paragraph">
<p>What we need is, given a tuple-like type <code>Tp</code>, to obtain <code>mp_list&lt;std::tuple_element&lt;0, Tp&gt;::type, std::tuple_element&lt;1, Tp&gt;::type,
&#8230;&#8203;, std::tuple_element&lt;N-1, Tp&gt;::type&gt;</code>, where <code>N</code> is <code>tuple_size&lt;Tp&gt;::value</code>. Here&#8217;s one way to do it:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">I</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">tuple_element</span> <span class="o">=</span>
    <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple_element</span><span class="o">&lt;</span><span class="n">I</span><span class="o">::</span><span class="n">value</span><span class="p">,</span> <span class="n">T</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">from_tuple_like</span> <span class="o">=</span>
    <span class="n">mp_product</span><span class="o">&lt;</span><span class="n">tuple_element</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_iota</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">tuple_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;&gt;&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>(<code>mp_iota&lt;N&gt;</code> is an algorithm that returns an <code>mp_list</code> with elements <code>mp_size_t&lt;0&gt;</code>, <code>mp_size_t&lt;1&gt;</code>, &#8230;&#8203;, <code>mp_size_t&lt;N-1&gt;</code>.)</p>
</div>
<div class="paragraph">
<p>Remember that <code>mp_product&lt;F, L1, L2&gt;</code> performs the equivalent of two nested loops over the elements of <code>L1</code> and <code>L2</code>,
applying <code>F</code> to the two variables and gathering the result. In our case <code>L1</code> consists of the single element <code>T</code>, so
only the second loop (over <code>mp_iota&lt;N&gt;</code>, where <code>N</code> is <code>tuple_size&lt;T&gt;</code>), remains, and we get a list of the same type
as <code>L1</code> (an <code>mp_list</code>) with contents <code>tuple_element&lt;T, mp_size_t&lt;0&gt;&gt;</code>, <code>tuple_element&lt;T, mp_size_t&lt;1&gt;&gt;</code>, &#8230;&#8203;,
<code>tuple_element&lt;T, mp_size_t&lt;N-1&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>For completeness&#8217;s sake, here&#8217;s another, more traditional way to achieve the same result:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T&gt; using from_tuple_like =
    mp_transform_q&lt;mp_bind_front&lt;tuple_element, T&gt;, mp_iota&lt;std::tuple_size&lt;T&gt;&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>With all these fixes applied, our fully operational <code>tuple_cat</code> now looks like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">L</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">F</span> <span class="o">=</span> <span class="n">mp_iota</span><span class="o">&lt;</span><span class="n">mp_size</span><span class="o">&lt;</span><span class="n">L</span><span class="o">&gt;&gt;</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">R</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span><span class="nc">Is</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">Ks</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tp</span><span class="p">&gt;</span>
<span class="n">R</span> <span class="nf">tuple_cat_</span><span class="p">(</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">Is</span><span class="p">...</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">Ks</span><span class="p">...</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">Tp</span> <span class="n">tp</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="n">R</span><span class="p">{</span> <span class="n">std</span><span class="o">::</span><span class="n">get</span><span class="o">&lt;</span><span class="n">Ks</span><span class="o">::</span><span class="n">value</span><span class="o">&gt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">get</span><span class="o">&lt;</span><span class="n">Is</span><span class="o">::</span><span class="n">value</span><span class="o">&gt;</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">move</span><span class="p">(</span><span class="n">tp</span><span class="p">)))...</span> <span class="p">};</span>
<span class="p">}</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">remove_cv_ref</span> <span class="o">=</span> <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">remove_cv</span><span class="o">&lt;</span>
    <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">remove_reference</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;::</span><span class="n">type</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">I</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">tuple_element</span> <span class="o">=</span>
    <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple_element</span><span class="o">&lt;</span><span class="n">I</span><span class="o">::</span><span class="n">value</span><span class="p">,</span> <span class="n">T</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">from_tuple_like</span> <span class="o">=</span>
    <span class="n">mp_product</span><span class="o">&lt;</span><span class="n">tuple_element</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_iota</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">tuple_size</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;&gt;&gt;</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">Tp</span><span class="p">,</span>
    <span class="k">class</span> <span class="nc">R</span> <span class="o">=</span> <span class="n">mp_append</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="p">&gt;,</span> <span class="n">from_tuple_like</span><span class="o">&lt;</span><span class="n">remove_cv_ref</span><span class="o">&lt;</span><span class="n">Tp</span><span class="o">&gt;&gt;</span><span class="p">...</span><span class="o">&gt;&gt;</span>
    <span class="n">R</span> <span class="nf">tuple_cat</span><span class="p">(</span> <span class="n">Tp</span> <span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">tp</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="k">const</span> <span class="n">N</span> <span class="o">=</span> <span class="k">sizeof</span><span class="p">...(</span><span class="n">Tp</span><span class="p">);</span>

    <span class="c1">// inner</span>

    <span class="k">using</span> <span class="n">list1</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">from_tuple_like</span><span class="o">&lt;</span><span class="n">remove_cv_ref</span><span class="o">&lt;</span><span class="n">Tp</span><span class="o">&gt;&gt;</span><span class="p">...</span><span class="o">&gt;</span><span class="p">;</span>
    <span class="k">using</span> <span class="n">list2</span> <span class="o">=</span> <span class="n">mp_iota_c</span><span class="o">&lt;</span><span class="n">N</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="k">using</span> <span class="n">list3</span> <span class="o">=</span> <span class="n">mp_transform</span><span class="o">&lt;</span><span class="n">mp_fill</span><span class="p">,</span> <span class="n">list1</span><span class="p">,</span> <span class="n">list2</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="k">using</span> <span class="n">inner</span> <span class="o">=</span> <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_append</span><span class="p">,</span> <span class="n">list3</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="c1">// outer</span>

    <span class="k">using</span> <span class="n">list4</span> <span class="o">=</span> <span class="n">mp_transform</span><span class="o">&lt;</span><span class="n">F</span><span class="p">,</span> <span class="n">list1</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="k">using</span> <span class="n">outer</span> <span class="o">=</span> <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_append</span><span class="p">,</span> <span class="n">list4</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="c1">//</span>

    <span class="k">return</span> <span class="n">tuple_cat_</span><span class="o">&lt;</span><span class="n">R</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">inner</span><span class="p">(),</span> <span class="n">outer</span><span class="p">(),</span>
        <span class="n">std</span><span class="o">::</span><span class="n">forward_as_tuple</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">forward</span><span class="o">&lt;</span><span class="n">Tp</span><span class="o">&gt;</span><span class="p">(</span><span class="n">tp</span><span class="p">)...</span> <span class="p">)</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="computing_return_types">Computing Return Types</h3>
<div class="paragraph">
<p>C&#43;&#43;17 has a standard variant type, called <code>std::variant</code>. It also defines a function template
<code>std::visit</code> that can be used to apply a function to the contained value of one or more variants.
So for instance, if the variant <code>v1</code> contains <code>1</code>, and the variant <code>v2</code> contains <code>2.0f</code>,
<code>std::visit(f, v1, v2)</code> will call <code>f(1, 2.0f)</code>.</p>
</div>
<div class="paragraph">
<p>However, <code>std::visit</code> has one limitation: it cannot return a result unless all
possible applications of the function have the same return type. If, for instance, <code>v1</code> and <code>v2</code>
are both of type <code>std::variant&lt;short, int, float&gt;</code>,</p>
</div>
<div class="literalblock">
<div class="content">
<pre>std::visit( []( auto const&amp; x, auto const&amp; y ){ return x + y; }, v1, v2 );</pre>
</div>
</div>
<div class="paragraph">
<p>will fail to compile because the result of <code>x + y</code> can be either <code>int</code> or <code>float</code> depending on
what <code>v1</code> and <code>v2</code> hold.</p>
</div>
<div class="paragraph">
<p>A type that can hold either <code>int</code> or <code>float</code> already exists, called, surprisingly enough, <code>std::variant&lt;int, float&gt;</code>.
Let&#8217;s write our own function template <code>rvisit</code> that is the same as <code>visit</code> but returns a <code>variant</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">F</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="k">auto</span> <span class="nf">rvisit</span><span class="p">(</span> <span class="n">F</span><span class="o">&amp;&amp;</span> <span class="n">f</span><span class="p">,</span> <span class="n">V</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">using</span> <span class="n">R</span> <span class="o">=</span> <span class="cm">/*...*/</span><span class="p">;</span>

    <span class="k">return</span> <span class="n">std</span><span class="o">::</span><span class="n">visit</span><span class="p">(</span> <span class="p">[</span><span class="o">&amp;</span><span class="p">](</span> <span class="k">auto</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">x</span> <span class="p">)</span>
        <span class="p">{</span> <span class="k">return</span> <span class="n">R</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">forward</span><span class="o">&lt;</span><span class="n">F</span><span class="o">&gt;</span><span class="p">(</span><span class="n">f</span><span class="p">)(</span> <span class="n">std</span><span class="o">::</span><span class="n">forward</span><span class="o">&lt;</span><span class="k">decltype</span><span class="p">(</span><span class="n">x</span><span class="p">)</span><span class="o">&gt;</span><span class="p">(</span><span class="n">x</span><span class="p">)...</span> <span class="p">)</span> <span class="p">);</span> <span class="p">},</span>
        <span class="n">std</span><span class="o">::</span><span class="n">forward</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">v</span> <span class="p">)...</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>What this does is basically calls <code>std::visit</code> to do the work, but instead of passing it <code>f</code>, we pass a lambda that does the same as <code>f</code> except
it converts the result to a common type <code>R</code>. <code>R</code> is supposed to be <code>std::variant&lt;&#8230;&#8203;&gt;</code> where the ellipsis denotes the return types of
calling <code>f</code> with all possible combinations of variant values.</p>
</div>
<div class="paragraph">
<p>We&#8217;ll first define a helper quoted metafunction <code>Qret&lt;F&gt;</code> that returns the result of the application of <code>F</code> to arguments of type <code>T&#8230;&#8203;</code>:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>template&lt;class F&gt; struct Qret
{
    template&lt;class... T&gt; using fn =
        decltype( std::declval&lt;F&gt;()( std::declval&lt;T&gt;()... ) );
};</pre>
</div>
</div>
<div class="paragraph">
<p>It turns out that C&#43;&#43;17 already contains a metafunction that returns the result of the application of a function <code>F</code> to arguments
of type <code>T&#8230;&#8203;</code>: <code>std::invoke_result_t&lt;F, T&#8230;&#8203;&gt;</code>. We can make use of it to simplify our <code>Qret</code> to</p>
</div>
<div class="literalblock">
<div class="content">
<pre>template&lt;class F&gt; struct Qret
{
    template&lt;class... T&gt; using fn = std::invoke_result_t&lt;F, T...&gt;;
};</pre>
</div>
</div>
<div class="paragraph">
<p>which in Mp11 can be expressed more concisely as</p>
</div>
<div class="literalblock">
<div class="content">
<pre>using Qret = mp_bind_front&lt;std::invoke_result_t, F&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>With <code>Qret</code> in hand, a <code>variant</code> of the possible return types is just a matter of applying it over the possible combinations of the variant values:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>using R = mp_product_q&lt;Qret, remove_cv_ref&lt;V&gt;...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Why does this work? <code>mp_product&lt;F, L1&lt;T1&#8230;&#8203;&gt;, L2&lt;T2&#8230;&#8203;&gt;, &#8230;&#8203;, Ln&lt;Tn&#8230;&#8203;&gt;&gt;</code> returns <code>L1&lt;F&lt;U1, U2, &#8230;&#8203;, Un&gt;, &#8230;&#8203;&gt;</code>, where <code>Ui</code> traverse all
possible combinations of list values. Since in our case all <code>Li</code> are <code>std::variant</code>, the result will also be <code>std::variant</code>. (<code>mp_product_q</code> is
the same as <code>mp_product</code>, but for quoted metafunctions such as our <code>Qret</code>.)</p>
</div>
<div class="paragraph">
<p>One more step remains. Suppose that, as above, we&#8217;re passing two variants of type <code>std::variant&lt;short, int, float&gt;</code> and <code>F</code> is
<code>[]( auto const&amp; x, auto const&amp; y ){ return x + y; }</code>. This will generate <code>R</code> of length 9, one per each combination, but many of those
elements will be the same, either <code>int</code> or <code>float</code>, and we need to filter out the duplicates. So, we pass the result to <code>mp_unique</code>:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>using R = mp_unique&lt;mp_product_q&lt;Qret, remove_cv_ref&lt;V&gt;...&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>and we&#8217;re done:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#include &lt;boost/mp11.hpp&gt;
#include &lt;boost/core/demangle.hpp&gt;
#include &lt;variant&gt;
#include &lt;type_traits&gt;
#include &lt;typeinfo&gt;
#include &lt;iostream&gt;
</span>
<span class="k">using</span> <span class="k">namespace</span> <span class="n">boost</span><span class="o">::</span><span class="n">mp11</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">remove_cv_ref</span> <span class="o">=</span> <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">remove_cv</span><span class="o">&lt;</span>
    <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">remove_reference</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;::</span><span class="n">type</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">F</span><span class="p">,</span> <span class="k">class</span><span class="o">...</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="k">auto</span> <span class="nf">rvisit</span><span class="p">(</span> <span class="n">F</span><span class="o">&amp;&amp;</span> <span class="n">f</span><span class="p">,</span> <span class="n">V</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">using</span> <span class="n">Qret</span> <span class="o">=</span> <span class="n">mp_bind_front</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">invoke_result_t</span><span class="p">,</span> <span class="n">F</span><span class="o">&gt;</span><span class="p">;</span>

    <span class="k">using</span> <span class="n">R</span> <span class="o">=</span> <span class="n">mp_unique</span><span class="o">&lt;</span><span class="n">mp_product_q</span><span class="o">&lt;</span><span class="n">Qret</span><span class="p">,</span> <span class="n">remove_cv_ref</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;&gt;</span><span class="p">;</span>

    <span class="k">return</span> <span class="n">std</span><span class="o">::</span><span class="n">visit</span><span class="p">(</span> <span class="p">[</span><span class="o">&amp;</span><span class="p">](</span> <span class="k">auto</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">x</span> <span class="p">)</span>
        <span class="p">{</span> <span class="k">return</span> <span class="n">R</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">forward</span><span class="o">&lt;</span><span class="n">F</span><span class="o">&gt;</span><span class="p">(</span><span class="n">f</span><span class="p">)(</span> <span class="n">std</span><span class="o">::</span><span class="n">forward</span><span class="o">&lt;</span><span class="k">decltype</span><span class="p">(</span><span class="n">x</span><span class="p">)</span><span class="o">&gt;</span><span class="p">(</span><span class="n">x</span><span class="p">)...</span> <span class="p">)</span> <span class="p">);</span> <span class="p">},</span>
        <span class="n">std</span><span class="o">::</span><span class="n">forward</span><span class="o">&lt;</span><span class="n">V</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">v</span> <span class="p">)...</span> <span class="p">);</span>
<span class="p">}</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="nf">name</span><span class="p">()</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="n">boost</span><span class="o">::</span><span class="n">core</span><span class="o">::</span><span class="n">demangle</span><span class="p">(</span> <span class="k">typeid</span><span class="p">(</span><span class="n">T</span><span class="p">).</span><span class="n">name</span><span class="p">()</span> <span class="p">);</span>
<span class="p">}</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">V</span><span class="p">&gt;</span> <span class="kt">void</span> <span class="nf">print_variant</span><span class="p">(</span> <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">n</span><span class="p">,</span> <span class="n">V</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"("</span> <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="k">decltype</span><span class="p">(</span><span class="n">v</span><span class="p">)</span><span class="o">&gt;</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="s">")"</span> <span class="o">&lt;&lt;</span> <span class="n">n</span> <span class="o">&lt;&lt;</span> <span class="s">": "</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">visit</span><span class="p">(</span> <span class="p">[](</span> <span class="k">auto</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">x</span> <span class="p">)</span>
        <span class="p">{</span> <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"("</span> <span class="o">&lt;&lt;</span> <span class="n">name</span><span class="o">&lt;</span><span class="k">decltype</span><span class="p">(</span><span class="n">x</span><span class="p">)</span><span class="o">&gt;</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="s">")"</span> <span class="o">&lt;&lt;</span> <span class="n">x</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span> <span class="p">},</span> <span class="n">v</span> <span class="p">);</span>
<span class="p">}</span>

<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">variant</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span> <span class="n">v1</span><span class="p">(</span> <span class="mi">1</span> <span class="p">);</span>

    <span class="n">print_variant</span><span class="p">(</span> <span class="s">"v1"</span><span class="p">,</span> <span class="n">v1</span> <span class="p">);</span>

    <span class="n">std</span><span class="o">::</span><span class="n">variant</span><span class="o">&lt;</span><span class="kt">short</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">double</span><span class="o">&gt;</span> <span class="k">const</span> <span class="n">v2</span><span class="p">(</span> <span class="mf">3.14</span> <span class="p">);</span>

    <span class="n">print_variant</span><span class="p">(</span> <span class="s">"v2"</span><span class="p">,</span> <span class="n">v2</span> <span class="p">);</span>

    <span class="k">auto</span> <span class="n">v3</span> <span class="o">=</span> <span class="n">rvisit</span><span class="p">(</span> <span class="p">[](</span> <span class="k">auto</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">x</span><span class="p">,</span> <span class="k">auto</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">y</span> <span class="p">){</span> <span class="k">return</span> <span class="n">x</span> <span class="o">+</span> <span class="n">y</span><span class="p">;</span> <span class="p">},</span> <span class="n">v1</span><span class="p">,</span> <span class="n">v2</span> <span class="p">);</span>

    <span class="n">print_variant</span><span class="p">(</span> <span class="s">"v3"</span><span class="p">,</span> <span class="n">v3</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changelog">Revision History</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="changes_in_1_77_0">Changes in 1.77.0</h3>
<div class="ulist">
<ul>
<li>
<p>Added <code>mp_intersperse</code>, <code>mp_split</code>, <code>mp_join</code></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_1_75_0">Changes in 1.75.0</h3>
<div class="ulist">
<ul>
<li>
<p>Added <code>mp_pairwise_fold</code> (suggested by Barry Revzin)</p>
</li>
<li>
<p>Removed <code>mp_invoke</code> (use <code>mp_invoke_q</code>)</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_1_74_0">Changes in 1.74.0</h3>
<div class="ulist">
<ul>
<li>
<p>Improved compilation performance of <code>mp_with_index&lt;N&gt;</code> for large <code>N</code></p>
</li>
<li>
<p>Added <code>tuple_transform</code> (contributed by Hans Dembinski)</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_1_73_0">Changes in 1.73.0</h3>
<div class="ulist">
<ul>
<li>
<p>Added <code>mp_unique_if</code> (contributed by Kris Jusiak)</p>
</li>
<li>
<p>Added <code>mp_flatten</code></p>
</li>
<li>
<p>Added <code>mp_rotate_left</code>, <code>mp_rotate_right</code> (contributed by Duncan Barber)</p>
</li>
<li>
<p>Added <code>mp_compose</code></p>
</li>
<li>
<p>Added <code>mp_power_set</code></p>
</li>
<li>
<p>Added <code>mp_partial_sum</code></p>
</li>
<li>
<p>Added <code>mp_iterate</code></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_1_70_0">Changes in 1.70.0</h3>
<div class="ulist">
<ul>
<li>
<p>Renamed <code>mp_invoke</code> to <code>mp_invoke_q</code></p>
</li>
<li>
<p>Added <code>mp_similar</code></p>
</li>
<li>
<p>Added <code>mp_set_union</code>, <code>mp_set_intersection</code>, <code>mp_set_difference</code></p>
</li>
<li>
<p>Added <code>mp_not_fn</code></p>
</li>
<li>
<p>Added <code>mp_transform_first</code>, <code>mp_transform_second</code>, <code>mp_transform_third</code></p>
</li>
<li>
<p>Added <code>mp_filter</code></p>
</li>
<li>
<p>Added <code>mp_eval_if_not</code>, <code>mp_eval_or</code>, <code>mp_valid_q</code></p>
</li>
<li>
<p>Added <code>mp_back</code>, <code>mp_pop_back</code></p>
</li>
<li>
<p>Added <code>BOOST_MP11_VERSION</code></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_1_69_0">Changes in 1.69.0</h3>
<div class="ulist">
<ul>
<li>
<p>Removed dependency on Boost.Config; Mp11 is now standalone</p>
</li>
<li>
<p>Improved code generation for <code>mp_with_index</code></p>
</li>
<li>
<p>Added <code>mp_starts_with</code> (contributed by Glen Fernandes)</p>
</li>
<li>
<p>Added CMake support</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="reference">Reference</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The contents of the library are in namespace <code>boost::mp11</code>.</p>
</div>
<div class="sect2">
<h3 id="integral">Integral Constants, &lt;boost/mp11/integral.hpp&gt;</h3>
<div class="paragraph">
<p>For an Mp11 integral constant type <code>T</code>, <code>T::value</code> is an integral constant in the C++ sense.</p>
</div>
<div class="sect3">
<h4 id="mp_boolb">mp_bool&lt;B&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;bool B&gt; using mp_bool = std::integral_constant&lt;bool, B&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>std::bool_constant</code> in C++17.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_true">mp_true</h4>
<div class="literalblock">
<div class="content">
<pre>using mp_true = mp_bool&lt;true&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>std::true_type</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_false">mp_false</h4>
<div class="literalblock">
<div class="content">
<pre>using mp_false = mp_bool&lt;false&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>std::false_type</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_to_boolt">mp_to_bool&lt;T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T&gt; using mp_to_bool = mp_bool&lt;static_cast&lt;bool&gt;(T::value)&gt;;</pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_nott">mp_not&lt;T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T&gt; using mp_not = mp_bool&lt; !T::value &gt;;</pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_inti">mp_int&lt;I&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;int I&gt; using mp_int = std::integral_constant&lt;int, I&gt;;</pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_size_tn">mp_size_t&lt;N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;std::size_t N&gt; using mp_size_t = std::integral_constant&lt;std::size_t, N&gt;;</pre>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="list">List Operations, &lt;boost/mp11/list.hpp&gt;</h3>
<div class="sect3">
<h4 id="mp_listt">mp_list&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; struct mp_list {};</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_list</code> is the standard list type of Mp11, although the library is not restricted to it and can operate on arbitrary class templates
such as <code>std::tuple</code> or <code>std::variant</code>. Even <code>std::pair</code> can be used if the transformation does not alter the number of the elements in
the list.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_list_ct_i">mp_list_c&lt;T, I&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T, T... I&gt; using mp_list_c =
    mp_list&lt;std::integral_constant&lt;T, I&gt;...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_list_c</code> produces an <code>mp_list</code> of the <code>std::integral_constant</code> types corresponding to its integer template arguments.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 1. Using mp_list_c</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">mp_list_c</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;mp_int&lt;2&gt;, mp_int&lt;3&gt;&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_is_listl">mp_is_list&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_is_list = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_is_list&lt;L&gt;</code> is <code>mp_true</code> if <code>L</code> is a list (an instantiation of a class template whose template parameters are types), <code>mp_false</code> otherwise.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_sizel">mp_size&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_size = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_size&lt;L&gt;</code> returns the number of elements in the list <code>L</code>, as a <code>mp_size_t</code>. In other words, <code>mp_size&lt;L&lt;T&#8230;&#8203;&gt;&gt;</code> is an alias for
<code>mp_size_t&lt;sizeof&#8230;&#8203;(T)&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 2. Using mp_size with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_size</span><span class="o">&lt;</span><span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_size_t&lt;0&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 3. Using mp_size with std::pair</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">int</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_size</span><span class="o">&lt;</span><span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_size_t&lt;2&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 4. Using mp_size with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L3</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_size</span><span class="o">&lt;</span><span class="n">L3</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_size_t&lt;1&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_emptyl">mp_empty&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_empty = mp_bool&lt;mp_size&lt;L&gt;::value == 0&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_empty&lt;L&gt;</code> is an alias for <code>mp_true</code> if the list <code>L</code> is empty, for <code>mp_false</code> otherwise.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 5. Using mp_empty with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_empty</span><span class="o">&lt;</span><span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_false</span>

<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_empty</span><span class="o">&lt;</span><span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_true</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_assignl1_l2">mp_assign&lt;L1, L2&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L1, class L2&gt; using mp_assign = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_assign&lt;L1&lt;T1&#8230;&#8203;&gt;, L2&lt;T2&#8230;&#8203;&gt;&gt;</code> is an alias for <code>L1&lt;T2&#8230;&#8203;&gt;</code>. That is, it replaces the elements of <code>L1</code> with those of <code>L2</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 6. Using mp_assign with mp_list and std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">long</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_assign</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;int, float&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 7. Using mp_assign with mp_list and std::pair</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">long</span><span class="p">,</span> <span class="kt">char</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_assign</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::pair&lt;int, float&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_clearl">mp_clear&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_clear = mp_assign&lt;L, mp_list&lt;&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_clear&lt;L&lt;T&#8230;&#8203;&gt;&gt;</code> is an alias for <code>L&lt;&gt;</code>, that is, it removes the elements of <code>L</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 8. Using mp_clear with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_clear</span><span class="o">&lt;</span><span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_frontl">mp_front&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_front = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_front&lt;L&gt;</code> is the first element of the list <code>L</code>. That is, <code>mp_front&lt;L&lt;T1, T&#8230;&#8203;&gt;&gt;</code> is an alias for <code>T1</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 9. Using mp_front with std::pair</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_front</span><span class="o">&lt;</span><span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// int</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 10. Using mp_front with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_front</span><span class="o">&lt;</span><span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// float</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 11. Using mp_front with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L3</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_front</span><span class="o">&lt;</span><span class="n">L3</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// char[1]</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_pop_frontl">mp_pop_front&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_pop_front = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_pop_front&lt;L&gt;</code> removes the first element of the list <code>L</code>. That is, <code>mp_pop_front&lt;L&lt;T1, T&#8230;&#8203;&gt;&gt;</code> is an alias for <code>L&lt;T&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 12. Using mp_pop_front with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_pop_front</span><span class="o">&lt;</span><span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;double, long double&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 13. Using mp_pop_front with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_pop_front</span><span class="o">&lt;</span><span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_firstl">mp_first&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_first = mp_front&lt;L&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_first</code> is another name for <code>mp_front</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_restl">mp_rest&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_rest = mp_pop_front&lt;L&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_rest</code> is another name for <code>mp_pop_front</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_secondl">mp_second&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_second = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_second&lt;L&gt;</code> is the second element of the list <code>L</code>. That is, <code>mp_second&lt;L&lt;T1, T2, T&#8230;&#8203;&gt;&gt;</code> is an alias for <code>T2</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 14. Using mp_second with std::pair</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_second</span><span class="o">&lt;</span><span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// float</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 15. Using mp_second with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_second</span><span class="o">&lt;</span><span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// double</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 16. Using mp_second with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L3</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_second</span><span class="o">&lt;</span><span class="n">L3</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// char[2]</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_thirdl">mp_third&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_third = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_third&lt;L&gt;</code> is the third element of the list <code>L</code>. That is, <code>mp_third&lt;L&lt;T1, T2, T3, T&#8230;&#8203;&gt;&gt;</code> is an alias for <code>T3</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 17. Using mp_third with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_third</span><span class="o">&lt;</span><span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// long double</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 18. Using mp_third with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_third</span><span class="o">&lt;</span><span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// char[3]</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_push_frontl_t">mp_push_front&lt;L, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class... T&gt; using mp_push_front = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_push_front&lt;L, T&#8230;&#8203;&gt;</code> inserts the elements <code>T&#8230;&#8203;</code> at the front of the list <code>L</code>. That is, <code>mp_push_front&lt;L&lt;U&#8230;&#8203;&gt;, T&#8230;&#8203;&gt;</code>
is an alias for <code>L&lt;T&#8230;&#8203;, U&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 19. Using mp_push_front with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_push_front</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;float, double, long double&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 20. Using mp_push_front with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_push_front</span><span class="o">&lt;</span><span class="n">L2</span><span class="p">,</span> <span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;char[1], char[2], void&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_push_backl_t">mp_push_back&lt;L, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class... T&gt; using mp_push_back = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_push_back&lt;L, T&#8230;&#8203;&gt;</code> inserts the elements <code>T&#8230;&#8203;</code> at the back of the list <code>L</code>. That is, <code>mp_push_back&lt;L&lt;U&#8230;&#8203;&gt;, T&#8230;&#8203;&gt;</code>
is an alias for <code>L&lt;U&#8230;&#8203;, T&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 21. Using mp_push_back with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_push_back</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;double, long double, float&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 22. Using mp_push_back with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_push_back</span><span class="o">&lt;</span><span class="n">L2</span><span class="p">,</span> <span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;void, char[1], char[2]&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_renamel_y">mp_rename&lt;L, Y&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class Y&gt; using mp_rename = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_rename&lt;L, Y&gt;</code> changes the type of the list <code>L</code> to <code>Y</code>. That is, <code>mp_rename&lt;L&lt;T&#8230;&#8203;&gt;, Y&gt;</code> is an alias for <code>Y&lt;T&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 23. Using mp_rename to rename std::pair to std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_rename</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;double, long double&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 24. Using mp_rename to rename std::tuple to mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_rename</span><span class="o">&lt;</span><span class="n">L2</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;void&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_applyf_l">mp_apply&lt;F, L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F, class L&gt; using mp_apply = mp_rename&lt;L, F&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_apply&lt;F, L&gt;</code> applies the metafunction <code>F</code> to the contents of the list <code>L</code>, that is, <code>mp_apply&lt;F, L&lt;T&#8230;&#8203;&gt;&gt;</code> is an alias for <code>F&lt;T&#8230;&#8203;&gt;</code>.
(<code>mp_apply</code> is the same as <code>mp_rename</code> with the arguments reversed.)</p>
</div>
<div class="listingblock">
<div class="title">Code Example 25. Using mp_apply with std::pair</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_same</span><span class="p">,</span> <span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::is_same&lt;double, long double&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_apply_qq_l">mp_apply_q&lt;Q, L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q, class L&gt; using mp_apply_q = mp_apply&lt;Q::template fn, L&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_apply</code>, but takes a quoted metafunction.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 26. Using mp_apply_q with mp_bind_front</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">long</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_apply_q</span><span class="o">&lt;</span><span class="n">mp_bind_front</span><span class="o">&lt;</span><span class="n">mp_push_back</span><span class="p">,</span> <span class="n">L1</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span>
  <span class="c1">// R1 is std::tuple&lt;double, long double, int, long&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_appendl">mp_append&lt;L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... L&gt; using mp_append = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_append&lt;L&#8230;&#8203;&gt;</code> concatenates the lists in <code>L&#8230;&#8203;</code> into a single list that has the same type as the first list. <code>mp_append&lt;&gt;</code>
is an alias for <code>mp_list&lt;&gt;</code>. <code>mp_append&lt;L1&lt;T1&#8230;&#8203;&gt;, L2&lt;T2&#8230;&#8203;&gt;, &#8230;&#8203;, Ln&lt;Tn&#8230;&#8203;&gt;&gt;</code> is an alias for <code>L1&lt;T1&#8230;&#8203;, T2&#8230;&#8203;, &#8230;&#8203;, Tn&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 27. Using mp_append with lists of various types</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">int</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L3</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">short</span><span class="p">,</span> <span class="kt">long</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L4</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_append</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">L2</span><span class="p">,</span> <span class="n">L3</span><span class="p">,</span> <span class="n">L4</span><span class="o">&gt;</span><span class="p">;</span>
  <span class="c1">// std::tuple&lt;double, long double, int, short, long&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_replace_frontl_t">mp_replace_front&lt;L, T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class T&gt; using mp_replace_front = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_replace_front&lt;L, T&gt;</code> replaces the first element of the list <code>L</code> with <code>T</code>. That is, <code>mp_replace_front&lt;L&lt;U1, U&#8230;&#8203;&gt;, T&gt;</code> is
an alias for <code>L&lt;T, U&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 28. Using mp_replace_front with std::pair</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_replace_front</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::pair&lt;void, float&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 29. Using mp_replace_front with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_replace_front</span><span class="o">&lt;</span><span class="n">L2</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;void, double, long double&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 30. Using mp_replace_front with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L3</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_replace_front</span><span class="o">&lt;</span><span class="n">L3</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;void, char[2], char[3], char[4]&gt;;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_replace_firstl_t">mp_replace_first&lt;L, T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class T&gt; using mp_replace_first = mp_replace_front&lt;L, T&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_replace_first</code> is another name for <code>mp_replace_front</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_replace_secondl_t">mp_replace_second&lt;L, T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class T&gt; using mp_replace_second = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_replace_second&lt;L, T&gt;</code> replaces the second element of the list <code>L</code> with <code>T</code>. That is, <code>mp_replace_second&lt;L&lt;U1, U2, U&#8230;&#8203;&gt;, T&gt;</code>
is an alias for <code>L&lt;U1, T, U&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 31. Using mp_replace_second with std::pair</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_replace_second</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::pair&lt;int, void&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 32. Using mp_replace_second with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_replace_second</span><span class="o">&lt;</span><span class="n">L2</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;float, void, long double&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 33. Using mp_replace_front with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L3</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_replace_second</span><span class="o">&lt;</span><span class="n">L3</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;char[1], void, char[3], char[4]&gt;;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_replace_thirdl_t">mp_replace_third&lt;L, T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class T&gt; using mp_replace_third = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_replace_third&lt;L, T&gt;</code> replaces the third element of the list <code>L</code> with <code>T</code>. That is, <code>mp_replace_third&lt;L&lt;U1, U2, U3, U&#8230;&#8203;&gt;, T&gt;</code>
is an alias for <code>L&lt;U1, U2, T, U&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 34. Using mp_replace_third with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">double</span><span class="p">,</span> <span class="kt">long</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_replace_third</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;float, double, void&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 35. Using mp_replace_third with mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_replace_third</span><span class="o">&lt;</span><span class="n">L2</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;char[1], char[2], void, char[4]&gt;;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_transform_frontl_f">mp_transform_front&lt;L, F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class F&gt; using mp_transform_front =
    /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_transform_front&lt;L, F&gt;</code> replaces the first element <code>T1</code> of the list <code>L</code> with <code>F&lt;T1&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_transform_front_ql_q">mp_transform_front_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_transform_front_q =
    mp_transform_front&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_transform_front</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_transform_firstl_f">mp_transform_first&lt;L, F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class F&gt; using mp_transform_first =
    mp_transform_front&lt;L, F&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_transform_first</code> is another name for <code>mp_transform_front</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_transform_first_ql_q">mp_transform_first_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_transform_first_q =
    mp_transform_first&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_transform_first</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_transform_secondl_f">mp_transform_second&lt;L, F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class F&gt; using mp_transform_second =
    /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_transform_second&lt;L, F&gt;</code> replaces the second element <code>T2</code> of the list <code>L</code> with <code>F&lt;T2&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_transform_second_ql_q">mp_transform_second_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_transform_second_q =
    mp_transform_second&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_transform_second</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_transform_thirdl_f">mp_transform_third&lt;L, F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class F&gt; using mp_transform_third =
    /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_transform_third&lt;L, F&gt;</code> replaces the third element <code>T3</code> of the list <code>L</code> with <code>F&lt;T3&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_transform_third_ql_q">mp_transform_third_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_transform_third_q =
    mp_transform_third&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_transform_third</code>, but takes a quoted metafunction.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="utility">Utility Components, &lt;boost/mp11/utility.hpp&gt;</h3>
<div class="sect3">
<h4 id="mp_identityt">mp_identity&lt;T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T&gt; struct mp_identity
{
    using type = T;
};</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_identity</code> is a simple <em>transformation type trait</em> (as per the C++ standard)
that just returns the same type. It&#8217;s useful both as such, and as a type wrapper
for passing types as values to functions.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 36. Using mp_identity as a type trait</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">addp_if_not_ref</span> <span class="o">=</span>
    <span class="k">typename</span> <span class="n">mp_if</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_reference</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_identity</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">add_pointer</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;&gt;::</span><span class="n">type</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 37. Using mp_identity to protect qualifiers and references</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="kt">void</span> <span class="nf">print1</span><span class="p">()</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="k">typeid</span><span class="p">(</span><span class="n">T</span><span class="p">).</span><span class="n">name</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="p">}</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="kt">void</span> <span class="nf">print2</span><span class="p">()</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="k">typeid</span><span class="p">(</span><span class="n">mp_identity</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">).</span><span class="n">name</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="p">}</span>

<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
    <span class="n">print1</span><span class="o">&lt;</span><span class="kt">int</span> <span class="k">const</span><span class="o">&amp;&gt;</span><span class="p">();</span> <span class="c1">// 'int'</span>
    <span class="n">print2</span><span class="o">&lt;</span><span class="kt">int</span> <span class="k">const</span><span class="o">&amp;&gt;</span><span class="p">();</span> <span class="c1">// 'mp_identity&lt;int const &amp;&gt;'</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_identity_tt">mp_identity_t&lt;T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T&gt; using mp_identity_t = typename mp_identity&lt;T&gt;::type;</pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_inheritt">mp_inherit&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; struct mp_inherit: T... {};</pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_if_cc_t_e">mp_if_c&lt;C, T, E&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;bool C, class T, class... E&gt; using mp_if_c = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_if_c&lt;true, T, E&#8230;&#8203;&gt;</code> is an alias for <code>T</code>. <code>mp_if_c&lt;false, T, E&gt;</code> is an alias for <code>E</code>. Otherwise, the result is a substitution failure.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 38. Using mp_if_c to select between two alternatives</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_if_c</span><span class="o">&lt;</span><span class="nb">true</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>  <span class="c1">// int</span>

<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_if_c</span><span class="o">&lt;</span><span class="nb">false</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// void</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 39. Using mp_if_c to fail substitution when a condition is not met</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">I</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">void_if_5</span> <span class="o">=</span> <span class="n">mp_if_c</span><span class="o">&lt;</span><span class="n">I</span><span class="o">::</span><span class="n">value</span> <span class="o">==</span> <span class="mi">5</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This example returns <code>void</code> when <code>I::value</code> is 5, and generates a substitution failure
otherwise. It&#8217;s the same as <code>std::enable_if_t&lt;I::value == 5&gt;</code> in C&#43;&#43;14, or
<code>typename std::enable_if&lt;I::value == 5&gt;::type</code> in C&#43;&#43;11.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_ifc_t_e">mp_if&lt;C, T, E&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class C, class T, class... E&gt; using mp_if =
    mp_if_c&lt;static_cast&lt;bool&gt;(C::value), T, E...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Like <code>mp_if_c</code>, but the first argument is a type.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 40. Using mp_if to select between two alternatives</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_if</span><span class="o">&lt;</span><span class="n">mp_true</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>  <span class="c1">// int</span>

<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_if</span><span class="o">&lt;</span><span class="n">mp_false</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// void</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 41. Using mp_if to fail substitution when a condition is not met</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">void_if_const</span> <span class="o">=</span> <span class="n">mp_if</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_const</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">void_if_all_const</span> <span class="o">=</span>
    <span class="n">mp_if</span><span class="o">&lt;</span><span class="n">mp_all</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_const</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="p">...</span><span class="o">&gt;</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">if_non_const</span> <span class="o">=</span> <span class="n">mp_if</span><span class="o">&lt;</span><span class="n">mp_not</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_const</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;&gt;</span><span class="p">,</span> <span class="n">T</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_eval_if_cc_t_f_u">mp_eval_if_c&lt;C, T, F, U&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;bool C, class T, template&lt;class...&gt; class F, class... U&gt; using mp_eval_if_c =
    /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_eval_if_c&lt;C, T, F, U&#8230;&#8203;&gt;</code> is an alias for <code>T</code> when <code>C</code> is <code>true</code>, for <code>F&lt;U&#8230;&#8203;&gt;</code> otherwise. Its purpose
is to avoid evaluating <code>F&lt;U&#8230;&#8203;&gt;</code> when the condition is <code>true</code> as it may not be valid in this case.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 42. Using mp_eval_if_c to select the first pack element, or void</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">first_or_void</span> <span class="o">=</span>
    <span class="n">mp_eval_if_c</span><span class="o">&lt;</span><span class="k">sizeof</span><span class="p">...(</span><span class="n">T</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">,</span> <span class="kt">void</span><span class="p">,</span> <span class="n">mp_first</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_eval_ifc_t_f_u">mp_eval_if&lt;C, T, F, U&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class C, class T, template&lt;class...&gt; class F, class... U&gt; using mp_eval_if =
    mp_eval_if_c&lt;static_cast&lt;bool&gt;(C::value), T, F, U...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Like <code>mp_eval_if_c</code>, but the first argument is a type.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 43. Using mp_eval_if to select the first list element, or void</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">L</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">first_or_void</span> <span class="o">=</span> <span class="n">mp_eval_if</span><span class="o">&lt;</span><span class="n">mp_empty</span><span class="o">&lt;</span><span class="n">L</span><span class="o">&gt;</span><span class="p">,</span> <span class="kt">void</span><span class="p">,</span> <span class="n">mp_first</span><span class="p">,</span> <span class="n">L</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_eval_if_qc_t_q_u">mp_eval_if_q&lt;C, T, Q, U&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class C, class T, class Q, class... U&gt; using mp_eval_if_q =
    mp_eval_if&lt;C, T, Q::template fn, U...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Like <code>mp_eval_if</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_eval_if_notc_t_f_u">mp_eval_if_not&lt;C, T, F, U&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class C, class T, template&lt;class...&gt; class F, class... U&gt;
    using mp_eval_if_not = mp_eval_if&lt;mp_not&lt;C&gt;, T, F, U...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_eval_if</code>, but the condition is reversed.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_eval_if_not_qc_t_q_u">mp_eval_if_not_q&lt;C, T, Q, U&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class C, class T, class Q, class... U&gt; using mp_eval_if_not_q =
    mp_eval_if_not&lt;C, T, Q::template fn, U...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_eval_if_not</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_validf_t">mp_valid&lt;F, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F, class... T&gt; using mp_valid = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_valid&lt;F, T&#8230;&#8203;&gt;</code> is an alias for <code>mp_true</code> when <code>F&lt;T&#8230;&#8203;&gt;</code> is a valid expression, for <code>mp_false</code> otherwise.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 44. Using mp_valid to write a metafunction that checks for the existence of a nested type</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">get_nested_type</span> <span class="o">=</span> <span class="k">typename</span> <span class="n">T</span><span class="o">::</span><span class="n">type</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">has_nested_type</span><span class="o">:</span> <span class="n">mp_valid</span><span class="o">&lt;</span><span class="n">get_nested_type</span><span class="p">,</span> <span class="n">T</span><span class="o">&gt;</span> <span class="p">{};</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_valid_qq_t">mp_valid_q&lt;Q, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q, class... T&gt; using mp_valid_q = mp_valid&lt;Q::template fn, T...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Like <code>mp_valid</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_eval_ort_f_u">mp_eval_or&lt;T, F, U&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T, template&lt;class...&gt; class F, class... U&gt; using mp_eval_or =
    mp_eval_if_not&lt;mp_valid&lt;F, U...&gt;, T, F, U...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_eval_or&lt;T, F, U&#8230;&#8203;&gt;</code> is an alias for <code>F&lt;U&#8230;&#8203;&gt;</code> when this expression is valid, for <code>T</code> otherwise.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 45. Using mp_eval_or to select the first pack element, or void</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">first_or_void</span> <span class="o">=</span>
    <span class="n">mp_eval_or</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="n">mp_first</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_eval_or_qt_q_u">mp_eval_or_q&lt;T, Q, U&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T, class Q, class... U&gt; using mp_eval_or_q =
    mp_eval_or&lt;T, Q::template fn, U...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Like <code>mp_eval_or</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_valid_and_truef_t">mp_valid_and_true&lt;F, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F, class... T&gt; using mp_valid_and_true =
    mp_eval_or&lt;mp_false, F, T...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_valid_and_true&lt;F, T&#8230;&#8203;&gt;</code> is an alias for <code>F&lt;T&#8230;&#8203;&gt;</code> when this expression is valid, for <code>mp_false</code> otherwise.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_valid_and_true_qq_t">mp_valid_and_true_q&lt;Q, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q, class... T&gt; using mp_valid_and_true_q =
    mp_valid_and_true&lt;Q::template fn, T...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Like <code>mp_valid_and_true</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_condc_t_r">mp_cond&lt;C, T, R&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class C, class T, class... R&gt; using mp_cond = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_cond&lt;C, T, R&#8230;&#8203;&gt;</code> is an alias for <code>T</code> when <code>static_cast&lt;bool&gt;(C::value)</code> is <code>true</code>.
When <code>static_cast&lt;bool&gt;(C::value)</code> is <code>false</code>, it&#8217;s an alias for <code>mp_cond&lt;R&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>(If <code>static_cast&lt;bool&gt;(C::value)</code> is a substitution failure, the result is too a substitution
failure.)</p>
</div>
<div class="listingblock">
<div class="title">Code Example 46. Using mp_cond</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="kt">int</span> <span class="n">N</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">unsigned_</span> <span class="o">=</span> <span class="n">mp_cond</span><span class="o">&lt;</span>
    <span class="n">mp_bool</span><span class="o">&lt;</span><span class="n">N</span> <span class="o">==</span>  <span class="mi">8</span><span class="o">&gt;</span><span class="p">,</span> <span class="kt">uint8_t</span><span class="p">,</span>
    <span class="n">mp_bool</span><span class="o">&lt;</span><span class="n">N</span> <span class="o">==</span> <span class="mi">16</span><span class="o">&gt;</span><span class="p">,</span> <span class="kt">uint16_t</span><span class="p">,</span>
    <span class="n">mp_bool</span><span class="o">&lt;</span><span class="n">N</span> <span class="o">==</span> <span class="mi">32</span><span class="o">&gt;</span><span class="p">,</span> <span class="kt">uint32_t</span><span class="p">,</span>
    <span class="n">mp_bool</span><span class="o">&lt;</span><span class="n">N</span> <span class="o">==</span> <span class="mi">64</span><span class="o">&gt;</span><span class="p">,</span> <span class="kt">uint64_t</span><span class="p">,</span>
    <span class="n">mp_true</span><span class="p">,</span> <span class="kt">unsigned</span> <span class="c1">// default case</span>
<span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_deferf_t">mp_defer&lt;F, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F, class... T&gt; using mp_defer = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>When <code>mp_valid&lt;F, T&#8230;&#8203;&gt;</code> is <code>mp_true</code>, <code>mp_defer&lt;F, T&#8230;&#8203;&gt;</code> is a struct with a nested type <code>type</code> which is an alias for <code>F&lt;T&#8230;&#8203;&gt;</code>. Otherwise,
<code>mp_defer&lt;F, T&#8230;&#8203;&gt;</code> is an empty struct.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_quotef">mp_quote&lt;F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F&gt; struct mp_quote
{
    template&lt;class... T&gt; using fn = F&lt;T...&gt;;
};</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_quote&lt;F&gt;</code> transforms the template <code>F</code> into a <em>quoted metafunction</em>, a type with a nested template <code>fn</code> such that <code>fn&lt;T&#8230;&#8203;&gt;</code> returns <code>F&lt;T&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 47. Using mp_quote to make a list of metafunctions</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">LQ</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">mp_quote</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_const</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_quote</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_volatile</span><span class="o">&gt;&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_quote_traitf">mp_quote_trait&lt;F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F&gt; struct mp_quote_trait
{
    template&lt;class... T&gt; using fn = typename F&lt;T...&gt;::type;
};</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_quote_trait&lt;F&gt;</code> transforms the C++03-style trait <code>F</code> into a quoted metafunction.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 48. Using mp_quote_trait with std::add_pointer</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">void</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_transform_q</span><span class="o">&lt;</span><span class="n">mp_quote_trait</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">add_pointer</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span>
  <span class="c1">// mp_list&lt;int*, void*, float*&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_invoke_qq_t">mp_invoke_q&lt;Q, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q, class... T&gt; using mp_invoke_q = typename Q::template fn&lt;T...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_invoke_q&lt;Q, T&#8230;&#8203;&gt;</code> evaluates the nested template <code>fn</code> of a quoted metafunction. <code>mp_invoke_q&lt;mp_quote&lt;F&gt;, T&#8230;&#8203;&gt;</code> returns <code>F&lt;T&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 49. Using mp_invoke_q to invoke a list of metafunctions, technique 1</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">LQ</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">mp_quote</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_const</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_quote</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_volatile</span><span class="o">&gt;&gt;</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">is_const_and_volatile</span> <span class="o">=</span>
    <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_all</span><span class="p">,</span> <span class="n">mp_product</span><span class="o">&lt;</span><span class="n">mp_invoke_q</span><span class="p">,</span> <span class="n">LQ</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;&gt;&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 50. Using mp_invoke_q to invoke a list of metafunctions, technique 2</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">is_const_and_volatile</span> <span class="o">=</span>
    <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_all</span><span class="p">,</span> <span class="n">mp_transform_q</span><span class="o">&lt;</span><span class="n">mp_bind_back</span><span class="o">&lt;</span><span class="n">mp_invoke_q</span><span class="p">,</span> <span class="n">T</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">LQ</span><span class="o">&gt;&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 51. Using mp_invoke_q to invoke a list of metafunctions, technique 3</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">is_const_and_volatile</span> <span class="o">=</span>
    <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_all</span><span class="p">,</span> <span class="n">mp_transform</span><span class="o">&lt;</span><span class="n">mp_invoke_q</span><span class="p">,</span> <span class="n">LQ</span><span class="p">,</span> <span class="n">mp_fill</span><span class="o">&lt;</span><span class="n">LQ</span><span class="p">,</span> <span class="n">T</span><span class="o">&gt;&gt;&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_not_fnp">mp_not_fn&lt;P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class P&gt; struct mp_not_fn
{
    template&lt;class... T&gt; using fn = mp_not&lt;P&lt;T...&gt;&gt;;
};</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_not_fn&lt;P&gt;</code> returns a quoted metafunction <code>Q</code> such that <code>Q::fn&lt;T&#8230;&#8203;&gt;</code> returns <code>mp_not&lt;P&lt;T&#8230;&#8203;&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>That is, it negates the result of <code>P</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_not_fn_qq">mp_not_fn_q&lt;Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q&gt; using mp_not_fn_q = mp_not_fn&lt;Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_not_fn</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_composef">mp_compose&lt;F&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class... F&gt; struct mp_compose;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_compose&lt;F1, F2, &#8230;&#8203;, Fn&gt;</code> is a quoted metafunction that applies
<code>F1</code>, <code>F2</code>, &#8230;&#8203;, <code>Fn</code> to its argument, in sequence. That is,
<code>mp_compose&lt;F1, F2, &#8230;&#8203;, Fn&gt;::fn&lt;T&#8230;&#8203;&gt;</code> is <code>Fn&lt;&#8230;&#8203;F2&lt;F1&lt;T&#8230;&#8203;&gt;&gt;&#8230;&#8203;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_compose_qq">mp_compose_q&lt;Q&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... Q&gt; struct mp_compose_q;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_compose</code>, but takes quoted metafunctions.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="algorithm">Algorithms, &lt;boost/mp11/algorithm.hpp&gt;</h3>
<div class="sect3">
<h4 id="mp_transformf_l">mp_transform&lt;F, L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F, class... L&gt; using mp_transform = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_transform&lt;F, L1&lt;T1&#8230;&#8203;&gt;, L2&lt;T2&#8230;&#8203;&gt;, &#8230;&#8203;, Ln&lt;Tn&#8230;&#8203;&gt;&gt;</code> applies <code>F</code> to each successive tuple of elements and returns <code>L1&lt;F&lt;T1, T2, &#8230;&#8203;, Tn&gt;&#8230;&#8203;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 52. Using mp_transform to produce a list of pointers from a list of pointees</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">add_pointer_t</span> <span class="o">=</span>
    <span class="k">typename</span> <span class="n">std</span><span class="o">::</span><span class="n">add_pointer</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;::</span><span class="n">type</span><span class="p">;</span>  <span class="c1">// std::add_pointer_t in C++14</span>

<span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_transform</span><span class="o">&lt;</span><span class="n">add_pointer_t</span><span class="p">,</span> <span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;void*, int*, float*&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 53. Using mp_transform to compare the contents of two lists of types</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_all</span><span class="p">,</span> <span class="n">mp_transform</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_same</span><span class="p">,</span> <span class="n">L1</span><span class="p">,</span> <span class="n">L2</span><span class="o">&gt;&gt;</span><span class="p">;</span> <span class="c1">// mp_true</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 54. Using mp_transform to compare the contents of two lists of integral constants</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T2</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">eq</span> <span class="o">=</span> <span class="n">mp_bool</span><span class="o">&lt;</span><span class="n">T1</span><span class="o">::</span><span class="n">value</span> <span class="o">==</span> <span class="n">T2</span><span class="o">::</span><span class="n">value</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="n">mp_int</span><span class="o">&lt;</span><span class="mi">1</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_int</span><span class="o">&lt;</span><span class="mi">2</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_int</span><span class="o">&lt;</span><span class="mi">3</span><span class="o">&gt;&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">mp_size_t</span><span class="o">&lt;</span><span class="mi">1</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_size_t</span><span class="o">&lt;</span><span class="mi">2</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_size_t</span><span class="o">&lt;</span><span class="mi">3</span><span class="o">&gt;&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_all</span><span class="p">,</span> <span class="n">mp_transform</span><span class="o">&lt;</span><span class="n">eq</span><span class="p">,</span> <span class="n">L1</span><span class="p">,</span> <span class="n">L2</span><span class="o">&gt;&gt;</span><span class="p">;</span> <span class="c1">// mp_true</span></code></pre>
</div>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 1. mp_transform on one list</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_transform&lt;F, L1&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>1</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>2</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>n</sub>&gt;</code></p></td>
</tr>
</tbody>
</table>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 2. mp_transform on two lists</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L2</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_transform&lt;F, L1, L2&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>1</sub>,B<sub>1</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>2</sub>,B<sub>2</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>n</sub>,B<sub>n</sub>&gt;</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_transform_qq_l">mp_transform_q&lt;Q, L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q, class... L&gt; using mp_transform_q =
    mp_transform&lt;Q::template fn, L...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_transform</code>, but takes a quoted metafunction.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 55. Using mp_transform_q to count the occurrences of <code>void</code> in a list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_apply</span><span class="o">&lt;</span><span class="n">mp_plus</span><span class="p">,</span>
    <span class="n">mp_transform_q</span><span class="o">&lt;</span><span class="n">mp_bind_front</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_same</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">L1</span><span class="o">&gt;&gt;</span><span class="p">;</span> <span class="c1">// mp_int&lt;2&gt;</span></code></pre>
</div>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 3. mp_transform_q on two lists</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L2</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_transform_q&lt;Q, L1, L2&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>Q::fn&lt;A<sub>1</sub>,B<sub>1</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>Q::fn&lt;A<sub>2</sub>,B<sub>2</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>Q::fn&lt;A<sub>n</sub>,B<sub>n</sub>&gt;</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_transform_ifp_f_l">mp_transform_if&lt;P, F, L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class P, template&lt;class...&gt; class F, class... L&gt;
    using mp_transform_if = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_transform_if&lt;P, F, L1, L2, &#8230;&#8203;, Ln&gt;</code> replaces the elements of the list <code>L1</code> for which <code>mp_to_bool&lt;P&lt;T1, T2, &#8230;&#8203;, Tn&gt;&gt;</code> is <code>mp_true</code> with
<code>F&lt;T1, T2, &#8230;&#8203;, Tn&gt;</code>, and returns the result, where <code>Ti</code> are the corresponding elements of <code>Li</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 56. Using mp_transform_if to replace the occurrences of 'void' in a list with the corresponding elements of a second list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">4</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">5</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T2</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">first_is_void</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">is_same</span><span class="o">&lt;</span><span class="n">T1</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T1</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T2</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">second</span> <span class="o">=</span> <span class="n">T2</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_transform_if</span><span class="o">&lt;</span><span class="n">first_is_void</span><span class="p">,</span> <span class="n">second</span><span class="p">,</span> <span class="n">L1</span><span class="p">,</span> <span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span>
  <span class="c1">// std::tuple&lt;char[1], int, float, char[4], int&gt;</span></code></pre>
</div>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 4. mp_transform_if</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L2</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>P&lt;A<sub>i</sub>, B<sub>i</sub>&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_false</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_true</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_false</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_transform_if&lt;P, F, L1, L2&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>2</sub>,B<sub>2</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_transform_if_qqp_qf_l">mp_transform_if_q&lt;Qp, Qf, L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Qp, class Qf, class... L&gt; using mp_transform_if_q =
    mp_transform_if&lt;Qp::template fn, Qf::template fn, L...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_transform_if</code>, but takes quoted metafunctions.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 57. Using mp_transform_if_q to replace the occurrences of 'void' in a list with the corresponding elements of a second list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">char</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">4</span><span class="p">],</span> <span class="kt">char</span><span class="p">[</span><span class="mi">5</span><span class="p">]</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_transform_if_q</span><span class="o">&lt;</span><span class="n">mp_bind</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">is_same</span><span class="p">,</span> <span class="n">_1</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">_2</span><span class="p">,</span> <span class="n">L1</span><span class="p">,</span> <span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span>
  <span class="c1">// std::tuple&lt;char[1], int, float, char[4], int&gt;</span></code></pre>
</div>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 5. mp_transform_if_q</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L2</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>Qp::fn&lt;A<sub>i</sub>, B<sub>i</sub>&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_false</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_true</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_false</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_transform_if_q&lt;Qp, _2, L1, L2&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_filterp_l">mp_filter&lt;P, L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class P, class... L&gt; using mp_filter = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_filter&lt;P, L1, L2, &#8230;&#8203;, Ln&gt;</code> removes the elements of the list <code>L1</code> for which <code>mp_to_bool&lt;P&lt;T1, T2, &#8230;&#8203;, Tn&gt;&gt;</code>
is <code>mp_false</code> and returns the result, where <code>Ti</code> are the corresponding elements of <code>Li</code>.</p>
</div>
<div class="paragraph">
<p>See also <code>mp_copy_if</code> and <code>mp_remove_if</code>, less general variants of <code>mp_filter</code> that only take a single list.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_filter_qqp_l">mp_filter_q&lt;Qp, L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Qp, class... L&gt; using mp_filter_q =
    mp_filter&lt;Qp::template fn, L...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_filter</code>, but takes a quoted metafunction.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 58. Using mp_filter_q to pick elements of a list based on a mask in another list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">mp_true</span><span class="p">,</span> <span class="n">mp_false</span><span class="p">,</span> <span class="n">mp_true</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_filter_q</span><span class="o">&lt;</span><span class="n">_2</span><span class="p">,</span> <span class="n">L1</span><span class="p">,</span> <span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;void, float&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_filll_v">mp_fill&lt;L, V&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V&gt; using mp_fill = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_fill&lt;L&lt;T&#8230;&#8203;&gt;, V&gt;</code> returns <code>L&lt;V, V, &#8230;&#8203;, V&gt;</code>, with the result having the same size as the input.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 59. Using mp_fill with std::tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_fill</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::tuple&lt;double, double, double&gt;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Code Example 60. Using mp_fill with std::pair</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_fill</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::pair&lt;void, void&gt;</span></code></pre>
</div>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 6. mp_fill</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_fill&lt;L1, V&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>V</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>V</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>V</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_countl_v">mp_count&lt;L, V&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V&gt; using mp_count = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_count&lt;L, V&gt;</code> returns <code>mp_size_t&lt;N&gt;</code>, where <code>N</code> is the number of elements of <code>L</code> same as <code>V</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_count_ifl_p">mp_count_if&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_count_if = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_count_if&lt;L, P&gt;</code> returns <code>mp_size_t&lt;N&gt;</code>, where <code>N</code> is the number of elements <code>T</code> of <code>L</code> for which <code>mp_to_bool&lt;P&lt;T&gt;&gt;</code> is <code>mp_true</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_count_if_ql_q">mp_count_if_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_count_if_q = mp_count_if&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_count_if</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_containsl_v">mp_contains&lt;L, V&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V&gt; using mp_contains = mp_to_bool&lt;mp_count&lt;L, V&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_contains&lt;L, V&gt;</code> is <code>mp_true</code> when <code>L</code> contains an element <code>V</code>, <code>mp_false</code> otherwise.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_starts_withl1_l2">mp_starts_with&lt;L1, L2&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L1, class L2&gt; using mp_starts_with = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_starts_with&lt;L1, L2&gt;</code> is <code>mp_true</code> when <code>L1</code> starts with <code>L2</code>, <code>mp_false</code>
otherwise.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_repeat_cl_n">mp_repeat_c&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t N&gt; using mp_repeat_c = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_repeat_c&lt;L, N&gt;</code> returns a list of the same form as <code>L</code> that consists of <code>N</code> concatenated copies of <code>L</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 61. Using mp_repeat_c</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">tuple</span><span class="o">&lt;</span><span class="kt">int</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_repeat_c</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="mi">3</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// tuple&lt;int, int, int&gt;</span>

<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">pair</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_repeat_c</span><span class="o">&lt;</span><span class="n">L2</span><span class="p">,</span> <span class="mi">1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// pair&lt;int, float&gt;</span>

<span class="k">using</span> <span class="n">L3</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_repeat_c</span><span class="o">&lt;</span><span class="n">L3</span><span class="p">,</span> <span class="mi">2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;int, float, int, float&gt;</span>

<span class="k">using</span> <span class="n">L4</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="kt">double</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R4</span> <span class="o">=</span> <span class="n">mp_repeat_c</span><span class="o">&lt;</span><span class="n">L4</span><span class="p">,</span> <span class="mi">0</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_repeatl_n">mp_repeat&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class N&gt; using mp_repeat = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_repeat_c</code> but with a type argument <code>N</code>. The number of copies is <code>N::value</code> and must be nonnegative.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_productf_l">mp_product&lt;F, L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F, class... L&gt; using mp_product = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_product&lt;F, L1&lt;T1&#8230;&#8203;&gt;, L2&lt;T2&#8230;&#8203;&gt;, &#8230;&#8203;, Ln&lt;Tn&#8230;&#8203;&gt;&gt;</code> evaluates <code>F&lt;U1, U2, &#8230;&#8203;, Un&gt;</code> for values <code>Ui</code> taken from
the Cartesian product of the lists, as if the elements <code>Ui</code> are formed by <code>n</code> nested loops, each traversing <code>Li</code>.
It returns a list of the form <code>L1&lt;V&#8230;&#8203;&gt;</code> containing the results of the application of <code>F</code>. The degenerate case
of zero lists, <code>mp_product&lt;F&gt;</code>, returns <code>mp_list&lt;F&lt;&gt;&gt;</code>.</p>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 7. mp_product on two lists</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L2</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>m</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_product&lt;F, L1, L2&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>1</sub>,B<sub>1</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>1</sub>,B<sub>2</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>1</sub>,B<sub>m</sub>&gt;</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>2</sub>,B<sub>1</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>2</sub>,B<sub>2</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>2</sub>,B<sub>m</sub>&gt;</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"></td>
<td class="tableblock halign-center valign-middle" colspan="4"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>n</sub>,B<sub>1</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>n</sub>,B<sub>2</sub>&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>F&lt;A<sub>n</sub>,B<sub>m</sub>&gt;</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_product_qq_l">mp_product_q&lt;Q, L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q, class... L&gt; using mp_product_q = mp_product&lt;Q::template fn, L...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_product</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_power_setl">mp_power_set&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_power_set = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_power_set&lt;L&gt;</code> returns a list (of the same form as <code>L</code>) of all possible 2<sup>n</sup> subsets of <code>L</code> (where <code>n</code> is the length of <code>L</code>.)</p>
</div>
<div class="paragraph">
<p><code>mp_power_set&lt;L&lt;&gt;&gt;</code> returns <code>L&lt;L&lt;&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p><code>mp_power_set&lt;L&lt;T1&gt;&gt;</code> returns <code>L&lt;L&lt;&gt;, L&lt;T1&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p><code>mp_power_set&lt;L&lt;T1, T2&gt;&gt;</code> returns <code>L&lt;L&lt;&gt;, L&lt;T2&gt;, L&lt;T1&gt;, L&lt;T1, T2&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p><code>mp_power_set&lt;L&lt;T1, T&#8230;&#8203;&gt;&gt;</code> returns the concatenation of <code>mp_power_set&lt;L&lt;T&#8230;&#8203;&gt;&gt;</code> and that same list with <code>T1</code> prepended to each element.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_drop_cl_n">mp_drop_c&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t N&gt; using mp_drop_c = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_drop_c&lt;L, N&gt;</code> removes the first <code>N</code> elements of <code>L</code> and returns the result.</p>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 8. mp_drop_c</caption>
<colgroup>
<col style="width: 40%;">
<col style="width: 10%;">
<col style="width: 10%;">
<col style="width: 10%;">
<col style="width: 10%;">
<col style="width: 10%;">
<col style="width: 10%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m+1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="7"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_drop_c&lt;L1, M&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m+1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
<td class="tableblock halign-center valign-middle" colspan="3"></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_dropl_n">mp_drop&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class N&gt; using mp_drop = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_drop_c</code>, but with a type argument <code>N</code>. <code>N::value</code> must be a nonnegative number.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_from_sequences">mp_from_sequence&lt;S&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class S&gt; using mp_from_sequence = /*...*/</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_from_sequence</code> transforms an integer sequence produced by <code>make_integer_sequence</code> into an <code>mp_list</code>
of the corresponding <code>std::integral_constant</code> types. Given</p>
</div>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T, T... I&gt; struct S;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_from_sequence&lt;S&lt;T, I&#8230;&#8203;&gt;&gt;</code> is an alias for <code>mp_list&lt;std::integral_constant&lt;T, I&gt;&#8230;&#8203;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_iota_cn">mp_iota_c&lt;N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;std::size_t N&gt; using mp_iota_c = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_iota_c&lt;N&gt;</code> is an alias for <code>mp_list&lt;mp_size_t&lt;0&gt;, mp_size_t&lt;1&gt;, &#8230;&#8203;, mp_size_t&lt;N-1&gt;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_iotan">mp_iota&lt;N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class N&gt; using mp_iota = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_iota_c</code>, but with a type argument <code>N</code>. <code>N::value</code> must be a nonnegative number. Returns
<code>mp_list&lt;std::integral_constant&lt;T, 0&gt;, std::integral_constant&lt;T, 1&gt;, &#8230;&#8203;, std::integral_constant&lt;T, N::value-1&gt;&gt;</code>
where <code>T</code> is the type of <code>N::value</code>.</p>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 9. mp_iota</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_iota&lt;mp_int&lt;4&gt;&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_int&lt;0&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_int&lt;1&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_int&lt;2&gt;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_int&lt;3&gt;</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_at_cl_i">mp_at_c&lt;L, I&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t I&gt; using mp_at_c = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_at_c&lt;L, I&gt;</code> returns the <code>I</code>-th element of <code>L</code>, zero-based.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_atl_i">mp_at&lt;L, I&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class I&gt; using mp_at = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_at_c</code>, but with a type argument <code>I</code>. <code>I::value</code> must be a nonnegative number.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_take_cl_n">mp_take_c&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t N&gt; using mp_take_c = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_take_c&lt;L, N&gt;</code> returns a list of the same form as <code>L</code> containing the first <code>N</code> elements of <code>L</code>.</p>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 10. mp_take_c</caption>
<colgroup>
<col style="width: 40%;">
<col style="width: 10%;">
<col style="width: 10%;">
<col style="width: 10%;">
<col style="width: 10%;">
<col style="width: 10%;">
<col style="width: 10%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m+1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="7"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_take_c&lt;L1, M&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m</sub></code></p></td>
<td class="tableblock halign-center valign-middle" colspan="3"></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_takel_n">mp_take&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class N&gt; using mp_take = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_take_c</code>, but with a type argument <code>N</code>. <code>N::value</code> must be a nonnegative number.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_backl">mp_back&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_back = mp_at_c&lt;L, mp_size&lt;L&gt;::value - 1&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_back&lt;L&gt;</code> returns the last element of the list <code>L</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_pop_backl">mp_pop_back&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_pop_back = mp_take_c&lt;L, mp_size&lt;L&gt;::value - 1&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_pop_back&lt;L&gt;</code> removes the last element of the list <code>L</code> and returns the result.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_insert_cl_i_t">mp_insert_c&lt;L, I, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t I, class... T&gt; using mp_insert_c =
    mp_append&lt;mp_take_c&lt;L, I&gt;, mp_push_front&lt;mp_drop_c&lt;L, I&gt;, T...&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Inserts the elements <code>T&#8230;&#8203;</code> into the list <code>L</code> at position <code>I</code> (a zero-based index).</p>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 11. mp_insert_c with two elements</caption>
<colgroup>
<col style="width: 33.3333%;">
<col style="width: 8.3333%;">
<col style="width: 8.3333%;">
<col style="width: 8.3333%;">
<col style="width: 8.3333%;">
<col style="width: 8.3333%;">
<col style="width: 8.3333%;">
<col style="width: 8.3333%;">
<col style="width: 8.3336%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m+1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
<td class="tableblock halign-center valign-middle" colspan="2"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="9"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_insert_c&lt;L1, M, B<sub>1</sub>, B<sub>2</sub>&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>B<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>m+1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_insertl_i_t">mp_insert&lt;L, I, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class I, class... T&gt; using mp_insert =
    mp_append&lt;mp_take&lt;L, I&gt;, mp_push_front&lt;mp_drop&lt;L, I&gt;, T...&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_insert_c</code>, but with a type argument <code>I</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_erase_cl_i_j">mp_erase_c&lt;L, I, J&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t I, std::size_t J&gt; using mp_erase_c =
    mp_append&lt;mp_take_c&lt;L, I&gt;, mp_drop_c&lt;L, J&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Removes from the list <code>L</code> the elements with indices from <code>I</code> (inclusive) to <code>J</code> (exclusive).</p>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 12. mp_erase_c</caption>
<colgroup>
<col style="width: 30.7692%;">
<col style="width: 7.6923%;">
<col style="width: 7.6923%;">
<col style="width: 7.6923%;">
<col style="width: 7.6923%;">
<col style="width: 7.6923%;">
<col style="width: 7.6923%;">
<col style="width: 7.6923%;">
<col style="width: 7.6923%;">
<col style="width: 7.6924%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>0</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>i-1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>i</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>j-1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>j</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n-1</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="10"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_erase_c&lt;L1, I, J&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>0</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>i-1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>j</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n-1</sub></code></p></td>
<td class="tableblock halign-center valign-middle" colspan="3"></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_erasel_i_j">mp_erase&lt;L, I, J&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class I, class J&gt; using mp_erase =
    mp_append&lt;mp_take&lt;L, I&gt;, mp_drop&lt;L, J&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_erase_c</code>, but with a type arguments <code>I</code> and <code>J</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_replacel_v_w">mp_replace&lt;L, V, W&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V, class W&gt; using mp_replace = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Replaces all <code>V</code> elements of <code>L</code> with <code>W</code> and returns the result.</p>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 13. mp_replace</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>V</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_replace&lt;L1, V, W&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>W</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_replace_ifl_p_w">mp_replace_if&lt;L, P, W&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P, class W&gt; using mp_replace_if = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Replaces all <code>T</code> elements of <code>L</code> for which <code>mp_to_bool&lt;P&lt;T&gt;&gt;</code> is <code>mp_true</code> with <code>W</code> and returns the result.</p>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 14. mp_replace_if</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>P&lt;A<sub>i</sub>&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_false</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_true</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>mp_false</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_replace_if&lt;L1, P, W&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>W</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_replace_if_ql_q_w">mp_replace_if_q&lt;L, Q, W&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q, class W&gt; using mp_replace_if_q =
    mp_replace_if&lt;L, Q::template fn, W&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_replace_if</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_replace_at_cl_i_w">mp_replace_at_c&lt;L, I, W&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t I, class W&gt; using mp_replace_at_c = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Replaces the element of <code>L</code> at zero-based index <code>I</code> with <code>W</code> and returns the result.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_replace_atl_i_w">mp_replace_at&lt;L, I, W&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class I, class W&gt; using mp_replace_at = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_replace_at_c</code>, but with a type argument <code>I</code>. <code>I::value</code> must be a nonnegative number.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_rotate_left_cl_n">mp_rotate_left_c&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t N&gt; using mp_rotate_left_c = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Moves the <code>N % M</code> initial elements of the list <code>L</code> to the back, where <code>M</code> is the size of <code>L</code>. Empty
lists are unchanged.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_rotate_leftl_n">mp_rotate_left&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class N&gt; using mp_rotate_left = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_rotate_left_c</code>, but with a type argument <code>N</code>. <code>N::value</code> must be a nonnegative number.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_rotate_right_cl_n">mp_rotate_right_c&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t N&gt; using mp_rotate_right_c = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Moves the <code>N % M</code> trailing elements of the list <code>L</code> to the front, where <code>M</code> is the size of <code>L</code>. Empty
lists are unchanged.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_rotate_rightl_n">mp_rotate_right&lt;L, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class N&gt; using mp_rotate_right = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>mp_rotate_right_c</code>, but with a type argument <code>N</code>. <code>N::value</code> must be a nonnegative number.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_copy_ifl_p">mp_copy_if&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_copy_if = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Copies the elements <code>T</code> of <code>L</code> for which <code>mp_to_bool&lt;P&lt;T&gt;&gt;</code> is <code>mp_true</code> to a new list of the same form and returns it.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_copy_if_ql_q">mp_copy_if_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_copy_if_q = mp_copy_if&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_copy_if</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_removel_v">mp_remove&lt;L, V&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V&gt; using mp_remove = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Removes all <code>V</code> elements of <code>L</code> and returns the result.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_remove_ifl_p">mp_remove_if&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_remove_if = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Removes all elements <code>T</code> of <code>L</code> for which <code>mp_to_bool&lt;P&lt;T&gt;&gt;</code> is <code>mp_true</code> and returns the result.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_remove_if_ql_q">mp_remove_if_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_remove_if_q = mp_remove_if&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_remove_if</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_flattenl">mp_flatten&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class L2 = mp_clear&lt;L&gt;&gt; using mp_flatten = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Replaces all elements <code>T</code> of <code>L</code> that are lists of the same form as <code>L2</code> (that is, those for which
<code>mp_similar&lt;T, L2&gt;</code> is <code>mp_true</code>) with their elements and returns the result.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 62. Using mp_flatten</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">tuple</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="n">tuple</span><span class="o">&lt;&gt;</span><span class="p">,</span> <span class="kt">void</span><span class="p">,</span> <span class="n">tuple</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">double</span><span class="o">&gt;&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_flatten</span><span class="o">&lt;</span><span class="n">L1</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// tuple&lt;int, void, float, double&gt;</span>

<span class="k">using</span> <span class="n">L2</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">tuple</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R2a</span> <span class="o">=</span> <span class="n">mp_flatten</span><span class="o">&lt;</span><span class="n">L2</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;int, float, tuple&lt;void&gt;&gt;</span>
<span class="k">using</span> <span class="n">R2b</span> <span class="o">=</span> <span class="n">mp_flatten</span><span class="o">&lt;</span><span class="n">L2</span><span class="p">,</span> <span class="n">tuple</span><span class="o">&lt;&gt;&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;int, mp_list&lt;float&gt;, void&gt;</span>

<span class="k">using</span> <span class="n">L3</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;&gt;&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_flatten</span><span class="o">&lt;</span><span class="n">L3</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;float, mp_list&lt;void&gt;&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_interspersel_s">mp_intersperse&lt;L, S&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class S&gt; using mp_intersperse = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Inserts the separator <code>S</code> between the elements of the list <code>L</code>.</p>
</div>
<div class="paragraph">
<p><code>mp_intersperse&lt;L&lt;&gt;, S&gt;</code> is <code>L&lt;&gt;</code>. <code>mp_intersperse&lt;L&lt;T1&gt;, S&gt;</code> is <code>L&lt;T1&gt;</code>.
<code>mp_intersperse&lt;L&lt;T1, T2, T3, &#8230;&#8203;, Tn-1, Tn&gt;, S&gt;</code> is <code>L&lt;T1, S, T2, S, T3, S, &#8230;&#8203;, Tn-1, S, Tn&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_splitl_s">mp_split&lt;L, S&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class S&gt; using mp_split = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Splits the list <code>L</code> into segments at each separator <code>S</code> and returns a list of
the segments.</p>
</div>
<div class="paragraph">
<p><code>mp_split&lt;L&lt;&gt;, S&gt;</code> is <code>L&lt;L&lt;&gt;&gt;</code>. <code>mp_split&lt;L&lt;T&#8230;&#8203;&gt;, S&gt;</code>, where <code>S</code> does not occur in <code>T&#8230;&#8203;</code>,
is <code>L&lt;L&lt;T&#8230;&#8203;&gt;&gt;</code>. <code>mp_split&lt;L&lt;T1&#8230;&#8203;, S, T2&#8230;&#8203;, S, T3&#8230;&#8203;&gt;, S&gt;</code> is <code>L&lt;L&lt;T1&#8230;&#8203;&gt;, L&lt;T2&#8230;&#8203;&gt;, L&lt;T3&#8230;&#8203;&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>The segments may be empty; <code>mp_split&lt;L&lt;S, X, Y, S, S&gt;, S&gt;</code> is <code>L&lt;L&lt;&gt;, L&lt;X, Y&gt;, L&lt;&gt;, L&lt;&gt;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_joinl_s">mp_join&lt;L, S&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class S&gt; using mp_join = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_join</code> is the reverse operation of <code>mp_split</code>; it takes a list of segments <code>L</code> and joins
them into a single list, inserting the separator <code>S</code> between them.</p>
</div>
<div class="paragraph">
<p><code>mp_join&lt;mp_split&lt;L, S&gt;, S&gt;</code> yields back the original list <code>L</code>.</p>
</div>
<div class="paragraph">
<p>For example, <code>mp_split&lt;L&lt;X1, X2, S, X3&gt;, S&gt;</code> gives <code>L&lt;L&lt;X1, X2&gt;, L&lt;X3&gt;&gt;</code>, and
<code>mp_join&lt;L&lt;L&lt;X1, X2&gt;, L&lt;X3&gt;&gt;, S&gt;</code> results in <code>L&lt;X1, X2, S, X3&gt;</code>.</p>
</div>
<div class="paragraph">
<p><code>mp_join&lt;L, S&gt;</code> is equivalent to (and is implemented as) <code>mp_apply&lt;mp_append, mp_intersperse&lt;L, mp_list&lt;S&gt;&gt;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_partitionl_p">mp_partition&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_partition = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_partition&lt;L&lt;T&#8230;&#8203;&gt;, P&gt;</code> partitions <code>L</code> into two lists <code>L&lt;U1&#8230;&#8203;&gt;</code> and <code>L&lt;U2&#8230;&#8203;&gt;</code> such that <code>mp_to_bool&lt;P&lt;T&gt;&gt;</code> is <code>mp_true</code>
for the elements of <code>L&lt;U1&#8230;&#8203;&gt;</code> and <code>mp_false</code> for the elements of <code>L&lt;U2&#8230;&#8203;&gt;</code>. Returns <code>L&lt;L&lt;U1&#8230;&#8203;&gt;, L&lt;U2&#8230;&#8203;&gt;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_partition_ql_q">mp_partition_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_partition_q = mp_partition&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_partition</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_sortl_p">mp_sort&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_sort = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_sort&lt;L, P&gt;</code> sorts the list <code>L</code> according to the strict weak ordering <code>mp_to_bool&lt;P&lt;T, U&gt;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 63. Using mp_sort to sort a list of std::ratio values</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#include &lt;ratio&gt;
</span>
<span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">ratio</span><span class="o">&lt;</span><span class="mi">1</span><span class="p">,</span><span class="mi">2</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">ratio</span><span class="o">&lt;</span><span class="mi">1</span><span class="p">,</span><span class="mi">4</span><span class="o">&gt;&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_sort</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">ratio_less</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;ratio&lt;1,4&gt;, ratio&lt;1,2&gt;&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_sort_ql_q">mp_sort_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_sort_q = mp_sort&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_sort</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_nth_element_cl_i_p">mp_nth_element_c&lt;L, I, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, std::size_t I, template&lt;class...&gt; class P&gt; using mp_nth_element_c =
    /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Returns the element at position <code>I</code> in <code>mp_sort&lt;L, P&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_nth_elementl_i_p">mp_nth_element&lt;L, I, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class I, template&lt;class...&gt; class P&gt; using mp_nth_element = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>Like <code>mp_nth_element_c</code>, but with a type argument <code>I</code>. <code>I::value</code> must be a nonnegative number.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_nth_element_ql_i_q">mp_nth_element_q&lt;L, I, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class I, class Q&gt; using mp_nth_element_q =
    mp_nth_element&lt;L, I, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Like <code>mp_nth_element</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_min_elementl_p">mp_min_element&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_min_element = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_min_element&lt;L, P&gt;</code> returns the minimal element of the list <code>L</code> according to the ordering <code>mp_to_bool&lt;P&lt;T, U&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>It&#8217;s equivalent to <code>mp_fold&lt;mp_rest&lt;L&gt;, mp_first&lt;L&gt;, F&gt;</code>, where <code>F&lt;T, U&gt;</code> returns <code>mp_if&lt;P&lt;T, U&gt;, T, U&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_min_element_ql_q">mp_min_element_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_min_element_q = mp_min_element&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_min_element</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_max_elementl_p">mp_max_element&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_max_element = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_max_element&lt;L, P&gt;</code> returns the maximal element of the list <code>L</code> according to the ordering <code>mp_to_bool&lt;P&lt;T, U&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>It&#8217;s equivalent to <code>mp_fold&lt;mp_rest&lt;L&gt;, mp_first&lt;L&gt;, F&gt;</code>, where <code>F&lt;T, U&gt;</code> returns <code>mp_if&lt;P&lt;U, T&gt;, T, U&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_max_element_ql_q">mp_max_element_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_max_element_q = mp_max_element&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_max_element</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_findl_v">mp_find&lt;L, V&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V&gt; using mp_find = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_find&lt;L, V&gt;</code> returns the index at which the type <code>V</code> is located in the list <code>L</code>. It&#8217;s an alias for <code>mp_size_t&lt;I&gt;</code>,
where <code>I</code> is the zero-based index of the first occurrence of <code>V</code> in <code>L</code>. If <code>L</code> does not contain <code>V</code>, <code>mp_find&lt;L, V&gt;</code>
is <code>mp_size&lt;L&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_find_ifl_p">mp_find_if&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_find_if = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_find_f&lt;L, P&gt;</code> is an alias for <code>mp_size_t&lt;I&gt;</code>, where <code>I</code> is the zero-based index of the first element <code>T</code> in <code>L</code> for which
<code>mp_to_bool&lt;P&lt;T&gt;&gt;</code> is <code>mp_true</code>. If there is no such element, <code>mp_find_if&lt;L, P&gt;</code> is <code>mp_size&lt;L&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_find_if_ql_q">mp_find_if_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_find_if_q = mp_find_if&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_find_if</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_reversel">mp_reverse&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_reverse = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_reverse&lt;L&lt;T1, T2, &#8230;&#8203;, Tn&gt;&gt;</code> is <code>L&lt;Tn, &#8230;&#8203;, T2, T1&gt;</code>.</p>
</div>
<table class="tableblock frame-all grid-all" style="width: 85%;">
<caption class="title">Illustration 15. mp_reverse</caption>
<colgroup>
<col style="width: 50%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
<col style="width: 12.5%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>L1</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>2</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle" colspan="5"></td>
</tr>
<tr>
<td class="tableblock halign-left valign-middle"><p class="tableblock"><code><strong>mp_reverse&lt;L1&gt;</strong></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>n-1</sub></code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>&#8230;&#8203;</code></p></td>
<td class="tableblock halign-center valign-middle"><p class="tableblock"><code>A<sub>1</sub></code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="mp_foldl_v_f">mp_fold&lt;L, V, F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V, template&lt;class...&gt; class F&gt; using mp_fold = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_fold&lt;L&lt;T1, T2, &#8230;&#8203;, Tn&gt;, V, F&gt;</code> is <code>F&lt; F&lt; F&lt; F&lt;V, T1&gt;, T2&gt;, &#8230;&#8203;&gt;, Tn&gt;</code>, or <code>V</code>, if <code>L</code> is empty.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 64. Using mp_fold to add the contents of a list of std::ratio values</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#include &lt;ratio&gt;
</span>
<span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="n">ratio</span><span class="o">&lt;</span><span class="mi">1</span><span class="p">,</span><span class="mi">8</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">ratio</span><span class="o">&lt;</span><span class="mi">1</span><span class="p">,</span><span class="mi">4</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">ratio</span><span class="o">&lt;</span><span class="mi">1</span><span class="p">,</span><span class="mi">2</span><span class="o">&gt;&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_fold</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">ratio</span><span class="o">&lt;</span><span class="mi">0</span><span class="p">,</span><span class="mi">1</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">ratio_add</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// std::ratio&lt;7,8&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_fold_ql_v_q">mp_fold_q&lt;L, V, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V, class Q&gt; using mp_fold_q =
    mp_fold&lt;L, V, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_fold</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_reverse_foldl_v_f">mp_reverse_fold&lt;L, V, F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V, template&lt;class...&gt; class F&gt; using mp_reverse_fold =
    /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_reverse_fold&lt;L&lt;T1, T2, &#8230;&#8203;, Tn&gt;, V, F&gt;</code> is <code>F&lt;T1, F&lt;T2, F&lt;&#8230;&#8203;, F&lt;Tn, V&gt;&gt;&gt;&gt;</code>, or <code>V</code>, if <code>L</code> is empty.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_reverse_fold_ql_v_q">mp_reverse_fold_q&lt;L, V, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V, class Q&gt; using mp_reverse_fold_q =
    mp_reverse_fold&lt;L, V, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_reverse_fold</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_partial_suml_v_f">mp_partial_sum&lt;L, V, F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V, template&lt;class...&gt; class F&gt; using mp_partial_sum = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_partial_sum&lt;L, V, F&gt;</code> is similar to <code>mp_fold&lt;L, V, F&gt;</code>, but instead of its final result, it returns
a list (of the same form as <code>L</code>) holding the intermediate results of the fold. The last element of the
result of <code>mp_partial_sum</code> is the same as the result of <code>mp_fold</code>.</p>
</div>
<div class="paragraph">
<p>For example, <code>mp_fold&lt;mp_list&lt;X1, X2, X3&gt;, V, F&gt;</code> is <code>F&lt;F&lt;F&lt;V, X1&gt;, X2&gt;, X3&gt;</code>, but
<code>mp_partial_sum&lt;mp_list&lt;X1, X2, X3&gt;, V, F&gt;</code> is <code>mp_list&lt;F&lt;V, X1&gt;, F&lt;F&lt;V, X1&gt;, X2&gt;, F&lt;F&lt;F&lt;V, X1&gt;, X2&gt;, X3&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>It&#8217;s common for <code>F</code> to be <code>mp_plus</code>, in which case the result contains the partial sums of <code>L</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 65. Using mp_partial_sum</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">mp_list_c</span><span class="o">&lt;</span><span class="kt">int</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">4</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_partial_sum</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">mp_int</span><span class="o">&lt;</span><span class="mi">0</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_plus</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list_c&lt;int, 1, 3, 6, 10&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_partial_sum_ql_v_q">mp_partial_sum_q&lt;L, V, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class V, class Q&gt; using mp_partial_sum_q =
    mp_partial_sum&lt;L, V, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_partial_sum</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_pairwise_foldl_f">mp_pairwise_fold&lt;L, F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class F&gt; using mp_pairwise_fold = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_pairwise_fold&lt;L, F&gt;</code> returns a list of the same form as <code>L</code> whose elements are
the result of the application of the binary metafunction <code>F</code> to each pair of adjacent
elements of <code>L</code>. That is, <code>mp_pairwise_fold&lt;L&lt;T1, T2, T3&gt;, F&gt;</code> is
<code>L&lt;F&lt;T1, T2&gt;, F&lt;T2, T3&gt;&gt;</code>.</p>
</div>
<div class="paragraph">
<p>The result has one fewer element than the original. If <code>L</code> has only one element, the
result is an empty list. If <code>L</code> is an empty list, the result is also an empty list.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 66. Using mp_pairwise_fold</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">L</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">is_increasing</span> <span class="o">=</span> <span class="n">mp_all_of</span><span class="o">&lt;</span>
    <span class="n">mp_pairwise_fold</span><span class="o">&lt;</span><span class="n">L</span><span class="p">,</span> <span class="n">mp_less</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">mp_to_bool</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_pairwise_fold_ql_q">mp_pairwise_fold_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_pairwise_fold_q =
    mp_pairwise_fold&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_pairwise_fold</code>, but takes a quoted metafunction.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 67. Using mp_pairwise_fold_q</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">L</span><span class="p">,</span> <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span><span class="p">&gt;</span> <span class="k">class</span> <span class="nc">P</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">is_sorted</span> <span class="o">=</span>
    <span class="n">mp_none_of</span><span class="o">&lt;</span><span class="n">mp_pairwise_fold_q</span><span class="o">&lt;</span><span class="n">L</span><span class="p">,</span> <span class="n">mp_bind</span><span class="o">&lt;</span><span class="n">P</span><span class="p">,</span> <span class="n">_2</span><span class="p">,</span> <span class="n">_1</span><span class="o">&gt;&gt;</span><span class="p">,</span> <span class="n">mp_to_bool</span><span class="o">&gt;</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_iteratev_f_r">mp_iterate&lt;V, F, R&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class V, template&lt;class...&gt; class F, template&lt;class...&gt; class R&gt;
    using mp_iterate = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_iterate&lt;V, F, R&gt;</code> applies <code>R</code> to <code>V</code> successively until that&#8217;s no longer possible,
yielding the sequence <code>V</code>, <code>R&lt;V&gt;</code>, <code>R&lt;R&lt;V&gt;&gt;</code>, <code>R&lt;R&lt;R&lt;V&gt;&gt;&gt;</code>&#8230;&#8203;</p>
</div>
<div class="paragraph">
<p>It then returns an <code>mp_list</code> whose elements are formed by applying <code>F</code> to the above
sequence of values. That is, it returns <code>mp_list&lt;F&lt;V&gt;, F&lt;R&lt;V&gt;&gt;, F&lt;R&lt;R&lt;V&gt;&gt;&gt;, &#8230;&#8203;&gt;</code>.</p>
</div>
<div class="paragraph">
<p><code>mp_iterate</code> is in a way the reverse operation of <code>mp_reverse_fold</code>. Given</p>
</div>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T, class U&gt; struct cons {};
struct nil {};</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_reverse_fold&lt;mp_list&lt;X1, X2, X3&gt;, nil, cons&gt;</code> produces <code>cons&lt;X1, cons&lt;X2, cons&lt;X3, nil&gt;&gt;&gt;</code>,
which when passed as <code>V</code> to <code>mp_iterate&lt;V, mp_first, mp_second&gt;</code> recovers the original
<code>mp_list&lt;X1, X2, X3&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 68. Using mp_iterate</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">struct</span> <span class="nc">X1</span> <span class="p">{};</span>
<span class="k">struct</span> <span class="nc">X2</span> <span class="p">{};</span>
<span class="k">struct</span> <span class="nc">X3</span> <span class="p">{};</span>

<span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="n">X1</span><span class="p">,</span> <span class="n">X2</span><span class="p">,</span> <span class="n">X3</span><span class="o">&gt;</span><span class="p">;</span>
<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_iterate</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">mp_first</span><span class="p">,</span> <span class="n">mp_rest</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// L1</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">cons</span> <span class="p">{};</span>
<span class="k">struct</span> <span class="nc">nil</span> <span class="p">{};</span>

<span class="k">using</span> <span class="n">V2</span> <span class="o">=</span> <span class="n">mp_reverse_fold</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">nil</span><span class="p">,</span> <span class="n">cons</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// cons&lt;X1, cons&lt;X2, cons&lt;X3, nil&gt;&gt;&gt;</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_iterate</span><span class="o">&lt;</span><span class="n">V2</span><span class="p">,</span> <span class="n">mp_first</span><span class="p">,</span> <span class="n">mp_second</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// L1</span>

<span class="k">struct</span> <span class="nc">Y1</span> <span class="p">{};</span>
<span class="k">struct</span> <span class="nc">Y2</span> <span class="p">{</span> <span class="k">using</span> <span class="n">value_type</span> <span class="o">=</span> <span class="kt">double</span><span class="p">;</span> <span class="k">using</span> <span class="n">next_type</span> <span class="o">=</span> <span class="n">Y1</span><span class="p">;</span> <span class="p">};</span>
<span class="k">struct</span> <span class="nc">Y3</span> <span class="p">{</span> <span class="k">using</span> <span class="n">value_type</span> <span class="o">=</span> <span class="kt">float</span><span class="p">;</span> <span class="k">using</span> <span class="n">next_type</span> <span class="o">=</span> <span class="n">Y2</span><span class="p">;</span> <span class="p">};</span>
<span class="k">struct</span> <span class="nc">Y4</span> <span class="p">{</span> <span class="k">using</span> <span class="n">value_type</span> <span class="o">=</span> <span class="kt">int</span><span class="p">;</span> <span class="k">using</span> <span class="n">next_type</span> <span class="o">=</span> <span class="n">Y3</span><span class="p">;</span> <span class="p">};</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">value_type</span> <span class="o">=</span> <span class="k">typename</span> <span class="n">T</span><span class="o">::</span><span class="n">value_type</span><span class="p">;</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">next_type</span> <span class="o">=</span> <span class="k">typename</span> <span class="n">T</span><span class="o">::</span><span class="n">next_type</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_iterate</span><span class="o">&lt;</span><span class="n">Y4</span><span class="p">,</span> <span class="n">mp_identity_t</span><span class="p">,</span> <span class="n">next_type</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;Y4, Y3, Y2, Y1&gt;</span>
<span class="k">using</span> <span class="n">R4</span> <span class="o">=</span> <span class="n">mp_iterate</span><span class="o">&lt;</span><span class="n">Y4</span><span class="p">,</span> <span class="n">value_type</span><span class="p">,</span> <span class="n">next_type</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_list&lt;int, float, double&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_iterate_qv_qf_qr">mp_iterate_q&lt;V, Qf, Qr&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class V, class Qf, class Qr&gt; using mp_iterate_q =
    mp_iterate&lt;V, Qf::template fn, Qr::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_iterate</code>, but takes quoted metafunctions.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_uniquel">mp_unique&lt;L&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L&gt; using mp_unique = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_unique&lt;L&gt;</code> returns a list of the same form as <code>L</code> with the duplicate elements removed.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_unique_ifl_p">mp_unique_if&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_unique_if = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_unique</code>, but two elements <code>T</code> and <code>U</code> are considered duplicates when <code>mp_to_bool&lt;P&lt;T, U&gt;&gt;</code> is <code>mp_true</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_unique_if_ql_q">mp_unique_if_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_unique_if_q =
    mp_unique_if&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_unique_if</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_all_ofl_p">mp_all_of&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_all_of =
    mp_bool&lt; mp_count_if&lt;L, P&gt;::value == mp_size&lt;L&gt;::value &gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_all_of&lt;L, P&gt;</code> is <code>mp_true</code> when <code>P</code> holds for all elements of <code>L</code>, <code>mp_false</code> otherwise. When <code>L</code> is empty, the result is <code>mp_true</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_all_of_ql_q">mp_all_of_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_all_of_q = mp_all_of&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_all_of</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_none_ofl_p">mp_none_of&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_none_of =
    mp_bool&lt; mp_count_if&lt;L, P&gt;::value == 0 &gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_none_of&lt;L, P&gt;</code> is <code>mp_true</code> when <code>P</code> holds for no element of <code>L</code>, <code>mp_false</code> otherwise. When <code>L</code> is empty, the result is <code>mp_true</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_none_of_ql_q">mp_none_of_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_none_of_q = mp_none_of&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_none_of</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_any_ofl_p">mp_any_of&lt;L, P&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, template&lt;class...&gt; class P&gt; using mp_any_of =
    mp_bool&lt; mp_count_if&lt;L, P&gt;::value != 0 &gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_any_of&lt;L, P&gt;</code> is <code>mp_true</code> when <code>P</code> holds for at least one element of <code>L</code>, <code>mp_false</code> otherwise. When <code>L</code> is empty, the result is <code>mp_false</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_any_of_ql_q">mp_any_of_q&lt;L, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class Q&gt; using mp_any_of_q = mp_any_of&lt;L, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_any_of</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_for_eachlf">mp_for_each&lt;L&gt;(f)</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class F&gt; constexpr F mp_for_each(F&amp;&amp; f);</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_for_each&lt;L&gt;(f)</code> calls <code>f</code> with <code>T()</code> for each element <code>T</code> of the list <code>L</code>, in order.</p>
</div>
<div class="paragraph">
<p>Returns <code>std::forward&lt;F&gt;(f)</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 69. Using mp_for_each and a C++14 lambda to print a tuple</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="kt">void</span> <span class="nf">print</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">tp</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="k">const</span> <span class="n">N</span> <span class="o">=</span> <span class="k">sizeof</span><span class="p">...(</span><span class="n">T</span><span class="p">);</span>

    <span class="n">mp_for_each</span><span class="o">&lt;</span><span class="n">mp_iota_c</span><span class="o">&lt;</span><span class="n">N</span><span class="o">&gt;&gt;</span><span class="p">(</span> <span class="p">[</span><span class="o">&amp;</span><span class="p">](</span> <span class="k">auto</span> <span class="n">I</span> <span class="p">){</span>

        <span class="c1">// I is mp_size_t&lt;0&gt;, mp_size_t&lt;1&gt;, ..., mp_size_t&lt;N-1&gt;</span>

        <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">get</span><span class="o">&lt;</span><span class="n">I</span><span class="o">&gt;</span><span class="p">(</span><span class="n">tp</span><span class="p">)</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>

    <span class="p">});</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>In case the elements of the list <code>L</code> are not default-constructible, you can use
<code>mp_for_each&lt;mp_transform&lt;mp_identity, L&gt;&gt;</code>, which would call <code>f</code> with <code>mp_identity&lt;T&gt;()</code>
instead of <code>T()</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_with_indexni_f">mp_with_index&lt;N&gt;(i, f)</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;std::size_t N, class F&gt;
  constexpr auto mp_with_index( std::size_t i, F &amp;&amp; f )
    -&gt; decltype(std::declval&lt;F&gt;()(std::declval&lt;mp_size_t&lt;0&gt;&gt;()));</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_with_index&lt;N&gt;(i, f)</code> calls <code>f</code> with <code>mp_size_t&lt;i&gt;()</code> and returns the result. <code>i</code> must be less than <code>N</code>.
Only <code>constexpr</code> on C++14 and higher.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>template&lt;class N, class F&gt;
  constexpr auto mp_with_index( std::size_t i, F &amp;&amp; f )
    -&gt; decltype(std::declval&lt;F&gt;()(std::declval&lt;mp_size_t&lt;0&gt;&gt;()));</pre>
</div>
</div>
<div class="paragraph">
<p>Returns <code>mp_with_index&lt;N::value&gt;(i, f)</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 70. Using mp_with_index and a C++14 lambda to print the active element of a variant</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="kt">void</span> <span class="nf">print</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">variant</span><span class="o">&lt;</span><span class="n">T</span><span class="p">...</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">v</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">mp_with_index</span><span class="o">&lt;</span><span class="k">sizeof</span><span class="p">...(</span><span class="n">T</span><span class="p">)</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">v</span><span class="p">.</span><span class="n">index</span><span class="p">(),</span> <span class="p">[</span><span class="o">&amp;</span><span class="p">](</span> <span class="k">auto</span> <span class="n">I</span> <span class="p">)</span> <span class="p">{</span>

        <span class="c1">// I is mp_size_t&lt;v.index()&gt;{} here</span>

        <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">get</span><span class="o">&lt;</span><span class="n">I</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">v</span> <span class="p">)</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>

    <span class="p">});</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="set">Set Operations, &lt;boost/mp11/set.hpp&gt;</h3>
<div class="paragraph">
<p>A set is a list whose elements are unique.</p>
</div>
<div class="sect3">
<h4 id="mp_is_sets">mp_is_set&lt;S&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class S&gt; using mp_is_set = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_is_set&lt;S&gt;</code> is <code>mp_true</code> if <code>S</code> is a set, <code>mp_false</code> otherwise.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_set_containss_v">mp_set_contains&lt;S, V&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class S, class V&gt; using mp_set_contains = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_set_contains&lt;S, V&gt;</code> is <code>mp_true</code> if the type <code>V</code> is an element of the set <code>S</code>, <code>mp_false</code> otherwise.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_set_push_backs_t">mp_set_push_back&lt;S, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class S, class... T&gt; using mp_set_push_back = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>For each <code>T1</code> in <code>T&#8230;&#8203;</code>, <code>mp_set_push_back&lt;S, T&#8230;&#8203;&gt;</code> appends <code>T1</code> to the end of the set <code>S</code> if it&#8217;s not already an element of <code>S</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_set_push_fronts_t">mp_set_push_front&lt;S, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class S, class... T&gt; using mp_set_push_front = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_set_push_front&lt;S, T&#8230;&#8203;&gt;</code> inserts at the front of the set <code>S</code> those elements of <code>T&#8230;&#8203;</code> for which <code>S</code> does not already contain the same type.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_set_unionl">mp_set_union&lt;L&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... L&gt; using mp_set_union = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_set_union&lt;S, L&#8230;&#8203;&gt;</code> is <code>mp_set_push_back&lt;S, T&#8230;&#8203;&gt;</code>, where <code>T&#8230;&#8203;</code> are the combined elements of the lists <code>L&#8230;&#8203;</code>.
<code>mp_set_union&lt;&gt;</code> is <code>mp_list&lt;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_set_intersections">mp_set_intersection&lt;S&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... S&gt; using mp_set_intersection = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_set_intersection&lt;S&#8230;&#8203;&gt;</code> returns a set that contains the elements that occur in all of the sets <code>S&#8230;&#8203;</code>.
<code>mp_set_intersection&lt;&gt;</code> is <code>mp_list&lt;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_set_differencel_s">mp_set_difference&lt;L, S&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class L, class... S&gt; using mp_set_difference = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_set_difference&lt;L, S&#8230;&#8203;&gt;</code> removes the elements of the list <code>L</code> that appear in any of the sets <code>S&#8230;&#8203;</code> and
returns the result.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="map">Map Operations, &lt;boost/mp11/map.hpp&gt;</h3>
<div class="paragraph">
<p>A map is a list of lists, the inner lists having at least one element (the key.) The keys of the map must be unique.</p>
</div>
<div class="sect3">
<h4 id="mp_is_mapm">mp_is_map&lt;M&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class M&gt; using mp_is_map = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_is_map&lt;M&gt;</code> is <code>mp_true</code> if <code>M</code> is a map, <code>mp_false</code> otherwise.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_map_findm_k">mp_map_find&lt;M, K&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class M, class K&gt; using mp_map_find = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_map_find&lt;M, K&gt;</code> is an alias for the element of the map <code>M</code> with a key <code>K</code>, or for <code>void</code>, if there is no such element.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_map_containsm_k">mp_map_contains&lt;M, K&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class M, class K&gt; using mp_map_contains =
    mp_not&lt;std::is_same&lt;mp_map_find&lt;M, K&gt;, void&gt;&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_map_contains&lt;M, K&gt;</code> is <code>mp_true</code> if the map <code>M</code> contains an element with a key <code>K</code>, <code>mp_false</code> otherwise.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_map_insertm_t">mp_map_insert&lt;M, T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class M, class T&gt; using mp_map_insert =
    mp_if&lt; mp_map_contains&lt;M, mp_first&lt;T&gt;&gt;, M, mp_push_back&lt;M, T&gt; &gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>Inserts the element <code>T</code> into the map <code>M</code>, if an element with a key <code>mp_first&lt;T&gt;</code> is not already in <code>M</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_map_replacem_t">mp_map_replace&lt;M, T&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class M, class T&gt; using mp_map_replace = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>If the map <code>M</code> does not contain an element with a key <code>mp_first&lt;T&gt;</code>, inserts it (using <code>mp_push_back&lt;M, T&gt;</code>); otherwise,
replaces the existing element with <code>T</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_map_updatem_t_f">mp_map_update&lt;M, T, F&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class M, class T, template&lt;class...&gt; class F&gt; using mp_map_update = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>If the map <code>M</code> does not contain an element with a key <code>mp_first&lt;T&gt;</code>, inserts it (using <code>mp_push_back&lt;M, T&gt;</code>); otherwise,
replaces the existing element <code>L&lt;X, Y&#8230;&#8203;&gt;</code> with <code>L&lt;X, F&lt;X, Y&#8230;&#8203;&gt;&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 71. Using mp_map_update to count the number of occurrences of types in a list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">U</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">inc2nd</span> <span class="o">=</span> <span class="n">mp_int</span><span class="o">&lt;</span><span class="n">U</span><span class="o">::</span><span class="n">value</span> <span class="o">+</span> <span class="mi">1</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">M</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">using</span> <span class="n">count_types</span> <span class="o">=</span>
    <span class="n">mp_map_update</span><span class="o">&lt;</span><span class="n">M</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">pair</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span> <span class="n">mp_int</span><span class="o">&lt;</span><span class="mi">1</span><span class="o">&gt;&gt;</span><span class="p">,</span> <span class="n">inc2nd</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">L1</span> <span class="o">=</span> <span class="n">mp_list</span><span class="o">&lt;</span><span class="kt">float</span><span class="p">,</span> <span class="kt">char</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="kt">float</span><span class="p">,</span> <span class="kt">char</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_fold</span><span class="o">&lt;</span><span class="n">L1</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="n">tuple</span><span class="o">&lt;&gt;</span><span class="p">,</span> <span class="n">count_types</span><span class="o">&gt;</span><span class="p">;</span>
<span class="c1">// std::tuple&lt;std::pair&lt;float, mp_int&lt;6&gt;&gt;, std::pair&lt;char, mp_int&lt;2&gt;&gt;&gt;</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_map_update_qm_t_q">mp_map_update_q&lt;M, T, Q&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class M, class T, class Q&gt; using mp_map_update_q =
    mp_map_update&lt;M, T, Q::template fn&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_map_update</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_map_erasem_k">mp_map_erase&lt;M, K&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class M, class K&gt; using mp_map_erase = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p>If the map <code>M</code> contains an element with a key <code>K</code>, removes it.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_map_keysm">mp_map_keys&lt;M&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class M&gt; using mp_map_keys = mp_transform&lt;mp_first, M&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_map_keys&lt;M&gt;</code> returns a list of the keys of <code>M</code>. When <code>M</code> is a valid map, the keys are unique, so the result is a set.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="function">Helper Metafunctions, &lt;boost/mp11/function.hpp&gt;</h3>
<div class="sect3">
<h4 id="mp_voidt">mp_void&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; using mp_void = void;</pre>
</div>
</div>
<div class="paragraph">
<p>Same as <code>std::void_t</code> from C++17.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_andt">mp_and&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; using mp_and = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_and&lt;T&#8230;&#8203;&gt;</code> applies <code>mp_to_bool</code> to the types in <code>T&#8230;&#8203;</code>, in order. If the result of an application is <code>mp_false</code>, <code>mp_and</code>
returns <code>mp_false</code>. If the application causes a substitution failure, returns <code>mp_false</code>. If all results are <code>mp_true</code>,
returns <code>mp_true</code>. <code>mp_and&lt;&gt;</code> is <code>mp_true</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 72. mp_and behavior</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_and</span><span class="o">&lt;</span><span class="n">mp_true</span><span class="p">,</span> <span class="n">mp_true</span><span class="o">&gt;</span><span class="p">;</span>   <span class="c1">// mp_true</span>

<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_and</span><span class="o">&lt;</span><span class="n">mp_false</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>     <span class="c1">// mp_false, void is not reached</span>

<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_and</span><span class="o">&lt;</span><span class="n">mp_false</span><span class="p">,</span> <span class="n">mp_false</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_false</span>

<span class="k">using</span> <span class="n">R4</span> <span class="o">=</span> <span class="n">mp_and</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="n">mp_true</span><span class="o">&gt;</span><span class="p">;</span>      <span class="c1">// mp_false (!)</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_allt">mp_all&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; using mp_all = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_all&lt;T&#8230;&#8203;&gt;</code> is <code>mp_true</code> if <code>mp_to_bool&lt;U&gt;</code> is <code>mp_true</code> for all types <code>U</code> in <code>T&#8230;&#8203;</code>, <code>mp_false</code> otherwise. Same as
<code>mp_and</code>, but does not perform short-circuit evaluation. <code>mp_and&lt;mp_false, void&gt;</code> is <code>mp_false</code>, but <code>mp_all&lt;mp_false, void&gt;</code>
is an error because <code>void</code> does not have a nested <code>value</code>. The upside is that <code>mp_all</code> is potentially faster and does not
mask substitution failures as <code>mp_and</code> does.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 73. mp_all behavior</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">mp_true</span><span class="p">,</span> <span class="n">mp_true</span><span class="o">&gt;</span><span class="p">;</span>   <span class="c1">// mp_true</span>

<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">mp_false</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>     <span class="c1">// compile-time error</span>

<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="n">mp_false</span><span class="p">,</span> <span class="n">mp_false</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_false</span>

<span class="k">using</span> <span class="n">R4</span> <span class="o">=</span> <span class="n">mp_all</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="n">mp_true</span><span class="o">&gt;</span><span class="p">;</span>      <span class="c1">// compile-time error</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_ort">mp_or&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; using mp_or = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_or&lt;T&#8230;&#8203;&gt;</code> applies <code>mp_to_bool</code> to the types in <code>T&#8230;&#8203;</code>, in order. If the result of an application is <code>mp_true</code>, <code>mp_or</code>
returns <code>mp_true</code>. If all results are <code>mp_false</code>, returns <code>mp_false</code>. <code>mp_or&lt;&gt;</code> is <code>mp_false</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 74. mp_or behavior</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_or</span><span class="o">&lt;</span><span class="n">mp_true</span><span class="p">,</span> <span class="n">mp_false</span><span class="o">&gt;</span><span class="p">;</span>   <span class="c1">// mp_true</span>

<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_or</span><span class="o">&lt;</span><span class="n">mp_true</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>       <span class="c1">// mp_true, void is not reached</span>

<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_or</span><span class="o">&lt;</span><span class="n">mp_false</span><span class="p">,</span> <span class="n">mp_false</span><span class="o">&gt;</span><span class="p">;</span>  <span class="c1">// mp_false</span>

<span class="k">using</span> <span class="n">R4</span> <span class="o">=</span> <span class="n">mp_or</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="n">mp_true</span><span class="o">&gt;</span><span class="p">;</span>       <span class="c1">// compile-time error</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_anyt">mp_any&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; using mp_any = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_any&lt;T&#8230;&#8203;&gt;</code> is <code>mp_true</code> if <code>mp_to_bool&lt;U&gt;</code> is <code>mp_true</code> for any type <code>U</code> in <code>T&#8230;&#8203;</code>, <code>mp_false</code> otherwise. Same as
<code>mp_or</code>, but does not perform short-circuit evaluation.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 75. mp_any behavior</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_any</span><span class="o">&lt;</span><span class="n">mp_true</span><span class="p">,</span> <span class="n">mp_false</span><span class="o">&gt;</span><span class="p">;</span>  <span class="c1">// mp_true</span>

<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_any</span><span class="o">&lt;</span><span class="n">mp_true</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>      <span class="c1">// compile-time error</span>

<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_any</span><span class="o">&lt;</span><span class="n">mp_false</span><span class="p">,</span> <span class="n">mp_false</span><span class="o">&gt;</span><span class="p">;</span> <span class="c1">// mp_false</span>

<span class="k">using</span> <span class="n">R4</span> <span class="o">=</span> <span class="n">mp_any</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="n">mp_true</span><span class="o">&gt;</span><span class="p">;</span>      <span class="c1">// compile-time error</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_samet">mp_same&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; using mp_same = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_same&lt;T&#8230;&#8203;&gt;</code> is <code>mp_true</code> if all the types in <code>T&#8230;&#8203;</code> are the same type, <code>mp_false</code> otherwise. <code>mp_same&lt;&gt;</code> is <code>mp_true</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_similart">mp_similar&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; using mp_similar = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_similar&lt;T&#8230;&#8203;&gt;</code> is <code>mp_true</code> if all the types in <code>T&#8230;&#8203;</code> are the same type, or instantiations of the same class template
whose parameters are all types, <code>mp_false</code> otherwise. <code>mp_similar&lt;&gt;</code> is <code>mp_true</code>.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 76. mp_similar</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">R1</span> <span class="o">=</span> <span class="n">mp_similar</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>                        <span class="c1">// mp_true</span>
<span class="k">using</span> <span class="n">R2</span> <span class="o">=</span> <span class="n">mp_similar</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>                  <span class="c1">// mp_true</span>
<span class="k">using</span> <span class="n">R3</span> <span class="o">=</span> <span class="n">mp_similar</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">void</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;</span><span class="p">;</span>            <span class="c1">// mp_true</span>
<span class="k">using</span> <span class="n">R4</span> <span class="o">=</span> <span class="n">mp_similar</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">void</span><span class="p">,</span> <span class="kt">float</span><span class="o">&gt;</span><span class="p">;</span>           <span class="c1">// mp_false</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">X</span><span class="p">;</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">Y</span><span class="p">;</span>

<span class="k">using</span> <span class="n">R5</span> <span class="o">=</span> <span class="n">mp_similar</span><span class="o">&lt;</span><span class="n">X</span><span class="o">&lt;</span><span class="kt">int</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">X</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">X</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;&gt;</span><span class="p">;</span>   <span class="c1">// mp_true</span>
<span class="k">using</span> <span class="n">R6</span> <span class="o">=</span> <span class="n">mp_similar</span><span class="o">&lt;</span><span class="n">Y</span><span class="o">&lt;&gt;</span><span class="p">,</span> <span class="n">Y</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">Y</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="kt">void</span><span class="o">&gt;&gt;</span><span class="p">;</span> <span class="c1">// mp_true</span>
<span class="k">using</span> <span class="n">R7</span> <span class="o">=</span> <span class="n">mp_similar</span><span class="o">&lt;</span><span class="n">X</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">Y</span><span class="o">&lt;</span><span class="kt">void</span><span class="o">&gt;&gt;</span><span class="p">;</span>            <span class="c1">// mp_false</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="mp_plust">mp_plus&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; using mp_plus = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_plus&lt;T&#8230;&#8203;&gt;</code> is an integral constant type with a value that is the sum of <code>U::value</code> for all types <code>U</code> in <code>T&#8230;&#8203;</code>.
<code>mp_plus&lt;&gt;</code> is <code>mp_int&lt;0&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_lesst1_t2">mp_less&lt;T1, T2&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T1, class T2&gt; using mp_less = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_less&lt;T1, T2&gt;</code> is <code>mp_true</code> when the numeric value of <code>T1::value</code> is less than the numeric value of <code>T2::value</code>,
<code>mp_false</code> otherwise.</p>
</div>
<div class="paragraph">
<p>(Note that this is not necessarily the same as <code>T1::value &lt; T2::value</code> when comparing between signed and unsigned types;
<code>-1 &lt; 1u</code> is <code>false</code>, but <code>mp_less&lt;mp_int&lt;-1&gt;, mp_size_t&lt;1&gt;&gt;</code> is <code>mp_true</code>.)</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_mint1_t">mp_min&lt;T1, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T1, class... T&gt; using mp_min = mp_min_element&lt;mp_list&lt;T1, T...&gt;, mp_less&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_min&lt;T&#8230;&#8203;&gt;</code> returns the type <code>U</code> in <code>T&#8230;&#8203;</code> with the lowest <code>U::value</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_maxt1_t">mp_max&lt;T1, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T1, class... T&gt; using mp_max = mp_max_element&lt;mp_list&lt;T1, T...&gt;, mp_less&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_max&lt;T&#8230;&#8203;&gt;</code> returns the type <code>U</code> in <code>T&#8230;&#8203;</code> with the highest <code>U::value</code>.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="bind">Bind, &lt;boost/mp11/bind.hpp&gt;</h3>
<div class="sect3">
<h4 id="mp_argi">mp_arg&lt;I&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;std::size_t I&gt; struct mp_arg;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_arg&lt;I&gt;</code> is a quoted metafunction whose nested template <code>fn&lt;T&#8230;&#8203;&gt;</code> returns the <code>I</code>-th zero-based element of <code>T&#8230;&#8203;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="1_9">_1, &#8230;&#8203;, _9</h4>
<div class="literalblock">
<div class="content">
<pre>using _1 = mp_arg&lt;0&gt;;
using _2 = mp_arg&lt;1&gt;;
using _3 = mp_arg&lt;2&gt;;
using _4 = mp_arg&lt;3&gt;;
using _5 = mp_arg&lt;4&gt;;
using _6 = mp_arg&lt;5&gt;;
using _7 = mp_arg&lt;6&gt;;
using _8 = mp_arg&lt;7&gt;;
using _9 = mp_arg&lt;8&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>_1</code> to <code>_9</code> are placeholder types, the equivalent to the placeholders of <code>boost::bind</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_bindf_t">mp_bind&lt;F, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F, class... T&gt; struct mp_bind;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_bind&lt;F, T&#8230;&#8203;&gt;</code> is a quoted metafunction that implements the type-based
equivalent of <code>boost::bind</code>. Its nested template <code>fn&lt;U&#8230;&#8203;&gt;</code> returns <code>F&lt;V&#8230;&#8203;&gt;</code>,
where <code>V&#8230;&#8203;</code> is <code>T&#8230;&#8203;</code> with the placeholders replaced by the corresponding
element of <code>U&#8230;&#8203;</code> and the <code>mp_bind</code>, <code>mp_bind_front</code>, and <code>mp_bind_back</code>
expressions replaced with their corresponding evaluations against <code>U&#8230;&#8203;</code>.</p>
</div>
<div class="paragraph">
<p>For example, <code>mp_bind&lt;F, int, _2, mp_bind&lt;G, _1&gt;&gt;::fn&lt;float, void&gt;</code> is <code>F&lt;int, void, G&lt;float&gt;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_bind_qq_t">mp_bind_q&lt;Q, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q, class... T&gt; using mp_bind_q = mp_bind&lt;Q::template fn, T...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_bind</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_bind_frontf_t">mp_bind_front&lt;F, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F, class... T&gt; struct mp_bind_front;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_bind_front&lt;F, T&#8230;&#8203;&gt;</code> binds the leftmost arguments of <code>F</code> to <code>T&#8230;&#8203;</code>. Its nested template <code>fn&lt;U&#8230;&#8203;&gt;</code> returns <code>F&lt;T&#8230;&#8203;, U&#8230;&#8203;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_bind_front_qq_t">mp_bind_front_q&lt;Q, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q, class... T&gt; using mp_bind_front_q =
    mp_bind_front&lt;Q::template fn, T...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_bind_front</code>, but takes a quoted metafunction.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_bind_backf_t">mp_bind_back&lt;F, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;template&lt;class...&gt; class F, class... T&gt; struct mp_bind_back;</pre>
</div>
</div>
<div class="paragraph">
<p><code>mp_bind_back&lt;F, T&#8230;&#8203;&gt;</code> binds the rightmost arguments of <code>F</code> to <code>T&#8230;&#8203;</code>. Its nested template <code>fn&lt;U&#8230;&#8203;&gt;</code> returns <code>F&lt;U&#8230;&#8203;, T&#8230;&#8203;&gt;</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="mp_bind_back_qq_t">mp_bind_back_q&lt;Q, T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Q, class... T&gt; using mp_bind_back_q =
    mp_bind_back&lt;Q::template fn, T...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p>As <code>mp_bind_back</code>, but takes a quoted metafunction.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="integer_sequence">Integer Sequences, &lt;boost/mp11/integer_sequence.hpp&gt;</h3>
<div class="sect3">
<h4 id="integer_sequencet_i">integer_sequence&lt;T, I&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T, T... I&gt; struct integer_sequence
{
};</pre>
</div>
</div>
<div class="paragraph">
<p><code>integer_sequence&lt;T, I&#8230;&#8203;&gt;</code> holds a sequence of integers of type <code>T</code>. Same as C++14&#8217;s <code>std::integer_sequence</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="make_integer_sequencet_n">make_integer_sequence&lt;T, N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T, T N&gt; using make_integer_sequence = /*...*/;</pre>
</div>
</div>
<div class="paragraph">
<p><code>make_integer_sequence&lt;T, N&gt;</code> is <code>integer_sequence&lt;T, 0, 1, &#8230;&#8203;, N-1&gt;</code>. Same as C++14&#8217;s <code>std::make_integer_sequence</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="index_sequencei">index_sequence&lt;I&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;std::size_t... I&gt; using index_sequence = integer_sequence&lt;std::size_t, I...&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>index_sequence&lt;I&#8230;&#8203;&gt;</code> is an alias for <code>integer_sequence&lt;size_t, I&#8230;&#8203;&gt;</code>. Same as C++14&#8217;s <code>std::index_sequence</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="make_index_sequencen">make_index_sequence&lt;N&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;std::size_t N&gt; using make_index_sequence =
    make_integer_sequence&lt;std::size_t, N&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>make_index_sequence&lt;N&gt;</code> is <code>index_sequence&lt;0, 1, &#8230;&#8203;, N-1&gt;</code>. Same as C++14&#8217;s <code>std::make_index_sequence</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="index_sequence_fort">index_sequence_for&lt;T&#8230;&#8203;&gt;</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class... T&gt; using index_sequence_for =
    make_integer_sequence&lt;std::size_t, sizeof...(T)&gt;;</pre>
</div>
</div>
<div class="paragraph">
<p><code>index_sequence_for&lt;N&gt;</code> is <code>make_index_sequence&lt;sizeof&#8230;&#8203;(T)&gt;</code>. Same as C++14&#8217;s <code>std::index_sequence_for</code>.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="tuple">Tuple Operations, &lt;boost/mp11/tuple.hpp&gt;</h3>
<div class="sect3">
<h4 id="tuple_applyf_tp">tuple_apply(f, tp)</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class F, class Tp&gt; constexpr /*...*/ tuple_apply(F&amp;&amp; f, Tp&amp;&amp; tp);</pre>
</div>
</div>
<div class="paragraph">
<p><code>tuple_apply(f, tp)</code> returns <code>std::forward&lt;F&gt;(f)(std::get&lt;J&gt;(std::forward&lt;Tp&gt;(tp))&#8230;&#8203;)</code> for <code>J</code> in 0..<code>N-1</code>,
where <code>N</code> is <code>std::tuple_size&lt;typename std::remove_reference&lt;Tp&gt;::type&gt;::value</code>. Same as <code>std::apply</code> in C++17.</p>
</div>
</div>
<div class="sect3">
<h4 id="construct_from_tuplettp">construct_from_tuple&lt;T&gt;(tp)</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class T, class Tp&gt; T construct_from_tuple(Tp&amp;&amp; tp);</pre>
</div>
</div>
<div class="paragraph">
<p><code>construct_from_tuple&lt;T&gt;(tp)</code> returns <code>T(std::get&lt;J&gt;(std::forward&lt;Tp&gt;(tp))&#8230;&#8203;)</code> for <code>J</code> in 0..<code>N-1</code>,
where <code>N</code> is <code>std::tuple_size&lt;typename std::remove_reference&lt;Tp&gt;::type&gt;::value</code>. Same as <code>std::make_from_tuple</code> in C&#43;&#43;17.
The name of the function doesn&#8217;t match the C&#43;&#43;17 one to avoid ambiguities when both are visible or in unqualified calls.</p>
</div>
</div>
<div class="sect3">
<h4 id="tuple_for_eachtp_f">tuple_for_each(tp, f)</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class Tp, class F&gt; constexpr F tuple_for_each(Tp&amp;&amp; tp, F&amp;&amp; f);</pre>
</div>
</div>
<div class="paragraph">
<p><code>tuple_for_each(tp, f)</code> applies the function object <code>f</code> to each element of <code>tp</code> in order by evaluating the
expression <code>f(std::get&lt;J&gt;(std::forward&lt;Tp&gt;(tp)))</code> for <code>J</code> in 0..<code>N-1</code>, where <code>N</code> is <code>std::tuple_size&lt;typename std::remove_reference&lt;Tp&gt;::type&gt;::value</code>.</p>
</div>
<div class="paragraph">
<p>Returns <code>std::forward&lt;F&gt;(f)</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="tuple_transformf_tp">tuple_transform(f, tp&#8230;&#8203;)</h4>
<div class="literalblock">
<div class="content">
<pre>template&lt;class F, class... Tp&gt; constexpr /*...*/ tuple_transform(F const&amp; f, Tp&amp;&amp;... tp);</pre>
</div>
</div>
<div class="paragraph">
<p><code>tuple_transform(f, tp&#8230;&#8203;)</code> accepts a function object <code>f</code> followed by one or more tuples of equal length
(<code>std::tuple</code>, <code>std::pair</code> and <code>std::array</code> are considered tuples.)</p>
</div>
<div class="paragraph">
<p>The callable <code>f</code> must accept as many arguments as there are tuples. The function object is called with the
first elements of each tuple, with the second elements of each tuple, and so on, as if by evaluating
the expression <code>f(std::get&lt;J&gt;(std::forward&lt;Tp&gt;(tp))&#8230;&#8203;)</code> for <code>J</code> in 0..<code>N-1</code>, where <code>N</code> is the length of
the tuples.</p>
</div>
<div class="paragraph">
<p>The order in which the elements of the tuples are processed is unspecified.</p>
</div>
<div class="paragraph">
<p>The results are returned as a <code>std::tuple&lt;T&#8230;&#8203;&gt;</code> with <code>T&#8230;&#8203;</code> deduced from the return values of <code>f</code> (lvalue
references are preserved, rvalue references are returned by value.)</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="mp11">Convenience Header, &lt;boost/mp11.hpp&gt;</h3>
<div class="paragraph">
<p>The convenience header <code>&lt;boost/mp11.hpp&gt;</code> includes all of the
headers listed previously in this reference.</p>
</div>
</div>
<div class="sect2">
<h3 id="mpl">MPL Support, &lt;boost/mp11/mpl.hpp&gt;</h3>
<div class="paragraph">
<p>The header <code>&lt;boost/mp11/mpl.hpp&gt;</code>, when included, defines the
necessary support infrastructure for <code>mp_list</code> and <code>std::tuple</code>
to be valid <a href="../../../../libs/mpl">MPL</a> sequences.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
<code>mpl.hpp</code> is not included by <code>&lt;boost/mp11.hpp&gt;</code>.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>It&#8217;s also possible to only enable support for <code>mp_list</code> by
including <code>&lt;boost/mp11/mpl_list.hpp&gt;</code>, and for <code>std::tuple</code>
by including <code>&lt;boost/mp11/mpl_tuple.hpp&gt;</code>. This may be required
because some libraries, such as Boost.Fusion, contain their own MPL
support for <code>std::tuple</code>, which conflicts with Mp11&#8217;s one.</p>
</div>
<div class="listingblock">
<div class="title">Code Example 77. Converting an existing MPL Sequence into an mp_list</div>
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">using</span> <span class="n">L</span> <span class="o">=</span> <span class="n">mpl</span><span class="o">::</span><span class="n">copy</span><span class="o">&lt;</span><span class="n">Sequence</span><span class="p">,</span> <span class="n">mpl</span><span class="o">::</span><span class="n">back_inserter</span><span class="o">&lt;</span><span class="n">mp11</span><span class="o">::</span><span class="n">mp_list</span><span class="o">&lt;&gt;&gt;&gt;::</span><span class="n">type</span><span class="p">;</span></code></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="copyright_license_and_acknowledgments">Appendix A: Copyright, License, and Acknowledgments</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This documentation is</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Copyright 2017-2019 Peter Dimov</p>
</li>
<li>
<p>Copyright 2017 Bjørn Reese</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>and is distributed under the <a href="http://www.boost.org/LICENSE_1_0.txt">Boost Software License, Version 1.0</a>.</p>
</div>
<div class="paragraph">
<p>The "Simple C&#43;&#43;11 metaprogramming" articles have been graciously converted to Asciidoc format for incorporation
into this documentation by Glen Fernandes.</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2023-04-10 13:47:36 UTC
</div>
</div>
<style>

*:not(pre)>code { background: none; color: #600000; }
:not(pre):not([class^=L])>code { background: none; color: #600000; }
table tr.even, table tr.alt, table tr:nth-of-type(even) { background: none; }

</style>
</body>
</html>