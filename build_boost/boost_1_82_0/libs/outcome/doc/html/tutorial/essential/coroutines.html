<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html><meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Coroutines - Boost.Outcome documentation</title>
<link rel="stylesheet" href="../../css/boost.css" type="text/css">
<meta name="generator" content="Hugo 0.52 with Boostdoc theme">
<meta name="viewport" content="width=device-width,initial-scale=1.0"/>

<link rel="icon" href="../../images/favicon.ico" type="image/ico"/>
<body><div class="spirit-nav">
<a accesskey="p" href="../../tutorial/essential/no-value/builtin.html"><img src="../../images/prev.png" alt="Prev"></a>
    <a accesskey="u" href="../../tutorial/essential.html"><img src="../../images/up.png" alt="Up"></a>
    <a accesskey="h" href="../../index.html"><img src="../../images/home.png" alt="Home"></a><a accesskey="n" href="../../tutorial/essential/coroutines/try.html"><img src="../../images/next.png" alt="Next"></a></div><div id="content">

  <div class="titlepage"><div><div><h1 style="clear: both">Coroutines</h1></div></div></div>
<p><a href="../../changelog.html">In v2.1.2</a> Outcome published official support for using
Outcome within C++ coroutines. This page documents that support.</p>

<p>All standard C++ Coroutines have the following form:</p>
<div class="highlight"><pre class="chroma"><code class="language-c++" data-lang="c++"><span class="c1">// Coroutine functions MUST return an AWAITABLE type
</span><span class="c1"></span><span class="n">AWAITABLE</span><span class="o">&lt;</span><span class="kt">int</span><span class="o">&gt;</span> <span class="n">function_name</span><span class="p">(</span><span class="n">Args</span> <span class="p">...)</span>
<span class="p">{</span>
  <span class="p">...</span> <span class="n">ordinary</span> <span class="n">C</span><span class="o">++</span> <span class="p">...</span>
  <span class="k">if</span><span class="p">(</span><span class="o">!</span><span class="p">...)</span>
  <span class="p">{</span>
    <span class="n">co_return</span> <span class="mi">5</span><span class="p">;</span>  <span class="c1">// CANNOT use ordinary &#39;return&#39; from coroutines
</span><span class="c1"></span>  <span class="p">}</span>
  <span class="p">...</span>
  <span class="c1">// Possibly suspend this coroutine&#39;s execution until the
</span><span class="c1"></span>  <span class="c1">// awaitable resumes execution of dependent code
</span><span class="c1"></span>  <span class="k">auto</span> <span class="n">x</span> <span class="o">=</span> <span class="n">co_await</span> <span class="n">expr_resulting_in_AWAITABLE</span><span class="p">;</span>
  <span class="p">...</span>
<span class="p">}</span>
</code></pre></div>
<p>The type <code>AWAITABLE&lt;T&gt;</code> is any type which publishes the Coroutine protocol telling
C++ how to suspend and resume execution of a coroutine which returns a <code>T</code>. It is out of scope of
this page to document how to do this, however note that the <code>eager&lt;T, Executor = void&gt;</code> and <code>lazy&lt;T, Executor = void&gt;</code>
types below are completely generic awaitables suitable for use in ANY code.
They <strong>only</strong> behave differently if <code>T</code>, the type being returned by the awaitable,
is an Outcome type e.g. <code>outcome::basic_result</code> or <code>outcome::basic_outcome</code>.</p>



        </div><p><small>Last revised: March 18, 2022 at 14:45:32 UTC</small></p>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../tutorial/essential/no-value/builtin.html"><img src="../../images/prev.png" alt="Prev"></a>
    <a accesskey="u" href="../../tutorial/essential.html"><img src="../../images/up.png" alt="Up"></a>
    <a accesskey="h" href="../../index.html"><img src="../../images/home.png" alt="Home"></a><a accesskey="n" href="../../tutorial/essential/coroutines/try.html"><img src="../../images/next.png" alt="Next"></a></div></body>
</html>
