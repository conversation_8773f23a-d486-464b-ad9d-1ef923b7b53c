<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html><meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Using Outcome from C code - Boost.Outcome documentation</title>
<link rel="stylesheet" href="../css/boost.css" type="text/css">
<meta name="generator" content="Hugo 0.52 with Boostdoc theme">
<meta name="viewport" content="width=device-width,initial-scale=1.0"/>

<link rel="icon" href="../images/favicon.ico" type="image/ico"/>
<body><div class="spirit-nav">
<a accesskey="p" href="../experimental/outcome.html"><img src="../images/prev.png" alt="Prev"></a>
    <a accesskey="u" href="../experimental.html"><img src="../images/up.png" alt="Up"></a>
    <a accesskey="h" href="../index.html"><img src="../images/home.png" alt="Home"></a><a accesskey="n" href="../experimental/c-api/limitations.html"><img src="../images/next.png" alt="Next"></a></div><div id="content">

  <div class="titlepage"><div><div><h1 style="clear: both">Using Outcome from C code</h1></div></div></div>
<p>A long standing problem for C code (or more usually nowadays, the many other programming
languages which can speak the C ABI but not the C++ ABI) is how to interpret C++ exception throws. The answer
is of course that they cannot, thus requiring one to write C shim code on the C++ side
of things of the form:</p>
<div class="highlight"><pre class="chroma"><code class="language-c++" data-lang="c++"><span class="c1">// The API we wish to expose to C
</span><span class="c1"></span><span class="k">const</span> <span class="kt">char</span> <span class="o">*</span><span class="nf">get_value</span><span class="p">(</span><span class="kt">double</span> <span class="n">v</span><span class="p">);</span>

<span class="c1">// The C shim function for the C++ get_value() function.
</span><span class="c1"></span><span class="k">extern</span> <span class="s">&#34;C&#34;</span> <span class="kt">int</span> <span class="n">c_get_value</span><span class="p">(</span><span class="k">const</span> <span class="kt">char</span> <span class="o">**</span><span class="n">ret</span><span class="p">,</span> <span class="kt">double</span> <span class="n">v</span><span class="p">)</span>
<span class="p">{</span>
  <span class="k">try</span>
  <span class="p">{</span>
    <span class="o">*</span><span class="n">ret</span> <span class="o">=</span> <span class="n">get_value</span><span class="p">(</span><span class="n">v</span><span class="p">);</span>
    <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>  <span class="c1">// success
</span><span class="c1"></span>  <span class="p">}</span>
  <span class="k">catch</span><span class="p">(</span><span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">range_error</span> <span class="o">&amp;</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="k">return</span> <span class="n">ERANGE</span><span class="p">;</span>
  <span class="p">}</span>
  <span class="c1">// More catch clauses may go in here ...
</span><span class="c1"></span>  <span class="k">catch</span><span class="p">(...)</span>
  <span class="p">{</span>
    <span class="k">return</span> <span class="n">EAGAIN</span><span class="p">;</span>
  <span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<p>This is sufficiently painful that most reach for a bindings generator tool like
<a href="http://www.swig.org/">SWIG</a> to automate this sort of tedious boilerplate generation.
And this is fine for larger projects, but for smaller projects the cost of
setting up and configuring SWIG is also non-trivial.</p>

<p>What would be really great is if <code>result&lt;T&gt;</code> returning <code>noexcept</code> C++ functions
could be used straight from C. And indeed Experimental Outcome provides just that facility
which this section covers next.</p>



        </div><p><small>Last revised: February 05, 2019 at 17:14:18 UTC</small></p>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../experimental/outcome.html"><img src="../images/prev.png" alt="Prev"></a>
    <a accesskey="u" href="../experimental.html"><img src="../images/up.png" alt="Up"></a>
    <a accesskey="h" href="../index.html"><img src="../images/home.png" alt="Home"></a><a accesskey="n" href="../experimental/c-api/limitations.html"><img src="../images/next.png" alt="Next"></a></div></body>
</html>
