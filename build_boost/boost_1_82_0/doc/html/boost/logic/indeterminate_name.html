<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template indeterminate_name</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../tribool/reference.html#header.boost.logic.tribool_io_hpp" title="Header &lt;boost/logic/tribool_io.hpp&gt;">
<link rel="prev" href="../../BOOST_TRIBOOL_THIRD_STATE.html" title="Macro BOOST_TRIBOOL_THIRD_STATE">
<link rel="next" href="get_default_indeter_idm623.html" title="Function template get_default_indeterminate_name">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../BOOST_TRIBOOL_THIRD_STATE.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../tribool/reference.html#header.boost.logic.tribool_io_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="get_default_indeter_idm623.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.logic.indeterminate_name"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template indeterminate_name</span></h2>
<p>boost::logic::indeterminate_name — A locale facet specifying the name of the indeterminate value of a tribool. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../tribool/reference.html#header.boost.logic.tribool_io_hpp" title="Header &lt;boost/logic/tribool_io.hpp&gt;">boost/logic/tribool_io.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="indeterminate_name.html" title="Class template indeterminate_name">indeterminate_name</a> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">facet</span>, <span class="keyword">private</span> <span class="identifier">noncopyable</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">CharT</span>                      <a name="boost.logic.indeterminate_name.char_type"></a><span class="identifier">char_type</span><span class="special">;</span>  
  <span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span> <a name="boost.logic.indeterminate_name.string_type"></a><span class="identifier">string_type</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="indeterminate_name.html#boost.logic.indeterminate_nameconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="indeterminate_name.html#idm617-bb"><span class="identifier">indeterminate_name</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="indeterminate_name.html#idm619-bb"><span class="identifier">indeterminate_name</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">string_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="indeterminate_name.html#idm613-bb">public member functions</a></span>
  <span class="identifier">string_type</span> <a class="link" href="indeterminate_name.html#idm614-bb"><span class="identifier">name</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

  <span class="comment">// public data members</span>
  <span class="keyword">static</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span><span class="special">::</span><span class="identifier">id</span> <span class="identifier">id</span><span class="special">;</span>  <span class="comment">// Uniquily identifies this facet with the locale. </span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.4.3.4"></a><h2>Description</h2>
<p>The facet is used to perform I/O on tribool values when <code class="computeroutput">std::boolalpha</code> has been specified. This class template is only available if the C++ standard library implementation supports locales. </p>
<div class="refsect2">
<a name="id-********.*******"></a><h3>
<a name="boost.logic.indeterminate_nameconstruct-copy-destruct"></a><code class="computeroutput">indeterminate_name</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm617-bb"></a><span class="identifier">indeterminate_name</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Construct the facet with the default name. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm619-bb"></a><span class="identifier">indeterminate_name</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">string_type</span> <span class="special">&amp;</span> initial_name<span class="special">)</span><span class="special">;</span></pre>Construct the facet with the given name for the indeterminate value. </li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.4.3.4.4"></a><h3>
<a name="idm613-bb"></a><code class="computeroutput">indeterminate_name</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">
<pre class="literallayout"><span class="identifier">string_type</span> <a name="idm614-bb"></a><span class="identifier">name</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Returns the name for the indeterminate value. </li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2002-2004 Douglas Gregor<p>Use, modification and distribution is subject to the Boost
    Software License, Version 1.0. (See accompanying file
    <code class="filename">LICENSE_1_0.txt</code> or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)</p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../BOOST_TRIBOOL_THIRD_STATE.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../tribool/reference.html#header.boost.logic.tribool_io_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="get_default_indeter_idm623.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
