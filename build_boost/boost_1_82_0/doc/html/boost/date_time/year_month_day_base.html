<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct template year_month_day_base</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../date_time/doxy.html#header.boost.date_time.year_month_day_hpp" title="Header &lt;boost/date_time/year_month_day.hpp&gt;">
<link rel="prev" href="wrapping_int2.html" title="Class template wrapping_int2">
<link rel="next" href="../gregorian/gregorian_calendar.html" title="Class gregorian_calendar">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="wrapping_int2.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../date_time/doxy.html#header.boost.date_time.year_month_day_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../gregorian/gregorian_calendar.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.date_time.year_month_day_base"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct template year_month_day_base</span></h2>
<p>boost::date_time::year_month_day_base — Allow rapid creation of ymd triples of different types. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../date_time/doxy.html#header.boost.date_time.year_month_day_hpp" title="Header &lt;boost/date_time/year_month_day.hpp&gt;">boost/date_time/year_month_day.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> YearType<span class="special">,</span> <span class="keyword">typename</span> MonthType<span class="special">,</span> <span class="keyword">typename</span> DayType<span class="special">&gt;</span> 
<span class="keyword">struct</span> <a class="link" href="year_month_day_base.html" title="Struct template year_month_day_base">year_month_day_base</a> <span class="special">{</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">YearType</span>  <a name="boost.date_time.year_month_day_base.year_type"></a><span class="identifier">year_type</span><span class="special">;</span> 
  <span class="keyword">typedef</span> <span class="identifier">MonthType</span> <a name="boost.date_time.year_month_day_base.month_type"></a><span class="identifier">month_type</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="identifier">DayType</span>   <a name="boost.date_time.year_month_day_base.day_type"></a><span class="identifier">day_type</span><span class="special">;</span>  

  <span class="comment">// <a class="link" href="year_month_day_base.html#boost.date_time.year_month_day_baseconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="year_month_day_base.html#id-1_3_12_15_3_60_1_1_1_10-bb"><span class="identifier">year_month_day_base</span></a><span class="special">(</span><span class="identifier">YearType</span><span class="special">,</span> <span class="identifier">MonthType</span><span class="special">,</span> <span class="identifier">DayType</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// public data members</span>
  <span class="identifier">YearType</span> <span class="identifier">year</span><span class="special">;</span>
  <span class="identifier">MonthType</span> <span class="identifier">month</span><span class="special">;</span>
  <span class="identifier">DayType</span> <span class="identifier">day</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*********.********"></a><h2>Description</h2>
<div class="refsect2">
<a name="id-*********.********.2"></a><h3>
<a name="boost.date_time.year_month_day_baseconstruct-copy-destruct"></a><code class="computeroutput">year_month_day_base</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">
<pre class="literallayout"><a name="id-1_3_12_15_3_60_1_1_1_10-bb"></a><span class="identifier">year_month_day_base</span><span class="special">(</span><span class="identifier">YearType</span> year<span class="special">,</span> <span class="identifier">MonthType</span> month<span class="special">,</span> <span class="identifier">DayType</span> day<span class="special">)</span><span class="special">;</span></pre>A basic constructor. </li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2001-2005 CrystalClear Software, Inc<p>Subject to the Boost Software License, Version 1.0. (See accompanying file
    <code class="filename">LICENSE_1_0.txt</code> or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)</p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="wrapping_int2.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../date_time/doxy.html#header.boost.date_time.year_month_day_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../gregorian/gregorian_calendar.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
