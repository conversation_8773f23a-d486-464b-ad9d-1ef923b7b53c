<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct template bitorable</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../boost_typeerasure/reference.html#header.boost.type_erasure.operators_hpp" title="Header &lt;boost/type_erasure/operators.hpp&gt;">
<link rel="prev" href="bitor_assignable.html" title="Struct template bitor_assignable">
<link rel="next" href="bitxor_assignable.html" title="Struct template bitxor_assignable">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="bitor_assignable.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_typeerasure/reference.html#header.boost.type_erasure.operators_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="bitxor_assignable.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.type_erasure.bitorable"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct template bitorable</span></h2>
<p>boost::type_erasure::bitorable</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_typeerasure/reference.html#header.boost.type_erasure.operators_hpp" title="Header &lt;boost/type_erasure/operators.hpp&gt;">boost/type_erasure/operators.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T <span class="special">=</span> <a class="link" href="_self.html" title="Struct _self">_self</a><span class="special">,</span> <span class="keyword">typename</span> U <span class="special">=</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> R <span class="special">=</span> <span class="identifier">T</span><span class="special">&gt;</span> 
<span class="keyword">struct</span> <a class="link" href="bitorable.html" title="Struct template bitorable">bitorable</a> <span class="special">{</span>

  <span class="comment">// <a class="link" href="bitorable.html#id-1_3_40_13_24_1_1_6_2-bb">public static functions</a></span>
  <span class="keyword">static</span> <span class="identifier">R</span> <a class="link" href="bitorable.html#id-1_3_40_13_24_1_1_6_2_1-bb"><span class="identifier">apply</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">U</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*********.25.8.4"></a><h2>Description</h2>
<div class="refsect2">
<a name="id-*********.********"></a><h3>
<a name="id-1_3_40_13_24_1_1_6_2-bb"></a><code class="computeroutput">bitorable</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><pre class="literallayout"><span class="keyword">static</span> <span class="identifier">R</span> <a name="id-1_3_40_13_24_1_1_6_2_1-bb"></a><span class="identifier">apply</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">U</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span></pre></li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2011-2013 Steven Watanabe<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="bitor_assignable.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_typeerasure/reference.html#header.boost.type_erasure.operators_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="bitxor_assignable.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
