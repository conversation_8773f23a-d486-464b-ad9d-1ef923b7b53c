<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template subtract_with_carry_01_engine</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../boost_random/reference.html#header.boost.random.subtract_with_carry_hpp" title="Header &lt;boost/random/subtract_with_carry.hpp&gt;">
<link rel="prev" href="student_t_distribution/param_type.html" title="Class param_type">
<link rel="next" href="subtract_with_carry_engine.html" title="Class template subtract_with_carry_engine">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="student_t_distribution/param_type.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_random/reference.html#header.boost.random.subtract_with_carry_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="subtract_with_carry_engine.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.random.subtra_1_3_33_5_6_42_1_1_1"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template subtract_with_carry_01_engine</span></h2>
<p>boost::random::subtract_with_carry_01_engine</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_random/reference.html#header.boost.random.subtract_with_carry_hpp" title="Header &lt;boost/random/subtract_with_carry.hpp&gt;">boost/random/subtract_with_carry.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> RealType<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> w<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> s<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> r<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">RealType</span> <a name="boost.random.subtra_1_3_33_5_6_42_1_1_1.result_type"></a><span class="identifier">result_type</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#boost.random.subtra_1_3_33_5_6_42_1_1_1construct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_10-bb"><span class="identifier">subtract_with_carry_01_engine</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_11-bb"><span class="identifier">subtract_with_carry_01_engine</span></a><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">uint32_t</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> SeedSeq<span class="special">&gt;</span> <span class="keyword">explicit</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_12-bb"><span class="identifier">subtract_with_carry_01_engine</span></a><span class="special">(</span><span class="identifier">SeedSeq</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> It<span class="special">&gt;</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_13-bb"><span class="identifier">subtract_with_carry_01_engine</span></a><span class="special">(</span><span class="identifier">It</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">It</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_9-bb">public member functions</a></span>
  <span class="keyword">void</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_9_1-bb"><span class="identifier">seed</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_9_2-bb"><span class="identifier">seed</span></a><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">uint32_t</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> SeedSeq<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_9_3-bb"><span class="identifier">seed</span></a><span class="special">(</span><span class="identifier">SeedSeq</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> It<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_9_4-bb"><span class="identifier">seed</span></a><span class="special">(</span><span class="identifier">It</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">It</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">result_type</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_9_5-bb"><span class="keyword">operator</span><span class="special">(</span><span class="special">)</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_9_6-bb"><span class="identifier">discard</span></a><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">uintmax_t</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iter<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_9_7-bb"><span class="identifier">generate</span></a><span class="special">(</span><span class="identifier">Iter</span><span class="special">,</span> <span class="identifier">Iter</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_14-bb">public static functions</a></span>
  <span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="identifier">result_type</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_14_1-bb"><span class="identifier">min</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="identifier">result_type</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_14_2-bb"><span class="identifier">max</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_15-bb">friend functions</a></span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
    <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
    <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_15_1-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
               <span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
    <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
    <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_15_2-bb"><span class="keyword">operator</span><span class="special">&gt;&gt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
               <span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_15_3-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span><span class="special">,</span> 
                         <span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html#id-1_3_33_5_6_42_1_1_1_15_4-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span><span class="special">,</span> 
                         <span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// public data members</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">bool</span> <span class="identifier">has_fixed_range</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">word_size</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">long_lag</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">short_lag</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">uint32_t</span> <span class="identifier">default_seed</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.********"></a><h2>Description</h2>
<p>Instantiations of <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> model a  <a class="link" href="../../boost_random/reference.html#boost_random.reference.concepts.pseudo_random_number_generator" title="Pseudo-Random Number Generator">pseudo-random number generator</a> . The algorithm is described in</p>
<p> </p>
<div class="blockquote"><blockquote class="blockquote"><p>  "A New Class of Random Number Generators", George Marsaglia and Arif Zaman, Annals of Applied Probability, Volume 1, Number 3 (1991), 462-480.  </p></blockquote></div>
<p>  </p>
<div class="refsect2">
<a name="id-********.********.4"></a><h3>
<a name="boost.random.subtra_1_3_33_5_6_42_1_1_1construct-copy-destruct"></a><code class="computeroutput">subtract_with_carry_01_engine</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="id-1_3_33_5_6_42_1_1_1_10-bb"></a><span class="identifier">subtract_with_carry_01_engine</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Creates a new <code class="computeroutput"><a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a></code> using the default seed. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="id-1_3_33_5_6_42_1_1_1_11-bb"></a><span class="identifier">subtract_with_carry_01_engine</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">uint32_t</span> value<span class="special">)</span><span class="special">;</span></pre>
<p>Creates a new <code class="computeroutput"><a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a></code> and seeds it with value. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> SeedSeq<span class="special">&gt;</span> 
  <span class="keyword">explicit</span> <a name="id-1_3_33_5_6_42_1_1_1_12-bb"></a><span class="identifier">subtract_with_carry_01_engine</span><span class="special">(</span><span class="identifier">SeedSeq</span> <span class="special">&amp;</span> seq<span class="special">)</span><span class="special">;</span></pre>
<p>Creates a new <code class="computeroutput"><a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a></code> and seeds with values produced by seq.generate(). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> It<span class="special">&gt;</span> <a name="id-1_3_33_5_6_42_1_1_1_13-bb"></a><span class="identifier">subtract_with_carry_01_engine</span><span class="special">(</span><span class="identifier">It</span> <span class="special">&amp;</span> first<span class="special">,</span> <span class="identifier">It</span> last<span class="special">)</span><span class="special">;</span></pre>
<p>Creates a new <code class="computeroutput"><a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a></code> and seeds it with values from a range. Advances first to point one past the last consumed value. If the range does not contain enough elements to fill the entire state, throws <code class="computeroutput">std::invalid_argument</code>. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.********.5"></a><h3>
<a name="id-1_3_33_5_6_42_1_1_1_9-bb"></a><code class="computeroutput">subtract_with_carry_01_engine</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="id-1_3_33_5_6_42_1_1_1_9_1-bb"></a><span class="identifier">seed</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Seeds the generator with the default seed. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="id-1_3_33_5_6_42_1_1_1_9_2-bb"></a><span class="identifier">seed</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">uint32_t</span> value<span class="special">)</span><span class="special">;</span></pre>
<p>Seeds the generator with <code class="computeroutput">value</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> SeedSeq<span class="special">&gt;</span> <span class="keyword">void</span> <a name="id-1_3_33_5_6_42_1_1_1_9_3-bb"></a><span class="identifier">seed</span><span class="special">(</span><span class="identifier">SeedSeq</span> <span class="special">&amp;</span> seq<span class="special">)</span><span class="special">;</span></pre>
<p>Seeds the generator with values produced by <code class="computeroutput">seq.generate()</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> It<span class="special">&gt;</span> <span class="keyword">void</span> <a name="id-1_3_33_5_6_42_1_1_1_9_4-bb"></a><span class="identifier">seed</span><span class="special">(</span><span class="identifier">It</span> <span class="special">&amp;</span> first<span class="special">,</span> <span class="identifier">It</span> last<span class="special">)</span><span class="special">;</span></pre>
<p>Seeds the generator with values from a range. Updates first to point one past the last consumed element. If there are not enough elements in the range to fill the entire state, throws <code class="computeroutput">std::invalid_argument</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">result_type</span> <a name="id-1_3_33_5_6_42_1_1_1_9_5-bb"></a><span class="keyword">operator</span><span class="special">(</span><span class="special">)</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Returns the next value of the generator. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="id-1_3_33_5_6_42_1_1_1_9_6-bb"></a><span class="identifier">discard</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">uintmax_t</span> z<span class="special">)</span><span class="special">;</span></pre>
<p>Advances the state of the generator by <code class="computeroutput">z</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iter<span class="special">&gt;</span> <span class="keyword">void</span> <a name="id-1_3_33_5_6_42_1_1_1_9_7-bb"></a><span class="identifier">generate</span><span class="special">(</span><span class="identifier">Iter</span> first<span class="special">,</span> <span class="identifier">Iter</span> last<span class="special">)</span><span class="special">;</span></pre>
<p>Fills a range with random values. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.********.6"></a><h3>
<a name="id-1_3_33_5_6_42_1_1_1_14-bb"></a><code class="computeroutput">subtract_with_carry_01_engine</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="identifier">result_type</span> <a name="id-1_3_33_5_6_42_1_1_1_14_1-bb"></a><span class="identifier">min</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Returns the smallest value that the generator can produce. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="identifier">result_type</span> <a name="id-1_3_33_5_6_42_1_1_1_14_2-bb"></a><span class="identifier">max</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Returns the largest value that the generator can produce. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.********.7"></a><h3>
<a name="id-1_3_33_5_6_42_1_1_1_15-bb"></a><code class="computeroutput">subtract_with_carry_01_engine</code> friend functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
  <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
  <a name="id-1_3_33_5_6_42_1_1_1_15_1-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
             <span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span> f<span class="special">)</span><span class="special">;</span></pre>
<p>Writes a <code class="computeroutput"><a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a></code> to a <code class="computeroutput">std::ostream</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
  <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
  <a name="id-1_3_33_5_6_42_1_1_1_15_2-bb"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
             <span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span> f<span class="special">)</span><span class="special">;</span></pre>
<p>Reads a <code class="computeroutput"><a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a></code> from a <code class="computeroutput">std::istream</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="id-1_3_33_5_6_42_1_1_1_15_3-bb"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span> x<span class="special">,</span> 
                       <span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p>Returns true if the two generators will produce identical sequences. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="id-1_3_33_5_6_42_1_1_1_15_4-bb"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span> lhs<span class="special">,</span> 
                       <span class="keyword">const</span> <a class="link" href="subtra_1_3_33_5_6_42_1_1_1.html" title="Class template subtract_with_carry_01_engine">subtract_with_carry_01_engine</a> <span class="special">&amp;</span> rhs<span class="special">)</span><span class="special">;</span></pre>
<p>Returns true if the two generators will produce different sequences. </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2000-2005 Jens Maurer<br>Copyright © 2009, 2010 Steven Watanabe<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="student_t_distribution/param_type.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_random/reference.html#header.boost.random.subtract_with_carry_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="subtract_with_carry_engine.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
