<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class param_type</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../negati_1_3_33_5_6_28_1_1_1.html#id-********.********" title="Description">
<link rel="prev" href="../negati_1_3_33_5_6_28_1_1_1.html" title="Class template negative_binomial_distribution">
<link rel="next" href="../niederreiter_base2_engine.html" title="Class template niederreiter_base2_engine">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../negati_1_3_33_5_6_28_1_1_1.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../negati_1_3_33_5_6_28_1_1_1.html#id-********.********"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../niederreiter_base2_engine.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.random.negati_1_3_33_5_6_28_1_1_1.param_type"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class param_type</span></h2>
<p>boost::random::negative_binomial_distribution::param_type</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../boost_random/reference.html#header.boost.random.negative_binomial_distribution_hpp" title="Header &lt;boost/random/negative_binomial_distribution.hpp&gt;">boost/random/negative_binomial_distribution.hpp</a>&gt;

</span>

<span class="keyword">class</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <a class="link" href="../negati_1_3_33_5_6_28_1_1_1.html" title="Class template negative_binomial_distribution">negative_binomial_distribution</a> <a name="boost.random.negati_1_3_33_5_6_28_1_1_1.param_type.distribution_type"></a><span class="identifier">distribution_type</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="param_type.html#boost.random.negati_1_3_33_5_6_28_1_1_1.param_typeconstruct-copy-destruct">construct/copy/destruct</a></span>
  <span class="keyword">explicit</span> <a class="link" href="param_type.html#id-1_3_33_5_6_28_1_1_1_3_3-bb"><span class="identifier">param_type</span></a><span class="special">(</span><span class="identifier">IntType</span> <span class="special">=</span> <span class="number">1</span><span class="special">,</span> <span class="identifier">RealType</span> <span class="special">=</span> <span class="number">0</span><span class="special">.</span><span class="number">5</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="param_type.html#id-1_3_33_5_6_28_1_1_1_3_2-bb">public member functions</a></span>
  <span class="identifier">IntType</span> <a class="link" href="param_type.html#id-1_3_33_5_6_28_1_1_1_3_2_1-bb"><span class="identifier">k</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">RealType</span> <a class="link" href="param_type.html#id-1_3_33_5_6_28_1_1_1_3_2_2-bb"><span class="identifier">p</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="param_type.html#id-1_3_33_5_6_28_1_1_1_3_4-bb">friend functions</a></span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
    <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
    <a class="link" href="param_type.html#id-1_3_33_5_6_28_1_1_1_3_4_1-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
    <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
    <a class="link" href="param_type.html#id-1_3_33_5_6_28_1_1_1_3_4_2-bb"><span class="keyword">operator</span><span class="special">&gt;&gt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="param_type.html#id-1_3_33_5_6_28_1_1_1_3_4_3-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="param_type.html#id-1_3_33_5_6_28_1_1_1_3_4_4-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.********.8.4"></a><h2>Description</h2>
<div class="refsect2">
<a name="id-********.********.8.4.2"></a><h3>
<a name="boost.random.negati_1_3_33_5_6_28_1_1_1.param_typeconstruct-copy-destruct"></a><code class="computeroutput">param_type</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="id-1_3_33_5_6_28_1_1_1_3_3-bb"></a><span class="identifier">param_type</span><span class="special">(</span><span class="identifier">IntType</span> k <span class="special">=</span> <span class="number">1</span><span class="special">,</span> <span class="identifier">RealType</span> p <span class="special">=</span> <span class="number">0</span><span class="special">.</span><span class="number">5</span><span class="special">)</span><span class="special">;</span></pre>
<p>Construct a <code class="computeroutput"><a class="link" href="param_type.html" title="Class param_type">param_type</a></code> object. <code class="computeroutput">k</code> and <code class="computeroutput">p</code> are the parameters of the distribution.</p>
<p>Requires: k &gt;=0 &amp;&amp; 0 &lt;= p &lt;= 1 </p>
</li></ol></div>
</div>
<div class="refsect2">
<a name="id-********.********.8.4.3"></a><h3>
<a name="id-1_3_33_5_6_28_1_1_1_3_2-bb"></a><code class="computeroutput">param_type</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="identifier">IntType</span> <a name="id-1_3_33_5_6_28_1_1_1_3_2_1-bb"></a><span class="identifier">k</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Returns the <code class="computeroutput">k</code> parameter of the distribution. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">RealType</span> <a name="id-1_3_33_5_6_28_1_1_1_3_2_2-bb"></a><span class="identifier">p</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Returns the <code class="computeroutput">p</code> parameter of the distribution. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.********.8.4.4"></a><h3>
<a name="id-1_3_33_5_6_28_1_1_1_3_4-bb"></a><code class="computeroutput">param_type</code> friend functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
  <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
  <a name="id-1_3_33_5_6_28_1_1_1_3_4_1-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
             <span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span> param<span class="special">)</span><span class="special">;</span></pre>
<p>Writes the parameters of the distribution to a <code class="computeroutput">std::ostream</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
  <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
  <a name="id-1_3_33_5_6_28_1_1_1_3_4_2-bb"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span> param<span class="special">)</span><span class="special">;</span></pre>
<p>Reads the parameters of the distribution from a <code class="computeroutput">std::istream</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="id-1_3_33_5_6_28_1_1_1_3_4_3-bb"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span> lhs<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span> rhs<span class="special">)</span><span class="special">;</span></pre>
<p>Returns true if the parameters have the same values. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="id-1_3_33_5_6_28_1_1_1_3_4_4-bb"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span> lhs<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="param_type.html" title="Class param_type">param_type</a> <span class="special">&amp;</span> rhs<span class="special">)</span><span class="special">;</span></pre>
<p>Returns true if the parameters have different values. </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2000-2005 Jens Maurer<br>Copyright © 2009, 2010 Steven Watanabe<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../negati_1_3_33_5_6_28_1_1_1.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../negati_1_3_33_5_6_28_1_1_1.html#id-********.********"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../niederreiter_base2_engine.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
