<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Type definition tz_database</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../date_time/doxy.html#header.boost.date_time.local_time.tz_database_hpp" title="Header &lt;boost/date_time/local_time/tz_database.hpp&gt;">
<link rel="prev" href="posix_time_zone_base.html" title="Class template posix_time_zone_base">
<link rel="next" href="../../boost_dll.html" title="Chapter 12. Boost.DLL">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="posix_time_zone_base.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../date_time/doxy.html#header.boost.date_time.local_time.tz_database_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../boost_dll.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.local_time.tz_database"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Type definition tz_database</span></h2>
<p>tz_database — Object populated with boost::shared_ptr&lt;time_zone_base&gt; objects. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../date_time/doxy.html#header.boost.date_time.local_time.tz_database_hpp" title="Header &lt;boost/date_time/local_time/tz_database.hpp&gt;">boost/date_time/local_time/tz_database.hpp</a>&gt;

</span>
<span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">tz_db_base</span><span class="special">&lt;</span> <a class="link" href="../../date_time/doxy.html#boost.local_time.custom_time_zone">custom_time_zone</a><span class="special">,</span> <span class="identifier">nth_kday_dst_rule</span> <span class="special">&gt;</span> <span class="identifier">tz_database</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*********.********"></a><h2>Description</h2>
<p>Object populated with boost::shared_ptr&lt;time_zone_base&gt; objects Database is populated from specs stored in external csv file. See date_time::tz_db_base for greater detail </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2001-2005 CrystalClear Software, Inc<p>Subject to the Boost Software License, Version 1.0. (See accompanying file
    <code class="filename">LICENSE_1_0.txt</code> or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)</p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="posix_time_zone_base.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../date_time/doxy.html#header.boost.date_time.local_time.tz_database_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../../boost_dll.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
