<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function template is_from_range</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../string_algo/reference.html#header.boost.algorithm.string.classification_hpp" title="Header &lt;boost/algorithm/string/classification.hpp&gt;">
<link rel="prev" href="is_any_of.html" title="Function template is_any_of">
<link rel="next" href="operator__.html" title="Function template operator&amp;&amp;">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_any_of.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../string_algo/reference.html#header.boost.algorithm.string.classification_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="operator__.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.algorithm.is_from_range"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function template is_from_range</span></h2>
<p>boost::algorithm::is_from_range — is_from_range predicate </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../string_algo/reference.html#header.boost.algorithm.string.classification_hpp" title="Header &lt;boost/algorithm/string/classification.hpp&gt;">boost/algorithm/string/classification.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">&gt;</span> <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <span class="identifier">is_from_range</span><span class="special">(</span><span class="identifier">CharT</span> From<span class="special">,</span> <span class="identifier">CharT</span> To<span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*******.4.17.4"></a><h2>Description</h2>
<p>Construct the <code class="computeroutput">is_from_range</code> predicate. The predicate holds if the input is included in the specified range. (i.e. From &lt;= Ch &lt;= To )</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">From</code></span></p></td>
<td><p>The start of the range </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">To</code></span></p></td>
<td><p>The end of the range </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>An instance of the <code class="computeroutput">is_from_range</code> predicate </p></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2002-2004 Pavol Droba<p>Use, modification and distribution is subject to the Boost
                Software License, Version 1.0. (See accompanying file
                <code class="filename">LICENSE_1_0.txt</code> or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
            </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_any_of.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../string_algo/reference.html#header.boost.algorithm.string.classification_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="operator__.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
