<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template circular_buffer</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer.base_hpp" title="Header &lt;boost/circular_buffer/base.hpp&gt;">
<link rel="prev" href="../BOOST_CB_ENABLE_DEBUG.html" title="Macro BOOST_CB_ENABLE_DEBUG">
<link rel="next" href="operator___idm4981.html" title="Function template operator==">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../BOOST_CB_ENABLE_DEBUG.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer.base_hpp"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="operator___idm4981.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.circular_buffer"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template circular_buffer</span></h2>
<p>boost::circular_buffer — Circular buffer - a STL compliant container. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer.base_hpp" title="Header &lt;boost/circular_buffer/base.hpp&gt;">boost/circular_buffer/base.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Alloc<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a> <span class="special">:</span> <span class="keyword">private</span> <span class="identifier">empty_value</span><span class="special">&lt;</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// <a class="link" href="circular_buffer.html#boost.circular_buffertypes">types</a></span>
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span>                                                               <a name="boost.circular_buffer.this_type"></a><span class="identifier">this_type</span><span class="special">;</span>               <span class="comment">// The type of this <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </span>
  <span class="keyword">typedef</span> <span class="identifier">Alloc</span><span class="special">::</span><span class="identifier">value_type</span>                                                                         <a name="boost.circular_buffer.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>              <span class="comment">// The type of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </span>
  <span class="keyword">typedef</span> <span class="identifier">allocator_pointer</span><span class="special">&lt;</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span>                                                          <a name="boost.circular_buffer.pointer"></a><span class="identifier">pointer</span><span class="special">;</span>                 <span class="comment">// A pointer to an element. </span>
  <span class="keyword">typedef</span> <span class="identifier">allocator_const_pointer</span><span class="special">&lt;</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span>                                                    <a name="boost.circular_buffer.const_pointer"></a><span class="identifier">const_pointer</span><span class="special">;</span>           <span class="comment">// A const pointer to the element. </span>
  <span class="keyword">typedef</span> <span class="identifier">value_type</span> <span class="special">&amp;</span>                                                                              <a name="boost.circular_buffer.reference"></a><span class="identifier">reference</span><span class="special">;</span>               <span class="comment">// A reference to an element. </span>
  <span class="keyword">typedef</span> <span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span>                                                                        <a name="boost.circular_buffer.const_reference"></a><span class="identifier">const_reference</span><span class="special">;</span>         <span class="comment">// A const reference to an element. </span>
  <span class="keyword">typedef</span> <span class="identifier">allocator_difference_type</span><span class="special">&lt;</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span>                                                  <a class="link" href="circular_buffer.html#boost.circular_buffer.difference_type"><span class="identifier">difference_type</span></a><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">allocator_size_type</span><span class="special">&lt;</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span>                                                        <a class="link" href="circular_buffer.html#boost.circular_buffer.size_type"><span class="identifier">size_type</span></a><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="identifier">Alloc</span>                                                                                     <a name="boost.circular_buffer.allocator_type"></a><span class="identifier">allocator_type</span><span class="special">;</span>          <span class="comment">// The type of an allocator used in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </span>
  <span class="keyword">typedef</span> <span class="identifier">cb_details</span><span class="special">::</span><span class="identifier">iterator</span><span class="special">&lt;</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">cb_details</span><span class="special">::</span><span class="identifier">const_traits</span><span class="special">&lt;</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&gt;</span>    <a name="boost.circular_buffer.const_iterator"></a><span class="identifier">const_iterator</span><span class="special">;</span>          <span class="comment">// A const (random access) iterator used to iterate through the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </span>
  <span class="keyword">typedef</span> <span class="identifier">cb_details</span><span class="special">::</span><span class="identifier">iterator</span><span class="special">&lt;</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">cb_details</span><span class="special">::</span><span class="identifier">nonconst_traits</span><span class="special">&lt;</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.circular_buffer.iterator"></a><span class="identifier">iterator</span><span class="special">;</span>                <span class="comment">// A (random access) iterator used to iterate through the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </span>
  <span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">reverse_iterator</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span>                                                   <a name="boost.circular_buffer.const_reverse_iterator"></a><span class="identifier">const_reverse_iterator</span><span class="special">;</span>  <span class="comment">// A const iterator used to iterate backwards through a <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </span>
  <span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">reverse_iterator</span><span class="special">&lt;</span> <span class="identifier">iterator</span> <span class="special">&gt;</span>                                                         <a name="boost.circular_buffer.reverse_iterator"></a><span class="identifier">reverse_iterator</span><span class="special">;</span>        <span class="comment">// An iterator used to iterate backwards through a <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </span>
  <span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">pointer</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&gt;</span>                                                           <a class="link" href="circular_buffer.html#boost.circular_buffer.array_range"><span class="identifier">array_range</span></a><span class="special">;</span>           
  <span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_pointer</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&gt;</span>                                                     <a class="link" href="circular_buffer.html#boost.circular_buffer.const_array_range"><span class="identifier">const_array_range</span></a><span class="special">;</span>     
  <span class="keyword">typedef</span> <span class="identifier">size_type</span>                                                                                 <a class="link" href="circular_buffer.html#boost.circular_buffer.capacity_type"><span class="identifier">capacity_type</span></a><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span>                                                                        <a name="boost.circular_buffer.param_value_type"></a><span class="identifier">param_value_type</span><span class="special">;</span>        <span class="comment">// A type representing the "best" way to pass the value_type to a method. </span>
  <span class="keyword">typedef</span> <span class="identifier">value_type</span> <span class="special">&amp;&amp;</span>                                                                             <a class="link" href="circular_buffer.html#boost.circular_buffer.rvalue_type"><span class="identifier">rvalue_type</span></a><span class="special">;</span>           

  <span class="comment">// <a class="link" href="circular_buffer.html#boost.circular_bufferconstruct-copy-destruct">construct/copy/destruct</a></span>
  <span class="keyword">explicit</span> <a class="link" href="circular_buffer.html#idm4596-bb"><span class="identifier">circular_buffer</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="circular_buffer.html#idm4630-bb"><span class="identifier">circular_buffer</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> 
                           <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer.html#idm4657-bb"><span class="identifier">circular_buffer</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">,</span> 
                  <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer.html#idm4694-bb"><span class="identifier">circular_buffer</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">,</span> 
                  <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer.html#idm4740-bb"><span class="identifier">circular_buffer</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer.html#idm4765-bb"><span class="identifier">circular_buffer</span></a><span class="special">(</span><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="circular_buffer.html#idm4791-bb"><span class="identifier">circular_buffer</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                    <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="circular_buffer.html#idm4831-bb"><span class="identifier">circular_buffer</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                    <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> <a class="link" href="circular_buffer.html#idm4909-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
  <a class="link" href="circular_buffer.html#idm4954-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="circular_buffer.html#idm4885-bb"><span class="special">~</span><span class="identifier">circular_buffer</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="circular_buffer.html#idm1603-bb">public member functions</a></span>
  <span class="identifier">allocator_type</span> <a class="link" href="circular_buffer.html#idm1604-bb"><span class="identifier">get_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <a class="link" href="circular_buffer.html#idm1629-bb"><span class="identifier">get_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm1656-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm1690-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="circular_buffer.html#idm1724-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="circular_buffer.html#idm1758-bb"><span class="identifier">cbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="circular_buffer.html#idm1760-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="circular_buffer.html#idm1794-bb"><span class="identifier">cend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="circular_buffer.html#idm1796-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="circular_buffer.html#idm1830-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="circular_buffer.html#idm1864-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="circular_buffer.html#idm1898-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="circular_buffer.html#idm1932-bb"><span class="keyword">operator</span> <span class="special">[</span><span class="special">]</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="circular_buffer.html#idm1966-bb"><span class="keyword">operator</span> <span class="special">[</span><span class="special">]</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="circular_buffer.html#idm2000-bb"><span class="identifier">at</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="circular_buffer.html#idm2033-bb"><span class="identifier">at</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="circular_buffer.html#idm2066-bb"><span class="identifier">front</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="circular_buffer.html#idm2096-bb"><span class="identifier">back</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="circular_buffer.html#idm2126-bb"><span class="identifier">front</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="circular_buffer.html#idm2156-bb"><span class="identifier">back</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">array_range</span> <a class="link" href="circular_buffer.html#idm2186-bb"><span class="identifier">array_one</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">array_range</span> <a class="link" href="circular_buffer.html#idm2276-bb"><span class="identifier">array_two</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_array_range</span> <a class="link" href="circular_buffer.html#idm2305-bb"><span class="identifier">array_one</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_array_range</span> <a class="link" href="circular_buffer.html#idm2335-bb"><span class="identifier">array_two</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">pointer</span> <a class="link" href="circular_buffer.html#idm2364-bb"><span class="identifier">linearize</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="circular_buffer.html#idm2406-bb"><span class="identifier">is_linearized</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm2440-bb"><span class="identifier">rotate</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="circular_buffer.html#idm2509-bb"><span class="identifier">size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="circular_buffer.html#idm2541-bb"><span class="identifier">max_size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="circular_buffer.html#idm2572-bb"><span class="identifier">empty</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="circular_buffer.html#idm2603-bb"><span class="identifier">full</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="circular_buffer.html#idm2636-bb"><span class="identifier">reserve</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">capacity_type</span> <a class="link" href="circular_buffer.html#idm2666-bb"><span class="identifier">capacity</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm2698-bb"><span class="identifier">set_capacity</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm2748-bb"><span class="identifier">resize</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span> <span class="special">=</span> <span class="identifier">value_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm2807-bb"><span class="identifier">rset_capacity</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm2857-bb"><span class="identifier">rresize</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span> <span class="special">=</span> <span class="identifier">value_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm2916-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm2968-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3029-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3083-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3151-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3192-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3234-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="identifier">rvalue_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3276-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3315-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3357-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="identifier">rvalue_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3399-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3438-bb"><span class="identifier">pop_back</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3472-bb"><span class="identifier">pop_front</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm3506-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm3570-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">rvalue_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm3634-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3695-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm3784-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm3891-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm3954-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">rvalue_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm4017-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm4077-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm4165-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm4271-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm4316-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm4364-bb"><span class="identifier">rerase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer.html#idm4416-bb"><span class="identifier">rerase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm4469-bb"><span class="identifier">erase_begin</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm4514-bb"><span class="identifier">erase_end</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer.html#idm4559-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.3.3.4"></a><h2>Description</h2>
<p>
</p>
<p><b>Type Requirements T. </b>The <code class="computeroutput">T</code> has to be <a href="https://www.boost.org/sgi/stl/Assignable.html" target="_top">SGIAssignable</a> (SGI STL defined combination of <a href="../../../../utility/Assignable.html" target="_top">Assignable</a> and <a href="../../../../utility/CopyConstructible.html" target="_top">CopyConstructible</a>). Moreover <code class="computeroutput">T</code> has to be <a href="https://www.boost.org/sgi/stl/DefaultConstructible.html" target="_top">DefaultConstructible</a> if supplied as a default parameter when invoking some of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>'s methods e.g. <code class="computeroutput">insert(iterator pos, const value_type&amp; item = value_type())</code>. And <a href="https://www.boost.org/sgi/stl/EqualityComparable.html" target="_top">EqualityComparable</a> and/or <a href="../../../../utility/LessThanComparable.html" target="_top">LessThanComparable</a> if the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> will be compared with another container. </p>
<p>

</p>
<p><b>Type Requirements Alloc. </b>The <code class="computeroutput">Alloc</code> has to meet the allocator requirements imposed by STL. </p>
<p>
</p>
<p><b>Default Alloc. </b>std::allocator&lt;T&gt;</p>
<p>
For detailed documentation of the <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a> visit: <a href="http://www.boost.org/libs/circular_buffer/doc/circular_buffer.html" target="_top">http://www.boost.org/libs/circular_buffer/doc/circular_buffer.html</a> </p>
<div class="refsect2">
<a name="id-********.3.3.4.3"></a><h3>Template Parameters</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> T</pre>
<p>The type of the elements stored in the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Alloc</pre>
<p>The allocator type used for all internal memory management. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.3.3.4.4"></a><h3>
<a name="boost.circular_buffertypes"></a><code class="computeroutput">circular_buffer</code> 
        public
       types</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<p>
<span class="keyword">typedef</span> <span class="identifier">allocator_difference_type</span><span class="special">&lt;</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a name="boost.circular_buffer.difference_type"></a><span class="identifier">difference_type</span><span class="special">;</span></p>
<p>(A signed integral type used to represent the distance between two iterators.) </p>
</li>
<li class="listitem">
<p>
<span class="keyword">typedef</span> <span class="identifier">allocator_size_type</span><span class="special">&lt;</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a name="boost.circular_buffer.size_type"></a><span class="identifier">size_type</span><span class="special">;</span></p>
<p>(An unsigned integral type that can represent any non-negative value of the container's distance type.) </p>
</li>
<li class="listitem">
<p>
<span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">pointer</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&gt;</span> <a name="boost.circular_buffer.array_range"></a><span class="identifier">array_range</span><span class="special">;</span></p>
<p>(A typedef for the <a href="https://www.boost.org/sgi/stl/pair.html" target="_top"><code class="computeroutput">std::pair</code></a> where its first element is a pointer to a beginning of an array and its second element represents a size of the array.) </p>
</li>
<li class="listitem">
<p>
<span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_pointer</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">&gt;</span> <a name="boost.circular_buffer.const_array_range"></a><span class="identifier">const_array_range</span><span class="special">;</span></p>
<p>(A typedef for the <a href="https://www.boost.org/sgi/stl/pair.html" target="_top"><code class="computeroutput">std::pair</code></a> where its first element is a pointer to a beginning of a const array and its second element represents a size of the const array.) </p>
</li>
<li class="listitem">
<p>
<span class="keyword">typedef</span> <span class="identifier">size_type</span> <a name="boost.circular_buffer.capacity_type"></a><span class="identifier">capacity_type</span><span class="special">;</span></p>
<p>(Same as <code class="computeroutput">size_type</code> - defined for consistency with the __cbso class. </p>
</li>
<li class="listitem">
<p>
<span class="keyword">typedef</span> <span class="identifier">value_type</span> <span class="special">&amp;&amp;</span> <a name="boost.circular_buffer.rvalue_type"></a><span class="identifier">rvalue_type</span><span class="special">;</span></p>
<p>A type representing rvalue from param type. On compilers without rvalue references support this type is the Boost.Moves type used for emulation. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.3.3.4.5"></a><h3>
<a name="boost.circular_bufferconstruct-copy-destruct"></a><code class="computeroutput">circular_buffer</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm4596-bb"></a><span class="identifier">circular_buffer</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Create an empty <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with zero capacity. <p>


</p>
<p><b>Complexity. </b>Constant. </p>
<p>
</p>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top"><p>Since Boost version 1.36 the behaviour of this constructor has changed. Now the constructor does not allocate any memory and both capacity and size are set to zero. Also note when inserting an element into a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with zero capacity (e.g. by <code class="computeroutput">push_back(const_reference)</code> or <code class="computeroutput">insert(iterator, value_type)</code>) nothing will be inserted and the size (as well as capacity) remains zero. </p></td></tr>
</table></div>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>You can explicitly set the capacity by calling the <code class="computeroutput">set_capacity(capacity_type)</code> method or you can use the other constructor with the capacity specified. </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">circular_buffer(capacity_type, const allocator_type&amp; alloc)</code>, <code class="computeroutput">set_capacity(capacity_type)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == 0 &amp;&amp; size() == 0</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm4630-bb"></a><span class="identifier">circular_buffer</span><span class="special">(</span><span class="identifier">capacity_type</span> buffer_capacity<span class="special">,</span> 
                         <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create an empty <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with the specified capacity. <p>


</p>
<p><b>Complexity. </b>Constant. </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer_capacity</code></span></p></td>
<td><p>The maximum number of elements which can be stored in the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == buffer_capacity &amp;&amp; size() == 0</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm4657-bb"></a><span class="identifier">circular_buffer</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">,</span> 
                <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create a full <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with the specified capacity and filled with <code class="computeroutput">n</code> copies of <code class="computeroutput">item</code>. <p>


</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">n</code>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the created <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of elements the created <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == n &amp;&amp; full() &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this)[n - 1] == item </code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm4694-bb"></a><span class="identifier">circular_buffer</span><span class="special">(</span><span class="identifier">capacity_type</span> buffer_capacity<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> 
                <span class="identifier">param_value_type</span> item<span class="special">,</span> 
                <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with the specified capacity and filled with <code class="computeroutput">n</code> copies of <code class="computeroutput">item</code>. <p>



</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">n</code>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer_capacity</code></span></p></td>
<td><p>The capacity of the created <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the created <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of elements the created <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">buffer_capacity &gt;= n</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == buffer_capacity &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this)[n - 1] == item</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm4740-bb"></a><span class="identifier">circular_buffer</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> cb<span class="special">)</span><span class="special">;</span></pre>The copy constructor. <p>Creates a copy of the specified <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. 


</p>
<p><b>Complexity. </b>Linear (in the size of <code class="computeroutput">cb</code>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p>The <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> to be copied. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">*this == cb</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm4765-bb"></a><span class="identifier">circular_buffer</span><span class="special">(</span><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> cb<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>The move constructor. <p>Move constructs a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> from <code class="computeroutput">cb</code>, leaving <code class="computeroutput">cb</code> empty. 



</p>
<p><b>Constant. </b></p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p><code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> to 'steal' value from. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>C++ compiler with rvalue references support. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">cb.empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm4791-bb"></a><span class="identifier">circular_buffer</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> 
                  <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create a full <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> filled with a copy of the range. <p>



</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">std::distance(first, last)</code>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be copied. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be copied. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>.<br>
 <code class="computeroutput">first</code> and <code class="computeroutput">last</code> have to meet the requirements of <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == std::distance(first, last) &amp;&amp; full() &amp;&amp; (*this)[0]== *first &amp;&amp; (*this)[1] == *(first + 1) &amp;&amp; ... &amp;&amp; (*this)[std::distance(first, last) - 1] == *(last - 1)</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm4831-bb"></a><span class="identifier">circular_buffer</span><span class="special">(</span><span class="identifier">capacity_type</span> buffer_capacity<span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> 
                  <span class="identifier">InputIterator</span> last<span class="special">,</span> 
                  <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with the specified capacity and filled with a copy of the range. <p>



</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(first, last)</code>; in <code class="computeroutput">min[capacity, std::distance(first, last)]</code> if the <code class="computeroutput">InputIterator</code> is a <a href="https://www.boost.org/sgi/stl/RandomAccessIterator.html" target="_top">RandomAccessIterator</a>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer_capacity</code></span></p></td>
<td><p>The capacity of the created <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be copied. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be copied. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>.<br>
 <code class="computeroutput">first</code> and <code class="computeroutput">last</code> have to meet the requirements of <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == buffer_capacity &amp;&amp; size() &lt;= std::distance(first, last) &amp;&amp; (*this)[0]== *(last - buffer_capacity) &amp;&amp; (*this)[1] == *(last - buffer_capacity + 1) &amp;&amp; ... &amp;&amp; (*this)[buffer_capacity - 1] == *(last - 1)</code><br>
<br>
 If the number of items to be copied from the range <code class="computeroutput">[first, last)</code> is greater than the specified <code class="computeroutput">buffer_capacity</code> then only elements from the range <code class="computeroutput">[last - buffer_capacity, last)</code> will be copied. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
<a name="idm4909-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> cb<span class="special">)</span><span class="special">;</span></pre>The assign operator. <p>Makes this <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> to become a copy of the specified <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. 


</p>
<p><b>Exception Safety. </b>Strong. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to this <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of <code class="computeroutput">cb</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">assign(size_type, const_reference)</code>, <code class="computeroutput">assign(capacity_type, size_type, const_reference)</code>, <code class="computeroutput">assign(InputIterator, InputIterator)</code>, <code class="computeroutput">assign(capacity_type, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p>The <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> to be copied. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">*this == cb</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
<a name="idm4954-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> cb<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Move assigns content of <code class="computeroutput">cb</code> to <code class="computeroutput">*this</code>, leaving <code class="computeroutput">cb</code> empty. <p>



</p>
<p><b>Complexity. </b>Constant. </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p><code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> to 'steal' value from. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>C++ compiler with rvalue references support. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">cb.empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm4885-bb"></a><span class="special">~</span><span class="identifier">circular_buffer</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>The destructor. <p>Destroys the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. 
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (including iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>) for scalar types; linear for other types. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr></tbody>
</table></div>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.3.3.4.6"></a><h3>
<a name="idm1603-bb"></a><code class="computeroutput">circular_buffer</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="identifier">allocator_type</span> <a name="idm1604-bb"></a><span class="identifier">get_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the allocator. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">get_allocator()</code> for obtaining an allocator reference. </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">allocator_type</span> <span class="special">&amp;</span> <a name="idm1629-bb"></a><span class="identifier">get_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the allocator reference. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>This method was added in order to optimize obtaining of the allocator with a state, although use of stateful allocators in STL is discouraged. </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">get_allocator() const</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A reference to the allocator. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm1656-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the iterator pointing to the beginning of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">end()</code>, <code class="computeroutput">rbegin()</code>, <code class="computeroutput">rend()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A random access iterator pointing to the first element of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty it returns an iterator equal to the one returned by <code class="computeroutput">end()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm1690-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the iterator pointing to the end of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">begin()</code>, <code class="computeroutput">rbegin()</code>, <code class="computeroutput">rend()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A random access iterator pointing to the element "one behind" the last element of the <code class="computeroutput"> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty it returns an iterator equal to the one returned by <code class="computeroutput">begin()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm1724-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the const iterator pointing to the beginning of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">end() const</code>, <code class="computeroutput">rbegin() const</code>, <code class="computeroutput">rend() const</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A const random access iterator pointing to the first element of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty it returns an iterator equal to the one returned by <code class="computeroutput">end() const</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem"><pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm1758-bb"></a><span class="identifier">cbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre></li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm1760-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the const iterator pointing to the end of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">begin() const</code>, <code class="computeroutput">rbegin() const</code>, <code class="computeroutput">rend() const</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A const random access iterator pointing to the element "one behind" the last element of the <code class="computeroutput"> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty it returns an iterator equal to the one returned by <code class="computeroutput">begin() const</code> const. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem"><pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm1794-bb"></a><span class="identifier">cend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre></li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm1796-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the iterator pointing to the beginning of the "reversed" <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rend()</code>, <code class="computeroutput">begin()</code>, <code class="computeroutput">end()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A reverse random access iterator pointing to the last element of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty it returns an iterator equal to the one returned by <code class="computeroutput">rend()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm1830-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the iterator pointing to the end of the "reversed" <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rbegin()</code>, <code class="computeroutput">begin()</code>, <code class="computeroutput">end()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A reverse random access iterator pointing to the element "one before" the first element of the <code class="computeroutput"> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty it returns an iterator equal to the one returned by <code class="computeroutput">rbegin()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm1864-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the const iterator pointing to the beginning of the "reversed" <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rend() const</code>, <code class="computeroutput">begin() const</code>, <code class="computeroutput">end() const</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A const reverse random access iterator pointing to the last element of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty it returns an iterator equal to the one returned by <code class="computeroutput">rend() const</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm1898-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the const iterator pointing to the end of the "reversed" <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rbegin() const</code>, <code class="computeroutput">begin() const</code>, <code class="computeroutput">end() const</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A const reverse random access iterator pointing to the element "one before" the first element of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty it returns an iterator equal to the one returned by <code class="computeroutput">rbegin() const</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm1932-bb"></a><span class="keyword">operator</span> <span class="special">[</span><span class="special">]</span><span class="special">(</span><span class="identifier">size_type</span> index<span class="special">)</span><span class="special">;</span></pre>Get the element at the <code class="computeroutput">index</code> position. <p>



</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">at()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">index</code></span></p></td>
<td><p>The position of the element. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">0 &lt;= index &amp;&amp; index &lt; size()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A reference to the element at the <code class="computeroutput">index</code> position. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm1966-bb"></a><span class="keyword">operator</span> <span class="special">[</span><span class="special">]</span><span class="special">(</span><span class="identifier">size_type</span> index<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Get the element at the <code class="computeroutput">index</code> position. <p>



</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">at() const </code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">index</code></span></p></td>
<td><p>The position of the element. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">0 &lt;= index &amp;&amp; index &lt; size()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A const reference to the element at the <code class="computeroutput">index</code> position. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm2000-bb"></a><span class="identifier">at</span><span class="special">(</span><span class="identifier">size_type</span> index<span class="special">)</span><span class="special">;</span></pre>Get the element at the <code class="computeroutput">index</code> position. <p>


</p>
<p><b>Exception Safety. </b>Strong. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator[] </code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">index</code></span></p></td>
<td><p>The position of the element. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A reference to the element at the <code class="computeroutput">index</code> position. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>&lt;code&gt;std::out_of_range&lt;/code&gt; when the <code class="computeroutput">index</code> is invalid (when <code class="computeroutput">index &gt;= size()</code>). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm2033-bb"></a><span class="identifier">at</span><span class="special">(</span><span class="identifier">size_type</span> index<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Get the element at the <code class="computeroutput">index</code> position. <p>


</p>
<p><b>Exception Safety. </b>Strong. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator[] const </code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">index</code></span></p></td>
<td><p>The position of the element. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A const reference to the element at the <code class="computeroutput">index</code> position. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>&lt;code&gt;std::out_of_range&lt;/code&gt; when the <code class="computeroutput">index</code> is invalid (when <code class="computeroutput">index &gt;= size()</code>). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm2066-bb"></a><span class="identifier">front</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Get the first element. <p>


</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">back()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">!empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A reference to the first element of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm2096-bb"></a><span class="identifier">back</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Get the last element. <p>


</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">!empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A reference to the last element of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm2126-bb"></a><span class="identifier">front</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Get the first element. <p>


</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">back() const</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">!empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A const reference to the first element of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm2156-bb"></a><span class="identifier">back</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Get the last element. <p>


</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">front() const</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">!empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A const reference to the last element of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">array_range</span> <a name="idm2186-bb"></a><span class="identifier">array_one</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Get the first continuous array of the internal buffer. <p>This method in combination with <code class="computeroutput">array_two()</code> can be useful when passing the stored data into a legacy C API as an array. Suppose there is a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> of capacity 10, containing 7 characters <code class="computeroutput">'a', 'b', ..., 'g'</code> where <code class="computeroutput">buff[0] == 'a'</code>, <code class="computeroutput">buff[1] == 'b'</code>, ... and <code class="computeroutput">buff[6] == 'g'</code>:<br>
<br>
 <code class="computeroutput">circular_buffer&lt;char&gt; buff(10);</code><br>
<br>
 The internal representation is often not linear and the state of the internal buffer may look like this:<br>
 <br>
<code class="computeroutput"> |e|f|g| | | |a|b|c|d|<br>
 end ___^<br>
 begin _______^</code><br>
<br>
</p>
<p>where <code class="computeroutput">|a|b|c|d|</code> represents the "array one", <code class="computeroutput">|e|f|g|</code> represents the "array two" and <code class="computeroutput">| | | |</code> is a free space.<br>
 Now consider a typical C style function for writing data into a file:<br>
<br>
 <code class="computeroutput">int write(int file_desc, char* buff, int num_bytes);</code><br>
<br>
 There are two ways how to write the content of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> into a file. Either relying on <code class="computeroutput">array_one()</code> and <code class="computeroutput">array_two()</code> methods and calling the write function twice:<br>
<br>
 <code class="computeroutput">array_range ar = buff.array_one();<br>
 write(file_desc, ar.first, ar.second);<br>
 ar = buff.array_two();<br>
 write(file_desc, ar.first, ar.second);</code><br>
<br>
 Or relying on the <code class="computeroutput">linearize()</code> method:<br>
<br>
<code class="computeroutput"> write(file_desc, buff.linearize(), buff.size());</code><br>
<br>
 Since the complexity of <code class="computeroutput">array_one()</code> and <code class="computeroutput">array_two()</code> methods is constant the first option is suitable when calling the write method is "cheap". On the other hand the second option is more suitable when calling the write method is more "expensive" than calling the <code class="computeroutput">linearize()</code> method whose complexity is linear. 

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top"><p>In general invoking any method which modifies the internal state of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> may delinearize the internal buffer and invalidate the array ranges returned by <code class="computeroutput">array_one()</code> and <code class="computeroutput">array_two()</code> (and their const versions). </p></td></tr>
</table></div>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>In the case the internal buffer is linear e.g. <code class="computeroutput">|a|b|c|d|e|f|g| | | |</code> the "array one" is represented by <code class="computeroutput">|a|b|c|d|e|f|g|</code> and the "array two" does not exist (the <code class="computeroutput">array_two()</code> method returns an array with the size <code class="computeroutput">0</code>). </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">array_two()</code>, <code class="computeroutput">linearize()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The array range of the first continuous array of the internal buffer. In the case the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty the size of the returned array is <code class="computeroutput">0</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">array_range</span> <a name="idm2276-bb"></a><span class="identifier">array_two</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Get the second continuous array of the internal buffer. <p>This method in combination with <code class="computeroutput">array_one()</code> can be useful when passing the stored data into a legacy C API as an array. 

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">array_one()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The array range of the second continuous array of the internal buffer. In the case the internal buffer is linear or the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty the size of the returned array is <code class="computeroutput">0</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_array_range</span> <a name="idm2305-bb"></a><span class="identifier">array_one</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Get the first continuous array of the internal buffer. <p>This method in combination with <code class="computeroutput">array_two() const</code> can be useful when passing the stored data into a legacy C API as an array. 

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">array_two() const</code>; <code class="computeroutput">array_one()</code> for more details how to pass data into a legacy C API. </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The array range of the first continuous array of the internal buffer. In the case the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty the size of the returned array is <code class="computeroutput">0</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_array_range</span> <a name="idm2335-bb"></a><span class="identifier">array_two</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>Get the second continuous array of the internal buffer. <p>This method in combination with <code class="computeroutput">array_one() const</code> can be useful when passing the stored data into a legacy C API as an array. 

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">array_one() const</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The array range of the second continuous array of the internal buffer. In the case the internal buffer is linear or the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is empty the size of the returned array is <code class="computeroutput">0</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">pointer</span> <a name="idm2364-bb"></a><span class="identifier">linearize</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Linearize the internal buffer into a continuous array. <p>This method can be useful when passing the stored data into a legacy C API as an array. 


</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operations in the <span class="emphasis"><em>Throws</em></span> section do not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>); does not invalidate any iterators if the postcondition (the <span class="emphasis"><em>Effect</em></span>) is already met prior calling this method. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>); constant if the postcondition (the <span class="emphasis"><em>Effect</em></span>) is already met. </p>
<p>
</p>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top"><p>In general invoking any method which modifies the internal state of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> may delinearize the internal buffer and invalidate the returned pointer. </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">array_one()</code> and <code class="computeroutput">array_two()</code> for the other option how to pass data into a legacy C API; <code class="computeroutput">is_linearized()</code>, <code class="computeroutput">rotate(const_iterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">&amp;(*this)[0] &lt; &amp;(*this)[1] &lt; ... &lt; &amp;(*this)[size() - 1]</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>A pointer to the beginning of the array or <code class="computeroutput">0</code> if empty. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>&lt;a href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm2406-bb"></a><span class="identifier">is_linearized</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Is the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> linearized? <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">linearize()</code>, <code class="computeroutput">array_one()</code>, <code class="computeroutput">array_two()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">true</code> if the internal buffer is linearized into a continuous array (i.e. the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> meets a condition <code class="computeroutput">&amp;(*this)[0] &lt; &amp;(*this)[1] &lt; ... &lt; &amp;(*this)[size() - 1]</code>); <code class="computeroutput">false</code> otherwise. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm2440-bb"></a><span class="identifier">rotate</span><span class="special">(</span><span class="identifier">const_iterator</span> new_begin<span class="special">)</span><span class="special">;</span></pre>Rotate elements in the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>A more effective implementation of <code class="computeroutput"><a href="https://www.boost.org/sgi/stl/rotate.html" target="_top">std::rotate</a></code>. 



</p>
<p><b>Exception Safety. </b>Basic; no-throw if the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> is full or <code class="computeroutput">new_begin</code> points to <code class="computeroutput">begin()</code> or if the operations in the <span class="emphasis"><em>Throws</em></span> section do not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>If <code class="computeroutput">m &lt; n</code> invalidates iterators pointing to the last <code class="computeroutput">m</code> elements (<span class="bold"><strong>including</strong></span> <code class="computeroutput">new_begin</code>, but not iterators equal to <code class="computeroutput">end()</code>) else invalidates iterators pointing to the first <code class="computeroutput">n</code> elements; does not invalidate any iterators if the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> is full. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">(std::min)(m, n)</code>); constant if the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> is full. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput"><a href="https://www.boost.org/sgi/stl/rotate.html" target="_top">std::rotate</a></code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">new_begin</code></span></p></td>
<td><p>The new beginning. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">new_begin</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> <span class="bold"><strong>except</strong></span> its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>Before calling the method suppose:<br>
<br>
 <code class="computeroutput">m == std::distance(new_begin, end())</code><br>
<code class="computeroutput">n == std::distance(begin(), new_begin)</code> <br>
<code class="computeroutput">val_0 == *new_begin, val_1 == *(new_begin + 1), ... val_m == *(new_begin + m)</code><br>
 <code class="computeroutput">val_r1 == *(new_begin - 1), val_r2 == *(new_begin - 2), ... val_rn == *(new_begin - n)</code><br>
 <br>
then after call to the method:<br>
<br>
 <code class="computeroutput">val_0 == (*this)[0] &amp;&amp; val_1 == (*this)[1] &amp;&amp; ... &amp;&amp; val_m == (*this)[m - 1] &amp;&amp; val_r1 == (*this)[m + n - 1] &amp;&amp; val_r2 == (*this)[m + n - 2] &amp;&amp; ... &amp;&amp; val_rn == (*this)[m]</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>See <a href="../circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t" target="_top">Exceptions of move_if_noexcept(T&amp;)</a>. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm2509-bb"></a><span class="identifier">size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the number of elements currently stored in the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">capacity()</code>, <code class="computeroutput">max_size()</code>, <code class="computeroutput">reserve()</code>, <code class="computeroutput">resize(size_type, const_reference)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm2541-bb"></a><span class="identifier">max_size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the largest possible size or capacity of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. (It depends on allocator's max_size()). <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">size()</code>, <code class="computeroutput">capacity()</code>, <code class="computeroutput">reserve()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The maximum size/capacity the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> can be set to. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm2572-bb"></a><span class="identifier">empty</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Is the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> empty? <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">full()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">true</code> if there are no elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>; <code class="computeroutput">false</code> otherwise. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm2603-bb"></a><span class="identifier">full</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Is the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> full? <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">empty()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">true</code> if the number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> equals the capacity of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>; <code class="computeroutput">false</code> otherwise. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm2636-bb"></a><span class="identifier">reserve</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the maximum number of elements which can be inserted into the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> without overwriting any of already stored elements. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">capacity()</code>, <code class="computeroutput">size()</code>, <code class="computeroutput">max_size()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">capacity() - size()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">capacity_type</span> <a name="idm2666-bb"></a><span class="identifier">capacity</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the capacity of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">reserve()</code>, <code class="computeroutput">size()</code>, <code class="computeroutput">max_size()</code>, <code class="computeroutput">set_capacity(capacity_type)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The maximum number of elements which can be stored in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm2698-bb"></a><span class="identifier">set_capacity</span><span class="special">(</span><span class="identifier">capacity_type</span> new_capacity<span class="special">)</span><span class="special">;</span></pre>Change the capacity of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>



</p>
<p><b>Exception Safety. </b>Strong. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>) if the new capacity is different from the original. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">min[size(), new_capacity]</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rset_capacity(capacity_type)</code>, <code class="computeroutput">resize(size_type, const_reference)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">new_capacity</code></span></p></td>
<td><p>The new capacity. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>If <code class="computeroutput">T</code> is a move only type, then compiler shall support <code class="computeroutput">noexcept</code> modifiers and move constructor of <code class="computeroutput">T</code> must be marked with it (must not throw exceptions). </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == new_capacity &amp;&amp; size() &lt;= new_capacity</code><br>
<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is greater than the desired new capacity then number of <code class="computeroutput">[size() - new_capacity]</code> <span class="bold"><strong>last</strong></span> elements will be removed and the new size will be equal to <code class="computeroutput">new_capacity</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted, (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm2748-bb"></a><span class="identifier">resize</span><span class="special">(</span><span class="identifier">size_type</span> new_size<span class="special">,</span> <span class="identifier">param_value_type</span> item <span class="special">=</span> <span class="identifier">value_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Change the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>) if the new size is greater than the current capacity. Invalidates iterators pointing to the removed elements if the new size is lower that the original size. Otherwise it does not invalidate any iterator. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the new size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rresize(size_type, const_reference)</code>, <code class="computeroutput">set_capacity(capacity_type)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with in order to gain the requested size. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">new_size</code></span></p></td>
<td><p>The new size. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">size() == new_size &amp;&amp; capacity() &gt;= new_size</code><br>
<br>
 If the new size is greater than the current size, copies of <code class="computeroutput">item</code> will be inserted at the <span class="bold"><strong>back</strong></span> of the of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> in order to achieve the desired size. In the case the resulting size exceeds the current capacity the capacity will be set to <code class="computeroutput">new_size</code>.<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is greater than the desired new size then number of <code class="computeroutput">[size() - new_size]</code> <span class="bold"><strong>last</strong></span> elements will be removed. (The capacity will remain unchanged.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm2807-bb"></a><span class="identifier">rset_capacity</span><span class="special">(</span><span class="identifier">capacity_type</span> new_capacity<span class="special">)</span><span class="special">;</span></pre>Change the capacity of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>



</p>
<p><b>Exception Safety. </b>Strong. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>) if the new capacity is different from the original. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">min[size(), new_capacity]</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">set_capacity(capacity_type)</code>, <code class="computeroutput">rresize(size_type, const_reference)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">new_capacity</code></span></p></td>
<td><p>The new capacity. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>If <code class="computeroutput">T</code> is a move only type, then compiler shall support <code class="computeroutput">noexcept</code> modifiers and move constructor of <code class="computeroutput">T</code> must be marked with it (must not throw exceptions). </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == new_capacity &amp;&amp; size() &lt;= new_capacity</code><br>
<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is greater than the desired new capacity then number of <code class="computeroutput">[size() - new_capacity]</code> <span class="bold"><strong>first</strong></span> elements will be removed and the new size will be equal to <code class="computeroutput">new_capacity</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm2857-bb"></a><span class="identifier">rresize</span><span class="special">(</span><span class="identifier">size_type</span> new_size<span class="special">,</span> <span class="identifier">param_value_type</span> item <span class="special">=</span> <span class="identifier">value_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Change the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>) if the new size is greater than the current capacity. Invalidates iterators pointing to the removed elements if the new size is lower that the original size. Otherwise it does not invalidate any iterator. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the new size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">resize(size_type, const_reference)</code>, <code class="computeroutput">rset_capacity(capacity_type)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with in order to gain the requested size. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">new_size</code></span></p></td>
<td><p>The new size. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">size() == new_size &amp;&amp; capacity() &gt;= new_size</code><br>
<br>
 If the new size is greater than the current size, copies of <code class="computeroutput">item</code> will be inserted at the <span class="bold"><strong>front</strong></span> of the of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> in order to achieve the desired size. In the case the resulting size exceeds the current capacity the capacity will be set to <code class="computeroutput">new_size</code>.<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is greater than the desired new size then number of <code class="computeroutput">[size() - new_size]</code> <span class="bold"><strong>first</strong></span> elements will be removed. (The capacity will remain unchanged.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm2916-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Assign <code class="computeroutput">n</code> items into the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>The content of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be removed and replaced with <code class="computeroutput">n</code> copies of the <code class="computeroutput">item</code>. 


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">n</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator=</code>, <code class="computeroutput">assign(capacity_type, size_type, const_reference)</code>, <code class="computeroutput">assign(InputIterator, InputIterator)</code>, <code class="computeroutput">assign(capacity_type, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of elements the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == n &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [n - 1] == item</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm2968-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">capacity_type</span> buffer_capacity<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Assign <code class="computeroutput">n</code> items into the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> specifying the capacity. <p>The capacity of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be set to the specified value and the content of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be removed and replaced with <code class="computeroutput">n</code> copies of the <code class="computeroutput">item</code>. 



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">n</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator=</code>, <code class="computeroutput">assign(size_type, const_reference)</code>, <code class="computeroutput">assign(InputIterator, InputIterator)</code>, <code class="computeroutput">assign(capacity_type, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer_capacity</code></span></p></td>
<td><p>The new capacity. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of elements the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be filled with. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">capacity &gt;= n</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == buffer_capacity &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [n - 1] == item </code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm3029-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>Assign a copy of the range into the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>The content of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be removed and replaced with copies of elements from the specified range. 



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">std::distance(first, last)</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator=</code>, <code class="computeroutput">assign(size_type, const_reference)</code>, <code class="computeroutput">assign(capacity_type, size_type, const_reference)</code>, <code class="computeroutput">assign(capacity_type, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be copied. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be copied. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>.<br>
 <code class="computeroutput">first</code> and <code class="computeroutput">last</code> have to meet the requirements of <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == std::distance(first, last) &amp;&amp; size() == std::distance(first, last) &amp;&amp; (*this)[0]== *first &amp;&amp; (*this)[1] == *(first + 1) &amp;&amp; ... &amp;&amp; (*this)[std::distance(first, last) - 1] == *(last - 1)</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm3083-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">capacity_type</span> buffer_capacity<span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> 
              <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>Assign a copy of the range into the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> specifying the capacity. <p>The capacity of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be set to the specified value and the content of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> will be removed and replaced with copies of elements from the specified range. 



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(first, last)</code>; in <code class="computeroutput">min[capacity, std::distance(first, last)]</code> if the <code class="computeroutput">InputIterator</code> is a <a href="https://www.boost.org/sgi/stl/RandomAccessIterator.html" target="_top">RandomAccessIterator</a>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator=</code>, <code class="computeroutput">assign(size_type, const_reference)</code>, <code class="computeroutput">assign(capacity_type, size_type, const_reference)</code>, <code class="computeroutput">assign(InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">buffer_capacity</code></span></p></td>
<td><p>The new capacity. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be copied. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be copied. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>.<br>
 <code class="computeroutput">first</code> and <code class="computeroutput">last</code> have to meet the requirements of <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == buffer_capacity &amp;&amp; size() &lt;= std::distance(first, last) &amp;&amp; (*this)[0]== *(last - buffer_capacity) &amp;&amp; (*this)[1] == *(last - buffer_capacity + 1) &amp;&amp; ... &amp;&amp; (*this)[buffer_capacity - 1] == *(last - 1)</code><br>
<br>
 If the number of items to be copied from the range <code class="computeroutput">[first, last)</code> is greater than the specified <code class="computeroutput">buffer_capacity</code> then only elements from the range <code class="computeroutput">[last - buffer_capacity, last)</code> will be copied. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3151-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> cb<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Swap the contents of two <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>s. <p>


</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators of both <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>s. (On the other hand the iterators still point to the same elements but within another container. If you want to rely on this feature you have to turn the <a href="../#debug" target="_top">Debug Support</a> off otherwise an assertion will report an error if such invalidated iterator is used.) </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">swap(circular_buffer&lt;T, Alloc&gt;&amp;, circular_buffer&lt;T, Alloc&gt;&amp;)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p>The <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> whose content will be swapped. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">this</code> contains elements of <code class="computeroutput">cb</code> and vice versa; the capacity of <code class="computeroutput">this</code> equals to the capacity of <code class="computeroutput">cb</code> and vice versa. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3192-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert a new element at the end of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_front(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity() &gt; 0</code> then <code class="computeroutput">back() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the first element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3234-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">rvalue_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert a new element at the end of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> using rvalue references or rvalues references emulation. <p>


</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_front(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity() &gt; 0</code> then <code class="computeroutput">back() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the first element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3276-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Insert a new default-constructed element at the end of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_front(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity() &gt; 0</code> then <code class="computeroutput">back() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the first element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T()</code> throws. Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3315-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert a new element at the beginning of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity() &gt; 0</code> then <code class="computeroutput">front() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the last element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3357-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="identifier">rvalue_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert a new element at the beginning of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> using rvalue references or rvalues references emulation. <p>


</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity() &gt; 0</code> then <code class="computeroutput">front() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the last element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3399-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Insert a new default-constructed element at the beginning of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity() &gt; 0</code> then <code class="computeroutput">front() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the last element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T()</code> throws. Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3438-bb"></a><span class="identifier">pop_back</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Remove the last element from the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates only iterators pointing to the removed element. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">pop_front()</code>, <code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">push_front(const_reference)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">!empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The last element is removed from the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3472-bb"></a><span class="identifier">pop_front</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Remove the first element from the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates only iterators pointing to the removed element. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">pop_back()</code>, <code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">push_front(const_reference)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">!empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The first element is removed from the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm3506-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert an element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements at the insertion point (including <code class="computeroutput">pos</code>) and iterators behind the insertion point (towards the end; except iterators equal to <code class="computeroutput">end()</code>). It also invalidates iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(pos, end())</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted at the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the first element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">begin()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">begin()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. <a href="../circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t" target="_top">Exceptions of move_if_noexcept(T&amp;)</a>.</td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm3570-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">rvalue_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert an element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements at the insertion point (including <code class="computeroutput">pos</code>) and iterators behind the insertion point (towards the end; except iterators equal to <code class="computeroutput">end()</code>). It also invalidates iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(pos, end())</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted at the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the first element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">begin()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">begin()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws. <a href="../circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t" target="_top">Exceptions of move_if_noexcept(T&amp;)</a>. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm3634-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">)</span><span class="special">;</span></pre>Insert a default-constructed element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements at the insertion point (including <code class="computeroutput">pos</code>) and iterators behind the insertion point (towards the end; except iterators equal to <code class="computeroutput">end()</code>). It also invalidates iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(pos, end())</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted at the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the first element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">begin()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">begin()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T()</code> throws. Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws. <a href="../circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t" target="_top">Exceptions of move_if_noexcept(T&amp;)</a>. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm3695-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert <code class="computeroutput">n</code> copies of the <code class="computeroutput">item</code> at the specified position. <p>



</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operations in the <span class="emphasis"><em>Throws</em></span> section do not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements at the insertion point (including <code class="computeroutput">pos</code>) and iterators behind the insertion point (towards the end; except iterators equal to <code class="computeroutput">end()</code>). It also invalidates iterators pointing to the overwritten elements. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">min[capacity(), std::distance(pos, end()) + n]</code>). </p>
<p>
</p>
<p><b>Example. </b>Consider a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<br>
<br>
 <code class="computeroutput">|1|2|3|4| | |</code><br>
 <code class="computeroutput">p ___^</code><br>
<br>
After inserting 5 elements at the position <code class="computeroutput">p</code>:<br>
<br>
 <code class="computeroutput">insert(p, (size_t)5, 0);</code><br>
<br>
actually only 4 elements get inserted and elements <code class="computeroutput">1</code> and <code class="computeroutput">2</code> are overwritten. This is due to the fact the insert operation preserves the capacity. After insertion the internal buffer looks like this:<br>
<br>
<code class="computeroutput">|0|0|0|0|3|4|</code><br>
 <br>
For comparison if the capacity would not be preserved the internal buffer would then result in <code class="computeroutput">|1|2|0|0|0|0|0|3|4|</code>. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element whose copies will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of <code class="computeroutput">item</code>s the to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code>s will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The number of <code class="computeroutput">min[n, (pos - begin()) + reserve()]</code> elements will be inserted at the position <code class="computeroutput">pos</code>.<br>
The number of <code class="computeroutput">min[pos - begin(), max[0, n - reserve()]]</code> elements will be overwritten at the beginning of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>.<br>
(See <span class="emphasis"><em>Example</em></span> for the explanation.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. <a href="../circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t" target="_top">Exceptions of move_if_noexcept(T&amp;)</a>. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm3784-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>Insert the range <code class="computeroutput">[first, last)</code> at the specified position. <p>



</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operations in the <span class="emphasis"><em>Throws</em></span> section do not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements at the insertion point (including <code class="computeroutput">pos</code>) and iterators behind the insertion point (towards the end; except iterators equal to <code class="computeroutput">end()</code>). It also invalidates iterators pointing to the overwritten elements. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">[std::distance(pos, end()) + std::distance(first, last)]</code>; in <code class="computeroutput">min[capacity(), std::distance(pos, end()) + std::distance(first, last)]</code> if the <code class="computeroutput">InputIterator</code> is a <a href="https://www.boost.org/sgi/stl/RandomAccessIterator.html" target="_top">RandomAccessIterator</a>). </p>
<p>
</p>
<p><b>Example. </b>Consider a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<br>
<br>
 <code class="computeroutput">|1|2|3|4| | |</code><br>
 <code class="computeroutput">p ___^</code><br>
<br>
After inserting a range of elements at the position <code class="computeroutput">p</code>:<br>
<br>
 <code class="computeroutput">int array[] = { 5, 6, 7, 8, 9 };</code><br>
<code class="computeroutput">insert(p, array, array + 5);</code><br>
<br>
 actually only elements <code class="computeroutput">6</code>, <code class="computeroutput">7</code>, <code class="computeroutput">8</code> and <code class="computeroutput">9</code> from the specified range get inserted and elements <code class="computeroutput">1</code> and <code class="computeroutput">2</code> are overwritten. This is due to the fact the insert operation preserves the capacity. After insertion the internal buffer looks like this:<br>
<br>
<code class="computeroutput">|6|7|8|9|3|4|</code><br>
<br>
For comparison if the capacity would not be preserved the internal buffer would then result in <code class="computeroutput">|1|2|5|6|7|8|9|3|4|</code>. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the range will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end.<br>
 Valid range <code class="computeroutput">[first, last)</code> where <code class="computeroutput">first</code> and <code class="computeroutput">last</code> meet the requirements of an <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>Elements from the range <code class="computeroutput">[first + max[0, distance(first, last) - (pos - begin()) - reserve()], last)</code> will be inserted at the position <code class="computeroutput">pos</code>.<br>
The number of <code class="computeroutput">min[pos - begin(), max[0, distance(first, last) - reserve()]]</code> elements will be overwritten at the beginning of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>.<br>
(See <span class="emphasis"><em>Example</em></span> for the explanation.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws if the <code class="computeroutput">InputIterator</code> is not a move iterator. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws if the <code class="computeroutput">InputIterator</code> is not a move iterator. Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws if the <code class="computeroutput">InputIterator</code> is a move iterator. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws if the <code class="computeroutput">InputIterator</code> is a move iterator. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm3891-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert an element before the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operations in the <span class="emphasis"><em>Throws</em></span> section do not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <code class="computeroutput">pos</code>). It also invalidates iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(begin(), pos)</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position before which the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted before the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the last element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">end()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">end()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. <a href="../circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t" target="_top">Exceptions of move_if_noexcept(T&amp;)</a>. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm3954-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">rvalue_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert an element before the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operations in the <span class="emphasis"><em>Throws</em></span> section do not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <code class="computeroutput">pos</code>). It also invalidates iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(begin(), pos)</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position before which the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted before the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the last element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">end()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">end()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws. <a href="../circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t" target="_top">Exceptions of move_if_noexcept(T&amp;)</a>. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm4017-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">)</span><span class="special">;</span></pre>Insert an element before the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operations in the <span class="emphasis"><em>Throws</em></span> section do not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <code class="computeroutput">pos</code>). It also invalidates iterators pointing to the overwritten element. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(begin(), pos)</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position before which the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted before the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full, the last element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">end()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">end()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T()</code> throws. Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws. <a href="../circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t" target="_top">Exceptions of move_if_noexcept(T&amp;)</a>. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm4077-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert <code class="computeroutput">n</code> copies of the <code class="computeroutput">item</code> before the specified position. <p>



</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operations in the <span class="emphasis"><em>Throws</em></span> section do not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <code class="computeroutput">pos</code>). It also invalidates iterators pointing to the overwritten elements. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">min[capacity(), std::distance(begin(), pos) + n]</code>). </p>
<p>
</p>
<p><b>Example. </b>Consider a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<br>
<br>
 <code class="computeroutput">|1|2|3|4| | |</code><br>
 <code class="computeroutput">p ___^</code><br>
<br>
After inserting 5 elements before the position <code class="computeroutput">p</code>:<br>
<br>
 <code class="computeroutput">rinsert(p, (size_t)5, 0);</code><br>
<br>
actually only 4 elements get inserted and elements <code class="computeroutput">3</code> and <code class="computeroutput">4</code> are overwritten. This is due to the fact the rinsert operation preserves the capacity. After insertion the internal buffer looks like this:<br>
<br>
<code class="computeroutput">|1|2|0|0|0|0|</code><br>
 <br>
For comparison if the capacity would not be preserved the internal buffer would then result in <code class="computeroutput">|1|2|0|0|0|0|0|3|4|</code>. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element whose copies will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of <code class="computeroutput">item</code>s the to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code>s will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The number of <code class="computeroutput">min[n, (end() - pos) + reserve()]</code> elements will be inserted before the position <code class="computeroutput">pos</code>.<br>
The number of <code class="computeroutput">min[end() - pos, max[0, n - reserve()]]</code> elements will be overwritten at the end of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>.<br>
(See <span class="emphasis"><em>Example</em></span> for the explanation.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. <a href="../circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t" target="_top">Exceptions of move_if_noexcept(T&amp;)</a>. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm4165-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>Insert the range <code class="computeroutput">[first, last)</code> before the specified position. <p>



</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operations in the <span class="emphasis"><em>Throws</em></span> section do not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <code class="computeroutput">pos</code>). It also invalidates iterators pointing to the overwritten elements. </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">[std::distance(begin(), pos) + std::distance(first, last)]</code>; in <code class="computeroutput">min[capacity(), std::distance(begin(), pos) + std::distance(first, last)]</code> if the <code class="computeroutput">InputIterator</code> is a <a href="https://www.boost.org/sgi/stl/RandomAccessIterator.html" target="_top">RandomAccessIterator</a>). </p>
<p>
</p>
<p><b>Example. </b>Consider a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<br>
<br>
 <code class="computeroutput">|1|2|3|4| | |</code><br>
 <code class="computeroutput">p ___^</code><br>
<br>
After inserting a range of elements before the position <code class="computeroutput">p</code>:<br>
<br>
 <code class="computeroutput">int array[] = { 5, 6, 7, 8, 9 };</code><br>
<code class="computeroutput">insert(p, array, array + 5);</code><br>
<br>
 actually only elements <code class="computeroutput">5</code>, <code class="computeroutput">6</code>, <code class="computeroutput">7</code> and <code class="computeroutput">8</code> from the specified range get inserted and elements <code class="computeroutput">3</code> and <code class="computeroutput">4</code> are overwritten. This is due to the fact the rinsert operation preserves the capacity. After insertion the internal buffer looks like this:<br>
<br>
<code class="computeroutput">|1|2|5|6|7|8|</code><br>
<br>
For comparison if the capacity would not be preserved the internal buffer would then result in <code class="computeroutput">|1|2|5|6|7|8|9|3|4|</code>. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the range will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> or its end.<br>
 Valid range <code class="computeroutput">[first, last)</code> where <code class="computeroutput">first</code> and <code class="computeroutput">last</code> meet the requirements of an <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>Elements from the range <code class="computeroutput">[first, last - max[0, distance(first, last) - (end() - pos) - reserve()])</code> will be inserted before the position <code class="computeroutput">pos</code>.<br>
The number of <code class="computeroutput">min[end() - pos, max[0, distance(first, last) - reserve()]]</code> elements will be overwritten at the end of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>.<br>
(See <span class="emphasis"><em>Example</em></span> for the explanation.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws if the <code class="computeroutput">InputIterator</code> is not a move iterator. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws if the <code class="computeroutput">InputIterator</code> is not a move iterator. Whatever <code class="computeroutput">T::T(T&amp;&amp;)</code> throws if the <code class="computeroutput">InputIterator</code> is a move iterator. Whatever <code class="computeroutput">T::operator = (T&amp;&amp;)</code> throws if the <code class="computeroutput">InputIterator</code> is a move iterator. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm4271-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">)</span><span class="special">;</span></pre>Remove an element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the erased element and iterators pointing to the elements behind the erased element (towards the end; except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(pos, end())</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code>, <code class="computeroutput">erase_begin(size_type)</code>, <code class="computeroutput">erase_end(size_type)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator pointing at the element to be removed. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> (but not an <code class="computeroutput">end()</code>). </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The element at the position <code class="computeroutput">pos</code> is removed. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the first element remaining beyond the removed element or <code class="computeroutput">end()</code> if no such element exists. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>&lt;a href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm4316-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">iterator</span> first<span class="special">,</span> <span class="identifier">iterator</span> last<span class="special">)</span><span class="special">;</span></pre>Erase the range <code class="computeroutput">[first, last)</code>. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the erased elements and iterators pointing to the elements behind the erased range (towards the end; except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(first, end())</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code>, <code class="computeroutput">erase_begin(size_type)</code>, <code class="computeroutput">erase_end(size_type)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be removed. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be removed. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The elements from the range <code class="computeroutput">[first, last)</code> are removed. (If <code class="computeroutput">first == last</code> nothing is removed.) </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the first element remaining beyond the removed elements or <code class="computeroutput">end()</code> if no such element exists. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>&lt;a href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm4364-bb"></a><span class="identifier">rerase</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">)</span><span class="special">;</span></pre>Remove an element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the erased element and iterators pointing to the elements in front of the erased element (towards the beginning). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(begin(), pos)</code>). </p>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>This method is symmetric to the <code class="computeroutput">erase(iterator)</code> method and is more effective than <code class="computeroutput">erase(iterator)</code> if the iterator <code class="computeroutput">pos</code> is close to the beginning of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. (See the <span class="emphasis"><em>Complexity</em></span>.) </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code>, <code class="computeroutput">erase_begin(size_type)</code>, <code class="computeroutput">erase_end(size_type)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator pointing at the element to be removed. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> (but not an <code class="computeroutput">end()</code>). </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The element at the position <code class="computeroutput">pos</code> is removed. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the first element remaining in front of the removed element or <code class="computeroutput">begin()</code> if no such element exists. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>&lt;a href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm4416-bb"></a><span class="identifier">rerase</span><span class="special">(</span><span class="identifier">iterator</span> first<span class="special">,</span> <span class="identifier">iterator</span> last<span class="special">)</span><span class="special">;</span></pre>Erase the range <code class="computeroutput">[first, last)</code>. <p>




</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the erased elements and iterators pointing to the elements in front of the erased range (towards the beginning). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(begin(), last)</code>). </p>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>This method is symmetric to the <code class="computeroutput">erase(iterator, iterator)</code> method and is more effective than <code class="computeroutput">erase(iterator, iterator)</code> if <code class="computeroutput">std::distance(begin(), first)</code> is lower that <code class="computeroutput">std::distance(last, end())</code>. </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">erase_begin(size_type)</code>, <code class="computeroutput">erase_end(size_type)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be removed. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be removed. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The elements from the range <code class="computeroutput">[first, last)</code> are removed. (If <code class="computeroutput">first == last</code> nothing is removed.) </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the first element remaining in front of the removed elements or <code class="computeroutput">begin()</code> if no such element exists. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>&lt;a href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm4469-bb"></a><span class="identifier">erase_begin</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>Remove first <code class="computeroutput">n</code> elements (with constant complexity for scalar types). <p>



</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. (I.e. no throw in case of scalars.) </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the first <code class="computeroutput">n</code> erased elements. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in <code class="computeroutput">n</code>) for scalar types; linear for other types. </p>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>This method has been specially designed for types which do not require an explicit destructruction (e.g. integer, float or a pointer). For these scalar types a call to a destructor is not required which makes it possible to implement the "erase from beginning" operation with a constant complexity. For non-sacalar types the complexity is linear (hence the explicit destruction is needed) and the implementation is actually equivalent to <code class="computeroutput">rerase(begin(), begin() + n)</code>. </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code>, <code class="computeroutput">erase_end(size_type)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of elements to be removed. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">n &lt;= size()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">n</code> elements at the beginning of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> will be removed. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>&lt;a href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm4514-bb"></a><span class="identifier">erase_end</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>Remove last <code class="computeroutput">n</code> elements (with constant complexity for scalar types). <p>



</p>
<p><b>Exception Safety. </b>Basic; no-throw if the operation in the <span class="emphasis"><em>Throws</em></span> section does not throw anything. (I.e. no throw in case of scalars.) </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates iterators pointing to the last <code class="computeroutput">n</code> erased elements. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in <code class="computeroutput">n</code>) for scalar types; linear for other types. </p>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>This method has been specially designed for types which do not require an explicit destructruction (e.g. integer, float or a pointer). For these scalar types a call to a destructor is not required which makes it possible to implement the "erase from end" operation with a constant complexity. For non-sacalar types the complexity is linear (hence the explicit destruction is needed) and the implementation is actually equivalent to <code class="computeroutput">erase(end() - n, end())</code>. </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code>, <code class="computeroutput">erase_begin(size_type)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of elements to be removed. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">n &lt;= size()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">n</code> elements at the end of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code> will be removed. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>&lt;a href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm4559-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Remove all stored elements from the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>) for scalar types; linear for other types. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">~circular_buffer()</code>, <code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code>, <code class="computeroutput">erase_begin(size_type)</code>, <code class="computeroutput">erase_end(size_type)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">size() == 0</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2003-2013 Jan Gaspar<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../BOOST_CB_ENABLE_DEBUG.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer.base_hpp"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="operator___idm4981.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
