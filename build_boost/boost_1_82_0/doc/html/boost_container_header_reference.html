<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Boost.Container Header Reference</title>
<link rel="stylesheet" href="../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="container.html" title="Chapter 8. Boost.Container">
<link rel="prev" href="container/index.html" title="Indexes">
<link rel="next" href="boost/container/adaptive_pool.html" title="Class template adaptive_pool">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../boost.png"></td>
<td align="center"><a href="../../index.html">Home</a></td>
<td align="center"><a href="../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="container/index.html"><img src="../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="container.html"><img src="../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="index.html"><img src="../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost/container/adaptive_pool.html"><img src="../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_container_header_reference"></a>Boost.Container Header Reference</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.adaptive_pool_hpp">Header &lt;boost/container/adaptive_pool.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.allocator_hpp">Header &lt;boost/container/allocator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.allocator_traits_hpp">Header &lt;boost/container/allocator_traits.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.container_fwd_hpp">Header &lt;boost/container/container_fwd.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.deque_hpp">Header &lt;boost/container/deque.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.deque_hpp">Header &lt;boost/container/pmr/deque.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.devector_hpp">Header &lt;boost/container/devector.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.devector_hpp">Header &lt;boost/container/pmr/devector.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.flat_map_hpp">Header &lt;boost/container/flat_map.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.flat_map_hpp">Header &lt;boost/container/pmr/flat_map.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.flat_set_hpp">Header &lt;boost/container/flat_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.flat_set_hpp">Header &lt;boost/container/pmr/flat_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.list_hpp">Header &lt;boost/container/list.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.list_hpp">Header &lt;boost/container/pmr/list.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.map_hpp">Header &lt;boost/container/map.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.map_hpp">Header &lt;boost/container/pmr/map.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.new_allocator_hpp">Header &lt;boost/container/new_allocator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.node_allocator_hpp">Header &lt;boost/container/node_allocator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.node_handle_hpp">Header &lt;boost/container/node_handle.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.options_hpp">Header &lt;boost/container/options.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.global_resource_hpp">Header &lt;boost/container/pmr/global_resource.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.memory_resource_hpp">Header &lt;boost/container/pmr/memory_resource.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.monotonic_buffer_resource_hpp">Header &lt;boost/container/pmr/monotonic_buffer_resource.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.polymorphic_allocator_hpp">Header &lt;boost/container/pmr/polymorphic_allocator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.pool_options_hpp">Header &lt;boost/container/pmr/pool_options.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.resource_adaptor_hpp">Header &lt;boost/container/pmr/resource_adaptor.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.synchronized_pool_resource_hpp">Header &lt;boost/container/pmr/synchronized_pool_resource.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.unsynchronized_pool_resource_hpp">Header &lt;boost/container/pmr/unsynchronized_pool_resource.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.scoped_allocator_hpp">Header &lt;boost/container/scoped_allocator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.scoped_allocator_fwd_hpp">Header &lt;boost/container/scoped_allocator_fwd.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.set_hpp">Header &lt;boost/container/set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.set_hpp">Header &lt;boost/container/pmr/set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.slist_hpp">Header &lt;boost/container/slist.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.slist_hpp">Header &lt;boost/container/pmr/slist.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.small_vector_hpp">Header &lt;boost/container/small_vector.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.small_vector_hpp">Header &lt;boost/container/pmr/small_vector.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.stable_vector_hpp">Header &lt;boost/container/stable_vector.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.stable_vector_hpp">Header &lt;boost/container/pmr/stable_vector.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.static_vector_hpp">Header &lt;boost/container/static_vector.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.string_hpp">Header &lt;boost/container/string.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.string_hpp">Header &lt;boost/container/pmr/string.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.throw_exception_hpp">Header &lt;boost/container/throw_exception.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.uses_allocator_hpp">Header &lt;boost/container/uses_allocator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.uses_allocator_fwd_hpp">Header &lt;boost/container/uses_allocator_fwd.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.vector_hpp">Header &lt;boost/container/vector.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="boost_container_header_reference.html#header.boost.container.pmr.vector_hpp">Header &lt;boost/container/pmr/vector.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.adaptive_pool_hpp"></a>Header &lt;<a href="../../boost/container/adaptive_pool.hpp" target="_top">boost/container/adaptive_pool.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> NodesPerBlock <span class="special">=</span> <span class="identifier">ADP_nodes_per_block</span><span class="special">,</span> 
             <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> MaxFreeBlocks <span class="special">=</span> <span class="identifier">ADP_max_free_blocks</span><span class="special">,</span> 
             <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> OverheadPercent <span class="special">=</span> <span class="identifier">ADP_overhead_percent</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/adaptive_pool.html" title="Class template adaptive_pool">adaptive_pool</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> NodesPerBlock <span class="special">=</span> <span class="identifier">ADP_nodes_per_block</span><span class="special">,</span> 
             <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> MaxFreeBlocks <span class="special">=</span> <span class="identifier">ADP_max_free_blocks</span><span class="special">,</span> 
             <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> OverheadPercent <span class="special">=</span> <span class="identifier">ADP_overhead_percent</span><span class="special">,</span> 
             <span class="keyword">unsigned</span> Version <span class="special">=</span> <span class="number">2</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/private_adaptive_pool.html" title="Class template private_adaptive_pool">private_adaptive_pool</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.allocator_hpp"></a>Header &lt;<a href="../../boost/container/allocator.hpp" target="_top">boost/container/allocator.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">unsigned</span> Version <span class="special">=</span> <span class="number">2</span><span class="special">,</span> 
             <span class="keyword">unsigned</span> <span class="keyword">int</span> AllocationDisableMask <span class="special">=</span> <span class="number">0</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/allocator.html" title="Class template allocator">allocator</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.allocator_traits_hpp"></a>Header &lt;<a href="../../boost/container/allocator_traits.hpp" target="_top">boost/container/allocator_traits.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Allocator<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/allocator_traits.html" title="Struct template allocator_traits">allocator_traits</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.container_fwd_hpp"></a>Header &lt;<a href="../../boost/container/container_fwd.hpp" target="_top">boost/container/container_fwd.hpp</a>&gt;</h3></div></div></div>
<p>This header file forward declares the following containers:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p><a class="link" href="boost/container/vector.html" title="Class template vector">boost::container::vector</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/stable_vector.html" title="Class template stable_vector">boost::container::stable_vector</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/static_vector.html" title="Class template static_vector">boost::container::static_vector</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/small_vector_base.html" title="Class template small_vector_base">boost::container::small_vector_base</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/small_vector.html" title="Class template small_vector">boost::container::small_vector</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/devector.html" title="Class template devector">boost::container::devector</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/slist.html" title="Class template slist">boost::container::slist</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/list.html" title="Class template list">boost::container::list</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/set.html" title="Class template set">boost::container::set</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/multiset.html" title="Class template multiset">boost::container::multiset</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/map.html" title="Class template map">boost::container::map</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/multimap.html" title="Class template multimap">boost::container::multimap</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/flat_set.html" title="Class template flat_set">boost::container::flat_set</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/flat_multiset.html" title="Class template flat_multiset">boost::container::flat_multiset</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/flat_map.html" title="Class template flat_map">boost::container::flat_map</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/flat_multimap.html" title="Class template flat_multimap">boost::container::flat_multimap</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/basic_string.html" title="Class template basic_string">boost::container::basic_string</a></p></li>
<li class="listitem"><p>boost::container::string</p></li>
<li class="listitem"><p>boost::container::wstring</p></li>
</ul></div>
<p>
</p>
<p>Forward declares the following allocators:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p><a class="link" href="boost/container/allocator.html" title="Class template allocator">boost::container::allocator</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/node_allocator.html" title="Class template node_allocator">boost::container::node_allocator</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/adaptive_pool.html" title="Class template adaptive_pool">boost::container::adaptive_pool</a></p></li>
</ul></div>
<p>
</p>
<p>Forward declares the following polymorphic resource classes:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p><a class="link" href="boost/container/pmr/memory_resource.html" title="Class memory_resource">boost::container::pmr::memory_resource</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">boost::container::pmr::polymorphic_allocator</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/pmr/monotonic_buffer_resource.html" title="Class monotonic_buffer_resource">boost::container::pmr::monotonic_buffer_resource</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/pmr/pool_options.html" title="Struct pool_options">boost::container::pmr::pool_options</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/pmr/unsynchronized_po_idm19691.html" title="Class unsynchronized_pool_resource">boost::container::pmr::unsynchronized_pool_resource</a></p></li>
<li class="listitem"><p><a class="link" href="boost/container/pmr/synchronized_pool_resource.html" title="Class synchronized_pool_resource">boost::container::pmr::synchronized_pool_resource</a></p></li>
</ul></div>
<p>
</p>
<p>And finally it defines the following types </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="boost/container/default_init_t.html" title="Struct default_init_t">default_init_t</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">;</span>

    <span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a> <a class="link" href="boost/container/ordered_range.html" title="Global ordered_range">ordered_range</a><span class="special">;</span>
    <span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a> <a class="link" href="boost/container/ordered_unique_range.html" title="Global ordered_unique_range">ordered_unique_range</a><span class="special">;</span>
    <span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="boost/container/default_init_t.html" title="Struct default_init_t">default_init_t</a> <a class="link" href="boost/container/default_init.html" title="Global default_init">default_init</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.deque_hpp"></a>Header &lt;<a href="../../boost/container/deque.hpp" target="_top">boost/container/deque.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span><span class="special">,</span> <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/deque.html" title="Class template deque">deque</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span>  <a name="boost.container.deque_idm6241"></a><span class="identifier">deque</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
       <a name="boost.container.deque_idm6249"></a><span class="identifier">deque</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.deque_hpp"></a>Header &lt;<a href="../../boost/container/pmr/deque.hpp" target="_top">boost/container/pmr/deque.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/deque_of.html" title="Struct template deque_of">deque_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/deque.html" title="Class template deque">boost::container::deque</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.deque"></a><span class="identifier">deque</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.devector_hpp"></a>Header &lt;<a href="../../boost/container/devector.hpp" target="_top">boost/container/devector.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> A <span class="special">=</span> <span class="keyword">void</span><span class="special">,</span> <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/devector.html" title="Class template devector">devector</a><span class="special">;</span>

    <span class="keyword">struct</span> <a class="link" href="boost/container/reserve_only_tag_t.html" title="Struct reserve_only_tag_t">reserve_only_tag_t</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="boost/container/reserve_uninitialized_t.html" title="Struct reserve_uninitialized_t">reserve_uninitialized_t</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="boost/container/review_implementation_t.html" title="Struct review_implementation_t">review_implementation_t</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.devector_hpp"></a>Header &lt;<a href="../../boost/container/pmr/devector.hpp" target="_top">boost/container/pmr/devector.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> GrowthPolicy <span class="special">=</span> <a class="link" href="boost/container/growth_factor_60.html" title="Struct growth_factor_60">growth_factor_60</a><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/devector_of.html" title="Struct template devector_of">devector_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/devector.html" title="Class template devector">boost::container::devector</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">GrowthPolicy</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.devector"></a><span class="identifier">devector</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.flat_map_hpp"></a>Header &lt;<a href="../../boost/container/flat_map.hpp" target="_top">boost/container/flat_map.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> AllocatorOrContainer <span class="special">=</span> <a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/flat_map.html" title="Class template flat_map">flat_map</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> AllocatorOrContainer <span class="special">=</span> <a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/flat_multimap.html" title="Class template flat_multimap">flat_multimap</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span>  <a name="boost.container.flat_map_idm10861"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.flat_map_idm10869"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.flat_map_idm10880"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.flat_map_idm10898"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.flat_map_idm10909"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.flat_map_idm10923"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.flat_multimap_idm10944"></a><span class="identifier">flat_multimap</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.flat_multimap_idm10952"></a><span class="identifier">flat_multimap</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.flat_multimap_idm10963"></a><span class="identifier">flat_multimap</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                     <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.flat_multimap_idm10981"></a><span class="identifier">flat_multimap</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.flat_multimap_idm10992"></a><span class="identifier">flat_multimap</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                     <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.flat_multimap_idm11006"></a><span class="identifier">flat_multimap</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                     <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.flat_map_hpp"></a>Header &lt;<a href="../../boost/container/pmr/flat_map.hpp" target="_top">boost/container/pmr/flat_map.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/flat_map_of.html" title="Struct template flat_map_of">flat_map_of</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/flat_multimap_of.html" title="Struct template flat_multimap_of">flat_multimap_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/flat_map.html" title="Class template flat_map">boost::container::flat_map</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.flat_map"></a><span class="identifier">flat_map</span><span class="special">;</span>
      <span class="keyword">typedef</span> <a class="link" href="boost/container/flat_multimap.html" title="Class template flat_multimap">boost::container::flat_multimap</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.flat_multimap"></a><span class="identifier">flat_multimap</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.flat_set_hpp"></a>Header &lt;<a href="../../boost/container/flat_set.hpp" target="_top">boost/container/flat_set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> AllocatorOrContainer <span class="special">=</span> <a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/flat_multiset.html" title="Class template flat_multiset">flat_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> AllocatorOrContainer <span class="special">=</span> <a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/flat_set.html" title="Class template flat_set">flat_set</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span>  <a name="boost.container.flat_set_idm13587"></a><span class="identifier">flat_set</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.flat_set_idm13595"></a><span class="identifier">flat_set</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.flat_set_idm13606"></a><span class="identifier">flat_set</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.flat_set_idm13624"></a><span class="identifier">flat_set</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.flat_set_idm13635"></a><span class="identifier">flat_set</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.flat_set_idm13649"></a><span class="identifier">flat_set</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.flat_multiset_idm13670"></a><span class="identifier">flat_multiset</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.flat_multiset_idm13678"></a><span class="identifier">flat_multiset</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.flat_multiset_idm13689"></a><span class="identifier">flat_multiset</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                     <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.flat_multiset_idm13707"></a><span class="identifier">flat_multiset</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.flat_multiset_idm13718"></a><span class="identifier">flat_multiset</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                     <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.flat_multiset_idm13732"></a><span class="identifier">flat_multiset</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                     <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.flat_set_hpp"></a>Header &lt;<a href="../../boost/container/pmr/flat_set.hpp" target="_top">boost/container/pmr/flat_set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/flat_multiset_of.html" title="Struct template flat_multiset_of">flat_multiset_of</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/flat_set_of.html" title="Struct template flat_set_of">flat_set_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/flat_set.html" title="Class template flat_set">boost::container::flat_set</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">Key</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.flat_set"></a><span class="identifier">flat_set</span><span class="special">;</span>
      <span class="keyword">typedef</span> <a class="link" href="boost/container/flat_multiset.html" title="Class template flat_multiset">boost::container::flat_multiset</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">Key</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.flat_multiset"></a><span class="identifier">flat_multiset</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.list_hpp"></a>Header &lt;<a href="../../boost/container/list.hpp" target="_top">boost/container/list.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="boost/container/list.html" title="Class template list">list</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span>  <a name="boost.container.list_idm14973"></a><span class="identifier">list</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> ValueAllocator<span class="special">&gt;</span> 
       <a name="boost.container.list_idm14981"></a><span class="identifier">list</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">ValueAllocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.list_hpp"></a>Header &lt;<a href="../../boost/container/pmr/list.hpp" target="_top">boost/container/pmr/list.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/list_of.html" title="Struct template list_of">list_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/list.html" title="Class template list">boost::container::list</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.list"></a><span class="identifier">list</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.map_hpp"></a>Header &lt;<a href="../../boost/container/map.hpp" target="_top">boost/container/map.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span><span class="special">,</span> 
             <span class="keyword">typename</span> Options <span class="special">=</span> <span class="identifier">tree_assoc_defaults</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/map.html" title="Class template map">map</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> Allocator <span class="special">=</span> <a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="keyword">const</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> Options <span class="special">=</span> <span class="identifier">tree_assoc_defaults</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/multimap.html" title="Class template multimap">multimap</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span>  <a name="boost.container.map_idm17626"></a><span class="identifier">map</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.map_idm17634"></a><span class="identifier">map</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.map_idm17645"></a><span class="identifier">map</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.map_idm17663"></a><span class="identifier">map</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.map_idm17674"></a><span class="identifier">map</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
           <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.map_idm17688"></a><span class="identifier">map</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
           <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span>  <a name="boost.container.multimap_idm17709"></a><span class="identifier">multimap</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.multimap_idm17717"></a><span class="identifier">multimap</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.multimap_idm17728"></a><span class="identifier">multimap</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.multimap_idm17746"></a><span class="identifier">multimap</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.multimap_idm17757"></a><span class="identifier">multimap</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.multimap_idm17771"></a><span class="identifier">multimap</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.map_hpp"></a>Header &lt;<a href="../../boost/container/pmr/map.hpp" target="_top">boost/container/pmr/map.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
               <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/map_of.html" title="Struct template map_of">map_of</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
               <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/multimap_of.html" title="Struct template multimap_of">multimap_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/map.html" title="Class template map">boost::container::map</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="keyword">const</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">Options</span> <span class="special">&gt;</span> <a name="boost.container.pmr.map"></a><span class="identifier">map</span><span class="special">;</span>
      <span class="keyword">typedef</span> <a class="link" href="boost/container/multimap.html" title="Class template multimap">boost::container::multimap</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="keyword">const</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">Options</span> <span class="special">&gt;</span> <a name="boost.container.pmr.multimap"></a><span class="identifier">multimap</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.new_allocator_hpp"></a>Header &lt;<a href="../../boost/container/new_allocator.hpp" target="_top">boost/container/new_allocator.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="boost/container/new_allocator_voi_idm17950.html" title="Class new_allocator&lt;void&gt;">new_allocator</a><span class="special">&lt;</span><span class="keyword">void</span><span class="special">&gt;</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.node_allocator_hpp"></a>Header &lt;<a href="../../boost/container/node_allocator.hpp" target="_top">boost/container/node_allocator.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> 
             <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> NodesPerBlock <span class="special">=</span> <span class="identifier">NodeAlloc_nodes_per_block</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/node_allocator.html" title="Class template node_allocator">node_allocator</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.node_handle_hpp"></a>Header &lt;<a href="../../boost/container/node_handle.hpp" target="_top">boost/container/node_handle.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">,</span> <span class="keyword">typename</span> NodeType<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="boost/container/insert_return_type_base.html" title="Struct template insert_return_type_base">insert_return_type_base</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeAllocator<span class="special">,</span> <span class="keyword">typename</span> KeyMapped <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/node_handle.html" title="Class template node_handle">node_handle</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.options_hpp"></a>Header &lt;<a href="../../boost/container/options.hpp" target="_top">boost/container/options.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> BlockBytes<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/block_bytes.html" title="Struct template block_bytes">block_bytes</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> BlockSize<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/block_size.html" title="Struct template block_size">block_size</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/cache_begin.html" title="Struct template cache_begin">cache_begin</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/deque_options.html" title="Struct template deque_options">deque_options</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/devector_options.html" title="Struct template devector_options">devector_options</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/fastmod_buckets.html" title="Struct template fastmod_buckets">fastmod_buckets</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> GrowthFactor<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/growth_factor.html" title="Struct template growth_factor">growth_factor</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="boost/container/growth_factor_100.html" title="Struct growth_factor_100">growth_factor_100</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="boost/container/growth_factor_50.html" title="Struct growth_factor_50">growth_factor_50</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="boost/container/growth_factor_60.html" title="Struct growth_factor_60">growth_factor_60</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/hash_assoc_options.html" title="Struct template hash_assoc_options">hash_assoc_options</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> Alignment<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/inplace_alignment.html" title="Struct template inplace_alignment">inplace_alignment</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/linear_buckets.html" title="Struct template linear_buckets">linear_buckets</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/optimize_size.html" title="Struct template optimize_size">optimize_size</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/small_vector_options.html" title="Struct template small_vector_options">small_vector_options</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/static_vector_options.html" title="Struct template static_vector_options">static_vector_options</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/store_hash.html" title="Struct template store_hash">store_hash</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StoredSizeType<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/stored_size.html" title="Struct template stored_size">stored_size</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> ThrowOnOverflow<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/throw_on_overflow.html" title="Struct template throw_on_overflow">throw_on_overflow</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/tree_assoc_options.html" title="Struct template tree_assoc_options">tree_assoc_options</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">tree_type_enum</span> TreeType<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/tree_type.html" title="Struct template tree_type">tree_type</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/vector_options.html" title="Struct template vector_options">vector_options</a><span class="special">;</span>

    <span class="keyword">enum</span> <a class="link" href="boost/container/tree_type_enum.html" title="Type tree_type_enum">tree_type_enum</a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <a class="link" href="boost/container/tree_assoc_options.html" title="Struct template tree_assoc_options">boost::container::tree_assoc_options</a><span class="special">&lt;</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a class="link" href="boost/container/tree_assoc_options_t.html" title="Type definition tree_assoc_options_t"><span class="identifier">tree_assoc_options_t</span></a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <a class="link" href="boost/container/hash_assoc_options.html" title="Struct template hash_assoc_options">boost::container::hash_assoc_options</a><span class="special">&lt;</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a class="link" href="boost/container/hash_assoc_options_t.html" title="Type definition hash_assoc_options_t"><span class="identifier">hash_assoc_options_t</span></a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <a class="link" href="boost/container/vector_options.html" title="Struct template vector_options">boost::container::vector_options</a><span class="special">&lt;</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a class="link" href="boost/container/vector_options_t.html" title="Type definition vector_options_t"><span class="identifier">vector_options_t</span></a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <a class="link" href="boost/container/small_vector_options.html" title="Struct template small_vector_options">boost::container::small_vector_options</a><span class="special">&lt;</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a class="link" href="boost/container/small_vector_options_t.html" title="Type definition small_vector_options_t"><span class="identifier">small_vector_options_t</span></a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <a class="link" href="boost/container/static_vector_options.html" title="Struct template static_vector_options">boost::container::static_vector_options</a><span class="special">&lt;</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a class="link" href="boost/container/static_vector_options_t.html" title="Type definition static_vector_options_t"><span class="identifier">static_vector_options_t</span></a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <a class="link" href="boost/container/devector_options.html" title="Struct template devector_options">boost::container::devector_options</a><span class="special">&lt;</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a class="link" href="boost/container/devector_options_t.html" title="Type definition devector_options_t"><span class="identifier">devector_options_t</span></a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <a class="link" href="boost/container/deque_options.html" title="Struct template deque_options">boost::container::deque_options</a><span class="special">&lt;</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a class="link" href="boost/container/deque_options_t.html" title="Type definition deque_options_t"><span class="identifier">deque_options_t</span></a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.global_resource_hpp"></a>Header &lt;<a href="../../boost/container/pmr/global_resource.hpp" target="_top">boost/container/pmr/global_resource.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <a class="link" href="boost/container/pmr/memory_resource.html" title="Class memory_resource">memory_resource</a> <span class="special">*</span> <a class="link" href="boost/container/pmr/new_delete_resource.html" title="Function new_delete_resource"><span class="identifier">new_delete_resource</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
      <a class="link" href="boost/container/pmr/memory_resource.html" title="Class memory_resource">memory_resource</a> <span class="special">*</span> <a class="link" href="boost/container/pmr/null_memory_resource.html" title="Function null_memory_resource"><span class="identifier">null_memory_resource</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
      <a class="link" href="boost/container/pmr/memory_resource.html" title="Class memory_resource">memory_resource</a> <span class="special">*</span> <a class="link" href="boost/container/pmr/set_default_resource.html" title="Function set_default_resource"><span class="identifier">set_default_resource</span></a><span class="special">(</span><a class="link" href="boost/container/pmr/memory_resource.html" title="Class memory_resource">memory_resource</a> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
      <a class="link" href="boost/container/pmr/memory_resource.html" title="Class memory_resource">memory_resource</a> <span class="special">*</span> <a class="link" href="boost/container/pmr/get_default_resource.html" title="Function get_default_resource"><span class="identifier">get_default_resource</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.memory_resource_hpp"></a>Header &lt;<a href="../../boost/container/pmr/memory_resource.hpp" target="_top">boost/container/pmr/memory_resource.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">class</span> <a class="link" href="boost/container/pmr/memory_resource.html" title="Class memory_resource">memory_resource</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.monotonic_buffer_resource_hpp"></a>Header &lt;<a href="../../boost/container/pmr/monotonic_buffer_resource.hpp" target="_top">boost/container/pmr/monotonic_buffer_resource.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">class</span> <a class="link" href="boost/container/pmr/monotonic_buffer_resource.html" title="Class monotonic_buffer_resource">monotonic_buffer_resource</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.polymorphic_allocator_hpp"></a>Header &lt;<a href="../../boost/container/pmr/polymorphic_allocator.hpp" target="_top">boost/container/pmr/polymorphic_allocator.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">&gt;</span> 
        <span class="keyword">bool</span> <a class="link" href="boost/container/pmr/operator__.html" title="Function template operator=="><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T1</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                        <span class="keyword">const</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T2</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">&gt;</span> 
        <span class="keyword">bool</span> <a class="link" href="boost/container/pmr/operator__.html" title="Function template operator!="><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T1</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                        <span class="keyword">const</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T2</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.pool_options_hpp"></a>Header &lt;<a href="../../boost/container/pmr/pool_options.hpp" target="_top">boost/container/pmr/pool_options.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/pool_options.html" title="Struct pool_options">pool_options</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.resource_adaptor_hpp"></a>Header &lt;<a href="../../boost/container/pmr/resource_adaptor.hpp" target="_top">boost/container/pmr/resource_adaptor.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Allocator<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="boost/container/pmr/resource_adaptor_imp.html" title="Class template resource_adaptor_imp">resource_adaptor_imp</a><span class="special">;</span>
      <span class="keyword">typedef</span> <a class="link" href="boost/container/pmr/resource_adaptor_imp.html" title="Class template resource_adaptor_imp">resource_adaptor_imp</a><span class="special">&lt;</span> <span class="keyword">typename</span> <a class="link" href="boost/container/allocator_traits.html" title="Struct template allocator_traits">allocator_traits</a><span class="special">&lt;</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="keyword">template</span> <span class="identifier">rebind_alloc</span><span class="special">&lt;</span> <span class="keyword">char</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a class="link" href="boost/container/pmr/resource_adaptor.html" title="Type definition resource_adaptor"><span class="identifier">resource_adaptor</span></a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr_dtl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/pmr_dtl/max_allocator_alignment.html" title="Struct template max_allocator_alignment">max_allocator_alignment</a><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr_dtl/max_allocator_ali_idm19474.html" title="Struct template max_allocator_alignment&lt;::boost::container::new_allocator&lt; T &gt;&gt;">max_allocator_alignment</a><span class="special">&lt;</span><span class="special">::</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">container</span><span class="special">::</span><span class="identifier">new_allocator</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/pmr_dtl/max_allocator_ali_idm19481.html" title="Struct template max_allocator_alignment&lt;std::allocator&lt; T &gt;&gt;">max_allocator_alignment</a><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.synchronized_pool_resource_hpp"></a>Header &lt;<a href="../../boost/container/pmr/synchronized_pool_resource.hpp" target="_top">boost/container/pmr/synchronized_pool_resource.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">class</span> <a class="link" href="boost/container/pmr/synchronized_pool_resource.html" title="Class synchronized_pool_resource">synchronized_pool_resource</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.unsynchronized_pool_resource_hpp"></a>Header &lt;<a href="../../boost/container/pmr/unsynchronized_pool_resource.hpp" target="_top">boost/container/pmr/unsynchronized_pool_resource.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">class</span> <a class="link" href="boost/container/pmr/unsynchronized_po_idm19691.html" title="Class unsynchronized_pool_resource">unsynchronized_pool_resource</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.scoped_allocator_hpp"></a>Header &lt;<a href="../../boost/container/scoped_allocator.hpp" target="_top">boost/container/scoped_allocator.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OuterAlloc<span class="special">,</span> <span class="keyword">typename</span> <span class="special">...</span> InnerAllocs<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/scoped_allocator_adaptor.html" title="Class template scoped_allocator_adaptor">scoped_allocator_adaptor</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OuterA1<span class="special">,</span> <span class="keyword">typename</span> OuterA2<span class="special">,</span> 
             <span class="identifier">BOOST_CONTAINER_SCOPEDALLOC_ALLINNERCLASS</span> <span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator==_idm20192"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/scoped_allocator_adaptor.html" title="Class template scoped_allocator_adaptor">scoped_allocator_adaptor</a><span class="special">&lt;</span> <span class="identifier">OuterA1</span><span class="special">,</span> <span class="identifier">InnerAllocs</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> a<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/scoped_allocator_adaptor.html" title="Class template scoped_allocator_adaptor">scoped_allocator_adaptor</a><span class="special">&lt;</span> <span class="identifier">OuterA2</span><span class="special">,</span> <span class="identifier">InnerAllocs</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> b<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OuterA1<span class="special">,</span> <span class="keyword">typename</span> OuterA2<span class="special">,</span> 
             <span class="identifier">BOOST_CONTAINER_SCOPEDALLOC_ALLINNERCLASS</span> <span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator!=_idm20205"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/scoped_allocator_adaptor.html" title="Class template scoped_allocator_adaptor">scoped_allocator_adaptor</a><span class="special">&lt;</span> <span class="identifier">OuterA1</span><span class="special">,</span> <span class="identifier">InnerAllocs</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> a<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/scoped_allocator_adaptor.html" title="Class template scoped_allocator_adaptor">scoped_allocator_adaptor</a><span class="special">&lt;</span> <span class="identifier">OuterA2</span><span class="special">,</span> <span class="identifier">InnerAllocs</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> b<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.scoped_allocator_fwd_hpp"></a>Header &lt;<a href="../../boost/container/scoped_allocator_fwd.hpp" target="_top">boost/container/scoped_allocator_fwd.hpp</a>&gt;</h3></div></div></div>
<p>This header file forward declares <a class="link" href="boost/container/scoped_allocator_adaptor.html" title="Class template scoped_allocator_adaptor">boost::container::scoped_allocator_adaptor</a> </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.set_hpp"></a>Header &lt;<a href="../../boost/container/set.hpp" target="_top">boost/container/set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> Allocator <span class="special">=</span> <a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> Options <span class="special">=</span> <span class="identifier">tree_assoc_defaults</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/multiset.html" title="Class template multiset">multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> Allocator <span class="special">=</span> <a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/set.html" title="Class template set">set</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span>  <a name="boost.container.set_idm22585"></a><span class="identifier">set</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.set_idm22593"></a><span class="identifier">set</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.set_idm22604"></a><span class="identifier">set</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.set_idm22622"></a><span class="identifier">set</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.set_idm22633"></a><span class="identifier">set</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
           <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.set_idm22647"></a><span class="identifier">set</span><span class="special">(</span><a class="link" href="boost/container/ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
           <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span>  <a name="boost.container.multiset_idm22668"></a><span class="identifier">multiset</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.multiset_idm22676"></a><span class="identifier">multiset</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.multiset_idm22687"></a><span class="identifier">multiset</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.multiset_idm22705"></a><span class="identifier">multiset</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> AllocatorOrCompare<span class="special">&gt;</span> 
       <a name="boost.container.multiset_idm22716"></a><span class="identifier">multiset</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                <span class="identifier">AllocatorOrCompare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_nonallocator_t</span><span class="special">&lt;</span><span class="identifier">Compare</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span>  <span class="special">=</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">require_allocator_t</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
       <a name="boost.container.multiset_idm22730"></a><span class="identifier">multiset</span><span class="special">(</span><a class="link" href="boost/container/ordered_range_t.html" title="Struct ordered_range_t">ordered_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                <span class="identifier">Compare</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.set_hpp"></a>Header &lt;<a href="../../boost/container/pmr/set.hpp" target="_top">boost/container/pmr/set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
               <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/multiset_of.html" title="Struct template multiset_of">multiset_of</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
               <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/set_of.html" title="Struct template set_of">set_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/set.html" title="Class template set">boost::container::set</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">Key</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">Options</span> <span class="special">&gt;</span> <a name="boost.container.pmr.set"></a><span class="identifier">set</span><span class="special">;</span>
      <span class="keyword">typedef</span> <a class="link" href="boost/container/multiset.html" title="Class template multiset">boost::container::multiset</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">Key</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">Options</span> <span class="special">&gt;</span> <a name="boost.container.pmr.multiset"></a><span class="identifier">multiset</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.slist_hpp"></a>Header &lt;<a href="../../boost/container/slist.hpp" target="_top">boost/container/slist.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="boost/container/slist.html" title="Class template slist">slist</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InpIt<span class="special">&gt;</span>  <a name="boost.container.slist_idm24166"></a><span class="identifier">slist</span><span class="special">(</span><span class="identifier">InpIt</span><span class="special">,</span> <span class="identifier">InpIt</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InpIt<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
       <a name="boost.container.slist_idm24174"></a><span class="identifier">slist</span><span class="special">(</span><span class="identifier">InpIt</span><span class="special">,</span> <span class="identifier">InpIt</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span><span class="keyword">namespace</span> <span class="identifier">std</span> <span class="special">{</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> ValueAllocator<span class="special">&gt;</span> 
    <span class="keyword">class</span> <a class="link" href="std/insert_iterator_b_idm24186.html" title="Class template insert_iterator&lt;boost::container::slist&lt; T, ValueAllocator &gt;&gt;">insert_iterator</a><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">container</span><span class="special">::</span><span class="identifier">slist</span><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">ValueAllocator</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.slist_hpp"></a>Header &lt;<a href="../../boost/container/pmr/slist.hpp" target="_top">boost/container/pmr/slist.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/slist_of.html" title="Struct template slist_of">slist_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/slist.html" title="Class template slist">boost::container::slist</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.slist"></a><span class="identifier">slist</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.small_vector_hpp"></a>Header &lt;<a href="../../boost/container/small_vector.hpp" target="_top">boost/container/small_vector.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> N<span class="special">,</span> <span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span><span class="special">,</span> 
             <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/small_vector.html" title="Class template small_vector">small_vector</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> VoidAlloc <span class="special">=</span> <span class="keyword">void</span><span class="special">,</span> <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/small_vector_allocator.html" title="Class template small_vector_allocator">small_vector_allocator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> SecAlloc<span class="special">,</span> <span class="keyword">typename</span> Options<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/small_vector_base.html" title="Class template small_vector_base">small_vector_base</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> N<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> Alignment<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="boost/container/small_vector_storage.html" title="Struct template small_vector_storage">small_vector_storage</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> Alignment<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="boost/container/small_vector_stor_idm24763.html" title="Struct template small_vector_storage&lt;T, 0u, Alignment&gt;">small_vector_storage</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="number">0u</span><span class="special">,</span> <span class="identifier">Alignment</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">dtl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/dtl/get_small_vector_opt.html" title="Struct template get_small_vector_opt">get_small_vector_opt</a><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/dtl/get_small_vector__idm24780.html" title="Struct get_small_vector_opt&lt;void&gt;">get_small_vector_opt</a><span class="special">&lt;</span><span class="keyword">void</span><span class="special">&gt;</span><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/dtl/get_vopt_from_svopt.html" title="Struct template get_vopt_from_svopt">get_vopt_from_svopt</a><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/dtl/get_vopt_from_svo_idm24795.html" title="Struct get_vopt_from_svopt&lt;void&gt;">get_vopt_from_svopt</a><span class="special">&lt;</span><span class="keyword">void</span><span class="special">&gt;</span><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> SecAlloc<span class="special">,</span> <span class="keyword">typename</span> Options<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/dtl/vector_for_small_vector.html" title="Struct template vector_for_small_vector">vector_for_small_vector</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.small_vector_hpp"></a>Header &lt;<a href="../../boost/container/pmr/small_vector.hpp" target="_top">boost/container/pmr/small_vector.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> N<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/small_vector_of.html" title="Struct template small_vector_of">small_vector_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/small_vector.html" title="Class template small_vector">boost::container::small_vector</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">N</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.small_vector"></a><span class="identifier">small_vector</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.stable_vector_hpp"></a>Header &lt;<a href="../../boost/container/stable_vector.hpp" target="_top">boost/container/stable_vector.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="boost/container/stable_vector.html" title="Class template stable_vector">stable_vector</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.stable_vector_idm25867"></a><span class="identifier">stable_vector</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
       <a name="boost.container.stable_vector_idm25875"></a><span class="identifier">stable_vector</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.stable_vector_hpp"></a>Header &lt;<a href="../../boost/container/pmr/stable_vector.hpp" target="_top">boost/container/pmr/stable_vector.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/stable_vector_of.html" title="Struct template stable_vector_of">stable_vector_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/stable_vector.html" title="Class template stable_vector">boost::container::stable_vector</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.stable_vector"></a><span class="identifier">stable_vector</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.static_vector_hpp"></a>Header &lt;<a href="../../boost/container/static_vector.hpp" target="_top">boost/container/static_vector.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> Capacity<span class="special">,</span> <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> V<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C1<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C2<span class="special">,</span> <span class="keyword">typename</span> O1<span class="special">,</span> 
             <span class="keyword">typename</span> O2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="boost/container/operator___idm27527.html" title="Function template operator=="><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C1</span><span class="special">,</span> <span class="identifier">O1</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                      <a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">O2</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> V<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C1<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C2<span class="special">,</span> <span class="keyword">typename</span> O1<span class="special">,</span> 
             <span class="keyword">typename</span> O2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="boost/container/operator___idm27558.html" title="Function template operator!="><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C1</span><span class="special">,</span> <span class="identifier">O1</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                      <a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">O2</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> V<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C1<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C2<span class="special">,</span> <span class="keyword">typename</span> O1<span class="special">,</span> 
             <span class="keyword">typename</span> O2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="boost/container/operator_idm27589.html" title="Function template operator&lt;"><span class="keyword">operator</span><span class="special">&lt;</span></a><span class="special">(</span><a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C1</span><span class="special">,</span> <span class="identifier">O1</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                     <a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">O2</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> V<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C1<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C2<span class="special">,</span> <span class="keyword">typename</span> O1<span class="special">,</span> 
             <span class="keyword">typename</span> O2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="boost/container/operator_idm27620.html" title="Function template operator&gt;"><span class="keyword">operator</span><span class="special">&gt;</span></a><span class="special">(</span><a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C1</span><span class="special">,</span> <span class="identifier">O1</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                     <a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">O2</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> V<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C1<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C2<span class="special">,</span> <span class="keyword">typename</span> O1<span class="special">,</span> 
             <span class="keyword">typename</span> O2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="boost/container/operator___idm27651.html" title="Function template operator&lt;="><span class="keyword">operator</span><span class="special">&lt;=</span></a><span class="special">(</span><a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C1</span><span class="special">,</span> <span class="identifier">O1</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                      <a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">O2</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> V<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C1<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C2<span class="special">,</span> <span class="keyword">typename</span> O1<span class="special">,</span> 
             <span class="keyword">typename</span> O2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="boost/container/operator___idm27682.html" title="Function template operator&gt;="><span class="keyword">operator</span><span class="special">&gt;=</span></a><span class="special">(</span><a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C1</span><span class="special">,</span> <span class="identifier">O1</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                      <a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">O2</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> V<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C1<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> C2<span class="special">,</span> <span class="keyword">typename</span> O1<span class="special">,</span> 
             <span class="keyword">typename</span> O2<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="boost/container/swap_idm27713.html" title="Function template swap"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C1</span><span class="special">,</span> <span class="identifier">O1</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="boost/container/static_vector.html" title="Class template static_vector">static_vector</a><span class="special">&lt;</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">O2</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.string_hpp"></a>Header &lt;<a href="../../boost/container/string.hpp" target="_top">boost/container/string.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span><span class="special">,</span> 
             <span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="keyword">char</span><span class="special">,</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span> <span class="keyword">char</span> <span class="special">&gt;</span><span class="special">,</span><a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span> <span class="keyword">char</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a class="link" href="boost/container/string.html" title="Type definition string"><span class="identifier">string</span></a><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="keyword">wchar_t</span><span class="special">,</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span><span class="special">,</span><a class="link" href="boost/container/new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a class="link" href="boost/container/wstring.html" title="Type definition wstring"><span class="identifier">wstring</span></a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
       <a name="boost.container.basic_string_idm29956"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
       <a name="boost.container.basic_string_idm29964"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> 
      <a name="boost.container.operator+_idm29975"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> 
      <a name="boost.container.operator+_idm29988"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> x<span class="special">,</span> 
                <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> 
      <a name="boost.container.operator+_idm30001"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> x<span class="special">,</span> 
                <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> 
      <a name="boost.container.operator+_idm30014"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> 
      <a name="boost.container.operator+_idm30027"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> 
      <a name="boost.container.operator+_idm30039"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> x<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> 
      <a name="boost.container.operator+_idm30051"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">,</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> 
      <a name="boost.container.operator+_idm30063"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> x<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator==_idm30075"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator==_idm30087"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator==_idm30098"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator==_idm30109"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator==_idm30122"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator!=_idm30135"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator!=_idm30147"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator!=_idm30158"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator!=_idm30169"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator!=_idm30182"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30195"></a><span class="keyword">operator</span><span class="special">&lt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30207"></a><span class="keyword">operator</span><span class="special">&lt;</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30218"></a><span class="keyword">operator</span><span class="special">&lt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30229"></a><span class="keyword">operator</span><span class="special">&lt;</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30242"></a><span class="keyword">operator</span><span class="special">&lt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30255"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30267"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30278"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30289"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_idm30302"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30315"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30327"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30338"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30349"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30362"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30375"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30387"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30398"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30409"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">,</span> 
             <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.container.operator_=_idm30422"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.container.swap_idm30435"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.container.operator_idm30447"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.container.operator_idm30458"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
                 <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.container.getline_idm30469"></a><span class="identifier">getline</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">istream</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> s<span class="special">,</span> 
              <span class="identifier">CharT</span> delim<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.container.getline_idm30482"></a><span class="identifier">getline</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
              <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Ch<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <a name="boost.container.hash_value"></a><span class="identifier">hash_value</span><span class="special">(</span><a class="link" href="boost/container/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">Ch</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span> <span class="identifier">Ch</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> v<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.string_hpp"></a>Header &lt;<a href="../../boost/container/pmr/string.hpp" target="_top">boost/container/pmr/string.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/basic_string_of.html" title="Struct template basic_string_of">basic_string_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/basic_string.html" title="Class template basic_string">boost::container::basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.basic_string"></a><span class="identifier">basic_string</span><span class="special">;</span>
      <span class="keyword">typedef</span> <a class="link" href="boost/container/pmr/basic_string_of.html" title="Struct template basic_string_of">basic_string_of</a><span class="special">&lt;</span> <span class="keyword">char</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a name="boost.container.pmr.string"></a><span class="identifier">string</span><span class="special">;</span>
      <span class="keyword">typedef</span> <a class="link" href="boost/container/pmr/basic_string_of.html" title="Struct template basic_string_of">basic_string_of</a><span class="special">&lt;</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a name="boost.container.pmr.wstring"></a><span class="identifier">wstring</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.throw_exception_hpp"></a>Header &lt;<a href="../../boost/container/throw_exception.hpp" target="_top">boost/container/throw_exception.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="boost/container/bad_alloc.html" title="Class bad_alloc">bad_alloc</a><span class="special">;</span>
    <span class="keyword">class</span> <a class="link" href="boost/container/exception.html" title="Class exception">exception</a><span class="special">;</span>
    <span class="keyword">class</span> <a class="link" href="boost/container/length_error.html" title="Class length_error">length_error</a><span class="special">;</span>
    <span class="keyword">class</span> <a class="link" href="boost/container/logic_error.html" title="Class logic_error">logic_error</a><span class="special">;</span>
    <span class="keyword">class</span> <a class="link" href="boost/container/out_of_range.html" title="Class out_of_range">out_of_range</a><span class="special">;</span>
    <span class="keyword">class</span> <a class="link" href="boost/container/runtime_error.html" title="Class runtime_error">runtime_error</a><span class="special">;</span>

    <span class="keyword">typedef</span> <a class="link" href="boost/container/bad_alloc.html" title="Class bad_alloc">bad_alloc</a> <a name="boost.container.bad_alloc_t"></a><span class="identifier">bad_alloc_t</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="boost/container/out_of_range.html" title="Class out_of_range">out_of_range</a> <a name="boost.container.out_of_range_t"></a><span class="identifier">out_of_range_t</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="boost/container/length_error.html" title="Class length_error">length_error</a> <a name="boost.container.length_error_t"></a><span class="identifier">length_error_t</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="boost/container/logic_error.html" title="Class logic_error">logic_error</a> <a name="boost.container.logic_error_t"></a><span class="identifier">logic_error_t</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="boost/container/runtime_error.html" title="Class runtime_error">runtime_error</a> <a name="boost.container.runtime_error_t"></a><span class="identifier">runtime_error_t</span><span class="special">;</span>
    <span class="keyword">void</span> <a class="link" href="boost/container/throw_bad_alloc.html" title="Function throw_bad_alloc"><span class="identifier">throw_bad_alloc</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">void</span> <a class="link" href="boost/container/throw_out_of_range.html" title="Function throw_out_of_range"><span class="identifier">throw_out_of_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">void</span> <a class="link" href="boost/container/throw_length_error.html" title="Function throw_length_error"><span class="identifier">throw_length_error</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">void</span> <a class="link" href="boost/container/throw_logic_error.html" title="Function throw_logic_error"><span class="identifier">throw_logic_error</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">void</span> <a class="link" href="boost/container/throw_runtime_error.html" title="Function throw_runtime_error"><span class="identifier">throw_runtime_error</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.uses_allocator_hpp"></a>Header &lt;<a href="../../boost/container/uses_allocator.hpp" target="_top">boost/container/uses_allocator.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/constructible_wit_idm30697.html" title="Struct template constructible_with_allocator_prefix">constructible_with_allocator_prefix</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/constructible_wit_idm30723.html" title="Struct template constructible_with_allocator_suffix">constructible_with_allocator_suffix</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/uses_allocator.html" title="Struct template uses_allocator">uses_allocator</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.uses_allocator_fwd_hpp"></a>Header &lt;<a href="../../boost/container/uses_allocator_fwd.hpp" target="_top">boost/container/uses_allocator_fwd.hpp</a>&gt;</h3></div></div></div>
<p>This header forward declares <a class="link" href="boost/container/constructible_wit_idm30697.html" title="Struct template constructible_with_allocator_prefix">boost::container::constructible_with_allocator_prefix</a>, <a class="link" href="boost/container/constructible_wit_idm30723.html" title="Struct template constructible_with_allocator_suffix">boost::container::constructible_with_allocator_suffix</a> and <a class="link" href="boost/container/uses_allocator.html" title="Struct template uses_allocator">boost::container::uses_allocator</a>. Also defines the following types: </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="boost/container/erased_type.html" title="Struct erased_type">erased_type</a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">unspecified</span> <a class="link" href="boost/container/allocator_arg_t.html" title="Type definition allocator_arg_t"><span class="identifier">allocator_arg_t</span></a><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">allocator_arg_t</span> <a class="link" href="boost/container/allocator_arg.html" title="Global allocator_arg">allocator_arg</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.vector_hpp"></a>Header &lt;<a href="../../boost/container/vector.hpp" target="_top">boost/container/vector.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> A <span class="special">=</span> <span class="keyword">void</span><span class="special">,</span> <span class="keyword">typename</span> Options <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="boost/container/vector.html" title="Class template vector">vector</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span>  <a name="boost.container.vector_idm31851"></a><span class="identifier">vector</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Allocator<span class="special">&gt;</span> 
       <a name="boost.container.vector_idm31859"></a><span class="identifier">vector</span><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.container.pmr.vector_hpp"></a>Header &lt;<a href="../../boost/container/pmr/vector.hpp" target="_top">boost/container/pmr/vector.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">container</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">pmr</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="boost/container/pmr/vector_of.html" title="Struct template vector_of">vector_of</a><span class="special">;</span>

      <span class="keyword">typedef</span> <a class="link" href="boost/container/vector.html" title="Class template vector">boost::container::vector</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <a class="link" href="boost/container/pmr/polymorphic_allocator.html" title="Class template polymorphic_allocator">polymorphic_allocator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <a name="boost.container.pmr.vector"></a><span class="identifier">vector</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2018 Ion Gaztanaga<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="container/index.html"><img src="../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="container.html"><img src="../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="index.html"><img src="../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost/container/adaptive_pool.html"><img src="../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
