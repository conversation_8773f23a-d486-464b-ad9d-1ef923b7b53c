<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ip::resolver_query_base::flags</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../ip__resolver_query_base.html" title="ip::resolver_query_base">
<link rel="prev" href="canonical_name.html" title="ip::resolver_query_base::canonical_name">
<link rel="next" href="numeric_host.html" title="ip::resolver_query_base::numeric_host">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="canonical_name.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ip__resolver_query_base.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="numeric_host.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_asio.reference.ip__resolver_query_base.flags"></a><a class="link" href="flags.html" title="ip::resolver_query_base::flags">ip::resolver_query_base::flags</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from ip::resolver_base.</em></span>
        </p>
<p>
          <a class="indexterm" name="boost_asio.indexterm.ip__resolver_query_base.flags"></a> 
A
          bitmask type (C++ Std [lib.bitmask.types]).
        </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">unspecified</span> <span class="identifier">flags</span><span class="special">;</span>
</pre>
<h6>
<a name="boost_asio.reference.ip__resolver_query_base.flags.h0"></a>
          <span class="phrase"><a name="boost_asio.reference.ip__resolver_query_base.flags.requirements"></a></span><a class="link" href="flags.html#boost_asio.reference.ip__resolver_query_base.flags.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">boost/asio/ip/resolver_query_base.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">boost/asio.hpp</code>
        </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="canonical_name.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ip__resolver_query_base.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="numeric_host.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
