<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>posix::basic_stream_descriptor::release</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../posix__basic_stream_descriptor.html" title="posix::basic_stream_descriptor">
<link rel="prev" href="read_some/overload2.html" title="posix::basic_stream_descriptor::read_some (2 of 2 overloads)">
<link rel="next" href="wait.html" title="posix::basic_stream_descriptor::wait">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="read_some/overload2.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../posix__basic_stream_descriptor.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="wait.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_asio.reference.posix__basic_stream_descriptor.release"></a><a class="link" href="release.html" title="posix::basic_stream_descriptor::release">posix::basic_stream_descriptor::release</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from posix::basic_descriptor.</em></span>
        </p>
<p>
          <a class="indexterm" name="boost_asio.indexterm.posix__basic_stream_descriptor.release"></a> 
Release
          ownership of the native descriptor implementation.
        </p>
<pre class="programlisting"><span class="identifier">native_handle_type</span> <span class="identifier">release</span><span class="special">();</span>
</pre>
<p>
          This function may be used to obtain the underlying representation of the
          descriptor. After calling this function, <code class="computeroutput"><span class="identifier">is_open</span><span class="special">()</span></code> returns false. The caller is responsible
          for closing the descriptor.
        </p>
<p>
          All outstanding asynchronous read or write operations will finish immediately,
          and the handlers for cancelled operations will be passed the <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">error</span><span class="special">::</span><span class="identifier">operation_aborted</span></code> error.
        </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="read_some/overload2.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../posix__basic_stream_descriptor.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="wait.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
