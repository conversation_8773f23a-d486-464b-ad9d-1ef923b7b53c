<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>execution::blocking_t::always_t::value</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../execution__blocking_t__always_t.html" title="execution::blocking_t::always_t">
<link rel="prev" href="polymorphic_query_result_type.html" title="execution::blocking_t::always_t::polymorphic_query_result_type">
<link rel="next" href="../execution__blocking_t__never_t.html" title="execution::blocking_t::never_t">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="polymorphic_query_result_type.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../execution__blocking_t__always_t.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../execution__blocking_t__never_t.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_asio.reference.execution__blocking_t__always_t.value"></a><a class="link" href="value.html" title="execution::blocking_t::always_t::value">execution::blocking_t::always_t::value</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="boost_asio.indexterm.execution__blocking_t__always_t.value"></a> 
Get
          the value associated with a property object.
        </p>
<pre class="programlisting"><span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="identifier">blocking_t</span> <span class="identifier">value</span><span class="special">();</span>
</pre>
<h6>
<a name="boost_asio.reference.execution__blocking_t__always_t.value.h0"></a>
          <span class="phrase"><a name="boost_asio.reference.execution__blocking_t__always_t.value.return_value"></a></span><a class="link" href="value.html#boost_asio.reference.execution__blocking_t__always_t.value.return_value">Return
          Value</a>
        </h6>
<p>
          <code class="computeroutput"><span class="identifier">always_t</span><span class="special">()</span></code>;
        </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="polymorphic_query_result_type.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../execution__blocking_t__always_t.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../execution__blocking_t__never_t.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
