<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_socket::basic_socket (10 of 10 overloads)</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../basic_socket.html" title="basic_socket::basic_socket">
<link rel="prev" href="overload9.html" title="basic_socket::basic_socket (9 of 10 overloads)">
<link rel="next" href="../bind.html" title="basic_socket::bind">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload9.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../basic_socket.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../bind.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_asio.reference.basic_socket.basic_socket.overload10"></a><a class="link" href="overload10.html" title="basic_socket::basic_socket (10 of 10 overloads)">basic_socket::basic_socket
          (10 of 10 overloads)</a>
</h5></div></div></div>
<p>
            Move-construct a <a class="link" href="../../basic_socket.html" title="basic_socket"><code class="computeroutput"><span class="identifier">basic_socket</span></code></a> from a socket of
            another protocol type.
          </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">typename</span> <a class="link" href="../../Protocol.html" title="Protocol requirements">Protocol1</a><span class="special">,</span>
    <span class="keyword">typename</span> <a class="link" href="../../Executor1.html" title="Executor requirements">Executor1</a><span class="special">&gt;</span>
<span class="identifier">basic_socket</span><span class="special">(</span>
    <span class="identifier">basic_socket</span><span class="special">&lt;</span> <span class="identifier">Protocol1</span><span class="special">,</span> <span class="identifier">Executor1</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> <span class="identifier">other</span><span class="special">,</span>
    <span class="keyword">typename</span> <span class="identifier">constraint</span><span class="special">&lt;</span> <span class="identifier">is_convertible</span><span class="special">&lt;</span> <span class="identifier">Protocol1</span><span class="special">,</span> <span class="identifier">Protocol</span> <span class="special">&gt;::</span><span class="identifier">value</span> <span class="special">&amp;&amp;</span><span class="identifier">is_convertible</span><span class="special">&lt;</span> <span class="identifier">Executor1</span><span class="special">,</span> <span class="identifier">Executor</span> <span class="special">&gt;::</span><span class="identifier">value</span> <span class="special">&gt;::</span><span class="identifier">type</span>  <span class="special">=</span> <span class="number">0</span><span class="special">);</span>
</pre>
<p>
            This constructor moves a socket from one object to another.
          </p>
<h6>
<a name="boost_asio.reference.basic_socket.basic_socket.overload10.h0"></a>
            <span class="phrase"><a name="boost_asio.reference.basic_socket.basic_socket.overload10.parameters"></a></span><a class="link" href="overload10.html#boost_asio.reference.basic_socket.basic_socket.overload10.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">other</span></dt>
<dd><p>
                  The other <a class="link" href="../../basic_socket.html" title="basic_socket"><code class="computeroutput"><span class="identifier">basic_socket</span></code></a> object from
                  which the move will occur.
                </p></dd>
</dl>
</div>
<h6>
<a name="boost_asio.reference.basic_socket.basic_socket.overload10.h1"></a>
            <span class="phrase"><a name="boost_asio.reference.basic_socket.basic_socket.overload10.remarks"></a></span><a class="link" href="overload10.html#boost_asio.reference.basic_socket.basic_socket.overload10.remarks">Remarks</a>
          </h6>
<p>
            Following the move, the moved-from object is in the same state as if
            constructed using the <code class="computeroutput"><span class="identifier">basic_socket</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">executor_type</span><span class="special">&amp;)</span>
            <span class="identifier">constructor</span></code>.
          </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload9.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../basic_socket.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../bind.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
