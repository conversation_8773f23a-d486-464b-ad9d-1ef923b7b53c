<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>windows::basic_overlapped_handle::close (2 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../close.html" title="windows::basic_overlapped_handle::close">
<link rel="prev" href="overload1.html" title="windows::basic_overlapped_handle::close (1 of 2 overloads)">
<link rel="next" href="../executor_type.html" title="windows::basic_overlapped_handle::executor_type">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../close.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../executor_type.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_asio.reference.windows__basic_overlapped_handle.close.overload2"></a><a class="link" href="overload2.html" title="windows::basic_overlapped_handle::close (2 of 2 overloads)">windows::basic_overlapped_handle::close
          (2 of 2 overloads)</a>
</h5></div></div></div>
<p>
            Close the handle.
          </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">close</span><span class="special">(</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">system</span><span class="special">::</span><span class="identifier">error_code</span> <span class="special">&amp;</span> <span class="identifier">ec</span><span class="special">);</span>
</pre>
<p>
            This function is used to close the handle. Any asynchronous read or write
            operations will be cancelled immediately, and will complete with the
            <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">error</span><span class="special">::</span><span class="identifier">operation_aborted</span></code> error.
          </p>
<h6>
<a name="boost_asio.reference.windows__basic_overlapped_handle.close.overload2.h0"></a>
            <span class="phrase"><a name="boost_asio.reference.windows__basic_overlapped_handle.close.overload2.parameters"></a></span><a class="link" href="overload2.html#boost_asio.reference.windows__basic_overlapped_handle.close.overload2.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">ec</span></dt>
<dd><p>
                  Set to indicate what error occurred, if any.
                </p></dd>
</dl>
</div>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../close.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../executor_type.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
