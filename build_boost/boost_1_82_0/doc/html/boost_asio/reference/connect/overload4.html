<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>connect (4 of 12 overloads)</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../connect.html" title="connect">
<link rel="prev" href="overload3.html" title="connect (3 of 12 overloads)">
<link rel="next" href="overload5.html" title="connect (5 of 12 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../connect.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload5.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_asio.reference.connect.overload4"></a><a class="link" href="overload4.html" title="connect (4 of 12 overloads)">connect (4
        of 12 overloads)</a>
</h4></div></div></div>
<p>
          (Deprecated: Use range overload.) Establishes a socket connection by trying
          each endpoint in a sequence.
        </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">typename</span> <a class="link" href="../Protocol.html" title="Protocol requirements">Protocol</a><span class="special">,</span>
    <span class="keyword">typename</span> <a class="link" href="../Executor1.html" title="Executor requirements">Executor</a><span class="special">,</span>
    <span class="keyword">typename</span> <span class="identifier">Iterator</span><span class="special">&gt;</span>
<span class="identifier">Iterator</span> <span class="identifier">connect</span><span class="special">(</span>
    <span class="identifier">basic_socket</span><span class="special">&lt;</span> <span class="identifier">Protocol</span><span class="special">,</span> <span class="identifier">Executor</span> <span class="special">&gt;</span> <span class="special">&amp;</span> <span class="identifier">s</span><span class="special">,</span>
    <span class="identifier">Iterator</span> <span class="identifier">begin</span><span class="special">,</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">system</span><span class="special">::</span><span class="identifier">error_code</span> <span class="special">&amp;</span> <span class="identifier">ec</span><span class="special">,</span>
    <span class="keyword">typename</span> <span class="identifier">constraint</span><span class="special">&lt;!</span><span class="identifier">is_endpoint_sequence</span><span class="special">&lt;</span> <span class="identifier">Iterator</span> <span class="special">&gt;::</span><span class="identifier">value</span> <span class="special">&gt;::</span><span class="identifier">type</span>  <span class="special">=</span> <span class="number">0</span><span class="special">);</span>
</pre>
<p>
          This function attempts to connect a socket to one of a sequence of endpoints.
          It does this by repeated calls to the socket's <code class="computeroutput"><span class="identifier">connect</span></code>
          member function, once for each endpoint in the sequence, until a connection
          is successfully established.
        </p>
<h6>
<a name="boost_asio.reference.connect.overload4.h0"></a>
          <span class="phrase"><a name="boost_asio.reference.connect.overload4.parameters"></a></span><a class="link" href="overload4.html#boost_asio.reference.connect.overload4.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">s</span></dt>
<dd><p>
                The socket to be connected. If the socket is already open, it will
                be closed.
              </p></dd>
<dt><span class="term">begin</span></dt>
<dd><p>
                An iterator pointing to the start of a sequence of endpoints.
              </p></dd>
<dt><span class="term">ec</span></dt>
<dd><p>
                Set to indicate what error occurred, if any. If the sequence is empty,
                set to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">error</span><span class="special">::</span><span class="identifier">not_found</span></code>. Otherwise, contains
                the error from the last connection attempt.
              </p></dd>
</dl>
</div>
<h6>
<a name="boost_asio.reference.connect.overload4.h1"></a>
          <span class="phrase"><a name="boost_asio.reference.connect.overload4.return_value"></a></span><a class="link" href="overload4.html#boost_asio.reference.connect.overload4.return_value">Return Value</a>
        </h6>
<p>
          On success, an iterator denoting the successfully connected endpoint. Otherwise,
          the end iterator.
        </p>
<h6>
<a name="boost_asio.reference.connect.overload4.h2"></a>
          <span class="phrase"><a name="boost_asio.reference.connect.overload4.remarks"></a></span><a class="link" href="overload4.html#boost_asio.reference.connect.overload4.remarks">Remarks</a>
        </h6>
<p>
          This overload assumes that a default constructed object of type <code class="computeroutput"><span class="identifier">Iterator</span></code> represents the end of the sequence.
          This is a valid assumption for iterator types such as <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">resolver</span><span class="special">::</span><span class="identifier">iterator</span></code>.
        </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload3.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../connect.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload5.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
