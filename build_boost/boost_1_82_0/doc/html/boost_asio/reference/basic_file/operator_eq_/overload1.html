<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_file::operator= (1 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../operator_eq_.html" title="basic_file::operator=">
<link rel="prev" href="../operator_eq_.html" title="basic_file::operator=">
<link rel="next" href="overload2.html" title="basic_file::operator= (2 of 2 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../operator_eq_.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../operator_eq_.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_asio.reference.basic_file.operator_eq_.overload1"></a><a class="link" href="overload1.html" title="basic_file::operator= (1 of 2 overloads)">basic_file::operator=
          (1 of 2 overloads)</a>
</h5></div></div></div>
<p>
            Move-assign a <a class="link" href="../../basic_file.html" title="basic_file"><code class="computeroutput"><span class="identifier">basic_file</span></code></a> from another.
          </p>
<pre class="programlisting"><span class="identifier">basic_file</span> <span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span>
    <span class="identifier">basic_file</span> <span class="special">&amp;&amp;</span> <span class="identifier">other</span><span class="special">);</span>
</pre>
<p>
            This assignment operator moves a file from one object to another.
          </p>
<h6>
<a name="boost_asio.reference.basic_file.operator_eq_.overload1.h0"></a>
            <span class="phrase"><a name="boost_asio.reference.basic_file.operator_eq_.overload1.parameters"></a></span><a class="link" href="overload1.html#boost_asio.reference.basic_file.operator_eq_.overload1.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">other</span></dt>
<dd><p>
                  The other <a class="link" href="../../basic_file.html" title="basic_file"><code class="computeroutput"><span class="identifier">basic_file</span></code></a> object from
                  which the move will occur.
                </p></dd>
</dl>
</div>
<h6>
<a name="boost_asio.reference.basic_file.operator_eq_.overload1.h1"></a>
            <span class="phrase"><a name="boost_asio.reference.basic_file.operator_eq_.overload1.remarks"></a></span><a class="link" href="overload1.html#boost_asio.reference.basic_file.operator_eq_.overload1.remarks">Remarks</a>
          </h6>
<p>
            Following the move, the moved-from object is in the same state as if
            constructed using the <code class="computeroutput"><span class="identifier">basic_file</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">executor_type</span><span class="special">&amp;)</span>
            <span class="identifier">constructor</span></code>.
          </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../operator_eq_.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../operator_eq_.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
