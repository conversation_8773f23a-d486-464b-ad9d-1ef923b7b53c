<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>experimental::as_single_t::executor_with_default::default_completion_token_type</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../experimental__as_single_t__executor_with_default.html" title="experimental::as_single_t::executor_with_default">
<link rel="prev" href="../experimental__as_single_t__executor_with_default.html" title="experimental::as_single_t::executor_with_default">
<link rel="next" href="executor_with_default.html" title="experimental::as_single_t::executor_with_default::executor_with_default">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../experimental__as_single_t__executor_with_default.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../experimental__as_single_t__executor_with_default.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="executor_with_default.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type"></a><a class="link" href="default_completion_token_type.html" title="experimental::as_single_t::executor_with_default::default_completion_token_type">experimental::as_single_t::executor_with_default::default_completion_token_type</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="boost_asio.indexterm.experimental__as_single_t__executor_with_default.default_completion_token_type"></a> 
Specify
          <code class="computeroutput"><span class="identifier">as_single_t</span></code> as the default
          completion token type.
        </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">as_single_t</span> <span class="identifier">default_completion_token_type</span><span class="special">;</span>
</pre>
<h6>
<a name="boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.h0"></a>
          <span class="phrase"><a name="boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.types"></a></span><a class="link" href="default_completion_token_type.html#boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.types">Types</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a class="link" href="../experimental__as_single_t__default_constructor_tag.html" title="experimental::as_single_t::default_constructor_tag"><span class="bold"><strong>default_constructor_tag</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Tag type used to prevent the "default" constructor
                    from being used for conversions.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../experimental__as_single_t__executor_with_default.html" title="experimental::as_single_t::executor_with_default"><span class="bold"><strong>executor_with_default</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Adapts an executor to add the as_single_t completion token as
                    the default.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.h1"></a>
          <span class="phrase"><a name="boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.member_functions"></a></span><a class="link" href="default_completion_token_type.html#boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.member_functions">Member
          Functions</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a class="link" href="../experimental__as_single_t/as_default_on.html" title="experimental::as_single_t::as_default_on"><span class="bold"><strong>as_default_on</strong></span></a> <span class="silver">[static]</span>
                  </p>
                </td>
<td>
                  <p>
                    Function helper to adapt an I/O object to use as_single_t as
                    its default completion token type.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../experimental__as_single_t/as_single_t.html" title="experimental::as_single_t::as_single_t"><span class="bold"><strong>as_single_t</strong></span></a> <span class="silver">[constructor]</span>
                  </p>
                </td>
<td>
                  <p>
                    Default constructor. <br> <span class="silver"> —</span><br> Constructor.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.h2"></a>
          <span class="phrase"><a name="boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.data_members"></a></span><a class="link" href="default_completion_token_type.html#boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.data_members">Data
          Members</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody><tr>
<td>
                  <p>
                    <a class="link" href="../experimental__as_single_t/token_.html" title="experimental::as_single_t::token_"><span class="bold"><strong>token_</strong></span></a>
                  </p>
                </td>
<td>
                </td>
</tr></tbody>
</table></div>
<p>
          The <a class="link" href="../experimental__as_single_t.html" title="experimental::as_single_t"><code class="computeroutput"><span class="identifier">experimental</span><span class="special">::</span><span class="identifier">as_single_t</span></code></a> class is used to indicate
          that any arguments to the completion handler should be combined and passed
          as a single argument. If there is already one argument, that argument is
          passed as-is. If there is more than argument, the arguments are first moved
          into a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple</span></code> and that tuple is then passed to
          the completion handler.
        </p>
<h6>
<a name="boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.h3"></a>
          <span class="phrase"><a name="boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.requirements"></a></span><a class="link" href="default_completion_token_type.html#boost_asio.reference.experimental__as_single_t__executor_with_default.default_completion_token_type.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">boost/asio/experimental/as_single.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span>None
        </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../experimental__as_single_t__executor_with_default.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../experimental__as_single_t__executor_with_default.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="executor_with_default.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
