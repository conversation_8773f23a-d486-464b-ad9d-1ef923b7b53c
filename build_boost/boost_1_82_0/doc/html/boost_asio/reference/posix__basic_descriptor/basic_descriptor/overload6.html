<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>posix::basic_descriptor::basic_descriptor (6 of 6 overloads)</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../basic_descriptor.html" title="posix::basic_descriptor::basic_descriptor">
<link rel="prev" href="overload5.html" title="posix::basic_descriptor::basic_descriptor (5 of 6 overloads)">
<link rel="next" href="../bytes_readable.html" title="posix::basic_descriptor::bytes_readable">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload5.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../basic_descriptor.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../bytes_readable.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_asio.reference.posix__basic_descriptor.basic_descriptor.overload6"></a><a class="link" href="overload6.html" title="posix::basic_descriptor::basic_descriptor (6 of 6 overloads)">posix::basic_descriptor::basic_descriptor
          (6 of 6 overloads)</a>
</h5></div></div></div>
<p>
            Move-construct a <a class="link" href="../../posix__basic_descriptor.html" title="posix::basic_descriptor"><code class="computeroutput"><span class="identifier">posix</span><span class="special">::</span><span class="identifier">basic_descriptor</span></code></a> from a descriptor
            of another executor type.
          </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">typename</span> <a class="link" href="../../Executor1.html" title="Executor requirements">Executor1</a><span class="special">&gt;</span>
<span class="identifier">basic_descriptor</span><span class="special">(</span>
    <span class="identifier">basic_descriptor</span><span class="special">&lt;</span> <span class="identifier">Executor1</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> <span class="identifier">other</span><span class="special">,</span>
    <span class="keyword">typename</span> <span class="identifier">constraint</span><span class="special">&lt;</span> <span class="identifier">is_convertible</span><span class="special">&lt;</span> <span class="identifier">Executor1</span><span class="special">,</span> <span class="identifier">Executor</span> <span class="special">&gt;::</span><span class="identifier">value</span><span class="special">,</span> <span class="identifier">defaulted_constraint</span> <span class="special">&gt;::</span><span class="identifier">type</span>  <span class="special">=</span> <span class="identifier">defaulted_constraint</span><span class="special">());</span>
</pre>
<p>
            This constructor moves a descriptor from one object to another.
          </p>
<h6>
<a name="boost_asio.reference.posix__basic_descriptor.basic_descriptor.overload6.h0"></a>
            <span class="phrase"><a name="boost_asio.reference.posix__basic_descriptor.basic_descriptor.overload6.parameters"></a></span><a class="link" href="overload6.html#boost_asio.reference.posix__basic_descriptor.basic_descriptor.overload6.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">other</span></dt>
<dd><p>
                  The other <a class="link" href="../../posix__basic_descriptor.html" title="posix::basic_descriptor"><code class="computeroutput"><span class="identifier">posix</span><span class="special">::</span><span class="identifier">basic_descriptor</span></code></a> object
                  from which the move will occur.
                </p></dd>
</dl>
</div>
<h6>
<a name="boost_asio.reference.posix__basic_descriptor.basic_descriptor.overload6.h1"></a>
            <span class="phrase"><a name="boost_asio.reference.posix__basic_descriptor.basic_descriptor.overload6.remarks"></a></span><a class="link" href="overload6.html#boost_asio.reference.posix__basic_descriptor.basic_descriptor.overload6.remarks">Remarks</a>
          </h6>
<p>
            Following the move, the moved-from object is in the same state as if
            constructed using the <code class="computeroutput"><span class="identifier">basic_descriptor</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">executor_type</span><span class="special">&amp;)</span></code>
            constructor.
          </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload5.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../basic_descriptor.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../bytes_readable.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
