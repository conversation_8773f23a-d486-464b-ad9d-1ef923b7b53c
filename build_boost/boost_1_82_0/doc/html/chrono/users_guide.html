<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>User's Guide</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../chrono.html" title="Chapter 6. Boost.Chrono 2.0.8">
<link rel="prev" href="../chrono.html" title="Chapter 6. Boost.Chrono 2.0.8">
<link rel="next" href="reference.html" title="Reference">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../chrono.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../chrono.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="reference.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="chrono.users_guide"></a><a class="link" href="users_guide.html" title="User's Guide">User's Guide</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.getting_started">Getting Started</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial">Tutorial</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples">Examples</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.ext_references">External Resources</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="chrono.users_guide.getting_started"></a><a class="link" href="users_guide.html#chrono.users_guide.getting_started" title="Getting Started">Getting Started</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.getting_started.install">Installing
        Chrono</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.getting_started.hello_world__">Hello
        World! </a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.getting_started.install"></a><a class="link" href="users_guide.html#chrono.users_guide.getting_started.install" title="Installing Chrono">Installing
        Chrono</a>
</h4></div></div></div>
<h6>
<a name="chrono.users_guide.getting_started.install.h0"></a>
          <span class="phrase"><a name="chrono.users_guide.getting_started.install.getting__emphasis_role__bold__boost_chrono__emphasis__"></a></span><a class="link" href="users_guide.html#chrono.users_guide.getting_started.install.getting__emphasis_role__bold__boost_chrono__emphasis__">Getting
          <span class="bold"><strong>Boost.Chrono</strong></span> </a>
        </h6>
<p>
          <span class="bold"><strong>Boost.Chrono</strong></span> is in the latest Boost release
          in the folder <code class="computeroutput"><span class="special">/</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span></code>.
          Documentation, tests and examples folder are at <code class="computeroutput"><span class="identifier">boost</span><span class="special">/</span><span class="identifier">libs</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span></code>.
        </p>
<p>
          You can also access the latest (unstable?) state from the <a href="https://svn.boost.org/svn/boost-trunk" target="_top">Boost
          trunk</a> directories boost/chrono and libs/chrono. Just go to <a href="http://svn.boost.org/trac/boost/wiki/BoostSubversion" target="_top">here</a>
          and follow the instructions there for anonymous SVN access.
        </p>
<h6>
<a name="chrono.users_guide.getting_started.install.h1"></a>
          <span class="phrase"><a name="chrono.users_guide.getting_started.install.where_to_install_boost_chrono__"></a></span><a class="link" href="users_guide.html#chrono.users_guide.getting_started.install.where_to_install_boost_chrono__">Where
          to install Boost.Chrono? </a>
        </h6>
<p>
          The simple way is to decompress (or checkout from SVN) the files in your
          BOOST_ROOT directory.
        </p>
<h6>
<a name="chrono.users_guide.getting_started.install.h2"></a>
          <span class="phrase"><a name="chrono.users_guide.getting_started.install.building_boost_chrono_"></a></span><a class="link" href="users_guide.html#chrono.users_guide.getting_started.install.building_boost_chrono_">Building
          Boost.Chrono </a>
        </h6>
<p>
          <span class="bold"><strong>Boost.Chrono</strong></span> can be configured as a header-only
          library defining <a class="link" href="reference.html#chrono.reference.cpp0x.chrono_chrono_hpp.conf.header_only" title="How to Build Boost.Chrono as a Header Only Library?"><code class="computeroutput"><span class="identifier">BOOST_CHRONO_HEADER_ONLY</span></code></a>. However
          Boost.Chrono depends on the non header-only library Boost.System, so that
          you will need to link with boost_system.
        </p>
<p>
          Boost.System has an undocumented feature (use of macro BOOST_ERROR_CODE_HEADER_ONLY)
          to make it header only.
        </p>
<p>
          If <a class="link" href="reference.html#chrono.reference.cpp0x.chrono_chrono_hpp.conf.header_only" title="How to Build Boost.Chrono as a Header Only Library?"><code class="computeroutput"><span class="identifier">BOOST_CHRONO_HEADER_ONLY</span></code></a> is not
          defined you need to compile it and build the library before use, for example
          using:
        </p>
<pre class="programlisting"><span class="identifier">bjam</span> <span class="identifier">libs</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">build</span>
</pre>
<h6>
<a name="chrono.users_guide.getting_started.install.h3"></a>
          <span class="phrase"><a name="chrono.users_guide.getting_started.install.requirements"></a></span><a class="link" href="users_guide.html#chrono.users_guide.getting_started.install.requirements">Requirements</a>
        </h6>
<p>
          In particular, <span class="bold"><strong>Boost.Chrono</strong></span> depends on:
        </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term"><a href="http://www.boost.org/libs/config" target="_top"><span class="bold"><strong>Boost.Config</strong></span></a></span></dt>
<dd><p>
                for configuration purposes, ...
              </p></dd>
<dt><span class="term"><a href="http://www.boost.org/libs/exception" target="_top"><span class="bold"><strong>Boost.Exception</strong></span></a></span></dt>
<dd><p>
                for throw_exception, ...
              </p></dd>
<dt><span class="term"><a href="http://www.boost.org/libs/integer" target="_top"><span class="bold"><strong>Boost.Integer</strong></span></a></span></dt>
<dd><p>
                for cstdint conformance, ...
              </p></dd>
<dt><span class="term"><a href="http://www.boost.org/libs/mpl" target="_top"><span class="bold"><strong>Boost.MPL</strong></span></a></span></dt>
<dd><p>
                for MPL Assert and bool, logical ...
              </p></dd>
<dt><span class="term"><a href="http://www.boost.org/libs/operators" target="_top"><span class="bold"><strong>Boost.Operators</strong></span></a></span></dt>
<dd><p>
                for operators, ...
              </p></dd>
<dt><span class="term"><a href="http://www.boost.org/libs/ratio" target="_top"><span class="bold"><strong>Boost.Ratio</strong></span></a></span></dt>
<dd><p>
                for ratio, milli, micro, ...
              </p></dd>
<dt><span class="term"><a href="http://www.boost.org/libs/system" target="_top"><span class="bold"><strong>Boost.System</strong></span></a></span></dt>
<dd><p>
                for error_code, ...
              </p></dd>
<dt><span class="term"><a href="http://www.boost.org/libs/type_traits" target="_top"><span class="bold"><strong>Boost.TypeTraits</strong></span></a></span></dt>
<dd><p>
                for is_base, is_convertible, common_type, ...
              </p></dd>
<dt><span class="term"><a href="http://www.boost.org/libs/utility" target="_top"><span class="bold"><strong>Boost.Utility/EnableIf</strong></span></a></span></dt>
<dd><p>
                for enable_if, ...
              </p></dd>
</dl>
</div>
<h6>
<a name="chrono.users_guide.getting_started.install.h4"></a>
          <span class="phrase"><a name="chrono.users_guide.getting_started.install.building_an_executable_that_uses_boost_chrono_"></a></span><a class="link" href="users_guide.html#chrono.users_guide.getting_started.install.building_an_executable_that_uses_boost_chrono_">Building
          an Executable that Uses Boost.Chrono </a>
        </h6>
<p>
          In addition to link with the <span class="bold"><strong>Boost.Chrono</strong></span>
          library you need also to link with the <span class="bold"><strong>Boost.System</strong></span>
          library. If <span class="bold"><strong>Boost.System</strong></span> is configured
          defining BOOST_ERROR_CODE_HEADER_ONLY you will no need to link with it
          as the dependent part is header only then.
        </p>
<h6>
<a name="chrono.users_guide.getting_started.install.h5"></a>
          <span class="phrase"><a name="chrono.users_guide.getting_started.install.exception_safety_"></a></span><a class="link" href="users_guide.html#chrono.users_guide.getting_started.install.exception_safety_">Exception
          safety </a>
        </h6>
<p>
          All functions in the library are exception-neutral and provide strong guarantee
          of exception safety as long as the underlying parameters provide it.
        </p>
<h6>
<a name="chrono.users_guide.getting_started.install.h6"></a>
          <span class="phrase"><a name="chrono.users_guide.getting_started.install.thread_safety_"></a></span><a class="link" href="users_guide.html#chrono.users_guide.getting_started.install.thread_safety_">Thread
          safety </a>
        </h6>
<p>
          All functions in the library are thread-unsafe except when noted explicitly.
        </p>
<p>
          As Boost.Chrono doesn't use mutable global variables the thread-safety
          analysis is limited to the access to each instance variable. It is not
          thread safe to use a function that modifies the access to a user variable
          if another can be reading or writing it.
        </p>
<h6>
<a name="chrono.users_guide.getting_started.install.h7"></a>
          <span class="phrase"><a name="chrono.users_guide.getting_started.install.tested_compilers_"></a></span><a class="link" href="users_guide.html#chrono.users_guide.getting_started.install.tested_compilers_">Tested
          compilers </a>
        </h6>
<p>
          The implementation will eventually work with most C++03 conforming compilers.
          Currently I use to test with on:
        </p>
<p>
          Windows with
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
              MSVC 10.0
            </li></ul></div>
<p>
          MinGW with
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              GCC 4.5.0
            </li>
<li class="listitem">
              GCC 4.5.0 -std=c++0x
            </li>
<li class="listitem">
              GCC 4.5.2
            </li>
<li class="listitem">
              GCC 4.5.2 -std=c++0x
            </li>
<li class="listitem">
              GCC 4.6.0
            </li>
<li class="listitem">
              GCC 4.6.0 -std=c++0x
            </li>
<li class="listitem">
              GCC 4.8.0
            </li>
<li class="listitem">
              GCC 4.8.0 -std=c++0x
            </li>
</ul></div>
<p>
          Ubuntu with * GCC 4.4.6 * GCC 4.4.6 -std=c++0x * GCC 4.5.4 * GCC 4.5.4
          -std=c++0x * GCC 4.6.1 * GCC 4.6.1 -std=c++0x * Intel 12.1.3 * Intel 12.1.3
          -std=c++0x
        </p>
<p>
          OsX with
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              GCC 4.1.2
            </li>
<li class="listitem">
              GCC 4.6.2
            </li>
<li class="listitem">
              GCC 4.6.2 -std=c++0x
            </li>
<li class="listitem">
              GCC 4.7.0
            </li>
<li class="listitem">
              GCC 4.7.0 -std=c++0x
            </li>
<li class="listitem">
              GCC 4.7.1
            </li>
<li class="listitem">
              GCC 4.7.1 -std=c++0x
            </li>
<li class="listitem">
              GCC 4.7.2
            </li>
<li class="listitem">
              GCC 4.7.2 -std=c++0x
            </li>
<li class="listitem">
              GCC 4.8.0
            </li>
<li class="listitem">
              GCC 4.8.0 -std=c++0x
            </li>
<li class="listitem">
              GCC 4.8.1
            </li>
<li class="listitem">
              GCC 4.8.1 -std=c++0x
            </li>
<li class="listitem">
              clang 3.1
            </li>
<li class="listitem">
              clang 3.1 -std=c++0x -stdlib=libc++
            </li>
<li class="listitem">
              clang 3.2
            </li>
<li class="listitem">
              clang 3.2 -std=c++11 -stdlib=libc++
            </li>
</ul></div>
<p>
          The committed code is tested with much more compilers. There are two compilers
          (VACPP and Borland) that don't provide the needed features. Other as Intel
          and Sun have some issues with i/o. While everything compiles and link correctly,
          there are some runtime issues I have not cached yet. See the regression
          tests for details.
        </p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
            Please let us know how this works on other platforms/compilers.
          </p></td></tr>
</table></div>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
            Please send any questions, comments and bug reports to boost &lt;at&gt;
            lists &lt;dot&gt; boost &lt;dot&gt; org.
          </p></td></tr>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.getting_started.hello_world__"></a><a class="link" href="users_guide.html#chrono.users_guide.getting_started.hello_world__" title="Hello World!">Hello
        World! </a>
</h4></div></div></div>
<p>
          [//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
          If all you want to do is to time a program's execution, here is a complete
          program:
        </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">cmath</span><span class="special">&gt;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">start</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>

    <span class="keyword">for</span> <span class="special">(</span> <span class="keyword">long</span> <span class="identifier">i</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span> <span class="identifier">i</span> <span class="special">&lt;</span> <span class="number">10000000</span><span class="special">;</span> <span class="special">++</span><span class="identifier">i</span> <span class="special">)</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">sqrt</span><span class="special">(</span> <span class="number">123.456L</span> <span class="special">);</span> <span class="comment">// burn some time</span>

    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="identifier">sec</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"took "</span> <span class="special">&lt;&lt;</span> <span class="identifier">sec</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" seconds\n"</span><span class="special">;</span>
    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
          Output was:
        </p>
<pre class="programlisting"><span class="identifier">took</span> <span class="number">0.832</span> <span class="identifier">seconds</span>
</pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="chrono.users_guide.tutorial"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial" title="Tutorial">Tutorial</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration">Duration</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.clocks">Clocks</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.time_point">Time Point</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.specific_clocks">Specific
        Clocks</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.i_o">I/O</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.tutorial.duration"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.duration" title="Duration">Duration</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration.so_what_exactly_is_a__duration__and_how_do_i_use_one_">So
          What Exactly is a <code class="computeroutput"><span class="identifier">duration</span></code>
          and How Do I Use One?</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration.what_happens_if_i_assign__m3___us3__to__minutes__instead_of__microseconds__">What
          Happens if I Assign <code class="computeroutput"><span class="identifier">m3</span> <span class="special">+</span> <span class="identifier">us3</span></code>
          to <code class="computeroutput"><span class="identifier">minutes</span></code> Instead of
          <code class="computeroutput"><span class="identifier">microseconds</span></code>?</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration.but_what_if_the_truncation_behavior_is_what_i_want_to_do_">But
          What if the Truncation Behavior is What I Want to Do?</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration.round">Rounding
          functions</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration.trafficking_in_floating_point_durations">Trafficking
          in floating-point Durations</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration.how_expensive_is_all_of_this_">How
          Expensive is All of this?</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration.how_complicated_is_it_to_build_a_function_taking_a__duration__parameter_">How
          Complicated is it to Build a Function Taking a <code class="computeroutput"><span class="identifier">duration</span></code>
          Parameter?</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration.is_it_possible_for_the_user_to_pass_a___duration_to_a_function_with_the_units_being_ambiguous_">Is it possible for the user to pass a <code class="computeroutput"><span class="identifier">duration</span></code> to a function with the
          units being ambiguous?</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.duration.can_durations_overflow_">Can
          Durations Overflow?</a></span></dt>
</dl></div>
<p>
          The <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> is the heart of this
          library. The interface that the user will see in everyday use is nearly
          identical to that of <span class="bold"><strong>Boost.DateTime</strong></span> time
          <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s authored by Jeff Garland,
          both in syntax and in behavior. This has been a very popular boost library
          for 7 years. There is an enormous positive history with this interface.
        </p>
<p>
          The library consists of six units of time <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">hours</span></code></a>
            </li>
<li class="listitem">
              <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">minutes</span></code></a>
            </li>
<li class="listitem">
              <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">seconds</span></code></a>
            </li>
<li class="listitem">
              <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">milliseconds</span></code></a>
            </li>
<li class="listitem">
              <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a>
            </li>
<li class="listitem">
              <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a>
            </li>
</ul></div>
<p>
          These units were chosen as a subset of the boost library because they are
          the most common units used when sleeping, waiting on a condition variable,
          or waiting to obtain the lock on a mutex. Each of these units is nothing
          but a thin wrapper around a signed integral count. That is, when you construct
          <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">minutes</span></code></a><code class="computeroutput"><span class="special">(</span><span class="number">3</span><span class="special">)</span></code>, all that
          happens is a <code class="computeroutput"><span class="number">3</span></code> is stored inside
          <code class="computeroutput"><span class="identifier">minutes</span></code>. When you construct
          <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><code class="computeroutput"><span class="special">(</span><span class="number">3</span><span class="special">)</span></code>, all that
          happens is a <code class="computeroutput"><span class="number">3</span></code> is stored inside
          <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a>.
        </p>
<p>
          The only context in which these different types differ is when being converted
          to one another. At this time, unit-specific compile-time conversion constants
          are used to convert the source unit to the target unit. Only conversions
          from coarser units to finer units are allowed (in Boost). This restriction
          ensures that all conversions are always exact. That is, <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a> can always represent
          any value <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">minutes</span></code></a> has.
        </p>
<p>
          In <span class="bold"><strong>Boost.DateTime</strong></span>, these units are united
          via inheritance. <span class="bold"><strong>Boost.Chrono</strong></span> instead
          unites these units through the class template <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>. That is, in <span class="bold"><strong>Boost.Chrono</strong></span> all six of the above units are nothing
          but typedefs to different instantiations of <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>. This change from Boost.DateTime
          has a far reaching positive impact, while not changing the syntax of the
          everyday use at all.
        </p>
<p>
          The most immediate positive impact is that the library can immediately
          generate any unit, with any precision it needs. This is sometimes necessary
          when doing comparisons or arithmetic between <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s of differing precision,
          assuming one wants the comparison and arithmetic to be exact.
        </p>
<p>
          A secondary benefit is that by publishing the class template <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> interface, user code
          can very easily create <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s with any precision they
          desire. The <code class="computeroutput"><span class="identifier">ratio</span></code> utility
          is used to specify the precision, so as long as the precision can be expressed
          by a rational constant with respect to seconds, this framework can exactly
          represent it (one third of a second is no problem, and neither is one third
          of a <code class="computeroutput"><span class="identifier">femto</span></code> second). All
          of this utility and flexibility comes at no cost just by making use of
          the no-run-time-overhead <code class="computeroutput"><span class="identifier">ratio</span></code>
          facility.
        </p>
<p>
          In Boost.DateTime, <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">hours</span></code></a> does not have the same representation
          as <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a>. The former is usually
          represented with a <code class="computeroutput"><span class="keyword">long</span></code> whereas
          a <code class="computeroutput"><span class="keyword">long</span> <span class="keyword">long</span></code>
          is required for the latter. The reason for this is simply range. You don't
          need many hours to cover an extremely large range of time. But this isn't
          true of nanoseconds. Being able to reduce the sizeof overhead for some
          units when possible, can be a significant performance advantage.
        </p>
<p>
          <span class="bold"><strong>Boost.Chrono</strong></span> continues, and generalizes
          that philosophy. Not only can one specify the precision of a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>, one can also specify
          its representation. This can be any integral type, or even a floating-point
          type. Or it can be a user-defined type which emulates an arithmetic type.
          The six predefined units all use signed integral types as their representation.
          And they all have a minimum range of ± 292 years. <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a> needs 64 bits to cover
          that range. <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">hours</span></code></a> needs only 23 bits to cover
          that range.
        </p>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.duration.so_what_exactly_is_a__duration__and_how_do_i_use_one_"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.duration.so_what_exactly_is_a__duration__and_how_do_i_use_one_" title="So What Exactly is a duration and How Do I Use One?">So
          What Exactly is a <code class="computeroutput"><span class="identifier">duration</span></code>
          and How Do I Use One?</a>
</h5></div></div></div>
<p>
            A <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> has a representation
            and a tick period (precision).
          </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span> <span class="special">=</span> <code class="computeroutput"><span class="identifier">ratio</span></code><span class="special">&lt;</span><span class="number">1</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="keyword">class</span> <span class="identifier">duration</span><span class="special">;</span>
</pre>
<p>
            The representation is simply any arithmetic type, or an emulation of
            such a type. The representation stores a count of ticks. This count is
            the only data member stored in a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>. If the representation
            is floating-point, it can store fractions of a tick to the precision
            of the representation. The tick period is represented by a <code class="computeroutput"><span class="identifier">ratio</span></code> and is encoded into the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>'s type, instead of
            stored. The tick period only has an impact on the behavior of the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> when a conversion between
            different <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s is attempted. The
            tick period is completely ignored when simply doing arithmetic among
            like <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s.
          </p>
<p>
            <span class="bold"><strong>Example:</strong></span>
          </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">ratio</span><span class="special">&lt;</span><span class="number">60</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">minutes</span><span class="special">;</span>
<span class="identifier">minutes</span> <span class="identifier">m1</span><span class="special">(</span><span class="number">3</span><span class="special">);</span>                 <span class="comment">// m1 stores 3</span>
<span class="identifier">minutes</span> <span class="identifier">m2</span><span class="special">(</span><span class="number">2</span><span class="special">);</span>                 <span class="comment">// m2 stores 2</span>
<span class="identifier">minutes</span> <span class="identifier">m3</span> <span class="special">=</span> <span class="identifier">m1</span> <span class="special">+</span> <span class="identifier">m2</span><span class="special">;</span>          <span class="comment">// m3 stores 5</span>

<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">micro</span><span class="special">&gt;</span> <span class="identifier">microseconds</span><span class="special">;</span>
<span class="identifier">microseconds</span> <span class="identifier">us1</span><span class="special">(</span><span class="number">3</span><span class="special">);</span>           <span class="comment">// us1 stores 3</span>
<span class="identifier">microseconds</span> <span class="identifier">us2</span><span class="special">(</span><span class="number">2</span><span class="special">);</span>           <span class="comment">// us2 stores 2</span>
<span class="identifier">microseconds</span> <span class="identifier">us3</span> <span class="special">=</span> <span class="identifier">us1</span> <span class="special">+</span> <span class="identifier">us2</span><span class="special">;</span>  <span class="comment">// us3 stores 5</span>

<span class="identifier">microseconds</span> <span class="identifier">us4</span> <span class="special">=</span> <span class="identifier">m3</span> <span class="special">+</span> <span class="identifier">us3</span><span class="special">;</span>   <span class="comment">// us4 stores 300000005</span>
</pre>
<p>
            In the final line of code above, there is an implicit conversion from
            minutes to microseconds, resulting in a relatively large number of microseconds.
          </p>
<p>
            If you need to access the tick count within a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>, there is a member
            <code class="computeroutput"><span class="identifier">count</span><span class="special">()</span></code>
            which simply returns the stored tick count.
          </p>
<pre class="programlisting"><span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">tc</span> <span class="special">=</span> <span class="identifier">us4</span><span class="special">.</span><span class="identifier">count</span><span class="special">();</span>    <span class="comment">// tc is 300000005</span>
</pre>
<p>
            These <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>'s have very simple,
            very predictable, and very observable behavior. After all, this is really
            nothing but the time-tested interface of Jeff's boost time <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> library (unified with
            templates instead of inheritance).
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.duration.what_happens_if_i_assign__m3___us3__to__minutes__instead_of__microseconds__"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.duration.what_happens_if_i_assign__m3___us3__to__minutes__instead_of__microseconds__" title="What Happens if I Assign m3 + us3 to minutes Instead of microseconds?">What
          Happens if I Assign <code class="computeroutput"><span class="identifier">m3</span> <span class="special">+</span> <span class="identifier">us3</span></code>
          to <code class="computeroutput"><span class="identifier">minutes</span></code> Instead of
          <code class="computeroutput"><span class="identifier">microseconds</span></code>?</a>
</h5></div></div></div>
<pre class="programlisting"><span class="identifier">minutes</span> <span class="identifier">m4</span> <span class="special">=</span> <span class="identifier">m3</span> <span class="special">+</span> <span class="identifier">us3</span><span class="special">;</span>
</pre>
<p>
            It won't compile! The rationale is that implicit truncation error should
            not be allowed to happen. If this were to compile, then <code class="computeroutput"><span class="identifier">m4</span></code> would hold <code class="computeroutput"><span class="number">5</span></code>,
            the same value as <code class="computeroutput"><span class="identifier">m3</span></code>.
            The value associated with <code class="computeroutput"><span class="identifier">us3</span></code>
            has been effectively ignored. This is similar to the problem of assigning
            a double to an <code class="computeroutput"><span class="keyword">int</span></code>: the
            fractional part gets silently discarded.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.duration.but_what_if_the_truncation_behavior_is_what_i_want_to_do_"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.duration.but_what_if_the_truncation_behavior_is_what_i_want_to_do_" title="But What if the Truncation Behavior is What I Want to Do?">But
          What if the Truncation Behavior is What I Want to Do?</a>
</h5></div></div></div>
<p>
            There is a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a> facility to explicitly
            ask for this behavior:
          </p>
<pre class="programlisting"><span class="identifier">minutes</span> <span class="identifier">m4</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><span class="identifier">minutes</span><span class="special">&gt;(</span><span class="identifier">m3</span> <span class="special">+</span> <span class="identifier">us3</span><span class="special">);</span>  <span class="comment">// m4.count() == 5</span>
</pre>
<p>
            In general, one can perform <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> arithmetic at will.
            If <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a> isn't used, and
            it compiles, the arithmetic is exact. If one wants to override this exact
            arithmetic behavior, <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a> can be used to
            explicitly specify that desire. The <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a> has the same efficiency
            as the implicit conversion, and will even be exact as often as it can.
          </p>
<p>
            You can use <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><code class="computeroutput"><span class="special">&lt;&gt;</span></code>
            to convert the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> into whatever units
            you desire. This facility will round down (truncate) if an exact conversion
            is not possible. For example:
          </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a> <span class="identifier">start</span><span class="special">;</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a> <span class="identifier">end</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">milliseconds</span></code></a> <span class="identifier">ms</span><span class="special">;</span>
<span class="identifier">ms</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><span class="identifier">ms</span><span class="special">&gt;(</span><span class="identifier">end</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">);</span>

<span class="comment">// d now holds the number of milliseconds from start to end.</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">d</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">"ms\n"</span><span class="special">;</span>
</pre>
<p>
            We can convert to <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a>, or some integral-based
            duration which <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a> will always exactly
            convert to, then <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><code class="computeroutput"><span class="special">&lt;&gt;</span></code>
            is unnecessary:
          </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a> <span class="identifier">ns</span><span class="special">;</span>
<span class="identifier">ns</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">end</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">ns</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">"ns\n"</span><span class="special">;</span>
</pre>
<p>
            If you need seconds with a floating-point representation you can also
            eliminate the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><code class="computeroutput"><span class="special">&lt;&gt;</span></code>:
          </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="identifier">sec</span><span class="special">;</span>  <span class="comment">// seconds, stored with a double</span>
<span class="identifier">sec</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">end</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">sec</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">"s\n"</span><span class="special">;</span>
</pre>
<p>
            If you're not sure if you need <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><code class="computeroutput"><span class="special">&lt;&gt;</span></code>
            or not, feel free to try it without. If the conversion is exact, or if
            the destination has a floating-point representation, it will compile:
            else it will not compile.
          </p>
<p>
            If you need to use <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><code class="computeroutput"><span class="special">&lt;&gt;</span></code>,
            but want to round up, instead of down when the conversion is inexact,
            here is a handy little helper function to do so. Writing it is actually
            a good starter project for understanding <span class="bold"><strong>Boost.Chrono</strong></span>:
          </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">ToDuration</span></code></a><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="identifier">ToDuration</span>
<span class="identifier">round_up</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="comment">// first round down</span>
    <span class="identifier">ToDuration</span> <span class="identifier">result</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><span class="identifier">ToDuration</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">result</span> <span class="special">&lt;</span> <span class="identifier">d</span><span class="special">)</span>  <span class="comment">// comparisons are *always* exact</span>
       <span class="special">++</span><span class="identifier">result</span><span class="special">;</span>     <span class="comment">// increment by one tick period</span>
    <span class="keyword">return</span> <span class="identifier">result</span><span class="special">;</span>
<span class="special">}</span>

<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">milliseconds</span></code></a> <span class="identifier">ms</span><span class="special">;</span>
<span class="identifier">ms</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">round_up</span><span class="special">&lt;</span><span class="identifier">ms</span><span class="special">&gt;(</span><span class="identifier">end</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">);</span>
<span class="comment">// d now holds the number of milliseconds from start to end, rounded up.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">d</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">"ms\n"</span><span class="special">;</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.duration.round"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.duration.round" title="Rounding functions">Rounding
          functions</a>
</h5></div></div></div>
<p>
            <span class="bold"><strong>Boost.Chrono</strong></span> provides few simple rounding
            utility functions for working with durations.
          </p>
<pre class="programlisting"><span class="comment">// round down</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">To</span></code></a><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="identifier">To</span>
<span class="identifier">floor</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">duration</span><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;&amp;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">To</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
<span class="special">}</span>

<span class="comment">// round to nearest, to even on tie</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">To</span></code></a><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="identifier">To</span>
<span class="identifier">round</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">duration</span><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;&amp;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">To</span> <span class="identifier">t0</span> <span class="special">=</span> <span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">To</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
    <span class="identifier">To</span> <span class="identifier">t1</span> <span class="special">=</span> <span class="identifier">t0</span><span class="special">;</span>
    <span class="special">++</span><span class="identifier">t1</span><span class="special">;</span>
    <span class="identifier">BOOST_AUTO</span><span class="special">(</span><span class="identifier">diff0</span><span class="special">,</span> <span class="identifier">d</span> <span class="special">-</span> <span class="identifier">t0</span><span class="special">);</span>
    <span class="identifier">BOOST_AUTO</span><span class="special">(</span><span class="identifier">diff1</span><span class="special">,</span> <span class="identifier">t1</span> <span class="special">-</span> <span class="identifier">d</span><span class="special">);</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">diff0</span> <span class="special">==</span> <span class="identifier">diff1</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">t0</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&amp;</span> <span class="number">1</span><span class="special">)</span>
            <span class="keyword">return</span> <span class="identifier">t1</span><span class="special">;</span>
        <span class="keyword">return</span> <span class="identifier">t0</span><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">else</span> <span class="keyword">if</span> <span class="special">(</span><span class="identifier">diff0</span> <span class="special">&lt;</span> <span class="identifier">diff1</span><span class="special">)</span>
        <span class="keyword">return</span> <span class="identifier">t0</span><span class="special">;</span>
    <span class="keyword">return</span> <span class="identifier">t1</span><span class="special">;</span>
<span class="special">}</span>
<span class="comment">// round up</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">To</span></code></a><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="identifier">To</span>
<span class="identifier">ceil</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">duration</span><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;&amp;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">To</span> <span class="identifier">t</span> <span class="special">=</span> <span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">To</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">t</span> <span class="special">&lt;</span> <span class="identifier">d</span><span class="special">)</span>
        <span class="special">++</span><span class="identifier">t</span><span class="special">;</span>
    <span class="keyword">return</span> <span class="identifier">t</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            The beauty of the chrono library is the ease and accuracy with which
            such conversions can be made. For example to convert from <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">milliseconds</span></code></a> (<code class="computeroutput"><span class="number">1</span><span class="special">/</span><span class="number">1000</span></code>
            of a second), to <code class="computeroutput"><span class="number">1</span><span class="special">/</span><span class="number">30</span></code> of a second, one must multiply the milliseconds
            by <code class="computeroutput"><span class="number">0.03</span></code>. It is common knowledge
            that you can't exactly represent <code class="computeroutput"><span class="number">0.03</span></code>
            in a computer. Nevertheless round will exactly (with no round off error)
            detect a tie and round to even when this happens. The differences <code class="computeroutput"><span class="identifier">diff0</span></code> and <code class="computeroutput"><span class="identifier">diff1</span></code>
            are not approximate, but exact differences, even when <code class="computeroutput"><span class="identifier">d</span></code>
            has the units of millisecond and <code class="computeroutput"><span class="identifier">To</span></code>
            is <code class="computeroutput"><span class="number">1</span><span class="special">/</span><span class="number">30</span></code> of a second. The unit of <code class="computeroutput"><span class="identifier">diff0</span></code> and <code class="computeroutput"><span class="identifier">diff1</span></code>
            is <code class="computeroutput"><span class="number">1</span><span class="special">/</span><span class="number">3000</span></code> of a second which both millisecond
            and <code class="computeroutput"><span class="number">1</span><span class="special">/</span><span class="number">30</span></code> of a second exactly convert to (with
            no truncation error).
          </p>
<p>
            Similarly, the comparison <code class="computeroutput"><span class="identifier">t</span>
            <span class="special">&lt;</span> <span class="identifier">d</span></code>
            in <a class="link" href="reference.html#chrono.reference.round.ceil_hpp" title="Header &lt;boost/chrono/ceil.hpp&gt;"><code class="computeroutput"><span class="identifier">ceil</span></code></a>
            is exact, even when there is no exact conversion between <code class="computeroutput"><span class="identifier">t</span></code> and <code class="computeroutput"><span class="identifier">d</span></code>.
            Example use of rounding functions
          </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">chrono_io</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">floor</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">round</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">ceil</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">;</span>
    <span class="identifier">milliseconds</span> <span class="identifier">ms</span><span class="special">(</span><span class="number">2500</span><span class="special">);</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">floor</span><span class="special">&lt;</span><span class="identifier">seconds</span><span class="special">&gt;(</span><span class="identifier">ms</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">round</span><span class="special">&lt;</span><span class="identifier">seconds</span><span class="special">&gt;(</span><span class="identifier">ms</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">ceil</span><span class="special">&lt;</span><span class="identifier">seconds</span><span class="special">&gt;(</span><span class="identifier">ms</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">ms</span> <span class="special">=</span> <span class="identifier">milliseconds</span><span class="special">(</span><span class="number">2516</span><span class="special">);</span>
    <span class="keyword">typedef</span> <span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">ratio</span><span class="special">&lt;</span><span class="number">1</span><span class="special">,</span> <span class="number">30</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">frame_rate</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">floor</span><span class="special">&lt;</span><span class="identifier">frame_rate</span><span class="special">&gt;(</span><span class="identifier">ms</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">round</span><span class="special">&lt;</span><span class="identifier">frame_rate</span><span class="special">&gt;(</span><span class="identifier">ms</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">ceil</span><span class="special">&lt;</span><span class="identifier">frame_rate</span><span class="special">&gt;(</span><span class="identifier">ms</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            The output of this program should be
          </p>
<pre class="programlisting"><span class="number">2</span> <span class="identifier">seconds</span>
<span class="number">2</span> <span class="identifier">seconds</span>
<span class="number">3</span> <span class="identifier">seconds</span>
<span class="number">75</span> <span class="special">[</span><span class="number">1</span><span class="special">/</span><span class="number">30</span><span class="special">]</span><span class="identifier">seconds</span>
<span class="number">75</span> <span class="special">[</span><span class="number">1</span><span class="special">/</span><span class="number">30</span><span class="special">]</span><span class="identifier">seconds</span>
<span class="number">76</span> <span class="special">[</span><span class="number">1</span><span class="special">/</span><span class="number">30</span><span class="special">]</span><span class="identifier">seconds</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.duration.trafficking_in_floating_point_durations"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.duration.trafficking_in_floating_point_durations" title="Trafficking in floating-point Durations">Trafficking
          in floating-point Durations</a>
</h5></div></div></div>
<p>
            I don't want to deal with writing <code class="computeroutput"><span class="identifier">duration_cast</span></code>
            all over the place. I'm content with the precision of my floating-point
            representation.
          </p>
<p>
            Not a problem. When the destination of a conversion has floating-point
            representation, all conversions are allowed to happen implicitly.
          </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">double</span><span class="special">,</span> <code class="computeroutput"><span class="identifier">ratio</span></code><span class="special">&lt;</span><span class="number">60</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">dminutes</span><span class="special">;</span>
<span class="identifier">dminutes</span> <span class="identifier">dm4</span> <span class="special">=</span> <span class="identifier">m3</span> <span class="special">+</span> <span class="identifier">us3</span><span class="special">;</span>  <span class="comment">// dm4.count() == 5.000000083333333</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.duration.how_expensive_is_all_of_this_"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.duration.how_expensive_is_all_of_this_" title="How Expensive is All of this?">How
          Expensive is All of this?</a>
</h5></div></div></div>
<p>
            If you were writing these conversions by hand, you could not make it
            more efficient. The use of <code class="computeroutput"><span class="identifier">ratio</span></code>
            ensures that all conversion constants are simplified as much as possible
            at compile-time. This usually results in the numerator or denominator
            of the conversion factor simplifying to <code class="computeroutput"><span class="number">1</span></code>,
            and being subsequently ignored in converting the run-time values of the
            tick counts.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.duration.how_complicated_is_it_to_build_a_function_taking_a__duration__parameter_"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.duration.how_complicated_is_it_to_build_a_function_taking_a__duration__parameter_" title="How Complicated is it to Build a Function Taking a duration Parameter?">How
          Complicated is it to Build a Function Taking a <code class="computeroutput"><span class="identifier">duration</span></code>
          Parameter?</a>
</h5></div></div></div>
<p>
            There are several options open to the user:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
                If the author of the function wants to accept any <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>, and is willing
                to work in floating-point <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s, he can simply
                use any floating-point <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> as the parameter:
              </li></ul></div>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">f</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="identifier">d</span><span class="special">)</span>  <span class="comment">// accept floating-point seconds</span>
<span class="special">{</span>
    <span class="comment">// d.count() == 3.e-6 when passed boost::chrono::microseconds(3)</span>
<span class="special">}</span>

<span class="identifier">f</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">microseconds</span><span class="special">(</span><span class="number">3</span><span class="special">));</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
                If the author of the function wants to traffic only in integral
                <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s, and is content
                with handling nothing finer than say nanoseconds (just as an example),
                he can simply specify nanoseconds as the parameter:
              </li></ul></div>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">f</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">nanoseconds</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="comment">// d.count() == 3000 when passed boost::chrono::microseconds(3)</span>
<span class="special">}</span>

<span class="identifier">f</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">microseconds</span><span class="special">(</span><span class="number">3</span><span class="special">));</span>
</pre>
<p>
            In this design, if the client wants to pass in a floating-point <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>, or a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> of finer precision
            than nanoseconds, then the client is responsible for choosing his own
            rounding mode in the conversion to nanoseconds.
          </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="identifier">s</span><span class="special">(</span><span class="number">1.</span><span class="special">/</span><span class="number">3</span><span class="special">);</span>  <span class="comment">// 1/3 of a second</span>
<span class="identifier">f</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">nanoseconds</span><span class="special">&gt;(</span><span class="identifier">s</span><span class="special">));</span>  <span class="comment">// round towards zero in conversion to nanoseconds</span>
</pre>
<p>
            In the example above, the client of f has chosen "round towards
            zero" as the desired rounding mode to nanoseconds. If the client
            has a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> that won't exactly
            convert to nanoseconds, and fails to choose how the conversion will take
            place, the compiler will refuse the call:
          </p>
<pre class="programlisting"><span class="identifier">f</span><span class="special">(</span><span class="identifier">s</span><span class="special">);</span>  <span class="comment">// does not compile</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
                If the author of the function wants to accept any <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>, but wants to work
                with integral representations and wants to control the rounding mode
                internally, then he can template the function:
              </li></ul></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">f</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="comment">// convert d to nanoseconds, rounding up if it is not an exact conversion</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">nanoseconds</span> <span class="identifier">ns</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">nanoseconds</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">ns</span> <span class="special">&lt;</span> <span class="identifier">d</span><span class="special">)</span>
        <span class="special">++</span><span class="identifier">ns</span><span class="special">;</span>
    <span class="comment">// ns.count() == 333333334 when passed 1/3 of a floating-point second</span>
<span class="special">}</span>

  <span class="identifier">f</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">1.</span><span class="special">/</span><span class="number">3</span><span class="special">));</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
                If the author in the example does not want to accept floating-point
                based <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s, he can enforce
                that behavior like so:
              </li></ul></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">f</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="comment">// convert d to nanoseconds, rounding up if it is not an exact conversion</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">nanoseconds</span> <span class="identifier">ns</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">nanoseconds</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">ns</span> <span class="special">&lt;</span> <span class="identifier">d</span><span class="special">)</span>
        <span class="special">++</span><span class="identifier">ns</span><span class="special">;</span>
    <span class="comment">// ns.count() == 333333334 when passed 333333333333 picoseconds</span>
<span class="special">}</span>
<span class="comment">// About 1/3 of a second worth of picoseconds</span>
<span class="identifier">f</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">pico</span><span class="special">&gt;(</span><span class="number">333333333333</span><span class="special">));</span>
</pre>
<p>
            Clients with floating-point <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s who want to use f
            will now have to convert to an integral <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> themselves before passing
            the result to f.
          </p>
<p>
            In summary, the author of f has quite a bit of flexibility and control
            in the interface he wants to provide his clients with, and easy options
            for manipulating that <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> internal to his function.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.duration.is_it_possible_for_the_user_to_pass_a___duration_to_a_function_with_the_units_being_ambiguous_"></a>Is it possible for the user to pass a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> to a function with the
          units being ambiguous?</h5></div></div></div>
<p>
            No. No matter which option the author of <code class="computeroutput"><span class="identifier">f</span></code>
            chooses above, the following client code will not compile:
          </p>
<pre class="programlisting"><span class="identifier">f</span><span class="special">(</span><span class="number">3</span><span class="special">);</span>  <span class="comment">// Will not compile, 3 is not implicitly convertible to any __duration</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.duration.can_durations_overflow_"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.duration.can_durations_overflow_" title="Can Durations Overflow?">Can
          Durations Overflow?</a>
</h5></div></div></div>
<p>
            This depend on the representation. The default typedefs uses a representation
            that don't handle overflows. The user can define his own representation
            that manage overflow as required by its application.
          </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.tutorial.clocks"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.clocks" title="Clocks">Clocks</a>
</h4></div></div></div>
<p>
          While <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s only have precision
          and representation to concern themselves, clocks and <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s are intimately related
          and refer to one another. Because clocks are simpler to explain, we will
          do so first without fully explaining <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s. Once clocks are introduced,
          it will be easier to then fill in what a <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> is.
        </p>
<p>
          A clock is a concept which bundles 3 things:
        </p>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
              A concrete <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> type.
            </li>
<li class="listitem">
              A concrete <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> type.
            </li>
<li class="listitem">
              A function called now() which returns the concrete <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>.
            </li>
</ol></div>
<p>
          The standard defines three system-wide clocks that are associated to the
          computer time.
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a> represents system-wide
              realtime clock that can be synchronized with an external clock.
            </li>
<li class="listitem">
              <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a> can not be changed
              explicitly and the time since the initial epoch increase in a steady
              way.
            </li>
<li class="listitem">
              <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.high_resolution_clock" title="Class high_resolution_clock"><code class="computeroutput"><span class="identifier">high_resolution_clock</span></code></a> intend
              to use the system-wide clock provided by the platform with the highest
              resolution.
            </li>
</ul></div>
<p>
          <span class="bold"><strong>Boost.Chrono</strong></span> provides them when supported
          by the underlying platform. A given platform may not be able to supply
          all three of these clocks.
        </p>
<p>
          The library adds some clocks that are specific to a process or a thread,
          that is there is a clock per process or per thread.
        </p>
<p>
          The user is also able to easily create more clocks.
        </p>
<p>
          Given a clock named Clock, it will have:
        </p>
<pre class="programlisting"><span class="keyword">class</span> <span class="identifier">Clock</span> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">typedef</span> <span class="identifier">an</span> <span class="identifier">arithmetic</span><span class="special">-</span><span class="identifier">like</span> <span class="identifier">type</span>        <span class="identifier">rep</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">an</span> <span class="identifier">instantiation</span> <span class="identifier">of</span> <span class="identifier">ratio</span>      <span class="identifier">period</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">rep</span><span class="special">,</span> <span class="identifier">period</span><span class="special">&gt;</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">&gt;</span>     <span class="identifier">time_point</span><span class="special">;</span>
    <span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="keyword">bool</span> <span class="identifier">is_steady</span> <span class="special">=</span>      <span class="keyword">true</span> <span class="keyword">or</span> <span class="keyword">false</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">time_point</span> <span class="identifier">now</span><span class="special">();</span>
<span class="special">};</span>
</pre>
<p>
          One can get the current time from Clock with:
        </p>
<pre class="programlisting"><span class="identifier">Clock</span><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">t1</span> <span class="special">=</span> <span class="identifier">Clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
</pre>
<p>
          And one can get the time <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> between two <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s associated with Clock
          with:
        </p>
<pre class="programlisting"><span class="identifier">Clock</span><span class="special">::</span><span class="identifier">duration</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">Clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">-</span> <span class="identifier">t1</span><span class="special">;</span>
</pre>
<p>
          And one can specify a past or future <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> with:
        </p>
<pre class="programlisting"><span class="identifier">Clock</span><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">t2</span> <span class="special">=</span> <span class="identifier">Clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">d</span><span class="special">;</span>
</pre>
<p>
          Note how even if a particular clock becomes obsolete, the next clock in
          line will have the same API. There is no new learning curve to come up.
          The only source code changes will be simply changing the type of the clock.
          The same <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> and <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> framework continues
          to work as new clocks are introduced. And multiple clocks are safely and
          easily handled within the same program.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.tutorial.time_point"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.time_point" title="Time Point">Time Point</a>
</h4></div></div></div>
<div class="toc"><dl class="toc"><dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.time_point.so_what_exactly_is_a__time_point__and_how_do_i_use_one_">So
          What Exactly is a <code class="computeroutput"><span class="identifier">time_point</span></code>
          and How Do I Use One?</a></span></dt></dl></div>
<p>
          A <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> represents a point
          in time, as opposed to a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> of time. Another way
          of saying the same thing, is that a <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> represents an epoch
          plus or minus a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>. Examples of <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s include:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              3 minutes after the computer booted.
            </li>
<li class="listitem">
              03:14:07 UTC on Tuesday, January 19, 2038
            </li>
<li class="listitem">
              20 milliseconds after I started that timer.
            </li>
</ul></div>
<p>
          In each of the examples above, a different epoch is implied. Sometimes
          an epoch has meaning for several millennia. Other times the meaning of
          an epoch is lost after a while (such as the start of a timer, or when the
          computer booted). However, if two <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s are known to share
          the same epoch, they can be subtracted, yielding a valid <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>, even if the definition
          of the epoch no longer has meaning.
        </p>
<p>
          In <span class="bold"><strong>Boost.Chrono</strong></span>, an epoch is a purely
          abstract and unspecified concept. There is no type representing an epoch.
          It is simply an idea that relates (or doesn't) <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s to a clock, and in
          the case that they share a clock, <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s to one another. <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s associated with different
          clocks are generally not interoperable unless the relationship between
          the epochs associated with each clock is known.
        </p>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.time_point.so_what_exactly_is_a__time_point__and_how_do_i_use_one_"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.time_point.so_what_exactly_is_a__time_point__and_how_do_i_use_one_" title="So What Exactly is a time_point and How Do I Use One?">So
          What Exactly is a <code class="computeroutput"><span class="identifier">time_point</span></code>
          and How Do I Use One?</a>
</h5></div></div></div>
<p>
            A <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> has a clock and a
            <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>.
          </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a> <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">Clock</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">;</span>
</pre>
<p>
            The <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>'s clock is not stored.
            It is simply embedded into the <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>'s type and serves
            two purposes:
          </p>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
                Because <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s originating
                from different clocks have different types, the compiler can be instructed
                to fail if incompatible <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s are used in
                inappropriate ways.
              </li>
<li class="listitem">
                Given a <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>, one often needs
                to compare that <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> to "now".
                This is very simple as long as the <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> knows what clock
                it is defined with respect to.
              </li>
</ol></div>
<p>
            A <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>'s <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> is stored as the only
            data member of the <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>. Thus <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s and their corresponding
            <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> have exactly the same
            layout. But they have very different meanings. For example, it is one
            thing to say I want to sleep for 3 minutes. It is a completely different
            thing to say I want to sleep until 3 minutes past the time I started
            that timer (unless you just happened to start that timer now). Both meanings
            (and options for sleeping) have great practical value in common use cases
            for sleeping, waiting on a condition variable, and waiting for a mutex's
            lock. These same concepts and tools are found (for example) in Ada.
          </p>
<p>
            A timer example:
          </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">f</span><span class="special">()</span>
<span class="special">{</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">steady_clock</span><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">start</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">steady_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
    <span class="identifier">g</span><span class="special">();</span>
    <span class="identifier">h</span><span class="special">();</span>
    <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="identifier">sec</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">steady_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"f() took "</span> <span class="special">&lt;&lt;</span> <span class="identifier">sec</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" seconds\n"</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            Note that if one is using the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> between two clock
            <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s in a way where the
            precision of the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> matters, it is good
            practice to convert the clock's <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> to a known <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>. This insulates the
            code from future changes which may be made to the clock's precision in
            the future. For example <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a> could easily be
            based on the clock speed of the cpu. When you upgrade to a faster machine,
            you do not want your code that assumed a certain tick period of this
            clock to start experiencing run-time failures because your timing code
            has silently changed meaning.
          </p>
<p>
            A delay loop example:
          </p>
<pre class="programlisting"><span class="comment">// delay for at least 500 nanoseconds:</span>
<span class="keyword">auto</span> <span class="identifier">go</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">steady_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">nanoseconds</span><span class="special">(</span><span class="number">500</span><span class="special">);</span>
<span class="keyword">while</span> <span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">steady_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">&lt;</span> <span class="identifier">go</span><span class="special">)</span>
    <span class="special">;</span>
</pre>
<p>
            The above code will delay as close as possible to half a microsecond,
            no matter what the precision of <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a> is. The more precise
            <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a> becomes, the more
            accurate will be the delay to 500 nanoseconds.
          </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.tutorial.specific_clocks"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.specific_clocks" title="Specific Clocks">Specific
        Clocks</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.system_clock">system_clock</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.steady_clock">steady_clock</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.high_resolution_clock">high_resolution_clock</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.process_cpu_clock">process_cpu_clock</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.thread_clock">thread_clock</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.specific_clocks.system_clock"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.system_clock" title="system_clock">system_clock</a>
</h5></div></div></div>
<p>
            <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a> is useful when
            you need to correlate the time with a known epoch so you can convert
            it to a calendar time. Note the specific functions in the <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a> class.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.specific_clocks.steady_clock"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.steady_clock" title="steady_clock">steady_clock</a>
</h5></div></div></div>
<p>
            <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a> is useful when
            you need to wait for a specific amount of time. <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a> time can not be
            reset. As other steady clocks, it is usually based on the processor tick.
          </p>
<p>
            Here is a polling solution, but it will probably be too inefficient:
          </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">start</span><span class="special">=</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a><span class="special">::</span><span class="identifier">duration</span> <span class="identifier">delay</span><span class="special">=</span> <span class="identifier">chrono</span><span class="special">::</span><span class="identifier">seconds</span><span class="special">(</span><span class="number">5</span><span class="special">);</span>
<span class="keyword">while</span> <span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">-</span> <span class="identifier">start</span> <span class="special">&lt;=</span> <span class="identifier">delay</span><span class="special">)</span> <span class="special">{}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.specific_clocks.high_resolution_clock"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.high_resolution_clock" title="high_resolution_clock">high_resolution_clock</a>
</h5></div></div></div>
<p>
            When available, <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.high_resolution_clock" title="Class high_resolution_clock"><code class="computeroutput"><span class="identifier">high_resolution_clock</span></code></a> is usually
            more expensive than the other system-wide clocks, so they are used only
            when the provided resolution is required to the application.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.specific_clocks.process_cpu_clock"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.process_cpu_clock" title="process_cpu_clock">process_cpu_clock</a>
</h5></div></div></div>
<p>
            Process and thread clocks are used usually to measure the time spent
            by code blocks, as a basic time-spent profiling of different blocks of
            code (Boost.Chrono.Stopwatch is a clear example of this use).
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.specific_clocks.thread_clock"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.specific_clocks.thread_clock" title="thread_clock">thread_clock</a>
</h5></div></div></div>
<p>
            You can use <a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a> whenever you want
            to measure the time spent by the current thread. For example:
          </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">start</span><span class="special">=</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
<span class="comment">// ... do something ...</span>

<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">milliseconds</span></code></a> <span class="identifier">ms</span><span class="special">;</span>
<span class="identifier">ms</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">;</span>
<span class="comment">// d now holds the number of milliseconds from start to end.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">d</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">"ms\n"</span><span class="special">;</span>
</pre>
<p>
            If you need seconds with a floating-point representation you can do:
          </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="identifier">sec</span><span class="special">;</span>  <span class="comment">// seconds, stored with a double.</span>
<span class="identifier">sec</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">end</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">sec</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">"s\n"</span><span class="special">;</span>
</pre>
<p>
            If you would like to programmatically inspect <code class="computeroutput"><a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a><span class="special">::</span><span class="identifier">duration</span></code>, you can get the representation
            type with <code class="computeroutput"><a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a><span class="special">::</span><span class="identifier">rep</span></code>, and the tick period with <code class="computeroutput"><a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a><span class="special">::</span><span class="identifier">period</span></code> (which should be a type <code class="computeroutput"><span class="identifier">ratio</span></code> which has nested values <code class="computeroutput"><code class="computeroutput"><span class="identifier">ratio</span></code><span class="special">::</span><span class="identifier">num</span></code> and <code class="computeroutput"><code class="computeroutput"><span class="identifier">ratio</span></code><span class="special">::</span><span class="identifier">den</span></code>).
            The tick period of <a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a> is <code class="computeroutput"><a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a><span class="special">::</span><span class="identifier">period</span><span class="special">::</span><span class="identifier">num</span> <span class="special">/</span> <a class="link" href="reference.html#chrono.reference.other_clocks.thread_clock_hpp.thread_clock" title="Class thread_clock"><code class="computeroutput"><span class="identifier">thread_clock</span></code></a><span class="special">::</span><span class="identifier">period</span><span class="special">::</span><span class="identifier">den</span></code> seconds: <code class="computeroutput"><span class="number">1</span><span class="special">/</span><span class="number">1000000000</span></code>
            in this case (<code class="computeroutput"><span class="number">1</span></code> billionth
            of a second), stored in a <code class="computeroutput"><span class="keyword">long</span>
            <span class="keyword">long</span></code>.
          </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.tutorial.i_o"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.i_o" title="I/O">I/O</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.i_o.duration_io">duration</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.i_o.system_clock_time_point_io"><code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code></a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.i_o.other_clocks_time_point_io">Other
          clocks time_point</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.tutorial.i_o.low_level_i_o">Low
          level I/O</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.i_o.duration_io"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.i_o.duration_io" title="duration">duration</a>
</h5></div></div></div>
<p>
            Any <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> can be streamed out
            to a <code class="computeroutput"><span class="identifier">basic_ostream</span></code>. The
            run-time value of the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> is formatted according
            to the rules and current format settings for <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><code class="computeroutput"><span class="special">::</span><span class="identifier">rep</span></code> get_duration_style and the durationpunct
            facet.
          </p>
<p>
            the format is either
          </p>
<pre class="programlisting"><span class="special">&lt;</span><span class="identifier">value</span><span class="special">&gt;</span> <span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&gt;</span>
</pre>
<p>
            or
          </p>
<pre class="programlisting"><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&gt;</span> <span class="special">&lt;</span><span class="identifier">value</span><span class="special">&gt;</span>
</pre>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top">
<p>
              Need to be changed This is followed by a single space and then the
              compile-time unit name of the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>. This unit name is
              built on the string returned from <code class="computeroutput"><span class="identifier">ratio_string</span><span class="special">&lt;&gt;</span></code> and the data used to construct
              the <a class="link" href="reference.html#chrono.reference.io_v1.chrono_io_hpp.duration_punct" title="Template Class duration_punct&lt;&gt;"><code class="computeroutput"><span class="identifier">duration_punct</span></code></a> which was inserted
              into the stream's locale. If a <a class="link" href="reference.html#chrono.reference.io_v1.chrono_io_hpp.duration_punct" title="Template Class duration_punct&lt;&gt;"><code class="computeroutput"><span class="identifier">duration_punct</span></code></a> has not been
              inserted into the stream's locale, a default constructed <a class="link" href="reference.html#chrono.reference.io_v1.chrono_io_hpp.duration_punct" title="Template Class duration_punct&lt;&gt;"><code class="computeroutput"><span class="identifier">duration_punct</span></code></a> will be added
              to the stream's locale.
            </p>
<p>
              <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> unit names come in
              two varieties: long(prefix) and short(symbol). The default constructed
              <a class="link" href="reference.html#chrono.reference.io_v1.chrono_io_hpp.duration_punct" title="Template Class duration_punct&lt;&gt;"><code class="computeroutput"><span class="identifier">duration_punct</span></code></a> provides names
              in the long(prefix) format. These names are English descriptions. Other
              languages are supported by constructing a <a class="link" href="reference.html#chrono.reference.io_v1.chrono_io_hpp.duration_punct" title="Template Class duration_punct&lt;&gt;"><code class="computeroutput"><span class="identifier">duration_punct</span></code></a> with the proper
              spellings for "hours", "minutes" and "seconds",
              and their abbreviations (for the short format). The short or long format
              can be easily chosen by streaming a <code class="computeroutput"><span class="identifier">duration_short</span><span class="special">()</span></code> or <code class="computeroutput"><span class="identifier">duration_long</span><span class="special">()</span></code> manipulator respectively or using
              the parameterized manipulator <code class="computeroutput"><span class="identifier">duration_fmt</span><span class="special">(</span><span class="identifier">duration_style</span><span class="special">::</span><span class="identifier">prefix</span><span class="special">)</span></code> or <code class="computeroutput"><span class="identifier">duration_fmt</span><span class="special">(</span><span class="identifier">duration_style</span><span class="special">::</span><span class="identifier">symbol</span><span class="special">)</span></code>.
            </p>
</td></tr>
</table></div>
<p>
            <span class="bold"><strong>Example:</strong></span>
          </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">chrono_io</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">;</span>

    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"milliseconds(1) = "</span>
         <span class="special">&lt;&lt;</span>  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">milliseconds</span><span class="special">(</span><span class="number">1</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>

    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"milliseconds(3) + microseconds(10) = "</span>
         <span class="special">&lt;&lt;</span>  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">milliseconds</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">microseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>

    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"hours(3) + minutes(10) = "</span>
         <span class="special">&lt;&lt;</span>  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">hours</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">minutes</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">ratio</span><span class="special">&lt;</span><span class="number">1</span><span class="special">,</span> <span class="number">2500000000</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">ClockTick</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"ClockTick(3) + boost::chrono::nanoseconds(10) = "</span>
         <span class="special">&lt;&lt;</span>  <span class="identifier">ClockTick</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">nanoseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>

   <span class="comment">// ...</span>
    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            The output could be
          </p>
<pre class="programlisting"><span class="identifier">milliseconds</span><span class="special">(</span><span class="number">1</span><span class="special">)</span> <span class="special">=</span> <span class="number">1</span> <span class="identifier">microsecond</span>
<span class="identifier">milliseconds</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">microseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">=</span> <span class="number">3010</span> <span class="identifier">microseconds</span>
<span class="identifier">hours</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">minutes</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">=</span> <span class="number">190</span> <span class="identifier">minutes</span>
<span class="identifier">ClockTick</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">nanoseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">=</span> <span class="number">56</span> <span class="special">[</span><span class="number">1</span><span class="special">/</span><span class="number">5000000000</span><span class="special">]</span><span class="identifier">seconds</span>

<span class="identifier">Set</span> <span class="identifier">cout</span> <span class="identifier">to</span> <span class="identifier">use</span> <span class="keyword">short</span> <span class="identifier">names</span><span class="special">:</span>
<span class="identifier">milliseconds</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">microseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">=</span> <span class="number">3010</span> μs
<span class="identifier">hours</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">minutes</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">=</span> <span class="number">190</span> <span class="identifier">m</span>
<span class="identifier">ClockTick</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">nanoseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">=</span> <span class="number">56</span> <span class="special">[</span><span class="number">1</span><span class="special">/</span><span class="number">5000000000</span><span class="special">]</span><span class="identifier">s</span>

<span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">=</span> <span class="number">129387415616250000</span> <span class="special">[</span><span class="number">1</span><span class="special">/</span><span class="number">10000000</span><span class="special">]</span><span class="identifier">s</span> <span class="identifier">since</span> <span class="identifier">Jan</span> <span class="number">1</span><span class="special">,</span> <span class="number">1970</span>
<span class="identifier">monotonic_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">=</span> <span class="number">37297387636417</span> <span class="identifier">ns</span> <span class="identifier">since</span> <span class="identifier">boot</span>

<span class="identifier">Set</span> <span class="identifier">cout</span> <span class="identifier">to</span> <span class="identifier">use</span> <span class="keyword">long</span> <span class="identifier">names</span><span class="special">:</span>
<span class="identifier">high_resolution_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">=</span> <span class="number">37297387655134</span> <span class="identifier">nanoseconds</span> <span class="identifier">since</span> <span class="identifier">boot</span>
</pre>
<p>
            As can be seen, each duration type can be streamed without having to
            manually stream the compile-time units after the run-time value. And
            when the compile-time unit is known to be a "common unit",
            English names are used. For "uncommon units" a unit name is
            composed from the reduced numerator and denominator of the associated
            <code class="computeroutput"><span class="identifier">ratio</span></code>. Whatever stream/locale
            settings are set for <code class="computeroutput"><span class="identifier">duration</span><span class="special">::</span><span class="identifier">rep</span></code>
            are used for the value. Additionally, when the value is 1, singular forms
            for the units are used.
          </p>
<p>
            Sometimes it is desired to shorten these names by using the SI symbols
            instead of SI prefixes. This can be accomplished with the use of the
            <a class="link" href="reference.html#chrono.reference.io.duration_io_hpp.manipulators.symbol_format" title="Non Member Function symbol_format(ios_base&amp;)"><code class="computeroutput"><span class="identifier">symbol_format</span></code></a> manipulator <a href="#ftn.chrono.users_guide.tutorial.i_o.duration_io.f0" class="footnote" name="chrono.users_guide.tutorial.i_o.duration_io.f0"><sup class="footnote">[1]</sup></a>:
          </p>
<pre class="programlisting"><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"\nSet cout to use short names:\n"</span><span class="special">;</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">symbol_format</span><span class="special">;</span>

<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"milliseconds(3) + microseconds(10) = "</span>
     <span class="special">&lt;&lt;</span>  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">milliseconds</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">microseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>

<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"hours(3) + minutes(10) = "</span>
     <span class="special">&lt;&lt;</span>  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">hours</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">minutes</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>

<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"ClockTick(3) + nanoseconds(10) = "</span>
     <span class="special">&lt;&lt;</span>  <span class="identifier">ClockTick</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">nanoseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
</pre>
<p>
            The output could be
          </p>
<pre class="programlisting"><span class="identifier">Set</span> <span class="identifier">cout</span> <span class="identifier">to</span> <span class="identifier">use</span> <span class="keyword">short</span> <span class="identifier">names</span><span class="special">:</span>
<span class="identifier">milliseconds</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">microseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">=</span> <span class="number">3010</span> μs
<span class="identifier">hours</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">minutes</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">=</span> <span class="number">190</span> <span class="identifier">m</span>
<span class="identifier">ClockTick</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">nanoseconds</span><span class="special">(</span><span class="number">10</span><span class="special">)</span> <span class="special">=</span> <span class="number">56</span> <span class="special">[</span><span class="number">1</span><span class="special">/</span><span class="number">5000000000</span><span class="special">]</span><span class="identifier">s</span>

<span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">=</span> <span class="number">129387415616250000</span> <span class="special">[</span><span class="number">1</span><span class="special">/</span><span class="number">10000000</span><span class="special">]</span><span class="identifier">s</span> <span class="identifier">since</span> <span class="identifier">Jan</span> <span class="number">1</span><span class="special">,</span> <span class="number">1970</span>
<span class="identifier">monotonic_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">=</span> <span class="number">37297387636417</span> <span class="identifier">ns</span> <span class="identifier">since</span> <span class="identifier">boot</span>

<span class="identifier">Set</span> <span class="identifier">cout</span> <span class="identifier">to</span> <span class="identifier">use</span> <span class="keyword">long</span> <span class="identifier">names</span><span class="special">:</span>
<span class="identifier">high_resolution_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">=</span> <span class="number">37297387655134</span> <span class="identifier">nanoseconds</span> <span class="identifier">since</span> <span class="identifier">boot</span>
</pre>
<p>
            The μ for microsecond is specified to be U+00B5, encoded as UTF-8, UTF-16
            or UTF-32 as appropriate for the stream's character size.
          </p>
<p>
            When the format decision is taken at runtime, it could be better to use
            the parameterized manipulator <a class="link" href="reference.html#chrono.reference.io.duration_io_hpp.manipulators.duration_fmt" title="Template Class duration_fmt"><code class="computeroutput"><span class="identifier">duration_fmt</span></code></a> as in
          </p>
<pre class="programlisting"><span class="identifier">duration_style</span> <span class="identifier">style</span><span class="special">;</span>
<span class="comment">//...</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">duration_fmt</span><span class="special">(</span><span class="identifier">style</span><span class="special">);</span>
</pre>
<p>
            Parsing a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> follows rules analogous
            to the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> converting constructor.
            A value and a unit (SI symbol or prefixed) are read from the <code class="computeroutput"><span class="identifier">basic_istream</span></code>. If the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> has an integral representation,
            then the value parsed must be exactly representable in the target <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> (after conversion to
            the target <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> units), else <code class="computeroutput"><span class="identifier">failbit</span></code> is set. <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s based on floating-point
            representations can be parsed using any units that do not cause overflow.
          </p>
<p>
            For example a stream containing "5000 milliseconds" can be
            parsed into seconds, but if the stream contains "3001 ms",
            parsing into <code class="computeroutput"><span class="identifier">seconds</span></code>
            will cause <code class="computeroutput"><span class="identifier">failbit</span></code> to
            be set.
          </p>
<p>
            <span class="bold"><strong>Example:</strong></span>
          </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">chrono_io</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">sstream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">cassert</span><span class="special">&gt;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>

    <span class="identifier">istringstream</span> <span class="identifier">in</span><span class="special">(</span><span class="string">"5000 milliseconds 4000 ms 3001 ms"</span><span class="special">);</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">seconds</span> <span class="identifier">d</span><span class="special">(</span><span class="number">0</span><span class="special">);</span>
    <span class="identifier">in</span> <span class="special">&gt;&gt;</span> <span class="identifier">d</span><span class="special">;</span>
    <span class="identifier">assert</span><span class="special">(</span><span class="identifier">in</span><span class="special">.</span><span class="identifier">good</span><span class="special">());</span>
    <span class="identifier">assert</span><span class="special">(</span><span class="identifier">d</span> <span class="special">==</span> <span class="identifier">seconds</span><span class="special">(</span><span class="number">5</span><span class="special">));</span>
    <span class="identifier">in</span> <span class="special">&gt;&gt;</span> <span class="identifier">d</span><span class="special">;</span>
    <span class="identifier">assert</span><span class="special">(</span><span class="identifier">in</span><span class="special">.</span><span class="identifier">good</span><span class="special">());</span>
    <span class="identifier">assert</span><span class="special">(</span><span class="identifier">d</span> <span class="special">==</span> <span class="identifier">seconds</span><span class="special">(</span><span class="number">4</span><span class="special">));</span>
    <span class="identifier">in</span> <span class="special">&gt;&gt;</span> <span class="identifier">d</span><span class="special">;</span>
    <span class="identifier">assert</span><span class="special">(</span><span class="identifier">in</span><span class="special">.</span><span class="identifier">fail</span><span class="special">());</span>
    <span class="identifier">assert</span><span class="special">(</span><span class="identifier">d</span> <span class="special">==</span> <span class="identifier">seconds</span><span class="special">(</span><span class="number">4</span><span class="special">));</span>

    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            Note that a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> failure may occur late
            in the parsing process. This means that the characters making up the
            failed parse in the stream are usually consumed despite the failure to
            successfully parse.
          </p>
<p>
            Sometimes in templated code it is difficult to know what the unit of
            your duration is. It is all deterministic, and inspect-able. But it can
            be inconvenient to do so, especially if you just need to print out a
            "debugging" statement. For example:
          </p>
<pre class="programlisting"><span class="comment">// round to nearest, to even on tie</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">To</span></code></a><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="identifier">To</span>
<span class="identifier">round</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">duration</span><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;&amp;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">To</span> <span class="identifier">t0</span> <span class="special">=</span> <span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">To</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
    <span class="identifier">To</span> <span class="identifier">t1</span> <span class="special">=</span> <span class="identifier">t0</span><span class="special">;</span>
    <span class="special">++</span><span class="identifier">t1</span><span class="special">;</span>
    <span class="keyword">auto</span> <span class="identifier">diff0</span> <span class="special">=</span> <span class="identifier">d</span> <span class="special">-</span> <span class="identifier">t0</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"diff0 = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">diff0</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="keyword">auto</span> <span class="identifier">diff1</span> <span class="special">=</span> <span class="identifier">t1</span> <span class="special">-</span> <span class="identifier">d</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"diff1 = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">diff1</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">diff0</span> <span class="special">==</span> <span class="identifier">diff1</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">t0</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&amp;</span> <span class="number">1</span><span class="special">)</span>
            <span class="keyword">return</span> <span class="identifier">t1</span><span class="special">;</span>
        <span class="keyword">return</span> <span class="identifier">t0</span><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">else</span> <span class="keyword">if</span> <span class="special">(</span><span class="identifier">diff0</span> <span class="special">&lt;</span> <span class="identifier">diff1</span><span class="special">)</span>
        <span class="keyword">return</span> <span class="identifier">t0</span><span class="special">;</span>
    <span class="keyword">return</span> <span class="identifier">t1</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            This is where I/O for duration really shines. The compiler knows what
            the type of diff0 is and with this proposal that type (with proper units)
            will automatically be printed out for you. For example:
          </p>
<pre class="programlisting"><span class="identifier">milliseconds</span> <span class="identifier">ms</span> <span class="special">=</span> <span class="identifier">round</span><span class="special">&lt;</span><span class="identifier">milliseconds</span><span class="special">&gt;(</span><span class="identifier">nanoseconds</span><span class="special">(</span><span class="number">123</span><span class="special">));</span>  <span class="comment">// diff0 = 123 nanoseconds</span>
                                                          <span class="comment">// diff1 = 999877 nanoseconds</span>
<span class="identifier">milliseconds</span> <span class="identifier">ms</span> <span class="special">=</span> <span class="identifier">round</span><span class="special">&lt;</span><span class="identifier">milliseconds</span><span class="special">&gt;(</span><span class="identifier">Ticks</span><span class="special">(</span><span class="number">44</span><span class="special">));</span>         <span class="comment">// diff0 = 2 [1/3000]seconds</span>
                                                          <span class="comment">// diff1 = 1 [1/3000]second</span>
</pre>
<p>
            This simple I/O will make duration so much more accessible to programmers.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.i_o.system_clock_time_point_io"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.i_o.system_clock_time_point_io" title="system_clock::time_point"><code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code></a>
</h5></div></div></div>
<p>
            <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a> is special. It
            is the only clock that has conversions between its <code class="computeroutput"><span class="identifier">time_point</span></code>
            and <code class="computeroutput"><span class="identifier">time_t</span></code>. C subsequently
            relates time_t to the <a href="http://en.wikipedia.org/wiki/Gregorian_calendar" target="_top">Gregorian
            calendar</a> via <code class="computeroutput"><span class="identifier">ctime</span></code>,
            <code class="computeroutput"><span class="identifier">gmtime</span></code>, <code class="computeroutput"><span class="identifier">localtime</span></code>, and <code class="computeroutput"><span class="identifier">strftime</span></code>.
            Neither C, nor POSIX relate <code class="computeroutput"><span class="identifier">time_t</span></code>
            to any calendar other than the <a href="http://en.wikipedia.org/wiki/Gregorian_calendar" target="_top">Gregorian
            calendar</a>. ISO 8601 is specified only in terms of the <a href="http://en.wikipedia.org/wiki/Gregorian_calendar" target="_top">Gregorian
            calendar</a>.
          </p>
<p>
            <span class="bold"><strong>Boost.Chrono</strong></span> provides <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code> I/O in terms of the Gregorian
            calendar, and no other calendar. However as <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code>
            remains convertible with <code class="computeroutput"><span class="identifier">time_t</span></code>,
            it is possible for clients to create other calendars which interoperate
            with <code class="computeroutput"><span class="identifier">time_t</span></code> and subsequently
            <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code>.
          </p>
<p>
            Furthermore, it is existing practice for all major hosted operating systems
            to store system time in a format which facilitates display as <a href="http://en.wikipedia.org/wiki/Coordinated_Universal_Time" target="_top">Coordinated
            Universal Time</a> (UTC). Therefore <span class="bold"><strong>Boost.Chrono</strong></span>
            provides that the default output for <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code>
            be in a format that represents a point in time with respect to UTC.
          </p>
<pre class="programlisting"><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
</pre>
<p>
            could output
          </p>
<pre class="programlisting"><span class="number">2011</span><span class="special">-</span><span class="number">09</span><span class="special">-</span><span class="number">15</span> <span class="number">18</span><span class="special">:</span><span class="number">36</span><span class="special">:</span><span class="number">59.325132</span> <span class="special">+</span><span class="number">0000</span>
</pre>
<p>
            This format is strongly influenced by ISO 8601, but places a ' ' between
            the date and time instead of a 'T'. The former appears to more accurately
            represent existing practice. A fully numeric format was chosen so as
            to be understandable to as large a group of human readers as possible.
            A 24 hour format was chosen for the same reasons.
          </p>
<p>
            Of the referenced standards, only ISO 8601 discusses the output of fractional
            seconds. Neither C nor POSIX have built-in functionality for this. However
            it appears to be universal (as of this writing) that <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">period</span></code>
            is sub-second. And it seems desirable that if you stream out a <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code>, you ought to be able to
            stream it back in and get the same value. Therefore the streaming of
            fractional seconds (at least by default) appears to be unavoidable.
          </p>
<p>
            Finally the trailing " +0000" disambiguates the UTC-formatted
            <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code> from one formatted with
            respect to the local time zone of the computer. The latter can easily
            be achieved with:
          </p>
<pre class="programlisting"><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">time_fmt</span><span class="special">(</span><span class="identifier">local</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
</pre>
<p>
            that could result in
          </p>
<pre class="programlisting"><span class="number">2011</span><span class="special">-</span><span class="number">09</span><span class="special">-</span><span class="number">15</span> <span class="number">14</span><span class="special">:</span><span class="number">36</span><span class="special">:</span><span class="number">59.325132</span> <span class="special">-</span><span class="number">0400</span>
</pre>
<p>
            Note that <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code>
            itself is neither UTC, nor the local time. However in practice, <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code> is a count of ticks beyond
            some epoch which is synchronized with UTC. So as a mobile computer moves
            across time zones, the time zone traversal does not impact the value
            of a <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code> produced by <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span></code>.
            And it is only in formatting it for human consumption that one can choose
            UTC or the local time zone. C and POSIX treat <code class="computeroutput"><span class="identifier">time_t</span></code>
            just as <span class="bold"><strong>Boost.Chrono</strong></span> treats <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code>:
          </p>
<pre class="programlisting"><span class="identifier">tm</span><span class="special">*</span> <span class="identifier">gmtime</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">time_t</span><span class="special">*</span> <span class="identifier">timer</span><span class="special">)</span> <span class="special">-&gt;</span> <span class="identifier">UTC</span>
<span class="identifier">tm</span><span class="special">*</span> <span class="identifier">localtime</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">time_t</span><span class="special">*</span> <span class="identifier">timer</span><span class="special">)</span> <span class="special">-&gt;</span> <span class="identifier">local</span> <span class="identifier">time</span>
</pre>
<p>
            This proposal simply extends the C/POSIX <code class="computeroutput"><span class="identifier">time_t</span></code>
            functionality to C++ syntax and <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code>.
          </p>
<p>
            The <code class="computeroutput"><span class="identifier">time_fmt</span><span class="special">()</span></code>
            manipulator is "sticky". It will remain in effect until the
            stream destructs or until it is changed. The stream can be reset to its
            default state with:
          </p>
<pre class="programlisting"><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">time_fmt</span><span class="special">(</span><span class="identifier">utc</span><span class="special">);</span>
</pre>
<p>
            And the formatting can be further customized by using the time format
            sequences. For example:
          </p>
<pre class="programlisting"><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">time_fmt</span><span class="special">(</span><span class="identifier">local</span><span class="special">,</span> <span class="string">"%A %B %e, %Y %r"</span><span class="special">);</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>  <span class="comment">// Sunday April 24, 2011 02:36:59 PM</span>
</pre>
<p>
            When specifying formatting manipulators for wide streams, use wide strings.
          </p>
<p>
            You can use the same manipulators with istreams to specify parsing sequences.
          </p>
<p>
            Unfortunately there are no formatting/parsing sequences which indicate
            fractional seconds. <span class="bold"><strong>Boost.Chrono</strong></span> does
            not provide such sequences. In the meantime, one can format and parse
            fractional seconds for <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code>
            by defaulting the format, or by using an empty string in <code class="computeroutput"><span class="identifier">time_fmt</span><span class="special">()</span></code>.
          </p>
<p>
            The stream's current locale may impact the parsing/format sequences supplied
            to the <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code> manipulators (e.g. names
            of days of the week, and names of months).
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.i_o.other_clocks_time_point_io"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.i_o.other_clocks_time_point_io" title="Other clocks time_point">Other
          clocks time_point</a>
</h5></div></div></div>
<p>
            Unlike <code class="computeroutput"><span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">time_point</span></code>, the other clocks have no
            conversion with <code class="computeroutput"><span class="identifier">time_t</span></code>.
            There is likely no relationship between steady_clock::time_point and
            UTC at all (UTC is not steady).
          </p>
<p>
            In general a <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> is formatted by outputting
            its internal <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> followed by a string
            that describes the <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><code class="computeroutput"><span class="special">::</span><span class="identifier">clock</span></code> epoch. This string will vary
            for each distinct clock, and for each implementation of the supplied
            clocks.
          </p>
<pre class="programlisting"><span class="preprocessor">#ifdef</span> <span class="identifier">BOOST_CHRONO_HAS_CLOCK_STEADY</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"steady_clock::now() = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">steady_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
<span class="preprocessor">#endif</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"\nSet cout to use long names:\n"</span>
            <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_long</span>
            <span class="special">&lt;&lt;</span> <span class="string">"high_resolution_clock::now() = "</span>
            <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">high_resolution_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
</pre>
<p>
            The output could be
          </p>
<pre class="programlisting"><span class="identifier">steady_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">=</span> <span class="number">37297387636417</span> <span class="identifier">ns</span> <span class="identifier">since</span> <span class="identifier">boot</span>

<span class="identifier">Set</span> <span class="identifier">cout</span> <span class="identifier">to</span> <span class="identifier">use</span> <span class="keyword">long</span> <span class="identifier">names</span><span class="special">:</span>
<span class="identifier">high_resolution_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">=</span> <span class="number">37297387655134</span> <span class="identifier">nanoseconds</span> <span class="identifier">since</span> <span class="identifier">boot</span>
</pre>
<p>
            Parsing a <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> involves first parsing
            a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> and then parsing the
            epoch string. If the epoch string does not match that associated with
            <code class="computeroutput"><span class="identifier">time_point</span><span class="special">::</span><span class="identifier">clock</span></code> then failbit will be set.
          </p>
<p>
            <span class="bold"><strong>Example:</strong></span>
          </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">chrono_io</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">sstream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">cassert</span><span class="special">&gt;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>

    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">high_resolution_clock</span><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">t0</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">high_resolution_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
    <span class="identifier">stringstream</span> <span class="identifier">io</span><span class="special">;</span>
    <span class="identifier">io</span> <span class="special">&lt;&lt;</span> <span class="identifier">t0</span><span class="special">;</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">high_resolution_clock</span><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">t1</span><span class="special">;</span>
    <span class="identifier">io</span> <span class="special">&gt;&gt;</span> <span class="identifier">t1</span><span class="special">;</span>
    <span class="identifier">assert</span><span class="special">(!</span><span class="identifier">io</span><span class="special">.</span><span class="identifier">fail</span><span class="special">());</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">io</span><span class="special">.</span><span class="identifier">str</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">t0</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">t1</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">high_resolution_clock</span><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">t</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">high_resolution_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">t</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>

    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"That took "</span> <span class="special">&lt;&lt;</span> <span class="identifier">t</span> <span class="special">-</span> <span class="identifier">t0</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"That took "</span> <span class="special">&lt;&lt;</span> <span class="identifier">t</span> <span class="special">-</span> <span class="identifier">t1</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            The output could be:
          </p>
<pre class="programlisting"><span class="number">50908679121461</span> <span class="identifier">nanoseconds</span> <span class="identifier">since</span> <span class="identifier">boot</span>
<span class="identifier">That</span> <span class="identifier">took</span> <span class="number">649630</span> <span class="identifier">nanoseconds</span>
</pre>
<p>
            Here's a simple example to find out how many hours the computer has been
            up (on this platform):
          </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">chrono_io</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">time_point</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">steady_clock</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">ratio</span><span class="special">&lt;</span><span class="number">3600</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">T</span><span class="special">;</span>
    <span class="identifier">T</span> <span class="identifier">tp</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">steady_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">tp</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            The output could be:
          </p>
<pre class="programlisting"><span class="number">17.8666</span> <span class="identifier">hours</span> <span class="identifier">since</span> <span class="identifier">boot</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.tutorial.i_o.low_level_i_o"></a><a class="link" href="users_guide.html#chrono.users_guide.tutorial.i_o.low_level_i_o" title="Low level I/O">Low
          level I/O</a>
</h5></div></div></div>
<p>
            The I/O interface described in the preceding I/O sections were at the
            user level. These services are based on low level services that are useful
            when writing libraries. The low level services are related to access
            to the associated ios state and locale facets. The design follows the
            C++ IOStreams standard design:
          </p>
<p>
            The library encapsulate the locale-dependent parsing and formatting of
            <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> into a new facet class.
            Let's focus on formatting in this example. The concerned facet class
            is <a class="link" href="reference.html#chrono.reference.io.duration_put_hpp.duration_put" title="Template Class duration_put"><code class="computeroutput"><span class="identifier">duration_put</span></code></a>, analogous to time_put,
            money_put, etc.
          </p>
<p>
            The use of this facet is similar to the time_put facet.
          </p>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="chrono.users_guide.examples"></a><a class="link" href="users_guide.html#chrono.users_guide.examples" title="Examples">Examples</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.duration">Duration</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.clocks">Clocks</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.time_point">Time Point</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.io">IO</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.examples.duration"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.duration" title="Duration">Duration</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.duration.how_you_override_the_duration_s_default_constructor">How
          you Override the Duration's Default Constructor</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.duration.saturating">Saturating</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.duration.xtime_conversions">xtime
          Conversions</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.duration.how_you_override_the_duration_s_default_constructor"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.duration.how_you_override_the_duration_s_default_constructor" title="How you Override the Duration's Default Constructor">How
          you Override the Duration's Default Constructor</a>
</h5></div></div></div>
<p>
            Next we show how to override the <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>'s default constructor
            to do anything you want (in this case set it to zero). All we need to
            do is to change the representation
          </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">I_dont_like_the_default_duration_behavior</span> <span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">R</span><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">zero_default</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">typedef</span> <span class="identifier">R</span> <span class="identifier">rep</span><span class="special">;</span>

<span class="keyword">private</span><span class="special">:</span>
    <span class="identifier">rep</span> <span class="identifier">rep_</span><span class="special">;</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="identifier">zero_default</span><span class="special">(</span><span class="identifier">rep</span> <span class="identifier">i</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="special">:</span> <span class="identifier">rep_</span><span class="special">(</span><span class="identifier">i</span><span class="special">)</span> <span class="special">{}</span>
    <span class="keyword">operator</span> <span class="identifier">rep</span><span class="special">()</span> <span class="keyword">const</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">rep_</span><span class="special">;}</span>

    <span class="identifier">zero_default</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">+=(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">{</span><span class="identifier">rep_</span> <span class="special">+=</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">rep_</span><span class="special">;</span> <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;}</span>
    <span class="identifier">zero_default</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">-=(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">{</span><span class="identifier">rep_</span> <span class="special">-=</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">rep_</span><span class="special">;</span> <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;}</span>
    <span class="identifier">zero_default</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">*=(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">{</span><span class="identifier">rep_</span> <span class="special">*=</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">rep_</span><span class="special">;</span> <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;}</span>
    <span class="identifier">zero_default</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">/=(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">{</span><span class="identifier">rep_</span> <span class="special">/=</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">rep_</span><span class="special">;</span> <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;}</span>

    <span class="identifier">zero_default</span>  <span class="keyword">operator</span><span class="special">+</span> <span class="special">()</span> <span class="keyword">const</span> <span class="special">{</span><span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;}</span>
    <span class="identifier">zero_default</span>  <span class="keyword">operator</span><span class="special">-</span> <span class="special">()</span> <span class="keyword">const</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">zero_default</span><span class="special">(-</span><span class="identifier">rep_</span><span class="special">);}</span>
    <span class="identifier">zero_default</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">++()</span>       <span class="special">{++</span><span class="identifier">rep_</span><span class="special">;</span> <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;}</span>
    <span class="identifier">zero_default</span>  <span class="keyword">operator</span><span class="special">++(</span><span class="keyword">int</span><span class="special">)</span>    <span class="special">{</span><span class="keyword">return</span> <span class="identifier">zero_default</span><span class="special">(</span><span class="identifier">rep_</span><span class="special">++);}</span>
    <span class="identifier">zero_default</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">--()</span>       <span class="special">{--</span><span class="identifier">rep_</span><span class="special">;</span> <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;}</span>
    <span class="identifier">zero_default</span>  <span class="keyword">operator</span><span class="special">--(</span><span class="keyword">int</span><span class="special">)</span>    <span class="special">{</span><span class="keyword">return</span> <span class="identifier">zero_default</span><span class="special">(</span><span class="identifier">rep_</span><span class="special">--);}</span>

    <span class="keyword">friend</span> <span class="identifier">zero_default</span> <span class="keyword">operator</span><span class="special">+(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">x</span> <span class="special">+=</span> <span class="identifier">y</span><span class="special">;}</span>
    <span class="keyword">friend</span> <span class="identifier">zero_default</span> <span class="keyword">operator</span><span class="special">-(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">x</span> <span class="special">-=</span> <span class="identifier">y</span><span class="special">;}</span>
    <span class="keyword">friend</span> <span class="identifier">zero_default</span> <span class="keyword">operator</span><span class="special">*(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">x</span> <span class="special">*=</span> <span class="identifier">y</span><span class="special">;}</span>
    <span class="keyword">friend</span> <span class="identifier">zero_default</span> <span class="keyword">operator</span><span class="special">/(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">x</span> <span class="special">/=</span> <span class="identifier">y</span><span class="special">;}</span>

    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">rep_</span> <span class="special">==</span> <span class="identifier">y</span><span class="special">.</span><span class="identifier">rep_</span><span class="special">;}</span>
    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="special">!(</span><span class="identifier">x</span> <span class="special">==</span> <span class="identifier">y</span><span class="special">);}</span>
    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;</span> <span class="special">(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">rep_</span> <span class="special">&lt;</span> <span class="identifier">y</span><span class="special">.</span><span class="identifier">rep_</span><span class="special">;}</span>
    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="special">!(</span><span class="identifier">y</span> <span class="special">&lt;</span> <span class="identifier">x</span><span class="special">);}</span>
    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;</span> <span class="special">(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">y</span> <span class="special">&lt;</span> <span class="identifier">x</span><span class="special">;}</span>
    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="identifier">zero_default</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">zero_default</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="special">!(</span><span class="identifier">x</span> <span class="special">&lt;</span> <span class="identifier">y</span><span class="special">);}</span>
<span class="special">};</span>

<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">zero_default</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">&gt;,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">nano</span>        <span class="special">&gt;</span> <span class="identifier">nanoseconds</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">zero_default</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">&gt;,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">micro</span>       <span class="special">&gt;</span> <span class="identifier">microseconds</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">zero_default</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">&gt;,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">milli</span>       <span class="special">&gt;</span> <span class="identifier">milliseconds</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">zero_default</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">&gt;</span>                      <span class="special">&gt;</span> <span class="identifier">seconds</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">zero_default</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">&gt;,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">ratio</span><span class="special">&lt;</span><span class="number">60</span><span class="special">&gt;</span>   <span class="special">&gt;</span> <span class="identifier">minutes</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">zero_default</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">&gt;,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">ratio</span><span class="special">&lt;</span><span class="number">3600</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">hours</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            Usage
          </p>
<pre class="programlisting"><span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">I_dont_like_the_default_duration_behavior</span><span class="special">;</span>

<span class="identifier">milliseconds</span> <span class="identifier">ms</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">ms</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
</pre>
<p>
            <span class="emphasis"><em>See the source file <a href="../../../libs/chrono/example/i_dont_like_the_default_duration_behavior.cpp" target="_top">example/i_dont_like_the_default_duration_behavior.cpp</a></em></span>
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.duration.saturating"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.duration.saturating" title="Saturating">Saturating</a>
</h5></div></div></div>
<p>
            A "saturating" signed integral type is developed. This type
            has +/- infinity and a NaN (like IEEE floating-point) but otherwise obeys
            signed integral arithmetic. This class is subsequently used as the template
            parameter Rep in boost::chrono::<a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> to demonstrate a duration
            class that does not silently ignore overflow.
          </p>
<p>
            <span class="emphasis"><em>See the source file <a href="../../../libs/chrono/example/saturating.cpp" target="_top">example/saturating.cpp</a></em></span>
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.duration.xtime_conversions"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.duration.xtime_conversions" title="xtime Conversions">xtime
          Conversions</a>
</h5></div></div></div>
<p>
            Example round_up utility: converts d to To, rounding up for inexact conversions
            Being able to <span class="bold"><strong>easily</strong></span> write this function
            is a major feature!
          </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">type_traits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">To</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="identifier">To</span>
<span class="identifier">round_up</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">To</span> <span class="identifier">result</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">To</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">result</span> <span class="special">&lt;</span> <span class="identifier">d</span><span class="special">)</span>
        <span class="special">++</span><span class="identifier">result</span><span class="special">;</span>
    <span class="keyword">return</span> <span class="identifier">result</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            To demonstrate interaction with an xtime-like facility:
          </p>
<pre class="programlisting"><span class="keyword">struct</span> <span class="identifier">xtime</span>
<span class="special">{</span>
    <span class="keyword">long</span> <span class="identifier">sec</span><span class="special">;</span>
    <span class="keyword">unsigned</span> <span class="keyword">long</span> <span class="identifier">usec</span><span class="special">;</span>
<span class="special">};</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="identifier">xtime</span>
<span class="identifier">to_xtime_truncate</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">xtime</span> <span class="identifier">xt</span><span class="special">;</span>
    <span class="identifier">xt</span><span class="special">.</span><span class="identifier">sec</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">&gt;(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">seconds</span></code></a><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">).</span><span class="identifier">count</span><span class="special">());</span>
    <span class="identifier">xt</span><span class="special">.</span><span class="identifier">usec</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">&gt;(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">&gt;(</span><span class="identifier">d</span> <span class="special">-</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">seconds</span></code></a><span class="special">(</span><span class="identifier">xt</span><span class="special">.</span><span class="identifier">sec</span><span class="special">)).</span><span class="identifier">count</span><span class="special">());</span>
    <span class="keyword">return</span> <span class="identifier">xt</span><span class="special">;</span>
<span class="special">}</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="identifier">xtime</span>
<span class="identifier">to_xtime_round_up</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">xtime</span> <span class="identifier">xt</span><span class="special">;</span>
    <span class="identifier">xt</span><span class="special">.</span><span class="identifier">sec</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">&gt;(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">seconds</span></code></a><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">).</span><span class="identifier">count</span><span class="special">());</span>
    <span class="identifier">xt</span><span class="special">.</span><span class="identifier">usec</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">unsigned</span> <span class="keyword">long</span><span class="special">&gt;(</span><span class="identifier">round_up</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">&gt;(</span><span class="identifier">d</span> <span class="special">-</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">seconds</span></code></a><span class="special">(</span><span class="identifier">xt</span><span class="special">.</span><span class="identifier">sec</span><span class="special">)).</span><span class="identifier">count</span><span class="special">());</span>
    <span class="keyword">return</span> <span class="identifier">xt</span><span class="special">;</span>
<span class="special">}</span>

<span class="identifier">microseconds</span>
<span class="identifier">from_xtime</span><span class="special">(</span><span class="identifier">xtime</span> <span class="identifier">xt</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">seconds</span></code></a><span class="special">(</span><span class="identifier">xt</span><span class="special">.</span><span class="identifier">sec</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">(</span><span class="identifier">xt</span><span class="special">.</span><span class="identifier">usec</span><span class="special">);</span>
<span class="special">}</span>

<span class="keyword">void</span> <span class="identifier">print</span><span class="special">(</span><span class="identifier">xtime</span> <span class="identifier">xt</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="char">'{'</span> <span class="special">&lt;&lt;</span> <span class="identifier">xt</span><span class="special">.</span><span class="identifier">sec</span> <span class="special">&lt;&lt;</span> <span class="char">','</span> <span class="special">&lt;&lt;</span> <span class="identifier">xt</span><span class="special">.</span><span class="identifier">usec</span> <span class="special">&lt;&lt;</span> <span class="string">"}\n"</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            Usage
          </p>
<pre class="programlisting"><span class="identifier">xtime</span> <span class="identifier">xt</span> <span class="special">=</span> <span class="identifier">to_xtime_truncate</span><span class="special">(</span><span class="identifier">seconds</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">milliseconds</span></code></a><span class="special">(</span><span class="number">251</span><span class="special">));</span>
<span class="identifier">print</span><span class="special">(</span><span class="identifier">xt</span><span class="special">);</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">milliseconds</span> <span class="identifier">ms</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">milliseconds</span></code></a><span class="special">&gt;(</span><span class="identifier">from_xtime</span><span class="special">(</span><span class="identifier">xt</span><span class="special">));</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">ms</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" milliseconds\n"</span><span class="special">;</span>
<span class="identifier">xt</span> <span class="special">=</span> <span class="identifier">to_xtime_round_up</span><span class="special">(</span><span class="identifier">ms</span><span class="special">);</span>
<span class="identifier">print</span><span class="special">(</span><span class="identifier">xt</span><span class="special">);</span>
<span class="identifier">xt</span> <span class="special">=</span> <span class="identifier">to_xtime_truncate</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">seconds</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a><span class="special">(</span><span class="number">999</span><span class="special">));</span>
<span class="identifier">print</span><span class="special">(</span><span class="identifier">xt</span><span class="special">);</span>
<span class="identifier">xt</span> <span class="special">=</span> <span class="identifier">to_xtime_round_up</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">seconds</span><span class="special">(</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a><span class="special">(</span><span class="number">999</span><span class="special">));</span>
<span class="identifier">print</span><span class="special">(</span><span class="identifier">xt</span><span class="special">);</span>
</pre>
<p>
            <span class="emphasis"><em>See the source file <a href="../../../libs/chrono/example/xtime.cpp" target="_top">xtime.cpp</a></em></span>
          </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.examples.clocks"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.clocks" title="Clocks">Clocks</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.clocks.cycle_count">Cycle
          count</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.clocks.xtime_clock">xtime_clock</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.clocks.cycle_count"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.clocks.cycle_count" title="Cycle count">Cycle
          count</a>
</h5></div></div></div>
<p>
            Users can easily create their own clocks, with both points in time and
            time durations which have a representation and precision of their own
            choosing. For example if there is a hardware counter which simply increments
            a count with each cycle of the cpu, one can very easily build clocks,
            time points and durations on top of that, using only a few tens of lines
            of code. Such systems can be used to call the time-sensitive threading
            API's such as sleep, wait on a condition variable, or wait for a mutex
            lock. The API proposed herein is not sensitive as to whether this is
            a 300MHz clock (with a 3 1/3 nanosecond tick period) or a 3GHz clock
            (with a tick period of 1/3 of a nanosecond). And the resulting code will
            be just as efficient as if the user wrote a special purpose clock cycle
            counter.
          </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">type_traits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">speed</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">cycle_count</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">__ratio_multiply__</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><code class="computeroutput"><span class="identifier">ratio</span></code><span class="special">&lt;</span><span class="identifier">speed</span><span class="special">&gt;,</span> <span class="identifier">boost</span><span class="special">::</span><code class="computeroutput"><span class="identifier">mega</span></code><span class="special">&gt;::</span><span class="identifier">type</span>
        <span class="identifier">frequency</span><span class="special">;</span>  <span class="comment">// Mhz</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">__ratio_divide__</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><code class="computeroutput"><span class="identifier">ratio</span></code><span class="special">&lt;</span><span class="number">1</span><span class="special">&gt;,</span> <span class="identifier">frequency</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">period</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">rep</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">rep</span><span class="special">,</span> <span class="identifier">period</span><span class="special">&gt;</span> <span class="identifier">duration</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><span class="identifier">cycle_count</span><span class="special">&gt;</span> <span class="identifier">time_point</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">time_point</span> <span class="identifier">now</span><span class="special">()</span>
    <span class="special">{</span>
        <span class="keyword">static</span> <span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">tick</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
        <span class="comment">// return exact cycle count</span>
        <span class="keyword">return</span> <span class="identifier">time_point</span><span class="special">(</span><span class="identifier">duration</span><span class="special">(++</span><span class="identifier">tick</span><span class="special">));</span>  <span class="comment">// fake access to clock cycle count</span>
    <span class="special">}</span>
<span class="special">};</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">speed</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">approx_cycle_count</span>
<span class="special">{</span>
    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">frequency</span> <span class="special">=</span> <span class="identifier">speed</span> <span class="special">*</span> <span class="number">1000000</span><span class="special">;</span>  <span class="comment">// MHz</span>
    <span class="keyword">typedef</span> <span class="identifier">nanoseconds</span> <span class="identifier">duration</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">duration</span><span class="special">::</span><span class="identifier">rep</span> <span class="identifier">rep</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">duration</span><span class="special">::</span><span class="identifier">period</span> <span class="identifier">period</span><span class="special">;</span>
    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">nanosec_per_sec</span> <span class="special">=</span> <span class="identifier">period</span><span class="special">::</span><span class="identifier">den</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><span class="identifier">approx_cycle_count</span><span class="special">&gt;</span> <span class="identifier">time_point</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">time_point</span> <span class="identifier">now</span><span class="special">()</span>
    <span class="special">{</span>
        <span class="keyword">static</span> <span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">tick</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
        <span class="comment">// return cycle count as an approximate number of nanoseconds</span>
        <span class="comment">// compute as if nanoseconds is only duration in the std::lib</span>
        <span class="keyword">return</span> <span class="identifier">time_point</span><span class="special">(</span><span class="identifier">duration</span><span class="special">(++</span><span class="identifier">tick</span> <span class="special">*</span> <span class="identifier">nanosec_per_sec</span> <span class="special">/</span> <span class="identifier">frequency</span><span class="special">));</span>
    <span class="special">}</span>
<span class="special">};</span>
</pre>
<p>
            <span class="emphasis"><em>See the source file <a href="../../../libs/chrono/example/cycle_count.cpp" target="_top">cycle_count.cpp</a></em></span>
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.clocks.xtime_clock"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.clocks.xtime_clock" title="xtime_clock">xtime_clock</a>
</h5></div></div></div>
<p>
            This example demonstrates the use of a timeval-like struct to be used
            as the representation type for both <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> and <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>.
          </p>
<pre class="programlisting"><span class="keyword">class</span> <span class="identifier">xtime</span> <span class="special">{</span>
<span class="keyword">private</span><span class="special">:</span>
    <span class="keyword">long</span> <span class="identifier">tv_sec</span><span class="special">;</span>
    <span class="keyword">long</span> <span class="identifier">tv_usec</span><span class="special">;</span>

    <span class="keyword">void</span> <span class="identifier">fixup</span><span class="special">()</span> <span class="special">{</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">tv_usec</span> <span class="special">&lt;</span> <span class="number">0</span><span class="special">)</span> <span class="special">{</span>
            <span class="identifier">tv_usec</span> <span class="special">+=</span> <span class="number">1000000</span><span class="special">;</span>
            <span class="special">--</span><span class="identifier">tv_sec</span><span class="special">;</span>
        <span class="special">}</span>
    <span class="special">}</span>

<span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">explicit</span> <span class="identifier">xtime</span><span class="special">(</span><span class="keyword">long</span> <span class="identifier">sec</span><span class="special">,</span> <span class="keyword">long</span> <span class="identifier">usec</span><span class="special">)</span> <span class="special">{</span>
        <span class="identifier">tv_sec</span> <span class="special">=</span> <span class="identifier">sec</span><span class="special">;</span>
        <span class="identifier">tv_usec</span> <span class="special">=</span> <span class="identifier">usec</span><span class="special">;</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">tv_usec</span> <span class="special">&lt;</span> <span class="number">0</span> <span class="special">||</span> <span class="identifier">tv_usec</span> <span class="special">&gt;=</span> <span class="number">1000000</span><span class="special">)</span> <span class="special">{</span>
            <span class="identifier">tv_sec</span> <span class="special">+=</span> <span class="identifier">tv_usec</span> <span class="special">/</span> <span class="number">1000000</span><span class="special">;</span>
            <span class="identifier">tv_usec</span> <span class="special">%=</span> <span class="number">1000000</span><span class="special">;</span>
            <span class="identifier">fixup</span><span class="special">();</span>
        <span class="special">}</span>
    <span class="special">}</span>

    <span class="keyword">explicit</span> <span class="identifier">xtime</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">usec</span><span class="special">)</span> <span class="special">{</span>
        <span class="identifier">tv_usec</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">&gt;(</span><span class="identifier">usec</span> <span class="special">%</span> <span class="number">1000000</span><span class="special">);</span>
        <span class="identifier">tv_sec</span>  <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">&gt;(</span><span class="identifier">usec</span> <span class="special">/</span> <span class="number">1000000</span><span class="special">);</span>
        <span class="identifier">fixup</span><span class="special">();</span>
    <span class="special">}</span>

    <span class="comment">// explicit</span>
    <span class="keyword">operator</span> <span class="keyword">long</span> <span class="keyword">long</span><span class="special">()</span> <span class="keyword">const</span> <span class="special">{</span><span class="keyword">return</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">&gt;(</span><span class="identifier">tv_sec</span><span class="special">)</span> <span class="special">*</span> <span class="number">1000000</span> <span class="special">+</span> <span class="identifier">tv_usec</span><span class="special">;}</span>

    <span class="identifier">xtime</span><span class="special">&amp;</span> <span class="keyword">operator</span> <span class="special">+=</span> <span class="special">(</span><span class="identifier">xtime</span> <span class="identifier">rhs</span><span class="special">)</span> <span class="special">{</span>
        <span class="identifier">tv_sec</span> <span class="special">+=</span> <span class="identifier">rhs</span><span class="special">.</span><span class="identifier">tv_sec</span><span class="special">;</span>
        <span class="identifier">tv_usec</span> <span class="special">+=</span> <span class="identifier">rhs</span><span class="special">.</span><span class="identifier">tv_usec</span><span class="special">;</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">tv_usec</span> <span class="special">&gt;=</span> <span class="number">1000000</span><span class="special">)</span> <span class="special">{</span>
            <span class="identifier">tv_usec</span> <span class="special">-=</span> <span class="number">1000000</span><span class="special">;</span>
            <span class="special">++</span><span class="identifier">tv_sec</span><span class="special">;</span>
        <span class="special">}</span>
        <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
    <span class="special">}</span>

    <span class="identifier">xtime</span><span class="special">&amp;</span> <span class="keyword">operator</span> <span class="special">-=</span> <span class="special">(</span><span class="identifier">xtime</span> <span class="identifier">rhs</span><span class="special">)</span> <span class="special">{</span>
        <span class="identifier">tv_sec</span> <span class="special">-=</span> <span class="identifier">rhs</span><span class="special">.</span><span class="identifier">tv_sec</span><span class="special">;</span>
        <span class="identifier">tv_usec</span> <span class="special">-=</span> <span class="identifier">rhs</span><span class="special">.</span><span class="identifier">tv_usec</span><span class="special">;</span>
        <span class="identifier">fixup</span><span class="special">();</span>
        <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
    <span class="special">}</span>

    <span class="identifier">xtime</span><span class="special">&amp;</span> <span class="keyword">operator</span> <span class="special">%=</span> <span class="special">(</span><span class="identifier">xtime</span> <span class="identifier">rhs</span><span class="special">)</span> <span class="special">{</span>
        <span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">t</span> <span class="special">=</span> <span class="identifier">tv_sec</span> <span class="special">*</span> <span class="number">1000000</span> <span class="special">+</span> <span class="identifier">tv_usec</span><span class="special">;</span>
        <span class="keyword">long</span> <span class="keyword">long</span> <span class="identifier">r</span> <span class="special">=</span> <span class="identifier">rhs</span><span class="special">.</span><span class="identifier">tv_sec</span> <span class="special">*</span> <span class="number">1000000</span> <span class="special">+</span> <span class="identifier">rhs</span><span class="special">.</span><span class="identifier">tv_usec</span><span class="special">;</span>
        <span class="identifier">t</span> <span class="special">%=</span> <span class="identifier">r</span><span class="special">;</span>
        <span class="identifier">tv_sec</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">&gt;(</span><span class="identifier">t</span> <span class="special">/</span> <span class="number">1000000</span><span class="special">);</span>
        <span class="identifier">tv_usec</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">&gt;(</span><span class="identifier">t</span> <span class="special">%</span> <span class="number">1000000</span><span class="special">);</span>
        <span class="identifier">fixup</span><span class="special">();</span>
        <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
    <span class="special">}</span>

    <span class="keyword">friend</span> <span class="identifier">xtime</span> <span class="keyword">operator</span><span class="special">+(</span><span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">x</span> <span class="special">+=</span> <span class="identifier">y</span><span class="special">;}</span>
    <span class="keyword">friend</span> <span class="identifier">xtime</span> <span class="keyword">operator</span><span class="special">-(</span><span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">x</span> <span class="special">-=</span> <span class="identifier">y</span><span class="special">;}</span>
    <span class="keyword">friend</span> <span class="identifier">xtime</span> <span class="keyword">operator</span><span class="special">%(</span><span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span><span class="keyword">return</span> <span class="identifier">x</span> <span class="special">%=</span> <span class="identifier">y</span><span class="special">;}</span>

    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">y</span><span class="special">)</span>
        <span class="special">{</span> <span class="keyword">return</span> <span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">tv_sec</span> <span class="special">==</span> <span class="identifier">y</span><span class="special">.</span><span class="identifier">tv_sec</span> <span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">tv_usec</span> <span class="special">==</span> <span class="identifier">y</span><span class="special">.</span><span class="identifier">tv_usec</span><span class="special">);</span> <span class="special">}</span>

    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span><span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">tv_sec</span> <span class="special">==</span> <span class="identifier">y</span><span class="special">.</span><span class="identifier">tv_sec</span><span class="special">)</span>
            <span class="keyword">return</span> <span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">tv_usec</span> <span class="special">&lt;</span> <span class="identifier">y</span><span class="special">.</span><span class="identifier">tv_usec</span><span class="special">);</span>
        <span class="keyword">return</span> <span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">tv_sec</span> <span class="special">&lt;</span> <span class="identifier">y</span><span class="special">.</span><span class="identifier">tv_sec</span><span class="special">);</span>
    <span class="special">}</span>

    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span><span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span> <span class="keyword">return</span> <span class="special">!(</span><span class="identifier">x</span> <span class="special">==</span> <span class="identifier">y</span><span class="special">);</span> <span class="special">}</span>
    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;</span> <span class="special">(</span><span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span> <span class="keyword">return</span> <span class="identifier">y</span> <span class="special">&lt;</span> <span class="identifier">x</span><span class="special">;</span> <span class="special">}</span>
    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span> <span class="keyword">return</span> <span class="special">!(</span><span class="identifier">y</span> <span class="special">&lt;</span> <span class="identifier">x</span><span class="special">);</span> <span class="special">}</span>
    <span class="keyword">friend</span> <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">y</span><span class="special">)</span> <span class="special">{</span> <span class="keyword">return</span> <span class="special">!(</span><span class="identifier">x</span> <span class="special">&lt;</span> <span class="identifier">y</span><span class="special">);</span> <span class="special">}</span>

    <span class="keyword">friend</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream</span><span class="special">&amp;</span> <span class="identifier">os</span><span class="special">,</span> <span class="identifier">xtime</span> <span class="identifier">x</span><span class="special">)</span>
        <span class="special">{</span><span class="keyword">return</span> <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="char">'{'</span> <span class="special">&lt;&lt;</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">tv_sec</span> <span class="special">&lt;&lt;</span> <span class="char">','</span> <span class="special">&lt;&lt;</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">tv_usec</span> <span class="special">&lt;&lt;</span> <span class="char">'}'</span><span class="special">;}</span>
<span class="special">};</span>
</pre>
<p>
            Clock based on timeval-like struct.
          </p>
<pre class="programlisting"><span class="keyword">class</span> <span class="identifier">xtime_clock</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">typedef</span> <span class="identifier">xtime</span>                                  <span class="identifier">rep</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">micro</span>                           <span class="identifier">period</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="identifier">rep</span><span class="special">,</span> <span class="identifier">period</span><span class="special">&gt;</span>   <span class="identifier">duration</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">time_point</span><span class="special">&lt;</span><span class="identifier">xtime_clock</span><span class="special">&gt;</span> <span class="identifier">time_point</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">time_point</span> <span class="identifier">now</span><span class="special">()</span>
    <span class="special">{</span>
    <span class="preprocessor">#if</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_CHRONO_WINDOWS_API</span><span class="special">)</span>
        <span class="identifier">time_point</span> <span class="identifier">t</span><span class="special">(</span><span class="identifier">duration</span><span class="special">(</span><span class="identifier">xtime</span><span class="special">(</span><span class="number">0</span><span class="special">)));</span>
        <span class="identifier">gettimeofday</span><span class="special">((</span><span class="identifier">timeval</span><span class="special">*)&amp;</span><span class="identifier">t</span><span class="special">,</span> <span class="number">0</span><span class="special">);</span>
        <span class="keyword">return</span> <span class="identifier">t</span><span class="special">;</span>

    <span class="preprocessor">#elif</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_CHRONO_MAC_API</span><span class="special">)</span>

        <span class="identifier">time_point</span> <span class="identifier">t</span><span class="special">(</span><span class="identifier">duration</span><span class="special">(</span><span class="identifier">xtime</span><span class="special">(</span><span class="number">0</span><span class="special">)));</span>
        <span class="identifier">gettimeofday</span><span class="special">((</span><span class="identifier">timeval</span><span class="special">*)&amp;</span><span class="identifier">t</span><span class="special">,</span> <span class="number">0</span><span class="special">);</span>
        <span class="keyword">return</span> <span class="identifier">t</span><span class="special">;</span>

    <span class="preprocessor">#elif</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_CHRONO_POSIX_API</span><span class="special">)</span>
        <span class="comment">//time_point t(0,0);</span>

        <span class="identifier">timespec</span> <span class="identifier">ts</span><span class="special">;</span>
        <span class="special">::</span><span class="identifier">clock_gettime</span><span class="special">(</span> <span class="identifier">CLOCK_REALTIME</span><span class="special">,</span> <span class="special">&amp;</span><span class="identifier">ts</span> <span class="special">);</span>

        <span class="identifier">xtime</span> <span class="identifier">xt</span><span class="special">(</span> <span class="identifier">ts</span><span class="special">.</span><span class="identifier">tv_sec</span><span class="special">,</span> <span class="identifier">ts</span><span class="special">.</span><span class="identifier">tv_nsec</span><span class="special">/</span><span class="number">1000</span><span class="special">);</span>
        <span class="keyword">return</span> <span class="identifier">time_point</span><span class="special">(</span><span class="identifier">duration</span><span class="special">(</span><span class="identifier">xt</span><span class="special">));</span>

    <span class="preprocessor">#endif</span>  <span class="comment">// POSIX</span>
    <span class="special">}</span>
<span class="special">};</span>
</pre>
<p>
            Usage of xtime_clock
          </p>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"sizeof xtime_clock::time_point = "</span> <span class="special">&lt;&lt;</span> <span class="keyword">sizeof</span><span class="special">(</span><span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">time_point</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"sizeof xtime_clock::duration = "</span> <span class="special">&lt;&lt;</span> <span class="keyword">sizeof</span><span class="special">(</span><span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">duration</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"sizeof xtime_clock::rep = "</span> <span class="special">&lt;&lt;</span> <span class="keyword">sizeof</span><span class="special">(</span><span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">rep</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
<span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">duration</span> <span class="identifier">delay</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">milliseconds</span><span class="special">(</span><span class="number">5</span><span class="special">));</span>
<span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">start</span> <span class="special">=</span> <span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
<span class="keyword">while</span> <span class="special">(</span><span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">-</span> <span class="identifier">start</span> <span class="special">&lt;=</span> <span class="identifier">delay</span><span class="special">)</span> <span class="special">{}</span>
<span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">stop</span> <span class="special">=</span> <span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
<span class="identifier">xtime_clock</span><span class="special">::</span><span class="identifier">duration</span> <span class="identifier">elapsed</span> <span class="special">=</span> <span class="identifier">stop</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"paused "</span> <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::::</span><span class="identifier">nanoseconds</span><span class="special">(</span><span class="identifier">elapsed</span><span class="special">).</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" nanoseconds\n"</span><span class="special">;</span>
</pre>
<p>
            <span class="emphasis"><em>See the source file <a href="../../../libs/chrono/example/timeval_demo.cpp" target="_top">example/timeval_demo.cpp</a></em></span>
          </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.examples.time_point"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.time_point" title="Time Point">Time Point</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.time_point.min_utility">min
          Utility</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.time_point.a_tiny_program_that_times_how_long_until_a_key_is_struck">A
          Tiny Program that Times How Long Until a Key is Struck</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.time_point.24_hours_display">24
          Hours Display</a></span></dt>
<dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.time_point.simulated_thread_interface_demonstration_program">Simulated
          Thread Interface Demonstration Program</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.time_point.min_utility"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.time_point.min_utility" title="min Utility">min
          Utility</a>
</h5></div></div></div>
<p>
            The user can define a function returning the earliest <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a> as follows:
          </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration1</span></code></a><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration2</span></code></a><span class="special">&gt;</span>
<span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><code class="computeroutput"><span class="identifier">common_type</span></code><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration1</span></code></a><span class="special">&gt;,</span>
                     <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration2</span></code></a><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span>
<span class="identifier">min</span><span class="special">(</span><a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration1</span></code></a><span class="special">&gt;</span> <span class="identifier">t1</span><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration2</span></code></a><span class="special">&gt;</span> <span class="identifier">t2</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">t2</span> <span class="special">&lt;</span> <span class="identifier">t1</span> <span class="special">?</span> <span class="identifier">t2</span> <span class="special">:</span> <span class="identifier">t1</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            Being able to <span class="bold"><strong>easily</strong></span> write this function
            is a major feature!
          </p>
<pre class="programlisting"><span class="identifier">BOOST_AUTO</span><span class="special">(</span><span class="identifier">t1</span><span class="special">,</span> <span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">seconds</span><span class="special">(</span><span class="number">3</span><span class="special">));</span>
<span class="identifier">BOOST_AUTO</span><span class="special">(</span><span class="identifier">t2</span><span class="special">,</span> <span class="identifier">system_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">nanoseconds</span><span class="special">(</span><span class="number">3</span><span class="special">));</span>
<span class="identifier">BOOST_AUTO</span><span class="special">(</span><span class="identifier">t3</span><span class="special">,</span> <span class="identifier">min</span><span class="special">(</span><span class="identifier">t1</span><span class="special">,</span> <span class="identifier">t2</span><span class="special">));</span>
</pre>
<p>
            <span class="emphasis"><em>See the source file <a href="../../../libs/chrono/example/min_time_point.cpp" target="_top">example/min_time_point.cpp</a></em></span>
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.time_point.a_tiny_program_that_times_how_long_until_a_key_is_struck"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.time_point.a_tiny_program_that_times_how_long_until_a_key_is_struck" title="A Tiny Program that Times How Long Until a Key is Struck">A
          Tiny Program that Times How Long Until a Key is Struck</a>
</h5></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iomanip</span><span class="special">&gt;</span>

<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a> <span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">timer</span>
<span class="special">{</span>
  <span class="keyword">typename</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">start</span><span class="special">;</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="identifier">timer</span><span class="special">()</span> <span class="special">:</span> <span class="identifier">start</span><span class="special">(</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">)</span> <span class="special">{}</span>
  <span class="keyword">typename</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">duration</span> <span class="identifier">elapsed</span><span class="special">()</span> <span class="keyword">const</span>
  <span class="special">{</span>
    <span class="keyword">return</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">-</span> <span class="identifier">start</span><span class="special">;</span>
  <span class="special">}</span>
  <span class="keyword">double</span> <span class="identifier">seconds</span><span class="special">()</span> <span class="keyword">const</span>
  <span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">elapsed</span><span class="special">().</span><span class="identifier">count</span><span class="special">()</span> <span class="special">*</span> <span class="special">((</span><span class="keyword">double</span><span class="special">)</span><span class="identifier">Clock</span><span class="special">::</span><span class="identifier">period</span><span class="special">::</span><span class="identifier">num</span><span class="special">/</span><span class="identifier">Clock</span><span class="special">::</span><span class="identifier">period</span><span class="special">::</span><span class="identifier">den</span><span class="special">);</span>
  <span class="special">}</span>
<span class="special">};</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
  <span class="identifier">timer</span><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">&gt;</span> <span class="identifier">t1</span><span class="special">;</span>
  <span class="identifier">timer</span><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a><span class="special">&gt;</span> <span class="identifier">t2</span><span class="special">;</span>
  <span class="identifier">timer</span><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.high_resolution_clock" title="Class high_resolution_clock"><code class="computeroutput"><span class="identifier">high_resolution_clock</span></code></a><span class="special">&gt;</span> <span class="identifier">t3</span><span class="special">;</span>

  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Type the Enter key: "</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cin</span><span class="special">.</span><span class="identifier">get</span><span class="special">();</span>

  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">fixed</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">setprecision</span><span class="special">(</span><span class="number">9</span><span class="special">);</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"system_clock-----------: "</span>
            <span class="special">&lt;&lt;</span> <span class="identifier">t1</span><span class="special">.</span><span class="identifier">seconds</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" seconds\n"</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"steady_clock--------: "</span>
            <span class="special">&lt;&lt;</span> <span class="identifier">t2</span><span class="special">.</span><span class="identifier">seconds</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" seconds\n"</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"high_resolution_clock--: "</span>
            <span class="special">&lt;&lt;</span> <span class="identifier">t3</span><span class="special">.</span><span class="identifier">seconds</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" seconds\n"</span><span class="special">;</span>

  <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">d4</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
  <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">d5</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>

  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"\nsystem_clock latency-----------: "</span> <span class="special">&lt;&lt;</span> <span class="special">(</span><span class="identifier">d5</span> <span class="special">-</span> <span class="identifier">d4</span><span class="special">).</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

  <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">d6</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
  <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">d7</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.steady_clock" title="Class steady_clock"><code class="computeroutput"><span class="identifier">steady_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>

  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"steady_clock latency--------: "</span> <span class="special">&lt;&lt;</span> <span class="special">(</span><span class="identifier">d7</span> <span class="special">-</span> <span class="identifier">d6</span><span class="special">).</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

  <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.high_resolution_clock" title="Class high_resolution_clock"><code class="computeroutput"><span class="identifier">high_resolution_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">d8</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.high_resolution_clock" title="Class high_resolution_clock"><code class="computeroutput"><span class="identifier">high_resolution_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
  <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.high_resolution_clock" title="Class high_resolution_clock"><code class="computeroutput"><span class="identifier">high_resolution_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">d9</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.high_resolution_clock" title="Class high_resolution_clock"><code class="computeroutput"><span class="identifier">high_resolution_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>

  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"high_resolution_clock latency--: "</span> <span class="special">&lt;&lt;</span> <span class="special">(</span><span class="identifier">d9</span> <span class="special">-</span> <span class="identifier">d8</span><span class="special">).</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

  <span class="identifier">std</span><span class="special">::</span><span class="identifier">time_t</span> <span class="identifier">now</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">to_time_t</span><span class="special">(</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">());</span>

  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"\nsystem_clock::now() reports UTC is "</span>
    <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">asctime</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">gmtime</span><span class="special">(&amp;</span><span class="identifier">now</span><span class="special">))</span> <span class="special">&lt;&lt;</span> <span class="string">"\n"</span><span class="special">;</span>

  <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            The output of this program run looks like this:
          </p>
<p>
            <span class="emphasis"><em>See the source file <a href="../../../libs/chrono/example/await_keystroke.cpp" target="_top">example/await_keystroke.cpp</a></em></span>
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.time_point.24_hours_display"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.time_point.24_hours_display" title="24 Hours Display">24
          Hours Display</a>
</h5></div></div></div>
<p>
            In the example above we take advantage of the fact that <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s convert as long
            as they have the same clock, and as long as their internal <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s convert. We also take
            advantage of the fact that a <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a> with a floating-point
            representation will convert from anything. Finally the I/O system discovers
            the more readable "hours" unit for our <code class="computeroutput"><span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">,</span> <span class="identifier">ratio</span><span class="special">&lt;</span><span class="number">3600</span><span class="special">&gt;&gt;</span></code>.
          </p>
<p>
            There are many other ways to format <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s and <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a>s. For example see
            <a href="http://en.wikipedia.org/wiki/ISO_8601#Durations" target="_top">ISO 8601</a>.
            Instead of coding every possibility into <code class="computeroutput"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></code>, which would lead to significant
            code bloat for even the most trivial uses, this document seeks to inform
            the reader how to write custom I/O when desired.
          </p>
<p>
            As an example, the function below streams arbitrary <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a>s to arbitrary <code class="computeroutput"><span class="identifier">basic_ostreams</span></code> using the format:
          </p>
<pre class="programlisting"><span class="special">[-]</span><span class="identifier">d</span><span class="special">/</span><span class="identifier">hh</span><span class="special">:</span><span class="identifier">mm</span><span class="special">:</span><span class="identifier">ss</span><span class="special">.</span><span class="identifier">cc</span>
</pre>
<p>
            Where:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">d</span></code> is the number of
                <code class="computeroutput"><span class="identifier">days</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">h</span></code> is the number of
                <code class="computeroutput"><span class="identifier">hours</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">m</span></code> is the number of
                <code class="computeroutput"><span class="identifier">minutes</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ss</span><span class="special">.</span><span class="identifier">cc</span></code> is the number of <code class="computeroutput"><span class="identifier">seconds</span></code> rounded to the nearest
                hundredth of a second
                <div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
                      include &lt;boost/chrono/chrono_io.hpp&gt;
                    </li>
<li class="listitem">
                      include &lt;ostream&gt;
                    </li>
<li class="listitem">
                      include &lt;iostream&gt;
                    </li>
</ol></div>
              </li>
</ul></div>
<pre class="programlisting"><span class="comment">// format duration as [-]d/hh::mm::ss.cc</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">&gt;&amp;</span>
<span class="identifier">display</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">&gt;&amp;</span> <span class="identifier">os</span><span class="special">,</span>
        <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;</span> <span class="identifier">d</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">ratio</span><span class="special">&lt;</span><span class="number">86400</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">days</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">:</span><span class="identifier">centi</span><span class="special">&gt;</span> <span class="identifier">centiseconds</span><span class="special">;</span>

    <span class="comment">// if negative, print negative sign and negate</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">d</span> <span class="special">&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;(</span><span class="number">0</span><span class="special">))</span>
    <span class="special">{</span>
        <span class="identifier">d</span> <span class="special">=</span> <span class="special">-</span><span class="identifier">d</span><span class="special">;</span>
        <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="char">'-'</span><span class="special">;</span>
    <span class="special">}</span>
    <span class="comment">// round d to nearest centiseconds, to even on tie</span>
    <span class="identifier">centiseconds</span> <span class="identifier">cs</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">centiseconds</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">d</span> <span class="special">-</span> <span class="identifier">cs</span> <span class="special">&gt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">milliseconds</span><span class="special">(</span><span class="number">5</span><span class="special">)</span>
        <span class="special">||</span> <span class="special">(</span><span class="identifier">d</span> <span class="special">-</span> <span class="identifier">cs</span> <span class="special">==</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">milliseconds</span><span class="special">(</span><span class="number">5</span><span class="special">)</span> <span class="special">&amp;&amp;</span> <span class="identifier">cs</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&amp;</span> <span class="number">1</span><span class="special">))</span>
        <span class="special">++</span><span class="identifier">cs</span><span class="special">;</span>
    <span class="comment">// separate seconds from centiseconds</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">seconds</span> <span class="identifier">s</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">seconds</span><span class="special">&gt;(</span><span class="identifier">cs</span><span class="special">);</span>
    <span class="identifier">cs</span> <span class="special">-=</span> <span class="identifier">s</span><span class="special">;</span>
    <span class="comment">// separate minutes from seconds</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">minutes</span> <span class="identifier">m</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">minutes</span><span class="special">&gt;(</span><span class="identifier">s</span><span class="special">);</span>
    <span class="identifier">s</span> <span class="special">-=</span> <span class="identifier">m</span><span class="special">;</span>
    <span class="comment">// separate hours from minutes</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">hours</span> <span class="identifier">h</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">hours</span><span class="special">&gt;(</span><span class="identifier">m</span><span class="special">);</span>
    <span class="identifier">m</span> <span class="special">-=</span> <span class="identifier">h</span><span class="special">;</span>
    <span class="comment">// separate days from hours</span>
    <span class="identifier">days</span> <span class="identifier">dy</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration_cast</span><span class="special">&lt;</span><span class="identifier">days</span><span class="special">&gt;(</span><span class="identifier">h</span><span class="special">);</span>
    <span class="identifier">h</span> <span class="special">-=</span> <span class="identifier">dy</span><span class="special">;</span>
    <span class="comment">// print d/hh:mm:ss.cc</span>
    <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="identifier">dy</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">'/'</span><span class="special">;</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">h</span> <span class="special">&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">hours</span><span class="special">(</span><span class="number">10</span><span class="special">))</span>
        <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="char">'0'</span><span class="special">;</span>
    <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="identifier">h</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">':'</span><span class="special">;</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">m</span> <span class="special">&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">minutes</span><span class="special">(</span><span class="number">10</span><span class="special">))</span>
        <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="char">'0'</span><span class="special">;</span>
    <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="identifier">m</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">':'</span><span class="special">;</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">s</span> <span class="special">&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">seconds</span><span class="special">(</span><span class="number">10</span><span class="special">))</span>
        <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="char">'0'</span><span class="special">;</span>
    <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="identifier">s</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">'.'</span><span class="special">;</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">cs</span> <span class="special">&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">centiseconds</span><span class="special">(</span><span class="number">10</span><span class="special">))</span>
        <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="char">'0'</span><span class="special">;</span>
    <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="identifier">cs</span><span class="special">.</span><span class="identifier">count</span><span class="special">();</span>
    <span class="keyword">return</span> <span class="identifier">os</span><span class="special">;</span>
<span class="special">}</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">;</span>

    <span class="identifier">display</span><span class="special">(</span><span class="identifier">cout</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">steady_clock</span><span class="special">::</span><span class="identifier">now</span><span class="special">().</span><span class="identifier">time_since_epoch</span><span class="special">()</span>
                  <span class="special">+</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">mega</span><span class="special">&gt;(</span><span class="number">1</span><span class="special">))</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">display</span><span class="special">(</span><span class="identifier">cout</span><span class="special">,</span> <span class="special">-</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">milliseconds</span><span class="special">(</span><span class="number">6</span><span class="special">))</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">display</span><span class="special">(</span><span class="identifier">cout</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">mega</span><span class="special">&gt;(</span><span class="number">1</span><span class="special">))</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
    <span class="identifier">display</span><span class="special">(</span><span class="identifier">cout</span><span class="special">,</span> <span class="special">-</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&lt;</span><span class="keyword">long</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">mega</span><span class="special">&gt;(</span><span class="number">1</span><span class="special">))</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            The output could be:
          </p>
<pre class="programlisting"><span class="number">12</span><span class="special">/</span><span class="number">06</span><span class="special">:</span><span class="number">03</span><span class="special">:</span><span class="number">22.95</span>
<span class="special">-</span><span class="number">0</span><span class="special">/</span><span class="number">00</span><span class="special">:</span><span class="number">00</span><span class="special">:</span><span class="number">00</span><span class="special">.</span><span class="number">01</span>
<span class="number">11</span><span class="special">/</span><span class="number">13</span><span class="special">:</span><span class="number">46</span><span class="special">:</span><span class="number">40.00</span>
<span class="special">-</span><span class="number">11</span><span class="special">/</span><span class="number">13</span><span class="special">:</span><span class="number">46</span><span class="special">:</span><span class="number">40.00</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.time_point.simulated_thread_interface_demonstration_program"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.time_point.simulated_thread_interface_demonstration_program" title="Simulated Thread Interface Demonstration Program">Simulated
          Thread Interface Demonstration Program</a>
</h5></div></div></div>
<p>
            The C++11 standard library's multi-threading library requires the ability
            to deal with the representation of time in a manner consistent with modern
            C++ practices. Next is a simulation of this interface.
          </p>
<p>
            The non-member sleep functions can be emulated as follows:
          </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">this_thread</span> <span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">sleep_for</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;&amp;</span> <span class="identifier">d</span><span class="special">)</span> <span class="special">{</span>
    <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a> <span class="identifier">t</span> <span class="special">=</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">t</span> <span class="special">&lt;</span> <span class="identifier">d</span><span class="special">)</span>
        <span class="special">++</span><span class="identifier">t</span><span class="special">;</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">t</span> <span class="special">&gt;</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">(</span><span class="number">0</span><span class="special">))</span>
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"sleep_for "</span> <span class="special">&lt;&lt;</span> <span class="identifier">t</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" microseconds\n"</span><span class="special">;</span>
<span class="special">}</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">sleep_until</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a><span class="special">&gt;&amp;</span> <span class="identifier">t</span><span class="special">)</span> <span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">chrono</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a><span class="special">&gt;</span> <span class="identifier">Time</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">SysTime</span><span class="special">;</span>
    <span class="keyword">if</span> <span class="special">(</span><span class="identifier">t</span> <span class="special">&gt;</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">())</span> <span class="special">{</span>
        <span class="keyword">typedef</span> <span class="keyword">typename</span> <code class="computeroutput"><span class="identifier">common_type</span></code><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Time</span><span class="special">::</span><span class="identifier">duration</span><span class="special">,</span>
                                     <span class="keyword">typename</span> <span class="identifier">SysTime</span><span class="special">::</span><span class="identifier">duration</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">D</span><span class="special">;</span>
        <span class="comment">/* auto */</span> <span class="identifier">D</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">t</span> <span class="special">-</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
        <span class="identifier">microseconds</span> <span class="identifier">us</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">us</span> <span class="special">&lt;</span> <span class="identifier">d</span><span class="special">)</span>
            <span class="special">++</span><span class="identifier">us</span><span class="special">;</span>
        <span class="identifier">SysTime</span> <span class="identifier">st</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">us</span><span class="special">;</span>
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"sleep_until    "</span><span class="special">;</span>
        <span class="identifier">detail</span><span class="special">::</span><span class="identifier">print_time</span><span class="special">(</span><span class="identifier">st</span><span class="special">);</span>
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">" which is "</span> <span class="special">&lt;&lt;</span> <span class="special">(</span><span class="identifier">st</span> <span class="special">-</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()).</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" microseconds away\n"</span><span class="special">;</span>
    <span class="special">}</span>
<span class="special">}</span>

<span class="special">}}</span>
</pre>
<p>
            Next is the <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">thread</span><span class="special">::</span><span class="identifier">timed_mutex</span></code> modified functions
          </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">struct</span> <span class="identifier">timed_mutex</span> <span class="special">{</span>
    <span class="comment">// ...</span>

    <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock_for</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;&amp;</span> <span class="identifier">d</span><span class="special">)</span> <span class="special">{</span>
        <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a> <span class="identifier">t</span> <span class="special">=</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">t</span> <span class="special">&lt;=</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">(</span><span class="number">0</span><span class="special">))</span>
            <span class="keyword">return</span> <span class="identifier">try_lock</span><span class="special">();</span>
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"try_lock_for "</span> <span class="special">&lt;&lt;</span> <span class="identifier">t</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" microseconds\n"</span><span class="special">;</span>
        <span class="keyword">return</span> <span class="keyword">true</span><span class="special">;</span>
    <span class="special">}</span>

    <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a><span class="special">&gt;</span>
    <span class="keyword">bool</span> <span class="identifier">try_lock_until</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a><span class="special">&gt;&amp;</span> <span class="identifier">t</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">chrono</span><span class="special">;</span>
        <span class="keyword">typedef</span> <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a><span class="special">&gt;</span> <span class="identifier">Time</span><span class="special">;</span>
        <span class="keyword">typedef</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">SysTime</span><span class="special">;</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">t</span> <span class="special">&lt;=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">())</span>
            <span class="keyword">return</span> <span class="identifier">try_lock</span><span class="special">();</span>
        <span class="keyword">typedef</span> <span class="keyword">typename</span> <code class="computeroutput"><span class="identifier">common_type</span></code><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Time</span><span class="special">::</span><span class="identifier">duration</span><span class="special">,</span>
          <span class="keyword">typename</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">duration</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">D</span><span class="special">;</span>
        <span class="comment">/* auto */</span> <span class="identifier">D</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">t</span> <span class="special">-</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
        <span class="identifier">microseconds</span> <span class="identifier">us</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
        <span class="identifier">SysTime</span> <span class="identifier">st</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">us</span><span class="special">;</span>
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"try_lock_until "</span><span class="special">;</span>
        <span class="identifier">detail</span><span class="special">::</span><span class="identifier">print_time</span><span class="special">(</span><span class="identifier">st</span><span class="special">);</span>
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">" which is "</span> <span class="special">&lt;&lt;</span> <span class="special">(</span><span class="identifier">st</span> <span class="special">-</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()).</span><span class="identifier">count</span><span class="special">()</span>
          <span class="special">&lt;&lt;</span> <span class="string">" microseconds away\n"</span><span class="special">;</span>
        <span class="keyword">return</span> <span class="keyword">true</span><span class="special">;</span>
    <span class="special">}</span>
<span class="special">};</span>
<span class="special">}</span>
</pre>
<p>
            <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">thread</span><span class="special">::</span><span class="identifier">condition_variable</span></code> time related function
            are modified as follows:
          </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">struct</span> <span class="identifier">condition_variable</span>
<span class="special">{</span>
    <span class="comment">// ...</span>

    <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Rep</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Period</span><span class="special">&gt;</span>
    <span class="keyword">bool</span> <span class="identifier">wait_for</span><span class="special">(</span><span class="identifier">mutex</span><span class="special">&amp;,</span> <span class="keyword">const</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="identifier">Rep</span><span class="special">,</span> <span class="identifier">Period</span><span class="special">&gt;&amp;</span> <span class="identifier">d</span><span class="special">)</span> <span class="special">{</span>
        <span class="identifier">chrono</span><span class="special">::</span><span class="identifier">microseconds</span> <span class="identifier">t</span> <span class="special">=</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><span class="identifier">chrono</span><span class="special">::</span><span class="identifier">microseconds</span><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"wait_for "</span> <span class="special">&lt;&lt;</span> <span class="identifier">t</span><span class="special">.</span><span class="identifier">count</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" microseconds\n"</span><span class="special">;</span>
        <span class="keyword">return</span> <span class="keyword">true</span><span class="special">;</span>
    <span class="special">}</span>

    <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a><span class="special">&gt;</span>
    <span class="keyword">bool</span> <span class="identifier">wait_until</span><span class="special">(</span><span class="identifier">mutex</span><span class="special">&amp;,</span> <span class="keyword">const</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a><span class="special">&gt;&amp;</span> <span class="identifier">t</span><span class="special">)</span> <span class="special">{</span>
        <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">;</span>
        <span class="keyword">typedef</span> <a class="link" href="reference.html#chrono.reference.cpp0x.time_point_hpp.time_point" title="Class template time_point&lt;&gt;"><code class="computeroutput"><span class="identifier">time_point</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">,</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">Duration</span></code></a><span class="special">&gt;</span> <span class="identifier">Time</span><span class="special">;</span>
        <span class="keyword">typedef</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">SysTime</span><span class="special">;</span>
        <span class="keyword">if</span> <span class="special">(</span><span class="identifier">t</span> <span class="special">&lt;=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">())</span>
            <span class="keyword">return</span> <span class="keyword">false</span><span class="special">;</span>
        <span class="keyword">typedef</span> <span class="keyword">typename</span> <code class="computeroutput"><span class="identifier">common_type</span></code><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Time</span><span class="special">::</span><span class="identifier">duration</span><span class="special">,</span>
          <span class="keyword">typename</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">duration</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">D</span><span class="special">;</span>
        <span class="comment">/* auto */</span> <span class="identifier">D</span> <span class="identifier">d</span> <span class="special">=</span> <span class="identifier">t</span> <span class="special">-</span> <a class="link" href="reference.html#chrono.reference.cpp0x.clock" title="Clock Requirements"><code class="computeroutput"><span class="identifier">Clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">();</span>
        <span class="identifier">microseconds</span> <span class="identifier">us</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_cast" title="Non-Member Function duration_cast(duration)"><code class="computeroutput"><span class="identifier">duration_cast</span></code></a><span class="special">&lt;</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">microseconds</span></code></a><span class="special">&gt;(</span><span class="identifier">d</span><span class="special">);</span>
        <span class="identifier">SysTime</span> <span class="identifier">st</span> <span class="special">=</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">us</span><span class="special">;</span>
         <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"wait_until     "</span><span class="special">;</span>
        <span class="identifier">detail</span><span class="special">::</span><span class="identifier">print_time</span><span class="special">(</span><span class="identifier">st</span><span class="special">);</span>
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">" which is "</span> <span class="special">&lt;&lt;</span> <span class="special">(</span><span class="identifier">st</span> <span class="special">-</span> <a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()).</span><span class="identifier">count</span><span class="special">()</span>
          <span class="special">&lt;&lt;</span> <span class="string">" microseconds away\n"</span><span class="special">;</span>
        <span class="keyword">return</span> <span class="keyword">true</span><span class="special">;</span>
    <span class="special">}</span>
<span class="special">};</span>
<span class="special">}</span>
</pre>
<p>
            Next follows how simple is the usage of this functions:
          </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">mutex</span> <span class="identifier">m</span><span class="special">;</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">timed_mutex</span> <span class="identifier">mut</span><span class="special">;</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">condition_variable</span> <span class="identifier">cv</span><span class="special">;</span>

<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">;</span>

<span class="identifier">this_thread</span><span class="special">::</span><span class="identifier">sleep_for</span><span class="special">(</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">seconds</span></code></a><span class="special">(</span><span class="number">3</span><span class="special">));</span>
<span class="identifier">this_thread</span><span class="special">::</span><span class="identifier">sleep_for</span><span class="special">(</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">nanoseconds</span></code></a><span class="special">(</span><span class="number">300</span><span class="special">));</span>
<span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">time_point</span> <span class="identifier">time_limit</span> <span class="special">=</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">chrono</span><span class="special">::</span><span class="identifier">__seconds_</span><span class="special">(</span><span class="number">4</span><span class="special">)</span> <span class="special">+</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">milliseconds</span></code></a><span class="special">(</span><span class="number">500</span><span class="special">);</span>
<span class="identifier">this_thread</span><span class="special">::</span><span class="identifier">sleep_until</span><span class="special">(</span><span class="identifier">time_limit</span><span class="special">);</span>

<span class="identifier">mut</span><span class="special">.</span><span class="identifier">try_lock_for</span><span class="special">(</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">milliseconds</span></code></a><span class="special">(</span><span class="number">30</span><span class="special">));</span>
<span class="identifier">mut</span><span class="special">.</span><span class="identifier">try_lock_until</span><span class="special">(</span><span class="identifier">time_limit</span><span class="special">);</span>

<span class="identifier">cv</span><span class="special">.</span><span class="identifier">wait_for</span><span class="special">(</span><span class="identifier">m</span><span class="special">,</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration_typedefs" title="duration typedefs"><code class="computeroutput"><span class="identifier">minutes</span></code></a><span class="special">(</span><span class="number">1</span><span class="special">));</span>    <span class="comment">// real code would put this in a loop</span>
<span class="identifier">cv</span><span class="special">.</span><span class="identifier">wait_until</span><span class="special">(</span><span class="identifier">m</span><span class="special">,</span> <span class="identifier">time_limit</span><span class="special">);</span>  <span class="comment">// real code would put this in a loop</span>

<span class="comment">// For those who prefer floating-point</span>
<span class="identifier">this_thread</span><span class="special">::</span><span class="identifier">sleep_for</span><span class="special">(</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">0.25</span><span class="special">));</span>
<span class="identifier">this_thread</span><span class="special">::</span><span class="identifier">sleep_until</span><span class="special">(</span><span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.system_clocks_hpp.system_clock" title="Class system_clock"><code class="computeroutput"><span class="identifier">system_clock</span></code></a><span class="special">::</span><span class="identifier">now</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">chrono</span><span class="special">::</span><a class="link" href="reference.html#chrono.reference.cpp0x.duration_hpp.duration" title="Class Template duration&lt;&gt;"><code class="computeroutput"><span class="identifier">duration</span></code></a><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">1.5</span><span class="special">));</span>
</pre>
<p>
            <span class="emphasis"><em>See the source file <a href="../../../libs/chrono/example/simulated_thread_interface_demo.cpp" target="_top">example/simulated_thread_interface_demo.cpp</a></em></span>
          </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="chrono.users_guide.examples.io"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.io" title="IO">IO</a>
</h4></div></div></div>
<div class="toc"><dl class="toc"><dt><span class="section"><a href="users_guide.html#chrono.users_guide.examples.io.french">French Output</a></span></dt></dl></div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="chrono.users_guide.examples.io.french"></a><a class="link" href="users_guide.html#chrono.users_guide.examples.io.french" title="French Output">French Output</a>
</h5></div></div></div>
<p>
            Example use of output in French
          </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">chrono</span><span class="special">/</span><span class="identifier">chrono_io</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">locale</span><span class="special">&gt;</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">;</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">chrono</span><span class="special">;</span>

    <span class="identifier">cout</span><span class="special">.</span><span class="identifier">imbue</span><span class="special">(</span><span class="identifier">locale</span><span class="special">(</span><span class="identifier">locale</span><span class="special">(),</span> <span class="keyword">new</span> <span class="identifier">duration_punct</span><span class="special">&lt;</span><span class="keyword">char</span><span class="special">&gt;</span>
        <span class="special">(</span>
            <span class="identifier">duration_punct</span><span class="special">&lt;</span><span class="keyword">char</span><span class="special">&gt;::</span><span class="identifier">use_long</span><span class="special">,</span>
            <span class="string">"secondes"</span><span class="special">,</span> <span class="string">"minutes"</span><span class="special">,</span> <span class="string">"heures"</span><span class="special">,</span>
            <span class="string">"s"</span><span class="special">,</span> <span class="string">"m"</span><span class="special">,</span> <span class="string">"h"</span>
        <span class="special">)));</span>
    <span class="identifier">hours</span> <span class="identifier">h</span><span class="special">(</span><span class="number">5</span><span class="special">);</span>
    <span class="identifier">minutes</span> <span class="identifier">m</span><span class="special">(</span><span class="number">45</span><span class="special">);</span>
    <span class="identifier">seconds</span> <span class="identifier">s</span><span class="special">(</span><span class="number">15</span><span class="special">);</span>
    <span class="identifier">milliseconds</span> <span class="identifier">ms</span><span class="special">(</span><span class="number">763</span><span class="special">);</span>
    <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">h</span> <span class="special">&lt;&lt;</span> <span class="string">", "</span> <span class="special">&lt;&lt;</span> <span class="identifier">m</span> <span class="special">&lt;&lt;</span> <span class="string">", "</span> <span class="special">&lt;&lt;</span> <span class="identifier">s</span> <span class="special">&lt;&lt;</span> <span class="string">" et "</span> <span class="special">&lt;&lt;</span> <span class="identifier">ms</span> <span class="special">&lt;&lt;</span> <span class="char">'\n'</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            Output is:
          </p>
<pre class="programlisting"><span class="number">5</span> <span class="identifier">heures</span><span class="special">,</span> <span class="number">45</span> <span class="identifier">minutes</span><span class="special">,</span> <span class="number">15</span> <span class="identifier">secondes</span> <span class="identifier">et</span> <span class="number">763</span> <span class="identifier">millisecondes</span>
</pre>
<p>
            <span class="emphasis"><em>See the source file <a href="../../../libs/chrono/example/french.cpp" target="_top">example/french.cpp</a></em></span>
          </p>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="chrono.users_guide.ext_references"></a><a class="link" href="users_guide.html#chrono.users_guide.ext_references" title="External Resources">External Resources</a>
</h3></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term"><a href="http://www.open-std.org/jtc1/sc22/wg21" target="_top"><span class="bold"><strong>C++
          Standards Committee's current Working Paper</strong></span></a></span></dt>
<dd><p>
              The most authoritative reference material for the library is the C++
              Standards Committee's current Working Paper (WP). 20.11 Time utilities
              "time"
            </p></dd>
<dt><span class="term"><a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2661.htm" target="_top"><span class="bold"><strong>N2661 - A Foundation to Sleep On</strong></span></a></span></dt>
<dd><p>
              From Howard E. Hinnant, Walter E. Brown, Jeff Garland and Marc Paterno.
              Is very informative and provides motivation for key design decisions
            </p></dd>
<dt><span class="term"><a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2010/n3134.html#934" target="_top"><span class="bold"><strong>LGW 934. duration is missing operator%</strong></span></a></span></dt>
<dd><p>
              From Terry Golubiewski. Is very informative and provides motivation
              for key design decisions
            </p></dd>
<dt><span class="term"><a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2010/n3135.html#935" target="_top"><span class="bold"><strong>LGW 935. clock error handling needs to be specified</strong></span></a></span></dt>
<dd><p>
              From Beman Dawes. This issue has been stated as NAD Future.
            </p></dd>
</dl>
</div>
</div>
<div class="footnotes">
<br><hr style="width:100; text-align:left;margin-left: 0">
<div id="ftn.chrono.users_guide.tutorial.i_o.duration_io.f0" class="footnote"><p><a href="#chrono.users_guide.tutorial.i_o.duration_io.f0" class="para"><sup class="para">[1] </sup></a>
              <a class="link" href="reference.html#chrono.reference.io_v1.chrono_io_hpp.manipulators" title="I/O Manipulators"><code class="computeroutput"><span class="identifier">duration_short</span></code></a> in V1
            </p></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2008 Howard Hinnant<br>Copyright © 2006, 2008 Beman Dawes<br>Copyright © 2009-2013 Vicente J. Botet Escriba<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../chrono.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../chrono.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="reference.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
