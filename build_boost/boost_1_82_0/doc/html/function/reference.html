<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Reference</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../function.html" title="Chapter 14. Boost.Function">
<link rel="prev" href="tutorial.html" title="Tutorial">
<link rel="next" href="../boost/bad_function_call.html" title="Class bad_function_call">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="tutorial.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../function.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/bad_function_call.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="function.reference"></a>Reference</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#function.definitions">Definitions</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.function_hpp">Header &lt;boost/function.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.function_equal_hpp">Header &lt;boost/function_equal.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="function.definitions"></a>Definitions</h3></div></div></div>
<p>
  </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
<p>A function object <code class="computeroutput">f</code> is
      <span class="emphasis"><em>compatible</em></span> if for the given set of argument
      types <code class="computeroutput">Arg1</code>,
      <code class="computeroutput">Arg2</code>, ...,
      <code class="computeroutput">ArgN</code> and a
      return type <code class="computeroutput">ResultType</code>, the
      appropriate following function is well-formed:
</p>
<pre class="programlisting">
  <span class="emphasis"><em>// if ResultType is not <span class="bold"><strong>void</strong></span></em></span>
  ResultType foo(Arg1 arg1, Arg2 arg2, ..., Arg<span class="emphasis"><em>N</em></span> arg<span class="emphasis"><em>N</em></span>)
  {
    <span class="bold"><strong>return</strong></span> f(arg1, arg2, ..., arg<span class="emphasis"><em>N</em></span>);
  }

  <span class="emphasis"><em>// if ResultType is <span class="bold"><strong>void</strong></span></em></span>
  ResultType foo(Arg1 arg1, Arg2 arg2, ..., Arg<span class="emphasis"><em>N</em></span> arg<span class="emphasis"><em>N</em></span>)
  {
    f(arg1, arg2, ..., arg<span class="emphasis"><em>N</em></span>);
  }
</pre>
<p> A special provision is made for pointers to member
      functions. Though they are not function objects, Boost.Function
      will adapt them internally to function objects. This requires
      that a pointer to member function of the form <code class="computeroutput">R
      (X::*mf)(Arg1, Arg2, ..., ArgN)
      cv-quals</code> be adapted to a
      function object with the following function call operator
      overloads:
</p>
<pre class="programlisting">
  <span class="bold"><strong>template</strong></span>&lt;<span class="bold"><strong>typename P</strong></span>&gt;
  R <span class="bold"><strong>operator</strong></span>()(<span class="emphasis"><em>cv-quals</em></span> P&amp; x, Arg1 arg1, Arg2 arg2, ..., Arg<span class="emphasis"><em>N</em></span> arg<span class="emphasis"><em>N</em></span>) <span class="bold"><strong>const</strong></span>
  {
    <span class="bold"><strong>return</strong></span> (*x).*mf(arg1, arg2, ..., arg<span class="emphasis"><em>N</em></span>);
  }
</pre>
<p>
</p>
</li>
<li class="listitem"><p>A function object <code class="computeroutput">f</code> of
      type <code class="computeroutput">F</code> is
      <span class="emphasis"><em>stateless</em></span> if it is a function pointer or if
      <code class="computeroutput">boost::is_stateless&lt;F&gt;</code>
      is true. The construction of or copy to a Boost.Function object
      from a stateless function object will not cause exceptions to be
      thrown and will not allocate any storage.
      </p></li>
</ul></div>
<p>
</p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.function_hpp"></a>Header &lt;<a href="../../../boost/function.hpp" target="_top">boost/function.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">class</span> <a class="link" href="../boost/bad_function_call.html" title="Class bad_function_call">bad_function_call</a><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="../boost/function_base.html" title="Class function_base">function_base</a><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> R<span class="special">,</span> <span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">&gt;</span> 
    <span class="keyword">class</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="../boost/functionN.html#boost.functionN.swap"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_1_1-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Functor</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_1_2-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="identifier">Functor</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_1_3-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> 
                    reference_wrapper<span class="special">&lt;</span><span class="identifier">Functor</span><span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_1_4-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span>reference_wrapper<span class="special">&lt;</span><span class="identifier">Functor</span><span class="special">&gt;</span><span class="special">,</span> 
                    <span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> U1<span class="special">,</span> 
           <span class="keyword">typename</span> U2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> UN<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_1_5-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> 
                    <span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">U1</span><span class="special">,</span> <span class="identifier">U2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">UN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_2_1-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Functor</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_2_2-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="identifier">Functor</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_2_3-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> 
                    reference_wrapper<span class="special">&lt;</span><span class="identifier">Functor</span><span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_2_4-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span>reference_wrapper<span class="special">&lt;</span><span class="identifier">Functor</span><span class="special">&gt;</span><span class="special">,</span> 
                    <span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T1<span class="special">,</span> <span class="keyword">typename</span> T2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> TN<span class="special">,</span> <span class="keyword">typename</span> U1<span class="special">,</span> 
           <span class="keyword">typename</span> U2<span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="keyword">typename</span> UN<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="../boost/functionN.html#id-1_3_15_6_2_1_3_28_2_5-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">T1</span><span class="special">,</span> <span class="identifier">T2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">TN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> 
                    <span class="keyword">const</span> <a class="link" href="../boost/functionN.html" title="Class template functionN">functionN</a><span class="special">&lt;</span><span class="identifier">U1</span><span class="special">,</span> <span class="identifier">U2</span><span class="special">,</span> <span class="special">...</span><span class="special">,</span> <span class="identifier">UN</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="../boost/function.html#boost.function.swap"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_1_1-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Functor</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_1_2-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="identifier">Functor</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_1_3-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> reference_wrapper<span class="special">&lt;</span><span class="identifier">Functor</span><span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_1_4-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span>reference_wrapper<span class="special">&lt;</span><span class="identifier">Functor</span><span class="special">&gt;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature1<span class="special">,</span> <span class="keyword">typename</span> Signature2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_1_5-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature1</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature2</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_2_1-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Functor</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_2_2-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="identifier">Functor</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_2_3-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> reference_wrapper<span class="special">&lt;</span><span class="identifier">Functor</span><span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">,</span> <span class="keyword">typename</span> Functor<span class="special">&gt;</span> 
    <span class="keyword">bool</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_2_4-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span>reference_wrapper<span class="special">&lt;</span><span class="identifier">Functor</span><span class="special">&gt;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature1<span class="special">,</span> <span class="keyword">typename</span> Signature2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="../boost/function.html#id-1_3_15_6_2_1_4_32_2_5-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature1</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/function.html" title="Class template function">function</a><span class="special">&lt;</span><span class="identifier">Signature2</span><span class="special">&gt;</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.function_equal_hpp"></a>Header &lt;<a href="../../../boost/function_equal.hpp" target="_top">boost/function_equal.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> F<span class="special">,</span> <span class="keyword">typename</span> G<span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="../boost/function_equal.html" title="Function template function_equal"><span class="identifier">function_equal</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">F</span><span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">G</span><span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span></pre>
</div>
</div>
<div class="copyright-footer">Copyright © 2001-2004 Douglas Gregor<p>Use, modification and distribution is subject to the Boost
    Software License, Version 1.0. (See accompanying file
    <code class="filename">LICENSE_1_0.txt</code> or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)</p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="tutorial.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../function.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/bad_function_call.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
