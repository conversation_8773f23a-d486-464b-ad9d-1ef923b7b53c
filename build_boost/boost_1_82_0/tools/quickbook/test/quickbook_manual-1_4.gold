<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE article PUBLIC "-//Boost//DTD BoostBook XML V1.0//EN" "http://www.boost.org/tools/boostbook/dtd/boostbook.dtd">
<article id="quickbook" last-revision="DEBUG MODE Date: 2000/12/20 12:00:00 $" xmlns:xi="http://www.w3.org/2001/XInclude">
  <title>Quickbook 1.4</title>
  <articleinfo>
    <authorgroup>
    <author>
      <firstname>Joel</firstname> <surname>de <PERSON></surname>
    </author>
    <author>
      <firstname>Eric</firstname> <surname><PERSON><PERSON><PERSON></surname>
    </author>
    </authorgroup>
    <copyright>
      <year>2002</year> <year>2004</year> <year>2006</year> <holder><PERSON>,
      <PERSON></holder>
    </copyright>
    <legalnotice id="quickbook.legal">
      <para>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <ulink url="http://www.boost.org/LICENSE_1_0.txt">http://www.boost.org/LICENSE_1_0.txt</ulink>)
      </para>
    </legalnotice>
    <articlepurpose>
      <emphasis>WikiWiki</emphasis> style documentation tool
    </articlepurpose>
  </articleinfo>
  <section id="quickbook.intro">
    <title><link linkend="quickbook.intro">Introduction</link></title>
    <blockquote>
      <para>
        <emphasis role="bold"><emphasis><quote>Why program by hand in five days what
        you can spend five years of your life automating?</quote></emphasis></emphasis>
      </para>
      <para>
        -- Terrence Parr, author ANTLR/PCCTS
      </para>
    </blockquote>
    <para>
      Well, QuickBook started as a weekend hack. It was originally intended to be
      a sample application using <ulink url="http://spirit.sourceforge.net">Spirit</ulink>.
      What is it? What you are viewing now, this documentation, is autogenerated
      by QuickBook. These files were generated from one master:
    </para>
    <blockquote>
      <para>
        <ulink url="../quickbook.qbk">quickbook.qbk</ulink>
      </para>
    </blockquote>
    <para>
      Originally named QuickDoc, this funky tool that never dies evolved into a funkier
      tool thanks to Eric Niebler who resurrected the project making it generate
      <ulink url="http://www.boost.org/doc/html/boostbook.html">BoostBook</ulink>
      instead of HTML. The <ulink url="http://www.boost.org/doc/html/boostbook.html">BoostBook</ulink>
      documentation format is an extension of <ulink url="http://www.docbook.org/">DocBook</ulink>,
      an SGML or XML based format for describing documentation.
    </para>
    <para>
      QuickBook is a WikiWiki style documentation tool geared towards C++ documentation
      using simple rules and markup for simple formatting tasks. QuickBook extends
      the WikiWiki concept. Like the WikiWiki, QuickBook documents are simple text
      files. A single QuickBook document can generate a fully linked set of nice
      HTML and PostScript/PDF documents complete with images and syntax- colorized
      source code.
    </para>
    <para>
      Features include:
    </para>
    <itemizedlist>
      <listitem>
        <simpara>
          generate <ulink url="http://www.boost.org/doc/html/boostbook.html">BoostBook</ulink>
          xml, to generate HTML, PostScript and PDF
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          simple markup to link to Doxygen-generated entities
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          macro system for simple text substitution
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          simple markup for italics, bold, preformatted, blurbs, code samples, tables,
          URLs, anchors, images, etc.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          automatic syntax coloring of code samples
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          CSS support
        </simpara>
      </listitem>
    </itemizedlist>
  </section>
  <section id="quickbook.change_log">
    <title><link linkend="quickbook.change_log">Change Log</link></title>
    <bridgehead renderas="sect3" id="quickbook.change_log.h0">
      <phrase id="quickbook.change_log.version_1_3"/><link linkend="quickbook.change_log.version_1_3">Version
      1.3</link>
    </bridgehead>
    <itemizedlist>
      <listitem>
        <simpara>
          Quickbook file inclusion [include].
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Better xml output (pretty layout). Check out the generated XML.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Regression testing facility: to make sure your document will always be
          compatible (full backward compatibility) regardless of changes to QuickBook.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Code cleanup and refactoring.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Allow phrase markup in the doc-info.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Preformatted code blocks via ``code`` (double ticks) allows code in tables
          and lists, for example.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Quickbook versioning; allows full backward compatibility. You have to add
          [quickbook 1.3] to the doc-info header to enable the new features. Without
          this, QuickBook will assume that the document is a pre-1.3 document.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Better (intuitive) paragraph termination. Some markups may terminate a
          paragraph. Example:
<programlisting><phrase role="special">[</phrase><phrase role="identifier">section</phrase> <phrase role="identifier">x</phrase><phrase role="special">]</phrase>
<phrase role="identifier">blah</phrase><phrase role="special">...</phrase>
<phrase role="special">[</phrase><phrase role="identifier">endsect</phrase><phrase role="special">]</phrase></programlisting>
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Fully qualified section and headers. Subsection names are concatenated
          to the ID to avoid clashing. Example: <code><phrase role="identifier">doc_name</phrase><phrase
          role="special">.</phrase><phrase role="identifier">sect_name</phrase><phrase
          role="special">.</phrase><phrase role="identifier">sub_sect_name</phrase><phrase
          role="special">.</phrase><phrase role="identifier">sub_sub_sect_name</phrase></code>
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Better &amp;nbsp; and whitespace handling in code snippets.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          [xinclude] fixes up the relative path to the target XML file when input_directory
          is not the same as the output_directory.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Allow untitled tables.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Allow phrase markups in section titles.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Allow escaping back to QuickBook from code, code blocks and inline code.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Footnotes, with the [footnote This is the footnote] syntax.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Post-processor bug fix for escaped XML code that it does not recognize.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Replaceable, with the [~replacement] syntax.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Generic Headers
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Code changes to allow full recursion (i.e. Collectors and push/pop functions)
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Various code cleanup/maintenance
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Templates!
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          [conceptref] for referencing BoostBook &lt;concept&gt; entities.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Allow escape of spaces. The escaped space is removed from the output. Syntax:
          <code><phrase role="special">\</phrase> </code>.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Nested comments are now allowed.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Quickbook blocks can nest inside comments.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          <link linkend="quickbook.syntax.block.import">Import</link> facility.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Callouts on imported code
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          Simple markups can now span a whole block.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          <link linkend="quickbook.syntax.block.blurbs">Blurbs</link>, <link linkend="quickbook.syntax.block.admonitions">Admonitions</link>
          and table cells (see <link linkend="quickbook.syntax.block.tables">Tables</link>)
          may now contain paragraphs.
        </simpara>
      </listitem>
      <listitem>
        <simpara>
          <code><phrase role="special">\</phrase><phrase role="identifier">n</phrase></code>
          and <code><phrase role="special">[</phrase><phrase role="identifier">br</phrase><phrase
          role="special">]</phrase></code> are now deprecated.
        </simpara>
      </listitem>
    </itemizedlist>
  </section>
  <section id="quickbook.syntax">
    <title><link linkend="quickbook.syntax">Syntax Summary</link></title>
    <para>
      A QuickBook document is composed of one or more blocks. An example of a block
      is the paragraph or a C++ code snippet. Some blocks have special mark-ups.
      Blocks, except code snippets which have their own grammar (C++ or Python),
      are composed of one or more phrases. A phrase can be a simple contiguous run
      of characters. Phrases can have special mark-ups. Marked up phrases can recursively
      contain other phrases, but cannot contain blocks. A terminal is a self contained
      block-level or phrase-level element that does not nest anything.
    </para>
    <para>
      Blocks, in general, are delimited by two end-of-lines (the block terminator).
      Phrases in each block cannot contain a block terminator. This way, syntax errors
      such as un-matched closing brackets do not go haywire and corrupt anything
      past a single block.
    </para>
    <section id="quickbook.syntax.comments">
      <title><link linkend="quickbook.syntax.comments">Comments</link></title>
      <para>
        Can be placed anywhere.
      </para>
<programlisting><!--quickbook-escape-prefix-->[/ comment (no output generated) ]<!--quickbook-escape-postfix-->
</programlisting>
<programlisting><!--quickbook-escape-prefix-->[/ comments can be nested [/ some more here] ]<!--quickbook-escape-postfix-->
</programlisting>
<programlisting><!--quickbook-escape-prefix-->[/ Quickbook blocks can nest inside comments. [*Comment this out too!] ]<!--quickbook-escape-postfix-->
</programlisting>
    </section>
    <section id="quickbook.syntax.phrase">
      <title><link linkend="quickbook.syntax.phrase">Phrase Level Elements</link></title>
      <section id="quickbook.syntax.phrase.font_styles">
        <title><link linkend="quickbook.syntax.phrase.font_styles">Font Styles</link></title>
<programlisting><!--quickbook-escape-prefix-->['italic], [*bold], [_underline], [^teletype], [-strikethrough]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <para>
          <emphasis>italic</emphasis>, <emphasis role="bold">bold</emphasis>, <emphasis
          role="underline">underline</emphasis>, <literal>teletype</literal>, <emphasis
          role="strikethrough">strikethrough</emphasis>
        </para>
        <para>
          Like all non-terminal phrase level elements, this can of course be nested:
        </para>
<programlisting><!--quickbook-escape-prefix-->[*['bold-italic]]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <para>
          <emphasis role="bold"><emphasis>bold-italic</emphasis></emphasis>
        </para>
      </section>
      <section id="quickbook.syntax.phrase.replaceable">
        <title><link linkend="quickbook.syntax.phrase.replaceable">Replaceable</link></title>
        <para>
          When you want content that may or must be replaced by the user, use the
          syntax:
        </para>
<programlisting><!--quickbook-escape-prefix-->[~replacement]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          This will generate:
        </para>
        <para>
          <replaceable>replacement</replaceable>
        </para>
      </section>
      <section id="quickbook.syntax.phrase.quotations">
        <title><link linkend="quickbook.syntax.phrase.quotations">Quotations</link></title>
<programlisting><!--quickbook-escape-prefix-->["A question that sometimes drives me hazy: am I or are the others crazy?]--Einstein
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <para>
          <quote>A question that sometimes drives me hazy: am I or are the others
          crazy?</quote>--Einstein
        </para>
        <para>
          Note the proper left and right quote marks. Also, while you can simply
          use ordinary quote marks like &quot;quoted&quot;, our quotation, above,
          will generate correct DocBook quotations (e.g. &lt;quote&gt;quoted&lt;/quote&gt;).
        </para>
        <para>
          Like all phrase elements, quotations may be nested. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->["Here's the rule for bargains: ["Do other men, for they would do you.] That's
the true business precept.]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <para>
          <quote>Here's the rule for bargains: <quote>Do other men, for they would
          do you.</quote> That's the true business precept.</quote>
        </para>
      </section>
      <section id="quickbook.syntax.phrase.simple_formatting">
        <title><link linkend="quickbook.syntax.phrase.simple_formatting">Simple formatting</link></title>
        <para>
          Simple markup for formatting text, common in many applications, is now
          supported:
        </para>
<programlisting><!--quickbook-escape-prefix-->/italic/, *bold*, _underline_, =teletype=
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <para>
          <emphasis>italic</emphasis>, <emphasis role="bold">bold</emphasis>, <emphasis
          role="underline">underline</emphasis>, <literal>teletype</literal>
        </para>
        <para>
          Unlike QuickBook's standard formatting scheme, the rules for simpler alternatives
          are much stricter<footnote id="quickbook.syntax.phrase.simple_formatting.f0">
          <para>
            Thanks to David Barrett, author of <ulink url="http://quinthar.com/qwikiwiki/index.php?page=Home">Qwiki</ulink>,
            for sharing these samples and teaching me these obscure formatting rules.
            I wasn't sure at all if <ulink url="http://spirit.sourceforge.net">Spirit</ulink>,
            being more or less a formal EBNF parser, can handle the context sensitivity
            and ambiguity.
          </para>
          </footnote>.
        </para>
        <itemizedlist>
          <listitem>
            <simpara>
              Simple markups cannot nest. You can combine a simple markup with a
              nestable markup.
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              Simple markups cannot contain any other form of quickbook markup.
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              A non-space character must follow the leading markup
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              A non-space character must precede the trailing markup
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              A space or a punctuation must follow the trailing markup
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              If the matching markup cannot be found within a block, the formatting
              will not be applied. This is to ensure that un-matched formatting markups,
              which can be a common mistake, does not corrupt anything past a single
              block. We do not want the rest of the document to be rendered bold
              just because we forgot a trailing '*'. A single block is terminated
              by two end of lines or the close bracket: ']'.
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              A line starting with the star will be interpreted as an unordered list.
              See <link linkend="quickbook.syntax.block.lists.unordered_lists">Unordered
              lists</link>.
            </simpara>
          </listitem>
        </itemizedlist>
        <table frame="all" id="quickbook.syntax.phrase.simple_formatting.t0">
          <title>More Formatting Samples</title>
          <tgroup cols="2">
            <thead>
              <row>
                <entry>
                  <para>
                    Markup
                  </para>
                </entry>
                <entry>
                  <para>
                    Result
                  </para>
                </entry>
              </row>
            </thead>
            <tbody>
              <row>
                <entry>
                  <para>
                    <literal>*Bold*</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    <emphasis role="bold">Bold</emphasis>
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>*Is bold*</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    <emphasis role="bold">Is bold</emphasis>
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>* Not bold* *Not bold * * Not bold *</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    * Not bold* *Not bold * * Not bold *
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>This*Isn't*Bold (no bold)</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    This*Isn't*Bold (no bold)
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>(*Bold Inside*) (parenthesis not bold)</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    (<emphasis role="bold">Bold Inside</emphasis>) (parenthesis not
                    bold)
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>*(Bold Outside)* (parenthesis bold)</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    <emphasis role="bold">(Bold Outside)</emphasis> (parenthesis
                    bold)
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>3*4*5 = 60 (no bold)</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    3*4*5 = 60 (no bold)
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>3 * 4 * 5 = 60 (no bold)</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    3 * 4 * 5 = 60 (no bold)
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>3 *4* 5 = 60 (4 is bold)</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    3 <emphasis role="bold">4</emphasis> 5 = 60 (4 is bold)
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>*This is bold* this is not *but this is*</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    <emphasis role="bold">This is bold</emphasis> this is not <emphasis
                    role="bold">but this is</emphasis>
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>*This is bold*.</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    <emphasis role="bold">This is bold</emphasis>.
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>*B*. (bold B)</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    <emphasis role="bold">B</emphasis>. (bold B)
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>['*Bold-Italic*]</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    <emphasis><emphasis role="bold">Bold-Italic</emphasis></emphasis>
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    <literal>*side-by*/-side/</literal>
                  </para>
                </entry>
                <entry>
                  <para>
                    <emphasis role="bold">side-by</emphasis><emphasis>-side</emphasis>
                  </para>
                </entry>
              </row>
            </tbody>
          </tgroup>
        </table>
        <para>
          As mentioned, simple markups cannot go past a single block. The text from
          &quot;have&quot; to &quot;full&quot; in the following paragraph will be
          rendered as bold:
        </para>
<programlisting><!--quickbook-escape-prefix-->Baa baa black sheep, *have you any wool?
Yes sir, yes sir, three bags full!*
One for the master, one for the dame,
And one for the little boy who lives down the lane.
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Baa baa black sheep, <emphasis role="bold">have you any wool? Yes sir,
          yes sir, three bags full!</emphasis> One for the master, one for the dame,
          And one for the little boy who lives down the lane.
        </para>
        <para>
          But in the following paragraph, bold is not applied:
        </para>
<programlisting><!--quickbook-escape-prefix-->Baa baa black sheep, *have you any wool?
Yes sir, yes sir, three bags full!
One for the master, one for the dame,
And one for the little boy who lives down the lane.
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Baa baa black sheep, *have you any wool? Yes sir, yes sir, three bags full!
          One for the master, one for the dame, And one for the little boy who lives
          down the lane.
        </para>
      </section>
      <section id="quickbook.syntax.phrase.inline_code">
        <title><link linkend="quickbook.syntax.phrase.inline_code">Inline code</link></title>
        <para>
          Inlining code in paragraphs is quite common when writing C++ documentation.
          We provide a very simple markup for this. For example, this:
        </para>
<programlisting><!--quickbook-escape-prefix-->This text has inlined code `int main() { return 0; }` in it.
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <para>
          This text has inlined code <code><phrase role="keyword">int</phrase> <phrase
          role="identifier">main</phrase><phrase role="special">()</phrase> <phrase
          role="special">{</phrase> <phrase role="keyword">return</phrase> <phrase
          role="number">0</phrase><phrase role="special">;</phrase> <phrase role="special">}</phrase></code>
          in it. The code will be syntax highlighted.
        </para>
        <note>
          <para>
            We simply enclose the code with the tick: <literal>"`"</literal>, not the
            single quote: <code><phrase role="string">&quot;'&quot;</phrase></code>.
            Note too that <literal>`some code`</literal> is preferred over <literal>[^some code]</literal>.
          </para>
        </note>
      </section>
      <section id="quickbook.syntax.phrase.code_blocks">
        <title><link linkend="quickbook.syntax.phrase.code_blocks">Code blocks</link></title>
        <para>
          Preformatted code simply starts with a space or a tab (See <link linkend="quickbook.syntax.block.code">Code</link>).
          However, such a simple syntax cannot be used as phrase elements in lists
          (See <link linkend="quickbook.syntax.block.lists.ordered_lists">Ordered
          lists</link> and <link linkend="quickbook.syntax.block.lists.unordered_lists">Unordered
          lists</link>), tables (See <link linkend="quickbook.syntax.block.tables">Tables</link>),
          etc. Inline code (see above) can. The problem is, inline code does not
          allow formatting with newlines, spaces, and tabs. These are lost.
        </para>
        <para>
          We provide a phrase level markup that is a mix between the two. By using
          the double-tick, instead of the single-tick, we are telling QuickBook to
          use preformatted blocks of code. Example:
        </para>
<programlisting>``
    #include &lt;iostream&gt;

    int main()
    {
        std::cout &lt;&lt; &quot;Hello, World!&quot; &lt;&lt; std::endl;
        return 0;
    }
``
</programlisting>
        <para>
          will generate:
        </para>
        <para>
<programlisting><phrase role="preprocessor">#include</phrase> <phrase role="special">&lt;</phrase><phrase role="identifier">iostream</phrase><phrase role="special">&gt;</phrase>

<phrase role="keyword">int</phrase> <phrase role="identifier">main</phrase><phrase role="special">()</phrase>
<phrase role="special">{</phrase>
    <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">cout</phrase> <phrase role="special">&lt;&lt;</phrase> <phrase role="string">&quot;Hello, World!&quot;</phrase> <phrase role="special">&lt;&lt;</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">endl</phrase><phrase role="special">;</phrase>
    <phrase role="keyword">return</phrase> <phrase role="number">0</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase>
</programlisting>
        </para>
      </section>
      <section id="quickbook.syntax.phrase.source_mode">
        <title><link linkend="quickbook.syntax.phrase.source_mode">Source Mode</link></title>
        <para>
          If a document contains more than one type of source code then the source
          mode may be changed dynamically as the document is processed. All QuickBook
          documents are initially in C++ mode by default, though an alternative initial
          value may be set in the <link linkend="quickbook.syntax.block.document">Document</link>
          section.
        </para>
        <para>
          To change the source mode, use the <literal>[source-mode]</literal> markup,
          where <literal>source-mode</literal> is one of the supported modes. For
          example, this:
        </para>
<programlisting><!--quickbook-escape-prefix-->Python's [python] `import` is rather like C++'s [c++] `#include`. A
C++ comment `// looks like this` whereas a Python comment [python]
`# looks like this`.
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <para>
          Python's <code><phrase role="keyword">import</phrase></code> is rather
          like C++'s <code><phrase role="preprocessor">#include</phrase></code>.
          A C++ comment <code><phrase role="comment">// looks like this</phrase></code>
          whereas a Python comment <code><phrase role="comment">#looks like this</phrase></code>.
        </para>
        <table frame="all" id="quickbook.syntax.phrase.source_mode.t0">
          <title>Supported Source Modes</title>
          <tgroup cols="2">
            <thead>
              <row>
                <entry>
                  <para>
                    Mode
                  </para>
                </entry>
                <entry>
                  <para>
                    Source Mode Markup
                  </para>
                </entry>
              </row>
            </thead>
            <tbody>
              <row>
                <entry>
                  <para>
                    C++
                  </para>
                </entry>
                <entry>
                  <para>
                    <literal>[c++]</literal>
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    Python
                  </para>
                </entry>
                <entry>
                  <para>
                    <literal>[python]</literal>
                  </para>
                </entry>
              </row>
            </tbody>
          </tgroup>
        </table>
        <note>
          <para>
            The source mode strings are lowercase.
          </para>
        </note>
      </section>
      <section id="quickbook.syntax.phrase.line_break">
        <title><link linkend="quickbook.syntax.phrase.line_break">line-break</link></title>
<programlisting><!--quickbook-escape-prefix-->[br]
<!--quickbook-escape-postfix--></programlisting>
        <warning>
          <para>
            <code><phrase role="special">[</phrase><phrase role="identifier">br</phrase><phrase
            role="special">]</phrase></code> is now deprecated. <link linkend="quickbook.syntax.block.blurbs">Blurbs</link>,
            <link linkend="quickbook.syntax.block.admonitions">Admonitions</link>
            and table cells (see <link linkend="quickbook.syntax.block.tables">Tables</link>)
            may now contain paragraphs.
          </para>
        </warning>
      </section>
      <section id="quickbook.syntax.phrase.anchors">
        <title><link linkend="quickbook.syntax.phrase.anchors">Anchors</link></title>
<programlisting><!--quickbook-escape-prefix-->[#named_anchor]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          A named anchor is a hook that can be referenced by a link elsewhere in
          the document. You can then reference an anchor with <literal>[link named_anchor
Some link text]</literal>.
          See <link linkend="quickbook.syntax.phrase.anchor_links">Anchor links</link>,
          <link linkend="quickbook.syntax.block.section">Section</link> and <link
          linkend="quickbook.syntax.block.headings">Heading</link>.
        </para>
      </section>
      <section id="quickbook.syntax.phrase.links">
        <title><link linkend="quickbook.syntax.phrase.links">Links</link></title>
<programlisting><!--quickbook-escape-prefix-->[@http://www.boost.org this is [*boost's] website....]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <para>
          <ulink url="http://www.boost.org">this is <emphasis role="bold">boost's</emphasis>
          website....</ulink>
        </para>
        <para>
          URL links where the link text is the link itself is common. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->see http://spirit.sourceforge.net/
<!--quickbook-escape-postfix--></programlisting>
        <para>
          so, when the text is absent in a link markup, the URL is assumed. Example:
        </para>
<programlisting>see <!--quickbook-escape-prefix-->[@http://spirit.sourceforge.net/]<!--quickbook-escape-postfix-->
</programlisting>
        <para>
          will generate:
        </para>
        <para>
          see <ulink url="http://spirit.sourceforge.net/">http://spirit.sourceforge.net/</ulink>
        </para>
      </section>
      <section id="quickbook.syntax.phrase.anchor_links">
        <title><link linkend="quickbook.syntax.phrase.anchor_links">Anchor links</link></title>
        <para>
          You can link within a document using:
        </para>
<programlisting><!--quickbook-escape-prefix-->[link section_id.normalized_header_text The link text]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          See sections <link linkend="quickbook.syntax.block.section">Section</link>
          and <link linkend="quickbook.syntax.block.headings">Heading</link> for
          more info.
        </para>
      </section>
      <section id="quickbook.syntax.phrase.refentry_links">
        <title><link linkend="quickbook.syntax.phrase.refentry_links">refentry links</link></title>
        <para>
          In addition, you can link internally to an XML refentry like:
        </para>
<programlisting><!--quickbook-escape-prefix-->[link xml.refentry The link text]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          This gets converted into <literal>&lt;link linkend=&quot;xml.refentry&quot;&gt;The
          link text&lt;/link&gt;</literal>.
        </para>
        <para>
          Like URLs, the link text is optional. If this is not present, the link
          text will automatically be the refentry. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[link xml.refentry]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          This gets converted into <literal>&lt;link linkend=&quot;xml.refentry&quot;&gt;xml.refentry&lt;/link&gt;</literal>.
        </para>
      </section>
      <section id="quickbook.syntax.phrase.code_links">
        <title><link linkend="quickbook.syntax.phrase.code_links">Code Links</link></title>
        <para>
          If you want to link to a function, class, member, enum, concept or header
          in the reference section, you can use:
        </para>
<programlisting><!--quickbook-escape-prefix-->[funcref fully::qualified::function_name The link text]
[classref fully::qualified::class_name The link text]
[memberref fully::qualified::member_name The link text]
[enumref fully::qualified::enum_name The link text]
[macroref MACRO_NAME The link text]
[conceptref ConceptName The link text]
[headerref path/to/header.hpp The link text]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Again, the link text is optional. If this is not present, the link text
          will automatically be the function, class, member, enum, macro, concept
          or header. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[classref boost::bar::baz]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          would have &quot;boost::bar::baz&quot; as the link text.
        </para>
      </section>
      <section id="quickbook.syntax.phrase.escape">
        <title><link linkend="quickbook.syntax.phrase.escape">Escape</link></title>
        <para>
          The escape mark-up is used when we don't want to do any processing.
        </para>
<programlisting>'''
escape (no processing/formatting)
'''
</programlisting>
        <para>
          Escaping allows us to pass XML markup to <ulink url="http://www.boost.org/doc/html/boostbook.html">BoostBook</ulink>
          or <ulink url="http://www.docbook.org/">DocBook</ulink>. For example:
        </para>
<programlisting>'''
&lt;emphasis role=&quot;bold&quot;&gt;This is direct XML markup&lt;/emphasis&gt;
'''
</programlisting>
        <para>
          <emphasis role="bold">This is direct XML markup</emphasis>
        </para>
        <important>
          <para>
            Be careful when using the escape. The text must conform to <ulink url="http://www.boost.org/doc/html/boostbook.html">BoostBook</ulink>/<ulink
            url="http://www.docbook.org/">DocBook</ulink> syntax.
          </para>
        </important>
      </section>
      <section id="quickbook.syntax.phrase.single_char_escape">
        <title><link linkend="quickbook.syntax.phrase.single_char_escape">Single
        char escape</link></title>
        <para>
          The backslash may be used to escape a single punctuation character. The
          punctuation immediately after the backslash is passed without any processing.
          This is useful when we need to escape QuickBook punctuations such as <code><phrase
          role="special">[</phrase></code> and <code><phrase role="special">]</phrase></code>.
          For example, how do you escape the triple quote? Simple: <literal>\'\'\'</literal>
        </para>
        <para>
          <code><phrase role="special">\</phrase><phrase role="identifier">n</phrase></code>
          has a special meaning. It is used to generate line breaks.
        </para>
        <warning>
          <para>
            <code><phrase role="special">\</phrase><phrase role="identifier">n</phrase></code>
            and <code><phrase role="special">[</phrase><phrase role="identifier">br</phrase><phrase
            role="special">]</phrase></code> are now deprecated. <link linkend="quickbook.syntax.block.blurbs">Blurbs</link>,
            <link linkend="quickbook.syntax.block.admonitions">Admonitions</link>
            and table cells (see <link linkend="quickbook.syntax.block.tables">Tables</link>)
            may now contain paragraphs.
          </para>
        </warning>
        <para>
          The escaped space: <code><phrase role="special">\</phrase> </code> also
          has a special meaning. The escaped space is removed from the output.
        </para>
      </section>
      <section id="quickbook.syntax.phrase.images">
        <title><link linkend="quickbook.syntax.phrase.images">Images</link></title>
<programlisting><!--quickbook-escape-prefix-->[$image.jpg]
<!--quickbook-escape-postfix--></programlisting>
      </section>
      <section id="quickbook.syntax.phrase.footnotes">
        <title><link linkend="quickbook.syntax.phrase.footnotes">Footnotes</link></title>
        <para>
          As of version 1.3, QuickBook supports footnotes. Just put the text of the
          footnote in a <code><phrase role="special">[</phrase><phrase role="identifier">footnote</phrase><phrase
          role="special">]</phrase></code> block, and the text will be put at the
          bottom of the current page. For example, this:
        </para>
<programlisting><!--quickbook-escape-prefix-->[footnote A sample footnote]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate this<footnote id="quickbook.syntax.phrase.footnotes.f0">
          <para>
            A sample footnote
          </para>
          </footnote>.
        </para>
        <section id="quickbook.syntax.phrase.footnotes.macro_expansion">
          <title><link linkend="quickbook.syntax.phrase.footnotes.macro_expansion">Macro
          Expansion</link></title>
<programlisting><!--quickbook-escape-prefix-->__a_macro_identifier__
<!--quickbook-escape-postfix--></programlisting>
          <para>
            See <link linkend="quickbook.syntax.block.macros">Macros</link> for details.
          </para>
        </section>
        <section id="quickbook.syntax.phrase.footnotes.template_expansion">
          <title><link linkend="quickbook.syntax.phrase.footnotes.template_expansion">Template
          Expansion</link></title>
<programlisting><!--quickbook-escape-prefix-->[a_template_identifier]
<!--quickbook-escape-postfix--></programlisting>
          <para>
            See <link linkend="quickbook.syntax.block.templates">Templates</link>
            for details.
          </para>
        </section>
      </section>
    </section>
    <section id="quickbook.syntax.block">
      <title><link linkend="quickbook.syntax.block">Block Level Elements</link></title>
      <section id="quickbook.syntax.block.document">
        <title><link linkend="quickbook.syntax.block.document">Document</link></title>
        <para>
          Every document must begin with a Document Info section, which should look
          like this:
        </para>
<programlisting><!--quickbook-escape-prefix-->[document-type The Document Title
    [quickbook 1.3]
    [version 1.0]
    [id the_document_name]
    [dirname the_document_dir]
    [copyright 2000 2002 2003 Joe Blow, Jane Doe]
    [purpose The document's reason for being]
    [category The document's category]
    [authors [Blow, Joe], [Doe, Jane]]
    [license The document's license]
    [source-mode source-type]
]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Where document-type is one of:
        </para>
        <itemizedlist>
          <listitem>
            <simpara>
              book
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              article
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              library
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              chapter
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              part
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              appendix
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              preface
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              qandadiv
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              qandaset
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              reference
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              set
            </simpara>
          </listitem>
        </itemizedlist>
        <para>
          quickbook 1.3 declares the version of quickbook the document is written
          for. In its absence, version 1.1 is assumed.
        </para>
        <para>
          <literal>version</literal>, <literal>id</literal>, <literal>dirname</literal>,
          <literal>copyright</literal>, <literal>purpose</literal>, <literal>category</literal>,
          <literal>authors</literal>, <literal>license</literal>, <literal>last-revision</literal>
          and <literal>source-mode</literal> are optional information.
        </para>
        <para>
          <literal>source-type</literal> is a lowercase string setting the initial
          <link linkend="quickbook.syntax.phrase.source_mode">Source Mode</link>.
          If the <literal>source-mode</literal> field is omitted, a default value
          of <literal>c++</literal> will be used.
        </para>
      </section>
      <section id="quickbook.syntax.block.section">
        <title><link linkend="quickbook.syntax.block.section">Section</link></title>
        <para>
          Starting a new section is accomplished with:
        </para>
<programlisting><!--quickbook-escape-prefix-->[section:id The Section Title]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          where <emphasis>id</emphasis> is optional. id will be the filename of the
          generated section. If it is not present, &quot;The Section Title&quot;
          will be normalized and become the id. Valid characters are <literal>a-Z</literal>,
          <literal>A-Z</literal>, <literal>0-9</literal> and <literal>_</literal>.
          All non-valid characters are converted to underscore and all upper-case
          are converted to lower case. Thus: &quot;The Section Title&quot; will be
          normalized to &quot;the_section_title&quot;.
        </para>
        <para>
          End a section with:
        </para>
<programlisting><!--quickbook-escape-prefix-->[endsect]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Sections can nest, and that results in a hierarchy in the table of contents.
        </para>
      </section>
      <section id="quickbook.syntax.block.xinclude">
        <title><link linkend="quickbook.syntax.block.xinclude">xinclude</link></title>
        <para>
          You can include another XML file with:
        </para>
<programlisting><!--quickbook-escape-prefix-->[xinclude file.xml]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          This is useful when file.xml has been generated by Doxygen and contains
          your reference section.
        </para>
      </section>
      <section id="quickbook.syntax.block.paragraphs">
        <title><link linkend="quickbook.syntax.block.paragraphs">Paragraphs</link></title>
        <para>
          Paragraphs start left-flushed and are terminated by two or more newlines.
          No markup is needed for paragraphs. QuickBook automatically detects paragraphs
          from the context. Block markups [section, endsect, h1, h2, h3, h4, h5,
          h6, blurb, (block-quote) ':', pre, def, table and include ] may also terminate
          a paragraph.
        </para>
      </section>
      <section id="quickbook.syntax.block.lists">
        <title><link linkend="quickbook.syntax.block.lists">Lists</link></title>
        <section id="quickbook.syntax.block.lists.ordered_lists">
          <title><link linkend="quickbook.syntax.block.lists.ordered_lists">Ordered
          lists</link></title>
<programlisting># One
# Two
# Three
</programlisting>
          <para>
            will generate:
          </para>
          <orderedlist>
            <listitem>
              <simpara>
                One
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Two
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Three
              </simpara>
            </listitem>
          </orderedlist>
        </section>
        <section id="quickbook.syntax.block.lists.list_hierarchies">
          <title><link linkend="quickbook.syntax.block.lists.list_hierarchies">List
          Hierarchies</link></title>
          <para>
            List hierarchies are supported. Example:
          </para>
<programlisting># One
# Two
# Three
    # Three.a
    # Three.b
    # Three.c
# Four
    # Four.a
        # Four.a.i
        # Four.a.ii
# Five
</programlisting>
          <para>
            will generate:
          </para>
          <orderedlist>
            <listitem>
              <simpara>
                One
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Two
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Three
                <orderedlist>
                  <listitem>
                    <simpara>
                      Three.a
                    </simpara>
                  </listitem>
                  <listitem>
                    <simpara>
                      Three.b
                    </simpara>
                  </listitem>
                  <listitem>
                    <simpara>
                      Three.c
                    </simpara>
                  </listitem>
                </orderedlist>
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Fourth
                <orderedlist>
                  <listitem>
                    <simpara>
                      Four.a
                      <orderedlist>
                        <listitem>
                          <simpara>
                            Four.a.i
                          </simpara>
                        </listitem>
                        <listitem>
                          <simpara>
                            Four.a.ii
                          </simpara>
                        </listitem>
                      </orderedlist>
                    </simpara>
                  </listitem>
                </orderedlist>
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Five
              </simpara>
            </listitem>
          </orderedlist>
        </section>
        <section id="quickbook.syntax.block.lists.long_list_lines">
          <title><link linkend="quickbook.syntax.block.lists.long_list_lines">Long
          List Lines</link></title>
          <para>
            Long lines will be wrapped appropriately. Example:
          </para>
<programlisting># A short item.
# A very long item. A very long item. A very long item.
  A very long item. A very long item. A very long item.
  A very long item. A very long item. A very long item.
  A very long item. A very long item. A very long item.
  A very long item. A very long item. A very long item.
# A short item.
</programlisting>
          <orderedlist>
            <listitem>
              <simpara>
                A short item.
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                A very long item. A very long item. A very long item. A very long
                item. A very long item. A very long item. A very long item. A very
                long item. A very long item. A very long item. A very long item.
                A very long item. A very long item. A very long item. A very long
                item.
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                A short item.
              </simpara>
            </listitem>
          </orderedlist>
        </section>
        <section id="quickbook.syntax.block.lists.unordered_lists">
          <title><link linkend="quickbook.syntax.block.lists.unordered_lists">Unordered
          lists</link></title>
<programlisting><!--quickbook-escape-prefix-->* First
* Second
* Third
<!--quickbook-escape-postfix--></programlisting>
          <para>
            will generate:
          </para>
          <itemizedlist>
            <listitem>
              <simpara>
                First
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Second
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Third
              </simpara>
            </listitem>
          </itemizedlist>
        </section>
        <section id="quickbook.syntax.block.lists.mixed_lists">
          <title><link linkend="quickbook.syntax.block.lists.mixed_lists">Mixed lists</link></title>
          <para>
            Mixed lists (ordered and unordered) are supported. Example:
          </para>
<programlisting><!--quickbook-escape-prefix--># One
# Two
# Three
    * Three.a
    * Three.b
    * Three.c
# Four
<!--quickbook-escape-postfix--></programlisting>
          <para>
            will generate:
          </para>
          <orderedlist>
            <listitem>
              <simpara>
                One
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Two
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Three
                <itemizedlist>
                  <listitem>
                    <simpara>
                      Three.a
                    </simpara>
                  </listitem>
                  <listitem>
                    <simpara>
                      Three.b
                    </simpara>
                  </listitem>
                  <listitem>
                    <simpara>
                      Three.c
                    </simpara>
                  </listitem>
                </itemizedlist>
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                Four
              </simpara>
            </listitem>
          </orderedlist>
          <para>
            And...
          </para>
<programlisting><!--quickbook-escape-prefix--># 1
    * 1.a
        # 1.a.1
        # 1.a.2
    * 1.b
# 2
    * 2.a
    * 2.b
        # 2.b.1
        # 2.b.2
            * 2.b.2.a
            * 2.b.2.b
<!--quickbook-escape-postfix--></programlisting>
          <para>
            will generate:
          </para>
          <orderedlist>
            <listitem>
              <simpara>
                1
                <itemizedlist>
                  <listitem>
                    <simpara>
                      1.a
                      <orderedlist>
                        <listitem>
                          <simpara>
                            1.a.1
                          </simpara>
                        </listitem>
                        <listitem>
                          <simpara>
                            1.a.2
                          </simpara>
                        </listitem>
                      </orderedlist>
                    </simpara>
                  </listitem>
                  <listitem>
                    <simpara>
                      1.b
                    </simpara>
                  </listitem>
                </itemizedlist>
              </simpara>
            </listitem>
            <listitem>
              <simpara>
                2
                <itemizedlist>
                  <listitem>
                    <simpara>
                      2.a
                    </simpara>
                  </listitem>
                  <listitem>
                    <simpara>
                      2.b
                      <orderedlist>
                        <listitem>
                          <simpara>
                            2.b.1
                          </simpara>
                        </listitem>
                        <listitem>
                          <simpara>
                            2.b.2
                            <itemizedlist>
                              <listitem>
                                <simpara>
                                  2.b.2.a
                                </simpara>
                              </listitem>
                              <listitem>
                                <simpara>
                                  2.b.2.b
                                </simpara>
                              </listitem>
                            </itemizedlist>
                          </simpara>
                        </listitem>
                      </orderedlist>
                    </simpara>
                  </listitem>
                </itemizedlist>
              </simpara>
            </listitem>
          </orderedlist>
        </section>
      </section>
      <section id="quickbook.syntax.block.code">
        <title><link linkend="quickbook.syntax.block.code">Code</link></title>
        <para>
          Preformatted code starts with a space or a tab. The code will be syntax
          highlighted according to the current <link linkend="quickbook.syntax.phrase.source_mode">Source
          Mode</link>:
        </para>
<programlisting><phrase role="preprocessor">#include</phrase> <phrase role="special">&lt;</phrase><phrase role="identifier">iostream</phrase><phrase role="special">&gt;</phrase>

<phrase role="keyword">int</phrase> <phrase role="identifier">main</phrase><phrase role="special">()</phrase>
<phrase role="special">{</phrase>
    <phrase role="comment">// Sample code</phrase>
    <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">cout</phrase> <phrase role="special">&lt;&lt;</phrase> <phrase role="string">&quot;Hello, World\n&quot;</phrase><phrase role="special">;</phrase>
    <phrase role="keyword">return</phrase> <phrase role="number">0</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase>
</programlisting>
<programlisting><phrase role="keyword">import</phrase> <phrase role="identifier">cgi</phrase>

<phrase role="keyword">def</phrase> <phrase role="identifier">cookForHtml</phrase><phrase role="special">(</phrase><phrase role="identifier">text</phrase><phrase role="special">):</phrase>
    <phrase role="string">'''&quot;Cooks&quot; the input text for HTML.'''</phrase>

    <phrase role="keyword">return</phrase> <phrase role="identifier">cgi</phrase><phrase role="special">.</phrase><phrase role="identifier">escape</phrase><phrase role="special">(</phrase><phrase role="identifier">text</phrase><phrase role="special">)</phrase>
</programlisting>
        <para>
          Macros that are already defined are expanded in source code. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[def __array__ [@http://www.boost.org/doc/html/array/reference.html array]]
[def __boost__ [@http://www.boost.org/libs/libraries.htm boost]]

    using __boost__::__array__;
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Generates:
        </para>
<programlisting><phrase role="keyword">using</phrase> <ulink url="http://www.boost.org/libs/libraries.htm">boost</ulink><phrase role="special">::</phrase><ulink url="http://www.boost.org/doc/html/array/reference.html">array</ulink><phrase role="special">;</phrase>
</programlisting>
      </section>
      <section id="quickbook.syntax.block.escape_back">
        <title><link linkend="quickbook.syntax.block.escape_back">Escaping Back To
        QuickBook</link></title>
        <para>
          Inside code, code blocks and inline code, QuickBook does not allow any
          markup to avoid conflicts with the target syntax (e.g. c++). In case you
          need to switch back to QuickBook markup inside code, you can do so using
          a language specific <emphasis>escape-back</emphasis> delimiter. In C++
          and Python, the delimiter is the double tick (back-quote): &quot;``&quot;
          and &quot;``&quot;. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->void ``[@http://en.wikipedia.org/wiki/Foo#Foo.2C_Bar_and_Baz foo]``()
{
}
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Will generate:
        </para>
<programlisting><phrase role="keyword">void</phrase> <ulink url="http://en.wikipedia.org/wiki/Foo#Foo.2C_Bar_and_Baz">foo</ulink><phrase role="special">()</phrase>
<phrase role="special">{</phrase>
<phrase role="special">}</phrase>
</programlisting>
        <para>
          When escaping from code to QuickBook, only phrase level markups are allowed.
          Block level markups like lists, tables etc. are not allowed.
        </para>
      </section>
      <section id="quickbook.syntax.block.preformatted">
        <title><link linkend="quickbook.syntax.block.preformatted">Preformatted</link></title>
        <para>
          Sometimes, you don't want some preformatted text to be parsed as C++. In
          such cases, use the <literal>[pre ... ]</literal> markup block.
        </para>
<programlisting><!--quickbook-escape-prefix-->[pre

    Some *preformatted* text                    Some *preformatted* text

        Some *preformatted* text            Some *preformatted* text

            Some *preformatted* text    Some *preformatted* text

]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Spaces, tabs and newlines are rendered as-is. Unlike all quickbook block
          level markup, pre (and Code) are the only ones that allow multiple newlines.
          The markup above will generate:
        </para>
<programlisting>Some <emphasis role="bold">preformatted</emphasis> text                    Some <emphasis role="bold">preformatted</emphasis> text

    Some <emphasis role="bold">preformatted</emphasis> text            Some <emphasis role="bold">preformatted</emphasis> text

        Some <emphasis role="bold">preformatted</emphasis> text    Some <emphasis role="bold">preformatted</emphasis> text

</programlisting>
        <para>
          Notice that unlike Code, phrase markup such as font style is still permitted
          inside <literal>pre</literal> blocks.
        </para>
      </section>
      <section id="quickbook.syntax.block.blockquote">
        <title><link linkend="quickbook.syntax.block.blockquote">Blockquote</link></title>
<programlisting><!--quickbook-escape-prefix-->[:sometext...]<!--quickbook-escape-postfix-->
</programlisting>
        <blockquote>
          <para>
            Indents the paragraph. This applies to one paragraph only.
          </para>
        </blockquote>
      </section>
      <section id="quickbook.syntax.block.admonitions">
        <title><link linkend="quickbook.syntax.block.admonitions">Admonitions</link></title>
<programlisting><!--quickbook-escape-prefix-->[note This is a note]
[tip This is a tip]
[important This is important]
[caution This is a caution]
[warning This is a warning]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          generates <ulink url="http://www.docbook.org/">DocBook</ulink> admonitions:
        </para>
        <note>
          <para>
            This is a note
          </para>
        </note>
        <tip>
          <para>
            This is a tip
          </para>
        </tip>
        <important>
          <para>
            This is important
          </para>
        </important>
        <caution>
          <para>
            This is a caution
          </para>
        </caution>
        <warning>
          <para>
            This is a warning
          </para>
        </warning>
        <para>
          These are the only admonitions supported by <ulink url="http://www.docbook.org/">DocBook</ulink>.
          So, for example <literal>[information This is some information]</literal>
          is unlikely to produce the desired effect.
        </para>
      </section>
      <section id="quickbook.syntax.block.headings">
        <title><link linkend="quickbook.syntax.block.headings">Headings</link></title>
<programlisting><!--quickbook-escape-prefix-->[h1 Heading 1]
[h2 Heading 2]
[h3 Heading 3]
[h4 Heading 4]
[h5 Heading 5]
[h6 Heading 6]
<!--quickbook-escape-postfix--></programlisting>
        <bridgehead renderas="sect1" id="quickbook.syntax.block.headings.h0">
          <phrase id="quickbook.syntax.block.headings.heading_1"/><link linkend="quickbook.syntax.block.headings.heading_1">Heading
          1</link>
        </bridgehead>
        <bridgehead renderas="sect2" id="quickbook.syntax.block.headings.h1">
          <phrase id="quickbook.syntax.block.headings.heading_2"/><link linkend="quickbook.syntax.block.headings.heading_2">Heading
          2</link>
        </bridgehead>
        <bridgehead renderas="sect3" id="quickbook.syntax.block.headings.h2">
          <phrase id="quickbook.syntax.block.headings.heading_3"/><link linkend="quickbook.syntax.block.headings.heading_3">Heading
          3</link>
        </bridgehead>
        <bridgehead renderas="sect4" id="quickbook.syntax.block.headings.h3">
          <phrase id="quickbook.syntax.block.headings.heading_4"/><link linkend="quickbook.syntax.block.headings.heading_4">Heading
          4</link>
        </bridgehead>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.headings.h4">
          <phrase id="quickbook.syntax.block.headings.heading_5"/><link linkend="quickbook.syntax.block.headings.heading_5">Heading
          5</link>
        </bridgehead>
        <bridgehead renderas="sect6" id="quickbook.syntax.block.headings.h5">
          <phrase id="quickbook.syntax.block.headings.heading_6"/><link linkend="quickbook.syntax.block.headings.heading_6">Heading
          6</link>
        </bridgehead>
        <para>
          Headings 1-3 [h1 h2 and h3] will automatically have anchors with normalized
          names with <literal>name=&quot;section_id.normalized_header_text&quot;</literal>
          (i.e. valid characters are <literal>a-z</literal>, <literal>A-Z</literal>,
          <literal>0-9</literal> and <literal>_</literal>. All non-valid characters
          are converted to underscore and all upper-case are converted to lower-case.
          For example: Heading 1 in section Section 2 will be normalized to <literal>section_2.heading_1</literal>).
          You can use:
        </para>
<programlisting><!--quickbook-escape-prefix-->[link section_id.normalized_header_text The link text]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          to link to them. See <link linkend="quickbook.syntax.phrase.anchor_links">Anchor
          links</link> and <link linkend="quickbook.syntax.block.section">Section</link>
          for more info.
        </para>
      </section>
      <section id="quickbook.syntax.block.generic_heading">
        <title><link linkend="quickbook.syntax.block.generic_heading">Generic Heading</link></title>
        <para>
          In cases when you don't want to care about the heading level (1 to 6),
          you can use the <emphasis>Generic Heading</emphasis>:
        </para>
<programlisting><!--quickbook-escape-prefix-->[heading Heading]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          The <emphasis>Generic Heading</emphasis> assumes the level, plus one, of
          the innermost section where it is placed. For example, if it is placed
          in the outermost section, then, it assumes <emphasis>h2</emphasis>.
        </para>
        <para>
          Headings are often used as an alternative to sections. It is used particularly
          if you do not want to start a new section. In many cases, however, headings
          in a particular section is just flat. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[section A]
[h2 X]
[h2 Y]
[h2 Z]
[endsect]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Here we use h2 assuming that section A is the outermost level. If it is
          placed in an inner level, you'll have to use h3, h4, etc. depending on
          where the section is. In general, it is the section level plus one. It
          is rather tedious, however, to scan the section level everytime. If you
          rewrite the example above as shown below, this will be automatic:
        </para>
<programlisting><!--quickbook-escape-prefix-->[section A]
[heading X]
[heading Y]
[heading Z]
[endsect]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          They work well regardless where you place them. You can rearrange sections
          at will without any extra work to ensure correct heading levels. In fact,
          with <emphasis>section</emphasis> and <emphasis>heading</emphasis>, you
          have all you need. <emphasis>h1</emphasis>..<emphasis>h6</emphasis> becomes
          redundant. <emphasis>h1</emphasis>..<emphasis>h6</emphasis> might be deprecated
          in the future.
        </para>
      </section>
      <section id="quickbook.syntax.block.macros">
        <title><link linkend="quickbook.syntax.block.macros">Macros</link></title>
<programlisting><!--quickbook-escape-prefix-->[def macro_identifier some text]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          When a macro is defined, the identifier replaces the text anywhere in the
          file, in paragraphs, in markups, etc. macro_identifier is a string of non-
          white space characters except ']'. A macro may not follow an alphabetic
          character or the underscore. The replacement text can be any phrase (even
          marked up). Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[def sf_logo [$http://sourceforge.net/sflogo.php?group_id=28447&amp;type=1]]
sf_logo
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Now everywhere the sf_logo is placed, the picture will be inlined.
        </para>
        <para>
          <inlinemediaobject><imageobject><imagedata fileref="http://sourceforge.net/sflogo.php?group_id=28447&amp;type=1"></imagedata></imageobject>
          <textobject>
            <phrase>sflogo</phrase>
          </textobject>
          </inlinemediaobject>
        </para>
        <tip>
          <para>
            It's a good idea to use macro identifiers that are distinguishable. For
            instance, in this document, macro identifiers have two leading and trailing
            underscores (e.g. <literal>__spirit__</literal>). The reason is to avoid unwanted
            macro replacement.
          </para>
        </tip>
        <para>
          Links (URLS) and images are good candidates for macros. <emphasis role="bold">1</emphasis>)
          They tend to change a lot. It is a good idea to place all links and images
          in one place near the top to make it easy to make changes. <emphasis role="bold">2</emphasis>)
          The syntax is not pretty. It's easier to read and write, e.g. <literal>__spirit__</literal>
          than <literal>[@http://spirit.sourceforge.net Spirit]</literal>.
        </para>
        <para>
          Some more examples:
        </para>
<programlisting><!--quickbook-escape-prefix-->[def :-)            [$theme/smiley.png]]
[def __spirit__     [@http://spirit.sourceforge.net Spirit]]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          (See <link linkend="quickbook.syntax.phrase.images">Images</link> and
          <link linkend="quickbook.syntax.phrase.links">Links</link>)
        </para>
        <para>
          Invoking these macros:
        </para>
<programlisting><!--quickbook-escape-prefix-->Hi __spirit__  :-)
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate this:
        </para>
        <para>
          Hi <ulink url="http://spirit.sourceforge.net">Spirit</ulink> <inlinemediaobject><imageobject><imagedata
          fileref="images/smiley.png"></imagedata></imageobject>
          <textobject>
            <phrase>smiley</phrase>
          </textobject>
          </inlinemediaobject>
        </para>
      </section>
      <section id="quickbook.syntax.block.predefined_macros">
        <title><link linkend="quickbook.syntax.block.predefined_macros">Predefined
        Macros</link></title>
        <para>
          Quickbook has some predefined macros that you can already use.
        </para>
        <table frame="all" id="quickbook.syntax.block.predefined_macros.t0">
          <title>Predefined Macros</title>
          <tgroup cols="3">
            <thead>
              <row>
                <entry>
                  <para>
                    Macro
                  </para>
                </entry>
                <entry>
                  <para>
                    Meaning
                  </para>
                </entry>
                <entry>
                  <para>
                    Example
                  </para>
                </entry>
              </row>
            </thead>
            <tbody>
              <row>
                <entry>
                  <para>
                    __DATE__
                  </para>
                </entry>
                <entry>
                  <para>
                    Today's date
                  </para>
                </entry>
                <entry>
                  <para>
                    2000-Dec-20
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    __TIME__
                  </para>
                </entry>
                <entry>
                  <para>
                    The current time
                  </para>
                </entry>
                <entry>
                  <para>
                    12:00:00 PM
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    __FILENAME__
                  </para>
                </entry>
                <entry>
                  <para>
                    Quickbook source filename
                  </para>
                </entry>
                <entry>
                  <para>
                    quickbook_manual-1_4.quickbook
                  </para>
                </entry>
              </row>
            </tbody>
          </tgroup>
        </table>
      </section>
      <section id="quickbook.syntax.block.templates">
        <title><link linkend="quickbook.syntax.block.templates">Templates</link></title>
        <para>
          Templates provide a more versatile text substitution mechanism. Templates
          come in handy when you need to create parameterizable, multi-line, boilerplate
          text that you specify once and expand many times. Templates accept one
          or more arguments. These arguments act like place-holders for text replacement.
          Unlike simple macros, which are limited to phrase level markup, templates
          can contain block level markup (e.g. paragraphs, code blocks and tables).
        </para>
        <para>
          Example template:
        </para>
<programlisting><!--quickbook-escape-prefix-->[template person[name age what]

Hi, my name is [name]. I am [age] years old. I am a [what].

]
<!--quickbook-escape-postfix--></programlisting>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.templates.h0">
          <phrase id="quickbook.syntax.block.templates.template_identifier"/><link
          linkend="quickbook.syntax.block.templates.template_identifier">Template
          Identifier</link>
        </bridgehead>
        <para>
          Template identifiers can either consist of:
        </para>
        <itemizedlist>
          <listitem>
            <simpara>
              An initial alphabetic character or the underscore, followed by zero
              or more alphanumeric characters or the underscore. This is similar
              to your typical C/C++ identifier.
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              A single character punctuation (a non-alphanumeric printable character)
            </simpara>
          </listitem>
        </itemizedlist>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.templates.h1">
          <phrase id="quickbook.syntax.block.templates.formal_template_arguments"/><link
          linkend="quickbook.syntax.block.templates.formal_template_arguments">Formal
          Template Arguments</link>
        </bridgehead>
        <para>
          Template formal arguments are identifiers consisting of an initial alphabetic
          character or the underscore, followed by zero or more alphanumeric characters
          or the underscore. This is similar to your typical C/C++ identifier.
        </para>
        <para>
          A template formal argument temporarily hides a template of the same name
          at the point where the <link linkend="quickbook.syntax.block.templates.template_expansion">template
          is expanded</link>. Note that the body of the <literal>person</literal>
          template above refers to <literal>name</literal> <literal>age</literal>
          and <literal>what</literal> as <literal>[name]</literal> <literal>[age]</literal>
          and <literal>[what]</literal>. <literal>name</literal> <literal>age</literal>
          and <literal>what</literal> are actually templates that exist in the duration
          of the template call.
        </para>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.templates.h2">
          <phrase id="quickbook.syntax.block.templates.template_body"/><link linkend="quickbook.syntax.block.templates.template_body">Template
          Body</link>
        </bridgehead>
        <para>
          The template body can be just about any QuickBook block or phrase. There
          are actually two forms. Templates may be phrase or block level. Phrase
          templates are of the form:
        </para>
<programlisting><!--quickbook-escape-prefix-->[template sample[arg1 arg2...argN] replacement text... ]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Block templates are of the form:
        </para>
<programlisting><!--quickbook-escape-prefix-->[template sample[arg1 arg2...argN]
replacement text...
]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          The basic rule is as follows: if a newline immediately follows the argument
          list, then it is a block template, otherwise, it is a phrase template.
          Phrase templates are typically expanded as part of phrases. Like macros,
          block level elements are not allowed in phrase templates.
        </para>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.templates.h3">
          <phrase id="quickbook.syntax.block.templates.template_expansion"/><link
          linkend="quickbook.syntax.block.templates.template_expansion">Template
          Expansion</link>
        </bridgehead>
        <para>
          You expand a template this way:
        </para>
<programlisting><!--quickbook-escape-prefix-->[template_identifier arg1..arg2..arg3]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          At template expansion, you supply the actual arguments. The template will
          be expanded with your supplied arguments. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[person James Bond..39..Spy]
[person Santa Clause..87..Big Red Fatso]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Which will expand to:
        </para>
        <para>
          Hi, my name is James Bond. I am 39 years old. I am a Spy.
        </para>
        <para>
          Hi, my name is Santa Clause. I am 87 years old. I am a Big Red Fatso.
        </para>
        <caution>
          <para>
            A word of caution: Templates are recursive. A template can call another
            template or even itself, directly or indirectly. There are no control
            structures in QuickBook (yet) so this will always mean infinite recursion.
            QuickBook can detect this situation and report an error if recursion
            exceeds a certain limit.
          </para>
        </caution>
        <para>
          Each actual argument can be a word, a text fragment or just about any
          <link linkend="quickbook.syntax.phrase">QuickBook phrase</link>. Arguments
          are separated by the double dot <literal>&quot;..&quot;</literal> and terminated
          by the close parenthesis.
        </para>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.templates.h4">
          <phrase id="quickbook.syntax.block.templates.nullary_templates"/><link
          linkend="quickbook.syntax.block.templates.nullary_templates">Nullary Templates</link>
        </bridgehead>
        <para>
          Nullary templates look and act like simple macros. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[template alpha[]&apos;&apos;&apos;&amp;#945;&apos;&apos;&apos;]
[template beta[]&apos;&apos;&apos;&amp;#946;&apos;&apos;&apos;]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Expanding:
        </para>
<programlisting><!--quickbook-escape-prefix-->Some squigles...[*[alpha][beta]]<!--quickbook-escape-postfix--></programlisting>
        <para>
          We have:
        </para>
        <para>
          Some squiggles...<emphasis role="bold">&#945;&#946;</emphasis>
        </para>
        <para>
          The difference with macros are
        </para>
        <itemizedlist>
          <listitem>
            <simpara>
              The explicit <link linkend="quickbook.syntax.block.templates.template_expansion">template
              expansion syntax</link>. This is an advantage because, now, we don't
              have to use obscure naming conventions like double underscores (e.g.
              __alpha__) to avoid unwanted macro replacement.
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              The template is expanded at the point where it is invoked. A macro
              is expanded immediately at its point of declaration. This is subtle
              and can cause a slight difference in behavior especially if you refer
              to other macros and templates in the body.
            </simpara>
          </listitem>
        </itemizedlist>
        <para>
          The empty brackets after the template identifier (<literal>alpha[]</literal>)
          indicates no arguments. If the template body does not look like a template
          argument list, we can elide the empty brackets. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[template aristotle_quote Aristotle: [*['Education is the best provision
for the journey to old age.]]]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Expanding:
        </para>
<programlisting><!--quickbook-escape-prefix-->Here's a quote from [aristotle_quote].
<!--quickbook-escape-postfix--></programlisting>
        <para>
          We have:
        </para>
        <para>
          Here's a quote from Aristotle: <emphasis role="bold"><emphasis>Education
          is the best provision for the journey to old age.</emphasis></emphasis>.
        </para>
        <para>
          The disadvantage is that you can't avoid the space between the template
          identifier, <code><phrase role="identifier">aristotle_quote</phrase></code>,
          and the template body &quot;Aristotle...&quot;. This space will be part
          of the template body. If that space is unwanted, use empty brackets or
          use the space escape: &quot;<code><phrase role="special">\</phrase> </code>&quot;.
          Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[template tag\ _tag]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Then expanding:
        </para>
<programlisting><!--quickbook-escape-prefix-->`struct` x[tag];
<!--quickbook-escape-postfix--></programlisting>
        <para>
          We have:
        </para>
        <para>
          <code><phrase role="keyword">struct</phrase></code> x_tag;
        </para>
        <para>
          You have a couple of ways to do it. I personally prefer the explicit empty
          brackets, though.
        </para>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.templates.h5">
          <phrase id="quickbook.syntax.block.templates.simple_arguments"/><link linkend="quickbook.syntax.block.templates.simple_arguments">Simple
          Arguments</link>
        </bridgehead>
        <para>
          As mentioned, arguments are separated by the double dot <literal>&quot;..&quot;</literal>.
          If there are less arguments passed than expected, QuickBook attempts to
          break the last argument into two or more arguments following this logic:
        </para>
        <itemizedlist>
          <listitem>
            <simpara>
              Break the last argument into two, at the first space found (<literal>'',
              '\n', \t' or '\r'</literal>).
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              Repeat until there are enough arguments or if there are no more spaces
              found (in which case, an error is reported).
            </simpara>
          </listitem>
        </itemizedlist>
        <para>
          For example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[template simple[a b c d] [a][b][c][d]]
[simple w x y z]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will produce:
        </para>
        <para>
          wxyz
        </para>
        <para>
          &quot;w x y z&quot; is initially treated as a single argument because we
          didn't supply any <literal>&quot;..&quot;</literal> separators. However,
          since <literal>simple</literal> expects 4 arguments, &quot;w x y z&quot;
          is broken down iteratively (applying the logic above) until we have &quot;w&quot;,
          &quot;x&quot;, &quot;y&quot; and &quot;z&quot;.
        </para>
        <para>
          QuickBook only tries to get the arguments it needs. For example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[simple w x y z trail]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will produce:
        </para>
        <para>
          wxyz trail
        </para>
        <para>
          The arguments being: &quot;w&quot;, &quot;x&quot;, &quot;y&quot; and &quot;z
          trail&quot;.
        </para>
        <para>
          It should be obvious now that for simple arguments with no spaces, we can
          get by without separating the arguments with <literal>&quot;..&quot;</literal>
          separators. It is possible to combine <literal>&quot;..&quot;</literal>
          separators with the argument passing simplification presented above. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[simple what do you think ..m a n?]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will produce:
        </para>
        <para>
          what do you think man?
        </para>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.templates.h6">
          <phrase id="quickbook.syntax.block.templates.punctuation_templates"/><link
          linkend="quickbook.syntax.block.templates.punctuation_templates">Punctuation
          Templates</link>
        </bridgehead>
        <para>
          With templates, one of our objectives is to allow us to rewrite QuickBook
          in QuickBook (as a qbk library). For that to happen, we need to accommodate
          single character punctuation templates which are fairly common in QuickBook.
          You might have noticed that single character punctuations are allowed as
          <link linkend="quickbook.syntax.block.templates.template_identifier">template
          identifiers</link>. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[template ![bar] <!--quickbook-escape-postfix-->&lt;hey&gt;<!--quickbook-escape-prefix-->[bar]<!--quickbook-escape-postfix-->&lt;/hey&gt;<!--quickbook-escape-prefix-->]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          Now, expanding this:
        </para>
<programlisting><!--quickbook-escape-prefix-->[!baz]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          We will have:
        </para>
<programlisting>&lt;hey&gt;baz&lt;/hey&gt;
</programlisting>
      </section>
      <section id="quickbook.syntax.block.blurbs">
        <title><link linkend="quickbook.syntax.block.blurbs">Blurbs</link></title>
<programlisting><!--quickbook-escape-prefix-->[blurb :-) [*An eye catching advertisement or note...]

    __spirit__ is an object-oriented recursive-descent parser generator framework
    implemented using template meta-programming techniques. Expression templates
    allow us to approximate the syntax of Extended Backus-Normal Form (EBNF)
    completely in C++.
]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate this:
        </para>
        <sidebar role="blurb">
        <para>
          <inlinemediaobject><imageobject><imagedata fileref="images/smiley.png"></imagedata></imageobject>
          <textobject>
            <phrase>smiley</phrase>
          </textobject>
          </inlinemediaobject> <emphasis role="bold">An eye catching advertisement
          or note...</emphasis>
        </para>
        <para>
          <ulink url="http://spirit.sourceforge.net">Spirit</ulink> is an object-oriented
          recursive-descent parser generator framework implemented using template
          meta-programming techniques. Expression templates allow us to approximate
          the syntax of Extended Backus-Normal Form (EBNF) completely in C++.
        </para>
        </sidebar>
        <note>
          <para>
            Prefer <link linkend="quickbook.syntax.block.admonitions">admonitions</link>
            wherever appropriate.
          </para>
        </note>
      </section>
      <section id="quickbook.syntax.block.tables">
        <title><link linkend="quickbook.syntax.block.tables">Tables</link></title>
<programlisting><!--quickbook-escape-prefix-->[table A Simple Table
    [[Heading 1] [Heading 2] [Heading 3]]
    [[R0-C0]     [R0-C1]     [R0-C2]]
    [[R1-C0]     [R1-C1]     [R1-C2]]
    [[R2-C0]     [R2-C1]     [R2-C2]]
]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <table frame="all" id="quickbook.syntax.block.tables.t0">
          <title>A Simple Table</title>
          <tgroup cols="3">
            <thead>
              <row>
                <entry>
                  <para>
                    Heading 1
                  </para>
                </entry>
                <entry>
                  <para>
                    Heading 2
                  </para>
                </entry>
                <entry>
                  <para>
                    Heading 3
                  </para>
                </entry>
              </row>
            </thead>
            <tbody>
              <row>
                <entry>
                  <para>
                    R0-C0
                  </para>
                </entry>
                <entry>
                  <para>
                    R0-C1
                  </para>
                </entry>
                <entry>
                  <para>
                    R0-C2
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    R2-C0
                  </para>
                </entry>
                <entry>
                  <para>
                    R2-C1
                  </para>
                </entry>
                <entry>
                  <para>
                    R2-C2
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    R3-C0
                  </para>
                </entry>
                <entry>
                  <para>
                    R3-C1
                  </para>
                </entry>
                <entry>
                  <para>
                    R3-C2
                  </para>
                </entry>
              </row>
            </tbody>
          </tgroup>
        </table>
        <para>
          The table title is optional. The first row of the table is automatically
          treated as the table header; that is, it is wrapped in <literal>&lt;thead&gt;...&lt;/thead&gt;</literal>
          XML tags. Note that unlike the original QuickDoc, the columns are nested
          in [ cells... ]. The syntax is free-format and allows big cells to be formatted
          nicely. Example:
        </para>
<programlisting><!--quickbook-escape-prefix-->[table Table with fat cells
    [[Heading 1] [Heading 2]]
    [
        [Row 0, Col 0: a small cell]
        [
            Row 0, Col 1: a big fat cell with paragraphs

            Boost provides free peer-reviewed portable C++ source libraries.

            We emphasize libraries that work well with the C++ Standard Library.
            Boost libraries are intended to be widely useful, and usable across
            a broad spectrum of applications. The Boost license encourages both
            commercial and non-commercial use.
        ]
    ]
    [
        [Row 1, Col 0: a small cell]
        [Row 1, Col 1: a small cell]
    ]
]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          and thus:
        </para>
        <table frame="all" id="quickbook.syntax.block.tables.t1">
          <title>Table with fat cells</title>
          <tgroup cols="2">
            <thead>
              <row>
                <entry>
                  <para>
                    Heading 1
                  </para>
                </entry>
                <entry>
                  <para>
                    Heading 2
                  </para>
                </entry>
              </row>
            </thead>
            <tbody>
              <row>
                <entry>
                  <para>
                    Row 0, Col 0: a small cell
                  </para>
                </entry>
                <entry>
                  <para>
                    Row 0, Col 1: a big fat cell with paragraphs
                  </para>
                  <para>
                    Boost provides free peer-reviewed portable C++ source libraries.
                  </para>
                  <para>
                    We emphasize libraries that work well with the C++ Standard Library.
                    Boost libraries are intended to be widely useful, and usable
                    across a broad spectrum of applications. The Boost license encourages
                    both commercial and non-commercial use.
                  </para>
                </entry>
              </row>
              <row>
                <entry>
                  <para>
                    Row 1, Col 0: a small cell
                  </para>
                </entry>
                <entry>
                  <para>
                    Row 1, Col 1: a small cell
                  </para>
                </entry>
              </row>
            </tbody>
          </tgroup>
        </table>
        <para>
          Here's how to have preformatted blocks of code in a table cell:
        </para>
<programlisting><!--quickbook-escape-prefix-->[table Table with code
    [[Comment] [Code]]
    [
        [My first program]
        [<!--quickbook-escape-postfix-->``
            #include &lt;iostream&gt;

            int main()
            {
                std::cout &lt;&lt; &quot;Hello, World!&quot; &lt;&lt; std::endl;
                return 0;
            }
        ``<!--quickbook-escape-prefix-->]
    ]
]
<!--quickbook-escape-postfix--></programlisting>
        <table frame="all" id="quickbook.syntax.block.tables.t2">
          <title>Table with code</title>
          <tgroup cols="2">
            <thead>
              <row>
                <entry>
                  <para>
                    Comment
                  </para>
                </entry>
                <entry>
                  <para>
                    Code
                  </para>
                </entry>
              </row>
            </thead>
            <tbody>
              <row>
                <entry>
                  <para>
                    My first program
                  </para>
                </entry>
                <entry>
                  <para>
<programlisting><phrase role="preprocessor">#include</phrase> <phrase role="special">&lt;</phrase><phrase role="identifier">iostream</phrase><phrase role="special">&gt;</phrase>

<phrase role="keyword">int</phrase> <phrase role="identifier">main</phrase><phrase role="special">()</phrase>
<phrase role="special">{</phrase>
    <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">cout</phrase> <phrase role="special">&lt;&lt;</phrase> <phrase role="string">&quot;Hello, World!&quot;</phrase> <phrase role="special">&lt;&lt;</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">endl</phrase><phrase role="special">;</phrase>
    <phrase role="keyword">return</phrase> <phrase role="number">0</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase>
</programlisting>
                  </para>
                </entry>
              </row>
            </tbody>
          </tgroup>
        </table>
      </section>
      <section id="quickbook.syntax.block.variable_lists">
        <title><link linkend="quickbook.syntax.block.variable_lists">Variable Lists</link></title>
<programlisting><!--quickbook-escape-prefix-->[variablelist A Variable List
    [[term 1] [The definition of term 1]]
    [[term 2] [The definition of term 2]]
    [[term 3] [The definition of term 3]]
]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          will generate:
        </para>
        <variablelist>
          <title>A Variable List</title>
          <varlistentry>
            <term>term 1</term>
            <listitem>
              <para>
                The definition of term 1
              </para>
            </listitem>
          </varlistentry>
          <varlistentry>
            <term>term 2</term>
            <listitem>
              <para>
                The definition of term 2
              </para>
            </listitem>
          </varlistentry>
          <varlistentry>
            <term>term 3</term>
            <listitem>
              <para>
                The definition of term 3
              </para>
            </listitem>
          </varlistentry>
        </variablelist>
        <para>
          The rules for variable lists are the same as for tables, except that only
          2 &quot;columns&quot; are allowed. The first column contains the terms,
          and the second column contains the definitions. Those familiar with HTML
          will recognize this as a &quot;definition list&quot;.
        </para>
      </section>
      <section id="quickbook.syntax.block.include">
        <title><link linkend="quickbook.syntax.block.include">Include</link></title>
        <para>
          You can include one QuickBook file from another. The syntax is simply:
        </para>
<programlisting><!--quickbook-escape-prefix-->[include someother.qbk]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          The included file will be processed as if it had been cut and pasted into
          the current document, with the following exceptions:
        </para>
        <itemizedlist>
          <listitem>
            <simpara>
              The __FILENAME__ predefined macro will reflect the name of the file currently being
              processed.
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              Any macros defined in the included file are scoped to that file.
            </simpara>
          </listitem>
        </itemizedlist>
        <para>
          The <literal>[include]</literal> directive lets you specify a document
          id to use for the included file. When this id is not explicitly specified,
          the id defaults to the filename (&quot;someother&quot;, in the example
          above). You can specify the id like this:
        </para>
<programlisting><!--quickbook-escape-prefix-->[include:someid someother.qbk]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          All auto-generated anchors will use the document id as a unique prefix.
          So for instance, if there is a top section in someother.qbk named &quot;Intro&quot;,
          the named anchor for that section will be &quot;someid.intro&quot;, and
          you can link to it with <literal>[link someid.intro The Intro]</literal>.
        </para>
      </section>
      <section id="quickbook.syntax.block.import">
        <title><link linkend="quickbook.syntax.block.import">Import</link></title>
        <para>
          When documenting code, you'd surely need to present code from actual source
          files. While it is possible to copy some code and paste them in your QuickBook
          file, doing so is error prone and the extracted code in the documentation
          tends to get out of sync with the actual code as the code evolves. The
          problem, as always, is that once documentation is written, the tendency
          is for the docs to languish in the archives without maintenance.
        </para>
        <para>
          QuickBook's import facility provides a nice solution.
        </para>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.import.h0">
          <phrase id="quickbook.syntax.block.import.example"/><link linkend="quickbook.syntax.block.import.example">Example</link>
        </bridgehead>
        <para>
          You can effortlessly import code snippets from source code into your QuickBook.
          The following illustrates how this is done:
        </para>
<programlisting><!--quickbook-escape-prefix-->[import ../test/stub.cpp]
[foo]
[bar]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          The first line:
        </para>
<programlisting><!--quickbook-escape-prefix-->[import ../test/stub.cpp]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          collects specially marked-up code snippets from <ulink url="../../test/stub.cpp">stub.cpp</ulink>
          and places them in your QuickBook file as virtual templates. Each of the
          specially marked-up code snippets has a name (e.g. <code><phrase role="identifier">foo</phrase></code>
          and <code><phrase role="identifier">bar</phrase></code> in the example
          above). This shall be the template identifier for that particular code
          snippet. The second and third line above does the actual template expansion:
        </para>
<programlisting><!--quickbook-escape-prefix-->[foo]
[bar]
<!--quickbook-escape-postfix--></programlisting>
        <para>
          And the result is:
        </para>
        <para>
          This is the <emphasis role="bold"><emphasis>foo</emphasis></emphasis> function.
        </para>
        <para>
          This description can have paragraphs...
        </para>
        <itemizedlist>
          <listitem>
            <simpara>
              lists
            </simpara>
          </listitem>
          <listitem>
            <simpara>
              etc.
            </simpara>
          </listitem>
        </itemizedlist>
        <para>
          And any quickbook block markup.
        </para>
        <para>
<programlisting><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">string</phrase> <phrase role="identifier">foo</phrase><phrase role="special">()</phrase>
<phrase role="special">{</phrase>
    <phrase role="comment">// return 'em, foo man!</phrase>
    <phrase role="keyword">return</phrase> <phrase role="string">&quot;foo&quot;</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase>
</programlisting>
        </para>
        <para>
          This is the <emphasis role="bold"><emphasis>bar</emphasis></emphasis> function
        </para>
        <para>
<programlisting><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">string</phrase> <phrase role="identifier">bar</phrase><phrase role="special">()</phrase>
<phrase role="special">{</phrase>
    <phrase role="comment">// return 'em, bar man!</phrase>
    <phrase role="keyword">return</phrase> <phrase role="string">&quot;bar&quot;</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase>
</programlisting>
        </para>
        <para>
          Some trailing text here
        </para>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.import.h1">
          <phrase id="quickbook.syntax.block.import.code_snippet_markup"/><link linkend="quickbook.syntax.block.import.code_snippet_markup">Code
          Snippet Markup</link>
        </bridgehead>
        <para>
          Note how the code snippets in <ulink url="../../test/stub.cpp">stub.cpp</ulink>
          get marked up. We use distinguishable comments following the form:
        </para>
<programlisting><phrase role="comment">//[id</phrase>
<phrase role="identifier">some</phrase> <phrase role="identifier">code</phrase> <phrase role="identifier">here</phrase>
<phrase role="comment">//]</phrase>
</programlisting>
        <para>
          The first comment line above initiates a named code-snippet. This prefix
          will not be visible in quickbook. The entire code-snippet in between <code><phrase
          role="comment">//[id</phrase></code> and <code><phrase role="comment">//]</phrase></code>
          will be inserted as a template in quickbook with name <emphasis><emphasis>id</emphasis></emphasis>.
          The comment <code><phrase role="comment">//]</phrase></code> ends a code-snippet
          This too will not be visible in quickbook.
        </para>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.import.h2">
          <phrase id="quickbook.syntax.block.import.special_comments"/><link linkend="quickbook.syntax.block.import.special_comments">Special
          Comments</link>
        </bridgehead>
        <para>
          Special comments of the form:
        </para>
<programlisting><phrase role="comment">//` some [*quickbook] markup here</phrase>
</programlisting>
        <para>
          and:
        </para>
<programlisting><phrase role="comment">/*` some [*quickbook] markup here */</phrase>
</programlisting>
        <para>
          will be parsed by QuickBook. This can contain quickbook <emphasis>blocks</emphasis>
          (e.g. sections, paragraphs, tables, etc). In the first case, the initial
          slash-slash, tick and white-space shall be ignored. In the second, the
          initial slash-star-tick and the final star-slash shall be ignored.
        </para>
        <bridgehead renderas="sect5" id="quickbook.syntax.block.import.h3">
          <phrase id="quickbook.syntax.block.import.callouts"/><link linkend="quickbook.syntax.block.import.callouts">Callouts</link>
        </bridgehead>
        <para>
          Special comments of the form:
        </para>
<programlisting><phrase role="comment">/*&lt; some [*quickbook] markup here &gt;*/</phrase>
</programlisting>
        <para>
          will be regarded as callouts. These will be collected, numbered and rendered
          as a &quot;callout bug&quot; (a small icon with a number). After the whole
          snippet is parsed, the callout list is generated. See <ulink url="http://www.docbook.org/tdg/en/html/callout.html">Callouts</ulink>
          for details. Example:
        </para>
        <para>
<programlisting><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">string</phrase> <phrase role="identifier">foo_bar</phrase><phrase role="special">()</phrase> <co id="quickbook.syntax.block.import.c0" linkends="quickbook.syntax.block.import.c1" />
<phrase role="special">{</phrase>
    <phrase role="keyword">return</phrase> <phrase role="string">&quot;foo-bar&quot;</phrase><phrase role="special">;</phrase> <co id="quickbook.syntax.block.import.c2" linkends="quickbook.syntax.block.import.c3" />
<phrase role="special">}</phrase>
</programlisting>
        </para>
        <calloutlist>
          <callout arearefs="quickbook.syntax.block.import.c0" id="quickbook.syntax.block.import.c1">
            <para>
              The <emphasis>Mythical</emphasis> FooBar. See <ulink url="http://en.wikipedia.org/wiki/Foobar">Foobar
              for details</ulink>
            </para>
          </callout>
          <callout arearefs="quickbook.syntax.block.import.c2" id="quickbook.syntax.block.import.c3">
            <para>
              return 'em, foo-bar man!
            </para>
          </callout>
        </calloutlist>
        <para>
          Checkout <ulink url="../../test/stub.cpp">stub.cpp</ulink> to see the actual
          code.
        </para>
      </section>
    </section>
  </section>
  <section id="quickbook.install">
    <title><link linkend="quickbook.install">Installation and configuration</link></title>
    <para>
      This section provides some guidelines on how to install and configure BoostBook
      and Quickbook under several operating systems.
    </para>
    <para>
      Before continuing, it is very important that you keep this in mind: if you
      try to build some documents and the process breaks due to misconfiguration,
      be absolutely sure to delete any <code><phrase role="identifier">bin</phrase></code>
      and <code><phrase role="identifier">bin</phrase><phrase role="special">.</phrase><phrase
      role="identifier">v2</phrase></code> directories generated by the build before
      trying again. Otherwise your configuration fixes will not take any effect.
    </para>
    <section id="quickbook.install.windows">
      <title><link linkend="quickbook.install.windows">Windows 2000, XP, 2003, Vista</link></title>
      <blockquote>
        <para>
          <emphasis>Section contributed by Julio M. Merino Vidal</emphasis>
        </para>
      </blockquote>
      <para>
        The following instructions apply to any Windows system based on Windows 2000,
        including Windows XP, Windows 2003 Server and Windows Vista. The paths shown
        below are taken from a Windows Vista machine; you will need to adjust them
        to match your system in case you are running an older version.
      </para>
      <orderedlist>
        <listitem>
          <simpara>
            First of all you need to have a copy of <code><phrase role="identifier">xsltproc</phrase></code>
            for Windows. There are many ways to get this tool, but to keep things
            simple, use the <ulink url="http://www.zlatkovic.com/pub/libxml/">binary
            packages</ulink> made by Igor Zlatkovic. At the very least, you need
            to download the following packages: <code><phrase role="identifier">iconv</phrase></code>,
            <code><phrase role="identifier">zlib</phrase></code>, <code><phrase role="identifier">libxml2</phrase></code>
            and <code><phrase role="identifier">libxslt</phrase></code>.
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Unpack all these packages in the same directory so that you get unique
            <code><phrase role="identifier">bin</phrase></code>, <code><phrase role="identifier">include</phrase></code>
            and <code><phrase role="identifier">lib</phrase></code> directories within
            the hierarchy. These instructions use <code><phrase role="identifier">C</phrase><phrase
            role="special">:\</phrase><phrase role="identifier">Users</phrase><phrase
            role="special">\</phrase><phrase role="identifier">example</phrase><phrase
            role="special">\</phrase><phrase role="identifier">Documents</phrase><phrase
            role="special">\</phrase><phrase role="identifier">boost</phrase><phrase
            role="special">\</phrase><phrase role="identifier">xml</phrase></code>
            as the root for all files.
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            From the command line, go to the <code><phrase role="identifier">bin</phrase></code>
            directory and launch <code><phrase role="identifier">xsltproc</phrase><phrase
            role="special">.</phrase><phrase role="identifier">exe</phrase></code>
            to ensure it works. You should get usage information on screen.
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Download <ulink url="http://www.docbook.org/xml/4.2/docbook-xml-4.2.zip">Docbook
            XML 4.2</ulink> and unpack it in the same directory used above. That
            is: <code><phrase role="identifier">C</phrase><phrase role="special">:\</phrase><phrase
            role="identifier">Users</phrase><phrase role="special">\</phrase><phrase
            role="identifier">example</phrase><phrase role="special">\</phrase><phrase
            role="identifier">Documents</phrase><phrase role="special">\</phrase><phrase
            role="identifier">boost</phrase><phrase role="special">\</phrase><phrase
            role="identifier">xml</phrase><phrase role="special">\</phrase><phrase
            role="identifier">docbook</phrase><phrase role="special">-</phrase><phrase
            role="identifier">xml</phrase></code>.
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Download the latest <ulink url="http://sourceforge.net/project/showfiles.php?group_id=21935&amp;package_id=16608">Docbook
            XSL</ulink> version and unpack it, again in the same directory used before.
            To make things easier, rename the directory created during the extraction
            to <code><phrase role="identifier">docbook</phrase><phrase role="special">-</phrase><phrase
            role="identifier">xsl</phrase></code> (bypassing the version name):
            <code><phrase role="identifier">C</phrase><phrase role="special">:\</phrase><phrase
            role="identifier">Users</phrase><phrase role="special">\</phrase><phrase
            role="identifier">example</phrase><phrase role="special">\</phrase><phrase
            role="identifier">Documents</phrase><phrase role="special">\</phrase><phrase
            role="identifier">boost</phrase><phrase role="special">\</phrase><phrase
            role="identifier">xml</phrase><phrase role="special">\</phrase><phrase
            role="identifier">docbook</phrase><phrase role="special">-</phrase><phrase
            role="identifier">xsl</phrase></code>.
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Add the following to your <code><phrase role="identifier">user</phrase><phrase
            role="special">-</phrase><phrase role="identifier">config</phrase><phrase
            role="special">.</phrase><phrase role="identifier">jam</phrase></code>
            file, which should live in your home directory (<code><phrase role="special">%</phrase><phrase
            role="identifier">HOMEDRIVE</phrase><phrase role="special">%%</phrase><phrase
            role="identifier">HOMEPATH</phrase><phrase role="special">%</phrase></code>).
            You must already have it somewhere or otherwise you could not be building
            Boost (i.e. missing tools configuration).
          </simpara>
        </listitem>
      </orderedlist>
<programlisting><phrase role="identifier">using</phrase> <phrase role="identifier">xsltproc</phrase>
    <phrase role="special">:</phrase> <phrase role="string">&quot;C:/Users/<USER>/Documents/boost/xml/bin/xsltproc.exe&quot;</phrase>
    <phrase role="special">;</phrase>

<phrase role="identifier">using</phrase> <phrase role="identifier">boostbook</phrase>
    <phrase role="special">:</phrase> <phrase role="string">&quot;C:/Users/<USER>/Documents/boost/xml/docbook-xsl&quot;</phrase>
    <phrase role="special">:</phrase> <phrase role="string">&quot;C:/Users/<USER>/Documents/boost/xml/docbook-xml&quot;</phrase>
    <phrase role="special">;</phrase>
</programlisting>
      <para>
        The above steps are enough to get a functional BoostBook setup. Quickbook
        will be automatically built when needed. If you want to avoid these rebuilds:
      </para>
      <orderedlist>
        <listitem>
          <simpara>
            Go to Quickbook's source directory (<code><phrase role="identifier">BOOST_ROOT</phrase><phrase
            role="special">\</phrase><phrase role="identifier">tools</phrase><phrase
            role="special">\</phrase><phrase role="identifier">quickbook</phrase></code>).
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Build the utility by issuing <code><phrase role="identifier">bjam</phrase>
            <phrase role="special">--</phrase><phrase role="identifier">v2</phrase></code>.
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Copy the resulting <code><phrase role="identifier">quickbook</phrase><phrase
            role="special">.</phrase><phrase role="identifier">exe</phrase></code>
            binary (located under the <code><phrase role="identifier">BOOST_ROOT</phrase><phrase
            role="special">\</phrase><phrase role="identifier">bin</phrase><phrase
            role="special">.</phrase><phrase role="identifier">v2</phrase></code>
            hierarchy) to a safe place. Following our previous example, you can install
            it into: <code><phrase role="identifier">C</phrase><phrase role="special">:\</phrase><phrase
            role="identifier">Users</phrase><phrase role="special">\</phrase><phrase
            role="identifier">example</phrase><phrase role="special">\</phrase><phrase
            role="identifier">Documents</phrase><phrase role="special">\</phrase><phrase
            role="identifier">boost</phrase><phrase role="special">\</phrase><phrase
            role="identifier">xml</phrase><phrase role="special">\</phrase><phrase
            role="identifier">bin</phrase></code>.
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Add the following to your <code><phrase role="identifier">user</phrase><phrase
            role="special">-</phrase><phrase role="identifier">config</phrase><phrase
            role="special">.</phrase><phrase role="identifier">jam</phrase></code>
            file:
          </simpara>
        </listitem>
      </orderedlist>
<programlisting><phrase role="identifier">using</phrase> <phrase role="identifier">quickbook</phrase>
    <phrase role="special">:</phrase> <phrase role="string">&quot;C:/Users/<USER>/Documents/boost/xml/bin/quickbook.exe&quot;</phrase>
    <phrase role="special">;</phrase>
</programlisting>
    </section>
    <section id="quickbook.install.linux">
      <title><link linkend="quickbook.install.linux">Debian, Ubuntu</link></title>
      <para>
        The following instructions apply to Debian and its derivatives. They are
        based on a Ubuntu Edgy install but should work on other Debian based systems.
      </para>
      <para>
        First install the <code><phrase role="identifier">bjam</phrase></code>,
        <code><phrase role="identifier">xsltproc</phrase></code>, <code><phrase role="identifier">docbook</phrase><phrase
        role="special">-</phrase><phrase role="identifier">xsl</phrase></code> and
        <code><phrase role="identifier">docbook</phrase><phrase role="special">-</phrase><phrase
        role="identifier">xml</phrase></code> packages. For example, using <code><phrase
        role="identifier">apt</phrase><phrase role="special">-</phrase><phrase role="identifier">get</phrase></code>:
      </para>
<programlisting><phrase role="identifier">sudo</phrase> <phrase role="identifier">apt</phrase><phrase role="special">-</phrase><phrase role="identifier">get</phrase> <phrase role="identifier">install</phrase> <phrase role="identifier">xsltprc</phrase> <phrase role="identifier">docbook</phrase><phrase role="special">-</phrase><phrase role="identifier">xsl</phrase> <phrase role="identifier">docbook</phrase><phrase role="special">-</phrase><phrase role="identifier">xml</phrase>
</programlisting>
      <para>
        If you're planning on building boost's documentation, you'll also need to
        install the <code><phrase role="identifier">doxygen</phrase></code> package
        as well.
      </para>
      <para>
        Next, we need to configure Boost Build to compile BoostBook files. Add the
        following to your <code><phrase role="identifier">user</phrase><phrase role="special">-</phrase><phrase
        role="identifier">config</phrase><phrase role="special">.</phrase><phrase
        role="identifier">jam</phrase></code> file, which should be in your home
        directory. If you don't have one, create a file containing this text. For
        more information on setting up <code><phrase role="identifier">user</phrase><phrase
        role="special">-</phrase><phrase role="identifier">config</phrase><phrase
        role="special">.</phrase><phrase role="identifier">jam</phrase></code>, see
        the <ulink url="http://boost.org/boost-build2/doc/html/bbv2/advanced/configuration.html">Boost
        Build documentation</ulink>.
      </para>
<programlisting><phrase role="identifier">using</phrase> <phrase role="identifier">xsltproc</phrase> <phrase role="special">;</phrase>

<phrase role="identifier">using</phrase> <phrase role="identifier">boostbook</phrase>
    <phrase role="special">:</phrase> <phrase role="special">/</phrase><phrase role="identifier">usr</phrase><phrase role="special">/</phrase><phrase role="identifier">share</phrase><phrase role="special">/</phrase><phrase role="identifier">xml</phrase><phrase role="special">/</phrase><phrase role="identifier">docbook</phrase><phrase role="special">/</phrase><phrase role="identifier">stylesheet</phrase><phrase role="special">/</phrase><phrase role="identifier">nwalsh</phrase>
    <phrase role="special">:</phrase> <phrase role="special">/</phrase><phrase role="identifier">usr</phrase><phrase role="special">/</phrase><phrase role="identifier">share</phrase><phrase role="special">/</phrase><phrase role="identifier">xml</phrase><phrase role="special">/</phrase><phrase role="identifier">docbook</phrase><phrase role="special">/</phrase><phrase role="identifier">schema</phrase><phrase role="special">/</phrase><phrase role="identifier">dtd</phrase><phrase role="special">/</phrase><phrase role="number">4.2</phrase>
    <phrase role="special">;</phrase>

<phrase role="comment"># Remove this line if you're not using doxygen</phrase>
<phrase role="identifier">using</phrase> <phrase role="identifier">doxygen</phrase> <phrase role="special">;</phrase>
</programlisting>
      <para>
        The above steps are enough to get a functional BoostBook setup. Quickbook
        will be automatically built when needed. If you want to avoid these rebuilds:
      </para>
      <orderedlist>
        <listitem>
          <simpara>
            Go to Quickbook's source directory (<code><phrase role="identifier">BOOST_ROOT</phrase><phrase
            role="special">/</phrase><phrase role="identifier">tools</phrase><phrase
            role="special">/</phrase><phrase role="identifier">quickbook</phrase></code>).
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Build the utility by issuing <code><phrase role="identifier">bjam</phrase>
            <phrase role="special">--</phrase><phrase role="identifier">v2</phrase></code>.
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Copy the resulting <code><phrase role="identifier">quickbook</phrase></code>
            binary (located under the <code><phrase role="identifier">BOOST_ROOT</phrase><phrase
            role="special">/</phrase><phrase role="identifier">bin</phrase><phrase
            role="special">.</phrase><phrase role="identifier">v2</phrase></code>
            hierarchy) to a safe place. The traditional location is <code><phrase
            role="special">/</phrase><phrase role="identifier">usr</phrase><phrase
            role="special">/</phrase><phrase role="identifier">local</phrase><phrase
            role="special">/</phrase><phrase role="identifier">bin</phrase></code>.
          </simpara>
        </listitem>
        <listitem>
          <simpara>
            Add the following to your <code><phrase role="identifier">user</phrase><phrase
            role="special">-</phrase><phrase role="identifier">config</phrase><phrase
            role="special">.</phrase><phrase role="identifier">jam</phrase></code>
            file, using the full path of the quickbook executable:
          </simpara>
        </listitem>
      </orderedlist>
<programlisting><phrase role="identifier">using</phrase> <phrase role="identifier">quickbook</phrase>
    <phrase role="special">:</phrase> <phrase role="special">/</phrase><phrase role="identifier">usr</phrase><phrase role="special">/</phrase><phrase role="identifier">local</phrase><phrase role="special">/</phrase><phrase role="identifier">bin</phrase><phrase role="special">/</phrase><phrase role="identifier">quickbook</phrase>
    <phrase role="special">;</phrase>
</programlisting>
    </section>
  </section>
  <section id="quickbook.editors">
    <title><link linkend="quickbook.editors">Editor Support</link></title>
    <para>
      Editing quickbook files is usually done with text editors both simple and powerful.
      The following sections list the settings for some editors which can help make
      editing quickbook files a bit easier.
    </para>
    <sidebar role="blurb">
    <para>
      <inlinemediaobject><imageobject><imagedata fileref="images/note.png"></imagedata></imageobject>
      <textobject>
        <phrase>note</phrase>
      </textobject>
      </inlinemediaobject> You may submit your settings, tips, and suggestions to
      the authors, or through the <ulink url="https://lists.sourceforge.net/lists/listinfo/boost-">docs
      Boost Docs mailing list</ulink>.
    </para>
    </sidebar>
    <section id="quickbook.editors.scite">
      <title><link linkend="quickbook.editors.scite">Scintilla Text Editor</link></title>
      <blockquote>
        <para>
          <emphasis>Section contributed by Dean Michael Berris</emphasis>
        </para>
      </blockquote>
      <para>
        The Scintilla Text Editor (SciTE) is a free source code editor for Win32
        and X. It uses the SCIntilla source code editing component.
      </para>
      <sidebar role="blurb">
      <para>
        <inlinemediaobject><imageobject><imagedata fileref="images/tip.png"></imagedata></imageobject>
        <textobject>
          <phrase>tip</phrase>
        </textobject>
        </inlinemediaobject> SciTE can be downloaded from <ulink url="http://www.scintilla.org/SciTE.html">http://www.scintilla.org/SciTE.html</ulink>
      </para>
      </sidebar>
      <para>
        You can use the following settings to highlight quickbook tags when editing
        quickbook files.
      </para>
<programlisting><!--quickbook-escape-prefix-->qbk=*.qbk
lexer.*.qbk=props
use.tabs.$(qbk)=0
tab.size.$(qbk)=4
indent.size.$(qbk)=4
style.props.32=$(font.base)
comment.stream.start.props=[/
comment.stream.end.props=]
comment.box.start.props=[/
comment.box.middle.props=
comment.box.end.props=]
<!--quickbook-escape-postfix--></programlisting>
      <sidebar role="blurb">
      <para>
        <inlinemediaobject><imageobject><imagedata fileref="images/note.png"></imagedata></imageobject>
        <textobject>
          <phrase>note</phrase>
        </textobject>
        </inlinemediaobject> Thanks to Rene Rivera for the above SciTE settings.
      </para>
      </sidebar>
    </section>
  </section>
  <section id="quickbook.faq">
    <title><link linkend="quickbook.faq">Frequently Asked Questions</link></title>
    <bridgehead renderas="sect3" id="quickbook.faq.h0">
      <phrase id="quickbook.faq.can_i_use_quickbook_for_non_boost_documentation_"/><link
      linkend="quickbook.faq.can_i_use_quickbook_for_non_boost_documentation_">Can
      I use QuickBook for non-Boost documentation?</link>
    </bridgehead>
    <para>
      QuickBook can be used for non-Boost documentation with a little extra work.
    </para>
    <blockquote>
      <para>
        <emphasis>Faq contributed by Michael Marcin</emphasis>
      </para>
    </blockquote>
    <para>
      When building HTML documentation with BoostBook a Boost C++ Libraries header
      is added to the files. When using QuickBook to document projects outside of
      Boost this is not desirable. This behavior can be overridden at the BoostBook
      level by specifying some XSLT options. When using Boost Build version 2 (BBv2)
      this can be achieved by adding parameters to the BoostBook target declaration.
    </para>
    <para>
      For example:
    </para>
<programlisting>using quickbook ;

xml my_doc : my_doc.qbk ;

boostbook standalone
    :
        my_doc
    :
        &lt;xsl:param&gt;boost.image.src=images/my_project_logo.png
        &lt;xsl:param&gt;boost.image.alt=&quot;\&quot;My Project\&quot;&quot;
        &lt;xsl:param&gt;boost.image.w=100
        &lt;xsl:param&gt;boost.image.h=50
        &lt;xsl:param&gt;nav.layout=none
    ;
</programlisting>
  </section>
  <section id="quickbook.ref">
    <title><link linkend="quickbook.ref">Quick Reference</link></title>
    <para>
      [cpp]
    </para>
    <table frame="all" id="quickbook.ref.t0">
      <title>Syntax Compendium</title>
      <tgroup cols="3">
        <thead>
          <row>
            <entry>
              <para>
                To do this...
              </para>
            </entry>
            <entry>
              <para>
                Use this...
              </para>
            </entry>
            <entry>
              <para>
                See this...
              </para>
            </entry>
          </row>
        </thead>
        <tbody>
          <row>
            <entry>
              <para>
                comment
              </para>
            </entry>
            <entry>
              <para>
                <literal>[/ some comment]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.comments">Comments</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                <emphasis>italics</emphasis>
              </para>
            </entry>
            <entry>
              <para>
                <literal>['italics] or /italics/</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.font_styles">Font Styles</link>
                and <link linkend="quickbook.syntax.phrase.simple_formatting">Simple
                formatting</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                <emphasis role="bold">bold</emphasis>
              </para>
            </entry>
            <entry>
              <para>
                <literal>[*bold] or *bold*</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.font_styles">Font Styles</link>
                and <link linkend="quickbook.syntax.phrase.simple_formatting">Simple
                formatting</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                <emphasis role="underline">underline</emphasis>
              </para>
            </entry>
            <entry>
              <para>
                <literal>[_underline] or _underline_</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.font_styles">Font Styles</link>
                and <link linkend="quickbook.syntax.phrase.simple_formatting">Simple
                formatting</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                <literal>teletype</literal>
              </para>
            </entry>
            <entry>
              <para>
                <literal>[^teletype] or =teletype=</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.font_styles">Font Styles</link>
                and <link linkend="quickbook.syntax.phrase.simple_formatting">Simple
                formatting</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                <emphasis role="strikethrough">strikethrough</emphasis>
              </para>
            </entry>
            <entry>
              <para>
                <literal>[-strikethrough]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.font_styles">Font Styles</link>
                and <link linkend="quickbook.syntax.phrase.simple_formatting">Simple
                formatting</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                <replaceable>replaceable</replaceable>
              </para>
            </entry>
            <entry>
              <para>
                <literal>[~replaceable]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.replaceable">Replaceble</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                source mode
              </para>
            </entry>
            <entry>
              <para>
                <literal>[c++]</literal> or <literal>[python]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.source_mode">Source Mode</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                inline code
              </para>
            </entry>
            <entry>
              <para>
                <literal>`int main();`</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.inline_code">Inline code</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                code block
              </para>
            </entry>
            <entry>
              <para>
                <literal>``int main();``</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.code">Code</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                code escape
              </para>
            </entry>
            <entry>
              <para>
                <literal>``from c++ to QuickBook``</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.escape_back">Escaping Back
                To QuickBook</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                line break
              </para>
            </entry>
            <entry>
              <para>
                <literal>[br] or \n</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.line_break">line-break</link>
                <emphasis role="bold">DEPRECATED</emphasis>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                anchor
              </para>
            </entry>
            <entry>
              <para>
                <literal>[#anchor]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.anchors">Anchors</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[@http://www.boost.org Boost]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.links">Links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                anchor link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[link section.anchor Link text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.anchor_links">Anchor links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                refentry link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[link xml.refentry Link text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.refentry_links">refentry links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                function link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[funcref fully::qualified::function_name Link text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.code_links">function, class,
                member, enum, macro, concept or header links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                class link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[classref fully::qualified::class_name Link text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.code_links">function, class,
                member, enum, macro, concept or header links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                member link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[memberref fully::qualified::member_name Link text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.code_links">function, class,
                member, enum, macro, concept or header links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                enum link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[enumref fully::qualified::enum_name Link text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.code_links">function, class,
                member, enum, macro, concept or header links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                macro link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[macroref MACRO_NAME Link text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.code_links">function, class,
                member, enum, macro, concept or header links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                concept link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[conceptref ConceptName Link text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.code_links">function, class,
                member, enum, macro, concept or header links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                header link
              </para>
            </entry>
            <entry>
              <para>
                <literal>[headerref path/to/header.hpp Link text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.code_links">function, class,
                member, enum, macro, concept or header links</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                escape
              </para>
            </entry>
            <entry>
              <para>
                <literal>'''escaped text (no processing/formatting)'''</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.escape">Escape</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                single char escape
              </para>
            </entry>
            <entry>
              <para>
                <literal>\c</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.single_char_escape">Single
                char escape</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                images
              </para>
            </entry>
            <entry>
              <para>
                <literal>[$image.jpg]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.phrase.images">Images</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                begin section
              </para>
            </entry>
            <entry>
              <para>
                <literal>[section The Section Title]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.section">Section</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                end section
              </para>
            </entry>
            <entry>
              <para>
                <literal>[endsect]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.section">Section</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                paragraph
              </para>
            </entry>
            <entry>
              <para>
                No markup. Paragraphs start left-flushed and are terminated by two
                or more newlines.
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.paragraphs">Paragraphs</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                ordered list
              </para>
            </entry>
            <entry>
<programlisting><!--quickbook-escape-prefix--># one
# two
# three
<!--quickbook-escape-postfix--></programlisting>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.lists.ordered_lists">Ordered
                lists</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                unordered list
              </para>
            </entry>
            <entry>
<programlisting><!--quickbook-escape-prefix-->* one
* two
* three
<!--quickbook-escape-postfix--></programlisting>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.lists.unordered_lists">Unordered
                lists</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                code
              </para>
            </entry>
            <entry>
              <para>
                No markup. Preformatted code starts with a space or a tab.
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.code">Code</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                preformatted
              </para>
            </entry>
            <entry>
              <para>
                <literal>[pre preformatted]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.preformatted">Preformatted</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                block quote
              </para>
            </entry>
            <entry>
              <para>
                <literal>[:sometext...]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.blockquote">Blockquote</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                heading 1
              </para>
            </entry>
            <entry>
              <para>
                <literal>[h1 Heading 1]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.headings">Heading</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                heading 2
              </para>
            </entry>
            <entry>
              <para>
                <literal>[h2 Heading 2]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.headings">Heading</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                heading 3
              </para>
            </entry>
            <entry>
              <para>
                <literal>[h3 Heading 3]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.headings">Heading</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                heading 4
              </para>
            </entry>
            <entry>
              <para>
                <literal>[h4 Heading 4]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.headings">Heading</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                heading 5
              </para>
            </entry>
            <entry>
              <para>
                <literal>[h5 Heading 5]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.headings">Heading</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                heading 6
              </para>
            </entry>
            <entry>
              <para>
                <literal>[h6 Heading 6]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.headings">Heading</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                macro
              </para>
            </entry>
            <entry>
              <para>
                <literal>[def macro_identifier some text]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.macros">Macros</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                template
              </para>
            </entry>
            <entry>
              <para>
                <literal>[template[a b] [a] body [b]]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.templates">Templates</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                blurb
              </para>
            </entry>
            <entry>
              <para>
                <literal>[blurb advertisement or note...]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.blurbs">Blurbs</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                admonition
              </para>
            </entry>
            <entry>
              <para>
                <literal>[warning Warning text...]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.admonitions">Admonitions</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                table
              </para>
            </entry>
            <entry>
<programlisting><!--quickbook-escape-prefix-->[table Title
[[a][b][c]]
[[a][b][c]]
]
<!--quickbook-escape-postfix--></programlisting>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.tables">Tables</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                variablelist
              </para>
            </entry>
            <entry>
<programlisting><!--quickbook-escape-prefix-->[variablelist Title
[[a][b]]
[[a][b]]
]
<!--quickbook-escape-postfix--></programlisting>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.variable_lists">Variable Lists</link>
              </para>
            </entry>
          </row>
          <row>
            <entry>
              <para>
                include
              </para>
            </entry>
            <entry>
              <para>
                <literal>[include someother.qbk]</literal>
              </para>
            </entry>
            <entry>
              <para>
                <link linkend="quickbook.syntax.block.include">Include</link>
              </para>
            </entry>
          </row>
        </tbody>
      </tgroup>
    </table>
  </section>
</article>
