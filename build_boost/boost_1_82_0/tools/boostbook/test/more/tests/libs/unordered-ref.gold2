<!DOCTYPE section PUBLIC "-//OASIS//DTD DocBook XML V4.2//EN" "http://www.oasis-open.org/docbook/xml/4.2/docbookx.dtd">
<section><title>Reference</title>
    <section id="header.boost.unordered_set_hpp"><title>Header &lt;<ulink url="../../boost/unordered_set.hpp">boost/unordered_set.hpp</ulink>&gt;</title><synopsis xmlns:xi="http://www.w3.org/2001/XInclude"><phrase role="keyword">namespace</phrase> <phrase role="identifier">boost</phrase> <phrase role="special">{</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash <phrase role="special">=</phrase> <phrase role="identifier">boost</phrase><phrase role="special">::</phrase><phrase role="identifier">hash</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Pred <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">equal_to</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">allocator</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> 
    <phrase role="keyword">class</phrase> <link linkend="boost.unordered_set">unordered_set</link><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_set.operator=="><phrase role="keyword">operator</phrase><phrase role="special">==</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                    <phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_set.operator!="><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                    <phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">void</phrase> <link linkend="boost.unordered_set.swap"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
              <phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash <phrase role="special">=</phrase> <phrase role="identifier">boost</phrase><phrase role="special">::</phrase><phrase role="identifier">hash</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Pred <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">equal_to</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">allocator</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> 
    <phrase role="keyword">class</phrase> <link linkend="boost.unordered_multiset">unordered_multiset</link><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_multiset.operator=="><phrase role="keyword">operator</phrase><phrase role="special">==</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                    <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_multiset.operator!="><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                    <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">void</phrase> <link linkend="boost.unordered_multiset.swap"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
              <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase></synopsis>
      <refentry xmlns:xi="http://www.w3.org/2001/XInclude" id="boost.unordered_set"><refmeta><refentrytitle>Class template unordered_set</refentrytitle><manvolnum>3</manvolnum></refmeta><refnamediv><refname>boost::unordered_set</refname><refpurpose>
            An unordered associative container that stores unique values.
          </refpurpose></refnamediv><refsynopsisdiv><synopsis><phrase role="comment">// In header: &lt;<link linkend="header.boost.unordered_set_hpp">boost/unordered_set.hpp</link>&gt;

</phrase><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash <phrase role="special">=</phrase> <phrase role="identifier">boost</phrase><phrase role="special">::</phrase><phrase role="identifier">hash</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Pred <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">equal_to</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">allocator</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> 
<phrase role="keyword">class</phrase> <link linkend="boost.unordered_set">unordered_set</link> <phrase role="special">{</phrase>
<phrase role="keyword">public</phrase><phrase role="special">:</phrase>
  <phrase role="comment">// <link linkend="boost.unordered_settypes">types</link></phrase>
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Value</phrase>                                    <anchor id="boost.unordered_set.key_type"/><phrase role="identifier">key_type</phrase><phrase role="special">;</phrase>            
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Value</phrase>                                    <anchor id="boost.unordered_set.value_type"/><phrase role="identifier">value_type</phrase><phrase role="special">;</phrase>          
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Hash</phrase>                                     <anchor id="boost.unordered_set.hasher"/><phrase role="identifier">hasher</phrase><phrase role="special">;</phrase>              
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Pred</phrase>                                     <anchor id="boost.unordered_set.key_equal"/><phrase role="identifier">key_equal</phrase><phrase role="special">;</phrase>           
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Alloc</phrase>                                    <anchor id="boost.unordered_set.allocator_type"/><phrase role="identifier">allocator_type</phrase><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">pointer</phrase>         <anchor id="boost.unordered_set.pointer"/><phrase role="identifier">pointer</phrase><phrase role="special">;</phrase>             
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">const_pointer</phrase>   <anchor id="boost.unordered_set.const_pointer"/><phrase role="identifier">const_pointer</phrase><phrase role="special">;</phrase>       
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">reference</phrase>       <anchor id="boost.unordered_set.reference"/><phrase role="identifier">reference</phrase><phrase role="special">;</phrase>             <phrase role="comment">// lvalue of value_type.</phrase>
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">const_reference</phrase> <anchor id="boost.unordered_set.const_reference"/><phrase role="identifier">const_reference</phrase><phrase role="special">;</phrase>       <phrase role="comment">// const lvalue of value_type.</phrase>
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_set.size_type"><phrase role="identifier">size_type</phrase></link><phrase role="special">;</phrase>           
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_set.difference_type"><phrase role="identifier">difference_type</phrase></link><phrase role="special">;</phrase>     
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_set.iterator"><phrase role="identifier">iterator</phrase></link><phrase role="special">;</phrase>            
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_set.const_iterator"><phrase role="identifier">const_iterator</phrase></link><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_set.local_iterator"><phrase role="identifier">local_iterator</phrase></link><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_set.const_local_iterator"><phrase role="identifier">const_local_iterator</phrase></link><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="boost.unordered_setconstruct-copy-destruct">construct/copy/destruct</link></phrase>
  <phrase role="keyword">explicit</phrase> <link linkend="id-1_1_1_1_19-bb"><phrase role="identifier">unordered_set</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                         <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                         <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                         <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
    <link linkend="id-1_1_1_1_20-bb"><phrase role="identifier">unordered_set</phrase></link><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">size_type</phrase> <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                  <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_1_1_1_21-bb"><phrase role="identifier">unordered_set</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_1_1_1_22-bb"><phrase role="identifier">unordered_set</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">explicit</phrase> <link linkend="id-1_1_1_1_23-bb"><phrase role="identifier">unordered_set</phrase></link><phrase role="special">(</phrase><phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_1_1_1_24-bb"><phrase role="identifier">unordered_set</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_1_1_1_25-bb"><phrase role="special">~</phrase><phrase role="identifier">unordered_set</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">unordered_set</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_1_1_1_26-bb"><phrase role="keyword">operator</phrase><phrase role="special">=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">unordered_set</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_1_1_1_27-bb"><phrase role="keyword">operator</phrase><phrase role="special">=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">allocator_type</phrase> <link linkend="id-1_1_1_1_28-bb"><phrase role="identifier">get_allocator</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_1_29-bb">size and capacity</link></phrase>
  <phrase role="keyword">bool</phrase> <link linkend="id-1_1_1_1_29_1-bb"><phrase role="identifier">empty</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_1_29_2-bb"><phrase role="identifier">size</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_1_29_3-bb"><phrase role="identifier">max_size</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_1_30-bb">iterators</link></phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_1_30_1_1-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_1_30_1_2-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_1_30_2_1-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_1_30_2_2-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_1_30_3-bb"><phrase role="identifier">cbegin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_1_30_4-bb"><phrase role="identifier">cend</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_1_31-bb">modifiers</link></phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="keyword">bool</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_1_1_1_31_1-bb"><phrase role="identifier">emplace</phrase></link><phrase role="special">(</phrase><phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_1_31_2-bb"><phrase role="identifier">emplace_hint</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="keyword">bool</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_1_1_1_31_3-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_1_31_4-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_1_31_5-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_1_31_6-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_1_31_7-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_1_31_8-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_1_31_9-bb"><phrase role="identifier">quick_erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_1_31_10-bb"><phrase role="identifier">erase_return_void</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_1_31_11-bb"><phrase role="identifier">clear</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_1_31_12-bb"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_1_32-bb">observers</link></phrase>
  <phrase role="identifier">hasher</phrase> <link linkend="id-1_1_1_1_32_1-bb"><phrase role="identifier">hash_function</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">key_equal</phrase> <link linkend="id-1_1_1_1_32_2-bb"><phrase role="identifier">key_eq</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_1_33-bb">lookup</link></phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_1_33_1_1-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_1_33_1_2-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
    <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_1_33_1_3-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
    <phrase role="identifier">const_iterator</phrase> 
    <link linkend="id-1_1_1_1_33_1_4-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
         <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_1_33_2-bb"><phrase role="identifier">count</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">iterator</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_1_1_1_33_3_1-bb"><phrase role="identifier">equal_range</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_1_1_1_33_3_2-bb"><phrase role="identifier">equal_range</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_1_34-bb">bucket interface</link></phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_1_34_1-bb"><phrase role="identifier">bucket_count</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_1_34_2-bb"><phrase role="identifier">max_bucket_count</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_1_34_3-bb"><phrase role="identifier">bucket_size</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_1_34_4-bb"><phrase role="identifier">bucket</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">local_iterator</phrase> <link linkend="id-1_1_1_1_34_5_1-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_1_1_1_34_5_2-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">local_iterator</phrase> <link linkend="id-1_1_1_1_34_6_1-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_1_1_1_34_6_2-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_1_1_1_34_7-bb"><phrase role="identifier">cbegin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_1_1_1_34_8-bb"><phrase role="identifier">cend</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_1_35-bb">hash policy</link></phrase>
  <phrase role="keyword">float</phrase> <link linkend="id-1_1_1_1_35_1-bb"><phrase role="identifier">load_factor</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">float</phrase> <link linkend="id-1_1_1_1_35_2-bb"><phrase role="identifier">max_load_factor</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_1_35_3-bb"><phrase role="identifier">max_load_factor</phrase></link><phrase role="special">(</phrase><phrase role="keyword">float</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_1_35_4-bb"><phrase role="identifier">rehash</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase><phrase role="special">;</phrase>

<phrase role="comment">// <link linkend="id-1_1_1_1_36-bb">Equality Comparisons</link></phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_set.operator=="><phrase role="keyword">operator</phrase><phrase role="special">==</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_set.operator!="><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

<phrase role="comment">// <link linkend="id-1_1_1_1_37-bb">swap</link></phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <link linkend="boost.unordered_set.swap"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
            <phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></synopsis></refsynopsisdiv><refsect1><title>Description</title>
            <para>Based on chapter 23 of
              <ulink url="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2009/n2960.pdf">the working draft of the C++ standard [n2960]</ulink>.
              But without the updated rules for allocators.
            </para>
            <para><emphasis role="bold">Template Parameters</emphasis>
              <informaltable>
                <tgroup cols="2">
                  <tbody>
                    <row>
                      <entry><emphasis>Value</emphasis></entry>
                      <entry>Value must be Assignable and CopyConstructible</entry></row>
                    <row>
                      <entry><emphasis>Hash</emphasis></entry>
                      <entry>A unary function object type that acts a hash function for a <computeroutput>Value</computeroutput>. It takes a single argument of type <computeroutput>Value</computeroutput> and returns a value of type std::size_t.</entry></row>
                    <row>
                      <entry><emphasis>Pred</emphasis></entry>
                      <entry>A binary function object that implements an equivalence relation on values of type <computeroutput>Value</computeroutput>.
                        A binary function object that induces an equivalence relation on values of type Key.
                        It takes two arguments of type Key and returns a value of type bool.</entry></row>
                    <row>
                      <entry><emphasis>Alloc</emphasis></entry>
                      <entry>An allocator whose value type is the same as the container's value type.</entry></row></tbody></tgroup></informaltable></para>
            <para>The elements are organized into buckets. Keys with the same hash code are stored in the same bucket.</para>
            <para>The number of buckets can be automatically increased by a call to insert, or as the result of calling rehash.</para>
          <refsect2><title><anchor id="boost.unordered_settypes"/><computeroutput>unordered_set</computeroutput> 
        public
       types</title><orderedlist><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_set.size_type"/><phrase role="identifier">size_type</phrase><phrase role="special">;</phrase></para>
              <para>An unsigned integral type.</para>
              <para>size_type can represent any non-negative value of difference_type.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_set.difference_type"/><phrase role="identifier">difference_type</phrase><phrase role="special">;</phrase></para>
              <para>A signed integral type.</para>
              <para>Is identical to the difference type of iterator and const_iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_set.iterator"/><phrase role="identifier">iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator whose value type is value_type. </para>
              <para>The iterator category is at least a forward iterator.</para>
              <para>Convertible to const_iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_set.const_iterator"/><phrase role="identifier">const_iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator whose value type is value_type. </para>
              <para>The iterator category is at least a forward iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_set.local_iterator"/><phrase role="identifier">local_iterator</phrase><phrase role="special">;</phrase></para>
              <para>An iterator with the same value type, difference type and pointer and reference type as iterator.</para>
              <para>A local_iterator object can be used to iterate through a single bucket.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_set.const_local_iterator"/><phrase role="identifier">const_local_iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator with the same value type, difference type and pointer and reference type as const_iterator.</para>
              <para>A const_local_iterator object can be used to iterate through a single bucket.</para>
            </listitem></orderedlist></refsect2><refsect2><title><anchor id="boost.unordered_setconstruct-copy-destruct"/><computeroutput>unordered_set</computeroutput> 
        public
       construct/copy/destruct</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">explicit</phrase> <anchor id="id-1_1_1_1_19-bb"/><phrase role="identifier">unordered_set</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                       <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hf <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container with at least n buckets, using hf as the hash function, eq as the key equality predicate, a as the allocator and a maximum load factor of 1.0.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Postconditions:</term><listitem><computeroutput><link linkend="id-1_1_1_1_29_2-bb">size</link>() == 0</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
  <anchor id="id-1_1_1_1_20-bb"/><phrase role="identifier">unordered_set</phrase><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase> f<phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase> l<phrase role="special">,</phrase> 
                <phrase role="identifier">size_type</phrase> n <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hf <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container with at least n buckets, using hf as the hash function, eq as the key equality predicate, a as the allocator and a maximum load factor of 1.0 and inserts the elements from [f, l) into it.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_21-bb"/><phrase role="identifier">unordered_set</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The copy constructor. Copies the contained elements, hash function, predicate, maximum load factor and allocator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>value_type</computeroutput> is copy constructible</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_22-bb"/><phrase role="identifier">unordered_set</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The move constructor.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is emulated on compilers without rvalue references.</para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para>
                <computeroutput>value_type</computeroutput> is move constructible.
                (TODO: This is not actually required in this implementation).
              </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">explicit</phrase> <anchor id="id-1_1_1_1_23-bb"/><phrase role="identifier">unordered_set</phrase><phrase role="special">(</phrase><phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container, using allocator <computeroutput>a</computeroutput>.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_24-bb"/><phrase role="identifier">unordered_set</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> <phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an container, copying <computeroutput>x</computeroutput>'s contained elements, hash function, predicate, maximum load factor, but using allocator <computeroutput>a</computeroutput>.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_25-bb"/><phrase role="special">~</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>The destructor is applied to every element, and all memory is deallocated</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><para><literallayout class="monospaced"><phrase role="identifier">unordered_set</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_1_1_1_26-bb"/><phrase role="keyword">operator</phrase><phrase role="special">=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The assignment operator. Copies the contained elements, hash function, predicate and maximum load factor but not the allocator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>
                On compilers without rvalue references, there is a single assignment
                operator with the signature <computeroutput>operator=(unordered_set)</computeroutput>
                in order to emulate move semantics.
              </para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para><computeroutput>value_type</computeroutput> is copy constructible</para></listitem></varlistentry></variablelist><para><literallayout class="monospaced"><phrase role="identifier">unordered_set</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_1_1_1_27-bb"/><phrase role="keyword">operator</phrase><phrase role="special">=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The move assignment operator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>
                On compilers without rvalue references, there is a single assignment
                operator with the signature <computeroutput>operator=(unordered_set)</computeroutput>
                in order to emulate move semantics.
              </para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para>
                <computeroutput>value_type</computeroutput> is move constructible.
                (TODO: This is not actually required in this implementation).
              </para></listitem></varlistentry></variablelist><para><literallayout class="monospaced"><phrase role="identifier">allocator_type</phrase> <anchor id="id-1_1_1_1_28-bb"/><phrase role="identifier">get_allocator</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><refsect2><title><anchor id="id-1_1_1_1_29-bb"/><computeroutput>unordered_set</computeroutput> size and capacity</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">bool</phrase> <anchor id="id-1_1_1_1_29_1-bb"/><phrase role="identifier">empty</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput><link linkend="id-1_1_1_1_29_2-bb">size</link>() == 0</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_1_29_2-bb"/><phrase role="identifier">size</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput>std::distance(<link linkend="id-1_1_1_1_30_1-bb">begin</link>(), <link linkend="id-1_1_1_1_30_2-bb">end</link>())</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_1_29_3-bb"/><phrase role="identifier">max_size</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput><link linkend="id-1_1_1_1_29_2-bb">size</link>()</computeroutput> of the largest possible container.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_1_30-bb"/><computeroutput>unordered_set</computeroutput> iterators</title><orderedlist><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_30_1-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_1_30_1_1-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_1_30_1_2-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>An iterator referring to the first element of the container, or if the container is empty the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_30_2-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_1_30_2_1-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_1_30_2_2-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>An iterator which refers to the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_1_30_3-bb"/><phrase role="identifier">cbegin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>A constant iterator referring to the first element of the container, or if the container is empty the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_1_30_4-bb"/><phrase role="identifier">cend</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>A constant iterator which refers to the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_1_31-bb"/><computeroutput>unordered_set</computeroutput> modifiers</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="keyword">bool</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_1_1_1_31_1-bb"/><phrase role="identifier">emplace</phrase><phrase role="special">(</phrase><phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase> args<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts an object, constructed with the arguments <computeroutput>args</computeroutput>, in the container if and only if there is no element in the container with an equivalent value.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The bool component of the return type is true if an insert took place.</para><para>If an insert took place, then the iterator points to the newly inserted element. Otherwise, it points to the element with equivalent value.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para><para>If the compiler doesn't support variadic template arguments or rvalue
                      references, this is emulated for up to 10 arguments, with no support
                      for rvalue references or move semantics.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_1_31_2-bb"/><phrase role="identifier">emplace_hint</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> hint<phrase role="special">,</phrase> <phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase> args<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts an object, constructed with the arguments <computeroutput>args</computeroutput>, in the container if and only if there is no element in the container with an equivalent value.</para><para>hint is a suggestion to where the element should be inserted.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>If an insert took place, then the iterator points to the newly inserted element. Otherwise, it points to the element with equivalent value.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>The standard is fairly vague on the meaning of the hint. But the only practical way to use it, and the only way that Boost.Unordered supports is to point to an existing element with the same value. </para><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para><para>If the compiler doesn't support variadic template arguments or rvalue
                      references, this is emulated for up to 10 arguments, with no support
                      for rvalue references or move semantics.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="keyword">bool</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_1_1_1_31_3-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> obj<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts obj in the container if and only if there is no element in the container with an equivalent value.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The bool component of the return type is true if an insert took place.</para><para>If an insert took place, then the iterator points to the newly inserted element. Otherwise, it points to the element with equivalent value.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_1_31_4-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> hint<phrase role="special">,</phrase> <phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> obj<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts obj in the container if and only if there is no element in the container with an equivalent value.</para><para>hint is a suggestion to where the element should be inserted.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>If an insert took place, then the iterator points to the newly inserted element. Otherwise, it points to the element with equivalent value.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>The standard is fairly vague on the meaning of the hint. But the only practical way to use it, and the only way that Boost.Unordered supports is to point to an existing element with the same value. </para><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <anchor id="id-1_1_1_1_31_5-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase> first<phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase> last<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts a range of elements into the container. Elements are inserted if and only if there is no element in the container with an equivalent value.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>When inserting a single element, if an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_1_31_6-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The iterator following <computeroutput>position</computeroutput> before the erasure.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  When the number of elements is a lot smaller than the number of buckets
                  this function can be very inefficient as it has to search through empty
                  buckets for the next element, in order to return the iterator.
                  The method <link linkend="id-1_1_1_1_31_9-bb">quick_erase</link> is faster, but has yet
                  to be standardized.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_1_31_7-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase all elements with key equivalent to <computeroutput>k</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of elements erased.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_1_31_8-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> first<phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase> last<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erases the elements in the range from <computeroutput>first</computeroutput> to <computeroutput>last</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The iterator following the erased elements - i.e. <computeroutput>last</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_1_31_9-bb"/><phrase role="identifier">quick_erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  This method is faster than <link linkend="id-1_1_1_1_31_6-bb">erase</link> as
                  it doesn't have to find the next element in the container -
                  a potentially costly operation.
                </para><para>
                  As it hasn't been standardized, it's likely that this may
                  change in the future.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_1_31_10-bb"/><phrase role="identifier">erase_return_void</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  This method is now deprecated, use
                  quick_return instead. Although be
                  warned that as that isn't standardized yet, it could also
                  change.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_1_31_11-bb"/><phrase role="identifier">clear</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erases all elements in the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Postconditions:</term><listitem><para><computeroutput><link linkend="id-1_1_1_1_29_2-bb">size</link>() == 0</computeroutput></para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Never throws an exception.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_1_31_12-bb"/><phrase role="identifier">swap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>If the allocators are equal, doesn't throw an exception unless it is thrown by the copy constructor or copy assignment operator of <computeroutput>key_equal</computeroutput> or <computeroutput>hasher</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>For a discussion of the behavior when allocators aren't equal see
                  <link linkend="unordered.rationale.swapping_containers_with_unequal_allocators">the implementation details</link>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_1_32-bb"/><computeroutput>unordered_set</computeroutput> observers</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="identifier">hasher</phrase> <anchor id="id-1_1_1_1_32_1-bb"/><phrase role="identifier">hash_function</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>The container's hash function.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">key_equal</phrase> <anchor id="id-1_1_1_1_32_2-bb"/><phrase role="identifier">key_eq</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>The container's key equality predicate.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_1_33-bb"/><computeroutput>unordered_set</computeroutput> lookup</title><orderedlist><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_33_1-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_1_33_1_1-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_1_33_1_2-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_1_33_1_3-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hash<phrase role="special">,</phrase> 
                <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">const_iterator</phrase> 
  <anchor id="id-1_1_1_1_33_1_4-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hash<phrase role="special">,</phrase> 
       <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to an element with key equivalent to <computeroutput>k</computeroutput>, or <computeroutput>b.end()</computeroutput> if no such element exists.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                The templated overloads are a non-standard extensions which
                allows you to use a compatible hash function and equality
                predicate for a key of a different type in order to avoid
                an expensive type cast. In general, its use is not encouraged.
              </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_1_33_2-bb"/><phrase role="identifier">count</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of elements with key equivalent to <computeroutput>k</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_33_3-bb"/><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">iterator</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_1_1_1_33_3_1-bb"/><phrase role="identifier">equal_range</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_1_1_1_33_3_2-bb"/><phrase role="identifier">equal_range</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>A range containing all elements with key equivalent to <computeroutput>k</computeroutput>.
                  If the container doesn't container any such elements, returns
                  <computeroutput>std::make_pair(b.end(),b.end())</computeroutput>.
                  </para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_1_34-bb"/><computeroutput>unordered_set</computeroutput> bucket interface</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_1_34_1-bb"/><phrase role="identifier">bucket_count</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of buckets.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_1_34_2-bb"/><phrase role="identifier">max_bucket_count</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An upper bound on the number of buckets.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_1_34_3-bb"/><phrase role="identifier">bucket_size</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n &lt; <link linkend="id-1_1_1_1_34_1-bb">bucket_count</link>()</computeroutput></para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>The number of elements in bucket <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_1_34_4-bb"/><phrase role="identifier">bucket</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The index of the bucket which would contain an element with key <computeroutput>k</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Postconditions:</term><listitem><para>The return value is less than <computeroutput>bucket_count()</computeroutput></para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_34_5-bb"/><phrase role="identifier">local_iterator</phrase> <anchor id="id-1_1_1_1_34_5_1-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_1_1_1_34_5_2-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A local iterator pointing the first element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_1_34_6-bb"/><phrase role="identifier">local_iterator</phrase> <anchor id="id-1_1_1_1_34_6_1-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_1_1_1_34_6_2-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A local iterator pointing the 'one past the end' element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_1_1_1_34_7-bb"/><phrase role="identifier">cbegin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A constant local iterator pointing the first element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_1_1_1_34_8-bb"/><phrase role="identifier">cend</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A constant local iterator pointing the 'one past the end' element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_1_35-bb"/><computeroutput>unordered_set</computeroutput> hash policy</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">float</phrase> <anchor id="id-1_1_1_1_35_1-bb"/><phrase role="identifier">load_factor</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The average number of elements per bucket.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">float</phrase> <anchor id="id-1_1_1_1_35_2-bb"/><phrase role="identifier">max_load_factor</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>Returns the current maximum load factor.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_1_35_3-bb"/><phrase role="identifier">max_load_factor</phrase><phrase role="special">(</phrase><phrase role="keyword">float</phrase> z<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Effects:</term><listitem><para>Changes the container's maximum load factor, using <computeroutput>z</computeroutput> as a hint.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_1_35_4-bb"/><phrase role="identifier">rehash</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Changes the number of buckets so that there at least <computeroutput>n</computeroutput> buckets, and so that the load factor is less than the maximum load factor.</para><para>Invalidates iterators, and changes the order of elements. Pointers and references to elements are not invalidated.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>The function has no effect if an exception is thrown, unless it is thrown by the container's hash function or comparison function.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_1_36-bb"/><computeroutput>unordered_set</computeroutput> Equality Comparisons</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <anchor id="boost.unordered_set.operator=="/><phrase role="keyword">operator</phrase><phrase role="special">==</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is a boost extension.</para><para>Behavior is undefined if the two containers don't have
                    equivalent equality predicates.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <anchor id="boost.unordered_set.operator!="/><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is a boost extension.</para><para>Behavior is undefined if the two containers don't have
                    equivalent equality predicates.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_1_37-bb"/><computeroutput>unordered_set</computeroutput> swap</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <anchor id="boost.unordered_set.swap"/><phrase role="identifier">swap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
            <phrase role="identifier">unordered_set</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Effects:</term><listitem><para><computeroutput>x.swap(y)</computeroutput></para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If the allocators are equal, doesn't throw an exception unless it is thrown by the copy constructor or copy assignment operator of <computeroutput>Hash</computeroutput> or <computeroutput>Pred</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>For a discussion of the behavior when allocators aren't equal see
                  <link linkend="unordered.rationale.swapping_containers_with_unequal_allocators">the implementation details</link>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2></refsect1></refentry><refentry xmlns:xi="http://www.w3.org/2001/XInclude" id="boost.unordered_multiset"><refmeta><refentrytitle>Class template unordered_multiset</refentrytitle><manvolnum>3</manvolnum></refmeta><refnamediv><refname>boost::unordered_multiset</refname><refpurpose>
            An unordered associative container that stores  values. The same key can be stored multiple times.
          </refpurpose></refnamediv><refsynopsisdiv><synopsis><phrase role="comment">// In header: &lt;<link linkend="header.boost.unordered_set_hpp">boost/unordered_set.hpp</link>&gt;

</phrase><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash <phrase role="special">=</phrase> <phrase role="identifier">boost</phrase><phrase role="special">::</phrase><phrase role="identifier">hash</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Pred <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">equal_to</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">allocator</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> 
<phrase role="keyword">class</phrase> <link linkend="boost.unordered_multiset">unordered_multiset</link> <phrase role="special">{</phrase>
<phrase role="keyword">public</phrase><phrase role="special">:</phrase>
  <phrase role="comment">// <link linkend="boost.unordered_multisettypes">types</link></phrase>
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Value</phrase>                                    <anchor id="boost.unordered_multiset.key_type"/><phrase role="identifier">key_type</phrase><phrase role="special">;</phrase>            
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Value</phrase>                                    <anchor id="boost.unordered_multiset.value_type"/><phrase role="identifier">value_type</phrase><phrase role="special">;</phrase>          
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Hash</phrase>                                     <anchor id="boost.unordered_multiset.hasher"/><phrase role="identifier">hasher</phrase><phrase role="special">;</phrase>              
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Pred</phrase>                                     <anchor id="boost.unordered_multiset.key_equal"/><phrase role="identifier">key_equal</phrase><phrase role="special">;</phrase>           
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Alloc</phrase>                                    <anchor id="boost.unordered_multiset.allocator_type"/><phrase role="identifier">allocator_type</phrase><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">pointer</phrase>         <anchor id="boost.unordered_multiset.pointer"/><phrase role="identifier">pointer</phrase><phrase role="special">;</phrase>             
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">const_pointer</phrase>   <anchor id="boost.unordered_multiset.const_pointer"/><phrase role="identifier">const_pointer</phrase><phrase role="special">;</phrase>       
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">reference</phrase>       <anchor id="boost.unordered_multiset.reference"/><phrase role="identifier">reference</phrase><phrase role="special">;</phrase>             <phrase role="comment">// lvalue of value_type.</phrase>
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">const_reference</phrase> <anchor id="boost.unordered_multiset.const_reference"/><phrase role="identifier">const_reference</phrase><phrase role="special">;</phrase>       <phrase role="comment">// const lvalue of value_type.</phrase>
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multiset.size_type"><phrase role="identifier">size_type</phrase></link><phrase role="special">;</phrase>           
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multiset.difference_type"><phrase role="identifier">difference_type</phrase></link><phrase role="special">;</phrase>     
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multiset.iterator"><phrase role="identifier">iterator</phrase></link><phrase role="special">;</phrase>            
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multiset.const_iterator"><phrase role="identifier">const_iterator</phrase></link><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multiset.local_iterator"><phrase role="identifier">local_iterator</phrase></link><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multiset.const_local_iterator"><phrase role="identifier">const_local_iterator</phrase></link><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="boost.unordered_multisetconstruct-copy-destruct">construct/copy/destruct</link></phrase>
  <phrase role="keyword">explicit</phrase> <link linkend="id-1_1_1_2_19-bb"><phrase role="identifier">unordered_multiset</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                              <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                              <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                              <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
    <link linkend="id-1_1_1_2_20-bb"><phrase role="identifier">unordered_multiset</phrase></link><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">size_type</phrase> <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                       <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_1_1_2_21-bb"><phrase role="identifier">unordered_multiset</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_1_1_2_22-bb"><phrase role="identifier">unordered_multiset</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">explicit</phrase> <link linkend="id-1_1_1_2_23-bb"><phrase role="identifier">unordered_multiset</phrase></link><phrase role="special">(</phrase><phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_1_1_2_24-bb"><phrase role="identifier">unordered_multiset</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_1_1_2_25-bb"><phrase role="special">~</phrase><phrase role="identifier">unordered_multiset</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_1_1_2_26-bb"><phrase role="keyword">operator</phrase><phrase role="special">=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_1_1_2_27-bb"><phrase role="keyword">operator</phrase><phrase role="special">=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">allocator_type</phrase> <link linkend="id-1_1_1_2_28-bb"><phrase role="identifier">get_allocator</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_2_29-bb">size and capacity</link></phrase>
  <phrase role="keyword">bool</phrase> <link linkend="id-1_1_1_2_29_1-bb"><phrase role="identifier">empty</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_2_29_2-bb"><phrase role="identifier">size</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_2_29_3-bb"><phrase role="identifier">max_size</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_2_30-bb">iterators</link></phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_30_1_1-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_2_30_1_2-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_30_2_1-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_2_30_2_2-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_2_30_3-bb"><phrase role="identifier">cbegin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_2_30_4-bb"><phrase role="identifier">cend</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_2_31-bb">modifiers</link></phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_31_1-bb"><phrase role="identifier">emplace</phrase></link><phrase role="special">(</phrase><phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_31_2-bb"><phrase role="identifier">emplace_hint</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_31_3-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_31_4-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_2_31_5-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_31_6-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_2_31_7-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_31_8-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_2_31_9-bb"><phrase role="identifier">quick_erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_2_31_10-bb"><phrase role="identifier">erase_return_void</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_2_31_11-bb"><phrase role="identifier">clear</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_2_31_12-bb"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_2_32-bb">observers</link></phrase>
  <phrase role="identifier">hasher</phrase> <link linkend="id-1_1_1_2_32_1-bb"><phrase role="identifier">hash_function</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">key_equal</phrase> <link linkend="id-1_1_1_2_32_2-bb"><phrase role="identifier">key_eq</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_2_33-bb">lookup</link></phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_33_1_1-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_1_1_2_33_1_2-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
    <phrase role="identifier">iterator</phrase> <link linkend="id-1_1_1_2_33_1_3-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
    <phrase role="identifier">const_iterator</phrase> 
    <link linkend="id-1_1_1_2_33_1_4-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
         <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_2_33_2-bb"><phrase role="identifier">count</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">iterator</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_1_1_2_33_3_1-bb"><phrase role="identifier">equal_range</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_1_1_2_33_3_2-bb"><phrase role="identifier">equal_range</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_2_34-bb">bucket interface</link></phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_2_34_1-bb"><phrase role="identifier">bucket_count</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_2_34_2-bb"><phrase role="identifier">max_bucket_count</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_2_34_3-bb"><phrase role="identifier">bucket_size</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_1_1_2_34_4-bb"><phrase role="identifier">bucket</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">local_iterator</phrase> <link linkend="id-1_1_1_2_34_5_1-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_1_1_2_34_5_2-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">local_iterator</phrase> <link linkend="id-1_1_1_2_34_6_1-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_1_1_2_34_6_2-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_1_1_2_34_7-bb"><phrase role="identifier">cbegin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_1_1_2_34_8-bb"><phrase role="identifier">cend</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_1_1_2_35-bb">hash policy</link></phrase>
  <phrase role="keyword">float</phrase> <link linkend="id-1_1_1_2_35_1-bb"><phrase role="identifier">load_factor</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">float</phrase> <link linkend="id-1_1_1_2_35_2-bb"><phrase role="identifier">max_load_factor</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_2_35_3-bb"><phrase role="identifier">max_load_factor</phrase></link><phrase role="special">(</phrase><phrase role="keyword">float</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_1_1_2_35_4-bb"><phrase role="identifier">rehash</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase><phrase role="special">;</phrase>

<phrase role="comment">// <link linkend="id-1_1_1_2_36-bb">Equality Comparisons</link></phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_multiset.operator=="><phrase role="keyword">operator</phrase><phrase role="special">==</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_multiset.operator!="><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

<phrase role="comment">// <link linkend="id-1_1_1_2_37-bb">swap</link></phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <link linkend="boost.unordered_multiset.swap"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
            <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></synopsis></refsynopsisdiv><refsect1><title>Description</title>
            <para>Based on chapter 23 of
              <ulink url="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2009/n2960.pdf">the working draft of the C++ standard [n2960]</ulink>.
              But without the updated rules for allocators.
            </para>
            <para><emphasis role="bold">Template Parameters</emphasis>
              <informaltable>
                <tgroup cols="2">
                  <tbody>
                    <row>
                      <entry><emphasis>Value</emphasis></entry>
                      <entry>Value must be Assignable and CopyConstructible</entry></row>
                    <row>
                      <entry><emphasis>Hash</emphasis></entry>
                      <entry>A unary function object type that acts a hash function for a <computeroutput>Value</computeroutput>. It takes a single argument of type <computeroutput>Value</computeroutput> and returns a value of type std::size_t.</entry></row>
                    <row>
                      <entry><emphasis>Pred</emphasis></entry>
                      <entry>A binary function object that implements an equivalence relation on values of type <computeroutput>Value</computeroutput>.
                        A binary function object that induces an equivalence relation on values of type Key.
                        It takes two arguments of type Key and returns a value of type bool.</entry></row>
                    <row>
                      <entry><emphasis>Alloc</emphasis></entry>
                      <entry>An allocator whose value type is the same as the container's value type.</entry></row></tbody></tgroup></informaltable></para>
            <para>The elements are organized into buckets. Keys with the same hash code are stored in the same bucket and elements with equivalent keys are stored next to each other.</para>
            <para>The number of buckets can be automatically increased by a call to insert, or as the result of calling rehash.</para>
          <refsect2><title><anchor id="boost.unordered_multisettypes"/><computeroutput>unordered_multiset</computeroutput> 
        public
       types</title><orderedlist><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multiset.size_type"/><phrase role="identifier">size_type</phrase><phrase role="special">;</phrase></para>
              <para>An unsigned integral type.</para>
              <para>size_type can represent any non-negative value of difference_type.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multiset.difference_type"/><phrase role="identifier">difference_type</phrase><phrase role="special">;</phrase></para>
              <para>A signed integral type.</para>
              <para>Is identical to the difference type of iterator and const_iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multiset.iterator"/><phrase role="identifier">iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator whose value type is value_type. </para>
              <para>The iterator category is at least a forward iterator.</para>
              <para>Convertible to const_iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multiset.const_iterator"/><phrase role="identifier">const_iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator whose value type is value_type. </para>
              <para>The iterator category is at least a forward iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multiset.local_iterator"/><phrase role="identifier">local_iterator</phrase><phrase role="special">;</phrase></para>
              <para>An iterator with the same value type, difference type and pointer and reference type as iterator.</para>
              <para>A local_iterator object can be used to iterate through a single bucket.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multiset.const_local_iterator"/><phrase role="identifier">const_local_iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator with the same value type, difference type and pointer and reference type as const_iterator.</para>
              <para>A const_local_iterator object can be used to iterate through a single bucket.</para>
            </listitem></orderedlist></refsect2><refsect2><title><anchor id="boost.unordered_multisetconstruct-copy-destruct"/><computeroutput>unordered_multiset</computeroutput> 
        public
       construct/copy/destruct</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">explicit</phrase> <anchor id="id-1_1_1_2_19-bb"/><phrase role="identifier">unordered_multiset</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                            <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hf <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                            <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                            <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container with at least n buckets, using hf as the hash function, eq as the key equality predicate, a as the allocator and a maximum load factor of 1.0.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Postconditions:</term><listitem><computeroutput><link linkend="id-1_1_1_2_29_2-bb">size</link>() == 0</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
  <anchor id="id-1_1_1_2_20-bb"/><phrase role="identifier">unordered_multiset</phrase><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase> f<phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase> l<phrase role="special">,</phrase> 
                     <phrase role="identifier">size_type</phrase> n <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                     <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hf <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                     <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                     <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container with at least n buckets, using hf as the hash function, eq as the key equality predicate, a as the allocator and a maximum load factor of 1.0 and inserts the elements from [f, l) into it.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_21-bb"/><phrase role="identifier">unordered_multiset</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The copy constructor. Copies the contained elements, hash function, predicate, maximum load factor and allocator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>value_type</computeroutput> is copy constructible</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_22-bb"/><phrase role="identifier">unordered_multiset</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The move constructor.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is emulated on compilers without rvalue references.</para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para>
                <computeroutput>value_type</computeroutput> is move constructible.
                (TODO: This is not actually required in this implementation).
              </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">explicit</phrase> <anchor id="id-1_1_1_2_23-bb"/><phrase role="identifier">unordered_multiset</phrase><phrase role="special">(</phrase><phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container, using allocator <computeroutput>a</computeroutput>.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_24-bb"/><phrase role="identifier">unordered_multiset</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> <phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an container, copying <computeroutput>x</computeroutput>'s contained elements, hash function, predicate, maximum load factor, but using allocator <computeroutput>a</computeroutput>.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_25-bb"/><phrase role="special">~</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>The destructor is applied to every element, and all memory is deallocated</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><para><literallayout class="monospaced"><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_1_1_2_26-bb"/><phrase role="keyword">operator</phrase><phrase role="special">=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The assignment operator. Copies the contained elements, hash function, predicate and maximum load factor but not the allocator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>
                On compilers without rvalue references, there is a single assignment
                operator with the signature <computeroutput>operator=(unordered_multiset)</computeroutput>
                in order to emulate move semantics.
              </para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para><computeroutput>value_type</computeroutput> is copy constructible</para></listitem></varlistentry></variablelist><para><literallayout class="monospaced"><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_1_1_2_27-bb"/><phrase role="keyword">operator</phrase><phrase role="special">=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The move assignment operator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>
                On compilers without rvalue references, there is a single assignment
                operator with the signature <computeroutput>operator=(unordered_multiset)</computeroutput>
                in order to emulate move semantics.
              </para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para>
                <computeroutput>value_type</computeroutput> is move constructible.
                (TODO: This is not actually required in this implementation).
              </para></listitem></varlistentry></variablelist><para><literallayout class="monospaced"><phrase role="identifier">allocator_type</phrase> <anchor id="id-1_1_1_2_28-bb"/><phrase role="identifier">get_allocator</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><refsect2><title><anchor id="id-1_1_1_2_29-bb"/><computeroutput>unordered_multiset</computeroutput> size and capacity</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">bool</phrase> <anchor id="id-1_1_1_2_29_1-bb"/><phrase role="identifier">empty</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput><link linkend="id-1_1_1_2_29_2-bb">size</link>() == 0</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_2_29_2-bb"/><phrase role="identifier">size</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput>std::distance(<link linkend="id-1_1_1_2_30_1-bb">begin</link>(), <link linkend="id-1_1_1_2_30_2-bb">end</link>())</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_2_29_3-bb"/><phrase role="identifier">max_size</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput><link linkend="id-1_1_1_2_29_2-bb">size</link>()</computeroutput> of the largest possible container.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_2_30-bb"/><computeroutput>unordered_multiset</computeroutput> iterators</title><orderedlist><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_30_1-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_30_1_1-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_2_30_1_2-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>An iterator referring to the first element of the container, or if the container is empty the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_30_2-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_30_2_1-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_2_30_2_2-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>An iterator which refers to the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_2_30_3-bb"/><phrase role="identifier">cbegin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>A constant iterator referring to the first element of the container, or if the container is empty the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_2_30_4-bb"/><phrase role="identifier">cend</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>A constant iterator which refers to the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_2_31-bb"/><computeroutput>unordered_multiset</computeroutput> modifiers</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_31_1-bb"/><phrase role="identifier">emplace</phrase><phrase role="special">(</phrase><phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase> args<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts an object, constructed with the arguments <computeroutput>args</computeroutput>, in the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to the inserted element.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para><para>If the compiler doesn't support variadic template arguments or rvalue
                      references, this is emulated for up to 10 arguments, with no support
                      for rvalue references or move semantics.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_31_2-bb"/><phrase role="identifier">emplace_hint</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> hint<phrase role="special">,</phrase> <phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase> args<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts an object, constructed with the arguments <computeroutput>args</computeroutput>, in the container.</para><para>hint is a suggestion to where the element should be inserted.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to the inserted element.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>The standard is fairly vague on the meaning of the hint. But the only practical way to use it, and the only way that Boost.Unordered supports is to point to an existing element with the same value. </para><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para><para>If the compiler doesn't support variadic template arguments or rvalue
                      references, this is emulated for up to 10 arguments, with no support
                      for rvalue references or move semantics.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_31_3-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> obj<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts obj in the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to the inserted element.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_31_4-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> hint<phrase role="special">,</phrase> <phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> obj<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts obj in the container.</para><para>hint is a suggestion to where the element should be inserted.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to the inserted element.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>The standard is fairly vague on the meaning of the hint. But the only practical way to use it, and the only way that Boost.Unordered supports is to point to an existing element with the same value. </para><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <anchor id="id-1_1_1_2_31_5-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase> first<phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase> last<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts a range of elements into the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>When inserting a single element, if an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_31_6-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The iterator following <computeroutput>position</computeroutput> before the erasure.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  When the number of elements is a lot smaller than the number of buckets
                  this function can be very inefficient as it has to search through empty
                  buckets for the next element, in order to return the iterator.
                  The method <link linkend="id-1_1_1_2_31_9-bb">quick_erase</link> is faster, but has yet
                  to be standardized.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_2_31_7-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase all elements with key equivalent to <computeroutput>k</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of elements erased.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_31_8-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> first<phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase> last<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erases the elements in the range from <computeroutput>first</computeroutput> to <computeroutput>last</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The iterator following the erased elements - i.e. <computeroutput>last</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_2_31_9-bb"/><phrase role="identifier">quick_erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  This method is faster than <link linkend="id-1_1_1_2_31_6-bb">erase</link> as
                  it doesn't have to find the next element in the container -
                  a potentially costly operation.
                </para><para>
                  As it hasn't been standardized, it's likely that this may
                  change in the future.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_2_31_10-bb"/><phrase role="identifier">erase_return_void</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  This method is now deprecated, use
                  quick_return instead. Although be
                  warned that as that isn't standardized yet, it could also
                  change.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_2_31_11-bb"/><phrase role="identifier">clear</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erases all elements in the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Postconditions:</term><listitem><para><computeroutput><link linkend="id-1_1_1_2_29_2-bb">size</link>() == 0</computeroutput></para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Never throws an exception.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_2_31_12-bb"/><phrase role="identifier">swap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>If the allocators are equal, doesn't throw an exception unless it is thrown by the copy constructor or copy assignment operator of <computeroutput>key_equal</computeroutput> or <computeroutput>hasher</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>For a discussion of the behavior when allocators aren't equal see
                  <link linkend="unordered.rationale.swapping_containers_with_unequal_allocators">the implementation details</link>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_2_32-bb"/><computeroutput>unordered_multiset</computeroutput> observers</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="identifier">hasher</phrase> <anchor id="id-1_1_1_2_32_1-bb"/><phrase role="identifier">hash_function</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>The container's hash function.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">key_equal</phrase> <anchor id="id-1_1_1_2_32_2-bb"/><phrase role="identifier">key_eq</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>The container's key equality predicate.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_2_33-bb"/><computeroutput>unordered_multiset</computeroutput> lookup</title><orderedlist><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_33_1-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_33_1_1-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_1_1_2_33_1_2-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">iterator</phrase> <anchor id="id-1_1_1_2_33_1_3-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hash<phrase role="special">,</phrase> 
                <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">const_iterator</phrase> 
  <anchor id="id-1_1_1_2_33_1_4-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hash<phrase role="special">,</phrase> 
       <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to an element with key equivalent to <computeroutput>k</computeroutput>, or <computeroutput>b.end()</computeroutput> if no such element exists.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                The templated overloads are a non-standard extensions which
                allows you to use a compatible hash function and equality
                predicate for a key of a different type in order to avoid
                an expensive type cast. In general, its use is not encouraged.
              </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_2_33_2-bb"/><phrase role="identifier">count</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of elements with key equivalent to <computeroutput>k</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_33_3-bb"/><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">iterator</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_1_1_2_33_3_1-bb"/><phrase role="identifier">equal_range</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_1_1_2_33_3_2-bb"/><phrase role="identifier">equal_range</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>A range containing all elements with key equivalent to <computeroutput>k</computeroutput>.
                  If the container doesn't container any such elements, returns
                  <computeroutput>std::make_pair(b.end(),b.end())</computeroutput>.
                  </para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_2_34-bb"/><computeroutput>unordered_multiset</computeroutput> bucket interface</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_2_34_1-bb"/><phrase role="identifier">bucket_count</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of buckets.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_2_34_2-bb"/><phrase role="identifier">max_bucket_count</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An upper bound on the number of buckets.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_2_34_3-bb"/><phrase role="identifier">bucket_size</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n &lt; <link linkend="id-1_1_1_2_34_1-bb">bucket_count</link>()</computeroutput></para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>The number of elements in bucket <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_1_1_2_34_4-bb"/><phrase role="identifier">bucket</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The index of the bucket which would contain an element with key <computeroutput>k</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Postconditions:</term><listitem><para>The return value is less than <computeroutput>bucket_count()</computeroutput></para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_34_5-bb"/><phrase role="identifier">local_iterator</phrase> <anchor id="id-1_1_1_2_34_5_1-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_1_1_2_34_5_2-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A local iterator pointing the first element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_1_1_2_34_6-bb"/><phrase role="identifier">local_iterator</phrase> <anchor id="id-1_1_1_2_34_6_1-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_1_1_2_34_6_2-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A local iterator pointing the 'one past the end' element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_1_1_2_34_7-bb"/><phrase role="identifier">cbegin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A constant local iterator pointing the first element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_1_1_2_34_8-bb"/><phrase role="identifier">cend</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A constant local iterator pointing the 'one past the end' element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_2_35-bb"/><computeroutput>unordered_multiset</computeroutput> hash policy</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">float</phrase> <anchor id="id-1_1_1_2_35_1-bb"/><phrase role="identifier">load_factor</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The average number of elements per bucket.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">float</phrase> <anchor id="id-1_1_1_2_35_2-bb"/><phrase role="identifier">max_load_factor</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>Returns the current maximum load factor.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_2_35_3-bb"/><phrase role="identifier">max_load_factor</phrase><phrase role="special">(</phrase><phrase role="keyword">float</phrase> z<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Effects:</term><listitem><para>Changes the container's maximum load factor, using <computeroutput>z</computeroutput> as a hint.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_1_1_2_35_4-bb"/><phrase role="identifier">rehash</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Changes the number of buckets so that there at least <computeroutput>n</computeroutput> buckets, and so that the load factor is less than the maximum load factor.</para><para>Invalidates iterators, and changes the order of elements. Pointers and references to elements are not invalidated.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>The function has no effect if an exception is thrown, unless it is thrown by the container's hash function or comparison function.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_2_36-bb"/><computeroutput>unordered_multiset</computeroutput> Equality Comparisons</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <anchor id="boost.unordered_multiset.operator=="/><phrase role="keyword">operator</phrase><phrase role="special">==</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is a boost extension.</para><para>Behavior is undefined if the two containers don't have
                    equivalent equality predicates.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <anchor id="boost.unordered_multiset.operator!="/><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is a boost extension.</para><para>Behavior is undefined if the two containers don't have
                    equivalent equality predicates.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_1_1_2_37-bb"/><computeroutput>unordered_multiset</computeroutput> swap</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Value<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <anchor id="boost.unordered_multiset.swap"/><phrase role="identifier">swap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
            <phrase role="identifier">unordered_multiset</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Value</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Effects:</term><listitem><para><computeroutput>x.swap(y)</computeroutput></para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If the allocators are equal, doesn't throw an exception unless it is thrown by the copy constructor or copy assignment operator of <computeroutput>Hash</computeroutput> or <computeroutput>Pred</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>For a discussion of the behavior when allocators aren't equal see
                  <link linkend="unordered.rationale.swapping_containers_with_unequal_allocators">the implementation details</link>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2></refsect1></refentry>
    </section>
    <section id="header.boost.unordered_map_hpp"><title>Header &lt;<ulink url="../../boost/unordered_map.hpp">boost/unordered_map.hpp</ulink>&gt;</title><synopsis xmlns:xi="http://www.w3.org/2001/XInclude"><phrase role="keyword">namespace</phrase> <phrase role="identifier">boost</phrase> <phrase role="special">{</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash <phrase role="special">=</phrase> <phrase role="identifier">boost</phrase><phrase role="special">::</phrase><phrase role="identifier">hash</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Pred <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">equal_to</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">allocator</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase> <phrase role="keyword">const</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> 
    <phrase role="keyword">class</phrase> <link linkend="boost.unordered_map">unordered_map</link><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_map.operator=="><phrase role="keyword">operator</phrase><phrase role="special">==</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                    <phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_map.operator!="><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                    <phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">void</phrase> <link linkend="boost.unordered_map.swap"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
              <phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash <phrase role="special">=</phrase> <phrase role="identifier">boost</phrase><phrase role="special">::</phrase><phrase role="identifier">hash</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Pred <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">equal_to</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">allocator</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase> <phrase role="keyword">const</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> 
    <phrase role="keyword">class</phrase> <link linkend="boost.unordered_multimap">unordered_multimap</link><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_multimap.operator=="><phrase role="keyword">operator</phrase><phrase role="special">==</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                    <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_multimap.operator!="><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                    <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
    <phrase role="keyword">void</phrase> <link linkend="boost.unordered_multimap.swap"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
              <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase></synopsis>
      <refentry xmlns:xi="http://www.w3.org/2001/XInclude" id="boost.unordered_map"><refmeta><refentrytitle>Class template unordered_map</refentrytitle><manvolnum>3</manvolnum></refmeta><refnamediv><refname>boost::unordered_map</refname><refpurpose>
            An unordered associative container that associates unique keys with another value.
          </refpurpose></refnamediv><refsynopsisdiv><synopsis><phrase role="comment">// In header: &lt;<link linkend="header.boost.unordered_map_hpp">boost/unordered_map.hpp</link>&gt;

</phrase><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash <phrase role="special">=</phrase> <phrase role="identifier">boost</phrase><phrase role="special">::</phrase><phrase role="identifier">hash</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Pred <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">equal_to</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">allocator</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase> <phrase role="keyword">const</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> 
<phrase role="keyword">class</phrase> <link linkend="boost.unordered_map">unordered_map</link> <phrase role="special">{</phrase>
<phrase role="keyword">public</phrase><phrase role="special">:</phrase>
  <phrase role="comment">// <link linkend="boost.unordered_maptypes">types</link></phrase>
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Key</phrase>                                      <anchor id="boost.unordered_map.key_type"/><phrase role="identifier">key_type</phrase><phrase role="special">;</phrase>            
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase> <phrase role="keyword">const</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">&gt;</phrase>             <anchor id="boost.unordered_map.value_type"/><phrase role="identifier">value_type</phrase><phrase role="special">;</phrase>          
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Mapped</phrase>                                   <anchor id="boost.unordered_map.mapped_type"/><phrase role="identifier">mapped_type</phrase><phrase role="special">;</phrase>         
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Hash</phrase>                                     <anchor id="boost.unordered_map.hasher"/><phrase role="identifier">hasher</phrase><phrase role="special">;</phrase>              
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Pred</phrase>                                     <anchor id="boost.unordered_map.key_equal"/><phrase role="identifier">key_equal</phrase><phrase role="special">;</phrase>           
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Alloc</phrase>                                    <anchor id="boost.unordered_map.allocator_type"/><phrase role="identifier">allocator_type</phrase><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">pointer</phrase>         <anchor id="boost.unordered_map.pointer"/><phrase role="identifier">pointer</phrase><phrase role="special">;</phrase>             
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">const_pointer</phrase>   <anchor id="boost.unordered_map.const_pointer"/><phrase role="identifier">const_pointer</phrase><phrase role="special">;</phrase>       
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">reference</phrase>       <anchor id="boost.unordered_map.reference"/><phrase role="identifier">reference</phrase><phrase role="special">;</phrase>             <phrase role="comment">// lvalue of value_type.</phrase>
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">const_reference</phrase> <anchor id="boost.unordered_map.const_reference"/><phrase role="identifier">const_reference</phrase><phrase role="special">;</phrase>       <phrase role="comment">// const lvalue of value_type.</phrase>
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_map.size_type"><phrase role="identifier">size_type</phrase></link><phrase role="special">;</phrase>           
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_map.difference_type"><phrase role="identifier">difference_type</phrase></link><phrase role="special">;</phrase>     
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_map.iterator"><phrase role="identifier">iterator</phrase></link><phrase role="special">;</phrase>            
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_map.const_iterator"><phrase role="identifier">const_iterator</phrase></link><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_map.local_iterator"><phrase role="identifier">local_iterator</phrase></link><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_map.const_local_iterator"><phrase role="identifier">const_local_iterator</phrase></link><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="boost.unordered_mapconstruct-copy-destruct">construct/copy/destruct</link></phrase>
  <phrase role="keyword">explicit</phrase> <link linkend="id-1_2_1_1_20-bb"><phrase role="identifier">unordered_map</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                         <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                         <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                         <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
    <link linkend="id-1_2_1_1_21-bb"><phrase role="identifier">unordered_map</phrase></link><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">size_type</phrase> <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                  <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_2_1_1_22-bb"><phrase role="identifier">unordered_map</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_2_1_1_23-bb"><phrase role="identifier">unordered_map</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">explicit</phrase> <link linkend="id-1_2_1_1_24-bb"><phrase role="identifier">unordered_map</phrase></link><phrase role="special">(</phrase><phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_2_1_1_25-bb"><phrase role="identifier">unordered_map</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_2_1_1_26-bb"><phrase role="special">~</phrase><phrase role="identifier">unordered_map</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">unordered_map</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_2_1_1_27-bb"><phrase role="keyword">operator</phrase><phrase role="special">=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">unordered_map</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_2_1_1_28-bb"><phrase role="keyword">operator</phrase><phrase role="special">=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">allocator_type</phrase> <link linkend="id-1_2_1_1_29-bb"><phrase role="identifier">get_allocator</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_1_30-bb">size and capacity</link></phrase>
  <phrase role="keyword">bool</phrase> <link linkend="id-1_2_1_1_30_1-bb"><phrase role="identifier">empty</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_1_30_2-bb"><phrase role="identifier">size</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_1_30_3-bb"><phrase role="identifier">max_size</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_1_31-bb">iterators</link></phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_1_31_1_1-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_1_31_1_2-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_1_31_2_1-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_1_31_2_2-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_1_31_3-bb"><phrase role="identifier">cbegin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_1_31_4-bb"><phrase role="identifier">cend</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_1_32-bb">modifiers</link></phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="keyword">bool</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_2_1_1_32_1-bb"><phrase role="identifier">emplace</phrase></link><phrase role="special">(</phrase><phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_1_32_2-bb"><phrase role="identifier">emplace_hint</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="keyword">bool</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_2_1_1_32_3-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_1_32_4-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_1_32_5-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_1_32_6-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_1_32_7-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_1_32_8-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_1_32_9-bb"><phrase role="identifier">quick_erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_1_32_10-bb"><phrase role="identifier">erase_return_void</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_1_32_11-bb"><phrase role="identifier">clear</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_1_32_12-bb"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_1_33-bb">observers</link></phrase>
  <phrase role="identifier">hasher</phrase> <link linkend="id-1_2_1_1_33_1-bb"><phrase role="identifier">hash_function</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">key_equal</phrase> <link linkend="id-1_2_1_1_33_2-bb"><phrase role="identifier">key_eq</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_1_34-bb">lookup</link></phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_1_34_1_1-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_1_34_1_2-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
    <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_1_34_1_3-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
    <phrase role="identifier">const_iterator</phrase> 
    <link linkend="id-1_2_1_1_34_1_4-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
         <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_1_34_2-bb"><phrase role="identifier">count</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">iterator</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_2_1_1_34_3_1-bb"><phrase role="identifier">equal_range</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_2_1_1_34_3_2-bb"><phrase role="identifier">equal_range</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">mapped_type</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_2_1_1_34_4-bb"><phrase role="keyword">operator</phrase><phrase role="special">[</phrase><phrase role="special">]</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">Mapped</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_2_1_1_34_5_1-bb"><phrase role="identifier">at</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">Mapped</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_2_1_1_34_5_2-bb"><phrase role="identifier">at</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_1_35-bb">bucket interface</link></phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_1_35_1-bb"><phrase role="identifier">bucket_count</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_1_35_2-bb"><phrase role="identifier">max_bucket_count</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_1_35_3-bb"><phrase role="identifier">bucket_size</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_1_35_4-bb"><phrase role="identifier">bucket</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">local_iterator</phrase> <link linkend="id-1_2_1_1_35_5_1-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_2_1_1_35_5_2-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">local_iterator</phrase> <link linkend="id-1_2_1_1_35_6_1-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_2_1_1_35_6_2-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_2_1_1_35_7-bb"><phrase role="identifier">cbegin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_2_1_1_35_8-bb"><phrase role="identifier">cend</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_1_36-bb">hash policy</link></phrase>
  <phrase role="keyword">float</phrase> <link linkend="id-1_2_1_1_36_1-bb"><phrase role="identifier">load_factor</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">float</phrase> <link linkend="id-1_2_1_1_36_2-bb"><phrase role="identifier">max_load_factor</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_1_36_3-bb"><phrase role="identifier">max_load_factor</phrase></link><phrase role="special">(</phrase><phrase role="keyword">float</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_1_36_4-bb"><phrase role="identifier">rehash</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase><phrase role="special">;</phrase>

<phrase role="comment">// <link linkend="id-1_2_1_1_37-bb">Equality Comparisons</link></phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_map.operator=="><phrase role="keyword">operator</phrase><phrase role="special">==</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_map.operator!="><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

<phrase role="comment">// <link linkend="id-1_2_1_1_38-bb">swap</link></phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <link linkend="boost.unordered_map.swap"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
            <phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></synopsis></refsynopsisdiv><refsect1><title>Description</title>
            <para>Based on chapter 23 of
              <ulink url="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2009/n2960.pdf">the working draft of the C++ standard [n2960]</ulink>.
              But without the updated rules for allocators.
            </para>
            <para><emphasis role="bold">Template Parameters</emphasis>
              <informaltable>
                <tgroup cols="2">
                  <tbody>
                    <row>
                      <entry><emphasis>Key</emphasis></entry>
                      <entry>Key must be Assignable and CopyConstructible.</entry></row>
                    <row>
                      <entry><emphasis>Mapped</emphasis></entry>
                      <entry>Mapped must be CopyConstructible</entry></row>
                    <row>
                      <entry><emphasis>Hash</emphasis></entry>
                      <entry>A unary function object type that acts a hash function for a <computeroutput>Key</computeroutput>. It takes a single argument of type <computeroutput>Key</computeroutput> and returns a value of type std::size_t.</entry></row>
                    <row>
                      <entry><emphasis>Pred</emphasis></entry>
                      <entry>A binary function object that implements an equivalence relation on values of type <computeroutput>Key</computeroutput>.
                        A binary function object that induces an equivalence relation on values of type Key.
                        It takes two arguments of type Key and returns a value of type bool.</entry></row>
                    <row>
                      <entry><emphasis>Alloc</emphasis></entry>
                      <entry>An allocator whose value type is the same as the container's value type.</entry></row></tbody></tgroup></informaltable></para>
            <para>The elements are organized into buckets. Keys with the same hash code are stored in the same bucket.</para>
            <para>The number of buckets can be automatically increased by a call to insert, or as the result of calling rehash.</para>
          <refsect2><title><anchor id="boost.unordered_maptypes"/><computeroutput>unordered_map</computeroutput> 
        public
       types</title><orderedlist><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_map.size_type"/><phrase role="identifier">size_type</phrase><phrase role="special">;</phrase></para>
              <para>An unsigned integral type.</para>
              <para>size_type can represent any non-negative value of difference_type.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_map.difference_type"/><phrase role="identifier">difference_type</phrase><phrase role="special">;</phrase></para>
              <para>A signed integral type.</para>
              <para>Is identical to the difference type of iterator and const_iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_map.iterator"/><phrase role="identifier">iterator</phrase><phrase role="special">;</phrase></para>
              <para>A iterator whose value type is value_type. </para>
              <para>The iterator category is at least a forward iterator.</para>
              <para>Convertible to const_iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_map.const_iterator"/><phrase role="identifier">const_iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator whose value type is value_type. </para>
              <para>The iterator category is at least a forward iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_map.local_iterator"/><phrase role="identifier">local_iterator</phrase><phrase role="special">;</phrase></para>
              <para>An iterator with the same value type, difference type and pointer and reference type as iterator.</para>
              <para>A local_iterator object can be used to iterate through a single bucket.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_map.const_local_iterator"/><phrase role="identifier">const_local_iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator with the same value type, difference type and pointer and reference type as const_iterator.</para>
              <para>A const_local_iterator object can be used to iterate through a single bucket.</para>
            </listitem></orderedlist></refsect2><refsect2><title><anchor id="boost.unordered_mapconstruct-copy-destruct"/><computeroutput>unordered_map</computeroutput> 
        public
       construct/copy/destruct</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">explicit</phrase> <anchor id="id-1_2_1_1_20-bb"/><phrase role="identifier">unordered_map</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                       <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hf <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container with at least n buckets, using hf as the hash function, eq as the key equality predicate, a as the allocator and a maximum load factor of 1.0.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Postconditions:</term><listitem><computeroutput><link linkend="id-1_2_1_1_30_2-bb">size</link>() == 0</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
  <anchor id="id-1_2_1_1_21-bb"/><phrase role="identifier">unordered_map</phrase><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase> f<phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase> l<phrase role="special">,</phrase> 
                <phrase role="identifier">size_type</phrase> n <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hf <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container with at least n buckets, using hf as the hash function, eq as the key equality predicate, a as the allocator and a maximum load factor of 1.0 and inserts the elements from [f, l) into it.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_22-bb"/><phrase role="identifier">unordered_map</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The copy constructor. Copies the contained elements, hash function, predicate, maximum load factor and allocator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>value_type</computeroutput> is copy constructible</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_23-bb"/><phrase role="identifier">unordered_map</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The move constructor.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is emulated on compilers without rvalue references.</para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para>
                <computeroutput>value_type</computeroutput> is move constructible.
                (TODO: This is not actually required in this implementation).
              </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">explicit</phrase> <anchor id="id-1_2_1_1_24-bb"/><phrase role="identifier">unordered_map</phrase><phrase role="special">(</phrase><phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container, using allocator <computeroutput>a</computeroutput>.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_25-bb"/><phrase role="identifier">unordered_map</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> <phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an container, copying <computeroutput>x</computeroutput>'s contained elements, hash function, predicate, maximum load factor, but using allocator <computeroutput>a</computeroutput>.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_26-bb"/><phrase role="special">~</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>The destructor is applied to every element, and all memory is deallocated</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><para><literallayout class="monospaced"><phrase role="identifier">unordered_map</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_2_1_1_27-bb"/><phrase role="keyword">operator</phrase><phrase role="special">=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The assignment operator. Copies the contained elements, hash function, predicate and maximum load factor but not the allocator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>
                On compilers without rvalue references, there is a single assignment
                operator with the signature <computeroutput>operator=(unordered_map)</computeroutput>
                in order to emulate move semantics.
              </para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para><computeroutput>value_type</computeroutput> is copy constructible</para></listitem></varlistentry></variablelist><para><literallayout class="monospaced"><phrase role="identifier">unordered_map</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_2_1_1_28-bb"/><phrase role="keyword">operator</phrase><phrase role="special">=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The move assignment operator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>
                On compilers without rvalue references, there is a single assignment
                operator with the signature <computeroutput>operator=(unordered_map)</computeroutput>
                in order to emulate move semantics.
              </para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para>
                <computeroutput>value_type</computeroutput> is move constructible.
                (TODO: This is not actually required in this implementation).
              </para></listitem></varlistentry></variablelist><para><literallayout class="monospaced"><phrase role="identifier">allocator_type</phrase> <anchor id="id-1_2_1_1_29-bb"/><phrase role="identifier">get_allocator</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><refsect2><title><anchor id="id-1_2_1_1_30-bb"/><computeroutput>unordered_map</computeroutput> size and capacity</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">bool</phrase> <anchor id="id-1_2_1_1_30_1-bb"/><phrase role="identifier">empty</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput><link linkend="id-1_2_1_1_30_2-bb">size</link>() == 0</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_1_30_2-bb"/><phrase role="identifier">size</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput>std::distance(<link linkend="id-1_2_1_1_31_1-bb">begin</link>(), <link linkend="id-1_2_1_1_31_2-bb">end</link>())</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_1_30_3-bb"/><phrase role="identifier">max_size</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput><link linkend="id-1_2_1_1_30_2-bb">size</link>()</computeroutput> of the largest possible container.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_1_31-bb"/><computeroutput>unordered_map</computeroutput> iterators</title><orderedlist><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_31_1-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_1_31_1_1-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_1_31_1_2-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>An iterator referring to the first element of the container, or if the container is empty the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_31_2-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_1_31_2_1-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_1_31_2_2-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>An iterator which refers to the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_1_31_3-bb"/><phrase role="identifier">cbegin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>A constant iterator referring to the first element of the container, or if the container is empty the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_1_31_4-bb"/><phrase role="identifier">cend</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>A constant iterator which refers to the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_1_32-bb"/><computeroutput>unordered_map</computeroutput> modifiers</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="keyword">bool</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_2_1_1_32_1-bb"/><phrase role="identifier">emplace</phrase><phrase role="special">(</phrase><phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase> args<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts an object, constructed with the arguments <computeroutput>args</computeroutput>, in the container if and only if there is no element in the container with an equivalent key.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The bool component of the return type is true if an insert took place.</para><para>If an insert took place, then the iterator points to the newly inserted element. Otherwise, it points to the element with equivalent key.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para><para>If the compiler doesn't support variadic template arguments or rvalue
                      references, this is emulated for up to 10 arguments, with no support
                      for rvalue references or move semantics.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_1_32_2-bb"/><phrase role="identifier">emplace_hint</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> hint<phrase role="special">,</phrase> <phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase> args<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts an object, constructed with the arguments <computeroutput>args</computeroutput>, in the container if and only if there is no element in the container with an equivalent key.</para><para>hint is a suggestion to where the element should be inserted.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>If an insert took place, then the iterator points to the newly inserted element. Otherwise, it points to the element with equivalent key.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>The standard is fairly vague on the meaning of the hint. But the only practical way to use it, and the only way that Boost.Unordered supports is to point to an existing element with the same key. </para><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para><para>If the compiler doesn't support variadic template arguments or rvalue
                      references, this is emulated for up to 10 arguments, with no support
                      for rvalue references or move semantics.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="keyword">bool</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_2_1_1_32_3-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> obj<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts obj in the container if and only if there is no element in the container with an equivalent key.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The bool component of the return type is true if an insert took place.</para><para>If an insert took place, then the iterator points to the newly inserted element. Otherwise, it points to the element with equivalent key.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_1_32_4-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> hint<phrase role="special">,</phrase> <phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> obj<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts obj in the container if and only if there is no element in the container with an equivalent key.</para><para>hint is a suggestion to where the element should be inserted.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>If an insert took place, then the iterator points to the newly inserted element. Otherwise, it points to the element with equivalent key.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>The standard is fairly vague on the meaning of the hint. But the only practical way to use it, and the only way that Boost.Unordered supports is to point to an existing element with the same key. </para><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <anchor id="id-1_2_1_1_32_5-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase> first<phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase> last<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts a range of elements into the container. Elements are inserted if and only if there is no element in the container with an equivalent key.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>When inserting a single element, if an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_1_32_6-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The iterator following <computeroutput>position</computeroutput> before the erasure.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  When the number of elements is a lot smaller than the number of buckets
                  this function can be very inefficient as it has to search through empty
                  buckets for the next element, in order to return the iterator.
                  The method <link linkend="id-1_2_1_1_32_9-bb">quick_erase</link> is faster, but has yet
                  to be standardized.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_1_32_7-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase all elements with key equivalent to <computeroutput>k</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of elements erased.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_1_32_8-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> first<phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase> last<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erases the elements in the range from <computeroutput>first</computeroutput> to <computeroutput>last</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The iterator following the erased elements - i.e. <computeroutput>last</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_1_32_9-bb"/><phrase role="identifier">quick_erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  This method is faster than <link linkend="id-1_2_1_1_32_6-bb">erase</link> as
                  it doesn't have to find the next element in the container -
                  a potentially costly operation.
                </para><para>
                  As it hasn't been standardized, it's likely that this may
                  change in the future.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_1_32_10-bb"/><phrase role="identifier">erase_return_void</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  This method is now deprecated, use
                  quick_return instead. Although be
                  warned that as that isn't standardized yet, it could also
                  change.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_1_32_11-bb"/><phrase role="identifier">clear</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erases all elements in the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Postconditions:</term><listitem><para><computeroutput><link linkend="id-1_2_1_1_30_2-bb">size</link>() == 0</computeroutput></para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Never throws an exception.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_1_32_12-bb"/><phrase role="identifier">swap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>If the allocators are equal, doesn't throw an exception unless it is thrown by the copy constructor or copy assignment operator of <computeroutput>key_equal</computeroutput> or <computeroutput>hasher</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>For a discussion of the behavior when allocators aren't equal see
                  <link linkend="unordered.rationale.swapping_containers_with_unequal_allocators">the implementation details</link>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_1_33-bb"/><computeroutput>unordered_map</computeroutput> observers</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="identifier">hasher</phrase> <anchor id="id-1_2_1_1_33_1-bb"/><phrase role="identifier">hash_function</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>The container's hash function.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">key_equal</phrase> <anchor id="id-1_2_1_1_33_2-bb"/><phrase role="identifier">key_eq</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>The container's key equality predicate.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_1_34-bb"/><computeroutput>unordered_map</computeroutput> lookup</title><orderedlist><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_34_1-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_1_34_1_1-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_1_34_1_2-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_1_34_1_3-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hash<phrase role="special">,</phrase> 
                <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">const_iterator</phrase> 
  <anchor id="id-1_2_1_1_34_1_4-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hash<phrase role="special">,</phrase> 
       <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to an element with key equivalent to <computeroutput>k</computeroutput>, or <computeroutput>b.end()</computeroutput> if no such element exists.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                The templated overloads are a non-standard extensions which
                allows you to use a compatible hash function and equality
                predicate for a key of a different type in order to avoid
                an expensive type cast. In general, its use is not encouraged.
              </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_1_34_2-bb"/><phrase role="identifier">count</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of elements with key equivalent to <computeroutput>k</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_34_3-bb"/><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">iterator</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_2_1_1_34_3_1-bb"/><phrase role="identifier">equal_range</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_2_1_1_34_3_2-bb"/><phrase role="identifier">equal_range</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>A range containing all elements with key equivalent to <computeroutput>k</computeroutput>.
                  If the container doesn't container any such elements, returns
                  <computeroutput>std::make_pair(b.end(),b.end())</computeroutput>.
                  </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">mapped_type</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_2_1_1_34_4-bb"/><phrase role="keyword">operator</phrase><phrase role="special">[</phrase><phrase role="special">]</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Effects:</term><listitem><para>If the container does not already contain an elements with a key equivalent to <computeroutput>k</computeroutput>, inserts the value <computeroutput>std::pair&lt;key_type const, mapped_type&gt;(k, mapped_type())</computeroutput></para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A reference to <computeroutput>x.second</computeroutput> where x is the element already in the container, or the newly inserted element with a key equivalent to <computeroutput>k</computeroutput></para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_34_5-bb"/><phrase role="identifier">Mapped</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_2_1_1_34_5_1-bb"/><phrase role="identifier">at</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">Mapped</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_2_1_1_34_5_2-bb"/><phrase role="identifier">at</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>A reference to <computeroutput>x.second</computeroutput> where <computeroutput>x</computeroutput> is the (unique) element whose key is equivalent to <computeroutput>k</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>An exception object of type <computeroutput>std::out_of_range</computeroutput> if no such element is present.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>This is not specified in the draft standard, but that is probably an oversight. The issue has been raised in
                  <ulink url="http://groups.google.com/group/comp.std.c++/browse_thread/thread/ab7c22a868fd370b">comp.std.c++</ulink>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_1_35-bb"/><computeroutput>unordered_map</computeroutput> bucket interface</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_1_35_1-bb"/><phrase role="identifier">bucket_count</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of buckets.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_1_35_2-bb"/><phrase role="identifier">max_bucket_count</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An upper bound on the number of buckets.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_1_35_3-bb"/><phrase role="identifier">bucket_size</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n &lt; <link linkend="id-1_2_1_1_35_1-bb">bucket_count</link>()</computeroutput></para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>The number of elements in bucket <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_1_35_4-bb"/><phrase role="identifier">bucket</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The index of the bucket which would contain an element with key <computeroutput>k</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Postconditions:</term><listitem><para>The return value is less than <computeroutput>bucket_count()</computeroutput></para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_35_5-bb"/><phrase role="identifier">local_iterator</phrase> <anchor id="id-1_2_1_1_35_5_1-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_2_1_1_35_5_2-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A local iterator pointing the first element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_1_35_6-bb"/><phrase role="identifier">local_iterator</phrase> <anchor id="id-1_2_1_1_35_6_1-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_2_1_1_35_6_2-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A local iterator pointing the 'one past the end' element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_2_1_1_35_7-bb"/><phrase role="identifier">cbegin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A constant local iterator pointing the first element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_2_1_1_35_8-bb"/><phrase role="identifier">cend</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A constant local iterator pointing the 'one past the end' element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_1_36-bb"/><computeroutput>unordered_map</computeroutput> hash policy</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">float</phrase> <anchor id="id-1_2_1_1_36_1-bb"/><phrase role="identifier">load_factor</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The average number of elements per bucket.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">float</phrase> <anchor id="id-1_2_1_1_36_2-bb"/><phrase role="identifier">max_load_factor</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>Returns the current maximum load factor.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_1_36_3-bb"/><phrase role="identifier">max_load_factor</phrase><phrase role="special">(</phrase><phrase role="keyword">float</phrase> z<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Effects:</term><listitem><para>Changes the container's maximum load factor, using <computeroutput>z</computeroutput> as a hint.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_1_36_4-bb"/><phrase role="identifier">rehash</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Changes the number of buckets so that there at least <computeroutput>n</computeroutput> buckets, and so that the load factor is less than the maximum load factor.</para><para>Invalidates iterators, and changes the order of elements. Pointers and references to elements are not invalidated.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>The function has no effect if an exception is thrown, unless it is thrown by the container's hash function or comparison function.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_1_37-bb"/><computeroutput>unordered_map</computeroutput> Equality Comparisons</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <anchor id="boost.unordered_map.operator=="/><phrase role="keyword">operator</phrase><phrase role="special">==</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is a boost extension.</para><para>Behavior is undefined if the two containers don't have
                    equivalent equality predicates.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <anchor id="boost.unordered_map.operator!="/><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is a boost extension.</para><para>Behavior is undefined if the two containers don't have
                    equivalent equality predicates.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_1_38-bb"/><computeroutput>unordered_map</computeroutput> swap</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <anchor id="boost.unordered_map.swap"/><phrase role="identifier">swap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
            <phrase role="identifier">unordered_map</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Effects:</term><listitem><para><computeroutput>x.swap(y)</computeroutput></para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If the allocators are equal, doesn't throw an exception unless it is thrown by the copy constructor or copy assignment operator of <computeroutput>Hash</computeroutput> or <computeroutput>Pred</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>For a discussion of the behavior when allocators aren't equal see
                  <link linkend="unordered.rationale.swapping_containers_with_unequal_allocators">the implementation details</link>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2></refsect1></refentry><refentry xmlns:xi="http://www.w3.org/2001/XInclude" id="boost.unordered_multimap"><refmeta><refentrytitle>Class template unordered_multimap</refentrytitle><manvolnum>3</manvolnum></refmeta><refnamediv><refname>boost::unordered_multimap</refname><refpurpose>
            An unordered associative container that associates  keys with another value. The same key can be stored multiple times.
          </refpurpose></refnamediv><refsynopsisdiv><synopsis><phrase role="comment">// In header: &lt;<link linkend="header.boost.unordered_map_hpp">boost/unordered_map.hpp</link>&gt;

</phrase><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash <phrase role="special">=</phrase> <phrase role="identifier">boost</phrase><phrase role="special">::</phrase><phrase role="identifier">hash</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Pred <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">equal_to</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">&gt;</phrase><phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc <phrase role="special">=</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">allocator</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase> <phrase role="keyword">const</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> <phrase role="special">&gt;</phrase> 
<phrase role="keyword">class</phrase> <link linkend="boost.unordered_multimap">unordered_multimap</link> <phrase role="special">{</phrase>
<phrase role="keyword">public</phrase><phrase role="special">:</phrase>
  <phrase role="comment">// <link linkend="boost.unordered_multimaptypes">types</link></phrase>
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Key</phrase>                                      <anchor id="boost.unordered_multimap.key_type"/><phrase role="identifier">key_type</phrase><phrase role="special">;</phrase>            
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase> <phrase role="keyword">const</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">&gt;</phrase>             <anchor id="boost.unordered_multimap.value_type"/><phrase role="identifier">value_type</phrase><phrase role="special">;</phrase>          
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Mapped</phrase>                                   <anchor id="boost.unordered_multimap.mapped_type"/><phrase role="identifier">mapped_type</phrase><phrase role="special">;</phrase>         
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Hash</phrase>                                     <anchor id="boost.unordered_multimap.hasher"/><phrase role="identifier">hasher</phrase><phrase role="special">;</phrase>              
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Pred</phrase>                                     <anchor id="boost.unordered_multimap.key_equal"/><phrase role="identifier">key_equal</phrase><phrase role="special">;</phrase>           
  <phrase role="keyword">typedef</phrase> <phrase role="identifier">Alloc</phrase>                                    <anchor id="boost.unordered_multimap.allocator_type"/><phrase role="identifier">allocator_type</phrase><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">pointer</phrase>         <anchor id="boost.unordered_multimap.pointer"/><phrase role="identifier">pointer</phrase><phrase role="special">;</phrase>             
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">const_pointer</phrase>   <anchor id="boost.unordered_multimap.const_pointer"/><phrase role="identifier">const_pointer</phrase><phrase role="special">;</phrase>       
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">reference</phrase>       <anchor id="boost.unordered_multimap.reference"/><phrase role="identifier">reference</phrase><phrase role="special">;</phrase>             <phrase role="comment">// lvalue of value_type.</phrase>
  <phrase role="keyword">typedef</phrase> <phrase role="keyword">typename</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">::</phrase><phrase role="identifier">const_reference</phrase> <anchor id="boost.unordered_multimap.const_reference"/><phrase role="identifier">const_reference</phrase><phrase role="special">;</phrase>       <phrase role="comment">// const lvalue of value_type.</phrase>
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multimap.size_type"><phrase role="identifier">size_type</phrase></link><phrase role="special">;</phrase>           
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multimap.difference_type"><phrase role="identifier">difference_type</phrase></link><phrase role="special">;</phrase>     
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multimap.iterator"><phrase role="identifier">iterator</phrase></link><phrase role="special">;</phrase>            
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multimap.const_iterator"><phrase role="identifier">const_iterator</phrase></link><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multimap.local_iterator"><phrase role="identifier">local_iterator</phrase></link><phrase role="special">;</phrase>      
  <phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis>                   <link linkend="boost.unordered_multimap.const_local_iterator"><phrase role="identifier">const_local_iterator</phrase></link><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="boost.unordered_multimapconstruct-copy-destruct">construct/copy/destruct</link></phrase>
  <phrase role="keyword">explicit</phrase> <link linkend="id-1_2_1_2_20-bb"><phrase role="identifier">unordered_multimap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                              <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                              <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                              <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
    <link linkend="id-1_2_1_2_21-bb"><phrase role="identifier">unordered_multimap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">size_type</phrase> <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                       <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                       <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_2_1_2_22-bb"><phrase role="identifier">unordered_multimap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_2_1_2_23-bb"><phrase role="identifier">unordered_multimap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">explicit</phrase> <link linkend="id-1_2_1_2_24-bb"><phrase role="identifier">unordered_multimap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_2_1_2_25-bb"><phrase role="identifier">unordered_multimap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <link linkend="id-1_2_1_2_26-bb"><phrase role="special">~</phrase><phrase role="identifier">unordered_multimap</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_2_1_2_27-bb"><phrase role="keyword">operator</phrase><phrase role="special">=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&amp;</phrase> <link linkend="id-1_2_1_2_28-bb"><phrase role="keyword">operator</phrase><phrase role="special">=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">allocator_type</phrase> <link linkend="id-1_2_1_2_29-bb"><phrase role="identifier">get_allocator</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_2_30-bb">size and capacity</link></phrase>
  <phrase role="keyword">bool</phrase> <link linkend="id-1_2_1_2_30_1-bb"><phrase role="identifier">empty</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_2_30_2-bb"><phrase role="identifier">size</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_2_30_3-bb"><phrase role="identifier">max_size</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_2_31-bb">iterators</link></phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_31_1_1-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_2_31_1_2-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_31_2_1-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_2_31_2_2-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_2_31_3-bb"><phrase role="identifier">cbegin</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_2_31_4-bb"><phrase role="identifier">cend</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_2_32-bb">modifiers</link></phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_32_1-bb"><phrase role="identifier">emplace</phrase></link><phrase role="special">(</phrase><phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_32_2-bb"><phrase role="identifier">emplace_hint</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_32_3-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_32_4-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_2_32_5-bb"><phrase role="identifier">insert</phrase></link><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_32_6-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_2_32_7-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_32_8-bb"><phrase role="identifier">erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_2_32_9-bb"><phrase role="identifier">quick_erase</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_2_32_10-bb"><phrase role="identifier">erase_return_void</phrase></link><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_2_32_11-bb"><phrase role="identifier">clear</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_2_32_12-bb"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_2_33-bb">observers</link></phrase>
  <phrase role="identifier">hasher</phrase> <link linkend="id-1_2_1_2_33_1-bb"><phrase role="identifier">hash_function</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">key_equal</phrase> <link linkend="id-1_2_1_2_33_2-bb"><phrase role="identifier">key_eq</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_2_34-bb">lookup</link></phrase>
  <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_34_1_1-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_iterator</phrase> <link linkend="id-1_2_1_2_34_1_2-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
    <phrase role="identifier">iterator</phrase> <link linkend="id-1_2_1_2_34_1_3-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
           <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
    <phrase role="identifier">const_iterator</phrase> 
    <link linkend="id-1_2_1_2_34_1_4-bb"><phrase role="identifier">find</phrase></link><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
         <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_2_34_2-bb"><phrase role="identifier">count</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">iterator</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_2_1_2_34_3_1-bb"><phrase role="identifier">equal_range</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">&gt;</phrase> <link linkend="id-1_2_1_2_34_3_2-bb"><phrase role="identifier">equal_range</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_2_35-bb">bucket interface</link></phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_2_35_1-bb"><phrase role="identifier">bucket_count</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_2_35_2-bb"><phrase role="identifier">max_bucket_count</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_2_35_3-bb"><phrase role="identifier">bucket_size</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">size_type</phrase> <link linkend="id-1_2_1_2_35_4-bb"><phrase role="identifier">bucket</phrase></link><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">local_iterator</phrase> <link linkend="id-1_2_1_2_35_5_1-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_2_1_2_35_5_2-bb"><phrase role="identifier">begin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">local_iterator</phrase> <link linkend="id-1_2_1_2_35_6_1-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_2_1_2_35_6_2-bb"><phrase role="identifier">end</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_2_1_2_35_7-bb"><phrase role="identifier">cbegin</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="identifier">const_local_iterator</phrase> <link linkend="id-1_2_1_2_35_8-bb"><phrase role="identifier">cend</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

  <phrase role="comment">// <link linkend="id-1_2_1_2_36-bb">hash policy</link></phrase>
  <phrase role="keyword">float</phrase> <link linkend="id-1_2_1_2_36_1-bb"><phrase role="identifier">load_factor</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">float</phrase> <link linkend="id-1_2_1_2_36_2-bb"><phrase role="identifier">max_load_factor</phrase></link><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_2_36_3-bb"><phrase role="identifier">max_load_factor</phrase></link><phrase role="special">(</phrase><phrase role="keyword">float</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
  <phrase role="keyword">void</phrase> <link linkend="id-1_2_1_2_36_4-bb"><phrase role="identifier">rehash</phrase></link><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="special">}</phrase><phrase role="special">;</phrase>

<phrase role="comment">// <link linkend="id-1_2_1_2_37-bb">Equality Comparisons</link></phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_multimap.operator=="><phrase role="keyword">operator</phrase><phrase role="special">==</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <link linkend="boost.unordered_multimap.operator!="><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>

<phrase role="comment">// <link linkend="id-1_2_1_2_38-bb">swap</link></phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <link linkend="boost.unordered_multimap.swap"><phrase role="identifier">swap</phrase></link><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">,</phrase> 
            <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></synopsis></refsynopsisdiv><refsect1><title>Description</title>
            <para>Based on chapter 23 of
              <ulink url="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2009/n2960.pdf">the working draft of the C++ standard [n2960]</ulink>.
              But without the updated rules for allocators.
            </para>
            <para><emphasis role="bold">Template Parameters</emphasis>
              <informaltable>
                <tgroup cols="2">
                  <tbody>
                    <row>
                      <entry><emphasis>Key</emphasis></entry>
                      <entry>Key must be Assignable and CopyConstructible.</entry></row>
                    <row>
                      <entry><emphasis>Mapped</emphasis></entry>
                      <entry>Mapped must be CopyConstructible</entry></row>
                    <row>
                      <entry><emphasis>Hash</emphasis></entry>
                      <entry>A unary function object type that acts a hash function for a <computeroutput>Key</computeroutput>. It takes a single argument of type <computeroutput>Key</computeroutput> and returns a value of type std::size_t.</entry></row>
                    <row>
                      <entry><emphasis>Pred</emphasis></entry>
                      <entry>A binary function object that implements an equivalence relation on values of type <computeroutput>Key</computeroutput>.
                        A binary function object that induces an equivalence relation on values of type Key.
                        It takes two arguments of type Key and returns a value of type bool.</entry></row>
                    <row>
                      <entry><emphasis>Alloc</emphasis></entry>
                      <entry>An allocator whose value type is the same as the container's value type.</entry></row></tbody></tgroup></informaltable></para>
            <para>The elements are organized into buckets. Keys with the same hash code are stored in the same bucket and elements with equivalent keys are stored next to each other.</para>
            <para>The number of buckets can be automatically increased by a call to insert, or as the result of calling rehash.</para>
          <refsect2><title><anchor id="boost.unordered_multimaptypes"/><computeroutput>unordered_multimap</computeroutput> 
        public
       types</title><orderedlist><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multimap.size_type"/><phrase role="identifier">size_type</phrase><phrase role="special">;</phrase></para>
              <para>An unsigned integral type.</para>
              <para>size_type can represent any non-negative value of difference_type.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multimap.difference_type"/><phrase role="identifier">difference_type</phrase><phrase role="special">;</phrase></para>
              <para>A signed integral type.</para>
              <para>Is identical to the difference type of iterator and const_iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multimap.iterator"/><phrase role="identifier">iterator</phrase><phrase role="special">;</phrase></para>
              <para>A iterator whose value type is value_type. </para>
              <para>The iterator category is at least a forward iterator.</para>
              <para>Convertible to const_iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multimap.const_iterator"/><phrase role="identifier">const_iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator whose value type is value_type. </para>
              <para>The iterator category is at least a forward iterator.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multimap.local_iterator"/><phrase role="identifier">local_iterator</phrase><phrase role="special">;</phrase></para>
              <para>An iterator with the same value type, difference type and pointer and reference type as iterator.</para>
              <para>A local_iterator object can be used to iterate through a single bucket.</para>
            </listitem><listitem><para>
<phrase role="keyword">typedef</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis> <anchor id="boost.unordered_multimap.const_local_iterator"/><phrase role="identifier">const_local_iterator</phrase><phrase role="special">;</phrase></para>
              <para>A constant iterator with the same value type, difference type and pointer and reference type as const_iterator.</para>
              <para>A const_local_iterator object can be used to iterate through a single bucket.</para>
            </listitem></orderedlist></refsect2><refsect2><title><anchor id="boost.unordered_multimapconstruct-copy-destruct"/><computeroutput>unordered_multimap</computeroutput> 
        public
       construct/copy/destruct</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">explicit</phrase> <anchor id="id-1_2_1_2_20-bb"/><phrase role="identifier">unordered_multimap</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                            <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hf <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                            <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                            <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container with at least n buckets, using hf as the hash function, eq as the key equality predicate, a as the allocator and a maximum load factor of 1.0.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Postconditions:</term><listitem><computeroutput><link linkend="id-1_2_1_2_30_2-bb">size</link>() == 0</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
  <anchor id="id-1_2_1_2_21-bb"/><phrase role="identifier">unordered_multimap</phrase><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase> f<phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase> l<phrase role="special">,</phrase> 
                     <phrase role="identifier">size_type</phrase> n <phrase role="special">=</phrase> <emphasis><phrase role="identifier">implementation</phrase><phrase role="special">-</phrase><phrase role="identifier">defined</phrase></emphasis><phrase role="special">,</phrase> 
                     <phrase role="identifier">hasher</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hf <phrase role="special">=</phrase> <phrase role="identifier">hasher</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                     <phrase role="identifier">key_equal</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq <phrase role="special">=</phrase> <phrase role="identifier">key_equal</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">,</phrase> 
                     <phrase role="identifier">allocator_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a <phrase role="special">=</phrase> <phrase role="identifier">allocator_type</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container with at least n buckets, using hf as the hash function, eq as the key equality predicate, a as the allocator and a maximum load factor of 1.0 and inserts the elements from [f, l) into it.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_22-bb"/><phrase role="identifier">unordered_multimap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The copy constructor. Copies the contained elements, hash function, predicate, maximum load factor and allocator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>value_type</computeroutput> is copy constructible</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_23-bb"/><phrase role="identifier">unordered_multimap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The move constructor.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is emulated on compilers without rvalue references.</para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para>
                <computeroutput>value_type</computeroutput> is move constructible.
                (TODO: This is not actually required in this implementation).
              </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">explicit</phrase> <anchor id="id-1_2_1_2_24-bb"/><phrase role="identifier">unordered_multimap</phrase><phrase role="special">(</phrase><phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an empty container, using allocator <computeroutput>a</computeroutput>.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_25-bb"/><phrase role="identifier">unordered_multimap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> <phrase role="identifier">Allocator</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> a<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Constructs an container, copying <computeroutput>x</computeroutput>'s contained elements, hash function, predicate, maximum load factor, but using allocator <computeroutput>a</computeroutput>.</para></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_26-bb"/><phrase role="special">~</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>The destructor is applied to every element, and all memory is deallocated</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><para><literallayout class="monospaced"><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_2_1_2_27-bb"/><phrase role="keyword">operator</phrase><phrase role="special">=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The assignment operator. Copies the contained elements, hash function, predicate and maximum load factor but not the allocator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>
                On compilers without rvalue references, there is a single assignment
                operator with the signature <computeroutput>operator=(unordered_multimap)</computeroutput>
                in order to emulate move semantics.
              </para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para><computeroutput>value_type</computeroutput> is copy constructible</para></listitem></varlistentry></variablelist><para><literallayout class="monospaced"><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&amp;</phrase> <anchor id="id-1_2_1_2_28-bb"/><phrase role="keyword">operator</phrase><phrase role="special">=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase> <phrase role="special">&amp;&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>The move assignment operator.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>
                On compilers without rvalue references, there is a single assignment
                operator with the signature <computeroutput>operator=(unordered_multimap)</computeroutput>
                in order to emulate move semantics.
              </para></listitem></varlistentry><varlistentry><term>Requires:</term><listitem><para>
                <computeroutput>value_type</computeroutput> is move constructible.
                (TODO: This is not actually required in this implementation).
              </para></listitem></varlistentry></variablelist><para><literallayout class="monospaced"><phrase role="identifier">allocator_type</phrase> <anchor id="id-1_2_1_2_29-bb"/><phrase role="identifier">get_allocator</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><refsect2><title><anchor id="id-1_2_1_2_30-bb"/><computeroutput>unordered_multimap</computeroutput> size and capacity</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">bool</phrase> <anchor id="id-1_2_1_2_30_1-bb"/><phrase role="identifier">empty</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput><link linkend="id-1_2_1_2_30_2-bb">size</link>() == 0</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_2_30_2-bb"/><phrase role="identifier">size</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput>std::distance(<link linkend="id-1_2_1_2_31_1-bb">begin</link>(), <link linkend="id-1_2_1_2_31_2-bb">end</link>())</computeroutput></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_2_30_3-bb"/><phrase role="identifier">max_size</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><computeroutput><link linkend="id-1_2_1_2_30_2-bb">size</link>()</computeroutput> of the largest possible container.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_2_31-bb"/><computeroutput>unordered_multimap</computeroutput> iterators</title><orderedlist><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_31_1-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_31_1_1-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_2_31_1_2-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>An iterator referring to the first element of the container, or if the container is empty the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_31_2-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_31_2_1-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_2_31_2_2-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>An iterator which refers to the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_2_31_3-bb"/><phrase role="identifier">cbegin</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>A constant iterator referring to the first element of the container, or if the container is empty the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_2_31_4-bb"/><phrase role="identifier">cend</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>A constant iterator which refers to the past-the-end value for the container.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_2_32-bb"/><computeroutput>unordered_multimap</computeroutput> modifiers</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> <phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_32_1-bb"/><phrase role="identifier">emplace</phrase><phrase role="special">(</phrase><phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase> args<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts an object, constructed with the arguments <computeroutput>args</computeroutput>, in the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to the inserted element.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para><para>If the compiler doesn't support variadic template arguments or rvalue
                      references, this is emulated for up to 10 arguments, with no support
                      for rvalue references or move semantics.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase><phrase role="special">...</phrase> Args<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_32_2-bb"/><phrase role="identifier">emplace_hint</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> hint<phrase role="special">,</phrase> <phrase role="identifier">Args</phrase><phrase role="special">&amp;&amp;</phrase><phrase role="special">...</phrase> args<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts an object, constructed with the arguments <computeroutput>args</computeroutput>, in the container.</para><para>hint is a suggestion to where the element should be inserted.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to the inserted element.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>The standard is fairly vague on the meaning of the hint. But the only practical way to use it, and the only way that Boost.Unordered supports is to point to an existing element with the same key. </para><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para><para>If the compiler doesn't support variadic template arguments or rvalue
                      references, this is emulated for up to 10 arguments, with no support
                      for rvalue references or move semantics.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_32_3-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> obj<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts obj in the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to the inserted element.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_32_4-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> hint<phrase role="special">,</phrase> <phrase role="identifier">value_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> obj<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts obj in the container.</para><para>hint is a suggestion to where the element should be inserted.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to the inserted element.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>The standard is fairly vague on the meaning of the hint. But the only practical way to use it, and the only way that Boost.Unordered supports is to point to an existing element with the same key. </para><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> InputIterator<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <anchor id="id-1_2_1_2_32_5-bb"/><phrase role="identifier">insert</phrase><phrase role="special">(</phrase><phrase role="identifier">InputIterator</phrase> first<phrase role="special">,</phrase> <phrase role="identifier">InputIterator</phrase> last<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Inserts a range of elements into the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>When inserting a single element, if an exception is thrown by an operation other than a call to <computeroutput>hasher</computeroutput> the function has no effect.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>Can invalidate iterators, but only if the insert causes the load factor to be greater to or equal to the maximum load factor.</para><para>Pointers and references to elements are never invalidated.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_32_6-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The iterator following <computeroutput>position</computeroutput> before the erasure.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  When the number of elements is a lot smaller than the number of buckets
                  this function can be very inefficient as it has to search through empty
                  buckets for the next element, in order to return the iterator.
                  The method <link linkend="id-1_2_1_2_32_9-bb">quick_erase</link> is faster, but has yet
                  to be standardized.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_2_32_7-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase all elements with key equivalent to <computeroutput>k</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of elements erased.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_32_8-bb"/><phrase role="identifier">erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> first<phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase> last<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erases the elements in the range from <computeroutput>first</computeroutput> to <computeroutput>last</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The iterator following the erased elements - i.e. <computeroutput>last</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_2_32_9-bb"/><phrase role="identifier">quick_erase</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  This method is faster than <link linkend="id-1_2_1_2_32_6-bb">erase</link> as
                  it doesn't have to find the next element in the container -
                  a potentially costly operation.
                </para><para>
                  As it hasn't been standardized, it's likely that this may
                  change in the future.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_2_32_10-bb"/><phrase role="identifier">erase_return_void</phrase><phrase role="special">(</phrase><phrase role="identifier">const_iterator</phrase> position<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erase the element pointed to by <computeroutput>position</computeroutput>.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>Only throws an exception if it is thrown by <computeroutput>hasher</computeroutput> or <computeroutput>key_equal</computeroutput>.</para><para>In this implementation, this overload doesn't call either function object's methods so it is no throw, but this might not be true in other implementations.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                  This method is now deprecated, use
                  quick_return instead. Although be
                  warned that as that isn't standardized yet, it could also
                  change.
                </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_2_32_11-bb"/><phrase role="identifier">clear</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Erases all elements in the container.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Postconditions:</term><listitem><para><computeroutput><link linkend="id-1_2_1_2_30_2-bb">size</link>() == 0</computeroutput></para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>Never throws an exception.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_2_32_12-bb"/><phrase role="identifier">swap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&amp;</phrase><phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>If the allocators are equal, doesn't throw an exception unless it is thrown by the copy constructor or copy assignment operator of <computeroutput>key_equal</computeroutput> or <computeroutput>hasher</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>For a discussion of the behavior when allocators aren't equal see
                  <link linkend="unordered.rationale.swapping_containers_with_unequal_allocators">the implementation details</link>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_2_33-bb"/><computeroutput>unordered_multimap</computeroutput> observers</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="identifier">hasher</phrase> <anchor id="id-1_2_1_2_33_1-bb"/><phrase role="identifier">hash_function</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>The container's hash function.
              </listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">key_equal</phrase> <anchor id="id-1_2_1_2_33_2-bb"/><phrase role="identifier">key_eq</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem>The container's key equality predicate.
              </listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_2_34-bb"/><computeroutput>unordered_multimap</computeroutput> lookup</title><orderedlist><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_34_1-bb"/><phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_34_1_1-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_iterator</phrase> <anchor id="id-1_2_1_2_34_1_2-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">iterator</phrase> <anchor id="id-1_2_1_2_34_1_3-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hash<phrase role="special">,</phrase> 
                <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> CompatibleKey<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> CompatibleHash<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> CompatiblePredicate<phrase role="special">&gt;</phrase> 
  <phrase role="identifier">const_iterator</phrase> 
  <anchor id="id-1_2_1_2_34_1_4-bb"/><phrase role="identifier">find</phrase><phrase role="special">(</phrase><phrase role="identifier">CompatibleKey</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">,</phrase> <phrase role="identifier">CompatibleHash</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> hash<phrase role="special">,</phrase> 
       <phrase role="identifier">CompatiblePredicate</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> eq<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An iterator pointing to an element with key equivalent to <computeroutput>k</computeroutput>, or <computeroutput>b.end()</computeroutput> if no such element exists.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>
                The templated overloads are a non-standard extensions which
                allows you to use a compatible hash function and equality
                predicate for a key of a different type in order to avoid
                an expensive type cast. In general, its use is not encouraged.
              </para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_2_34_2-bb"/><phrase role="identifier">count</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of elements with key equivalent to <computeroutput>k</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_34_3-bb"/><phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">iterator</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_2_1_2_34_3_1-bb"/><phrase role="identifier">equal_range</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">std</phrase><phrase role="special">::</phrase><phrase role="identifier">pair</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">const_iterator</phrase><phrase role="special">,</phrase> <phrase role="identifier">const_iterator</phrase><phrase role="special">&gt;</phrase> <anchor id="id-1_2_1_2_34_3_2-bb"/><phrase role="identifier">equal_range</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>A range containing all elements with key equivalent to <computeroutput>k</computeroutput>.
                  If the container doesn't container any such elements, returns
                  <computeroutput>std::make_pair(b.end(),b.end())</computeroutput>.
                  </para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_2_35-bb"/><computeroutput>unordered_multimap</computeroutput> bucket interface</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_2_35_1-bb"/><phrase role="identifier">bucket_count</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The number of buckets.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_2_35_2-bb"/><phrase role="identifier">max_bucket_count</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>An upper bound on the number of buckets.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_2_35_3-bb"/><phrase role="identifier">bucket_size</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n &lt; <link linkend="id-1_2_1_2_35_1-bb">bucket_count</link>()</computeroutput></para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>The number of elements in bucket <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">size_type</phrase> <anchor id="id-1_2_1_2_35_4-bb"/><phrase role="identifier">bucket</phrase><phrase role="special">(</phrase><phrase role="identifier">key_type</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> k<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The index of the bucket which would contain an element with key <computeroutput>k</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Postconditions:</term><listitem><para>The return value is less than <computeroutput>bucket_count()</computeroutput></para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_35_5-bb"/><phrase role="identifier">local_iterator</phrase> <anchor id="id-1_2_1_2_35_5_1-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_2_1_2_35_5_2-bb"/><phrase role="identifier">begin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A local iterator pointing the first element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><anchor id="id-1_2_1_2_35_6-bb"/><phrase role="identifier">local_iterator</phrase> <anchor id="id-1_2_1_2_35_6_1-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase>
<phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_2_1_2_35_6_2-bb"/><phrase role="identifier">end</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A local iterator pointing the 'one past the end' element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_2_1_2_35_7-bb"/><phrase role="identifier">cbegin</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A constant local iterator pointing the first element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="identifier">const_local_iterator</phrase> <anchor id="id-1_2_1_2_35_8-bb"/><phrase role="identifier">cend</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Requires:</term><listitem><para><computeroutput>n</computeroutput> shall be in the range <computeroutput>[0, bucket_count())</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Returns:</term><listitem><para>A constant local iterator pointing the 'one past the end' element in the bucket with index <computeroutput>n</computeroutput>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_2_36-bb"/><computeroutput>unordered_multimap</computeroutput> hash policy</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">float</phrase> <anchor id="id-1_2_1_2_36_1-bb"/><phrase role="identifier">load_factor</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>The average number of elements per bucket.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">float</phrase> <anchor id="id-1_2_1_2_36_2-bb"/><phrase role="identifier">max_load_factor</phrase><phrase role="special">(</phrase><phrase role="special">)</phrase> <phrase role="keyword">const</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Returns:</term><listitem><para>Returns the current maximum load factor.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_2_36_3-bb"/><phrase role="identifier">max_load_factor</phrase><phrase role="special">(</phrase><phrase role="keyword">float</phrase> z<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Effects:</term><listitem><para>Changes the container's maximum load factor, using <computeroutput>z</computeroutput> as a hint.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">void</phrase> <anchor id="id-1_2_1_2_36_4-bb"/><phrase role="identifier">rehash</phrase><phrase role="special">(</phrase><phrase role="identifier">size_type</phrase> n<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><para>Changes the number of buckets so that there at least <computeroutput>n</computeroutput> buckets, and so that the load factor is less than the maximum load factor.</para><para>Invalidates iterators, and changes the order of elements. Pointers and references to elements are not invalidated.</para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Throws:</term><listitem><para>The function has no effect if an exception is thrown, unless it is thrown by the container's hash function or comparison function.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_2_37-bb"/><computeroutput>unordered_multimap</computeroutput> Equality Comparisons</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <anchor id="boost.unordered_multimap.operator=="/><phrase role="keyword">operator</phrase><phrase role="special">==</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is a boost extension.</para><para>Behavior is undefined if the two containers don't have
                    equivalent equality predicates.</para></listitem></varlistentry></variablelist></listitem><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">bool</phrase> <anchor id="boost.unordered_multimap.operator!="/><phrase role="keyword">operator</phrase><phrase role="special">!=</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
                  <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase> <phrase role="keyword">const</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Notes:</term><listitem><para>This is a boost extension.</para><para>Behavior is undefined if the two containers don't have
                    equivalent equality predicates.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2><refsect2><title><anchor id="id-1_2_1_2_38-bb"/><computeroutput>unordered_multimap</computeroutput> swap</title><orderedlist><listitem><para><literallayout class="monospaced"><phrase role="keyword">template</phrase><phrase role="special">&lt;</phrase><phrase role="keyword">typename</phrase> Key<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Mapped<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Hash<phrase role="special">,</phrase> <phrase role="keyword">typename</phrase> Pred<phrase role="special">,</phrase> 
         <phrase role="keyword">typename</phrase> Alloc<phrase role="special">&gt;</phrase> 
  <phrase role="keyword">void</phrase> <anchor id="boost.unordered_multimap.swap"/><phrase role="identifier">swap</phrase><phrase role="special">(</phrase><phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase> x<phrase role="special">,</phrase> 
            <phrase role="identifier">unordered_multimap</phrase><phrase role="special">&lt;</phrase><phrase role="identifier">Key</phrase><phrase role="special">,</phrase> <phrase role="identifier">Mapped</phrase><phrase role="special">,</phrase> <phrase role="identifier">Hash</phrase><phrase role="special">,</phrase> <phrase role="identifier">Pred</phrase><phrase role="special">,</phrase> <phrase role="identifier">Alloc</phrase><phrase role="special">&gt;</phrase><phrase role="special">&amp;</phrase> y<phrase role="special">)</phrase><phrase role="special">;</phrase></literallayout></para><variablelist spacing="compact"><?dbhtml 
          list-presentation="table"
        ?><varlistentry><term>Effects:</term><listitem><para><computeroutput>x.swap(y)</computeroutput></para></listitem></varlistentry><varlistentry><term>Throws:</term><listitem><para>If the allocators are equal, doesn't throw an exception unless it is thrown by the copy constructor or copy assignment operator of <computeroutput>Hash</computeroutput> or <computeroutput>Pred</computeroutput>.</para></listitem></varlistentry><varlistentry><term>Notes:</term><listitem><para>For a discussion of the behavior when allocators aren't equal see
                  <link linkend="unordered.rationale.swapping_containers_with_unequal_allocators">the implementation details</link>.</para></listitem></varlistentry></variablelist></listitem></orderedlist></refsect2></refsect1></refentry>
    </section>
  </section>