<?xml version="1.0" encoding="utf-8"?>
<explicit-failures-markup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:noNamespaceSchemaLocation="explicit-failures.xsd">

    <!--
    PLEASE VALIDATE THE XML BEFORE COMMITTING YOUR CHANGES!

    Locally, the xmlint tool can be used:

        xmllint explicit-failures-markup.xml <two-dashes>schema explicit-failures.xsd

    The following online services can be used to validate your changes to this
    file:

        - http://syseng.nist.gov/b2bTestbed/projects/xmlvalidation/instance_validation.html
        - http://xmlvalidation.com/

    With both tools you need to provide both the explicit-failures-markup.xml
    file as the XML document and the explicit-failures.xsd as the schema
    document. Use the browse buttons to select them from your local hard
    drive.
    -->

    <!-- /////////////// Toolsets /////////////// -->
    <mark-toolset name="acc" status="required"/>
    <mark-toolset name="darwin-4.0.1" status="required"/>
    <mark-toolset name="gcc-4.1.2_sunos_i86pc" status="required"/>
    <mark-toolset name="gcc-4.1.3_linux" status="required"/>
    <mark-toolset name="gcc-4.2.1" status="required"/>
    <mark-toolset name="gcc-4.2.1_hpux_ia64" status="required"/>
    <mark-toolset name="gcc-4.2.1_linux_x86_64" status="required"/>
    <mark-toolset name="intel-linux-9.0" status="required"/>
    <mark-toolset name="intel-vc8-win-10.0" status="required"/>
    <mark-toolset name="intel-win-10.0" status="required"/>
    <mark-toolset name="msvc-7.1" status="required"/>
    <mark-toolset name="msvc-8.0" status="required"/>
    <mark-toolset name="msvc-8.0_64" status="required"/>

    <!-- /////////////// Libraries /////////////// -->

    <!-- accumulators -->
    <library name="accumulators">
      <mark-unusable>
        <toolset name="sun-5.7"/>
        <toolset name="sun-5.8"/>
        <toolset name="sun-5.9"/>
        <toolset name="borland-*"/>
        <toolset name="vacpp-*"/>
        <toolset name="cray-*"/>
      </mark-unusable>
      <mark-expected-failures>
          <test name="tail_variate_means"/>
          <test name="weighted_tail_variate_means"/>
          <toolset name="gcc-4.2.1*"/>
          <note author="Boris Gubenko" refid="42"/>
      </mark-expected-failures>
      <mark-expected-failures>
          <test name="weighted_kurtosis"/>
          <toolset name="acc"/>
          <note author="Boris Gubenko" refid="38"/>
      </mark-expected-failures>
      <mark-expected-failures>
        <test name="weighted_tail_variate_means"/>
        <toolset name="hp_cxx-71*"/>
        <note author="Markus Schoepflin">
          This failure is caused by a timeout when compiling the test. It
          passes when the timeout value is increased.
        </note>
      </mark-expected-failures>
      <mark-expected-failures>
        <test name="covariance"/>
        <test name="pot_quantile"/>
        <test name="tail_variate_means"/>
        <test name="weighted_covariance"/>
        <test name="weighted_pot_quantile"/>
        <test name="weighted_tail_variate_means"/>
        <toolset name="acc"/>
        <note author="Boris Gubenko" refid="47"/>
      </mark-expected-failures>
      <mark-expected-failures>
        <test name="p_square_cumul_dist"/>
        <test name="weighted_p_square_cumul_dist"/>
        <toolset name="*"/>
        <note author="Eric Niebler" refid="53"/>
      </mark-expected-failures>
    </library>

    <!-- algorithm -->
    <library name="algorithm">
      <mark-expected-failures>
          <test name="empty_search_test"/>
          <test name="search_test1"/>
          <test name="search_test2"/>
          <test name="search_test3"/>
          <test name="is_permutation_test1"/>
          <toolset name="vacpp-10.1"/>
        <note author="Marshall Clow">
          These failures are caused by a lack of support/configuration for Boost.Tr1
        </note>
      </mark-expected-failures>
    </library>

    <!-- minmax -->
    <library name="algorithm/minmax">
      <mark-unusable>
        <toolset name="sunpro-5_3-sunos"/>
      </mark-unusable>
    </library>

    <!-- string_algo -->
    <library name="algorithm/string">
        <mark-unusable>
            <toolset name="borland-5.5*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.1_stlport4"/>
            <toolset name="iw-7_1-vc6"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <toolset name="mipspro"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="P.Droba">
                The compiler does not support features that are essential for the library.
            </note>
        </mark-unusable>
        <test name="regex">
            <mark-failure>
                <toolset name="borland-5.9*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.6*"/>
                <note author="P.Droba">
                    The toolset is not supported by Boost.Regex.
                </note>
            </mark-failure>
        </test>
    </library>

    <!-- any -->
    <library name="any">
        <test name="any_to_ref_test">
          <mark-failure>
              <toolset name="msvc-6.5*"/>
              <note author="Vladimir Prus">
                The test fail with ICE, but the exact reason for ICE is not
                known. A minimal example of casting any to reference type
                seem to work. Anyone interested in using this functionality
                with msvc is suggested to do additional testing.
              </note>
          </mark-failure>
        </test>
    </library>


    <!-- array -->
    <library name="array">
        <test name="array0">
            <mark-failure>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-6.5_stlport4"/>
                <toolset name="msvc-7.0"/>
                <note author="A.Meredith">
                    Compilers need to support partial template specialization
                    to work with zero length arrays.
                </note>
            </mark-failure>
        </test>
        <test name="array3">
            <mark-failure>
                <toolset name="borland-5.5*"/>
                <toolset name="borland-5.6*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-6.5_stlport4"/>
                <toolset name="msvc-7.0"/>
                <note refid="3"/>
            </mark-failure>
            <mark-failure>
                <toolset name="sunpro-5_3-sunos"/>
                <note refid="4"/>
            </mark-failure>
        </test>
        <test name="array4">
            <mark-failure>
                <toolset name="borland-5.5*"/>
                <toolset name="borland-5.6*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-6.5_stlport4"/>
                <toolset name="msvc-7.0"/>
                <note refid="3"/>
            </mark-failure>
        </test>
    </library>

    <!-- asio -->
    <library name="asio">
      <mark-unusable>
        <toolset name="borland-5.6*"/>
        <toolset name="borland-5.8*"/>
        <note author="Chris Kohlhoff">
            This compiler does not support enable_if, which is needed by the
            Boost.System library on which Boost.Asio depends.
        </note>
      </mark-unusable>
      <mark-expected-failures>
        <test name="read_until"/>
        <test name="read_until_select"/>
        <toolset name="gcc-4.2.1_hpux_ia64"/>
        <note author="Boris Gubenko">
            On HP-UX 11.23 platform, these tests must be compiled with
            _XOPEN_SOURCE_EXTENDED macro defined. It is likely related
            to CR JAGag28813.
        </note>
        </mark-expected-failures>
    </library>

    <!-- assign -->
    <library name="assign">
        <mark-unusable>
            <toolset name="dmc-8_43-stlport-4_5_3"/>
        </mark-unusable>
        <mark-expected-failures>
            <test name="array"/>
            <toolset name="msvc-6.5_stlport4"/>
            <toolset name="msvc-7.0"/>
            <note author="Thorsten Ottosen" >
                The test would (most likely) compile and run properly if the workaround
                syntax .to_container( c ) was applied to all list_of() expressions.
            </note>
         </mark-expected-failures>
        <mark-expected-failures>
            <test name="email_example"/>
            <toolset name="gcc-2.95.3*"/>
            <note refid="27" author="Thorsten Ottosen"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="list_inserter"/>
            <toolset name="msvc-7.0"/>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="hp_cxx-65*"/>
            <note refid="6" author="Thorsten Ottosen"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="list_inserter"/>
            <toolset name="gcc-2.95.3*"/>
            <note  author="Thorsten Ottosen">
                This test could probably be made to work if somebody with knowledge
                about the compilers would submit a patch.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="list_of"/>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="borland-5*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Thorsten Ottosen" >
                The test would (most likely) compile and run properly if the workaround
                syntax .to_container( c ) was applied to all list_of() expressions.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="list_of_workaround"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="Thorsten Ottosen" >
                The test could probably be made to work if somebody submitted a patch.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="multi_index_container"/>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="sun-5.8"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="borland-5*"/>
            <toolset name="gcc-2.95.3*"/>
            <note refid="27" author="Thorsten Ottosen"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="multi_index_container"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="mipspro"/>
            <toolset name="hp_cxx-65*"/>
            <note author="Thorsten Ottosen" >
                The test would (most likely) compile and run properly if the workaround
                syntax .to_container( c ) was applied to all list_of() expressions.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="my_vector_example"/>
            <toolset name="gcc-2.95.3*"/>
            <note refid="27" author="Thorsten Ottosen"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ptr_list_inserter"/>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="borland-5*"/>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="mipspro"/>
            <note author="Thorsten Ottosen" >
                The test depends on Boost.Pointer Container which probably does not work for
                this compiler.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ptr_list_of"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="borland-5*"/>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="mipspro"/>
            <note author="Thorsten Ottosen" >
                The test depends on Boost.Pointer Container which probably does not work for
                this compiler.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ptr_map_inserter"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="borland-5*"/>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="mipspro"/>
            <note author="Thorsten Ottosen" >
                The test depends on Boost.Pointer Container which probably does not work for
                this compiler.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="std"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="Thorsten Ottosen" >
                The test does not work for
                this compiler.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="tuple_list_of"/>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="borland-5*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Thorsten Ottosen" >
                The test depends on Boost.Tuple which probably does not work for
                this compiler.
            </note>
        </mark-expected-failures>
    </library>

    <!-- beast -->
    <library name="beast">
        <mark-unusable>
            <toolset name="clang-linux-3.0*"/>
            <toolset name="clang-linux-3.1*"/>
            <toolset name="clang-linux-3.2*"/>
            <toolset name="gcc-3.*"/>
            <toolset name="gcc-4.0*"/>
            <toolset name="gcc-4.1*"/>
            <toolset name="gcc-4.2*"/>
            <toolset name="gcc-4.3*"/>
            <toolset name="gcc-4.4*"/>
            <toolset name="gcc-4.5*"/>
            <toolset name="gcc-4.6*"/>
            <toolset name="gcc-4.7*"/>
            <toolset name="gcc-mngw-3.*"/>
            <toolset name="gcc-mngw-4.0*"/>
            <toolset name="gcc-mngw-4.1*"/>
            <toolset name="gcc-mngw-4.2*"/>
            <toolset name="gcc-mngw-4.3*"/>
            <toolset name="gcc-mngw-4.4*"/>
            <toolset name="gcc-mngw-4.5*"/>
            <toolset name="gcc-mngw-4.6*"/>
            <toolset name="gcc-mngw-4.7*"/>
            <toolset name="qcc-3.*"/>
            <toolset name="qcc-4.0*"/>
            <toolset name="qcc-4.1*"/>
            <toolset name="qcc-4.2*"/>
            <toolset name="qcc-4.3*"/>
            <toolset name="qcc-4.4*"/>
            <toolset name="qcc-4.5*"/>
            <toolset name="qcc-4.6*"/>
            <toolset name="qcc-4.7*"/>    
            <toolset name="msvc-7.1"/>
            <toolset name="msvc-8.*"/>
            <toolset name="msvc-9.*"/>
            <toolset name="msvc-10.*"/>
            <toolset name="msvc-11.*"/>
            <toolset name="msvc-12.*"/>
            <note author="Vinnie Falco">C++11 is the minimum requirement.</note>
        </mark-unusable>
    </library>

    <!-- bimap -->
    <library name="bimap">
        <mark-unusable>
            <toolset name="borland-5.6*"/>
            <note author="J. L&#195;&#179;pez" date="05 Jul 2004" refid="17"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="borland-5.8*"/>
            <note author="Alisdair Meredith" date="26 May 2006"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="borland-5.9*"/>
            <note author="Alisdair Meredith" date="27 Feb 2007"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <note author="J. L&#195;&#179;pez" date="09 Jul 2004" refid="17"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="*como-4_3_3-msvc"/>
            <note author="J. L&#195;&#179;pez" date="30 Jul 2004">
                The VC++ 6.0 backend runs out of internal resources while
                trying to process the Comeau output for this library;
                Comeau Computing has been asked about a solution.
                On the other hand, Comeau 4.3.3 with VC++ 7.0 backend works
                fine.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="sunpro-5_8u1-sunos"/>
            <note author="J. L&#195;&#179;pez" date="22 Apr 2005" refid="17"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="dmc-8_43-stlport-4_5_3"/>
            <toolset name="dmc-8_44b-stlport-4_5_3"/>
            <toolset name="dmc-8_47-stlport-4_5_3"/>
            <note author="J. L&#195;&#179;pez" date="03 Jun 2005" refid="17"/>
        </mark-unusable>
        <mark-expected-failures>
            <test name="test_bimap_assign"/>
            <test name="test_bimap_ordered"/>
            <test name="test_bimap_unconstrained"/>
            <test name="test_bimap_unordered"/>
            <toolset name="acc"/>
            <note refid="38" author="Boris Gubenko"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="typeof"/>
            <toolset name="acc*"/>
            <toolset name="intel-vc71-win*"/>
            <toolset name="intel-vc8-win*"/>
            <toolset name="intel-win-9.1"/>
            <toolset name="hp_cxx*"/>
            <note refid="39" author="Boris Gubenko"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_bimap_serialization"/>
            <toolset name="gcc-mingw-3.4.5"/>
            <note author="Matias Capeletto">Compiler bug.</note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_bimap_property_map"/>
            <toolset name="gcc-3.4.6_linux_x86_64"/>
            <note author="Matias Capeletto">Time out.</note>
        </mark-expected-failures>
    </library>

    <!-- bind-->
    <library name="bind">
        <mark-expected-failures>
            <test name="bind_cv_test"/>
            <test name="bind_stateful_test"/>
            <toolset name="intel-7.1-linux"/>
            <toolset name="intel-7.1-stdlib-default-linux"/>
            <note refid="2" author="Aleksey Gurtovoy"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="bind_dm2_test"/>
            <test name="mem_fn_dm_test"/>
            <toolset name="msvc-6.*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="cw-8.3"/>
            <note refid="31" author="Peter Dimov"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="bind_dm_test"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note refid="31" author="Peter Dimov"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="bind_function_test"/>
            <toolset name="sunpro-5_8u1-sunos"/>
            <note author="Peter Dimov">
               This failure is caused by Boost.Function.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="mem_fn_derived_test"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note refid="31" author="Peter Dimov"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="bind_rv_sp_test"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="hp_cxx-71*"/>
            <note author="Markus Schoepflin">
              This failure is caused by a bug in the compiler triggered by the
              use of the debug flag '-gall'. It has been reported to the
              compiler vendor.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="bind_dm3_test"/>
            <toolset name="borland-5*"/>
            <toolset name="msvc-6.*"/>
            <toolset name="msvc-7.0"/>
            <note refid="31" author="Peter Dimov"/>
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="mem_fn_eq_test"/>
          <toolset name="msvc-7.1"/>
          <note author="Peter Dimov">
            This failure is only present in release mode and is caused by /OPT:ICF.
          </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="bind_placeholder_test"/>
            <toolset name="borland-*"/>
            <toolset name="msvc-6.*"/>
            <toolset name="msvc-7.0"/>
            <note refid="31" author="Peter Dimov"/>
        </mark-expected-failures>
    </library>

    <!-- build -->
    <library name="build">
        <mark-expected-failures>
            <test name="collect_debug_info"/>
            <toolset name="*"/>
            <note author="Jurko Gospodnetić">
                Temporarily enabled and always failing test used for collecting
                additional feedback from the testing site.
            </note>
        </mark-expected-failures>
    </library>

    <!-- chrono -->
    <library name="chrono">
        <mark-unusable>
            <toolset name="borland-*"/>
            <toolset name="vacpp-10*"/>
            <note author="Vicente J. Botet Escriba">
                The compiler does not support features that are essential for the library.
            </note>
        </mark-unusable>
        <test name="*_h" category="Header Only">
        </test>
        <test name="*_l" category="Non Header Only">
        </test>
        <test name="*_s" category="Static Link">
        </test>
        <test name="*_d" category="Dynamic Link">
        </test>
        <test name="*_f" category="Compile Diagnostic Required">
        </test>
      
    </library>

    <!-- circular_buffer -->
    <library name="circular_buffer">
        <mark-expected-failures>
            <test name="base_test"/>
            <test name="space_optimized_test"/>
            <toolset name="acc"/>
            <note author="Boris Gubenko" refid="41"/>
        </mark-expected-failures>
    </library>


    <!-- concept_check -->
    <library name="concept_check">
        <test name="class_concept_fail_expected">
            <mark-failure>
                <toolset name="cw-8.3*"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
        </test>
        <test name="class_concept_fail_expected">
            <mark-failure>
                <toolset name="borland-5*"/>
                <toolset name="msvc-6.5*"/>
                <toolset name="msvc-7.0"/>
                <note author="Jeremy Siek"/>
            </mark-failure>
        </test>
        <test name="stl_concept_check">
          <mark-failure>
            <toolset name="hp_cxx*"/>
            <note author="Markus Schoepflin" date="09 Dec 2007">
              This version of the Rogue Wave library fails to provide all
              needed addition operators for the iterator type and the
              difference type of std::deque.
            </note>
          </mark-failure>
        </test>
    </library>

    <!-- config -->
    <library name="config">
        <test name="config_link_test">
            <mark-failure>
                <toolset name="*como-4_3_3-vc7*"/>
                <note author="J. Maddock" refid="3"/>
            </mark-failure>
        </test>
        <test name="limits_test">
            <mark-failure>
                <toolset name="cw-8.3*"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
        </test>
        <test name="limits_test">
            <mark-failure>
                <toolset name="gcc-3_4_4_tru64"/>
                <note author="John Maddock">
                   Long double NaN's are apparently handled incorrectly on this platform.
                </note>
            </mark-failure>
        </test>

        <test name="limits_test">
            <mark-failure>
                <toolset name="iw-7_1-vc6-stlp-4_5_3"/>
                <note author="Aleksey Gurtovoy" refid="4"/>
            </mark-failure>
        </test>
        <test name="limits_test">
            <mark-failure>
                <toolset name="borland-5.8*"/>
                <note author="A.Meredith">
                    This failure is due to NaNs trapping.
                </note>
            </mark-failure>
        </test>
        <test name="limits_test">
            <mark-failure>
                <toolset name="borland-5.9*"/>
                <note author="A.Meredith">
                    This failure is due to the compiler not recognising the long double special values for infinity and quiet NaN
                </note>
            </mark-failure>
        </test>

        <test name="test_thread_fail1">
            <mark-failure>
                <toolset name="sunpro-5_3-sunos"/>
                <note author="J. Maddock" refid="3"/>
            </mark-failure>
        </test>
        <test name="test_thread_fail2">
            <mark-failure>
                <toolset name="sunpro-5_3-sunos"/>
                <note author="J. Maddock" refid="3"/>
            </mark-failure>
        </test>
    </library>

    <!-- container-->
    <library name="container">
        <mark-unusable>
            <toolset name="borland-5.*"/>
            <toolset name="sun-5.*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="cw-9.*"/>
            <toolset name="gcc-2.95*"/>
            <toolset name="gcc-3.0*"/>
            <toolset name="gcc-3.1*"/>
            <toolset name="gcc-3.2*"/>
            <toolset name="gcc-3.3*"/>
            <toolset name="mipspro"/>
            <toolset name="intel-linux-8.*"/>
            <note author="Ion Gazta&#241;aga">
                The compiler does not support features that are essential for the library.
            </note>
        </mark-unusable>
    </library>

    <library name="container_hash">
        <mark-expected-failures>
            <test name="hash_value_array_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0*"/>
            <note author="Daniel James">
              hash_value is not overloaded for arrays for older versions
              of Visual C++. There is a work around so that
              boost::hash&lt;T[N]&gt;, boost::hash_combine and boost::hash_range
              work.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="hash_function_pointer_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0*"/>
            <note refid="2" author="Daniel James"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="hash_function_pointer_test"/>
            <toolset name="sun-5.7"/>
            <toolset name="sun-5.8"/>
            <toolset name="sun-5.9"/>
            <note author="Daniel James">
                On these compilers the wrong overload of hash_value is called
                when the argument is a hash function pointer. So calling
                hash_value doesn't work but boost::hash does work (and it's
                recommended that user never call hash_value directly so this
                shouldn't be a problem).
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="hash_long_double_test"/>
            <toolset name="gcc-3.4.3_sunos"/>
            <toolset name="*pa_risc"/>
            <note author="Daniel James">
                This platform has poor support for <code>long double</code> so
                the hash function perform poorly for values out of the range
                of <code>double</code> or if they differ at a greater precision
                that <code>double</code> is capable of representing.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="point" />
            <test name="books" />
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0*"/>
            <note author="Daniel James">
                These examples only work on compilers with support for ADL.
                It is possible to work around this, but I wanted to keep the
                example code as clean as possible.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="point" />
            <toolset name="borland-*"/>
            <note author="Daniel James">
                It appears that Borland doesn't find friend functions defined
                in a class by ADL. This is easily fixed but this example is
                meant to show the typical way of customising boost::hash, not
                the portable way.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="hash_global_namespace_test" />
            <toolset name="borland-*"/>
            <note author="Daniel James">
                The test demonstrates a Borland bug - functions that aren't
                in a namespace don't appear to be found by ADL.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="container_fwd_gcc_debug"/>
            <toolset name="darwin-4.2"/>
            <note author="Daniel James">
              Debug containers aren't supported on Apple's version of gcc 4.2.
            </note>
        </mark-expected-failures>
    </library>

    <!-- convert-->
    <library name="convert">
        <test name="convert_test_has_begin">
          <mark-failure>
            <toolset name="msvc-8.0*"/>
            <toolset name="msvc-9.0*"/>
            <toolset name="msvc-10.0*"/>
            <toolset name="msvc-11.0*"/>
            <note author="Vladimir Batov">
                The relevant SFINAE support is broken in MSVC up to version 11.
            </note>
          </mark-failure>
        </test>
    </library>

    <!-- lexical_cast -->
    <library name="lexical_cast">
        <test name="lexical_cast_test">
            <mark-failure>
                <toolset name="sunpro-5_3-sunos"/>
                <note author="Douglas Gregor" refid="3"/>
            </mark-failure>
        </test>
        <test name="lexical_cast_abstract_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="Alisdair Meredith">
                    This compiler does not support the is_abstract type trait
                </note>
            </mark-failure>
        </test>
        <test name="lexical_cast_loopback_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="intel-darwin-11.*"/>
                <toolset name="intel-darwin-12.0"/>
                <toolset name="gcc-3.4.0*"/>
                <toolset name="gcc-4.1*"/>
                <toolset name="gcc-4.2.0*"/>
                <toolset name="gcc-mingw-3.4*"/>
                <toolset name="gcc-mingw-4.*"/>
                <toolset name="sun-5.7*"/>
                <toolset name="sun-5.8*"/>
                <toolset name="sun-5.9*"/>
                <toolset name="sun-5.10*"/>
                <toolset name="msvc-8.0*"/>
                <toolset name="msvc-9.0*"/>
                <toolset name="msvc-10.0*"/>
                <toolset name="msvc-11.0*"/>
                <toolset name="msvc-12.0*"/>
                <toolset name="msvc-7.1*"/>
                <toolset name="vacpp-10.1"/>
                <toolset name="qcc-4*"/>
                <toolset name="cray-8.0"/>
                <toolset name="acc"/>
                <note author="Alexander Nasonov">
                    Conversion double-string-double may give a different 
                    value (or even throw) on many compilers
                </note>
            </mark-failure>
        </test>
        <test name="lexical_cast_float_types_test">
            <mark-failure>
                <toolset name="vacpp-*"/>
                <toolset name="vacpp"/>
                <toolset name="msvc-8.0"/>
                <toolset name="msvc-9.0"/>
                <toolset name="msvc-9.0~stlport5.2"/>
                <toolset name="msvc-9.0~wm5~stlport5.2"/>
                <toolset name="msvc-10.0"/>
                <toolset name="intel-darwin-11.*"/>
                <toolset name="intel-darwin-12.0"/>
                <toolset name="qcc-4*"/>
                <toolset name="clang-darwin-libcxx*"/>
                <toolset name="msvc-9.0~wm5"/>
                <note author="Antony Polukhin">
                    Some compilers and STL realizations convert double and long 
                    double types with bigger precision loss than minimal (or 
                    even round to infinity). Such failures are not a 
                    lexical_cast, but a compiler fault. 
                </note>
            </mark-failure>
        </test>
    </library>

    <!-- contract -->
    <library name="contract">
        <mark-unusable>
            <toolset name="clang-darwin-ubsan"/>
            <note author="Lorenzo Caminiti">
                On this compiler, Boost.Function gives a run-time error when
                calling non-nullary lambdas as used by the tests of this library
                to program contract failure handlers.
                It might still be possible to use this library on this compiler
                using default contract failure handlers or programming custom
                contract failure handlers but without using non-nullary lambdas
                (however, the authors did not confirm that).
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="gcc-3.*"/>
            <toolset name="gcc-4.*"/>
            <note author="Lorenzo Caminiti">
                Even tests that do not use C++11 lambda functions fail on this
                compiler because it incorrectly attempts an extra copy when
                objects are constructed using `boost::check c = ...`.
                This is fixed in MinGW GCC 4.3.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="msvc-7.*"/>
            <note author="Lorenzo Caminiti">
                Even tests that do not use C++11 lambda functions fail on this
                compiler because of a number of issues (Boost.Exception is not
                supported on this compiler but it is used by this library
                implementation, some aspects of `friend` and `volatile` are not
                properly implemented on this compiler, etc.).
                These specific issues are fixed in MSVC 9.0 (but only MSVC 11.0
                has adequate lambda function support that makes this library
                actually usable).
            </note>
        </mark-unusable>
        <mark-expected-failures>
            <test name="disable-audit"/>
            <toolset name="gcc-4.9"/>
            <toolset name="clang-linux-3.6"/>
            <toolset name="clang-linux-3.7"/>
            <toolset name="clang-linux-3.8"/>
            <note author="Lorenzo Caminiti">
                This test fails on this compiler because of a bug with
                exceptions (see http://grokbase.com/t/gg/android-ndk/1656csqqtp/assertion-ttypeencoding-dw-eh-pe-absptr-unexpected-ttypeencoding-failed).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="invariant-ifdef"/>
            <test name="specify-nothing"/>
            <toolset name="clang-linux-3.6"/>
            <toolset name="clang-linux-3.7"/>
            <toolset name="clang-linux-3.8"/>
            <note author="Lorenzo Caminiti">
                This test fails on this compiler because of a bug in its STL
                implementation (undefined references to
                `std::ios_base::failure::failure`).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="destructor-throwing_old"/>
            <test name="destructor-throwing_post"/>
            <test name="public_function-decl_pre_all"/>
            <test name="public_function-decl_pre_ends"/>
            <test name="public_function-smoke"/>
            <test name="public_function-throwing_post"/>
            <test name="public_function-virtual"/>
            <test name="public_function-virtual_branch"/>
            <toolset name="clang-linux-*~gnu++*"/>
            <note author="Lorenzo Caminiti">
                This test fails because of a libcxxrt bug on Clang for FreeBSD
                which causes `std::uncaught_exception` to not work properly on
                re-throws (see https://github.com/pathscale/libcxxrt/issues/49).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="old-if_copyable"/>
            <test name="old-if_copyable_macro"/>
            <toolset name="gcc-4.6*"/>
            <toolset name="gcc-4.7*"/>
            <toolset name="msvc-10.*"/>
            <toolset name="msvc-11.*"/>
            <note author="Lorenzo Caminiti">
                This test fails because this complier does not properly
                implement SFINAE giving incorrect errors on substitution
                failures for private members.
                This seems to be fixed in GCC 4.8 and MSVC 12.0.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="public_function-protected_error"/>
            <toolset name="clang-linux-3.0~*"/>
            <toolset name="gcc-4.6*"/>
            <note author="Lorenzo Caminiti">
                This test fails because SFINAE on this complier seems to not
                fail as it should when a derived class tries to call a
                protected member function on a base class object via a function
                pointer instead of via inheritance.
                This seems to be fixed in Clang 3.1, and to be specific to
                version 4.6 of GCC.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="public_function-virtual_access_multi"/>
            <toolset name="gcc-4.6*"/>
            <toolset name="gcc-4.7*"/>
            <note author="Lorenzo Caminiti">
                This test fails because this compiler seems to incorrectly check
                access level of members in base classes in a context when only
                derived class members are used.
                This seems to be fixed in GCC 4.8 (possibly related to
                https://gcc.gnu.org/bugzilla/show_bug.cgi?id=57973).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="constructor-throwing_body"/>
            <test name="destructor-decl_entry_inv_all"/>
            <test name="destructor-decl_entry_inv_ends"/>
            <test name="destructor-decl_entry_static_inv_all"/>
            <test name="destructor-decl_entry_static_inv_ends"/>
            <test name="destructor-decl_entry_static_inv_mid"/>
            <test name="destructor-decl_exit_static_inv_all"/>
            <test name="destructor-decl_exit_static_inv_ends"/>
            <test name="destructor-decl_exit_static_inv_mid"/>
            <test name="destructor-throwing_body"/>
            <test name="destructor-throwing_old"/>
            <test name="destructor-throwing_post"/>
            <test name="function-ifdef_macro"/>
            <test name="function-throwing_body"/>
            <test name="public_function-static_throwing_body"/>
            <test name="public_function-throwing_body"/>
            <test name="public_function-throwing_body_virtual"/>
            <test name="public_function-throwing_body_virtual_branch"/>
            <toolset name="qcc-4.7*"/>
            <note author="Lorenzo Caminiti">
                This test fails because `std::unchaught_exception` seems to
                always return zero on this compiler (even if the authors could
                not find a direct reference to this possible compiler issue
                online).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="invariant-ifdef"/>
            <test name="invariant-ifdef_macro"/>
            <test name="invariant-volatile_error"/>
            <toolset name="msvc-8.*"/>
            <note author="Lorenzo Caminiti">
                This test fails because this complier seems to dispatch calls
                incorrectly when both `const` and `const volatile` overloads
                are present (even if the authors could not find a direct
                reference to this possible compiler issue online).
                This is fixed in MSVC 9.0 (but only MSVC 11.0 has adequate
                lambda function support).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="call_if-no_equal_call_if"/>
            <toolset name="msvc-10.*"/>
            <note author="Lorenzo Caminiti">
                This test fails because MSVC 10.0 is not able to properly deduce
                a template specialization.
                This is fixed in MSVC 11.0.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="constructor-ifdef_macro"/>
            <test name="constructor-smoke"/>
            <toolset name="msvc-10.*"/>
            <note author="Lorenzo Caminiti">
                This test fails because of a MSVC 10.0 bug with lambdas within
                template class initialization list.
                This can be worked around using a functor bind instead of a
                lambda, but it is fixed in MSVC 11.0.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="destructor-smoke"/>
            <toolset name="msvc-10.*"/>
            <note author="Lorenzo Caminiti">
                This test fails because of a MSVC 10.0 bug for which lambdas
                cannot access typedefs declared within classes.
                This can be worked around declaring typedefs outside of
                classes, but it is fixed in MSVC 11.0.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="disable-no_post_except_lib"/>
            <test name="disable-no_post_except_unit"/>
            <test name="disable-nothing_for_pre_prog"/>
            <test name="disable-other_assertions_lib"/>
            <test name="disable-other_assertions_prog"/>
            <test name="disable-other_assertions_unit"/>
            <toolset name="msvc-10.*"/>
            <note author="Lorenzo Caminiti">
                This test fails because of an internal MSVC 10.0 compiler bug.
                This is fixed in MSVC 11.0.
            </note>
        </mark-expected-failures>
    </library>

    <!-- coroutine -->
    <library name="coroutine">
      <mark-unusable>
        <toolset name="cray-*"/>
        <toolset name="darwin-4.4"/>
        <toolset name="darwin-4.4*"/>
        <toolset name="gcc-4.4"/>
        <toolset name="gcc-4.4*"/>
        <toolset name="gcc-mingw-4.4"/>
        <toolset name="gcc-mingw-4.4*"/>
        <toolset name="gcc-mingw-4.5"/>
        <toolset name="gcc-mingw-4.5*"/>
        <toolset name="gcc-mingw-4.6"/>
        <toolset name="gcc-mingw-4.6*"/>
        <toolset name="gcc-mingw-4.7"/>
        <toolset name="gcc-mingw-4.7*"/>
        <toolset name="msvc-8.0"/>
        <toolset name="pgi-*"/>
        <toolset name="vacpp-*"/>
      </mark-unusable>
    </library>


    <!-- crc -->
    <library name="crc">
        <test name="crc_test">
            <mark-failure>
                <toolset name="sunpro-5_3-sunos"/>
                <note author="Douglas Gregor" refid="3"/>
            </mark-failure>
        </test>
    </library>

    <!-- date_time -->
    <library name="date_time">
        <mark-unusable>
            <toolset name="como-4_3_3-vc7_1"/>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="msvc-6.5"/>
            <toolset name="msvc-6.5_stlport5"/>
            <toolset name="msvc-6.5_stlport4"/>
            <toolset name="msvc-7.0"/>
            <toolset name="msvc-7.0_stlport5"/>
            <toolset name="iw-7_1-vc6-stlp-4_5_3"/>
            <toolset name="iw-7_1-vc6"/>
            <toolset name="dmc-*"/>
        </mark-unusable>

        <test name="testgreg_serialize*">
            <mark-failure>
            <toolset name="gcc-2.*"/>
            <toolset name="msvc-6.5*"/>
            <note author="B. Garst">The serialization library does not support this compiler.
            </note>
            </mark-failure>
        </test>

        <test name="testgreg_serialize_xml">
            <mark-failure>
            <toolset name="msvc-7.0"/>
            <note author="J. Garland">XML serialization is not supported on this compiler.
            </note>
            </mark-failure>
        </test>

        <test name="testtime_serialize*">
            <mark-failure>
            <toolset name="gcc-2.*"/>
            <toolset name="msvc-6.5*"/>
            <note author="B. Garst">The serialization library does not support this compiler.
            </note>
            </mark-failure>
        </test>

        <test name="testtime_serialize_xml*">
            <mark-failure>
            <toolset name="msvc-7.0"/>
            <note author="J. Garland">XML serialization is not supported on this compiler.
            </note>
            </mark-failure>
        </test>

        <test name="testdate_iterator">
            <mark-failure>
            <toolset name="intel-7.1-stdlib-default-linux"/>
            <toolset name="intel-7.1-linux"/>
                <note author="J. Garland" refid="19,21"/>
            </mark-failure>
        </test>
        <test name="testdate_iterator_dll">
            <mark-failure>
            <toolset name="intel-7.1-stdlib-default-linux"/>
            <toolset name="intel-7.1-linux"/>
                <note author="J. Garland" refid="19,21"/>
            </mark-failure>
        </test>


        <test name="testgeneric_period">
            <mark-failure>
            <toolset name="intel-7.1-stdlib-default-linux"/>
            <toolset name="intel-7.1-linux"/>
                <note author="J. Garland">These are strange runtime failures for
                           which there is no obvious explanation.  Later versions of the
                           Intel compiler (eg:8.0) seem to have resolved the issue.
                        </note>
            </mark-failure>
        </test>

        <test name="testgreg_wstream">
            <mark-failure>
                <toolset name="msvc-6.5*"/>
                <toolset name="msvc-7.0*"/>
                <toolset name="cw-8.3*"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="mingw*"/>
                <toolset name="*mingw*"/>
                <toolset name="*cygwin*"/>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-3.1-darwin"/>
                <toolset name="*como-4_3_3*"/>
                <note author="B. Garst" refid="19,21"/>
            </mark-failure>
        </test>

        <test name="testdate_input_facet*">
            <mark-failure>
            <toolset name="cw-9.4"/>
            <toolset name="cw-9.5*"/>
                <note author="J. Garland">
                   For some reason Code Warrior has difficulty compiling some of the
                   input code.  This may be related to limitations of locale handling,
                   but it's unclear at this time (2005-May-21).
                </note>
            </mark-failure>
        </test>


        <test name="testlocal_time_facet">
            <mark-failure>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="*como-4_3_3*"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <toolset name="msvc-6.5"/>
            <toolset name="msvc-7.0"/>
                <note author="J. Garland">
                   Some older compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>

        <test name="testlocal_time">
            <mark-failure>
            <toolset name="msvc-6.5"/>
            <toolset name="*como-4_3_3*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
                <note author="J. Garland">
                   Some older compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>

        <test name="testlocal_time_iterator">
            <mark-failure>
            <toolset name="msvc-6.5"/>
            <toolset name="*como-4_3_3*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
                <note author="J. Garland">
                   Some older compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>

        <test name="testlocal_time_period">
            <mark-failure>
            <toolset name="msvc-6.5"/>
            <toolset name="*como-4_3_3*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
                <note author="J. Garland">
                   Some older compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>

        <test name="testclocks">
            <mark-failure>
            <toolset name="*como-4_3_3*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="msvc-7.0"/>
            <toolset name="msvc-6.5"/>
                <note author="J. Garland">
                   Some compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>

        <test name="testlocal_time_input_facet">
            <mark-failure>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="*como-4_3_3*"/>
            <toolset name="cw-8.3*"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <toolset name="msvc-6.5"/>
            <toolset name="msvc-7.0"/>
                <note author="J. Garland">
                   Some older compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>


        <test name="testtime_input_facet">
            <mark-failure>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="*como-4_3_3*"/>
            <toolset name="cw-8.3*"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <toolset name="msvc-6.5"/>
            <toolset name="msvc-7.0"/>
                <note author="J. Garland">
                   Some older compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>

        <test name="testcustom_time_zone">
            <mark-failure>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8.1"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="*como-4_3_3*"/>
            <toolset name="msvc-6.5"/>
                <note author="J. Garland">
                   Some older compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>

        <test name="testposix_time_zone">
            <mark-failure>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8.1"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="msvc-6.5"/>
                <note author="J. Garland">
                   Some older compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>

        <test name="testtz_database">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8.1"/>
                <toolset name="*como-4_3_3*"/>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="msvc-6.5"/>
                <note author="J. Garland">
                   Some compilers are confused by the template code here.
                   These are new features to date-time in 1.33 and there is no
                   plan to backport to these non-compliant compilers.
                </note>
            </mark-failure>
        </test>

        <test name="testtime_wstream">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-3.1-darwin"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="mingw*"/>
                <toolset name="*mingw*"/>
                <toolset name="*cygwin*"/>
                <toolset name="*como-4_3_3*"/>
                <toolset name="hp_cxx-65*"/>
                <note author="B. Garst" refid="19,21,22"/>
            </mark-failure>
        </test>

        <test name="testtime_wstream_std_config">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-3.1-darwin"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="mingw*"/>
                <toolset name="*como-4_3_3*"/>
                <toolset name="hp_cxx-65*"/>
                <note author="B. Garst" refid="19,21,22"/>
            </mark-failure>
        </test>
        <test name="testdate_facet_new">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="cw-8.3*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="J. Garland">
                These compilers are unfortunately able to correctly compile the
                new format-based input-output code for date time.  Suitable, but
                less flexible, alternatives are available on these compilers.
               </note>
            </mark-failure>
        </test>
        <test name="testdate_facet_new_dll">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="cw-8.3*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="J. Garland">
                These compilers are unfortunately able to correctly compile the
                new format-based input-output code for date time.  Suitable, but
                less flexible, alternatives are available on these compilers.
               </note>
            </mark-failure>
        </test>
        <test name="testtime_facet">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="cw-8.3*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="J. Garland">
                These compilers are unfortunately able to correctly compile the
                new format-based input-output code for date time.  Suitable, but
                less flexible, alternatives are available on these compilers.
               </note>
            </mark-failure>
        </test>

        <test name="testwcustom_time_zone">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-3.4.2_mingw"/>
                <toolset name="gcc-3.4.5_mingw"/>
                <toolset name="*mingw*"/>
                <toolset name="*cygwin*"/>
                <note author="J. Garland">
                These compilers are unfortunately able to correctly compile the
                new format-based input-output code for date time.  Suitable, but
                less flexible, alternatives are available on these compilers.
               </note>
            </mark-failure>
        </test>

        <test name="testwposix_time_zone">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-3.4.2_mingw"/>
                <toolset name="gcc-3.4.5_mingw"/>
                <toolset name="*mingw*"/>
                <toolset name="*cygwin*"/>
                <note author="J. Garland">
                These compilers are unfortunately able to correctly compile the
                new format-based input-output code for date time.  Suitable, but
                less flexible, alternatives are available on these compilers.
               </note>
            </mark-failure>
        </test>

        <test name="testfacet">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-3.1-darwin"/>
                <toolset name="msvc-6.5"/>
                <toolset name="mingw*"/>
                <toolset name="*mingw*"/>
                <toolset name="*cygwin*"/>
                <toolset name="gcc-3.4.2_mingw"/>
                <toolset name="gcc-3.4.5_mingw"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="B. Garst" refid="18,19"/>
            </mark-failure>
        </test>
        <test name="testfacet_dll">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-3.1-darwin"/>
                <toolset name="msvc-6.5"/>
                <toolset name="mingw*"/>
                <toolset name="*mingw*"/>
                <toolset name="*cygwin*"/>
                <toolset name="gcc-3.4.2_mingw"/>
                <toolset name="gcc-3.4.5_mingw"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="*como-4_3_3*"/>
                <note author="B. Garst" refid="18,19"/>
            </mark-failure>
        </test>
        <test name="testgreg_year_dll">
            <mark-failure>
                <toolset name="*como-4_3_3*"/>
            </mark-failure>
        </test>
        <test name="testparse_date">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="B. Garst" refid="18,20"/>
            </mark-failure>
        </test>
        <test name="testmicrosec_time_clock">
            <mark-failure>
            <toolset name="intel-7.1-stdlib-default-linux"/>
            <toolset name="*como-4_3_3*"/>
            <toolset name="intel-7.1-linux"/>
                <note author="B. Garst" refid="22"/>
            </mark-failure>
        </test>
        <test name="testmicrosec_time_clock">
            <mark-failure>
            <toolset name="borland-5.6.4"/>
            <toolset name="borland-5.8.2"/>
            <note author="J. Garland">
               There is apparently a bug in Borland library
               such that  std::local_time and std::gmtime are
               returning a time that's 1 hour ahead GetSystemTimeAsFileTime
               during DST.  This is a rather serious problem in that
               some of the date-time clock interfaces will give the wrong
               current time.
            </note>
            </mark-failure>
        </test>
        <test name="teststreams">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="gcc-3.1-darwin"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="mingw-3*"/>
                <toolset name="gcc-3.4.2_mingw"/>
                <toolset name="gcc-3.4.5_mingw"/>
                <toolset name="*mingw*"/>
                <toolset name="*cygwin*"/>
                <toolset name="mingw"/>
                <toolset name="*como-4_3_3*"/>
                <note author="B. Garst" refid="18,19,20"/>
            </mark-failure>
        </test>
        <test name="testdate_dll">
            <mark-failure>
                <toolset name="*como-4_3_3*"/>
                <note author="J. Garland" date="30 Jan 2004" id="24"/>
            </mark-failure>
        </test>
        <test name="testgreg_day_dll">
            <mark-failure>
                <toolset name="*como-4_3_3*"/>
                <note author="J. Garland" date="30 Jan 2004" id="24"/>
            </mark-failure>
        </test>
        <test name="*_dll">
            <mark-failure>
                <toolset name="*como-4_3_3*"/>
                <note author="J. Garland" date="30 Jan 2004" id="24"/>
            </mark-failure>
        </test>

        <mark-expected-failures>
            <test name="testdate_dll"/>
            <test name="testdate_duration_dll"/>
            <test name="testdate_input_facet_dll"/>
            <test name="testdate_iterator_dll"/>
            <test name="testfacet_dll"/>
            <test name="testformatters_dll"/>
            <test name="testgenerators_dll"/>
            <test name="testgreg_durations_dll"/>
            <test name="testperiod_dll"/>
            <toolset name="cw-8.3*"/>
            <note author="R. Rivera" refid="25"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testdate_facet_new"/>
            <test name="testdate_facet_new_dll"/>
            <test name="testdate_input_facet"/>
            <test name="testdate_input_facet_dll"/>
            <test name="testdate_facet"/>
            <test name="testdate_facet_dll"/>
            <test name="testtime_facet"/>
            <test name="testtime_input_facet"/>
            <toolset name="sun-5.8"/>
            <note author="J. Garland">
               The sun 5.8 compiler and standard library have a problem with
               the classic facet which causes some of the io tests for date-time
               to fail.  Overall this should not affect most uses of the library.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testdate_input_facet"/>
            <test name="testdate_input_facet_dll"/>
            <toolset name="msvc-7.1_stlport4"/>
            <note author="J. Garland">
               The STLPort standard library has issues with some custom
               facet settings causing an unexplained failure in these
               facet tests.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testdate_facet_new"/>
            <test name="testdate_facet_new_dll"/>
            <test name="testtime_facet"/>
            <toolset name="msvc-7.1_stlport4"/>
            <toolset name="msvc-8.0_stlport5"/>
            <note author="J. Garland">
               The STLPort standard library has issues with the handling
               of the classic facet which causes some fo the i/o tests
               for date-time to fail.  Overall this should not affect
               most uses of the library.
            </note>
        </mark-expected-failures>


        <mark-expected-failures>
            <test name="testgreg_wstream"/>
            <test name="testtime_facet"/>
            <test name="testtime_input_facet"/>
            <test name="testtime_wstream"/>
            <toolset name="msvc-7.1_stlport4"/>
            <note author="J. Garland">
               MSVC 7.1 with its standard library passes all date-time tests.
               For some reason when paired with stlport a few widestream
               io tests do not format output correctly.   Overall this should
               not affect most uses of the library.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testlocal_time_input_facet"/>
            <test name="testtime_input_facet"/>
            <toolset name="cw-9.4"/>
            <toolset name="cw-9.5*"/>
            <note author="J. Garland">
                 Although these tests compile, the execution aborts for
                 an unknown reason. Note that sometimes the excution is
                 ok on cw-9_4. This may be fixable if someone
                 can track down the source of the problem.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testlocal_time"/>
            <test name="testlocal_time_input_facet"/>
            <test name="testtime_input_facet"/>
            <toolset name="msvc-8.0*"/>
            <note author="J. Garland">
               These tests are failing with the beta2 version of VC_8.  At least
               one of them is directly a result of the new VC_8 standard library
               restricting the year value in a tm struct to be positive (that is
               greater than year 1900).  This is a change from VC7_1 and Microsoft
               is considering removing this restriction.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testtime_serialize*"/>
            <test name="testgreg_serialize*"/>
            <toolset name="vacpp"/>
            <note author="J. Garland">
              These tests are for serialization which has been marked as unusable.
              The issue was specifically noted on
              AIX version : 5.2.0.41 using IBM XL Version 8.0.0.0.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testfacet"/>
            <test name="testfacet_dll"/>
            <toolset name="hp_cxx*"/>
            <toolset name="acc*"/>
            <note author="Markus Schoepflin">
            The failure is caused by a standard library bug. It doesn't
            support user defined facets which are not default
            constructible. This has been reported to the compiler vendor.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testdate_input_facet_dll"/>
            <test name="testdate_input_facet"/>
            <test name="testtime_input_facet"/>
            <test name="testlocal_time_input_facet"/>
            <toolset name="acc*"/>
            <note author="Jeff Garland">
            These tests rely on the ability of an std::map to be
            instantiated on an incomplete type. The Rogue Wave
            version 2.2 and higher does not allow this.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testtime_wstream"/>
            <toolset name="hp_cxx-65*"/>
            <note author="Jeff Garland">
            The failure is caused by a standard library bug. It doesn't
            support user defined facets which are not default
            constructible. This has been reported to the compiler vendor.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testgreg_wstream"/>
            <test name="testparse_date"/>
            <test name="teststreams"/>
            <toolset name="hp_cxx*"/>
            <note author="Markus Schoepflin">
            The failure is caused by a standard library bug. The end-of-stream
            istream iterator can only be constructed when the istream iterator
            has been instantiated with char as the character type. This has
            been reported to the compiler vendor.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testfacet"/>
            <test name="testfacet_dll"/>
            <test name="testgreg_wstream"/>
            <test name="teststreams"/>
            <test name="testtime_wstream"/>
            <test name="testwcustom_time_zone"/>
            <test name="testwposix_time_zone"/>
            <toolset name="qcc-3.3.5_gpp"/>
            <note author="Jim Douglas" date="12 Feb 06" refid="36"/>
        </mark-expected-failures>

    </library>

    <!-- detail -->
    <library name="detail">
        <mark-expected-failures>
            <test name="correctly_disable"/>
            <test name="correctly_disable_debug"/>
            <toolset name="pathscale-4.*"/>
            <toolset name="sun-5.10"/>
            <toolset name="pgi-*"/>
            <toolset name="msvc-9.0~stlport*"/>
            <toolset name="msvc-9.0~wm5~stlport*"/>
            <note author="Daniel James">
            This indicates that forward declarations could probably be used
            for these compilers but currently aren't. All these compilers use
            STLport, which is compatible with forward declarations in some
            circumstances, but not in others. I haven't looked into how to
            determine this, so I've just set container_fwd to never forward
            declare for STLport.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="correctly_disable"/>
            <toolset name="gcc-4.2*"/>
            <toolset name="gcc-4.3*"/>
            <toolset name="gcc-4.4*"/>
            <toolset name="gcc-4.5*"/>
            <toolset name="gcc-4.6*"/>
            <toolset name="gcc-4.7*"/>
            <toolset name="gcc-4.8*"/>
            <toolset name="gcc-4.9*"/>
            <toolset name="gcc-mingw-*"/>
            <toolset name="darwin-4.2*"/>
            <toolset name="darwin-4.3*"/>
            <toolset name="darwin-4.4*"/>
            <toolset name="clang-darwin-4.2.1"/>
            <toolset name="clang-darwin-asan"/>
            <toolset name="clang-darwin-tot"/>
            <toolset name="clang-darwin-trunk"/>
            <toolset name="clang-darwin-normal"/>
            <toolset name="clang-linux-*"/>
            <toolset name="intel-linux-*"/>
            <toolset name="intel-darwin-*"/>
            <note author="Daniel James">
                GCC's libstdc++ has a versioned namespace feature which breaks
                container forwarding. I don't know how to detect it so I'm just
                always disabling it, which means that a lot of setups which
                means that it's disabled for a lot of setups where it could
                work - which is what these failures represent.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="container_fwd"/>
            <test name="container_fwd_debug"/>
            <test name="container_no_fwd_test"/>
            <toolset name="msvc-9.0~wm5~stlport5.2"/>
            <note author="Daniel James">
            Failing because these tests are run with warnings as errors,
            and the standard library is causing warnings.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="container_fwd_debug"/>
            <toolset name="sun-5.10"/>
            <note author="Daniel James">
            STLport debug mode seems to be broken here.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="container_fwd_debug"/>
            <toolset name="clang-darwin-0x"/>
            <toolset name="clang-darwin-normal"/>
            <toolset name="clang-darwin-trunk"/>
            <note author="Daniel James">
            Some old versions of GCC's libstdc++ don't work on clang with
            _GLIBCXX_DEBUG defined.
            http://lists.cs.uiuc.edu/pipermail/cfe-dev/2011-May/015178.html
            </note>
        </mark-expected-failures>
    </library>

    <!-- dynamic_bitset -->
    <library name="dynamic_bitset">
        <test name="dyn_bitset_unit_tests1">
            <mark-failure>
                <toolset name="msvc-6.5_stlport4"/>
                <note author="Gennaro Prota" refid="37" />
            </mark-failure>
        </test>
        <test name="dyn_bitset_unit_tests2">
            <mark-failure>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="Roland Schwarz">
                    The exact reason of this (linker related) bug is unresearched. The test passes
                    on some environments. The test was found to fail on a platform whit a german
                    version of the compiler.
                </note>
            </mark-failure>
        </test>
        <test name="dyn_bitset_unit_tests4">
            <mark-failure>
                <toolset name="cw-9.3"/>
                <note author="Aleksey Gurtovoy" refid="2"/>
            </mark-failure>
            <mark-failure>
                <toolset name="cw-9.3-darwin"/>
                <note author="Douglas Gregor" refid="2"/>
            </mark-failure>
            <mark-failure>
                <toolset name="sunpro-5_3-sunos"/>
                <note author="Douglas Gregor" refid="2"/>
            </mark-failure>
        </test>
    </library>
    
    <!-- fiber -->
    <library name="fiber">
      <mark-unusable>
        <toolset name="cray-*"/>
        <toolset name="darwin-4.4"/>
        <toolset name="darwin-4.4*"/>
        <toolset name="gcc-4.4"/>
        <toolset name="gcc-4.4*"/>
        <toolset name="gcc-4.7*"/>
        <toolset name="gcc-4.7"/>
        <toolset name="gcc-m*ngw-4.4"/>
        <toolset name="gcc-m*ngw-4.4*"/>
        <toolset name="gcc-m*ngw-4.5"/>
        <toolset name="gcc-m*ngw-4.5*"/>
        <toolset name="gcc-m*ngw-4.6"/>
        <toolset name="gcc-m*ngw-4.6*"/>
        <toolset name="gcc-m*ngw-4.7"/>
        <toolset name="gcc-m*ngw-4.7*"/>
        <toolset name="msvc-7.*"/>
        <toolset name="msvc-8.0"/>
        <toolset name="pgi-*"/>
        <toolset name="vacpp-*"/>
      </mark-unusable>
    </library>


    <!-- filesystem -->
    <library name="filesystem">
        <mark-unusable>
          <toolset name="borland-5.6*"/>
          <toolset name="borland-5.8*"/>
          <note author="Beman Dawes">
              This compiler does not support enable_if, which is needed by the
              Boost.System library on which Boost.Filesystem depends.
          </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="intel-7.1-linux"/>
            <toolset name="intel-7.1-stdlib-default-linux"/>
            <note author="Aleksey Gurtovoy">
                Due to standard library bugs this configuration is not supported by
                the most recent version of the library.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="cw-8.3"/>
            <note author="Beman Dawes">
                Due to standard library bugs, this version is not supported.
                More recent version of the library should work OK.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="msvc-8.0~wm5~stlport5.1"/>
            <note author="Beman Dawes">
                Due to lack of C library featues, this toolset is not supported.
            </note>
        </mark-unusable>
        <mark-expected-failures>
          <test name="*"/>
          <toolset name="sun-5.7"/>
          <toolset name="sun-5.8"/>
          <note author="Beman Dawes">
              The library works well with versions of this compiler 5.9 and later
          </note>
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="fstream_test"/>
          <toolset name="msvc-6.5*"/>
          <note author="Beman Dawes">
              fstream for this compiler has serious problems and is not supported
          </note>
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="operations_test_dll"/>
          <test name="path_test_dll"/>
          <toolset name="borland-5.6*"/>
          <toolset name="borland-5.8*"/>
          <toolset name="borland-5.9*"/>
          <toolset name="gcc-3.4.2_mingw"/>
          <toolset name="gcc-3.4.2_mingw"/>
          <note author="Beman Dawes" refid="35"/> <!-- dll's don't work - use static -->
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="operations_test"/>
          <test name="operations_test_dll"/>
            <toolset name="msvc-6.5*"/>
          <note author="Beman Dawes" refid="31"/> <!-- esoteric features don't work -->
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="mbcopy"/>
          <test name="mbpath"/>
          <test name="wide_test"/>
          <toolset name="gcc-3.4.2_mingw"/>
          <toolset name="gcc-3.4.5_mingw"/>
          <toolset name="gcc-mingw-3.4.5"/>
          <toolset name="gcc-mingw-3.4.2"/>
          <toolset name="gcc-cygwin-3.4.4"/>
          <note author="Beman Dawes" refid="19"/> <!-- no wchar_t, wstring support -->
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="mbcopy"/>
          <test name="mbpath"/>
          <test name="wide_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="cw-8.3"/>
            <toolset name="dmc-8_4_7*"/>
          <note author="Beman Dawes">
              The library does not support wide paths on this compiler because
              it does not support SFINAE.
          </note>
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="mbcopy"/>
          <test name="mbpath"/>
          <test name="wide_test"/>
          <toolset name="qcc-3.3.5_gpp"/>
          <note author="Jim Douglas" refid="36"/>
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="mbcopy"/>
          <test name="wide_test"/>
          <toolset name="sun-5.8"/>
           <note author="John Maddock">
              These failures are reported to be fixed in Sun's
              next compiler release.
           </note>
        </mark-expected-failures>
      </library>

    <!-- flyweight -->
    <library name="flyweight">
        <mark-expected-failures>
            <test name="test_intermod_holder"/>
            <toolset name="borland-5.*"/>
            <toolset name="borland-6.10.0"/>
            <toolset name="sun-5.*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="cw-9.*"/>
            <toolset name="gcc-2.95*"/>
            <toolset name="gcc-3.0*"/>
            <toolset name="gcc-3.1*"/>
            <toolset name="gcc-3.2*"/>
            <toolset name="gcc-3.3*"/>
            <toolset name="gcc-4.2.1_hpux_ia64"/>
            <toolset name="mipspro"/>
            <toolset name="acc*"/>
            <toolset name="msvc-8.0~wm5*"/>
            <toolset name="vacpp*"/>
            <toolset name="intel-linux-8.*"/>
            <toolset name="gcc-3.4.6_linux_ia64"/>
            <note author="J. L&#195;&#179;pez" date="03 Dec 2008">
                This compiler does not support Boost.Interprocess,
                on which intermodule_holder depends.
            </note>
        </mark-expected-failures>
    </library>

    <!-- foreach -->
    <library name="foreach">
        <mark-unusable>
            <toolset name="dmc*"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="Eric Niebler">
                This compiler does not support the Boost.Range
                library, on which Boost.Foreach depends.
            </note>
        </mark-unusable>

        <mark-expected-failures>
            <test name="rvalue_const"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="borland-6.0*"/>
            <toolset name="borland-6.1*"/>
            <toolset name="gcc-2*"/>
            <toolset name="gcc-3.2*"/>
            <toolset name="gcc-3_3-darwin"/>
            <toolset name="intel-linux"/>
            <toolset name="vacpp*"/>
            <toolset name="cw-8.3"/>
            <toolset name="cw-9.4"/>
            <toolset name="cw-9.5-darwin"/>
            <toolset name="sunpro*"/>
            <toolset name="mingw"/>
            <toolset name="hp_cxx*"/>
            <toolset name="intel-win32-8_1"/>
            <toolset name="sun-5.7"/>
            <toolset name="sun-5.8"/>
            <toolset name="sun-5.9"/>
            <toolset name="sun-5.10"/>
            <note author="Eric Niebler">
                This compiler does not support detection of
                const rvalues.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="rvalue_const_r"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="borland-6.0*"/>
            <toolset name="borland-6.1*"/>
            <toolset name="gcc-2*"/>
            <toolset name="gcc-3.2*"/>
            <toolset name="gcc-3_3-darwin"/>
            <toolset name="intel-linux"/>
            <toolset name="vacpp*"/>
            <toolset name="cw-8.3"/>
            <toolset name="cw-9.4"/>
            <toolset name="cw-9.5-darwin"/>
            <toolset name="sunpro*"/>
            <toolset name="mingw"/>
            <toolset name="hp_cxx*"/>
            <toolset name="intel-win32-8_1"/>
            <toolset name="sun-5.7"/>
            <toolset name="sun-5.8"/>
            <toolset name="sun-5.9"/>
            <toolset name="sun-5.10"/>
            <note author="Eric Niebler">
                This compiler does not support detection of
                const rvalues.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="rvalue_nonconst"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="borland-6.0*"/>
            <toolset name="borland-6.1*"/>
            <toolset name="hp_cxx*"/>
            <toolset name="sunpro*"/>
            <toolset name="sun-5.7"/>
            <toolset name="sun-5.8"/>
            <toolset name="sun-5.9"/>
            <toolset name="sun-5.10"/>
            <note author="Eric Niebler">
                This compiler does not support detection of
                rvalues.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="rvalue_nonconst_r"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="borland-6.0*"/>
            <toolset name="borland-6.1*"/>
            <toolset name="hp_cxx*"/>
            <toolset name="sunpro*"/>
            <toolset name="sun-5.7"/>
            <toolset name="sun-5.8"/>
            <toolset name="sun-5.9"/>
            <toolset name="sun-5.10"/>
            <note author="Eric Niebler">
                This compiler does not support detection of
                rvalues.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="dependent_type"/>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="hp_cxx-65*"/>
            <note author="Eric Niebler">
                These compilers cannot handle BOOST_FOREACH
                in a template, where the collection type
                depends on a template parameter.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="user_defined"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0*"/>
            <note author="Eric Niebler">
                This failure is because the Boost.Range extension
                mechanism is broken on these compilers. It requires
                ADL which these compilers do not support.
            </note>
        </mark-expected-failures>
    </library>

    <!-- format -->
    <library name="format">
        <mark-unusable>
            <toolset name="iw-7_1*"/>
            <note author="Aleksey Gurtovoy">
                The failure is caused by a standard library bug: the
                iostream components fail to handle <code>ios::internal</code>
                flag.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="sunpro-5_3-sunos"/>
        </mark-unusable>
        <mark-expected-failures>
            <test name="format_test2"/>
            <test name="format_test3"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="acc*"/>
            <note author="Markus Schoepflin" refid="33"/>
        </mark-expected-failures>
    </library>

    <!-- function_types -->
    <library name="function_types">
        <mark-expected-failures>
            <test name="member_ccs"/>
            <test name="member_ccs_exact"/>
            <toolset name="*"/>
            <note author="Tobias Schwinger">
              Not all compilers/platforms implement nonstandard calling conventions.
              <hr/>
              With GCC/x86 this failure reflects
              http://gcc.gnu.org/bugzilla/show_bug.cgi?id=29328 .
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="nonmember_ccs"/>
            <test name="nonmember_ccs_exact"/>
            <toolset name="*"/>
            <note author="Tobias Schwinger">
              Not all compilers/platforms implement nonstandard calling conventions.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="interface_example"/>
            <toolset name="msvc-7.1*"/>
            <note author="Tobias Schwinger">
              Overload selection does not work in some assignment contexts with this compiler.
            </note>
        </mark-expected-failures>
    </library>

    <!-- functional/factory -->
    <library name="functional/factory">
        <mark-expected-failures>
            <test name="factory_with_allocator"/>
            <toolset name="borland-*"/>
            <note author="Tobias Schwinger">
              Probably broken const conversion with that compiler.
            </note>
        </mark-expected-failures>
    </library>

    <!-- functional/forward -->
    <library name="functional/foward">
        <mark-unusable>
            <toolset name="msvc-7.0*"/>
            <toolset name="msvc-7.1*"/>
            <toolset name="sun-5.*"/>
            <toolset name="vacpp*"/>
            <toolset name="borland-*"/>
            <note author="Tobias Schwinger">
              This compiler is currently not supported.
            </note>
        </mark-unusable>
    </library>

    <!-- functional/hash -->
    <library name="functional/hash">
        <mark-expected-failures>
            <test name="hash_value_array_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0*"/>
            <note author="Daniel James">
              hash_value is not overloaded for arrays for older versions
              of Visual C++. There is a work around so that
              boost::hash&lt;T[N]&gt;, boost::hash_combine and boost::hash_range
              work.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="hash_function_pointer_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0*"/>
            <note refid="2" author="Daniel James"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="hash_function_pointer_test"/>
            <toolset name="sun-5.7"/>
            <toolset name="sun-5.8"/>
            <toolset name="sun-5.9"/>
            <note author="Daniel James">
                On these compilers the wrong overload of hash_value is called
                when the argument is a hash function pointer. So calling
                hash_value doesn't work but boost::hash does work (and it's
                recommended that user never call hash_value directly so this
                shouldn't be a problem).
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="hash_long_double_test"/>
            <toolset name="gcc-3.4.3_sunos"/>
            <toolset name="*pa_risc"/>
            <note author="Daniel James">
                This platform has poor support for <code>long double</code> so
                the hash function perform poorly for values out of the range
                of <code>double</code> or if they differ at a greater precision
                that <code>double</code> is capable of representing.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="point" />
            <test name="books" />
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0*"/>
            <note author="Daniel James">
                These examples only work on compilers with support for ADL.
                It is possible to work around this, but I wanted to keep the
                example code as clean as possible.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="point" />
            <toolset name="borland-*"/>
            <note author="Daniel James">
                It appears that Borland doesn't find friend functions defined
                in a class by ADL. This is easily fixed but this example is
                meant to show the typical way of customising boost::hash, not
                the portable way.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="hash_global_namespace_test" />
            <toolset name="borland-*"/>
            <note author="Daniel James">
                The test demonstrates a Borland bug - functions that aren't
                in a namespace don't appear to be found by ADL.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="container_fwd_gcc_debug"/>
            <toolset name="darwin-4.2"/>
            <note author="Daniel James">
              Debug containers aren't supported on Apple's version of gcc 4.2.
            </note>
        </mark-expected-failures>
    </library>

    <!-- fusion -->
    <library name="fusion">
        <mark-expected-failures>
            <test name="define_struct_inline_move"/>
            <test name="define_tpl_struct_inline_move"/>
            <toolset name="msvc-10.0"/>
            <toolset name="msvc-11.0"/>
            <toolset name="msvc-12.0"/>
            <toolset name="qcc-4.4.2_x86"/>
            <toolset name="gcc-4.4~c++0x*"/>
            <toolset name="gcc-4.4~gnu0x*"/>
            <note author="Kohei Takahashi">
                The compiler doesn't generate defaulted move ctor/assgin thus
                perform copy construction/assginment. Even though such case,
                the `inline` versions don't force generating move ctor/assign
                to preserve trivial requirements. Since that is not documented
                behaviour, it might be changed in future release.
            </note>
        </mark-expected-failures>
    </library>

    <!-- geometry -->
    <library name="geometry">
        <mark-unusable>
            <toolset name="borland-*"/>
            <toolset name="sun-5.10"/>
            <toolset name="vacpp*"/>
            <toolset name="msvc-7.1"/>
            <toolset name="*_fno_rtti"/>
            <toolset name="cray-8.0"/>
            <note author="Barend Gehrels">
                These compilers do not support features that are essential for the library.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="intel-darwin-10*"/>
            <toolset name="intel-darwin-11*"/>
            <toolset name="intel-darwin-12*"/>
            <note author="Barend Gehrels">
                Intel 11.1 and 12.0 on Darwin raises a SIGSEGV in almost all unit tests.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="intel-linux-13*"/>
            <note author="Barend Gehrels" date="12 Aug 2013">
                Intel 13.1.3 does not support BOOST_TEST
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="pgi-11*"/>
            <note author="Barend Gehrels">
                pgi 11.1 does not support BOOST_AUTO and is not configured for UBLAS
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="msvc-9.0~wm5~stlport5.2"/>
            <note author="Barend Gehrels">
                This configuration is not well configured for UBLAS
            </note>
        </mark-unusable>
        <test name="algorithms_*" category="Algorithms">
        </test>
        <test name="arithmetic_*" category="Arithmetic">
        </test>
        <test name="concepts_*" category="Concepts">
        </test>
        <test name="core_*" category="Core">
        </test>
        <test name="formulas_*" category="Formulas">
        </test>
        <test name="geometries_*" category="Geometries">
        </test>
        <test name="io_*" category="IO">
        </test>
        <test name="iterators_*" category="Iterators">
        </test>
        <test name="policies_*" category="Policies">
        </test>
        <test name="strategies_*" category="Strategies">
        </test>
        <test name="util_*" category="Util">
        </test>
        <test name="views_*" category="Views">
        </test>
    </library>
    
    <library name="geometry/extensions">
        <mark-unusable>
            <toolset name="borland-*"/>
            <toolset name="sun-5.10"/>
            <toolset name="vacpp*"/>
            <toolset name="msvc-7.1"/>
            <toolset name="*_fno_rtti"/>
            <toolset name="cray-8.0"/>
            <toolset name="intel-darwin-*"/>
            <toolset name="intel-linux-13*"/>
            <toolset name="pgi-11*"/>
            <toolset name="msvc-9.0~wm5~stlport5.2"/>
        </mark-unusable>
    </library>

    <library name="geometry/index">
        <mark-unusable>
            <toolset name="borland-*"/>
            <toolset name="sun-5.10"/>
            <toolset name="vacpp*"/>
            <toolset name="msvc-7.1"/>
            <toolset name="*_fno_rtti"/>
            <toolset name="cray-8.0"/>
            <toolset name="intel-darwin-*"/>
            <toolset name="intel-linux-13*"/>
            <toolset name="pgi-11*"/>
            <toolset name="msvc-9.0~wm5~stlport5.2"/>
        </mark-unusable>
    </library>

    <!-- gil -->
    <library name="gil">
      <mark-expected-failures>
          <test name="pixel"/>
          <toolset name="acc"/>
          <note author="Boris Gubenko" refid="46"/>
      </mark-expected-failures>
      <mark-expected-failures>
          <test name="image"/>
          <toolset name="acc"/>
          <note author="Boris Gubenko" refid="47"/>
      </mark-expected-failures>
    </library>

    <!-- graph -->
    <library name="graph">
        <mark-unusable>
            <toolset name="borland-5.*"/>
            <toolset name="borland-6.*"/>
            <toolset name="borland-cb2009"/>
            <toolset name="borland-cb2010"/>
            <toolset name="sun-5.9"/>
        </mark-unusable>
        <mark-expected-failures>
          <test name="grid_graph_cc"/>
          <test name="grid_graph_test"/>
          <toolset name="msvc-7.1"/>
          <note author="Jeremiah Willcock" refid="2"/>
        </mark-expected-failures>
    </library>

	<!-- icl -->
	<library name="icl">
		<mark-unusable>
			<toolset name="borland-cb2009"/>
			<toolset name="borland-cb2010"/>
			<toolset name="sun-5.10"/>
			<toolset name="vacpp"/>
			<note author="Joachim Faulhaber">
				The compiler does not support features that are essential for the library.
			</note>
		</mark-unusable>
		<mark-expected-failures>
			<test name="cmp_msvc_value_born_error"/>
			<toolset name="msvc-7.1"/>
			<toolset name="msvc-8.0"/>
			<toolset name="msvc-9.0*"/>
			<toolset name="msvc-10.0*"/>
			<toolset name="msvc-11.0*"/>
			<toolset name="msvc-12.0*"/>
			<toolset name="msvc-14.0*"/>
			<note author="Joachim Faulhaber">
				Compiler error expected for msvc: A minimal example of a class template 'value' that
				results in syntax error in a subsequent meta function.
				See <a
		            href="https://svn.boost.org/trac/boost/ticket/5141">
					ticket #5141
				</a> for details.
			</note>
		</mark-expected-failures>
	</library>

	<!-- integer -->
	<library name="integer">
		<mark-expected-failures>
			<test name="integer_test"/>
			<toolset name="acc"/>
			<toolset name="gcc-4.2.1_hpux_ia64"/>
			<note author="Boris Gubenko">
				When compiling with aC++, depending on system load, the compile time may exceed
				specified timeout value. The test passes when the timeout value is increased.
				When compiling with GCC, linker takes segmentation fault.
				In the HP bug tracking system, this issue is tracked as QuIX ID: QXCR1000836120.
			</note>
		</mark-expected-failures>
	</library>

    <!-- interprocess-->
    <library name="interprocess">
        <mark-unusable>
            <toolset name="borland-5.*"/>
            <toolset name="sun-5.*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="cw-9.*"/>
            <toolset name="gcc-2.95*"/>
            <toolset name="gcc-3.0*"/>
            <toolset name="gcc-3.1*"/>
            <toolset name="gcc-3.2*"/>
            <toolset name="gcc-3.3*"/>
            <toolset name="gcc-4.2.1_hpux_ia64"/>
            <toolset name="mipspro"/>
            <toolset name="acc*"/>
            <toolset name="msvc-8.0~wm5*"/>
            <toolset name="pathscale*"/>
            <toolset name="intel-linux-8.*"/>
            <toolset name="gcc-3.4.6_linux_ia64"/>
            <note author="Ion Gazta&#241;aga">
                The compiler does not support features that are essential for the library.
            </note>
        </mark-unusable>
    </library>

    <!-- intrusive-->
    <library name="intrusive">
        <mark-unusable>
            <toolset name="borland-5.*"/>
            <toolset name="sun-5.*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="cw-9.*"/>
            <toolset name="gcc-2.95*"/>
            <toolset name="gcc-3.0*"/>
            <toolset name="gcc-3.1*"/>
            <toolset name="gcc-3.2*"/>
            <toolset name="gcc-3.3*"/>
            <toolset name="mipspro"/>
            <toolset name="intel-linux-8.*"/>
            <note author="Ion Gazta&#241;aga">
                The compiler does not support features that are essential for the library.
            </note>
        </mark-unusable>
        <mark-expected-failures>
            <test name="doc_offset_ptr" />
            <toolset name="acc"/>
            <toolset name="gcc-3.4.2_hpux_pa_risc"/>
            <toolset name="gcc-3.4.6_linux_ia64"/>
            <note author="Ion Gazta&#241;aga">
                The compiler is not supported by Interprocess.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="unordered_multiset_test"/>
          <test name="unordered_set_test"/>
          <toolset name="acc"/>
          <note author="Boris Gubenko" refid="47"/>
        </mark-expected-failures>
    </library>

    <!-- io-->
    <library name="io">
        <mark-expected-failures>
            <test name="ios_state_unit_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="iw-7_1-vc6*"/>
            <toolset name="msvc-6.5*"/>
            <note refid="4" author="Aleksey Gurtovoy"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ios_state_test"/>
            <test name="ios_state_unit_test"/>
            <toolset name="hp_cxx-65*"/>
            <note refid="34" author="Markus Schoepflin"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ios_state_unit_test"/>
            <toolset name="gcc-2.95.3-*"/>
            <note refid="3" author="Doug Gregor"/>
        </mark-expected-failures>
       <mark-expected-failures>
          <test name="ios_state_unit_test"/>
          <toolset name="gcc-4.1.0*"/>
          <note author="John Maddock">
             This is gcc bug 26526, and is fixed in later releases.
          </note>
       </mark-expected-failures>
    </library>

    <!-- iostreams -->
    <library name="iostreams">
        <mark-expected-failures>
            <test name="auto_close_test"/>
            <test name="component_access_test"/>
            <test name="compose_test"/>
            <test name="counter_test"/>
            <test name="filtering_stream_test"/>
            <test name="flush_test"/>
            <test name="line_filter_test"/>
            <test name="newline_test"/>
            <test name="pipeline_test"/>
            <test name="regex_filter_test"/>
            <test name="restrict_test"/>
            <test name="seekable_file_test"/>
            <test name="seekable_filter_test"/>
            <test name="sequence_test"/>
            <test name="slice_test"/>
            <test name="stdio_filter_test"/>
            <test name="tee_test"/>
            <test name="wide_stream_test"/>
            <toolset name="sun-5.7"/>
            <toolset name="sun-5.8"/>
            <note author="Jonathan Turkanis" date="09 Jan 2008" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="bzip2_test"/>
            <toolset name="gcc-3.4.3_sunos"/>
            <note author="Caleb Epstein">
                No bzip2 support on the testing machine and no way to
                disable this test with BBv2 at present.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="bzip2_test"/>
            <test name="file_descriptor_test"/>
            <test name="mapped_file_test"/>
            <toolset name="*como-4_3_3*"/>
            <note author="Jonathan Turkanis">
                compiler can't compile "windows.h" in strict mode
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="code_converter_test"/>
            <toolset name="pgi-7.0"/>
            <note author="Jonathan Turkanis">
                This platform lacks the placement delete operator
                required by the C++ standard
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <!-- Insufficient wide character support -->
            <test name="code_converter_test"/>
            <test name="wide_stream_test"/>
            <toolset name="gcc-2.95.3-linux"/>
            <!-- Must enumerate MinGW's since some use STLPort -->
            <toolset name="gcc-3.4.2_mingw"/>
            <toolset name="mingw-3_4_4"/>
            <toolset name="gcc-3.4.5_mingw"/>
            <toolset name="gcc-3.4.5_mingw"/>
            <toolset name="*cygwin*"/>
            <toolset name="gcc-3.3.6-osf1"/>
            <toolset name="gcc-3.4.2_hpux_pa_risc"/>
            <note author="Jonathan Turkanis" refid="19"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <!-- Insufficient wide character support -->
            <test name="code_converter_test"/>
            <test name="wide_stream_test"/>
            <toolset name="qcc-3.3.5*gpp"/>
            <note author="Jim Douglas" date="12 Feb 06" refid="36"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="compose_test"/>
            <toolset name="msvc-6.5_stlport4"/>
            <note author="Jonathan Turkanis">
                These six tests pass individually but cause a compiler stack overflow
                when compiled as a group
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="compose_test"/>
            <toolset name="gcc-3.4.6_linux_ia64"/>
            <note author="Boris Gubenko">
                On this platform, linking this test takes longer than 10 minutes
                which is a time limit specified for bjam. When linked manually,
                the test succeeds.
            </note>
        </mark-expected-failures>
        <mark-expected-failures reason="?">
            <test name="direct_adapter_test"/>
            <test name="gzip_test"/>
            <toolset name="gcc-2.95.3-linux"/>
            <note author="Jonathan Turkanis" refid="29"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="file_descriptor_test"/>
            <toolset name="gcc-cygwin-3.4.4"/>
            <note author="Vladimir Prus">
                The test fails at runtime for unknown reasons.
            </note>
        </mark-expected-failures>
        <mark-expected-failures reason="?">
            <test name="file_descriptor_test"/>
            <toolset name="gcc-3_4_4-cygwin"/>
            <note author="Jonathan Turkanis" refid="29"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="finite_state_filter_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="sun-5.*"/>
            <toolset name="vacpp"/>
            <note author="Jonathan Turkanis" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="finite_state_filter_test"/>
            <toolset name="cw-9.4"/>
            <note author="Jonathan Turkanis" date="20 Dec 06">
                I'm not sure whether CodeWarrior is correct to report that the member
                in question is inaccessible; however, when the member is made public
                an internal error occur that I have not been able to fix, so for
                now the question is moot.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="gzip_test"/>
            <test name="zlib_test"/>
            <toolset name="como-4_3_3-vc7_1"/>
            <note author="Jonathan Turkanis">
                The failure reflects a problem with the build system: the zlib
                object files are generated in the wrong directory.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="mapped_file_test"/>
            <toolset name="qcc-3.3*"/>
            <note author="Jim Douglas" date="19 Feb 06">
                Memory mapped files are not supported in QNX Neutrino version 6.3.0.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="restrict_test"/>
            <toolset name="vacpp"/>
            <note author="Jonathan Turkanis" date="06 Jan 2008">
                "restrict" is treated as a keyword on this platform (as in C99);
                use the alias "slice" instead, defined in
                "boost/iostreams/slice.hpp."
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <!-- STLPort bug -->
            <test name="seekable_file_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="iw-7_1-vc6-stlp-4_5_3"/>
            <toolset name="*como-4_3_3*"/>
            <toolset name="sun-5.*"/>
            <toolset name="*stlport"/>
            <toolset name="pgi-7.0"/>
            <note author="Jonathan Turkanis" refid="4"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="stdio_filter_test"/>
            <toolset name="*como-4_3_3*"/>
            <note author="Jonathan Turkanis" refid="0"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="stream_offset_64bit_test"/>
            <toolset name="borland-*"/>
            <note author="Jonathan Turkanis" date="04 Jan 2008">
                In the Dinkumware standard library, streampos relies on fpos_t
                to store stream offsets, but fpos_t is defined as a 32-bit
                long by the Borland runtime library. In Borland's modified
                version of STLPort, streampos relies on streamoff to store
                stream offsets, but streamoff is defined to be a 32-bit long.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="stream_offset_64bit_test"/>
            <toolset name="sun-5.*"/>
            <note author="Jonathan Turkanis" date="06 Jan 2008">
                In STLPort, streampos consists of a long together with a
                conversion state; on this platform, long is a 32-bit type
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="stream_offset_64bit_test"/>
            <toolset name="vacpp*"/>
            <note author="Jonathan Turkanis" date="09 Jan 2008">
                On this platform, streampos is an alias for fpos, whose
                implementation stores stream offsets using streamsize and
                fpos_t; both of the latter types are 32-bit
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="stream_offset_64bit_test"/>
            <toolset name="intel-win-10.0_stdcxx_421"/>
            <toolset name="msvc-7.1_stdcxx_421"/>
            <toolset name="msvc-9.0_stdcxx_421"/>
            <toolset name="intel-win-10.1_stdcxx_421"/>
            <toolset name="intel-linux-10.1_stdcxx_421"/>
            <toolset name="gcc-4.2.1_stdcxx_421"/>
            <note author="Jonathan Turkanis" date="09 Jan 2008">
                On this platform, streampos is an alias for ptrdiff_t, which
                is an alias for a 32-bit type
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="stream_offset_64bit_test"/>
            <toolset name="gcc-4.2"/>
            <note author="Jonathan Turkanis" date="09 Jan 2008">
              The following applies only to gcc-4.2 using the stdcxx
              standard library: On this platform, streampos is an alias for
              ptrdiff_t, which is an alias for a 32-bit type
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="path_test_filesystem2"/>
            <toolset name="msvc-7.1"/>
            <note author="Daniel James" date="07 Jun 2011">
              Visual C++ 7.1's SFINAE implementation can't deal with multiple
              assignment functions with the same parameters (but disabled with
              SFINAE). So path's assignment from boost::filesystem2::path is
              disabled. Would be pretty easy to workaround, but probably not
              worth it since this is pretty obscure, and filesystem2 will be
              removed soon.
            </note>
        </mark-expected-failures>
    </library>

    <!-- json -->
    <library name="json">
        <mark-unusable>
            <toolset name="*c++98"/>
            <toolset name="*gnu98"/>
            <toolset name="*c++0x"/>
            <toolset name="*gnu0x"/>
            <toolset name="gcc-4.7*"/>
            <toolset name="msvc-7.1"/>
            <toolset name="msvc-8.*"/>
            <toolset name="msvc-9.*"/>
            <toolset name="msvc-10.*"/>
            <toolset name="msvc-11.*"/>
            <toolset name="msvc-12.*"/>
            <note author="Vinnie Falco">C++11 is the minimum requirement.</note>
        </mark-unusable>
    </library>

    <!-- lambda -->
    <library name="lambda">
        <mark-unusable>
            <toolset name="msvc-6.5*"/>
            <toolset name="borland-5.5*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note refid="17">
            </note>
        </mark-unusable>
        <mark-expected-failures>
            <test name="bll_and_function"/>
            <toolset name="msvc-8.0"/>
            <note author="Aleksey Gurtovoy" refid="6"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="member_pointer_test"/>
            <toolset name="gcc-2.95.3-*"/>
            <note author="Doug Gregor" refid="3"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="control_structures"/>
            <toolset name="gcc-4.2.1*"/>
            <note author="Boris Gubenko" refid="42"/>
        </mark-expected-failures>
    </library>

    <!-- local_function -->
    <library name="local_function">
        <mark-unusable>
            <!-- most Preprocessor tests also fail on this compiler -->
            <toolset name="cray-8.0"/>
        </mark-unusable>
        <!-- variadic macros required -->
        <mark-expected-failures>
            <test name="add"/>
            <test name="add_classifiers"/>
            <test name="add_default"/>
            <test name="add_except"/>
            <test name="add_inline"/>
            <test name="add_params_only"/>
            <test name="add_template"/>
            <test name="add_this"/>
            <test name="add_typed"/>
            <test name="add_with_default"/>
            <test name="all_decl"/>
            <test name="factorial"/>
            <test name="goto"/>
            <test name="macro_commas"/>
            <test name="nesting"/>
            <test name="operator"/>
            <test name="overload"/>
            <test name="return_assign"/>
            <test name="return_derivative"/>
            <test name="return_inc"/>
            <test name="return_setget"/>
            <test name="return_this"/>
            <test name="same_line"/>
            <test name="transform"/>
            <test name="typeof"/>
            <test name="typeof_template"/>
            <toolset name="intel-darwin-11.1"/>
            <toolset name="intel-linux-10.1"/>
            <toolset name="intel-linux-11.1"/>
            <toolset name="pgi-11.9"/>
            <toolset name="sun-5.10"/>
            <note author="Lorenzo Caminiti" refid="51"/>
        </mark-expected-failures>
        <!-- auto-declarations not allowed -->
        <mark-expected-failures>
            <test name="add_classifiers"/>
            <test name="add_classifiers_seq"/>
            <test name="add_classifiers_seq_nova"/>
            <toolset name="clang-darwin-trunkLX"/>
            <toolset name="clang-darwin-trunkRX"/>
            <toolset name="darwin-4.4_0x"/>
            <toolset name="gcc-4.4.3_0x"/>
            <toolset name="gcc-4.4.4_0x"/>
            <toolset name="gcc-4.5.3_0x"/>
            <toolset name="gcc-4.6.2_0x"/>
            <toolset name="gcc-mingw-4.5.2_0x"/>
            <toolset name="msvc-10.0"/>
            <toolset name="msvc-11.0"/>
            <toolset name="msvc-12.0"/>
            <toolset name="msvc-14.0"/>
            <note author="Lorenzo Caminiti">
                This test does not allow C++11 auto-declaration support
                (because it uses the `auto` keyword as storage classifier).
            </note>
        </mark-expected-failures>
    </library>

	<!-- log -->
	<library name="log">
		<mark-unusable>
			<toolset name="vacpp-12.1*"/>
			<note author="Andrey Semashev" date="02 May 2013">
				The compiler fails to compile Boost.Move and Boost.PropertyTree, which are used by this library.
			</note>
		</mark-unusable>
		<mark-unusable>
			<toolset name="msvc-9.0~w*"/>
			<note author="Andrey Semashev" date="02 May 2013">
				Boost.Filesystem used by Boost.Log does not support Windows Mobile.
			</note>
		</mark-unusable>
	</library>

    <!-- logic -->
    <library name="logic">
      <test name="tribool_io_test">
        <mark-failure>
          <toolset name="msvc-6.5_stlport4"/>
          <toolset name="gcc-2.95.3-linux"/>
          <toolset name="sunpro-5_3-sunos"/>
          <toolset name="hp_cxx-65*"/>
          <note author="Douglas Gregor" refid="4"/>
        </mark-failure>
      </test>
    </library>

    <!-- move-->
    <library name="move">
        <mark-unusable>
            <toolset name="borland-5.*"/>
            <toolset name="sun-5.*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="cw-9.*"/>
            <toolset name="gcc-2.95*"/>
            <toolset name="gcc-3.0*"/>
            <toolset name="gcc-3.1*"/>
            <toolset name="gcc-3.2*"/>
            <toolset name="gcc-3.3*"/>
            <toolset name="mipspro"/>
            <toolset name="intel-linux-8.*"/>
            <note author="Ion Gazta&#241;aga">
                The compiler does not support features that are essential for the library.
            </note>
        </mark-unusable>
    </library>

    <!-- MPL -->
    <library name="mpl">

        <mark-unusable>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="Aleksey Gurtovoy" date="10 Jul 2005">
                The compiler is not supported by the library due to an
                utterly broken templates support.
            </note>
        </mark-unusable>

        <mark-expected-failures>
            <test name="as_sequence"/>
            <test name="is_sequence"/>
            <test name="has_xxx"/>
            <test name="no_has_xxx"/>
            <test name="single_view"/>
            <toolset name="cw-8.3*"/>
            <note author="Aleksey Gurtovoy" date="17 Sep 2004">
                This failure is caused by a deficient SFINAE implementation; the bug
                was fixed in the next major compiler version (CodeWarrior 9.x).
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="is_sequence"/>
            <test name="as_sequence"/>
            <test name="has_xxx"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="gcc-2.95.3*"/>
            <note author="Aleksey Gurtovoy" date="17 Sep 2004">
                This failure is caused by a deficient SFINAE implementation.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="arithmetic"/>
            <test name="at"/>
            <test name="back"/>
            <test name="bitwise"/>
            <test name="contains"/>
            <test name="copy"/>
            <test name="count"/>
            <test name="count_if"/>
            <test name="deque"/>
            <test name="distance"/>
            <test name="find_if"/>
            <test name="for_each"/>
            <test name="front"/>
            <test name="insert"/>
            <test name="insert_range"/>
            <test name="joint_view"/>
            <test name="numeric_ops"/>
            <test name="pair_view"/>
            <test name="partition"/>
            <test name="range_c"/>
            <test name="remove"/>
            <test name="reverse"/>
            <test name="sort"/>
            <test name="stable_partition"/>
            <test name="transform"/>
            <test name="unpack_args"/>
            <test name="vector"/>
            <test name="vector_c"/>

            <toolset name="borland-5.8.1"/>

            <note author="A. Meredith" date="17 May 2006">
                This failure is caused by a problem with recursive templates and default template parameters, fixed in Update 2.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="apply"/>
            <test name="multiset"/>
            <test name="zip_view"/>

            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Aleksey Gurtovoy" date="17 Sep 2004" refid="26"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="assert"/>
            <test name="at"/>
            <test name="back"/>
            <test name="front"/>
            <test name="has_xxx"/>
            <test name="multiset"/>
            <test name="no_has_xxx"/>
            <test name="zip_view"/>

            <toolset name="mipspro"/>
            <note author="Aleksey Gurtovoy" date="17 Sep 2004" refid="26"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="quote"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="mipspro"/>
            <note author="Aleksey Gurtovoy" date="17 Sep 2004">
                This failure is caused by a lack of compiler support for template template
                parameters.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="map"/>
            <test name="set"/>
            <test name="set_c"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="mipspro"/>
            <note author="Aleksey Gurtovoy" date="17 Sep 2004">
                This is an advanced functionality that hasn't been ported to the deficient
                compilers (yet). Patches are welcome!
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="map"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Aleksey Gurtovoy" date="17 Sep 2004">
                This is an advanced functionality that hasn't been ported to the deficient
                compilers (yet). Patches are welcome!
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="apply"/>
            <toolset name="gcc-4.1.*"/>
            <note author="Caleb Epstein">
              This is a regression in the gcc 4.1 series that has been
              fixed in gcc 4.2.0.  See <a
              href="http://gcc.gnu.org/bugzilla/show_bug.cgi?id=28088">bug
              #28088</a> for details.
            </note>
        </mark-expected-failures>
       <mark-expected-failures>
          <test name="vector_c"/>
          <toolset name="sun-5.8"/>
          <note author="John Maddock">
             This is reported to be fixed in the next Sun
             compiler release.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
            <test name="copy"/>
            <toolset name="acc"/>
            <note refid="38" author="Boris Gubenko"/>
       </mark-expected-failures>

    </library>

    <!-- multi_array -->
    <library name="multi_array">
        <mark-unusable>
            <toolset name="borland-5.5*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Alisdair Meredith" date="30 Jan 2004">
                <p>
                This library has never worked [on Borland 5.5.1 and 5.6.4], and the only tests
                that 'pass' are compile-fail tests failing for the wrong reasons!
                </p>
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="Douglas Gregor" refid="3"/>
        </mark-unusable>
        <!-- RG: testing usability <mark-unusable>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <note author="Ronald Garcia" date="08 Jan 2006">
                <p>
          These compiler/standard library combinations don't
          support enable_if.
                </p>
            </note>
        </mark-unusable> -->
        <test name="constructors">
            <mark-failure>
               <toolset name="msvc-6.5"/>
               <note author="Ronald Garcia" date="13 Jul 2004">
                  Known error in MSVC. see
<a href="http://boost-consulting.com/boost/libs/multi_index/doc/compiler_specifics.html#msvc_60">
http://boost-consulting.com/boost/libs/multi_index/doc/compiler_specifics.html#msvc_60</a>
for more information.
               </note>
            </mark-failure>
        </test>
        <mark-expected-failures>
            <test name="assign_to_array"/>
            <toolset name="gcc-2.95.3*"/>
            <note author="Aleksey Gurtovoy" date="21 Sep 2004" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="assign"/>
            <test name="compare"/>
            <test name="concept_checks"/>
            <test name="constructors"/>
            <test name="iterators"/>
            <test name="resize"/>
            <test name="stl_interaction"/>
            <toolset name="gcc-2.95.3*"/>
            <note author="Doug Gregor" date="23 Jun 2005" refid="3"/>
        </mark-expected-failures>
    </library>


    <!-- multi_index -->
    <library name="multi_index">
        <mark-unusable>
            <toolset name="borland-5.6*"/>
            <note author="J. L&#195;&#179;pez" date="05 Jul 2004" refid="17"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="borland-5.8*"/>
            <note author="Alisdair Meredith" date="26 May 2006"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="borland-5.9*"/>
            <note author="Alisdair Meredith" date="27 Feb 2007"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <note author="J. L&#195;&#179;pez" date="09 Jul 2004" refid="17"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="*como-4_3_3-msvc"/>
            <note author="J. L&#195;&#179;pez" date="30 Jul 2004">
                The VC++ 6.0 backend runs out of internal resources while
                trying to process the Comeau output for this library;
                Comeau Computing has been asked about a solution.
                On the other hand, Comeau 4.3.3 with VC++ 7.0 backend works
                fine.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="sunpro-5_8u1-sunos"/>
            <note author="J. L&#195;&#179;pez" date="22 Apr 2005" refid="17"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="dmc-8_43-stlport-4_5_3"/>
            <toolset name="dmc-8_44b-stlport-4_5_3"/>
            <toolset name="dmc-8_47-stlport-4_5_3"/>
            <note author="J. L&#195;&#179;pez" date="03 Jun 2005" refid="17"/>
        </mark-unusable>
        <mark-expected-failures>
            <test name="test_serialization"/>
            <toolset name="msvc-stlport"/>
            <toolset name="msvc-6.5_stlport4"/>
            <note author="J. L&#195;&#179;pez" date="10 Jan 2005">
              This error shows when using the dynamic version of the STLport
              library. The problem is reportedly fixed in STLport 5.0 (in beta
              stage as of this writing.)
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_serialization"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="hp_cxx-71*"/>
            <note author="J. L&#195;&#179;pez" date="16 Mar 2006">
              This test fails due to limitations of the template
              instantiation model used in the testing environment
              (-timplicit_local) resulting in erroneous duplication of some
              function-static variables. The test passes with other template
              instantiation models.
            </note>
        </mark-expected-failures>
    </library>

   <!-- multiprecision -->
   <library name="multiprecision">
      <mark-unusable>
         <toolset name="borland-5.5*"/>
         <toolset name="borland-5.6*"/>
         <toolset name="borland-5.8*"/>
         <toolset name="borland-5.9*"/>
         <note author="John Maddock" date="05 Dec 2012">
            <p>
               These compilers are too antiquated for this library.
            </p>
         </note>
      </mark-unusable>
      <mark-unusable>
         <toolset name="vacpp*"/>
         <toolset name="pgi*"/>
         <toolset name="pgi*"/>
         <toolset name="msvc-8.0"/>
         <toolset name="msvc- 9.0~wm5*"/>
         <note author="John Maddock" date="05 Dec 2012">
            <p>
               These compilers don't quite have the necessary support for this library, it's possible
               that they could be made to work if someone cares enough.
            </p>
         </note>
      </mark-unusable>

      <mark-expected-failures>
         <test name="test_round_cpp_dec_float"/>
         <test name="test_atan_cpp_dec_float"/>
         <test name="sf_concept_check_bessel_cpp_dec_float"/>
         <test name="sf_concept_check_bessel_backend_concept"/>
         <test name="sf_concept_check_basic_cpp_dec_float"/>
         <test name="sf_concept_check_basic_cpp_dec_float_no_et"/>
         <test name="sf_concept_check_basic_backend_concept"/>
         <test name="sf_concept_check_poly_backend_concept"/>
         <test name="sf_concept_check_gamma_backend_concept"/>
         <test name="sf_concept_check_elliptic_cpp_dec_float"/>
         <test name="sf_concept_check_elliptic_backend_concept"/>
         <test name="test_sinh_cpp_dec_float"/>
         <test name="test_sin_cpp_dec_float"/>
         <test name="test_pow_cpp_dec_float"/>
         <test name="test_log_cpp_dec_float"/>
         <test name="test_cos_cpp_dec_float"/>
         <test name="test_atan_cpp_dec_float"/>
         <test name="test_asin_cpp_dec_float"/>
         <toolset name="intel*"/>
         <note author="John Maddock">
            This failure is caused by a test-runner timeout: unfortunately the Intel
            compiler is exceptionally slow at building some of these tests.
         </note>
      </mark-expected-failures>
      <mark-expected-failures>
         <test name="test_arithmetic_cpp_dec_float"/>
         <test name="test_arithmetic_mpfr_50"/>
         <test name="test_arithmetic_mpz"/>
         <toolset name="gcc-4.7"/>
         <note author="John Maddock">
            This failure is caused by a test-runner timeout.
         </note>
      </mark-expected-failures>
   </library>
   
         <!-- optional -->
    <library name="optional">
        <mark-expected-failures>
            <test name="optional_test_ref_converting_ctor"/>
            <toolset name="gcc-4.4*"/>
            <toolset name="gcc-4.2.1"/>
            <toolset name="gcc-4.5*"/>
            <toolset name="gcc-5.0.0"/>
            <toolset name="qcc-4.4.2_x86"/>
            <toolset name="qcc-4.4.2_arm"/>
            <toolset name="gcc-mingw-4.4*"/>
            <toolset name="gcc-mingw-4.5*"/>
            <note author="Andrzej Krzemienski" id="optional-ref-convert-assign-bug">
                <p>This is a compiler bug: it sometimes creates an illegal temporary object.
		The following code illustrates the bug:</p>
		<pre>
		#include &lt;cassert&gt;
		const int global_i = 0;
		
		struct TestingReferenceBinding
		{
		  TestingReferenceBinding(const int&amp; ii)
		  {
		    assert(&amp;ii == &amp;global_i);
  		  }
		};
		
		int main()
		{
		  TestingReferenceBinding ttt = global_i;
		}
                </pre>
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="optional_test_ref"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Aleksey Gurtovoy" refid="3"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="optional_test_ref"/>
            <toolset name="darwin-4.0.1"/>
            <toolset name="darwin-4.2.1"/>
            <toolset name="gcc-mingw-3.4.5"/>
            <toolset name="gcc-mingw-4.4.*"/>
            <toolset name="gcc-mingw-4.5.*"/>
            <toolset name="gcc-3.4.2_hpux_pa_risc"/>
            <toolset name="gcc-3.4.6_linux_ia64"/>
            <toolset name="gcc-4.2.*"/>
            <toolset name="gcc-4.1.2_sunos_i86pc"/>
            <toolset name="gcc-4.3*"/>
            <toolset name="gcc-4.4*"/>
            <toolset name="gcc-4.5*"/>
            <toolset name="qcc-4.4.2_arm"/>
            <toolset name="qcc-4.4.2_x86"/>
                <note author="Fernando Cacciola" id="optional-compiler-bug">
                <p>This failure is caused by a compiler bug, and as far as we can 
                tell, can't be worked around in the library, although we think 
                the library might be made safer with respect to this bug.</p>

                <p>Specifics: the following simple test fails when it should succeed.</p>
                <pre>
                #include &lt;cassert&gt;

                int const x = 0;
                struct A
                {
                   A(int const&amp; y)
                   {
                     assert(&amp;x == &amp;y);
                   }
                };

                int main()
                {
                    A a(x);  // direct initialization works fine
                    A b = x; // copy initialization causes x to be copied before it is bound
                }
                </pre>

                The possible safety enhancement would be to cause the constructor 
                in question to be explicit for optional&lt;T const&amp;&gt;; that 
                would prevent copy initialization.
                </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="optional_test_ref_fail1"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Fernando Cacciola" refid="optional-compiler-bug"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="optional_test_fail3a"/>
            <toolset name="gcc-3_3-darwin"/>
            <note author="Fernando Cacciola" refid="optional-compiler-bug"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="optional_test_inplace_fail2"/>
            <toolset name="gcc-3_3-darwin"/>
            <note author="Fernando Cacciola" refid="optional-compiler-bug"/>
        </mark-expected-failures>
    </library>
    
    <!-- outcome -->
    <library name="outcome">
        <mark-unusable>
            <toolset name="*c++98"/>
            <toolset name="*c++11"/>
            <toolset name="*gnu++98"/>
            <toolset name="*gnu++11"/>
            <toolset name="*gnu98"/>
            <toolset name="*gnu11"/>
            <toolset name="clang-linux-3*"/>
            <toolset name="gcc-3*"/>
            <toolset name="gcc-4*"/>
            <toolset name="gcc-5*"/>
            <toolset name="gcc-mngw-3*"/>
            <toolset name="gcc-mngw-4*"/>
            <toolset name="gcc-mngw-5*"/>
            <toolset name="msvc-7*"/>
            <toolset name="msvc-8*"/>
            <toolset name="msvc-9*"/>
            <toolset name="msvc-10*"/>
            <toolset name="msvc-11*"/>
            <toolset name="msvc-12*"/>
            <toolset name="msvc-13*"/>
            <toolset name="msvc-14.0"/>
            <toolset name="msvc-14.0*"/>
            <note author="Niall Douglas">Complete C++14 support is the minimum requirement.</note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="clang-linux-4.0~c++14"/>
            <toolset name="clang-linux-5.0~c++14"/>
            <toolset name="clang-linux-6.0~c++17"/>
            <note author="Niall Douglas">libstdc++ 6 is the minimum version which works.</note>
        </mark-unusable>
    </library>

    <!-- phoenix -->
    <library name="phoenix">
        <mark-expected-failures>
            <test name="lambda_tests7"/>
            <test name="lambda_tests10"/>
            <test name="lambda_tests11"/>
            <test name="lambda_tests14"/>
            <test name="lambda_tests15"/>
            <toolset name="msvc-12.0"/>
            <note author="Kohei Takahsahi">
                Wrokaround: define BOOST_RESULT_OF_USE_TR1
            </note>
        </mark-expected-failures>
    </library>

    <!-- poly_collection -->
  <library name="poly_collection">
    <mark-unusable>
      <toolset name="clang*-3.0*"/>
      <toolset name="clang*-3.1*"/>
      <toolset name="clang*-3.2*"/>
      <toolset name="gcc*-3.*"/>
      <toolset name="gcc*-4.0*"/>
      <toolset name="gcc*-4.1*"/>
      <toolset name="gcc*-4.2*"/>
      <toolset name="gcc*-4.3*"/>
      <toolset name="gcc*-4.4*"/>
      <toolset name="gcc*-4.5*"/>
      <toolset name="gcc*-4.6*"/>
      <toolset name="gcc*-4.7*"/>
      <toolset name="qcc*-3.*"/>
      <toolset name="qcc*-4.0*"/>
      <toolset name="qcc*-4.1*"/>
      <toolset name="qcc*-4.2*"/>
      <toolset name="qcc*-4.3*"/>
      <toolset name="qcc*-4.4*"/>
      <toolset name="qcc*-4.5*"/>
      <toolset name="qcc*-4.6*"/>
      <toolset name="qcc*-4.7*"/>	
      <toolset name="msvc-7.1"/>
      <toolset name="msvc-8.0"/>
      <toolset name="msvc-9.0"/>
      <toolset name="msvc-10.0"/>
      <toolset name="msvc-11.0"/>
      <toolset name="msvc-12.0"/>
      <note author="Joaqu&#237;n M L&#243;pez Mu&#241;oz" date="18 Jun 2017">
        Compiler's too old for working.
      </note> 
    </mark-unusable>
    <mark-unusable>
      <toolset name="*98"/>
      <note author="Joaqu&#237;n M L&#243;pez Mu&#241;oz" date="18 Jun 2017">
        C++11 or later required.
      </note> 
    </mark-unusable>
  </library>

    <library name="pool">
      <mark-unusable>
        <toolset name="gcc-2.95.3-*"/>
        <note author="Doug Gregor" refid="2"/>
      </mark-unusable>
    </library>

    <!-- preprocessor -->
    <library name="preprocessor">
        <mark-expected-failures>
            <test name="seq"/>
            <toolset name="cw-8.3"/>
            <note author="Paul Mensonides" refid="2"/>
        </mark-expected-failures>
    </library>

    <!-- proto -->
    <library name="proto">
      <mark-unusable>
        <toolset name="sun-5.7"/>
        <toolset name="sun-5.8"/>
        <toolset name="sun-5.9"/>
        <toolset name="sun-5.10"/>
        <toolset name="borland-*"/>
        <toolset name="vacpp"/>
        <toolset name="vacpp-*"/>
        <toolset name="cray-8.0"/>
      </mark-unusable>
    </library>

    <!-- rational -->
    <library name="rational">
        <mark-expected-failures>
            <test name="rational_test"/>
            <toolset name="sun-5.8"/>
            <note author="J. L&#195;&#179;pez" date="19 Oct 2006">
              The test is exposing the following known error of Sun Studio 11:
              overload resolution fails if
              a) some class has a conversion operator to a reference to
              a built-in type, and
              b) overload resolution involves a user-defined operator as well
              as a built-in operator, and
              c) the built-in operator takes the result of the conversion
              mentioned in a) as an operand.
              A fix will be reportedly included in patch no 6 of Sun Studio 11.
            </note>
        </mark-expected-failures>
    </library>

    <!-- serialization -->
    <library name="serialization">
        <mark-unusable>
            <toolset name="mipspro*" />
            <toolset name="dmc*" />
            <toolset name="sunpro*" />
            <note author="Robert Ramey" date="13 Jul 2007" refid="9,17,18"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="gcc-2.95.3-linux"/>
            <note author="Robert Ramey" date="12 Feb 05" refid="18,19"/>
        </mark-unusable>

        <mark-expected-failures>
            <test name="*_warchive"/>
            <test name="test_codecvt_null"/>
            <test name="test_utf8_codecvt"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="*como-4_3_3*"/>
            <note author="Robert Ramey,Roland Schwarz" date="16 Feb 07" refid="19"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_void_cast*"/>
            <toolset name="msvc-6.5*"/>
            <note author="Robert Ramey" date="20 Sep 2004" refid="16,29"/>
        </mark-expected-failures>

        <mark-expected-failures>
             <test name="test_reset_object_address*"/>
             <toolset name="msvc-6.5*"/>
             <note author="Robert Ramey" date="12 Feb 05" refid="6,29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_reset_object_address*"/>
            <toolset name="msvc-7.0"/>
            <note author="J. L&#195;&#179;pez" date="20 Dec 2006">
              This error shows when the code has become too complex for the
              compiler to handle. The problem has no relationship with the
              functionality being tested, which in fact does work for
              MSVC++ 7.0.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_const"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Aleksey Gurtovoy" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_demo_pimpl"/>
            <test name="test_diamond*"/>
            <test name="test_mult_archive_types"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Robert Ramey" refid="6">
                msvc 6 compiler failure.  The facility being tested conflicts the the
                compiler in a fundamental way and cannnot be worked around.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_mi*"/>
            <toolset name="msvc-6.5*"/>
            <note author="Robert Ramey" refid="6">
                msvc 6 compiler failure.  The facility being tested conflicts the the
                compiler in a fundamental way and cannnot be worked around.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="*_dll"/>
            <toolset name="msvc-stlport"/>
            <toolset name="msvc-6.5_stlport4"/>
            <note author="Robert Ramey">
                This failure appears when STLPort is built and used as a DLL with msvc 6.
                STLPort suggests that the next version of STLPort(5.0) will include a workaround
                for this problem.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="*"/>
            <toolset name="gcc-2.95.3-stlport*"/>
            <note author="Aleksey Gurtovoy">
                The library is believed to work in this configuration <i>if compiled against
                Spirit 1.6</i>. The latter is not provided by the particular testing
                environment these tests have been run in.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_exported*"/>
            <test name="test_mi*"/>
            <test name="test_mult_archive_types*"/>
            <test name="test_no_rtti*"/>
            <test name="test_non_default_ctor2*"/>
            <test name="test_registered*"/>
            <test name="test_shared_ptr*"/>
            <test name="test_unregistered*"/>
            <toolset name="cw*"/>
            <note author="Robert Ramey" refid="29">
                All tests that serialize derived pointers currently fail with Metrowerks compilers.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_no_rtti_*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Aleksey Gurtovoy" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_smart_cast"/>
            <toolset name="intel-7.1-linux"/>
            <note author="Aleksey Gurtovoy" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_diamond*"/>
            <toolset name="cw-8*"/>
            <toolset name="cw-9.5-darwin"/>
            <note author="Rene Rivera">
                The CW compilers have problems with the static construction idiom used to
                implement the type registration in the Boost.Serialization library. In many
                cases CW specific work arounds are implemented in the library but this one
                is not immediately solvable. There is a user work around possible, please
                contact the library developers on the Boost list for information on the
                work around if needed.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_class_info_load_text*"/>
            <test name="test_class_info_load_xml_warchive*"/>
            <toolset name="cw-9.5-darwin"/>
            <note author="Rene Rivera" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_class_info_load_text_warchive_dll"/>
            <toolset name="msvc-6.5"/>
            <note author="Doug Gregor" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_variant_*"/>
            <toolset name="hp_cxx-65*"/>
            <note author="Markus Schoepflin">
                The variant library is not supported for this compiler version.
                Therefore serialization of variants doesn't work.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="*_warchive"/>
            <toolset name="qcc-3.3.5*gpp"/>
            <note author="Jim Douglas" date="12 Feb 06" refid="36"/>
        </mark-expected-failures>

    <mark-expected-failures>
            <test name="test_variant_*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Vladimir Prus">
                The compiler fails with an error supposedly related to std::fpos&lt;&gt;::_Stz from the
        &lt;iosfwd&gt; header. It is not known what causes the compiler to instantiate this
        field and what causes the instantiation to fail.
            </note>
        </mark-expected-failures>

    </library>


    <!-- smart_ptr -->
    <library name="smart_ptr">
        <mark-expected-failures>
            <test name="shared_ptr_assign_fail"/>
            <toolset name="gcc-2.9*"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note refid="32" author="Peter Dimov"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="weak_ptr_test"/>
            <toolset name="hp_cxx-71_006_*"/>
            <note author="Markus Schoepflin" refid="3"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="auto_ptr_rv_test"/>
            <toolset name="gcc-2.9*"/>
            <toolset name="borland-5*"/>
            <toolset name="cw-8*"/>
            <note refid="31" author="Peter Dimov"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="pointer_to_other_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note refid="31" author="Peter Dimov"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="shared_ptr_alloc2_test"/>
            <toolset name="msvc-6.5*"/>
            <note refid="31" author="Peter Dimov"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="sp_convertible_test"/>
            <test name="wp_convertible_test"/>
            <test name="ip_convertible_test"/>
            <toolset name="borland-*"/>
            <note refid="31" author="Peter Dimov"/>
        </mark-expected-failures>
   </library>

    <!-- spirit (v2) -->
    <library name="spirit">
      <mark-unusable>
        <toolset name="sun-5.7"/>
        <toolset name="sun-5.8"/>
        <toolset name="sun-5.9"/>
        <toolset name="sun-5.10"/>
        <toolset name="vacpp*"/>
        <toolset name="borland-*"/>
        <toolset name="cray-8.0"/>
        <toolset name="msvc-7.1*"/>
        <toolset name="clang-*-3.1*11*"/>
        <toolset name="gcc-mngw-gnu-4.3c+"/>
      </mark-unusable>
      <test name="karma_*" category="Karma" />
      <test name="lex_*" category="Lex" />
      <test name="qi_*" category="Qi" />
      <test name="support_*" category="Support" />
      <test name="x3_*" category="X3" />
    </library>

    <!-- spirit (v2) repository -->
    <library name="spirit/repository">
      <mark-unusable>
        <toolset name="borland-cb2009"/>
        <toolset name="borland-cb2010"/>
        <toolset name="msvc-7.1*"/>
        <toolset name="clang-*-3.1*11*"/>
        <toolset name="gcc-mngw-gnu-4.3c+"/>
      </mark-unusable>
      <test name="karma_*" category="Karma" />
      <test name="qi_*" category="Qi" />
    </library>

    <!-- spirit (classic) -->
    <library name="spirit/classic">
        <mark-unusable>
            <toolset name="msvc-6.5*"/>
            <toolset name="borland-5.5*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <toolset name="sunpro-5_3-sunos"/>

            <note>
                <p>
                Historically, Spirit supported a lot of compilers, including (to some
                extent) poorly conforming compilers such as VC6. Spirit v1.6.x will be
                the last release that will support older poorly conforming compilers.
                Starting from Spirit v1.8.0, ill conforming compilers will not be
                supported. If you are still using one of these older compilers, you can
                still use Spirit v1.6.x.
                </p>
                <p>
                The reason why Spirit v1.6.x worked on old non-conforming compilers is
                that the authors laboriously took the trouble of searching for
                workarounds to make these compilers happy. The process takes a lot of
                time and energy, especially when one encounters the dreaded ICE or
                "Internal Compiler Error". Sometimes searching for a single workaround
                takes days or even weeks. Sometimes, there are no known workarounds. This
                stifles progress a lot. And, as the library gets more progressive and
                takes on more advanced C++ techniques, the difficulty is escalated to
                even new heights.
                </p>
                <p>
                Spirit v1.6.x will still be supported. Maintenance and bug fixes will
                still be applied. There will still be active development for the back-
                porting of new features introduced in Spirit v1.8.0 (and Spirit 1.9.0)
                to lesser able compilers; hopefully, fueled by contributions from the
                community. For instance, there is already a working AST tree back-port
                for VC6 and VC7 by Peder Holt.
                </p>
            </note>
        </mark-unusable>
        <mark-expected-failures>
            <test name="action_tests*"/>
            <toolset name="iw-7_1-vc6"/>
            <note author="Aleksey Gurtovoy" refid="4"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ast_calc_tests*"/>
            <test name="closure_tests*"/>
            <test name="multi_pass_compile_tests"/>
            <test name="repeat_ast_tests*"/>
            <toolset name="intel-8.0-linux"/>
            <toolset name="intel-8.1-linux"/>
            <note author="Aleksey Gurtovoy">
                This failure is caused by a compiler bug that manifests itself in the
                particular environment/hardware configuration the test has been run in.
                You may or may not experience this issue in your local setup.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="escape_char_parser_tests*"/>
            <toolset name="intel-7.1-linux"/>
            <toolset name="intel-7.1-stdlib-default-linux"/>
            <note author="Aleksey Gurtovoy" refid="19"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="escape_char_parser_tests*"/>
            <toolset name="iw-7_1-vc6*"/>
            <note author="Aleksey Gurtovoy" refid="28"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="chset_tests*"/>
            <toolset name="iw-7_1-vc6-stlp-4_5_3"/>
            <note author="Aleksey Gurtovoy" refid="28"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="int_numerics"/>
            <test name="karma_pattern*"/>
            <test name="karma_sequence"/>
            <test name="rule"/>
            <test name="sequence"/>
            <toolset name="acc"/>
            <note author="Boris Gubenko" refid="47"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="lexertl3"/>
            <test name="lexertl4"/>
            <test name="lexertl5"/>
            <toolset name="gcc-3.4.6_linux_ia64"/>
            <note author="Boris Gubenko">
                With GCC 3.4.6 the test fails with ICE: internal compiler error.
                The footprint is similar to that in GCC Bugzilla Bug 34950
                except 34950 is a regression introduced in GCC 4.2.3. In any
                case, whatever the problem is, the GCC 4.x series does not seem
                to have it: the test compiles just fine with GCC 4.x compiler.
            </note>
        </mark-expected-failures>
    </library>

    <!-- typeof -->
    <library name="typeof">
        <mark-unusable>
            <toolset name="gcc-2.95.*"/>
            <toolset name="sunpro*"/>
            <toolset name="borland-5.6.*"/>
            <note author="Arkadiy Vertleyb">
                This compiler is not supported.
            </note>
        </mark-unusable>
        <test name="*_native" category="Native compiler support">
            <mark-failure>
                <toolset name="acc*"/>
                <toolset name="intel-vc71-win*"/>
                <toolset name="intel-vc8-win*"/>
                <toolset name="como-4_3_3-vc7_1"/>
                <toolset name="hp_cxx*"/>
                <toolset name="sun-5.*"/>
                <toolset name="borland-5*"/>
                <toolset name="mipspro*"/>
                <note author="Arkadiy Vertleyb">
                    Native mode is not supported for this compiler.
                </note>
            </mark-failure>
        </test>
        <mark-expected-failures>
            <test name="*_emulation"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="cw-8_*"/>
            <note author="Arkadiy Vertleyb">
                Emulation mode is not supported for this compiler.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="function_native"/>
            <test name="template_tpl_native"/>
            <test name="function_binding_native"/>
            <test name="odr_no_uns"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Arkadiy Vertleyb">
                The feature is not supported by this compiler.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="function_native"/>
            <toolset name="cw-8_*"/>
            <note author="Arkadiy Vertleyb">
                The feature is not supported by this compiler.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="function_binding_emulation"/>
            <test name="function_emulation"/>
            <test name="function_ptr_from_tpl_emulation"/>
            <test name="modifiers_emulation"/>
            <test name="nested_typedef_emulation"/>
            <toolset name="borland-5.8*"/>
            <note author="Peder Holt">
                The feature is not supported by this compiler.
            </note>
        </mark-expected-failures>
    </library>

    <!-- function -->
    <library name="function">
        <mark-unusable>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="Douglas Gregor" refid="3"/>
        </mark-unusable>
        <test name="allocator_test">
            <mark-failure>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="5"/>
            </mark-failure>
        </test>
        <test name="contains_test">
            <mark-failure>
                <toolset name="msvc-6.5*"/>
                <note refid="3" author="D. Gregor"/>
            </mark-failure>
        </test>
        <test name="function_30">
            <mark-failure>
                <toolset name="vacpp"/>
                <note refid="16" author="D. Gregor"/>
            </mark-failure>
        </test>
        <test name="function_arith_cxx98">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
        </test>
        <test name="function_ref_cxx98">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
        </test>
        <test name="lambda_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
            <mark-failure>
                <toolset name="cw-8.3*"/>
                <note author="B. Dawes" refid="2"/>
            </mark-failure>
        </test>
        <test name="lib_function_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
            <mark-failure>
                <toolset name="cw-8.3*"/>
                <note author="B. Dawes" refid="2"/>
            </mark-failure>
        </test>
        <test name="mem_fun_cxx98">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
            <mark-failure>
                <toolset name="cw-8.3*"/>
                <note author="B. Dawes" refid="2"/>
            </mark-failure>
        </test>
        <test name="std_bind_cxx98">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
        </test>
        <test name="std_bind_portable">
            <mark-failure>
                <toolset name="msvc-6.5"/>
                <note author="B. Dawes" refid="5"/>
            </mark-failure>
        </test>
        <test name="sum_avg_cxx98">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
        </test>
    </library>


    <!-- iterator -->
    <library name="iterator">
        <test name="interoperable_fail" category="Corner-case tests">
            <mark-failure>
                <toolset name="gcc-3.3*"/>
                <toolset name="gcc-3.2*"/>
                <toolset name="gcc-2*"/>
                <toolset name="gcc"/>
                <toolset name="mingw"/>
                <toolset name="borland*"/>
                <toolset name="cw-8*"/>
                <toolset name="qcc-3.3*"/>
                <note author="D. Abrahams">
                    This failure is caused by a compiler bug.  Templated operators
                    that combine different iterators built with iterator_facade or
                    iterator_adaptor may be present in an overload set even when those
                    iterators are not interoperable.  The usual result is that error
                    messages generated by illegal use of these operators will be of
                    lower quality.
                </note>
            </mark-failure>
        </test>

        <test name="is_convertible_fail" category="Corner-case tests">
            <mark-failure>
                <toolset name="gcc-2*"/>
                <toolset name="gcc"/>
                <toolset name="mingw"/>
                <toolset name="borland*"/>
                <toolset name="cw-8*"/>
                <toolset name="msvc-6*"/>
                <toolset name="msvc-7.0*"/>
                <note author="D. Abrahams">
                    This failure is caused by a compiler bug.
                    <code>is_convertible&lt;T,U&gt;::value</code> may be true for unrelated
                    iterators <code>T</code> and <code>U</code>
                    (including many of the Boost specialized adaptors) which use
                    <code>enable_if_convertible</code> to restrict the applicability
                    of converting constructors, even when <code>T</code> is not
                    convertible to <code>U</code> because instantiating the
                    conversion will cause a compilation failure.
                </note>
            </mark-failure>
        </test>

        <test name="indirect_iter_member_types" category="Corner-case tests"/>

        <mark-expected-failures>
            <test name="indirect_iter_member_types"/>
            <test name="pointee"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="D. Abrahams">
                This failure is caused by a compiler bug.  The
                compiler tends to drop const-ness and as a result
                some indirect_iterators will have pointer and
                reference members of <code>T*</code> and <code>T&amp;</code> that should
                have been <code>T const*</code> and <code>T const&amp;</code>.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="zip_iterator_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Aleksey Gurtovoy" date="19 Sep 2004" refid="26"/>
        </mark-expected-failures>

       <mark-expected-failures>
            <test name="is_lvalue_iterator"/>
            <toolset name="acc*"/>
            <note author="Boris Gubenko">
                For some currently unknown reason, with aCC, this test can be compiled
                only in strict ansi mode. Since on HP-UX/aCC boost testing is done in the
                default compilation mode, this test fails to compile on this platform.
            </note>
       </mark-expected-failures>

    </library>


    <!-- math -->
    <library name="math">
        <mark-unusable>
          <toolset name="gcc-2.95.3-*"/>
          <note author="Doug Gregor" refid="3"/>
        </mark-unusable>
        <mark-unusable>
          <toolset name="borland-5.9.2"/>
           <note author="John Maddock">
              Sadly Borland-5.9.2 has an even harder time compiling this
              library than earlier versions did.  There are currently too
              many issues to stand a chance of porting to this compiler.
           </note>
        </mark-unusable>
       <mark-expected-failures>
          <test name="mpfr_concept_check"/>
          <test name="ntl_concept_check"/>
          <toolset name="*"/>
          <note author="John Maddock">
             This test relies on external software being installed in order to pass.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_traits"/>
          <toolset name="gcc-3.3.6"/>
          <note author="John Maddock">
             This compiler is not sufficiently conforming to correctly handle these tests.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_tr1_long_double"/>
          <toolset name="darwin*"/>
          <toolset name="intel-linux-10.0"/>
          <toolset name="intel-linux-9*"/>
          <toolset name="intel-linux-8*"/>
          <note author="John Maddock">
             Some versions of the Darwin platform have insufficient long double support
             for us to be able to run this test.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_policy_2"/>
          <toolset name="acc"/>
          <toolset name="gcc-mingw-3.4.5"/>
          <note author="John Maddock">
             This test takes too long to build for this compiler and times out.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_traits"/>
          <toolset name="sun-5.8"/>
          <toolset name="sun-5.9"/>
          <note author="John Maddock">
             This is a compiler bug: it is unable to use
             SFINAE to detect the presence of specific
             member functions.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="std_real_concept_check"/>
          <test name="test_instantiate1"/>
          <test name="test_policy_sf"/>
          <toolset name="sun-5.8"/>
          <toolset name="sun-5.9"/>
          <note author="John Maddock">
             This is a compiler bug: it is unable to resolve the
             overloaded functions.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_binomial_real_concept"/>
          <test name="test_ibeta_inv_ab_real_concept"/>
          <test name="test_igamma_inva_real_concept"/>
          <toolset name="sun-5.9"/>
          <toolset name="sun-5.8"/>
          <note author="John Maddock">
             This test takes too long to execute and times out.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="dist_binomial_incl_test"/>
          <test name="dist_neg_binom_incl_test"/>
          <test name="dist_poisson_incl_test"/>
          <test name="test_binomial_double"/>
          <test name="test_binomial_float"/>
          <test name="test_binomial_long_double"/>
          <test name="test_binomial_real_concept"/>
          <test name="test_negative_binomial_double"/>
          <test name="test_negative_binomial_float"/>
          <test name="test_negative_binomial_long_double"/>
          <test name="test_negative_binomial_real_concept"/>
          <test name="test_poisson_double"/>
          <test name="test_poisson_float"/>
          <test name="test_poisson_long_double"/>
          <test name="test_poisson_real_concept"/>
          <test name="tools_roots_inc_test"/>
          <toolset name="sun-5.8"/>
          <toolset name="sun-5.7"/>
          <note author="John Maddock">
             These tests fail with an internal compiler error: there is no
             known workaround at present, except to use Sun-5.9 which does
             build this code correctly.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
         <test name="log1p_expm1_test"/>
         <test name="test_bernoulli"/>
         <test name="test_beta_dist"/>
         <test name="test_binomial_float"/>
         <test name="test_binomial_double"/>
         <test name="test_binomial_coeff"/>
         <test name="test_carlson"/>
         <test name="test_cauchy"/>
         <test name="test_cbrt"/>
         <test name="test_chi_squared"/>
         <test name="test_classify"/>
         <test name="test_dist_overloads"/>
         <test name="test_ellint_3"/>
         <test name="test_exponential_dist"/>
         <test name="test_factorials"/>
         <test name="test_find_location"/>
         <test name="test_find_scale"/>
         <test name="test_fisher_f"/>
         <test name="test_gamma_dist"/>
         <test name="test_hermite"/>
         <test name="test_ibeta_inv_float"/>
         <test name="test_ibeta_inv_double"/>
         <test name="test_ibeta_inv_ab_float"/>
         <test name="test_igamma_inv_float"/>
         <test name="test_igamma_inv_double"/>
         <test name="test_igamma_inva_float"/>
         <test name="test_igamma_inva_double"/>
         <test name="test_instantiate1"/>
         <test name="test_instantiate1"/>
         <test name="test_laguerre"/>
         <test name="test_legendre"/>
         <test name="test_lognormal"/>
         <test name="test_negative_binomial_float"/>
         <test name="test_negative_binomial_double"/>
         <test name="test_normal"/>
         <test name="test_rayleigh"/>
         <test name="test_remez"/>
         <test name="test_roots"/>
         <test name="test_students_t"/>
         <test name="test_toms748_solve"/>
         <test name="test_triangular"/>
         <test name="test_uniform"/>
         <test name="test_policy"/>
         <test name="test_policy_sf"/>
         <test name="test_bessel_j"/>
         <test name="test_bessel_y"/>
         <test name="dist_beta_incl_test"/>
         <test name="dist_cauchy_incl_test"/>
         <test name="dist_chi_squared_incl_test"/>
         <test name="dist_exponential_incl_test"/>
         <test name="dist_fisher_f_incl_test"/>
         <test name="dist_gamma_incl_test"/>
         <test name="dist_lognormal_incl_test"/>
         <test name="dist_normal_incl_test"/>
         <test name="dist_students_t_incl_test"/>
         <test name="sf_beta_incl_test"/>
         <test name="sf_bessel_incl_test"/>
         <test name="sf_cbrt_incl_test"/>
         <test name="sf_gamma_incl_test"/>
         <test name="sf_legendre_incl_test"/>
         <test name="std_real_concept_check"/>
         <test name="test_traits"/>
         <test name="tools_remez_inc_test"/>
         <test name="tools_roots_inc_test"/>
         <test name="tools_series_inc_test"/>
         <test name="tools_solve_inc_test"/>
         <test name="tools_test_data_inc_test"/>
         <test name="common_factor_test"/>
         <test name="octonion_test"/>
         <test name="quaternion_test"/>
         <test name="complex_test"/>
          <toolset name="borland-5.6.*"/>
          <note author="John Maddock">
             This compiler is not sufficiently conforming to correctly handle these tests.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_bernoulli"/>
          <test name="test_beta_dist"/>
          <test name="test_binomial_float"/>
          <test name="test_binomial_double"/>
          <test name="test_binomial_coeff"/>
          <test name="test_cauchy"/>
          <test name="test_dist_overloads"/>
          <test name="test_ellint_3"/>
          <test name="test_exponential_dist"/>
          <test name="test_factorials"/>
          <test name="test_find_location"/>
          <test name="test_find_scale"/>
          <test name="test_hermite"/>
          <test name="test_ibeta_inv_float"/>
          <test name="test_ibeta_inv_double"/>
          <test name="test_ibeta_inv_ab_float"/>
          <test name="test_igamma_inva_float"/>
          <test name="test_igamma_inva_double"/>
          <test name="test_instantiate1"/>
          <test name="test_instantiate1"/>
          <test name="test_laguerre"/>
          <test name="test_legendre"/>
          <test name="test_lognormal"/>
          <test name="test_negative_binomial_double"/>
          <test name="test_normal"/>
          <test name="test_rayleigh"/>
          <test name="test_remez"/>
          <test name="test_roots"/>
          <test name="test_toms748_solve"/>
          <test name="test_policy"/>
          <test name="test_policy_sf"/>
          <test name="dist_cauchy_incl_test"/>
          <test name="dist_exponential_incl_test"/>
          <test name="dist_lognormal_incl_test"/>
          <test name="dist_normal_incl_test"/>
          <test name="sf_gamma_incl_test"/>
          <test name="sf_legendre_incl_test"/>
          <test name="std_real_concept_check"/>
          <test name="test_traits"/>
          <test name="tools_remez_inc_test"/>
          <test name="tools_roots_inc_test"/>
          <test name="tools_series_inc_test"/>
          <test name="tools_solve_inc_test"/>
          <test name="tools_test_data_inc_test"/>
          <test name="complex_test"/>
          <toolset name="borland-5.8.2"/>
          <note author="John Maddock">
             This compiler is not sufficiently conforming to correctly handle these tests.
          </note>
       </mark-expected-failures>
        <mark-expected-failures>
            <test name="octonion_test"/>
            <test name="quaternion_test"/>
            <toolset name="gcc-3.4.3_sunos"/>
            <note author="Caleb Epstein">
              There appears to be a bug in gcc's <code>std::exp (long
              double)</code> on this platform.
            </note>
        </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_remez"/>
          <toolset name="hp_cxx-71_006_tru64"/>
          <note author="John Maddock">
             For some reason taking the address of std library math functions fails
             on this platform: this is a problem for our test code, not the library.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="special_functions_test"/>
          <test name="octonion_test"/>
          <test name="quaternion_test"/>
          <test name="quaternion_mult_incl_test"/>
          <toolset name="msvc-6*"/>
          <note author="John Maddock">
             This compiler is not sufficiently conforming to compile these tests.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
            <test name="complex_test"/>
            <test name="log1p_expm1_test"/>
            <toolset name="sunpro*"/>
            <note author="John Maddock">
              std::numeric_limits&lt;long double&gt;::infinity() is apparently
              broken in this compiler: it's filed as bug 6347520 with Sun.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="complex_test"/>
            <toolset name="msvc-6*"/>
            <note author="John Maddock">
              Incomplete std::complex support make these tests pointless
              (the complex trig functions are absent).
            </note>
        </mark-expected-failures>
       <mark-expected-failures>
          <test name="special_functions_test"/>
          <test name="octonion_test"/>
          <test name="quaternion_test"/>
          <test name="quaternion_mult_incl_test"/>
          <toolset name="sun-5.8"/>
          <note author="John Maddock">
             These have yet to fully investigated, but the code is known
             to compile with more conforming compilers, probably workarounds
             are possible if someone is prepared to invest the time.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="quaternion_test"/>
          <toolset name="msvc-7.1_stlport4"/>
          <note author="John Maddock">
             Appears to be a bug in STLport's complex abs function, but needs more investigation.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="special_functions_test"/>
          <toolset name="msvc-7.1_stlport4"/>
          <note author="John Maddock">
             This appears to be a problem with STLPort's abs function: the issue only effects the
             test code.  A workaround should be possible but users should be encouraged to use
             STLport 5 instead.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="quaternion_test"/>
          <test name="octonion_test"/>
          <toolset name="gcc-cygwin*"/>
          <note author="John Maddock">
            No true long double standard lib support causes these tests to fail.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="quaternion_test"/>
          <test name="complex_test"/>
          <test name="special_functions_test"/>
          <toolset name="intel-linux*"/>
          <note author="John Maddock">
            This is Intel issue 409291, it should be fixed from
            compiler package l_cc_c_9.1.046 onwards.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
            <test name="complex_test"/>
            <toolset name="qcc-3.3.5*cpp"/>
            <note author="Jim Douglas" date="14 Feb 06" refid="27"/>
       </mark-expected-failures>
       <mark-expected-failures>
            <test name="common_factor_test"/>
            <toolset name="msvc-6.5_stlport*"/>
            <toolset name="msvc-7.1_stlport*"/>
            <note author="John Maddock">
            This failure appears to be caused by a compiler bug: please note
            that the issue only effects the test suite, not the library itself.
            A workaround is available but breaks other compilers.
            </note>
       </mark-expected-failures>
    </library>

    <!-- numeric/conversion -->
    <library name="numeric/conversion">
        <test name="bounds_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="Fernando Cacciola" refid="3"/>
            </mark-failure>
        </test>
        <test name="converter_test">
            <mark-failure>
                <toolset name="gcc-3.4.5_linux_x86_64"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="Fernando Cacciola" refid="3"/>
            </mark-failure>
        </test>
        <test name="traits_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="Fernando Cacciola" refid="3"/>
            </mark-failure>
        </test>
        <test name="udt_example_0">
            <mark-failure>
                <toolset name="msvc-6.5_stlport4"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5*"/>
                <note author="Fernando Cacciola" refid="30"/>
            </mark-failure>
        </test>
        <test name="udt_support_test">
            <mark-failure>
                <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="Fernando Cacciola" refid="3"/>
            </mark-failure>
        </test>
    </library>

    <!-- numeric/interval -->
    <library name="numeric/interval">
        <mark-unusable>
            <toolset name="borland-5.6*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
        </mark-unusable>
        <mark-expected-failures>
            <test name="det"/>
            <test name="mul"/>
            <test name="overflow"/>
            <toolset name="hp_cxx*"/>
            <note author="G. Melquiond">
                This test ensures the inclusion property of interval
                arithmetic is available for built-in floating-point types
                <code>float</code> and <code>double</code>. If the test
                fails, <code>interval&lt;float&gt;</code> and
                <code>interval&lt;double&gt;</code> should not be used
                on this compiler/platform since there will be no
                numerical guarantee.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="det"/>
            <test name="integer"/>
            <test name="overflow"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="A.Meredith">
                This compiler has some problems with name looup / overload resolution.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="cmp_exn"/>
            <test name="cmp_set"/>
            <test name="cmp_tribool"/>
            <toolset name="gcc-2.95.3-linux"/>
            <note author="Aleksey Gurtovoy" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="det"/>
            <toolset name="cw-8.3*"/>
            <note author="Aleksey Gurtovoy" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_float"/>
            <toolset name="msvc-7.1_stlport4"/>
            <note author="Vladimir Prus">
              This failure is unresearched. Presumably, the problem
              is that the abs function is not available in the "right"
              namespace with this compiler/stdlib combination.
            </note>
        </mark-expected-failures>

    </library>


    <!-- numeric/ublas -->
    <library name="numeric/ublas">
        <mark-unusable>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="gcc-3_3-darwin"/>
            <note author="M.Stevens" refid="17"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="cw-9.4"/>
            <note author="M.Stevens" refid="2"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="sun-5.8"/>
            <note author="M.Stevens" refid="4"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="cw-8.3"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="iw-7_1-vc6"/>
            <toolset name="gcc-2.95*"/>
            <note author="M.Stevens" refid="30"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="msvc-7.1_stlport4"/>
            <note author="Roland Schwarz">
                This old version of the stlport library causes the BOOST_NO_STDC_NAMESPACE
                macro to be set. But this conflicts with the requirements of the library.
            </note>
        </mark-unusable>
        <mark-expected-failures>
            <test name="test3"/>
            <toolset name="qcc-3.3.5*cpp"/>
            <note author="Jim Douglas" date="14 Feb 06" refid="27"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="begin_end"/>
            <toolset name="msvc-7.1"/>
            <note author="Gunter Winkler" date="07 Oct 09" refid="48"/>
        </mark-expected-failures>
    </library>

    <!-- program_options -->
    <library name="program_options">

        <!-- Mark unusable toolsets -->
        <mark-unusable>
            <toolset name="gcc-2.95.3-linux"/>
            <note>
                The failure is caused by standard library deficiencies
                -- it lacks the basic_string class template and
                    the &lt;locale&gt; header.
            </note>
        </mark-unusable>

        <mark-unusable>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <note refid="2"/>
        </mark-unusable>

        <mark-unusable>
            <toolset name="msvc-6.5*"/>
            <note refid="17"/>
        </mark-unusable>

        <mark-unusable>
            <toolset name="msvc-7.0"/>
            <note refid="29"/>
        </mark-unusable>

        <!-- Mark expected failures -->

        <test name="unicode_test*">
            <mark-failure>
                <toolset name="iw-7_1-vc6"/>
                <toolset name="iw-7_1-vc6-stlp-4_5_3"/>
                <toolset name="msvc-6.5*"/>
                <note>The failures are caused by problems
                    with std::locale implementation</note>
            </mark-failure>
        </test>

        <test name="options_description_test_dll">
             <mark-failure>
                <toolset name="msvc-6.5"/>
                <toolset name="iw-7_1-vc6"/>
                <note refid="23"/>
            </mark-failure>
        </test>

        <test name="variable_map_test_dll">
             <mark-failure>
                <toolset name="iw-7_1-vc6"/>
                <note refid="23"/>
            </mark-failure>
        </test>

        <test name="*dll">
            <mark-failure>
                <toolset name="cw-8.3*"/>
                <note refid="18"/>
            </mark-failure>
        </test>

        <test name="*dll">
             <mark-failure>
                <toolset name="*como-4_3_3*"/>
                <note refid="24"/>
            </mark-failure>
        </test>

        <mark-expected-failures>
            <test name="variable_map_test"/>
            <test name="variable_map_test_dll"/>
            <toolset name="msvc-6.5*"/>
            <note>
               The failures are caused by compiler bug: it's not possible to
               explicitly pass template arguments to member template function. The
               failure is serious and makes one of the primary interfaces
               unusable.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="cmdline_test_dll"/>
            <test name="options_description_test_dll"/>
            <test name="parsers_test_dll"/>
            <test name="variable_map_test_dll"/>
            <test name="positional_options_test_dll"/>
            <toolset name="mingw-3*"/>
            <note author="Aleksey Gurtovoy" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="unicode_test*"/>
            <toolset name="mingw-3*"/>
            <toolset name="gcc-3.4.2_mingw"/>
            <toolset name="gcc-3.4.5_mingw"/>
            <toolset name="gcc-mingw-3.4.5"/>
            <toolset name="gcc-mingw-3.4.2"/>
            <toolset name="gcc-cygwin-3.4.4"/>
            <note refid="19"/>
        </mark-expected-failures>

        <mark-expected-failures>
          <test name="unicode_test_dll"/>
          <toolset name="*-darwin"/>
          <note refid="35" author="Doug Gregor"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="unicode_test*"/>
            <toolset name="qcc-3.3.5*gpp"/>
            <note author="Jim Douglas" date="12 Feb 06" refid="36"/>
        </mark-expected-failures>

    </library>

    <!-- parameter -->
    <library name="parameter">
        <mark-expected-failures>
            <test name="duplicates"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note refid="32" author="David Abrahams"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="unnamed_fail"/>
            <toolset name="msvc-7.0"/>
            <toolset name="msvc-6*"/>
            <note refid="32" author="David Abrahams"/>
        </mark-expected-failures>
        <test name="preprocessor">
          <toolset name="[Ss]un-5.8"/>
            <note>
              Several compiler bugs were worked around in order to get
              this test to pass, so it could be considered to be only
              partially working.  However, the library's macro system,
              which is really being tested here, does work on this
              compiler, which is why we worked around the failures.
              Please see the <a
              href="http://www.boost.org/libs/parameter/test/preprocessor.cpp">test's
              source file</a> for details.
            </note>
        </test>

        <mark-expected-failures>
            <test name="maybe"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="msvc-6*"/>
            <toolset name="msvc-7.0"/>
            <note refid="31" author="Daniel Wallin"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="python-parameter-test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="msvc-6*"/>
            <toolset name="msvc-7.0"/>
            <note refid="31" author="Daniel Wallin"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="python_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="msvc-6*"/>
            <toolset name="msvc-7.0"/>
            <note refid="31" author="Daniel Wallin"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="optional_deduced_sfinae"/>
            <toolset name="msvc-6*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Daniel Wallin">
              These compilers do not support SFINAE, so are expected to
              fail this test.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="preprocessor_deduced"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Daniel Wallin">
              Borland does not support this feature. A compatibility syntax
              might be developed later on.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="normalized_argument_types"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="msvc-6*"/>
            <toolset name="msvc-7.0"/>
            <note author="Daniel Wallin">
              This feature generally requires advanced compiler
              features not supported by these compilers. It might
              be possible to work around the issue on VC6/7, but
              at this time no such workaround has been done.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="unnamed"/>
            <toolset name="*"/>
            <note author="Daniel Wallin">
              This is old and should not be tested any more.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="deduced_dependent_predicate"/>
            <toolset name="msvc-6*"/>
            <note refid="31" author="Daniel Wallin"/>
        </mark-expected-failures>
       <mark-expected-failures>
          <test name="optional_deduced_sfinae"/>
          <test name="preprocessor_deduced"/>
          <test name="python_test"/>
          <toolset name="sun-5.8"/>
          <note author="John Maddock">
             These test failure are reported to be under investigation
             at Sun's compiler labs.
          </note>
       </mark-expected-failures>

        <mark-expected-failures>
            <test name="result_of"/>
            <toolset name="msvc-6*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note refid="31" author="Daniel Wallin"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="python_test"/>
            <toolset name="qcc-3.3.5_gpp"/>
            <note refid="6" author="Daniel Wallin"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="sfinae"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="msvc-6.5_stlport4"/>
            <note refid="29" author="Daniel Wallin"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="basics"/>
            <test name="macros"/>
            <test name="maybe"/>
            <test name="sfinae"/>
            <toolset name="gcc-4.2.1*"/>
            <note author="Boris Gubenko" refid="42"/>
        </mark-expected-failures>

    </library>

    <library name="property_tree">
        <mark-unusable>
            <toolset name="borland-*"/>
            <note author="Sebastian Redl">Inherited from MultiIndex</note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="sun-5.*"/>
            <note author="Sebastian Redl">
                Lots of test failures complaining about the ambiguity of a
                const and a non-const overload of the same function.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="vacpp"/>
            <note author="Sebastian Redl">
                This compiler seems to have very broken name lookup.
            </note>
        </mark-unusable>
        <mark-expected-failures>
            <test name="test_property_tree"/>
            <test name="test_json_parser"/>
            <toolset name="intel-darwin-*"/>
            <toolset name="intel-linux-*"/>
            <note author="Sebastian Redl">
                Tend to crash the compiler (Intel 10) or simply take too long
                (Intel 11).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_xml_parser_rapidxml"/>
            <toolset name="gcc-3.4.3"/>
            <note author="Sebastian Redl">
                This ancient GCC doesn't like local const ints as template
                parameters. Or something like that.
            </note>
        </mark-expected-failures>
    </library>

     <!-- pointer container -->
    <library name="ptr_container">
        <mark-unusable>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="dmc-8_47-stlport-4_5_3"/>
            <toolset name="hp_cxx-65_042_tru64"/>
        </mark-unusable>
        <mark-expected-failures>
            <test name="ptr_list"/>
            <toolset name="gcc-4.0.*"/>
            <note author="Thorsten Ottosen">
                The error is due to problems in the standard library implementation.
                It should be fixed in newer versions of the compiler.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ptr_list"/>
            <toolset name="gcc-4.0.0*"/>
            <note author="Thorsten Ottosen">
                The error is due to problems in the standard library implementation.
                It should be fixed in newer versions of the compiler.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="incomplete_type_test"/>
            <toolset name="cw-9.4"/>
            <note author="Thorsten Ottosen">
                This error seems to be a bug the compiler. Please submit a
                patch.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="iterator_test"/>
            <toolset name="gcc-3.2.3*"/>
            <toolset name="gcc-3.3.6*"/>
            <toolset name="gcc"/>
            <toolset name="qcc-3.3.5*"/>
            <note author="Thorsten Ottosen">
                This error seems to be a bug the standard library. Please submit a
                patch.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="no_exceptions"/>
            <toolset name="cw-9.4"/>
            <toolset name="sun-5.*"/>
            <note author="Thorsten Ottosen">
                This test fails because the test ptr_vector fails. Please see the note
                for that test.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ptr_deque"/>
            <toolset name="cw-9.4"/>
            <toolset name="sun-5.*"/>
            <note author="Thorsten Ottosen">
                For sun the problem is that <code>insert(iterator,range)</code>
                is not available due to partial ordering errors (the core library remains usable).
                For codewarrior the problem is at least <code>std::auto_ptr</code> overloads (the core library remains usable).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ptr_list"/>
            <toolset name="cw-9.4"/>
            <toolset name="sun-5.*"/>
            <note author="Thorsten Ottosen">
                For sun the problem is that <code>insert(iterator,range)</code>
                is not available due to partial ordering errors (the core library remains usable).
                For codewarrior the problem is at least <code>std::auto_ptr</code> overloads (the core library remains usable).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ptr_vector"/>
            <toolset name="cw-9.4"/>
            <toolset name="sun-5.8"/>
            <note author="Thorsten Ottosen">
                For sun the problem is that <code>insert(iterator,range)</code>
                is not available due to partial ordering errors (the core library remains usable).
                For codewarrior the problem is at least <code>std::auto_ptr</code> overloads (the core library remains usable).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ptr_map"/>
            <toolset name="hp_cxx-71_006_tru64"/>
            <toolset name="cw-9.4"/>
            <toolset name="sun-5.*"/>
            <note author="Thorsten Ottosen">
                For hp, this compiler bug is insignificant.
                For sun the problem is that <code>transfer(range,ptr_map)</code>
                is not available due to partial ordering errors (the core library remains usable).
                For codewarrior the problem is not known so please submit a patch.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="ptr_set"/>
            <toolset name="cw-9.4"/>
            <toolset name="sun-5.*"/>
            <note author="Thorsten Ottosen">
                For sun the problem is that <code>transfer(range,ptr_map)</code> and
                <code>insert(range)</code>code>
                is not available due to partial ordering errors (the core library remains usable).
                For codewarrior the problem is at least <code>std::auto_ptr</code> overloads (the core library remains usable)..
            </note>
        </mark-expected-failures>
           <mark-expected-failures>
            <test name="serialization"/>
            <toolset name="cw*"/>
            <toolset name="intel-darwin-*"/>
            <toolset name="intel-linux-*"/>
            <toolset name="pathscale-3.1"/>
            <toolset name="sun-5.*"/>
            <note author="Thorsten Ottosen">
                For codewarrior, the cause of this problem is unknown. Please
                submit a patch. Other failures are due to problems with
                the serialization library, or to a minor problem with the use of
                the library.
            </note>
        </mark-expected-failures>
           <mark-expected-failures>
               <test name="tree_test"/>
               <toolset name="sun-5.8"/>
               <note author="Thorsten Ottosen">
                   For sun the problem is due to Boost.Test.
               </note>
           </mark-expected-failures>
           <mark-expected-failures>
               <test name="tut1"/>
               <toolset name="cw-9.4"/>
               <note author="Thorsten Ottosen">
                   Seem like a bug in the compiler. Please submit a patch.
               </note>
           </mark-expected-failures>
           <mark-expected-failures>
               <test name="view_example"/>
               <toolset name="cw-9.4"/>
               <note author="Thorsten Ottosen">
                   Seem like a bug in the compiler. Please submit a patch.
               </note>
           </mark-expected-failures>
    </library>

    <!-- python -->
    <library name="python">
        <mark-unusable>
            <toolset name="borland-5.5*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note refid="2"/>
            <note refid="17"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="hp_cxx-65*"/>
            <note author="Markus Schoepflin">
            The library fails to compile because of an error in the C++
            standard library implementation on this platform. It incorrectly
            assumes that fpos_t is of an integral type, which is not always
            the case. This is fixed in a later release.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="sun-5.6*"/>
            <note author="David Abrahams">
              The old reasoning given for this markup, which applied
              to sun-5.8*, was as follows.  However, tuple's tests
              seem to use the test library, which is apparently
              completely broken on Sun.  Therefore, I've backed off
              the version number to sun-5.6 so I can see the actual
              state of the failures.

            <blockquote>This compiler seems to be having trouble digesting
            Boost.Tuple.  Until it can handle Boost.Tuple there's
            little chance it will handle Boost.Python</blockquote>
            </note>
        </mark-unusable>
        <mark-expected-failures>
          <test name="object"/>
          <toolset name="intel-10.*"/>
            <note author="David Abrahams">

              This compiler has a bug that causes silent misbehavior at runtime
              when each of an assignment expression follows one of the following patterns:
              <em>expr</em><code>.attr(</code><em>name</em><code>)</code>
              or <em>expr</em><code>[</code><em>item</em><code>]</code>,
              where <em>expr</em>
              is-a <code>boost::python::object</code>.  We've been
              unable to find a workaround.

            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="object"/>
            <toolset name="acc"/>
            <note author="Boris Gubenko" refid="46"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="args"/>
            <test name="auto_ptr"/>
            <test name="builtin_convertors"/>
            <test name="callbacks"/>
            <test name="crossmod_exception"/>
            <test name="data_members"/>
            <test name="enum"/>
            <test name="exception_translator"/>
            <test name="extract"/>
            <test name="implicit"/>
            <test name="iterator"/>
            <test name="list"/>
            <test name="map_indexing_suite"/>
            <test name="object"/>
            <test name="opaque"/>
            <test name="pickle2"/>
            <test name="polymorphism"/>
            <test name="polymorphism2"/>
            <test name="shared_ptr"/>
            <test name="slice"/>
            <test name="test_pointer_adoption"/>
            <test name="try"/>
            <test name="vector_indexing_suite"/>
            <test name="virtual_functions"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <note author="D. Abrahams">
                The problems with GCC 2.x only occur when C++ exceptions are thrown and
                the framework catches them, which happens quite often in the tests.
                So technically GCC 2.x is usable if you're careful.
             </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="pointer_vector"/>
            <test name="polymorphism"/>
            <toolset name="hp_cxx*"/>
            <note author="Markus Schoepflin" refid="29"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="data_members"/>
            <toolset name="acc"/>
            <note author="Boris Gubenko">
                This test assumes standard-compliant dependent template name lookup which
                is performed by aCC6 only in strict ansi mode. Since on HP-UX/aCC6 boost
                testing is done in the default compilation mode, this test fails to
                compile on this platform (in strict ansi mode, it compiles and succeeds).
            </note>
        </mark-expected-failures>
    </library>

  <library name="quickbook">
    <mark-unusable>
      <toolset name="gcc*-3.*"/>
      <toolset name="gcc*-4.0*"/>
      <toolset name="gcc*-4.1*"/>
      <toolset name="gcc*-4.2*"/>
      <toolset name="gcc*-4.3*"/>
      <toolset name="qcc-3.*"/>
      <toolset name="qcc-4.0.*"/>
      <toolset name="qcc-4.1.*"/>
      <toolset name="qcc-4.2.*"/>
      <toolset name="qcc-4.3.*"/>
      <toolset name="msvc-7.1"/>
      <toolset name="msvc-8.0"/>
      <toolset name="msvc-9.0"/>
      <note author="Daniel James" date="30 Dec 2017">
        Unsupported compiler
      </note>
    </mark-unusable>
    <mark-unusable>
      <toolset name="*~c++98"/>
      <toolset name="*~gnu98"/>
      <toolset name="*~gnu++98"/>
      <note author="Daniel James" date="29 Jan 2018">
        C++11 mode (or later) required
      </note>
    </mark-unusable>
  </library>

    <!-- random -->
    <library name="random">
        <mark-unusable>
            <toolset name="msvc"/>
            <toolset name="msvc-7.0"/>
            <note author="B. Dawes" refid="10"/>
        </mark-unusable>
        <test name="random_test">
            <mark-failure>
                <toolset name="cw-8.3*"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="B. Dawes" refid="2"/>
            </mark-failure>
            <mark-failure>
                <toolset name="intel-vc71-win*"/>
                <toolset name="intel-vc8-win*"/>
                <note author="S. Slapeta" refid="1"/>
            </mark-failure>
            <mark-failure>
                <toolset name="intel-linux-9.0"/>
                <note author="John Maddock">
                  Reported to Intel as issue 409291, and confirmed
                  as a problem.  Probably this relates to a specific
                  Linux-Kernal or GLibC version.
                </note>
            </mark-failure>
            <mark-failure>
                <toolset name="qcc-3.3.5*"/>
                <note author="Jim Douglas" date="13 Feb 06">
                    Test fails with ranlux*_O1 RNGs when saving and recalling the state due to a bug in the
                    double to string conversion. The problem has been reported to QNX as PR29252.
                </note>
            </mark-failure>
            <mark-failure>
                <toolset name="gcc-*_tru64"/>
                <note author="Markus Schoepflin">
                This test fails because of limitations in the system assembler
                version used by GCC. It most probably would pass if the test
                were split into multiple source files.
                </note>
            </mark-failure>
            <mark-failure>
                <toolset name="gcc-3.4.6_linux_ia64"/>
                <note author="Boris Gubenko">
                It looks like a compiler issue: the test fails with gcc 3.4.6
                and succeeds with gcc 4.2.1.
                </note>
            </mark-failure>
        </test>
        <mark-expected-failures>
            <test name="test_ecuyer1988"/>
            <test name="test_hellekalek1995"/>
            <test name="test_rand48"/>
            <test name="test_minstd_rand0"/>
            <test name="test_minstd_rand"/>
            <test name="test_kreutzer1986"/>
            <test name="test_mt11213b"/>
            <test name="test_mt19937"/>
            <test name="test_lagged_fibonacci"/>
            <test name="test_lagged_fibonacci607"/>
            <test name="test_ranlux3"/>
            <test name="test_ranlux4"/>
            <test name="test_ranlux3_01"/>
            <test name="test_ranlux4_01"/>
            <test name="test_ranlux64_3_01"/>
            <test name="test_ranlux64_4_01"/>
            <test name="test_taus88"/>
            <toolset name="gcc-mingw-4.3.3"/>
            <note refid="5" author="Steven Watanabe"/>
        </mark-expected-failures>
    </library>

    <!-- range -->
    <library name="range">
        <mark-unusable>
            <toolset name="borland-*"/>
            <toolset name="mipspro"/>
            <toolset name="dmc-8_43-stlport-4_5_3"/>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="sunpro-5_3-sunos"/>
        </mark-unusable>
        <mark-expected-failures>
            <test name="array"/>
            <toolset name="como-4_3_3*"/>
            <toolset name="sun-5.8"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note refid="27" author="Thorsten Ottosen"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="adl_conformance"/>
            <toolset name="msvc-7.1"/>
            <note author="Neil Groves">
                This test is designed to give users visibility of the ADL problems with their compilers.
                Lack of Argument Dependent Lookup changes how one can extend the library. The lack of
                ADL is worked-around internally so that most of the functionality is preserved.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="iterator_range"/>
            <toolset name="msvc-stlport"/>
            <toolset name="msvc-6.5_stlport4"/>
            <toolset name="hp_cxx-65*"/>
            <note author="Thorsten Ottosen">
                For most compilers this is due to problems
                with built-in arrays (notably char arrays) and operator==()
                and operator!=() for iterator_range. Thus, not using built-in arrays
                fixes the problem.

                For other compilers it is simply a bug in the standard library.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="push_front"/>
            <toolset name="msvc-9.0~stlport5.2"/>
            <note author="Neil Groves">
                push_front fails the unit test in this configuration. I do not have this
                configuration available to determine if a work-around is possible.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="reversible_range"/>
            <toolset name="hp_cxx-65*"/>
            <note author="Thorsten Ottosen">
                This test probably fails because it uses built-in arrays. So do expect these
                functions to work in normal code.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="string"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="sun-5.8"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Thorsten Ottosen">
                The string functionality is expected to work if
                the user employs std::string and stays away from built-in
                arrays.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="sub_range"/>
            <toolset name="msvc-8.0"/>
            <toolset name="intel-vc8-*"/>
            <toolset name="iw-7_1-vc6-stlp-4_5_3"/>
            <toolset name="msvc-6.5_stlport4"/>
            <toolset name="msvc-7.0"/>
            <toolset name="msvc-7.1_stlport4"/>
            <toolset name="hp_cxx-65*"/>
            <note refid="6" author="Thorsten Ottosen">
                For most compilers this is due to problems
                with built-in arrays (notably char arrays) and operator==()
                and operator!=() for iterator_range. Thus, not using built-in arrays
                fixes the problem.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="sub_range"/>
            <toolset name="cw-9_5-darwin"/>
            <note author="Thorsten Ottosen">
                At the time of release I couldn't figure out why this was failing.
                Anyway, the failure is not very important; also, the well-definedness of
                "singularity" of an iterator range is likely to change.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="extension_mechanism"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Thorsten Ottosen">
                The test requires support for Argument Dependent Lookup (ADL)
                which the compiler in question does not provide.
            </note>
        </mark-expected-failures>
    </library>

    <!-- ratio -->
    <library name="ratio">
      <mark-unusable>
        <toolset name="borland-*"/>
        <note author="Vicente J. Botet Escriba">
            The compiler does not support features that are essential for the library.
        </note>
      </mark-unusable>
        <mark-expected-failures>
            <test name="mpl_plus_pass"/>
            <test name="ratio_ext_pass"/>
            <toolset name="intel-*-10.1"/>
            <toolset name="intel-*-11.0"/>
            <toolset name="intel-*-11.1"/>
            <note author="Vicente J. Botet Escriba">
                internal error: assertion failed: copy_template_param_expr.
                It seems to be already an Intel internal bug database (tracker number #82149).
                I have no workaround yet.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="si_physics"/>
            <toolset name="vacpp-10*"/>
            <note author="Vicente J. Botet Escriba">
                The compiler does not support features that are essential for this test .
            </note>
        </mark-expected-failures>
    </library>

    <!-- regex -->
    <library name="regex">
        <test name="regex_token_iterator_eg_2">
            <mark-failure>
                <toolset name="msvc-6.5"/>
                <note author="J. Maddock"/>
            </mark-failure>
        </test>
        <test name="posix_api_check">
            <mark-failure>
                <toolset name="como-4_3_3-vc7*"/>
                <note author="J. Maddock"/>
            </mark-failure>
        </test>
        <test name="wide_posix_api_check">
            <mark-failure>
                <toolset name="qcc-3.3.5_gpp"/>
                <note author="J. Maddock">
                    No Wide character support on this platform.
                </note>
            </mark-failure>
        </test>
        <test name="wide_posix_api_check_c">
            <mark-failure>
                <toolset name="qcc-3.3.5_gpp"/>
                <note author="J. Maddock">
                    No Wide character support on this platform.
                </note>
            </mark-failure>
        </test>
        <test name="*_dll">
            <mark-failure>
                <toolset name="*como-4_3_3*"/>
                <note author="J. Maddock">
                This test requires features that are unsupported by Como:
                use and building of dll's mainly.
                </note>
            </mark-failure>
        </test>
        <mark-expected-failures>
            <test name="static_mutex_test"/>
            <test name="test_grep"/>
            <toolset name="*como-4_3_3*"/>
            <note author="J. Maddock">
            This test requires features that are unsupported by Como:
            use and building of dll's mainly.
            </note>
        </mark-expected-failures>
       <mark-expected-failures>
          <test name="regex_regress_threaded"/>
          <toolset name="darwin*"/>
          <note author="J. Maddock">
             This tests fails because a dependency (Boost.Test)
             fails to initialise correctly.  The issue has been
             reported to the library's author.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
            <test name="regex_regress_threaded"/>
            <toolset name="gcc-*_tru64"/>
            <note author="J. Maddock">
              GCC on tru64 appears not to cope with C++ exceptions
              thrown from within threads.
            </note>
        </mark-expected-failures>
        <test name="concept_check">
            <mark-failure>
                <toolset name="msvc-8.0"/>
                <toolset name="sunpro-5_3-sunos"/>
                <toolset name="sun-5.8"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="qcc-3.3.5_cpp"/>
                <note author="John Maddock" refid="2"/>
            </mark-failure>
        </test>
        <test name="test_grep">
            <mark-failure>
                <toolset name="gcc-2.95.3-linux"/>
                <toolset name="sunpro-5_3-sunos"/>
                <toolset name="sun-5.8"/>
                <toolset name="msvc-6.5*"/>
                <toolset name="msvc-7.0"/>
                <note author="John Maddock">
                  This test fails because a dependency (Boost.Program Options) doesn't build with this compiler.
                </note>
            </mark-failure>
        </test>
        <test name="test_grep">
            <mark-failure>
                <toolset name="borland-5.9*"/>
                <note author="A.Meredith">
                  This test fails because a dependency (Boost.Program Options) which currently doesn't build with this compiler.
                </note>
            </mark-failure>
        </test>
        <mark-expected-failures>
            <test name="regex_regress"/>
            <test name="regex_regress_dll"/>
            <toolset name="iw-7_1-vc6-stlp-4_5_3"/>
            <note author="John Maddock" refid="29"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="regex_regress"/>
            <test name="regex_regress_threaded"/>
            <test name="regex_regress_dll"/>
            <toolset name="borland*"/>
            <note author="John Maddock">
              There appears to be a linker bug that prevents these
              projects from building, see http://qc.borland.com/wc/qcmain.aspx?d=32020.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="unicode_iterator_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <note author="John Maddock" refid="6"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="regex_regress"/>
            <test name="regex_regress_threaded"/>
            <test name="regex_regress_dll"/>
            <toolset name="borland*"/>
            <note author="John Maddock">
              There appears to be a linker bug that prevents these
              projects from building, see http://qc.borland.com/wc/qcmain.aspx?d=32020.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="regex_timer"/>
            <toolset name="msvc-6.5_stlport4"/>
            <note author="John Maddock">
               Test fails due to unresilved externals from STLport: appears to be
               an STLport bug. </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="regex_regress_threaded"/>
            <test name="static_mutex_test"/>
            <toolset name="msvc-6.5_stlport*"/>
            <toolset name="msvc-7.1_stlport*"/>
            <toolset name="msvc-8.0"/>
            <toolset name="gcc-cygwin*"/>
            <note author="John Maddock">
               These tests pass when run directly from the command line,
               but fail when run under the regression test script.
               The issue has never been fully pinned down, but appears
               to be related to how long the tests take to run.</note>
        </mark-expected-failures>
    </library>

    <!-- scope_exit -->
    <library name="scope_exit">
        <mark-unusable>
            <!-- most Preprocessor tests also fail on this compiler -->
            <toolset name="cray-8.0"/>
        </mark-unusable>
        <mark-expected-failures>
            <test name="emulation_tpl"/>
            <toolset name="intel-*-9.1"/>
            <toolset name="intel-*-10.0"/>
            <toolset name="intel-*-11.0"/>
            <note author="Alexander Nasonov">
                The test does not compile in typeof emulation mode,
                most likely due to a compiler bug. Users are advised
                to use native typeof.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="native"/>
            <toolset name="acc"/>
            <note author="Alexander Nasonov" refid="39"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="native_tpl"/>
            <toolset name="acc"/>
            <note author="Alexander Nasonov" refid="39"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="native_tu_test"/>
            <toolset name="acc"/>
            <note author="Alexander Nasonov" refid="39"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="native_tu_test"/>
            <toolset name="msvc-7.1*"/>
            <toolset name="msvc-8.0*"/>
            <note author="Alexander Nasonov" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="emulation_tu_test"/>
            <toolset name="msvc-7.1*"/>
            <toolset name="msvc-8.0*"/>
            <note author="Alexander Nasonov" refid="2"/>
        </mark-expected-failures>
        <!-- type-of emulation mode required -->
        <mark-expected-failures>
            <test name="native"/>
            <test name="native_this"/>
            <test name="native_this_tpl"/>
            <test name="native_tpl"/>
            <test name="native_tu_test"/>
            <toolset name="sun-5.10"/>
            <note author="Lorenzo Caminiti">
                This compiler does not support native type-of (force type-of
                emulation mode defining the BOOST_TYPEOF_EMULATION macro).
            </note>
        </mark-expected-failures>
        <!-- variadic macros required -->
        <mark-expected-failures>
            <test name="same_line"/>
            <test name="world"/>
            <test name="world_checkpoint"/>
            <test name="world_this"/>
            <test name="world_tpl"/>
            <toolset name="intel-darwin-11.1"/>
            <toolset name="intel-linux-10.1"/>
            <toolset name="intel-linux-11.1"/>
            <toolset name="pgi-11.9"/>
            <toolset name="sun-5.10"/>
            <note author="Lorenzo Caminiti" refid="51"/>
        </mark-expected-failures>
        <!-- lambda functions required -->
        <mark-expected-failures>
            <test name="world_checkpoint_all"/>
            <test name="world_checkpoint_all_seq"/>
            <test name="world_checkpoint_all_seq_nova"/>
            <toolset name="clang-darwin-trunk"/>
            <toolset name="darwin-4.4"/>
            <toolset name="darwin-4.4_0x"/>
            <toolset name="gcc-4.2.1"/>
            <toolset name="gcc-4.2.4"/>
            <toolset name="gcc-4.3.4"/>
            <toolset name="gcc-4.3.4_0x"/>
            <toolset name="gcc-4.4.3"/>
            <toolset name="gcc-4.4.3_0x"/>
            <toolset name="gcc-4.4.4"/>
            <toolset name="gcc-4.4.4_0x"/>
            <toolset name="gcc-4.5.3"/>
            <toolset name="gcc-4.6"/>
            <toolset name="gcc-4.6.2"/>
            <toolset name="gcc-mingw-4.4.0"/>
            <toolset name="gcc-mingw-4.4.7"/>
            <toolset name="gcc-mingw-4.5.2"/>
            <toolset name="gcc-mingw-4.5.4"/>
            <toolset name="gcc-mingw-4.6.3"/>
            <toolset name="gcc-mingw-4.7.0"/>
            <toolset name="intel-darwin-11.1"/>
            <toolset name="intel-darwin-12.0"/>
            <toolset name="intel-linux-10.1"/>
            <toolset name="intel-linux-11.1"/>
            <toolset name="intel-linux-12.0"/>
            <toolset name="intel-linux-12.1"/>
            <toolset name="msvc-8.0"/>
            <toolset name="msvc-9.0"/>
            <toolset name="msvc-9.0~stlport5.2"/>
            <toolset name="msvc-9.0~wm5~stlport5.2"/>
            <toolset name="pathscale-4.0.8"/>
            <toolset name="pgi-11.9"/>
            <toolset name="sun-5.10"/>
            <toolset name="vacpp-10.1"/>
            <toolset name="vacpp"/>
            <note author="Lorenzo Caminiti" refid="52"/>
        </mark-expected-failures>
    </library>

    <!-- signals -->
    <library name="signals">
        <mark-unusable>
            <toolset name="sunpro-5_3-sunos"/>
        </mark-unusable>
        <test name="signal_test">
            <mark-failure>
                <toolset name="cw-8.3*"/>
                <note author="B. Dawes" refid="2"/>
            </mark-failure>
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5"/>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="3"/>
            </mark-failure>
        </test>
    </library>

    <!-- statechart -->
    <library name="statechart">
        <mark-unusable>
            <toolset name="borland*"/>
            <toolset name="sun-5*"/>
            <toolset name="pgi-1*"/>
            <note author="Andreas Huber" refid="17"/>
        </mark-unusable>
        <mark-expected-failures>
            <test name="DllTestNormal"/>
            <toolset name="*mingw*"/>
            <toolset name="intel-darwin-10*"/>
            <toolset name="intel-darwin-11*"/>
            <note author="Andreas Huber">
                A runtime failure of this test indicates that this platform
                <b>dynamically</b> links code in a manner such that under
                certain circumstances more than one instance of a
                header-defined static class member can exist at runtime. See
                <a href="http://www.boost.org/libs/statechart/doc/faq.html#Dll">FAQ</a>
                for more information.
            </note>
        </mark-expected-failures>
        <mark-expected-failures reason="?">
            <test name="FifoSchedulerTest*"/>
            <toolset name="darwin-4.4"/>
            <toolset name="intel-darwin-10*"/>
            <note author="Andreas Huber" refid="29"/>
        </mark-expected-failures>
        <mark-expected-failures reason="?">
            <test name="TransitionTest*"/>
            <toolset name="intel-darwin-10*"/>
            <toolset name="intel-linux-10*"/>
            <toolset name="gcc-4.3.4_0x"/>
            <toolset name="pathscale-3.2"/>
            <note author="Andreas Huber" refid="29"/>
        </mark-expected-failures>
    </library>

    <!-- static_assert -->
    <library name="static_assert">
        <test name="static_assert_example_2">
            <mark-failure>
                <toolset name="sunpro-5_3-sunos"/>
                <note author="J. Maddock" refid="4"/>
            </mark-failure>
        </test>
        <test name="static_assert_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="A.Meredith" date="26 May 2006">
                  This test runs without problem on Borland compilers,
                  which means the static assertion is not being caught.
                </note>
            </mark-failure>
        </test>
    </library>

    <!-- system -->
    <library name="system">
      <mark-unusable>
        <toolset name="borland-5.6*"/>
        <toolset name="borland-5.8*"/>
        <note author="Beman Dawes">
            This compiler does not support enable_if, which is required
            by Boost.System.
        </note>
      </mark-unusable>
    </library>

    <!-- test -->
    <library name="test">
        <mark-expected-failures>
            <test name="ifstream_line_iterator_test"/>
            <toolset name="sunpro*"/>
            <note author="Gennadiy Rozental" refid="2"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="custom_exception_test"/>
            <toolset name="msvc-6.5*"/>
            <note author="Gennadiy Rozental" refid="2"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="errors_handling_test"/>
            <toolset name="*como-4_3_3*"/>
            <note author="B. Dawes" refid="3"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="token_iterator_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="iw-7_1-vc6"/>
            <toolset name="msvc-7.0"/>
            <toolset name="msvc-7.0-stlport"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <toolset name="hp_cxx-65*"/>
            <toolset name="sunpro*"/>
            <toolset name="borland*"/>
            <note author="Gennadiy Rozental" refid="3"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="token_iterator_test"/>
            <toolset name="qcc-3.3.5*gpp"/>
            <note author="Jim Douglas" date="14 Feb 06" refid="36"/>
       </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_fp_comparisons"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="msvc-7.0-stlport"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Gennadiy Rozental" refid="3"/>
        </mark-expected-failures>

        <mark-expected-failures reason="?">
            <test name="basic_cstring_test"/>
            <toolset name="gcc-2.95.3-linux"/>
            <note refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
          <test name="test_tools_test"/>
          <toolset name="cw-9_5-darwin"/>
          <note refid="29" author="Doug Gregor"/>
        </mark-expected-failures>

        <mark-expected-failures>
          <test name="errors_handling_test"/>
          <toolset name="acc*"/>
          <toolset name="cw-9_5-darwin"/>
          <note refid="29" author="Doug Gregor"/>
        </mark-expected-failures>

        <mark-expected-failures>
          <test name="test_tools_test"/>
          <toolset name="cw-9.4"/>
          <note refid="29" author="Doug Gregor"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="prg_exec_fail2"/>
            <toolset name="gcc-3.4.2_hpux_pa_risc"/>
            <toolset name="gcc-3.4.6_linux_ia64"/>
            <note author="Vladimir Prus">
              The test verifies that Boost.Test detects division by
              zero. It fails on PowerPC, PA-RISC and Linux ia64. On PowerPC
              processors, division has an undefined result. The compiler
              has to emit extra code to assert that the divisor isn't zero.

              Compiler options -fno-trapping-math and -fnon-call-exceptions
              might affect this. However, in default configuration
              no check is done, and division by zero is not detected.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="prg_exec_fail3"/>
            <toolset name="cw-9.4"/>
            <toolset name="gcc-3.4.6_linux_ia64"/>
            <note author="Vladimir Prus">
              The test appears to test that failed assertion result
              in non-zero exit status.  That seems to be not the
              case, for unknown reasons.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
          <test name="sync_access_test"/>
          <toolset name="acc*"/>
          <toolset name="gcc-4.2.1_hpux_ia64"/>
          <note author="Boris Gubenko">
             On HP-UX platform, this test must be compiled/linked in multithread mode.
             When compiled/linked with aC++ with -mt, it succeeds. When compiled/linked
             with GCC with -pthread, it links cleanly but fails in run-time.
          </note>
        </mark-expected-failures>

    </library>


    <!-- thread -->
    <library name="thread">
        <mark-unusable>
            <toolset name="*como-4_3_3*"/>
            <note author="B. Dawes" refid="10"/>
        </mark-unusable>

        <test name="test_mutex">
            <mark-failure>
                <toolset name="msvc-7.0"/>
                <note author="B. Dawes" refid="0"/>
                <note author="B. Dawes" refid="6"/>
            </mark-failure>
        </test>
        <mark-expected-failures>
            <test name="*_lib"/>
            <toolset name="intel-8.0-linux*"/>
            <note author="Aleksey Gurtovoy">
                This failure is caused by a conflict between the compiler
                and the testing environment: the tests are run on a platform with
                <i>too recent</i> version of glibc, which is not currently
                supported by the compiler vendor (Intel).

                If you are having the same problem and <i>really</i> want to make
                things work, renaming <code>strol</code> symbol in the
                compiler's static runtime library (<code>libcprts.a</code>) to
                something else is known to resolve the issue.
            </note>
        </mark-expected-failures>
        <mark-expected-failures reason="?">
            <test name="*_lib"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <note author="Aleksey Gurtovoy" refid="29"/>
        </mark-expected-failures>
        <!--
        It is unclear why this has been marked as expected failure. The
        pthread_timedwait is giving an error code of EINVAL, which needs to
        be resolved, since the timed behaviour is affected by this bug.
        Marked as a failure again by Roland Schwarz, 2007-01-12
        <mark-expected-failures>
            <test name="test_mutex"/>
            <test name="test_mutex_lib"/>
            <toolset name="qcc-3.3*"/>
            <note author="Jim Douglas" date="13 Feb 06" refid="16"/>
        </mark-expected-failures>
        -->
        <mark-expected-failures>
            <test name="test_tss_lib"/>
            <toolset name="borland-*"/>
            <toolset name="como-win-*"/>
            <toolset name="msvc*wm5*"/>
            <toolset name="cw-9.4"/>
            <toolset name="gcc-mingw*"/>
            <note author="Roland Schwarz" date="2006-12-14">
                When a thread ends, tss data needs to be cleaned up. This process
                is mostly automatic. When threads are launched by the Boost.Thread API
                cleanup is handled by the library implementation. For threads, launched
                by the native operating system API it is not possible to get this cleanup
                on every compiler/platform. A warning (error) will be present in this case,
                which cleary states this fact. It is recommended to start threads only
                by means of the Boost.Thread API if you need to avoid the leaks that appear
                on the end of the thread. If this is not possible the cleanup can be invoked
                from user code before the process actually ends. For library implementors
                this means to call these functions during library initialization and
                finalization.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_thread_move"/>
            <test name="test_thread_move_lib"/>
            <test name="test_move_function"/>
            <test name="test_move_function_lib"/>
            <toolset name="acc"/>
            <toolset name="borland-*"/>
            <toolset name="sun-*"/>
            <note author="Anthony Williams" date="2007-12-14">
The Borland compiler and HP-UX aC++ compiler in default mode fail to bind rvalues to the thread move constructor,
choosing instead to bind them to the private (and unimplemented) copy constructor.
With aC++, the tests compile cleanly in strict ansi mode and succeed.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_thread_move_return"/>
            <test name="test_thread_move_return_lib"/>
            <test name="test_thread_return_local"/>
            <test name="test_thread_return_local_lib"/>
            <toolset name="sun-*"/>
            <note author="Anthony Williams" date="2009-10-28">
These tests will fail in most compilers that don't support rvalue references.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="*mutex__native_handle*"/>
            <test name="*variable__native_handle*"/>
            <toolset name="msvc-*"/>
            <toolset name="gcc-mingw*"/>
            <note author="Vicente J. Botet Escriba" date="2012-03-21">
The implementation of native_handle() is not possible on this platform.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="recursive_mutex__native_handle*"/>
            <toolset name="pgi-*"/>
            <toolset name="sun-*"/>
            <note author="Vicente J. Botet Escriba" date="2012-03-21">
The implementation of native_handle() is not possible on this platform.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="*sleep_for*"/>
            <test name="*sleep_until*"/>
            <test name="*thread__join*"/>
            <test name="*wait_for*"/>
            <test name="*wait_until*"/>
            <test name="*cons__duration*"/>
            <test name="*cons__time_point*"/>
            <test name="*try_lock_for*"/>
            <test name="*try_lock_until*"/>
            <test name="*upgrade_lock_for*"/>
            <test name="*upgrade_lock_until*"/>
            <test name="*try_join_for*"/>
            <test name="*try_join_until*"/>
            <test name="*chrono*"/>
            <test name="*shared_lock_until*"/>
            <test name="*shared_lock_for*"/>
            <test name="shared_mutex*"/>
            <test name="future__get*"/>
            <test name="packaged_task__operator*"/>
            <test name="test_3628*"/>
            <test name="test_7328*"/>
            <toolset name="vacpp-10*"/>
            <note author="Vicente J. Botet Escriba" date="2012-10-13">
This platform doesn't supports Boost.Chrono.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="container__*"/>
            <toolset name="vacpp-10*"/>
            <note author="Vicente J. Botet Escriba" date="2012-10-13">
This platform doesn't supports Boost.Container.
            </note>
        </mark-expected-failures>

    </library>

    <!-- tti -->
    <library name="tti">
      <mark-expected-failures>
        <test name="test_has_mem_data2"/>
        <test name="test_has_mem_fun_cv_fail2"/>
        <test name="test_has_mem_fun_cv_fail4"/>
        <test name="test_has_member "/>
        <test name="test_has_member_compile"/>
        <test name="test_has_member_cv "/>
        <test name="test_has_member_cv_compile "/>
        <toolset name="vacpp-12.1.0.1"/>
        <note>These are all failures with Boost.FunctionTypes which TTI uses.</note>
      </mark-expected-failures>
    </library>
  
    <!-- tuple -->
    <library name="tuple">
        <mark-unusable>
            <toolset name="sunpro-5_3-sunos"/>
        </mark-unusable>
    </library>
  
    <!-- type_erasure -->
    <library name="type_erasure">
      <mark-unusable>
        <toolset name="gcc-4.2.1"/>
        <toolset name="gcc-4.4*"/>
        <toolset name="gcc-mingw-4.4*"/>
        <toolset name="darwin-4.2.1"/>
        <note author="Steven Watanabe" refid="17"/>
      </mark-unusable>
      <mark-expected-failures>
        <test name="test_add"/>
        <test name="test_add_assign"/>
        <test name="test_equal"/>
        <test name="test_less"/>
        <test name="test_construct"/>
        <toolset name="msvc-8.0*"/>
        <note author="Steven Watanabe">
          SFINAE for the constructors of param doesn't work correctly on this compiler.
          This affects free functions (including operators) with more than one
          any argument and overloaded member functions.
        </note>
      </mark-expected-failures>
      <mark-expected-failures reason="?">
        <test name="test_free"/>
        <toolset name="msvc-8.0"/>
        <note>This looks like an instance of MSVC substituting int in a template-id.</note>
      </mark-expected-failures>
    </library>

    <!-- type_index -->
    <library name="type_index">
      <mark-expected-failures>
        <test name="testing_crossmodule_anonymous"/>
        <toolset name="clang-*"/>
        <toolset name="darwin-*"/>
        <toolset name="gcc-3.*"/>
        <toolset name="gcc-4.0*"/>
        <toolset name="gcc-4.1*"/>
        <toolset name="gcc-4.2*"/>
        <toolset name="gcc-4.3*"/>
        <toolset name="gcc-4.4*"/>
        <!-- gcc-4.5+ is passing the test -->
        <toolset name="intel-linux"/>
        <toolset name="vacpp"/>
        <toolset name="gcc-mingw-3.*"/>
        <toolset name="gcc-mingw-4.0*"/>
        <toolset name="gcc-mingw-4.1*"/>
        <toolset name="gcc-mingw-4.2*"/>
        <toolset name="gcc-mingw-4.3*"/>
        <toolset name="gcc-mingw-4.4*"/>
        <toolset name="gcc-mingw-4.5*"/>
        <toolset name="gcc-mingw-4.6*"/>
        <toolset name="qcc-4.4*"/>
        <note author="Antony Polukhin">
          Classes with exactly the same names defined in different modules in anonymous namespaces collapse
          for this compiler even with RTTI on. This is a known compiler limitation that already fixed
          in newer versions or will be fixed soon.
        </note>
      </mark-expected-failures>
      <mark-expected-failures>
        <test name="testing_crossmodule_anonymous_no_rtti"/>
        <toolset name="clang-*"/>
        <toolset name="darwin-*"/>
        <toolset name="intel-linux-*"/>
        <toolset name="gcc-*"/>
        <note author="Antony Polukhin">
          Classes with exactly the same names defined in different modules in anonymous namespaces collapse
          for this compiler with RTTI off. This is a known limitation of RTTI-off mode. Such behavior is
          reflected in docs.
        </note>
      </mark-expected-failures>
    </library>

    <!-- type_traits -->
    <library name="type_traits">
       <mark-expected-failures>
          <test name="is_virtual_base_of_test"/>
          <toolset name="gcc-3.4.6"/>
          <note author="John Maddock">
             Type Traits tests are run with warnings-as-errors and GCC 3.x emits warnings with this test
             that I haven't been able to suppress.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="has_operator_new_test"/>
          <test name="make_signed_test"/>
          <test name="make_unsigned_test"/>
          <toolset name="msvc-7.1"/>
          <note author="John Maddock">
             Apparently the compiler can't cope with these - later versions are fine though.
             Probably work-round-able if someone would care to look into these.
          </note>
       </mark-expected-failures>
        <mark-expected-failures>
            <test name="function_traits_test"/>
            <test name="remove_bounds_test"/>
            <test name="remove_const_test"/>
            <test name="remove_cv_test"/>
            <test name="remove_pointer_test"/>
            <test name="remove_reference_test"/>
            <test name="remove_volatile_test"/>
            <test name="decay_test"/>
            <test name="extent_test"/>
            <test name="remove_extent_test"/>
            <test name="remove_all_extents_test"/>
            <test name="rank_test"/>
            <test name="is_unsigned_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Aleksey Gurtovoy">
                This failure is caused by the lack of compiler support for class template
                partial specialization. A limited subset of the tested functionality is
                available on the compiler through a user-side workaround (see
                <a href="http://www.boost.org/libs/type_traits/index.html#transformations">
                http://www.boost.org/libs/type_traits/index.html#transformations</a> for
                details).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="decay_test"/>
            <test name="extent_test"/>
            <test name="is_base_and_derived_test"/>
            <test name="is_base_of_test"/>
            <test name="is_convertible_test"/>
            <test name="rank_test"/>
            <test name="remove_all_extents_test"/>
            <test name="remove_bounds_test"/>
            <test name="remove_const_test"/>
            <test name="remove_extent_test"/>
            <test name="remove_pointer_test"/>
            <test name="remove_volatile_test"/>
            <test name="tricky_add_pointer_test"/>
            <test name="tricky_function_type_test"/>
            <test name="tricky_incomplete_type_test"/>
            <test name="make_signed_test"/>
            <test name="make_unsigned_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="John Maddock" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="promote_basic_test"/>
            <test name="promote_enum_test"/>
            <test name="promote_mpl_test"/>
            <test name="tricky_partial_spec_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="AlisdairM" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="promote_enum_msvc_bug_test"/>
            <toolset name="msvc-7.1*"/>
            <toolset name="msvc-8.0*"/>
            <toolset name="msvc-9.0*"/>
            <toolset name="msvc-10.0*"/>
            <toolset name="msvc-11.0*"/>
            <toolset name="msvc-12.0*"/>
            <toolset name="msvc-14.0*"/>
            <toolset name="msvc-14.1*"/>
            <toolset name="msvc-14.2*"/>
            <note author="Alexander Nasonov">
                See bug 99776 'enum UIntEnum { value = UINT_MAX } is promoted to int'
                http://lab.msdn.microsoft.com/ProductFeedback/viewfeedback.aspx?feedbackid=22b0a6b7-120f-4ca0-9136-fa1b25b26efe
                https://developercommunity.visualstudio.com/content/problem/490264/standard-violation-enum-underlying-type-cannot-rep.html
	    </note>
        </mark-expected-failures>
        <test name="tricky_is_enum_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5*"/>
                <toolset name="gcc-2.95.3-*"/>
            </mark-failure>
        </test>
        <test name="tricky_incomplete_type_test">
            <mark-failure>
                <toolset name="iw-7_1*"/>
                <note author="John Maddock" refid="2"/>
            </mark-failure>
        </test>
        <test name="is_abstract_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="cw-8.3*"/>
                <toolset name="cw-9.3*"/>
                <toolset name="cw-9.4"/>
                <toolset name="cw-9.5"/>
                <toolset name="msvc-6.5*"/>
                <toolset name="msvc-7.0"/>
                <toolset name="mingw-3_3*"/>
                <toolset name="gcc-2*"/>
                <toolset name="gcc-3.2*"/>
                <toolset name="gcc-3.3*"/>
                <toolset name="qcc-3.3*"/>
                <toolset name="sunpro-5_3-sunos"/>
                <toolset name="hp_cxx-65*"/>
                <toolset name="darwin"/>
                <toolset name="mingw"/>
                <note author="Aleksey Gurtovoy">
                    This functionality is available only on compilers that implement C++ Core Language
                    <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/cwg_defects.html#337">Defect Report 337</a>.
                </note>
            </mark-failure>
        </test>

        <mark-expected-failures>
          <test name="is_polymorphic_test"/>
          <toolset name="gcc-2.95.3-stlport-*"/>
          <note author="Doug Gregor" refid="3"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="decay_test"/>
            <test name="extent_test"/>
            <test name="has_nothrow_assign_test"/>
            <test name="has_nothrow_constr_test"/>
            <test name="has_nothrow_copy_test"/>
            <test name="has_trivial_assign_test"/>
            <test name="has_trivial_constr_test"/>
            <test name="has_trivial_copy_test"/>
            <test name="has_trivial_destructor_test"/>
            <test name="is_array_test"/>
            <test name="is_base_and_derived_test"/>
            <test name="is_base_of_test"/>
            <test name="is_class_test"/>
            <test name="is_convertible_test"/>
            <test name="is_object_test"/>
            <test name="is_pod_test"/>
            <test name="is_polymorphic_test"/>
            <test name="rank_test"/>
            <test name="remove_all_extents_test"/>
            <test name="remove_bounds_test"/>
            <test name="remove_extent_test"/>
            <toolset name="sunpro-5_3-sunos"/>

            <note author="John Maddock">
                The Type Traits library is broken when used with Sunpro-5.3 and the
                argument to the template is an array or function type.  Most other argument types
                do work as expected: in other words the functionality is limited
                with this compiler, but not so much as to render the library unuseable.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="is_empty_test"/>
            <test name="is_function_test"/>
            <test name="is_member_func_test"/>
            <test name="is_member_obj_test"/>
            <test name="is_reference_test"/>
            <test name="tricky_function_type_test"/>
            <test name="tricky_incomplete_type_test"/>
            <test name="tricky_is_enum_test"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="John Maddock" refid="2"/>
        </mark-expected-failures>
       <mark-expected-failures>
          <test name="decay_test"/>
          <test name="extent_test"/>
          <test name="is_abstract_test"/>
          <test name="is_empty_test"/>
          <test name="is_function_test"/>
          <test name="is_member_func_test"/>
          <test name="is_member_obj_test"/>
          <test name="is_object_test"/>
          <test name="is_reference_test"/>
          <test name="rank_test"/>
          <test name="tricky_function_type_test"/>
          <toolset name="sun-5.8"/>

          <note author="John Maddock">
             The Type Traits library is broken when used with Sunpro-5.8 and the
             argument to the template is a function type.  Most other argument types
             do work as expected: in other words the functionality is limited
             with this compiler, but not so much as to render the library unuseable.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="tricky_partial_spec_test"/>
          <toolset name="sun-5.9"/>
          <note author="John Maddock">
             This fails with an internal compiler error,
             there is no workaround as yet.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
            <test name="tricky_function_type_test"/>
            <test name="is_const_test"/>
            <test name="is_volatile_test"/>
            <test name="is_convertible_test"/>
            <toolset name="gcc-2*"/>
            <note author="John Maddock" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="aligned_storage_test"/>
            <toolset name="cw-8.3"/>
            <note author="John Maddock">
               Older versions of MWCW incorrectly align pointers to member functions
               (they use 12-byte boundaries, rather than a power-of-2 boundary),
               leading to alignment_of / aligned_storage
               to fail with these types on this compiler.
            </note>
        </mark-expected-failures>
    </library>

    <!-- tr1 -->
    <library name="tr1">
        <mark-unusable>
            <toolset name="msvc-7.0"/>
            <toolset name="msvc-6*"/>
            <note author="John Maddock">
                VC6/7 has a buggy using declaration syntax which
                basically makes it impossible to implement the
                namespace forwarding that this library relies upon.
                See KB article 263630 here: http://support.microsoft.com/default.aspx?scid=kb;en-us;263630
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="cw-*"/>
            <note author="John Maddock">
                Metrowerks Codeworrier has partial TR1 support built in
                which conflicts with this implementation.  Porting to this
                compiler is almost certainly possible, but will require some
                work by someone who has this compiler.
            </note>
        </mark-unusable>
       <mark-expected-failures>
          <test name="test_type_traits"/>
          <test name="std_test_type_traits"/>
          <toolset name="msvc-7.1"/>
          <note author="John Maddock">
             Later versions of MSVC are required for these tests - the issues may
             be work-round-able if anyone cares enough to look into them.
          </note>
       </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_mem_fn_tricky"/>
            <test name="test_bind_tricky"/>
            <test name="test_ref_wrapper_tricky"/>
            <test name="test_function_tricky"/>
            <test name="std_test_mem_fn_tricky"/>
            <test name="std_test_bind_tricky"/>
            <test name="std_test_ref_wrapper_tricky"/>
            <test name="std_test_function_tricky"/>
            <test name="std_test_reference_wrapper_tricky"/>
            <test name="test_reference_wrapper_tricky"/>
            <test name="std_test_cmath_tricky"/>
            <test name="test_cmath_tricky"/>
            <toolset name="*"/>
            <note author="John Maddock">
                These tests test features that are not supported in the
                current Boost implementations of TR1 components, they will
                currently fail on all compilers, unless that compiler has
                native TR1 support.
            </note>
        </mark-expected-failures>

       <mark-expected-failures>
          <test name="run_random"/>
          <test name="std_run_random"/>
          <test name="std_test_bind"/>
          <test name="test_bind"/>
          <test name="std_test_regex"/>
          <test name="test_regex"/>
          <test name="std_test_result_of"/>
          <test name="test_result_of"/>
          <test name="tr1_has_tr1_result_of_pass"/>
          <test name="tr1_has_trivial_constr_test"/>
          <test name="tr1_is_base_of_test"/>
          <test name="tr1_is_convertible_test"/>
          <test name="tr1_is_pod_test"/>
          <test name="tr1_is_polymorphic_test"/>
          <test name="tr1_tky_function_type_test"/>
          <toolset name="msvc-9.0"/>
          <note author="John Maddock">
             MSVC 9.0 with the optional feature pack installed includes
             a version of the TR1 libraries that is not as interface-conforming
             as the Boost version.  Most of these failures are of the "annoying"
             rather than "unusable" kind.
          </note>
       </mark-expected-failures>

       <mark-expected-failures>
            <test name="test_regex"/>
            <test name="std_test_regex"/>
            <test name="test_hash"/>
            <test name="std_test_hash"/>
            <toolset name="mingw*"/>
            <toolset name="qcc*gpp"/>
            <toolset name="gcc-2*"/>
            <note author="John Maddock">
               These tests fail on this platform due to a lack of
               wide character support.
            </note>
        </mark-expected-failures>

       <mark-expected-failures>
          <test name="test_cmath"/>
          <test name="std_test_cmath"/>
          <toolset name="intel-linux-9.0"/>
          <toolset name="darwin-4.0.1"/>
          <note author="John Maddock">
             These tests fail due to a lack of adequate
             long double std math lib support.
          </note>
       </mark-expected-failures>

       <mark-expected-failures>
            <test name="test_regex"/>
            <test name="std_test_regex"/>
            <toolset name="gcc-mingw*"/>
            <note author="John Maddock">
               These tests fail on this platform due to incomplete
               wide character support.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_hash"/>
            <test name="std_test_hash"/>
            <toolset name="gcc-cygwin*"/>
            <note author="John Maddock">
               These tests fail on this platform due to incomplete
               wide character support.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_array"/>
            <test name="std_test_array"/>
            <test name="test_array_tricky"/>
            <test name="std_test_array_tricky"/>
            <test name="test_complex"/>
            <test name="std_test_complex"/>
            <test name="test_function"/>
            <test name="std_test_function"/>
            <test name="test_mem_fn"/>
            <test name="std_test_mem_fn"/>
            <test name="test_random"/>
            <test name="std_test_random"/>
            <test name="test_regex"/>
            <test name="std_test_regex"/>
            <test name="test_result_of"/>
            <test name="std_test_result_of"/>
            <test name="test_shared_ptr"/>
            <test name="std_test_shared_ptr"/>
            <test name="test_tr1_include"/>
            <test name="std_test_tr1_include"/>
            <test name="test_tuple"/>
            <test name="std_test_tuple"/>
            <test name="test_tuple_tricky"/>
            <test name="std_test_tuple_tricky"/>
            <test name="test_type_traits"/>
            <test name="std_test_type_traits"/>
            <test name="run_complex_overloads"/>
            <test name="std_run_complex_overloads"/>
            <test name="run_random"/>
            <test name="std_run_random"/>
            <test name="test_tuple_tricky"/>
            <test name="tr1_add_const_test"/>
            <test name="tr1_add_cv_test"/>
            <test name="tr1_add_pointer_test"/>
            <test name="tr1_add_reference_test"/>
            <test name="tr1_add_volatile_test"/>
            <test name="tr1_aligned_storage_test"/>
            <test name="tr1_alignment_of_test"/>
            <test name="tr1_has_nothrow_assign_test"/>
            <test name="tr1_has_nothrow_constr_test"/>
            <test name="tr1_has_nothrow_copy_test"/>
            <test name="tr1_has_trivial_assign_test"/>
            <test name="tr1_has_trivial_constr_test"/>
            <test name="tr1_has_trivial_copy_test"/>
            <test name="tr1_has_trivial_destr_test"/>
            <test name="tr1_has_virtual_destr_test"/>
            <test name="tr1_is_arithmetic_test"/>
            <test name="tr1_is_array_test"/>
            <test name="tr1_is_class_test"/>
            <test name="tr1_is_compound_test"/>
            <test name="tr1_is_const_test"/>
            <test name="tr1_is_convertible_test"/>
            <test name="tr1_is_empty_test"/>
            <test name="tr1_is_enum_test"/>
            <test name="tr1_is_floating_point_test"/>
            <test name="tr1_is_function_test"/>
            <test name="tr1_is_fundamental_test"/>
            <test name="tr1_is_integral_test"/>
            <test name="tr1_is_member_func_test"/>
            <test name="tr1_is_member_obj_test"/>
            <test name="tr1_is_member_pointer_test"/>
            <test name="tr1_is_object_test"/>
            <test name="tr1_is_pod_test"/>
            <test name="tr1_is_pointer_test"/>
            <test name="tr1_is_polymorphic_test"/>
            <test name="tr1_is_reference_test"/>
            <test name="tr1_is_same_test"/>
            <test name="tr1_is_scalar_test"/>
            <test name="tr1_is_signed_test"/>
            <test name="tr1_is_union_test"/>
            <test name="tr1_is_unsigned_test"/>
            <test name="tr1_is_void_test"/>
            <test name="tr1_is_volatile_test"/>
            <test name="tr1_remove_const_test"/>
            <test name="tr1_remove_cv_test"/>
            <test name="tr1_remove_pointer_test"/>
            <test name="tr1_remove_reference_test"/>
            <test name="tr1_remove_volatile_test"/>
            <test name="tr1_tky_abstract_type_test"/>
            <test name="tr1_tricky_add_pointer_test"/>
            <test name="tr1_tky_partial_spec_test"/>
            <toolset name="borland-5.6*"/>
            <note author="John Maddock">
               Support for Borland C++ in the various TR1 libraries is pretty
               poor (due to numerous compiler bugs sadly).  The TR1 concept
               checks are *very* strict, and are expected to fail with this
               compiler.  In addition most of the type_traits tests fail
               whenever debugging support is turned on with an internal
               compiler error.  More conservative uses are more likely to succeed
               with this compiler however.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_complex"/>
            <test name="std_test_complex"/>
            <test name="test_function"/>
            <test name="std_test_function"/>
            <test name="test_random"/>
            <test name="std_test_random"/>
            <test name="test_result_of"/>
            <test name="std_test_result_of"/>
            <test name="test_tuple_tricky"/>
            <test name="std_test_tuple_tricky"/>
            <test name="test_type_traits"/>
            <test name="std_test_type_traits"/>
            <test name="run_complex_overloads"/>
            <test name="std_run_complex_overloads"/>
            <test name="test_shared_ptr"/>
            <test name="std_test_shared_ptr"/>
            <test name="std_run_random"/>
            <test name="run_random"/>
            <test name="test_tuple_tricky"/>
            <test name="tr1_is_convertible_test"/>
            <test name="tr1_remove_const_test"/>
            <test name="tr1_remove_pointer_test"/>
            <test name="tr1_remove_volatile_test"/>
            <test name="tr1_tricky_add_pointer_test"/>
            <test name="tr1_tky_partial_spec_test"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="John Maddock">
               Support for Borland C++ in the various TR1 libraries is pretty
               poor (due to numerous compiler bugs sadly).  The TR1 concept
               checks are *very* strict, and are expected to fail with this
               compiler.  More conservative uses are more likely to succeed
               with this compiler however.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="std_test_bind"/>
            <test name="test_bind"/>
            <toolset name="gcc-4*darwin"/>
            <toolset name="darwin*"/>
            <note author="John Maddock">
               These tests fail on this platform due to a recuring GCC bug.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_type_traits"/>
            <test name="std_test_type_traits"/>
            <test name="tr1_is_abstract_test"/>
            <toolset name="gcc-3.3.*"/>
            <toolset name="gcc-3.2*"/>
            <toolset name="qcc-3.3*"/>
            <note author="John Maddock">
               These tests fail due to a known compiler bug
               that is fixed in more recent GNU compiler releases.  Users are
               very unlikely to encounter this as a real problem
               in practice.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_regex"/>
            <test name="std_test_regex"/>
            <test name="test_complex"/>
            <test name="std_test_complex"/>
            <test name="run_complex_overloads"/>
            <test name="std_run_complex_overloads"/>
            <toolset name="gcc-2*"/>
            <note author="John Maddock">
               These tests fail due to a known compiler bug
               that is fixed in more recent releases.  Users are
               very unlikely to encounter this as a real problem
               in practice.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_type_traits"/>
            <test name="std_test_type_traits"/>
            <test name="test_result_of"/>
            <test name="std_test_result_of"/>
            <test name="tr1_is_abstract_test"/>
            <test name="test_ios"/>
            <test name="test_istream"/>
            <test name="test_ostream"/>
            <test name="test_streambuf"/>
            <test name="test_limits"/>
            <test name="test_locale"/>
            <test name="test_ios_std_header"/>
            <test name="test_istream_std_header"/>
            <test name="test_limits_std_header"/>
            <test name="test_locale_std_header"/>
            <test name="test_ostream_std_header"/>
            <test name="test_streambuf_std_header"/>
            <toolset name="gcc-2*"/>
            <note author="John Maddock">
               These tests fail due to a known compiler bug
               that is fixed in more recent releases.  This
               functionality may not be usable with this compiler.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
           <test name="run_complex_overloads"/>
           <test name="std_run_complex_overloads"/>
           <test name="std_test_complex"/>
           <test name="test_complex"/>
           <toolset name="qcc-3.3.5*gpp"/>
            <note author="John Maddock">
               These tests fail due to a known stdlib bug
               that has been reported to the vendor.
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="tr1_function_traits_test"/>
            <test name="tr1_remove_bounds_test"/>
            <test name="tr1_remove_const_test"/>
            <test name="tr1_remove_cv_test"/>
            <test name="tr1_remove_pointer_test"/>
            <test name="tr1_remove_reference_test"/>
            <test name="tr1_remove_volatile_test"/>
            <test name="tr1_decay_test"/>
            <test name="tr1_extent_test"/>
            <test name="tr1_remove_extent_test"/>
            <test name="tr1_remove_all_extents_test"/>
            <test name="tr1_rank_test"/>
            <test name="tr1_is_unsigned_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Aleksey Gurtovoy">
                This failure is caused by the lack of compiler support for class template
                partial specialization. A limited subset of the tested functionality is
                available on the compiler through a user-side workaround (see
                <a href="http://www.boost.org/libs/type_traits/index.html#transformations">
                http://www.boost.org/libs/type_traits/index.html#transformations</a> for
                details).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="tr1_tky_incomplete_type_test"/>
            <test name="tr1_tky_incomp_type_test"/>
            <test name="tr1_decay_test"/>
            <test name="tr1_extent_test"/>
            <test name="tr1_is_base_of_test"/>
            <test name="tr1_rank_test"/>
            <test name="tr1_remove_all_extents_test"/>
            <test name="tr1_remove_extent_test"/>
            <test name="tr1_tky_function_type_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="John Maddock" refid="2"/>
        </mark-expected-failures>
        <test name="tr1_tricky_is_enum_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="msvc-6.5*"/>
                <toolset name="gcc-2.95.3-*"/>
            </mark-failure>
        </test>
        <test name="tr1_tricky_incomplete_type_test">
            <mark-failure>
                <toolset name="iw-7_1*"/>
                <note author="John Maddock" refid="2"/>
            </mark-failure>
        </test>
        <test name="tr1_tricky_incomp_type_test">
            <mark-failure>
                <toolset name="iw-7_1*"/>
                <note author="John Maddock" refid="2"/>
            </mark-failure>
        </test>
        <test name="tr1_tky_incomp_type_test">
            <mark-failure>
                <toolset name="iw-7_1*"/>
                <note author="John Maddock" refid="2"/>
            </mark-failure>
        </test>
        <test name="tr1_is_abstract_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <toolset name="cw-8.3*"/>
                <toolset name="cw-9.3*"/>
                <toolset name="cw-9.4*"/>
                <toolset name="cw-9.5*"/>
                <toolset name="msvc-6.5*"/>
                <toolset name="msvc-7.0"/>
                <toolset name="mingw-3_3*"/>
                <toolset name="gcc-2*"/>
                <toolset name="gcc-3.2*"/>
                <toolset name="gcc-3.3*"/>
                <toolset name="gcc-3_3*"/>
                <toolset name="qcc-3_3*"/>
                <toolset name="sunpro-5_3-sunos"/>
                <toolset name="hp_cxx-65*"/>
                <toolset name="darwin"/>
                <toolset name="mingw"/>
                <note author="Aleksey Gurtovoy">
                    This functionality is available only on compilers that implement C++ Core Language
                    <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/cwg_defects.html#337">Defect Report 337</a>.
                </note>
            </mark-failure>
        </test>

        <mark-expected-failures>
          <test name="tr1_is_polymorphic_test"/>
          <toolset name="gcc-2.95.3-stlport-*"/>
          <note author="Doug Gregor" refid="3"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="tr1_decay_test"/>
            <test name="tr1_extent_test"/>
            <test name="tr1_has_nothrow_assign_test"/>
            <test name="tr1_has_nothrow_constr_test"/>
            <test name="tr1_has_nothrow_copy_test"/>
            <test name="tr1_has_trivial_assign_test"/>
            <test name="tr1_has_trivial_constr_test"/>
            <test name="tr1_has_trivial_copy_test"/>
            <test name="tr1_has_trivial_destr_test"/>
            <test name="tr1_is_array_test"/>
            <test name="tr1_is_base_and_derived_test"/>
            <test name="tr1_is_base_of_test"/>
            <test name="tr1_is_class_test"/>
            <test name="tr1_is_convertible_test"/>
            <test name="tr1_is_object_test"/>
            <test name="tr1_is_pod_test"/>
            <test name="tr1_is_polymorphic_test"/>
            <test name="tr1_rank_test"/>
            <test name="tr1_remove_all_extents_test"/>
            <test name="tr1_remove_bounds_test"/>
            <test name="tr1_remove_extent_test"/>
            <toolset name="sunpro-5_3-sunos"/>

            <note author="John Maddock">
                The Type Traits library is broken when used with Sunpro-5.3 and the
                argument to the template is an array or function type.  Most other argument types
                do work as expected: in other words the functionality is limited
                with this compiler, but not so much as to render the library unuseable.
            </note>
        </mark-expected-failures>
       <mark-expected-failures>
          <test name="tr1_decay_test"/>
          <test name="tr1_extent_test"/>
          <test name="tr1_is_abstract_test"/>
          <test name="tr1_is_empty_test"/>
          <test name="tr1_is_function_test"/>
          <test name="tr1_is_member_func_test"/>
          <test name="tr1_is_member_obj_test"/>
          <test name="tr1_is_object_test"/>
          <test name="tr1_is_reference_test"/>
          <test name="tr1_rank_test"/>
          <test name="tr1_tricky_function_type_test"/>
          <test name="tr1_tky_function_type_test"/>
          <test name="test_type_traits"/>
          <test name="std_test_type_traits"/>
          <toolset name="sun-5.8"/>

          <note author="John Maddock">
             The Type Traits library is broken when used with Sunpro-5.8 and the
             argument to the template is a function type.  Most other argument types
             do work as expected: in other words the functionality is limited
             with this compiler, but not so much as to render the library unuseable.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_random"/>
          <test name="std_test_random"/>
          <toolset name="sun-5.8"/>
          <toolset name="sun-5.9"/>

          <note author="John Maddock">
             These failures appear to represent a genuine issue with the
             Boost.Random library that has yet to be addressed.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_tuple_tricky"/>
          <test name="std_test_tuple_tricky"/>
          <toolset name="sun-5.8"/>

          <note author="John Maddock">
             These fail with an internal compiler error: there's no
             workaround as yet.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="tr1_tky_partial_spec_test"/>
          <toolset name="sun-5.9"/>
          <note author="John Maddock">
             This fails with an internal compiler error: there's no
             workaround as yet.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
          <test name="test_boost"/>
          <test name="test_hash"/>
          <test name="test_random"/>
          <test name="test_regex"/>
          <toolset name="msvc-7.1_stlport4"/>

          <note author="John Maddock">
             These failures are completely spurious: they're caused by the tests
             being run with bjam -j2 and the post-processing not coping with the
             resulting output.  These failures should clear if these tests
             are re-run at some point in the future.
          </note>
       </mark-expected-failures>
       <mark-expected-failures>
            <test name="tr1_is_empty_test"/>
            <test name="tr1_is_function_test"/>
            <test name="tr1_is_member_func_test"/>
            <test name="tr1_is_member_obj_test"/>
            <test name="tr1_is_reference_test"/>
            <test name="tr1_tricky_function_type_test"/>
            <test name="tr1_tricky_incomplete_type_test"/>
            <test name="tr1_tricky_incomp_type_test"/>
            <test name="tr1_tricky_is_enum_test"/>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="John Maddock" refid="2"/>
        </mark-expected-failures>
       <mark-expected-failures>
            <test name="tr1_tricky_function_type_test"/>
            <test name="tr1_is_const_test"/>
            <test name="tr1_is_volatile_test"/>
            <test name="tr1_is_convertible_test"/>
            <toolset name="gcc-2*"/>
            <note author="John Maddock" refid="2"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_array"/>
            <test name="std_test_array"/>
            <test name="test_array_tricky"/>
            <test name="std_test_array_tricky"/>
            <test name="test_bind"/>
            <test name="std_test_bind"/>
            <test name="test_complex"/>
            <test name="std_test_complex"/>
            <test name="test_function"/>
            <test name="std_test_function"/>
            <test name="test_random"/>
            <test name="std_test_random"/>
            <test name="test_reference_wrapper"/>
            <test name="std_test_reference_wrapper"/>
            <test name="test_regex"/>
            <test name="std_test_regex"/>
            <test name="test_result_of"/>
            <test name="std_test_result_of"/>
            <test name="test_shared_ptr"/>
            <test name="std_test_shared_ptr"/>
            <test name="test_tuple"/>
            <test name="std_test_tuple"/>
            <toolset name="vc-7"/>
            <note author="John Maddock">
            This library is almost unusable with VC7 due to name lookup issues.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="tr1_aligned_storage_test"/>
            <toolset name="cw-8.3"/>
            <note author="John Maddock">
               Older versions of MWCW incorrectly align pointers to member functions
               (they use 12-byte boundaries, rather than a power-of-2 boundary),
               leading to alignment_of / aligned_storage
               to fail with these types on this compiler.
            </note>
        </mark-expected-failures>
    </library>

    <!-- units -->
    <library name="units">
        <mark-expected-failures>
            <test name="fail_base_dimension"/>
            <toolset name="vacpp"/>
            <note author="Steven Watanabe" refid="16"/>
        </mark-expected-failures>
    </library>

    <!-- unordered -->
    <library name="unordered">
      <mark-expected-failures>
        <test name="unnecessary_copy_tests"/>
        <toolset name="borland-*"/>
        <toolset name="sun-*"/>
        <note author="Daniel James">
            This tests whether inserting elements creates as few copies as I think
            is possible. If this fails it just means that the container might be
            a little inefficient.
        </note>
      </mark-expected-failures>

      <mark-expected-failures>
        <test name="compile_map_unordered_allocator"/>
        <toolset name="msvc-7.1"/>
        <note author="Daniel James">
            This test fail because it's using unordered's internal
            allocator traits, which doesn't work on Visual C++ 7.1.
            It normally uses the one from Boost.Container by default.
        </note>
      </mark-expected-failures>

      <mark-expected-failures>
        <test name="noexcept_tests"/>
        <toolset name="gcc-4.3c+"/>
        <note author="Daniel James">
            boost::is_nothrow_move_constructible and
            boost::is_nothrow_move_assignable don't seem to work on this
            compiler. I'd hope that anyone wanting noexcept support would
            use a more recent compiler anyway.
        </note>
      </mark-expected-failures>
      </library>

    <!-- url -->
    <library name="url">
        <mark-unusable>
            <toolset name="*c++98"/>
            <toolset name="*gnu98"/>
            <toolset name="*c++0x"/>
            <toolset name="*gnu0x"/>
            <toolset name="gcc-4.7*"/>
            <toolset name="gcc-8~c++2a"/>
            <toolset name="clang-linux-3.4*"/>
            <toolset name="clang-linux-3.5*"/>
            <toolset name="clang-linux-3.6*"/>
            <toolset name="clang-linux-3.7*"/>
            <toolset name="msvc-7.1"/>
            <toolset name="msvc-8.*"/>
            <toolset name="msvc-9.*"/>
            <toolset name="msvc-10.*"/>
            <toolset name="msvc-11.*"/>
            <toolset name="msvc-12.*"/>
            <toolset name="msvc-14.0"/>
            <note author="Vinnie Falco">C++11 is the minimum requirement.</note>
        </mark-unusable>
    </library>

    <!-- utility/enable_if -->
    <library name="utility/enable_if">
        <mark-unusable>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="cw-8.3*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <toolset name="gcc-2.95.3-*"/>
            <note refid="3"/>
        </mark-unusable>

        <mark-expected-failures>
          <test name="no_disambiguation"/>
          <toolset name="gcc-3.2.*"/>
          <note refid="3"/>
        </mark-expected-failures>

        <mark-expected-failures>
          <test name="partial_specializations"/>
          <toolset name="borland-5.9*"/>
          <note author="Alisdair Meredith" refid="29"/>
        </mark-expected-failures>
    </library>

    <!-- utility/swap -->
    <library name="utility/swap">
        <mark-expected-failures>
          <test name="array_of_array_of_class"/>
          <test name="array_of_class"/>
          <test name="specialized_in_std"/>
            <toolset name="borland-6.10.0"/>
          <note refid="3" author="Niels Dekker" date="2008-11-11">
            The definition of a custom template specialization of std::swap
            appears to trigger an internal compiler error ("Fatal F1004") on
            CodeGear 6.10.0 (formerly named Borland), as I reported,
            with help from Nicola Musatti and David Dean.
            Related Boost mailing list discussion:
            http://lists.boost.org/Archives/boost/2008/11/144465.php
            CodeGear bug reports on this issue:
            http://qc.codegear.com/wc/qcmain.aspx?d=68959
            http://qc.codegear.com/wc/qcmain.aspx?d=69196
          </note>
        </mark-expected-failures>
        <mark-expected-failures>
          <test name="array_of_array_of_class"/>
          <test name="array_of_array_of_int"/>
            <toolset name="borland-5.9.3"/>
          <note refid="3" author="Niels Dekker" date="2008-12-09">
            Borland 5.9.3 has an error (E2285) when trying to pass a
            multi-dimensional array by reference to a function template.
            A bug report by Christopher Yeleighton appears related:
            "The compiler obligatorily converts member arrays to pointers"
            http://qc.codegear.com/wc/qcmain.aspx?d=10267
          </note>
        </mark-expected-failures>
    </library>

    <!-- utility -->
    <library name="utility">
        <test name="addressof_test">
          <mark-failure>
            <toolset name="sunpro-5_3-sunos"/>
            <note author="D. Gregor" refid="3"/>
          </mark-failure>
        </test>
        <test name="fun_out_iter_example">
            <mark-failure>
                <toolset name="como-win32"/>
                <note author="B. Dawes" refid="4"/>
            </mark-failure>
        </test>
        <test name="numeric_traits_test">
            <mark-failure>
                <toolset name="borland-5.6*"/>
                <toolset name="borland-5.8*"/>
                <toolset name="borland-5.9*"/>
                <note author="A.Meredith">
                  Compiler has a problem with BOOST_STATIC_CONSTANT in nested templates
                  inside class template specializations.
                </note>
            </mark-failure>
        </test>
        <test name="result_of_test">
            <mark-failure>
                <toolset name="borland-5*"/>
                <toolset name="cw-8.3*"/>
                <toolset name="msvc-6.5*"/>
                <toolset name="msvc-7.0"/>
                <toolset name="gcc-2.95.3*"/>
                <toolset name="sunpro-5_3-sunos"/>
                <note refid="3" author="D. Gregor"/>
            </mark-failure>
        </test>
        <test name="value_init_test">
          <mark-failure>
            <toolset name="intel-9.0*"/>
            <toolset name="intel-linux-9.0*"/>
            <note author="Niels Dekker" date="2009-07-01">
            When I made the conversion from value_initialized&lt;T&gt; to T&amp;
            const-correct, this specific compiler version gave compile errors.
            See also: Ticket #2548 - "Let's fix the logical constness of value_initialized!"
            </note>
          </mark-failure>
        </test>
        <mark-expected-failures>
            <test name="value_init_test"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Aleksey Gurtovoy">
                This failure is caused by a compiler bug (default-constructed scalar
                types are not zero-initialized) that has been fixed in the latest
                versions of the compiler (VC 7.1 and greater).
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="operators_test"/>
            <toolset name="gcc-3.4.5_linux_x86_64"/>
            <note author="Vladimir Prus">
                The test takes more that 30 minutes to compile and the
                compilation is automatically killed. It is likely caused
                by the compiler bug, but it unknown how much this
                bug affects regular use of the operators library. Is it
                also unknown if the test can be refactored so that
                not to trigger this bug.
            </note>
        </mark-expected-failures>
    </library>

    <!-- uuid -->
    <library name="uuid">
        <mark-expected-failures>
            <test name="test_serialization"/>
            <toolset name="cuda-2.2"/>
            <toolset name="borland-cb2009"/>
            <toolset name="borland-cb2010"/>
            <toolset name="borland-5.9.3"/>
            <toolset name="borland-6.1.3"/>
            <toolset name="borland-6.2.1"/>
            <note author="Andy Tompkins">
                The test relies on Boost.Serialization which is not
                supported on this toolset.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_random_generator"/>
            <test name="test_tagging"/>
            <test name="test_uuid_class"/>
            <toolset name="borland-cb2009"/>
            <toolset name="borland-cb2010"/>
            <toolset name="borland-5.9.3"/>
            <toolset name="borland-6.1.3"/>
            <toolset name="borland-6.2.1"/>
            <note author="Andy Tompkins">
                The test relies on Boost.Random which is not supported
                on this toolset.
            </note>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="test_uuid"/>
            <toolset name="borland-cb2009"/>
            <toolset name="borland-cb2010"/>
            <toolset name="borland-5.9.3"/>
            <toolset name="borland-6.1.3"/>
            <toolset name="borland-6.2.1"/>
            <note author="Andy Tompkins" refid="28"/>
        </mark-expected-failures>
        <mark-expected-failures>
            <test name="compile_random_generator"/>
            <test name="compile_uuid_generators"/>
            <test name="test_include1"/>
            <toolset name="borland-6.2.1"/>
            <note author="Andy Tompkins">
                 The test relies on Boost.Iterator (iterator_facade)
                 which is not supported on this toolset.
           </note>
        </mark-expected-failures>
    </library>

    <!-- variant -->
    <library name="variant">
        <mark-unusable>
            <toolset name="mipspro"/>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="hp_cxx-65*"/>
            <note refid="2"/>
        </mark-unusable>
        <test name="recursive_variant_test">
            <mark-failure>
                <toolset name="como-win32"/>
                <toolset name="msvc-6.5*"/>
                <toolset name="msvc-7.0"/>
                <note refid="3"/>
            </mark-failure>
        </test>
        <mark-expected-failures>
            <test name="recursive_variant_test"/>
            <test name="variant_test1"/>
            <test name="variant_test5"/>
            <test name="variant_visit_test"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="borland-5.9*"/>
            <note author="Aleksey Gurtovoy" refid="3"/>
        </mark-expected-failures>
        <test name="variant_reference_test">
            <mark-failure>
                <toolset name="cw-8.3*"/>
                <toolset name="msvc-6.5*"/>
                <toolset name="msvc-7.0"/>
                <note refid="3"/>
            </mark-failure>
            <mark-failure>
                <toolset name="iw-7_1*"/>
                <toolset name="intel-7.1*"/>
                <note refid="2"/>
            </mark-failure>
        </test>
    </library>

    <!-- wave -->
    <library name="wave">
        <mark-unusable>
            <toolset name="msvc-6.5*"/>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="borland-5.5*"/>
            <toolset name="borland-5.6*"/>
            <toolset name="borland-5.8*"/>
            <toolset name="gcc-2.95.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.5.3-linux"/>
            <toolset name="gcc-2.95.3-stlport-4.6.2-linux"/>
            <toolset name="hp_cxx-65*"/>
            <note refid="29"/>
        </mark-unusable>
       <mark-unusable>
            <toolset name="msvc-7.0"/>
            <note>
               This toolset isn't supported because of the used Spirit V1.8.x, which in turn is
               not usable with this toolset.
            </note>
        </mark-unusable>

       <mark-unusable>
            <toolset name="borland-5.9*"/>
            <note author="Alisdair Meredith">
               This toolset isn't supported because of the used multi_index library, which in turn is
               not usable with this toolset.
            </note>
        </mark-unusable>

        <mark-expected-failures>
            <test name="testwave"/>
            <!-- toolset name="cw-9_5-darwin"/ -->
            <toolset name="cw-8*"/>
            <note author="Rene Rivera" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testwave"/>
            <toolset name="gcc-3.2.3-linux"/>
            <toolset name="gcc-3.2.3_linux"/>
            <toolset name="gcc-3.3.6-linux"/>
            <toolset name="gcc-3.3.6"/>
            <note author="Hartmut Kaiser" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testwave"/>
            <!-- <toolset name="qcc-3.3.5_gpp"/> -->
            <toolset name="qcc-3.3.5*gpp"/>
            <note author="Hartmut Kaiser" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testwave_dll"/>
            <toolset name="mingw-3*"/>
            <note author="Hartmut Kaiser" refid="29"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="testwave_dll"/>
            <toolset name="cw-9.4"/>
            <note author="Hartmut Kaiser" refid="2"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_slex_lexer"/>
            <toolset name="hp_cxx-65*"/>
            <note author="Hartmut Kaiser" refid="2"/>
        </mark-expected-failures>

    </library>

    <!-- xpressive -->
    <library name="xpressive">

        <mark-unusable>
            <toolset name="gcc-2.95.3*"/>
            <toolset name="msvc-6.5*"/>
            <toolset name="msvc-7.0"/>
            <note author="Eric Niebler">
                These compilers do not support class template partial
                specialization.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="borland-*"/>
            <note author="Eric Niebler">
                Boost.Proto doesn't work on this compiler.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="cw-8.3"/>
            <note author="Eric Niebler">
                This compiler doesn't support SFINAE / enable_if
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="dmc*"/>
            <note author="Eric Niebler">
                Digital Mars cannot seem to handle dependent default template parameters,
                such as "template &lt; class T, bool B = is_foo &lt; T &gt; ::value &gt;"
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="sunpro-5_3-sunos"/>
            <toolset name="sun-5.7"/>
            <toolset name="sun-5.8"/>
            <toolset name="sun-5.9"/>
            <toolset name="sun-5.10"/>
            <note refid="17"/>
        </mark-unusable>
        <mark-unusable>
            <toolset name="cray-8.0"/>
            <note author="Eric Niebler">
                Boost.Proto doesn't work on the cray compiler.
            </note>
        </mark-unusable>
        <mark-unusable>
            <toolset name="vacpp*"/>
            <note author="Eric Niebler">
                Boost.Proto doesn't work on the vacpp compiler.
            </note>
        </mark-unusable>

        <mark-expected-failures>
            <test name="test_actions"/>
            <toolset name="acc"/>
            <note author="Eric Niebler" refid="43"/>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_symbols"/>
            <toolset name="acc"/>
            <note author="Eric Niebler" refid="43"/>
        </mark-expected-failures>

    </library>

    <library name="locale">
        <mark-unusable>
            <toolset name="sun*" />
            <note author="Artyom Beilis">
                This library is unusable due to bug #5373 in Boost.Thread
                on this compiler.
            </note>
        </mark-unusable>

        <mark-unusable>
            <toolset name="msvc-9.0~wm5~stlport5.2" />
            <note author="Artyom Beilis">
                Windows Mobile lacks essential features of the standard C
                library like gmtime, mktime, localtime making it impossible
                to use Boost.Locale on this platform.
            </note>
        </mark-unusable>

        <mark-expected-failures>
            <test name="test_date_time" />
            <toolset name="gcc-mingw-4.4.0"/>
            <note author="Artyom Beilis">
                Compiler does not support shared runtime linking
                thus is makes it problematic to use Boost.Locale as dll
            </note>
        </mark-expected-failures>

        <mark-expected-failures>
            <test name="test_codepage"/>
            <toolset name="pathscale-4*"/>
            <note author="Artyom Beilis">
                Problems with wide file stream I/O. 
                Currently unresolved due to lack of access to the 
                compiler.
            </note>
        </mark-expected-failures>
    </library>


    <!-- /////////////// Standard note definitions /////////////// -->

    <note id="0">
        This test fails only intermittently.
    </note>

    <note id="1">
        The failure is caused by a problem in Boost code. The Boost developers are aware of
        the problem and plan to fix it.
    </note>

    <note id="2">
        The failure is caused by a compiler bug.
    </note>

    <note id="3">
        The failure is caused by a compiler bug, which has been reported to the compiler
        supplier (or is already known to them).
    </note>

    <note id="4">
        The failure is caused by a standard library bug.
    </note>

    <note id="5">
        The failure is caused by a standard library bug, which has been reported to the
        standard library supplier (or is already known to them).
    </note>

    <note id="6">
        The failure is probably caused by the test code, harness, or configuration. Thus,
        it may not affect users of the library.
    </note>

    <note id="9">
        The failure is serious and likely to prevent all use of this Boost library with this compiler.
    </note>

    <note id="10">
        The failure is serious and likely to prevent all use of this Boost library with this
        compiler. The failure is caused by a compiler bug, which has been reported to the compiler
        supplier (or is already known to them).
    </note>

    <note id="14">
        The failure is caused by a platform API bug.
    </note>

    <note id="15">
        The failure is caused by a platform API bug, which has been reported to the platform API
        supplier (or is already known to them).
    </note>

    <note id="16">
        The failure is not serious and will not affect most users. The library degrades gracefully.
    </note>

    <note id="17">
        This compiler's bugs are not supported by the library.
    </note>

    <note id="18">
      Locales missing or adequately supported by this compiler.
    </note>

    <note id="19">
      Missing or inadequate wchar/wstring/wstream support for this compiler.
    </note>

    <note id="20">
      No std iterator traits for this compiler.
    </note>

    <note id="21">
      Library has limited input/output support due to compiler inadequacies.
    </note>

    <note id="22">
      No high precision clock for this platform.
    </note>

    <note id="23">
      A bug in standard library prevents passing std::set from DLL to
      application. A fixed &lt;tree&gt; header is available from
      http://www.dinkumware.com/vc_fixes.html.
    </note>

    <note id="24">
      Although the documentation from the Comeau website would make it appear
      that windows DLL's are supported using the --windows option,  after some
      experimentation we have been unsuccessful in making dll configurations
      work correctly.
    </note>

    <note id="25">
      The failure is caused by a runtime limitation. Locale support is only
      available with the static linked variant of the runtime. Generally
      the dynamic linked variant is required when building dynamic modules,
      DLL, <code>so</code>, etc.
    </note>

    <note id="26">
        This failure is caused by a compiler bug with no known workaround.
        Patches are welcome!
    </note>

    <note id="27" >
        This failure is caused by bugs in the standard library implementation and/or
        bugs in the compiler.
    </note>

    <note id="28">
        Unresearched failure -- please contact library developers for more
        information about possible causes.
    </note>

    <note id="29">
        The test fails due to unresearched issues. The library
        developers are aware of this failure, but need help with
        investigating/addressing it for future releases.
    </note>

    <note id="30">
        The support for this deficient compiler will be dropped starting
        from Boost 1.33.0 release. Please use one of the previous Boost
        releases if you need the library to work on this compiler.
    </note>

    <note id="31">
        This failure is caused by compiler bugs or limitations. Some advanced
        or esoteric library features may be unavailable or only partially available.
        This does not impact most common uses of the library.
    </note>

    <note id="32">
        This failure is caused by a compiler bug. Certain code constructs that should
        fail compilation are accepted by the compiler. This can mask some programming
        errors, but does not impact the usability of the library.
    </note>

    <note id="33">
        The failures are caused by the wrong handling of the
        <code>std::internal</code> flag in the iostreams implementation of the
        standard library used on that compiler/platform combo. Apart from that,
        the format library works as expected.
    </note>

    <note id="34">
        The failures are caused by the fact that the iword and pword arrays seem
        to share the same memory area in the iostreams implementation of the
        standard library used on that compiler/platform combo. As long as you
        stay clear of iword and pword, the library should work ok.
    </note>

    <note id="35">
        This failure occurs only when using shared libraries for this
        compiler and platform, although the same programs should work
        properly when using static libraries. This problem has not
        been researched.
    </note>

    <note id="36">
        Wide character support is disabled in the GNU Standard C++ library as
        supplied on the QNX Neutrino version 6.3.0 distribution.
    </note>

    <note id="37">
        This problem is due to the non-conforming STLport
        implementation of vector's swap: it can be easily
        reproduced with the following code snippet:

            typedef std::vector&lt;int&gt; vector_type;
            typedef vector_type::reference reference_type;

            vector_type v1(4u, 1);
            vector_type v2(7u, 0);

            reference_type ref = v1[2];
            int x = ref;

            std::swap(v1, v2);
            BOOST_CHECK(v2[2] == x); // ok
            v2[2] = 1 - v2[2];
            BOOST_CHECK(ref != x);   // oops
    </note>

    <note id="38">
        When compiling this test, aCC6 runs out of memory. The HP
        compiler group is aware of this issue and is working on the fix.
    </note>

    <note id="39">
        This test assumes native typeof support.
    </note>

    <note id="40">
        This test assumes compiler support for rvalue references.
    </note>

    <note id="41">
        These tests rely on the ability of an std::deque container to be
        constructed off two input iterators. Unfortunately, the Rogue Wave
        library version 2.2 and higher assumes iterator which has + and -
        operators which only random access iterator is required to provide.
    </note>

    <note id="42">
        Internal compiler error: GCC Bugzilla Bug 33580.
        This is a regression in the gcc 4.2 series.
    </note>

    <note id="43">
        These test failures are reported to be
        under investigation at HP's compiler lab.
    </note>

    <note id="44">
        This compiler does not support gcc stdcall function attribute.
    </note>

    <note id="45">
        The Rogue Wave standard library version used by this compiler provides
        a faulty vector&lt;bool&gt; iterator, which is not symmetric. There is an
        associated bug report in the Rogue Wave bug tracking system for this
        problem.
    </note>

    <note id="46">
        The test does not compile, most likely because of new version of EDG Front End
        implementing Core Issue 574. In the HP bug tracking system, it is tracked as
        QuIX ID: QXCR1000804484.
    </note>

    <note id="47">
        Depending on system load, the compile time may exceed specified timeout value.
        The test passes when the timeout value is increased.
    </note>

    <note id="48">
        This test fails when BOOST_UBLAS_NO_NESTED_CLASS_RELATION is defined.
    </note>

    <note id="49">
        This test fails because MinGW apparently does not always catch exceptions properly.
    </note>

    <note id="51">
        This test requires variadic macro support.
    </note>
    
    <note id="52">
        This test requires lambda function support.
    </note>

    <note id="53">
        This test has not been updated to accomodate changes in Boost.Random.
    </note>

</explicit-failures-markup>

