// Copyright (c) 2016-2023 <PERSON>
// Copyright (c) 2023 <PERSON>
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////// THIS HEADER IS AUTO GENERATED BY misc/generate_cpp17.py                                    ////////////////
//////////////// MODIFY AND RUN THE misc/generate_cpp17.py INSTEAD OF DIRECTLY MODIFYING THE GENERATED FILE ////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

#ifndef BOOST_PFR_DETAIL_CORE17_GENERATED_HPP
#define BOOST_PFR_DETAIL_CORE17_GENERATED_HPP
#pragma once

#include <boost/pfr/detail/config.hpp>
#if !BOOST_PFR_USE_CPP17
#   error C++17 is required for this header.
#endif

#include <boost/pfr/detail/sequence_tuple.hpp>
#include <boost/pfr/detail/size_t_.hpp>
#include <type_traits> // for std::conditional_t, std::is_reference

namespace boost { namespace pfr { namespace detail {

template <class... Args>
constexpr auto make_tuple_of_references(Args&&... args) noexcept {
  return sequence_tuple::tuple<Args&...>{ args... };
}

template<typename T, typename Arg>
constexpr decltype(auto) add_cv_like(Arg& arg) noexcept {
    if constexpr (std::is_const<T>::value && std::is_volatile<T>::value) {
        return const_cast<const volatile Arg&>(arg);
    }
    else if constexpr (std::is_const<T>::value) {
        return const_cast<const Arg&>(arg);
    }
    else if constexpr (std::is_volatile<T>::value) {
        return const_cast<volatile Arg&>(arg);
    }
    else {
        return const_cast<Arg&>(arg);
    }
}

// https://gcc.gnu.org/bugzilla/show_bug.cgi?id=78939
template<typename T, typename Sb, typename Arg>
constexpr decltype(auto) workaround_cast(Arg& arg) noexcept {
    using output_arg_t = std::conditional_t<!std::is_reference<Sb>(), decltype(detail::add_cv_like<T>(arg)), Sb>;
    return const_cast<output_arg_t>(arg);
}

template <class T>
constexpr auto tie_as_tuple(T& /*val*/, size_t_<0>) noexcept {
  return sequence_tuple::tuple<>{};
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<1>, std::enable_if_t<std::is_class< std::remove_cv_t<T> >::value>* = nullptr) noexcept {
  auto& [a] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.
  return ::boost::pfr::detail::make_tuple_of_references(detail::workaround_cast<T, decltype(a)>(a));
}


template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<1>, std::enable_if_t<!std::is_class< std::remove_cv_t<T> >::value>* = nullptr) noexcept {
  return ::boost::pfr::detail::make_tuple_of_references( val );
}


template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<2>) noexcept {
  auto& [a,b] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.
  return ::boost::pfr::detail::make_tuple_of_references(detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b));
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<3>) noexcept {
  auto& [a,b,c] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<4>) noexcept {
  auto& [a,b,c,d] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<5>) noexcept {
  auto& [a,b,c,d,e] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<6>) noexcept {
  auto& [a,b,c,d,e,f] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<7>) noexcept {
  auto& [a,b,c,d,e,f,g] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<8>) noexcept {
  auto& [a,b,c,d,e,f,g,h] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<9>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<10>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<11>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<12>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<13>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<14>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<15>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<16>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<17>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<18>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<19>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<20>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<21>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<22>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<23>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<24>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<25>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<26>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<27>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<28>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<29>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<30>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<31>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<32>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<33>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<34>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<35>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<36>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<37>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<38>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<39>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<40>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<41>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<42>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<43>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<44>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<45>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<46>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<47>) noexcept {
  auto& [a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<48>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<49>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<50>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<51>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<52>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<53>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<54>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<55>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<56>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<57>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<58>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<59>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<60>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<61>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<62>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<63>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<64>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<65>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<66>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<67>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<68>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<69>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<70>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<71>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<72>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<73>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<74>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<75>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<76>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<77>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<78>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<79>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<80>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<81>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<82>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<83>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<84>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<85>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<86>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<87>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<88>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<89>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<90>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<91>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<92>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW,aX
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW),detail::workaround_cast<T, decltype(aX)>(aX)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<93>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW,aX,aY
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW),detail::workaround_cast<T, decltype(aX)>(aX),detail::workaround_cast<T, decltype(aY)>(aY)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<94>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW,aX,aY,aZ
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW),detail::workaround_cast<T, decltype(aX)>(aX),detail::workaround_cast<T, decltype(aY)>(aY),
    detail::workaround_cast<T, decltype(aZ)>(aZ)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<95>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW,aX,aY,aZ,
    ba
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW),detail::workaround_cast<T, decltype(aX)>(aX),detail::workaround_cast<T, decltype(aY)>(aY),
    detail::workaround_cast<T, decltype(aZ)>(aZ),detail::workaround_cast<T, decltype(ba)>(ba)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<96>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW,aX,aY,aZ,
    ba,bb
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW),detail::workaround_cast<T, decltype(aX)>(aX),detail::workaround_cast<T, decltype(aY)>(aY),
    detail::workaround_cast<T, decltype(aZ)>(aZ),detail::workaround_cast<T, decltype(ba)>(ba),detail::workaround_cast<T, decltype(bb)>(bb)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<97>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW,aX,aY,aZ,
    ba,bb,bc
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW),detail::workaround_cast<T, decltype(aX)>(aX),detail::workaround_cast<T, decltype(aY)>(aY),
    detail::workaround_cast<T, decltype(aZ)>(aZ),detail::workaround_cast<T, decltype(ba)>(ba),detail::workaround_cast<T, decltype(bb)>(bb),
    detail::workaround_cast<T, decltype(bc)>(bc)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<98>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW,aX,aY,aZ,
    ba,bb,bc,bd
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW),detail::workaround_cast<T, decltype(aX)>(aX),detail::workaround_cast<T, decltype(aY)>(aY),
    detail::workaround_cast<T, decltype(aZ)>(aZ),detail::workaround_cast<T, decltype(ba)>(ba),detail::workaround_cast<T, decltype(bb)>(bb),
    detail::workaround_cast<T, decltype(bc)>(bc),detail::workaround_cast<T, decltype(bd)>(bd)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<99>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW,aX,aY,aZ,
    ba,bb,bc,bd,be
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW),detail::workaround_cast<T, decltype(aX)>(aX),detail::workaround_cast<T, decltype(aY)>(aY),
    detail::workaround_cast<T, decltype(aZ)>(aZ),detail::workaround_cast<T, decltype(ba)>(ba),detail::workaround_cast<T, decltype(bb)>(bb),
    detail::workaround_cast<T, decltype(bc)>(bc),detail::workaround_cast<T, decltype(bd)>(bd),detail::workaround_cast<T, decltype(be)>(be)
  );
}

template <class T>
constexpr auto tie_as_tuple(T& val, size_t_<100>) noexcept {
  auto& [
    a,b,c,d,e,f,g,h,j,k,l,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,S,U,V,W,X,Y,Z,
    aa,ab,ac,ad,ae,af,ag,ah,aj,ak,al,am,an,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD,aE,aF,aG,aH,aJ,aK,aL,aM,aN,aP,aQ,aR,aS,aU,aV,aW,aX,aY,aZ,
    ba,bb,bc,bd,be,bf
  ] = const_cast<std::remove_cv_t<T>&>(val); // ====================> Boost.PFR: User-provided type is not a SimpleAggregate.

  return ::boost::pfr::detail::make_tuple_of_references(
    detail::workaround_cast<T, decltype(a)>(a),detail::workaround_cast<T, decltype(b)>(b),detail::workaround_cast<T, decltype(c)>(c),
    detail::workaround_cast<T, decltype(d)>(d),detail::workaround_cast<T, decltype(e)>(e),detail::workaround_cast<T, decltype(f)>(f),
    detail::workaround_cast<T, decltype(g)>(g),detail::workaround_cast<T, decltype(h)>(h),detail::workaround_cast<T, decltype(j)>(j),
    detail::workaround_cast<T, decltype(k)>(k),detail::workaround_cast<T, decltype(l)>(l),detail::workaround_cast<T, decltype(m)>(m),
    detail::workaround_cast<T, decltype(n)>(n),detail::workaround_cast<T, decltype(p)>(p),detail::workaround_cast<T, decltype(q)>(q),
    detail::workaround_cast<T, decltype(r)>(r),detail::workaround_cast<T, decltype(s)>(s),detail::workaround_cast<T, decltype(t)>(t),
    detail::workaround_cast<T, decltype(u)>(u),detail::workaround_cast<T, decltype(v)>(v),detail::workaround_cast<T, decltype(w)>(w),
    detail::workaround_cast<T, decltype(x)>(x),detail::workaround_cast<T, decltype(y)>(y),detail::workaround_cast<T, decltype(z)>(z),
    detail::workaround_cast<T, decltype(A)>(A),detail::workaround_cast<T, decltype(B)>(B),detail::workaround_cast<T, decltype(C)>(C),
    detail::workaround_cast<T, decltype(D)>(D),detail::workaround_cast<T, decltype(E)>(E),detail::workaround_cast<T, decltype(F)>(F),
    detail::workaround_cast<T, decltype(G)>(G),detail::workaround_cast<T, decltype(H)>(H),detail::workaround_cast<T, decltype(J)>(J),
    detail::workaround_cast<T, decltype(K)>(K),detail::workaround_cast<T, decltype(L)>(L),detail::workaround_cast<T, decltype(M)>(M),
    detail::workaround_cast<T, decltype(N)>(N),detail::workaround_cast<T, decltype(P)>(P),detail::workaround_cast<T, decltype(Q)>(Q),
    detail::workaround_cast<T, decltype(R)>(R),detail::workaround_cast<T, decltype(S)>(S),detail::workaround_cast<T, decltype(U)>(U),
    detail::workaround_cast<T, decltype(V)>(V),detail::workaround_cast<T, decltype(W)>(W),detail::workaround_cast<T, decltype(X)>(X),
    detail::workaround_cast<T, decltype(Y)>(Y),detail::workaround_cast<T, decltype(Z)>(Z),detail::workaround_cast<T, decltype(aa)>(aa),
    detail::workaround_cast<T, decltype(ab)>(ab),detail::workaround_cast<T, decltype(ac)>(ac),detail::workaround_cast<T, decltype(ad)>(ad),
    detail::workaround_cast<T, decltype(ae)>(ae),detail::workaround_cast<T, decltype(af)>(af),detail::workaround_cast<T, decltype(ag)>(ag),
    detail::workaround_cast<T, decltype(ah)>(ah),detail::workaround_cast<T, decltype(aj)>(aj),detail::workaround_cast<T, decltype(ak)>(ak),
    detail::workaround_cast<T, decltype(al)>(al),detail::workaround_cast<T, decltype(am)>(am),detail::workaround_cast<T, decltype(an)>(an),
    detail::workaround_cast<T, decltype(ap)>(ap),detail::workaround_cast<T, decltype(aq)>(aq),detail::workaround_cast<T, decltype(ar)>(ar),
    detail::workaround_cast<T, decltype(as)>(as),detail::workaround_cast<T, decltype(at)>(at),detail::workaround_cast<T, decltype(au)>(au),
    detail::workaround_cast<T, decltype(av)>(av),detail::workaround_cast<T, decltype(aw)>(aw),detail::workaround_cast<T, decltype(ax)>(ax),
    detail::workaround_cast<T, decltype(ay)>(ay),detail::workaround_cast<T, decltype(az)>(az),detail::workaround_cast<T, decltype(aA)>(aA),
    detail::workaround_cast<T, decltype(aB)>(aB),detail::workaround_cast<T, decltype(aC)>(aC),detail::workaround_cast<T, decltype(aD)>(aD),
    detail::workaround_cast<T, decltype(aE)>(aE),detail::workaround_cast<T, decltype(aF)>(aF),detail::workaround_cast<T, decltype(aG)>(aG),
    detail::workaround_cast<T, decltype(aH)>(aH),detail::workaround_cast<T, decltype(aJ)>(aJ),detail::workaround_cast<T, decltype(aK)>(aK),
    detail::workaround_cast<T, decltype(aL)>(aL),detail::workaround_cast<T, decltype(aM)>(aM),detail::workaround_cast<T, decltype(aN)>(aN),
    detail::workaround_cast<T, decltype(aP)>(aP),detail::workaround_cast<T, decltype(aQ)>(aQ),detail::workaround_cast<T, decltype(aR)>(aR),
    detail::workaround_cast<T, decltype(aS)>(aS),detail::workaround_cast<T, decltype(aU)>(aU),detail::workaround_cast<T, decltype(aV)>(aV),
    detail::workaround_cast<T, decltype(aW)>(aW),detail::workaround_cast<T, decltype(aX)>(aX),detail::workaround_cast<T, decltype(aY)>(aY),
    detail::workaround_cast<T, decltype(aZ)>(aZ),detail::workaround_cast<T, decltype(ba)>(ba),detail::workaround_cast<T, decltype(bb)>(bb),
    detail::workaround_cast<T, decltype(bc)>(bc),detail::workaround_cast<T, decltype(bd)>(bd),detail::workaround_cast<T, decltype(be)>(be),
    detail::workaround_cast<T, decltype(bf)>(bf)
  );
}


template <class T, std::size_t I>
constexpr void tie_as_tuple(T& /*val*/, size_t_<I>) noexcept {
  static_assert(sizeof(T) && false,
                "====================> Boost.PFR: Too many fields in a structure T. Regenerate include/boost/pfr/detail/core17_generated.hpp file for appropriate count of fields. For example: `python ./misc/generate_cpp17.py 300 > include/boost/pfr/detail/core17_generated.hpp`");
}

}}} // namespace boost::pfr::detail

#endif // BOOST_PFR_DETAIL_CORE17_GENERATED_HPP

