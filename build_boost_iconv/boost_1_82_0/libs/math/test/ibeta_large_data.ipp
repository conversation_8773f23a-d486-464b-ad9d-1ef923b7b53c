//  (C) Copyright John <PERSON> 2006.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

   static const std::array<std::array<typename table_type<T>::type, 7>, 1210> ibeta_large_data = { {
      {{ SC_(0.104760829344741068780422210693359375e-4), SC_(39078.1875), SC_(0.913384497165679931640625), SC_(95444.37547576888548779405522478045372688), SC_(BOOST_MATH_SMALL_CONSTANT(0.4076397251031275963346153642645211144346e-41521)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4270966445860582673748568606264586002504e-41526)) }}, 
      {{ SC_(0.1127331415773369371891021728515625e-4), SC_(0.0226620174944400787353515625), SC_(0.1355634629726409912109375), SC_(88703.20318198098901713372585734808194292), SC_(45.9460483635769831377505786833842050448), SC_(0.9994822930837981780736860587426162687504), SC_(0.0005177069162018219263139412573837312496096) }}, 
      {{ SC_(0.113778432933031581342220306396484375e-4), SC_(0.03654421865940093994140625), SC_(0.9688708782196044921875), SC_(87893.29210911967129223056449206866305889), SC_(24.13233514927218107101077084629346084654), SC_(0.999725511349976252733137998478545702411), SC_(0.0002744886500237472668620015214542975889712) }}, 
      {{ SC_(0.1142846667789854109287261962890625e-4), SC_(0.00244517601095139980316162109375), SC_(0.1355634629726409912109375), SC_(87498.94901523223439182946063290895919618), SC_(410.8174698800966982396187764410683048418), SC_(0.9953268278792474174353255397883295739773), SC_(0.004673172120752582564674460211670426022665) }}, 
      {{ SC_(0.1184685606858693063259124755859375e-4), SC_(0.015964560210704803466796875), SC_(0.3082362115383148193359375), SC_(84409.76586511709213323237371074024560834), SC_(63.42758275377907514602214367744158071269), SC_(0.9992491395179357013663738064942314292026), SC_(0.000750860482064298633626193505768570797446) }}, 
      {{ SC_(0.1215800511999987065792083740234375e-4), SC_(24110.49609375), SC_(0.1355634629726409912109375), SC_(82239.66859351932879196086451784571067159), SC_(0.1228963536503215267489197519054078145969e-1528), SC_(1.0), SC_(0.1494368298804234679967895435418141236754e-1533) }}, 
      {{ SC_(0.130341722979210317134857177734375e-4), SC_(26168.341796875), SC_(0.12707412242889404296875), SC_(76710.65536325700740870385917451316226344), SC_(0.8987481009726700678494101665597031705645e-1548), SC_(1.0), SC_(0.1171607903382812021950729983554623721701e-1552) }}, 
      {{ SC_(0.13885271982871927320957183837890625e-4), SC_(0.04976274073123931884765625), SC_(0.632396042346954345703125), SC_(72019.23463150248088958543324182892626471), SC_(19.53662131349699149968802706905083676178), SC_(0.9997288040735046086759949196286322002492), SC_(0.0002711959264953913240050803713677997508421) }}, 
      {{ SC_(0.139016165121574886143207550048828125e-4), SC_(0.3188882328686304390430450439453125e-4), SC_(0.81474220752716064453125), SC_(71935.56143195648321209894597982190925135), SC_(31357.46843093207518473308985368320533006), SC_(0.6964222225589077802406713530999497187926), SC_(0.3035777774410922197593286469000502812074) }}, 
      {{ SC_(0.14759907571715302765369415283203125e-4), SC_(2.8241312503814697265625), SC_(0.632396042346954345703125), SC_(67749.64468000029269520566988398084664291), SC_(0.02906706447901268252429885336709779665611), SC_(0.9999995709637719063997441118286889404404), SC_(0.4290362280936002558881713110595595793096e-6) }}, 
      {{ SC_(0.150794721776037476956844329833984375e-4), SC_(0.4875471131526865065097808837890625e-4), SC_(0.221111953258514404296875), SC_(66314.05927892349341973416366818795267336), SC_(20512.09739510432080805623001488366337009), SC_(0.763756704421306309320206420222803196485), SC_(0.236243295578693690679793579777196803515) }}, 
      {{ SC_(0.1519624856882728636264801025390625e-4), SC_(16177.537109375), SC_(0.81474220752716064453125), SC_(65795.44709267135893551075697872372424105), SC_(BOOST_MATH_SMALL_CONSTANT(0.2027537374480409874707652773038782413935e-11849)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3081577014933374612265415972232266786926e-11854)) }}, 
      {{ SC_(0.1554497794131748378276824951171875e-4), SC_(40.46924591064453125), SC_(0.905801355838775634765625), SC_(64325.19244254056356455961240584532088293), SC_(0.822463871000651749828830546797984530393e-43), SC_(1.0), SC_(0.1278603047686689558334345413308146407234e-47) }}, 
      {{ SC_(0.15675454051233828067779541015625e-4), SC_(0.000101913392427377402782440185546875), SC_(0.81474220752716064453125), SC_(63795.48621858577102490380365796969496633), SC_(9810.772055381842015487811747997830665087), SC_(0.8667128001689005026050394523916069667826), SC_(0.1332871998310994973949605476083930332174) }}, 
      {{ SC_(0.15971760149113833904266357421875e-4), SC_(19.206241607666015625), SC_(0.913384497165679931640625), SC_(62607.00088023805345936256566188949995708), SC_(0.2234001688321881800710378923972526401497e-21), SC_(0.9999999999999999999999999964317062678097), SC_(0.3568293732190334165134476285535997648472e-26) }}, 
      {{ SC_(0.16304524251609109342098236083984375e-4), SC_(0.00039033559733070433139801025390625), SC_(0.12707412242889404296875), SC_(61330.74255992408673684618143646855164824), SC_(2563.824473817131717490401647850652347432), SC_(0.9598741396516039259205408266092071862094), SC_(0.04012586034839607407945917339079281379055) }}, 
      {{ SC_(0.16487292668898589909076690673828125e-4), SC_(470997.15625), SC_(0.12707412242889404296875), SC_(60639.13353449960017258664254772876309268), SC_(BOOST_MATH_SMALL_CONSTANT(0.5384739990831595984009356761636892828401e-27804)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.887997515295636614071071990663640203201e-27809)) }}, 
      {{ SC_(0.165044693858362734317779541015625e-4), SC_(3.57584381103515625), SC_(0.3082362115383148193359375), SC_(60587.77026541341888225085862457381274413), SC_(0.1732225036748861524733225741802535443988), SC_(0.9999971409740337294746579825837771947623), SC_(0.2859025966270525342017416222805237665672e-5) }}, 
      {{ SC_(0.166259487741626799106597900390625e-4), SC_(147818.875), SC_(0.632396042346954345703125), SC_(60134.46375982525806101037989862809783137), SC_(BOOST_MATH_SMALL_CONSTANT(0.1037988440880944042360986327562736779854e-64249)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1726112408728927999761612396367365655052e-64254)) }}, 
      {{ SC_(0.16847467122715897858142852783203125e-4), SC_(0.002207652665674686431884765625), SC_(0.9688708782196044921875), SC_(59359.52475707738611546412137293027748567), SC_(449.5447623669572666846242510916839845304), SC_(0.9924836690157701392419526809332561139692), SC_(0.007516330984229860758047319066743886030786) }}, 
      {{ SC_(0.1747490387060679495334625244140625e-4), SC_(0.26349246501922607421875), SC_(0.632396042346954345703125), SC_(57225.14946260528992955268005621257633375), SC_(3.201269200695144242963625771809339374153), SC_(0.9999440614807213732173180138063205712752), SC_(0.5593851927862678268198619367942872477437e-4) }}, 
      {{ SC_(0.17863809262053109705448150634765625e-4), SC_(439.38714599609375), SC_(0.8350250720977783203125), SC_(55972.44090864893014330138933206516204433), SC_(0.3791074904984294581987640686637822179263e-346), SC_(1.0), SC_(0.6773109843774015299501716456463481210658e-351) }}, 
      {{ SC_(0.1895247623906470835208892822265625e-4), SC_(1.07109558582305908203125), SC_(0.9688708782196044921875), SC_(52763.41949943324228341686952963434540238), SC_(0.02308304962964954759921229436647956617731), SC_(0.9999995625181280852782419366072564332654), SC_(0.4374818719147217580633927435667345739681e-6) }}, 
      {{ SC_(0.19740999050554819405078887939453125e-4), SC_(105.41565704345703125), SC_(0.3082362115383148193359375), SC_(50650.76748327468387622526146707985788856), SC_(0.4058462091652395705622130232256784578791e-18), SC_(0.999999999999999999999991987363087849489), SC_(0.8012636912150510965168560289672323749562e-23) }}, 
      {{ SC_(0.2004335328820161521434783935546875e-4), SC_(482.00701904296875), SC_(0.905801355838775634765625), SC_(49885.0975467399541005594058055485920959), SC_(0.6952488973381382600133321305803386175123e-497), SC_(1.0), SC_(0.1393700587007418889673088312510010012273e-501) }}, 
      {{ SC_(0.2017128645093180239200592041015625e-4), SC_(232.9792938232421875), SC_(0.1355634629726409912109375), SC_(49569.39447695058637088307802861825336459), SC_(0.5613839078401083563926364753233791797764e-16), SC_(0.9999999999999999999988674787865299668824), SC_(0.1132521213470033117556444752410328436421e-20) }}, 
      {{ SC_(0.20203804524498991668224334716796875e-4), SC_(42.8336334228515625), SC_(0.1355634629726409912109375), SC_(49491.30543730291452299570686539488489706), SC_(0.0002970093232930177329636992017779607619212), SC_(0.9999999939987575820392696584870441824195), SC_(0.6001242417960730341512955817580536150083e-8) }}, 
      {{ SC_(0.20300331016187556087970733642578125e-4), SC_(42.38938140869140625), SC_(0.9688708782196044921875), SC_(49255.96842848213676438936988840982061108), SC_(0.3254102070311191847990477740588760715854e-65), SC_(1.0), SC_(0.660651322902325825501909533025783367306e-70) }}, 
      {{ SC_(0.2051885167020373046398162841796875e-4), SC_(236087.890625), SC_(0.8350250720977783203125), SC_(48722.72335464117531549642945589803667543), SC_(BOOST_MATH_SMALL_CONSTANT(0.3623657681223884685762672034466978337415e-184763)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.7437305289460397760237525357326064309497e-184768)) }}, 
      {{ SC_(0.21041001673438586294651031494140625e-4), SC_(3.8230969905853271484375), SC_(0.12707412242889404296875), SC_(47523.85310963552184634237668034540815544), SC_(0.6195869439847247414271234613685101142876), SC_(0.9999869627813034247834524610650576290292), SC_(0.1303721869657521654753893494237097084781e-4) }}, 
      {{ SC_(0.2107691761921159923076629638671875e-4), SC_(0.04489715397357940673828125), SC_(0.221111953258514404296875), SC_(47443.99666807844164422517130961809149441), SC_(23.47266252209839487239051712128153957277), SC_(0.999505500022370764381415753210237199735), SC_(0.0004944999776292356185842467897628002650075) }}, 
      {{ SC_(0.21139929231139831244945526123046875e-4), SC_(0.17535277947899885475635528564453125e-4), SC_(0.8350250720977783203125), SC_(47305.46963235175970278465413513213767625), SC_(57026.27394012006075825157468938510447443), SC_(0.4534139659948462776784686703995923371492), SC_(0.5465860340051537223215313296004076628508) }}, 
      {{ SC_(0.21433561414596624672412872314453125e-4), SC_(3719.279052734375), SC_(0.81474220752716064453125), SC_(46647.00459679377138587098836772819692673), SC_(0.1495926352972579290785339523472754005755e-2726), SC_(1.0), SC_(0.3206907637270668567387926794334274553413e-2731) }}, 
      {{ SC_(0.22448601157520897686481475830078125e-4), SC_(445071.28125), SC_(0.221111953258514404296875), SC_(44532.62436928473062336268384761066982164), SC_(BOOST_MATH_SMALL_CONSTANT(0.4607684427809900763431582243857051777023e-48306)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1034676148793947213721892942120911058743e-48310)) }}, 
      {{ SC_(0.22683978386339731514453887939453125e-4), SC_(0.0405500046908855438232421875), SC_(0.905801355838775634765625), SC_(44086.07924003136859651634907996836330649), SC_(22.49465708555873548404357783254789649371), SC_(0.9994900162236478687022169258555052598576), SC_(0.0005099837763521312977830741444947401424393) }}, 
      {{ SC_(0.234849067055620253086090087890625e-4), SC_(25542.79296875), SC_(0.9688708782196044921875), SC_(42569.81566430276714554195940795833966451), SC_(BOOST_MATH_SMALL_CONSTANT(0.7581371473885469585126327225518771230987e-38493)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1780926545153655561405185897694774794731e-38497)) }}, 
      {{ SC_(0.23615430109202861785888671875e-4), SC_(4.50702953338623046875), SC_(0.905801355838775634765625), SC_(42343.22752013782255946235697838204438231), SC_(0.5715334129764534783145383504186846510886e-5), SC_(0.999999999865023653988705273815604260127), SC_(0.1349763460112947261843957398729530965949e-9) }}, 
      {{ SC_(0.2399934965069405734539031982421875e-4), SC_(462.945892333984375), SC_(0.81474220752716064453125), SC_(41661.08259105714016534966079504412432568), SC_(0.2775658605653054188832508127259377541357e-341), SC_(1.0), SC_(0.6662473543711679396846266818523256261124e-346) }}, 
      {{ SC_(0.24066381229204125702381134033203125e-4), SC_(1270.3814697265625), SC_(0.12707412242889404296875), SC_(41544.01619584179617307996501816686679587), SC_(0.6432823532988754950678493440100093053861e-77), SC_(1.0), SC_(0.1548435640565879148514421887738552981949e-81) }}, 
      {{ SC_(0.25217834263457916676998138427734375e-4), SC_(1832.2723388671875), SC_(0.913384497165679931640625), SC_(39646.38644935289616324338485234769483149), SC_(0.1452763128621285161831040219171995838252e-1949), SC_(1.0), SC_(0.3664301488048974504586999693295043470729e-1954) }}, 
      {{ SC_(0.252623358392156660556793212890625e-4), SC_(0.18408362567424774169921875), SC_(0.1355634629726409912109375), SC_(39582.74118681315029489567111244553726972), SC_(7.044686151999700105392458377191074685903), SC_(0.9998220579880223516008255071920565810242), SC_(0.0001779420119776483991744928079434189758066) }}, 
      {{ SC_(0.2568908166722394526004791259765625e-4), SC_(0.0026883506216108798980712890625), SC_(0.905801355838775634765625), SC_(38929.29587722904553152445726009402934876), SC_(369.7184745062910124389774018949948166521), SC_(0.9905921692794321886093269352728967872691), SC_(0.00940783072056781139067306472710321273093) }}, 
      {{ SC_(0.26870451620197854936122894287109375e-4), SC_(0.000144985810038633644580841064453125), SC_(0.8350250720977783203125), SC_(37217.22199709670655355686997165747036372), SC_(6895.605189165562504177708220015695722412), SC_(0.8436825379600015811441576566235048540017), SC_(0.1563174620399984188558423433764951459983) }}, 
      {{ SC_(0.27549775040824897587299346923828125e-4), SC_(0.1503586471080780029296875), SC_(0.12707412242889404296875), SC_(36295.98898214209116290907229901584968788), SC_(8.375418806411123975304744110348984534574), SC_(0.999769299946587350130083574806330185546), SC_(0.0002307000534126498699164251936698144539948) }}, 
      {{ SC_(0.2782344745355658233165740966796875e-4), SC_(0.295027554035186767578125), SC_(0.8350250720977783203125), SC_(35941.82178311282183858216166056846305608), SC_(2.074584015871362718585422877982321716874), SC_(0.9999422827176363493743336035161848668839), SC_(0.5771728236365062566639648381513311607213e-4) }}, 
      {{ SC_(0.28775624741683714091777801513671875e-4), SC_(25491.8359375), SC_(0.905801355838775634765625), SC_(34740.91309313126314329366380972784559115), SC_(BOOST_MATH_SMALL_CONSTANT(0.1416279658206675284644779676116805674026e-26157)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4076690944794690667194646507475445069595e-26162)) }}, 
      {{ SC_(0.2893155397032387554645538330078125e-4), SC_(494.983856201171875), SC_(0.9688708782196044921875), SC_(34557.55760263256678063136756722525969856), SC_(0.2890949069585648946963256640313804418152e-748), SC_(1.0), SC_(0.8365605876514313574231285595563498875846e-753) }}, 
      {{ SC_(0.29415024982881732285022735595703125e-4), SC_(0.00041924233664758503437042236328125), SC_(0.3082362115383148193359375), SC_(33995.42298781772266213120899206663131448), SC_(2386.063098878378675267956366611194207388), SC_(0.9344154580933704966321633296307533232161), SC_(0.06558454190662950336783667036924667678394) }}, 
      {{ SC_(0.29590575650217942893505096435546875e-4), SC_(0.283195674419403076171875), SC_(0.81474220752716064453125), SC_(33795.39511420584887740898771673983552197), SC_(2.290823897771470125511242490096281441071), SC_(0.9999322195045552279110293804079128871172), SC_(0.6778049544477208897061959208711288282975e-4) }}, 
      {{ SC_(0.299345629173330962657928466796875e-4), SC_(4.095668792724609375), SC_(0.81474220752716064453125), SC_(33404.33980056291121139398867359007762442), SC_(0.0002879141232846902209324334859829932849643), SC_(0.9999999913809366530785803322881992366014), SC_(0.861906334692141966771180076339861949515e-8) }}, 
      {{ SC_(0.30238283216021955013275146484375e-4), SC_(47.60295867919921875), SC_(0.221111953258514404296875), SC_(33066.23130522049694371736967150705819949), SC_(0.6066711009943786655226912341571896993929e-6), SC_(0.9999999999816528501423908342357451119694), SC_(0.1834714985760916576425488803055205902901e-10) }}, 
      {{ SC_(0.3114132778136990964412689208984375e-4), SC_(348144.0), SC_(0.3082362115383148193359375), SC_(32098.33389035029241397207487302347639031), SC_(BOOST_MATH_SMALL_CONSTANT(0.1762609719892802507741212026091836262589e-55722)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5491281030080801389728927665390102499293e-55727)) }}, 
      {{ SC_(0.31231902539730072021484375e-4), SC_(33712.953125), SC_(0.221111953258514404296875), SC_(32007.54161537159159710306959672364944529), SC_(0.2695260440597220947109382805026254025866e-3662), SC_(1.0), SC_(0.8420704323329926310015340121536534269201e-3667) }}, 
      {{ SC_(0.3187526090187020599842071533203125e-4), SC_(0.4971525049768388271331787109375e-4), SC_(0.905801355838775634765625), SC_(31374.55539344689310304928706875069870009), SC_(20112.28887924914257320836126084639953446), SC_(0.6093703321041394434571656013777815272162), SC_(0.3906296678958605565428343986222184727838) }}, 
      {{ SC_(0.3188861956004984676837921142578125e-4), SC_(16.32230377197265625), SC_(0.8350250720977783203125), SC_(31355.81127255330873045397584652154569512), SC_(0.1222074701559891111911389773172013021319e-13), SC_(0.999999999999999999610255754208595422225), SC_(0.3897442457914045777750191938125222519392e-18) }}, 
      {{ SC_(0.319889441016130149364471435546875e-4), SC_(3931.19482421875), SC_(0.3082362115383148193359375), SC_(31251.94800013161196897912799096276134602), SC_(0.5745864678972875412080359974019879563813e-632), SC_(1.0), SC_(0.1838562088657218338652843181267156977738e-636) }}, 
      {{ SC_(0.32712865504436194896697998046875e-4), SC_(3109.48583984375), SC_(0.9688708782196044921875), SC_(30560.39464204411290636833886910765434566), SC_(0.1108549488685000006098886343654101789748e-4688), SC_(1.0), SC_(0.3627405672176397453051972129897762624733e-4693) }}, 
      {{ SC_(0.3275294511695392429828643798828125e-4), SC_(25796.328125), SC_(0.8350250720977783203125), SC_(30520.87225244356335157942706699076684673), SC_(BOOST_MATH_SMALL_CONSTANT(0.8381445176678792659668927836693079104497e-20192)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2746135532220170098568726383437115956981e-20196)) }}, 
      {{ SC_(0.334105643560178577899932861328125e-4), SC_(3378.014404296875), SC_(0.221111953258514404296875), SC_(29921.95193372606848176520231044586993696), SC_(0.3367989592512801451610310755314039758674e-369), SC_(1.0), SC_(0.1125591538938548898321239668571790685691e-373) }}, 
      {{ SC_(0.334107098751701414585113525390625e-4), SC_(4.2327022552490234375), SC_(0.913384497165679931640625), SC_(29928.62516317695550339210665359170562499), SC_(0.809428035585891781081852118385381939328e-5), SC_(0.9999999997295472040717495972301680634642), SC_(0.2704527959282504027698319365358097631894e-9) }}, 
      {{ SC_(0.3407927579246461391448974609375e-4), SC_(288782.71875), SC_(0.81474220752716064453125), SC_(29330.19888791878932947154534551660334586), SC_(BOOST_MATH_SMALL_CONSTANT(0.135184030849881991590274414805713778675e-211458)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4609039010150209477801496667289009548031e-211463)) }}, 
      {{ SC_(0.346417873515747487545013427734375e-4), SC_(411.559112548828125), SC_(0.913384497165679931640625), SC_(28860.27563597394879233427806328876239557), SC_(0.1522697740032561870065865846935945974367e-439), SC_(1.0), SC_(0.527610255438634632761400523758123631574e-444) }}, 
      {{ SC_(0.3529437162796966731548309326171875e-4), SC_(0.22326681573758833110332489013671875e-4), SC_(0.632396042346954345703125), SC_(28333.67188577703227003090077461249901366), SC_(44788.91695876111389744122818346075462797), SC_(0.3874817937042091089750716503682375456457), SC_(0.6125182062957908910249283496317624543543) }}, 
      {{ SC_(0.358525212504900991916656494140625e-4), SC_(0.4175899922847747802734375), SC_(0.905801355838775634765625), SC_(27892.98297091729514155146883067834541719), SC_(0.9191818316297753966421503516069036609437), SC_(0.9999670472124482163691157081516938377334), SC_(0.329527875517836308842918483061622666184e-4) }}, 
      {{ SC_(0.3616316462284885346889495849609375e-4), SC_(311936.78125), SC_(0.905801355838775634765625), SC_(27639.22261434461938975554213848538169757), SC_(BOOST_MATH_SMALL_CONSTANT(0.21872320557191207599327386050169592483e-320038)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.7913507866114722691803263837905894928814e-320043)) }}, 
      {{ SC_(0.362039208994247019290924072265625e-4), SC_(3.842815399169921875), SC_(0.221111953258514404296875), SC_(27619.24145744833795205594628565485626954), SC_(0.2885911357778822591534545401320889491212), SC_(0.9999895511931133427608999244407741894681), SC_(0.1044880688665723910007555922581053190729e-4) }}, 
      {{ SC_(0.3621911673690192401409149169921875e-4), SC_(0.001695460639894008636474609375), SC_(0.913384497165679931640625), SC_(27612.07735758991527179555055475190235649), SC_(587.4590726384651825885815591687179437043), SC_(0.9791677755380141364366349977092058495222), SC_(0.02083222446198586356336500229079415047777) }}, 
      {{ SC_(0.36229626857675611972808837890625e-4), SC_(0.004191714338958263397216796875), SC_(0.12707412242889404296875), SC_(27599.79194493898806466554839813002500616), SC_(240.4865490349295080263659930502620987497), SC_(0.9913619201371500903163174293666919250546), SC_(0.008638079862849909683682570633308074945419) }}, 
      {{ SC_(0.3714940612553618848323822021484375e-4), SC_(0.00259495410136878490447998046875), SC_(0.632396042346954345703125), SC_(26918.86986823624863883899272517520565716), SC_(384.8197974694066082527997026471724900083), SC_(0.9859059415712319237580331908573981554027), SC_(0.01409405842876807624196680914260184459732) }}, 
      {{ SC_(0.3718810694408603012561798095703125e-4), SC_(1.01127374172210693359375), SC_(0.1355634629726409912109375), SC_(26888.31759685705379594950317256428025768), SC_(1.981430989726498944950388740699451364096), SC_(0.9999263142820511371425674052947570323764), SC_(0.7368571794886285743259470524296762357664e-4) }}, 
      {{ SC_(0.3756858131964690983295440673828125e-4), SC_(386440.28125), SC_(0.913384497165679931640625), SC_(26604.54823315935969085332892717711494674), SC_(BOOST_MATH_SMALL_CONSTANT(0.4065286066478631235489668166780115749874e-410561)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.152804175844329602500189843747099318363e-410565)) }}, 
      {{ SC_(0.3779314647545106709003448486328125e-4), SC_(0.015007309615612030029296875), SC_(0.8350250720977783203125), SC_(26461.40422999164437038149048593427607469), SC_(65.02937587019798712765467475392460775433), SC_(0.9975485066392103415098279398679676242021), SC_(0.002451493360789658490172060132032375797861) }}, 
      {{ SC_(0.3796306918957270681858062744140625e-4), SC_(26.5066623687744140625), SC_(0.3082362115383148193359375), SC_(26337.55438031456065854766964405203660784), SC_(0.650968760775798160106200264618281989814e-5), SC_(0.9999999997528362917775482883034768425904), SC_(0.2471637082224517116965231574095716914252e-9) }}, 
      {{ SC_(0.382418438675813376903533935546875e-4), SC_(0.041891194880008697509765625), SC_(0.12707412242889404296875), SC_(26147.43384232011674693762277429193851936), SC_(25.73732536299210380242827376122671812981), SC_(0.9990166523881228866321958851431044023592), SC_(0.0009833476118771133678041148568955976407561) }}, 
      {{ SC_(0.3837459371425211429595947265625e-4), SC_(0.468349933624267578125), SC_(0.913384497165679931640625), SC_(26059.76048433521995668834278068219745685), SC_(0.6987607570621884456957464270944079098916), SC_(0.9999731869361744352453122586012926205363), SC_(0.2681306382556475468774139870737946368093e-4) }}, 
      {{ SC_(0.3972529884777031838893890380859375e-4), SC_(0.00289903464727103710174560546875), SC_(0.8350250720977783203125), SC_(25174.48903569539400579147933223238025856), SC_(343.3239985735199759551980828118697972407), SC_(0.986545712279008544056699869292160934395), SC_(0.01345428772099145594330013070783906560505) }}, 
      {{ SC_(0.3992606434621848165988922119140625e-4), SC_(495351.78125), SC_(0.9688708782196044921875), SC_(25032.60889109550663689197939247915216651), SC_(BOOST_MATH_SMALL_CONSTANT(0.696740351458271689129001813176869375763e-746418)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2783330952396708475154173721707152958145e-746422)) }}, 
      {{ SC_(0.4005068331025540828704833984375e-4), SC_(42.8498382568359375), SC_(0.12707412242889404296875), SC_(24964.03974453176254732786393631764995119), SC_(0.0004764518849491958406381833364919699287086), SC_(0.9999999809144721474726734378650429764282), SC_(0.1908552785252732656213495702357176544891e-7) }}, 
      {{ SC_(0.401491633965633809566497802734375e-4), SC_(3246.22998046875), SC_(0.905801355838775634765625), SC_(24898.4584653078017864790087780927673871), SC_(0.1108057695326287750167782477290997721268e-3333), SC_(1.0), SC_(0.4450306419050789325668215199282677439922e-3338) }}, 
      {{ SC_(0.401874640374444425106048583984375e-4), SC_(0.4230124056339263916015625), SC_(0.9688708782196044921875), SC_(24884.65638019556468000577131054549427565), SC_(0.5499362787544073007908815351717741538709), SC_(0.9999779010761751112114263009977381864979), SC_(0.2209892382488878857369900226181350205022e-4) }}, 
      {{ SC_(0.40288010495714843273162841796875e-4), SC_(2569.28173828125), SC_(0.8350250720977783203125), SC_(24812.8533303734616332218096405631482841), SC_(0.9878144010473054266519912609738087253485e-2014), SC_(1.0), SC_(0.3981059283650058546619165773436982959803e-2018) }}, 
      {{ SC_(0.40309605537913739681243896484375e-4), SC_(0.0039625889621675014495849609375), SC_(0.81474220752716064453125), SC_(24809.45385325406043207665165354862438535), SC_(250.8826249698205791295193383165914277873), SC_(0.9899888564869101246312292759512497284871), SC_(0.01001114351308987536877072404875027151294) }}, 
      {{ SC_(0.4062067091581411659717559814453125e-4), SC_(0.12048657238483428955078125), SC_(0.221111953258514404296875), SC_(24616.71699087330989611384352424571439851), SC_(9.408154880466094757120473718264061713371), SC_(0.9996179604048634374596919774821892391986), SC_(0.0003820395951365625403080225178107608014209) }}, 
      {{ SC_(0.4116668787901289761066436767578125e-4), SC_(24253.806640625), SC_(0.3082362115383148193359375), SC_(24280.81431332926606221047228474533799192), SC_(0.3121000460868512625690086385758400813897e-3885), SC_(1.0), SC_(0.1285377179115116903067863941396360394018e-3889) }}, 
      {{ SC_(0.416882903664372861385345458984375e-4), SC_(0.00045137223787605762481689453125), SC_(0.913384497165679931640625), SC_(23989.904870502330213515101909269354169), SC_(2213.111868591642997977556052058897902679), SC_(0.9155398063273470932413144008298325539327), SC_(0.08446019367265290675868559917016744606727) }}, 
      {{ SC_(0.417713818023912608623504638671875e-4), SC_(274447.0), SC_(0.1355634629726409912109375), SC_(23926.73917601287382168657073522639595926), SC_(BOOST_MATH_SMALL_CONSTANT(0.1053243186220253393843550866913641343978e-17367)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4401950380585728898409181782487226374213e-17372)) }}, 
      {{ SC_(0.418079944211058318614959716796875e-4), SC_(0.11457650363445281982421875), SC_(0.3082362115383148193359375), SC_(23918.0162433633390700257956287535078955), SC_(9.407728093440956133874016879229505002698), SC_(0.9996068223597883536477963324387786099182), SC_(0.0003931776402116463522036675612213900817857) }}, 
      {{ SC_(0.42011219193227589130401611328125e-4), SC_(0.000218811779632233083248138427734375), SC_(0.632396042346954345703125), SC_(23803.70760352901714607612121091230507105), SC_(4569.595256369948321655519715808656289915), SC_(0.8389473626340370230854972294747071692757), SC_(0.1610526373659629769145027705252928307243) }}, 
      {{ SC_(0.4257139153196476399898529052734375e-4), SC_(219.9326934814453125), SC_(0.221111953258514404296875), SC_(23483.98585440845061066129375621335062778), SC_(0.2742309436430616964714146372071762250557e-25), SC_(0.9999999999999999999999999999988322640571), SC_(0.1167735942881189541197741014749144791176e-29) }}, 
      {{ SC_(0.4258894841768778860569000244140625e-4), SC_(0.15419080227729864418506622314453125e-4), SC_(0.12707412242889404296875), SC_(23478.34280619960863222452345787790270158), SC_(64856.63842900619312653853485483821631559), SC_(0.2657876016714694565174333097418585187823), SC_(0.7342123983285305434825666902581414812177) }}, 
      {{ SC_(0.429383144364692270755767822265625e-4), SC_(0.0428761281073093414306640625), SC_(0.81474220752716064453125), SC_(23290.59836884916498617969483016403134016), SC_(21.8796279795074343794641287446475380789), SC_(0.999061462793338235287244798625770077551), SC_(0.0009385372066617647127552013742299224490029) }}, 
      {{ SC_(0.4323314715293236076831817626953125e-4), SC_(4174.39013671875), SC_(0.1355634629726409912109375), SC_(23121.48823631931525175925257480111357524), SC_(0.1399277347281718468615718118039207924204e-266), SC_(1.0), SC_(0.6051848103288302512572813624272096122725e-271) }}, 
      {{ SC_(0.43628693674691021442413330078125e-4), SC_(495.40863037109375), SC_(0.12707412242889404296875), SC_(22913.91488624606215427063174309270358555), SC_(0.9010090312960712504628615275371366340374e-31), SC_(0.9999999999999999999999999999999999960679), SC_(0.3932147936173475243230005286999380602616e-35) }}, 
      {{ SC_(0.4396517397253774106502532958984375e-4), SC_(0.001847697305493056774139404296875), SC_(0.221111953258514404296875), SC_(22744.0159608675001616316114316006039126), SC_(542.4707485929476872268423155163880612107), SC_(0.9767044829320448042128544796818381977098), SC_(0.02329551706795519578714552031816180229023) }}, 
      {{ SC_(0.45636130380444228649139404296875e-4), SC_(26.9266033172607421875), SC_(0.632396042346954345703125), SC_(21908.61132490693279868128115458396733217), SC_(0.114077674760694480313659131142998906791e-12), SC_(0.9999999999999999947930211975140291707132), SC_(0.5206978802485970829286796900444686620986e-17) }}, 
      {{ SC_(0.4623167842510156333446502685546875e-4), SC_(0.43400345020927488803863525390625e-4), SC_(0.1355634629726409912109375), SC_(21628.33767970691711806181483651588374799), SC_(23043.14390569971465324345453148116785628), SC_(0.4841643239066533530875464516496873777069), SC_(0.5158356760933466469124535483503126222931) }}, 
      {{ SC_(0.4653503492590971291065216064453125e-4), SC_(0.188413614523597061634063720703125e-4), SC_(0.3082362115383148193359375), SC_(21488.37715854430957260169413847909850451), SC_(53075.52915738713468516386757165996559283), SC_(0.2881873847582079845585432664242853856844), SC_(0.7118126152417920154414567335757146143156) }}, 
      {{ SC_(0.4662941864808090031147003173828125e-4), SC_(0.0003559053293429315090179443359375), SC_(0.905801355838775634765625), SC_(21447.95065480673458299046695950823916045), SC_(2807.473410191298259899782228370404233737), SC_(0.8842537898876547244205053464718520707457), SC_(0.1157462101123452755794946535281479292543) }}, 
      {{ SC_(0.466877463622950017452239990234375e-4), SC_(3780.931396484375), SC_(0.632396042346954345703125), SC_(21410.08338969725340269900662600119477313), SC_(0.2257391921210343612254294174028316748378e-1646), SC_(1.0), SC_(0.1054359238178690708697627729079251689545e-1650) }}, 
      {{ SC_(0.47170542529784142971038818359375e-4), SC_(186.8951263427734375), SC_(0.632396042346954345703125), SC_(21193.86711827442353521289275282851388591), SC_(0.4985855099448642037318909421798173769551e-83), SC_(1.0), SC_(0.2352498990214761583545264867928475446121e-87) }}, 
      {{ SC_(0.4735972834168933331966400146484375e-4), SC_(0.00372543814592063426971435546875), SC_(0.3082362115383148193359375), SC_(21114.17609539758913560451160434591780078), SC_(269.2284995693509671127780993439854580164), SC_(0.9874094652058952350894427065438275388296), SC_(0.01259053479410476491055729345617246117038) }}, 
      {{ SC_(0.47360430471599102020263671875e-4), SC_(48598.65234375), SC_(0.632396042346954345703125), SC_(21103.30748867175501316330739017132629156), SC_(BOOST_MATH_SMALL_CONSTANT(0.3756295693411259570184707878046911501874e-21126)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1779955912326841294543708384397391330829e-21130)) }}, 
      {{ SC_(0.4800888200406916439533233642578125e-4), SC_(0.029623560607433319091796875), SC_(0.913384497165679931640625), SC_(20831.70908451148224093013025807091311149), SC_(31.47906305784045464960320622881661698154), SC_(0.9984911671775577626496515142461326823679), SC_(0.001508832822442237350348485753867317632055) }}, 
      {{ SC_(0.4828667806577868759632110595703125e-4), SC_(0.000143944707815535366535186767578125), SC_(0.221111953258514404296875), SC_(20708.38659725890917753161481227003835775), SC_(6948.3708462003657583467288361519096977), SC_(0.7487640819642206167429448551580153639244), SC_(0.2512359180357793832570551448419846360756) }}, 
      {{ SC_(0.4830027683055959641933441162109375e-4), SC_(0.49858455895446240901947021484375e-4), SC_(0.913384497165679931640625), SC_(20706.17047129887892431507203038774212276), SC_(20054.42282583120409517932285215261290946), SC_(0.5079948253048804836573987501458642708508), SC_(0.4920051746951195163426012498541357291492) }}, 
      {{ SC_(0.48371657612733542919158935546875e-4), SC_(33.2382659912109375), SC_(0.81474220752716064453125), SC_(20669.19764848503364624734617129261301394), SC_(0.1685156464776935048025946255601914881441e-25), SC_(0.9999999999999999999999999999991847015576), SC_(0.8152984423662183223987521601970712509975e-30) }}, 
      {{ SC_(0.483796975458972156047821044921875e-4), SC_(0.00030146507197059690952301025390625), SC_(0.9688708782196044921875), SC_(20673.26308500593355276351774405787788275), SC_(3313.697649454521976780718009014432836067), SC_(0.8618542096208981110796609827832647336483), SC_(0.1381457903791018889203390172167352663517) }}, 
      {{ SC_(0.48389760195277631282806396484375e-4), SC_(3.295018672943115234375), SC_(0.8350250720977783203125), SC_(20663.91805289900876890154410773793244473), SC_(0.0009178956010703162417484590875699248379401), SC_(0.9999999555797909211884841922449603268117), SC_(0.4442020907881151580775503967318833535076e-7) }}, 
      {{ SC_(0.4859554246650077402591705322265625e-4), SC_(0.487077995785512030124664306640625e-4), SC_(0.9688708782196044921875), SC_(20581.45662117370905508383740125237934323), SC_(20527.15511089249993551464158930481274349), SC_(0.5006604639270614430763832942072115970552), SC_(0.4993395360729385569236167057927884029448) }}, 
      {{ SC_(0.4882370922132395207881927490234375e-4), SC_(0.0004924438544549047946929931640625), SC_(0.1355634629726409912109375), SC_(20479.99964335724309503229742001761592511), SC_(2032.540099484611307902518137337743858143), SC_(0.9097152021627909380582945346683700759856), SC_(0.09028479783720906194170546533162992401441) }}, 
      {{ SC_(0.0001018536931951530277729034423828125), SC_(0.000113726928248070180416107177734375), SC_(0.221111953258514404296875), SC_(9816.745147240660054719545524844572180529), SC_(8794.251081599785574986978540940650032881), SC_(0.5274701593904030565289133757941525840479), SC_(0.4725298406095969434710866242058474159521) }}, 
      {{ SC_(0.0001061613802448846399784088134765625), SC_(26.1992549896240234375), SC_(0.3082362115383148193359375), SC_(9415.798426350103046318463818991542320744), SC_(0.7369899242850637546729437316708551741582e-5), SC_(0.9999999992172836648183290614385452819041), SC_(0.7827163351816709385614547180959375321755e-9) }}, 
      {{ SC_(0.0001172095144283957779407501220703125), SC_(46.33734893798828125), SC_(0.632396042346954345703125), SC_(8527.329488596474715089305766693498378197), SC_(0.2447293570872016714498785087529847657244e-21), SC_(0.9999999999999999999999999713005862603906), SC_(0.2869941373960935386531353707011572170523e-25) }}, 
      {{ SC_(0.00011986176832579076290130615234375), SC_(0.429382145404815673828125), SC_(0.1355634629726409912109375), SC_(8341.027544080360332200297292708416218498), SC_(3.699574962167871674773895030720394058429), SC_(0.999556657166928142770298962075220278176), SC_(0.0004433428330718572297010379247797218240141) }}, 
      {{ SC_(0.0001239118282683193683624267578125), SC_(2.7409827709197998046875), SC_(0.9688708782196044921875), SC_(8068.862587007014755599626097509203647137), SC_(0.2766476686086212776961540397072958985728e-4), SC_(0.9999999965714167929246057924524390146568), SC_(0.3428583207075394207547560985343193664607e-8) }}, 
      {{ SC_(0.000130386673845350742340087890625), SC_(0.0404968559741973876953125), SC_(0.81474220752716064453125), SC_(7670.876004000112160595974437427584771402), SC_(23.24779265027832276370247573327652992582), SC_(0.9969785003120954244493917022618511056939), SC_(0.003021499687904575550608297738148894306108) }}, 
      {{ SC_(0.000131270222482271492481231689453125), SC_(0.4417803938849829137325286865234375e-4), SC_(0.913384497165679931640625), SC_(7620.22917032047510627610544897817586537), SC_(22633.32527420279150819003532931304932879), SC_(0.2518788059860499522934010449905912063787), SC_(0.7481211940139500477065989550094087936213) }}, 
      {{ SC_(0.0001324503100477159023284912109375), SC_(251.767547607421875), SC_(0.9688708782196044921875), SC_(7543.900042051433931648555662858407951561), SC_(0.1741787482946524009711870633934271398076e-381), SC_(1.0), SC_(0.2308868719412240311497352277329431192782e-385) }}, 
      {{ SC_(0.0001335285487584769725799560546875), SC_(0.4650310074794106185436248779296875e-4), SC_(0.1355634629726409912109375), SC_(7487.182750038846402463728024225717876808), SC_(21505.79454199191359536047812040556466148), SC_(0.2582412518253802418809364911949674144747), SC_(0.7417587481746197581190635088050325855253) }}, 
      {{ SC_(0.000133774345158599317073822021484375), SC_(0.00023849334684200584888458251953125), SC_(0.8350250720977783203125), SC_(7476.895678058054774259508620476217132484), SC_(4191.367659512759880268294836037910161055), SC_(0.6407890756101713253131302848597459915084), SC_(0.3592109243898286746868697151402540084916) }}, 
      {{ SC_(0.00013858181773684918880462646484375), SC_(1.8936798572540283203125), SC_(0.632396042346954345703125), SC_(7214.919039396837025996817766915342335639), SC_(0.1058849986659934859231152313569036893371), SC_(0.9999853243752065257270337185073816865539), SC_(0.1467562479347427296628149261831344609681e-4) }}, 
      {{ SC_(0.000142661112477071583271026611328125), SC_(0.00029941767570562660694122314453125), SC_(0.12707412242889404296875), SC_(7007.691810164761195138318693866049067171), SC_(3341.742337402713953129626940663288035245), SC_(0.6771086911850001880314970324220522450512), SC_(0.3228913088149998119685029675779477549488) }}, 
      {{ SC_(0.000144481091410852968692779541015625), SC_(0.16514885425567626953125), SC_(0.9688708782196044921875), SC_(6923.703398412580108119042386705070550996), SC_(3.429391338744755945325454534581991992038), SC_(0.9995049335067145628266727095108803677206), SC_(0.0004950664932854371733272904891196322793862) }}, 
      {{ SC_(0.000149327577673830091953277587890625), SC_(0.02011823840439319610595703125), SC_(0.905801355838775634765625), SC_(6698.868401735548606119290401322727275245), SC_(47.49156331534766907614771425732897644353), SC_(0.9929604166452168579912913902762729111497), SC_(0.007039583354783142008708609723727088850348) }}, 
      {{ SC_(0.00015278931823559105396270751953125), SC_(2.961205959320068359375), SC_(0.81474220752716064453125), SC_(6543.473191595368650892016888995054407072), SC_(0.002665238399175122624906174010262311430263), SC_(0.9999995926876697509864382992389617569011), SC_(0.4073123302490135617007610382430989376129e-6) }}, 
      {{ SC_(0.0001544274273328483104705810546875), SC_(0.00420844554901123046875), SC_(0.81474220752716064453125), SC_(6477.003929603966342138554593711051578367), SC_(236.13997873939813690271262254066917675), SC_(0.9648242340751977771603186451193768503439), SC_(0.03517576592480222283968135488062314965612) }}, 
      {{ SC_(0.000157981921802274882793426513671875), SC_(0.0037211482413113117218017578125), SC_(0.9688708782196044921875), SC_(6333.248025889411807409973129675958965542), SC_(265.3180437601998887423173310844643774494), SC_(0.9597915606270062768933396368390258534385), SC_(0.04020843937299372310666036316097414656149) }}, 
      {{ SC_(0.000158215596457011997699737548828125), SC_(0.0021306932903826236724853515625), SC_(0.632396042346954345703125), SC_(6321.029006220338639420486814392216778079), SC_(468.7874392533131252041345405946340481051), SC_(0.930957273584939906869139763616711947603), SC_(0.06904272641506009313086023638328805239701) }}, 
      {{ SC_(0.000160951211000792682170867919921875), SC_(0.30127601348794996738433837890625e-4), SC_(0.632396042346954345703125), SC_(6213.605202880870319684522296848826470202), SC_(33191.61194034333178935603703855301603968), SC_(0.1576848360027197794266700610327895075738), SC_(0.8423151639972802205733299389672104924262) }}, 
      {{ SC_(0.00016759600839577615261077880859375), SC_(42.365489959716796875), SC_(0.81474220752716064453125), SC_(5962.419209243696990787532758138575009456), SC_(0.2745901684849735730833116097646148949735e-32), SC_(0.9999999999999999999999999999999999995395), SC_(0.460534824621638706370261627704862425701e-36) }}, 
      {{ SC_(0.000168283222592435777187347412109375), SC_(195800.984375), SC_(0.905801355838775634765625), SC_(5929.614792333636458838497244984211996004), SC_(BOOST_MATH_SMALL_CONSTANT(0.4831197613058346312798602692403539272976e-200888)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.814757413804446933868292816740745794065e-200892)) }}, 
      {{ SC_(0.000172738815308548510074615478515625), SC_(0.0049294424243271350860595703125), SC_(0.221111953258514404296875), SC_(5787.826408666762378346106402064048429964), SC_(204.1147555603997993918196397390832350333), SC_(0.9659351201946045097863972781297349672121), SC_(0.03406487980539549021360272187026503278789) }}, 
      {{ SC_(0.000173404449014924466609954833984375), SC_(254.2841949462890625), SC_(0.81474220752716064453125), SC_(5760.754405025796286017185717611522877711), SC_(0.309308341870567221840603649252970401078e-188), SC_(1.0), SC_(0.5369233265711180045878920124653560411787e-192) }}, 
      {{ SC_(0.000173563123098574578762054443359375), SC_(0.03019084036350250244140625), SC_(0.913384497165679931640625), SC_(5763.820101603284338087718804379332704233), SC_(30.8462262273872423234544825886802406685), SC_(0.9946767899164031704926066210832765036334), SC_(0.005323210083596829507393378916723496366579) }}, 
      {{ SC_(0.000175581997609697282314300537109375), SC_(424.74957275390625), SC_(0.3082362115383148193359375), SC_(5688.721163726462073502362206742214633907), SC_(0.7994233073263827924032244564721438697512e-70), SC_(1.0), SC_(0.1405277714126370331640974784771264102987e-73) }}, 
      {{ SC_(0.0001779057201929390430450439453125), SC_(276489.40625), SC_(0.81474220752716064453125), SC_(5607.863007605415905492639632979871681912), SC_(BOOST_MATH_SMALL_CONSTANT(0.4002198864067135036323444681833070153753e-202457)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.7136762896381973011352322634945436475998e-202461)) }}, 
      {{ SC_(0.0001830969122238457202911376953125), SC_(316055.1875), SC_(0.12707412242889404296875), SC_(5448.363855012008867050069508711867940505), SC_(BOOST_MATH_SMALL_CONSTANT(0.9706033380826670137569743884892059945736e-18659)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.17814583678911211117845773161397628964e-18662)) }}, 
      {{ SC_(0.00019036870799027383327484130859375), SC_(159131.59375), SC_(0.8350250720977783203125), SC_(5240.424606587977644850715935961858665052), SC_(BOOST_MATH_SMALL_CONSTANT(0.2224500012712520372183312643665155986136e-124538)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4244885061252478631853807266395371322656e-124542)) }}, 
      {{ SC_(0.00019106571562588214874267578125), SC_(419861.28125), SC_(0.913384497165679931640625), SC_(5220.294070692503835759362993222013780906), SC_(BOOST_MATH_SMALL_CONSTANT(0.9049906875539083416221732298640456870193e-446068)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1733600972088255957843856613647525229045e-446071)) }}, 
      {{ SC_(0.000191590792383067309856414794921875), SC_(0.302199405268765985965728759765625e-4), SC_(0.221111953258514404296875), SC_(5218.198403960466669221628284729384062189), SC_(33091.99229469319000376077420451715593021), SC_(0.1362091471954967570972452259296608626232), SC_(0.8637908528045032429027547740703391373768) }}, 
      {{ SC_(0.000192195278941653668880462646484375), SC_(177798.15625), SC_(0.3082362115383148193359375), SC_(5190.391379153075167027803493007631836791), SC_(BOOST_MATH_SMALL_CONSTANT(0.1141146016081000795919967144320131012393e-28459)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2198574120372409176371795674206966282075e-28463)) }}, 
      {{ SC_(0.000193911953829228878021240234375), SC_(32.115245819091796875), SC_(0.12707412242889404296875), SC_(5152.947846003342098294718061584450246476), SC_(0.002639498699135445838819091652742231315418), SC_(0.999999487769442659745453026145816785441), SC_(0.512230557340254546973854183214558974377e-6) }}, 
      {{ SC_(0.000195966451428830623626708984375), SC_(0.011618844233453273773193359375), SC_(0.8350250720977783203125), SC_(5104.503639855632707391854768076715658822), SC_(84.45843463596422207679946774464377889093), SC_(0.9837234434510220864537775748281121571201), SC_(0.01627655654897791354622242517188784287993) }}, 
      {{ SC_(0.000195981003344058990478515625), SC_(0.0429041944444179534912109375), SC_(0.9688708782196044921875), SC_(5105.664201743881453946730034241174981722), SC_(20.11015895111600867001983475458394989157), SC_(0.9960766593423770419601555094346086588159), SC_(0.003923340657622958039844490565391341184107) }}, 
      {{ SC_(0.000196676512132398784160614013671875), SC_(2.09101200103759765625), SC_(0.221111953258514404296875), SC_(5082.742517647445801774492385509659202038), SC_(0.6918357515342059318292693635443862043404), SC_(0.9998639038682437950829710698619794166099), SC_(0.0001360961317562049170289301380205833900962) }}, 
      {{ SC_(0.000203948162379674613475799560546875), SC_(0.0001180238105007447302341461181640625), SC_(0.913384497165679931640625), SC_(4905.561569471299445541907025253345945424), SC_(8470.511202586446201247227543726581950478), SC_(0.3667415431320682499296214543111572259584), SC_(0.6332584568679317500703785456888427740416) }}, 
      {{ SC_(0.00020552115165628492832183837890625), SC_(0.00138142076320946216583251953125), SC_(0.3082362115383148193359375), SC_(4864.870283621552589600617549642511712112), SC_(724.6987501625367922536917238180934601035), SC_(0.8703480096976417388633162741430148381307), SC_(0.1296519903023582611366837258569851618693) }}, 
      {{ SC_(0.000218528322875499725341796875), SC_(215.121978759765625), SC_(0.1355634629726409912109375), SC_(4570.123815914933502416775387073811835093), SC_(0.8177025127270565517519048866936017134941e-15), SC_(0.9999999999999999998210765078443824493488), SC_(0.1789234921556175506512002662847620835962e-18) }}, 
      {{ SC_(0.0002204985357820987701416015625), SC_(107380.34375), SC_(0.1355634629726409912109375), SC_(4523.032636406744668872171043808459678016), SC_(BOOST_MATH_SMALL_CONSTANT(0.1647562629728817439093908329431436142546e-6797)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3642606105618770904813216523239665239724e-6801)) }}, 
      {{ SC_(0.000222539805690757930278778076171875), SC_(1432.2476806640625), SC_(0.81474220752716064453125), SC_(4485.741270282409935675504946595385781733), SC_(0.1612283750403730679258423751114438195734e-1051), SC_(1.0), SC_(0.3594241516078848051177543874430077069284e-1055) }}, 
      {{ SC_(0.000235087776673026382923126220703125), SC_(0.4550904333591461181640625), SC_(0.8350250720977783203125), SC_(4254.333433061065961463490484341133892093), SC_(1.023193488721235218241525045756827448596), SC_(0.9997595516478366624699532523192953225665), SC_(0.0002404483521633375300467476807046774334954) }}, 
      {{ SC_(0.0002402908285148441791534423828125), SC_(49604.3984375), SC_(0.632396042346954345703125), SC_(4150.250398112142629228158594289025284156), SC_(BOOST_MATH_SMALL_CONSTANT(0.2809555849412092585807767265143871993905e-21563)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6769605637987764728357471683016484257431e-21567)) }}, 
      {{ SC_(0.000240380948525853455066680908203125), SC_(0.01798204891383647918701171875), SC_(0.221111953258514404296875), SC_(4158.799612898859404525246505395700079542), SC_(56.84527887576449179974280320693646351955), SC_(0.9865156386899004621823922118833551461494), SC_(0.01348436131009953781760778811664485385065) }}, 
      {{ SC_(0.000241263434872962534427642822265625), SC_(12.126102447509765625), SC_(0.1355634629726409912109375), SC_(4141.742262758911222245097610877600052319), SC_(0.07515615341279770596981104392508648196893), SC_(0.9999818543055351449214821881784878112583), SC_(0.18145694464855078517811821512188741683e-4) }}, 
      {{ SC_(0.000247393851168453693389892578125), SC_(237.5718994140625), SC_(0.8350250720977783203125), SC_(4036.096790745714381586806296665342854216), SC_(0.606208749817224680109953757243807964491e-188), SC_(1.0), SC_(0.1501967820016577897041114220165502307491e-191) }}, 
      {{ SC_(0.00024769871379248797893524169921875), SC_(0.11330743134021759033203125), SC_(0.913384497165679931640625), SC_(4039.064655808197643788968479734038263565), SC_(6.75084267983746755998409266216487478625), SC_(0.9983314012509071829197699472791630796573), SC_(0.001668598749092817080230052720836920342725) }}, 
      {{ SC_(0.0002514437655918300151824951171875), SC_(15605.75390625), SC_(0.1355634629726409912109375), SC_(3966.813170071210650703421810375775952551), SC_(0.2221965421176740251409055120157725713745e-990), SC_(1.0), SC_(0.5601386619216181569774995706483415180832e-994) }}, 
      {{ SC_(0.00025589551660232245922088623046875), SC_(1.8412075042724609375), SC_(0.1355634629726409912109375), SC_(3905.732551009074298466780308567194030379), SC_(1.220471269888763743288439839955123473762), SC_(0.9996876155758901725469091054905562192717), SC_(0.0003123844241098274530908945094437807283082) }}, 
      {{ SC_(0.00025991306756623089313507080078125), SC_(0.00044235005043447017669677734375), SC_(0.905801355838775634765625), SC_(3849.701457077141431050044085672310243523), SC_(2258.390718424077938542748761691454979038), SC_(0.630262502016227621370414919511565748614), SC_(0.369737497983772378629585080488434251386) }}, 
      {{ SC_(0.00026072320179082453250885009765625), SC_(0.0252933166921138763427734375), SC_(0.632396042346954345703125), SC_(3835.995370898243525795356909959823218854), SC_(38.98476761977825124057415821836327491418), SC_(0.9899393632415654404293989497331186370653), SC_(0.0100606367584345595706010502668813629347) }}, 
      {{ SC_(0.000261564855463802814483642578125), SC_(2.969768047332763671875), SC_(0.3082362115383148193359375), SC_(3821.405524466826928964942296938639926773), SC_(0.2505263953449591757780672632223011395179), SC_(0.9999344455932164695959214942937015701483), SC_(0.6555440678353040407850570629842985169478e-4) }}, 
      {{ SC_(0.00026690683444030582904815673828125), SC_(0.4926892220973968505859375), SC_(0.12707412242889404296875), SC_(3744.630999610799179102664201934107067766), SC_(3.417322244783778445441490978156483043996), SC_(0.9990882395446033280373927712026808730813), SC_(0.0009117604553966719626072287973191269187271) }}, 
      {{ SC_(0.000272565521299839019775390625), SC_(0.00399976409971714019775390625), SC_(0.12707412242889404296875), SC_(3666.915441118759754681231662466320459986), SC_(251.9349129604083041843669622324897005207), SC_(0.9357120353681872816886730014355383410074), SC_(0.06428796463181271831132699856446165899256) }}, 
      {{ SC_(0.00027427947497926652431488037109375), SC_(289206.03125), SC_(0.9688708782196044921875), SC_(3632.788078545421912405913291645417768756), SC_(BOOST_MATH_SMALL_CONSTANT(0.2102799001769416384757592031219644042906e-435790)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5788388852595504947548501030017527205046e-435794)) }}, 
      {{ SC_(0.00027434344519861042499542236328125), SC_(2954.470947265625), SC_(0.3082362115383148193359375), SC_(3636.508295700661209639964141212992979246), SC_(0.1585655815345211799992885129986502872445e-475), SC_(1.0), SC_(0.4360380030536124173269201014387833274522e-479) }}, 
      {{ SC_(0.0002770713181234896183013916015625), SC_(0.341692575602792203426361083984375e-4), SC_(0.9688708782196044921875), SC_(3612.616345527002166796516337140214888032), SC_(29262.63561079274137388778786727086485259), SC_(0.1098886283921706710241786573420304234281), SC_(0.8901113716078293289758213426579695765719) }}, 
      {{ SC_(0.00027871350175701081752777099609375), SC_(4023.159423828125), SC_(0.632396042346954345703125), SC_(3579.048017739283881605316170941963833806), SC_(0.1120712826873127698254223263897725146742e-1751), SC_(1.0), SC_(0.3131315426108837801546009947512560530295e-1755) }}, 
      {{ SC_(0.00028021665639244019985198974609375), SC_(0.38819736801087856292724609375e-4), SC_(0.12707412242889404296875), SC_(3566.740628995367719834358851359670908542), SC_(25762.01830124904137748808250335197962905), SC_(0.1216123954470256387367677660584358510842), SC_(0.8783876045529743612632322339415641489158) }}, 
      {{ SC_(0.0002803694806061685085296630859375), SC_(25.140018463134765625), SC_(0.9688708782196044921875), SC_(3562.942591774059668711567414861210623046), SC_(0.5382935653401613794049965988355487897247e-39), SC_(1.0), SC_(0.1510811784009504217418370693440827162617e-42) }}, 
      {{ SC_(0.0002883693086914718151092529296875), SC_(460073.28125), SC_(0.221111953258514404296875), SC_(3454.185996240757717347805410673427755317), SC_(BOOST_MATH_SMALL_CONSTANT(0.3609488049862958914998492071737962153408e-49934)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1044960535938487025713540225543036817363e-49937)) }}, 
      {{ SC_(0.00029471665038727223873138427734375), SC_(4642.259765625), SC_(0.221111953258514404296875), SC_(3384.081751203178175370620299135085565111), SC_(0.1538423775489765357475481074516180071161e-506), SC_(1.0), SC_(0.4546059724895219723882118449541202246563e-510) }}, 
      {{ SC_(0.00029570105834864079952239990234375), SC_(0.3176224529743194580078125), SC_(0.81474220752716064453125), SC_(3382.578828730636936095408426359067756467), SC_(1.935221992619980707578905579564134695818), SC_(0.9994282127467585984399348909298228431753), SC_(0.0005717872532414015600651090701771568246996) }}, 
      {{ SC_(0.00029634564998559653759002685546875), SC_(0.19913904368877410888671875), SC_(0.632396042346954345703125), SC_(3374.738592221403546906618687303557085428), SC_(4.433505765756542858273043071415657657518), SC_(0.9986879905381565479113426540536530533211), SC_(0.001312009461843452088657345946346946678902) }}, 
      {{ SC_(0.000303403474390506744384765625), SC_(2574.359375), SC_(0.8350250720977783203125), SC_(3287.521806034971802887301527204610384962), SC_(0.1047442076816838732832369224443180558536e-2017), SC_(1.0), SC_(0.3186114461336887984555451406531853131734e-2021) }}, 
      {{ SC_(0.00030430863262154161930084228515625), SC_(4480.7470703125), SC_(0.905801355838775634765625), SC_(3277.165341360202837565254801793910487962), SC_(0.2213976574024951929657574974001049932498e-4600), SC_(1.0), SC_(0.6755767083469858051077185880165399540339e-4604) }}, 
      {{ SC_(0.00030529979267157614231109619140625), SC_(0.01552005298435688018798828125), SC_(0.3082362115383148193359375), SC_(3274.654322125870601212494443787065254475), SC_(65.22169126633106105574103247333184237395), SC_(0.9804718226051488823874458467991263059976), SC_(0.01952817739485111761255415320087369400238) }}, 
      {{ SC_(0.00031313023646362125873565673828125), SC_(47957.0), SC_(0.3082362115383148193359375), SC_(3182.224523010860532469370310063985137153), SC_(BOOST_MATH_SMALL_CONSTANT(0.4869323848916658188091372694151177783149e-7679)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1530163510998761729295090649649835593669e-7682)) }}, 
      {{ SC_(0.0003153369762003421783447265625), SC_(0.3186367757734842598438262939453125e-4), SC_(0.8350250720977783203125), SC_(3172.831842932285600026538031157109836735), SC_(31382.0752330794825907395443248734678599), SC_(0.09182000796450948165926037295322936243235), SC_(0.9081799920354905183407396270467706375677) }}, 
      {{ SC_(0.00031880356254987418651580810546875), SC_(387.38800048828125), SC_(0.12707412242889404296875), SC_(3130.199521661416446995255486905059239462), SC_(0.2724998200174160044620150661637361031001e-24), SC_(0.9999999999999999999999999999129449039489), SC_(0.8705509605112367919854721169070761696175e-28) }}, 
      {{ SC_(0.00031994408345781266689300537109375), SC_(0.001032375730574131011962890625), SC_(0.913384497165679931640625), SC_(3127.896874011541295607313029565696187665), SC_(966.2866448734111309827638173027792308751), SC_(0.7639855076314267128924605979897220292708), SC_(0.2360144923685732871075394020102779707292) }}, 
      {{ SC_(0.00032006253604777157306671142578125), SC_(25544.60546875), SC_(0.905801355838775634765625), SC_(3113.682688441297209705041700589809314877), SC_(BOOST_MATH_SMALL_CONSTANT(0.1025783281688806599327078671241459557444e-26211)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3294437437368775306821540221261273382728e-26215)) }}, 
      {{ SC_(0.00033008345053531229496002197265625), SC_(4.030014514923095703125), SC_(0.913384497165679931640625), SC_(3027.695874136566107847728245395000749406), SC_(0.139479648999015386391381516849575180088e-4), SC_(0.9999999953932080976845710611394580627781), SC_(0.4606791902315428938860541937221886144335e-8) }}, 
      {{ SC_(0.00033188183442689478397369384765625), SC_(0.00350953754968941211700439453125), SC_(0.905801355838775634765625), SC_(3015.369155981247271907055083325780561977), SC_(282.6829927870727441056338941058658985682), SC_(0.9142878947827909184252636231795874358471), SC_(0.08571210521720908157473637682041256415287) }}, 
      {{ SC_(0.00033481788705103099346160888671875), SC_(29065.5234375), SC_(0.9688708782196044921875), SC_(2975.863726169082216793986722632003389703), SC_(BOOST_MATH_SMALL_CONSTANT(0.4535759794105782584597366345081942126175e-43801)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1524182627792839465220615780991684493447e-43804)) }}, 
      {{ SC_(0.0003488220390863716602325439453125), SC_(0.02754667215049266815185546875), SC_(0.1355634629726409912109375), SC_(2864.935092454806724311892421987098664245), SC_(38.1132975293663947680462461735658995282), SC_(0.9868712841091931707720919654423078799989), SC_(0.01312871589080682922790803455769212000107) }}, 
      {{ SC_(0.0003489900263957679271697998046875), SC_(41187.57421875), SC_(0.913384497165679931640625), SC_(2854.230462336243022882038863689073490423), SC_(BOOST_MATH_SMALL_CONSTANT(0.3679302093634662820727270275131856040468e-43762)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1289069730768370629823366152554604244725e-43765)) }}, 
      {{ SC_(0.0003502474282868206501007080078125), SC_(426.3076171875), SC_(0.905801355838775634765625), SC_(2848.501239409495981055595161738776652775), SC_(0.1097851165581016728904078837337908787324e-439), SC_(1.0), SC_(0.3854136169547902386280098239236817602428e-443) }}, 
      {{ SC_(0.0003577272291295230388641357421875), SC_(31752.21875), SC_(0.12707412242889404296875), SC_(2784.504773532744521809274363546994037008), SC_(0.1968964349841053353157066029235379101063e-1877), SC_(1.0), SC_(0.7071147331318802709017406374093782540593e-1881) }}, 
      {{ SC_(0.00035909839789383113384246826171875), SC_(29.733074188232421875), SC_(0.913384497165679931640625), SC_(2780.802578187537810976609822680554063792), SC_(0.9466966087555186266682909543165127593827e-33), SC_(0.9999999999999999999999999999999999996596), SC_(0.3404400643833383421960793256360613832518e-36) }}, 
      {{ SC_(0.00035964619019068777561187744140625), SC_(20.331836700439453125), SC_(0.8350250720977783203125), SC_(2776.948437579244482906438446372059229445), SC_(0.7158065221381836966654729664148194532451e-17), SC_(0.9999999999999999999974223269238582790226), SC_(0.2577673076141720977350908877406218436716e-20) }}, 
      {{ SC_(0.00037471018731594085693359375), SC_(225.0031890869140625), SC_(0.632396042346954345703125), SC_(2662.74507079856651874603621021499439225), SC_(0.1134460445823962098537520023375762055653e-99), SC_(1.0), SC_(0.4260492144986802887971643669989427167467e-103) }}, 
      {{ SC_(0.0003926889621652662754058837890625), SC_(15.1938610076904296875), SC_(0.905801355838775634765625), SC_(2543.282274694489899149049394421170507156), SC_(0.1863354746768956949463016115232572128955e-16), SC_(0.9999999999999999999926734253397303639613), SC_(0.7326574660269636038667645239154558789159e-20) }}, 
      {{ SC_(0.0003978771273978054523468017578125), SC_(349.374237060546875), SC_(0.221111953258514404296875), SC_(2506.915366175151979603156285400780638393), SC_(0.1554926413830479500487743576624150972687e-39), SC_(1.0), SC_(0.6202548497689652901079433577060643469017e-43) }}, 
      {{ SC_(0.0004099641810171306133270263671875), SC_(0.000490800826810300350189208984375), SC_(0.3082362115383148193359375), SC_(2438.428882587437109925834868096142527), SC_(2038.293507112223688445759683916368304741), SC_(0.5446906621232390218689383638556236491231), SC_(0.4553093378767609781310616361443763508769) }}, 
      {{ SC_(0.00041028507985174655914306640625), SC_(2214.455810546875), SC_(0.1355634629726409912109375), SC_(2429.064305994519441738193117470290876888), SC_(0.2625842762445926687666088964797929354078e-142), SC_(1.0), SC_(0.1081009982307093036612147153031276889652e-145) }}, 
      {{ SC_(0.0004120909725315868854522705078125), SC_(367.71405029296875), SC_(0.913384497165679931640625), SC_(2420.174473903902857806633347353957408088), SC_(0.6496728701662286052346044917231870062105e-393), SC_(1.0), SC_(0.2684405100423453896401204773247119627795e-396) }}, 
      {{ SC_(0.00041210078052245080471038818359375), SC_(4.50945568084716796875), SC_(0.12707412242889404296875), SC_(2424.117403894619977669694751796875692789), SC_(0.5061366211946652295263819390852493512635), SC_(0.9997912514612115868195623051721445151557), SC_(0.0002087485387884131804376948278554848443257) }}, 
      {{ SC_(0.00041793254786171019077301025390625), SC_(4668.47119140625), SC_(0.9688708782196044921875), SC_(2383.722258071079769629454763425015064461), SC_(BOOST_MATH_SMALL_CONSTANT(0.5463688026886666147824002995784594817501e-7038)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2292082480828916039393283409724883460028e-7041)) }}, 
      {{ SC_(0.000420027412474155426025390625), SC_(0.00036404779530130326747894287109375), SC_(0.9688708782196044921875), SC_(2384.231564515840506117162175742121420846), SC_(2743.456202519181394365317435287217855567), SC_(0.4649720639863515895633703526231003551161), SC_(0.5350279360136484104366296473768996448839) }}, 
      {{ SC_(0.0004246321623213589191436767578125), SC_(17994.9140625), SC_(0.221111953258514404296875), SC_(2344.627547898256098125532721852248067543), SC_(0.3180733015474547916320064048072348766489e-1956), SC_(1.0), SC_(0.1356604812702889125683060028507461195511e-1959) }}, 
      {{ SC_(0.00042692126589827239513397216796875), SC_(0.00024527459754608571529388427734375), SC_(0.632396042346954345703125), SC_(2342.893992061399189259727437078954765507), SC_(4076.52023651885880746294074207107365464), SC_(0.3649700593600052683423340989110598558496), SC_(0.6350299406399947316576659010889401441504) }}, 
      {{ SC_(0.00042705106898210942745208740234375), SC_(2443.44287109375), SC_(0.913384497165679931640625), SC_(2333.277461493787480390344066682863595571), SC_(0.5332474573974756910385823818234032261756e-2599), SC_(1.0), SC_(0.2285400970084738037799111507992759078905e-2602) }}, 
      {{ SC_(0.00042847762233577668666839599609375), SC_(22.90042877197265625), SC_(0.221111953258514404296875), SC_(2330.160471219871088491361081493811799882), SC_(0.0005700639899661150267975099079387186909912), SC_(0.9999997553542524030768794920903804597612), SC_(0.2446457475969231205079096195402388391619e-6) }}, 
      {{ SC_(0.000430326792411506175994873046875), SC_(0.2849896918633021414279937744140625e-4), SC_(0.81474220752716064453125), SC_(2325.295773305186883745980657749475592263), SC_(35087.50723729603932294663380257741060442), SC_(0.06215240736296329237883350233563708602062), SC_(0.9378475926370367076211664976643629139794) }}, 
      {{ SC_(0.0004377235309220850467681884765625), SC_(454399.25), SC_(0.632396042346954345703125), SC_(2270.983882694597092822607027073564087212), SC_(BOOST_MATH_SMALL_CONSTANT(0.41735903811534924396720244633094087005e-197496)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1837789520637808375875188270585199147772e-197499)) }}, 
      {{ SC_(0.00044121246901340782642364501953125), SC_(0.031357325613498687744140625), SC_(0.12707412242889404296875), SC_(2264.551008840063404666623371407831723918), SC_(33.77007171411169184440381793659063020672), SC_(0.985306634482085072904417030273710660607), SC_(0.014693365517914927095582969726289339393) }}, 
      {{ SC_(0.00044747791253030300140380859375), SC_(0.0003718078951351344585418701171875), SC_(0.81474220752716064453125), SC_(2236.226718277089743036911311259913705518), SC_(2688.080069522922904007660912092374368351), SC_(0.4541201055582786368165961028461423460782), SC_(0.5458798944417213631834038971538576539218) }}, 
      {{ SC_(0.000447716913186013698577880859375), SC_(0.00131022813729941844940185546875), SC_(0.8350250720977783203125), SC_(2235.171607174475021181533559243044539962), SC_(761.6055853953372827060617266157216328845), SC_(0.7458584551151628280606321596990124303608), SC_(0.2541415448848371719393678403009875696392) }}, 
      {{ SC_(0.00045037711970508098602294921875), SC_(10660.8232421875), SC_(0.8350250720977783203125), SC_(2210.532153806107891289496569153621125534), SC_(BOOST_MATH_SMALL_CONSTANT(0.1206505832474931860295852623731075417229e-8346)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5457988160894030332567255774440224107433e-8350)) }}, 
      {{ SC_(0.000460021547041833400726318359375), SC_(0.2375358045101165771484375), SC_(0.905801355838775634765625), SC_(2175.238041648735644187860487639011201803), SC_(2.447811582193532553373907277195109839798), SC_(0.9988759574396087339002056496566398076671), SC_(0.001124042560391266099794350343360192332935) }}, 
      {{ SC_(0.0004610864561982452869415283203125), SC_(0.2207309305667877197265625), SC_(0.221111953258514404296875), SC_(2167.473886629351983032808170495678751964), SC_(5.532500683070298228554305431007140478259), SC_(0.9974539878412815418467482310918660577926), SC_(0.002546012158718458153251768908133942207371) }}, 
      {{ SC_(0.000464259064756333827972412109375), SC_(0.001531984074972569942474365234375), SC_(0.1355634629726409912109375), SC_(2152.117646884666200073960196407820694935), SC_(654.5971633708985065857497130109313151371), SC_(0.7667746074595678897794541209327724450161), SC_(0.2332253925404321102205458790672275549839) }}, 
      {{ SC_(0.00046533494605682790279388427734375), SC_(0.3233075040043331682682037353515625e-4), SC_(0.3082362115383148193359375), SC_(2148.181259917978900222556896452313827016), SC_(30931.11351285719300828566689211065659355), SC_(0.06494035845304564942861416504475144721513), SC_(0.9350596415469543505713858349552485527849) }}, 
      {{ SC_(0.000471754348836839199066162109375), SC_(2833.98779296875), SC_(0.12707412242889404296875), SC_(2111.238317153358003896756528720761672756), SC_(0.1488182277406715504172797814810214922066e-169), SC_(1.0), SC_(0.7048859739402956270968877726532616479643e-173) }}, 
      {{ SC_(0.00047560062375850975513458251953125), SC_(19603.037109375), SC_(0.81474220752716064453125), SC_(2092.170212686575096214087634075788007936), SC_(BOOST_MATH_SMALL_CONSTANT(0.9814459051172135904993735187569389742934e-14358)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4691042340464879469732504596518886411408e-14361)) }}, 
      {{ SC_(0.0004768202197737991809844970703125), SC_(4.81977367401123046875), SC_(0.8350250720977783203125), SC_(2095.18515800974545939651141116099485538), SC_(0.4066280103382282973903147124636493154344e-4), SC_(0.9999999805922640826282579500754695410751), SC_(0.1940773591737174204992453045892490288306e-7) }}, 
      {{ SC_(0.00047791490214876830577850341796875), SC_(0.11917771399021148681640625), SC_(0.3082362115383148193359375), SC_(2091.566761906672525335452041944246414406), SC_(9.065521332975766581322942677912211293429), SC_(0.995684384456381537630722352446379570576), SC_(0.004315615543618462369277647553620429423982) }}, 
      {{ SC_(0.00048245381913147866725921630859375), SC_(3.6078746318817138671875), SC_(0.905801355838775634765625), SC_(2071.02282050938481232080136434279772972), SC_(0.5951445121764384153313490722249785840832e-4), SC_(0.9999999712632574986526118543960442440817), SC_(0.2873674250134738814560395575591831761354e-7) }}, 
      {{ SC_(0.00048475922085344791412353515625), SC_(0.0004919702769257128238677978515625), SC_(0.1355634629726409912109375), SC_(2061.027836908120591791512438991288730224), SC_(2034.493470940324778631046964076907156462), SC_(0.5032394369328449989460119643414107638167), SC_(0.4967605630671550010539880356585892361833) }}, 
      {{ SC_(0.0004984539118595421314239501953125), SC_(0.27903375666937790811061859130859375e-4), SC_(0.905801355838775634765625), SC_(2008.466065633385514957748677800699067617), SC_(35835.69441714848349955135696320524343566), SC_(0.05307202062382085388638414390869253138822), SC_(0.9469279793761791461136158560913074686118) }}, 
      {{ SC_(0.001114696613512933254241943359375), SC_(338.423187255859375), SC_(0.3082362115383148193359375), SC_(890.7287333592224099650895877533423597214), SC_(0.655032394719436519104435410014932608533e-56), SC_(1.0), SC_(0.7353893168452085809661635006675386913064e-59) }}, 
      {{ SC_(0.00111688114702701568603515625), SC_(0.00206368253566324710845947265625), SC_(0.3082362115383148193359375), SC_(894.5411216021030206815562847824910599935), SC_(485.374731025981562374452616292613599528), SC_(0.6482577324540673028519637971436168042822), SC_(0.3517422675459326971480362028563831957178) }}, 
      {{ SC_(0.001122163725085556507110595703125), SC_(26.959133148193359375), SC_(0.12707412242889404296875), SC_(887.2857273413217591663071683655306756531), SC_(0.006170907249695033680550517863454817419816), SC_(0.9999930452343114190393974275086141619959), SC_(0.6954765688580960602572491385838004054883e-5) }}, 
      {{ SC_(0.001150955446064472198486328125), SC_(0.4535874426364898681640625), SC_(0.12707412242889404296875), SC_(866.855684243827267933107714455579701728), SC_(3.622050917060569664015394421000097533285), SC_(0.9958390079714202952539867788276064104906), SC_(0.00416099202857970474601322117239358950939) }}, 
      {{ SC_(0.00116972462274134159088134765625), SC_(4487.220703125), SC_(0.221111953258514404296875), SC_(845.9639896981215015789312874369381932995), SC_(0.1063779666391269484543054611732150401814e-489), SC_(1.0), SC_(0.1257476298454352107021172465546170244079e-492) }}, 
      {{ SC_(0.001238475437276065349578857421875), SC_(4809.8876953125), SC_(0.12707412242889404296875), SC_(798.4404102135347267583554734446658228025), SC_(0.2088573664802592741195276726261002951534e-286), SC_(1.0), SC_(0.2615816582033999333902290007214431749988e-289) }}, 
      {{ SC_(0.0012418846599757671356201171875), SC_(211065.625), SC_(0.632396042346954345703125), SC_(792.4934093883819497798461847348403401357), SC_(BOOST_MATH_SMALL_CONSTANT(0.3715857217027592182907565434204034076818e-91738)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4688817816031249765706087431377733825105e-91741)) }}, 
      {{ SC_(0.00128578185103833675384521484375), SC_(4738.4111328125), SC_(0.3082362115383148193359375), SC_(768.7497259471312992013909189845633725619), SC_(0.3083149516572614656818024343993762882429e-761), SC_(1.0), SC_(0.4010602426913450368564667294554493472522e-764) }}, 
      {{ SC_(0.001333879190497100353240966796875), SC_(46277.76171875), SC_(0.913384497165679931640625), SC_(738.4595682741381490604572839483104630489), SC_(BOOST_MATH_SMALL_CONSTANT(0.4760816758433140315470190574602976110403e-49170)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6446956560614004498713862685655419699461e-49173)) }}, 
      {{ SC_(0.001342063187621533870697021484375), SC_(0.0049741393886506557464599609375), SC_(0.12707412242889404296875), SC_(743.1959244878558453841456162423124931873), SC_(202.954935047346035345520597561537300498), SC_(0.7854941069893991630581498347410718336765), SC_(0.2145058930106008369418501652589281663235) }}, 
      {{ SC_(0.001386920106597244739532470703125), SC_(2158.757568359375), SC_(0.81474220752716064453125), SC_(712.8159877459091673089852757033234802738), SC_(0.1152262625557983164464601153211751891717e-1583), SC_(1.0), SC_(0.1616493801158566915366164602637515465764e-1586) }}, 
      {{ SC_(0.00139484903775155544281005859375), SC_(0.406741619110107421875), SC_(0.632396042346954345703125), SC_(716.9970687322662580276185822651018601599), SC_(1.861070463016669614611362269319717106569), SC_(0.997411074088832015598519013857371063956), SC_(0.00258892591116798440148098614262893604403) }}, 
      {{ SC_(0.001427047536708414554595947265625), SC_(3.0078976154327392578125), SC_(0.12707412242889404296875), SC_(698.4416522699784700368407029156265476697), SC_(0.8052389292592501254860055319140641451921), SC_(0.9988484197221410142868386632542163536932), SC_(0.00115158027785898571316133674578364630679) }}, 
      {{ SC_(0.00144447688944637775421142578125), SC_(0.3829285924439318478107452392578125e-4), SC_(0.905801355838775634765625), SC_(694.5531721715049157414316486281103107119), SC_(26112.26597142779218826565701649442464771), SC_(0.02590957056303132403077964326929276679704), SC_(0.974090429436968675969220356730707233203) }}, 
      {{ SC_(0.001469670678488910198211669921875), SC_(0.00022998542408458888530731201171875), SC_(0.81474220752716064453125), SC_(681.9029882237068745571574254101015948808), SC_(4346.620404150712400107828871749266738966), SC_(0.135607003292017896363216853396063797727), SC_(0.864392996707982103636783146603936202273) }}, 
      {{ SC_(0.001532684080302715301513671875), SC_(13060.16015625), SC_(0.9688708782196044921875), SC_(642.4739939673390065903879830508019993861), SC_(BOOST_MATH_SMALL_CONSTANT(0.2604575679676652000185387046751197315368e-19683)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4053978377542016060586253784468846562986e-19686)) }}, 
      {{ SC_(0.001546212588436901569366455078125), SC_(0.4388438761234283447265625), SC_(0.913384497165679931640625), SC_(647.6636500859710420001838060635189817527), SC_(0.8005234405450714384152145899891401006528), SC_(0.9987655086075278181507533227647548955077), SC_(0.001234491392472181849246677235245104492313) }}, 
      {{ SC_(0.001599461771547794342041015625), SC_(1780.4295654296875), SC_(0.9688708782196044921875), SC_(617.2018193338968435469389828476195862445), SC_(0.8972675710962492797338106626950543867371e-2686), SC_(1.0), SC_(0.1453766892755773137132663986139134796046e-2688) }}, 
      {{ SC_(0.001671708538196980953216552734375), SC_(485.198974609375), SC_(0.81474220752716064453125), SC_(591.4690747200591551150100900999853565481), SC_(0.1344651463466289510213986697745699545973e-357), SC_(1.0), SC_(0.2273409584605433023016572030651343918317e-360) }}, 
      {{ SC_(0.00169355445541441440582275390625), SC_(385561.5625), SC_(0.12707412242889404296875), SC_(577.1875596723301316935121874803525601185), SC_(BOOST_MATH_SMALL_CONSTANT(0.2819545160750931987103049585360311608808e-22761)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4884972161131799465362637889601809293001e-22764)) }}, 
      {{ SC_(0.001731689902953803539276123046875), SC_(198.0077056884765625), SC_(0.12707412242889404296875), SC_(571.6386685027893522584576324587060153788), SC_(0.787971814516226550741052893624149874176e-13), SC_(0.9999999999999998621556136886177991962018), SC_(0.1378443863113822008037981580291257606581e-15) }}, 
      {{ SC_(0.00173926516436040401458740234375), SC_(0.333752905135042965412139892578125e-4), SC_(0.3082362115383148193359375), SC_(574.1469349079614375311988072446327918744), SC_(29963.09162049822007245271293100907523344), SC_(0.01880153419459458396276801883523054498866), SC_(0.9811984658054054160372319811647694550113) }}, 
      {{ SC_(0.0017912392504513263702392578125), SC_(3.0554368495941162109375), SC_(0.9688708782196044921875), SC_(556.7542411201096097947875983675589227617), SC_(0.834060948041552202288100016197929847781e-5), SC_(0.9999999850192261873270525479907231427017), SC_(0.1498077381267294745200927685729828308752e-7) }}, 
      {{ SC_(0.001886987010948359966278076171875), SC_(0.00043248571455478668212890625), SC_(0.632396042346954345703125), SC_(530.4851810582123232839269017624533495092), SC_(2311.671464907864847076008216787250144871), SC_(0.1866488188858764188501071844747519471149), SC_(0.8133511811141235811498928155252480528851) }}, 
      {{ SC_(0.001926377532072365283966064453125), SC_(0.02606286108493804931640625), SC_(0.3082362115383148193359375), SC_(518.2900622257642140741244638447647938659), SC_(39.14263590794633588982379781196165727714), SC_(0.9297805169323647207529506425642268004599), SC_(0.07021948306763527924704935743577319954015) }}, 
      {{ SC_(0.001949134399183094501495361328125), SC_(0.0048745614476501941680908203125), SC_(0.913384497165679931640625), SC_(515.3799117627053428087257562370441485807), SC_(202.8038333085502702615250609769245964937), SC_(0.7176156732864668192398001864189021684779), SC_(0.2823843267135331807601998135810978315221) }}, 
      {{ SC_(0.00195972807705402374267578125), SC_(233.2021026611328125), SC_(0.1355634629726409912109375), SC_(504.2849401834236771885481932255122661676), SC_(0.5408755886612975324386952502978929980407e-16), SC_(0.9999999999999999998927440529029947374173), SC_(0.1072559470970052625826883045294568067446e-18) }}, 
      {{ SC_(0.00203225878067314624786376953125), SC_(0.16493140719830989837646484375e-4), SC_(0.913384497165679931640625), SC_(494.4157758283894540733235453123792528319), SC_(60628.91004687561681074785643773032239094), SC_(0.008088823197587536402023877265138455150887), SC_(0.9919111768024124635979761227348615448491) }}, 
      {{ SC_(0.00204748497344553470611572265625), SC_(0.23111911118030548095703125), SC_(0.81474220752716064453125), SC_(489.3574931248246500958149322733304524226), SC_(3.044086375985297430577533004169912549125), SC_(0.9938178785310328391491565365735272409495), SC_(0.006182121468967160850843463426472759050498) }}, 
      {{ SC_(0.00204884703271090984344482421875), SC_(0.0001168216476798988878726959228515625), SC_(0.1355634629726409912109375), SC_(486.2299335841715210383269237524336139823), SC_(8561.903211761318122131824929830851496484), SC_(0.05373814971260632868035612498442966583607), SC_(0.9462618502873936713196438750155703341639) }}, 
      {{ SC_(0.0020499289967119693756103515625), SC_(0.00172629184089601039886474609375), SC_(0.1355634629726409912109375), SC_(485.9720825815714437946579073706036129396), SC_(581.1198257959552771768495501811380052221), SC_(0.4554172689028012529844655878344171852393), SC_(0.5445827310971987470155344121655828147607) }}, 
      {{ SC_(0.002186703495681285858154296875), SC_(0.0003623119555413722991943359375), SC_(0.8350250720977783203125), SC_(458.9268578669144837934305949905467477431), SC_(2758.430776352358282603722698626439987806), SC_(0.1426409215394166573171341183639776550646), SC_(0.8573590784605833426828658816360223449354) }}, 
      {{ SC_(0.0022444091737270355224609375), SC_(0.1359292582492344081401824951171875e-4), SC_(0.12707412242889404296875), SC_(443.6283221817582637208491481509138411299), SC_(73569.59824929939717598364474498445218135), SC_(0.005993905991293420208700510136359953973266), SC_(0.9940060940087065797912994898636400460267) }}, 
      {{ SC_(0.0022751130163669586181640625), SC_(0.00026807599351741373538970947265625), SC_(0.905801355838775634765625), SC_(441.7974141085059302120749124862243598296), SC_(3728.022546291355237164806652953164350875), SC_(0.1059511965274731336562603304565300050724), SC_(0.8940488034725268663437396695434699949276) }}, 
      {{ SC_(0.00233665225096046924591064453125), SC_(3.87988376617431640625), SC_(0.905801355838775634765625), SC_(426.1693615792936534606453335142808945757), SC_(0.291370860355069138222087549900934467444e-4), SC_(0.9999999316302703332864236242486086404964), SC_(0.6836972966671357637575139135950356893863e-7) }}, 
      {{ SC_(0.0023414273746311664581298828125), SC_(0.15162646770477294921875), SC_(0.8350250720977783203125), SC_(428.3189578851107875838432428534412926218), SC_(5.137971347708949215771792948424410154265), SC_(0.9881465239076862789479164536000099760677), SC_(0.0118534760923137210520835463999900239323) }}, 
      {{ SC_(0.00246974662877619266510009765625), SC_(0.016062967479228973388671875), SC_(0.913384497165679931640625), SC_(407.1830493976956564929063020776892494275), SC_(59.94170443126037209213789060212744913934), SC_(0.8716794519238669414440334800056096450427), SC_(0.1283205480761330585559665199943903549573) }}, 
      {{ SC_(0.002563751302659511566162109375), SC_(346881.96875), SC_(0.1355634629726409912109375), SC_(376.9468505421772891867303011795099003985), SC_(BOOST_MATH_SMALL_CONSTANT(0.1526762108618082671304981685541855177048e-21950)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.405033788297231150127593256436643990287e-21953)) }}, 
      {{ SC_(0.00258206087164580821990966796875), SC_(0.035458743572235107421875), SC_(0.905801355838775634765625), SC_(389.4050536513380444462075705806268494174), SC_(26.02338763289848548972683845210583599059), SC_(0.9373577130336792246365028976677251178974), SC_(0.06264228696632077536349710233227488210262) }}, 
      {{ SC_(0.002597031183540821075439453125), SC_(443239.6875), SC_(0.81474220752716064453125), SC_(371.7146847560071370857526138227373442642), SC_(BOOST_MATH_SMALL_CONSTANT(0.8317828376957235925809370915610552337856e-324556)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2237691626957660763285628102201579159915e-324558)) }}, 
      {{ SC_(0.00263487943448126316070556640625), SC_(0.153494547703303396701812744140625e-4), SC_(0.9688708782196044921875), SC_(382.9576369498287876959530556221352464367), SC_(65145.45590204658276263249213907497657163), SC_(0.0058441463216857531973539577971001457265), SC_(0.9941558536783142468026460422028998542735) }}, 
      {{ SC_(0.00266719772480428218841552734375), SC_(346911.65625), SC_(0.905801355838775634765625), SC_(361.8277327374087690789383627225654385793), SC_(BOOST_MATH_SMALL_CONSTANT(0.4301033964228662860739993977341942313128e-355921)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1188696602023614342439206050153540352294e-355923)) }}, 
      {{ SC_(0.00269666709937155246734619140625), SC_(0.000412763678468763828277587890625), SC_(0.913384497165679931640625), SC_(373.1778411851096713440039645727112970052), SC_(2420.338967675658008549863316157254032731), SC_(0.1335871114150540680718697579133501564323), SC_(0.8664128885849459319281302420866498435677) }}, 
      {{ SC_(0.0027208295650780200958251953125), SC_(0.35322547773830592632293701171875e-4), SC_(0.221111953258514404296875), SC_(366.277193522541350098831770621351634747), SC_(28311.78187626887319957779121599661218375), SC_(0.01277203567477014095262479480170183963687), SC_(0.9872279643252298590473752051982981603631) }}, 
      {{ SC_(0.00272956606931984424591064453125), SC_(19772.78515625), SC_(0.8350250720977783203125), SC_(356.0396978354009539538064166896399808086), SC_(BOOST_MATH_SMALL_CONSTANT(0.9020241742296869709062755875164275443572e-15478)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2533493258514946679969092434416268747323e-15480)) }}, 
      {{ SC_(0.00275547988712787628173828125), SC_(0.3721626490005291998386383056640625e-4), SC_(0.8350250720977783203125), SC_(364.5307529094604948678995366003655869646), SC_(26868.34996677103086213252400124456788182), SC_(0.0133856846310799446265850708719477444261), SC_(0.9866143153689200553734149291280522555739) }}, 
      {{ SC_(0.0028151907026767730712890625), SC_(27477.31640625), SC_(0.81474220752716064453125), SC_(344.5821321686204748252909961370200655145), SC_(BOOST_MATH_SMALL_CONSTANT(0.1295810546972773481375166603817084134083e-20123)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3760527392461172556866211109744018769105e-20126)) }}, 
      {{ SC_(0.0028353952802717685699462890625), SC_(0.00185509095899760723114013671875), SC_(0.9688708782196044921875), SC_(356.1040183367038867687609162691621768696), SC_(535.6299297943333181238622417787029525964), SC_(0.3993388600748612956692602522580959228826), SC_(0.6006611399251387043307397477419040771174) }}, 
      {{ SC_(0.00288435374386608600616455078125), SC_(227.292877197265625), SC_(0.9688708782196044921875), SC_(340.7508230138447273892606683780027192615), SC_(0.1460864057696427910239499338148093806251e-344), SC_(1.0), SC_(0.4287191575285125317552473740924057149251e-347) }}, 
      {{ SC_(0.00291968858800828456878662109375), SC_(26.0607814788818359375), SC_(0.3082362115383148193359375), SC_(338.7074543170829748623319885868500550544), SC_(0.7770058183815534163008783765245794826343e-5), SC_(0.9999999770596781779301693123298207927688), SC_(0.2294032182206983068767017920723123773662e-7) }}, 
      {{ SC_(0.00295443576760590076446533203125), SC_(0.002377311699092388153076171875), SC_(0.8350250720977783203125), SC_(340.084913203444681640900691819322445843), SC_(419.0236545387330577006632320070756985302), SC_(0.4480056314144388715684755495621762315138), SC_(0.5519943685855611284315244504378237684862) }}, 
      {{ SC_(0.00295559107325971126556396484375), SC_(0.0265316255390644073486328125), SC_(0.632396042346954345703125), SC_(338.8474026621043894274825793489057322249), SC_(37.13778205569304263974776324417888043014), SC_(0.9012254110928134434981541262261287356423), SC_(0.09877458890718655650184587377387126435774) }}, 
      {{ SC_(0.0029596057720482349395751953125), SC_(224.0623931884765625), SC_(0.632396042346954345703125), SC_(331.9510420242185700459042733585735513605), SC_(0.2917302320480546763371900321801779746377e-99), SC_(1.0), SC_(0.8788351145671973717023128235686742000708e-102) }}, 
      {{ SC_(0.00297669577412307262420654296875), SC_(1.050155162811279296875), SC_(0.221111953258514404296875), SC_(334.4256122099075256327804045091328954065), SC_(1.437919940234428741218332239012603051541), SC_(0.9957187375151178019989677330902077333493), SC_(0.004281262484882198001032266909792266650686) }}, 
      {{ SC_(0.00300008966587483882904052734375), SC_(20.15772247314453125), SC_(0.221111953258514404296875), SC_(329.7875749931732244518608190723798908745), SC_(0.001263128645858334169415836204191040912488), SC_(0.9999961698866066784429226955393197869515), SC_(0.3830113393321557077304460680213048538951e-5) }}, 
      {{ SC_(0.00300188665278255939483642578125), SC_(229.01318359375), SC_(0.913384497165679931640625), SC_(327.1713098521674670683768608706996708035), SC_(0.2369095387200623579620960376756008067307e-245), SC_(1.0), SC_(0.7241146506003538662764887107768150912259e-248) }}, 
      {{ SC_(0.0030314330942928791046142578125), SC_(0.00014527700841426849365234375), SC_(0.9688708782196044921875), SC_(333.3089988677217538465464560575610901933), SC_(6879.96442361192272963018171634817815369), SC_(0.04620773112925241204262014864942842964082), SC_(0.9537922688707475879573798513505715703592) }}, 
      {{ SC_(0.00307437963783740997314453125), SC_(47442.203125), SC_(0.1355634629726409912109375), SC_(314.1223904268485420880421784898991700758), SC_(0.4661768136237720240915215589544819843799e-3005), SC_(1.0), SC_(0.1484061078837145980648748951899006466344e-3007) }}, 
      {{ SC_(0.003084543161094188690185546875), SC_(0.01759622059762477874755859375), SC_(0.221111953258514404296875), SC_(322.9349072653367105766494017705421467629), SC_(58.05908350144759010425199254432375827914), SC_(0.8476115505533341366790499411863212066513), SC_(0.1523884494466658633209500588136787933487) }}, 
      {{ SC_(0.00308659928850829601287841796875), SC_(3668.456298828125), SC_(0.632396042346954345703125), SC_(315.3170414058778588050051523922452679563), SC_(0.1778371467350701582251508756447844775794e-1597), SC_(1.0), SC_(0.5639947208123051889368889813375583407456e-1600) }}, 
      {{ SC_(0.0031075035221874713897705078125), SC_(463574.59375), SC_(0.8350250720977783203125), SC_(308.4646032735494517067363328496041155987), SC_(BOOST_MATH_SMALL_CONSTANT(0.1797844067909903582651263410877396839848e-362790)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.582836425583507789423876789417407161173e-362793)) }}, 
      {{ SC_(0.0031872228719294071197509765625), SC_(0.0190620534121990203857421875), SC_(0.1355634629726409912109375), SC_(311.9022616493719638657150320729604260007), SC_(54.2747395546762176378137852196514113164), SC_(0.8517800425034553034418633348183155902219), SC_(0.1482199574965446965581366651816844097781) }}, 
      {{ SC_(0.00330688594840466976165771484375), SC_(30.580783843994140625), SC_(0.9688708782196044921875), SC_(298.4468703302791730813913635675715736091), SC_(0.2803251409832261518456329622579776569536e-47), SC_(1.0), SC_(0.9392798814509315152409941206676649986713e-50) }}, 
      {{ SC_(0.003314100205898284912109375), SC_(0.00212650909088551998138427734375), SC_(0.905801355838775634765625), SC_(303.9906368171599635083999269918749891773), SC_(467.9957556260882890554503599970169589256), SC_(0.3937771958066054998125263900780659473505), SC_(0.6062228041933945001874736099219340526495) }}, 
      {{ SC_(0.003379584290087223052978515625), SC_(0.0002798224450089037418365478515625), SC_(0.12707412242889404296875), SC_(293.9730527985776003498995477729524104802), SC_(3575.610064802152442402176684014142116673), SC_(0.07597021277600845269066323095679638601062), SC_(0.9240297872239915473093367690432036139894) }}, 
      {{ SC_(0.00341137242503464221954345703125), SC_(0.00041894565219990909099578857421875), SC_(0.221111953258514404296875), SC_(291.8796448840601203765381207112725704486), SC_(2388.195676438229826944464885936141344267), SC_(0.1089072544200187450950507101442173577766), SC_(0.8910927455799812549049492898557826422234) }}, 
      {{ SC_(0.003439466468989849090576171875), SC_(47.370025634765625), SC_(0.81474220752716064453125), SC_(286.3543973001909882141877599135861019408), SC_(0.5317440631703433310038774219119571724429e-36), SC_(0.9999999999999999999999999999999999999981), SC_(0.1856943941436685863656041188548565270956e-38) }}, 
      {{ SC_(0.00347066554240882396697998046875), SC_(28.90100860595703125), SC_(0.8350250720977783203125), SC_(284.2348293012922564216437732094170115324), SC_(0.9928236151090230290780320975099581911317e-24), SC_(0.9999999999999999999999999965070304102084), SC_(0.349296958979160973885270952634051727603e-26) }}, 
      {{ SC_(0.0034962403587996959686279296875), SC_(0.01680609025061130523681640625), SC_(0.81474220752716064453125), SC_(287.4556524618854262257601300345525805849), SC_(58.03520462211484350304858132426032752853), SC_(0.8320210117513918373203343357281742508238), SC_(0.1679789882486081626796656642718257491762) }}, 
      {{ SC_(0.003511893562972545623779296875), SC_(306009.46875), SC_(0.9688708782196044921875), SC_(271.8425099127372893881440001524582982557), SC_(BOOST_MATH_SMALL_CONSTANT(0.2098346586792457546028748896273451865186e-461110)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.7718978858258910883202302938978234471983e-461113)) }}, 
      {{ SC_(0.003595965914428234100341796875), SC_(35727.2421875), SC_(0.3082362115383148193359375), SC_(267.2484485453116977496319346035879242014), SC_(BOOST_MATH_SMALL_CONSTANT(0.123220733069695734857336242073453867289e-5721)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4610718368634562424578929197535105547018e-5724)) }}, 
      {{ SC_(0.00361502938903868198394775390625), SC_(1.00639164447784423828125), SC_(0.1355634629726409912109375), SC_(274.6309212278353425080128018731142847078), SC_(1.981565426188746873660093456168307131028), SC_(0.9928363124522747586721581935139153545555), SC_(0.007163687547725241327841806486084645444541) }}, 
      {{ SC_(0.0036274394951760768890380859375), SC_(172327.484375), SC_(0.913384497165679931640625), SC_(263.3301748625356454777269224347300828409), SC_(BOOST_MATH_SMALL_CONSTANT(0.2140641630436078755985104352180763998712e-183086)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.8129116351946914128401510233881952058757e-183089)) }}, 
      {{ SC_(0.00363842095248401165008544921875), SC_(26888.734375), SC_(0.12707412242889404296875), SC_(264.2792579892578058278858335749316739876), SC_(0.2625023783883669955447912855947339013651e-1590), SC_(1.0), SC_(0.9932765075306703209197188121942593137623e-1593) }}, 
      {{ SC_(0.00371654215268790721893310546875), SC_(0.0219620727002620697021484375), SC_(0.8350250720977783203125), SC_(270.6235440389191393343680599718164707064), SC_(43.93534798390533253783375183114130395838), SC_(0.8603271148961277092097871984653584021336), SC_(0.1396728851038722907902128015346415978664) }}, 
      {{ SC_(0.00371891190297901630401611328125), SC_(0.21678431332111358642578125), SC_(0.905801355838775634765625), SC_(270.3822108409686784182500469315048040357), SC_(2.812915810668042565860828464590581196028), SC_(0.989703638402543992470686047492567774885), SC_(0.01029636159745600752931395250743222511495) }}, 
      {{ SC_(0.00372788752429187297821044921875), SC_(3138.884033203125), SC_(0.1355634629726409912109375), SC_(259.7600227700787864749175126258375772946), SC_(0.6019427502354860163606589328528886393532e-201), SC_(1.0), SC_(0.2317303270212149605305041501961105672503e-203) }}, 
      {{ SC_(0.0037949834950268268585205078125), SC_(1.261780261993408203125), SC_(0.913384497165679931640625), SC_(263.10507173624446951140135211654228472), SC_(0.03803578006705358232704019671473446473767), SC_(0.999855455913605125119517909762529317058), SC_(0.0001445440863948748804820902374706829420089) }}, 
      {{ SC_(0.0038448632694780826568603515625), SC_(0.000167022328241728246212005615234375), SC_(0.3082362115383148193359375), SC_(259.278541019298844017950173475044594277), SC_(5988.025599312784792519082812101177568898), SC_(0.04150246813588245656855822467726866784525), SC_(0.9584975318641175434314417753227313321547) }}, 
      {{ SC_(0.00385077786631882190704345703125), SC_(398.761688232421875), SC_(0.905801355838775634765625), SC_(253.2088150189338966705210920133960048251), SC_(0.2139385153421763922206172654998536689392e-411), SC_(1.0), SC_(0.8449094291056927239834881475182035789154e-414) }}, 
      {{ SC_(0.00386014836840331554412841796875), SC_(1.3947961330413818359375), SC_(0.632396042346954345703125), SC_(258.3194125890441856672733012197143596101), SC_(0.2291882381865878823082695525553500160164), SC_(0.9991135583892030507885087285816593214056), SC_(0.00088644161079694921149127141834067859442) }}, 
      {{ SC_(0.0038849101401865482330322265625), SC_(0.19230575859546661376953125), SC_(0.9688708782196044921875), SC_(259.6412577666067843549069947891696418952), SC_(2.681877591073786575325032818748540425787), SC_(0.9897764351305994568268041493922879072956), SC_(0.01022356486940054317319585060771209270443) }}, 
      {{ SC_(0.00388975837267935276031494140625), SC_(3818.894775390625), SC_(0.913384497165679931640625), SC_(248.4134047683841737841390343104715870606), SC_(0.176502403346789906860035669293567371707e-4060), SC_(1.0), SC_(0.7105188365795207961355131997781176641781e-4063) }}, 
      {{ SC_(0.003921323455870151519775390625), SC_(0.001402953988872468471527099609375), SC_(0.81474220752716064453125), SC_(256.4879484588884448427401600118455003358), SC_(711.3010211604746587801977490827802722136), SC_(0.2650246660279323199798192805411218471858), SC_(0.7349753339720676800201807194588781528142) }}, 
      {{ SC_(0.0039762970991432666778564453125), SC_(37.592052459716796875), SC_(0.1355634629726409912109375), SC_(247.3367820233605475301767964824970133042), SC_(0.0007107334231237934151205769345398144655273), SC_(0.9999971264630557944382455340963720076504), SC_(0.2873536944205561754465903627992349578265e-5) }}, 
      {{ SC_(0.0041162068955600261688232421875), SC_(2.1463463306427001953125), SC_(0.3082362115383148193359375), SC_(241.4220915300916300953691932959895955989), SC_(0.4343201155688253213834961378974985709908), SC_(0.9982042232719257406771480462898741165473), SC_(0.001795776728074259322851953710125883452674) }}, 
      {{ SC_(0.0041847354732453823089599609375), SC_(0.3331385552883148193359375), SC_(0.3082362115383148193359375), SC_(238.0257599315494126786014321742223711994), SC_(3.490743363693061305290567907883857475748), SC_(0.9855465638328417124616511464889327225367), SC_(0.01445343616715828753834885351106727746328) }}, 
      {{ SC_(0.0042013223282992839813232421875), SC_(28909.28515625), SC_(0.632396042346954345703125), SC_(227.4180185431250825583311019533951629295), SC_(BOOST_MATH_SMALL_CONSTANT(0.154444039219970105225464675248506843513e-12568)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6791196238950741512067428162651220919711e-12571)) }}, 
      {{ SC_(0.0042040585540235042572021484375), SC_(0.004051725380122661590576171875), SC_(0.221111953258514404296875), SC_(236.6073390385921364322672554231554985574), SC_(248.0529807507079020399926946536561498738), SC_(0.4881920994511252602745898242826427410612), SC_(0.5118079005488747397254101757173572589388) }}, 
      {{ SC_(0.0042134574614465236663818359375), SC_(258792.734375), SC_(0.3082362115383148193359375), SC_(224.6488701427391990771119838504413297139), SC_(BOOST_MATH_SMALL_CONSTANT(0.2206824276115503855571645372383063588368e-41422)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.9823438126856877424580544291022948955495e-41425)) }}, 
      {{ SC_(0.0042219576425850391387939453125), SC_(29.536075592041015625), SC_(0.913384497165679931640625), SC_(232.9471648952734556361109119453173366286), SC_(0.1542533613322604488019413446604890225816e-32), SC_(0.9999999999999999999999999999999999933782), SC_(0.6621817501046147319767982459195303414945e-35) }}, 
      {{ SC_(0.004270188510417938232421875), SC_(1927.554443359375), SC_(0.905801355838775634765625), SC_(226.18399773531284549516645893385992798), SC_(0.1489236244284971002826315484787575770326e-1980), SC_(1.0), SC_(0.6584180398242491837038013433931894830401e-1983) }}, 
      {{ SC_(0.0042725945822894573211669921875), SC_(1262.0977783203125), SC_(0.8350250720977783203125), SC_(226.4617086503311279179717694502512535438), SC_(0.1913088965087741355042123383782213408177e-990), SC_(1.0), SC_(0.8447737043447164123758604932012104601162e-993) }}, 
      {{ SC_(0.0043012551032006740570068359375), SC_(30236.775390625), SC_(0.905801355838775634765625), SC_(221.8509296407934992626076247854197759141), SC_(BOOST_MATH_SMALL_CONSTANT(0.9568037088440380747994881168949410528981e-31026)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4312822625504576421690187492172329467755e-31028)) }}, 
      {{ SC_(0.004325519315898418426513671875), SC_(175633.671875), SC_(0.221111953258514404296875), SC_(218.8761487276503563560687159943414565662), SC_(BOOST_MATH_SMALL_CONSTANT(0.5895749519418378855182936121514390889886e-19065)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2693646408569859866252719376075168160127e-19067)) }}, 
      {{ SC_(0.00443776883184909820556640625), SC_(34.436649322509765625), SC_(0.905801355838775634765625), SC_(221.2773318443523398630204647384902441298), SC_(0.1492888594965177397706744196539651601244e-36), SC_(0.9999999999999999999999999999999999999993), SC_(0.6746685629846997747027306963470039808401e-39) }}, 
      {{ SC_(0.00454067252576351165771484375), SC_(0.483688652515411376953125), SC_(0.1355634629726409912109375), SC_(218.3153168844210370175417520624042488847), SC_(3.382264759538559604494704955469664186651), SC_(0.9847437904624039309446363558333050327663), SC_(0.01525620953759606905536364416669496723367) }}, 
      {{ SC_(0.0045460476540029048919677734375), SC_(300.9449462890625), SC_(0.221111953258514404296875), SC_(213.7813022064452868909326801570865976177), SC_(0.3227929715492706277605621917742581687685e-34), SC_(0.999999999999999999999999999999999999849), SC_(0.1509921439422959745170413628430055157838e-36) }}, 
      {{ SC_(0.004563690163195133209228515625), SC_(2.421394824981689453125), SC_(0.8350250720977783203125), SC_(217.8799586513934820214849474815116953363), SC_(0.005961477363609150534306763126278020763766), SC_(0.9999726394557294648206678550132970370657), SC_(0.2736054427053517933214498670296293425429e-4) }}, 
      {{ SC_(0.0046148826368153095245361328125), SC_(2.5390207767486572265625), SC_(0.81474220752716064453125), SC_(215.3911637954872479757273454765446788177), SC_(0.006290815635725090143045244499021439610959), SC_(0.9999707943826584093865704269334329868276), SC_(0.292056173415906134295730665670131724332e-4) }}, 
      {{ SC_(0.00461888872087001800537109375), SC_(20.5790615081787109375), SC_(0.632396042346954345703125), SC_(212.9582988909358024374609617762690102154), SC_(0.8498984519843349366341711897318345725924e-10), SC_(0.9999999999996009085081867311109206470199), SC_(0.3990914918132688890793529801004722615195e-12) }}, 
      {{ SC_(0.00461952388286590576171875), SC_(0.3906617712345905601978302001953125e-4), SC_(0.632396042346954345703125), SC_(217.0097822764051787582601420839069626597), SC_(25597.04549299603115155470379908437383166), SC_(0.008406652111118751809788600453438090257441), SC_(0.9915933478888812481902113995465619097426) }}, 
      {{ SC_(0.004653147421777248382568359375), SC_(0.10722650587558746337890625), SC_(0.221111953258514404296875), SC_(213.6235551301594634201895396322779399163), SC_(10.4405283220568910078873938202949912294), SC_(0.9534038291135427516095651839362691856796), SC_(0.04659617088645724839043481606373081432043) }}, 
      {{ SC_(0.0046935188584029674530029296875), SC_(0.3578202085918746888637542724609375e-4), SC_(0.1355634629726409912109375), SC_(211.2144483144425736194095915990015833595), SC_(27948.83373135622455004399362042513700411), SC_(0.007500500246548680973867880011181586541863), SC_(0.9924994997534513190261321199888184134581) }}, 
      {{ SC_(0.0047154170460999011993408203125), SC_(0.00197655311785638332366943359375), SC_(0.632396042346954345703125), SC_(212.6050063852692393122510509364342610233), SC_(505.3856142451954157399817267250359150906), SC_(0.2961111193884143564019037971113840518997), SC_(0.7038888806115856435980962028886159481003) }}, 
      {{ SC_(0.0048523540608584880828857421875), SC_(0.0342371277511119842529296875), SC_(0.12707412242889404296875), SC_(204.1620299300672943703945362707973541147), SC_(31.06905262950431718965636174688662260339), SC_(0.8679211425146752585460500302016400495225), SC_(0.1320788574853247414539499697983599504775) }}, 
      {{ SC_(0.0048918980173766613006591796875), SC_(15688.6884765625), SC_(0.221111953258514404296875), SC_(194.4377154348503515109659413448110836721), SC_(0.6953322571227953874419265151726638154282e-1706), SC_(1.0), SC_(0.357611822154832009703914255565629106317e-1708) }}, 
      {{ SC_(0.004914722405374050140380859375), SC_(192.96014404296875), SC_(0.8350250720977783203125), SC_(197.7200234660610376061244398566376729857), SC_(0.609344481862498887765102701970213350289e-153), SC_(1.0), SC_(0.3081855196962860240597711506298949774366e-155) }}, 
      {{ SC_(0.004918993450701236724853515625), SC_(0.241855232161469757556915283203125e-4), SC_(0.81474220752716064453125), SC_(204.7676834084662972959975720485250736829), SC_(41345.56627906620690305086905485724581902), SC_(0.004928183816606576629273815334288997756918), SC_(0.9950718161833934233707261846657110022431) }}, 
      {{ SC_(0.0049519282765686511993408203125), SC_(0.0407697297632694244384765625), SC_(0.9688708782196044921875), SC_(205.0778900575319925822637629141708215302), SC_(21.31886806987151533840023202831889807089), SC_(0.9058340400003676655664382570795025221403), SC_(0.09416595999963233443356174292049747785975) }}, 
      {{ SC_(0.01061948575079441070556640625), SC_(0.4264468225301243364810943603515625e-4), SC_(0.221111953258514404296875), SC_(92.9129363125494581195718877372374381726), SC_(23450.81880370592316039081125925465981978), SC_(0.003946398019589249610370640587827130087418), SC_(0.9960536019804107503896293594121728699126) }}, 
      {{ SC_(0.01090050302445888519287109375), SC_(11.95932865142822265625), SC_(0.81474220752716064453125), SC_(88.77976801465830495290548601285141963018), SC_(0.1762122080462366385145816418820675567568e-9), SC_(0.9999999999980151760701065051908548696109), SC_(0.1984823929893494809145130389058690398462e-11) }}, 
      {{ SC_(0.011304032988846302032470703125), SC_(0.01250172965228557586669921875), SC_(0.12707412242889404296875), SC_(86.55452525913597512910645788840115136069), SC_(81.85992294295806815712701650060841387305), SC_(0.5139376471742628519296990138709223195204), SC_(0.4860623528257371480703009861290776804796) }}, 
      {{ SC_(0.0113441534340381622314453125), SC_(311.568267822265625), SC_(0.905801355838775634765625), SC_(82.06357322946025559981749842463637118366), SC_(0.782803795459850358127785916565440098974e-322), SC_(1.0), SC_(0.9538992328192080307496883720122144674341e-324) }}, 
      {{ SC_(0.01210707984864711761474609375), SC_(0.00047863091458566486835479736328125), SC_(0.221111953258514404296875), SC_(81.34339868853391223538286201827231300729), SC_(2090.524928665945974602738186159976498435), SC_(0.03745319072248596285475895371946612740082), SC_(0.9625468092775140371452410462805338725992) }}, 
      {{ SC_(0.0127197094261646270751953125), SC_(461789.9375), SC_(0.9688708782196044921875), SC_(66.12132788164299594672500954172495150149), SC_(BOOST_MATH_SMALL_CONSTANT(0.9364481593250719291878011517099647747747e-695846)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1416257339842466089474115368029481978682e-695847)) }}, 
      {{ SC_(0.01275224424898624420166015625), SC_(218.467041015625), SC_(0.913384497165679931640625), SC_(72.68661693692461976099341225710200800222), SC_(0.3971318105817831708831072346376447547305e-234), SC_(1.0), SC_(0.5463616650729567846142053580440966568318e-236) }}, 
      {{ SC_(0.013632931746542453765869140625), SC_(12715.3935546875), SC_(0.221111953258514404296875), SC_(63.98900337441679303826080321259474819805), SC_(0.4021743596727233669711945434813477418928e-1383), SC_(1.0), SC_(0.6285054282209921266545872655753585971277e-1385) }}, 
      {{ SC_(0.01376917399466037750244140625), SC_(410.58148193359375), SC_(0.1355634629726409912109375), SC_(66.33313730298259857653322130873174127017), SC_(0.1818894929654292743718248106882972544963e-27), SC_(0.9999999999999999999999999999972579392388), SC_(0.2742060761194402421981008649460048394723e-29) }}, 
      {{ SC_(0.01422516815364360809326171875), SC_(3725.08544921875), SC_(0.913384497165679931640625), SC_(62.03677192816864098479063759683213635154), SC_(0.8328815474964475797096485621577023047519e-3961), SC_(1.0), SC_(0.1342561067588796270972616746712860724678e-3962) }}, 
      {{ SC_(0.014248653315007686614990234375), SC_(0.29711585739278234541416168212890625e-4), SC_(0.81474220752716064453125), SC_(71.64299306976453152515129584000996156553), SC_(33655.42020612590340963780773633804059336), SC_(0.00212419897477089238116375040187716497925), SC_(0.9978758010252291076188362495981228350207) }}, 
      {{ SC_(0.01431076042354106903076171875), SC_(2.4992504119873046875), SC_(0.9688708782196044921875), SC_(68.61710492016970322126199614371659115584), SC_(0.7012636309613990157780059258802336781301e-4), SC_(0.9999989780056808141162183538611423252437), SC_(0.1021994319185883781646138857674756280835e-5) }}, 
      {{ SC_(0.014824463985860347747802734375), SC_(0.38286387920379638671875), SC_(0.81474220752716064453125), SC_(68.10963743604857726757757409915022637996), SC_(1.447378573532694812428383633394595771425), SC_(0.9791914797878430736779535679470712537945), SC_(0.02080852021215692632204643205292874620548) }}, 
      {{ SC_(0.01512057520449161529541015625), SC_(0.0045269015245139598846435546875), SC_(0.905801355838775634765625), SC_(68.35700996380495368521452643346801127428), SC_(218.6478262852280572973442797881192642363), SC_(0.2381737215901540929460045012772197673396), SC_(0.7618262784098459070539954987227802326604) }}, 
      {{ SC_(0.0161462686955928802490234375), SC_(31081.712890625), SC_(0.632396042346954345703125), SC_(51.93206210105480255973064811400686795094), SC_(BOOST_MATH_SMALL_CONSTANT(0.9435669288959732592362662699464427373729e-13513)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1816925596098769745444433468593524929453e-13514)) }}, 
      {{ SC_(0.01625619828701019287109375), SC_(0.0234319157898426055908203125), SC_(0.913384497165679931640625), SC_(63.74680880160488943079204944589970820775), SC_(40.38159452420545542553293035595221543857), SC_(0.6121942406256405639246777015747062202937), SC_(0.3878057593743594360753222984252937797063) }}, 
      {{ SC_(0.01645939052104949951171875), SC_(29.5273590087890625), SC_(0.913384497165679931640625), SC_(56.94779131906162858369277796095164780241), SC_(0.1574555210382990903562280640914644107276e-32), SC_(0.9999999999999999999999999999999999723509), SC_(0.2764910058690817070374202714873696186641e-34) }}, 
      {{ SC_(0.01668673567473888397216796875), SC_(0.16884712749742902815341949462890625e-4), SC_(0.632396042346954345703125), SC_(60.45175821265212983145085274083071185428), SC_(59224.61994087775188585379099402587411431), SC_(0.001019679262926144457952937015509715257382), SC_(0.9989803207370738555420470629844902847426) }}, 
      {{ SC_(0.0168448425829410552978515625), SC_(0.0039571900852024555206298828125), SC_(0.9688708782196044921875), SC_(62.74700478699485422094099347314541954573), SC_(249.2892070628920184326937272208527854942), SC_(0.2010888557292860971820994695281544363273), SC_(0.7989111442707139028179005304718455636727) }}, 
      {{ SC_(0.01712529920041561126708984375), SC_(0.002250707708299160003662109375), SC_(0.8350250720977783203125), SC_(59.98405449676909698960547676830592952825), SC_(442.6823527134120492207603234213023338597), SC_(0.1193317349963428452237957406008792060563), SC_(0.8806682650036571547762042593991207939437) }}, 
      {{ SC_(0.017150647938251495361328125), SC_(27.16506195068359375), SC_(0.9688708782196044921875), SC_(54.58403599527897563165979391919408203096), SC_(0.4423706914893721503126455993719907104759e-42), SC_(1.0), SC_(0.8104396888636692338298325380387679082135e-44) }}, 
      {{ SC_(0.01761732995510101318359375), SC_(0.028260417282581329345703125), SC_(0.632396042346954345703125), SC_(57.25006238845797785100260049527653607354), SC_(34.82441408608533263441290810702178160147), SC_(0.6217799392460997235296555763573093809535), SC_(0.3782200607539002764703444236426906190465) }}, 
      {{ SC_(0.017636947333812713623046875), SC_(0.3383924067020416259765625), SC_(0.221111953258514404296875), SC_(55.36493320206828451037648768654366556785), SC_(3.822088511133688011920517500844828920013), SC_(0.9354235371116646113774367970695081980511), SC_(0.0645764628883353886225632029304918019489) }}, 
      {{ SC_(0.01792473532259464263916015625), SC_(0.4457229442778043448925018310546875e-4), SC_(0.905801355838775634765625), SC_(58.02477425424591799257680088122965272887), SC_(22433.19647744917052681554311666242096818), SC_(0.00257988544085178561421642780244658967612), SC_(0.9974201145591482143857835721975534103239) }}, 
      {{ SC_(0.019047506153583526611328125), SC_(0.291198313236236572265625), SC_(0.905801355838775634765625), SC_(53.75288387375856676369870717987725563769), SC_(1.764049560934113725136984906846004723634), SC_(0.9682250179936675964372918419102480226956), SC_(0.03177498200633240356270815808975197730438) }}, 
      {{ SC_(0.01962828077375888824462890625), SC_(266305.03125), SC_(0.632396042346954345703125), SC_(39.43145046709021020530909908321394585192), SC_(BOOST_MATH_SMALL_CONSTANT(0.2111290448479440101170488734157419233744e-115746)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5354331183534674278805278800610250514981e-115748)) }}, 
      {{ SC_(0.02007224597036838531494140625), SC_(3.6188943386077880859375), SC_(0.12707412242889404296875), SC_(47.50213838563133912147182892348047784988), SC_(0.6410275772846972029953195540588357296081), SC_(0.9866849725300892924651401131277058855528), SC_(0.01331502746991070753485988687229411444717) }}, 
      {{ SC_(0.02042911946773529052734375), SC_(36.3684234619140625), SC_(0.3082362115383148193359375), SC_(44.97928431689277334640281609218909306214), SC_(0.1246898290004505980070430865032688069324e-6), SC_(0.9999999972278387584757369003545065374324), SC_(0.2772161241524263099645493462567561193467e-8) }}, 
      {{ SC_(0.02061123587191104888916015625), SC_(4.98589992523193359375), SC_(0.3082362115383148193359375), SC_(46.41751188885766131910543919599525010038), SC_(0.07736459768351619881850816549081161689262), SC_(0.9983360618732709001455824216111224311826), SC_(0.001663938126729099854417578388877568817449) }}, 
      {{ SC_(0.02065885998308658599853515625), SC_(12765.6015625), SC_(0.3082362115383148193359375), SC_(39.35863216607121843622315888513904687239), SC_(0.2289782883998965937992770650127766430927e-2046), SC_(1.0), SC_(0.5817739992430057648123382299173062141794e-2048) }}, 
      {{ SC_(0.020764775574207305908203125), SC_(0.0004887509276159107685089111328125), SC_(0.632396042346954345703125), SC_(48.67736451129363365158999240294060392189), SC_(2045.478609018724104419608670317961662147), SC_(0.02324438347791284508002932992282891416589), SC_(0.9767556165220871549199706700771710858341) }}, 
      {{ SC_(0.02124021016061305999755859375), SC_(20481.953125), SC_(0.81474220752716064453125), SC_(37.67922399458307854876617196441433654621), SC_(BOOST_MATH_SMALL_CONSTANT(0.2558564892703794491473251035369658526678e-15001)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6790386375981693220899301732852035737115e-15003)) }}, 
      {{ SC_(0.021562583744525909423828125), SC_(122176.578125), SC_(0.8350250720977783203125), SC_(35.59341411600131762873790863135769487504), SC_(BOOST_MATH_SMALL_CONSTANT(0.6198904429834281812244715470706494453801e-95618)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1741587477287690806826379876287995341719e-95619)) }}, 
      {{ SC_(0.02161762677133083343505859375), SC_(1.312267780303955078125), SC_(0.1355634629726409912109375), SC_(44.26210844968925347790095601894818044278), SC_(1.58203800395258243471926218101715395344), SC_(0.9654909486524662707028901677517790456839), SC_(0.03450905134753372929710983224822095431611) }}, 
      {{ SC_(0.021679364144802093505859375), SC_(0.42985266190953552722930908203125e-4), SC_(0.12707412242889404296875), SC_(44.2365702512397635991673589750004369616), SC_(23265.6402816351731290526307170355289763), SC_(0.001897760787511831199676029655530990490207), SC_(0.9981022392124881688003239703444690095098) }}, 
      {{ SC_(0.02205819822847843170166015625), SC_(0.00224195979535579681396484375), SC_(0.12707412242889404296875), SC_(43.44472254336863189641421901977429954758), SC_(447.8889491974596897304687119460459534591), SC_(0.08842203382772658579099103813157515805609), SC_(0.9115779661722734142090089618684248419439) }}, 
      {{ SC_(0.02250875532627105712890625), SC_(29.670085906982421875), SC_(0.905801355838775634765625), SC_(40.66369422324434322610774979974376657438), SC_(0.1342966680454402292807913080954109891919e-31), SC_(0.9999999999999999999999999999999996697382), SC_(0.3302618480951320742636123042544696331085e-33) }}, 
      {{ SC_(0.02278398908674716949462890625), SC_(230.1311492919921875), SC_(0.9688708782196044921875), SC_(38.28669745795363063686160624927465162117), SC_(0.7623360207191257305904221059661317168012e-349), SC_(1.0), SC_(0.1991125041684051020850888239397734803256e-350) }}, 
      {{ SC_(0.02300582826137542724609375), SC_(3731.865966796875), SC_(0.905801355838775634765625), SC_(35.51469950912618008711178645980789988599), SC_(0.5523234982753809639645003441940015963004e-3832), SC_(1.0), SC_(0.155519687878381427976758710314007925128e-3833) }}, 
      {{ SC_(0.0235797353088855743408203125), SC_(0.34650889574550092220306396484375e-4), SC_(0.9688708782196044921875), SC_(45.80964464398935306471823683562476436923), SC_(28855.84941603052538944927117338284389429), SC_(0.001585017820181850667013514727458714383511), SC_(0.9984149821798181493329864852725412856165) }}, 
      {{ SC_(0.023755080997943878173828125), SC_(1.81503880023956298828125), SC_(0.905801355838775634765625), SC_(41.23445462424267729334167588399942511541), SC_(0.008049732102934764351924599921733673304299), SC_(0.9998048195125741382216162986900024161379), SC_(0.00019518048742586177838370130999758386209) }}, 
      {{ SC_(0.02475666068494319915771484375), SC_(0.0353721342980861663818359375), SC_(0.81474220752716064453125), SC_(41.7533041603007802475917960987564209577), SC_(26.81601355910983436259292369982846630827), SC_(0.6089210969133100694014430010843864971209), SC_(0.3910789030866899305985569989156135028791) }}, 
      {{ SC_(0.02489638887345790863037109375), SC_(0.17812990336096845567226409912109375e-4), SC_(0.8350250720977783203125), SC_(41.75255294558606456802864973785461196712), SC_(56137.17917069929805316307765371517816686), SC_(0.0007432066019157325946480829806667978089399), SC_(0.9992567933980842674053519170193332021911) }}, 
      {{ SC_(0.02505088783800601959228515625), SC_(0.169265210628509521484375), SC_(0.1355634629726409912109375), SC_(38.0813106107053677764135408369988393382), SC_(7.465549004765010494606672988391137544503), SC_(0.8360908069668699337443128615509513371332), SC_(0.1639091930331300662556871384490486628668) }}, 
      {{ SC_(0.0253847651183605194091796875), SC_(0.107284702360630035400390625), SC_(0.913384497165679931640625), SC_(41.28510997753819000995352603934561537102), SC_(7.230832848311232497799102024589147916793), SC_(0.8509596551742445855613258931629667335774), SC_(0.1490403448257554144386741068370332664226) }}, 
      {{ SC_(0.0263047702610492706298828125), SC_(3064.19140625), SC_(0.221111953258514404296875), SC_(30.33265925103147848556863665020339880076), SC_(0.4074684034074821389767004725537986882465e-335), SC_(1.0), SC_(0.1343332281008714906881753724347451500761e-336) }}, 
      {{ SC_(0.02670976333320140838623046875), SC_(0.00032671095686964690685272216796875), SC_(0.913384497165679931640625), SC_(39.75315117721609457251393032241304406899), SC_(3058.452225296471443258692889496221797513), SC_(0.01283102517318019070223831788427815322336), SC_(0.9871689748268198092977616821157218467766) }}, 
      {{ SC_(0.0269134230911731719970703125), SC_(0.00032558594830334186553955078125), SC_(0.81474220752716064453125), SC_(38.59887132599509921018498050670079074153), SC_(3069.898994508502396477530541679847586528), SC_(0.01241721017416010624038834588073304322311), SC_(0.9875827898258398937596116541192669567769) }}, 
      {{ SC_(0.02691542543470859527587890625), SC_(257.891082763671875), SC_(0.12707412242889404296875), SC_(31.52275844618623133260222388650836586129), SC_(0.1691134000097981295856664529447969224758e-16), SC_(0.9999999999999999994635196653284692209244), SC_(0.5364803346715307790755666192552011395549e-18) }}, 
      {{ SC_(0.02693811617791652679443359375), SC_(37925.875), SC_(0.1355634629726409912109375), SC_(27.52884559586118116367855498911481924404), SC_(0.6508712684201788275544647969373724417315e-2403), SC_(1.0), SC_(0.2364324599641163083991414614689709885369e-2404) }}, 
      {{ SC_(0.027010373771190643310546875), SC_(40.798625946044921875), SC_(0.8350250720977783203125), SC_(33.00592499252189951355614137798818549215), SC_(0.3429917551747212316183718711491129418791e-33), SC_(0.9999999999999999999999999999999999896082), SC_(0.1039182374838554998371391484307285180498e-34) }}, 
      {{ SC_(0.027130119502544403076171875), SC_(0.390757262706756591796875), SC_(0.3082362115383148193359375), SC_(35.90552418547066004606923895937043297202), SC_(2.98380695457424005202119856899103204052), SC_(0.9232744079904791320765841232160411392892), SC_(0.07672559200952086792341587678395886071084) }}, 
      {{ SC_(0.027266047894954681396484375), SC_(0.2437297735013999044895172119140625e-4), SC_(0.1355634629726409912109375), SC_(34.86521525249479186979358108920793470328), SC_(41030.81205322347091227833069937233911148), SC_(0.0008490110859381600035235893021351087779298), SC_(0.9991509889140618399964764106978648912221) }}, 
      {{ SC_(0.0276034064590930938720703125), SC_(29335.0078125), SC_(0.8350250720977783203125), SC_(26.85785623035577696227462871088924256017), SC_(BOOST_MATH_SMALL_CONSTANT(0.3615083784310516986138575354063124550047e-22961)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1346006082281656919355265001294210112337e-22962)) }}, 
      {{ SC_(0.02829697541892528533935546875), SC_(13777.6298828125), SC_(0.913384497165679931640625), SC_(26.56580055953003226290677804181116986292), SC_(BOOST_MATH_SMALL_CONSTANT(0.3054028669242327526394177881070317637973e-14641)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1149609123353426035890781795044026282057e-14642)) }}, 
      {{ SC_(0.02842903696000576019287109375), SC_(0.03119052015244960784912109375), SC_(0.8350250720977783203125), SC_(36.6730480246053194934000096056202450598), SC_(30.46935560382069638858935070257014865759), SC_(0.5461980215596435075766995065932255193652), SC_(0.4538019784403564924233004934067744806348) }}, 
      {{ SC_(0.02883697114884853363037109375), SC_(399.402069091796875), SC_(0.3082362115383148193359375), SC_(28.71526545288497625508742226734804914204), SC_(0.9363671800038887851786384477722789146146e-66), SC_(1.0), SC_(0.3260868967205781097054901857257900727036e-67) }}, 
      {{ SC_(0.0292808823287487030029296875), SC_(0.4510292112827301025390625), SC_(0.632396042346954345703125), SC_(34.16626096491055239111143376362071217439), SC_(1.614162501040144007071966262357462703595), SC_(0.9548869928111328675235876376981510710952), SC_(0.04511300718886713247641236230184892890484) }}, 
      {{ SC_(0.0295875072479248046875), SC_(0.18167886082665063440799713134765625e-4), SC_(0.913384497165679931640625), SC_(36.10874453604145969175645989765609155776), SC_(55039.81854938211499539380858745297466486), SC_(0.0006556175503563209739788378491170180687729), SC_(0.9993443824496436790260211621508829819312) }}, 
      {{ SC_(0.03072208352386951446533203125), SC_(487934.875), SC_(0.12707412242889404296875), SC_(21.40039309248696402801946483893072349666), SC_(BOOST_MATH_SMALL_CONSTANT(0.9540553262538226105022025664601283221644e-28804)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4458120568770126061414102572984363082005e-28805)) }}, 
      {{ SC_(0.0310857109725475311279296875), SC_(15232.21875), SC_(0.905801355838775634765625), SC_(23.44028489227366049390242332703067336678), SC_(BOOST_MATH_SMALL_CONSTANT(0.1916791109501658525873364793532490055884e-15631)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.8177337085751322722517495985834528569095e-15633)) }}, 
      {{ SC_(0.03123457171022891998291015625), SC_(3524.479736328125), SC_(0.12707412242889404296875), SC_(24.38302966802117338623323410267777880059), SC_(0.1976861395081482593400168565772118123666e-210), SC_(1.0), SC_(0.8107529794273988391895895895799958760346e-212) }}, 
      {{ SC_(0.0312533564865589141845703125), SC_(3994.4365234375), SC_(0.8350250720977783203125), SC_(24.26925582076123268914474286266556107924), SC_(0.3162589788262145279359701440899680574297e-3129), SC_(1.0), SC_(0.13031259844221078673429625968931804599e-3130) }}, 
      {{ SC_(0.0315650589764118194580078125), SC_(0.001970894634723663330078125), SC_(0.3082362115383148193359375), SC_(30.869710220720063489222966688220281495), SC_(508.1408365649057706132322210921735073939), SC_(0.05727106900748177964273230228606521142893), SC_(0.9427289309925182203572676977139347885711) }}, 
      {{ SC_(0.0319148339331150054931640625), SC_(0.00033934754901565611362457275390625), SC_(0.8350250720977783203125), SC_(32.9088421478168554145764192207301637524), SC_(2945.204071549599643271354333806045230095), SC_(0.01105023318506736567008856604466052756282), SC_(0.9889497668149326343299114339553394724372) }}, 
      {{ SC_(0.0324479900300502777099609375), SC_(0.015262463130056858062744140625), SC_(0.1355634629726409912109375), SC_(29.01393962639045401353088948311780874504), SC_(67.24901888102986901421625241689103177541), SC_(0.3014029495483867428234722841210975246223), SC_(0.6985970504516132571765277158789024753777) }}, 
      {{ SC_(0.0333194546401500701904296875), SC_(0.3989324867725372314453125), SC_(0.9688708782196044921875), SC_(31.34340863495666809511625976130148105884), SC_(0.6335111109710499809653389054053267939414), SC_(0.980188488572238792923820822114921805459), SC_(0.01981151142776120707617917788507819454097) }}, 
      {{ SC_(0.0333627723157405853271484375), SC_(4.62843418121337890625), SC_(0.913384497165679931640625), SC_(28.06261703304876393724814064222820742171), SC_(0.2807984906692262501897155307645772687697e-5), SC_(0.9999998999386043570181515856232760129102), SC_(0.1000613956429818484143767239870898099748e-6) }}, 
      {{ SC_(0.0335802994668483734130859375), SC_(0.19390623271465301513671875), SC_(0.8350250720977783203125), SC_(30.87620652731886714384399478171794302635), SC_(3.739844900374224433053749987064023915851), SC_(0.8919621173955640285328737176113056024516), SC_(0.1080378826044359714671262823886943975484) }}, 
      {{ SC_(0.033774249255657196044921875), SC_(33.470600128173828125), SC_(0.632396042346954345703125), SC_(25.82658250378490186184478364556565883422), SC_(0.1299351033510292982287995011527472612391e-15), SC_(0.9999999999999999949689393348118270508162), SC_(0.5031060665188172949183785434091737800436e-17) }}, 
      {{ SC_(0.033940948545932769775390625), SC_(489.9017333984375), SC_(0.221111953258514404296875), SC_(23.43592890949145426826226872729950125799), SC_(0.5935926099240656620059970318616507256013e-55), SC_(1.0), SC_(0.2532831586136375035323482066483628637581e-56) }}, 
      {{ SC_(0.0344383455812931060791015625), SC_(1315.3695068359375), SC_(0.9688708782196044921875), SC_(22.25010120703504429376162251599304630768), SC_(0.7109679311034694236733626555971539797882e-1985), SC_(1.0), SC_(0.3195346953651947210669700263684425060838e-1986) }}, 
      {{ SC_(0.0346836335957050323486328125), SC_(2.891076564788818359375), SC_(0.221111953258514404296875), SC_(26.99757576962473828694663602310193086957), SC_(0.4344915503078346109866161249402339894598), SC_(0.9841611809551033656235948199023818862418), SC_(0.01583881904489663437640518009761811375819) }}, 
      {{ SC_(0.0355083644390106201171875), SC_(207270.546875), SC_(0.221111953258514404296875), SC_(17.88254290202323034987146975286489955936), SC_(BOOST_MATH_SMALL_CONSTANT(0.1938863043447469635408367737841101764686e-22498)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1084221105505138606850733967746475364952e-22499)) }}, 
      {{ SC_(0.035541228950023651123046875), SC_(353.3453369140625), SC_(0.8350250720977783203125), SC_(22.400439232555436086445547455037739456), SC_(0.1012431808330590013913997249824269720048e-278), SC_(1.0), SC_(0.4519696233720199536282891802287731883289e-280) }}, 
      {{ SC_(0.0357905812561511993408203125), SC_(0.19347016513347625732421875), SC_(0.12707412242889404296875), SC_(26.0493907087657493086432836468187365539), SC_(6.737193859417837128916013021795419009459), SC_(0.7945137028406528766417351867693694311322), SC_(0.2054862971593471233582648132306305688678) }}, 
      {{ SC_(0.03617782890796661376953125), SC_(4978.021484375), SC_(0.1355634629726409912109375), SC_(19.91580571160478764140047890799065498089), SC_(0.1566588315502458236212370977217589413572e-317), SC_(1.0), SC_(0.7866055424459273613263907314587584981944e-319) }}, 
      {{ SC_(0.036653555929660797119140625), SC_(0.0039511634968221187591552734375), SC_(0.221111953258514404296875), SC_(26.04199226253237026297526089983526355229), SC_(254.2656285318020713174260723752206115004), SC_(0.09290504549514099127804895601282910382889), SC_(0.9070949545048590087219510439871708961711) }}, 
      {{ SC_(0.0366611182689666748046875), SC_(0.001741903950460255146026611328125), SC_(0.81474220752716064453125), SC_(28.70284461580296960605174706910055535579), SC_(572.5970242286072277899701318800904722238), SC_(0.04773465969810480016227634131591638953376), SC_(0.9522653403018951998377236586840836104662) }}, 
      {{ SC_(0.036767013370990753173828125), SC_(0.010332576930522918701171875), SC_(0.3082362115383148193359375), SC_(26.38411238247533242854934500580607347367), SC_(97.52057268574274732010177365298373642887), SC_(0.2129387792555952004858471262124048281948), SC_(0.7870612207444047995141528737875951718052) }}, 
      {{ SC_(0.036872327327728271484375), SC_(345497.65625), SC_(0.905801355838775634765625), SC_(16.60802591265856682616182018546405467996), SC_(BOOST_MATH_SMALL_CONSTANT(0.2161420343251689268071125522978270155822e-354470)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1301431220434370746446718784751414005802e-354471)) }}, 
      {{ SC_(0.0370448939502239227294921875), SC_(289887.34375), SC_(0.81474220752716064453125), SC_(16.6004189281411556087941964361545494995), SC_(BOOST_MATH_SMALL_CONSTANT(0.1965954913814918759506831311707010927856e-212267)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1184280301795406580843211345157086411144e-212268)) }}, 
      {{ SC_(0.0378056205809116363525390625), SC_(296247.6875), SC_(0.913384497165679931640625), SC_(16.09194444987159321324019514614444639933), SC_(BOOST_MATH_SMALL_CONSTANT(0.535410774324226603415118024790685936299e-314740)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3327197505510273811696486116295989619144e-314741)) }}, 
      {{ SC_(0.0378379710018634796142578125), SC_(353.800811767578125), SC_(0.632396042346954345703125), SC_(20.73345001001782217833384545958108093874), SC_(0.7467809176821582887634024422866665464842e-156), SC_(1.0), SC_(0.360181695434833407410588393958961418318e-157) }}, 
      {{ SC_(0.0379242189228534698486328125), SC_(0.004291436634957790374755859375), SC_(0.632396042346954345703125), SC_(26.86430465925230384256659574614086409308), SC_(232.4589024982946008459886804740086725718), SC_(0.1035939087508331029327591205661608644132), SC_(0.8964060912491668970672408794338391355868) }}, 
      {{ SC_(0.0379955135285854339599609375), SC_(128.872100830078125), SC_(0.81474220752716064453125), SC_(21.43553637109974649031979315943776616494), SC_(0.4088158636111406672945129705764095533446e-96), SC_(1.0), SC_(0.1907187469133371818859225613657519503967e-97) }}, 
      {{ SC_(0.0380439497530460357666015625), SC_(0.001482595689594745635986328125), SC_(0.1355634629726409912109375), SC_(24.49118255913105834378826695309291059458), SC_(676.223725445557921341528840576305732538), SC_(0.03495170757658144544142413554020212652152), SC_(0.9650482924234185545585758644597978734785) }}, 
      {{ SC_(0.0387343578040599822998046875), SC_(1982.2989501953125), SC_(0.632396042346954345703125), SC_(18.83692714161079381516027785361741964716), SC_(0.2226356599665315206895105877923420739305e-864), SC_(1.0), SC_(0.1181910713423789226468088759994045271419e-865) }}, 
      {{ SC_(0.0392099507153034210205078125), SC_(4.091603755950927734375), SC_(0.8350250720977783203125), SC_(23.73509913430908231696756865120043602659), SC_(0.0001761198658116325421932783950127400579367), SC_(0.9999925798262743696598251232422708253738), SC_(0.7420173725630340174876757729174626194011e-5) }}, 
      {{ SC_(0.039514325559139251708984375), SC_(0.000166679310495965182781219482421875), SC_(0.3082362115383148193359375), SC_(24.49682757816677769454386394981019028476), SC_(6000.291861957064830651127334417602907534), SC_(0.004066006102540424336654745459914299462271), SC_(0.9959339938974595756633452545400857005377) }}, 
      {{ SC_(0.0411520861089229583740234375), SC_(28357.451171875), SC_(0.12707412242889404296875), SC_(15.58292342054710209148389058345104151075), SC_(0.4730480254556644651265694901617344274591e-1677), SC_(1.0), SC_(0.3035682154684272872897738970830059105764e-1678) }}, 
      {{ SC_(0.04271042346954345703125), SC_(23.0694675445556640625), SC_(0.221111953258514404296875), SC_(20.0243389160179008623187016052591843516), SC_(0.0005118878203582350885059024060507572610437), SC_(0.9999744373715753168509299843454723780082), SC_(0.255626284246831490700156545276219918113e-4) }}, 
      {{ SC_(0.04279924929141998291015625), SC_(3897.815673828125), SC_(0.3082362115383148193359375), SC_(16.02484096656381736207710622754696913186), SC_(0.1211378961687267592427950158904546549886e-626), SC_(1.0), SC_(0.7559382113150679011913355365533542467539e-628) }}, 
      {{ SC_(0.04297505319118499755859375), SC_(3.1283900737762451171875), SC_(0.632396042346954345703125), SC_(21.77594742125604909733896625284641283204), SC_(0.01924256699181671126161716640584811112549), SC_(0.9991171186393790349390521464440413266787), SC_(0.0008828813606209650609478535559586733212865) }}, 
      {{ SC_(0.0442209132015705108642578125), SC_(0.02011293359100818634033203125), SC_(0.9688708782196044921875), SC_(25.83640189819377370074430241522822555899), SC_(46.39556092643460168783413044868798538564), SC_(0.3576865543709762649319662277323093277561), SC_(0.6423134456290237350680337722676906722439) }}, 
      {{ SC_(0.0450148619711399078369140625), SC_(14815.267578125), SC_(0.9688708782196044921875), SC_(14.07099423884325284155797173281234217818), SC_(BOOST_MATH_SMALL_CONSTANT(0.5085882135107557429438835316270112271216e-22328)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3614444046226585064269677541879272600456e-22329)) }}, 
      {{ SC_(0.04518614709377288818359375), SC_(23.06797027587890625), SC_(0.1355634629726409912109375), SC_(18.75026000874948524362588152802072705895), SC_(0.008353637632983779823472639118059362970688), SC_(0.9995546772383899090161089691183323492346), SC_(0.0004453227616100909838910308816676507654412) }}, 
      {{ SC_(0.04527465999126434326171875), SC_(0.013598117046058177947998046875), SC_(0.221111953258514404296875), SC_(20.8493594462655209724110517945580848197), SC_(74.6848226228261945497192248886865267748), SC_(0.2182397859562659966619080829557459720467), SC_(0.7817602140437340033380919170442540279533) }}, 
      {{ SC_(0.04625232517719268798828125), SC_(43.923553466796875), SC_(0.12707412242889404296875), SC_(17.71130196100934836543988893586324943749), SC_(0.0003682241231851475227643177992042953399886), SC_(0.9999792100846878776631728586938955375579), SC_(0.2078991531212233682714130610446244208255e-4) }}, 
      {{ SC_(0.0468132793903350830078125), SC_(0.00025698760873638093471527099609375), SC_(0.1355634629726409912109375), SC_(19.58067472138623149146094712368030565282), SC_(3892.944114011192267804353768770152496087), SC_(0.005004613588078808404357317268552392675046), SC_(0.994995386411921191595642682731447607325) }}, 
      {{ SC_(0.04770947992801666259765625), SC_(0.00015248873387463390827178955078125), SC_(0.905801355838775634765625), SC_(23.15197505544387007118889541618334627984), SC_(6555.593650161884398802067838570577463821), SC_(0.003519208124828362954315511675485902971432), SC_(0.9964807918751716370456844883245140970286) }}, 
      {{ SC_(0.0477449037134647369384765625), SC_(402512.65625), SC_(0.1355634629726409912109375), SC_(11.0230169005882531820742576201897796517), SC_(BOOST_MATH_SMALL_CONSTANT(0.3159717752762467343985344247714084898607e-25470)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2866472746307633958676851942917575263438e-25471)) }}, 
      {{ SC_(0.0480652190744876861572265625), SC_(0.00045122022856958210468292236328125), SC_(0.12707412242889404296875), SC_(18.95862272936696329395560578384428525019), SC_(2217.982046632910544999553191852062947007), SC_(0.008475246120305827497705967588029104133415), SC_(0.9915247538796941725022940324119708958666) }}, 
      {{ SC_(0.0483077578246593475341796875), SC_(389962.375), SC_(0.3082362115383148193359375), SC_(10.82924224047048652720135547852684809634), SC_(BOOST_MATH_SMALL_CONSTANT(0.2941052044145309049967634408530288794614e-62415)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2715842880634954105001337483314292410207e-62416)) }}, 
      {{ SC_(0.04874597489833831787109375), SC_(2329.3564453125), SC_(0.81474220752716064453125), SC_(13.69417520929604126788814613120980079318), SC_(0.1281744195556668348251261203821222329825e-1708), SC_(1.0), SC_(0.9359776517877321012858673788534608515729e-1710) }}, 
      {{ SC_(0.04926551878452301025390625), SC_(0.0299147777259349822998046875), SC_(0.905801355838775634765625), SC_(22.37091475699371165610887016365649486326), SC_(31.23253834199579451650658398141050636854), SC_(0.4173409260720823242228709969532622686722), SC_(0.5826590739279176757771290030467377313278) }}, 
      {{ SC_(0.049306534230709075927734375), SC_(4.597743988037109375), SC_(0.81474220752716064453125), SC_(18.41612001008947971188429212832608416516), SC_(0.0001095078525055740520832753964766298238141), SC_(0.9999940537311180398689483683191900603588), SC_(0.594626888196013105163168080993964120443e-5) }}, 
      {{ SC_(0.0493220984935760498046875), SC_(0.00032175212982110679149627685546875), SC_(0.9688708782196044921875), SC_(23.63373039587093996815976313738828491512), SC_(3104.544868471964579602419702569680899307), SC_(0.007555109035150539698573666668347833707795), SC_(0.9924448909648494603014263333316521662922) }}, 
      {{ SC_(0.0493625514209270477294921875), SC_(0.2173553002648986876010894775390625e-4), SC_(0.3082362115383148193359375), SC_(19.44767790303436984325765780490289747006), SC_(46008.35189346145740678178976723999122557), SC_(0.0004225202613234079575884535722356063059018), SC_(0.9995774797386765920424115464277643936941) }}, 
      {{ SC_(0.0499632172286510467529296875), SC_(0.00369685166515409946441650390625), SC_(0.913384497165679931640625), SC_(22.28013181892201261410756042288477568693), SC_(268.1500837137282408596227603218865830633), SC_(0.07671423504630933758613085717404390220721), SC_(0.9232857649536906624138691428259560977928) }}, 
      {{ SC_(0.10046042501926422119140625), SC_(0.000209066798561252653598785400390625), SC_(0.1355634629726409912109375), SC_(8.252310036018254646221510609655865173472), SC_(4784.707689769652824754566087774246223879), SC_(0.001721756500440821898886461164041864193657), SC_(0.9982782434995591781011135388359581358063) }}, 
      {{ SC_(0.102686129510402679443359375), SC_(0.19225277355872094631195068359375e-4), SC_(0.905801355838775634765625), SC_(11.85483541062642757513981710923389935021), SC_(52012.58051877421631706953337214645263474), SC_(0.0002278705252621800009748306170952863296859), SC_(0.9997721294747378199990251693829047136703) }}, 
      {{ SC_(0.11430509388446807861328125), SC_(0.00042258042958565056324005126953125), SC_(0.9688708782196044921875), SC_(12.01329309340862835147249771396542240502), SC_(2362.974282807676113435814374883535855015), SC_(0.005058255131650830565829250827858602156065), SC_(0.9949417448683491694341707491721413978439) }}, 
      {{ SC_(0.11778163909912109375), SC_(206355.0), SC_(0.632396042346954345703125), SC_(1.897249074578603579880390834557515140444), SC_(BOOST_MATH_SMALL_CONSTANT(0.7720868413993223061762249642195049797765e-89691)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4069507012782756923257716167260406359152e-89691)) }}, 
      {{ SC_(0.1254365444183349609375), SC_(233.073516845703125), SC_(0.9688708782196044921875), SC_(3.789367389662553925323948561477155058364), SC_(0.2764197193237842253458928385156242796673e-353), SC_(1.0), SC_(0.7294613873488778123589525827176549048439e-354) }}, 
      {{ SC_(0.13644538819789886474609375), SC_(0.0134486891329288482666015625), SC_(0.632396042346954345703125), SC_(7.721137222106381932723137407676225170978), SC_(73.74216216797055061410884528445009217529), SC_(0.09478056106142560478448745147440359632423), SC_(0.9052194389385743952155125485255964036758) }}, 
      {{ SC_(0.13752801716327667236328125), SC_(43384.8671875), SC_(0.1355634629726409912109375), SC_(1.569318408615190130813475372041265160589), SC_(0.1930636483362086764296738057805909991263e-2748), SC_(1.0), SC_(0.1230238855775440545065233522181695629778e-2748) }}, 
      {{ SC_(0.14231930673122406005859375), SC_(444.787506103515625), SC_(0.905801355838775634765625), SC_(2.760657048444986089263952960805643166214), SC_(0.113891648338950546373638826013652230207e-458), SC_(1.0), SC_(0.4125526870608685823643230535346118797679e-459) }}, 
      {{ SC_(0.14387898147106170654296875), SC_(282.2655029296875), SC_(0.913384497165679931640625), SC_(2.886375857208684041526189982555401262593), SC_(0.50442835692798726440979965186144662879e-302), SC_(1.0), SC_(0.1747618404124966519382734754762036539193e-302) }}, 
      {{ SC_(0.14768581092357635498046875), SC_(43.79425048828125), SC_(0.81474220752716064453125), SC_(3.623526026665682414439047242280013644154), SC_(0.231940011285713602857259877385433485333e-33), SC_(0.9999999999999999999999999999999999359905), SC_(0.640094784965961822139983865020879451094e-34) }}, 
      {{ SC_(0.14775849878787994384765625), SC_(0.1444317400455474853515625), SC_(0.905801355838775634765625), SC_(8.325590106245668883338953744594313315262), SC_(4.974516500743730333812021534374216102687), SC_(0.625979200938919568234413720041948646786), SC_(0.374020799061080431765586279958051353214) }}, 
      {{ SC_(0.1555496752262115478515625), SC_(4926.16162109375), SC_(0.9688708782196044921875), SC_(1.595231945629570077638907300201011887714), SC_(BOOST_MATH_SMALL_CONSTANT(0.2603423318770011435431033758868982902314e-7426)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1632002998625100364874964811622052002368e-7426)) }}, 
      {{ SC_(0.15999889373779296875), SC_(346211.125), SC_(0.1355634629726409912109375), SC_(0.7550675518068729220256451449394596675709), SC_(BOOST_MATH_SMALL_CONSTANT(0.3091551967012326406929489089422086414851e-21908)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4094404480253796921531614155425924978421e-21908)) }}, 
      {{ SC_(0.16108848154544830322265625), SC_(0.003161008469760417938232421875), SC_(0.3082362115383148193359375), SC_(5.400843106601757600521129512394784044383), SC_(316.9198894651245599485293307741302659594), SC_(0.01675611451832346282500337182117707686699), SC_(0.983243885481676537174996628178822923133) }}, 
      {{ SC_(0.17034976184368133544921875), SC_(0.00294548436067998409271240234375), SC_(0.12707412242889404296875), SC_(4.212699542106957969935444584135593452544), SC_(340.9066466328349110274891723020101988374), SC_(0.01220650070416953653716545402022420581463), SC_(0.9877934992958304634628345459797757941854) }}, 
      {{ SC_(0.17546479403972625732421875), SC_(0.045397706329822540283203125), SC_(0.221111953258514404296875), SC_(4.530100682241991525935733248800172574816), SC_(22.88470823467187025751407777819916982283), SC_(0.1652428326592164242163300383340972184638), SC_(0.8347571673407835757836699616659027815362) }}, 
      {{ SC_(0.17669810354709625244140625), SC_(0.00317144836299121379852294921875), SC_(0.8350250720977783203125), SC_(7.048025451352586558010174583728968443586), SC_(313.6624804004123949336417680912565679026), SC_(0.02197628491350464695692964330727035845612), SC_(0.9780237150864953530430703566927296415439) }}, 
      {{ SC_(0.17681133747100830078125), SC_(4087.143798828125), SC_(0.913384497165679931640625), SC_(1.202099211642655226877190345273283008415), SC_(0.1665453529724523109284743849683439358816e-4345), SC_(1.0), SC_(0.1385454306594793744983238528650532335039e-4345) }}, 
      {{ SC_(0.1835739612579345703125), SC_(0.3351508080959320068359375), SC_(0.632396042346954345703125), SC_(5.494981111922799259837646030510157973708), SC_(2.335174821650200283511122928454257536868), SC_(0.7017716069180974316541039963689408680056), SC_(0.2982283930819025683458960036310591319944) }}, 
      {{ SC_(0.1874706447124481201171875), SC_(406.947998046875), SC_(0.8350250720977783203125), SC_(1.593950370369185073634567181631377851823), SC_(0.9631902208098806186131874884536400924932e-321), SC_(1.0), SC_(0.6042786768742303756895366312326667059292e-321) }}, 
      {{ SC_(0.18961600959300994873046875), SC_(0.4673106013797223567962646484375e-4), SC_(0.1355634629726409912109375), SC_(3.694836563621182491215455880578456693337), SC_(21400.34772910220757893165895074561807598), SC_(0.0001726233047932758551768571330726338834291), SC_(0.9998273766952067241448231428669273661166) }}, 
      {{ SC_(0.19449223577976226806640625), SC_(0.23129458725452423095703125), SC_(0.8350250720977783203125), SC_(6.015660159425561670344251989489929530053), SC_(2.927582419949657985277532568191640439983), SC_(0.6726486624995270265101377241589246335436), SC_(0.3273513375004729734898622758410753664564) }}, 
      {{ SC_(0.1971141397953033447265625), SC_(147358.625), SC_(0.8350250720977783203125), SC_(0.4464878109850020374312930918610764954226), SC_(BOOST_MATH_SMALL_CONSTANT(0.4777993900862828666875629809326430706783e-115325)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1070128631355476389128871358053279403756e-115324)) }}, 
      {{ SC_(0.1971398293972015380859375), SC_(0.00232790899462997913360595703125), SC_(0.913384497165679931640625), SC_(7.15265893822281085848080465485692234334), SC_(427.2025689850565811808513235055773807162), SC_(0.01646730251739065592434230421607959606613), SC_(0.9835326974826093440756576957839204039339) }}, 
      {{ SC_(0.2019160687923431396484375), SC_(0.4740857184515334665775299072265625e-4), SC_(0.12707412242889404296875), SC_(3.340312551347237514184812055786484456526), SC_(21094.55348210925371156573532264319825814), SC_(0.0001583244556948426274976933012534521009447), SC_(0.9998416755443051573725023066987465478991) }}, 
      {{ SC_(0.2025763988494873046875), SC_(2.372247219085693359375), SC_(0.913384497165679931640625), SC_(3.937203199022278252105354314395874943421), SC_(0.0013379829260142046180656790494772087335), SC_(0.9996602846424085529684782517403873628343), SC_(0.0003397153575914470315217482596126371656571) }}, 
      {{ SC_(0.20485810935497283935546875), SC_(480505.75), SC_(0.3082362115383148193359375), SC_(0.306848523885785014481646694440861253385), SC_(BOOST_MATH_SMALL_CONSTANT(0.3460117627697644993182072834296248764001e-76906)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1127630527232247021191770437558198209557e-76905)) }}, 
      {{ SC_(0.2059116065502166748046875), SC_(24.2244358062744140625), SC_(0.3082362115383148193359375), SC_(2.317098126687310653694466350546003052727), SC_(0.1309204045483426382329857359196673939813e-4), SC_(0.9999943498437411983238274705951394985482), SC_(0.5650156258801676172529404860501451803968e-5) }}, 
      {{ SC_(0.20762462913990020751953125), SC_(0.0442828945815563201904296875), SC_(0.12707412242889404296875), SC_(3.208757799440129059173937558212400209151), SC_(23.84071978676539155823574004573949164694), SC_(0.1186254998535168040048487450476216883436), SC_(0.8813745001464831959951512549523783116564) }}, 
      {{ SC_(0.21499927341938018798828125), SC_(0.0337234772741794586181640625), SC_(0.3082362115383148193359375), SC_(3.842065222121155731997225444566201482496), SC_(30.11620543051094229321847610375530049551), SC_(0.1131407797947849299248107466223786193189), SC_(0.8868592202052150700751892533776213806811) }}, 
      {{ SC_(0.22722963988780975341796875), SC_(39.870731353759765625), SC_(0.632396042346954345703125), SC_(1.739737749891743852937605228835489594363), SC_(0.1658976312618398321080302337113242415999e-18), SC_(0.9999999999999999999046421615716719923605), SC_(0.9535783842832800763946160877438449731154e-19) }}, 
      {{ SC_(0.22898872196674346923828125), SC_(0.000221865411731414496898651123046875), SC_(0.8350250720977783203125), SC_(5.706591552815756273351959094649631661446), SC_(4505.572905024566783440534660711790531952), SC_(0.001264960762716039444330752487957723028592), SC_(0.9987350392372839605556692475120422769714) }}, 
      {{ SC_(0.23644983768463134765625), SC_(0.00389209692366421222686767578125), SC_(0.632396042346954345703125), SC_(4.552530094026749191113284955376849779478), SC_(256.2697676925452286102734645995453186659), SC_(0.01745452797809501155414428231193259547456), SC_(0.9825454720219049884458557176880674045254) }}, 
      {{ SC_(0.2377849519252777099609375), SC_(0.19049095499212853610515594482421875e-4), SC_(0.632396042346954345703125), SC_(4.53164976289766096174798459680844480505), SC_(52495.26961808240492975835642795738264917), SC_(0.863174650848282912012987122430403011656e-4), SC_(0.9999136825349151717087987012877569596988) }}, 
      {{ SC_(0.2390850484371185302734375), SC_(394272.125), SC_(0.12707412242889404296875), SC_(0.1745849851757016911719459583019216641561), SC_(BOOST_MATH_SMALL_CONSTANT(0.1280755209981353127528079468932698099317e-23275)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.733599861805072047806121153366488358174e-23275)) }}, 
      {{ SC_(0.24008722603321075439453125), SC_(1.274954319000244140625), SC_(0.3082362115383148193359375), SC_(3.084743633676409259964114272723441763204), SC_(0.7624524502911317466523552818168956347006), SC_(0.8018160671694100742859808331924415867861), SC_(0.1981839328305899257140191668075584132139) }}, 
      {{ SC_(0.2436912953853607177734375), SC_(193457.484375), SC_(0.9688708782196044921875), SC_(0.1917873480216332357378284754442872026183), SC_(BOOST_MATH_SMALL_CONSTANT(0.3774012622666596906907009409478240642198e-291513)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1967811047807437070384744017057333226547e-291512)) }}, 
      {{ SC_(0.2463264763355255126953125), SC_(1483.4622802734375), SC_(0.221111953258514404296875), SC_(0.6095848838071273412043230795119430431948), SC_(0.2134084169223695609854362659824172215945e-163), SC_(1.0), SC_(0.3500881051864993172066533730543461613597e-163) }}, 
      {{ SC_(0.254708468914031982421875), SC_(0.493781044497154653072357177734375e-4), SC_(0.9688708782196044921875), SC_(7.016415499405410658790600405881980952071), SC_(20248.4454227091885406449218658109898752), SC_(0.0003463962241616282470308411565585885253707), SC_(0.9996536037758383717529691588434414114746) }}, 
      {{ SC_(0.2573825418949127197265625), SC_(44778.4140625), SC_(0.8350250720977783203125), SC_(0.2233180923540966297674867063805482002216), SC_(BOOST_MATH_SMALL_CONSTANT(0.4206110747125570277793308692053653655775e-35047)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1883461703790795356476903325019216889077e-35046)) }}, 
      {{ SC_(0.2618319988250732421875), SC_(4353.03955078125), SC_(0.12707412242889404296875), SC_(0.3849666563346155953776492505211989550318), SC_(0.1242248199936297604165680007483887186758e-259), SC_(1.0), SC_(0.3226898172855072286657292908581418011501e-259) }}, 
      {{ SC_(0.2664634287357330322265625), SC_(2.7337188720703125), SC_(0.81474220752716064453125), SC_(2.685258724408317040116055739832085019844), SC_(0.004059762694853894582711241957150810346059), SC_(0.998490412082346218227504068762203917039), SC_(0.001509587917653781772495931237796082961007) }}, 
      {{ SC_(0.269739627838134765625), SC_(0.0001012004868243820965290069580078125), SC_(0.3082362115383148193359375), SC_(2.914655976096051160435051331811495380652), SC_(9881.794762389233106512649854575334706675), SC_(0.0002948651146669780977537873667881976598873), SC_(0.9997051348853330219022462126332118023401) }}, 
      {{ SC_(0.270291507244110107421875), SC_(0.04600088298320770263671875), SC_(0.913384497165679931640625), SC_(5.535625010893188303466524796023891090522), SC_(19.48123447500466816913415535740350134829), SC_(0.2212757766023209693546547405899639082743), SC_(0.7787242233976790306453452594100360917257) }}, 
      {{ SC_(0.276960909366607666015625), SC_(225226.28125), SC_(0.905801355838775634765625), SC_(0.1071421706233882854378819962409694800002), SC_(BOOST_MATH_SMALL_CONSTANT(0.3720988495311030205840463975177304151268e-231077)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3472944848569987764982518079554275520801e-231076)) }}, 
      {{ SC_(0.279349148273468017578125), SC_(3217.235107421875), SC_(0.1355634629726409912109375), SC_(0.337829357005361094121756192427915189105), SC_(0.3740045494430662948332189772006883872255e-206), SC_(1.0), SC_(0.1107081257704703052228323192318580079036e-205) }}, 
      {{ SC_(0.280055105686187744140625), SC_(0.489566028118133544921875), SC_(0.9688708782196044921875), SC_(4.472960873464681839529880962499036608606), SC_(0.3764727172031023899329394807885403670498), SC_(0.9223676930172662165205455538273957814328), SC_(0.07763230698273378347945444617260421856722) }}, 
      {{ SC_(0.28349018096923828125), SC_(3.132917881011962890625), SC_(0.12707412242889404296875), SC_(1.852567089757968504381052032680922751599), SC_(0.521631353273639512227923011132635751048), SC_(0.7802915949150527830116740656433407989041), SC_(0.2197084050849472169883259343566592010959) }}, 
      {{ SC_(0.284366548061370849609375), SC_(0.0002785695833154022693634033203125), SC_(0.632396042346954345703125), SC_(3.809895588278766434269061035796907648468), SC_(3589.083623868482170377362674424433691414), SC_(0.001060397578622039381085502303428714769925), SC_(0.9989396024213779606189144976965712852301) }}, 
      {{ SC_(0.2849796712398529052734375), SC_(0.00028760623536072671413421630859375), SC_(0.221111953258514404296875), SC_(2.410907419249654348893363288596069260864), SC_(3477.68303903531433253507573406687628692), SC_(0.0006927707861754217420005022817828058786344), SC_(0.9993072292138245782579994977182171941214) }}, 
      {{ SC_(0.28854286670684814453125), SC_(0.00019903402426280081272125244140625), SC_(0.913384497165679931640625), SC_(5.452222757212524014274110277597719693085), SC_(5021.88490729987368580974885081972739547), SC_(0.001084515045672024219473733057218408210046), SC_(0.99891548495432797578052626694278159179) }}, 
      {{ SC_(0.2893944084644317626953125), SC_(0.0018625170923769474029541015625), SC_(0.221111953258514404296875), SC_(2.359684838555847552855883355484859523753), SC_(537.6057681887136579927596914914783486131), SC_(0.004370066316884680407864702286930947053297), SC_(0.9956299336831153195921352977130690529467) }}, 
      {{ SC_(0.291785299777984619140625), SC_(38.487705230712890625), SC_(0.913384497165679931640625), SC_(1.064563216983953577984974879133551224242), SC_(0.3566983853901848960591959182819036301658e-42), SC_(1.0), SC_(0.335065480094980116296341064211507092312e-42) }}, 
      {{ SC_(0.293941318988800048828125), SC_(43703.57421875), SC_(0.81474220752716064453125), SC_(0.1321828888145803590804606716816193088251), SC_(BOOST_MATH_SMALL_CONSTANT(0.4340711794287861382533364616958556756036e-32005)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3283868156624113372810977808405361315335e-32004)) }}, 
      {{ SC_(0.308021008968353271484375), SC_(34557.2734375), SC_(0.9688708782196044921875), SC_(0.1163863903955997243376526669904137951038), SC_(BOOST_MATH_SMALL_CONSTANT(0.2669396375425675399509007637464316628519e-52076)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2293564020975598998320985646863906582949e-52075)) }}, 
      {{ SC_(0.3089981377124786376953125), SC_(432.827178955078125), SC_(0.632396042346954345703125), SC_(0.4445432103700716281916733518106100388495), SC_(0.2429571357222311862579075445645067748202e-190), SC_(1.0), SC_(0.5465321031896430517328038990414424129403e-190) }}, 
      {{ SC_(0.310161769390106201171875), SC_(24687.779296875), SC_(0.221111953258514404296875), SC_(0.1254101149018303501185970214060483310229), SC_(0.65963006765331415770328699169886780425e-2683), SC_(1.0), SC_(0.5259783616095601808094442123983332365233e-2682) }}, 
      {{ SC_(0.3121376931667327880859375), SC_(12025.0380859375), SC_(0.3082362115383148193359375), SC_(0.152852811831666880331296851749833541672), SC_(0.5730315156314877826157933739045864554133e-1928), SC_(1.0), SC_(0.3748910528793893482878921750841203613866e-1927) }}, 
      {{ SC_(0.31629574298858642578125), SC_(20.7625732421875), SC_(0.1355634629726409912109375), SC_(1.082099366658232990430534484429843123385), SC_(0.00786505660129495803292747614316984755223), SC_(0.9927841162211748313922185635959633515603), SC_(0.007215883778825168607781436404036648439728) }}, 
      {{ SC_(0.3175543844699859619140625), SC_(241.035797119140625), SC_(0.221111953258514404296875), SC_(0.4940177692306408909226479665789455678924), SC_(0.7990090143787921274483214524613937643683e-28), SC_(0.9999999999999999999999999998382631022315), SC_(0.1617368977685012592096455157994299357983e-27) }}, 
      {{ SC_(0.3178864419460296630859375), SC_(403.9595947265625), SC_(0.12707412242889404296875), SC_(0.4179356240859554717243168635510952728132), SC_(0.1435894358068937495908945092539077631277e-25), SC_(0.9999999999999999999999999656431690596057), SC_(0.3435683094039434491627650664387086926279e-25) }}, 
      {{ SC_(0.31863725185394287109375), SC_(0.049230270087718963623046875), SC_(0.905801355838775634765625), SC_(4.835956404824482039448613310634303665217), SC_(18.13934952405892063945685223408767166096), SC_(0.210484962411097216872077065087854065853), SC_(0.789515037588902783127922934912145934147) }}, 
      {{ SC_(0.330483734607696533203125), SC_(0.0469950400292873382568359375), SC_(0.81474220752716064453125), SC_(4.041757710054468269024263067242651231763), SC_(19.77709858403868207245622664736302187405), SC_(0.1696873124448374841824015206249455353811), SC_(0.8303126875551625158175984793750544646189) }}, 
      {{ SC_(0.3328996598720550537109375), SC_(49.7097015380859375), SC_(0.12707412242889404296875), SC_(0.7323668753990232209369905213683887988684), SC_(0.8576132716239690748322478888864445900834e-4), SC_(0.9998829121190064645049870036907901811314), SC_(0.0001170878809935354950129963092098188685626) }}, 
      {{ SC_(0.334436833858489990234375), SC_(365052.03125), SC_(0.221111953258514404296875), SC_(0.03683055124744016650145146533269472360119), SC_(BOOST_MATH_SMALL_CONSTANT(0.4134049894941059365873825201708076187273e-39622)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1122451268015812878040846843117965816022e-39620)) }}, 
      {{ SC_(0.340868175029754638671875), SC_(0.3847523112199269235134124755859375e-4), SC_(0.913384497165679931640625), SC_(4.867158987608664583707798913433802847757), SC_(25988.36014028287055499976803342113923811), SC_(0.0001872471983402101556651165326821452152247), SC_(0.9998127528016597898443348834673178547848) }}, 
      {{ SC_(0.342921555042266845703125), SC_(0.14970906078815460205078125), SC_(0.913384497165679931640625), SC_(4.34661506955168974465709711878525557199), SC_(4.66698548364575957976986790627158113517), SC_(0.4822284994657089119420803591920631570587), SC_(0.5177715005342910880579196408079368429413) }}, 
      {{ SC_(0.3429556787014007568359375), SC_(0.00213477574288845062255859375), SC_(0.81474220752716064453125), SC_(4.009626419111881299546237742750132966845), SC_(466.8817946854137311266127224461611608934), SC_(0.008514970202062459796618729698481092001944), SC_(0.9914850297979375402033812703015189079981) }}, 
      {{ SC_(0.345384299755096435546875), SC_(2.248920440673828125), SC_(0.9688708782196044921875), SC_(2.053234119300960105030679833815673726525), SC_(0.0001842819797558331634028937502098036384439), SC_(0.9999102560006081095830131747017360604843), SC_(0.8974399939189041698682529826393951565586e-4) }}, 
      {{ SC_(0.351158559322357177734375), SC_(2964.95703125), SC_(0.632396042346954345703125), SC_(0.1531718481401497943191795776961586069083), SC_(0.106649018926027166778219468481424606114e-1291), SC_(1.0), SC_(0.6962703670484214439055637258878850292219e-1291) }}, 
      {{ SC_(0.35431468486785888671875), SC_(0.411117613315582275390625), SC_(0.221111953258514404296875), SC_(1.716393376579801302703795521785201858791), SC_(2.7817646696244534870802926701437333703), SC_(0.3815769385933804926437546211109883377736), SC_(0.6184230614066195073562453788890116622264) }}, 
      {{ SC_(0.355726778507232666015625), SC_(49.3067169189453125), SC_(0.9688708782196044921875), SC_(0.6271299388017466130642484925768782079622), SC_(0.1044165188137607422884854107540884721121e-75), SC_(1.0), SC_(0.1664990177526347318580512923218074563927e-75) }}, 
      {{ SC_(0.357777118682861328125), SC_(0.047988586127758026123046875), SC_(0.9688708782196044921875), SC_(5.460720825092680690461878608377271284188), SC_(17.65850527756403161936884713022897207675), SC_(0.236198253386395571568449888197008547221), SC_(0.763801746613604428431550111802991452779) }}, 
      {{ SC_(0.358220756053924560546875), SC_(48.59809112548828125), SC_(0.905801355838775634765625), SC_(0.6198063131688178179770467940498847966326), SC_(0.3026229090997884765694295665412900153594e-51), SC_(1.0), SC_(0.4882539959178545868731792708738313096962e-51) }}, 
      {{ SC_(0.3589245975017547607421875), SC_(177.3041534423828125), SC_(0.1355634629726409912109375), SC_(0.3869783267224492553863112446868131674137), SC_(0.1204020251317165712119951359355068754193e-12), SC_(0.999999999999688866231472351889087862646), SC_(0.3111337685276481109121373540352782963312e-12) }}, 
      {{ SC_(0.35904705524444580078125), SC_(0.473808765411376953125), SC_(0.12707412242889404296875), SC_(1.352768414521322252947254300403898245783), SC_(2.751557931035401754468082701102017150797), SC_(0.3295957242741691390776525571023137943215), SC_(0.6704042757258308609223474428976862056785) }}, 
      {{ SC_(0.3647778928279876708984375), SC_(3.6492011547088623046875), SC_(0.1355634629726409912109375), SC_(1.203574965158435233461977332268630128914), SC_(0.3670868422164121606267998575347684448692), SC_(0.7662852432695558674737034751895626930234), SC_(0.2337147567304441325262965248104373069766) }}, 
      {{ SC_(0.364803850650787353515625), SC_(1.8238222599029541015625), SC_(0.632396042346954345703125), SC_(1.98332037500285904167492684245866890195), SC_(0.1057199556537739191447240811398613600922), SC_(0.9493930518705956581716798098610616676469), SC_(0.05060694812940434182832019013893833235313) }}, 
      {{ SC_(0.367133080959320068359375), SC_(0.2215300992247648537158966064453125e-4), SC_(0.221111953258514404296875), SC_(1.672202901005245368409465681456105196346), SC_(45141.16279023901003340872215218221080163), SC_(0.3704248750126916634066649181251461079228e-4), SC_(0.9999629575124987308336593335081874853892) }}, 
      {{ SC_(0.3685724437236785888671875), SC_(13019.9375), SC_(0.905801355838775634765625), SC_(0.07346192894752838396872414217069367165819), SC_(BOOST_MATH_SMALL_CONSTANT(0.1091462836658307512933433692191812119613e-13361)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1485753031938361078192174724676668699441e-13360)) }}, 
      {{ SC_(0.3701328337192535400390625), SC_(0.30404355129576288163661956787109375e-4), SC_(0.8350250720977783203125), SC_(3.907212891949879411112160145684479307266), SC_(32888.33472482277201881408367831161659839), SC_(0.0001187882814235843380579530114317406305126), SC_(0.9998812117185764156619420469885682593695) }}, 
      {{ SC_(0.37160670757293701171875), SC_(0.10332970321178436279296875), SC_(0.1355634629726409912109375), SC_(1.326235322335575399353327226388802714303), SC_(10.47079586999725008986459054878780116643), SC_(0.1124211083884840158821807304424753673166), SC_(0.8875788916115159841178192695575246326834) }}, 
      {{ SC_(0.373345315456390380859375), SC_(0.0465487875044345855712890625), SC_(0.8350250720977783203125), SC_(3.781022238997846088967810182963259723552), SC_(19.85213028953340746912429272502061562773), SC_(0.1599880606039835847379289976786339079274), SC_(0.8400119393960164152620710023213660920726) }}, 
      {{ SC_(0.375118434429168701171875), SC_(448846.15625), SC_(0.913384497165679931640625), SC_(0.01796721578967246096941631594289269772469), SC_(BOOST_MATH_SMALL_CONSTANT(0.1799138068499384979536172441972070918912e-476861)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1001344943791195449739236764586364500037e-476859)) }}, 
      {{ SC_(0.37850654125213623046875), SC_(43846.46875), SC_(0.12707412242889404296875), SC_(0.04108227627338822067418734977442707267622), SC_(0.9568951816810034520862409628026188555004e-2592), SC_(1.0), SC_(0.2329216558773909572112587849537516074883e-2590) }}, 
      {{ SC_(0.3837126791477203369140625), SC_(0.3599123060703277587890625), SC_(0.81474220752716064453125), SC_(3.075247592841129835761442448815551460631), SC_(1.564718451797673059591945445086361195995), SC_(0.6627737279229425454390476573983761733012), SC_(0.3372262720770574545609523426016238266988) }}, 
      {{ SC_(0.3884186446666717529296875), SC_(178.5201263427734375), SC_(0.3082362115383148193359375), SC_(0.3053396123511452758493138162878906281623), SC_(0.3068204932133039376108445126541750560395e-30), SC_(0.9999999999999999999999999999989951500532), SC_(0.1004849946755207201449902212960864401949e-29) }}, 
      {{ SC_(0.3887031972408294677734375), SC_(0.00263000768609344959259033203125), SC_(0.1355634629726409912109375), SC_(1.231835472333350871108555599550915098284), SC_(381.0613905476908828408619275278063196499), SC_(0.003222226784287379052086614849141003662113), SC_(0.9967777732157126209479133851508589963379) }}, 
      {{ SC_(0.3953707516193389892578125), SC_(0.0040236986242234706878662109375), SC_(0.905801355838775634765625), SC_(4.30748590147399691027261636167922996641), SC_(246.2348442100958977461491752998931352643), SC_(0.01719264724470238250085784376930689493694), SC_(0.9828073527552976174991421562306931050631) }}, 
      {{ SC_(0.3965031802654266357421875), SC_(11295.04296875), SC_(0.913384497165679931640625), SC_(0.05532529105048326762731390349286115129294), SC_(BOOST_MATH_SMALL_CONSTANT(0.1169114037234189913010919921095716564677e-12003)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2113163826228036944645503113506416485004e-12002)) }}, 
      {{ SC_(0.401973307132720947265625), SC_(467754.28125), SC_(0.81474220752716064453125), SC_(0.01160401103786041951825925851136580471694), SC_(BOOST_MATH_SMALL_CONSTANT(0.5001299126500188022188279304610580232569e-342506)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4309974465021141298745525936903956016857e-342504)) }}, 
      {{ SC_(0.4054018557071685791015625), SC_(1848.73486328125), SC_(0.3082362115383148193359375), SC_(0.1036718367061499887847634293582280399546), SC_(0.1449344911630642718545284308256712096656e-298), SC_(1.0), SC_(0.1398012186992212443204102412298732809805e-297) }}, 
      {{ SC_(0.4062000215053558349609375), SC_(0.0105956681072711944580078125), SC_(0.1355634629726409912109375), SC_(1.139345600873763402654598734204640178761), SC_(95.16973264199774880726676400337473448765), SC_(0.01183009558040385558933506736549787048401), SC_(0.988169904419596144410664932634502129516) }}, 
      {{ SC_(0.4080638885498046875), SC_(0.0004883776418864727020263671875), SC_(0.81474220752716064453125), SC_(3.492855214092404977589960204283500634297), SC_(2046.029050123807241396902523730452797889), SC_(0.00170422926683310882251145890570357012742), SC_(0.9982957707331668911774885410942964298726) }}, 
      {{ SC_(0.4081141948699951171875), SC_(3.8330304622650146484375), SC_(0.221111953258514404296875), SC_(1.1104961557306195502231978560103480678), SC_(0.1857080010874114019713792174877101659101), SC_(0.8567293584806160869401272877196158744144), SC_(0.1432706415193839130598727122803841255856) }}, 
      {{ SC_(0.4087921679019927978515625), SC_(2638.619140625), SC_(0.81474220752716064453125), SC_(0.08664048317667893436136356542454754521263), SC_(0.3734427620392960277310699953488718481801e-1935), SC_(1.0), SC_(0.4310257149394751039794604388812509803005e-1934) }}, 
      {{ SC_(0.412207901477813720703125), SC_(0.2795807449729181826114654541015625e-4), SC_(0.81474220752716064453125), SC_(3.465760567134203303723500757151555616934), SC_(35766.27421441400004448970935886567398139), SC_(0.9689085158456009219341769456126737115064e-4), SC_(0.9999031091484154399078065823054387326288) }}, 
      {{ SC_(0.4138956964015960693359375), SC_(0.0001860436168499290943145751953125), SC_(0.905801355838775634765625), SC_(4.190404422274049287039209092176814894544), SC_(5372.77915881606336085832868763513563296), SC_(0.0007793245568885707844367986988937403608349), SC_(0.9992206754431114292155632013011062596392) }}, 
      {{ SC_(0.433166682720184326171875), SC_(1.27705013751983642578125), SC_(0.905801355838775634765625), SC_(1.984679081190707733574624066435764139873), SC_(0.03954575294540647145094585103870723513553), SC_(0.9804637546785737582005006621268432097211), SC_(0.01953624532142624179949933787315679027891) }}, 
      {{ SC_(0.436771690845489501953125), SC_(1.04477035999298095703125), SC_(0.8350250720977783203125), SC_(2.081714265177482078767294301059126918754), SC_(0.1532535623587647546743803544317434770187), SC_(0.9314291863754896579986797985018804703014), SC_(0.06857081362451034200132020149811952969859) }}, 
      {{ SC_(0.4377568662166595458984375), SC_(0.4160407115705311298370361328125e-4), SC_(0.3082362115383148193359375), SC_(1.522719063698491191810023886243372284363), SC_(24036.31702533616631466385339866738944195), SC_(0.6334675161703087587041918595495668507129e-4), SC_(0.9999366532483829691241295808140450433149) }}, 
      {{ SC_(0.444455921649932861328125), SC_(49022.46484375), SC_(0.632396042346954345703125), SC_(0.0163983440631684185036000697841305621376), SC_(BOOST_MATH_SMALL_CONSTANT(0.1928572246899730380461347400325118333019e-21310)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1176077437740442085175403878859448687866e-21308)) }}, 
      {{ SC_(0.447976410388946533203125), SC_(44.28264617919921875), SC_(0.221111953258514404296875), SC_(0.3628789143928556564497309259291339420156), SC_(0.781030226862406623923165710779878019526e-6), SC_(0.9999978476882649722212930554802620074136), SC_(0.2152311735027778706944519737992586411172e-5) }}, 
      {{ SC_(0.466396510601043701171875), SC_(0.000362085294909775257110595703125), SC_(0.12707412242889404296875), SC_(0.8550289748994692887932915188394847007569), SC_(2762.487191450379985253322065514292380138), SC_(0.0003094184167923580573652410584667572272426), SC_(0.9996905815832076419426347589415332427728) }}, 
      {{ SC_(0.4669697284698486328125), SC_(0.004207052290439605712890625), SC_(0.9688708782196044921875), SC_(4.983939876459954438522038296768868926815), SC_(234.2681889463908380212481891486849812964), SC_(0.02083132928004251077110019900857162650321), SC_(0.9791686707199574892288998009914283734968) }}, 
      {{ SC_(0.4731414318084716796875), SC_(4038.0458984375), SC_(0.8350250720977783203125), SC_(0.03681800968424248843137587296847813644791), SC_(0.2151902007932805662941584901999999190826e-3163), SC_(1.0), SC_(0.5844699445700305886575723374423408588147e-3162) }}, 
      {{ SC_(0.4759317934513092041015625), SC_(18.6855449676513671875), SC_(0.8350250720977783203125), SC_(0.4650360995194773224517039299033264077037), SC_(0.1394117220024755848255733958883901110584e-15), SC_(0.9999999999999997002131186234145306897716), SC_(0.2997868813765854693102283740645151188783e-15) }}, 
      {{ SC_(0.4780696332454681396484375), SC_(0.110383547842502593994140625), SC_(0.3082362115383148193359375), SC_(1.320819025065765173251106611906988075106), SC_(9.164469076523780166952316245131402199336), SC_(0.125968787149065753818174278586794208014), SC_(0.874031212850934246181825721413205791986) }}, 
      {{ SC_(0.489096343517303466796875), SC_(2428.257080078125), SC_(0.905801355838775634765625), SC_(0.04002110223900413723281365417342574372867), SC_(0.2255848328346718286367403126341691915741e-2494), SC_(1.0), SC_(0.5636647173970617649062126080503579749532e-2493) }}, 
      {{ SC_(0.497481822967529296875), SC_(481.03790283203125), SC_(0.81474220752716064453125), SC_(0.08250998527360665655591448319605255300839), SC_(0.1365109765989683046247416612067038022512e-354), SC_(1.0), SC_(0.1654478256738163756230894760558582890084e-353) }}, 
      {{ SC_(1.00208950042724609375), SC_(1.134289264678955078125), SC_(0.913384497165679931640625), SC_(0.8246440603155744403297434208324277495865), SC_(0.05497424159943482845087112626319305832947), SC_(0.9375021626087691779855871130139941988826), SC_(0.06249783739123082201441288698600580111744) }}, 
      {{ SC_(1.06793177127838134765625), SC_(2593.565185546875), SC_(0.632396042346954345703125), SC_(0.00021814818706820204122140431640517093719), SC_(0.2279040265365697422812937326452021450552e-1130), SC_(1.0), SC_(0.1044721157665718426627598635375750226813e-1126) }}, 
      {{ SC_(1.078310489654541015625), SC_(0.1611862899153493344783782958984375e-4), SC_(0.81474220752716064453125), SC_(1.579238934608002020930576188419414139161), SC_(62038.31480199683602703014226388490958657), SC_(0.2545521650256338689760697709112582973268e-4), SC_(0.9999745447834974366131023930229088741703) }}, 
      {{ SC_(1.127964019775390625), SC_(28082.556640625), SC_(0.221111953258514404296875), SC_(0.9031444614627416618001308756504366467831e-5), SC_(0.6446722352112902992756278548084437136712e-3052), SC_(1.0), SC_(0.7138085463837898091420140568036635292841e-3047) }}, 
      {{ SC_(1.17060959339141845703125), SC_(374486.4375), SC_(0.221111953258514404296875), SC_(0.2770348746813317152148567841195551130469e-6), SC_(BOOST_MATH_SMALL_CONSTANT(0.1544222082236331533651701311405865986201e-40646)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5574107173375275245760170786317275232285e-40640)) }}, 
      {{ SC_(1.1981303691864013671875), SC_(18840.236328125), SC_(0.81474220752716064453125), SC_(0.6934970851989473984828582349279761548277e-5), SC_(BOOST_MATH_SMALL_CONSTANT(0.2774673653527033277856718432732877361682e-13799)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4000988198430635197865793159272447313039e-13794)) }}, 
      {{ SC_(1.2198965549468994140625), SC_(0.1385947167873382568359375), SC_(0.9688708782196044921875), SC_(2.476100393862268102451316018157336805629), SC_(4.457073809447786720692093046710205236916), SC_(0.3571380613341752670809919250585172665935), SC_(0.6428619386658247329190080749414827334065) }}, 
      {{ SC_(1.3339312076568603515625), SC_(327.5166015625), SC_(0.632396042346954345703125), SC_(0.000393880060303142468976379431401696514736), SC_(0.1184022713919227905295431760253988365597e-144), SC_(1.0), SC_(0.3006048879468452467763121228666591540639e-141) }}, 
      {{ SC_(1.359802722930908203125), SC_(0.39465808868408203125), SC_(0.1355634629726409912109375), SC_(0.05106481064957347464764855792811332922631), SC_(2.124848787490442060819126279665208636722), SC_(0.02346821615215971447624268273783067701828), SC_(0.9765317838478402855237573172621693229817) }}, 
      {{ SC_(1.44682300090789794921875), SC_(0.17606438696384429931640625), SC_(0.221111953258514404296875), SC_(0.0876907555927175240997186365838154717148), SC_(5.103276100986290681799294432218717156109), SC_(0.01689295231804045371190720225591093428374), SC_(0.9831070476819595462880927977440890657163) }}, 
      {{ SC_(1.4834382534027099609375), SC_(1159.6116943359375), SC_(0.81474220752716064453125), SC_(0.2520493995468790742310543925834688118861e-4), SC_(0.6277129730492132650095565877062169428781e-852), SC_(1.0), SC_(0.2490436296129576444460388465665272717916e-847) }}, 
      {{ SC_(1.49233496189117431640625), SC_(47973.6953125), SC_(0.913384497165679931640625), SC_(0.9158142701219620515735116806823620402928e-7), SC_(BOOST_MATH_SMALL_CONSTANT(0.6856885307691086571645551101168057228575e-50972)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.7487200769188639313615991098103951104891e-50965)) }}, 
      {{ SC_(1.54517018795013427734375), SC_(0.2047308981418609619140625), SC_(0.3082362115383148193359375), SC_(0.1246691455112791133529726738608095355786), SC_(4.205316764985243567300214824811226800357), SC_(0.02879204415170561384327527121287883723012), SC_(0.9712079558482943861567247287871211627699) }}, 
      {{ SC_(1.5860595703125), SC_(268341.4375), SC_(0.12707412242889404296875), SC_(0.2188529267992022850133716156935067320774e-8), SC_(BOOST_MATH_SMALL_CONSTANT(0.6735609334108315179849663945793421049789e-15844)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3077687574307946771709117498857270187347e-15835)) }}, 
      {{ SC_(1.5904328823089599609375), SC_(0.175132334232330322265625), SC_(0.913384497165679931640625), SC_(1.417983846922945074086281300478897686333), SC_(3.691617709165716915935800312844163731536), SC_(0.2775135852292158976635665611127901281819), SC_(0.7224864147707841023364334388872098718181) }}, 
      {{ SC_(1.61900937557220458984375), SC_(0.0355711281299591064453125), SC_(0.3082362115383148193359375), SC_(0.1136541817329229254269843881829401312168), SC_(27.30222816046602658712459577564402451514), SC_(0.004145559873445497452627965327295647615897), SC_(0.9958544401265545025473720346727043523841) }}, 
      {{ SC_(1.6690142154693603515625), SC_(0.0045049847103655338287353515625), SC_(0.905801355838775634765625), SC_(1.654671420443895204691845091716983754591), SC_(219.5639400669292015831138254049874771508), SC_(0.007479802035274694512028581353642119369761), SC_(0.9925201979647253054879714186463578806302) }}, 
      {{ SC_(1.7562887668609619140625), SC_(374293.53125), SC_(0.1355634629726409912109375), SC_(0.1499189513271098776623048463642459294765e-9), SC_(BOOST_MATH_SMALL_CONSTANT(0.2424349442341666429067734444899906899063e-23686)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1617106723920413906032975601315111209707e-23676)) }}, 
      {{ SC_(1.75884163379669189453125), SC_(0.369454860687255859375), SC_(0.8350250720977783203125), SC_(0.7459991194649348508593671759038903077423), SC_(1.343398870365095255155485654427562685422), SC_(0.3570402207219606539986727822574040037171), SC_(0.6429597792780393460013272177425959962829) }}, 
      {{ SC_(1.77004158496856689453125), SC_(49033.26953125), SC_(0.905801355838775634765625), SC_(0.4604637724742806794236788169427640062807e-8), SC_(BOOST_MATH_SMALL_CONSTANT(0.2144426942604382168761343777126695639019e-50310)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4657102405866597634441661634012437599171e-50302)) }}, 
      {{ SC_(1.788215160369873046875), SC_(0.12965712812729179859161376953125e-4), SC_(0.9688708782196044921875), SC_(2.640634438889896708617514645592185372095), SC_(77123.00209217535250302348088163455537592), SC_(0.3423808665361923747204066878435148080854e-4), SC_(0.9999657619133463807625279593312156485192) }}, 
      {{ SC_(1.799451351165771484375), SC_(0.04982374608516693115234375), SC_(0.632396042346954345703125), SC_(0.4255395277792709792589480096041978492367), SC_(18.82260408866470434474041837153588423404), SC_(0.02210808149912837478136406335251040762786), SC_(0.9778919185008716252186359366474895923721) }}, 
      {{ SC_(1.82197666168212890625), SC_(39458.8984375), SC_(0.9688708782196044921875), SC_(0.3961398447554578817125063708808197814433e-8), SC_(BOOST_MATH_SMALL_CONSTANT(0.2612564425789845768175531564246729143871e-59462)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6595055913657498178517734953584139382822e-59454)) }}, 
      {{ SC_(1.837620258331298828125), SC_(3435.755859375), SC_(0.9688708782196044921875), SC_(0.2992581288998905430300739013792708491349e-6), SC_(BOOST_MATH_SMALL_CONSTANT(0.2196425106883746221137085162739396644011e-5180)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.7339567065255915886696628555434224696579e-5174)) }}, 
      {{ SC_(1.86407566070556640625), SC_(0.000142144519486464560031890869140625), SC_(0.9688708782196044921875), SC_(2.587406529835929906908103338386687729829), SC_(7031.597939263406660826488911159064663572), SC_(0.0003678331466462302876247043330802756681583), SC_(0.9996321668533537697123752956669197243318) }}, 
      {{ SC_(1.911371707916259765625), SC_(16.85260009765625), SC_(0.81474220752716064453125), SC_(0.00415327177405512137666547731638393421375), SC_(0.2277069326301129117415576625655239073555e-13), SC_(0.9999999999945174083224864733449004647348), SC_(0.5482591677513526655099535265191131570159e-11) }}, 
      {{ SC_(1.98693811893463134765625), SC_(298.79779052734375), SC_(0.221111953258514404296875), SC_(0.1196137731365210334436030960431701756123e-4), SC_(0.2856165679605071527615820193479064068975e-35), SC_(0.9999999999999999999999999999997612176587), SC_(0.2387823412564028331398841789617579979614e-30) }}, 
      {{ SC_(2.081081867218017578125), SC_(0.171459905686788260936737060546875e-4), SC_(0.913384497165679931640625), SC_(1.488702561868755884237721327915942320219), SC_(58320.13323944254348270913531881604926284), SC_(0.2552574006513632598147199047180593453667e-4), SC_(0.9999744742599348636740185280095281940655) }}, 
      {{ SC_(2.1274673938751220703125), SC_(497909.9375), SC_(0.632396042346954345703125), SC_(0.8037448229976571143979811001021714752968e-12), SC_(BOOST_MATH_SMALL_CONSTANT(0.3550228339076187849936593977146902793143e-216407)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4417108810524228571698460596174699747163e-216395)) }}, 
      {{ SC_(2.274096965789794921875), SC_(0.0312146879732608795166015625), SC_(0.9688708782196044921875), SC_(2.196683805098439034040943843963161336389), SC_(28.71343219945377139900472099439413865446), SC_(0.0710668250088394327537390803024871598051), SC_(0.9289331749911605672462609196975128401949) }}, 
      {{ SC_(2.31026172637939453125), SC_(0.00233163125813007354736328125), SC_(0.221111953258514404296875), SC_(0.01571244871861467114292688043541166213954), SC_(427.6886752748253213817442343909326747566), SC_(0.3673670219341017196605035396837681998268e-4), SC_(0.99996326329780658982803394964603162318) }}, 
      {{ SC_(2.323431491851806640625), SC_(0.489291487610898911952972412109375e-4), SC_(0.8350250720977783203125), SC_(0.827369238662420067558137225717222927895), SC_(20435.69785260805758421542451561713036197), SC_(0.4048482947472691363415515690231412697368e-4), SC_(0.999959515170525273086365844843097685873) }}, 
      {{ SC_(2.44964599609375), SC_(12697.8740234375), SC_(0.632396042346954345703125), SC_(0.1137057590475477348116732810083465638967e-9), SC_(BOOST_MATH_SMALL_CONSTANT(0.7245353688384564801212454707725109820553e-5523)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6372019983046605086666389661217280482321e-5513)) }}, 
      {{ SC_(2.509582042694091796875), SC_(0.00033403583802282810211181640625), SC_(0.913384497165679931640625), SC_(1.289833455805136344069185638870621448839), SC_(2991.11632989703270150746226073849403594), SC_(0.0004310355564700294631384799607263649785933), SC_(0.9995689644435299705368615200392736350214) }}, 
      {{ SC_(2.564732074737548828125), SC_(0.000453212647698819637298583984375), SC_(0.3082362115383148193359375), SC_(0.02464832555746644285754024188263887460935), SC_(2205.134132599338738162441322041789880736), SC_(0.1117757404622280625320507532230938354193e-4), SC_(0.9999888224259537771937467949246776906165) }}, 
      {{ SC_(2.57882976531982421875), SC_(0.3226127228117547929286956787109375e-4), SC_(0.1355634629726409912109375), SC_(0.002486466998068928679780937936994252108702), SC_(30995.59689380055526154730970067395611954), SC_(0.8022000050922924115668151603694250255305e-7), SC_(0.9999999197799994907707588433184839630575) }}, 
      {{ SC_(2.5871660709381103515625), SC_(0.00046129638212732970714569091796875), SC_(0.81474220752716064453125), SC_(0.6493235945001016310261817647526997747666), SC_(2165.832800060715756721049233362385593433), SC_(0.0002997133405396323675757091217121378066853), SC_(0.9997002866594603676324242908782878621933) }}, 
      {{ SC_(2.6278192996978759765625), SC_(0.03557576239109039306640625), SC_(0.81474220752716064453125), SC_(0.6123860587254943627358332840924393789845), SC_(26.20657873847624470581117739479457315595), SC_(0.02283406773364309879630544392201241822222), SC_(0.9771659322663569012036945560779875817778) }}, 
      {{ SC_(2.69723796844482421875), SC_(0.353506766259670257568359375e-4), SC_(0.905801355838775634765625), SC_(1.146759054864891147215735050805714230645), SC_(28285.48221864071434680861300657464188461), SC_(0.4054067580018557312858650792971044356998e-4), SC_(0.9999594593241998144268714134920702895564) }}, 
      {{ SC_(2.71968555450439453125), SC_(0.00041439200867898762226104736328125), SC_(0.1355634629726409912109375), SC_(0.001781952190693927759024929827182201958694), SC_(2411.79004735384091727391030124394912695), SC_(0.7388499160836224595939787763650885622781e-6), SC_(0.9999992611500839163775404060212236349114) }}, 
      {{ SC_(2.754580020904541015625), SC_(0.0048456829972565174102783203125), SC_(0.632396042346954345703125), SC_(0.2012235661922152132272903655062681078713), SC_(204.7775390244987611591096954298352797072), SC_(0.0009816800708960553370130825813222883720957), SC_(0.9990183199291039446629869174186777116279) }}, 
      {{ SC_(2.769221782684326171875), SC_(0.366566746379248797893524169921875e-4), SC_(0.632396042346954345703125), SC_(0.1996957759895774162080025052862658212039), SC_(27278.55725574597868280625903171760364926), SC_(0.7320559963361371431432147856490047793823e-5), SC_(0.9999926794400366386285685678521435099522) }}, 
      {{ SC_(2.780732631683349609375), SC_(495953.28125), SC_(0.9688708782196044921875), SC_(0.2398292646917707560124992979242780157301e-15), SC_(BOOST_MATH_SMALL_CONSTANT(0.2781154293448569232517364618623603676914e-747324)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.115963925295893999212106421694525461812e-747308)) }}, 
      {{ SC_(2.91809368133544921875), SC_(21.43137359619140625), SC_(0.3082362115383148193359375), SC_(0.0002115892914364910572043643848950895139729), SC_(0.2192618572284581292361088522964625063202e-5), SC_(0.9897436664674079513230225262156800692848), SC_(0.01025633353259204867697747378431993071523) }}, 
      {{ SC_(2.9371860027313232421875), SC_(2652.859619140625), SC_(0.905801355838775634765625), SC_(0.1658236966080120503243323886023037349916e-9), SC_(0.5991967514867191164566809014128887789337e-2725), SC_(1.0), SC_(0.3613456723879160702175323605361247762983e-2715) }}, 
      {{ SC_(2.958280086517333984375), SC_(35774.58984375), SC_(0.8350250720977783203125), SC_(0.6511424206979178883680584277451301714988e-13), SC_(BOOST_MATH_SMALL_CONSTANT(0.5509841454195994872563646231911938269328e-28001)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.84618069397019909122625585151108936335e-27988)) }}, 
      {{ SC_(2.98002338409423828125), SC_(0.1707812249660491943359375), SC_(0.905801355838775634765625), SC_(0.8061305149524062992643042855909028552288), SC_(3.807753672865923506647365054411987336976), SC_(0.174718411242477295345916452260913416746), SC_(0.825281588757522704654083547739086583254) }}, 
      {{ SC_(2.98070812225341796875), SC_(0.2840107381343841552734375), SC_(0.81474220752716064453125), SC_(0.392888581028449529241290682772237418065), SC_(2.013262244676749848732070538277818192877), SC_(0.1632851011795160336456375818504424610958), SC_(0.8367148988204839663543624181495575389042) }}, 
      {{ SC_(2.99237728118896484375), SC_(13.7659931182861328125), SC_(0.8350250720977783203125), SC_(0.0006330260109235047478811608476068694254983), SC_(0.8784866862106843248872857887758672368004e-12), SC_(0.9999999986122423568188660379383225681103), SC_(0.1387757643181133962061677431889710145459e-8) }}, 
      {{ SC_(2.9964640140533447265625), SC_(235220.21875), SC_(0.905801355838775634765625), SC_(0.1600213661608141959980213910633746624008e-15), SC_(BOOST_MATH_SMALL_CONSTANT(0.1262778634498618515615030641078309168309e-241330)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.7891312671519023359516257488928419584491e-241315)) }}, 
      {{ SC_(3.0576937198638916015625), SC_(0.03248409926891326904296875), SC_(0.12707412242889404296875), SC_(0.000657166396253856112420817904131697694946), SC_(29.31723438639669348292098048836029171345), SC_(0.2241519977896403808220848026298478626326e-4), SC_(0.9999775848002210359619177915197370152137) }}, 
      {{ SC_(3.1107203960418701171875), SC_(10.69952869415283203125), SC_(0.221111953258514404296875), SC_(0.0005593621180658764916032354796476988885327), SC_(0.0004897865015629660797959732567916697366427), SC_(0.5331581318419520895479003417175303065101), SC_(0.4668418681580479104520996582824696934899) }}, 
      {{ SC_(3.1362564563751220703125), SC_(0.412800610065460205078125), SC_(0.12707412242889404296875), SC_(0.0005243957231309698424424038853343999532901), SC_(1.392495891474714025850895086191276968485), SC_(0.0003764451443746217706553968021572423592609), SC_(0.9996235548556253782293446031978427576407) }}, 
      {{ SC_(3.143204212188720703125), SC_(245166.296875), SC_(0.913384497165679931640625), SC_(0.2630059636404044176917380685846427352151e-16), SC_(BOOST_MATH_SMALL_CONSTANT(0.6043270917410382335978807169229650293393e-260471)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2297769538668355098082544810040529712714e-260454)) }}, 
      {{ SC_(3.1543867588043212890625), SC_(274237.21875), SC_(0.81474220752716064453125), SC_(0.1625350376420910741384848449693863184265e-16), SC_(BOOST_MATH_SMALL_CONSTANT(0.2690539847628662110788655761771698612467e-200808)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1655359906799506806868897367788715537181e-200791)) }}, 
      {{ SC_(3.2091653347015380859375), SC_(12216.2568359375), SC_(0.12707412242889404296875), SC_(0.1873797592728273205179908606995030456467e-12), SC_(0.7919285686868581022013174499890084615767e-727), SC_(1.0), SC_(0.4226329309847175120814746567152805472633e-714) }}, 
      {{ SC_(3.24223804473876953125), SC_(1.68130600452423095703125), SC_(0.1355634629726409912109375), SC_(0.0004394960498861844768550401648312202217273), SC_(0.106531152706243738563169469720543304429), SC_(0.004108566742342013504145261269084905519575), SC_(0.9958914332576579864958547387309150944804) }}, 
      {{ SC_(3.2986447811126708984375), SC_(18.05455780029296875), SC_(0.913384497165679931640625), SC_(0.0001571231807759573941342283741313652373997), SC_(0.2997084203140047681159353167938184883028e-20), SC_(0.9999999999999999809252575696414736899537), SC_(0.190747424303585263100463198766717611279e-16) }}, 
      {{ SC_(3.3203613758087158203125), SC_(4707.14794921875), SC_(0.3082362115383148193359375), SC_(0.174837797989208477568413462293102195009e-11), SC_(0.6303028289438603901098749689614525530665e-758), SC_(1.0), SC_(0.3605071879152610948355870976343748337838e-746) }}, 
      {{ SC_(3.3257858753204345703125), SC_(3224.86181640625), SC_(0.1355634629726409912109375), SC_(0.5905458153290816749396191237660878415679e-11), SC_(0.2805909749269826507071883053014434221043e-209), SC_(1.0), SC_(0.4751383680038835272016840533489017630165e-198) }}, 
      {{ SC_(3.331163883209228515625), SC_(1.9793136119842529296875), SC_(0.632396042346954345703125), SC_(0.03395298723188450151204978548749765959161), SC_(0.03703887574485270285604165860520092728045), SC_(0.4782658998963064738707927469026664128811), SC_(0.5217341001036935261292072530973335871189) }}, 
      {{ SC_(3.3439481258392333984375), SC_(334.433685302734375), SC_(0.1355634629726409912109375), SC_(0.1005433692432035880909489062786411594593e-7), SC_(0.2005891010803133486881905313928167777841e-25), SC_(0.9999999999999999980049494801083311279569), SC_(0.1995050519891668872043050768016044534771e-17) }}, 
      {{ SC_(3.35210418701171875), SC_(0.0159323550760746002197265625), SC_(0.221111953258514404296875), SC_(0.002282976272379056096506902888344657555717), SC_(61.16375741970565532957422675625988954631), SC_(0.3732424491759602156863300845915103269515e-4), SC_(0.9999626757550824039784313669915408489673) }}, 
      {{ SC_(3.4502658843994140625), SC_(41.324951171875), SC_(0.12707412242889404296875), SC_(0.6714227859028409223078791133262327911845e-5), SC_(0.8395443123259849127162481713501751685527e-6), SC_(0.8888576073938626205703705028323799622538), SC_(0.1111423926061373794296294971676200377462) }}, 
      {{ SC_(3.45885372161865234375), SC_(29400.57421875), SC_(0.3082362115383148193359375), SC_(0.1113097721240300362972826582934483808616e-14), SC_(0.877209818992475775035689064062142091421e-4711), SC_(1.0), SC_(0.7880797905282027931851392155990827896495e-4696) }}, 
      {{ SC_(3.5038392543792724609375), SC_(174.0391998291015625), SC_(0.81474220752716064453125), SC_(0.4588448232831660229952451882786936627741e-7), SC_(0.1265843939076240475896867177771413219439e-129), SC_(1.0), SC_(0.2758762603049032638216040912750710542023e-122) }}, 
      {{ SC_(3.519533634185791015625), SC_(43931.828125), SC_(0.1355634629726409912109375), SC_(0.1550601994296576624475572682354891325322e-15), SC_(0.5506303550678763576292786848202724162192e-2786), SC_(1.0), SC_(0.355107472512743190826437889308446598755e-2770) }}, 
      {{ SC_(3.5407917499542236328125), SC_(192506.5), SC_(0.3082362115383148193359375), SC_(0.6762501081091727750666256335913014047553e-18), SC_(BOOST_MATH_SMALL_CONSTANT(0.1810714101987144792606684975020851024195e-30815)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2677580499099640659954906366427546539929e-30797)) }}, 
      {{ SC_(3.643778324127197265625), SC_(460.66265869140625), SC_(0.8350250720977783203125), SC_(0.7631535147388549115637412492866378561899e-9), SC_(0.4205040932908895771554373642902487973848e-363), SC_(1.0), SC_(0.5510085260300252268571165317876316783109e-354) }}, 
      {{ SC_(3.6656649112701416015625), SC_(336.389312744140625), SC_(0.3082362115383148193359375), SC_(0.215791720749920489432567844635617465443e-8), SC_(0.1913959160478324006182179322373144888867e-57), SC_(1.0), SC_(0.8869474481351385317830904747175621652939e-49) }}, 
      {{ SC_(3.6850574016571044921875), SC_(0.0037575312890112400054931640625), SC_(0.3082362115383148193359375), SC_(0.004700958723625040380482933503067520773596), SC_(264.3959126179765937938283198290409057692), SC_(0.1777968159768031291326144486881844545145e-4), SC_(0.9999822203184023196870867385551311815545) }}, 
      {{ SC_(3.7146091461181640625), SC_(0.280897915363311767578125), SC_(0.632396042346954345703125), SC_(0.08266985092943892844994513141054194966211), SC_(2.197083375517572737580826900944351079459), SC_(0.03626263139817094798750334205303616370305), SC_(0.963737368601829052012496657946963836297) }}, 
      {{ SC_(3.7336635589599609375), SC_(0.477436915389262139797210693359375e-4), SC_(0.221111953258514404296875), SC_(0.001161350390915668250446450228578170310865), SC_(20943.41972718709787515626983068263368694), SC_(0.5545180021432339794938139482542775628364e-7), SC_(0.9999999445481997856766020506186051745722) }}, 
      {{ SC_(3.78065204620361328125), SC_(397517.0625), SC_(0.8350250720977783203125), SC_(0.3106692370941557178524894184578248280721e-20), SC_(BOOST_MATH_SMALL_CONSTANT(0.2911649718207596959822032674625140507844e-311095)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.9372185496838079614946082001992536183017e-311075)) }}, 
      {{ SC_(3.7866687774658203125), SC_(4.32257175445556640625), SC_(0.3082362115383148193359375), SC_(0.001233453284813738256980335173501548189344), SC_(0.005474414653806166186400567572273711207249), SC_(0.183881569538996081375634417888316953202), SC_(0.816118430461003918624365582111683046798) }}, 
      {{ SC_(3.81618976593017578125), SC_(0.30148139558150433003902435302734375e-4), SC_(0.3082362115383148193359375), SC_(0.003902917947682581703045615669910691845878), SC_(33167.75902216366067552099307425048603802), SC_(0.1176720286049553847622526013198847043118e-6), SC_(0.9999998823279713950446152377473986801153) }}, 
      {{ SC_(3.9190075397491455078125), SC_(109.78485107421875), SC_(0.905801355838775634765625), SC_(0.5188940518655968999732995391783015444467e-7), SC_(0.1588078213027438911853841563985523076893e-114), SC_(1.0), SC_(0.3060505718494495997940146116913898836079e-107) }}, 
      {{ SC_(3.9453601837158203125), SC_(0.42322339140810072422027587890625e-4), SC_(0.12707412242889404296875), SC_(0.8236591000866047274326096294890126372598e-4), SC_(23626.36592110891663715623440051911992038), SC_(0.3486186153069269315742362759644030992004e-8), SC_(0.999999996513813846930730684257637240356) }}, 
      {{ SC_(3.9545612335205078125), SC_(420.351806640625), SC_(0.12707412242889404296875), SC_(0.2356317584514070056632402769608253165585e-9), SC_(0.8711124672670564009175660201341093325929e-30), SC_(0.9999999999999999999963030770003496750235), SC_(0.3696922999650324976460210874151614428123e-20) }}, 
      {{ SC_(3.994822978973388671875), SC_(0.02758073247969150543212890625), SC_(0.8350250720977783203125), SC_(0.4098735721841564788896545917766083138654), SC_(34.07853530102203102562478577903427263834), SC_(0.01188438624962442079159001634372174773343), SC_(0.9881156137503755792084099836562782522666) }}, 
      {{ SC_(4.02030849456787109375), SC_(0.00037742473068647086620330810546875), SC_(0.905801355838775634765625), SC_(0.7940260393044519559181226178432463520661), SC_(2646.902698496763698448822721100164773373), SC_(0.0002998931229344561458873290296292921232436), SC_(0.9997001068770655438541126709703707078768) }}, 
      {{ SC_(4.075417041778564453125), SC_(0.0046969656832516193389892578125), SC_(0.8350250720977783203125), SC_(0.4105384414854240139610412208872348140263), SC_(210.649583048364410681785558959946689255), SC_(0.001945125581220549805037242783737639399678), SC_(0.9980548744187794501949627572162623606003) }}, 
      {{ SC_(4.0761165618896484375), SC_(3398.80859375), SC_(0.12707412242889404296875), SC_(0.2661516748320809135757809071004428436407e-13), SC_(0.1284465826019821475525755935974524934126e-206), SC_(1.0), SC_(0.4826067041773117693895150053958020416853e-193) }}, 
      {{ SC_(4.076457500457763671875), SC_(0.000358947552740573883056640625), SC_(0.632396042346954345703125), SC_(0.07990215805348860602606188021623435934074), SC_(2783.988480316991854913593918716445388643), SC_(0.286997828632554427741237124873359859469e-4), SC_(0.9999713002171367445572258762875126640141) }}, 
      {{ SC_(4.15985202789306640625), SC_(0.01332603581249713897705078125), SC_(0.913384497165679931640625), SC_(0.8106760641473409814599499536789212637117), SC_(72.38484116409426018759060376250696965144), SC_(0.01107548788294580791509377274581652662185), SC_(0.9889245121170541920849062272541834733782) }}, 
      {{ SC_(4.16162872314453125), SC_(0.0049612796865403652191162109375), SC_(0.12707412242889404296875), SC_(0.5001267297020304346384291352885648903897e-4), SC_(199.6947476062598992323407238206379873761), SC_(0.2504455477385024806867405766095845732647e-6), SC_(0.9999997495544522614975193132594233904154) }}, 
      {{ SC_(4.205390453338623046875), SC_(48.05587005615234375), SC_(0.632396042346954345703125), SC_(0.576694044310049590467080310610345695785e-6), SC_(0.6470155628434412761370877039014851790302e-23), SC_(0.9999999999999999887806095931244864948915), SC_(0.1121939040687551350510849484508581549787e-16) }}, 
      {{ SC_(4.2340564727783203125), SC_(0.000102389443782158195972442626953125), SC_(0.8350250720977783203125), SC_(0.3908308061963272778320899491289780578316), SC_(9764.343584311484163498409861158291703101), SC_(0.4002472464496794494668149916931584790399e-4), SC_(0.9999599752753550320550533185008306841521) }}, 
      {{ SC_(4.26158905029296875), SC_(1.5681400299072265625), SC_(0.81474220752716064453125), SC_(0.0522681771553854610094348918090025337654), SC_(0.03097517785556224640631163047119112004045), SC_(0.6278960903066849999650560502968121365326), SC_(0.3721039096933150000349439497031878634674) }}, 
      {{ SC_(4.286884784698486328125), SC_(0.00029798992909491062164306640625), SC_(0.12707412242889404296875), SC_(0.375382569001888294680832372613483569343e-4), SC_(3353.90718977927479988056295324370962887), SC_(0.1119239572115776028497004703533167721871e-7), SC_(0.9999999888076042788422397150299529646683) }}, 
      {{ SC_(4.302335262298583984375), SC_(0.011135534383356571197509765625), SC_(0.905801355838775634765625), SC_(0.7274189702615498210620952895066901020165), SC_(87.18728869405267176035247088170016141474), SC_(0.008274144219862077733957054764482754088799), SC_(0.9917258557801379222660429452355172459112) }}, 
      {{ SC_(4.33400249481201171875), SC_(0.0019063963554799556732177734375), SC_(0.81474220752716064453125), SC_(0.3158284853966184390604543146132717148806), SC_(522.3149737882240994046063224240272319367), SC_(0.0006043051500651276770767317016898387954114), SC_(0.9993956948499348723229232682983101612046) }}, 
      {{ SC_(4.379422664642333984375), SC_(1518.1336669921875), SC_(0.913384497165679931640625), SC_(0.1146281886799135235589695211019191532827e-12), SC_(0.6515879173539096867256517152138981630728e-1616), SC_(1.0), SC_(0.5684360233357577730226484994237076618639e-1603) }}, 
      {{ SC_(4.3807125091552734375), SC_(40.12465667724609375), SC_(0.9688708782196044921875), SC_(0.7810946424301170190417410890436800416059e-6), SC_(0.7765031164598682165452612982978476490707e-62), SC_(1.0), SC_(0.9941216778085126897100001434940744139359e-56) }}, 
      {{ SC_(4.402850627899169921875), SC_(2.295498371124267578125), SC_(0.12707412242889404296875), SC_(0.2239223338577079030873961571794477568143e-4), SC_(0.02870413618297970057227656984631523435351), SC_(0.0007794966750320571209806200492040174162521), SC_(0.9992205033249679428790193799507959825837) }}, 
      {{ SC_(4.4479217529296875), SC_(0.0027269781567156314849853515625), SC_(0.913384497165679931640625), SC_(0.7727460612153742201764237007167402790834), SC_(363.9875570278401865556342043965409621681), SC_(0.002118503726066675854378571395200342242294), SC_(0.9978814962739333241456214286047996577577) }}, 
      {{ SC_(4.45084285736083984375), SC_(3340.46728515625), SC_(0.8350250720977783203125), SC_(0.2244990476855018774487678088930777899517e-14), SC_(0.1038325505917867909964943789917169332435e-2617), SC_(1.0), SC_(0.4625077552099223675790368726616036217505e-2603) }}, 
      {{ SC_(4.461754322052001953125), SC_(2.637157440185546875), SC_(0.9688708782196044921875), SC_(0.01867088107506661562263700947373778673728), SC_(0.3722775835516935268098074499034701228028e-4), SC_(0.9980100736698376234182703619442376597162), SC_(0.001989926330162376581729638055762340283763) }}, 
      {{ SC_(4.516055583953857421875), SC_(1.62176787853240966796875), SC_(0.8350250720977783203125), SC_(0.04714701190206004185311752063294221274949), SC_(0.02294679212038186957620566227684173444898), SC_(0.6726273821144733094920485440879345046862), SC_(0.3273726178855266905079514559120654953138) }}, 
      {{ SC_(4.537124156951904296875), SC_(0.043504618108272552490234375), SC_(0.1355634629726409912109375), SC_(0.2849473743817781104862905200840506043338e-4), SC_(21.12052456017385670036068900858362490078), SC_(0.1349147314660481902117514558212828818074e-5), SC_(0.9999986508526853395180978824854417871712) }}, 
      {{ SC_(4.55108356475830078125), SC_(0.00028587187989614903926849365234375), SC_(0.221111953258514404296875), SC_(0.0002796952883842011988881123363817759988857), SC_(3496.092302523280454605375025284158000743), SC_(0.8000225446166837209590733695556689803331e-7), SC_(0.9999999199977455383316279040926630444331) }}, 
      {{ SC_(4.56300830841064453125), SC_(205.750579833984375), SC_(0.913384497165679931640625), SC_(0.3396421824056862937117118134465990662714e-9), SC_(0.9054459885381117537853705858550233754877e-221), SC_(1.0), SC_(0.2665882023619198030445523294235488150914e-211) }}, 
      {{ SC_(4.60340976715087890625), SC_(20.9564685821533203125), SC_(0.905801355838775634765625), SC_(0.7662814851119697002565648547511514245558e-5), SC_(0.1073715569115845211869701967911544027724e-22), SC_(0.9999999999999999985987974524022944634869), SC_(0.1401202547597705536513072398399453713197e-17) }}, 
      {{ SC_(4.71325206756591796875), SC_(1386.131103515625), SC_(0.221111953258514404296875), SC_(0.2431268230931994109313207303367950601082e-13), SC_(0.9973688779029749120647199340499327763088e-156), SC_(1.0), SC_(0.4102257682693640563670705316162444929497e-142) }}, 
      {{ SC_(4.7184352874755859375), SC_(1.0989363193511962890625), SC_(0.221111953258514404296875), SC_(0.0001679238125060957819092267452697444408295), SC_(0.1709435373809264311336338608100820471118), SC_(0.0009813709224086791272635944182639305555371), SC_(0.9990186290775913208727364055817360694445) }}, 
      {{ SC_(4.79721546173095703125), SC_(0.001919968170113861560821533203125), SC_(0.1355634629726409912109375), SC_(0.1612480694169045520097031824245881881183e-4), SC_(518.8098630097590106316683546253859642722), SC_(0.3108037759147623052845169599594860548187e-7), SC_(0.9999999689196224085237694715483040040514) }}, 
      {{ SC_(4.9292125701904296875), SC_(413.006103515625), SC_(0.9688708782196044921875), SC_(0.2687812328867933956455358023671395654682e-11), SC_(0.9975426648476879332945952527059336656624e-625), SC_(1.0), SC_(0.3711355343279632433489429140149721765579e-613) }}, 
      {{ SC_(4.955646514892578125), SC_(2.21204280853271484375), SC_(0.905801355838775634765625), SC_(0.02334227396273797203783275881153385121431), SC_(0.001870026244073160041905611067087718660269), SC_(0.9258288125742699871613485636668508321246), SC_(0.0741711874257300128386514363331491678754) }}, 
      {{ SC_(4.95948886871337890625), SC_(0.0028835497796535491943359375), SC_(0.9688708782196044921875), SC_(1.505120348345037862046592949557491620961), SC_(343.2235542268416328838663734776345631994), SC_(0.004366101399020014604892419942279366266527), SC_(0.9956338986009799853951075800577206337335) }}, 
      {{ SC_(4.95980072021484375), SC_(27.734874725341796875), SC_(0.1355634629726409912109375), SC_(0.4956435247414797545016036224277522888071e-6), SC_(0.6283101375714764191544923982356043578174e-6), SC_(0.4409821697822553666771950005765585992489), SC_(0.5590178302177446333228049994234414007511) }}, 
      {{ SC_(10.5313282012939453125), SC_(4949.1328125), SC_(0.81474220752716064453125), SC_(0.1489333556837978488936234271997902284363e-32), SC_(0.3856025533992777785693030839310786174967e-3628), SC_(1.0), SC_(0.2589094643230594187220313290600198648947e-3595) }}, 
      {{ SC_(11.0091266632080078125), SC_(0.01806267909705638885498046875), SC_(0.913384497165679931640625), SC_(0.2290913115286434049901291231609836006505), SC_(52.29317531396651045563254082820621090561), SC_(0.004361794077969947966105722200544195166528), SC_(0.9956382059220300520338942777994558048335) }}, 
      {{ SC_(11.23558139801025390625), SC_(0.01205970533192157745361328125), SC_(0.1355634629726409912109375), SC_(0.1801437548445751478743847387122581131884e-10), SC_(80.03049850672077749426711239130895565057), SC_(0.2250938807152457506452211604760772953299e-12), SC_(0.9999999999997749061192847542493547788395) }}, 
      {{ SC_(11.8540496826171875), SC_(28582.3984375), SC_(0.905801355838775634765625), SC_(0.4196989563963513018235513889188109873243e-45), SC_(BOOST_MATH_SMALL_CONSTANT(0.6501486219485649635488047189211134227925e-29329)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1549083246551091735727896562237340707779e-29283)) }}, 
      {{ SC_(12.08311557769775390625), SC_(0.000145281082950532436370849609375), SC_(0.905801355838775634765625), SC_(0.1724057421068149458120519666384394766988), SC_(6880.009901516096508765952433178202969915), SC_(0.250583101446216964096661427068897712434e-4), SC_(0.9999749416898553783035903338572931102288) }}, 
      {{ SC_(12.1501750946044921875), SC_(2569.097900390625), SC_(0.3082362115383148193359375), SC_(0.2089415491959561405232965744934814940039e-33), SC_(0.5387791953231732231031430172096660368293e-420), SC_(1.0), SC_(0.2578612044356378151404340936138124265347e-386) }}, 
      {{ SC_(12.16957950592041015625), SC_(0.2478319108486175537109375), SC_(0.632396042346954345703125), SC_(0.0006061150384945097283682858755569887265526), SC_(1.984399791404405138978224349455243951108), SC_(0.0003053467178748392974745176907177243792601), SC_(0.9996946532821251607025254823092822756207) }}, 
      {{ SC_(12.2681884765625), SC_(31.347698211669921875), SC_(0.221111953258514404296875), SC_(0.9029530084836614196063215287799145722015e-12), SC_(0.3829678009880710229549215843075214127243e-11), SC_(0.1907930292853736794061279981430787188763), SC_(0.8092069707146263205938720018569212811237) }}, 
      {{ SC_(12.48180866241455078125), SC_(35551.12890625), SC_(0.81474220752716064453125), SC_(0.2055129032867034297384496390928032173932e-48), SC_(BOOST_MATH_SMALL_CONSTANT(0.1134877969519662374773260042042733147932e-26036)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.552217379721620791615004012932233616666e-25988)) }}, 
      {{ SC_(12.5361804962158203125), SC_(0.0011118580587208271026611328125), SC_(0.1355634629726409912109375), SC_(0.12035557149326799083259332542666489627e-11), SC_(896.3360416890092611550346282723233913045), SC_(0.1342750552197767451331942946403735674419e-14), SC_(0.9999999999999986572494478022325486680571) }}, 
      {{ SC_(12.85811328887939453125), SC_(1765.9459228515625), SC_(0.1355634629726409912109375), SC_(0.5705926152881144062747827324988998356049e-33), SC_(0.5687161280418321885891109339443080853181e-125), SC_(1.0), SC_(0.9967113362563679407527245274419457886804e-92) }}, 
      {{ SC_(13.23449611663818359375), SC_(385250.125), SC_(0.221111953258514404296875), SC_(0.1032646208826374291094174773587843687418e-64), SC_(BOOST_MATH_SMALL_CONSTANT(0.1384078056643836734532369459798476005579e-41822)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1340321636600857388950148353569427019247e-41757)) }}, 
      {{ SC_(13.28284740447998046875), SC_(0.00048034972860477864742279052734375), SC_(0.1355634629726409912109375), SC_(0.2556419984719537621177367194308522762536e-12), SC_(2078.69366536242135471302999616696632128), SC_(0.1229820452776443186126984426686117854589e-15), SC_(0.9999999999999998770179547223556813873016) }}, 
      {{ SC_(13.5956668853759765625), SC_(153334.59375), SC_(0.1355634629726409912109375), SC_(0.6876783917625074831478377703058966947845e-61), SC_(BOOST_MATH_SMALL_CONSTANT(0.7607041673086546740187797968209920060655e-9717)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1106191755362537170021971657634988714053e-9655)) }}, 
      {{ SC_(13.7348194122314453125), SC_(3484.218017578125), SC_(0.913384497165679931640625), SC_(0.6832590237907416461316409008802195758425e-39), SC_(0.2034847173393662557682179508557633804649e-3705), SC_(1.0), SC_(0.2978148992609375494249351287176812960761e-3666) }}, 
      {{ SC_(14.008861541748046875), SC_(2.686798095703125), SC_(0.9688708782196044921875), SC_(0.001064885029412461370744338050039719052766), SC_(0.2477669808586811850386960714962704372694e-4), SC_(0.9772620277829239418423712708773841814268), SC_(0.02273797221707605815762872912261581857319) }}, 
      {{ SC_(14.066776275634765625), SC_(10778.09375), SC_(0.1355634629726409912109375), SC_(0.1384429458382036725199325066251311565329e-46), SC_(0.5423868404097407030381093993157021243669e-697), SC_(1.0), SC_(0.3917764369472609965631906874797581143959e-650) }}, 
      {{ SC_(14.16046237945556640625), SC_(201.0361175537109375), SC_(0.1355634629726409912109375), SC_(0.1457467076579967985937310888273502286932e-22), SC_(0.5953315175646441172741979225187016188513e-26), SC_(0.9995916968202057773412609528963707342408), SC_(0.0004083031797942226587390471036292657591638) }}, 
      {{ SC_(14.22837734222412109375), SC_(0.000125592589029110968112945556640625), SC_(0.221111953258514404296875), SC_(0.4191360841278267573232549994819329283305e-10), SC_(7959.057087433980072463360139209842812211), SC_(0.5266152504290619088438816820102498081658e-14), SC_(0.9999999999999947338474957093809115611832) }}, 
      {{ SC_(14.366168975830078125), SC_(236787.609375), SC_(0.8350250720977783203125), SC_(0.100281550093745232466792422575515082718e-66), SC_(BOOST_MATH_SMALL_CONSTANT(0.7008982824946768264114851096978910610725e-185312)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6989304431766988620282656617551653668956e-185245)) }}, 
      {{ SC_(14.726207733154296875), SC_(0.21476264297962188720703125), SC_(0.9688708782196044921875), SC_(0.3433724310693093819167547189662152817345), SC_(2.059940898494670096164527259701817456699), SC_(0.1428746001802460104759930526155408867704), SC_(0.8571253998197539895240069473844591132296) }}, 
      {{ SC_(14.9572906494140625), SC_(0.3900313095073215663433074951171875e-4), SC_(0.12707412242889404296875), SC_(0.3015618536926003968329396709576020408051e-14), SC_(25635.71894737369159379969581373144169475), SC_(0.1176334684865526556691865610881892798014e-18), SC_(0.9999999999999999998823665315134473443308) }}, 
      {{ SC_(15.357250213623046875), SC_(0.01863213069736957550048828125), SC_(0.12707412242889404296875), SC_(0.1284230241208752102484513913830316353992e-14), SC_(50.50650654176348723073367831074223892027), SC_(0.2542702572681067944331417962210811575535e-16), SC_(0.9999999999999999745729742731893205566858) }}, 
      {{ SC_(15.364917755126953125), SC_(226.784942626953125), SC_(0.913384497165679931640625), SC_(0.9237144754778138105349974376550282816358e-25), SC_(0.1394660362001441670347986938484403591558e-243), SC_(1.0), SC_(0.1509839240399496424041030108516204158158e-218) }}, 
      {{ SC_(15.681644439697265625), SC_(0.00036581026506610214710235595703125), SC_(0.3082362115383148193359375), SC_(0.8674693174595872650196860681500669650424e-9), SC_(2730.362362247953273286364375686350702958), SC_(0.3177121577170839604484183103035436646471e-12), SC_(0.9999999999996822878422829160395515816897) }}, 
      {{ SC_(16.6584186553955078125), SC_(0.000170524624991230666637420654296875), SC_(0.632396042346954345703125), SC_(0.7245430747183076819989673100370576842949e-4), SC_(5860.896884370240189900624067253474405591), SC_(0.1236232406158642636291896529534599217805e-7), SC_(0.999999987637675938413573637081034704654) }}, 
      {{ SC_(17.026760101318359375), SC_(11.721271514892578125), SC_(0.81474220752716064453125), SC_(0.3472776252881066374547385984980315037063e-8), SC_(0.1151389423769935669070984961546305088861e-10), SC_(0.9966954835126975740422724809411400643163), SC_(0.003304516487302425957727519058859935683689) }}, 
      {{ SC_(17.084300994873046875), SC_(0.4187429845333099365234375), SC_(0.81474220752716064453125), SC_(0.004203661052637708128488774353342658694732), SC_(0.645387351033425040800967110636851350491), SC_(0.006471242634866960256436904932253540033702), SC_(0.9935287573651330397435630950677464599663) }}, 
      {{ SC_(17.104099273681640625), SC_(0.0039452277123928070068359375), SC_(0.913384497165679931640625), SC_(0.09847146833345925488375270162904703640608), SC_(250.0109136026854321016499743260356685012), SC_(0.0003937136077700488965975533705668480040311), SC_(0.999606286392229951103402446629433151996) }}, 
      {{ SC_(17.1246776580810546875), SC_(44.7584991455078125), SC_(0.12707412242889404296875), SC_(0.1049222201173919838340789891853527243191e-18), SC_(0.1005549969655430482646264041643485624018e-15), SC_(0.00104234357593582026065091508113565017741), SC_(0.9989576564240641797393490849188643498226) }}, 
      {{ SC_(17.367763519287109375), SC_(211.8177642822265625), SC_(0.81474220752716064453125), SC_(0.1228598464727664074823299491874427055223e-26), SC_(0.134073420572815897212820738814761646768e-158), SC_(1.0), SC_(0.1091271269027144147374029746464801551819e-131) }}, 
      {{ SC_(17.8663272857666015625), SC_(3551.811279296875), SC_(0.905801355838775634765625), SC_(0.8574917069550697731515696275483479205542e-49), SC_(0.531240231851813933991728714564815822366e-3648), SC_(1.0), SC_(0.6195281278442142103594159098728371820668e-3599) }}, 
      {{ SC_(18.23902130126953125), SC_(0.387346590287052094936370849609375e-4), SC_(0.9688708782196044921875), SC_(0.4918514346702719123317949815826385962091), SC_(25812.72762651756668207506025815452341111), SC_(0.1905424602655145026545908450065938550628e-4), SC_(0.9999809457539734485497345409154993406145) }}, 
      {{ SC_(18.3384532928466796875), SC_(0.4789576996699906885623931884765625e-4), SC_(0.81474220752716064453125), SC_(0.005742515693533407825828890828827578452719), SC_(20875.20638979056464032366321122837727672), SC_(0.275087776695995874046724128508732036129e-6), SC_(0.999999724912223304004125953275871491268) }}, 
      {{ SC_(18.3578662872314453125), SC_(44.260616302490234375), SC_(0.8350250720977783203125), SC_(0.2470425321126225908500656094421956373639e-16), SC_(0.2461983521306521627940343652276125309589e-37), SC_(0.9999999999999999999990034171443060888819), SC_(0.9965828556939111180611753049869381439226e-21) }}, 
      {{ SC_(18.504062652587890625), SC_(410.696044921875), SC_(0.9688708782196044921875), SC_(0.4479580769511737775750258157095141936375e-33), SC_(0.1978167753354815994969724539958034330135e-621), SC_(1.0), SC_(0.4415966259205168759046300915776808570772e-588) }}, 
      {{ SC_(19.699462890625), SC_(1044.098388671875), SC_(0.221111953258514404296875), SC_(0.1427764414528640851938160431554939361847e-42), SC_(0.2775170190989384442597659521518656372131e-128), SC_(1.0), SC_(0.194371715862211995706515872837686429047e-85) }}, 
      {{ SC_(20.811771392822265625), SC_(0.1686411633272655308246612548828125e-4), SC_(0.632396042346954345703125), SC_(0.8779696878552985850651735416020130779145e-5), SC_(59293.91525827321754111891249017431745502), SC_(0.1480707900466725772495285243001825944989e-9), SC_(0.9999999998519292099533274227504714756998) }}, 
      {{ SC_(21.513973236083984375), SC_(1.2695052623748779296875), SC_(0.632396042346954345703125), SC_(0.1892628249277866715008878383095558629667e-5), SC_(0.0182028323467953004598273326758314495929), SC_(0.0001039635727467633546556483853281441265773), SC_(0.9998960364272532366453443516146718558734) }}, 
      {{ SC_(21.758922576904296875), SC_(208175.28125), SC_(0.9688708782196044921875), SC_(0.4612573152176481888738663958595416358469e-96), SC_(BOOST_MATH_SMALL_CONSTANT(0.9671960975436415515109880867851741788795e-313691)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2096868853098322881509460069025765821922e-313594)) }}, 
      {{ SC_(21.821353912353515625), SC_(0.0424164198338985443115234375), SC_(0.632396042346954345703125), SC_(0.50822417383931418054857572691006794692e-5), SC_(20.23387388522215583387412099522239396239), SC_(0.2511748610617565545640382292453663293953e-6), SC_(0.9999997488251389382434454359617707546337) }}, 
      {{ SC_(21.8938732147216796875), SC_(12260.4462890625), SC_(0.632396042346954345703125), SC_(0.1110991956559958905405342390968892539915e-69), SC_(BOOST_MATH_SMALL_CONSTANT(0.1320801184663675939690239246407185274489e-5336)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1188848557241911842325110186828327556073e-5266)) }}, 
      {{ SC_(21.929759979248046875), SC_(21858.861328125), SC_(0.8350250720977783203125), SC_(0.2774931077356019451270225172182562988159e-75), SC_(BOOST_MATH_SMALL_CONSTANT(0.4667400506650037846545412750177604140044e-17112)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1681987904037307170893345577930663142505e-17036)) }}, 
      {{ SC_(21.9974803924560546875), SC_(241.2039947509765625), SC_(0.905801355838775634765625), SC_(0.7843991893180589007908056830908579631267e-33), SC_(0.1798121401045468000655093415604055950109e-250), SC_(1.0), SC_(0.2292354996706102012785787858677543673356e-217) }}, 
      {{ SC_(22.052242279052734375), SC_(0.02685627527534961700439453125), SC_(0.3082362115383148193359375), SC_(0.3412495872226285359474144888488643586863e-12), SC_(33.77965545931704801885704495388096429783), SC_(0.1010222225722856503263931994106391745227e-13), SC_(0.9999999999999898977777427714349673606801) }}, 
      {{ SC_(22.2946758270263671875), SC_(4700.37939453125), SC_(0.9688708782196044921875), SC_(0.1625376075227914134322624671457026695199e-61), SC_(BOOST_MATH_SMALL_CONSTANT(0.222876718150242603733562852318365160857e-7086)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1371231689373736446745878922392568188495e-7024)) }}, 
      {{ SC_(23.23316192626953125), SC_(0.13708417117595672607421875), SC_(0.905801355838775634765625), SC_(0.02605383957379540375908337816020645148022), SC_(4.428348071894942126796540322967444941726), SC_(0.005849009607039420435378760155821299478133), SC_(0.9941509903929605795646212398441787005219) }}, 
      {{ SC_(23.2837123870849609375), SC_(33643.07421875), SC_(0.3082362115383148193359375), SC_(0.1068799239730547041080141349760579217134e-83), SC_(BOOST_MATH_SMALL_CONSTANT(0.5934564882022625589600999132268094038937e-5400)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5552553427637895345283585051275346312517e-5316)) }}, 
      {{ SC_(23.31745147705078125), SC_(0.022672764956951141357421875), SC_(0.81474220752716064453125), SC_(0.001622437892479151174304580158396023246771), SC_(40.56717549342520710039911539684386231383), SC_(0.399922594508694116453159686750409274362e-4), SC_(0.9999600077405491305883546840313249590726) }}, 
      {{ SC_(23.4339599609375), SC_(23.290653228759765625), SC_(0.632396042346954345703125), SC_(0.6120488483323810340876127825639431641592e-14), SC_(0.2219752993363680426258948638666335917817e-15), SC_(0.965001723787019185029655736223016441261), SC_(0.03499827621298081497034426377698355873905) }}, 
      {{ SC_(23.523906707763671875), SC_(363844.15625), SC_(0.913384497165679931640625), SC_(0.8852371887134551999324120911774536127461e-109), SC_(BOOST_MATH_SMALL_CONSTANT(0.854289970022026695484753283772963117985e-386556)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.9650407607294434478678963380059740446762e-386447)) }}, 
      {{ SC_(23.9151668548583984375), SC_(0.13243019580841064453125), SC_(0.221111953258514404296875), SC_(0.1090292982477346593414029076655843761839e-16), SC_(4.668514756535746933179227111744024113897), SC_(0.2335417235108825537419793742198452968969e-17), SC_(0.9999999999999999976645827648911744625802) }}, 
      {{ SC_(24.385395050048828125), SC_(49.88166046142578125), SC_(0.1355634629726409912109375), SC_(0.3229288310092962335144804488418336170046e-25), SC_(0.2382116635449748094378654872427524354017e-20), SC_(0.1355619823632515389034043550239533010916e-4), SC_(0.9999864438017636748461096595644976046699) }}, 
      {{ SC_(25.94358062744140625), SC_(0.00362619315274059772491455078125), SC_(0.9688708782196044921875), SC_(0.3046507692738888516972813499195180346853), SC_(271.6820215214510436761364592323637559144), SC_(0.00112009447635084656146453462226077833215), SC_(0.9988799055236491534385354653777392216679) }}, 
      {{ SC_(26.580902099609375), SC_(2.500098705291748046875), SC_(0.81474220752716064453125), SC_(0.1625579245740934049887524200285159377885e-4), SC_(0.0003243137307939350046722880074979209220019), SC_(0.04773120126022660276002026308820168569593), SC_(0.9522687987397733972399797369117983143041) }}, 
      {{ SC_(26.6811580657958984375), SC_(0.2598920036689378321170806884765625e-4), SC_(0.913384497165679931640625), SC_(0.02950032461583076693372771151886915042511), SC_(38473.64928474534324862751624529141819336), SC_(0.7667664114116017632462221478027761577658e-6), SC_(0.9999992332335885883982367537778521972238) }}, 
      {{ SC_(27.668880462646484375), SC_(4626.693359375), SC_(0.632396042346954345703125), SC_(0.1296435483660732787044301232546660519621e-73), SC_(0.1500192979933108010732816373099939593654e-2019), SC_(1.0), SC_(0.1157167478706327199045166602225219324219e-1945) }}, 
      {{ SC_(27.841068267822265625), SC_(0.23176459968090057373046875), SC_(0.3082362115383148193359375), SC_(0.277450983410859249961629758925573998672e-15), SC_(1.822755491705946635316393599035252249908), SC_(0.1522151405788322715817021367011591147129e-15), SC_(0.9999999999999998477848594211677284182979) }}, 
      {{ SC_(28.242305755615234375), SC_(45229.91796875), SC_(0.12707412242889404296875), SC_(0.7990196810495850448097641956823676609418e-103), SC_(0.223887250907582918064012333467492757398e-2698), SC_(1.0), SC_(0.2802024233163902108539734567515392457317e-2595) }}, 
      {{ SC_(28.593597412109375), SC_(4.394314289093017578125), SC_(0.8350250720977783203125), SC_(0.8301177741196569341876030238949308187699e-6), SC_(0.2294179754707570268618676350600587609904e-5), SC_(0.2656974140459854454596235367608590058689), SC_(0.7343025859540145545403764632391409941311) }}, 
      {{ SC_(28.682727813720703125), SC_(0.0453636646270751953125), SC_(0.8350250720977783203125), SC_(0.0009661455107012884120479986102936651646781), SC_(18.48515953086525057150779816375925963023), SC_(0.522632772066436067096579898978566860452e-4), SC_(0.999947736722793356393290342010102143314) }}, 
      {{ SC_(28.738727569580078125), SC_(27.254573822021484375), SC_(0.9688708782196044921875), SC_(0.9572498697263697862453881461093343605252e-17), SC_(0.1347061208111147737313027634003403911828e-42), SC_(0.9999999999999999999999999859277995149145), SC_(0.1407220048508552589290190367969040450163e-25) }}, 
      {{ SC_(29.614292144775390625), SC_(0.435002657468430697917938232421875e-4), SC_(0.1355634629726409912109375), SC_(0.7737013612359115147454805505018838782536e-27), SC_(22984.41718847787840930792802750541434429), SC_(0.336619960772278849225161034207510040012e-31), SC_(0.9999999999999999999999999999999663380039) }}, 
      {{ SC_(30.21712493896484375), SC_(47917.328125), SC_(0.913384497165679931640625), SC_(0.6776336763750069355502711645477384929586e-110), SC_(BOOST_MATH_SMALL_CONSTANT(0.390114760786060488589150194209388553626e-50913)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5757015543751819277815605708572541789266e-50803)) }}, 
      {{ SC_(30.5350971221923828125), SC_(0.0039173173718154430389404296875), SC_(0.905801355838775634765625), SC_(0.01340698363588127821045074934336249847345), SC_(251.3176638596272682077848509721466391698), SC_(0.5334391641629631882491499304169925615287e-4), SC_(0.9999466560835837036811750850069583007438) }}, 
      {{ SC_(30.87542724609375), SC_(25.78265380859375), SC_(0.3082362115383148193359375), SC_(0.8718342814354225610387065167462673942443e-21), SC_(0.7428350393405893614201669493770856529965e-17), SC_(0.000117352026190071202713981323406974886229), SC_(0.9998826479738099287972860186765930251138) }}, 
      {{ SC_(31.3508777618408203125), SC_(354516.9375), SC_(0.81474220752716064453125), SC_(0.9094401866825436655613518390610522322292e-141), SC_(BOOST_MATH_SMALL_CONSTANT(0.1292851760462042082207729125855859647249e-259593)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1421590753734016673666087992993000747878e-259452)) }}, 
      {{ SC_(31.5992832183837890625), SC_(2.974167346954345703125), SC_(0.1355634629726409912109375), SC_(0.9033933515544142198440298653550612801455e-29), SC_(0.6181735987291850858669229798372732083083e-4), SC_(0.146139102901122230857444458947007129656e-24), SC_(0.9999999999999999999999998538608970988778) }}, 
      {{ SC_(32.361301422119140625), SC_(0.12657617032527923583984375), SC_(0.12707412242889404296875), SC_(0.3515627543575595912294473523056058630931e-30), SC_(4.796522581505533865741816552693577853579), SC_(0.7329534019356393280378750970791864073727e-31), SC_(0.9999999999999999999999999999999267046598) }}, 
      {{ SC_(32.474456787109375), SC_(480.857269287109375), SC_(0.632396042346954345703125), SC_(0.1198870036486328385012482508850487533555e-52), SC_(0.1204794083488578152029519671194520563234e-217), SC_(1.0), SC_(0.1004941358797832731865124325954045027862e-164) }}, 
      {{ SC_(32.59918212890625), SC_(0.24035865862970240414142608642578125e-4), SC_(0.8350250720977783203125), SC_(0.0004589945503402147630319808375617976950178), SC_(41600.44612776084666602124878546380557324), SC_(0.1103340439826787397894637688463490320215e-7), SC_(0.9999999889665956017321260210536231153651) }}, 
      {{ SC_(32.948390960693359375), SC_(0.000167359961778856813907623291015625), SC_(0.8350250720977783203125), SC_(0.0004267963806230203802319762649087267148703), SC_(5971.088947042175590625017553976944711217), SC_(0.7147713824096579668234406750576390886032e-7), SC_(0.9999999285228617590342033176559324942361) }}, 
      {{ SC_(33.888454437255859375), SC_(331.263580322265625), SC_(0.8350250720977783203125), SC_(0.4535727011434517253717920014222046688612e-49), SC_(0.4702005612542732083919944746569871773653e-264), SC_(1.0), SC_(0.1036659746208056245375677482680543842459e-214) }}, 
      {{ SC_(34.838344573974609375), SC_(0.00024838323588483035564422607421875), SC_(0.81474220752716064453125), SC_(0.000110674026959646824563311286265396374658), SC_(4021.925282897090220365260490176746425958), SC_(0.2751767278839998335093556980718689979271e-7), SC_(0.9999999724823272116000166490644301928131) }}, 
      {{ SC_(35.242801666259765625), SC_(301364.125), SC_(0.12707412242889404296875), SC_(0.5552864720973749931023406224100690397589e-154), SC_(BOOST_MATH_SMALL_CONSTANT(0.3450335943757303600834727106815755814629e-17823)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6213614264228380040050540433114097406271e-17669)) }}, 
      {{ SC_(35.6124725341796875), SC_(0.25395145712536759674549102783203125e-4), SC_(0.905801355838775634765625), SC_(0.007194412942043370323502024754284716097514), SC_(39373.46157644505017523016809700259111813), SC_(0.1827223550943082418355783690202895032613e-6), SC_(0.9999998172776449056917581644216309797105) }}, 
      {{ SC_(35.927936553955078125), SC_(0.02636432647705078125), SC_(0.905801355838775634765625), SC_(0.006536006690061065132195621679577078104776), SC_(34.0160493697400203998889955221757994158), SC_(0.0001921078782739724469334649000234180162257), SC_(0.9998078921217260275530665350999765819838) }}, 
      {{ SC_(36.51232147216796875), SC_(0.17059408128261566162109375), SC_(0.8350250720977783203125), SC_(0.0001534303250957219523679684882514773307855), SC_(2.94556776861927066396555528151116241826), SC_(0.5208582711449593474334844011835086057491e-4), SC_(0.9999479141728855040652566515598816491394) }}, 
      {{ SC_(37.01564788818359375), SC_(23.332645416259765625), SC_(0.913384497165679931640625), SC_(0.2168213812401695710521809477147884098627e-17), SC_(0.3098785037231789147280018839657329186902e-27), SC_(0.9999999998570812057803266193609615243812), SC_(0.1429187942196733806390384756187817546804e-9) }}, 
      {{ SC_(38.27669525146484375), SC_(4.620498180389404296875), SC_(0.221111953258514404296875), SC_(0.8902215770883091858845606185526432876961e-27), SC_(0.5413650712423482953199737388194834894534e-6), SC_(0.1644401577378070752525600970456402900463e-20), SC_(0.9999999999999999999983555984226219292475) }}, 
      {{ SC_(39.146465301513671875), SC_(0.00025820670998655259609222412109375), SC_(0.9688708782196044921875), SC_(0.1528193190529204055893789146745423950247), SC_(3868.484047792696236363740225018416110127), SC_(0.3950210999436925908479143968799440572715e-4), SC_(0.9999604978900056307409152085603120055943) }}, 
      {{ SC_(39.4506988525390625), SC_(136.2086944580078125), SC_(0.3082362115383148193359375), SC_(0.1046401415703928449021460480791107851989e-40), SC_(0.6499172262477108767221541032448302027552e-43), SC_(0.9938273638207100374070280334202013073578), SC_(0.006172636179289962592971966579798692642214) }}, 
      {{ SC_(39.513668060302734375), SC_(0.0025859004817903041839599609375), SC_(0.12707412242889404296875), SC_(0.114441788784727428298583906744515343314e-36), SC_(382.4965696456579013529430340110046661171), SC_(0.2991969023166547260127478856811919127572e-39), SC_(0.9999999999999999999999999999999999999997) }}, 
      {{ SC_(39.82184600830078125), SC_(152.626007080078125), SC_(0.221111953258514404296875), SC_(0.7584129261311716058539320034041156306212e-43), SC_(0.3317115800628570076497725906155907508941e-43), SC_(0.6957122070203084957785356385194147673907), SC_(0.3042877929796915042214643614805852326093) }}, 
      {{ SC_(40.45703887939453125), SC_(32077.298828125), SC_(0.9688708782196044921875), SC_(0.5274972812271768787697694139128653121614e-135), SC_(BOOST_MATH_SMALL_CONSTANT(0.653534335683051610104865963589483864264e-48340)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1238934036138841145558836218395953108413e-48204)) }}, 
      {{ SC_(40.55828094482421875), SC_(2.458832263946533203125), SC_(0.905801355838775634765625), SC_(0.194560320532190398741461580821072086047e-4), SC_(0.0001180576580973117370161338171703709343677), SC_(0.1414843280834169605218952831421147719938), SC_(0.8585156719165830394781047168578852280062) }}, 
      {{ SC_(41.08962249755859375), SC_(470040.03125), SC_(0.3082362115383148193359375), SC_(0.9752425688700116628147333573383493902956e-185), SC_(BOOST_MATH_SMALL_CONSTANT(0.4057571005755055542592905716135103019968e-75252)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4160576184093828170964391396667592625281e-75067)) }}, 
      {{ SC_(41.42235565185546875), SC_(0.0039965151809155941009521484375), SC_(0.8350250720977783203125), SC_(0.7483570093601061794142170984517784798211e-4), SC_(245.9687245597023094193990632989811033502), SC_(0.3042487548012529543430621190391693066668e-6), SC_(0.9999996957512451987470456569378809608307) }}, 
      {{ SC_(42.728160858154296875), SC_(1.57044470310211181640625), SC_(0.913384497165679931640625), SC_(0.0001361019068919888642281443609832535642091), SC_(0.002285967953332932614556214340365342908101), SC_(0.05619239524302985529643101317208258610858), SC_(0.9438076047569701447035689868279174138914) }}, 
      {{ SC_(43.03235626220703125), SC_(365461.34375), SC_(0.905801355838775634765625), SC_(0.6563308692566014948006600328843554947252e-188), SC_(BOOST_MATH_SMALL_CONSTANT(0.4083200099908300905039485839115530865362e-374954)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6221252558992342928897984273440111903962e-374766)) }}, 
      {{ SC_(43.688262939453125), SC_(0.011019987054169178009033203125), SC_(0.9688708782196044921875), SC_(0.1182948453489624950569266818770470091269), SC_(86.39385961971211899557668084726661214148), SC_(0.001367378330599051432954434144521106173212), SC_(0.9986326216694009485670455658554788938268) }}, 
      {{ SC_(44.11992645263671875), SC_(0.25921415726770646870136260986328125e-4), SC_(0.221111953258514404296875), SC_(0.3511712859668647997106545731357581339004e-30), SC_(38573.78732568621448631527013502161347731), SC_(0.9103884018487873036859859860774153373753e-35), SC_(0.9999999999999999999999999999999999908961) }}, 
      {{ SC_(44.16400146484375), SC_(0.3705587089061737060546875), SC_(0.1355634629726409912109375), SC_(0.1163472906159461555726531373829001038094e-39), SC_(0.5911925656899954531514499390330400989391), SC_(0.1968010042212800113349372444003097709178e-39), SC_(0.9999999999999999999999999999999999999998) }}, 
      {{ SC_(44.35755157470703125), SC_(0.00286526815034449100494384765625), SC_(0.81474220752716064453125), SC_(0.1256028523348760544533299662172549814566e-4), SC_(344.67866743749718330668405079066961949), SC_(0.3644056323288813635460389084112185333907e-7), SC_(0.9999999635594367671118636453961091588781) }}, 
      {{ SC_(44.4176177978515625), SC_(0.0028001288883388042449951171875), SC_(0.221111953258514404296875), SC_(0.2224432395000124782894923772653167124811e-30), SC_(352.7956069883608843045601218702867628118), SC_(0.6305158995569667787622147545653907093939e-33), SC_(0.9999999999999999999999999999999993694841) }}, 
      {{ SC_(44.95709228515625), SC_(0.4406783045851625502109527587890625e-4), SC_(0.3082362115383148193359375), SC_(0.3347956324494806396373677325453744033059e-24), SC_(22687.91895800761887506541658500103816675), SC_(0.1475655978272593983284572135847984574553e-28), SC_(0.9999999999999999999999999999852434402173) }}, 
      {{ SC_(45.79766845703125), SC_(2480.718017578125), SC_(0.12707412242889404296875), SC_(0.1259804823233771527697669291269210245088e-99), SC_(0.1282520196934329494253693718110955598641e-189), SC_(1.0), SC_(0.1018030867386465675676633176562499501144e-89) }}, 
      {{ SC_(45.8876495361328125), SC_(3835.69384765625), SC_(0.8350250720977783203125), SC_(0.2095396776227800319268013022132640844885e-108), SC_(0.1436386331441517479550600784227933947687e-3008), SC_(1.0), SC_(0.6854961063877103377095682562436891119733e-2900) }}, 
      {{ SC_(45.93944549560546875), SC_(0.1238458156585693359375), SC_(0.913384497165679931640625), SC_(0.002473439698616220984076705500972664961831), SC_(4.73891573081797345547575301975972726354), SC_(0.0005216698333890891587849521861409941981553), SC_(0.9994783301666109108412150478138590058018) }}, 
      {{ SC_(46.205387115478515625), SC_(461419.53125), SC_(0.632396042346954345703125), SC_(0.5079474489759754960060400183458927170744e-205), SC_(BOOST_MATH_SMALL_CONSTANT(0.1841643826332333036789004225959389385977e-200556)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3625658185792833219552831830159943286788e-200351)) }}, 
      {{ SC_(46.20613861083984375), SC_(17.2277240753173828125), SC_(0.905801355838775634765625), SC_(0.5503381473363679858700365191997254597413e-16), SC_(0.1873186360010425903381250840707434565313e-20), SC_(0.9999659641510662426455714247638091370827), SC_(0.3403584893375735442857523619086291730661e-4) }}, 
      {{ SC_(46.48529815673828125), SC_(230.984283447265625), SC_(0.12707412242889404296875), SC_(0.3958743424763615180298933316445042817009e-56), SC_(0.1354089865134714125392200419399266007453e-54), SC_(0.02840502192665440250928387940996779418467), SC_(0.9715949780733455974907161205900322058153) }}, 
      {{ SC_(47.248058319091796875), SC_(0.00016102936933748424053192138671875), SC_(0.913384497165679931640625), SC_(0.002842960961791706137633136490995901584215), SC_(6205.624152518811758082949945302510079975), SC_(0.4581263043786777349368582631087681503809e-6), SC_(0.9999995418736956213222650631417368912318) }}, 
      {{ SC_(47.3762054443359375), SC_(0.00139417522586882114410400390625), SC_(0.3082362115383148193359375), SC_(0.1843162938442819051279223660612063593014e-25), SC_(712.8599529600331148016278771718783651745), SC_(0.2585589119979863695341599414665814674649e-28), SC_(0.9999999999999999999999999999741441088002) }}, 
      {{ SC_(47.565670013427734375), SC_(0.012347941286861896514892578125), SC_(0.221111953258514404296875), SC_(0.1792446446010282589470388000940557643706e-32), SC_(76.6847350901318973662822024434040930375), SC_(0.2337422752916234388494397986547617679222e-34), SC_(0.9999999999999999999999999999999999766258) }}, 
      {{ SC_(47.917327880859375), SC_(0.00043436698615550994873046875), SC_(0.12707412242889404296875), SC_(0.2792732464268549680023039533633360805758e-44), SC_(2297.769148436670615671880818997234781364), SC_(0.1215410375828501498757183126262973655019e-47), SC_(1.0) }}, 
      {{ SC_(49.375934600830078125), SC_(0.004664070904254913330078125), SC_(0.632396042346954345703125), SC_(0.7918493691578628910632434619079350807713e-11), SC_(209.9883595217977264928814198383317790731), SC_(0.3770920307016520797076765357624405073541e-13), SC_(0.9999999999999622907969298347920292323464) }}, 
      {{ SC_(49.536712646484375), SC_(3.863943576812744140625), SC_(0.12707412242889404296875), SC_(0.5722122371900771884836644158867112909992e-46), SC_(0.128391359380948559506095811600063365791e-5), SC_(0.4456781515119508083604696797625477258644e-40), SC_(1.0) }}, 
      {{ SC_(49.81558990478515625), SC_(38816.20703125), SC_(0.221111953258514404296875), SC_(0.7140981938463531043372241188626609665284e-166), SC_(0.7805812151567538364681773771415207116495e-4249), SC_(1.0), SC_(0.1093100671424895609355803207497854177793e-4082) }}, 
      {{ SC_(49.97966766357421875), SC_(2.618212223052978515625), SC_(0.3082362115383148193359375), SC_(0.3183691967137990310274008287004766564778e-27), SC_(0.4957259188701203252073324312379755446411e-4), SC_(0.6422282648432821387495444768097760247251e-23), SC_(0.9999999999999999999999935777173515671786) }}, 
      {{ SC_(103.12812042236328125), SC_(114067.6640625), SC_(0.1355634629726409912109375), SC_(0.4840193810822088905056438194611237216161e-359), SC_(BOOST_MATH_SMALL_CONSTANT(0.4044686084940322906243428825958074889722e-7310)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.8356454809509679588208652528566894372411e-6951)) }}, 
      {{ SC_(104.1346435546875), SC_(0.27170151952304877340793609619140625e-4), SC_(0.1355634629726409912109375), SC_(0.4688222559640187561116980967428509380181e-92), SC_(36799.87642605902996151913362190767512823), SC_(0.127397779964291537566046982943678428291e-96), SC_(1.0) }}, 
      {{ SC_(114.89385223388671875), SC_(4951.32373046875), SC_(0.9688708782196044921875), SC_(0.1308089138735526057460193163216993396376e-238), SC_(BOOST_MATH_SMALL_CONSTANT(0.8370057028341217097143028672278420279251e-7466)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6398690104890094876228279887907518512493e-7227)) }}, 
      {{ SC_(119.378936767578125), SC_(0.4208850805298425257205963134765625e-4), SC_(0.221111953258514404296875), SC_(0.618124420969444256377785266068493817182e-80), SC_(23754.10000237703534142589158822470559032), SC_(0.260217992223485427769847730483781873655e-84), SC_(1.0) }}, 
      {{ SC_(120.53275299072265625), SC_(0.00151182874105870723724365234375), SC_(0.81474220752716064453125), SC_(0.8130315786385940598826717432380190957003e-12), SC_(656.1085234044774548296590454561463146907), SC_(0.1239172407667954880152803421760691006921e-14), SC_(0.9999999999999987608275923320451198471966) }}, 
      {{ SC_(123.761322021484375), SC_(31601.099609375), SC_(0.632396042346954345703125), SC_(0.3907984695587686805179723879016625105735e-352), SC_(BOOST_MATH_SMALL_CONSTANT(0.4038022423803575124969725251308526345727e-13763)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1033274881644932621954231446766784155708e-13410)) }}, 
      {{ SC_(124.63626861572265625), SC_(0.0002591950469650328159332275390625), SC_(0.3082362115383148193359375), SC_(0.2286602885657900358920089526905234202367e-65), SC_(3852.703804376633993315113456969305977472), SC_(0.5935060159725597784541759251964673259975e-69), SC_(1.0) }}, 
      {{ SC_(129.1541290283203125), SC_(0.001998660154640674591064453125), SC_(0.8350250720977783203125), SC_(0.3476183625813380126233045754717090061941e-11), SC_(494.9318626564336077744675904785127268524), SC_(0.7023559984915367785981115982916012717189e-14), SC_(0.999999999999992976440015084632214018884) }}, 
      {{ SC_(134.4938201904296875), SC_(0.369370639324188232421875), SC_(0.81474220752716064453125), SC_(0.227660055512276422731147692360582174155e-13), SC_(0.3941976092074812035130070739014794440327), SC_(0.5775277429255973076361119528365283386592e-13), SC_(0.9999999999999422472257074402692363888047) }}, 
      {{ SC_(134.6666259765625), SC_(1.34013068675994873046875), SC_(0.3082362115383148193359375), SC_(0.9693383317484800528178309536604610067942e-71), SC_(0.001248080442774986081119014683534380655141), SC_(0.7766633451873102668027649589750566871098e-68), SC_(1.0) }}, 
      {{ SC_(135.4109954833984375), SC_(0.00301261036656796932220458984375), SC_(0.905801355838775634765625), SC_(0.1109605271415662750236334436887917080405e-6), SC_(326.50365289455264554913282902592824183), SC_(0.3398446728548322019973559428175602751721e-9), SC_(0.9999999996601553271451677980026440571824) }}, 
      {{ SC_(136.5994873046875), SC_(43.41033172607421875), SC_(0.905801355838775634765625), SC_(0.2853398846078208710261830663332092623754e-43), SC_(0.1453317414454361857387631649481562211522e-51), SC_(0.9999999949067148146860648367467346428607), SC_(0.5093285185313935163253265357139339803881e-8) }}, 
      {{ SC_(136.9409332275390625), SC_(131351.0625), SC_(0.12707412242889404296875), SC_(0.3043829429539097644465665899119787466457e-468), SC_(BOOST_MATH_SMALL_CONSTANT(0.2534855485403572206098440978016365817555e-7879)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.8327849980041111480192868524026541897304e-7411)) }}, 
      {{ SC_(140.242523193359375), SC_(10.396793365478515625), SC_(0.3082362115383148193359375), SC_(0.4808562615150081561181266391405746679761e-75), SC_(0.3039808893830175850688892962372282308458e-16), SC_(0.1581863460203008465056674226001222325059e-58), SC_(1.0) }}, 
      {{ SC_(141.92529296875), SC_(0.000180798946530558168888092041015625), SC_(0.913384497165679931640625), SC_(0.198065041643384141690518647089004395326e-6), SC_(5525.479599810219206389522532998440566081), SC_(0.3584576470847656128976467755574628703498e-10), SC_(0.9999999999641542352915234387102353224443) }}, 
      {{ SC_(141.9384918212890625), SC_(13.08813571929931640625), SC_(0.9688708782196044921875), SC_(0.236888370715470680626417233504345420607e-19), SC_(0.244365343557627256774710860721631870458e-22), SC_(0.9989694997509884144768508510553717884208), SC_(0.001030500249011585523149148944628211579229) }}, 
      {{ SC_(143.5271759033203125), SC_(276260.9375), SC_(0.8350250720977783203125), SC_(0.3742702817290309906721968317388625320875e-534), SC_(BOOST_MATH_SMALL_CONSTANT(0.3525421786589960772588483801597439607466e-216213)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.9419454225175005886234763722070685227395e-215679)) }}, 
      {{ SC_(143.902008056640625), SC_(3178.291015625), SC_(0.12707412242889404296875), SC_(0.1041390448460074214940384887504552195879e-257), SC_(0.1082471811489777657966730157841617119947e-318), SC_(1.0), SC_(0.1039448569064995085861634581761604480829e-60) }}, 
      {{ SC_(144.141387939453125), SC_(0.02794832177460193634033203125), SC_(0.3082362115383148193359375), SC_(0.210121969321088412000745631560412360473e-75), SC_(30.66336863860899471105345531623399851862), SC_(0.6852540299715104354573897272954159449745e-77), SC_(1.0) }}, 
      {{ SC_(144.9135894775390625), SC_(216.5679473876953125), SC_(0.12707412242889404296875), SC_(0.2432218642440712886007055366627878680872e-144), SC_(0.5225315287504169883069444039355278897519e-106), SC_(0.4654683035600025384322185120148805193791e-38), SC_(0.9999999999999999999999999999999999999953) }}, 
      {{ SC_(146.9971466064453125), SC_(0.03688235580921173095703125), SC_(0.632396042346954345703125), SC_(0.9828141249232948864866827305334372752907e-31), SC_(22.10701285297225737646153116481359017817), SC_(0.4445712007586573974443384645607925683607e-32), SC_(0.999999999999999999999999999999995554288) }}, 
      {{ SC_(150.13287353515625), SC_(33750.34765625), SC_(0.913384497165679931640625), SC_(0.7629791065827503204271437688211232798831e-419), SC_(BOOST_MATH_SMALL_CONSTANT(0.1222534096324452698576282770002449221401e-35866)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1602316611001274496355022047977218365824e-35447)) }}, 
      {{ SC_(150.599945068359375), SC_(306722.0), SC_(0.913384497165679931640625), SC_(0.3686020516088520592717144023675096548389e-564), SC_(BOOST_MATH_SMALL_CONSTANT(0.6826246931306197753836329254533494563908e-325874)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.185192863184331335763940971897577903196e-325309)) }}, 
      {{ SC_(151.1553497314453125), SC_(0.00040882988832890987396240234375), SC_(0.9688708782196044921875), SC_(0.00151337404731319656908269530771069428744), SC_(2440.418116026032771985404725000611438149), SC_(0.6201286160303603506040678590772569703063e-6), SC_(0.999999379871383969639649395932140922743) }}, 
      {{ SC_(152.060577392578125), SC_(48686.92578125), SC_(0.9688708782196044921875), SC_(0.1566704821027432284960697774985309189842e-447), SC_(BOOST_MATH_SMALL_CONSTANT(0.1462550236236577686175407186333220017517e-73369)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.9335199691780160165629655945998021793685e-72922)) }}, 
      {{ SC_(152.8331756591796875), SC_(0.01893596164882183074951171875), SC_(0.1355634629726409912109375), SC_(0.173789437775042018661111866596562065448e-134), SC_(47.50713183618351040884535215505963259578), SC_(0.3658175752944032252165469442263971607961e-136), SC_(1.0) }}, 
      {{ SC_(153.7213134765625), SC_(247144.21875), SC_(0.9688708782196044921875), SC_(0.458380108098649973404151004050230761875e-560), SC_(BOOST_MATH_SMALL_CONSTANT(0.2585179282730915085169292532378029360404e-372412)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5639815596392650238174911274822139942524e-371852)) }}, 
      {{ SC_(160.7382049560546875), SC_(25.5228900909423828125), SC_(0.1355634629726409912109375), SC_(0.5688547289308073203114763516392845261777e-143), SC_(0.2571459217934017533228733831741249744827e-32), SC_(0.2212186469703537302353523764298414585353e-110), SC_(1.0) }}, 
      {{ SC_(174.9843292236328125), SC_(3107.8662109375), SC_(0.221111953258514404296875), SC_(0.3615410786240862772639301332997469171478e-297), SC_(0.1970044949079050617211883893754729790376e-454), SC_(1.0), SC_(0.5449021053365314558486616995281192927788e-157) }}, 
      {{ SC_(182.269805908203125), SC_(4.91394901275634765625), SC_(0.81474220752716064453125), SC_(0.4974658008839300075233400944071145768898e-21), SC_(0.1557816258000933394170684974660506345715e-9), SC_(0.3193353505763985662610590462172514124542e-11), SC_(0.9999999999968066464942360143373894095378) }}, 
      {{ SC_(182.4111480712890625), SC_(1.8299617767333984375), SC_(0.221111953258514404296875), SC_(0.1257179411236037996814190087574533157581e-121), SC_(0.6816139347461980262551131510755840907283e-4), SC_(0.1844415654008239033739014559927052450466e-117), SC_(1.0) }}, 
      {{ SC_(191.581298828125), SC_(2.2190074920654296875), SC_(0.913384497165679931640625), SC_(0.818303885039488969076427373132265901127e-11), SC_(0.9528526405685344833422899124374382328851e-5), SC_(0.8587930047584097508247405741775224777972e-6), SC_(0.9999991412069952415902491752594258224775) }}, 
      {{ SC_(193.9307708740234375), SC_(0.488285362720489501953125), SC_(0.1355634629726409912109375), SC_(0.2756164985499759765668542836759401656321e-170), SC_(0.1386656566083260766266602990901950991737), SC_(0.1987633457998040360412131164374774681596e-169), SC_(1.0) }}, 
      {{ SC_(194.9492034912109375), SC_(25.73528289794921875), SC_(0.81474220752716064453125), SC_(0.3794752662261748555539548669278054074362e-37), SC_(0.1606216100529939192494848255703696421246e-34), SC_(0.002356973342594440300373688940889980449801), SC_(0.9976430266574055596996263110591100195502) }}, 
      {{ SC_(206.4715423583984375), SC_(4999.44482421875), SC_(0.3082362115383148193359375), SC_(0.9664840459027490442579633751284112615285e-378), SC_(0.1589222089973494413454176328959237907617e-908), SC_(1.0), SC_(0.1644333495944130067402465205464989686328e-530) }}, 
      {{ SC_(209.1755218505859375), SC_(4321.3662109375), SC_(0.913384497165679931640625), SC_(0.1421115387752692033915658409193225706805e-368), SC_(0.1371116603810202130897814545091836021412e-4602), SC_(1.0), SC_(0.9648172242920003595658828821448992914472e-4234) }}, 
      {{ SC_(215.1907958984375), SC_(0.25329818527097813785076141357421875e-4), SC_(0.9688708782196044921875), SC_(0.0001464897688255976900811653585245822205338), SC_(39473.21554892836728858453251636025514219), SC_(0.3711118191026973474994418758364436476617e-8), SC_(0.9999999962888818089730265250055812416356) }}, 
      {{ SC_(216.6281280517578125), SC_(348.682403564453125), SC_(0.221111953258514404296875), SC_(0.164783927326766909594538745614784038617e-181), SC_(0.8349234060239147498805922200825817369842e-164), SC_(0.1973641248261364415368843487755364206024e-17), SC_(0.9999999999999999980263587517386355846312) }}, 
      {{ SC_(217.62652587890625), SC_(17.93491363525390625), SC_(0.632396042346954345703125), SC_(0.1131025448330549907800879310735393550066e-52), SC_(0.1771247617123621225419147341481414126187e-27), SC_(0.6385473365758164932877615155616247159519e-25), SC_(0.9999999999999999999999999361452663424184) }}, 
      {{ SC_(226.324554443359375), SC_(39210.1875), SC_(0.81474220752716064453125), SC_(0.9619060456905223231314220053099666967455e-606), SC_(BOOST_MATH_SMALL_CONSTANT(0.5451448812286429935081677790602533918603e-28735)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5667340211354015356418078146042937541176e-28129)) }}, 
      {{ SC_(231.5256805419921875), SC_(0.0435504876077175140380859375), SC_(0.8350250720977783203125), SC_(0.1764246149052565528301167841309519016063e-19), SC_(17.69346219873896208988238740952600405439), SC_(0.9971175393690364512707768877106432329784e-21), SC_(0.9999999999999999999990028824606309635487) }}, 
      {{ SC_(235.0335540771484375), SC_(0.00019701055134646594524383544921875), SC_(0.81474220752716064453125), SC_(0.27531994594023280538911518101612379631e-22), SC_(5069.839200047210415604381785276244494116), SC_(0.5430545922199446130158487343748527895309e-26), SC_(0.9999999999999999999999999945694540778006) }}, 
      {{ SC_(246.57464599609375), SC_(0.1393482387065887451171875), SC_(0.8350250720977783203125), SC_(0.9273358296306291857739256587313571273325e-21), SC_(3.120782444115820198187644033429437246973), SC_(0.2971485024145481213597968821633580834816e-21), SC_(0.9999999999999999999997028514975854518786) }}, 
      {{ SC_(247.0611572265625), SC_(2.6212046146392822265625), SC_(0.1355634629726409912109375), SC_(0.1233763466042882539812276757354160897425e-216), SC_(0.7698914562888141892386152475177160543465e-6), SC_(0.1602516115700410042106569220683381652875e-210), SC_(1.0) }}, 
      {{ SC_(247.6795196533203125), SC_(0.3789966404438018798828125), SC_(0.905801355838775634765625), SC_(0.3901476747947370858941749167350950866057e-12), SC_(0.2904064811325379343604720187688880329981), SC_(0.1343453745497347754446696003333483251126e-11), SC_(0.9999999999986565462545026522455533039967) }}, 
      {{ SC_(255.30865478515625), SC_(1.6643607616424560546875), SC_(0.8350250720977783203125), SC_(0.1224195708757557681609468212080995446386e-22), SC_(0.8876048803681905763494927429214001435258e-4), SC_(0.1379212457968622988680230984865860809486e-18), SC_(0.999999999999999999862078754203137701132) }}, 
      {{ SC_(258.402679443359375), SC_(3463.272216796875), SC_(0.905801355838775634765625), SC_(0.4274015793494460038889981037032130451897e-408), SC_(0.1743410487391723622973621969516092419379e-3567), SC_(1.0), SC_(0.4079092290780472578614331149052623639426e-3159) }}, 
      {{ SC_(258.608306884765625), SC_(0.00045402420801110565662384033203125), SC_(0.221111953258514404296875), SC_(0.1609294182526599030396675264914452400612e-171), SC_(2196.404029586864051763637824429964329181), SC_(0.7326949690714698156932579494006958296424e-175), SC_(1.0) }}, 
      {{ SC_(262.12615966796875), SC_(41.1895599365234375), SC_(0.913384497165679931640625), SC_(0.196155236562708974581412258075747841396e-52), SC_(0.5197083331772625347348423839179114220326e-55), SC_(0.9973575264838519192374913606794778162899), SC_(0.002642473516148080762508639320522183710125) }}, 
      {{ SC_(269.2437744140625), SC_(248884.53125), SC_(0.221111953258514404296875), SC_(0.4495990210228204825261153088053209387021e-916), SC_(BOOST_MATH_SMALL_CONSTANT(0.4148636163061508725942755799652902000729e-27191)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.9227413693258319379201933796485124942393e-26275)) }}, 
      {{ SC_(271.8258056640625), SC_(40013.29296875), SC_(0.1355634629726409912109375), SC_(0.2729944157865051084814361794570061186133e-708), SC_(0.7284127379139313776586553004405313755609e-2771), SC_(1.0), SC_(0.2668233105850727419781718050477605101313e-2062) }}, 
      {{ SC_(272.993988037109375), SC_(213.918212890625), SC_(0.81474220752716064453125), SC_(0.2212637644878602262122560508627173804609e-145), SC_(0.9520034199590249554257981594736319403554e-183), SC_(0.999999999999999999999999999999999999957), SC_(0.4302572642938366051420975175205847076302e-37) }}, 
      {{ SC_(273.05694580078125), SC_(120.83953857421875), SC_(0.913384497165679931640625), SC_(0.9441715054771388442198671739449233407868e-106), SC_(0.8626889088160577360563703310298453850553e-141), SC_(0.999999999999999999999999999999999990863), SC_(0.9136993690358154529882782822264029164688e-35) }}, 
      {{ SC_(280.6956787109375), SC_(40294.875), SC_(0.221111953258514404296875), SC_(0.2420346888698004161420917282827963577001e-728), SC_(0.1249196613161309469489033186755940857252e-4560), SC_(1.0), SC_(0.5161229652635864202330597197876994386475e-3832) }}, 
      {{ SC_(293.792144775390625), SC_(43.755107879638671875), SC_(0.12707412242889404296875), SC_(0.626213412688692816194961369857081022211e-268), SC_(0.1178646487717884142561923122934594965963e-56), SC_(0.5312987560003492924332910773570388749067e-211), SC_(1.0) }}, 
      {{ SC_(294.09173583984375), SC_(0.0041162283159792423248291015625), SC_(0.1355634629726409912109375), SC_(0.2315371791014920935486914615013948495911e-257), SC_(236.7646773686439981757341670939706842877), SC_(0.9779211226723120568625519548237377914634e-260), SC_(1.0) }}, 
      {{ SC_(295.04150390625), SC_(4611.853515625), SC_(0.81474220752716064453125), SC_(0.5643528361826129736519071496970908840319e-485), SC_(0.1866904355931125055366992084754075152891e-3406), SC_(1.0), SC_(0.3308044606560696352715396097168974011776e-2921) }}, 
      {{ SC_(310.44097900390625), SC_(0.3617111724452115595340728759765625e-4), SC_(0.81474220752716064453125), SC_(0.4085436315134407397665257096352830176133e-29), SC_(27640.05464626633929170953687365053540456), SC_(0.1478085469590876645316585336633217008742e-33), SC_(0.9999999999999999999999999999999998521915) }}, 
      {{ SC_(312.348907470703125), SC_(17.8608245849609375), SC_(0.8350250720977783203125), SC_(0.9723876523048781193094731483076253335422e-40), SC_(0.4134164544643688521390779638495418981003e-30), SC_(0.235207776946375077595003817041232173269e-9), SC_(0.9999999997647922230536249224049961829588) }}, 
      {{ SC_(312.48370361328125), SC_(290847.96875), SC_(0.81474220752716064453125), SC_(0.4523765765919173382352735144370898815018e-1064), SC_(BOOST_MATH_SMALL_CONSTANT(0.1254850832918198558931943494569323341016e-212998)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2773907620000807787162089481974537683579e-211934)) }}, 
      {{ SC_(319.8160400390625), SC_(0.0034338268451392650604248046875), SC_(0.12707412242889404296875), SC_(0.1039487352249436822822754219339456868299e-288), SC_(284.9482190829596218680197390653836755493), SC_(0.3647986836326923012989805955948769306774e-291), SC_(1.0) }}, 
      {{ SC_(320.71142578125), SC_(4.67389774322509765625), SC_(0.905801355838775634765625), SC_(0.9850171618247693902460627494485569139128e-20), SC_(0.2800574695987022181206384892775397912514e-10), SC_(0.3517196534303343749823322883058843095715e-9), SC_(0.9999999996482803465696656250176677116941) }}, 
      {{ SC_(322.667938232421875), SC_(0.000298001919873058795928955078125), SC_(0.1355634629726409912109375), SC_(0.3346120796609924218957333268979457363039e-282), SC_(3349.337042651967348019908243227315338955), SC_(0.9990397365206648353102493000873009612277e-286), SC_(1.0) }}, 
      {{ SC_(333.2742919921875), SC_(0.0451156608760356903076171875), SC_(0.9688708782196044921875), SC_(0.2015910821067014358020191371622465099008e-5), SC_(16.6450344808057419754379344400681482869), SC_(0.1211118294312347957164841213867908691819e-6), SC_(0.9999998788881705687652042835158786132091) }}, 
      {{ SC_(339.176666259765625), SC_(0.4111320078372955322265625), SC_(0.9688708782196044921875), SC_(0.475871489271297053139403787881672962571e-6), SC_(0.1966151866730698732605667437164230613948), SC_(0.2420313229946520491730901085230714735745e-5), SC_(0.9999975796867700534795082690989147692853) }}, 
      {{ SC_(341.38720703125), SC_(0.384202649001963436603546142578125e-4), SC_(0.632396042346954345703125), SC_(0.9108135773369031849026323044395471973537e-70), SC_(26021.52295326780794111723906784983713539), SC_(0.3500231631225574895492215967492848453865e-74), SC_(1.0) }}, 
      {{ SC_(341.41339111328125), SC_(468.468475341796875), SC_(0.3082362115383148193359375), SC_(0.3565543496946831024491691828768394458409e-251), SC_(0.629433015816388768125402133427542758933e-240), SC_(0.5664690931888984677553491428462049448298e-11), SC_(0.9999999999943353090681110153224465085715) }}, 
      {{ SC_(343.1463623046875), SC_(0.0001005965095828287303447723388671875), SC_(0.8350250720977783203125), SC_(0.2356381288057253516466149971441467456345e-28), SC_(9934.290991814903354659373047105922533208), SC_(0.237196724959912250831117297940355079515e-32), SC_(0.9999999999999999999999999999999976280328) }}, 
      {{ SC_(343.94287109375), SC_(36665.765625), SC_(0.3082362115383148193359375), SC_(0.4202526370890087084639418504930818300741e-848), SC_(BOOST_MATH_SMALL_CONSTANT(0.12369306034179296732473775142835346482e-6047)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2943302419196836892889910632108001147524e-5199)) }}, 
      {{ SC_(352.70654296875), SC_(212717.25), SC_(0.905801355838775634765625), SC_(0.1441972851961034211719947431273403233423e-1134), SC_(BOOST_MATH_SMALL_CONSTANT(0.1446128095776781832284043928815361433868e-218258)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1002881638035068850146195912609331103968e-217123)) }}, 
      {{ SC_(356.28717041015625), SC_(0.0463746227324008941650390625), SC_(0.81474220752716064453125), SC_(0.274854253144334457190696078886098926166e-33), SC_(16.01525623507416452576597962646525649559), SC_(0.1716202657703288651931002315562720282044e-34), SC_(0.999999999999999999999999999999999982838) }}, 
      {{ SC_(356.7762451171875), SC_(3.3235576152801513671875), SC_(0.9688708782196044921875), SC_(0.1364165363012484972823945395989807921582e-10), SC_(0.8931730062261176515126969192659486206286e-8), SC_(0.001524995725542705293456313521237359078585), SC_(0.9984750042744572947065436864787626409214) }}, 
      {{ SC_(361.524810791015625), SC_(0.01847557537257671356201171875), SC_(0.905801355838775634765625), SC_(0.8022720212822092847986147827704351777061e-17), SC_(48.04400546905792875821423344152799898027), SC_(0.166986914069623396372200973840296672574e-18), SC_(0.9999999999999999998330130859303766036278) }}, 
      {{ SC_(362.19921875), SC_(344.353759765625), SC_(0.9688708782196044921875), SC_(0.4787224091975894935100062848169341926213e-213), SC_(0.4299691468982810933904699845220691067036e-526), SC_(1.0), SC_(0.89815964040407848330304241199415911355e-313) }}, 
      {{ SC_(362.229278564453125), SC_(131165.359375), SC_(0.3082362115383148193359375), SC_(0.5057364165135122178363336585033416342913e-1085), SC_(BOOST_MATH_SMALL_CONSTANT(0.1841654023932158082629951444684797586402e-21181)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3641529389218806520857720108160630097918e-20096)) }}, 
      {{ SC_(367.16644287109375), SC_(0.23287344447453506290912628173828125e-4), SC_(0.3082362115383148193359375), SC_(0.8509062422527874302645435035136800483759e-190), SC_(42935.29789300576214757142986631869718034), SC_(0.1981833791798150303512183506262879890148e-194), SC_(1.0) }}, 
      {{ SC_(369.317962646484375), SC_(35824.7421875), SC_(0.12707412242889404296875), SC_(0.1401277778307608857522144096233095426528e-895), SC_(0.103560415644484015438123327460794726839e-2448), SC_(1.0), SC_(0.7390427311960869932370824428985573234745e-1553) }}, 
      {{ SC_(373.486236572265625), SC_(0.0390747748315334320068359375), SC_(0.12707412242889404296875), SC_(0.7276873943205061127949449274485728007157e-337), SC_(19.8771556345436876725173165123440803917), SC_(0.3660923160735775747610133192362243213198e-338), SC_(1.0) }}, 
      {{ SC_(374.011383056640625), SC_(0.1863805353641510009765625), SC_(0.913384497165679931640625), SC_(0.3680853549931935424729101433697114219094e-16), SC_(1.63996778181418168964791681198568110514), SC_(0.2244466989381989216250070729496838307869e-16), SC_(0.9999999999999999775553301061801078374993) }}, 
      {{ SC_(377.012786865234375), SC_(0.00041205019806511700153350830078125), SC_(0.12707412242889404296875), SC_(0.5018255520456287019307505533535785648385e-340), SC_(2420.389662042830422339503339413745095856), SC_(0.2073325464553849847840499156751296217151e-343), SC_(1.0) }}, 
      {{ SC_(377.90087890625), SC_(133.8535003662109375), SC_(0.8350250720977783203125), SC_(0.4764303775395275156357475406276231056301e-128), SC_(0.8979815675883332024694445404371251955499e-136), SC_(0.999999981151882982344684499992705910614), SC_(0.1884811701765531550000729408938601969429e-7) }}, 
      {{ SC_(378.573211669921875), SC_(29545.880859375), SC_(0.905801355838775634765625), SC_(0.1819161083786019421236995979220271276677e-882), SC_(BOOST_MATH_SMALL_CONSTANT(0.3567768305107357071563069529318010263981e-30333)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1961216264412469843105695279989097621613e-29450)) }}, 
      {{ SC_(382.9013671875), SC_(0.46764689614064991474151611328125e-4), SC_(0.905801355838775634765625), SC_(0.9554051624934791521808252488493868262495e-18), SC_(21377.13258428691757807457543956816125374), SC_(0.4469285853593581217844881708560939442293e-22), SC_(0.9999999999999999999999553071414640641878) }}, 
      {{ SC_(389.089813232421875), SC_(0.0373030789196491241455078125), SC_(0.221111953258514404296875), SC_(0.3231575825547556828096359195883892985532e-257), SC_(21.02788392868429236020299549248550271942), SC_(0.1536805052047743276126284045624474354474e-258), SC_(1.0) }}, 
      {{ SC_(389.16900634765625), SC_(413674.96875), SC_(0.632396042346954345703125), SC_(0.1523073710619273141670447579828970845159e-1347), SC_(BOOST_MATH_SMALL_CONSTANT(0.6203083669742001315335466647235180952343e-179874)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4072740292536375431999707817360029371767e-178526)) }}, 
      {{ SC_(391.8837890625), SC_(0.19626211724244058132171630859375e-4), SC_(0.8350250720977783203125), SC_(0.3156660959615283137675626160491049296641e-32), SC_(50945.72155259349797640877051570356243093), SC_(0.6196125726390044116169667113856543669063e-37), SC_(0.999999999999999999999999999999999999938) }}, 
      {{ SC_(393.736419677734375), SC_(0.004322923719882965087890625), SC_(0.632396042346954345703125), SC_(0.3004974652924474956100160452044348570937e-80), SC_(224.8686489014385231824592335003405580727), SC_(0.133632441320958709225529491856927709637e-82), SC_(1.0) }}, 
      {{ SC_(393.983001708984375), SC_(0.2691637575626373291015625), SC_(0.221111953258514404296875), SC_(0.1870527793829857996963117135782901988535e-260), SC_(0.6714385221846706365806862683133772310713), SC_(0.2785851171814942556490675848554657168373e-260), SC_(1.0) }}, 
      {{ SC_(396.012939453125), SC_(0.3836281299591064453125), SC_(0.12707412242889404296875), SC_(0.4301288599143994816570364356887688358255e-357), SC_(0.233460848724051421741768156340076225462), SC_(0.1842402536721726057994231516390974357011e-356), SC_(1.0) }}, 
      {{ SC_(396.501617431640625), SC_(0.00025799489230848848819732666015625), SC_(0.905801355838775634765625), SC_(0.2403255365257639667540869044416409896705e-18), SC_(3869.492850349797971737181114829875494191), SC_(0.6210776084107218287862374702379978340916e-22), SC_(0.9999999999999999999999378922391589278171) }}, 
      {{ SC_(398.525390625), SC_(0.3569120235624723136425018310546875e-4), SC_(0.12707412242889404296875), SC_(0.2525921292916898143688329256341569736429e-359), SC_(28011.54612355233296168609852241990334577), SC_(0.9017429033640821536450249063192331434311e-364), SC_(1.0) }}, 
      {{ SC_(399.652587890625), SC_(0.0480428077280521392822265625), SC_(0.913384497165679931640625), SC_(0.4724066069503013068013794705665556538261e-17), SC_(15.21131085667592202681534268766103161162), SC_(0.3105627196770961107238612064348950002903e-18), SC_(0.9999999999999999996894372803229038892761) }}, 
      {{ SC_(403.239715576171875), SC_(231.1188201904296875), SC_(0.905801355838775634765625), SC_(0.4209076100284479575459272050589722133011e-181), SC_(0.2097420529948794342641861558008743468945e-256), SC_(1.0), SC_(0.4983090065316318723760331393489524960538e-75) }}, 
      {{ SC_(407.583343505859375), SC_(1515.2218017578125), SC_(0.8350250720977783203125), SC_(0.6106516291260666365026418574546229451623e-432), SC_(0.1667628395292839871395498306645192446908e-1220), SC_(1.0), SC_(0.2730899772885997692805519448869943783372e-788) }}, 
      {{ SC_(408.7735595703125), SC_(3.076730251312255859375), SC_(0.632396042346954345703125), SC_(0.1378287322801105462206772129061690311001e-84), SC_(0.1968181405629450728659864963206833038828e-7), SC_(0.7002846987878694790764947964174289324482e-77), SC_(1.0) }}, 
      {{ SC_(409.088836669921875), SC_(41189.671875), SC_(0.8350250720977783203125), SC_(0.143904091158808850646611131514643898925e-998), SC_(BOOST_MATH_SMALL_CONSTANT(0.1363139954023903785257309899372358371717e-32270)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.9472558723292846634420162507585859990812e-31272)) }}, 
      {{ SC_(412.0701904296875), SC_(0.000211175109143368899822235107421875), SC_(0.632396042346954345703125), SC_(0.647616717896141274500164522105167342336e-84), SC_(4728.814144546499136816670985737169832699), SC_(0.1369511886279152459177282803600209927911e-87), SC_(1.0) }}, 
      {{ SC_(412.55084228515625), SC_(0.3415349419810809195041656494140625e-4), SC_(0.913384497165679931640625), SC_(0.1598951502476228351696480573690411165113e-17), SC_(29272.98341448901344132555396999064805538), SC_(0.5462208890142738821114941245966009915949e-22), SC_(0.9999999999999999999999453779110985726118) }}, 
      {{ SC_(412.772796630859375), SC_(10.2405414581298828125), SC_(0.221111953258514404296875), SC_(0.7210631161000411893596368421118781124139e-274), SC_(0.9132989709772482176203558464533790146331e-21), SC_(0.7895148675449499217836227273432362656399e-253), SC_(1.0) }}, 
      {{ SC_(413.771148681640625), SC_(210.19207763671875), SC_(0.1355634629726409912109375), SC_(0.1229532018885143519530085872807521574186e-374), SC_(0.1535284080116869474721557382363960697841e-173), SC_(0.8008498458419165127509827916525553946866e-201), SC_(1.0) }}, 
      {{ SC_(415.74554443359375), SC_(1.32294642925262451171875), SC_(0.12707412242889404296875), SC_(0.7549174221810121502717526237701650680409e-375), SC_(0.0003066699505629536665884706792860346267623), SC_(0.2461660885897725318891548391823367919716e-371), SC_(1.0) }}, 
      {{ SC_(419.13214111328125), SC_(4420.99755859375), SC_(0.632396042346954345703125), SC_(0.7407617462496530577763132713638848532344e-620), SC_(0.5164426832634216817430840635953603860361e-2008), SC_(1.0), SC_(0.6971778522285748528924408977666419339682e-1388) }}, 
      {{ SC_(419.34033203125), SC_(0.00151284574531018733978271484375), SC_(0.913384497165679931640625), SC_(0.8476478364612169062479548704808673361511e-18), SC_(654.4254343208734946159619247852681637715), SC_(0.1295255031370929110178745795102234278647e-20), SC_(0.9999999999999999999987047449686290708898) }}, 
      {{ SC_(419.584075927734375), SC_(0.002538044936954975128173828125), SC_(0.3082362115383148193359375), SC_(0.1202488174477083325907762514356474369212e-216), SC_(387.4460414331091288271121581334002725513), SC_(0.3103627462624850874050904942689863752141e-219), SC_(1.0) }}, 
      {{ SC_(446.7720947265625), SC_(0.13732771575450897216796875), SC_(0.632396042346954345703125), SC_(0.6470227542994612812986619257281890098288e-91), SC_(2.953024413658057743687435482679677536763), SC_(0.2191051151852693686381210108675725739574e-91), SC_(1.0) }}, 
      {{ SC_(456.1903076171875), SC_(0.002997568808495998382568359375), SC_(0.221111953258514404296875), SC_(0.2932462781569434944772885666165452845617e-301), SC_(326.9738781432603861029409834126639297054), SC_(0.8968492523689018494217012933996865298417e-304), SC_(1.0) }}, 
      {{ SC_(473.50396728515625), SC_(2391.615234375), SC_(0.1355634629726409912109375), SC_(0.6427607325399612250424567229505498036024e-564), SC_(0.1908855843450567141251098864376542233725e-558), SC_(0.3367245202995933628525893930444210636251e-5), SC_(0.9999966327547970040663714741060695557894) }}, 
      {{ SC_(477.203277587890625), SC_(0.0049147461540997028350830078125), SC_(0.9688708782196044921875), SC_(0.1741668940985797760911155038012495611787e-7), SC_(196.8396382927676191756430337270244331433), SC_(0.8848161660616531178339587580130596657789e-10), SC_(0.9999999999115183833938346882166041241987) }}, 
      {{ SC_(485.769073486328125), SC_(190.1673431396484375), SC_(0.632396042346954345703125), SC_(0.7802325302446099953553469952021295981017e-181), SC_(0.7885435919806277604143311907077514864473e-175), SC_(0.9894592590329194989765794932850304103993e-6), SC_(0.9999990105407409670805010234205067149696) }}, 
      {{ SC_(488.2393798828125), SC_(0.434910595417022705078125), SC_(0.3082362115383148193359375), SC_(0.7152474532457840823266183808633084306729e-252), SC_(0.1379700680620020085460118932624223921791), SC_(0.518407697620588907258288335656687930697e-251), SC_(1.0) }}, 
      {{ SC_(1082.14306640625), SC_(10693.6611328125), SC_(0.12707412242889404296875), SC_(0.2278754068927472156288012140413843328972e-1570), SC_(0.4710634212604126043266066650730864250032e-1603), SC_(0.9999999999999999999999999999999979328027), SC_(0.2067197279793011048484820322721178352804e-32) }}, 
      {{ SC_(1097.736083984375), SC_(1.48078691959381103515625), SC_(0.1355634629726409912109375), SC_(0.1781516798617882923386756479578446275105e-955), SC_(0.2785129897942893166037431459860886839776e-4), SC_(0.6396530373444044954143234151352585767162e-951), SC_(1.0) }}, 
      {{ SC_(1146.2520751953125), SC_(184.5145416259765625), SC_(0.9688708782196044921875), SC_(0.4677065383471207613026721995792121706691e-233), SC_(0.1170010114027342356906428338257499650131e-295), SC_(1.0), SC_(0.2501590245375163959038911002528123869642e-62) }}, 
      {{ SC_(1256.7484130859375), SC_(114671.2265625), SC_(0.221111953258514404296875), SC_(0.3864052120281740910984826266649804985814e-3013), SC_(BOOST_MATH_SMALL_CONSTANT(0.1841074042592996185772811936640211942933e-13272)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4764620106777331318281507507630583790219e-10259)) }}, 
      {{ SC_(1295.97900390625), SC_(0.00442600063979625701904296875), SC_(0.632396042346954345703125), SC_(0.254468554506769970848011093621387782895e-260), SC_(218.3284697331136530629038750535253642784), SC_(0.1165530793202710697732542983745490174379e-262), SC_(1.0) }}, 
      {{ SC_(1309.38720703125), SC_(0.00044117416837252676486968994140625), SC_(0.3082362115383148193359375), SC_(0.621665421336862472879397361848521150676e-672), SC_(2258.937956171080727633037218705285702127), SC_(0.2752025214497660268024310700652187996244e-675), SC_(1.0) }}, 
      {{ SC_(1394.074951171875), SC_(15.51703548431396484375), SC_(0.905801355838775634765625), SC_(0.1282445221776865687937653231808833904475e-77), SC_(0.5247043222849333125720311799101580200549e-37), SC_(0.2444129326383653883103923748144980641462e-40), SC_(1.0) }}, 
      {{ SC_(1394.3763427734375), SC_(0.147156597449793480336666107177734375e-4), SC_(0.12707412242889404296875), SC_(0.4295749354510508788027152257959430409014e-1252), SC_(67947.00278024669441321354162427619743248), SC_(0.6322205805609652911187396252464186094979e-1257), SC_(1.0) }}, 
      {{ SC_(1423.681640625), SC_(37.307399749755859375), SC_(0.3082362115383148193359375), SC_(0.2365255442145481725135864944077129293965e-736), SC_(0.1588073045974917207389001054879195033287e-75), SC_(0.1489387058196339233021721809171109690765e-660), SC_(1.0) }}, 
      {{ SC_(1480.748046875), SC_(2957.284423828125), SC_(0.221111953258514404296875), SC_(0.7885727064770790413556014619422116655333e-1294), SC_(0.450979522013964292445694216615695511684e-1228), SC_(0.1748577635976698276519132508093443809805e-65), SC_(1.0) }}, 
      {{ SC_(1499.490966796875), SC_(4.857861995697021484375), SC_(0.12707412242889404296875), SC_(0.137486782575950488326355414743930597725e-1346), SC_(0.7198001946235739559682607692261403186166e-14), SC_(0.191006870521687548278195292638440088557e-1332), SC_(1.0) }}, 
      {{ SC_(1568.1090087890625), SC_(0.3394500163267366588115692138671875e-4), SC_(0.1355634629726409912109375), SC_(0.9395568605115892818305347167221015411794e-1364), SC_(29451.48480634567000911358526858687329636), SC_(0.3190185033758130479411737248140272384345e-1368), SC_(1.0) }}, 
      {{ SC_(1614.361572265625), SC_(257.008087158203125), SC_(0.81474220752716064453125), SC_(0.1585702744226958009407406130989849499305e-333), SC_(0.113558977705796973767770949754211098822e-325), SC_(0.1396369317618244336614987327591678300512e-7), SC_(0.9999999860363068238175566338501267240832) }}, 
      {{ SC_(1617.479248046875), SC_(0.032529197633266448974609375), SC_(0.12707412242889404296875), SC_(0.4777591022400609015805375048629480436748e-1452), SC_(23.74474755791250852883499903705096067287), SC_(0.2012062251135014928192024393427748953461e-1453), SC_(1.0) }}, 
      {{ SC_(1644.535888671875), SC_(0.0342094860970973968505859375), SC_(0.221111953258514404296875), SC_(0.1202190328594983373413205725678074814986e-1080), SC_(22.26763753526301348896484161372508813266), SC_(0.5398822963105968038539413361276247656143e-1082), SC_(1.0) }}, 
      {{ SC_(1654.0494384765625), SC_(34146.52734375), SC_(0.632396042346954345703125), SC_(0.3896459091825206171891077272890649849148e-2911), SC_(BOOST_MATH_SMALL_CONSTANT(0.5580096527651012054744158801308476347691e-15174)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1432094215837678627665949709074620682969e-12262)) }}, 
      {{ SC_(1654.2796630859375), SC_(364.202728271484375), SC_(0.12707412242889404296875), SC_(0.1652798774335487385895757390454814471833e-1506), SC_(0.2262099992848952631445017227753709826679e-414), SC_(0.7306479729279809248231411847474679865719e-1092), SC_(1.0) }}, 
      {{ SC_(1664.814208984375), SC_(0.00033929268829524517059326171875), SC_(0.913384497165679931640625), SC_(0.2155607436923262531841164263370337144072e-67), SC_(2939.324565586548760075341929333703526553), SC_(0.7333682922127744913958456803612633697384e-71), SC_(1.0) }}, 
      {{ SC_(1673.0052490234375), SC_(0.106633078758022747933864593505859375e-4), SC_(0.221111953258514404296875), SC_(0.2616727938825976180789490877394296978506e-1099), SC_(93771.5309777213146595157831837880636062), SC_(0.2790535583180006757787084431103671419022e-1104), SC_(1.0) }}, 
      {{ SC_(1681.7281494140625), SC_(0.00465255416929721832275390625), SC_(0.1355634629726409912109375), SC_(0.2172944533340070883814465445985034209869e-1462), SC_(207.0821002299898716371085681896459283656), SC_(0.1049315479670503418234586466438025686003e-1464), SC_(1.0) }}, 
      {{ SC_(1700.0389404296875), SC_(23.1719760894775390625), SC_(0.9688708782196044921875), SC_(0.1695187186090668187830750244132033787937e-59), SC_(0.2302238057126964975244171118795249821807e-53), SC_(0.73632087378680280926584687853988044467e-6), SC_(0.9999992636791262131971907341531214601196) }}, 
      {{ SC_(1722.9510498046875), SC_(3180.828857421875), SC_(0.913384497165679931640625), SC_(0.1755836602389347948809722822170864370393e-1381), SC_(0.2762808793333067821016101261999256588321e-3450), SC_(1.0), SC_(0.1573499942747194676521081383410127698125e-2068) }}, 
      {{ SC_(1737.734619140625), SC_(0.000441255629993975162506103515625), SC_(0.1355634629726409912109375), SC_(0.5217499237895080815010214367184995363247e-1511), SC_(2258.23736731588023923074855386316185076), SC_(0.2310429945677744004420968030735982371954e-1514), SC_(1.0) }}, 
      {{ SC_(1784.9957275390625), SC_(0.28711086997645907104015350341796875e-4), SC_(0.3082362115383148193359375), SC_(0.3696627964006476525140797301824218088004e-915), SC_(34821.68746476687688629313972925009458462), SC_(0.1061587830212646069186137725360432422791e-919), SC_(1.0) }}, 
      {{ SC_(1848.123291015625), SC_(0.00048024553689174354076385498046875), SC_(0.221111953258514404296875), SC_(0.4020280761426968915026600499046717884357e-1214), SC_(2074.18543171540418571701655366550582641), SC_(0.1938245587860529094866465011198826538811e-1217), SC_(1.0) }}, 
      {{ SC_(1848.65283203125), SC_(24.98059844970703125), SC_(0.8350250720977783203125), SC_(0.1759276691447393041938193480576385650064e-166), SC_(0.1223762749573607286345903162959711693756e-57), SC_(0.1437596210589326723100717819013761715731e-108), SC_(1.0) }}, 
      {{ SC_(1870.2532958984375), SC_(0.13475679224939085543155670166015625e-4), SC_(0.8350250720977783203125), SC_(0.1168858965976695939868324029529178043302e-148), SC_(74199.65192080726603366419296630218839546), SC_(0.1575289015134748042787177027180545895155e-153), SC_(1.0) }}, 
      {{ SC_(1914.6778564453125), SC_(240454.8125), SC_(0.1355634629726409912109375), SC_(0.1362123729045771190351072122214389258813e-4854), SC_(BOOST_MATH_SMALL_CONSTANT(0.1035613818291344775459851316322919807035e-16878)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.7602935006622626988376589038345728804959e-12024)) }}, 
      {{ SC_(1961.91357421875), SC_(1.22353923320770263671875), SC_(0.913384497165679931640625), SC_(0.1888794324802670476538793010492694418827e-80), SC_(0.8538183807506730243916820891060685578539e-4), SC_(0.2212173416953206801610018852971508294782e-76), SC_(1.0) }}, 
      {{ SC_(2004.1676025390625), SC_(0.1820631950977258384227752685546875e-4), SC_(0.905801355838775634765625), SC_(0.4062543973156701589290933207940784529363e-88), SC_(54917.8038997340536514817480325450015585), SC_(0.739749896149138434077240141578222282626e-93), SC_(1.0) }}, 
      {{ SC_(2021.546875), SC_(1278.527099609375), SC_(0.9688708782196044921875), SC_(0.1483934995162793775206111848679747717182e-957), SC_(0.4350310933674388433268538805755337518644e-1957), SC_(1.0), SC_(0.2931604785826310014861243178722065932111e-999) }}, 
      {{ SC_(2030.455078125), SC_(27725.34375), SC_(0.9688708782196044921875), SC_(0.1878636810444501212373007325979956102648e-3219), SC_(BOOST_MATH_SMALL_CONSTANT(0.1655258511735768636603108633056838283451e-41809)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.8810955382824211783756781228548296136572e-38590)) }}, 
      {{ SC_(2031.1689453125), SC_(0.001671129255555570125579833984375), SC_(0.221111953258514404296875), SC_(0.3955790606046084527284621559758683305339e-1334), SC_(590.2616130712235793589422310423909958626), SC_(0.670175820084841146568352704004977826627e-1337), SC_(1.0) }}, 
      {{ SC_(2160.7412109375), SC_(4.9396953582763671875), SC_(0.221111953258514404296875), SC_(0.1302217695091183525459476058687262124071e-1419), SC_(0.7363180529281059482774557084481883763268e-15), SC_(0.1768553262971989069540929369132310123363e-1404), SC_(1.0) }}, 
      {{ SC_(2176.594482421875), SC_(0.0227449946105480194091796875), SC_(0.905801355838775634765625), SC_(0.1384115282058986953743777793463642081081e-95), SC_(36.44850067419443199002993066424687116033), SC_(0.3797454645477205307734827607870898832397e-97), SC_(1.0) }}, 
      {{ SC_(2196.900146484375), SC_(0.4170066416263580322265625), SC_(0.221111953258514404296875), SC_(0.7941796897290817909621409737327488000876e-1443), SC_(0.08589885998744758438632829492273694921049), SC_(0.9245520718728227703934958204057092166778e-1442), SC_(1.0) }}, 
      {{ SC_(2253.716064453125), SC_(0.00046744965948164463043212890625), SC_(0.905801355838775634765625), SC_(0.6842946162292510824869860090053164606198e-99), SC_(2130.986913486688919376966163667496177972), SC_(0.3211162921266458952099301467815703890065e-102), SC_(1.0) }}, 
      {{ SC_(2265.716064453125), SC_(0.23549166144221089780330657958984375e-4), SC_(0.81474220752716064453125), SC_(0.5932439033278450915088762148901296798054e-204), SC_(42456.04677341868170596383947190243957006), SC_(0.139731310004884706834533685175899477175e-208), SC_(1.0) }}, 
      {{ SC_(2269.919189453125), SC_(0.142603230415261350572109222412109375e-4), SC_(0.632396042346954345703125), SC_(0.2186312509126877416810650374743027230363e-454), SC_(70116.33463615652369843143732016405198974), SC_(0.3118121505455125538956625389017312039928e-459), SC_(1.0) }}, 
      {{ SC_(2270.08251953125), SC_(1.9198791980743408203125), SC_(0.3082362115383148193359375), SC_(0.1661668332475144840023531864378149258297e-1163), SC_(0.3490115275353736740302437043085435552513e-6), SC_(0.4761070054646628541159883168154295504094e-1157), SC_(1.0) }}, 
      {{ SC_(2303.33447265625), SC_(2434.39501953125), SC_(0.632396042346954345703125), SC_(0.2826270526839534849740276157087846557041e-1426), SC_(0.5413948517508386786713013774457472317632e-1519), SC_(1.0), SC_(0.191558043226757630502347773223457225048e-92) }}, 
      {{ SC_(2341.2451171875), SC_(0.4919659197330474853515625), SC_(0.1355634629726409912109375), SC_(0.6250391372013374711516787461020583460369e-2035), SC_(0.03961556752030931944550141047861508128849), SC_(0.1577761411295963047970296614473158988214e-2033), SC_(1.0) }}, 
      {{ SC_(2403.10693359375), SC_(0.0393528044223785400390625), SC_(0.81474220752716064453125), SC_(0.3118436767587323412962861794379243037092e-216), SC_(18.30891617470164827363948970052722957826), SC_(0.1703233953245263406203517612910504765217e-217), SC_(1.0) }}, 
      {{ SC_(2438.42529296875), SC_(0.4890716075897216796875), SC_(0.81474220752716064453125), SC_(0.1037642856895648843730984337375533046527e-219), SC_(0.03994908265612262192819015004190041106887), SC_(0.2597413477119277572735736406645521482144e-218), SC_(1.0) }}, 
      {{ SC_(2525.380859375), SC_(0.0469692982733249664306640625), SC_(0.1355634629726409912109375), SC_(0.9718816748681519785982031265848916843842e-2195), SC_(14.36749526206160737764778488127206016531), SC_(0.6764447505575134115382019671129730076222e-2196), SC_(1.0) }}, 
      {{ SC_(2563.04833984375), SC_(444797.40625), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.4708659010959950717205430665225397776429e-6857)), SC_(BOOST_MATH_SMALL_CONSTANT(0.6871523478649237942696144958600737267074e-472661)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1459337671862619267889334835943691340587e-465803)) }}, 
      {{ SC_(2587.197265625), SC_(0.001075554522685706615447998046875), SC_(0.3082362115383148193359375), SC_(0.2444353237317730025383383576818923702803e-1325), SC_(921.3566322728319931387356299737332825061), SC_(0.2652993587605617297051874018503019594896e-1328), SC_(1.0) }}, 
      {{ SC_(2595.52294921875), SC_(2311.214111328125), SC_(0.8350250720977783203125), SC_(0.2288949937715536177111141422371568975486e-1474), SC_(0.7549038718853678415354774527137853469774e-2015), SC_(1.0), SC_(0.3298035747513080940137389710734199967035e-540) }}, 
      {{ SC_(2608.736083984375), SC_(0.0039381901733577251434326171875), SC_(0.913384497165679931640625), SC_(0.9903151303240974158935153837082905608973e-105), SC_(245.6220706381780385069217188919019587618), SC_(0.4031865409126506659492178070254142848986e-107), SC_(1.0) }}, 
      {{ SC_(2609.55322265625), SC_(0.0041527482680976390838623046875), SC_(0.8350250720977783203125), SC_(0.1078073927655620034929749306816219287884e-206), SC_(232.5100497888069205730664581484971150885), SC_(0.4636676688318866487891112040740303654784e-209), SC_(1.0) }}, 
      {{ SC_(2660.373779296875), SC_(2036.114990234375), SC_(0.905801355838775634765625), SC_(0.143128444481996215147192895235313137492e-1396), SC_(0.335806034392376310362878782680161159116e-2206), SC_(1.0), SC_(0.2346186571143910871009982672536508940281e-809) }}, 
      {{ SC_(2676.193359375), SC_(457043.46875), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3920215987045519051917282021639556080379e-7141)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1599886413887271785369191300485834742603e-469026)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4081118028124338963951366996409686764645e-461885)) }}, 
      {{ SC_(2690.582763671875), SC_(0.22973056137561798095703125), SC_(0.632396042346954345703125), SC_(0.2815358879783806589544950929902226594006e-538), SC_(0.646079473776380407645459179984928723535), SC_(0.4357604588995582842767141615286707798988e-538), SC_(1.0) }}, 
      {{ SC_(2697.339111328125), SC_(0.3278447091579437255859375), SC_(0.913384497165679931640625), SC_(0.1417568117001298118698339763069731766565e-108), SC_(0.2045026287313223140065364339627882109421), SC_(0.6931784328619628121835127461938924376676e-108), SC_(1.0) }}, 
      {{ SC_(2717.423095703125), SC_(0.278538644313812255859375), SC_(0.9688708782196044921875), SC_(0.2128517470081175567588832613689989800062e-39), SC_(0.357516946170104782730738707906199957098), SC_(0.5953612808799383608197051593765274145908e-39), SC_(0.9999999999999999999999999999999999999994) }}, 
      {{ SC_(2760.142333984375), SC_(46082.70703125), SC_(0.913384497165679931640625), SC_(0.1606817442384679216447504369336462826297e-4609), SC_(BOOST_MATH_SMALL_CONSTANT(0.2029688686521188812468055426861266945045e-49071)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1263173172621854291637924813807736147072e-44461)) }}, 
      {{ SC_(2810.3701171875), SC_(0.3556720912456512451171875), SC_(0.3082362115383148193359375), SC_(0.1691291405420655174976547197853551601938e-1439), SC_(0.1485995060620461974166844358868336437211), SC_(0.1138154123281186291924394687497950591179e-1438), SC_(1.0) }}, 
      {{ SC_(2831.54541015625), SC_(2.64873027801513671875), SC_(0.905801355838775634765625), SC_(0.1569550910329461716749990717647089249125e-126), SC_(0.1065471925601074637322494217647203412137e-8), SC_(0.1473103957613914875972800446242766673551e-117), SC_(1.0) }}, 
      {{ SC_(2853.04248046875), SC_(23.2325763702392578125), SC_(0.81474220752716064453125), SC_(0.2615085001757634387811519908910861141731e-273), SC_(0.112424123146530934341191800142750908027e-58), SC_(0.2326088857592595720025972574740611065982e-214), SC_(1.0) }}, 
      {{ SC_(3066.23291015625), SC_(141.8632659912109375), SC_(0.3082362115383148193359375), SC_(0.5978071926368854746585028081658852840129e-1593), SC_(0.9314235640159916103657757020301440254905e-253), SC_(0.6418209885729514606575566400263059349643e-1340), SC_(1.0) }}, 
      {{ SC_(3100.1806640625), SC_(1447.2921142578125), SC_(0.3082362115383148193359375), SC_(0.3881628201536046209044413810328915164758e-1819), SC_(0.295510977385263319211575272155145096904e-1236), SC_(0.1313530967912401170530178081319370684746e-582), SC_(1.0) }}, 
      {{ SC_(3122.51708984375), SC_(0.032955713570117950439453125), SC_(0.913384497165679931640625), SC_(0.4696589450245759541144932092444991500489e-125), SC_(22.85746444773787096079013322854683509866), SC_(0.20547289752912929716559916852783376508e-126), SC_(1.0) }}, 
      {{ SC_(3163.536376953125), SC_(298.2393798828125), SC_(0.905801355838775634765625), SC_(0.351222071570293015556072386272611379072e-443), SC_(0.687751514318488865930365167471866751403e-442), SC_(0.04858691928285678922119001974960641153162), SC_(0.9514130807171432107788099802503935884684) }}, 
      {{ SC_(3185.797607421875), SC_(2162.649658203125), SC_(0.81474220752716064453125), SC_(0.3677754518960236492827038681381714969954e-1568), SC_(0.8248381901884188145509867859116301758571e-1870), SC_(1.0), SC_(0.224277663431874334141657029236919878297e-301) }}, 
      {{ SC_(3222.951904296875), SC_(0.000114076086902059614658355712890625), SC_(0.12707412242889404296875), SC_(0.933100695906133383792779828454651317464e-2891), SC_(8757.428341063870418190564143042884979598), SC_(0.106549623881110557951346641461218442187e-2894), SC_(1.0) }}, 
      {{ SC_(3231.15576171875), SC_(0.00042852279148064553737640380859375), SC_(0.8350250720977783203125), SC_(0.1867021500763222500441418898175790971479e-255), SC_(2324.956900363979379703392736889282702904), SC_(0.8030348865696969954787532787646234158509e-259), SC_(1.0) }}, 
      {{ SC_(3233.27685546875), SC_(0.302093982696533203125), SC_(0.8350250720977783203125), SC_(0.7394495679867784030074674295563958853451e-256), SC_(0.2585220426817447194394678287236757523982), SC_(0.2860296013122110106293010342463747525215e-255), SC_(1.0) }}, 
      {{ SC_(3309.57666015625), SC_(19694.05859375), SC_(0.905801355838775634765625), SC_(0.2208631038565245596877093778960712501508e-4116), SC_(BOOST_MATH_SMALL_CONSTANT(0.2132326548035614993901261756283994925164e-20351)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.9654516806124399318694577899108144606729e-16235)) }}, 
      {{ SC_(3389.960693359375), SC_(0.12216867506504058837890625), SC_(0.12707412242889404296875), SC_(0.2043860892710376191166579788352867691171e-3040), SC_(2.858776376468209088925102203244016022808), SC_(0.7149425570794046564076716911579132342617e-3041), SC_(1.0) }}, 
      {{ SC_(3482.687744140625), SC_(0.003345853649079799652099609375), SC_(0.9688708782196044921875), SC_(0.1331946003311204974453353230024933321625e-49), SC_(290.2737663310366178992356910668771076101), SC_(0.4588585527884787012964148793593696016582e-52), SC_(1.0) }}, 
      {{ SC_(3489.989013671875), SC_(0.000430709798820316791534423828125), SC_(0.9688708782196044921875), SC_(0.1065835473530261878862571077378414811744e-49), SC_(2313.030932747487101849276179998333520729), SC_(0.4607960310605231409250741425914671459289e-53), SC_(1.0) }}, 
      {{ SC_(3568.2431640625), SC_(352075.78125), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.2541234347003218620925586365830762003992e-8674)), SC_(BOOST_MATH_SMALL_CONSTANT(0.8907337148236950426034394889044629146075e-275813)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3505122287813021112960779898848412225165e-267138)) }}, 
      {{ SC_(3614.7607421875), SC_(3.387983798980712890625), SC_(0.632396042346954345703125), SC_(0.1066392343401903074570981794034797612126e-723), SC_(0.2591906791946646226214895922674915680462e-11), SC_(0.4114315941897707341535557667714226386279e-712), SC_(1.0) }}, 
      {{ SC_(3614.799560546875), SC_(45861.59765625), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1002562797439735725293044078624918573793e-5619)), SC_(BOOST_MATH_SMALL_CONSTANT(0.8157703784701342011174480634396233609118e-7350)), SC_(1.0), SC_(0.8136850684599338941739115897548011164103e-1730) }}, 
      {{ SC_(3663.94873046875), SC_(125.4974365234375), SC_(0.1355634629726409912109375), SC_(0.5983916526263260783662182189142940149961e-3191), SC_(0.109238605784170592476358066692321757953e-239), SC_(0.5477840442312172452470686670755495488233e-2951), SC_(1.0) }}, 
      {{ SC_(3684.808837890625), SC_(238875.625), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1173669919221787144531594474279032100143e-8289)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2668463891339278217426918600732690621104e-104558)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2273606784698570314862915887926907619496e-96268)) }}, 
      {{ SC_(3718.935546875), SC_(284.95684814453125), SC_(0.913384497165679931640625), SC_(0.1294003811275988797120708635387277581269e-450), SC_(0.7922660499004278048558506697943302441345e-447), SC_(0.0001633027815614288283942094303317874664245), SC_(0.9998366972184385711716057905696682125336) }}, 
      {{ SC_(3726.241943359375), SC_(28.8971099853515625), SC_(0.632396042346954345703125), SC_(0.5586199653986671340101278306961205757215e-757), SC_(0.1225345553171307233831318418620512936238e-73), SC_(0.4558876995578163029911899103643809814477e-683), SC_(1.0) }}, 
      {{ SC_(3736.384033203125), SC_(0.0043655070476233959197998046875), SC_(0.81474220752716064453125), SC_(0.4936522971383731596646688934747087886844e-335), SC_(220.4359571359634487110970810001367212539), SC_(0.2239436358533338865353329179187538484566e-337), SC_(1.0) }}, 
      {{ SC_(3742.142822265625), SC_(0.02163856662809848785400390625), SC_(0.8350250720977783203125), SC_(0.1514811948436923300301275717145076367747e-295), SC_(38.21172611873917177520771747403712728379), SC_(0.3964259410134455872751960852083833476475e-297), SC_(1.0) }}, 
      {{ SC_(3810.80908203125), SC_(139.7005615234375), SC_(0.632396042346954345703125), SC_(0.5924463400183605803006809012446629946778e-822), SC_(0.954841383577710103509948090108551124963e-263), SC_(0.6204657131622365690086051201735696798525e-559), SC_(1.0) }}, 
      {{ SC_(3812.892822265625), SC_(0.290711686830036342144012451171875e-4), SC_(0.9688708782196044921875), SC_(0.3592448713049080325420212703865359978702e-54), SC_(34389.51983461813619743577281330947149361), SC_(0.1044634740562079483553913297843410433397e-58), SC_(1.0) }}, 
      {{ SC_(3826.86083984375), SC_(0.00044492297456599771976470947265625), SC_(0.81474220752716064453125), SC_(0.4318212520262088654085243799487132645206e-343), SC_(2238.770836217429788151584057613326346436), SC_(0.1928831861843452721743690083042640483525e-346), SC_(1.0) }}, 
      {{ SC_(3860.85009765625), SC_(408688.75), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.7343070404148790245024162966963883904056e-9503)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2677465506453831922845591502537589422007e-299600)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3646247903249136968170165929436587515456e-290097)) }}, 
      {{ SC_(3962.59228515625), SC_(31.1610393524169921875), SC_(0.1355634629726409912109375), SC_(0.3384712230537736690981003779060463677214e-3444), SC_(0.3124137125523955424441891849418603640826e-79), SC_(0.1083407063948923075182335962189351711032e-3364), SC_(1.0) }}, 
      {{ SC_(3970.181396484375), SC_(0.46857738494873046875), SC_(0.905801355838775634765625), SC_(0.2285235610597586946096424145268850967653e-173), SC_(0.03891888964637741735745142775849093350722), SC_(0.5871790360314910485880298902783526181515e-172), SC_(1.0) }}, 
      {{ SC_(3974.75341796875), SC_(31.3806819915771484375), SC_(0.221111953258514404296875), SC_(0.1258218693853321148582037748838785943212e-2611), SC_(0.9749514582632188361913601678810860673319e-80), SC_(0.1290544963227928631200180913029430611917e-2531), SC_(1.0) }}, 
      {{ SC_(3994.4755859375), SC_(1430.89794921875), SC_(0.1355634629726409912109375), SC_(0.2105984586363485191489122069253217127303e-3560), SC_(0.3229438169052957659815167182603135997404e-1360), SC_(0.6521210427698237770929763396330685024629e-2200), SC_(1.0) }}, 
      {{ SC_(4007.785888671875), SC_(325072.90625), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1535875454312946710181314646533980315805e-9403)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1039179881121639310008295742856771329998e-22781)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6766042638441034890994705226282583622852e-13378)) }}, 
      {{ SC_(4032.44970703125), SC_(0.014052885584533214569091796875), SC_(0.3082362115383148193359375), SC_(0.3171277208553330764287268239017078454643e-2064), SC_(62.82230248061425754625616329120345086349), SC_(0.5048011746356996896875551850116190870035e-2066), SC_(1.0) }}, 
      {{ SC_(4037.3095703125), SC_(16.8816509246826171875), SC_(0.12707412242889404296875), SC_(0.1811392346614968167108368205175892965523e-3621), SC_(0.1929835772220333361251284618212707307798e-47), SC_(0.9386251269095854120904261337078739905176e-3574), SC_(1.0) }}, 
      {{ SC_(4055.591552734375), SC_(3.02400684356689453125), SC_(0.9688708782196044921875), SC_(0.4456144694071283666347523985176164644498e-62), SC_(0.2509527649774158192255909566659648745673e-10), SC_(0.1775690614316327156100048460672669241989e-51), SC_(1.0) }}, 
      {{ SC_(4069.31787109375), SC_(325513.34375), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.162173975418663081604091567477023934026e-9523)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1787797438774108221234499823359245468524e-54180)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1102394779531542139116411393051318030817e-44656)) }}, 
      {{ SC_(4178.6318359375), SC_(46566.515625), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.3679641802030769930157052195262162209034e-6270)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1287385523085865425356989676186722457646e-36773)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3498670773811097278706589044194044553718e-30503)) }}, 
      {{ SC_(4236.8154296875), SC_(2638.01123046875), SC_(0.12707412242889404296875), SC_(0.6702401115167996316133372613571524749209e-3955), SC_(0.5590999583938067133692408291595627864472e-1989), SC_(0.1198784048280523078702343865750003226665e-1965), SC_(1.0) }}, 
      {{ SC_(4264.560546875), SC_(235044.84375), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1079378039697116076905432865775769928285e-9295)), SC_(BOOST_MATH_SMALL_CONSTANT(0.5187858929714358425321211767043278891815e-354237)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.4806341002796501070170458421211796097238e-344941)) }}, 
      {{ SC_(4294.29833984375), SC_(39.17559051513671875), SC_(0.913384497165679931640625), SC_(0.7705234436368799337133459389811661281009e-213), SC_(0.3990173102068232973197151632490777618695e-97), SC_(0.1931052673472971015938167725950074066875e-115), SC_(1.0) }}, 
      {{ SC_(4329.69384765625), SC_(0.0356505475938320159912109375), SC_(0.9688708782196044921875), SC_(0.2234805586966795339320542404669008076833e-61), SC_(20.40812812474043026599138750402293917848), SC_(0.1095056623178280685632856382784930975713e-62), SC_(1.0) }}, 
      {{ SC_(4484.44482421875), SC_(0.0347605831921100616455078125), SC_(0.632396042346954345703125), SC_(0.2062703691196530763626473007312376014898e-895), SC_(21.071219100449385506787973991376012016), SC_(0.9789199577695718229676416274222490268767e-897), SC_(1.0) }}, 
      {{ SC_(4571.689453125), SC_(0.30711162253282964229583740234375e-4), SC_(0.913384497165679931640625), SC_(0.3326135673042652498235499652967901833815e-182), SC_(32552.4473674992253698229040907974757233), SC_(0.1021777452089058092599760990417017662114e-186), SC_(1.0) }}, 
      {{ SC_(4577.5576171875), SC_(462.9542236328125), SC_(0.221111953258514404296875), SC_(0.1390280064397496966216136027920506216894e-3053), SC_(0.3194354696778845146248942290654235433269e-672), SC_(0.4352303348777896430560533546618204120396e-2381), SC_(1.0) }}, 
      {{ SC_(4655.2021484375), SC_(0.0004921201034449040889739990234375), SC_(0.632396042346954345703125), SC_(0.2140620923443938107709164196116768458034e-929), SC_(2023.021838799842632538718276005449325328), SC_(0.105813040788223082775212877148027545833e-932), SC_(1.0) }}, 
      {{ SC_(4684.38916015625), SC_(48115.4140625), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.218241877450408797106019672016568921144e-6870)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2305962248621997562140158914137553109272e-35652)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1056608509586333829044418529250443856336e-28781)) }}, 
      {{ SC_(4694.7021484375), SC_(15396.767578125), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1852561299193606023720471490013502627154e-5051)), SC_(0.5989612122653983882790191873744879415003e-4745), SC_(0.3092957041720324637368428164684280177988e-306), SC_(1.0) }}, 
      {{ SC_(4730.45458984375), SC_(39692.90234375), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.2035281588348346104333793090830905189314e-6543)), SC_(BOOST_MATH_SMALL_CONSTANT(0.4970853485809765005131726892117555907765e-8774)), SC_(1.0), SC_(0.2442341892280207038115598697680841789413e-2230) }}, 
      {{ SC_(4742.923828125), SC_(4.058574676513671875), SC_(0.8350250720977783203125), SC_(0.3618704425719174888414811614820879760013e-377), SC_(0.7766753096761709008080229458823739563667e-14), SC_(0.4659224235192911276439005519311341553406e-363), SC_(1.0) }}, 
      {{ SC_(4813.82861328125), SC_(366.26776123046875), SC_(0.8350250720977783203125), SC_(0.5627514171627524766139963270209350110119e-666), SC_(0.2634983998623451125939473126729828251295e-575), SC_(0.2135691971779490556767376018124259992261e-90), SC_(1.0) }}, 
      {{ SC_(4827.74365234375), SC_(3.2813909053802490234375), SC_(0.81474220752716064453125), SC_(0.118872123863226120265418126982617028843e-434), SC_(0.214888648478858832158315123268435991217e-11), SC_(0.5531800991103585147606812339512713885385e-423), SC_(1.0) }}, 
      {{ SC_(4931.3408203125), SC_(0.003918074071407318115234375), SC_(0.905801355838775634765625), SC_(0.2773594750858212051700506959595157168854e-214), SC_(246.3096931692891819165947525290236505784), SC_(0.1126059926903450938715209232296910374899e-216), SC_(1.0) }}, 
      {{ SC_(4951.73876953125), SC_(0.0031504244543612003326416015625), SC_(0.12707412242889404296875), SC_(0.7744045211659641741396761663113692238095e-4440), SC_(308.4642224078795873590957835909065411626), SC_(0.2510516503732402840998214197696161679016e-4442), SC_(1.0) }}, 
      {{ SC_(10727.1015625), SC_(20.6144351959228515625), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1081001151781088997936050592587015059289e-5489)), SC_(0.6132368639454089451800706265258888733253e-65), SC_(BOOST_MATH_SMALL_CONSTANT(0.1762779140226835695604238411072259470264e-5424)), SC_(1.0) }}, 
      {{ SC_(10865.9921875), SC_(0.472520351409912109375), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.7347876450068710869507820874537195116341e-9434)), SC_(0.02321258677224304556295768537681135974067), SC_(BOOST_MATH_SMALL_CONSTANT(0.316547075177984627391986679086994910297e-9432)), SC_(1.0) }}, 
      {{ SC_(11005.3994140625), SC_(0.00037013436667621135711669921875), SC_(0.913384497165679931640625), SC_(0.9943504329391992763799984620230626791599e-436), SC_(2691.856610960288035586597960035050675795), SC_(0.3693920504125502100678479520619804484949e-439), SC_(1.0) }}, 
      {{ SC_(11199.677734375), SC_(2.693786144256591796875), SC_(0.8350250720977783203125), SC_(0.4846281950960513689799422380816610977361e-882), SC_(0.1900690857842524014984635567886583437591e-10), SC_(0.2549747598860728410190825408241079567128e-871), SC_(1.0) }}, 
      {{ SC_(11672.794921875), SC_(151.0876007080078125), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.5737665427335230575523667671751519408751e-5994)), SC_(0.1066411814561236409434102801689669503075e-351), SC_(BOOST_MATH_SMALL_CONSTANT(0.5380346831299811573931802848624492607659e-5642)), SC_(1.0) }}, 
      {{ SC_(11802.0439453125), SC_(0.0215594768524169921875), SC_(0.81474220752716064453125), SC_(0.3169522110929015172287107743853420674783e-1053), SC_(37.43972471890515899015607804901055773783), SC_(0.8465666173364163445990163934368844000655e-1055), SC_(1.0) }}, 
      {{ SC_(12400.75390625), SC_(0.351217095158062875270843505859375e-4), SC_(0.913384497165679931640625), SC_(0.1106557530092916473993753368683133042285e-490), SC_(28462.41726270018294068189654350498967089), SC_(0.3887784793117530075305939975237267838173e-495), SC_(1.0) }}, 
      {{ SC_(12677.8505859375), SC_(38.009876251220703125), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.9770029059207102099490143013811735760616e-11009)), SC_(0.1492641238077330770184205950231500370148e-112), SC_(BOOST_MATH_SMALL_CONSTANT(0.6545463712225895469605766540125052659091e-10896)), SC_(1.0) }}, 
      {{ SC_(13303.708984375), SC_(16683.587890625), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.1933259776074049940506534044874302951426e-8945)), SC_(BOOST_MATH_SMALL_CONSTANT(0.417959873762397775217420769886278674411e-17692)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2161943671176804100612026364980929327471e-8746)) }}, 
      {{ SC_(13483.087890625), SC_(3.4626166820526123046875), SC_(0.913384497165679931640625), SC_(0.5549699412116022378520103010599550872238e-537), SC_(0.159864431299585207613567103065375578284e-13), SC_(0.347150355272956950177545390769399859092e-523), SC_(1.0) }}, 
      {{ SC_(13500.0146484375), SC_(0.0147750042378902435302734375), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1705934191262976500825609090119819526895e-8851)), SC_(58.32040248375219115941908275056004877979), SC_(BOOST_MATH_SMALL_CONSTANT(0.292510702706182848954962974234494531825e-8853)), SC_(1.0) }}, 
      {{ SC_(14277.6669921875), SC_(275.66424560546875), SC_(0.632396042346954345703125), SC_(0.1187658379676931288134484422703850186642e-2964), SC_(0.5630317048018228546398578271006617933091e-594), SC_(0.2109398759515622504280670173384052208024e-2370), SC_(1.0) }}, 
      {{ SC_(15026.18359375), SC_(191435.6875), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.2119395103570560832804887387441312169326e-23383)), SC_(BOOST_MATH_SMALL_CONSTANT(0.4640380238764152752098128051205651494234e-197055)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2189483325193339055602886539896657820207e-173671)) }}, 
      {{ SC_(15514.759765625), SC_(469.305145263671875), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.6792116795333714050014097120690611996861e-13932)), SC_(0.1551617501782117229216160679908864375336e-920), SC_(BOOST_MATH_SMALL_CONSTANT(0.4377442757337164534594408118556290623672e-13011)), SC_(1.0) }}, 
      {{ SC_(15687.4873046875), SC_(0.000288298237137496471405029296875), SC_(0.905801355838775634765625), SC_(0.6096020893529961277223878428636413874687e-677), SC_(3458.407825009435937506481330445155152371), SC_(0.1762666869258928091512979989687453621203e-680), SC_(1.0) }}, 
      {{ SC_(17074.203125), SC_(0.00107910879887640476226806640625), SC_(0.8350250720977783203125), SC_(0.427457964385380263638580848973505633587e-1340), SC_(916.4262639951811326129298125584673977294), SC_(0.4664401067270459379336484710267800483659e-1343), SC_(1.0) }}, 
      {{ SC_(17224.67578125), SC_(0.0225061289966106414794921875), SC_(0.632396042346954345703125), SC_(0.1955070422559485104388995940681677254754e-3431), SC_(35.22874831250725228282816191624496159701), SC_(0.5549644867357881744630482252448403510375e-3433), SC_(1.0) }}, 
      {{ SC_(17285.642578125), SC_(252.595855712890625), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.4579155613408594147943987501378120777984e-11360)), SC_(0.1329327800877027982650034796865467411697e-574), SC_(BOOST_MATH_SMALL_CONSTANT(0.3444715148804894150399635636083692920444e-10785)), SC_(1.0) }}, 
      {{ SC_(17364.01171875), SC_(0.0032844119705259799957275390625), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.4534117685393013223625663165569470156362e-15561)), SC_(294.3053538476502534219745888362523594316), SC_(BOOST_MATH_SMALL_CONSTANT(0.154061678665218540123085427908925128439e-15563)), SC_(1.0) }}, 
      {{ SC_(18709.283203125), SC_(311063.03125), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1623326155096450649755434641645297779583e-31206)), SC_(BOOST_MATH_SMALL_CONSTANT(0.199146209059788229813537624593649188508e-244902)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1226778786472246969416903414773867933151e-213695)) }}, 
      {{ SC_(18712.064453125), SC_(125.128173828125), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.296851932434343132924442873205905982913e-16251)), SC_(0.5056024816465771861278220568508739133031e-327), SC_(BOOST_MATH_SMALL_CONSTANT(0.587125149124261132925097733767024791782e-15924)), SC_(1.0) }}, 
      {{ SC_(18950.81640625), SC_(0.02248309552669525146484375), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.8531376165533034514490752209777736500755e-16983)), SC_(35.19754234719377982457251861372588583048), SC_(BOOST_MATH_SMALL_CONSTANT(0.2423855643493024101837650235108650041103e-16984)), SC_(1.0) }}, 
      {{ SC_(18966.861328125), SC_(0.00021034784731455147266387939453125), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.4066547098951612620721277536808353340447e-9698)), SC_(4743.61407389308172270355006936173880486), SC_(BOOST_MATH_SMALL_CONSTANT(0.8572676941263477256811863073166251697026e-9702)), SC_(1.0) }}, 
      {{ SC_(19108.51171875), SC_(1.5829699039459228515625), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.8971659142595029657488728896907354438759e-9771)), SC_(0.1489910955347552015717735020626187042582e-6), SC_(BOOST_MATH_SMALL_CONSTANT(0.6021607607081597568215929208053439043328e-9764)), SC_(1.0) }}, 
      {{ SC_(19766.611328125), SC_(1197.8387451171875), SC_(0.8350250720977783203125), SC_(0.3185318408399375391201349006284076382164e-2488), SC_(0.6244715042856530734735781967451609038198e-1995), SC_(0.5100822674115662637340284485698509200389e-493), SC_(1.0) }}, 
      {{ SC_(19945.158203125), SC_(1.323333263397216796875), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1341686475958076796448446957548106673943e-17313)), SC_(0.18252239404929000361227231329604692237e-5), SC_(BOOST_MATH_SMALL_CONSTANT(0.7350804721505875082771016025446126632403e-17308)), SC_(1.0) }}, 
      {{ SC_(20612.880859375), SC_(0.0045445901341736316680908203125), SC_(0.913384497165679931640625), SC_(0.5026950516660181375002184069309382660166e-814), SC_(209.7816273601100812081722221573307482765), SC_(0.2396277777000434612070453109788469106733e-816), SC_(1.0) }}, 
      {{ SC_(21002.7890625), SC_(3.6717395782470703125), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1662262372153926156509978579564012413128e-18821)), SC_(0.5439464377981884946280312356646226100979e-15), SC_(BOOST_MATH_SMALL_CONSTANT(0.3055930247254690249520818325673208514748e-18806)), SC_(1.0) }}, 
      {{ SC_(21820.291015625), SC_(4256.572265625), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.1236249902866452151132958091361628613911e-5040)), SC_(BOOST_MATH_SMALL_CONSTANT(0.137446002602018047941490836678526755619e-5307)), SC_(1.0), SC_(0.1111797883933713598996484573949101107612e-266) }}, 
      {{ SC_(22356.583984375), SC_(457832.53125), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.2427624054077081053070462603157523396911e-39260)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2811118692419803409475001024557664483642e-487288)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1157971180792454683526657616202966016306e-448027)) }}, 
      {{ SC_(22697.115234375), SC_(0.3655127875390462577342987060546875e-4), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.2422351225756202534756635143055666957355e-20339)), SC_(27348.21883352735268633229163531358469692), SC_(BOOST_MATH_SMALL_CONSTANT(0.8857436897449929818388276572775353757861e-20344)), SC_(1.0) }}, 
      {{ SC_(23467.970703125), SC_(2143.580810546875), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1302191943227601633813919978470740088649e-5605)), SC_(0.4256209373395801514253968777699399700945e-3201), SC_(0.3059511008474313899338424120814023168547e-2404), SC_(1.0) }}, 
      {{ SC_(23857.951171875), SC_(344059.4375), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1820268245405173677104573378456949715364e-38365)), SC_(BOOST_MATH_SMALL_CONSTANT(0.9855951801856089118034713573367093594693e-52980)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5414560093950467116381035650090584886562e-14614)) }}, 
      {{ SC_(23897.50390625), SC_(0.0264662839472293853759765625), SC_(0.905801355838775634765625), SC_(0.6537725144721824905665984618756409566583e-1030), SC_(28.51287213248977398680191123392335013322), SC_(0.2292903048960905878796791478694241369087e-1031), SC_(1.0) }}, 
      {{ SC_(24178.228515625), SC_(454.8056640625), SC_(0.905801355838775634765625), SC_(0.1785916433983294692635116615174974457353e-1508), SC_(0.7879600822752658967292127616670030077666e-985), SC_(0.2266506228115503474591694981080031053324e-523), SC_(1.0) }}, 
      {{ SC_(24181.37109375), SC_(463750.875), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1849255643263548665067441360065463603155e-41792)), SC_(BOOST_MATH_SMALL_CONSTANT(0.3146356315397489737950931076379571144576e-49041)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1701417717371314989041581514224517991596e-7248)) }}, 
      {{ SC_(24202.947265625), SC_(0.000431136810220777988433837890625), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.9414521021069579791689130150635411436057e-21009)), SC_(2308.802802750210144576162773483800451287), SC_(BOOST_MATH_SMALL_CONSTANT(0.4077663544870591868832466457655898176825e-21012)), SC_(1.0) }}, 
      {{ SC_(24814.5078125), SC_(0.0022801845334470272064208984375), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3876201512836319382668825596201791404601e-16267)), SC_(427.9958316303507195186244355967105304424), SC_(BOOST_MATH_SMALL_CONSTANT(0.9056633794938679563008497581469693912042e-16270)), SC_(1.0) }}, 
      {{ SC_(24942.55078125), SC_(0.03099299408495426177978515625), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1225881087866866874909840493874552423954e-21650)), SC_(23.17560209270867089614435392066751649514), SC_(BOOST_MATH_SMALL_CONSTANT(0.5289532858576926135978436656838263708535e-21652)), SC_(1.0) }}, 
      {{ SC_(25332.25390625), SC_(0.1855850517749786376953125), SC_(0.8350250720977783203125), SC_(0.507984703157042916373804360059617322265e-1987), SC_(0.7568185447119811054787401996866618079721), SC_(0.671210697341413957308514820423884955283e-1987), SC_(1.0) }}, 
      {{ SC_(25354.7421875), SC_(0.02945673465728759765625), SC_(0.9688708782196044921875), SC_(0.6813421592537537934114140295569834417605e-351), SC_(24.77466284487784724303745251655406030537), SC_(0.2750157140461836973908132806701626197706e-352), SC_(1.0) }}, 
      {{ SC_(25489.81640625), SC_(0.00049681845121085643768310546875), SC_(0.8350250720977783203125), SC_(0.3241670731870165278670277444065337162173e-1999), SC_(2002.113381083162552722948484655907114959), SC_(0.1619124452440545751504157926476730166189e-2002), SC_(1.0) }}, 
      {{ SC_(26425.1640625), SC_(307.398162841796875), SC_(0.913384497165679931640625), SC_(0.240837043913045867013176071571758822933e-1369), SC_(0.1895505175800123992274428762526147765707e-729), SC_(0.1270569170624314329751710900359604903738e-639), SC_(1.0) }}, 
      {{ SC_(26463.7421875), SC_(19163.873046875), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3811618166243515739079675218011879119595e-24845)), SC_(BOOST_MATH_SMALL_CONSTANT(0.6205606313458244734681171673982181745552e-13482)), SC_(BOOST_MATH_SMALL_CONSTANT(0.6142217172199869571772476440726756588e-11363)), SC_(1.0) }}, 
      {{ SC_(26537.091796875), SC_(270229.28125), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.7840170224157673881647928730837906756439e-38821)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1472617967355003738702596497793040110774e-200234)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1878298461961286974428732052785606229546e-161413)) }}, 
      {{ SC_(26844.490234375), SC_(0.000473332009278237819671630859375), SC_(0.9688708782196044921875), SC_(0.2465535777981155170717536487881773845459e-371), SC_(2101.934770935125324206281681756040345732), SC_(0.1172983963191335457974021591141553130826e-374), SC_(1.0) }}, 
      {{ SC_(27758.564453125), SC_(0.18002581782639026641845703125e-4), SC_(0.905801355838775634765625), SC_(0.7572136353491294299735386009252448431571e-1196), SC_(55536.78080233768051407979393020163072174), SC_(0.1363445313915740025302288081798092363667e-1200), SC_(1.0) }}, 
      {{ SC_(28065.55078125), SC_(3.5871093273162841796875), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.2831918939336931775182831068832737617574e-18398)), SC_(0.4052213968913740427625123980894064134795e-15), SC_(BOOST_MATH_SMALL_CONSTANT(0.6988572077046741208424416664469332024614e-18383)), SC_(1.0) }}, 
      {{ SC_(28187.794921875), SC_(384992.25), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.4195483788367892268291818901823265579288e-44685)), SC_(BOOST_MATH_SMALL_CONSTANT(0.4922837564878794849276603313467462725246e-172940)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1173365888941702808662502166307690959849e-128254)) }}, 
      {{ SC_(30225.46484375), SC_(0.1016343958326615393161773681640625e-4), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.5735445758829078877322279214267862293538e-6019)), SC_(98380.99406981288047553435885352296888513), SC_(BOOST_MATH_SMALL_CONSTANT(0.5829831069565231146984689007106713301415e-6024)), SC_(1.0) }}, 
      {{ SC_(30472.796875), SC_(42078.734375), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.9483704074780328821085578083750287302226e-21437)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2655891017223718785870199558721735657561e-35320)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.2800478585457378085797559061293897649618e-13883)) }}, 
      {{ SC_(31113.873046875), SC_(2243.24951171875), SC_(0.9688708782196044921875), SC_(0.1738403130458333833378719546384908867263e-3571), SC_(0.2483709955132829075876446969586895664448e-3810), SC_(1.0), SC_(0.1428730719368868973678261648125656163208e-238) }}, 
      {{ SC_(31202.068359375), SC_(0.3144348561763763427734375), SC_(0.9688708782196044921875), SC_(0.1014606551373880184881575878619157270648e-431), SC_(0.1099900996643561454577515798148436940672), SC_(0.9224526157081734634028730038649490917215e-431), SC_(1.0) }}, 
      {{ SC_(31365.501953125), SC_(42.0266265869140625), SC_(0.8350250720977783203125), SC_(0.2922365214408881636459506278468027347876e-2492), SC_(0.3844722578182003219478844936798405179602e-139), SC_(0.7600978106958076047155690712834246165663e-2353), SC_(1.0) }}, 
      {{ SC_(31426.568359375), SC_(3.5692617893218994140625), SC_(0.905801355838775634765625), SC_(0.3636457231475868049124966817745055235491e-1357), SC_(0.3184250129085964454474326617632075570021e-15), SC_(0.1142013687385704574933108386765313909448e-1341), SC_(1.0) }}, 
      {{ SC_(32071.69921875), SC_(37284.46484375), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1415938233451437073644891654526396338438e-20794)), SC_(BOOST_MATH_SMALL_CONSTANT(0.5554990610707697922721920634839578452726e-22591)), SC_(1.0), SC_(0.3923187099176681260861333290360925901128e-1796) }}, 
      {{ SC_(32841.03515625), SC_(0.00211882009170949459075927734375), SC_(0.81474220752716064453125), SC_(0.1061606319772079274790936641952283975456e-2925), SC_(461.1125150121719098661013588359762674941), SC_(0.2302271756263341129412695244629710642605e-2928), SC_(1.0) }}, 
      {{ SC_(33019.796875), SC_(0.3179961740970611572265625), SC_(0.913384497165679931640625), SC_(0.9925450963325257937067158349703657211857e-1303), SC_(0.1028972504039433204138221286589713750593), SC_(0.9645982690850294997294805844164437309475e-1302), SC_(1.0) }}, 
      {{ SC_(33341.328125), SC_(44482.7265625), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.4158755572080177693624137640217208898905e-24164)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2696826538514875460811876828109498848842e-23081)), SC_(0.1542092349169174552498114444757830719228e-1082), SC_(1.0) }}, 
      {{ SC_(33342.82421875), SC_(33088.58203125), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.4511196723857646439931417508868110380746e-19999)), SC_(BOOST_MATH_SMALL_CONSTANT(0.4047086548745808875099380119057871658307e-27199)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.8971203865578789500681498288400685913727e-7200)) }}, 
      {{ SC_(34105.53125), SC_(27232.5546875), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.858632509974686789044520831374440759616e-31326)), SC_(BOOST_MATH_SMALL_CONSTANT(0.193563172259515494452895394204534986601e-18298)), SC_(BOOST_MATH_SMALL_CONSTANT(0.443592910754476814082200170995698799272e-13027)), SC_(1.0) }}, 
      {{ SC_(34199.625), SC_(0.000267275259830057621002197265625), SC_(0.81474220752716064453125), SC_(0.132643294506611330073418825528524868617e-3046), SC_(3730.460399129728567420227043754630634483), SC_(0.3555681613388937499195389288597612766175e-3050), SC_(1.0) }}, 
      {{ SC_(34657.7421875), SC_(103.4410247802734375), SC_(0.81474220752716064453125), SC_(0.4146866532289065093188708344417768991589e-3163), SC_(0.159293278333510523988593026460220721103e-306), SC_(0.2603290343241488086207617300289569355598e-2856), SC_(1.0) }}, 
      {{ SC_(34691.16796875), SC_(0.1412391960620880126953125), SC_(0.905801355838775634765625), SC_(0.5809634550216812209088028784875721779853e-1494), SC_(1.513770318715366625025854737630098626195), SC_(0.3837857354177119719624259539664969858835e-1494), SC_(1.0) }}, 
      {{ SC_(35037.50390625), SC_(11.6895084381103515625), SC_(0.9688708782196044921875), SC_(0.1392515511651523124059194101920454621853e-501), SC_(0.141084174330549940047030109734968874949e-45), SC_(0.9870104271149227190945465193986459578784e-456), SC_(1.0) }}, 
      {{ SC_(35093.859375), SC_(0.19068343937397003173828125), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.2564880079031630591421972951902695966518e-31446)), SC_(0.6563018747289881100024884524131931727022), SC_(BOOST_MATH_SMALL_CONSTANT(0.3908079769070851238843257638900015112909e-31446)), SC_(1.0) }}, 
      {{ SC_(35247.55078125), SC_(0.00044111299212090671062469482421875), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.524416373059227452327029532233653483142e-31584)), SC_(2255.97273393481107215818585100800014121), SC_(BOOST_MATH_SMALL_CONSTANT(0.2324568755512189035231803971642924529304e-31587)), SC_(1.0) }}, 
      {{ SC_(35426.453125), SC_(0.25611940145608969032764434814453125e-4), SC_(0.81474220752716064453125), SC_(0.8803503845736481738526768470619380243194e-3156), SC_(39033.23844697706670690655117716505316948), SC_(0.2255386484955687817889300452155100323305e-3160), SC_(1.0) }}, 
      {{ SC_(35604.6640625), SC_(0.0404350571334362030029296875), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.2995110884485993993638424385068457774754e-18202)), SC_(15.83573475873814293328067317403119221055), SC_(BOOST_MATH_SMALL_CONSTANT(0.1891362118725368736323990363026947714916e-18203)), SC_(1.0) }}, 
      {{ SC_(36098.04296875), SC_(0.00035800389014184474945068359375), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.9429878307894887982229671727138664099931e-7188)), SC_(2782.216745908151514947359188102505687192), SC_(BOOST_MATH_SMALL_CONSTANT(0.3389339928948222109519281563909451175851e-7191)), SC_(1.0) }}, 
      {{ SC_(36424.671875), SC_(0.020534180104732513427734375), SC_(0.913384497165679931640625), SC_(0.1998363537649993427821455663211980779133e-1436), SC_(38.80250984361917484278570877400762258836), SC_(0.5150088346614031104127057660043541503683e-1438), SC_(1.0) }}, 
      {{ SC_(36495.27734375), SC_(2484.92626953125), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.2891129665563148807756785314594203541986e-5070)), SC_(0.7483339000415565880282347712036234787721e-4016), SC_(0.3863422017100385497186393404241853178733e-1054), SC_(1.0) }}, 
      {{ SC_(37065.7890625), SC_(2610.560791015625), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.3309451879311226443563015822821123087694e-32337)), SC_(0.8761279815637359392108508293101243357992e-4182), SC_(BOOST_MATH_SMALL_CONSTANT(0.3777361240539802091868865758227080822171e-28155)), SC_(1.0) }}, 
      {{ SC_(37207.1328125), SC_(3485.194091796875), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3056312468045395693244553222208424065696e-5168)), SC_(BOOST_MATH_SMALL_CONSTANT(0.9580419324916712053803069946390067494199e-5168)), SC_(0.2418594078057093959803756377889539827651), SC_(0.7581405921942906040196243622110460172349) }}, 
      {{ SC_(37353.54296875), SC_(12.2282161712646484375), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.6127780828699515822469574632599335812528e-7443)), SC_(0.8550114765655429655165308741366514996961e-48), SC_(BOOST_MATH_SMALL_CONSTANT(0.7166898920835451515299975390148663687493e-7395)), SC_(1.0) }}, 
      {{ SC_(38782.8046875), SC_(35499.7734375), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3154615245272554347164260433201874137959e-22331)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2883157131832080481457769642315887354047e-39245)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.9139488995219699877558490838998145341563e-16914)) }}, 
      {{ SC_(38926.94140625), SC_(0.02303241193294525146484375), SC_(0.8350250720977783203125), SC_(0.1499284844117009513270765293679465581648e-3051), SC_(33.60081130975603606394362964314838746437), SC_(0.4462049532957825788106406576933266995857e-3053), SC_(1.0) }}, 
      {{ SC_(39031.0078125), SC_(0.00275336927734315395355224609375), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1333042063563220018903085469232404224353e-33877)), SC_(352.2136571794090544917638968073346300555), SC_(BOOST_MATH_SMALL_CONSTANT(0.3784754044571874378745530172666885958958e-33880)), SC_(1.0) }}, 
      {{ SC_(39044.17578125), SC_(143514.0), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.2445319239265617598939834130184288746047e-41153)), SC_(BOOST_MATH_SMALL_CONSTANT(0.9420859125561112963495678455294030032674e-216793)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3852609088533733238151472859262645829194e-175639)) }}, 
      {{ SC_(39369.1875), SC_(0.004928432404994964599609375), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.874124682703121149706250853273509037208e-7839)), SC_(192.0514067601594880194418396360822303492), SC_(BOOST_MATH_SMALL_CONSTANT(0.4551514083907537411987592363241931698251e-7841)), SC_(1.0) }}, 
      {{ SC_(39833.8984375), SC_(0.413198292255401611328125), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1890257504908931707894003312804909768565e-7931)), SC_(0.02696284642773986106932257472445046354622), SC_(BOOST_MATH_SMALL_CONSTANT(0.7010600716711425574687549185433903929967e-7930)), SC_(1.0) }}, 
      {{ SC_(40020.80078125), SC_(38980.671875), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1340168901428884537927946857577049637898e-30463)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2583038216475443404512247159191887250728e-23780)), SC_(BOOST_MATH_SMALL_CONSTANT(0.5188343296203900135697408474883260619761e-6683)), SC_(1.0) }}, 
      {{ SC_(40673.2578125), SC_(3205.197998046875), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.6802197549545965984035196789549544466361e-21306)), SC_(BOOST_MATH_SMALL_CONSTANT(0.258943490416802222895137511807214102674e-4983)), SC_(BOOST_MATH_SMALL_CONSTANT(0.262690424794883651458380029209063549487e-16322)), SC_(1.0) }}, 
      {{ SC_(41349.45703125), SC_(38.29947662353515625), SC_(0.81474220752716064453125), SC_(0.6425739337486675098510616911375013747856e-3711), SC_(0.6234000656743663181579777483541679566988e-133), SC_(0.1030756923410907502062264526828802816786e-3577), SC_(1.0) }}, 
      {{ SC_(41562.9375), SC_(0.26003241146099753677845001220703125e-4), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.4451628432615203302839774316230569936245e-27244)), SC_(38445.53395226496611584481972736517371363), SC_(BOOST_MATH_SMALL_CONSTANT(0.1157905216804237334489568087834910631462e-27248)), SC_(1.0) }}, 
      {{ SC_(42022.34375), SC_(0.4905900061130523681640625), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1417209460579730791079348883478424448945e-21482)), SC_(0.009737853159983752988806478754785472880161), SC_(BOOST_MATH_SMALL_CONSTANT(0.1455361297091170471303137485376139560109e-21480)), SC_(1.0) }}, 
      {{ SC_(42083.65625), SC_(4.19858837127685546875), SC_(0.9688708782196044921875), SC_(0.3768163821884418241224052326287648485896e-587), SC_(0.2978888719443599109772257168060445641745e-18), SC_(0.1264956222529937610270595263016474793515e-568), SC_(1.0) }}, 
      {{ SC_(42177.98046875), SC_(2.612369060516357421875), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.6282772223306738618674595731246666952309e-8399)), SC_(0.1193491464720943905209382805095579667749e-11), SC_(BOOST_MATH_SMALL_CONSTANT(0.5264195353735306700582799804942401915427e-8387)), SC_(1.0) }}, 
      {{ SC_(42524.5078125), SC_(0.2802095115184783935546875), SC_(0.81474220752716064453125), SC_(0.1193524162120151495829870822995328691468e-3787), SC_(0.1622187320901969961897451599226976127073), SC_(0.7357499018402678581539835169646052661627e-3787), SC_(1.0) }}, 
      {{ SC_(42581.58984375), SC_(0.37374455132521688938140869140625e-4), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.4880975703369441798957686255404298423277e-36959)), SC_(26745.00891443861029221559998579428768041), SC_(BOOST_MATH_SMALL_CONSTANT(0.1825004328465334394009465665206216869516e-36963)), SC_(1.0) }}, 
      {{ SC_(43662.40625), SC_(0.0046618557535111904144287109375), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.8908986356863706658573384931232469735442e-22321)), SC_(203.5395372687220792563282284831229950607), SC_(BOOST_MATH_SMALL_CONSTANT(0.4377029876559884758218235401695605907255e-22323)), SC_(1.0) }}, 
      {{ SC_(44090.55859375), SC_(0.10735684554674662649631500244140625e-4), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1290253648236426039020448598282133966015e-22539)), SC_(93136.0266985255526497037479478000941428), SC_(BOOST_MATH_SMALL_CONSTANT(0.1385343238243222368094895425484609570952e-22544)), SC_(1.0) }}, 
      {{ SC_(44669.99609375), SC_(0.4677930337493307888507843017578125e-4), SC_(0.9688708782196044921875), SC_(0.2261282329889107346362825204067670515544e-616), SC_(21365.69378087691196235245281532328438315), SC_(0.1058370653946673552698442997883654609519e-620), SC_(1.0) }}, 
      {{ SC_(45414.3828125), SC_(48.6922149658203125), SC_(0.905801355838775634765625), SC_(0.124686350330629295784253960815731543812e-2004), SC_(0.6241983475781985591204988281618446972134e-166), SC_(0.1997543742536242320628759391631820477378e-1838), SC_(1.0) }}, 
      {{ SC_(45461.75390625), SC_(459125.75), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.9597732801097847670639475789719189038193e-66349)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1694625349339944115847813449396605029185e-96720)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1765651726776663809923506725204795102396e-30371)) }}, 
      {{ SC_(45960.19921875), SC_(49.68207550048828125), SC_(0.913384497165679931640625), SC_(0.1801858939735704757188458424142507079542e-1864), SC_(0.3958744688593045786782344257924267795688e-169), SC_(0.4551591682402971196985143943698001164995e-1695), SC_(1.0) }}, 
      {{ SC_(46422.796875), SC_(0.11781878769397735595703125), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3128018409960626982809502420265694906417e-30429)), SC_(2.260043414599503805378278737013694930524), SC_(BOOST_MATH_SMALL_CONSTANT(0.1384052354815022296405819797968850482285e-30429)), SC_(1.0) }}, 
      {{ SC_(46983.234375), SC_(0.0014450610615313053131103515625), SC_(0.9688708782196044921875), SC_(0.3631579655779383458200579071191530299984e-648), SC_(680.7710207928221035284165757961012688395), SC_(0.5334509761520204264540019443624319725709e-651), SC_(1.0) }}, 
      {{ SC_(47575.9296875), SC_(42.76256561279296875), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1281007314819202426546706305153763033198e-31189)), SC_(0.5446274668111871054623429242369010326878e-149), SC_(BOOST_MATH_SMALL_CONSTANT(0.2352079894757319391053891898620243458818e-31040)), SC_(1.0) }}, 
      {{ SC_(47586.44140625), SC_(449.601318359375), SC_(0.8350250720977783203125), SC_(0.1717885779363308110563529610640180833346e-4081), SC_(0.4055579985382530879990057548669177197369e-1107), SC_(0.4235857227708636823447501475688153166501e-2974), SC_(1.0) }}, 
      {{ SC_(47823.16796875), SC_(2585.581787109375), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.8873433122876154215235504655591740863431e-43004)), SC_(0.6899131916765299728241300702373898959911e-4430), SC_(BOOST_MATH_SMALL_CONSTANT(0.1286166611963627694262326368951666529208e-38573)), SC_(1.0) }}, 
      {{ SC_(48035.7734375), SC_(0.4999766315449960529804229736328125e-4), SC_(0.8350250720977783203125), SC_(0.7526697336024885931646505148613916252162e-3765), SC_(19989.58114015959355372790956380367867298), SC_(0.3765310179963477718170777308052048038044e-3769), SC_(1.0) }}, 
      {{ SC_(48295.36328125), SC_(0.0023451945744454860687255859375), SC_(0.905801355838775634765625), SC_(0.1702399821655822132964248219814399971084e-2078), SC_(415.1934803800744794350002685129457832461), SC_(0.4100256632395622464631609228605330128581e-2081), SC_(1.0) }}, 
      {{ SC_(48850.35546875), SC_(289555.46875), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1294773939114091904070677516334875748016e-60717)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1910549841723689253904285694417754227142e-60668)), SC_(0.6776970225209894702083863339546688639084e-49), SC_(1.0) }}, 
      {{ SC_(49373.98046875), SC_(222.311065673828125), SC_(0.9688708782196044921875), SC_(0.6106410258636356710957886396350286515268e-1016), SC_(0.6310884578023346318794902107496885899933e-619), SC_(0.9675997371114916339117077163833508385771e-397), SC_(1.0) }}, 
      {{ SC_(49444.171875), SC_(4.673758029937744140625), SC_(0.81474220752716064453125), SC_(0.1214094340613585096764415398046112492438e-4406), SC_(0.1709328901299858846919930075689684944451e-20), SC_(0.710275441835873295036486702114009786702e-4386), SC_(1.0) }}, 
      {{ SC_(49532.08984375), SC_(1899.7972412109375), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3285641253811721046295585659566089755466e-32673)), SC_(0.4154294863215141418765544887779036460343e-3532), SC_(BOOST_MATH_SMALL_CONSTANT(0.7909022739105375845300408580947121495777e-29141)), SC_(1.0) }}, 
      {{ SC_(49565.796875), SC_(27.7097911834716796875), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.4032053953934215782773620452150408686366e-44414)), SC_(0.3267223807451580153760937282811257617716e-102), SC_(BOOST_MATH_SMALL_CONSTANT(0.1234091752373462195294816501093343638153e-44311)), SC_(1.0) }}, 
      {{ SC_(49846.2421875), SC_(49663.23828125), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.862576376721314951484184712102874499511e-29957)), SC_(BOOST_MATH_SMALL_CONSTANT(0.3356710975950936547856944082829601692435e-75523)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.3891494210298130852536543532323001074747e-45566)) }}, 
      {{ SC_(49880.1328125), SC_(0.00046944446512497961521148681640625), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3811422605486493883342432112209493755269e-32695)), SC_(2118.813642454311489365540333218677987084), SC_(BOOST_MATH_SMALL_CONSTANT(0.1798847491406352196962183911656597563472e-32698)), SC_(1.0) }}, 
      {{ SC_(103920.8984375), SC_(0.111212420961237512528896331787109375e-4), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.402724510532558600508958354716027788154e-68113)), SC_(89905.88600644825125958799928771679492755), SC_(BOOST_MATH_SMALL_CONSTANT(0.4479400942711073418907433981637247217451e-68118)), SC_(1.0) }}, 
      {{ SC_(112769.0546875), SC_(421.523895263671875), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.1489938256413702851146155215564486467662e-5281)), SC_(0.3067957656567422861129083322098226909147e-1207), SC_(0.4856449870565412989838005491171072436419e-4074), SC_(1.0) }}, 
      {{ SC_(116919.109375), SC_(234197.234375), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.2064958853295781582868627436497981502063e-97026)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2390507369881614656651790264224788501208e-192438)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.115765375473038638637891869996823133935e-95411)) }}, 
      {{ SC_(117063.9375), SC_(0.4761638047057203948497772216796875e-4), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.3557072314175628838245768895584561334308e-9170)), SC_(20988.93220124087773696925323411944285242), SC_(BOOST_MATH_SMALL_CONSTANT(0.1694737150070613253156363270278658624233e-9174)), SC_(1.0) }}, 
      {{ SC_(117666.234375), SC_(0.003791221417486667633056640625), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.1521718998877627133096948856559430340768e-5059)), SC_(251.797624834930194264630014030887089571), SC_(BOOST_MATH_SMALL_CONSTANT(0.6043420782365256334800931559435553635151e-5062)), SC_(1.0) }}, 
      {{ SC_(119021.8671875), SC_(1724.5032958984375), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.506791007427913391904456640012169560459e-10673)), SC_(0.1395293850133434234550711107533045517001e-3926), SC_(BOOST_MATH_SMALL_CONSTANT(0.3632145353320722644944857764132454568671e-6746)), SC_(1.0) }}, 
      {{ SC_(121846.6484375), SC_(0.0102547816932201385498046875), SC_(0.9688708782196044921875), SC_(0.8947177053524591999969874426003937360555e-1677), SC_(85.97781064589153929768992522596508618429), SC_(0.1040637925798606503865143332970238762629e-1678), SC_(1.0) }}, 
      {{ SC_(139635.859375), SC_(0.00359443644993007183074951171875), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1200411021373305972085728293542348706339e-10937)), SC_(266.0598728377260065511100686016954842333), SC_(BOOST_MATH_SMALL_CONSTANT(0.4511807844490157479272247531660627407605e-10940)), SC_(1.0) }}, 
      {{ SC_(139638.109375), SC_(0.00364904291927814483642578125), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.7124698785671631677263569877949201217441e-91522)), SC_(261.9007208942754265545010052942457193583), SC_(BOOST_MATH_SMALL_CONSTANT(0.2720381509964511867366632709770210627566e-91524)), SC_(1.0) }}, 
      {{ SC_(140613.5625), SC_(0.04485572874546051025390625), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.1213996798462934451546061797201029097163e-6045)), SC_(12.78564295017450834301178895528450273433), SC_(BOOST_MATH_SMALL_CONSTANT(0.9495000002689461028413898434572917976252e-6047)), SC_(1.0) }}, 
      {{ SC_(143155.5625), SC_(0.00386732374317944049835205078125), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.4964902710277630208125952137476169753569e-128264)), SC_(246.4257926660344960293210192076077331621), SC_(BOOST_MATH_SMALL_CONSTANT(0.2014765847585708337294337097631779282769e-128266)), SC_(1.0) }}, 
      {{ SC_(149909.609375), SC_(2.0033237934112548828125), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.5601865597517213906982198460312303425576e-29839)), SC_(0.4282985391041620067373351878082277338411e-10), SC_(BOOST_MATH_SMALL_CONSTANT(0.1307934789886089894651986816995366289121e-29828)), SC_(1.0) }}, 
      {{ SC_(155018.640625), SC_(0.27750885486602783203125), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.2189600536278663888089739250282366199981e-12142)), SC_(0.1177997645565165471561636064914377897339), SC_(BOOST_MATH_SMALL_CONSTANT(0.1858747803547742923699583327919111965553e-12141)), SC_(1.0) }}, 
      {{ SC_(155105.15625), SC_(27467.107421875), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1102644209378919640642692274235750137785e-42809)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2501495324258768121484793466326974864798e-33579)), SC_(BOOST_MATH_SMALL_CONSTANT(0.4407940317480506300101086260146805075637e-9230)), SC_(1.0) }}, 
      {{ SC_(155440.6875), SC_(25353.65625), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.8941214025675720839947382390911459942351e-32016)), SC_(BOOST_MATH_SMALL_CONSTANT(0.7341950414271885193993490458038675049208e-31832)), SC_(0.1217825444352641760127168800486312561702e-183), SC_(1.0) }}, 
      {{ SC_(170849.5), SC_(0.00046143750660121440887451171875), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.8932798569056563733432357552812282247856e-13382)), SC_(2154.552031316987476016365197599340088807), SC_(BOOST_MATH_SMALL_CONSTANT(0.4146011996561678697797342745398553532881e-13385)), SC_(1.0) }}, 
      {{ SC_(171593.0), SC_(0.0390150845050811767578125), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.3808818562331720430729550615151248330977e-148923)), SC_(15.6782046961596187023865989050765796256), SC_(BOOST_MATH_SMALL_CONSTANT(0.2429371625224852285654983284139567086578e-148924)), SC_(1.0) }}, 
      {{ SC_(172891.0), SC_(0.00412352383136749267578125), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1261508876351832263878339216717470434296e-150049)), SC_(230.2002478717688094390524743042864738996), SC_(BOOST_MATH_SMALL_CONSTANT(0.54800500347616723860757541011092778473e-150052)), SC_(1.0) }}, 
      {{ SC_(175682.734375), SC_(342375.09375), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3387341086210335388285649512749436564435e-144096)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1795794751912361346488594465486064257971e-370658)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.5301487822478035199922994877956397118574e-226562)) }}, 
      {{ SC_(177298.140625), SC_(0.00180856394581496715545654296875), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.8516891724051227220598361169759734476922e-35289)), SC_(540.4074280614505942989554143978724575293), SC_(BOOST_MATH_SMALL_CONSTANT(0.1576013074913314814731763422753196121745e-35291)), SC_(1.0) }}, 
      {{ SC_(184058.265625), SC_(0.0481239259243011474609375), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.5011685357386681038084289717245581028305e-94080)), SC_(11.29830628383595379593224834916619211301), SC_(BOOST_MATH_SMALL_CONSTANT(0.4435784649028946789930916331332918288727e-94081)), SC_(1.0) }}, 
      {{ SC_(189710.828125), SC_(0.0004468346596695482730865478515625), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.5188315366392478090272792788651594123975e-169975)), SC_(2225.270299039879402937966752903472740162), SC_(BOOST_MATH_SMALL_CONSTANT(0.2331543888682215998996030637290955805423e-169978)), SC_(1.0) }}, 
      {{ SC_(192153.234375), SC_(4460.083984375), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1022452100691270242676491725999926765425e-167048)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1398359931090325990354518704244200134233e-9249)), SC_(BOOST_MATH_SMALL_CONSTANT(0.731179489599681422187067081110076015194e-157799)), SC_(1.0) }}, 
      {{ SC_(194577.984375), SC_(0.00036247042589820921421051025390625), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.8624226692331135959285353838204701375781e-17318)), SC_(2746.119705338191749945130579954251613059), SC_(BOOST_MATH_SMALL_CONSTANT(0.3140513749479482402469467099759580915747e-17321)), SC_(1.0) }}, 
      {{ SC_(196362.0), SC_(2509.231201171875), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3856482340702243145420560651420455809962e-6481)), SC_(BOOST_MATH_SMALL_CONSTANT(0.5652420341505424085842925275720886631988e-5849)), SC_(0.68227097556498000743757316100203279656e-632), SC_(1.0) }}, 
      {{ SC_(207621.90625), SC_(0.00042250819387845695018768310546875), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.3544168575172053195688364323122776381218e-180191)), SC_(2354.032323315899608882640510773291239734), SC_(BOOST_MATH_SMALL_CONSTANT(0.1505573453715250081770279309998939513989e-180194)), SC_(1.0) }}, 
      {{ SC_(212600.609375), SC_(3789.47216796875), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1162875437533969677380975629265995346525e-190706)), SC_(BOOST_MATH_SMALL_CONSTANT(0.3572904950620103274095363616181997412084e-8289)), SC_(BOOST_MATH_SMALL_CONSTANT(0.3254705774728612096631189967081659505062e-182417)), SC_(1.0) }}, 
      {{ SC_(212603.09375), SC_(20133.15625), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.2362572268084451377670154616635164036924e-141527)), SC_(BOOST_MATH_SMALL_CONSTANT(0.3652077641977781302789589817509647608679e-29756)), SC_(BOOST_MATH_SMALL_CONSTANT(0.646911840243626703260093779489488783331e-111771)), SC_(1.0) }}, 
      {{ SC_(224776.03125), SC_(0.0415735431015491485595703125), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1658960264768293374695368021921618319401e-201391)), SC_(14.08884084986776431793587580402506526197), SC_(BOOST_MATH_SMALL_CONSTANT(0.1177499470997192873580592646539708667598e-201392)), SC_(1.0) }}, 
      {{ SC_(228984.171875), SC_(46.157588958740234375), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1540400982450543871048593457437142794287e-198733)), SC_(0.8740613889857634925378998367387854310713e-191), SC_(BOOST_MATH_SMALL_CONSTANT(0.1762348734152394371413169466461289084642e-198542)), SC_(1.0) }}, 
      {{ SC_(231101.984375), SC_(155.261932373046875), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.8852764944774570528678983043239949746808e-118150)), SC_(0.1763399753127686268061992508179417253384e-560), SC_(BOOST_MATH_SMALL_CONSTANT(0.5020282513407808892851228645306011609752e-117589)), SC_(1.0) }}, 
      {{ SC_(232169.4375), SC_(16.358287811279296875), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1004815071118392356784942205953132166455e-20674)), SC_(0.587724374876512150244459824716029030012e-75), SC_(BOOST_MATH_SMALL_CONSTANT(0.1709670577010721905366456702457756799289e-20599)), SC_(1.0) }}, 
      {{ SC_(235582.25), SC_(0.011084310710430145263671875), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1017643250025693405674093870392166132869e-154402)), SC_(78.16455130127354840873852787165449218854), SC_(BOOST_MATH_SMALL_CONSTANT(0.1301924252214203870286963316556287714256e-154404)), SC_(1.0) }}, 
      {{ SC_(237315.15625), SC_(3.7124271392822265625), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.7536126865779733008730126500222323004189e-155539)), SC_(0.4688134850906469512740105351527398525871e-19), SC_(BOOST_MATH_SMALL_CONSTANT(0.1607489354603908826711205809046906316143e-155519)), SC_(1.0) }}, 
      {{ SC_(239513.921875), SC_(3236.540771484375), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.9078466292704468179757983138091189896474e-13616)), SC_(BOOST_MATH_SMALL_CONSTANT(0.4763031957127142496806317602545438438184e-7466)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1906026743977634660356542047153393121634e-6149)), SC_(1.0) }}, 
      {{ SC_(241256.71875), SC_(417787.0625), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.4139218165554650302732575887154116926375e-190178)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1384615419520965439611502658608246043672e-187999)), SC_(0.2989435266427043771761060718022390102514e-2178), SC_(1.0) }}, 
      {{ SC_(242747.59375), SC_(307.633697509765625), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.4135448462258999745189194724632122657543e-9882)), SC_(0.1641240444860787359657006071324757612212e-1025), SC_(BOOST_MATH_SMALL_CONSTANT(0.2519709086629153292670089405584358725747e-8856)), SC_(1.0) }}, 
      {{ SC_(244254.640625), SC_(17.7450542449951171875), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3430985610854914780380641194927931339872e-10517)), SC_(0.4237689124483248224991752915244175660383e-81), SC_(BOOST_MATH_SMALL_CONSTANT(0.8096359855734569989336526965289033914691e-10436)), SC_(1.0) }}, 
      {{ SC_(246462.734375), SC_(43952.4453125), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3938710785527875787688018700990301514826e-53609)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1105323376193801074638945243105005161062e-56396)), SC_(1.0), SC_(0.2806307536605947812694951237611079993073e-2787) }}, 
      {{ SC_(251274.4375), SC_(0.1303907993133179843425750732421875e-4), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.1284050969853127988754756985503067194045e-10800)), SC_(76679.51739053277711700788121412509668115), SC_(BOOST_MATH_SMALL_CONSTANT(0.1674568403076129834643607249408326608986e-10805)), SC_(1.0) }}, 
      {{ SC_(256001.953125), SC_(0.3102722465991973876953125), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.4517081658880984032838741386685794411436e-11004)), SC_(0.0606067283762034266767895128284342433694), SC_(BOOST_MATH_SMALL_CONSTANT(0.7453102617323536411221722791114842895032e-11003)), SC_(1.0) }}, 
      {{ SC_(256341.890625), SC_(0.045557327568531036376953125), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3046836016076670331250987993117619690904e-10090)), SC_(12.14326372762000066047653769127404375479), SC_(BOOST_MATH_SMALL_CONSTANT(0.2509075059571180069981406913298733504809e-10091)), SC_(1.0) }}, 
      {{ SC_(259312.890625), SC_(4.09720611572265625), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.1889642755657341099597171869122225183632e-10211)), SC_(0.4468819938274178704121653254171240524415e-21), SC_(BOOST_MATH_SMALL_CONSTANT(0.422850502315630443921252075167032941947e-10190)), SC_(1.0) }}, 
      {{ SC_(265560.28125), SC_(14.26784992218017578125), SC_(0.9688708782196044921875), SC_(0.2255456396828479920717334888632409438555e-3672), SC_(0.5092282394886762178699134425746537111738e-67), SC_(0.4429165984771814351326604284513000560131e-3605), SC_(1.0) }}, 
      {{ SC_(272688.46875), SC_(0.4279204308986663818359375), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.8125575456729716243938388570732195995244e-236660)), SC_(0.009774280032563511863022336056748788267978), SC_(BOOST_MATH_SMALL_CONSTANT(0.8313221464556926613351826150695679545886e-236658)), SC_(1.0) }}, 
      {{ SC_(279777.4375), SC_(110919.8125), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.6148430343219194706377521237908296432287e-103891)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1156211272107542159862785096226588071867e-101231)), SC_(0.5317739492378269700677621217632990048617e-2659), SC_(1.0) }}, 
      {{ SC_(280536.21875), SC_(1998.197021484375), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.4730459791299165766564524415469547707803e-13165)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1075155179828709931223331787105624315381e-5162)), SC_(BOOST_MATH_SMALL_CONSTANT(0.4399792588129284315579695728392266529331e-8002)), SC_(1.0) }}, 
      {{ SC_(290996.875), SC_(0.00046665043919347226619720458984375), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.2740689055798068038261548986673720163647e-148738)), SC_(2129.814074427695605797478885855586306243), SC_(BOOST_MATH_SMALL_CONSTANT(0.1286820802202897094627135589769732633674e-148741)), SC_(1.0) }}, 
      {{ SC_(294260.65625), SC_(0.393247187137603759765625), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.5366621090960572054689623726756327820964e-58566)), SC_(0.01595930469496627944511629771146368297354), SC_(BOOST_MATH_SMALL_CONSTANT(0.3362691040451941966929326235860298229558e-58564)), SC_(1.0) }}, 
      {{ SC_(295837.75), SC_(414044.375), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.2738207633868624490637016944591828716385e-282945)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1291893132914534123455535857061490734143e-209403)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2119531069641324636847604040747073688875e-73541)), SC_(1.0) }}, 
      {{ SC_(295905.53125), SC_(0.0040032281540334224700927734375), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.3432444114466741914522489325459591711318e-151247)), SC_(236.9679241666109725149413470050507888954), SC_(BOOST_MATH_SMALL_CONSTANT(0.1448484695360460463354669319016151667841e-151249)), SC_(1.0) }}, 
      {{ SC_(296938.03125), SC_(220.9876708984375), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1066546302444701932433833129869504077407e-266057)), SC_(0.799025964451165746090446805742325043734e-788), SC_(BOOST_MATH_SMALL_CONSTANT(0.1334808066190052186293725658701353747668e-265269)), SC_(1.0) }}, 
      {{ SC_(300513.1875), SC_(0.37814271450042724609375), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.8089094344018176787861408819014348077561e-269248)), SC_(0.01993759802261050545029555014189027071964), SC_(BOOST_MATH_SMALL_CONSTANT(0.4057206056037757819954051223760869381102e-269246)), SC_(1.0) }}, 
      {{ SC_(301512.3125), SC_(290772.15625), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.4984517826717744682218608311609889507618e-287305)), SC_(BOOST_MATH_SMALL_CONSTANT(0.519892537600854112225555677828535502315e-178255)), SC_(BOOST_MATH_SMALL_CONSTANT(0.9587592562339474907280276143889734396976e-109050)), SC_(1.0) }}, 
      {{ SC_(304061.0), SC_(0.015454678796231746673583984375), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.3197884056982186223192534133421457418371e-60516)), SC_(52.7731607602237885882113216192417829959), SC_(BOOST_MATH_SMALL_CONSTANT(0.6059678842265777058340606757102341256221e-60518)), SC_(1.0) }}, 
      {{ SC_(319723.40625), SC_(20.909183502197265625), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.2590505925978753870005807652732470012149e-63642)), SC_(0.1467093689208665286611141322144853204044e-96), SC_(BOOST_MATH_SMALL_CONSTANT(0.1765739942195542522633559404183774890433e-63545)), SC_(1.0) }}, 
      {{ SC_(320904.625), SC_(29.05339813232421875), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.301795651159796849839717117809261571877e-210325)), SC_(0.3822651521762847371013382041405160862927e-130), SC_(BOOST_MATH_SMALL_CONSTANT(0.7894929721991014467158241189990231432032e-210195)), SC_(1.0) }}, 
      {{ SC_(322918.0625), SC_(0.00355008454062044620513916015625), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.7905337968047845333067020031953625780259e-12710)), SC_(268.7312156127938150100094841850794506944), SC_(BOOST_MATH_SMALL_CONSTANT(0.29417267175386104489176827833275889391e-12712)), SC_(1.0) }}, 
      {{ SC_(335283.75), SC_(21810.169921875), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3299040575389629100198235447056122444995e-36786)), SC_(BOOST_MATH_SMALL_CONSTANT(0.202307289118563899376051109222726987044e-35658)), SC_(0.1630707716841679611298911719116727449455e-1127), SC_(1.0) }}, 
      {{ SC_(336243.46875), SC_(3128.056640625), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1989755909590248190077843590850637980752e-68280)), SC_(BOOST_MATH_SMALL_CONSTANT(0.386253976690019495221586847622724535798e-7720)), SC_(BOOST_MATH_SMALL_CONSTANT(0.5151418573450927910578006074003632539119e-60560)), SC_(1.0) }}, 
      {{ SC_(347334.96875), SC_(4.06880474090576171875), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1151000019927157406145680469175846621823e-301442)), SC_(0.1869580828614440943080976971537425939805e-21), SC_(BOOST_MATH_SMALL_CONSTANT(0.6156460326886061099554832161303503534759e-301421)), SC_(1.0) }}, 
      {{ SC_(347788.625), SC_(37.86353302001953125), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.3911807398179509287085805652571430683349e-27266)), SC_(0.128644350335128231481736445186331109064e-166), SC_(BOOST_MATH_SMALL_CONSTANT(0.3040792221336542252097696240821869657085e-27099)), SC_(1.0) }}, 
      {{ SC_(349486.5625), SC_(0.0004298138082958757877349853515625), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1865595218115872133983227884322014482709e-69556)), SC_(2313.2859134142840503071282236890179605), SC_(BOOST_MATH_SMALL_CONSTANT(0.8064697957557503876725389314635299246407e-69560)), SC_(1.0) }}, 
      {{ SC_(349829.1875), SC_(4556.69775390625), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.8716243656492448960493553078724261487418e-179538)), SC_(BOOST_MATH_SMALL_CONSTANT(0.3005933497722659142770197020235806505349e-10583)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2899679471650323433353852583136590704171e-168954)), SC_(1.0) }}, 
      {{ SC_(351569.59375), SC_(0.0152290202677249908447265625), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.142942439498922314939960947453612938523e-27532)), SC_(53.59599328878060781737142119144735114836), SC_(BOOST_MATH_SMALL_CONSTANT(0.2667035924285124443055616600216689774719e-27534)), SC_(1.0) }}, 
      {{ SC_(358590.96875), SC_(1.7338650226593017578125), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1058148376671415191380608829185909505343e-28083)), SC_(0.2141548976451757099123225598000962489873e-9), SC_(BOOST_MATH_SMALL_CONSTANT(0.4941042153631326077987405894010837264966e-28074)), SC_(1.0) }}, 
      {{ SC_(364175.1875), SC_(2293.671875), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.2970907400871436762454120402519455621845e-34088)), SC_(BOOST_MATH_SMALL_CONSTANT(0.3942277478053191901866283024837159384948e-6048)), SC_(BOOST_MATH_SMALL_CONSTANT(0.7536017993179300124085651069786590216621e-28040)), SC_(1.0) }}, 
      {{ SC_(365061.53125), SC_(135.407379150390625), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.132561620732637353847620367820812298033e-5221)), SC_(0.9348116048625191354304436021264235582514e-524), SC_(0.1418057072067830448845425654367014413568e-4697), SC_(1.0) }}, 
      {{ SC_(366848.125), SC_(489622.875), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1593861357254841199493602244543381689225e-253990)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1087599097235379865638568519804395894287e-742823)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.6823674419891745504101999703531254110972e-488833)) }}, 
      {{ SC_(369212.46875), SC_(0.00043601836659945547580718994140625), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1589837506262236294533702040658122820427e-241982)), SC_(2280.124431101420007150565551801342146135), SC_(BOOST_MATH_SMALL_CONSTANT(0.6972590989230623579661684537574695268047e-241986)), SC_(1.0) }}, 
      {{ SC_(377515.0625), SC_(0.25923203793354332447052001953125e-4), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1846099338711330641874444967580256082628e-327634)), SC_(38562.06263811561345901519963121865990616), SC_(BOOST_MATH_SMALL_CONSTANT(0.4787345936434957111959631565210397850823e-327639)), SC_(1.0) }}, 
      {{ SC_(377897.28125), SC_(217.7733001708984375), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.5979546991309016931374244693774165753579e-327980)), SC_(0.1504122228349321219225606712861511198541e-800), SC_(BOOST_MATH_SMALL_CONSTANT(0.3975439547802701271946172226507152197205e-327179)), SC_(1.0) }}, 
      {{ SC_(381735.84375), SC_(0.1554123082314617931842803955078125e-4), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3657468778839370617455648636532628074199e-15024)), SC_(64331.53971593053993165972765577354825155), SC_(BOOST_MATH_SMALL_CONSTANT(0.568534313804658519403859557834748785329e-15029)), SC_(1.0) }}, 
      {{ SC_(384451.40625), SC_(2821.419677734375), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3774667393849660313070000619550292965817e-252276)), SC_(BOOST_MATH_SMALL_CONSTANT(0.7926054736804532094181417506264134619276e-7253)), SC_(BOOST_MATH_SMALL_CONSTANT(0.4762353427010844825792649025736193403842e-245023)), SC_(1.0) }}, 
      {{ SC_(385429.59375), SC_(3.3841388225555419921875), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.235912325871912806903517917043120974532e-345328)), SC_(0.3659527638406590216438323566506540139083e-18), SC_(BOOST_MATH_SMALL_CONSTANT(0.6446523955606258252275291431690649324288e-345310)), SC_(1.0) }}, 
      {{ SC_(386018.0), SC_(14093.7041015625), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3533816041883537299284294859041196659449e-346687)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1786199254113275768897792024673451795822e-26493)), SC_(BOOST_MATH_SMALL_CONSTANT(0.1978399685111184439989792884160734955792e-320193)), SC_(1.0) }}, 
      {{ SC_(391805.21875), SC_(0.3055977504118345677852630615234375e-4), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.6645640986776143695258650559207070499956e-5385)), SC_(32709.30102510362913407477993114535519627), SC_(BOOST_MATH_SMALL_CONSTANT(0.2031728217510905698625535037789910554642e-5389)), SC_(1.0) }}, 
      {{ SC_(392234.125), SC_(2.3254187107086181640625), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.3372946332051058353869565881444329491903e-34907)), SC_(0.116500178379113381650444797417636967012e-12), SC_(BOOST_MATH_SMALL_CONSTANT(0.2895228470015607868587943879852069969558e-34894)), SC_(1.0) }}, 
      {{ SC_(392420.34375), SC_(26721.41796875), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.3760691264606479837185828827725121521107e-204854)), SC_(BOOST_MATH_SMALL_CONSTANT(0.6134691376735092695204920695453546200521e-43174)), SC_(BOOST_MATH_SMALL_CONSTANT(0.6130204493853339987206932621927405818447e-161680)), SC_(1.0) }}, 
      {{ SC_(395628.875), SC_(169.021026611328125), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.2526414214512150994786424981884882502037e-35331)), SC_(0.2367657718298624418210670946569777464019e-643), SC_(BOOST_MATH_SMALL_CONSTANT(0.1067052131305367667538379844205511148824e-34687)), SC_(1.0) }}, 
      {{ SC_(399928.875), SC_(2.3772995471954345703125), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.4575823381440133470258713714489339986042e-5500)), SC_(0.5891271920613133184754929015938875783606e-13), SC_(BOOST_MATH_SMALL_CONSTANT(0.7767123030647523365790059307890380108771e-5487)), SC_(1.0) }}, 
      {{ SC_(402603.8125), SC_(15.56592464447021484375), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.839964051894727144748188102571476786515e-15862)), SC_(0.2276187152898794510296259141901545424811e-75), SC_(BOOST_MATH_SMALL_CONSTANT(0.3690224025845181622960356235998888477582e-15786)), SC_(1.0) }}, 
      {{ SC_(406768.65625), SC_(0.00042969666537828743457794189453125), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.3845971664948874841658532459081877377021e-16009)), SC_(2313.76926505807402290357969801321322552), SC_(BOOST_MATH_SMALL_CONSTANT(0.1662210542351699700942923632024041265388e-16012)), SC_(1.0) }}, 
      {{ SC_(408381.65625), SC_(0.456345951533876359462738037109375e-4), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.256636316436966534711309393776406356604e-36342)), SC_(21899.70679105534773755741399478867643693), SC_(BOOST_MATH_SMALL_CONSTANT(0.1171871015833811629121890074513165499571e-36346)), SC_(1.0) }}, 
      {{ SC_(408998.0), SC_(0.0042365207336843013763427734375), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.4679643318850173892444623183563750454659e-5621)), SC_(222.9260767164665503554164247892365552352), SC_(BOOST_MATH_SMALL_CONSTANT(0.2099190632059649758309471308391737222338e-5623)), SC_(1.0) }}, 
      {{ SC_(413148.84375), SC_(0.4799632370122708380222320556640625e-4), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.4760216892086520862254727449513859060669e-370163)), SC_(20821.4247115356994008030634209707211316), SC_(BOOST_MATH_SMALL_CONSTANT(0.2286210938029239011421440938749654302622e-370167)), SC_(1.0) }}, 
      {{ SC_(422703.84375), SC_(15069.125), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1894290547150840332529440634809860623788e-28480)), SC_(BOOST_MATH_SMALL_CONSTANT(0.5317858193741856644130900155741731742e-28515)), SC_(0.9999999999999999999999999999999999719269), SC_(0.2807308626303566343788814258620297467638e-34) }}, 
      {{ SC_(424641.03125), SC_(0.2840512692928314208984375), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.3281697221736068695894776974100410805984e-217046)), SC_(0.07983838849602386203799283789214340070297), SC_(BOOST_MATH_SMALL_CONSTANT(0.4110425177105753921575682833010882185377e-217045)), SC_(1.0) }}, 
      {{ SC_(431857.34375), SC_(0.000211423015571199357509613037109375), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.5772928801419627986284807700357329751852e-18560)), SC_(4716.320526853965142300050830950109310733), SC_(BOOST_MATH_SMALL_CONSTANT(0.1224032329556379061882981898965492987967e-18563)), SC_(1.0) }}, 
      {{ SC_(433260.8125), SC_(3.3808853626251220703125), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.9743394652123491454497722494064540082877e-18624)), SC_(0.2560546738053348250776027384729315592872e-18), SC_(BOOST_MATH_SMALL_CONSTANT(0.3805200860942258123591032689689538525655e-18605)), SC_(1.0) }}, 
      {{ SC_(434088.21875), SC_(21.81499481201171875), SC_(0.12707412242889404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.7666944874274611518715379495155788957394e-388925)), SC_(0.3007553193509600488237813376679700633905e-103), SC_(BOOST_MATH_SMALL_CONSTANT(0.254923001555554620514995644832550525135e-388821)), SC_(1.0) }}, 
      {{ SC_(434689.125), SC_(39291.4765625), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.4229593173885190819792612716082535703903e-67453)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2997707408973057615935360098045888677795e-58830)), SC_(BOOST_MATH_SMALL_CONSTANT(0.141094262943231928276825070136084907e-8622)), SC_(1.0) }}, 
      {{ SC_(435121.25), SC_(397.58978271484375), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1391846018183584493348497097868590409154e-86771)), SC_(0.1011089115991303029245123638141426629899e-1381), SC_(BOOST_MATH_SMALL_CONSTANT(0.13765809523317592290575564449364874034e-85389)), SC_(1.0) }}, 
      {{ SC_(437285.3125), SC_(0.2788266647257842123508453369140625e-4), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.7080016520655375267035486305854570379383e-223509)), SC_(35851.012645549836380276487988456969042), SC_(BOOST_MATH_SMALL_CONSTANT(0.1974844222854668306991988406185103636153e-223513)), SC_(1.0) }}, 
      {{ SC_(442472.9375), SC_(49476.66015625), SC_(0.1355634629726409912109375), SC_(BOOST_MATH_SMALL_CONSTANT(0.6336326725813869671779066098442524839183e-387139)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2249187802897843416031847629628799944729e-69724)), SC_(BOOST_MATH_SMALL_CONSTANT(0.2817162140773738373578099290063302965899e-317414)), SC_(1.0) }}, 
      {{ SC_(450819.5), SC_(499216.46875), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3800121390355127178063074898342430570494e-349644)), SC_(BOOST_MATH_SMALL_CONSTANT(0.9678656246284879041515322169506269463219e-285456)), SC_(BOOST_MATH_SMALL_CONSTANT(0.3926290275898362964767484676866590121196e-64188)), SC_(1.0) }}, 
      {{ SC_(457779.125), SC_(0.326781928539276123046875), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.5550829230252479526732424261273786286927e-40738)), SC_(0.03865177027724154668769750896774259956337), SC_(BOOST_MATH_SMALL_CONSTANT(0.1436112548128448741327568829062085238803e-40736)), SC_(1.0) }}, 
      {{ SC_(458356.625), SC_(0.00327513157390058040618896484375), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.3934256588938313890657511884630295473806e-40789)), SC_(292.0201861612453957241730382306629407247), SC_(BOOST_MATH_SMALL_CONSTANT(0.1347255010229302168172554227989064367004e-40791)), SC_(1.0) }}, 
      {{ SC_(462545.71875), SC_(0.01835658587515354156494140625), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.6842410988092162996870266776720414780451e-41162)), SC_(42.43584359872765638163172784469089833824), SC_(BOOST_MATH_SMALL_CONSTANT(0.1612413094174311960166824477001882166453e-41163)), SC_(1.0) }}, 
      {{ SC_(466997.53125), SC_(0.356361567974090576171875), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.3241995530691347719937107476224957921527e-6418)), SC_(0.02384677265659975718672488537621955689915), SC_(BOOST_MATH_SMALL_CONSTANT(0.1359511233397070742246772924547520956389e-6416)), SC_(1.0) }}, 
      {{ SC_(468932.8125), SC_(0.182366857188753783702850341796875e-4), SC_(0.632396042346954345703125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1103527708644207448237778040457649555939e-93327)), SC_(54820.89133734763217172708014125126167222), SC_(BOOST_MATH_SMALL_CONSTANT(0.2012969292771077366711511442513115417448e-93332)), SC_(1.0) }}, 
      {{ SC_(470942.5), SC_(0.326362073421478271484375), SC_(0.913384497165679931640625), SC_(BOOST_MATH_SMALL_CONSTANT(0.1471321546322475787061574604050565023955e-18534)), SC_(0.03855775001051170472627007361165177373414), SC_(BOOST_MATH_SMALL_CONSTANT(0.3815890569136841848282960464759255989168e-18533)), SC_(1.0) }}, 
      {{ SC_(473791.3125), SC_(0.00037331905332393944263458251953125), SC_(0.9688708782196044921875), SC_(BOOST_MATH_SMALL_CONSTANT(0.5426122640062687858436002133933513536523e-6511)), SC_(2665.063154701061606205642057038907522539), SC_(BOOST_MATH_SMALL_CONSTANT(0.2036020283606124332820904603012161247452e-6514)), SC_(1.0) }}, 
      {{ SC_(474410.90625), SC_(1.53685867786407470703125), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.117766994152457958795877225944368333002e-242484)), SC_(0.1678625980035251962785258430723784518394e-8), SC_(BOOST_MATH_SMALL_CONSTANT(0.7015678033887262640021676404777871768517e-242476)), SC_(1.0) }}, 
      {{ SC_(481669.8125), SC_(360.35894775390625), SC_(0.8350250720977783203125), SC_(BOOST_MATH_SMALL_CONSTANT(0.1292404819014634354658936369356745477441e-38001)), SC_(0.1184119790891173687044477395438058404198e-1283), SC_(BOOST_MATH_SMALL_CONSTANT(0.1091447697231683701613307519286097336645e-36717)), SC_(1.0) }}, 
      {{ SC_(485412.125), SC_(483725.21875), SC_(0.81474220752716064453125), SC_(BOOST_MATH_SMALL_CONSTANT(0.8593009078995238625969168132860357389775e-291741)), SC_(BOOST_MATH_SMALL_CONSTANT(0.4682031506788712249402837988058297965792e-397392)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.544865187939051001712434628531605526675e-105651)) }}, 
      {{ SC_(489093.59375), SC_(101.60869598388671875), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.2679049247465053509778519007374647739724e-320562)), SC_(0.1239584973345346670544577370734255574289e-418), SC_(BOOST_MATH_SMALL_CONSTANT(0.2161246953675901063139086740469757800594e-320143)), SC_(1.0) }}, 
      {{ SC_(489183.34375), SC_(118808.328125), SC_(0.905801355838775634765625), SC_(BOOST_MATH_SMALL_CONSTANT(0.1458359410123570793275225181353576621217e-130434)), SC_(BOOST_MATH_SMALL_CONSTANT(0.259453747115697820698795192063284012701e-142915)), SC_(1.0), SC_(BOOST_MATH_SMALL_CONSTANT(0.1779079596666185278653733063531830995884e-12480)) }}, 
      {{ SC_(491651.625), SC_(32.955524444580078125), SC_(0.3082362115383148193359375), SC_(BOOST_MATH_SMALL_CONSTANT(0.1033057257566332368173504705063859426151e-251301)), SC_(0.603837473523874333668117011509794982175e-152), SC_(BOOST_MATH_SMALL_CONSTANT(0.1710820051524158477748585452714475118872e-251149)), SC_(1.0) }}, 
      {{ SC_(499024.09375), SC_(0.3617647588253021240234375), SC_(0.221111953258514404296875), SC_(BOOST_MATH_SMALL_CONSTANT(0.1198688160746637782814955712359261486421e-327059)), SC_(0.0213597393543653960374826366383814420494), SC_(BOOST_MATH_SMALL_CONSTANT(0.5611904437877215617130419280865220458283e-327058)), SC_(1.0) }}
   } };


