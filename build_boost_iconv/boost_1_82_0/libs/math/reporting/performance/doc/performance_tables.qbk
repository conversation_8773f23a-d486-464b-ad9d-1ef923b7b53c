

[/tables:]
[template table_Distribution_performance_comparison_for_different_performance_options_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[table:table_Distribution_performance_comparison_for_different_performance_options_with_Intel_C_C_0x_mode_version_1910_on_linux Distribution performance comparison for different performance options with Intel C++ C++0x mode version 1910 on linux
[[Function][boost 1.73][Boost[br]promote_double<false>][Boost[br]promote_double<false>[br]digits10<10>][Boost[br]float[br]promote_float<false>]]
[[ArcSine (CDF)][[role green 1.07[br](30ns)]][[role green 1.07[br](30ns)]][[role green 1.07[br](30ns)]][[role green 1.00[br](28ns)]]]
[[ArcSine (PDF)][[role green 1.07[br](16ns)]][[role green 1.00[br](15ns)]][[role green 1.00[br](15ns)]][[role green 1.00[br](15ns)]]]
[[ArcSine (quantile)][[role green 1.00[br](31ns)]][[role green 1.00[br](31ns)]][[role green 1.03[br](32ns)]][[role green 1.00[br](31ns)]]]
[[Beta (CDF)][[role red 3.75[br](300ns)]][[role blue 1.46[br](117ns)]][[role blue 1.40[br](112ns)]][[role green 1.00[br](80ns)]]]
[[Beta (PDF)][[role red 3.64[br](255ns)]][[role green 1.09[br](76ns)]][[role green 1.10[br](77ns)]][[role green 1.00[br](70ns)]]]
[[Beta (quantile)][[role red 3.53[br](1693ns)]][[role blue 1.45[br](694ns)]][[role green 1.19[br](571ns)]][[role green 1.00[br](480ns)]]]
[[Binomial (CDF)][[role red 3.88[br](656ns)]][[role blue 1.49[br](252ns)]][[role blue 1.27[br](215ns)]][[role green 1.00[br](169ns)]]]
[[Binomial (PDF)][[role red 3.37[br](290ns)]][[role green 1.03[br](89ns)]][[role green 1.03[br](89ns)]][[role green 1.00[br](86ns)]]]
[[Binomial (quantile)][[role red 3.30[br](2982ns)]][[role blue 1.43[br](1290ns)]][[role blue 1.24[br](1126ns)]][[role green 1.00[br](905ns)]]]
[[Cauchy (CDF)][[role green 1.04[br](25ns)]][[role green 1.04[br](25ns)]][[role green 1.00[br](24ns)]][[role green 1.04[br](25ns)]]]
[[Cauchy (PDF)][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role green 1.11[br](10ns)]]]
[[Cauchy (quantile)][[role green 1.00[br](39ns)]][[role green 1.00[br](39ns)]][[role green 1.00[br](39ns)]][[role green 1.00[br](39ns)]]]
[[ChiSquared (CDF)][[role red 3.09[br](918ns)]][[role blue 1.62[br](482ns)]][[role blue 1.29[br](383ns)]][[role green 1.00[br](297ns)]]]
[[ChiSquared (PDF)][[role red 3.43[br](206ns)]][[role green 1.08[br](65ns)]][[role green 1.05[br](63ns)]][[role green 1.00[br](60ns)]]]
[[ChiSquared (quantile)][[role red 3.10[br](1400ns)]][[role blue 1.69[br](764ns)]][[role blue 1.26[br](570ns)]][[role green 1.00[br](451ns)]]]
[[Exponential (CDF)][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]]]
[[Exponential (PDF)][[role green 1.14[br](24ns)]][[role green 1.10[br](23ns)]][[role green 1.10[br](23ns)]][[role green 1.00[br](21ns)]]]
[[Exponential (quantile)][[role green 1.04[br](27ns)]][[role green 1.04[br](27ns)]][[role green 1.04[br](27ns)]][[role green 1.00[br](26ns)]]]
[[ExtremeValue (CDF)][[role green 1.00[br](37ns)]][[role green 1.00[br](37ns)]][[role green 1.00[br](37ns)]][[role green 1.03[br](38ns)]]]
[[ExtremeValue (PDF)][[role green 1.00[br](38ns)]][[role green 1.03[br](39ns)]][[role green 1.00[br](38ns)]][[role green 1.03[br](39ns)]]]
[[ExtremeValue (quantile)][[role green 1.02[br](41ns)]][[role green 1.05[br](42ns)]][[role green 1.02[br](41ns)]][[role green 1.00[br](40ns)]]]
[[F (CDF)][[role red 3.93[br](590ns)]][[role blue 1.56[br](234ns)]][[role blue 1.28[br](192ns)]][[role green 1.00[br](150ns)]]]
[[F (PDF)][[role red 3.38[br](304ns)]][[role green 1.07[br](96ns)]][[role green 1.07[br](96ns)]][[role green 1.00[br](90ns)]]]
[[F (quantile)][[role red 2.67[br](1978ns)]][[role green 1.15[br](848ns)]][[role green 1.00[br](740ns)]][[role green 1.03[br](761ns)]]]
[[Gamma (CDF)][[role red 2.66[br](473ns)]][[role blue 1.31[br](234ns)]][[role green 1.16[br](207ns)]][[role green 1.00[br](178ns)]]]
[[Gamma (PDF)][[role red 3.45[br](245ns)]][[role green 1.20[br](85ns)]][[role green 1.17[br](83ns)]][[role green 1.00[br](71ns)]]]
[[Gamma (quantile)][[role red 3.10[br](1166ns)]][[role blue 1.61[br](605ns)]][[role green 1.10[br](413ns)]][[role green 1.00[br](376ns)]]]
[[Geometric (CDF)][[role green 1.00[br](24ns)]][[role green 1.00[br](24ns)]][[role green 1.00[br](24ns)]][[role green 1.00[br](24ns)]]]
[[Geometric (PDF)][[role green 1.18[br](20ns)]][[role green 1.18[br](20ns)]][[role green 1.12[br](19ns)]][[role green 1.00[br](17ns)]]]
[[Geometric (quantile)][[role green 1.00[br](25ns)]][[role green 1.04[br](26ns)]][[role green 1.00[br](25ns)]][[role green 1.08[br](27ns)]]]
[[Hypergeometric (CDF)][[role green 1.11[br](48090ns)]][[role green 1.00[br](43638ns)]][[role green 1.00[br](43443ns)]][[role green 1.12[br](48749ns)]]]
[[Hypergeometric (PDF)][[role green 1.11[br](50909ns)]][[role green 1.00[br](45698ns)]][[role green 1.00[br](45877ns)]][[role blue 1.21[br](55175ns)]]]
[[Hypergeometric (quantile)][[role green 1.00[br](85300ns)]][[role blue 1.23[br](105218ns)]][[role blue 1.24[br](106075ns)]][[role blue 1.32[br](112724ns)]]]
[[InverseChiSquared (CDF)][[role red 3.39[br](1123ns)]][[role blue 1.68[br](557ns)]][[role blue 1.31[br](435ns)]][[role green 1.00[br](331ns)]]]
[[InverseChiSquared (PDF)][[role red 3.17[br](203ns)]][[role green 1.11[br](71ns)]][[role green 1.08[br](69ns)]][[role green 1.00[br](64ns)]]]
[[InverseChiSquared (quantile)][[role red 3.09[br](1509ns)]][[role blue 1.66[br](810ns)]][[role blue 1.27[br](620ns)]][[role green 1.00[br](488ns)]]]
[[InverseGamma (CDF)][[role red 2.66[br](477ns)]][[role blue 1.30[br](233ns)]][[role green 1.15[br](205ns)]][[role green 1.00[br](179ns)]]]
[[InverseGamma (PDF)][[role red 3.44[br](248ns)]][[role green 1.19[br](86ns)]][[role green 1.17[br](84ns)]][[role green 1.00[br](72ns)]]]
[[InverseGamma (quantile)][[role red 3.13[br](1153ns)]][[role blue 1.63[br](599ns)]][[role green 1.12[br](413ns)]][[role green 1.00[br](368ns)]]]
[[InverseGaussian (CDF)][[role green 1.00[br](140ns)]][[role green 1.00[br](140ns)]][[role green 1.00[br](140ns)]][[role grey -]]]
[[InverseGaussian (PDF)][[role blue 1.70[br](51ns)]][[role blue 1.70[br](51ns)]][[role blue 1.77[br](53ns)]][[role green 1.00[br](30ns)]]]
[[InverseGaussian (quantile)][[role blue 1.52[br](1712ns)]][[role blue 1.52[br](1712ns)]][[role blue 1.40[br](1583ns)]][[role green 1.00[br](1127ns)]]]
[[Laplace (CDF)][[role green 1.00[br](26ns)]][[role green 1.00[br](26ns)]][[role green 1.00[br](26ns)]][[role green 1.00[br](26ns)]]]
[[Laplace (PDF)][[role green 1.00[br](25ns)]][[role green 1.00[br](25ns)]][[role green 1.00[br](25ns)]][[role green 1.04[br](26ns)]]]
[[Laplace (quantile)][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role green 1.04[br](28ns)]]]
[[LogNormal (CDF)][[role blue 1.26[br](97ns)]][[role green 1.09[br](84ns)]][[role green 1.09[br](84ns)]][[role green 1.00[br](77ns)]]]
[[LogNormal (PDF)][[role green 1.02[br](41ns)]][[role green 1.02[br](41ns)]][[role green 1.00[br](40ns)]][[role green 1.02[br](41ns)]]]
[[LogNormal (quantile)][[role blue 1.34[br](75ns)]][[role green 1.00[br](56ns)]][[role green 1.00[br](56ns)]][[role green 1.00[br](56ns)]]]
[[Logistic (CDF)][[role green 1.00[br](25ns)]][[role green 1.00[br](25ns)]][[role green 1.00[br](25ns)]][[role green 1.04[br](26ns)]]]
[[Logistic (PDF)][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]]]
[[Logistic (quantile)][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]]]
[[NegativeBinomial (CDF)][[role red 4.47[br](970ns)]][[role blue 1.71[br](372ns)]][[role blue 1.36[br](295ns)]][[role green 1.00[br](217ns)]]]
[[NegativeBinomial (PDF)][[role red 3.61[br](321ns)]][[role green 1.04[br](93ns)]][[role green 1.04[br](93ns)]][[role green 1.00[br](89ns)]]]
[[NegativeBinomial (quantile)][[role red 2.94[br](5428ns)]][[role blue 1.20[br](2221ns)]][[role green 1.00[br](1845ns)]][[role green 1.16[br](2133ns)]]]
[[NonCentralBeta (CDF)][[role red 3.84[br](1432ns)]][[role blue 1.65[br](616ns)]][[role blue 1.47[br](550ns)]][[role green 1.00[br](373ns)]]]
[[NonCentralBeta (PDF)][[role red 3.56[br](946ns)]][[role blue 1.44[br](384ns)]][[role blue 1.28[br](340ns)]][[role green 1.00[br](266ns)]]]
[[NonCentralBeta (quantile)][[role red 6.38[br](37988ns)]][[role red 2.60[br](15507ns)]][[role red 2.32[br](13842ns)]][[role green 1.00[br](5955ns)]]]
[[NonCentralChiSquared (CDF)][[role red 3.18[br](3469ns)]][[role blue 2.00[br](2180ns)]][[role blue 1.63[br](1778ns)]][[role green 1.00[br](1090ns)]]]
[[NonCentralChiSquared (PDF)][[role red 2.62[br](601ns)]][[role blue 1.48[br](338ns)]][[role blue 1.46[br](335ns)]][[role green 1.00[br](229ns)]]]
[[NonCentralChiSquared (quantile)][[role red 4.68[br](25443ns)]][[role red 2.61[br](14172ns)]][[role blue 1.61[br](8761ns)]][[role green 1.00[br](5437ns)]]]
[[NonCentralF (CDF)][[role red 3.51[br](1254ns)]][[role blue 1.44[br](513ns)]][[role blue 1.24[br](442ns)]][[role green 1.00[br](357ns)]]]
[[NonCentralF (PDF)][[role red 3.42[br](1006ns)]][[role blue 1.33[br](390ns)]][[role blue 1.20[br](354ns)]][[role green 1.00[br](294ns)]]]
[[NonCentralF (quantile)][[role red 3.96[br](19355ns)]][[role blue 1.56[br](7609ns)]][[role blue 1.28[br](6255ns)]][[role green 1.00[br](4885ns)]]]
[[NonCentralT (CDF)][[role red 3.88[br](4765ns)]][[role blue 1.74[br](2131ns)]][[role blue 1.50[br](1843ns)]][[role green 1.00[br](1227ns)]]]
[[NonCentralT (PDF)][[role red 3.25[br](2983ns)]][[role blue 1.69[br](1549ns)]][[role blue 1.48[br](1355ns)]][[role green 1.00[br](917ns)]]]
[[NonCentralT (quantile)][[role red 5.19[br](50843ns)]][[role red 2.13[br](20840ns)]][[role blue 1.62[br](15907ns)]][[role green 1.00[br](9802ns)]]]
[[Normal (CDF)][[role blue 1.43[br](76ns)]][[role green 1.15[br](61ns)]][[role green 1.15[br](61ns)]][[role green 1.00[br](53ns)]]]
[[Normal (PDF)][[role green 1.00[br](24ns)]][[role green 1.04[br](25ns)]][[role green 1.04[br](25ns)]][[role green 1.04[br](25ns)]]]
[[Normal (quantile)][[role blue 1.55[br](51ns)]][[role green 1.00[br](33ns)]][[role green 1.03[br](34ns)]][[role green 1.00[br](33ns)]]]
[[Pareto (CDF)][[role green 1.05[br](39ns)]][[role green 1.05[br](39ns)]][[role green 1.08[br](40ns)]][[role green 1.00[br](37ns)]]]
[[Pareto (PDF)][[role green 1.03[br](67ns)]][[role green 1.05[br](68ns)]][[role green 1.05[br](68ns)]][[role green 1.00[br](65ns)]]]
[[Pareto (quantile)][[role green 1.00[br](35ns)]][[role green 1.00[br](35ns)]][[role green 1.00[br](35ns)]][[role green 1.00[br](35ns)]]]
[[Poisson (CDF)][[role red 2.52[br](189ns)]][[role blue 1.21[br](91ns)]][[role green 1.09[br](82ns)]][[role green 1.00[br](75ns)]]]
[[Poisson (PDF)][[role red 2.92[br](140ns)]][[role green 1.04[br](50ns)]][[role green 1.02[br](49ns)]][[role green 1.00[br](48ns)]]]
[[Poisson (quantile)][[role red 2.16[br](854ns)]][[role green 1.08[br](428ns)]][[role green 1.01[br](401ns)]][[role green 1.00[br](396ns)]]]
[[Rayleigh (CDF)][[role blue 1.26[br](24ns)]][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]]]
[[Rayleigh (PDF)][[role green 1.09[br](24ns)]][[role green 1.14[br](25ns)]][[role green 1.05[br](23ns)]][[role green 1.00[br](22ns)]]]
[[Rayleigh (quantile)][[role green 1.04[br](27ns)]][[role green 1.04[br](27ns)]][[role green 1.04[br](27ns)]][[role green 1.00[br](26ns)]]]
[[SkewNormal (CDF)][[role blue 1.37[br](368ns)]][[role blue 1.32[br](354ns)]][[role blue 1.32[br](354ns)]][[role green 1.00[br](269ns)]]]
[[SkewNormal (PDF)][[role green 1.13[br](88ns)]][[role green 1.00[br](78ns)]][[role green 1.00[br](78ns)]][[role green 1.00[br](78ns)]]]
[[SkewNormal (quantile)][[role blue 1.88[br](3270ns)]][[role blue 1.77[br](3093ns)]][[role blue 1.36[br](2377ns)]][[role green 1.00[br](1744ns)]]]
[[StudentsT (CDF)][[role red 3.90[br](1002ns)]][[role red 2.10[br](540ns)]][[role red 2.12[br](545ns)]][[role green 1.00[br](257ns)]]]
[[StudentsT (PDF)][[role red 2.81[br](278ns)]][[role green 1.08[br](107ns)]][[role green 1.07[br](106ns)]][[role green 1.00[br](99ns)]]]
[[StudentsT (quantile)][[role red 3.10[br](1506ns)]][[role blue 1.65[br](802ns)]][[role blue 1.64[br](797ns)]][[role green 1.00[br](486ns)]]]
[[Weibull (CDF)][[role blue 1.30[br](52ns)]][[role green 1.15[br](46ns)]][[role green 1.15[br](46ns)]][[role green 1.00[br](40ns)]]]
[[Weibull (PDF)][[role blue 1.25[br](71ns)]][[role blue 1.25[br](71ns)]][[role blue 1.25[br](71ns)]][[role green 1.00[br](57ns)]]]
[[Weibull (quantile)][[role green 1.02[br](49ns)]][[role green 1.02[br](49ns)]][[role green 1.02[br](49ns)]][[role green 1.00[br](48ns)]]]
]
]

[template table_Distribution_performance_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[table:table_Distribution_performance_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux Distribution performance comparison with Intel C++ C++0x mode version 1910 on linux
[[Function][boost 1.73][Boost[br]promote_double<false>][Boost[br]float[br]promote_float<false>][GSL][Rmath 3.6.3][DCDFLIB]]
[[ArcSine (CDF)][[role green 1.00[br](30ns)]][[role green 1.00[br](30ns)]][][][][]]
[[ArcSine (PDF)][[role green 1.07[br](16ns)]][[role green 1.00[br](15ns)]][][][][]]
[[ArcSine (quantile)][[role green 1.00[br](31ns)]][[role green 1.00[br](31ns)]][][][][]]
[[Beta (CDF)][[role red 2.56[br](300ns)]][[role green 1.00[br](117ns)]][[role grey -]][[role red 3.20[br](374ns)]][[role blue 1.79[br](209ns)]][[role blue 1.87[br](219ns)]]]
[[Beta (PDF)][[role red 3.36[br](255ns)]][[role green 1.00[br](76ns)]][[role grey -]][[role grey -]][[role red 2.68[br](204ns)]][]]
[[Beta (quantile)][[role red 2.44[br](1693ns)]][[role green 1.00[br](694ns)]][[role grey -]][[role red 18.14[br](12591ns)]][[role red 2.14[br](1486ns)]][[role red 6.32[br](4384ns)]]]
[[Binomial (CDF)][[role red 2.60[br](656ns)]][[role green 1.00[br](252ns)]][[role grey -]][[role red 2.45[br](617ns)]][[role blue 1.56[br](392ns)]][[role blue 1.51[br](380ns)]]]
[[Binomial (PDF)][[role red 3.26[br](290ns)]][[role green 1.00[br](89ns)]][[role grey -]][[role grey -]][[role blue 1.98[br](176ns)]][]]
[[Binomial (quantile)][[role red 2.90[br](2982ns)]][[role blue 1.26[br](1290ns)]][[role grey -]][[role grey -]][[role green 1.00[br](1027ns)]][[role red 6.06[br](6224ns)]]]
[[Cauchy (CDF)][[role green 1.00[br](25ns)]][[role green 1.00[br](25ns)]][[role grey -]][[role blue 1.32[br](33ns)]][[role blue 1.40[br](35ns)]][]]
[[Cauchy (PDF)][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role grey -]][[role grey -]][[role blue 1.44[br](13ns)]][]]
[[Cauchy (quantile)][[role green 1.00[br](39ns)]][[role green 1.00[br](39ns)]][[role grey -]][[role green 1.00[br](39ns)]][[role blue 1.62[br](63ns)]][]]
[[ChiSquared (CDF)][[role red 8.50[br](918ns)]][[role red 4.46[br](482ns)]][[role grey -]][[role red 12.22[br](1320ns)]][[role blue 1.71[br](185ns)]][[role green 1.00[br](108ns)]]]
[[ChiSquared (PDF)][[role red 3.17[br](206ns)]][[role green 1.00[br](65ns)]][[role grey -]][[role grey -]][[role blue 1.25[br](81ns)]][]]
[[ChiSquared (quantile)][[role blue 1.83[br](1400ns)]][[role green 1.00[br](764ns)]][[role grey -]][[role red 17.14[br](13096ns)]][[role blue 1.45[br](1108ns)]][[role red 3.61[br](2757ns)]]]
[[Exponential (CDF)][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]][[role grey -]][[role blue 1.79[br](34ns)]][[role blue 1.84[br](35ns)]][]]
[[Exponential (PDF)][[role green 1.04[br](24ns)]][[role green 1.00[br](23ns)]][[role grey -]][[role grey -]][[role blue 1.48[br](34ns)]][]]
[[Exponential (quantile)][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role grey -]][[role blue 1.30[br](35ns)]][[role blue 1.37[br](37ns)]][]]
[[ExtremeValue (CDF)][[role green 1.00[br](37ns)]][[role green 1.00[br](37ns)]][][][][]]
[[ExtremeValue (PDF)][[role green 1.00[br](38ns)]][[role green 1.03[br](39ns)]][][][][]]
[[ExtremeValue (quantile)][[role green 1.00[br](41ns)]][[role green 1.02[br](42ns)]][][][][]]
[[F (CDF)][[role red 2.52[br](590ns)]][[role green 1.00[br](234ns)]][[role grey -]][[role red 2.61[br](610ns)]][[role blue 1.41[br](330ns)]][[role blue 1.26[br](295ns)]]]
[[F (PDF)][[role red 3.17[br](304ns)]][[role green 1.00[br](96ns)]][[role grey -]][[role grey -]][[role blue 1.36[br](131ns)]][]]
[[F (quantile)][[role red 2.33[br](1978ns)]][[role green 1.00[br](848ns)]][[role grey -]][[role red 13.00[br](11024ns)]][[role red 2.41[br](2045ns)]][[role red 5.26[br](4461ns)]]]
[[Gamma (CDF)][[role red 4.19[br](473ns)]][[role red 2.07[br](234ns)]][[role grey -]][[role red 5.12[br](578ns)]][[role blue 1.76[br](199ns)]][[role green 1.00[br](113ns)]]]
[[Gamma (PDF)][[role red 2.88[br](245ns)]][[role green 1.00[br](85ns)]][[role grey -]][[role grey -]][[role blue 1.31[br](111ns)]][]]
[[Gamma (quantile)][[role red 2.86[br](1166ns)]][[role blue 1.49[br](605ns)]][[role grey -]][[role red 13.75[br](5597ns)]][[role red 2.92[br](1187ns)]][[role green 1.00[br](407ns)]]]
[[Geometric (CDF)][[role green 1.00[br](24ns)]][[role green 1.00[br](24ns)]][[role grey -]][[role blue 1.96[br](47ns)]][[role red 2.50[br](60ns)]][]]
[[Geometric (PDF)][[role green 1.00[br](20ns)]][[role green 1.00[br](20ns)]][[role grey -]][[role grey -]][[role red 14.95[br](299ns)]][]]
[[Geometric (quantile)][[role green 1.00[br](25ns)]][[role green 1.04[br](26ns)]][[role grey -]][[role grey -]][[role red 2.88[br](72ns)]][]]
[[Hypergeometric (CDF)][[role red 82.35[br](48090ns)]][[role red 74.72[br](43638ns)]][[role grey -]][[role green 1.00[br](584ns)]][[role green 1.07[br](623ns)]][]]
[[Hypergeometric (PDF)][[role red 115.97[br](50909ns)]][[role red 104.10[br](45698ns)]][[role grey -]][[role grey -]][[role green 1.00[br](439ns)]][]]
[[Hypergeometric (quantile)][[role green 1.00[br](85300ns)]][[role blue 1.23[br](105218ns)]][[role grey -]][[role grey -]][[role blue 1.50[br](128199ns)]][]]
[[InverseChiSquared (CDF)][[role red 2.02[br](1123ns)]][[role green 1.00[br](557ns)]][][][][]]
[[InverseChiSquared (PDF)][[role red 2.86[br](203ns)]][[role green 1.00[br](71ns)]][][][][]]
[[InverseChiSquared (quantile)][[role blue 1.86[br](1509ns)]][[role green 1.00[br](810ns)]][][][][]]
[[InverseGamma (CDF)][[role red 2.05[br](477ns)]][[role green 1.00[br](233ns)]][][][][]]
[[InverseGamma (PDF)][[role red 2.88[br](248ns)]][[role green 1.00[br](86ns)]][][][][]]
[[InverseGamma (quantile)][[role blue 1.92[br](1153ns)]][[role green 1.00[br](599ns)]][][][][]]
[[InverseGaussian (CDF)][[role red inf[br](140ns)]][[role red inf[br](140ns)]][[role green -nan[br](0ns)]][][][]]
[[InverseGaussian (PDF)][[role green 1.00[br](51ns)]][[role green 1.00[br](51ns)]][][][][]]
[[InverseGaussian (quantile)][[role green 1.00[br](1712ns)]][[role green 1.00[br](1712ns)]][][][][]]
[[Laplace (CDF)][[role green 1.00[br](26ns)]][[role green 1.00[br](26ns)]][[role grey -]][[role blue 1.81[br](47ns)]][][]]
[[Laplace (PDF)][[role green 1.00[br](25ns)]][[role green 1.00[br](25ns)]][[role grey -]][][][]]
[[Laplace (quantile)][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role grey -]][[role blue 1.37[br](37ns)]][][]]
[[LogNormal (CDF)][[role green 1.20[br](97ns)]][[role green 1.04[br](84ns)]][[role grey -]][[role green 1.00[br](81ns)]][[role blue 1.23[br](100ns)]][]]
[[LogNormal (PDF)][[role green 1.00[br](41ns)]][[role green 1.00[br](41ns)]][[role grey -]][[role grey -]][[role blue 1.41[br](58ns)]][]]
[[LogNormal (quantile)][[role blue 1.34[br](75ns)]][[role green 1.00[br](56ns)]][[role grey -]][[role green 1.12[br](63ns)]][[role green 1.16[br](65ns)]][]]
[[Logistic (CDF)][[role green 1.00[br](25ns)]][[role green 1.00[br](25ns)]][[role grey -]][[role red 2.96[br](74ns)]][[role blue 1.40[br](35ns)]][]]
[[Logistic (PDF)][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role grey -]][[role grey -]][[role blue 1.30[br](35ns)]][]]
[[Logistic (quantile)][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role grey -]][[role blue 1.33[br](36ns)]][[role blue 1.44[br](39ns)]][]]
[[NegativeBinomial (CDF)][[role red 2.61[br](970ns)]][[role green 1.00[br](372ns)]][[role grey -]][[role red 2.23[br](828ns)]][[role green 1.07[br](397ns)]][[role green 1.15[br](426ns)]]]
[[NegativeBinomial (PDF)][[role red 3.45[br](321ns)]][[role green 1.00[br](93ns)]][[role grey -]][[role grey -]][[role blue 1.73[br](161ns)]][]]
[[NegativeBinomial (quantile)][[role red 2.44[br](5428ns)]][[role green 1.00[br](2221ns)]][[role grey -]][[role grey -]][[role red 3.02[br](6698ns)]][[role red 3.72[br](8260ns)]]]
[[NonCentralBeta (CDF)][[role red 2.32[br](1432ns)]][[role green 1.00[br](616ns)]][[role grey -]][[role grey -]][[role blue 1.28[br](789ns)]][]]
[[NonCentralBeta (PDF)][[role red 2.46[br](946ns)]][[role green 1.00[br](384ns)]][[role grey -]][[role grey -]][[role blue 1.25[br](481ns)]][]]
[[NonCentralBeta (quantile)][[role red 2.45[br](37988ns)]][[role green 1.00[br](15507ns)]][[role grey -]][[role grey -]][[role red 3.64[br](56520ns)]][]]
[[NonCentralChiSquared (CDF)][[role red 10.58[br](3469ns)]][[role red 6.65[br](2180ns)]][[role grey -]][[role grey -]][[role red 21.05[br](6906ns)]][[role green 1.00[br](328ns)]]]
[[NonCentralChiSquared (PDF)][[role red 2.55[br](601ns)]][[role blue 1.43[br](338ns)]][[role grey -]][[role grey -]][[role green 1.00[br](236ns)]][]]
[[NonCentralChiSquared (quantile)][[role red 3.19[br](25443ns)]][[role blue 1.78[br](14172ns)]][[role grey -]][[role grey -]][[role red 42.30[br](337057ns)]][[role green 1.00[br](7968ns)]]]
[[NonCentralF (CDF)][[role red 2.44[br](1254ns)]][[role green 1.00[br](513ns)]][[role grey -]][[role grey -]][[role blue 1.66[br](850ns)]][[role blue 1.40[br](716ns)]]]
[[NonCentralF (PDF)][[role red 2.58[br](1006ns)]][[role green 1.00[br](390ns)]][[role grey -]][[role grey -]][[role green 1.14[br](445ns)]][]]
[[NonCentralF (quantile)][[role red 2.54[br](19355ns)]][[role green 1.00[br](7609ns)]][[role grey -]][[role grey -]][[role red 5.88[br](44766ns)]][[role green 1.14[br](8684ns)]]]
[[NonCentralT (CDF)][[role red 4.19[br](4765ns)]][[role blue 1.87[br](2131ns)]][[role grey -]][[role grey -]][[role green 1.00[br](1137ns)]][]]
[[NonCentralT (PDF)][[role blue 1.93[br](2983ns)]][[role green 1.00[br](1549ns)]][[role grey -]][[role grey -]][[role blue 1.53[br](2366ns)]][]]
[[NonCentralT (quantile)][[role red 2.44[br](50843ns)]][[role green 1.00[br](20840ns)]][[role grey -]][[role grey -]][[role red 2.57[br](53465ns)]][]]
[[Normal (CDF)][[role blue 1.77[br](76ns)]][[role blue 1.42[br](61ns)]][[role grey -]][[role green 1.00[br](43ns)]][[role blue 1.49[br](64ns)]][[role red 2.47[br](106ns)]]]
[[Normal (PDF)][[role green 1.00[br](24ns)]][[role green 1.04[br](25ns)]][[role grey -]][[role grey -]][[role blue 1.50[br](36ns)]][]]
[[Normal (quantile)][[role blue 1.55[br](51ns)]][[role green 1.00[br](33ns)]][[role grey -]][[role blue 1.27[br](42ns)]][[role green 1.03[br](34ns)]][[role red 9.21[br](304ns)]]]
[[Pareto (CDF)][[role green 1.00[br](39ns)]][[role green 1.00[br](39ns)]][[role grey -]][[role green 1.03[br](40ns)]][][]]
[[Pareto (PDF)][[role green 1.00[br](67ns)]][[role green 1.01[br](68ns)]][[role grey -]][][][]]
[[Pareto (quantile)][[role green 1.00[br](35ns)]][[role green 1.00[br](35ns)]][[role grey -]][[role blue 1.69[br](59ns)]][][]]
[[Poisson (CDF)][[role red 2.08[br](189ns)]][[role green 1.00[br](91ns)]][[role grey -]][[role red 3.37[br](307ns)]][[role red 2.03[br](185ns)]][[role green 1.10[br](100ns)]]]
[[Poisson (PDF)][[role red 2.80[br](140ns)]][[role green 1.00[br](50ns)]][[role grey -]][[role grey -]][[role blue 1.66[br](83ns)]][]]
[[Poisson (quantile)][[role blue 2.00[br](854ns)]][[role green 1.00[br](428ns)]][[role grey -]][[role grey -]][[role green 1.04[br](447ns)]][[role red 3.60[br](1539ns)]]]
[[Rayleigh (CDF)][[role blue 1.26[br](24ns)]][[role green 1.00[br](19ns)]][[role grey -]][[role blue 1.79[br](34ns)]][][]]
[[Rayleigh (PDF)][[role green 1.00[br](24ns)]][[role green 1.04[br](25ns)]][[role grey -]][][][]]
[[Rayleigh (quantile)][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role grey -]][[role blue 1.33[br](36ns)]][][]]
[[SkewNormal (CDF)][[role green 1.04[br](368ns)]][[role green 1.00[br](354ns)]][[role grey -]][][][]]
[[SkewNormal (PDF)][[role green 1.13[br](88ns)]][[role green 1.00[br](78ns)]][[role grey -]][][][]]
[[SkewNormal (quantile)][[role green 1.06[br](3270ns)]][[role green 1.00[br](3093ns)]][[role grey -]][][][]]
[[StudentsT (CDF)][[role red 4.62[br](1002ns)]][[role red 2.49[br](540ns)]][[role grey -]][[role green 1.00[br](217ns)]][[role green 1.15[br](249ns)]][[role green 1.00[br](218ns)]]]
[[StudentsT (PDF)][[role red 2.60[br](278ns)]][[role green 1.00[br](107ns)]][[role grey -]][[role grey -]][[role green 1.01[br](108ns)]][]]
[[StudentsT (quantile)][[role red 2.26[br](1506ns)]][[role blue 1.20[br](802ns)]][[role grey -]][[role blue 1.21[br](806ns)]][[role green 1.00[br](666ns)]][[role red 2.60[br](1732ns)]]]
[[Weibull (CDF)][[role green 1.13[br](52ns)]][[role green 1.00[br](46ns)]][[role grey -]][[role blue 1.35[br](62ns)]][[role blue 1.43[br](66ns)]][]]
[[Weibull (PDF)][[role green 1.06[br](71ns)]][[role green 1.06[br](71ns)]][[role grey -]][[role grey -]][[role green 1.00[br](67ns)]][]]
[[Weibull (quantile)][[role green 1.00[br](49ns)]][[role green 1.00[br](49ns)]][[role grey -]][[role blue 1.27[br](62ns)]][[role blue 1.31[br](64ns)]][]]
]
]

[template table_gcd_method_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[table:table_gcd_method_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux gcd method comparison with Intel C++ C++0x mode version 1910 on linux
[[Function][gcd
boost 1.73][Euclid_gcd
boost 1.73][Stein_gcd
boost 1.73][mixed_binary_gcd
boost 1.73][Stein_gcd_textbook
boost 1.73][gcd_euclid_textbook
boost 1.73]]
[[gcd<boost::multiprecision::uint1024_t> (Trivial cases)][[role green 1.00[br](1149ns)]][[role green 1.02[br](1170ns)]][[role red 5.93[br](6814ns)]][[role red 5.74[br](6595ns)]][[role blue 1.78[br](2049ns)]][[role green 1.05[br](1201ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (adjacent Fibonacci numbers)][[role green 1.00[br](19340886ns)]][[role red 4.29[br](82975969ns)]][[role red 4.36[br](84393719ns)]][[role red 2.77[br](53544360ns)]][[role blue 1.84[br](35544584ns)]][[role red 4.93[br](95303528ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (permutations of Fibonacci numbers)][[role red 5.53[br](9790433775ns)]][[role green 1.00[br](1771993224ns)]][[role red 28.62[br](50708130603ns)]][[role red 4.28[br](7580487204ns)]][[role red 11.99[br](21242571235ns)]][[role green 1.16[br](2061299022ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (random prime number products)][[role green 1.00[br](1131208ns)]][[role blue 1.42[br](1607205ns)]][[role red 13.59[br](15368502ns)]][[role red 2.82[br](3190601ns)]][[role red 5.84[br](6602226ns)]][[role blue 1.77[br](2001345ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (uniform random numbers)][[role green 1.00[br](77812152ns)]][[role blue 1.50[br](116798160ns)]][[role red 3.59[br](278980771ns)]][[role red 2.21[br](171989192ns)]][[role blue 1.56[br](121454143ns)]][[role blue 1.79[br](139613330ns)]]]
[[gcd<boost::multiprecision::uint256_t> (Trivial cases)][[role green 1.01[br](996ns)]][[role green 1.00[br](983ns)]][[role red 6.31[br](6207ns)]][[role red 5.97[br](5865ns)]][[role blue 1.92[br](1884ns)]][[role green 1.02[br](1003ns)]]]
[[gcd<boost::multiprecision::uint256_t> (adjacent Fibonacci numbers)][[role green 1.00[br](7415785ns)]][[role red 2.79[br](20685595ns)]][[role red 6.63[br](49202540ns)]][[role red 3.85[br](28549805ns)]][[role red 2.71[br](20117830ns)]][[role red 3.45[br](25574573ns)]]]
[[gcd<boost::multiprecision::uint256_t> (permutations of Fibonacci numbers)][[role green 1.00[br](4196653358ns)]][[role blue 1.55[br](6484167697ns)]][[role red 6.27[br](26312048010ns)]][[role red 2.81[br](11779198450ns)]][[role red 2.62[br](10988018887ns)]][[role blue 1.89[br](7921304962ns)]]]
[[gcd<boost::multiprecision::uint256_t> (random prime number products)][[role green 1.00[br](1210038ns)]][[role blue 1.28[br](1549419ns)]][[role red 12.82[br](15516807ns)]][[role red 2.61[br](3161310ns)]][[role red 5.50[br](6658681ns)]][[role blue 1.62[br](1954873ns)]]]
[[gcd<boost::multiprecision::uint256_t> (uniform random numbers)][[role green 1.00[br](10984189ns)]][[role blue 1.93[br](21227287ns)]][[role red 5.41[br](59471076ns)]][[role red 3.15[br](34637585ns)]][[role red 2.27[br](24972739ns)]][[role red 2.23[br](24506025ns)]]]
[[gcd<boost::multiprecision::uint512_t> (Trivial cases)][[role green 1.01[br](998ns)]][[role green 1.00[br](985ns)]][[role red 6.29[br](6194ns)]][[role red 6.06[br](5965ns)]][[role blue 1.89[br](1865ns)]][[role green 1.04[br](1025ns)]]]
[[gcd<boost::multiprecision::uint512_t> (adjacent Fibonacci numbers)][[role green 1.00[br](17627813ns)]][[role red 3.09[br](54458749ns)]][[role red 4.33[br](76324899ns)]][[role red 2.74[br](48302252ns)]][[role blue 1.79[br](31633363ns)]][[role red 3.55[br](62588873ns)]]]
[[gcd<boost::multiprecision::uint512_t> (permutations of Fibonacci numbers)][[role blue 1.23[br](8810971578ns)]][[role green 1.00[br](7145516420ns)]][[role red 6.22[br](44416637043ns)]][[role blue 1.84[br](13131566715ns)]][[role red 2.64[br](18893315261ns)]][[role green 1.15[br](8194931085ns)]]]
[[gcd<boost::multiprecision::uint512_t> (random prime number products)][[role green 1.00[br](1324762ns)]][[role blue 1.30[br](1723839ns)]][[role red 11.71[br](15516337ns)]][[role red 2.54[br](3362672ns)]][[role red 5.12[br](6787358ns)]][[role blue 1.54[br](2037419ns)]]]
[[gcd<boost::multiprecision::uint512_t> (uniform random numbers)][[role green 1.00[br](31350786ns)]][[role blue 1.67[br](52351105ns)]][[role red 3.98[br](124782505ns)]][[role red 2.75[br](86100180ns)]][[role blue 1.74[br](54544300ns)]][[role blue 1.93[br](60454350ns)]]]
[[gcd<unsigned long long> (Trivial cases)][[role blue 1.21[br](436ns)]][[role blue 1.69[br](610ns)]][[role green 1.00[br](361ns)]][[role blue 1.28[br](463ns)]][[role blue 1.22[br](440ns)]][[role green 1.06[br](383ns)]]]
[[gcd<unsigned long long> (adjacent Fibonacci numbers)][[role green 1.19[br](31612ns)]][[role red 6.47[br](172715ns)]][[role blue 1.46[br](38871ns)]][[role green 1.18[br](31388ns)]][[role green 1.00[br](26675ns)]][[role red 3.44[br](91801ns)]]]
[[gcd<unsigned long long> (permutations of Fibonacci numbers)][[role green 1.13[br](2001235ns)]][[role blue 1.81[br](3209638ns)]][[role red 2.28[br](4038421ns)]][[role green 1.18[br](2096669ns)]][[role red 2.00[br](3546898ns)]][[role green 1.00[br](1771175ns)]]]
[[gcd<unsigned long long> (random prime number products)][[role green 1.04[br](424068ns)]][[role red 2.08[br](846900ns)]][[role blue 1.55[br](629740ns)]][[role green 1.00[br](406335ns)]][[role blue 1.56[br](635145ns)]][[role green 1.12[br](453110ns)]]]
[[gcd<unsigned long long> (uniform random numbers)][[role green 1.00[br](741076ns)]][[role red 2.03[br](1503087ns)]][[role blue 1.53[br](1131004ns)]][[role green 1.06[br](788318ns)]][[role green 1.14[br](845368ns)]][[role green 1.14[br](846833ns)]]]
[[gcd<unsigned long> (Trivial cases)][[role blue 1.30[br](478ns)]][[role blue 1.88[br](691ns)]][[role green 1.00[br](367ns)]][[role blue 1.34[br](491ns)]][[role blue 1.22[br](446ns)]][[role green 1.02[br](373ns)]]]
[[gcd<unsigned long> (adjacent Fibonacci numbers)][[role green 1.03[br](30717ns)]][[role red 5.82[br](173201ns)]][[role blue 1.30[br](38671ns)]][[role green 1.06[br](31473ns)]][[role green 1.00[br](29769ns)]][[role red 3.05[br](90878ns)]]]
[[gcd<unsigned long> (permutations of Fibonacci numbers)][[role green 1.09[br](1911949ns)]][[role blue 1.70[br](2978431ns)]][[role red 2.08[br](3662873ns)]][[role green 1.15[br](2017722ns)]][[role red 2.07[br](3629813ns)]][[role green 1.00[br](1756842ns)]]]
[[gcd<unsigned long> (random prime number products)][[role green 1.06[br](423997ns)]][[role red 2.18[br](870271ns)]][[role blue 1.61[br](641684ns)]][[role green 1.00[br](399630ns)]][[role blue 1.52[br](608026ns)]][[role green 1.15[br](458994ns)]]]
[[gcd<unsigned long> (uniform random numbers)][[role green 1.00[br](742957ns)]][[role blue 1.98[br](1473392ns)]][[role blue 1.50[br](1111490ns)]][[role green 1.06[br](791049ns)]][[role green 1.17[br](868779ns)]][[role green 1.12[br](834281ns)]]]
[[gcd<unsigned short> (Trivial cases)][[role green 1.15[br](416ns)]][[role blue 1.58[br](575ns)]][[role green 1.02[br](372ns)]][[role blue 1.21[br](438ns)]][[role green 1.10[br](401ns)]][[role green 1.00[br](363ns)]]]
[[gcd<unsigned short> (adjacent Fibonacci numbers)][[role blue 1.43[br](2101ns)]][[role red 6.25[br](9163ns)]][[role blue 1.85[br](2712ns)]][[role blue 1.38[br](2019ns)]][[role green 1.00[br](1465ns)]][[role red 2.64[br](3871ns)]]]
[[gcd<unsigned short> (permutations of Fibonacci numbers)][[role green 1.08[br](31053ns)]][[role red 2.76[br](79355ns)]][[role blue 1.59[br](45830ns)]][[role green 1.00[br](28777ns)]][[role green 1.17[br](33794ns)]][[role green 1.06[br](30467ns)]]]
[[gcd<unsigned short> (random prime number products)][[role green 1.18[br](129652ns)]][[role red 2.37[br](260708ns)]][[role blue 1.72[br](189203ns)]][[role green 1.06[br](116103ns)]][[role blue 1.70[br](187056ns)]][[role green 1.00[br](109794ns)]]]
[[gcd<unsigned short> (uniform random numbers)][[role green 1.14[br](194559ns)]][[role red 2.31[br](393170ns)]][[role blue 1.75[br](297338ns)]][[role green 1.03[br](175631ns)]][[role blue 1.42[br](241167ns)]][[role green 1.00[br](170302ns)]]]
[[gcd<unsigned> (Trivial cases)][[role green 1.20[br](420ns)]][[role blue 1.57[br](550ns)]][[role green 1.04[br](365ns)]][[role blue 1.21[br](426ns)]][[role green 1.20[br](421ns)]][[role green 1.00[br](351ns)]]]
[[gcd<unsigned> (adjacent Fibonacci numbers)][[role green 1.11[br](6840ns)]][[role red 5.49[br](33983ns)]][[role blue 1.63[br](10071ns)]][[role green 1.09[br](6726ns)]][[role green 1.00[br](6186ns)]][[role red 2.68[br](16573ns)]]]
[[gcd<unsigned> (permutations of Fibonacci numbers)][[role green 1.13[br](251730ns)]][[role red 2.09[br](464874ns)]][[role red 2.01[br](448111ns)]][[role green 1.06[br](236437ns)]][[role blue 1.93[br](429052ns)]][[role green 1.00[br](222499ns)]]]
[[gcd<unsigned> (random prime number products)][[role green 1.00[br](219029ns)]][[role red 2.02[br](442534ns)]][[role blue 1.68[br](367430ns)]][[role green 1.01[br](220569ns)]][[role blue 1.62[br](354825ns)]][[role green 1.05[br](229989ns)]]]
[[gcd<unsigned> (uniform random numbers)][[role green 1.01[br](339532ns)]][[role blue 1.87[br](628141ns)]][[role blue 1.52[br](512196ns)]][[role green 1.00[br](336399ns)]][[role blue 1.34[br](449918ns)]][[role green 1.04[br](348735ns)]]]
]
]

[template table_Polynomial_Arithmetic_Intel_C_C_0x_mode_version_1910_linux_[]
[table:table_Polynomial_Arithmetic_Intel_C_C_0x_mode_version_1910_linux_ Polynomial Arithmetic (Intel C++ C++0x mode version 1910, linux)
[[Function][std::uint64_t][double][cpp_int]]
[[operator *][[role green 1.12[br](417ns)]][[role green 1.00[br](372ns)]][[role red 23.61[br](8782ns)]]]
[[operator * (int)][[role green 1.04[br](103ns)]][[role green 1.00[br](99ns)]][[role red 12.15[br](1203ns)]]]
[[operator *=][[role green 1.14[br](199820ns)]][[role green 1.00[br](175634ns)]][[role red 22.31[br](3918504ns)]]]
[[operator *= (int)][[role green 1.00[br](15241ns)]][[role green 1.08[br](16386ns)]][[role red 23.08[br](351800ns)]]]
[[operator +][[role green 1.00[br](146ns)]][[role green 1.12[br](163ns)]][[role red 9.82[br](1433ns)]]]
[[operator + (int)][[role green 1.00[br](94ns)]][[role green 1.00[br](94ns)]][[role red 6.64[br](624ns)]]]
[[operator +=][[role green 1.00[br](27ns)]][[role green 1.04[br](28ns)]][[role red 21.19[br](572ns)]]]
[[operator += (int)][[role blue 1.67[br](5ns)]][[role green 1.00[br](3ns)]][[role red 44.67[br](134ns)]]]
[[operator -][[role green 1.00[br](144ns)]][[role green 1.09[br](157ns)]][[role red 11.32[br](1630ns)]]]
[[operator - (int)][[role green 1.00[br](93ns)]][[role green 1.01[br](94ns)]][[role red 6.55[br](609ns)]]]
[[operator -=][[role blue 1.40[br](28ns)]][[role green 1.00[br](20ns)]][[role red 31.60[br](632ns)]]]
[[operator -= (int)][[role blue 1.33[br](4ns)]][[role green 1.00[br](3ns)]][[role red 43.00[br](129ns)]]]
[[operator /][[role blue 1.70[br](840ns)]][[role green 1.00[br](495ns)]][[role red 45.57[br](22558ns)]]]
[[operator / (int)][[role green 1.07[br](106ns)]][[role green 1.00[br](99ns)]][[role red 19.42[br](1923ns)]]]
[[operator /=][[role green 1.04[br](29ns)]][[role green 1.00[br](28ns)]][[role red 85.39[br](2391ns)]]]
[[operator /= (int)][[role green 1.00[br](723ns)]][[role red 35.63[br](25759ns)]][[role red 4559.59[br](3296580ns)]]]
]
]

[template table_Rational_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[table:table_Rational_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux Rational Method Comparison with Intel C++ C++0x mode version 1910 on linux
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role red 2.17[br](26ns)]][[role blue 2.00[br](24ns)]][[role green 1.08[br](13ns)]][[role green 1.08[br](13ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]]]
[[Order  3][[role red 2.88[br](49ns)]][[role red 3.18[br](54ns)]][[role blue 1.59[br](27ns)]][[role blue 1.41[br](24ns)]][[role green 1.00[br](17ns)]][[role green 1.00[br](17ns)]][[role green 1.00[br](17ns)]][[role green 1.00[br](17ns)]]]
[[Order  4][[role red 4.00[br](68ns)]][[role red 4.35[br](74ns)]][[role blue 1.88[br](32ns)]][[role blue 1.88[br](32ns)]][[role green 1.06[br](18ns)]][[role green 1.06[br](18ns)]][[role green 1.00[br](17ns)]][[role green 1.00[br](17ns)]]]
[[Order  5][[role red 2.15[br](86ns)]][[role red 2.25[br](90ns)]][[role green 1.00[br](40ns)]][[role green 1.00[br](40ns)]][[role green 1.20[br](48ns)]][[role blue 1.23[br](49ns)]][[role green 1.05[br](42ns)]][[role green 1.07[br](43ns)]]]
[[Order  6][[role red 2.16[br](106ns)]][[role red 2.20[br](108ns)]][[role green 1.04[br](51ns)]][[role green 1.00[br](49ns)]][[role green 1.08[br](53ns)]][[role green 1.08[br](53ns)]][[role green 1.06[br](52ns)]][[role blue 1.24[br](61ns)]]]
[[Order  7][[role red 2.14[br](126ns)]][[role red 2.20[br](130ns)]][[role green 1.02[br](60ns)]][[role green 1.00[br](59ns)]][[role green 1.02[br](60ns)]][[role green 1.02[br](60ns)]][[role green 1.07[br](63ns)]][[role green 1.02[br](60ns)]]]
[[Order  8][[role red 2.43[br](170ns)]][[role red 2.11[br](148ns)]][[role green 1.03[br](72ns)]][[role green 1.01[br](71ns)]][[role green 1.00[br](70ns)]][[role green 1.00[br](70ns)]][[role green 1.00[br](70ns)]][[role blue 1.53[br](107ns)]]]
[[Order  9][[role red 2.06[br](165ns)]][[role red 2.12[br](170ns)]][[role green 1.14[br](91ns)]][[role green 1.15[br](92ns)]][[role green 1.02[br](82ns)]][[role green 1.00[br](80ns)]][[role green 1.01[br](81ns)]][[role blue 1.44[br](115ns)]]]
[[Order 10][[role red 2.22[br](189ns)]][[role red 2.26[br](192ns)]][[role green 1.13[br](96ns)]][[role green 1.12[br](95ns)]][[role green 1.01[br](86ns)]][[role green 1.04[br](88ns)]][[role green 1.00[br](85ns)]][[role blue 1.47[br](125ns)]]]
[[Order 11][[role red 2.15[br](209ns)]][[role red 2.15[br](209ns)]][[role green 1.12[br](109ns)]][[role green 1.20[br](116ns)]][[role green 1.00[br](97ns)]][[role green 1.01[br](98ns)]][[role blue 1.46[br](142ns)]][[role blue 1.42[br](138ns)]]]
[[Order 12][[role red 2.10[br](227ns)]][[role red 2.17[br](234ns)]][[role green 1.19[br](128ns)]][[role green 1.13[br](122ns)]][[role green 1.01[br](109ns)]][[role green 1.00[br](108ns)]][[role blue 1.31[br](141ns)]][[role blue 1.37[br](148ns)]]]
[[Order 13][[role red 2.73[br](246ns)]][[role red 2.78[br](250ns)]][[role green 1.18[br](106ns)]][[role green 1.17[br](105ns)]][[role green 1.01[br](91ns)]][[role green 1.00[br](90ns)]][[role blue 1.66[br](149ns)]][[role blue 1.80[br](162ns)]]]
[[Order 14][[role red 2.92[br](266ns)]][[role red 3.03[br](276ns)]][[role green 1.18[br](107ns)]][[role red 2.89[br](263ns)]][[role green 1.00[br](91ns)]][[role green 1.04[br](95ns)]][[role blue 1.78[br](162ns)]][[role red 2.15[br](196ns)]]]
[[Order 15][[role red 2.97[br](288ns)]][[role red 2.97[br](288ns)]][[role green 1.11[br](108ns)]][[role red 2.98[br](289ns)]][[role green 1.00[br](97ns)]][[role red 2.38[br](231ns)]][[role blue 1.79[br](174ns)]][[role blue 1.82[br](177ns)]]]
[[Order 16][[role red 3.17[br](314ns)]][[role red 3.15[br](312ns)]][[role green 1.13[br](112ns)]][[role red 3.35[br](332ns)]][[role green 1.00[br](99ns)]][[role red 2.53[br](250ns)]][[role blue 1.89[br](187ns)]][[role blue 1.94[br](192ns)]]]
[[Order 17][[role red 2.99[br](326ns)]][[role red 3.11[br](339ns)]][[role green 1.11[br](121ns)]][[role red 3.31[br](361ns)]][[role green 1.00[br](109ns)]][[role red 2.42[br](264ns)]][[role blue 1.81[br](197ns)]][[role blue 1.83[br](200ns)]]]
[[Order 18][[role red 3.43[br](350ns)]][[role red 3.52[br](359ns)]][[role blue 1.21[br](123ns)]][[role red 3.94[br](402ns)]][[role green 1.00[br](102ns)]][[role red 2.86[br](292ns)]][[role blue 1.98[br](202ns)]][[role red 2.08[br](212ns)]]]
[[Order 19][[role red 3.51[br](379ns)]][[role red 3.43[br](370ns)]][[role green 1.16[br](125ns)]][[role red 3.97[br](429ns)]][[role green 1.00[br](108ns)]][[role red 3.01[br](325ns)]][[role blue 1.94[br](209ns)]][[role red 2.17[br](234ns)]]]
[[Order 20][[role red 3.69[br](421ns)]][[role red 4.34[br](495ns)]][[role green 1.18[br](134ns)]][[role red 4.31[br](491ns)]][[role green 1.00[br](114ns)]][[role red 3.08[br](351ns)]][[role blue 1.96[br](224ns)]][[role red 2.06[br](235ns)]]]
]
]

[template table_Polynomial_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[table:table_Polynomial_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux Polynomial Method Comparison with Intel C++ C++0x mode version 1910 on linux
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role green 1.00[br](3ns)]][[role green 1.00[br](3ns)]][[role blue 1.33[br](4ns)]][[role blue 1.33[br](4ns)]][[role blue 1.33[br](4ns)]][[role green 1.00[br](3ns)]]]
[[Order  3][[role blue 1.75[br](14ns)]][[role red 2.12[br](17ns)]][[role green 1.00[br](8ns)]][[role green 1.00[br](8ns)]][[role green 1.00[br](8ns)]][[role green 1.00[br](8ns)]][[role green 1.00[br](8ns)]][[role green 1.00[br](8ns)]]]
[[Order  4][[role blue 1.64[br](18ns)]][[role red 2.18[br](24ns)]][[role green 1.09[br](12ns)]][[role green 1.18[br](13ns)]][[role green 1.00[br](11ns)]][[role green 1.00[br](11ns)]][[role green 1.00[br](11ns)]][[role green 1.00[br](11ns)]]]
[[Order  5][[role blue 1.60[br](24ns)]][[role red 2.13[br](32ns)]][[role green 1.00[br](15ns)]][[role green 1.00[br](15ns)]][[role green 1.07[br](16ns)]][[role blue 1.27[br](19ns)]][[role green 1.00[br](15ns)]][[role green 1.07[br](16ns)]]]
[[Order  6][[role blue 1.88[br](30ns)]][[role red 2.56[br](41ns)]][[role green 1.00[br](16ns)]][[role green 1.00[br](16ns)]][[role blue 1.44[br](23ns)]][[role blue 1.31[br](21ns)]][[role blue 1.25[br](20ns)]][[role blue 1.25[br](20ns)]]]
[[Order  7][[role blue 2.00[br](36ns)]][[role red 2.67[br](48ns)]][[role green 1.06[br](19ns)]][[role green 1.00[br](18ns)]][[role blue 1.33[br](24ns)]][[role blue 1.44[br](26ns)]][[role blue 1.28[br](23ns)]][[role blue 1.28[br](23ns)]]]
[[Order  8][[role blue 1.86[br](41ns)]][[role red 2.55[br](56ns)]][[role green 1.00[br](22ns)]][[role green 1.00[br](22ns)]][[role blue 1.32[br](29ns)]][[role blue 1.27[br](28ns)]][[role green 1.18[br](26ns)]][[role green 1.18[br](26ns)]]]
[[Order  9][[role blue 1.85[br](48ns)]][[role red 2.46[br](64ns)]][[role green 1.00[br](26ns)]][[role green 1.00[br](26ns)]][[role blue 1.27[br](33ns)]][[role blue 1.31[br](34ns)]][[role green 1.15[br](30ns)]][[role green 1.15[br](30ns)]]]
[[Order 10][[role blue 1.69[br](54ns)]][[role red 2.28[br](73ns)]][[role green 1.00[br](32ns)]][[role green 1.00[br](32ns)]][[role blue 1.22[br](39ns)]][[role green 1.19[br](38ns)]][[role green 1.06[br](34ns)]][[role green 1.09[br](35ns)]]]
[[Order 11][[role blue 1.77[br](62ns)]][[role red 2.29[br](80ns)]][[role green 1.00[br](35ns)]][[role green 1.00[br](35ns)]][[role blue 1.26[br](44ns)]][[role blue 1.49[br](52ns)]][[role green 1.06[br](37ns)]][[role green 1.09[br](38ns)]]]
[[Order 12][[role blue 1.77[br](71ns)]][[role red 2.20[br](88ns)]][[role green 1.05[br](42ns)]][[role green 1.00[br](40ns)]][[role blue 1.35[br](54ns)]][[role blue 1.32[br](53ns)]][[role blue 1.35[br](54ns)]][[role blue 1.48[br](59ns)]]]
[[Order 13][[role blue 1.81[br](76ns)]][[role red 2.33[br](98ns)]][[role green 1.02[br](43ns)]][[role green 1.00[br](42ns)]][[role blue 1.36[br](57ns)]][[role blue 1.24[br](52ns)]][[role blue 1.57[br](66ns)]][[role blue 1.36[br](57ns)]]]
[[Order 14][[role blue 1.98[br](85ns)]][[role red 2.47[br](106ns)]][[role green 1.00[br](43ns)]][[role green 1.00[br](43ns)]][[role blue 1.33[br](57ns)]][[role blue 1.30[br](56ns)]][[role blue 1.35[br](58ns)]][[role green 1.16[br](50ns)]]]
[[Order 15][[role red 2.26[br](95ns)]][[role red 2.67[br](112ns)]][[role green 1.05[br](44ns)]][[role green 1.00[br](42ns)]][[role green 1.14[br](48ns)]][[role green 1.17[br](49ns)]][[role green 1.19[br](50ns)]][[role green 1.12[br](47ns)]]]
[[Order 16][[role red 2.31[br](97ns)]][[role red 2.86[br](120ns)]][[role green 1.00[br](42ns)]][[role green 1.02[br](43ns)]][[role green 1.12[br](47ns)]][[role green 1.12[br](47ns)]][[role blue 1.33[br](56ns)]][[role blue 1.69[br](71ns)]]]
[[Order 17][[role red 2.39[br](105ns)]][[role red 2.93[br](129ns)]][[role green 1.00[br](44ns)]][[role green 1.02[br](45ns)]][[role blue 1.34[br](59ns)]][[role green 1.11[br](49ns)]][[role blue 1.23[br](54ns)]][[role green 1.02[br](45ns)]]]
[[Order 18][[role red 2.63[br](113ns)]][[role red 3.19[br](137ns)]][[role green 1.00[br](43ns)]][[role green 1.00[br](43ns)]][[role green 1.14[br](49ns)]][[role green 1.14[br](49ns)]][[role blue 1.37[br](59ns)]][[role green 1.14[br](49ns)]]]
[[Order 19][[role red 2.93[br](123ns)]][[role red 3.50[br](147ns)]][[role green 1.02[br](43ns)]][[role green 1.00[br](42ns)]][[role green 1.12[br](47ns)]][[role green 1.10[br](46ns)]][[role green 1.10[br](46ns)]][[role green 1.10[br](46ns)]]]
[[Order 20][[role red 3.28[br](141ns)]][[role red 3.60[br](155ns)]][[role green 1.00[br](43ns)]][[role green 1.02[br](44ns)]][[role green 1.09[br](47ns)]][[role green 1.14[br](49ns)]][[role green 1.07[br](46ns)]][[role green 1.05[br](45ns)]]]
]
]

[template table_Library_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[table:table_Library_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux Library Comparison with Intel C++ C++0x mode version 1910 on linux
[[Function][boost 1.73][boost 1.73[br]promote_double<false>][tr1/cmath][GSL 2.5][Rmath 3.6.3][math.h]]
[[assoc_laguerre[br](2240/2240 tests selected)][[role green 1.15[br](153ns)]][[role green 1.00[br](133ns)]][[role green 1.09[br](145ns)]][[role blue 1.23[br](163ns)]][[role grey -]][[role grey -]]]
[[assoc_legendre[br](110/400 tests selected)][[role red 7.24[br](268ns)]][[role red 5.59[br](207ns)]][[role green 1.00[br](37ns)]][[role red 3.59[br](133ns)]][[role grey -]][[role grey -]]]
[[beta (incomplete)[br](2682/3210 tests selected)][[role red 2.72[br](1315ns)]][[role green 1.00[br](484ns)]][[role grey -]][[role blue 1.23[br](593ns)]][[role grey -]][[role grey -]]]
[[beta[br](2196/2204 tests selected)][[role red 6.07[br](455ns)]][[role green 1.00[br](75ns)]][[role blue 1.69[br](127ns)]][[role red 3.89[br](292ns)]][[role red 3.04[br](228ns)]][[role grey -]]]
[[cbrt[br](85/85 tests selected)][[role red 2.59[br](44ns)]][[role blue 1.35[br](23ns)]][[role green 1.00[br](17ns)]][[role grey -]][[role grey -]][[role green 1.00[br](17ns)]]]
[[cyl_bessel_i (integer order)[br](494/526 tests selected)][[role red 5.13[br](631ns)]][[role blue 1.59[br](195ns)]][[role green 1.00[br](123ns)]][[role red 2.11[br](260ns)]][[role red 6.95[br](855ns)]][[role grey -]]]
[[cyl_bessel_i[br](177/240 tests selected)][[role red 7.46[br](933ns)]][[role blue 1.87[br](234ns)]][[role green 1.00[br](125ns)]][[role red 4.40[br](550ns)]][[role red 10.35[br](1294ns)]][[role grey -]]]
[[cyl_bessel_j (integer order)[br](241/268 tests selected)][[role red 2.26[br](237ns)]][[role green 1.10[br](115ns)]][[role blue 1.69[br](177ns)]][[role blue 1.64[br](172ns)]][[role red 3.11[br](327ns)]][[role green 1.00[br](105ns)]]]
[[cyl_bessel_j[br](433/451 tests selected)][[role red 3.46[br](857ns)]][[role green 1.13[br](281ns)]][[role green 1.00[br](248ns)]][[role red 2.08[br](517ns)]][[role blue 1.38[br](343ns)]][[role grey -]]]
[[cyl_bessel_k (integer order)[br](505/508 tests selected)][[role red 20.02[br](3423ns)]][[role green 1.00[br](171ns)]][[role red 9.32[br](1594ns)]][[role green 1.15[br](196ns)]][[role blue 1.33[br](227ns)]][[role grey -]]]
[[cyl_bessel_k[br](96/279 tests selected)][[role red 11.76[br](4739ns)]][[role green 1.03[br](416ns)]][[role blue 1.37[br](553ns)]][[role green 1.00[br](403ns)]][[role green 1.06[br](428ns)]][[role grey -]]]
[[cyl_neumann (integer order)[br](424/428 tests selected)][[role red 2.87[br](364ns)]][[role green 1.00[br](127ns)]][[role red 3.83[br](486ns)]][[role red 3.76[br](478ns)]][[role red 6.59[br](837ns)]][[role blue 1.91[br](242ns)]]]
[[cyl_neumann[br](428/450 tests selected)][[role red 18.49[br](9503ns)]][[role red 7.86[br](4041ns)]][[role green 1.00[br](514ns)]][[role blue 1.41[br](726ns)]][[role green 1.17[br](600ns)]][]]
[[digamma[br](1019/1019 tests selected)][[role blue 1.85[br](50ns)]][[role green 1.00[br](27ns)]][[role grey -]][[role red 3.81[br](103ns)]][[role red 8.04[br](217ns)]][[role grey -]]]
[[ellint_1 (complete)[br](109/109 tests selected)][[role blue 1.90[br](38ns)]][[role green 1.00[br](20ns)]][[role red 5.00[br](100ns)]][[role red 9.45[br](189ns)]][[role grey -]][[role grey -]]]
[[ellint_1[br](627/629 tests selected)][[role red 3.22[br](441ns)]][[role green 1.00[br](137ns)]][[role blue 1.28[br](176ns)]][[role red 2.33[br](319ns)]][[role grey -]][[role grey -]]]
[[ellint_2 (complete)[br](109/110 tests selected)][[role blue 2.00[br](48ns)]][[role green 1.00[br](24ns)]][[role red 8.46[br](203ns)]][[role red 15.75[br](378ns)]][[role grey -]][[role grey -]]]
[[ellint_2[br](527/530 tests selected)][[role red 4.36[br](807ns)]][[role green 1.00[br](185ns)]][[role blue 1.40[br](259ns)]][[role red 2.65[br](491ns)]][[role grey -]][[role grey -]]]
[[ellint_3 (complete)[br](0/500 tests selected)][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role grey -]][[role grey -]]]
[[ellint_3[br](22/845 tests selected)][[role red 3.52[br](528ns)]][[role blue 1.24[br](186ns)]][[role green 1.00[br](150ns)]][[role blue 1.53[br](229ns)]][[role grey -]][[role grey -]]]
[[ellint_rc[br](201/201 tests selected)][[role blue 1.83[br](44ns)]][[role green 1.00[br](24ns)]][[role grey -]][[role red 6.21[br](149ns)]][[role grey -]][[role grey -]]]
[[ellint_rd[br](7588/7588 tests selected)][[role red 4.17[br](409ns)]][[role green 1.00[br](98ns)]][[role grey -]][[role red 2.32[br](227ns)]][[role grey -]][[role grey -]]]
[[ellint_rf[br](7788/7788 tests selected)][[role blue 1.85[br](63ns)]][[role green 1.00[br](34ns)]][[role grey -]][[role red 6.35[br](216ns)]][[role grey -]][[role grey -]]]
[[ellint_rj[br](7642/8032 tests selected)][[role red 3.33[br](343ns)]][[role green 1.00[br](103ns)]][[role grey -]][[role red 14.01[br](1443ns)]][[role grey -]][[role grey -]]]
[[erf[br](950/950 tests selected)][[role blue 1.23[br](32ns)]][[role grey -]][[role green 1.00[br](26ns)]][[role red 3.69[br](96ns)]][[role grey -]][[role green 1.00[br](26ns)]]]
[[erfc[br](950/950 tests selected)][[role blue 1.22[br](66ns)]][[role green 1.00[br](54ns)]][[role blue 2.00[br](108ns)]][[role blue 1.72[br](93ns)]][[role grey -]][[role red 2.07[br](112ns)]]]
[[expint (En)[br](1059/1059 tests selected)][[role blue 1.84[br](184ns)]][[role green 1.00[br](100ns)]][[role grey -]][[role red 4.28[br](428ns)]][[role grey -]][[role grey -]]]
[[expint[br](436/436 tests selected)][[role red 2.21[br](64ns)]][[role green 1.00[br](29ns)]][[role red 3.17[br](92ns)]][[role red 5.59[br](162ns)]][[role grey -]][[role grey -]]]
[[expm1[br](80/80 tests selected)][[role blue 1.80[br](18ns)]][[role green 1.00[br](10ns)]][[role blue 1.80[br](18ns)]][[role grey -]][[role grey -]][[role blue 1.80[br](18ns)]]]
[[gamma_p[br](1379/1379 tests selected)][[role red 2.24[br](545ns)]][[role green 1.08[br](262ns)]][[role grey -]][[role blue 1.81[br](440ns)]][[role green 1.00[br](243ns)]][[role grey -]]]
[[gamma_p_inv[br](559/559 tests selected)][[role blue 1.92[br](1883ns)]][[role green 1.00[br](982ns)]][[role grey -]][[role grey -]][[role blue 1.27[br](1244ns)]][[role grey -]]]
[[gamma_q[br](1371/1379 tests selected)][[role red 2.12[br](551ns)]][[role green 1.01[br](263ns)]][[role grey -]][[role blue 1.95[br](507ns)]][[role green 1.00[br](260ns)]][[role grey -]]]
[[gamma_q_inv[br](78/559 tests selected)][[role red 2.17[br](1275ns)]][[role blue 1.26[br](739ns)]][[role grey -]][[role grey -]][[role green 1.00[br](588ns)]][[role grey -]]]
[[ibeta[br](3210/3210 tests selected)][[role red 3.42[br](1453ns)]][[role green 1.16[br](491ns)]][[role grey -]][[role grey -]][[role green 1.00[br](425ns)]][[role grey -]]]
[[ibeta_inv[br](1204/1210 tests selected)][[role red 2.58[br](4437ns)]][[role green 1.00[br](1722ns)]][[role grey -]][[role grey -]][[role red 2.74[br](4715ns)]][[role grey -]]]
[[ibetac[br](3210/3210 tests selected)][[role red 3.63[br](1438ns)]][[role blue 1.37[br](543ns)]][[role grey -]][[role grey -]][[role green 1.00[br](396ns)]][[role grey -]]]
[[ibetac_inv[br](1200/1210 tests selected)][[role red 2.48[br](4131ns)]][[role green 1.00[br](1666ns)]][[role grey -]][[role grey -]][[role red 2.51[br](4183ns)]][[role grey -]]]
[[jacobi_cn[br](2368/2757 tests selected)][[role red 5.15[br](345ns)]][[role blue 1.93[br](129ns)]][[role grey -]][[role green 1.00[br](67ns)]][[role grey -]][[role grey -]]]
[[jacobi_dn[br](2368/2757 tests selected)][[role red 5.43[br](375ns)]][[role blue 2.00[br](138ns)]][[role grey -]][[role green 1.00[br](69ns)]][[role grey -]][[role grey -]]]
[[jacobi_sn[br](2368/2757 tests selected)][[role red 4.72[br](316ns)]][[role blue 1.84[br](123ns)]][[role grey -]][[role green 1.00[br](67ns)]][[role grey -]][[role grey -]]]
[[laguerre[br](280/280 tests selected)][[role green 1.17[br](97ns)]][[role green 1.00[br](83ns)]][[role green 1.11[br](92ns)]][[role blue 1.31[br](109ns)]][[role grey -]][[role grey -]]]
[[legendre Q[br](300/300 tests selected)][[role blue 1.37[br](445ns)]][[role green 1.07[br](347ns)]][[role grey -]][[role green 1.00[br](325ns)]][[role grey -]][[role grey -]]]
[[legendre[br](300/300 tests selected)][[role blue 1.29[br](349ns)]][[role green 1.00[br](270ns)]][[role green 1.07[br](289ns)]][[role blue 1.22[br](330ns)]][[role grey -]][[role grey -]]]
[[lgamma[br](400/400 tests selected)][[role red 3.65[br](190ns)]][[role blue 1.92[br](100ns)]][[role green 1.00[br](52ns)]][[role red 3.29[br](171ns)]][[role blue 2.00[br](104ns)]][[role green 1.00[br](52ns)]]]
[[log1p[br](80/80 tests selected)][[role green 1.12[br](19ns)]][[role green 1.06[br](18ns)]][[role green 1.00[br](17ns)]][[role grey -]][[role grey -]][[role green 1.06[br](18ns)]]]
[[polygamma[br](823/1535 tests selected)][[role red 11.79[br](3985ns)]][[role blue 1.65[br](559ns)]][[role grey -]][[role blue 1.37[br](464ns)]][[role green 1.00[br](338ns)]][[role grey -]]]
[[sph_bessel[br](483/483 tests selected)][[role red 2.12[br](1010ns)]][[role green 1.00[br](476ns)]][[role red 2.70[br](1283ns)]][[role red 2.41[br](1148ns)]][[role grey -]][[role grey -]]]
[[sph_neumann[br](284/284 tests selected)][[role red 7.02[br](2627ns)]][[role red 2.24[br](837ns)]][[role red 5.25[br](1963ns)]][[role green 1.00[br](374ns)]][[role grey -]][]]
[[tgamma (incomplete)[br](1266/1379 tests selected)][[role red 2.21[br](451ns)]][[role green 1.00[br](204ns)]][[role grey -]][[role red 2.25[br](460ns)]][[role grey -]][[role grey -]]]
[[tgamma[br](400/400 tests selected)][[role red 3.32[br](229ns)]][[role blue 1.45[br](100ns)]][[role green 1.01[br](70ns)]][[role blue 1.22[br](84ns)]][[role blue 1.26[br](87ns)]][[role green 1.00[br](69ns)]]]
[[trigamma[br](659/659 tests selected)][[role blue 1.85[br](24ns)]][[role green 1.00[br](13ns)]][[role grey -]][[role red 44.00[br](572ns)]][[role red 20.85[br](271ns)]][[role grey -]]]
[[zeta[br](448/448 tests selected)][[role red 2.75[br](322ns)]][[role green 1.00[br](117ns)]][[role red 638.50[br](74704ns)]][[role blue 1.91[br](224ns)]][][]]
]
]

[template table_Distribution_performance_comparison_for_different_performance_options_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[table:table_Distribution_performance_comparison_for_different_performance_options_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Distribution performance comparison for different performance options with Clang version 9.0.0 (tags/RELEASE_900/final) on linux
[[Function][boost 1.73][Boost[br]promote_double<false>][Boost[br]promote_double<false>[br]digits10<10>][Boost[br]float[br]promote_float<false>]]
[[ArcSine (CDF)][[role blue 1.53[br](55ns)]][[role blue 1.47[br](53ns)]][[role blue 1.42[br](51ns)]][[role green 1.00[br](36ns)]]]
[[ArcSine (PDF)][[role green 1.05[br](22ns)]][[role green 1.00[br](21ns)]][[role green 1.00[br](21ns)]][[role blue 1.24[br](26ns)]]]
[[ArcSine (quantile)][[role green 1.05[br](40ns)]][[role green 1.05[br](40ns)]][[role green 1.03[br](39ns)]][[role green 1.00[br](38ns)]]]
[[Beta (CDF)][[role red 4.93[br](528ns)]][[role blue 1.50[br](161ns)]][[role green 1.17[br](125ns)]][[role green 1.00[br](107ns)]]]
[[Beta (PDF)][[role red 4.68[br](449ns)]][[role green 1.10[br](106ns)]][[role green 1.07[br](103ns)]][[role green 1.00[br](96ns)]]]
[[Beta (quantile)][[role red 4.83[br](3029ns)]][[role blue 1.43[br](896ns)]][[role blue 1.24[br](775ns)]][[role green 1.00[br](627ns)]]]
[[Binomial (CDF)][[role red 4.12[br](1013ns)]][[role blue 1.68[br](414ns)]][[role green 1.15[br](284ns)]][[role green 1.00[br](246ns)]]]
[[Binomial (PDF)][[role red 3.88[br](435ns)]][[role green 1.00[br](112ns)]][[role green 1.04[br](116ns)]][[role green 1.07[br](120ns)]]]
[[Binomial (quantile)][[role red 3.72[br](4511ns)]][[role blue 1.40[br](1696ns)]][[role green 1.18[br](1432ns)]][[role green 1.00[br](1214ns)]]]
[[Cauchy (CDF)][[role green 1.03[br](31ns)]][[role green 1.13[br](34ns)]][[role green 1.03[br](31ns)]][[role green 1.00[br](30ns)]]]
[[Cauchy (PDF)][[role green 1.14[br](8ns)]][[role blue 1.29[br](9ns)]][[role green 1.00[br](7ns)]][[role blue 1.43[br](10ns)]]]
[[Cauchy (quantile)][[role blue 1.43[br](63ns)]][[role blue 1.41[br](62ns)]][[role blue 1.43[br](63ns)]][[role green 1.00[br](44ns)]]]
[[ChiSquared (CDF)][[role red 4.06[br](1396ns)]][[role blue 1.72[br](592ns)]][[role blue 1.40[br](480ns)]][[role green 1.00[br](344ns)]]]
[[ChiSquared (PDF)][[role red 6.41[br](474ns)]][[role blue 1.34[br](99ns)]][[role blue 1.34[br](99ns)]][[role green 1.00[br](74ns)]]]
[[ChiSquared (quantile)][[role red 4.04[br](2226ns)]][[role blue 1.71[br](942ns)]][[role blue 1.29[br](709ns)]][[role green 1.00[br](551ns)]]]
[[Exponential (CDF)][[role blue 1.30[br](26ns)]][[role blue 1.40[br](28ns)]][[role blue 1.40[br](28ns)]][[role green 1.00[br](20ns)]]]
[[Exponential (PDF)][[role blue 1.67[br](35ns)]][[role blue 1.62[br](34ns)]][[role blue 1.62[br](34ns)]][[role green 1.00[br](21ns)]]]
[[Exponential (quantile)][[role green 1.05[br](22ns)]][[role green 1.05[br](22ns)]][[role green 1.00[br](21ns)]][[role green 1.19[br](25ns)]]]
[[ExtremeValue (CDF)][[role blue 1.62[br](68ns)]][[role blue 1.76[br](74ns)]][[role blue 1.57[br](66ns)]][[role green 1.00[br](42ns)]]]
[[ExtremeValue (PDF)][[role blue 1.71[br](94ns)]][[role blue 1.76[br](97ns)]][[role blue 1.84[br](101ns)]][[role green 1.00[br](55ns)]]]
[[ExtremeValue (quantile)][[role blue 1.49[br](61ns)]][[role blue 1.49[br](61ns)]][[role blue 1.54[br](63ns)]][[role green 1.00[br](41ns)]]]
[[F (CDF)][[role red 4.54[br](949ns)]][[role blue 1.49[br](312ns)]][[role blue 1.22[br](254ns)]][[role green 1.00[br](209ns)]]]
[[F (PDF)][[role red 3.83[br](467ns)]][[role green 1.03[br](126ns)]][[role green 1.07[br](131ns)]][[role green 1.00[br](122ns)]]]
[[F (quantile)][[role red 3.19[br](3273ns)]][[role green 1.15[br](1175ns)]][[role green 1.00[br](1026ns)]][[role green 1.00[br](1029ns)]]]
[[Gamma (CDF)][[role red 4.10[br](812ns)]][[role blue 1.51[br](299ns)]][[role blue 1.35[br](268ns)]][[role green 1.00[br](198ns)]]]
[[Gamma (PDF)][[role red 6.25[br](581ns)]][[role blue 1.38[br](128ns)]][[role blue 1.38[br](128ns)]][[role green 1.00[br](93ns)]]]
[[Gamma (quantile)][[role red 5.02[br](2155ns)]][[role blue 1.86[br](797ns)]][[role blue 1.64[br](703ns)]][[role green 1.00[br](429ns)]]]
[[Geometric (CDF)][[role green 1.15[br](30ns)]][[role blue 1.23[br](32ns)]][[role green 1.19[br](31ns)]][[role green 1.00[br](26ns)]]]
[[Geometric (PDF)][[role blue 1.56[br](25ns)]][[role blue 1.50[br](24ns)]][[role blue 1.56[br](25ns)]][[role green 1.00[br](16ns)]]]
[[Geometric (quantile)][[role green 1.07[br](30ns)]][[role green 1.04[br](29ns)]][[role green 1.00[br](28ns)]][[role green 1.04[br](29ns)]]]
[[Hypergeometric (CDF)][[role red 2.36[br](9837ns)]][[role green 1.17[br](4879ns)]][[role green 1.00[br](4160ns)]][[role green 1.04[br](4306ns)]]]
[[Hypergeometric (PDF)][[role red 2.41[br](9697ns)]][[role blue 1.24[br](5007ns)]][[role green 1.00[br](4032ns)]][[role green 1.05[br](4240ns)]]]
[[Hypergeometric (quantile)][[role green 1.00[br](58085ns)]][[role blue 1.27[br](73637ns)]][[role blue 1.31[br](75953ns)]][[role green 1.01[br](58776ns)]]]
[[InverseChiSquared (CDF)][[role red 4.12[br](1398ns)]][[role blue 1.72[br](582ns)]][[role blue 1.36[br](460ns)]][[role green 1.00[br](339ns)]]]
[[InverseChiSquared (PDF)][[role red 5.68[br](392ns)]][[role blue 1.32[br](91ns)]][[role blue 1.30[br](90ns)]][[role green 1.00[br](69ns)]]]
[[InverseChiSquared (quantile)][[role red 4.06[br](2045ns)]][[role blue 1.78[br](896ns)]][[role blue 1.34[br](676ns)]][[role green 1.00[br](504ns)]]]
[[InverseGamma (CDF)][[role red 4.08[br](726ns)]][[role blue 1.44[br](256ns)]][[role blue 1.28[br](227ns)]][[role green 1.00[br](178ns)]]]
[[InverseGamma (PDF)][[role red 6.47[br](511ns)]][[role blue 1.44[br](114ns)]][[role blue 1.39[br](110ns)]][[role green 1.00[br](79ns)]]]
[[InverseGamma (quantile)][[role red 5.07[br](1867ns)]][[role blue 1.83[br](673ns)]][[role blue 1.26[br](465ns)]][[role green 1.00[br](368ns)]]]
[[InverseGaussian (CDF)][[role red 2.13[br](209ns)]][[role red 2.14[br](210ns)]][[role red 2.14[br](210ns)]][[role green 1.00[br](98ns)]]]
[[InverseGaussian (PDF)][[role blue 1.46[br](19ns)]][[role blue 1.46[br](19ns)]][[role blue 1.46[br](19ns)]][[role green 1.00[br](13ns)]]]
[[InverseGaussian (quantile)][[role red 2.82[br](3346ns)]][[role red 2.77[br](3291ns)]][[role red 2.55[br](3028ns)]][[role green 1.00[br](1187ns)]]]
[[Laplace (CDF)][[role blue 1.62[br](34ns)]][[role blue 1.62[br](34ns)]][[role blue 1.62[br](34ns)]][[role green 1.00[br](21ns)]]]
[[Laplace (PDF)][[role blue 1.50[br](33ns)]][[role blue 1.50[br](33ns)]][[role blue 1.55[br](34ns)]][[role green 1.00[br](22ns)]]]
[[Laplace (quantile)][[role green 1.10[br](32ns)]][[role green 1.10[br](32ns)]][[role green 1.10[br](32ns)]][[role green 1.00[br](29ns)]]]
[[LogNormal (CDF)][[role red 2.44[br](200ns)]][[role blue 1.28[br](105ns)]][[role blue 1.28[br](105ns)]][[role green 1.00[br](82ns)]]]
[[LogNormal (PDF)][[role blue 1.54[br](57ns)]][[role blue 1.54[br](57ns)]][[role blue 1.51[br](56ns)]][[role green 1.00[br](37ns)]]]
[[LogNormal (quantile)][[role blue 1.71[br](89ns)]][[role blue 1.29[br](67ns)]][[role blue 1.29[br](67ns)]][[role green 1.00[br](52ns)]]]
[[Logistic (CDF)][[role blue 1.48[br](34ns)]][[role blue 1.52[br](35ns)]][[role blue 1.48[br](34ns)]][[role green 1.00[br](23ns)]]]
[[Logistic (PDF)][[role blue 1.57[br](36ns)]][[role blue 1.57[br](36ns)]][[role blue 1.57[br](36ns)]][[role green 1.00[br](23ns)]]]
[[Logistic (quantile)][[role blue 1.45[br](32ns)]][[role blue 1.41[br](31ns)]][[role blue 1.45[br](32ns)]][[role green 1.00[br](22ns)]]]
[[NegativeBinomial (CDF)][[role red 4.76[br](1324ns)]][[role blue 1.59[br](441ns)]][[role blue 1.23[br](342ns)]][[role green 1.00[br](278ns)]]]
[[NegativeBinomial (PDF)][[role red 3.92[br](412ns)]][[role green 1.02[br](107ns)]][[role green 1.00[br](105ns)]][[role green 1.06[br](111ns)]]]
[[NegativeBinomial (quantile)][[role red 3.74[br](7442ns)]][[role blue 1.21[br](2416ns)]][[role green 1.00[br](1990ns)]][[role blue 1.30[br](2580ns)]]]
[[NonCentralBeta (CDF)][[role red 5.37[br](2211ns)]][[role blue 1.51[br](624ns)]][[role blue 1.33[br](548ns)]][[role green 1.00[br](412ns)]]]
[[NonCentralBeta (PDF)][[role red 4.52[br](1513ns)]][[role blue 1.31[br](438ns)]][[role green 1.18[br](394ns)]][[role green 1.00[br](335ns)]]]
[[NonCentralBeta (quantile)][[role red 8.41[br](54735ns)]][[role red 2.07[br](13467ns)]][[role blue 1.80[br](11743ns)]][[role green 1.00[br](6512ns)]]]
[[NonCentralChiSquared (CDF)][[role red 2.90[br](4049ns)]][[role blue 1.70[br](2377ns)]][[role blue 1.37[br](1913ns)]][[role green 1.00[br](1398ns)]]]
[[NonCentralChiSquared (PDF)][[role red 4.06[br](1085ns)]][[role blue 1.72[br](459ns)]][[role blue 1.68[br](449ns)]][[role green 1.00[br](267ns)]]]
[[NonCentralChiSquared (quantile)][[role red 5.10[br](32034ns)]][[role red 2.39[br](15008ns)]][[role blue 1.59[br](10018ns)]][[role green 1.00[br](6282ns)]]]
[[NonCentralF (CDF)][[role red 5.20[br](2153ns)]][[role blue 1.42[br](586ns)]][[role blue 1.25[br](517ns)]][[role green 1.00[br](414ns)]]]
[[NonCentralF (PDF)][[role red 4.93[br](1707ns)]][[role blue 1.33[br](460ns)]][[role blue 1.23[br](426ns)]][[role green 1.00[br](346ns)]]]
[[NonCentralF (quantile)][[role red 6.06[br](32648ns)]][[role blue 1.63[br](8799ns)]][[role blue 1.37[br](7357ns)]][[role green 1.00[br](5388ns)]]]
[[NonCentralT (CDF)][[role red 5.02[br](7196ns)]][[role blue 1.72[br](2471ns)]][[role blue 1.52[br](2173ns)]][[role green 1.00[br](1434ns)]]]
[[NonCentralT (PDF)][[role red 4.14[br](4411ns)]][[role blue 1.91[br](2032ns)]][[role blue 1.76[br](1874ns)]][[role green 1.00[br](1065ns)]]]
[[NonCentralT (quantile)][[role red 5.91[br](75131ns)]][[role blue 1.92[br](24461ns)]][[role blue 1.49[br](18991ns)]][[role green 1.00[br](12722ns)]]]
[[Normal (CDF)][[role red 2.18[br](190ns)]][[role green 1.06[br](92ns)]][[role green 1.06[br](92ns)]][[role green 1.00[br](87ns)]]]
[[Normal (PDF)][[role blue 1.54[br](40ns)]][[role blue 1.54[br](40ns)]][[role blue 1.62[br](42ns)]][[role green 1.00[br](26ns)]]]
[[Normal (quantile)][[role blue 1.48[br](68ns)]][[role green 1.07[br](49ns)]][[role green 1.00[br](46ns)]][[role green 1.04[br](48ns)]]]
[[Pareto (CDF)][[role blue 1.25[br](50ns)]][[role green 1.18[br](47ns)]][[role green 1.18[br](47ns)]][[role green 1.00[br](40ns)]]]
[[Pareto (PDF)][[role blue 1.80[br](74ns)]][[role blue 1.78[br](73ns)]][[role blue 1.80[br](74ns)]][[role green 1.00[br](41ns)]]]
[[Pareto (quantile)][[role blue 1.52[br](44ns)]][[role blue 1.55[br](45ns)]][[role blue 1.52[br](44ns)]][[role green 1.00[br](29ns)]]]
[[Poisson (CDF)][[role red 4.11[br](288ns)]][[role blue 1.50[br](105ns)]][[role blue 1.40[br](98ns)]][[role green 1.00[br](70ns)]]]
[[Poisson (PDF)][[role red 6.02[br](307ns)]][[role blue 1.59[br](81ns)]][[role blue 1.37[br](70ns)]][[role green 1.00[br](51ns)]]]
[[Poisson (quantile)][[role red 4.07[br](1490ns)]][[role blue 1.36[br](499ns)]][[role blue 1.39[br](509ns)]][[role green 1.00[br](366ns)]]]
[[Rayleigh (CDF)][[role blue 1.29[br](22ns)]][[role blue 1.41[br](24ns)]][[role blue 1.41[br](24ns)]][[role green 1.00[br](17ns)]]]
[[Rayleigh (PDF)][[role blue 1.67[br](30ns)]][[role blue 1.72[br](31ns)]][[role blue 1.67[br](30ns)]][[role green 1.00[br](18ns)]]]
[[Rayleigh (quantile)][[role green 1.05[br](20ns)]][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]][[role blue 1.21[br](23ns)]]]
[[SkewNormal (CDF)][[role blue 1.69[br](704ns)]][[role blue 1.56[br](650ns)]][[role blue 1.52[br](634ns)]][[role green 1.00[br](416ns)]]]
[[SkewNormal (PDF)][[role red 2.30[br](175ns)]][[role blue 1.30[br](99ns)]][[role blue 1.30[br](99ns)]][[role green 1.00[br](76ns)]]]
[[SkewNormal (quantile)][[role red 2.28[br](6643ns)]][[role red 2.06[br](5985ns)]][[role blue 1.38[br](4006ns)]][[role green 1.00[br](2910ns)]]]
[[StudentsT (CDF)][[role red 6.07[br](1640ns)]][[role blue 1.65[br](446ns)]][[role blue 1.45[br](391ns)]][[role green 1.00[br](270ns)]]]
[[StudentsT (PDF)][[role red 6.70[br](677ns)]][[role blue 1.60[br](162ns)]][[role blue 1.45[br](146ns)]][[role green 1.00[br](101ns)]]]
[[StudentsT (quantile)][[role red 6.70[br](3108ns)]][[role blue 1.55[br](717ns)]][[role blue 1.44[br](666ns)]][[role green 1.00[br](464ns)]]]
[[Weibull (CDF)][[role blue 1.64[br](59ns)]][[role blue 1.75[br](63ns)]][[role blue 1.72[br](62ns)]][[role green 1.00[br](36ns)]]]
[[Weibull (PDF)][[role blue 1.75[br](96ns)]][[role blue 1.75[br](96ns)]][[role blue 1.75[br](96ns)]][[role green 1.00[br](55ns)]]]
[[Weibull (quantile)][[role green 1.16[br](57ns)]][[role green 1.16[br](57ns)]][[role blue 1.22[br](60ns)]][[role green 1.00[br](49ns)]]]
]
]

[template table_Distribution_performance_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[table:table_Distribution_performance_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Distribution performance comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux
[[Function][boost 1.73][Boost[br]promote_double<false>][GSL][Rmath 3.6.3][DCDFLIB]]
[[ArcSine (CDF)][[role green 1.04[br](55ns)]][[role green 1.00[br](53ns)]][][][]]
[[ArcSine (PDF)][[role green 1.05[br](22ns)]][[role green 1.00[br](21ns)]][][][]]
[[ArcSine (quantile)][[role green 1.00[br](40ns)]][[role green 1.00[br](40ns)]][][][]]
[[Beta (CDF)][[role red 3.28[br](528ns)]][[role green 1.00[br](161ns)]][[role red 2.34[br](376ns)]][[role blue 1.34[br](215ns)]][[role blue 1.70[br](273ns)]]]
[[Beta (PDF)][[role red 4.24[br](449ns)]][[role green 1.00[br](106ns)]][[role grey -]][[role blue 1.89[br](200ns)]][]]
[[Beta (quantile)][[role red 3.38[br](3029ns)]][[role green 1.00[br](896ns)]][[role red 13.94[br](12489ns)]][[role blue 1.69[br](1513ns)]][[role red 6.43[br](5758ns)]]]
[[Binomial (CDF)][[role red 2.55[br](1013ns)]][[role green 1.04[br](414ns)]][[role blue 1.49[br](591ns)]][[role green 1.00[br](397ns)]][[role green 1.11[br](439ns)]]]
[[Binomial (PDF)][[role red 3.88[br](435ns)]][[role green 1.00[br](112ns)]][[role grey -]][[role blue 1.55[br](174ns)]][]]
[[Binomial (quantile)][[role red 4.30[br](4511ns)]][[role blue 1.62[br](1696ns)]][[role grey -]][[role green 1.00[br](1049ns)]][[role red 6.71[br](7038ns)]]]
[[Cauchy (CDF)][[role green 1.07[br](31ns)]][[role green 1.17[br](34ns)]][[role green 1.00[br](29ns)]][[role green 1.00[br](29ns)]][]]
[[Cauchy (PDF)][[role green 1.00[br](8ns)]][[role green 1.12[br](9ns)]][[role grey -]][[role blue 1.75[br](14ns)]][]]
[[Cauchy (quantile)][[role green 1.02[br](63ns)]][[role green 1.00[br](62ns)]][[role red 44.97[br](2788ns)]][[role green 1.13[br](70ns)]][]]
[[ChiSquared (CDF)][[role red 10.12[br](1396ns)]][[role red 4.29[br](592ns)]][[role red 9.62[br](1327ns)]][[role blue 1.43[br](198ns)]][[role green 1.00[br](138ns)]]]
[[ChiSquared (PDF)][[role red 5.04[br](474ns)]][[role green 1.05[br](99ns)]][[role grey -]][[role green 1.00[br](94ns)]][]]
[[ChiSquared (quantile)][[role red 2.36[br](2226ns)]][[role green 1.00[br](942ns)]][[role red 13.55[br](12763ns)]][[role blue 1.25[br](1178ns)]][[role red 3.73[br](3511ns)]]]
[[Exponential (CDF)][[role green 1.00[br](26ns)]][[role green 1.08[br](28ns)]][[role green 1.08[br](28ns)]][[role green 1.12[br](29ns)]][]]
[[Exponential (PDF)][[role green 1.03[br](35ns)]][[role green 1.00[br](34ns)]][[role grey -]][[role green 1.12[br](38ns)]][]]
[[Exponential (quantile)][[role green 1.00[br](22ns)]][[role green 1.00[br](22ns)]][[role blue 1.27[br](28ns)]][[role blue 1.32[br](29ns)]][]]
[[ExtremeValue (CDF)][[role green 1.00[br](68ns)]][[role green 1.09[br](74ns)]][][][]]
[[ExtremeValue (PDF)][[role green 1.00[br](94ns)]][[role green 1.03[br](97ns)]][][][]]
[[ExtremeValue (quantile)][[role green 1.00[br](61ns)]][[role green 1.00[br](61ns)]][][][]]
[[F (CDF)][[role red 3.04[br](949ns)]][[role green 1.00[br](312ns)]][[role blue 1.96[br](613ns)]][[role green 1.06[br](331ns)]][[role green 1.10[br](342ns)]]]
[[F (PDF)][[role red 3.71[br](467ns)]][[role green 1.00[br](126ns)]][[role grey -]][[role green 1.02[br](129ns)]][]]
[[F (quantile)][[role red 2.79[br](3273ns)]][[role green 1.00[br](1175ns)]][[role red 9.28[br](10909ns)]][[role blue 1.75[br](2058ns)]][[role red 4.45[br](5234ns)]]]
[[Gamma (CDF)][[role red 5.45[br](812ns)]][[role red 2.01[br](299ns)]][[role red 3.92[br](584ns)]][[role blue 1.45[br](216ns)]][[role green 1.00[br](149ns)]]]
[[Gamma (PDF)][[role red 4.54[br](581ns)]][[role green 1.00[br](128ns)]][[role grey -]][[role green 1.05[br](134ns)]][]]
[[Gamma (quantile)][[role red 4.18[br](2155ns)]][[role blue 1.55[br](797ns)]][[role red 10.80[br](5564ns)]][[role red 2.40[br](1236ns)]][[role green 1.00[br](515ns)]]]
[[Geometric (CDF)][[role green 1.00[br](30ns)]][[role green 1.07[br](32ns)]][[role blue 1.47[br](44ns)]][[role blue 1.60[br](48ns)]][]]
[[Geometric (PDF)][[role green 1.04[br](25ns)]][[role green 1.00[br](24ns)]][[role grey -]][[role red 12.33[br](296ns)]][]]
[[Geometric (quantile)][[role green 1.03[br](30ns)]][[role green 1.00[br](29ns)]][[role grey -]][[role blue 2.00[br](58ns)]][]]
[[Hypergeometric (CDF)][[role red 16.73[br](9837ns)]][[role red 8.30[br](4879ns)]][[role green 1.00[br](588ns)]][[role green 1.04[br](612ns)]][]]
[[Hypergeometric (PDF)][[role red 22.34[br](9697ns)]][[role red 11.54[br](5007ns)]][[role grey -]][[role green 1.00[br](434ns)]][]]
[[Hypergeometric (quantile)][[role green 1.00[br](58085ns)]][[role blue 1.27[br](73637ns)]][[role grey -]][[role blue 1.37[br](79592ns)]][]]
[[InverseChiSquared (CDF)][[role red 2.40[br](1398ns)]][[role green 1.00[br](582ns)]][][][]]
[[InverseChiSquared (PDF)][[role red 4.31[br](392ns)]][[role green 1.00[br](91ns)]][][][]]
[[InverseChiSquared (quantile)][[role red 2.28[br](2045ns)]][[role green 1.00[br](896ns)]][][][]]
[[InverseGamma (CDF)][[role red 2.84[br](726ns)]][[role green 1.00[br](256ns)]][][][]]
[[InverseGamma (PDF)][[role red 4.48[br](511ns)]][[role green 1.00[br](114ns)]][][][]]
[[InverseGamma (quantile)][[role red 2.77[br](1867ns)]][[role green 1.00[br](673ns)]][][][]]
[[InverseGaussian (CDF)][[role green 1.00[br](209ns)]][[role green 1.00[br](210ns)]][][][]]
[[InverseGaussian (PDF)][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]][][][]]
[[InverseGaussian (quantile)][[role green 1.02[br](3346ns)]][[role green 1.00[br](3291ns)]][][][]]
[[Laplace (CDF)][[role green 1.00[br](34ns)]][[role green 1.00[br](34ns)]][[role blue 1.21[br](41ns)]][][]]
[[Laplace (PDF)][[role green 1.00[br](33ns)]][[role green 1.00[br](33ns)]][][][]]
[[Laplace (quantile)][[role green 1.00[br](32ns)]][[role green 1.00[br](32ns)]][[role green 1.16[br](37ns)]][][]]
[[LogNormal (CDF)][[role red 2.30[br](200ns)]][[role blue 1.21[br](105ns)]][[role green 1.00[br](87ns)]][[role green 1.15[br](100ns)]][]]
[[LogNormal (PDF)][[role green 1.00[br](57ns)]][[role green 1.00[br](57ns)]][[role grey -]][[role green 1.07[br](61ns)]][]]
[[LogNormal (quantile)][[role blue 1.37[br](89ns)]][[role green 1.03[br](67ns)]][[role green 1.00[br](65ns)]][[role green 1.03[br](67ns)]][]]
[[Logistic (CDF)][[role green 1.00[br](34ns)]][[role green 1.03[br](35ns)]][[role blue 1.76[br](60ns)]][[role green 1.15[br](39ns)]][]]
[[Logistic (PDF)][[role green 1.00[br](36ns)]][[role green 1.00[br](36ns)]][[role grey -]][[role green 1.08[br](39ns)]][]]
[[Logistic (quantile)][[role green 1.03[br](32ns)]][[role green 1.00[br](31ns)]][[role green 1.16[br](36ns)]][[role blue 1.23[br](38ns)]][]]
[[NegativeBinomial (CDF)][[role red 3.21[br](1324ns)]][[role green 1.07[br](441ns)]][[role red 2.02[br](836ns)]][[role green 1.00[br](413ns)]][[role green 1.11[br](459ns)]]]
[[NegativeBinomial (PDF)][[role red 3.85[br](412ns)]][[role green 1.00[br](107ns)]][[role grey -]][[role blue 1.50[br](160ns)]][]]
[[NegativeBinomial (quantile)][[role red 3.08[br](7442ns)]][[role green 1.00[br](2416ns)]][[role grey -]][[role red 2.85[br](6896ns)]][[role red 3.70[br](8931ns)]]]
[[NonCentralBeta (CDF)][[role red 3.54[br](2211ns)]][[role green 1.00[br](624ns)]][[role grey -]][[role blue 1.26[br](785ns)]][]]
[[NonCentralBeta (PDF)][[role red 3.45[br](1513ns)]][[role green 1.00[br](438ns)]][[role grey -]][[role green 1.16[br](506ns)]][]]
[[NonCentralBeta (quantile)][[role red 4.06[br](54735ns)]][[role green 1.00[br](13467ns)]][[role grey -]][[role red 4.29[br](57742ns)]][]]
[[NonCentralChiSquared (CDF)][[role red 9.46[br](4049ns)]][[role red 5.55[br](2377ns)]][[role grey -]][[role red 16.85[br](7210ns)]][[role green 1.00[br](428ns)]]]
[[NonCentralChiSquared (PDF)][[role red 4.29[br](1085ns)]][[role blue 1.81[br](459ns)]][[role grey -]][[role green 1.00[br](253ns)]][]]
[[NonCentralChiSquared (quantile)][[role red 3.29[br](32034ns)]][[role blue 1.54[br](15008ns)]][[role grey -]][[role red 35.56[br](346071ns)]][[role green 1.00[br](9732ns)]]]
[[NonCentralF (CDF)][[role red 3.67[br](2153ns)]][[role green 1.00[br](586ns)]][[role grey -]][[role blue 1.48[br](867ns)]][[role blue 1.51[br](884ns)]]]
[[NonCentralF (PDF)][[role red 3.79[br](1707ns)]][[role green 1.02[br](460ns)]][[role grey -]][[role green 1.00[br](450ns)]][]]
[[NonCentralF (quantile)][[role red 3.71[br](32648ns)]][[role green 1.00[br](8799ns)]][[role grey -]][[role red 5.08[br](44683ns)]][[role blue 1.23[br](10838ns)]]]
[[NonCentralT (CDF)][[role red 6.38[br](7196ns)]][[role red 2.19[br](2471ns)]][[role grey -]][[role green 1.00[br](1128ns)]][]]
[[NonCentralT (PDF)][[role red 2.17[br](4411ns)]][[role green 1.00[br](2032ns)]][[role grey -]][[role green 1.16[br](2359ns)]][]]
[[NonCentralT (quantile)][[role red 3.07[br](75131ns)]][[role green 1.00[br](24461ns)]][[role grey -]][[role red 2.18[br](53281ns)]][]]
[[Normal (CDF)][[role red 4.04[br](190ns)]][[role blue 1.96[br](92ns)]][[role green 1.00[br](47ns)]][[role blue 1.51[br](71ns)]][[role red 2.89[br](136ns)]]]
[[Normal (PDF)][[role green 1.00[br](40ns)]][[role green 1.00[br](40ns)]][[role grey -]][[role green 1.00[br](40ns)]][]]
[[Normal (quantile)][[role blue 2.00[br](68ns)]][[role blue 1.44[br](49ns)]][[role blue 1.24[br](42ns)]][[role green 1.00[br](34ns)]][[role red 12.06[br](410ns)]]]
[[Pareto (CDF)][[role green 1.16[br](50ns)]][[role green 1.09[br](47ns)]][[role green 1.00[br](43ns)]][][]]
[[Pareto (PDF)][[role green 1.01[br](74ns)]][[role green 1.00[br](73ns)]][][][]]
[[Pareto (quantile)][[role green 1.00[br](44ns)]][[role green 1.02[br](45ns)]][[role blue 1.27[br](56ns)]][][]]
[[Poisson (CDF)][[role red 2.74[br](288ns)]][[role green 1.00[br](105ns)]][[role red 3.03[br](318ns)]][[role blue 1.64[br](172ns)]][[role green 1.19[br](125ns)]]]
[[Poisson (PDF)][[role red 3.79[br](307ns)]][[role green 1.00[br](81ns)]][[role grey -]][[role blue 1.32[br](107ns)]][]]
[[Poisson (quantile)][[role red 3.41[br](1490ns)]][[role green 1.14[br](499ns)]][[role grey -]][[role green 1.00[br](437ns)]][[role red 4.31[br](1884ns)]]]
[[Rayleigh (CDF)][[role green 1.00[br](22ns)]][[role green 1.09[br](24ns)]][[role blue 1.23[br](27ns)]][][]]
[[Rayleigh (PDF)][[role green 1.00[br](30ns)]][[role green 1.03[br](31ns)]][][][]]
[[Rayleigh (quantile)][[role green 1.05[br](20ns)]][[role green 1.00[br](19ns)]][[role blue 1.58[br](30ns)]][][]]
[[SkewNormal (CDF)][[role green 1.08[br](704ns)]][[role green 1.00[br](650ns)]][][][]]
[[SkewNormal (PDF)][[role blue 1.77[br](175ns)]][[role green 1.00[br](99ns)]][][][]]
[[SkewNormal (quantile)][[role green 1.11[br](6643ns)]][[role green 1.00[br](5985ns)]][][][]]
[[StudentsT (CDF)][[role red 7.59[br](1640ns)]][[role red 2.06[br](446ns)]][[role green 1.00[br](216ns)]][[role green 1.19[br](257ns)]][[role blue 1.33[br](288ns)]]]
[[StudentsT (PDF)][[role red 5.99[br](677ns)]][[role blue 1.43[br](162ns)]][[role grey -]][[role green 1.00[br](113ns)]][]]
[[StudentsT (quantile)][[role red 4.65[br](3108ns)]][[role green 1.07[br](717ns)]][[role blue 1.22[br](817ns)]][[role green 1.00[br](669ns)]][[role red 3.38[br](2264ns)]]]
[[Weibull (CDF)][[role green 1.00[br](59ns)]][[role green 1.07[br](63ns)]][[role green 1.03[br](61ns)]][[role green 1.08[br](64ns)]][]]
[[Weibull (PDF)][[role blue 1.30[br](96ns)]][[role blue 1.30[br](96ns)]][[role grey -]][[role green 1.00[br](74ns)]][]]
[[Weibull (quantile)][[role green 1.00[br](57ns)]][[role green 1.00[br](57ns)]][[role green 1.07[br](61ns)]][[role green 1.11[br](63ns)]][]]
]
]

[template table_gcd_method_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[table:table_gcd_method_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux gcd method comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux
[[Function][gcd
boost 1.73][Euclid_gcd
boost 1.73][Stein_gcd
boost 1.73][mixed_binary_gcd
boost 1.73][Stein_gcd_textbook
boost 1.73][gcd_euclid_textbook
boost 1.73]]
[[gcd<boost::multiprecision::uint1024_t> (Trivial cases)][[role green 1.00[br](910ns)]][[role blue 1.24[br](1127ns)]][[role red 4.72[br](4296ns)]][[role red 4.57[br](4156ns)]][[role blue 1.56[br](1424ns)]][[role blue 1.41[br](1285ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (adjacent Fibonacci numbers)][[role green 1.00[br](12382394ns)]][[role red 7.02[br](86969395ns)]][[role red 5.75[br](71154698ns)]][[role red 4.02[br](49833555ns)]][[role red 2.19[br](27175904ns)]][[role red 8.63[br](106845219ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (permutations of Fibonacci numbers)][[role red 3.29[br](6535287907ns)]][[role green 1.00[br](1986171978ns)]][[role red 21.91[br](43514694173ns)]][[role red 3.65[br](7257760593ns)]][[role red 8.45[br](16773785971ns)]][[role green 1.17[br](2314444899ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (random prime number products)][[role green 1.00[br](916142ns)]][[role blue 1.93[br](1772064ns)]][[role red 13.90[br](12737972ns)]][[role red 3.14[br](2874736ns)]][[role red 5.17[br](4732873ns)]][[role red 2.63[br](2407929ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (uniform random numbers)][[role green 1.00[br](57843834ns)]][[role red 2.22[br](128347576ns)]][[role red 4.01[br](232125718ns)]][[role red 2.73[br](157946120ns)]][[role blue 1.73[br](100083314ns)]][[role red 2.38[br](137578188ns)]]]
[[gcd<boost::multiprecision::uint256_t> (Trivial cases)][[role green 1.00[br](771ns)]][[role blue 1.28[br](984ns)]][[role red 5.51[br](4252ns)]][[role red 5.43[br](4190ns)]][[role blue 1.41[br](1085ns)]][[role blue 1.38[br](1063ns)]]]
[[gcd<boost::multiprecision::uint256_t> (adjacent Fibonacci numbers)][[role green 1.00[br](4907753ns)]][[role red 4.50[br](22061395ns)]][[role red 7.46[br](36606776ns)]][[role red 5.03[br](24695288ns)]][[role red 2.25[br](11056629ns)]][[role red 5.76[br](28255185ns)]]]
[[gcd<boost::multiprecision::uint256_t> (permutations of Fibonacci numbers)][[role green 1.00[br](2569685620ns)]][[role red 2.68[br](6887193468ns)]][[role red 7.91[br](20331779133ns)]][[role red 3.97[br](10198797773ns)]][[role red 2.63[br](6747343835ns)]][[role red 3.30[br](8478369599ns)]]]
[[gcd<boost::multiprecision::uint256_t> (random prime number products)][[role green 1.00[br](986082ns)]][[role blue 1.63[br](1603509ns)]][[role red 11.35[br](11190786ns)]][[role red 2.61[br](2573628ns)]][[role red 3.37[br](3319471ns)]][[role blue 1.86[br](1829568ns)]]]
[[gcd<boost::multiprecision::uint256_t> (uniform random numbers)][[role green 1.00[br](6689919ns)]][[role red 3.46[br](23155310ns)]][[role red 6.88[br](46057252ns)]][[role red 4.71[br](31522751ns)]][[role red 2.07[br](13863298ns)]][[role red 3.78[br](25290302ns)]]]
[[gcd<boost::multiprecision::uint512_t> (Trivial cases)][[role green 1.00[br](868ns)]][[role green 1.17[br](1017ns)]][[role red 4.34[br](3764ns)]][[role red 4.27[br](3706ns)]][[role blue 1.31[br](1140ns)]][[role blue 1.21[br](1053ns)]]]
[[gcd<boost::multiprecision::uint512_t> (adjacent Fibonacci numbers)][[role green 1.00[br](10603742ns)]][[role red 5.11[br](54200684ns)]][[role red 5.92[br](62735542ns)]][[role red 3.95[br](41907607ns)]][[role blue 1.73[br](18291946ns)]][[role red 5.92[br](62759901ns)]]]
[[gcd<boost::multiprecision::uint512_t> (permutations of Fibonacci numbers)][[role green 1.00[br](5860606852ns)]][[role blue 1.32[br](7728722262ns)]][[role red 6.26[br](36672119649ns)]][[role red 2.05[br](12000430526ns)]][[role blue 1.87[br](10957442182ns)]][[role blue 1.42[br](8338173362ns)]]]
[[gcd<boost::multiprecision::uint512_t> (random prime number products)][[role green 1.00[br](1149285ns)]][[role blue 1.66[br](1910911ns)]][[role red 10.39[br](11943413ns)]][[role red 2.46[br](2830809ns)]][[role red 2.99[br](3438268ns)]][[role blue 1.83[br](2097821ns)]]]
[[gcd<boost::multiprecision::uint512_t> (uniform random numbers)][[role green 1.00[br](20626676ns)]][[role red 2.64[br](54415409ns)]][[role red 4.75[br](97897985ns)]][[role red 3.29[br](67763507ns)]][[role blue 1.53[br](31637289ns)]][[role red 2.75[br](56745031ns)]]]
[[gcd<unsigned long long> (Trivial cases)][[role green 1.09[br](380ns)]][[role green 1.14[br](399ns)]][[role green 1.00[br](349ns)]][[role green 1.11[br](388ns)]][[role green 1.09[br](382ns)]][[role green 1.09[br](380ns)]]]
[[gcd<unsigned long long> (adjacent Fibonacci numbers)][[role blue 1.74[br](22176ns)]][[role red 7.59[br](96816ns)]][[role green 1.04[br](13309ns)]][[role blue 1.74[br](22173ns)]][[role green 1.00[br](12750ns)]][[role red 7.21[br](91885ns)]]]
[[gcd<unsigned long long> (permutations of Fibonacci numbers)][[role green 1.00[br](1221766ns)]][[role blue 1.48[br](1807486ns)]][[role green 1.14[br](1395171ns)]][[role green 1.04[br](1271944ns)]][[role red 2.40[br](2933294ns)]][[role blue 1.43[br](1741131ns)]]]
[[gcd<unsigned long long> (random prime number products)][[role green 1.14[br](263894ns)]][[role red 2.07[br](477276ns)]][[role green 1.00[br](230568ns)]][[role green 1.08[br](248254ns)]][[role red 2.30[br](530956ns)]][[role red 2.12[br](489669ns)]]]
[[gcd<unsigned long long> (uniform random numbers)][[role blue 1.69[br](493158ns)]][[role red 2.91[br](846524ns)]][[role green 1.00[br](291345ns)]][[role blue 1.70[br](495636ns)]][[role red 2.50[br](729527ns)]][[role red 2.86[br](833672ns)]]]
[[gcd<unsigned long> (Trivial cases)][[role green 1.08[br](382ns)]][[role green 1.13[br](398ns)]][[role green 1.00[br](353ns)]][[role green 1.08[br](382ns)]][[role green 1.10[br](387ns)]][[role green 1.07[br](377ns)]]]
[[gcd<unsigned long> (adjacent Fibonacci numbers)][[role blue 1.76[br](21777ns)]][[role red 7.68[br](94907ns)]][[role green 1.07[br](13260ns)]][[role blue 1.74[br](21476ns)]][[role green 1.00[br](12354ns)]][[role red 7.29[br](90049ns)]]]
[[gcd<unsigned long> (permutations of Fibonacci numbers)][[role green 1.00[br](1235993ns)]][[role blue 1.50[br](1848327ns)]][[role green 1.19[br](1472470ns)]][[role green 1.04[br](1279710ns)]][[role red 2.46[br](3040947ns)]][[role blue 1.44[br](1781416ns)]]]
[[gcd<unsigned long> (random prime number products)][[role green 1.13[br](274762ns)]][[role red 2.03[br](494734ns)]][[role green 1.00[br](243421ns)]][[role green 1.07[br](261272ns)]][[role red 2.26[br](550984ns)]][[role blue 1.97[br](478381ns)]]]
[[gcd<unsigned long> (uniform random numbers)][[role blue 1.70[br](501279ns)]][[role red 2.86[br](842495ns)]][[role green 1.00[br](295074ns)]][[role blue 1.67[br](494223ns)]][[role red 2.43[br](717178ns)]][[role red 2.90[br](854718ns)]]]
[[gcd<unsigned short> (Trivial cases)][[role green 1.07[br](358ns)]][[role green 1.05[br](350ns)]][[role green 1.07[br](357ns)]][[role green 1.10[br](369ns)]][[role green 1.14[br](381ns)]][[role green 1.00[br](334ns)]]]
[[gcd<unsigned short> (adjacent Fibonacci numbers)][[role green 1.07[br](1131ns)]][[role red 3.25[br](3432ns)]][[role green 1.00[br](1057ns)]][[role green 1.06[br](1116ns)]][[role blue 1.24[br](1306ns)]][[role red 2.95[br](3120ns)]]]
[[gcd<unsigned short> (permutations of Fibonacci numbers)][[role green 1.00[br](18455ns)]][[role blue 1.54[br](28458ns)]][[role green 1.15[br](21253ns)]][[role blue 1.29[br](23735ns)]][[role blue 1.77[br](32640ns)]][[role blue 1.45[br](26779ns)]]]
[[gcd<unsigned short> (random prime number products)][[role green 1.00[br](74261ns)]][[role blue 1.47[br](109229ns)]][[role green 1.13[br](83929ns)]][[role green 1.00[br](74368ns)]][[role red 2.55[br](189130ns)]][[role blue 1.46[br](108614ns)]]]
[[gcd<unsigned short> (uniform random numbers)][[role green 1.05[br](98813ns)]][[role blue 1.65[br](154744ns)]][[role green 1.08[br](100929ns)]][[role green 1.00[br](93744ns)]][[role red 2.39[br](224169ns)]][[role blue 1.61[br](151037ns)]]]
[[gcd<unsigned> (Trivial cases)][[role green 1.03[br](344ns)]][[role green 1.00[br](333ns)]][[role green 1.05[br](349ns)]][[role green 1.17[br](391ns)]][[role blue 1.23[br](409ns)]][[role green 1.04[br](345ns)]]]
[[gcd<unsigned> (adjacent Fibonacci numbers)][[role blue 1.29[br](4355ns)]][[role red 4.98[br](16847ns)]][[role green 1.00[br](3386ns)]][[role blue 1.20[br](4077ns)]][[role green 1.05[br](3543ns)]][[role red 4.78[br](16178ns)]]]
[[gcd<unsigned> (permutations of Fibonacci numbers)][[role green 1.05[br](145315ns)]][[role blue 1.68[br](232879ns)]][[role blue 1.35[br](187305ns)]][[role green 1.00[br](138730ns)]][[role red 2.77[br](384819ns)]][[role blue 1.61[br](223908ns)]]]
[[gcd<unsigned> (random prime number products)][[role green 1.04[br](129896ns)]][[role blue 1.86[br](231942ns)]][[role green 1.09[br](136790ns)]][[role green 1.00[br](124969ns)]][[role red 2.36[br](295192ns)]][[role blue 1.89[br](236358ns)]]]
[[gcd<unsigned> (uniform random numbers)][[role blue 1.36[br](222042ns)]][[role red 2.12[br](345828ns)]][[role green 1.00[br](163385ns)]][[role blue 1.28[br](209481ns)]][[role red 2.39[br](390325ns)]][[role red 2.18[br](356622ns)]]]
]
]

[template table_Polynomial_Arithmetic_Clang_version_9_0_0_tags_RELEASE_900_final_linux_[]
[table:table_Polynomial_Arithmetic_Clang_version_9_0_0_tags_RELEASE_900_final_linux_ Polynomial Arithmetic (Clang version 9.0.0 (tags/RELEASE_900/final), linux)
[[Function][std::uint64_t][double][cpp_int]]
[[operator *][[role blue 1.61[br](502ns)]][[role green 1.00[br](312ns)]][[role red 34.02[br](10615ns)]]]
[[operator * (int)][[role green 1.09[br](118ns)]][[role green 1.00[br](108ns)]][[role red 10.12[br](1093ns)]]]
[[operator *=][[role blue 1.73[br](215039ns)]][[role green 1.00[br](124223ns)]][[role red 34.02[br](4226250ns)]]]
[[operator *= (int)][[role green 1.03[br](14581ns)]][[role green 1.00[br](14118ns)]][[role red 26.28[br](371028ns)]]]
[[operator +][[role green 1.16[br](181ns)]][[role green 1.00[br](156ns)]][[role red 8.81[br](1374ns)]]]
[[operator + (int)][[role green 1.04[br](94ns)]][[role green 1.00[br](90ns)]][[role red 6.01[br](541ns)]]]
[[operator +=][[role green 1.06[br](17ns)]][[role green 1.00[br](16ns)]][[role red 35.06[br](561ns)]]]
[[operator += (int)][[role green 1.20[br](6ns)]][[role green 1.00[br](5ns)]][[role red 26.40[br](132ns)]]]
[[operator -][[role green 1.06[br](165ns)]][[role green 1.00[br](156ns)]][[role red 9.38[br](1464ns)]]]
[[operator - (int)][[role green 1.04[br](95ns)]][[role green 1.00[br](91ns)]][[role red 5.86[br](533ns)]]]
[[operator -=][[role green 1.00[br](16ns)]][[role green 1.00[br](16ns)]][[role red 36.56[br](585ns)]]]
[[operator -= (int)][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role red 20.83[br](125ns)]]]
[[operator /][[role red 2.50[br](1065ns)]][[role green 1.00[br](426ns)]][[role red 56.04[br](23872ns)]]]
[[operator / (int)][[role green 1.12[br](122ns)]][[role green 1.00[br](109ns)]][[role red 17.12[br](1866ns)]]]
[[operator /=][[role blue 1.36[br](15ns)]][[role green 1.00[br](11ns)]][[role red 236.27[br](2599ns)]]]
[[operator /= (int)][[role green 1.00[br](643ns)]][[role red 23.58[br](15164ns)]][[role red 4107.46[br](2641094ns)]]]
]
]

[template table_Rational_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[table:table_Rational_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Rational Method Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role blue 2.00[br](24ns)]][[role red 2.08[br](25ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]]]
[[Order  3][[role blue 1.58[br](30ns)]][[role blue 1.89[br](36ns)]][[role blue 1.47[br](28ns)]][[role blue 1.53[br](29ns)]][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]][[role blue 1.58[br](30ns)]]]
[[Order  4][[role blue 1.48[br](37ns)]][[role blue 1.88[br](47ns)]][[role blue 1.44[br](36ns)]][[role blue 1.52[br](38ns)]][[role green 1.04[br](26ns)]][[role green 1.00[br](25ns)]][[role blue 1.24[br](31ns)]][[role green 1.04[br](26ns)]]]
[[Order  5][[role green 1.00[br](44ns)]][[role blue 1.32[br](58ns)]][[role green 1.11[br](49ns)]][[role red 2.16[br](95ns)]][[role green 1.18[br](52ns)]][[role blue 1.98[br](87ns)]][[role blue 1.25[br](55ns)]][[role blue 2.00[br](88ns)]]]
[[Order  6][[role green 1.00[br](53ns)]][[role blue 1.30[br](69ns)]][[role blue 1.45[br](77ns)]][[role red 2.21[br](117ns)]][[role blue 1.45[br](77ns)]][[role blue 1.64[br](87ns)]][[role blue 1.51[br](80ns)]][[role blue 1.72[br](91ns)]]]
[[Order  7][[role green 1.00[br](81ns)]][[role blue 1.36[br](110ns)]][[role green 1.10[br](89ns)]][[role blue 1.78[br](144ns)]][[role green 1.09[br](88ns)]][[role blue 1.26[br](102ns)]][[role green 1.04[br](84ns)]][[role blue 1.25[br](101ns)]]]
[[Order  8][[role green 1.01[br](88ns)]][[role blue 1.32[br](115ns)]][[role green 1.15[br](100ns)]][[role blue 1.90[br](165ns)]][[role green 1.11[br](97ns)]][[role blue 1.25[br](109ns)]][[role green 1.00[br](87ns)]][[role blue 1.22[br](106ns)]]]
[[Order  9][[role green 1.00[br](91ns)]][[role blue 1.37[br](125ns)]][[role blue 1.30[br](118ns)]][[role red 2.08[br](189ns)]][[role green 1.15[br](105ns)]][[role blue 1.31[br](119ns)]][[role green 1.01[br](92ns)]][[role blue 1.21[br](110ns)]]]
[[Order 10][[role green 1.00[br](94ns)]][[role blue 1.30[br](122ns)]][[role blue 1.39[br](131ns)]][[role red 2.29[br](215ns)]][[role blue 1.20[br](113ns)]][[role blue 1.44[br](135ns)]][[role green 1.05[br](99ns)]][[role blue 1.23[br](116ns)]]]
[[Order 11][[role green 1.03[br](108ns)]][[role blue 1.35[br](142ns)]][[role blue 1.44[br](151ns)]][[role red 2.25[br](236ns)]][[role blue 1.23[br](129ns)]][[role blue 1.40[br](147ns)]][[role green 1.00[br](105ns)]][[role blue 1.24[br](130ns)]]]
[[Order 12][[role green 1.03[br](120ns)]][[role blue 1.33[br](154ns)]][[role blue 1.46[br](169ns)]][[role red 2.27[br](263ns)]][[role green 1.16[br](134ns)]][[role blue 1.39[br](161ns)]][[role green 1.00[br](116ns)]][[role blue 1.27[br](147ns)]]]
[[Order 13][[role green 1.07[br](129ns)]][[role blue 1.54[br](186ns)]][[role blue 1.49[br](180ns)]][[role red 2.41[br](292ns)]][[role blue 1.24[br](150ns)]][[role blue 1.45[br](176ns)]][[role green 1.00[br](121ns)]][[role blue 1.40[br](170ns)]]]
[[Order 14][[role green 1.00[br](139ns)]][[role blue 1.32[br](183ns)]][[role blue 1.48[br](206ns)]][[role red 2.22[br](308ns)]][[role blue 1.26[br](175ns)]][[role blue 1.37[br](191ns)]][[role green 1.01[br](140ns)]][[role green 1.16[br](161ns)]]]
[[Order 15][[role green 1.04[br](147ns)]][[role blue 1.45[br](206ns)]][[role blue 1.61[br](229ns)]][[role red 2.39[br](340ns)]][[role blue 1.20[br](171ns)]][[role blue 1.41[br](200ns)]][[role green 1.00[br](142ns)]][[role green 1.18[br](167ns)]]]
[[Order 16][[role green 1.03[br](157ns)]][[role blue 1.40[br](214ns)]][[role blue 1.52[br](233ns)]][[role red 2.33[br](357ns)]][[role blue 1.21[br](185ns)]][[role blue 1.49[br](228ns)]][[role green 1.00[br](153ns)]][[role green 1.18[br](180ns)]]]
[[Order 17][[role green 1.02[br](179ns)]][[role blue 1.25[br](218ns)]][[role blue 1.45[br](253ns)]][[role red 2.17[br](380ns)]][[role green 1.16[br](203ns)]][[role blue 1.36[br](238ns)]][[role green 1.00[br](175ns)]][[role green 1.10[br](192ns)]]]
[[Order 18][[role blue 1.21[br](201ns)]][[role blue 1.40[br](232ns)]][[role blue 1.64[br](272ns)]][[role red 2.42[br](402ns)]][[role blue 1.24[br](206ns)]][[role blue 1.60[br](266ns)]][[role green 1.00[br](166ns)]][[role blue 1.20[br](200ns)]]]
[[Order 19][[role blue 1.24[br](211ns)]][[role blue 1.48[br](252ns)]][[role blue 1.74[br](295ns)]][[role red 2.49[br](423ns)]][[role blue 1.41[br](240ns)]][[role blue 1.59[br](271ns)]][[role green 1.00[br](170ns)]][[role blue 1.21[br](205ns)]]]
[[Order 20][[role blue 1.27[br](223ns)]][[role blue 1.67[br](294ns)]][[role blue 1.86[br](327ns)]][[role red 2.70[br](475ns)]][[role blue 1.34[br](235ns)]][[role blue 1.56[br](274ns)]][[role green 1.00[br](176ns)]][[role blue 1.21[br](213ns)]]]
]
]

[template table_Polynomial_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[table:table_Polynomial_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Polynomial Method Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role green 1.20[br](6ns)]][[role green 1.20[br](6ns)]][[role green 1.20[br](6ns)]][[role green 1.20[br](6ns)]][[role green 1.00[br](5ns)]][[role green 1.00[br](5ns)]]]
[[Order  3][[role blue 2.00[br](16ns)]][[role red 3.00[br](24ns)]][[role green 1.12[br](9ns)]][[role green 1.12[br](9ns)]][[role green 1.12[br](9ns)]][[role blue 1.25[br](10ns)]][[role green 1.00[br](8ns)]][[role green 1.00[br](8ns)]]]
[[Order  4][[role blue 1.83[br](22ns)]][[role red 2.67[br](32ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]][[role green 1.08[br](13ns)]][[role green 1.08[br](13ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]]]
[[Order  5][[role green 1.12[br](19ns)]][[role blue 1.71[br](29ns)]][[role green 1.00[br](17ns)]][[role green 1.00[br](17ns)]][[role green 1.06[br](18ns)]][[role green 1.12[br](19ns)]][[role green 1.00[br](17ns)]][[role green 1.00[br](17ns)]]]
[[Order  6][[role blue 1.45[br](29ns)]][[role red 2.05[br](41ns)]][[role blue 1.30[br](26ns)]][[role blue 1.30[br](26ns)]][[role green 1.10[br](22ns)]][[role green 1.10[br](22ns)]][[role green 1.00[br](20ns)]][[role green 1.05[br](21ns)]]]
[[Order  7][[role green 1.15[br](31ns)]][[role red 2.22[br](60ns)]][[role blue 1.22[br](33ns)]][[role blue 1.22[br](33ns)]][[role green 1.07[br](29ns)]][[role green 1.07[br](29ns)]][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]]]
[[Order  8][[role blue 1.32[br](37ns)]][[role red 2.18[br](61ns)]][[role blue 1.43[br](40ns)]][[role blue 1.36[br](38ns)]][[role green 1.07[br](30ns)]][[role green 1.07[br](30ns)]][[role green 1.00[br](28ns)]][[role green 1.04[br](29ns)]]]
[[Order  9][[role green 1.17[br](42ns)]][[role blue 1.72[br](62ns)]][[role blue 1.36[br](49ns)]][[role blue 1.36[br](49ns)]][[role green 1.06[br](38ns)]][[role green 1.14[br](41ns)]][[role green 1.00[br](36ns)]][[role green 1.00[br](36ns)]]]
[[Order 10][[role blue 1.33[br](48ns)]][[role red 2.19[br](79ns)]][[role blue 1.64[br](59ns)]][[role blue 1.64[br](59ns)]][[role green 1.11[br](40ns)]][[role green 1.08[br](39ns)]][[role green 1.00[br](36ns)]][[role green 1.00[br](36ns)]]]
[[Order 11][[role blue 1.32[br](58ns)]][[role red 2.02[br](89ns)]][[role blue 1.66[br](73ns)]][[role blue 1.68[br](74ns)]][[role green 1.09[br](48ns)]][[role green 1.05[br](46ns)]][[role green 1.05[br](46ns)]][[role green 1.00[br](44ns)]]]
[[Order 12][[role blue 1.42[br](64ns)]][[role red 2.38[br](107ns)]][[role blue 1.89[br](85ns)]][[role blue 1.84[br](83ns)]][[role green 1.04[br](47ns)]][[role green 1.04[br](47ns)]][[role green 1.00[br](45ns)]][[role green 1.00[br](45ns)]]]
[[Order 13][[role blue 1.51[br](71ns)]][[role red 2.21[br](104ns)]][[role blue 1.79[br](84ns)]][[role blue 1.83[br](86ns)]][[role green 1.06[br](50ns)]][[role green 1.11[br](52ns)]][[role green 1.00[br](47ns)]][[role green 1.00[br](47ns)]]]
[[Order 14][[role blue 1.52[br](79ns)]][[role red 2.38[br](124ns)]][[role red 2.02[br](105ns)]][[role blue 1.96[br](102ns)]][[role green 1.08[br](56ns)]][[role green 1.08[br](56ns)]][[role green 1.00[br](52ns)]][[role green 1.00[br](52ns)]]]
[[Order 15][[role blue 1.25[br](91ns)]][[role blue 1.92[br](140ns)]][[role blue 1.56[br](114ns)]][[role blue 1.60[br](117ns)]][[role green 1.07[br](78ns)]][[role green 1.11[br](81ns)]][[role green 1.01[br](74ns)]][[role green 1.00[br](73ns)]]]
[[Order 16][[role blue 1.59[br](100ns)]][[role red 2.51[br](158ns)]][[role blue 1.97[br](124ns)]][[role blue 1.98[br](125ns)]][[role green 1.05[br](66ns)]][[role green 1.06[br](67ns)]][[role green 1.00[br](63ns)]][[role green 1.00[br](63ns)]]]
[[Order 17][[role blue 1.29[br](108ns)]][[role blue 1.90[br](160ns)]][[role blue 1.36[br](114ns)]][[role red 4.58[br](385ns)]][[role green 1.15[br](97ns)]][[role red 2.05[br](172ns)]][[role green 1.00[br](84ns)]][[role blue 1.63[br](137ns)]]]
[[Order 18][[role red 2.22[br](120ns)]][[role red 3.22[br](174ns)]][[role red 2.28[br](123ns)]][[role red 7.74[br](418ns)]][[role green 1.06[br](57ns)]][[role red 2.85[br](154ns)]][[role green 1.00[br](54ns)]][[role red 3.78[br](204ns)]]]
[[Order 19][[role red 2.28[br](130ns)]][[role red 3.28[br](187ns)]][[role red 2.42[br](138ns)]][[role red 7.74[br](441ns)]][[role green 1.07[br](61ns)]][[role red 3.58[br](204ns)]][[role green 1.00[br](57ns)]][[role red 2.67[br](152ns)]]]
[[Order 20][[role red 2.43[br](146ns)]][[role red 3.35[br](201ns)]][[role red 2.53[br](152ns)]][[role red 8.17[br](490ns)]][[role green 1.07[br](64ns)]][[role red 4.12[br](247ns)]][[role green 1.00[br](60ns)]][[role red 2.90[br](174ns)]]]
]
]

[template table_Library_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[table:table_Library_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Library Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux
[[Function][boost 1.73][boost 1.73[br]promote_double<false>][tr1/cmath][GSL 2.5][Rmath 3.6.3][math.h]]
[[assoc_laguerre[br](2240/2240 tests selected)][[role blue 1.56[br](229ns)]][[role green 1.18[br](174ns)]][[role green 1.00[br](147ns)]][[role green 1.18[br](173ns)]][[role grey -]][[role grey -]]]
[[assoc_legendre[br](110/400 tests selected)][[role red 6.88[br](275ns)]][[role red 2.40[br](96ns)]][[role green 1.00[br](40ns)]][[role red 2.98[br](119ns)]][[role grey -]][[role grey -]]]
[[beta (incomplete)[br](2682/3210 tests selected)][[role red 5.44[br](2267ns)]][[role green 1.00[br](417ns)]][[role grey -]][[role blue 1.50[br](626ns)]][[role grey -]][[role grey -]]]
[[beta[br](2203/2204 tests selected)][[role red 6.09[br](792ns)]][[role green 1.00[br](130ns)]][[role green 1.12[br](146ns)]][[role red 2.52[br](327ns)]][[role blue 1.97[br](256ns)]][[role grey -]]]
[[cbrt[br](85/85 tests selected)][[role blue 1.93[br](54ns)]][[role green 1.00[br](28ns)]][[role green 1.14[br](32ns)]][[role grey -]][[role grey -]][[role green 1.11[br](31ns)]]]
[[cyl_bessel_i (integer order)[br](494/526 tests selected)][[role red 5.87[br](886ns)]][[role green 1.19[br](179ns)]][[role green 1.00[br](151ns)]][[role blue 1.89[br](286ns)]][[role red 5.61[br](847ns)]][[role grey -]]]
[[cyl_bessel_i[br](177/240 tests selected)][[role red 13.08[br](2511ns)]][[role red 2.11[br](406ns)]][[role green 1.00[br](192ns)]][[role red 3.57[br](686ns)]][[role red 7.41[br](1422ns)]][[role grey -]]]
[[cyl_bessel_j (integer order)[br](251/268 tests selected)][[role red 4.73[br](487ns)]][[role green 1.00[br](103ns)]][[role red 5.64[br](581ns)]][[role red 3.75[br](386ns)]][[role red 4.28[br](441ns)]][[role blue 1.46[br](150ns)]]]
[[cyl_bessel_j[br](433/451 tests selected)][[role red 3.69[br](1327ns)]][[role blue 1.21[br](436ns)]][[role green 1.00[br](360ns)]][[role blue 1.62[br](583ns)]][[role green 1.06[br](383ns)]][[role grey -]]]
[[cyl_bessel_k (integer order)[br](505/508 tests selected)][[role red 15.59[br](3382ns)]][[role green 1.00[br](217ns)]][[role red 15.18[br](3294ns)]][[role blue 1.22[br](265ns)]][[role blue 1.28[br](277ns)]][[role grey -]]]
[[cyl_bessel_k[br](96/279 tests selected)][[role red 10.22[br](4414ns)]][[role blue 1.35[br](582ns)]][[role blue 1.64[br](709ns)]][[role green 1.03[br](445ns)]][[role green 1.00[br](432ns)]][[role grey -]]]
[[cyl_neumann (integer order)[br](424/428 tests selected)][[role red 3.90[br](570ns)]][[role green 1.00[br](146ns)]][[role red 4.25[br](620ns)]][[role red 3.47[br](507ns)]][[role red 5.86[br](856ns)]][[role blue 1.35[br](197ns)]]]
[[cyl_neumann[br](428/450 tests selected)][[role red 25.16[br](13059ns)]][[role red 10.16[br](5275ns)]][[role green 1.17[br](609ns)]][[role blue 1.28[br](664ns)]][[role green 1.00[br](519ns)]][]]
[[digamma[br](1019/1019 tests selected)][[role red 2.33[br](84ns)]][[role green 1.00[br](36ns)]][[role grey -]][[role red 2.94[br](106ns)]][[role red 6.19[br](223ns)]][[role grey -]]]
[[ellint_1 (complete)[br](109/109 tests selected)][[role red 5.22[br](120ns)]][[role green 1.00[br](23ns)]][[role red 4.30[br](99ns)]][[role red 8.26[br](190ns)]][[role grey -]][[role grey -]]]
[[ellint_1[br](627/629 tests selected)][[role red 5.58[br](954ns)]][[role green 1.00[br](171ns)]][[role blue 1.22[br](208ns)]][[role blue 1.92[br](329ns)]][[role grey -]][[role grey -]]]
[[ellint_2 (complete)[br](109/110 tests selected)][[role red 5.00[br](150ns)]][[role green 1.00[br](30ns)]][[role red 7.47[br](224ns)]][[role red 14.77[br](443ns)]][[role grey -]][[role grey -]]]
[[ellint_2[br](527/530 tests selected)][[role red 6.59[br](1655ns)]][[role green 1.00[br](251ns)]][[role blue 1.27[br](318ns)]][[role blue 1.97[br](494ns)]][[role grey -]][[role grey -]]]
[[ellint_3 (complete)[br](0/500 tests selected)][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role grey -]][[role grey -]]]
[[ellint_3[br](22/845 tests selected)][[role red 4.07[br](998ns)]][[role green 1.00[br](245ns)]][[role green 1.06[br](259ns)]][[role green 1.20[br](294ns)]][[role grey -]][[role grey -]]]
[[ellint_rc[br](201/201 tests selected)][[role red 3.32[br](113ns)]][[role green 1.00[br](34ns)]][[role grey -]][[role red 4.68[br](159ns)]][[role grey -]][[role grey -]]]
[[ellint_rd[br](7588/7588 tests selected)][[role red 7.08[br](906ns)]][[role green 1.00[br](128ns)]][[role grey -]][[role blue 1.84[br](236ns)]][[role grey -]][[role grey -]]]
[[ellint_rf[br](7788/7788 tests selected)][[role red 3.49[br](157ns)]][[role green 1.00[br](45ns)]][[role grey -]][[role red 5.31[br](239ns)]][[role grey -]][[role grey -]]]
[[ellint_rj[br](7642/8032 tests selected)][[role red 5.48[br](740ns)]][[role green 1.00[br](135ns)]][[role grey -]][[role red 11.13[br](1503ns)]][[role grey -]][[role grey -]]]
[[erf[br](950/950 tests selected)][[role blue 1.83[br](44ns)]][[role grey -]][[role green 1.00[br](24ns)]][[role red 4.08[br](98ns)]][[role grey -]][[role green 1.00[br](24ns)]]]
[[erfc[br](950/950 tests selected)][[role red 4.75[br](133ns)]][[role red 2.43[br](68ns)]][[role green 1.00[br](28ns)]][[role red 3.18[br](89ns)]][[role grey -]][[role green 1.04[br](29ns)]]]
[[expint (En)[br](1059/1059 tests selected)][[role red 2.32[br](276ns)]][[role green 1.00[br](119ns)]][[role grey -]][[role red 3.72[br](443ns)]][[role grey -]][[role grey -]]]
[[expint[br](436/436 tests selected)][[role red 2.21[br](84ns)]][[role green 1.00[br](38ns)]][[role red 2.68[br](102ns)]][[role red 4.00[br](152ns)]][[role grey -]][[role grey -]]]
[[expm1[br](80/80 tests selected)][[role green 1.07[br](15ns)]][[role green 1.00[br](14ns)]][[role green 1.07[br](15ns)]][[role grey -]][[role grey -]][[role green 1.07[br](15ns)]]]
[[gamma_p[br](1379/1379 tests selected)][[role red 3.07[br](749ns)]][[role green 1.04[br](254ns)]][[role grey -]][[role blue 1.69[br](413ns)]][[role green 1.00[br](244ns)]][[role grey -]]]
[[gamma_p_inv[br](559/559 tests selected)][[role red 2.56[br](2676ns)]][[role green 1.00[br](1045ns)]][[role grey -]][[role grey -]][[role green 1.14[br](1196ns)]][[role grey -]]]
[[gamma_q[br](1371/1379 tests selected)][[role red 3.46[br](831ns)]][[role green 1.17[br](280ns)]][[role grey -]][[role red 2.05[br](493ns)]][[role green 1.00[br](240ns)]][[role grey -]]]
[[gamma_q_inv[br](78/559 tests selected)][[role red 3.08[br](2381ns)]][[role blue 1.24[br](962ns)]][[role grey -]][[role grey -]][[role green 1.00[br](773ns)]][[role grey -]]]
[[ibeta[br](3210/3210 tests selected)][[role red 5.41[br](1996ns)]][[role green 1.18[br](434ns)]][[role grey -]][[role grey -]][[role green 1.00[br](369ns)]][[role grey -]]]
[[ibeta_inv[br](1204/1210 tests selected)][[role red 3.38[br](6013ns)]][[role green 1.00[br](1781ns)]][[role grey -]][[role grey -]][[role red 2.85[br](5070ns)]][[role grey -]]]
[[ibetac[br](3210/3210 tests selected)][[role red 5.19[br](2008ns)]][[role green 1.20[br](463ns)]][[role grey -]][[role grey -]][[role green 1.00[br](387ns)]][[role grey -]]]
[[ibetac_inv[br](1201/1210 tests selected)][[role red 3.43[br](5915ns)]][[role green 1.00[br](1722ns)]][[role grey -]][[role grey -]][[role red 2.68[br](4623ns)]][[role grey -]]]
[[jacobi_cn[br](2368/2757 tests selected)][[role red 9.19[br](643ns)]][[role red 2.97[br](208ns)]][[role grey -]][[role green 1.00[br](70ns)]][[role grey -]][[role grey -]]]
[[jacobi_dn[br](2368/2757 tests selected)][[role red 8.42[br](648ns)]][[role red 2.68[br](206ns)]][[role grey -]][[role green 1.00[br](77ns)]][[role grey -]][[role grey -]]]
[[jacobi_sn[br](2368/2757 tests selected)][[role red 8.77[br](640ns)]][[role red 2.90[br](212ns)]][[role grey -]][[role green 1.00[br](73ns)]][[role grey -]][[role grey -]]]
[[laguerre[br](280/280 tests selected)][[role blue 1.27[br](117ns)]][[role green 1.00[br](92ns)]][[role green 1.13[br](104ns)]][[role blue 1.30[br](120ns)]][[role grey -]][[role grey -]]]
[[legendre Q[br](300/300 tests selected)][[role blue 1.21[br](404ns)]][[role green 1.07[br](358ns)]][[role grey -]][[role green 1.00[br](335ns)]][[role grey -]][[role grey -]]]
[[legendre[br](300/300 tests selected)][[role green 1.03[br](304ns)]][[role green 1.11[br](326ns)]][[role green 1.00[br](295ns)]][[role green 1.09[br](323ns)]][[role grey -]][[role grey -]]]
[[lgamma[br](400/400 tests selected)][[role red 7.28[br](386ns)]][[role red 2.34[br](124ns)]][[role green 1.09[br](58ns)]][[role red 4.34[br](230ns)]][[role red 2.15[br](114ns)]][[role green 1.00[br](53ns)]]]
[[log1p[br](80/80 tests selected)][[role green 1.06[br](19ns)]][[role green 1.00[br](18ns)]][[role green 1.11[br](20ns)]][[role grey -]][[role grey -]][[role green 1.00[br](18ns)]]]
[[polygamma[br](823/1535 tests selected)][[role red 9.49[br](3491ns)]][[role blue 1.45[br](532ns)]][[role grey -]][[role blue 1.60[br](590ns)]][[role green 1.00[br](368ns)]][[role grey -]]]
[[sph_bessel[br](483/483 tests selected)][[role red 2.34[br](1574ns)]][[role green 1.00[br](673ns)]][[role red 2.75[br](1848ns)]][[role blue 1.77[br](1192ns)]][[role grey -]][[role grey -]]]
[[sph_neumann[br](284/284 tests selected)][[role red 8.02[br](3009ns)]][[role red 2.70[br](1012ns)]][[role red 6.62[br](2483ns)]][[role green 1.00[br](375ns)]][[role grey -]][]]
[[tgamma (incomplete)[br](1266/1379 tests selected)][[role red 3.41[br](792ns)]][[role green 1.00[br](232ns)]][[role grey -]][[role red 2.13[br](494ns)]][[role grey -]][[role grey -]]]
[[tgamma[br](400/400 tests selected)][[role red 6.63[br](570ns)]][[role blue 1.22[br](105ns)]][[role blue 1.35[br](116ns)]][[role green 1.15[br](99ns)]][[role green 1.00[br](86ns)]][[role blue 1.36[br](117ns)]]]
[[trigamma[br](659/659 tests selected)][[role red 2.67[br](48ns)]][[role green 1.00[br](18ns)]][[role grey -]][[role red 32.83[br](591ns)]][[role red 15.83[br](285ns)]][[role grey -]]]
[[zeta[br](448/448 tests selected)][[role red 4.79[br](623ns)]][[role green 1.00[br](130ns)]][[role red 877.58[br](114085ns)]][[role blue 1.65[br](214ns)]][][]]
]
]

[template table_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_1_20191008_on_linux[]
[table:table_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_1_20191008_on_linux Distribution performance comparison for different performance options with GNU C++ version 9.2.1 20191008 on linux
[[Function][boost 1.73][Boost[br]promote_double<false>][Boost[br]promote_double<false>[br]digits10<10>][Boost[br]float[br]promote_float<false>]]
[[ArcSine (CDF)][[role blue 1.75[br](35ns)]][[role blue 1.65[br](33ns)]][[role blue 1.65[br](33ns)]][[role green 1.00[br](20ns)]]]
[[ArcSine (PDF)][[role green 1.00[br](5ns)]][[role green 1.00[br](5ns)]][[role green 1.00[br](5ns)]][[role green 1.20[br](6ns)]]]
[[ArcSine (quantile)][[role green 1.04[br](24ns)]][[role green 1.00[br](23ns)]][[role green 1.04[br](24ns)]][[role green 1.00[br](23ns)]]]
[[Beta (CDF)][[role red 4.41[br](437ns)]][[role blue 1.40[br](139ns)]][[role blue 1.38[br](137ns)]][[role green 1.00[br](99ns)]]]
[[Beta (PDF)][[role red 4.29[br](360ns)]][[role blue 1.23[br](103ns)]][[role blue 1.24[br](104ns)]][[role green 1.00[br](84ns)]]]
[[Beta (quantile)][[role red 4.17[br](2587ns)]][[role blue 1.60[br](991ns)]][[role blue 1.44[br](893ns)]][[role green 1.00[br](620ns)]]]
[[Binomial (CDF)][[role red 3.97[br](837ns)]][[role blue 1.54[br](324ns)]][[role blue 1.26[br](265ns)]][[role green 1.00[br](211ns)]]]
[[Binomial (PDF)][[role red 2.90[br](322ns)]][[role green 1.09[br](121ns)]][[role green 1.09[br](121ns)]][[role green 1.00[br](111ns)]]]
[[Binomial (quantile)][[role red 3.88[br](3917ns)]][[role blue 1.53[br](1548ns)]][[role blue 1.26[br](1273ns)]][[role green 1.00[br](1009ns)]]]
[[Cauchy (CDF)][[role green 1.15[br](23ns)]][[role green 1.15[br](23ns)]][[role green 1.00[br](20ns)]][[role green 1.00[br](20ns)]]]
[[Cauchy (PDF)][[role green 1.00[br](3ns)]][[role green 1.00[br](3ns)]][[role green 1.00[br](3ns)]][[role blue 1.33[br](4ns)]]]
[[Cauchy (quantile)][[role blue 1.88[br](45ns)]][[role blue 2.00[br](48ns)]][[role blue 1.79[br](43ns)]][[role green 1.00[br](24ns)]]]
[[ChiSquared (CDF)][[role red 3.54[br](1002ns)]][[role blue 1.79[br](506ns)]][[role blue 1.40[br](397ns)]][[role green 1.00[br](283ns)]]]
[[ChiSquared (PDF)][[role red 4.84[br](295ns)]][[role blue 1.41[br](86ns)]][[role blue 1.38[br](84ns)]][[role green 1.00[br](61ns)]]]
[[ChiSquared (quantile)][[role red 3.76[br](1664ns)]][[role blue 1.84[br](815ns)]][[role blue 1.37[br](609ns)]][[role green 1.00[br](443ns)]]]
[[Exponential (CDF)][[role blue 1.27[br](19ns)]][[role blue 1.87[br](28ns)]][[role blue 1.60[br](24ns)]][[role green 1.00[br](15ns)]]]
[[Exponential (PDF)][[role blue 1.58[br](30ns)]][[role blue 1.84[br](35ns)]][[role blue 2.00[br](38ns)]][[role green 1.00[br](19ns)]]]
[[Exponential (quantile)][[role green 1.00[br](23ns)]][[role green 1.13[br](26ns)]][[role green 1.17[br](27ns)]][[role green 1.04[br](24ns)]]]
[[ExtremeValue (CDF)][[role blue 1.78[br](57ns)]][[role blue 1.81[br](58ns)]][[role blue 1.81[br](58ns)]][[role green 1.00[br](32ns)]]]
[[ExtremeValue (PDF)][[role blue 1.96[br](90ns)]][[role blue 1.85[br](85ns)]][[role blue 1.83[br](84ns)]][[role green 1.00[br](46ns)]]]
[[ExtremeValue (quantile)][[role blue 1.43[br](53ns)]][[role blue 1.38[br](51ns)]][[role blue 1.43[br](53ns)]][[role green 1.00[br](37ns)]]]
[[F (CDF)][[role red 4.46[br](817ns)]][[role blue 1.58[br](289ns)]][[role blue 1.24[br](227ns)]][[role green 1.00[br](183ns)]]]
[[F (PDF)][[role red 3.37[br](361ns)]][[role green 1.18[br](126ns)]][[role green 1.12[br](120ns)]][[role green 1.00[br](107ns)]]]
[[F (quantile)][[role red 2.70[br](2615ns)]][[role blue 1.28[br](1241ns)]][[role green 1.03[br](995ns)]][[role green 1.00[br](969ns)]]]
[[Gamma (CDF)][[role red 3.90[br](714ns)]][[role blue 1.46[br](267ns)]][[role blue 1.47[br](269ns)]][[role green 1.00[br](183ns)]]]
[[Gamma (PDF)][[role red 5.75[br](437ns)]][[role blue 1.49[br](113ns)]][[role blue 1.51[br](115ns)]][[role green 1.00[br](76ns)]]]
[[Gamma (quantile)][[role red 4.35[br](1827ns)]][[role blue 1.80[br](755ns)]][[role blue 1.22[br](511ns)]][[role green 1.00[br](420ns)]]]
[[Geometric (CDF)][[role green 1.00[br](25ns)]][[role green 1.08[br](27ns)]][[role green 1.08[br](27ns)]][[role green 1.16[br](29ns)]]]
[[Geometric (PDF)][[role blue 1.44[br](23ns)]][[role blue 1.44[br](23ns)]][[role blue 1.56[br](25ns)]][[role green 1.00[br](16ns)]]]
[[Geometric (quantile)][[role green 1.00[br](25ns)]][[role green 1.12[br](28ns)]][[role green 1.04[br](26ns)]][[role blue 1.24[br](31ns)]]]
[[Hypergeometric (CDF)][[role green 1.08[br](66745ns)]][[role green 1.00[br](61922ns)]][[role green 1.02[br](62901ns)]][[role green 1.07[br](66289ns)]]]
[[Hypergeometric (PDF)][[role green 1.16[br](73824ns)]][[role green 1.00[br](63790ns)]][[role green 1.02[br](65130ns)]][[role green 1.09[br](69844ns)]]]
[[Hypergeometric (quantile)][[role green 1.00[br](131293ns)]][[role blue 1.27[br](166268ns)]][[role blue 1.24[br](163105ns)]][[role green 1.14[br](149745ns)]]]
[[InverseChiSquared (CDF)][[role red 3.06[br](1538ns)]][[role blue 1.60[br](803ns)]][[role blue 1.31[br](657ns)]][[role green 1.00[br](502ns)]]]
[[InverseChiSquared (PDF)][[role red 4.42[br](367ns)]][[role blue 1.63[br](135ns)]][[role blue 1.52[br](126ns)]][[role green 1.00[br](83ns)]]]
[[InverseChiSquared (quantile)][[role red 2.92[br](2328ns)]][[role blue 1.66[br](1322ns)]][[role blue 1.21[br](963ns)]][[role green 1.00[br](797ns)]]]
[[InverseGamma (CDF)][[role red 3.17[br](806ns)]][[role blue 1.41[br](359ns)]][[role green 1.18[br](299ns)]][[role green 1.00[br](254ns)]]]
[[InverseGamma (PDF)][[role red 5.26[br](515ns)]][[role blue 1.54[br](151ns)]][[role blue 1.49[br](146ns)]][[role green 1.00[br](98ns)]]]
[[InverseGamma (quantile)][[role red 4.02[br](2187ns)]][[role blue 1.69[br](921ns)]][[role green 1.19[br](645ns)]][[role green 1.00[br](544ns)]]]
[[InverseGaussian (CDF)][[role blue 1.89[br](236ns)]][[role blue 1.94[br](242ns)]][[role blue 1.99[br](249ns)]][[role green 1.00[br](125ns)]]]
[[InverseGaussian (PDF)][[role blue 1.53[br](23ns)]][[role blue 1.53[br](23ns)]][[role blue 1.53[br](23ns)]][[role green 1.00[br](15ns)]]]
[[InverseGaussian (quantile)][[role red 2.09[br](3530ns)]][[role red 2.26[br](3823ns)]][[role red 2.14[br](3611ns)]][[role green 1.00[br](1688ns)]]]
[[Laplace (CDF)][[role blue 1.58[br](41ns)]][[role blue 1.62[br](42ns)]][[role blue 1.62[br](42ns)]][[role green 1.00[br](26ns)]]]
[[Laplace (PDF)][[role blue 1.56[br](42ns)]][[role blue 1.59[br](43ns)]][[role blue 1.59[br](43ns)]][[role green 1.00[br](27ns)]]]
[[Laplace (quantile)][[role blue 1.39[br](39ns)]][[role blue 1.43[br](40ns)]][[role blue 1.36[br](38ns)]][[role green 1.00[br](28ns)]]]
[[LogNormal (CDF)][[role red 2.12[br](225ns)]][[role blue 1.33[br](141ns)]][[role blue 1.36[br](144ns)]][[role green 1.00[br](106ns)]]]
[[LogNormal (PDF)][[role blue 1.68[br](74ns)]][[role blue 1.73[br](76ns)]][[role blue 1.70[br](75ns)]][[role green 1.00[br](44ns)]]]
[[LogNormal (quantile)][[role blue 1.75[br](105ns)]][[role blue 1.68[br](101ns)]][[role blue 1.40[br](84ns)]][[role green 1.00[br](60ns)]]]
[[Logistic (CDF)][[role blue 1.52[br](41ns)]][[role blue 1.52[br](41ns)]][[role blue 1.74[br](47ns)]][[role green 1.00[br](27ns)]]]
[[Logistic (PDF)][[role blue 1.59[br](43ns)]][[role blue 1.59[br](43ns)]][[role blue 1.59[br](43ns)]][[role green 1.00[br](27ns)]]]
[[Logistic (quantile)][[role blue 1.23[br](37ns)]][[role blue 1.23[br](37ns)]][[role blue 1.27[br](38ns)]][[role green 1.00[br](30ns)]]]
[[NegativeBinomial (CDF)][[role red 4.51[br](1354ns)]][[role blue 1.80[br](540ns)]][[role blue 1.37[br](410ns)]][[role green 1.00[br](300ns)]]]
[[NegativeBinomial (PDF)][[role red 3.62[br](445ns)]][[role green 1.07[br](131ns)]][[role green 1.07[br](132ns)]][[role green 1.00[br](123ns)]]]
[[NegativeBinomial (quantile)][[role red 3.26[br](7468ns)]][[role blue 1.27[br](2918ns)]][[role green 1.00[br](2294ns)]][[role green 1.15[br](2649ns)]]]
[[NonCentralBeta (CDF)][[role red 4.88[br](2083ns)]][[role blue 1.80[br](769ns)]][[role blue 1.61[br](689ns)]][[role green 1.00[br](427ns)]]]
[[NonCentralBeta (PDF)][[role red 3.83[br](1265ns)]][[role blue 1.55[br](511ns)]][[role blue 1.42[br](468ns)]][[role green 1.00[br](330ns)]]]
[[NonCentralBeta (quantile)][[role red 7.51[br](55809ns)]][[role red 2.59[br](19273ns)]][[role red 2.26[br](16812ns)]][[role green 1.00[br](7433ns)]]]
[[NonCentralChiSquared (CDF)][[role red 2.90[br](4498ns)]][[role blue 1.82[br](2821ns)]][[role blue 1.43[br](2220ns)]][[role green 1.00[br](1552ns)]]]
[[NonCentralChiSquared (PDF)][[role red 3.52[br](953ns)]][[role blue 1.61[br](436ns)]][[role blue 1.54[br](416ns)]][[role green 1.00[br](271ns)]]]
[[NonCentralChiSquared (quantile)][[role red 4.15[br](31465ns)]][[role red 2.33[br](17712ns)]][[role blue 1.56[br](11870ns)]][[role green 1.00[br](7586ns)]]]
[[NonCentralF (CDF)][[role red 4.26[br](1828ns)]][[role blue 1.55[br](667ns)]][[role blue 1.36[br](584ns)]][[role green 1.00[br](429ns)]]]
[[NonCentralF (PDF)][[role red 3.98[br](1506ns)]][[role blue 1.49[br](564ns)]][[role blue 1.29[br](488ns)]][[role green 1.00[br](378ns)]]]
[[NonCentralF (quantile)][[role red 4.52[br](29414ns)]][[role blue 1.57[br](10228ns)]][[role blue 1.31[br](8543ns)]][[role green 1.00[br](6504ns)]]]
[[NonCentralT (CDF)][[role red 3.79[br](6416ns)]][[role blue 1.72[br](2907ns)]][[role blue 1.52[br](2572ns)]][[role green 1.00[br](1691ns)]]]
[[NonCentralT (PDF)][[role red 3.41[br](4034ns)]][[role blue 1.93[br](2284ns)]][[role blue 1.75[br](2065ns)]][[role green 1.00[br](1182ns)]]]
[[NonCentralT (quantile)][[role red 5.06[br](68590ns)]][[role red 2.17[br](29386ns)]][[role blue 1.65[br](22334ns)]][[role green 1.00[br](13546ns)]]]
[[Normal (CDF)][[role red 2.24[br](150ns)]][[role blue 1.34[br](90ns)]][[role blue 1.28[br](86ns)]][[role green 1.00[br](67ns)]]]
[[Normal (PDF)][[role blue 1.55[br](34ns)]][[role blue 1.50[br](33ns)]][[role blue 1.59[br](35ns)]][[role green 1.00[br](22ns)]]]
[[Normal (quantile)][[role blue 1.68[br](57ns)]][[role green 1.18[br](40ns)]][[role blue 1.24[br](42ns)]][[role green 1.00[br](34ns)]]]
[[Pareto (CDF)][[role blue 1.40[br](49ns)]][[role blue 1.46[br](51ns)]][[role blue 1.40[br](49ns)]][[role green 1.00[br](35ns)]]]
[[Pareto (PDF)][[role blue 1.95[br](86ns)]][[role blue 1.95[br](86ns)]][[role red 2.09[br](92ns)]][[role green 1.00[br](44ns)]]]
[[Pareto (quantile)][[role blue 1.76[br](51ns)]][[role blue 1.79[br](52ns)]][[role blue 1.72[br](50ns)]][[role green 1.00[br](29ns)]]]
[[Poisson (CDF)][[role red 3.38[br](264ns)]][[role blue 1.56[br](122ns)]][[role blue 1.45[br](113ns)]][[role green 1.00[br](78ns)]]]
[[Poisson (PDF)][[role red 4.19[br](218ns)]][[role blue 1.54[br](80ns)]][[role blue 1.52[br](79ns)]][[role green 1.00[br](52ns)]]]
[[Poisson (quantile)][[role red 3.04[br](1193ns)]][[role blue 1.38[br](540ns)]][[role blue 1.35[br](528ns)]][[role green 1.00[br](392ns)]]]
[[Rayleigh (CDF)][[role blue 1.35[br](23ns)]][[role blue 1.41[br](24ns)]][[role blue 1.41[br](24ns)]][[role green 1.00[br](17ns)]]]
[[Rayleigh (PDF)][[role blue 1.68[br](37ns)]][[role blue 1.64[br](36ns)]][[role blue 1.64[br](36ns)]][[role green 1.00[br](22ns)]]]
[[Rayleigh (quantile)][[role green 1.04[br](27ns)]][[role green 1.04[br](27ns)]][[role green 1.00[br](26ns)]][[role green 1.08[br](28ns)]]]
[[SkewNormal (CDF)][[role blue 1.43[br](691ns)]][[role blue 1.31[br](633ns)]][[role blue 1.31[br](635ns)]][[role green 1.00[br](483ns)]]]
[[SkewNormal (PDF)][[role red 2.07[br](174ns)]][[role blue 1.31[br](110ns)]][[role blue 1.29[br](108ns)]][[role green 1.00[br](84ns)]]]
[[SkewNormal (quantile)][[role red 2.16[br](6788ns)]][[role blue 1.85[br](5813ns)]][[role blue 1.31[br](4118ns)]][[role green 1.00[br](3143ns)]]]
[[StudentsT (CDF)][[role red 6.44[br](1893ns)]][[role red 2.23[br](656ns)]][[role red 2.21[br](650ns)]][[role green 1.00[br](294ns)]]]
[[StudentsT (PDF)][[role red 6.58[br](724ns)]][[role blue 1.60[br](176ns)]][[role blue 1.52[br](167ns)]][[role green 1.00[br](110ns)]]]
[[StudentsT (quantile)][[role red 5.79[br](2959ns)]][[role blue 1.87[br](954ns)]][[role blue 1.86[br](951ns)]][[role green 1.00[br](511ns)]]]
[[Weibull (CDF)][[role blue 1.73[br](69ns)]][[role blue 1.73[br](69ns)]][[role blue 1.75[br](70ns)]][[role green 1.00[br](40ns)]]]
[[Weibull (PDF)][[role blue 1.98[br](117ns)]][[role blue 1.98[br](117ns)]][[role blue 1.98[br](117ns)]][[role green 1.00[br](59ns)]]]
[[Weibull (quantile)][[role blue 1.21[br](69ns)]][[role blue 1.21[br](69ns)]][[role green 1.19[br](68ns)]][[role green 1.00[br](57ns)]]]
]
]

[template table_Distribution_performance_comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[table:table_Distribution_performance_comparison_with_GNU_C_version_9_2_1_20191008_on_linux Distribution performance comparison with GNU C++ version 9.2.1 20191008 on linux
[[Function][boost 1.73][Boost[br]promote_double<false>][GSL][Rmath 3.6.3][DCDFLIB]]
[[ArcSine (CDF)][[role green 1.06[br](35ns)]][[role green 1.00[br](33ns)]][][][]]
[[ArcSine (PDF)][[role green 1.00[br](5ns)]][[role green 1.00[br](5ns)]][][][]]
[[ArcSine (quantile)][[role green 1.04[br](24ns)]][[role green 1.00[br](23ns)]][][][]]
[[Beta (CDF)][[role red 3.14[br](437ns)]][[role green 1.00[br](139ns)]][[role red 3.24[br](450ns)]][[role blue 1.94[br](269ns)]][[role red 2.52[br](350ns)]]]
[[Beta (PDF)][[role red 3.50[br](360ns)]][[role green 1.00[br](103ns)]][[role grey -]][[role red 2.31[br](238ns)]][]]
[[Beta (quantile)][[role red 2.61[br](2587ns)]][[role green 1.00[br](991ns)]][[role red 14.97[br](14833ns)]][[role blue 1.83[br](1809ns)]][[role red 7.24[br](7178ns)]]]
[[Binomial (CDF)][[role red 2.58[br](837ns)]][[role green 1.00[br](324ns)]][[role red 2.16[br](701ns)]][[role blue 1.49[br](484ns)]][[role blue 1.65[br](536ns)]]]
[[Binomial (PDF)][[role red 2.66[br](322ns)]][[role green 1.00[br](121ns)]][[role grey -]][[role blue 1.83[br](222ns)]][]]
[[Binomial (quantile)][[role red 2.96[br](3917ns)]][[role green 1.17[br](1548ns)]][[role grey -]][[role green 1.00[br](1323ns)]][[role red 6.59[br](8716ns)]]]
[[Cauchy (CDF)][[role green 1.00[br](23ns)]][[role green 1.00[br](23ns)]][[role blue 1.57[br](36ns)]][[role blue 1.57[br](36ns)]][]]
[[Cauchy (PDF)][[role green 1.00[br](3ns)]][[role green 1.00[br](3ns)]][[role grey -]][[role red 5.67[br](17ns)]][]]
[[Cauchy (quantile)][[role green 1.00[br](45ns)]][[role green 1.07[br](48ns)]][[role red 75.60[br](3402ns)]][[role blue 1.93[br](87ns)]][]]
[[ChiSquared (CDF)][[role red 6.00[br](1002ns)]][[role red 3.03[br](506ns)]][[role red 9.54[br](1593ns)]][[role blue 1.46[br](243ns)]][[role green 1.00[br](167ns)]]]
[[ChiSquared (PDF)][[role red 3.43[br](295ns)]][[role green 1.00[br](86ns)]][[role grey -]][[role blue 1.28[br](110ns)]][]]
[[ChiSquared (quantile)][[role red 2.04[br](1664ns)]][[role green 1.00[br](815ns)]][[role red 18.96[br](15454ns)]][[role blue 1.84[br](1501ns)]][[role red 6.40[br](5220ns)]]]
[[Exponential (CDF)][[role green 1.00[br](19ns)]][[role blue 1.47[br](28ns)]][[role blue 1.68[br](32ns)]][[role blue 1.84[br](35ns)]][]]
[[Exponential (PDF)][[role green 1.00[br](30ns)]][[role green 1.17[br](35ns)]][[role grey -]][[role blue 1.57[br](47ns)]][]]
[[Exponential (quantile)][[role green 1.00[br](23ns)]][[role green 1.13[br](26ns)]][[role blue 1.48[br](34ns)]][[role blue 1.57[br](36ns)]][]]
[[ExtremeValue (CDF)][[role green 1.00[br](57ns)]][[role green 1.02[br](58ns)]][][][]]
[[ExtremeValue (PDF)][[role green 1.06[br](90ns)]][[role green 1.00[br](85ns)]][][][]]
[[ExtremeValue (quantile)][[role green 1.04[br](53ns)]][[role green 1.00[br](51ns)]][][][]]
[[F (CDF)][[role red 2.83[br](817ns)]][[role green 1.00[br](289ns)]][[role red 2.52[br](729ns)]][[role blue 1.38[br](399ns)]][[role blue 1.51[br](436ns)]]]
[[F (PDF)][[role red 2.87[br](361ns)]][[role green 1.00[br](126ns)]][[role grey -]][[role blue 1.24[br](156ns)]][]]
[[F (quantile)][[role red 2.11[br](2615ns)]][[role green 1.00[br](1241ns)]][[role red 11.07[br](13741ns)]][[role red 2.09[br](2589ns)]][[role red 5.32[br](6604ns)]]]
[[Gamma (CDF)][[role red 4.10[br](714ns)]][[role blue 1.53[br](267ns)]][[role red 3.93[br](684ns)]][[role blue 1.43[br](248ns)]][[role green 1.00[br](174ns)]]]
[[Gamma (PDF)][[role red 3.87[br](437ns)]][[role green 1.00[br](113ns)]][[role grey -]][[role blue 1.35[br](153ns)]][]]
[[Gamma (quantile)][[role red 2.83[br](1827ns)]][[role green 1.17[br](755ns)]][[role red 11.04[br](7119ns)]][[role red 2.25[br](1454ns)]][[role green 1.00[br](645ns)]]]
[[Geometric (CDF)][[role green 1.00[br](25ns)]][[role green 1.08[br](27ns)]][[role red 2.12[br](53ns)]][[role red 2.32[br](58ns)]][]]
[[Geometric (PDF)][[role green 1.00[br](23ns)]][[role green 1.00[br](23ns)]][[role grey -]][[role red 15.78[br](363ns)]][]]
[[Geometric (quantile)][[role green 1.00[br](25ns)]][[role green 1.12[br](28ns)]][[role grey -]][[role red 2.88[br](72ns)]][]]
[[Hypergeometric (CDF)][[role red 95.49[br](66745ns)]][[role red 88.59[br](61922ns)]][[role green 1.00[br](699ns)]][[role green 1.04[br](729ns)]][]]
[[Hypergeometric (PDF)][[role red 141.15[br](73824ns)]][[role red 121.97[br](63790ns)]][[role grey -]][[role green 1.00[br](523ns)]][]]
[[Hypergeometric (quantile)][[role blue 1.38[br](131293ns)]][[role blue 1.75[br](166268ns)]][[role grey -]][[role green 1.00[br](95222ns)]][]]
[[InverseChiSquared (CDF)][[role blue 1.92[br](1538ns)]][[role green 1.00[br](803ns)]][][][]]
[[InverseChiSquared (PDF)][[role red 2.72[br](367ns)]][[role green 1.00[br](135ns)]][][][]]
[[InverseChiSquared (quantile)][[role blue 1.76[br](2328ns)]][[role green 1.00[br](1322ns)]][][][]]
[[InverseGamma (CDF)][[role red 2.25[br](806ns)]][[role green 1.00[br](359ns)]][][][]]
[[InverseGamma (PDF)][[role red 3.41[br](515ns)]][[role green 1.00[br](151ns)]][][][]]
[[InverseGamma (quantile)][[role red 2.37[br](2187ns)]][[role green 1.00[br](921ns)]][][][]]
[[InverseGaussian (CDF)][[role green 1.00[br](236ns)]][[role green 1.03[br](242ns)]][][][]]
[[InverseGaussian (PDF)][[role green 1.00[br](23ns)]][[role green 1.00[br](23ns)]][][][]]
[[InverseGaussian (quantile)][[role green 1.00[br](3530ns)]][[role green 1.08[br](3823ns)]][][][]]
[[Laplace (CDF)][[role green 1.00[br](41ns)]][[role green 1.02[br](42ns)]][[role green 1.20[br](49ns)]][][]]
[[Laplace (PDF)][[role green 1.00[br](42ns)]][[role green 1.02[br](43ns)]][][][]]
[[Laplace (quantile)][[role green 1.00[br](39ns)]][[role green 1.03[br](40ns)]][[role green 1.08[br](42ns)]][][]]
[[LogNormal (CDF)][[role red 2.14[br](225ns)]][[role blue 1.34[br](141ns)]][[role green 1.00[br](105ns)]][[role green 1.14[br](120ns)]][]]
[[LogNormal (PDF)][[role green 1.00[br](74ns)]][[role green 1.03[br](76ns)]][[role grey -]][[role green 1.00[br](74ns)]][]]
[[LogNormal (quantile)][[role blue 1.35[br](105ns)]][[role blue 1.29[br](101ns)]][[role green 1.00[br](78ns)]][[role green 1.03[br](80ns)]][]]
[[Logistic (CDF)][[role green 1.00[br](41ns)]][[role green 1.00[br](41ns)]][[role blue 1.73[br](71ns)]][[role green 1.15[br](47ns)]][]]
[[Logistic (PDF)][[role green 1.00[br](43ns)]][[role green 1.00[br](43ns)]][[role grey -]][[role green 1.07[br](46ns)]][]]
[[Logistic (quantile)][[role green 1.00[br](37ns)]][[role green 1.00[br](37ns)]][[role green 1.14[br](42ns)]][[role green 1.19[br](44ns)]][]]
[[NegativeBinomial (CDF)][[role red 2.66[br](1354ns)]][[role green 1.06[br](540ns)]][[role blue 1.91[br](974ns)]][[role green 1.00[br](509ns)]][[role green 1.16[br](589ns)]]]
[[NegativeBinomial (PDF)][[role red 3.40[br](445ns)]][[role green 1.00[br](131ns)]][[role grey -]][[role blue 1.45[br](190ns)]][]]
[[NegativeBinomial (quantile)][[role red 2.56[br](7468ns)]][[role green 1.00[br](2918ns)]][[role grey -]][[role red 2.97[br](8664ns)]][[role red 3.91[br](11414ns)]]]
[[NonCentralBeta (CDF)][[role red 2.71[br](2083ns)]][[role green 1.00[br](769ns)]][[role grey -]][[role blue 1.27[br](976ns)]][]]
[[NonCentralBeta (PDF)][[role red 2.48[br](1265ns)]][[role green 1.00[br](511ns)]][[role grey -]][[role green 1.13[br](578ns)]][]]
[[NonCentralBeta (quantile)][[role red 2.90[br](55809ns)]][[role green 1.00[br](19273ns)]][[role grey -]][[role red 3.91[br](75343ns)]][]]
[[NonCentralChiSquared (CDF)][[role red 9.11[br](4498ns)]][[role red 5.71[br](2821ns)]][[role grey -]][[role red 18.21[br](8997ns)]][[role green 1.00[br](494ns)]]]
[[NonCentralChiSquared (PDF)][[role red 3.05[br](953ns)]][[role blue 1.40[br](436ns)]][[role grey -]][[role green 1.00[br](312ns)]][]]
[[NonCentralChiSquared (quantile)][[role red 2.54[br](31465ns)]][[role blue 1.43[br](17712ns)]][[role grey -]][[role red 34.65[br](428458ns)]][[role green 1.00[br](12366ns)]]]
[[NonCentralF (CDF)][[role red 2.74[br](1828ns)]][[role green 1.00[br](667ns)]][[role grey -]][[role blue 1.74[br](1160ns)]][[role blue 1.66[br](1104ns)]]]
[[NonCentralF (PDF)][[role red 2.73[br](1506ns)]][[role green 1.02[br](564ns)]][[role grey -]][[role green 1.00[br](551ns)]][]]
[[NonCentralF (quantile)][[role red 2.88[br](29414ns)]][[role green 1.00[br](10228ns)]][[role grey -]][[role red 5.53[br](56602ns)]][[role blue 1.34[br](13703ns)]]]
[[NonCentralT (CDF)][[role red 4.62[br](6416ns)]][[role red 2.09[br](2907ns)]][[role grey -]][[role green 1.00[br](1389ns)]][]]
[[NonCentralT (PDF)][[role blue 1.77[br](4034ns)]][[role green 1.00[br](2284ns)]][[role grey -]][[role blue 1.27[br](2898ns)]][]]
[[NonCentralT (quantile)][[role red 2.33[br](68590ns)]][[role green 1.00[br](29386ns)]][[role grey -]][[role red 2.29[br](67338ns)]][]]
[[Normal (CDF)][[role red 2.59[br](150ns)]][[role blue 1.55[br](90ns)]][[role green 1.00[br](58ns)]][[role blue 1.50[br](87ns)]][[role red 3.12[br](181ns)]]]
[[Normal (PDF)][[role green 1.03[br](34ns)]][[role green 1.00[br](33ns)]][[role grey -]][[role blue 1.55[br](51ns)]][]]
[[Normal (quantile)][[role blue 1.43[br](57ns)]][[role green 1.00[br](40ns)]][[role blue 1.30[br](52ns)]][[role green 1.00[br](40ns)]][[role red 13.10[br](524ns)]]]
[[Pareto (CDF)][[role green 1.00[br](49ns)]][[role green 1.04[br](51ns)]][[role green 1.06[br](52ns)]][][]]
[[Pareto (PDF)][[role green 1.00[br](86ns)]][[role green 1.00[br](86ns)]][][][]]
[[Pareto (quantile)][[role green 1.00[br](51ns)]][[role green 1.02[br](52ns)]][[role blue 1.24[br](63ns)]][][]]
[[Poisson (CDF)][[role red 2.16[br](264ns)]][[role green 1.00[br](122ns)]][[role red 3.03[br](370ns)]][[role blue 1.70[br](207ns)]][[role blue 1.27[br](155ns)]]]
[[Poisson (PDF)][[role red 2.73[br](218ns)]][[role green 1.00[br](80ns)]][[role grey -]][[role blue 1.54[br](123ns)]][]]
[[Poisson (quantile)][[role red 2.22[br](1193ns)]][[role green 1.01[br](540ns)]][[role grey -]][[role green 1.00[br](537ns)]][[role red 4.49[br](2409ns)]]]
[[Rayleigh (CDF)][[role green 1.00[br](23ns)]][[role green 1.04[br](24ns)]][[role blue 1.39[br](32ns)]][][]]
[[Rayleigh (PDF)][[role green 1.03[br](37ns)]][[role green 1.00[br](36ns)]][][][]]
[[Rayleigh (quantile)][[role green 1.00[br](27ns)]][[role green 1.00[br](27ns)]][[role blue 1.37[br](37ns)]][][]]
[[SkewNormal (CDF)][[role green 1.09[br](691ns)]][[role green 1.00[br](633ns)]][][][]]
[[SkewNormal (PDF)][[role blue 1.58[br](174ns)]][[role green 1.00[br](110ns)]][][][]]
[[SkewNormal (quantile)][[role green 1.17[br](6788ns)]][[role green 1.00[br](5813ns)]][][][]]
[[StudentsT (CDF)][[role red 6.46[br](1893ns)]][[role red 2.24[br](656ns)]][[role green 1.00[br](293ns)]][[role green 1.03[br](303ns)]][[role blue 1.25[br](367ns)]]]
[[StudentsT (PDF)][[role red 5.36[br](724ns)]][[role blue 1.30[br](176ns)]][[role grey -]][[role green 1.00[br](135ns)]][]]
[[StudentsT (quantile)][[role red 3.62[br](2959ns)]][[role green 1.17[br](954ns)]][[role blue 1.21[br](986ns)]][[role green 1.00[br](818ns)]][[role red 3.45[br](2822ns)]]]
[[Weibull (CDF)][[role green 1.00[br](69ns)]][[role green 1.00[br](69ns)]][[role green 1.10[br](76ns)]][[role green 1.17[br](81ns)]][]]
[[Weibull (PDF)][[role blue 1.27[br](117ns)]][[role blue 1.27[br](117ns)]][[role grey -]][[role green 1.00[br](92ns)]][]]
[[Weibull (quantile)][[role green 1.00[br](69ns)]][[role green 1.00[br](69ns)]][[role green 1.07[br](74ns)]][[role green 1.12[br](77ns)]][]]
]
]

[template table_gcd_method_comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[table:table_gcd_method_comparison_with_GNU_C_version_9_2_1_20191008_on_linux gcd method comparison with GNU C++ version 9.2.1 20191008 on linux
[[Function][gcd
boost 1.73][Euclid_gcd
boost 1.73][Stein_gcd
boost 1.73][mixed_binary_gcd
boost 1.73][Stein_gcd_textbook
boost 1.73][gcd_euclid_textbook
boost 1.73]]
[[gcd<boost::multiprecision::uint1024_t> (Trivial cases)][[role green 1.00[br](1139ns)]][[role blue 1.25[br](1422ns)]][[role red 3.97[br](4518ns)]][[role red 3.83[br](4361ns)]][[role blue 1.29[br](1468ns)]][[role green 1.13[br](1285ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (adjacent Fibonacci numbers)][[role green 1.00[br](11439589ns)]][[role red 8.50[br](97211077ns)]][[role red 6.64[br](75969380ns)]][[role red 4.90[br](56018232ns)]][[role red 2.04[br](23344331ns)]][[role red 9.78[br](111899267ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (permutations of Fibonacci numbers)][[role red 2.79[br](6382874689ns)]][[role green 1.00[br](2285479521ns)]][[role red 19.06[br](43566363418ns)]][[role red 3.36[br](7680786292ns)]][[role red 5.93[br](13549301457ns)]][[role green 1.05[br](2389415907ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (random prime number products)][[role green 1.00[br](997833ns)]][[role red 2.07[br](2070479ns)]][[role red 12.21[br](12186681ns)]][[role red 3.17[br](3160386ns)]][[role red 4.01[br](3998174ns)]][[role red 2.36[br](2359182ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (uniform random numbers)][[role green 1.00[br](59701401ns)]][[role red 2.19[br](130845141ns)]][[role red 4.03[br](240683133ns)]][[role red 2.94[br](175473511ns)]][[role blue 1.57[br](94011306ns)]][[role red 2.40[br](143023592ns)]]]
[[gcd<boost::multiprecision::uint256_t> (Trivial cases)][[role green 1.00[br](961ns)]][[role green 1.09[br](1049ns)]][[role red 3.13[br](3009ns)]][[role red 3.09[br](2969ns)]][[role green 1.15[br](1103ns)]][[role green 1.05[br](1013ns)]]]
[[gcd<boost::multiprecision::uint256_t> (adjacent Fibonacci numbers)][[role green 1.00[br](4796624ns)]][[role red 4.84[br](23199682ns)]][[role red 6.85[br](32879590ns)]][[role red 4.91[br](23556436ns)]][[role blue 1.97[br](9425574ns)]][[role red 5.43[br](26053510ns)]]]
[[gcd<boost::multiprecision::uint256_t> (permutations of Fibonacci numbers)][[role green 1.00[br](2533709823ns)]][[role red 2.85[br](7210157541ns)]][[role red 7.10[br](17999371341ns)]][[role red 3.79[br](9609176958ns)]][[role red 2.03[br](5150875607ns)]][[role red 3.27[br](8279928633ns)]]]
[[gcd<boost::multiprecision::uint256_t> (random prime number products)][[role green 1.00[br](940446ns)]][[role blue 1.95[br](1830213ns)]][[role red 11.90[br](11192968ns)]][[role red 2.77[br](2601192ns)]][[role red 3.02[br](2841359ns)]][[role red 2.08[br](1958545ns)]]]
[[gcd<boost::multiprecision::uint256_t> (uniform random numbers)][[role green 1.00[br](6927881ns)]][[role red 3.33[br](23088876ns)]][[role red 5.76[br](39884395ns)]][[role red 4.20[br](29108065ns)]][[role blue 1.80[br](12486928ns)]][[role red 3.92[br](27141013ns)]]]
[[gcd<boost::multiprecision::uint512_t> (Trivial cases)][[role green 1.00[br](990ns)]][[role green 1.09[br](1075ns)]][[role red 3.12[br](3084ns)]][[role red 3.15[br](3119ns)]][[role blue 1.23[br](1213ns)]][[role green 1.09[br](1082ns)]]]
[[gcd<boost::multiprecision::uint512_t> (adjacent Fibonacci numbers)][[role green 1.00[br](10497718ns)]][[role red 5.29[br](55512495ns)]][[role red 5.11[br](53676000ns)]][[role red 3.68[br](38651448ns)]][[role blue 1.53[br](16108385ns)]][[role red 6.07[br](63676361ns)]]]
[[gcd<boost::multiprecision::uint512_t> (permutations of Fibonacci numbers)][[role green 1.00[br](5682756699ns)]][[role blue 1.31[br](7453254917ns)]][[role red 5.72[br](32494337541ns)]][[role blue 1.99[br](11305853380ns)]][[role blue 1.71[br](9700566500ns)]][[role blue 1.56[br](8853917665ns)]]]
[[gcd<boost::multiprecision::uint512_t> (random prime number products)][[role green 1.00[br](908706ns)]][[role blue 1.86[br](1687603ns)]][[role red 10.59[br](9618850ns)]][[role red 2.85[br](2585383ns)]][[role red 3.27[br](2973364ns)]][[role red 2.33[br](2114724ns)]]]
[[gcd<boost::multiprecision::uint512_t> (uniform random numbers)][[role green 1.00[br](20848912ns)]][[role red 2.53[br](52822316ns)]][[role red 4.36[br](90827478ns)]][[role red 3.14[br](65525908ns)]][[role blue 1.41[br](29462857ns)]][[role red 2.85[br](59465047ns)]]]
[[gcd<unsigned long long> (Trivial cases)][[role green 1.10[br](386ns)]][[role green 1.15[br](404ns)]][[role green 1.00[br](352ns)]][[role green 1.18[br](416ns)]][[role green 1.20[br](421ns)]][[role green 1.15[br](406ns)]]]
[[gcd<unsigned long long> (adjacent Fibonacci numbers)][[role red 2.52[br](21347ns)]][[role red 12.31[br](104362ns)]][[role green 1.00[br](8477ns)]][[role red 2.53[br](21408ns)]][[role red 2.27[br](19232ns)]][[role red 11.20[br](94968ns)]]]
[[gcd<unsigned long long> (permutations of Fibonacci numbers)][[role green 1.03[br](1486020ns)]][[role blue 1.31[br](1895136ns)]][[role green 1.13[br](1627153ns)]][[role green 1.00[br](1443407ns)]][[role red 2.05[br](2961413ns)]][[role blue 1.21[br](1747366ns)]]]
[[gcd<unsigned long long> (random prime number products)][[role green 1.12[br](306204ns)]][[role blue 1.80[br](494509ns)]][[role green 1.00[br](274209ns)]][[role green 1.10[br](301452ns)]][[role blue 1.97[br](540954ns)]][[role blue 1.73[br](474387ns)]]]
[[gcd<unsigned long long> (uniform random numbers)][[role blue 1.32[br](549553ns)]][[role red 2.08[br](867161ns)]][[role green 1.00[br](417845ns)]][[role blue 1.35[br](562934ns)]][[role blue 1.74[br](727731ns)]][[role red 2.08[br](867699ns)]]]
[[gcd<unsigned long> (Trivial cases)][[role green 1.14[br](418ns)]][[role green 1.14[br](419ns)]][[role green 1.00[br](366ns)]][[role green 1.11[br](407ns)]][[role green 1.20[br](438ns)]][[role green 1.08[br](396ns)]]]
[[gcd<unsigned long> (adjacent Fibonacci numbers)][[role red 2.56[br](20719ns)]][[role red 11.85[br](96083ns)]][[role green 1.00[br](8109ns)]][[role red 2.60[br](21069ns)]][[role red 2.18[br](17643ns)]][[role red 11.47[br](93024ns)]]]
[[gcd<unsigned long> (permutations of Fibonacci numbers)][[role green 1.00[br](1462811ns)]][[role blue 1.29[br](1880754ns)]][[role green 1.12[br](1641469ns)]][[role green 1.02[br](1494359ns)]][[role red 2.34[br](3425296ns)]][[role blue 1.38[br](2014828ns)]]]
[[gcd<unsigned long> (random prime number products)][[role green 1.11[br](311333ns)]][[role blue 1.78[br](500249ns)]][[role green 1.00[br](280814ns)]][[role green 1.11[br](310556ns)]][[role blue 1.95[br](546546ns)]][[role blue 1.66[br](465036ns)]]]
[[gcd<unsigned long> (uniform random numbers)][[role blue 1.34[br](558101ns)]][[role red 2.03[br](842691ns)]][[role green 1.00[br](415811ns)]][[role blue 1.32[br](547760ns)]][[role blue 1.73[br](720927ns)]][[role red 2.08[br](864164ns)]]]
[[gcd<unsigned short> (Trivial cases)][[role green 1.05[br](376ns)]][[role green 1.00[br](359ns)]][[role green 1.04[br](375ns)]][[role green 1.01[br](362ns)]][[role green 1.19[br](426ns)]][[role green 1.05[br](378ns)]]]
[[gcd<unsigned short> (adjacent Fibonacci numbers)][[role blue 1.27[br](1190ns)]][[role red 3.74[br](3510ns)]][[role green 1.00[br](939ns)]][[role blue 1.28[br](1203ns)]][[role blue 1.59[br](1490ns)]][[role red 3.40[br](3196ns)]]]
[[gcd<unsigned short> (permutations of Fibonacci numbers)][[role green 1.15[br](20486ns)]][[role blue 1.64[br](29254ns)]][[role green 1.00[br](17809ns)]][[role green 1.18[br](21026ns)]][[role red 2.07[br](36923ns)]][[role blue 1.52[br](27126ns)]]]
[[gcd<unsigned short> (random prime number products)][[role green 1.05[br](81694ns)]][[role blue 1.51[br](117228ns)]][[role green 1.10[br](85525ns)]][[role green 1.00[br](77825ns)]][[role red 2.28[br](177072ns)]][[role blue 1.37[br](106489ns)]]]
[[gcd<unsigned short> (uniform random numbers)][[role green 1.02[br](104371ns)]][[role blue 1.48[br](150970ns)]][[role green 1.03[br](105321ns)]][[role green 1.00[br](102132ns)]][[role red 2.19[br](223600ns)]][[role blue 1.48[br](150944ns)]]]
[[gcd<unsigned> (Trivial cases)][[role green 1.16[br](400ns)]][[role green 1.08[br](373ns)]][[role green 1.08[br](373ns)]][[role green 1.17[br](403ns)]][[role blue 1.26[br](434ns)]][[role green 1.00[br](344ns)]]]
[[gcd<unsigned> (adjacent Fibonacci numbers)][[role blue 1.30[br](4109ns)]][[role red 5.85[br](18476ns)]][[role green 1.00[br](3157ns)]][[role blue 1.61[br](5095ns)]][[role red 2.12[br](6684ns)]][[role red 6.80[br](21469ns)]]]
[[gcd<unsigned> (permutations of Fibonacci numbers)][[role green 1.08[br](238246ns)]][[role blue 1.39[br](307714ns)]][[role blue 1.22[br](268675ns)]][[role green 1.00[br](220617ns)]][[role red 2.09[br](460151ns)]][[role green 1.17[br](257591ns)]]]
[[gcd<unsigned> (random prime number products)][[role green 1.05[br](178447ns)]][[role blue 1.45[br](246422ns)]][[role green 1.02[br](174244ns)]][[role green 1.00[br](170136ns)]][[role blue 1.87[br](317465ns)]][[role blue 1.29[br](220305ns)]]]
[[gcd<unsigned> (uniform random numbers)][[role green 1.14[br](251469ns)]][[role blue 1.58[br](347998ns)]][[role green 1.00[br](220832ns)]][[role green 1.15[br](253301ns)]][[role blue 1.78[br](392808ns)]][[role blue 1.58[br](349371ns)]]]
]
]

[template table_Polynomial_Arithmetic_GNU_C_version_9_2_1_20191008_linux_[]
[table:table_Polynomial_Arithmetic_GNU_C_version_9_2_1_20191008_linux_ Polynomial Arithmetic (GNU C++ version 9.2.1 20191008, linux)
[[Function][std::uint64_t][double][cpp_int]]
[[operator *][[role blue 1.26[br](556ns)]][[role green 1.00[br](440ns)]][[role red 34.34[br](15111ns)]]]
[[operator * (int)][[role blue 1.24[br](141ns)]][[role green 1.00[br](114ns)]][[role red 8.39[br](956ns)]]]
[[operator *=][[role green 1.07[br](243123ns)]][[role green 1.00[br](226544ns)]][[role red 19.57[br](4434116ns)]]]
[[operator *= (int)][[role green 1.00[br](14901ns)]][[role green 1.02[br](15258ns)]][[role red 24.57[br](366152ns)]]]
[[operator +][[role blue 1.38[br](208ns)]][[role green 1.00[br](151ns)]][[role red 9.46[br](1429ns)]]]
[[operator + (int)][[role green 1.06[br](94ns)]][[role green 1.00[br](89ns)]][[role red 6.16[br](548ns)]]]
[[operator +=][[role green 1.00[br](20ns)]][[role green 1.00[br](20ns)]][[role red 32.55[br](651ns)]]]
[[operator += (int)][[role green 1.17[br](7ns)]][[role green 1.00[br](6ns)]][[role red 21.83[br](131ns)]]]
[[operator -][[role blue 1.23[br](186ns)]][[role green 1.00[br](151ns)]][[role red 10.43[br](1575ns)]]]
[[operator - (int)][[role blue 1.29[br](103ns)]][[role green 1.00[br](80ns)]][[role red 6.69[br](535ns)]]]
[[operator -=][[role green 1.00[br](17ns)]][[role green 1.00[br](17ns)]][[role red 37.35[br](635ns)]]]
[[operator -= (int)][[role green 1.17[br](7ns)]][[role green 1.00[br](6ns)]][[role red 20.17[br](121ns)]]]
[[operator /][[role blue 1.63[br](729ns)]][[role green 1.00[br](447ns)]][[role red 61.47[br](27478ns)]]]
[[operator / (int)][[role green 1.18[br](136ns)]][[role green 1.00[br](115ns)]][[role red 19.01[br](2186ns)]]]
[[operator /=][[role green 1.07[br](15ns)]][[role green 1.00[br](14ns)]][[role red 200.14[br](2802ns)]]]
[[operator /= (int)][[role green 1.00[br](708ns)]][[role red 21.98[br](15561ns)]][[role red 3994.47[br](2828084ns)]]]
]
]

[template table_Rational_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[table:table_Rational_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux Rational Method Comparison with GNU C++ version 9.2.1 20191008 on linux
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role blue 1.92[br](23ns)]][[role blue 1.92[br](23ns)]][[role blue 1.25[br](15ns)]][[role green 1.08[br](13ns)]][[role green 1.00[br](12ns)]][[role green 1.08[br](13ns)]]]
[[Order  3][[role blue 1.89[br](36ns)]][[role red 2.21[br](42ns)]][[role blue 1.63[br](31ns)]][[role blue 1.47[br](28ns)]][[role green 1.00[br](19ns)]][[role green 1.00[br](19ns)]][[role green 1.11[br](21ns)]][[role green 1.16[br](22ns)]]]
[[Order  4][[role blue 1.85[br](48ns)]][[role red 2.42[br](63ns)]][[role blue 1.54[br](40ns)]][[role blue 1.54[br](40ns)]][[role green 1.00[br](26ns)]][[role green 1.00[br](26ns)]][[role green 1.04[br](27ns)]][[role green 1.00[br](26ns)]]]
[[Order  5][[role green 1.18[br](59ns)]][[role blue 1.42[br](71ns)]][[role green 1.00[br](50ns)]][[role green 1.00[br](50ns)]][[role green 1.10[br](55ns)]][[role green 1.10[br](55ns)]][[role green 1.12[br](56ns)]][[role green 1.10[br](55ns)]]]
[[Order  6][[role green 1.11[br](69ns)]][[role blue 1.37[br](85ns)]][[role green 1.06[br](66ns)]][[role green 1.00[br](62ns)]][[role green 1.02[br](63ns)]][[role green 1.02[br](63ns)]][[role green 1.05[br](65ns)]][[role green 1.18[br](73ns)]]]
[[Order  7][[role green 1.09[br](81ns)]][[role blue 1.35[br](100ns)]][[role green 1.00[br](74ns)]][[role green 1.00[br](74ns)]][[role green 1.00[br](74ns)]][[role green 1.01[br](75ns)]][[role green 1.03[br](76ns)]][[role green 1.03[br](76ns)]]]
[[Order  8][[role green 1.13[br](96ns)]][[role blue 1.33[br](113ns)]][[role green 1.01[br](86ns)]][[role green 1.00[br](85ns)]][[role green 1.00[br](85ns)]][[role green 1.01[br](86ns)]][[role green 1.01[br](86ns)]][[role green 1.04[br](88ns)]]]
[[Order  9][[role green 1.15[br](108ns)]][[role blue 1.34[br](126ns)]][[role green 1.05[br](99ns)]][[role green 1.05[br](99ns)]][[role green 1.01[br](95ns)]][[role green 1.00[br](94ns)]][[role green 1.02[br](96ns)]][[role green 1.12[br](105ns)]]]
[[Order 10][[role green 1.17[br](123ns)]][[role blue 1.36[br](143ns)]][[role green 1.10[br](115ns)]][[role green 1.10[br](115ns)]][[role green 1.02[br](107ns)]][[role green 1.05[br](110ns)]][[role green 1.01[br](106ns)]][[role green 1.00[br](105ns)]]]
[[Order 11][[role green 1.15[br](135ns)]][[role blue 1.34[br](157ns)]][[role green 1.15[br](134ns)]][[role green 1.09[br](128ns)]][[role green 1.01[br](118ns)]][[role green 1.01[br](118ns)]][[role green 1.00[br](117ns)]][[role green 1.02[br](119ns)]]]
[[Order 12][[role green 1.19[br](150ns)]][[role blue 1.34[br](169ns)]][[role green 1.17[br](148ns)]][[role blue 1.28[br](161ns)]][[role green 1.01[br](127ns)]][[role green 1.00[br](126ns)]][[role green 1.00[br](126ns)]][[role green 1.00[br](126ns)]]]
[[Order 13][[role blue 1.28[br](171ns)]][[role blue 1.40[br](187ns)]][[role blue 1.34[br](180ns)]][[role blue 1.28[br](172ns)]][[role green 1.01[br](136ns)]][[role green 1.00[br](134ns)]][[role green 1.01[br](136ns)]][[role green 1.06[br](142ns)]]]
[[Order 14][[role blue 1.34[br](191ns)]][[role blue 1.43[br](204ns)]][[role blue 1.27[br](181ns)]][[role blue 1.31[br](188ns)]][[role green 1.01[br](144ns)]][[role green 1.03[br](148ns)]][[role green 1.00[br](143ns)]][[role green 1.07[br](153ns)]]]
[[Order 15][[role blue 1.41[br](206ns)]][[role blue 1.50[br](219ns)]][[role blue 1.36[br](199ns)]][[role blue 1.31[br](191ns)]][[role green 1.16[br](169ns)]][[role blue 1.30[br](190ns)]][[role green 1.02[br](149ns)]][[role green 1.00[br](146ns)]]]
[[Order 16][[role blue 1.37[br](217ns)]][[role blue 1.48[br](234ns)]][[role blue 1.32[br](209ns)]][[role blue 1.35[br](213ns)]][[role blue 1.32[br](209ns)]][[role green 1.19[br](188ns)]][[role green 1.00[br](158ns)]][[role green 1.03[br](163ns)]]]
[[Order 17][[role blue 1.36[br](234ns)]][[role blue 1.48[br](254ns)]][[role blue 1.32[br](227ns)]][[role blue 1.30[br](224ns)]][[role green 1.13[br](194ns)]][[role green 1.05[br](180ns)]][[role green 1.00[br](172ns)]][[role green 1.02[br](176ns)]]]
[[Order 18][[role blue 1.37[br](248ns)]][[role blue 1.57[br](284ns)]][[role blue 1.35[br](244ns)]][[role blue 1.39[br](252ns)]][[role green 1.08[br](195ns)]][[role green 1.07[br](193ns)]][[role green 1.02[br](185ns)]][[role green 1.00[br](181ns)]]]
[[Order 19][[role blue 1.40[br](268ns)]][[role blue 1.77[br](338ns)]][[role blue 1.38[br](264ns)]][[role blue 1.40[br](267ns)]][[role green 1.01[br](193ns)]][[role green 1.00[br](191ns)]][[role green 1.02[br](195ns)]][[role green 1.04[br](198ns)]]]
[[Order 20][[role blue 1.39[br](286ns)]][[role blue 1.63[br](336ns)]][[role blue 1.41[br](291ns)]][[role blue 1.45[br](298ns)]][[role green 1.00[br](206ns)]][[role green 1.01[br](208ns)]][[role green 1.03[br](213ns)]][[role green 1.01[br](209ns)]]]
]
]

[template table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[table:table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux Polynomial Method Comparison with GNU C++ version 9.2.1 20191008 on linux
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]]]
[[Order  3][[role blue 1.70[br](17ns)]][[role red 2.40[br](24ns)]][[role green 1.00[br](10ns)]][[role green 1.00[br](10ns)]][[role green 1.00[br](10ns)]][[role green 1.00[br](10ns)]][[role green 1.00[br](10ns)]][[role green 1.00[br](10ns)]]]
[[Order  4][[role blue 1.85[br](24ns)]][[role red 2.31[br](30ns)]][[role green 1.08[br](14ns)]][[role green 1.08[br](14ns)]][[role green 1.08[br](14ns)]][[role green 1.15[br](15ns)]][[role green 1.00[br](13ns)]][[role green 1.08[br](14ns)]]]
[[Order  5][[role red 2.18[br](37ns)]][[role red 2.41[br](41ns)]][[role green 1.06[br](18ns)]][[role green 1.00[br](17ns)]][[role blue 1.29[br](22ns)]][[role green 1.18[br](20ns)]][[role green 1.12[br](19ns)]][[role green 1.12[br](19ns)]]]
[[Order  6][[role blue 1.68[br](37ns)]][[role red 2.18[br](48ns)]][[role green 1.00[br](22ns)]][[role green 1.00[br](22ns)]][[role green 1.09[br](24ns)]][[role green 1.05[br](23ns)]][[role green 1.05[br](23ns)]][[role green 1.05[br](23ns)]]]
[[Order  7][[role red 2.12[br](55ns)]][[role red 2.35[br](61ns)]][[role green 1.08[br](28ns)]][[role green 1.12[br](29ns)]][[role green 1.04[br](27ns)]][[role green 1.08[br](28ns)]][[role green 1.04[br](27ns)]][[role green 1.00[br](26ns)]]]
[[Order  8][[role blue 1.81[br](56ns)]][[role red 2.42[br](75ns)]][[role green 1.10[br](34ns)]][[role green 1.06[br](33ns)]][[role green 1.10[br](34ns)]][[role green 1.00[br](31ns)]][[role green 1.00[br](31ns)]][[role green 1.00[br](31ns)]]]
[[Order  9][[role blue 1.88[br](64ns)]][[role red 2.56[br](87ns)]][[role blue 1.24[br](42ns)]][[role green 1.18[br](40ns)]][[role green 1.00[br](34ns)]][[role green 1.09[br](37ns)]][[role green 1.03[br](35ns)]][[role green 1.06[br](36ns)]]]
[[Order 10][[role blue 1.70[br](68ns)]][[role red 2.65[br](106ns)]][[role green 1.20[br](48ns)]][[role green 1.20[br](48ns)]][[role green 1.02[br](41ns)]][[role green 1.00[br](40ns)]][[role green 1.02[br](41ns)]][[role green 1.00[br](40ns)]]]
[[Order 11][[role blue 1.91[br](84ns)]][[role red 2.45[br](108ns)]][[role blue 1.25[br](55ns)]][[role blue 1.25[br](55ns)]][[role green 1.02[br](45ns)]][[role green 1.05[br](46ns)]][[role green 1.00[br](44ns)]][[role green 1.02[br](45ns)]]]
[[Order 12][[role blue 1.84[br](92ns)]][[role red 2.56[br](128ns)]][[role blue 1.34[br](67ns)]][[role blue 1.26[br](63ns)]][[role green 1.00[br](50ns)]][[role green 1.06[br](53ns)]][[role green 1.02[br](51ns)]][[role green 1.00[br](50ns)]]]
[[Order 13][[role red 2.02[br](103ns)]][[role red 2.73[br](139ns)]][[role blue 1.37[br](70ns)]][[role blue 1.31[br](67ns)]][[role green 1.08[br](55ns)]][[role green 1.04[br](53ns)]][[role green 1.02[br](52ns)]][[role green 1.00[br](51ns)]]]
[[Order 14][[role red 2.02[br](115ns)]][[role red 2.74[br](156ns)]][[role blue 1.49[br](85ns)]][[role blue 1.44[br](82ns)]][[role green 1.04[br](59ns)]][[role green 1.02[br](58ns)]][[role green 1.00[br](57ns)]][[role green 1.00[br](57ns)]]]
[[Order 15][[role blue 1.89[br](125ns)]][[role red 2.55[br](168ns)]][[role blue 1.41[br](93ns)]][[role blue 1.38[br](91ns)]][[role green 1.00[br](66ns)]][[role green 1.00[br](66ns)]][[role green 1.02[br](67ns)]][[role green 1.03[br](68ns)]]]
[[Order 16][[role blue 1.77[br](136ns)]][[role red 2.40[br](185ns)]][[role blue 1.34[br](103ns)]][[role blue 1.40[br](108ns)]][[role green 1.10[br](85ns)]][[role green 1.06[br](82ns)]][[role green 1.00[br](77ns)]][[role green 1.00[br](77ns)]]]
[[Order 17][[role blue 1.77[br](143ns)]][[role red 2.60[br](211ns)]][[role blue 1.43[br](116ns)]][[role blue 1.44[br](117ns)]][[role green 1.04[br](84ns)]][[role green 1.00[br](81ns)]][[role green 1.02[br](83ns)]][[role green 1.16[br](94ns)]]]
[[Order 18][[role blue 1.85[br](163ns)]][[role red 2.39[br](210ns)]][[role blue 1.47[br](129ns)]][[role blue 1.53[br](135ns)]][[role green 1.00[br](88ns)]][[role green 1.01[br](89ns)]][[role green 1.00[br](88ns)]][[role green 1.00[br](88ns)]]]
[[Order 19][[role red 2.01[br](183ns)]][[role red 2.55[br](232ns)]][[role blue 1.53[br](139ns)]][[role blue 1.53[br](139ns)]][[role green 1.04[br](95ns)]][[role green 1.05[br](96ns)]][[role green 1.00[br](91ns)]][[role green 1.05[br](96ns)]]]
[[Order 20][[role blue 1.94[br](194ns)]][[role red 2.55[br](255ns)]][[role blue 1.48[br](148ns)]][[role blue 1.51[br](151ns)]][[role green 1.00[br](100ns)]][[role green 1.00[br](100ns)]][[role green 1.18[br](118ns)]][[role green 1.03[br](103ns)]]]
]
]

[template table_Compiler_Comparison_on_linux[]
[table:table_Compiler_Comparison_on_linux Compiler Comparison on linux
[[Function][GNU C++ version 9.2.1 20191008[br]boost 1.73][GNU C++ version 9.2.1 20191008[br]boost 1.73[br]promote_double<false>][Clang version 9.0.0 (tags/RELEASE_900/final)[br]boost 1.73][Clang version 9.0.0 (tags/RELEASE_900/final)[br]boost 1.73[br]promote_double<false>][Intel C++ C++0x mode version 1910[br]boost 1.73][Intel C++ C++0x mode version 1910[br]boost 1.73[br]promote_double<false>]]
[[assoc_laguerre][[role green 1.17[br](156ns)]][[role green 1.08[br](143ns)]][[role blue 1.72[br](229ns)]][[role blue 1.31[br](174ns)]][[role green 1.15[br](153ns)]][[role green 1.00[br](133ns)]]]
[[assoc_legendre][[role red 2.91[br](279ns)]][[role blue 1.78[br](171ns)]][[role red 2.86[br](275ns)]][[role green 1.00[br](96ns)]][[role red 2.79[br](268ns)]][[role red 2.16[br](207ns)]]]
[[beta][[role red 10.52[br](789ns)]][[role blue 1.40[br](105ns)]][[role red 10.56[br](792ns)]][[role blue 1.73[br](130ns)]][[role red 6.07[br](455ns)]][[role green 1.00[br](75ns)]]]
[[beta (incomplete)][[role red 4.98[br](2076ns)]][[role blue 1.26[br](524ns)]][[role red 5.44[br](2267ns)]][[role green 1.00[br](417ns)]][[role red 3.15[br](1315ns)]][[role green 1.16[br](484ns)]]]
[[cbrt][[role red 2.52[br](58ns)]][[role green 1.09[br](25ns)]][[role red 2.35[br](54ns)]][[role blue 1.22[br](28ns)]][[role blue 1.91[br](44ns)]][[role green 1.00[br](23ns)]]]
[[cyl_bessel_i][[role red 5.05[br](1182ns)]][[role green 1.02[br](239ns)]][[role red 10.73[br](2511ns)]][[role blue 1.74[br](406ns)]][[role red 3.99[br](933ns)]][[role green 1.00[br](234ns)]]]
[[cyl_bessel_i (integer order)][[role red 4.41[br](767ns)]][[role green 1.00[br](174ns)]][[role red 5.09[br](886ns)]][[role green 1.03[br](179ns)]][[role red 3.63[br](631ns)]][[role green 1.12[br](195ns)]]]
[[cyl_bessel_j][[role red 3.80[br](1067ns)]][[role blue 1.28[br](359ns)]][[role red 4.72[br](1327ns)]][[role blue 1.55[br](436ns)]][[role red 3.05[br](857ns)]][[role green 1.00[br](281ns)]]]
[[cyl_bessel_j (integer order)][[role red 3.10[br](319ns)]][[role green 1.05[br](108ns)]][[role red 4.73[br](487ns)]][[role green 1.00[br](103ns)]][[role red 2.30[br](237ns)]][[role green 1.12[br](115ns)]]]
[[cyl_bessel_k][[role red 10.37[br](3962ns)]][[role green 1.00[br](382ns)]][[role red 11.55[br](4414ns)]][[role blue 1.52[br](582ns)]][[role red 12.41[br](4739ns)]][[role green 1.09[br](416ns)]]]
[[cyl_bessel_k (integer order)][[role red 19.23[br](3289ns)]][[role green 1.06[br](181ns)]][[role red 19.78[br](3382ns)]][[role blue 1.27[br](217ns)]][[role red 20.02[br](3423ns)]][[role green 1.00[br](171ns)]]]
[[cyl_neumann][[role red 2.18[br](8826ns)]][[role green 1.19[br](4814ns)]][[role red 3.23[br](13059ns)]][[role blue 1.31[br](5275ns)]][[role red 2.35[br](9503ns)]][[role green 1.00[br](4041ns)]]]
[[cyl_neumann (integer order)][[role red 3.67[br](466ns)]][[role blue 1.35[br](172ns)]][[role red 4.49[br](570ns)]][[role green 1.15[br](146ns)]][[role red 2.87[br](364ns)]][[role green 1.00[br](127ns)]]]
[[digamma][[role red 2.30[br](62ns)]][[role blue 1.30[br](35ns)]][[role red 3.11[br](84ns)]][[role blue 1.33[br](36ns)]][[role blue 1.85[br](50ns)]][[role green 1.00[br](27ns)]]]
[[ellint_1][[role red 2.26[br](310ns)]][[role green 1.12[br](154ns)]][[role red 6.96[br](954ns)]][[role blue 1.25[br](171ns)]][[role red 3.22[br](441ns)]][[role green 1.00[br](137ns)]]]
[[ellint_1 (complete)][[role red 4.65[br](93ns)]][[role red 3.15[br](63ns)]][[role red 6.00[br](120ns)]][[role green 1.15[br](23ns)]][[role blue 1.90[br](38ns)]][[role green 1.00[br](20ns)]]]
[[ellint_2][[role red 3.81[br](704ns)]][[role blue 1.28[br](236ns)]][[role red 8.95[br](1655ns)]][[role blue 1.36[br](251ns)]][[role red 4.36[br](807ns)]][[role green 1.00[br](185ns)]]]
[[ellint_2 (complete)][[role blue 1.83[br](44ns)]][[role green 1.04[br](25ns)]][[role red 6.25[br](150ns)]][[role blue 1.25[br](30ns)]][[role blue 2.00[br](48ns)]][[role green 1.00[br](24ns)]]]
[[ellint_3][[role red 3.27[br](609ns)]][[role green 1.17[br](217ns)]][[role red 5.37[br](998ns)]][[role blue 1.32[br](245ns)]][[role red 2.84[br](528ns)]][[role green 1.00[br](186ns)]]]
[[ellint_3 (complete)][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]]]
[[ellint_rc][[role red 2.75[br](66ns)]][[role blue 1.29[br](31ns)]][[role red 4.71[br](113ns)]][[role blue 1.42[br](34ns)]][[role blue 1.83[br](44ns)]][[role green 1.00[br](24ns)]]]
[[ellint_rd][[role red 4.37[br](428ns)]][[role blue 1.31[br](128ns)]][[role red 9.24[br](906ns)]][[role blue 1.31[br](128ns)]][[role red 4.17[br](409ns)]][[role green 1.00[br](98ns)]]]
[[ellint_rf][[role red 2.47[br](84ns)]][[role blue 1.44[br](49ns)]][[role red 4.62[br](157ns)]][[role blue 1.32[br](45ns)]][[role blue 1.85[br](63ns)]][[role green 1.00[br](34ns)]]]
[[ellint_rj][[role red 4.14[br](426ns)]][[role blue 1.36[br](140ns)]][[role red 7.18[br](740ns)]][[role blue 1.31[br](135ns)]][[role red 3.33[br](343ns)]][[role green 1.00[br](103ns)]]]
[[erf][[role blue 1.69[br](54ns)]][[role blue 1.28[br](41ns)]][[role red 2.56[br](82ns)]][[role blue 1.38[br](44ns)]][[role blue 1.34[br](43ns)]][[role green 1.00[br](32ns)]]]
[[erfc][[role blue 1.65[br](89ns)]][[role blue 1.20[br](65ns)]][[role red 2.46[br](133ns)]][[role blue 1.26[br](68ns)]][[role blue 1.22[br](66ns)]][[role green 1.00[br](54ns)]]]
[[expint][[role red 2.93[br](85ns)]][[role blue 1.45[br](42ns)]][[role red 2.90[br](84ns)]][[role blue 1.31[br](38ns)]][[role red 2.21[br](64ns)]][[role green 1.00[br](29ns)]]]
[[expint (En)][[role red 2.42[br](242ns)]][[role green 1.14[br](114ns)]][[role red 2.76[br](276ns)]][[role green 1.19[br](119ns)]][[role blue 1.84[br](184ns)]][[role green 1.00[br](100ns)]]]
[[expm1][[role blue 1.90[br](19ns)]][[role blue 1.60[br](16ns)]][[role blue 1.50[br](15ns)]][[role blue 1.40[br](14ns)]][[role blue 1.80[br](18ns)]][[role green 1.00[br](10ns)]]]
[[gamma_p][[role red 2.43[br](618ns)]][[role green 1.02[br](260ns)]][[role red 2.95[br](749ns)]][[role green 1.00[br](254ns)]][[role red 2.15[br](545ns)]][[role green 1.03[br](262ns)]]]
[[gamma_p_inv][[role red 2.37[br](2328ns)]][[role green 1.06[br](1038ns)]][[role red 2.73[br](2676ns)]][[role green 1.06[br](1045ns)]][[role blue 1.92[br](1883ns)]][[role green 1.00[br](982ns)]]]
[[gamma_q][[role red 2.49[br](629ns)]][[role green 1.00[br](253ns)]][[role red 3.28[br](831ns)]][[role green 1.11[br](280ns)]][[role red 2.18[br](551ns)]][[role green 1.04[br](263ns)]]]
[[gamma_q_inv][[role red 2.73[br](2016ns)]][[role blue 1.30[br](958ns)]][[role red 3.22[br](2381ns)]][[role blue 1.30[br](962ns)]][[role blue 1.73[br](1275ns)]][[role green 1.00[br](739ns)]]]
[[ibeta][[role red 4.56[br](1979ns)]][[role blue 1.21[br](524ns)]][[role red 4.60[br](1996ns)]][[role green 1.00[br](434ns)]][[role red 3.35[br](1453ns)]][[role green 1.13[br](491ns)]]]
[[ibeta_inv][[role red 3.22[br](5552ns)]][[role green 1.10[br](1902ns)]][[role red 3.49[br](6013ns)]][[role green 1.03[br](1781ns)]][[role red 2.58[br](4437ns)]][[role green 1.00[br](1722ns)]]]
[[ibetac][[role red 4.34[br](2008ns)]][[role blue 1.27[br](590ns)]][[role red 4.34[br](2008ns)]][[role green 1.00[br](463ns)]][[role red 3.11[br](1438ns)]][[role green 1.17[br](543ns)]]]
[[ibetac_inv][[role red 3.32[br](5533ns)]][[role green 1.11[br](1853ns)]][[role red 3.55[br](5915ns)]][[role green 1.03[br](1722ns)]][[role red 2.48[br](4131ns)]][[role green 1.00[br](1666ns)]]]
[[jacobi_cn][[role red 3.74[br](483ns)]][[role blue 1.26[br](163ns)]][[role red 4.98[br](643ns)]][[role blue 1.61[br](208ns)]][[role red 2.67[br](345ns)]][[role green 1.00[br](129ns)]]]
[[jacobi_dn][[role red 3.64[br](503ns)]][[role blue 1.25[br](173ns)]][[role red 4.70[br](648ns)]][[role blue 1.49[br](206ns)]][[role red 2.72[br](375ns)]][[role green 1.00[br](138ns)]]]
[[jacobi_sn][[role red 4.99[br](614ns)]][[role blue 1.46[br](180ns)]][[role red 5.20[br](640ns)]][[role blue 1.72[br](212ns)]][[role red 2.57[br](316ns)]][[role green 1.00[br](123ns)]]]
[[laguerre][[role green 1.11[br](92ns)]][[role green 1.08[br](90ns)]][[role blue 1.41[br](117ns)]][[role green 1.11[br](92ns)]][[role green 1.17[br](97ns)]][[role green 1.00[br](83ns)]]]
[[legendre][[role green 1.12[br](282ns)]][[role green 1.00[br](251ns)]][[role blue 1.21[br](304ns)]][[role blue 1.30[br](326ns)]][[role blue 1.39[br](349ns)]][[role green 1.08[br](270ns)]]]
[[legendre Q][[role blue 1.24[br](390ns)]][[role green 1.00[br](314ns)]][[role blue 1.29[br](404ns)]][[role green 1.14[br](358ns)]][[role blue 1.42[br](445ns)]][[role green 1.11[br](347ns)]]]
[[lgamma][[role red 2.65[br](265ns)]][[role green 1.17[br](117ns)]][[role red 3.86[br](386ns)]][[role blue 1.24[br](124ns)]][[role blue 1.90[br](190ns)]][[role green 1.00[br](100ns)]]]
[[log1p][[role green 1.11[br](20ns)]][[role blue 1.22[br](22ns)]][[role green 1.06[br](19ns)]][[role green 1.00[br](18ns)]][[role green 1.06[br](19ns)]][[role green 1.00[br](18ns)]]]
[[polygamma][[role red 16.70[br](8883ns)]][[role blue 1.42[br](756ns)]][[role red 6.56[br](3491ns)]][[role green 1.00[br](532ns)]][[role red 7.49[br](3985ns)]][[role green 1.05[br](559ns)]]]
[[sph_bessel][[role red 2.36[br](1122ns)]][[role blue 1.24[br](588ns)]][[role red 3.31[br](1574ns)]][[role blue 1.41[br](673ns)]][[role red 2.12[br](1010ns)]][[role green 1.00[br](476ns)]]]
[[sph_neumann][[role red 3.40[br](2842ns)]][[role blue 1.21[br](1015ns)]][[role red 3.59[br](3009ns)]][[role blue 1.21[br](1012ns)]][[role red 3.14[br](2627ns)]][[role green 1.00[br](837ns)]]]
[[tgamma][[role red 4.72[br](472ns)]][[role blue 1.44[br](144ns)]][[role red 5.70[br](570ns)]][[role green 1.05[br](105ns)]][[role red 2.29[br](229ns)]][[role green 1.00[br](100ns)]]]
[[tgamma (incomplete)][[role red 3.28[br](669ns)]][[role green 1.13[br](231ns)]][[role red 3.88[br](792ns)]][[role green 1.14[br](232ns)]][[role red 2.21[br](451ns)]][[role green 1.00[br](204ns)]]]
[[trigamma][[role red 2.08[br](25ns)]][[role green 1.00[br](12ns)]][[role red 4.00[br](48ns)]][[role blue 1.50[br](18ns)]][[role blue 2.00[br](24ns)]][[role green 1.08[br](13ns)]]]
[[zeta][[role red 4.21[br](455ns)]][[role green 1.00[br](108ns)]][[role red 5.77[br](623ns)]][[role blue 1.20[br](130ns)]][[role red 2.98[br](322ns)]][[role green 1.08[br](117ns)]]]
]
]

[template table_Library_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[table:table_Library_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux Library Comparison with GNU C++ version 9.2.1 20191008 on linux
[[Function][boost 1.73][boost 1.73[br]promote_double<false>][tr1/cmath][GSL 2.5][Rmath 3.6.3][math.h]]
[[assoc_laguerre[br](2240/2240 tests selected)][[role green 1.09[br](156ns)]][[role green 1.00[br](143ns)]][[role green 1.03[br](148ns)]][[role green 1.16[br](166ns)]][[role grey -]][[role grey -]]]
[[assoc_legendre[br](110/400 tests selected)][[role red 7.75[br](279ns)]][[role red 4.75[br](171ns)]][[role green 1.00[br](36ns)]][[role red 3.25[br](117ns)]][[role grey -]][[role grey -]]]
[[beta (incomplete)[br](2682/3210 tests selected)][[role red 3.96[br](2076ns)]][[role green 1.00[br](524ns)]][[role grey -]][[role green 1.20[br](628ns)]][[role grey -]][[role grey -]]]
[[beta[br](2203/2204 tests selected)][[role red 7.51[br](789ns)]][[role green 1.00[br](105ns)]][[role blue 1.38[br](145ns)]][[role red 3.14[br](330ns)]][[role red 2.27[br](238ns)]][[role grey -]]]
[[cbrt[br](85/85 tests selected)][[role red 2.32[br](58ns)]][[role green 1.00[br](25ns)]][[role blue 1.28[br](32ns)]][[role grey -]][[role grey -]][[role blue 1.24[br](31ns)]]]
[[cyl_bessel_i (integer order)[br](494/526 tests selected)][[role red 4.76[br](767ns)]][[role green 1.08[br](174ns)]][[role green 1.00[br](161ns)]][[role blue 1.83[br](295ns)]][[role red 5.25[br](845ns)]][[role grey -]]]
[[cyl_bessel_i[br](177/240 tests selected)][[role red 7.12[br](1182ns)]][[role blue 1.44[br](239ns)]][[role green 1.00[br](166ns)]][[role red 4.14[br](688ns)]][[role red 7.92[br](1315ns)]][[role grey -]]]
[[cyl_bessel_j (integer order)[br](251/268 tests selected)][[role red 2.95[br](319ns)]][[role green 1.00[br](108ns)]][[role red 2.19[br](237ns)]][[role blue 1.73[br](187ns)]][[role red 3.59[br](388ns)]][[role blue 1.39[br](150ns)]]]
[[cyl_bessel_j[br](433/451 tests selected)][[role red 3.16[br](1067ns)]][[role green 1.06[br](359ns)]][[role green 1.00[br](338ns)]][[role blue 1.65[br](558ns)]][[role green 1.09[br](368ns)]][[role grey -]]]
[[cyl_bessel_k (integer order)[br](505/508 tests selected)][[role red 18.17[br](3289ns)]][[role green 1.00[br](181ns)]][[role red 9.73[br](1761ns)]][[role green 1.10[br](200ns)]][[role blue 1.31[br](238ns)]][[role grey -]]]
[[cyl_bessel_k[br](96/279 tests selected)][[role red 10.37[br](3962ns)]][[role green 1.00[br](382ns)]][[role blue 1.83[br](700ns)]][[role green 1.02[br](389ns)]][[role green 1.09[br](416ns)]][[role grey -]]]
[[cyl_neumann (integer order)[br](424/428 tests selected)][[role red 2.71[br](466ns)]][[role green 1.00[br](172ns)]][[role red 3.95[br](679ns)]][[role red 3.03[br](522ns)]][[role red 5.05[br](869ns)]][[role blue 1.31[br](225ns)]]]
[[cyl_neumann[br](428/450 tests selected)][[role red 14.08[br](8826ns)]][[role red 7.68[br](4814ns)]][[role blue 1.21[br](757ns)]][[role blue 1.26[br](789ns)]][[role green 1.00[br](627ns)]][[role grey -]]]
[[digamma[br](1019/1019 tests selected)][[role blue 1.77[br](62ns)]][[role green 1.00[br](35ns)]][[role grey -]][[role red 2.97[br](104ns)]][[role red 6.20[br](217ns)]][[role grey -]]]
[[ellint_1 (complete)[br](109/109 tests selected)][[role blue 1.48[br](93ns)]][[role green 1.00[br](63ns)]][[role blue 1.56[br](98ns)]][[role red 3.03[br](191ns)]][[role grey -]][[role grey -]]]
[[ellint_1[br](627/629 tests selected)][[role red 2.01[br](310ns)]][[role green 1.00[br](154ns)]][[role blue 1.24[br](191ns)]][[role red 2.13[br](328ns)]][[role grey -]][[role grey -]]]
[[ellint_2 (complete)[br](109/110 tests selected)][[role blue 1.76[br](44ns)]][[role green 1.00[br](25ns)]][[role red 8.76[br](219ns)]][[role red 15.24[br](381ns)]][[role grey -]][[role grey -]]]
[[ellint_2[br](527/530 tests selected)][[role red 2.98[br](704ns)]][[role green 1.00[br](236ns)]][[role blue 1.25[br](294ns)]][[role red 2.15[br](507ns)]][[role grey -]][[role grey -]]]
[[ellint_3 (complete)[br](0/500 tests selected)][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role green -nan[br](0ns)]][[role grey -]][[role grey -]]]
[[ellint_3[br](22/845 tests selected)][[role red 3.52[br](609ns)]][[role blue 1.25[br](217ns)]][[role green 1.00[br](173ns)]][[role blue 1.36[br](235ns)]][[role grey -]][[role grey -]]]
[[ellint_rc[br](201/201 tests selected)][[role red 2.13[br](66ns)]][[role green 1.00[br](31ns)]][[role grey -]][[role red 5.03[br](156ns)]][[role grey -]][[role grey -]]]
[[ellint_rd[br](7588/7588 tests selected)][[role red 3.34[br](428ns)]][[role green 1.00[br](128ns)]][[role grey -]][[role blue 1.88[br](240ns)]][[role grey -]][[role grey -]]]
[[ellint_rf[br](7788/7788 tests selected)][[role blue 1.71[br](84ns)]][[role green 1.00[br](49ns)]][[role grey -]][[role red 4.65[br](228ns)]][[role grey -]][[role grey -]]]
[[ellint_rj[br](7642/8032 tests selected)][[role red 3.04[br](426ns)]][[role green 1.00[br](140ns)]][[role grey -]][[role red 10.84[br](1518ns)]][[role grey -]][[role grey -]]]
[[erf[br](950/950 tests selected)][[role blue 1.71[br](41ns)]][[role grey -]][[role green 1.00[br](24ns)]][[role red 4.21[br](101ns)]][[role grey -]][[role green 1.00[br](24ns)]]]
[[erfc[br](950/950 tests selected)][[role red 3.18[br](89ns)]][[role red 2.32[br](65ns)]][[role green 1.00[br](28ns)]][[role red 3.32[br](93ns)]][[role grey -]][[role green 1.00[br](28ns)]]]
[[expint (En)[br](1059/1059 tests selected)][[role red 2.12[br](242ns)]][[role green 1.00[br](114ns)]][[role grey -]][[role red 3.86[br](440ns)]][[role grey -]][[role grey -]]]
[[expint[br](436/436 tests selected)][[role red 2.02[br](85ns)]][[role green 1.00[br](42ns)]][[role red 2.60[br](109ns)]][[role red 4.10[br](172ns)]][[role grey -]][[role grey -]]]
[[expm1[br](80/80 tests selected)][[role green 1.19[br](19ns)]][[role green 1.00[br](16ns)]][[role green 1.00[br](16ns)]][[role grey -]][[role grey -]][[role green 1.06[br](17ns)]]]
[[gamma_p[br](1379/1379 tests selected)][[role red 2.64[br](618ns)]][[role green 1.11[br](260ns)]][[role grey -]][[role blue 1.79[br](420ns)]][[role green 1.00[br](234ns)]][[role grey -]]]
[[gamma_p_inv[br](559/559 tests selected)][[role red 2.24[br](2328ns)]][[role green 1.00[br](1038ns)]][[role grey -]][[role grey -]][[role green 1.17[br](1211ns)]][[role grey -]]]
[[gamma_q[br](1371/1379 tests selected)][[role red 2.59[br](629ns)]][[role green 1.04[br](253ns)]][[role grey -]][[role red 2.02[br](492ns)]][[role green 1.00[br](243ns)]][[role grey -]]]
[[gamma_q_inv[br](78/559 tests selected)][[role red 2.57[br](2016ns)]][[role blue 1.22[br](958ns)]][[role grey -]][[role grey -]][[role green 1.00[br](783ns)]][[role grey -]]]
[[ibeta[br](3210/3210 tests selected)][[role red 5.33[br](1979ns)]][[role blue 1.41[br](524ns)]][[role grey -]][[role grey -]][[role green 1.00[br](371ns)]][[role grey -]]]
[[ibeta_inv[br](1204/1210 tests selected)][[role red 2.92[br](5552ns)]][[role green 1.00[br](1902ns)]][[role grey -]][[role grey -]][[role red 2.73[br](5188ns)]][[role grey -]]]
[[ibetac[br](3210/3210 tests selected)][[role red 4.44[br](2008ns)]][[role blue 1.31[br](590ns)]][[role grey -]][[role grey -]][[role green 1.00[br](452ns)]][[role grey -]]]
[[ibetac_inv[br](1201/1210 tests selected)][[role red 2.99[br](5533ns)]][[role green 1.00[br](1853ns)]][[role grey -]][[role grey -]][[role red 2.43[br](4504ns)]][[role grey -]]]
[[jacobi_cn[br](2368/2757 tests selected)][[role red 7.00[br](483ns)]][[role red 2.36[br](163ns)]][[role grey -]][[role green 1.00[br](69ns)]][[role grey -]][[role grey -]]]
[[jacobi_dn[br](2368/2757 tests selected)][[role red 7.19[br](503ns)]][[role red 2.47[br](173ns)]][[role grey -]][[role green 1.00[br](70ns)]][[role grey -]][[role grey -]]]
[[jacobi_sn[br](2368/2757 tests selected)][[role red 6.20[br](614ns)]][[role blue 1.82[br](180ns)]][[role grey -]][[role green 1.00[br](99ns)]][[role grey -]][[role grey -]]]
[[laguerre[br](280/280 tests selected)][[role green 1.02[br](92ns)]][[role green 1.00[br](90ns)]][[role green 1.10[br](99ns)]][[role blue 1.49[br](134ns)]][[role grey -]][[role grey -]]]
[[legendre Q[br](300/300 tests selected)][[role blue 1.24[br](390ns)]][[role green 1.00[br](314ns)]][[role grey -]][[role green 1.08[br](340ns)]][[role grey -]][[role grey -]]]
[[legendre[br](300/300 tests selected)][[role green 1.12[br](282ns)]][[role green 1.00[br](251ns)]][[role green 1.16[br](290ns)]][[role blue 1.31[br](328ns)]][[role grey -]][[role grey -]]]
[[lgamma[br](400/400 tests selected)][[role red 5.30[br](265ns)]][[role red 2.34[br](117ns)]][[role green 1.04[br](52ns)]][[role red 4.08[br](204ns)]][[role red 2.08[br](104ns)]][[role green 1.00[br](50ns)]]]
[[log1p[br](80/80 tests selected)][[role green 1.18[br](20ns)]][[role blue 1.29[br](22ns)]][[role green 1.00[br](17ns)]][[role grey -]][[role grey -]][[role green 1.00[br](17ns)]]]
[[polygamma[br](823/1535 tests selected)][[role red 23.31[br](8883ns)]][[role blue 1.98[br](756ns)]][[role grey -]][[role blue 1.57[br](598ns)]][[role green 1.00[br](381ns)]][[role grey -]]]
[[sph_bessel[br](483/483 tests selected)][[role blue 1.91[br](1122ns)]][[role green 1.00[br](588ns)]][[role red 3.14[br](1844ns)]][[role blue 1.96[br](1155ns)]][[role grey -]][[role grey -]]]
[[sph_neumann[br](284/284 tests selected)][[role red 7.16[br](2842ns)]][[role red 2.56[br](1015ns)]][[role red 7.44[br](2953ns)]][[role green 1.00[br](397ns)]][[role grey -]][[role grey -]]]
[[tgamma (incomplete)[br](1266/1379 tests selected)][[role red 2.90[br](669ns)]][[role green 1.00[br](231ns)]][[role grey -]][[role red 2.19[br](505ns)]][[role grey -]][[role grey -]]]
[[tgamma[br](400/400 tests selected)][[role red 4.10[br](472ns)]][[role blue 1.25[br](144ns)]][[role blue 1.33[br](153ns)]][[role green 1.03[br](118ns)]][[role green 1.00[br](115ns)]][[role blue 1.57[br](180ns)]]]
[[trigamma[br](659/659 tests selected)][[role red 2.08[br](25ns)]][[role green 1.00[br](12ns)]][[role grey -]][[role red 53.75[br](645ns)]][[role red 30.92[br](371ns)]][[role grey -]]]
[[zeta[br](448/448 tests selected)][[role red 4.21[br](455ns)]][[role green 1.00[br](108ns)]][[role red 1078.95[br](116527ns)]][[role red 2.03[br](219ns)]][[role grey -]][[role grey -]]]
]
]

[template table_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_[]
[table:table_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_ Polynomial Arithmetic (GNU C++ version 9.2.0, Windows x64)
[[Function][std::uint64_t][double][cpp_int]]
[[operator *][[role green 1.00[br](503ns)]][[role green 1.00[br](502ns)]][[role red 15.20[br](7629ns)]]]
[[operator * (int)][[role green 1.05[br](114ns)]][[role green 1.00[br](109ns)]][[role red 6.04[br](658ns)]]]
[[operator *=][[role green 1.04[br](223824ns)]][[role green 1.00[br](215955ns)]][[role red 19.30[br](4168184ns)]]]
[[operator *= (int)][[role green 1.06[br](13931ns)]][[role green 1.00[br](13163ns)]][[role red 26.10[br](343615ns)]]]
[[operator +][[role green 1.00[br](163ns)]][[role green 1.14[br](186ns)]][[role red 6.04[br](985ns)]]]
[[operator + (int)][[role green 1.16[br](116ns)]][[role green 1.00[br](100ns)]][[role red 4.07[br](407ns)]]]
[[operator +=][[role green 1.12[br](18ns)]][[role green 1.00[br](16ns)]][[role red 22.81[br](365ns)]]]
[[operator += (int)][[role blue 1.33[br](4ns)]][[role green 1.00[br](3ns)]][[role red 33.00[br](99ns)]]]
[[operator -][[role green 1.00[br](159ns)]][[role green 1.16[br](185ns)]][[role red 6.66[br](1059ns)]]]
[[operator - (int)][[role green 1.11[br](113ns)]][[role green 1.00[br](102ns)]][[role red 3.75[br](382ns)]]]
[[operator -=][[role blue 1.38[br](22ns)]][[role green 1.00[br](16ns)]][[role red 23.38[br](374ns)]]]
[[operator -= (int)][[role green 1.00[br](3ns)]][[role green 1.00[br](3ns)]][[role red 31.00[br](93ns)]]]
[[operator /][[role blue 1.44[br](767ns)]][[role green 1.00[br](533ns)]][[role red 41.38[br](22054ns)]]]
[[operator / (int)][[role blue 1.29[br](138ns)]][[role green 1.00[br](107ns)]][[role red 13.58[br](1453ns)]]]
[[operator /=][[role green 1.10[br](11ns)]][[role green 1.00[br](10ns)]][[role red 194.00[br](1940ns)]]]
[[operator /= (int)][[role green 1.00[br](679ns)]][[role red 21.14[br](14351ns)]][[role red 3447.12[br](2340595ns)]]]
]
]

[template table_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[table:table_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64 Rational Method Comparison with GNU C++ version 9.2.0 on Windows x64
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role blue 1.83[br](22ns)]][[role blue 1.83[br](22ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]][[role green 1.17[br](14ns)]][[role green 1.08[br](13ns)]]]
[[Order  3][[role blue 1.83[br](33ns)]][[role red 2.17[br](39ns)]][[role blue 1.56[br](28ns)]][[role blue 1.44[br](26ns)]][[role green 1.00[br](18ns)]][[role green 1.00[br](18ns)]][[role green 1.00[br](18ns)]][[role green 1.00[br](18ns)]]]
[[Order  4][[role blue 1.65[br](43ns)]][[role blue 2.00[br](52ns)]][[role blue 1.46[br](38ns)]][[role blue 1.46[br](38ns)]][[role green 1.00[br](26ns)]][[role green 1.00[br](26ns)]][[role green 1.04[br](27ns)]][[role green 1.12[br](29ns)]]]
[[Order  5][[role green 1.17[br](56ns)]][[role blue 1.40[br](67ns)]][[role green 1.02[br](49ns)]][[role green 1.00[br](48ns)]][[role green 1.12[br](54ns)]][[role green 1.10[br](53ns)]][[role green 1.17[br](56ns)]][[role green 1.12[br](54ns)]]]
[[Order  6][[role green 1.02[br](62ns)]][[role blue 1.25[br](76ns)]][[role green 1.00[br](61ns)]][[role green 1.00[br](61ns)]][[role green 1.02[br](62ns)]][[role green 1.02[br](62ns)]][[role green 1.05[br](64ns)]][[role blue 1.30[br](79ns)]]]
[[Order  7][[role green 1.03[br](74ns)]][[role blue 1.29[br](93ns)]][[role green 1.01[br](73ns)]][[role green 1.00[br](72ns)]][[role green 1.01[br](73ns)]][[role green 1.01[br](73ns)]][[role green 1.03[br](74ns)]][[role green 1.01[br](73ns)]]]
[[Order  8][[role green 1.10[br](90ns)]][[role blue 1.27[br](104ns)]][[role green 1.00[br](82ns)]][[role green 1.00[br](82ns)]][[role green 1.00[br](82ns)]][[role green 1.02[br](84ns)]][[role green 1.12[br](92ns)]][[role green 1.05[br](86ns)]]]
[[Order  9][[role blue 1.27[br](119ns)]][[role blue 1.66[br](156ns)]][[role green 1.03[br](97ns)]][[role green 1.02[br](96ns)]][[role green 1.00[br](94ns)]][[role green 1.01[br](95ns)]][[role green 1.00[br](94ns)]][[role green 1.01[br](95ns)]]]
[[Order 10][[role blue 1.22[br](128ns)]][[role blue 1.40[br](147ns)]][[role green 1.06[br](111ns)]][[role green 1.07[br](112ns)]][[role green 1.00[br](105ns)]][[role green 1.02[br](107ns)]][[role green 1.00[br](105ns)]][[role green 1.08[br](113ns)]]]
[[Order 11][[role green 1.20[br](140ns)]][[role blue 1.44[br](169ns)]][[role green 1.07[br](125ns)]][[role green 1.06[br](124ns)]][[role green 1.00[br](117ns)]][[role green 1.07[br](125ns)]][[role green 1.01[br](118ns)]][[role green 1.04[br](122ns)]]]
[[Order 12][[role blue 1.24[br](155ns)]][[role blue 1.32[br](165ns)]][[role green 1.10[br](137ns)]][[role green 1.12[br](140ns)]][[role green 1.02[br](128ns)]][[role blue 1.23[br](154ns)]][[role green 1.04[br](130ns)]][[role green 1.00[br](125ns)]]]
[[Order 13][[role blue 1.27[br](171ns)]][[role blue 1.36[br](183ns)]][[role green 1.18[br](159ns)]][[role green 1.13[br](153ns)]][[role green 1.06[br](143ns)]][[role green 1.00[br](135ns)]][[role green 1.01[br](136ns)]][[role green 1.02[br](138ns)]]]
[[Order 14][[role green 1.16[br](178ns)]][[role blue 1.28[br](196ns)]][[role green 1.10[br](168ns)]][[role green 1.08[br](166ns)]][[role green 1.14[br](174ns)]][[role green 1.10[br](168ns)]][[role green 1.13[br](173ns)]][[role green 1.00[br](153ns)]]]
[[Order 15][[role blue 1.32[br](196ns)]][[role blue 1.47[br](217ns)]][[role blue 1.23[br](182ns)]][[role blue 1.22[br](181ns)]][[role green 1.00[br](148ns)]][[role green 1.01[br](150ns)]][[role green 1.15[br](170ns)]][[role green 1.03[br](152ns)]]]
[[Order 16][[role blue 1.31[br](209ns)]][[role blue 1.39[br](223ns)]][[role blue 1.26[br](202ns)]][[role blue 1.28[br](205ns)]][[role green 1.00[br](160ns)]][[role green 1.01[br](161ns)]][[role green 1.09[br](174ns)]][[role green 1.01[br](161ns)]]]
[[Order 17][[role blue 1.34[br](221ns)]][[role blue 1.46[br](241ns)]][[role blue 1.32[br](217ns)]][[role blue 1.37[br](226ns)]][[role green 1.00[br](165ns)]][[role green 1.06[br](175ns)]][[role green 1.08[br](178ns)]][[role green 1.00[br](165ns)]]]
[[Order 18][[role blue 1.52[br](264ns)]][[role blue 1.53[br](266ns)]][[role blue 1.41[br](246ns)]][[role blue 1.43[br](249ns)]][[role blue 1.23[br](214ns)]][[role green 1.03[br](179ns)]][[role green 1.00[br](174ns)]][[role green 1.05[br](182ns)]]]
[[Order 19][[role blue 1.35[br](252ns)]][[role blue 1.56[br](292ns)]][[role blue 1.54[br](288ns)]][[role blue 1.39[br](259ns)]][[role green 1.00[br](187ns)]][[role blue 1.22[br](228ns)]][[role green 1.02[br](191ns)]][[role green 1.04[br](195ns)]]]
[[Order 20][[role blue 1.34[br](271ns)]][[role blue 1.59[br](322ns)]][[role blue 1.39[br](280ns)]][[role blue 1.46[br](294ns)]][[role green 1.06[br](214ns)]][[role green 1.01[br](205ns)]][[role green 1.00[br](202ns)]][[role green 1.00[br](202ns)]]]
]
]

[template table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[table:table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64 Polynomial Method Comparison with GNU C++ version 9.2.0 on Windows x64
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]]]
[[Order  3][[role blue 1.56[br](14ns)]][[role red 2.56[br](23ns)]][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role green 1.11[br](10ns)]][[role green 1.00[br](9ns)]]]
[[Order  4][[role blue 1.50[br](18ns)]][[role red 2.42[br](29ns)]][[role green 1.08[br](13ns)]][[role green 1.00[br](12ns)]][[role green 1.08[br](13ns)]][[role green 1.08[br](13ns)]][[role green 1.08[br](13ns)]][[role green 1.08[br](13ns)]]]
[[Order  5][[role blue 1.38[br](22ns)]][[role red 2.31[br](37ns)]][[role green 1.06[br](17ns)]][[role green 1.00[br](16ns)]][[role green 1.06[br](17ns)]][[role green 1.12[br](18ns)]][[role green 1.12[br](18ns)]][[role green 1.12[br](18ns)]]]
[[Order  6][[role blue 1.48[br](31ns)]][[role red 2.14[br](45ns)]][[role green 1.00[br](21ns)]][[role green 1.00[br](21ns)]][[role green 1.05[br](22ns)]][[role green 1.05[br](22ns)]][[role blue 1.24[br](26ns)]][[role green 1.05[br](22ns)]]]
[[Order  7][[role blue 1.31[br](34ns)]][[role red 2.15[br](56ns)]][[role green 1.00[br](26ns)]][[role green 1.00[br](26ns)]][[role green 1.00[br](26ns)]][[role green 1.12[br](29ns)]][[role green 1.04[br](27ns)]][[role green 1.19[br](31ns)]]]
[[Order  8][[role blue 1.37[br](41ns)]][[role red 2.23[br](67ns)]][[role green 1.07[br](32ns)]][[role green 1.03[br](31ns)]][[role green 1.10[br](33ns)]][[role green 1.03[br](31ns)]][[role green 1.20[br](36ns)]][[role green 1.00[br](30ns)]]]
[[Order  9][[role blue 1.58[br](52ns)]][[role red 2.42[br](80ns)]][[role green 1.15[br](38ns)]][[role green 1.15[br](38ns)]][[role green 1.00[br](33ns)]][[role green 1.00[br](33ns)]][[role green 1.00[br](33ns)]][[role green 1.03[br](34ns)]]]
[[Order 10][[role blue 1.51[br](56ns)]][[role red 2.41[br](89ns)]][[role blue 1.22[br](45ns)]][[role blue 1.22[br](45ns)]][[role green 1.00[br](37ns)]][[role green 1.03[br](38ns)]][[role green 1.05[br](39ns)]][[role green 1.05[br](39ns)]]]
[[Order 11][[role blue 1.56[br](64ns)]][[role red 2.46[br](101ns)]][[role blue 1.27[br](52ns)]][[role blue 1.27[br](52ns)]][[role green 1.00[br](41ns)]][[role green 1.00[br](41ns)]][[role green 1.00[br](41ns)]][[role green 1.00[br](41ns)]]]
[[Order 12][[role blue 1.70[br](78ns)]][[role red 2.63[br](121ns)]][[role blue 1.30[br](60ns)]][[role blue 1.28[br](59ns)]][[role green 1.00[br](46ns)]][[role green 1.04[br](48ns)]][[role green 1.02[br](47ns)]][[role green 1.02[br](47ns)]]]
[[Order 13][[role blue 1.78[br](87ns)]][[role red 2.78[br](136ns)]][[role blue 1.29[br](63ns)]][[role blue 1.29[br](63ns)]][[role green 1.00[br](49ns)]][[role green 1.02[br](50ns)]][[role green 1.00[br](49ns)]][[role green 1.00[br](49ns)]]]
[[Order 14][[role blue 1.79[br](95ns)]][[role red 2.75[br](146ns)]][[role blue 1.43[br](76ns)]][[role blue 1.43[br](76ns)]][[role green 1.00[br](53ns)]][[role green 1.02[br](54ns)]][[role green 1.00[br](53ns)]][[role green 1.00[br](53ns)]]]
[[Order 15][[role blue 1.63[br](103ns)]][[role red 2.51[br](158ns)]][[role blue 1.33[br](84ns)]][[role blue 1.43[br](90ns)]][[role green 1.02[br](64ns)]][[role green 1.02[br](64ns)]][[role green 1.00[br](63ns)]][[role green 1.02[br](64ns)]]]
[[Order 16][[role blue 1.61[br](119ns)]][[role red 2.31[br](171ns)]][[role blue 1.31[br](97ns)]][[role blue 1.31[br](97ns)]][[role green 1.01[br](75ns)]][[role green 1.01[br](75ns)]][[role green 1.01[br](75ns)]][[role green 1.00[br](74ns)]]]
[[Order 17][[role blue 1.67[br](127ns)]][[role red 2.42[br](184ns)]][[role blue 1.42[br](108ns)]][[role blue 1.41[br](107ns)]][[role green 1.00[br](76ns)]][[role green 1.00[br](76ns)]][[role green 1.01[br](77ns)]][[role green 1.01[br](77ns)]]]
[[Order 18][[role blue 1.66[br](136ns)]][[role red 2.39[br](196ns)]][[role blue 1.41[br](116ns)]][[role blue 1.44[br](118ns)]][[role green 1.05[br](86ns)]][[role green 1.02[br](84ns)]][[role green 1.06[br](87ns)]][[role green 1.00[br](82ns)]]]
[[Order 19][[role blue 1.72[br](146ns)]][[role red 2.51[br](213ns)]][[role blue 1.59[br](135ns)]][[role blue 1.56[br](133ns)]][[role green 1.01[br](86ns)]][[role green 1.01[br](86ns)]][[role green 1.00[br](85ns)]][[role green 1.02[br](87ns)]]]
[[Order 20][[role blue 1.70[br](158ns)]][[role red 2.52[br](234ns)]][[role blue 1.55[br](144ns)]][[role blue 1.59[br](148ns)]][[role green 1.05[br](98ns)]][[role green 1.02[br](95ns)]][[role green 1.00[br](93ns)]][[role green 1.06[br](99ns)]]]
]
]

[template table_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64[]
[table:table_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64 Distribution performance comparison for different performance options with GNU C++ version 9.2.0 on Windows x64
[[Function][boost 1.73][Boost[br]promote_double<false>][Boost[br]promote_double<false>[br]digits10<10>][Boost[br]float[br]promote_float<false>]]
[[ArcSine (CDF)][[role green 1.10[br](22ns)]][[role blue 1.30[br](26ns)]][[role green 1.00[br](20ns)]][[role red 3.20[br](64ns)]]]
[[ArcSine (PDF)][[role green 1.00[br](5ns)]][[role green 1.00[br](5ns)]][[role green 1.00[br](5ns)]][[role blue 1.60[br](8ns)]]]
[[ArcSine (quantile)][[role green 1.00[br](53ns)]][[role green 1.00[br](53ns)]][[role green 1.02[br](54ns)]][[role green 1.04[br](55ns)]]]
[[Beta (CDF)][[role red 2.32[br](362ns)]][[role blue 1.31[br](205ns)]][[role green 1.17[br](183ns)]][[role green 1.00[br](156ns)]]]
[[Beta (PDF)][[role red 2.44[br](302ns)]][[role green 1.12[br](139ns)]][[role green 1.13[br](140ns)]][[role green 1.00[br](124ns)]]]
[[Beta (quantile)][[role blue 1.76[br](1968ns)]][[role blue 1.24[br](1383ns)]][[role green 1.00[br](1118ns)]][[role green 1.03[br](1155ns)]]]
[[Binomial (CDF)][[role red 3.57[br](959ns)]][[role blue 1.30[br](350ns)]][[role blue 1.27[br](341ns)]][[role green 1.00[br](269ns)]]]
[[Binomial (PDF)][[role red 2.39[br](339ns)]][[role green 1.00[br](142ns)]][[role blue 1.20[br](171ns)]][[role green 1.04[br](148ns)]]]
[[Binomial (quantile)][[role red 3.20[br](4255ns)]][[role blue 1.42[br](1884ns)]][[role green 1.19[br](1582ns)]][[role green 1.00[br](1328ns)]]]
[[Cauchy (CDF)][[role green 1.12[br](19ns)]][[role green 1.18[br](20ns)]][[role green 1.00[br](17ns)]][[role red 3.18[br](54ns)]]]
[[Cauchy (PDF)][[role blue 1.33[br](4ns)]][[role blue 1.67[br](5ns)]][[role green 1.00[br](3ns)]][[role blue 1.33[br](4ns)]]]
[[Cauchy (quantile)][[role blue 1.32[br](25ns)]][[role blue 1.21[br](23ns)]][[role green 1.00[br](19ns)]][[role blue 1.21[br](23ns)]]]
[[ChiSquared (CDF)][[role red 2.79[br](953ns)]][[role blue 1.55[br](529ns)]][[role blue 1.27[br](434ns)]][[role green 1.00[br](341ns)]]]
[[ChiSquared (PDF)][[role blue 1.82[br](189ns)]][[role green 1.00[br](104ns)]][[role green 1.01[br](105ns)]][[role green 1.02[br](106ns)]]]
[[ChiSquared (quantile)][[role red 2.40[br](1452ns)]][[role blue 1.49[br](901ns)]][[role green 1.19[br](717ns)]][[role green 1.00[br](605ns)]]]
[[Exponential (CDF)][[role green 1.14[br](33ns)]][[role green 1.00[br](29ns)]][[role green 1.03[br](30ns)]][[role green 1.00[br](29ns)]]]
[[Exponential (PDF)][[role green 1.08[br](54ns)]][[role green 1.02[br](51ns)]][[role green 1.00[br](50ns)]][[role green 1.04[br](52ns)]]]
[[Exponential (quantile)][[role blue 1.89[br](36ns)]][[role green 1.00[br](19ns)]][[role green 1.05[br](20ns)]][[role blue 1.21[br](23ns)]]]
[[ExtremeValue (CDF)][[role green 1.05[br](104ns)]][[role green 1.02[br](101ns)]][[role green 1.00[br](99ns)]][[role green 1.04[br](103ns)]]]
[[ExtremeValue (PDF)][[role green 1.04[br](144ns)]][[role green 1.04[br](144ns)]][[role green 1.00[br](138ns)]][[role green 1.03[br](142ns)]]]
[[ExtremeValue (quantile)][[role green 1.07[br](64ns)]][[role green 1.02[br](61ns)]][[role green 1.00[br](60ns)]][[role green 1.13[br](68ns)]]]
[[F (CDF)][[role red 3.55[br](668ns)]][[role blue 1.58[br](297ns)]][[role blue 1.23[br](232ns)]][[role green 1.00[br](188ns)]]]
[[F (PDF)][[role red 2.29[br](291ns)]][[role green 1.06[br](135ns)]][[role green 1.02[br](129ns)]][[role green 1.00[br](127ns)]]]
[[F (quantile)][[role red 2.17[br](2215ns)]][[role green 1.14[br](1163ns)]][[role green 1.00[br](1023ns)]][[role green 1.07[br](1090ns)]]]
[[Gamma (CDF)][[role blue 1.94[br](492ns)]][[role green 1.19[br](301ns)]][[role green 1.10[br](280ns)]][[role green 1.00[br](254ns)]]]
[[Gamma (PDF)][[role blue 1.55[br](236ns)]][[role green 1.00[br](152ns)]][[role green 1.00[br](152ns)]][[role green 1.01[br](153ns)]]]
[[Gamma (quantile)][[role blue 1.95[br](1204ns)]][[role blue 1.35[br](837ns)]][[role green 1.00[br](619ns)]][[role green 1.04[br](644ns)]]]
[[Geometric (CDF)][[role blue 1.38[br](40ns)]][[role green 1.00[br](29ns)]][[role green 1.00[br](29ns)]][[role green 1.07[br](31ns)]]]
[[Geometric (PDF)][[role green 1.00[br](46ns)]][[role green 1.00[br](46ns)]][[role green 1.00[br](46ns)]][[role green 1.02[br](47ns)]]]
[[Geometric (quantile)][[role blue 1.64[br](36ns)]][[role green 1.00[br](22ns)]][[role green 1.00[br](22ns)]][[role green 1.09[br](24ns)]]]
[[Hypergeometric (CDF)][[role green 1.11[br](49938ns)]][[role green 1.00[br](45127ns)]][[role green 1.01[br](45445ns)]][[role green 1.12[br](50682ns)]]]
[[Hypergeometric (PDF)][[role green 1.13[br](53353ns)]][[role green 1.04[br](49364ns)]][[role green 1.00[br](47376ns)]][[role blue 1.20[br](57034ns)]]]
[[Hypergeometric (quantile)][[role green 1.00[br](105555ns)]][[role blue 1.25[br](132253ns)]][[role blue 1.36[br](143254ns)]][[role blue 1.70[br](179941ns)]]]
[[InverseChiSquared (CDF)][[role red 3.48[br](1326ns)]][[role blue 1.70[br](647ns)]][[role blue 1.33[br](508ns)]][[role green 1.00[br](381ns)]]]
[[InverseChiSquared (PDF)][[role blue 1.87[br](217ns)]][[role green 1.09[br](126ns)]][[role green 1.00[br](116ns)]][[role green 1.01[br](117ns)]]]
[[InverseChiSquared (quantile)][[role red 2.81[br](1852ns)]][[role blue 1.57[br](1035ns)]][[role blue 1.22[br](800ns)]][[role green 1.00[br](658ns)]]]
[[InverseGamma (CDF)][[role blue 1.95[br](516ns)]][[role blue 1.21[br](320ns)]][[role green 1.09[br](289ns)]][[role green 1.00[br](264ns)]]]
[[InverseGamma (PDF)][[role blue 1.67[br](256ns)]][[role green 1.09[br](167ns)]][[role green 1.05[br](161ns)]][[role green 1.00[br](153ns)]]]
[[InverseGamma (quantile)][[role blue 1.94[br](1268ns)]][[role blue 1.36[br](884ns)]][[role green 1.00[br](652ns)]][[role green 1.02[br](666ns)]]]
[[InverseGaussian (CDF)][[role blue 1.83[br](172ns)]][[role blue 1.83[br](172ns)]][[role blue 1.82[br](171ns)]][[role green 1.00[br](94ns)]]]
[[InverseGaussian (PDF)][[role green 1.00[br](28ns)]][[role green 1.14[br](32ns)]][[role green 1.00[br](28ns)]][[role green 1.07[br](30ns)]]]
[[InverseGaussian (quantile)][[role blue 1.94[br](2657ns)]][[role blue 1.93[br](2635ns)]][[role blue 1.72[br](2359ns)]][[role green 1.00[br](1368ns)]]]
[[Laplace (CDF)][[role green 1.02[br](50ns)]][[role green 1.00[br](49ns)]][[role green 1.00[br](49ns)]][[role green 1.08[br](53ns)]]]
[[Laplace (PDF)][[role green 1.00[br](49ns)]][[role green 1.02[br](50ns)]][[role green 1.00[br](49ns)]][[role green 1.04[br](51ns)]]]
[[Laplace (quantile)][[role green 1.03[br](33ns)]][[role green 1.03[br](33ns)]][[role green 1.00[br](32ns)]][[role green 1.16[br](37ns)]]]
[[LogNormal (CDF)][[role blue 1.73[br](176ns)]][[role blue 1.25[br](127ns)]][[role blue 1.28[br](131ns)]][[role green 1.00[br](102ns)]]]
[[LogNormal (PDF)][[role green 1.04[br](87ns)]][[role green 1.02[br](86ns)]][[role green 1.00[br](84ns)]][[role green 1.07[br](90ns)]]]
[[LogNormal (quantile)][[role blue 1.23[br](116ns)]][[role green 1.00[br](94ns)]][[role green 1.01[br](95ns)]][[role green 1.06[br](100ns)]]]
[[Logistic (CDF)][[role green 1.00[br](46ns)]][[role green 1.02[br](47ns)]][[role green 1.00[br](46ns)]][[role green 1.02[br](47ns)]]]
[[Logistic (PDF)][[role green 1.00[br](46ns)]][[role green 1.02[br](47ns)]][[role green 1.02[br](47ns)]][[role green 1.07[br](49ns)]]]
[[Logistic (quantile)][[role green 1.00[br](33ns)]][[role green 1.03[br](34ns)]][[role green 1.00[br](33ns)]][[role green 1.15[br](38ns)]]]
[[NegativeBinomial (CDF)][[role red 3.95[br](1158ns)]][[role blue 1.66[br](485ns)]][[role blue 1.32[br](386ns)]][[role green 1.00[br](293ns)]]]
[[NegativeBinomial (PDF)][[role red 2.29[br](307ns)]][[role green 1.01[br](135ns)]][[role green 1.00[br](134ns)]][[role green 1.01[br](136ns)]]]
[[NegativeBinomial (quantile)][[role red 2.81[br](6154ns)]][[role green 1.19[br](2608ns)]][[role green 1.00[br](2190ns)]][[role blue 1.26[br](2752ns)]]]
[[NonCentralBeta (CDF)][[role red 2.62[br](1450ns)]][[role blue 1.46[br](806ns)]][[role blue 1.28[br](708ns)]][[role green 1.00[br](553ns)]]]
[[NonCentralBeta (PDF)][[role red 2.58[br](969ns)]][[role blue 1.31[br](490ns)]][[role green 1.15[br](433ns)]][[role green 1.00[br](375ns)]]]
[[NonCentralBeta (quantile)][[role red 3.69[br](37583ns)]][[role blue 2.00[br](20369ns)]][[role blue 1.81[br](18498ns)]][[role green 1.00[br](10193ns)]]]
[[NonCentralChiSquared (CDF)][[role red 2.22[br](4037ns)]][[role blue 1.79[br](3256ns)]][[role blue 1.28[br](2332ns)]][[role green 1.00[br](1819ns)]]]
[[NonCentralChiSquared (PDF)][[role blue 1.58[br](630ns)]][[role blue 1.29[br](514ns)]][[role green 1.00[br](399ns)]][[role green 1.03[br](409ns)]]]
[[NonCentralChiSquared (quantile)][[role red 3.14[br](33255ns)]][[role blue 1.94[br](20620ns)]][[role blue 1.26[br](13388ns)]][[role green 1.00[br](10603ns)]]]
[[NonCentralF (CDF)][[role red 2.48[br](1426ns)]][[role blue 1.32[br](762ns)]][[role green 1.13[br](652ns)]][[role green 1.00[br](576ns)]]]
[[NonCentralF (PDF)][[role red 2.74[br](1306ns)]][[role blue 1.37[br](652ns)]][[role green 1.15[br](548ns)]][[role green 1.00[br](477ns)]]]
[[NonCentralF (quantile)][[role red 2.64[br](22025ns)]][[role blue 1.38[br](11560ns)]][[role green 1.12[br](9319ns)]][[role green 1.00[br](8356ns)]]]
[[NonCentralT (CDF)][[role red 3.75[br](6473ns)]][[role blue 1.83[br](3155ns)]][[role blue 1.63[br](2819ns)]][[role green 1.00[br](1727ns)]]]
[[NonCentralT (PDF)][[role red 2.81[br](4098ns)]][[role blue 1.40[br](2040ns)]][[role blue 1.42[br](2066ns)]][[role green 1.00[br](1456ns)]]]
[[NonCentralT (quantile)][[role red 3.80[br](65926ns)]][[role blue 1.87[br](32431ns)]][[role blue 1.37[br](23756ns)]][[role green 1.00[br](17331ns)]]]
[[Normal (CDF)][[role blue 1.78[br](135ns)]][[role blue 1.53[br](116ns)]][[role blue 1.22[br](93ns)]][[role green 1.00[br](76ns)]]]
[[Normal (PDF)][[role green 1.00[br](48ns)]][[role blue 1.23[br](59ns)]][[role green 1.19[br](57ns)]][[role blue 1.33[br](64ns)]]]
[[Normal (quantile)][[role blue 1.45[br](80ns)]][[role green 1.00[br](55ns)]][[role green 1.00[br](55ns)]][[role green 1.02[br](56ns)]]]
[[Pareto (CDF)][[role green 1.13[br](59ns)]][[role green 1.00[br](52ns)]][[role green 1.15[br](60ns)]][[role green 1.06[br](55ns)]]]
[[Pareto (PDF)][[role green 1.07[br](96ns)]][[role green 1.02[br](92ns)]][[role green 1.08[br](97ns)]][[role green 1.00[br](90ns)]]]
[[Pareto (quantile)][[role green 1.00[br](82ns)]][[role green 1.02[br](84ns)]][[role green 1.00[br](82ns)]][[role green 1.04[br](85ns)]]]
[[Poisson (CDF)][[role blue 1.97[br](254ns)]][[role green 1.04[br](134ns)]][[role green 1.16[br](150ns)]][[role green 1.00[br](129ns)]]]
[[Poisson (PDF)][[role blue 1.61[br](171ns)]][[role green 1.06[br](112ns)]][[role green 1.00[br](106ns)]][[role green 1.05[br](111ns)]]]
[[Poisson (quantile)][[role red 2.16[br](1128ns)]][[role blue 1.23[br](641ns)]][[role blue 1.26[br](657ns)]][[role green 1.00[br](523ns)]]]
[[Rayleigh (CDF)][[role blue 1.24[br](47ns)]][[role green 1.00[br](38ns)]][[role green 1.03[br](39ns)]][[role green 1.08[br](41ns)]]]
[[Rayleigh (PDF)][[role green 1.00[br](64ns)]][[role green 1.09[br](70ns)]][[role green 1.03[br](66ns)]][[role green 1.09[br](70ns)]]]
[[Rayleigh (quantile)][[role blue 2.00[br](48ns)]][[role green 1.00[br](24ns)]][[role green 1.08[br](26ns)]][[role blue 1.29[br](31ns)]]]
[[SkewNormal (CDF)][[role blue 1.40[br](669ns)]][[role blue 1.32[br](632ns)]][[role green 1.15[br](549ns)]][[role green 1.00[br](479ns)]]]
[[SkewNormal (PDF)][[role blue 1.27[br](173ns)]][[role green 1.17[br](159ns)]][[role green 1.15[br](156ns)]][[role green 1.00[br](136ns)]]]
[[SkewNormal (quantile)][[role red 2.15[br](6968ns)]][[role blue 1.82[br](5903ns)]][[role blue 1.32[br](4287ns)]][[role green 1.00[br](3237ns)]]]
[[StudentsT (CDF)][[role red 3.15[br](1151ns)]][[role blue 1.98[br](721ns)]][[role red 2.03[br](741ns)]][[role green 1.00[br](365ns)]]]
[[StudentsT (PDF)][[role red 2.12[br](360ns)]][[role green 1.09[br](186ns)]][[role green 1.11[br](188ns)]][[role green 1.00[br](170ns)]]]
[[StudentsT (quantile)][[role blue 1.70[br](1461ns)]][[role blue 1.35[br](1161ns)]][[role blue 1.28[br](1099ns)]][[role green 1.00[br](859ns)]]]
[[Weibull (CDF)][[role blue 1.30[br](96ns)]][[role green 1.04[br](77ns)]][[role green 1.15[br](85ns)]][[role green 1.00[br](74ns)]]]
[[Weibull (PDF)][[role green 1.18[br](164ns)]][[role green 1.00[br](139ns)]][[role green 1.15[br](160ns)]][[role green 1.06[br](148ns)]]]
[[Weibull (quantile)][[role green 1.12[br](133ns)]][[role green 1.13[br](134ns)]][[role green 1.00[br](119ns)]][[role green 1.03[br](123ns)]]]
]
]

[template table_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[table:table_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64 Distribution performance comparison with GNU C++ version 9.2.0 on Windows x64
[[Function][boost 1.73][Boost[br]promote_double<false>]]
[[ArcSine (CDF)][[role green 1.00[br](22ns)]][[role green 1.18[br](26ns)]]]
[[ArcSine (PDF)][[role green 1.00[br](5ns)]][[role green 1.00[br](5ns)]]]
[[ArcSine (quantile)][[role green 1.00[br](53ns)]][[role green 1.00[br](53ns)]]]
[[Beta (CDF)][[role blue 1.77[br](362ns)]][[role green 1.00[br](205ns)]]]
[[Beta (PDF)][[role red 2.17[br](302ns)]][[role green 1.00[br](139ns)]]]
[[Beta (quantile)][[role blue 1.42[br](1968ns)]][[role green 1.00[br](1383ns)]]]
[[Binomial (CDF)][[role red 2.74[br](959ns)]][[role green 1.00[br](350ns)]]]
[[Binomial (PDF)][[role red 2.39[br](339ns)]][[role green 1.00[br](142ns)]]]
[[Binomial (quantile)][[role red 2.26[br](4255ns)]][[role green 1.00[br](1884ns)]]]
[[Cauchy (CDF)][[role green 1.00[br](19ns)]][[role green 1.05[br](20ns)]]]
[[Cauchy (PDF)][[role green 1.00[br](4ns)]][[role blue 1.25[br](5ns)]]]
[[Cauchy (quantile)][[role green 1.09[br](25ns)]][[role green 1.00[br](23ns)]]]
[[ChiSquared (CDF)][[role blue 1.80[br](953ns)]][[role green 1.00[br](529ns)]]]
[[ChiSquared (PDF)][[role blue 1.82[br](189ns)]][[role green 1.00[br](104ns)]]]
[[ChiSquared (quantile)][[role blue 1.61[br](1452ns)]][[role green 1.00[br](901ns)]]]
[[Exponential (CDF)][[role green 1.14[br](33ns)]][[role green 1.00[br](29ns)]]]
[[Exponential (PDF)][[role green 1.06[br](54ns)]][[role green 1.00[br](51ns)]]]
[[Exponential (quantile)][[role blue 1.89[br](36ns)]][[role green 1.00[br](19ns)]]]
[[ExtremeValue (CDF)][[role green 1.03[br](104ns)]][[role green 1.00[br](101ns)]]]
[[ExtremeValue (PDF)][[role green 1.00[br](144ns)]][[role green 1.00[br](144ns)]]]
[[ExtremeValue (quantile)][[role green 1.05[br](64ns)]][[role green 1.00[br](61ns)]]]
[[F (CDF)][[role red 2.25[br](668ns)]][[role green 1.00[br](297ns)]]]
[[F (PDF)][[role red 2.16[br](291ns)]][[role green 1.00[br](135ns)]]]
[[F (quantile)][[role blue 1.90[br](2215ns)]][[role green 1.00[br](1163ns)]]]
[[Gamma (CDF)][[role blue 1.63[br](492ns)]][[role green 1.00[br](301ns)]]]
[[Gamma (PDF)][[role blue 1.55[br](236ns)]][[role green 1.00[br](152ns)]]]
[[Gamma (quantile)][[role blue 1.44[br](1204ns)]][[role green 1.00[br](837ns)]]]
[[Geometric (CDF)][[role blue 1.38[br](40ns)]][[role green 1.00[br](29ns)]]]
[[Geometric (PDF)][[role green 1.00[br](46ns)]][[role green 1.00[br](46ns)]]]
[[Geometric (quantile)][[role blue 1.64[br](36ns)]][[role green 1.00[br](22ns)]]]
[[Hypergeometric (CDF)][[role green 1.11[br](49938ns)]][[role green 1.00[br](45127ns)]]]
[[Hypergeometric (PDF)][[role green 1.08[br](53353ns)]][[role green 1.00[br](49364ns)]]]
[[Hypergeometric (quantile)][[role green 1.00[br](105555ns)]][[role blue 1.25[br](132253ns)]]]
[[InverseChiSquared (CDF)][[role red 2.05[br](1326ns)]][[role green 1.00[br](647ns)]]]
[[InverseChiSquared (PDF)][[role blue 1.72[br](217ns)]][[role green 1.00[br](126ns)]]]
[[InverseChiSquared (quantile)][[role blue 1.79[br](1852ns)]][[role green 1.00[br](1035ns)]]]
[[InverseGamma (CDF)][[role blue 1.61[br](516ns)]][[role green 1.00[br](320ns)]]]
[[InverseGamma (PDF)][[role blue 1.53[br](256ns)]][[role green 1.00[br](167ns)]]]
[[InverseGamma (quantile)][[role blue 1.43[br](1268ns)]][[role green 1.00[br](884ns)]]]
[[InverseGaussian (CDF)][[role green 1.00[br](172ns)]][[role green 1.00[br](172ns)]]]
[[InverseGaussian (PDF)][[role green 1.00[br](28ns)]][[role green 1.14[br](32ns)]]]
[[InverseGaussian (quantile)][[role green 1.01[br](2657ns)]][[role green 1.00[br](2635ns)]]]
[[Laplace (CDF)][[role green 1.02[br](50ns)]][[role green 1.00[br](49ns)]]]
[[Laplace (PDF)][[role green 1.00[br](49ns)]][[role green 1.02[br](50ns)]]]
[[Laplace (quantile)][[role green 1.00[br](33ns)]][[role green 1.00[br](33ns)]]]
[[LogNormal (CDF)][[role blue 1.39[br](176ns)]][[role green 1.00[br](127ns)]]]
[[LogNormal (PDF)][[role green 1.01[br](87ns)]][[role green 1.00[br](86ns)]]]
[[LogNormal (quantile)][[role blue 1.23[br](116ns)]][[role green 1.00[br](94ns)]]]
[[Logistic (CDF)][[role green 1.00[br](46ns)]][[role green 1.02[br](47ns)]]]
[[Logistic (PDF)][[role green 1.00[br](46ns)]][[role green 1.02[br](47ns)]]]
[[Logistic (quantile)][[role green 1.00[br](33ns)]][[role green 1.03[br](34ns)]]]
[[NegativeBinomial (CDF)][[role red 2.39[br](1158ns)]][[role green 1.00[br](485ns)]]]
[[NegativeBinomial (PDF)][[role red 2.27[br](307ns)]][[role green 1.00[br](135ns)]]]
[[NegativeBinomial (quantile)][[role red 2.36[br](6154ns)]][[role green 1.00[br](2608ns)]]]
[[NonCentralBeta (CDF)][[role blue 1.80[br](1450ns)]][[role green 1.00[br](806ns)]]]
[[NonCentralBeta (PDF)][[role blue 1.98[br](969ns)]][[role green 1.00[br](490ns)]]]
[[NonCentralBeta (quantile)][[role blue 1.85[br](37583ns)]][[role green 1.00[br](20369ns)]]]
[[NonCentralChiSquared (CDF)][[role blue 1.24[br](4037ns)]][[role green 1.00[br](3256ns)]]]
[[NonCentralChiSquared (PDF)][[role blue 1.23[br](630ns)]][[role green 1.00[br](514ns)]]]
[[NonCentralChiSquared (quantile)][[role blue 1.61[br](33255ns)]][[role green 1.00[br](20620ns)]]]
[[NonCentralF (CDF)][[role blue 1.87[br](1426ns)]][[role green 1.00[br](762ns)]]]
[[NonCentralF (PDF)][[role red 2.00[br](1306ns)]][[role green 1.00[br](652ns)]]]
[[NonCentralF (quantile)][[role blue 1.91[br](22025ns)]][[role green 1.00[br](11560ns)]]]
[[NonCentralT (CDF)][[role red 2.05[br](6473ns)]][[role green 1.00[br](3155ns)]]]
[[NonCentralT (PDF)][[role red 2.01[br](4098ns)]][[role green 1.00[br](2040ns)]]]
[[NonCentralT (quantile)][[role red 2.03[br](65926ns)]][[role green 1.00[br](32431ns)]]]
[[Normal (CDF)][[role green 1.16[br](135ns)]][[role green 1.00[br](116ns)]]]
[[Normal (PDF)][[role green 1.00[br](48ns)]][[role blue 1.23[br](59ns)]]]
[[Normal (quantile)][[role blue 1.45[br](80ns)]][[role green 1.00[br](55ns)]]]
[[Pareto (CDF)][[role green 1.13[br](59ns)]][[role green 1.00[br](52ns)]]]
[[Pareto (PDF)][[role green 1.04[br](96ns)]][[role green 1.00[br](92ns)]]]
[[Pareto (quantile)][[role green 1.00[br](82ns)]][[role green 1.02[br](84ns)]]]
[[Poisson (CDF)][[role blue 1.90[br](254ns)]][[role green 1.00[br](134ns)]]]
[[Poisson (PDF)][[role blue 1.53[br](171ns)]][[role green 1.00[br](112ns)]]]
[[Poisson (quantile)][[role blue 1.76[br](1128ns)]][[role green 1.00[br](641ns)]]]
[[Rayleigh (CDF)][[role blue 1.24[br](47ns)]][[role green 1.00[br](38ns)]]]
[[Rayleigh (PDF)][[role green 1.00[br](64ns)]][[role green 1.09[br](70ns)]]]
[[Rayleigh (quantile)][[role blue 2.00[br](48ns)]][[role green 1.00[br](24ns)]]]
[[SkewNormal (CDF)][[role green 1.06[br](669ns)]][[role green 1.00[br](632ns)]]]
[[SkewNormal (PDF)][[role green 1.09[br](173ns)]][[role green 1.00[br](159ns)]]]
[[SkewNormal (quantile)][[role green 1.18[br](6968ns)]][[role green 1.00[br](5903ns)]]]
[[StudentsT (CDF)][[role blue 1.60[br](1151ns)]][[role green 1.00[br](721ns)]]]
[[StudentsT (PDF)][[role blue 1.94[br](360ns)]][[role green 1.00[br](186ns)]]]
[[StudentsT (quantile)][[role blue 1.26[br](1461ns)]][[role green 1.00[br](1161ns)]]]
[[Weibull (CDF)][[role blue 1.25[br](96ns)]][[role green 1.00[br](77ns)]]]
[[Weibull (PDF)][[role green 1.18[br](164ns)]][[role green 1.00[br](139ns)]]]
[[Weibull (quantile)][[role green 1.00[br](133ns)]][[role green 1.01[br](134ns)]]]
]
]

[template table_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[table:table_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64 gcd method comparison with GNU C++ version 9.2.0 on Windows x64
[[Function][gcd
boost 1.73][Euclid_gcd
boost 1.73][Stein_gcd
boost 1.73][mixed_binary_gcd
boost 1.73][Stein_gcd_textbook
boost 1.73][gcd_euclid_textbook
boost 1.73]]
[[gcd<boost::multiprecision::uint1024_t> (Trivial cases)][[role green 1.00[br](585ns)]][[role blue 1.30[br](761ns)]][[role red 3.82[br](2237ns)]][[role red 3.97[br](2321ns)]][[role blue 1.43[br](836ns)]][[role green 1.10[br](645ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (adjacent Fibonacci numbers)][[role green 1.00[br](9970176ns)]][[role red 7.06[br](70352275ns)]][[role red 3.96[br](39452018ns)]][[role red 3.33[br](33171075ns)]][[role red 2.04[br](20368737ns)]][[role red 7.38[br](73577712ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (permutations of Fibonacci numbers)][[role red 3.58[br](5700044700ns)]][[role green 1.02[br](1619575299ns)]][[role red 15.19[br](24170880700ns)]][[role red 3.10[br](4926301699ns)]][[role red 7.61[br](12103557199ns)]][[role green 1.00[br](1591386600ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (random prime number products)][[role green 1.00[br](776840ns)]][[role blue 1.83[br](1420825ns)]][[role red 7.78[br](6040362ns)]][[role red 2.39[br](1853658ns)]][[role red 4.19[br](3251426ns)]][[role blue 1.96[br](1522179ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (uniform random numbers)][[role green 1.00[br](55462256ns)]][[role red 2.02[br](112246250ns)]][[role red 2.55[br](141227725ns)]][[role red 2.21[br](122643774ns)]][[role blue 1.49[br](82400762ns)]][[role blue 1.99[br](110242300ns)]]]
[[gcd<boost::multiprecision::uint256_t> (Trivial cases)][[role green 1.00[br](442ns)]][[role green 1.08[br](475ns)]][[role red 4.12[br](1815ns)]][[role red 4.11[br](1813ns)]][[role green 1.17[br](515ns)]][[role green 1.00[br](441ns)]]]
[[gcd<boost::multiprecision::uint256_t> (adjacent Fibonacci numbers)][[role green 1.00[br](4055238ns)]][[role red 3.74[br](15153867ns)]][[role red 3.14[br](12714485ns)]][[role red 2.78[br](11263817ns)]][[role blue 1.83[br](7405233ns)]][[role red 3.79[br](15349360ns)]]]
[[gcd<boost::multiprecision::uint256_t> (permutations of Fibonacci numbers)][[role green 1.00[br](2188053200ns)]][[role red 2.24[br](4905530400ns)]][[role red 3.53[br](7720779699ns)]][[role red 2.26[br](4951713400ns)]][[role red 2.06[br](4508168099ns)]][[role red 2.60[br](5692910900ns)]]]
[[gcd<boost::multiprecision::uint256_t> (random prime number products)][[role green 1.00[br](788189ns)]][[role blue 1.65[br](1298322ns)]][[role red 4.56[br](3592013ns)]][[role blue 1.51[br](1186279ns)]][[role red 2.77[br](2184586ns)]][[role blue 1.70[br](1337848ns)]]]
[[gcd<boost::multiprecision::uint256_t> (uniform random numbers)][[role green 1.00[br](5971862ns)]][[role red 2.75[br](16440456ns)]][[role red 3.13[br](18696806ns)]][[role red 2.48[br](14818301ns)]][[role blue 1.65[br](9829225ns)]][[role red 3.16[br](18848609ns)]]]
[[gcd<boost::multiprecision::uint512_t> (Trivial cases)][[role green 1.00[br](473ns)]][[role green 1.10[br](522ns)]][[role red 2.35[br](1113ns)]][[role red 2.54[br](1201ns)]][[role blue 1.30[br](617ns)]][[role green 1.05[br](497ns)]]]
[[gcd<boost::multiprecision::uint512_t> (adjacent Fibonacci numbers)][[role green 1.00[br](8919442ns)]][[role red 4.88[br](43541675ns)]][[role red 4.74[br](42250737ns)]][[role red 3.64[br](32424337ns)]][[role blue 1.68[br](14998360ns)]][[role red 4.90[br](43720662ns)]]]
[[gcd<boost::multiprecision::uint512_t> (permutations of Fibonacci numbers)][[role green 1.00[br](4874074099ns)]][[role blue 1.22[br](5941210899ns)]][[role red 3.28[br](15985377299ns)]][[role blue 1.50[br](7304170300ns)]][[role blue 1.76[br](8559919799ns)]][[role blue 1.23[br](6002105200ns)]]]
[[gcd<boost::multiprecision::uint512_t> (random prime number products)][[role green 1.00[br](829159ns)]][[role blue 1.59[br](1318798ns)]][[role red 8.12[br](6731670ns)]][[role blue 1.91[br](1581731ns)]][[role red 3.08[br](2551970ns)]][[role blue 1.58[br](1308443ns)]]]
[[gcd<boost::multiprecision::uint512_t> (uniform random numbers)][[role green 1.00[br](18120096ns)]][[role red 2.35[br](42631487ns)]][[role red 3.97[br](71846612ns)]][[role red 3.10[br](56237574ns)]][[role blue 1.49[br](27081093ns)]][[role red 2.66[br](48247731ns)]]]
[[gcd<unsigned long long> (Trivial cases)][[role blue 1.85[br](109ns)]][[role red 2.44[br](144ns)]][[role green 1.00[br](59ns)]][[role blue 1.88[br](111ns)]][[role blue 1.68[br](99ns)]][[role red 2.08[br](123ns)]]]
[[gcd<unsigned long long> (adjacent Fibonacci numbers)][[role red 2.98[br](17394ns)]][[role red 14.61[br](85221ns)]][[role green 1.00[br](5832ns)]][[role red 2.98[br](17351ns)]][[role red 2.20[br](12805ns)]][[role red 14.60[br](85125ns)]]]
[[gcd<unsigned long long> (permutations of Fibonacci numbers)][[role green 1.04[br](1203049ns)]][[role blue 1.30[br](1508607ns)]][[role green 1.13[br](1307113ns)]][[role green 1.00[br](1159442ns)]][[role red 2.23[br](2585039ns)]][[role blue 1.26[br](1455556ns)]]]
[[gcd<unsigned long long> (random prime number products)][[role green 1.14[br](267158ns)]][[role blue 1.88[br](441001ns)]][[role green 1.00[br](234725ns)]][[role green 1.07[br](249997ns)]][[role red 2.02[br](473466ns)]][[role blue 1.78[br](418669ns)]]]
[[gcd<unsigned long long> (uniform random numbers)][[role blue 1.39[br](507147ns)]][[role red 2.14[br](784228ns)]][[role green 1.00[br](365889ns)]][[role blue 1.33[br](488432ns)]][[role blue 1.75[br](641184ns)]][[role red 2.08[br](760185ns)]]]
[[gcd<unsigned long> (Trivial cases)][[role blue 1.23[br](70ns)]][[role green 1.16[br](66ns)]][[role green 1.00[br](57ns)]][[role green 1.19[br](68ns)]][[role blue 1.63[br](93ns)]][[role green 1.12[br](64ns)]]]
[[gcd<unsigned long> (adjacent Fibonacci numbers)][[role blue 1.79[br](2678ns)]][[role red 10.20[br](15231ns)]][[role green 1.00[br](1493ns)]][[role blue 1.85[br](2765ns)]][[role red 2.07[br](3093ns)]][[role red 9.50[br](14177ns)]]]
[[gcd<unsigned long> (permutations of Fibonacci numbers)][[role green 1.00[br](130874ns)]][[role blue 1.43[br](187180ns)]][[role blue 1.31[br](171288ns)]][[role green 1.01[br](132289ns)]][[role red 2.45[br](321281ns)]][[role blue 1.30[br](169852ns)]]]
[[gcd<unsigned long> (random prime number products)][[role green 1.02[br](132073ns)]][[role blue 1.56[br](202025ns)]][[role green 1.11[br](143913ns)]][[role green 1.00[br](129448ns)]][[role red 2.03[br](263053ns)]][[role blue 1.40[br](181659ns)]]]
[[gcd<unsigned long> (uniform random numbers)][[role green 1.14[br](209599ns)]][[role blue 1.61[br](296090ns)]][[role green 1.00[br](183672ns)]][[role green 1.17[br](214530ns)]][[role blue 1.76[br](322600ns)]][[role blue 1.55[br](284838ns)]]]
[[gcd<unsigned short> (Trivial cases)][[role green 1.19[br](74ns)]][[role green 1.05[br](65ns)]][[role green 1.00[br](62ns)]][[role blue 1.29[br](80ns)]][[role blue 1.53[br](95ns)]][[role green 1.08[br](67ns)]]]
[[gcd<unsigned short> (adjacent Fibonacci numbers)][[role blue 1.55[br](694ns)]][[role red 6.51[br](2915ns)]][[role green 1.00[br](448ns)]][[role blue 1.65[br](737ns)]][[role blue 1.42[br](634ns)]][[role red 6.06[br](2716ns)]]]
[[gcd<unsigned short> (permutations of Fibonacci numbers)][[role blue 1.31[br](10776ns)]][[role red 2.35[br](19287ns)]][[role green 1.00[br](8206ns)]][[role blue 1.41[br](11598ns)]][[role blue 1.63[br](13337ns)]][[role red 2.21[br](18163ns)]]]
[[gcd<unsigned short> (random prime number products)][[role green 1.04[br](48625ns)]][[role blue 1.82[br](84692ns)]][[role green 1.03[br](47933ns)]][[role green 1.00[br](46539ns)]][[role red 2.94[br](136663ns)]][[role blue 1.68[br](78386ns)]]]
[[gcd<unsigned short> (uniform random numbers)][[role green 1.05[br](73231ns)]][[role blue 1.72[br](120140ns)]][[role green 1.00[br](69680ns)]][[role green 1.04[br](72636ns)]][[role red 2.51[br](175204ns)]][[role blue 1.70[br](118679ns)]]]
[[gcd<unsigned> (Trivial cases)][[role blue 1.30[br](73ns)]][[role green 1.14[br](64ns)]][[role green 1.00[br](56ns)]][[role blue 1.23[br](69ns)]][[role blue 1.62[br](91ns)]][[role green 1.14[br](64ns)]]]
[[gcd<unsigned> (adjacent Fibonacci numbers)][[role blue 1.81[br](2689ns)]][[role red 10.14[br](15051ns)]][[role green 1.00[br](1485ns)]][[role blue 1.92[br](2845ns)]][[role red 2.10[br](3117ns)]][[role red 9.74[br](14464ns)]]]
[[gcd<unsigned> (permutations of Fibonacci numbers)][[role green 1.00[br](125228ns)]][[role blue 1.45[br](182101ns)]][[role blue 1.36[br](169753ns)]][[role green 1.04[br](130303ns)]][[role red 2.50[br](312889ns)]][[role blue 1.41[br](176940ns)]]]
[[gcd<unsigned> (random prime number products)][[role green 1.04[br](133297ns)]][[role blue 1.55[br](199022ns)]][[role green 1.05[br](134178ns)]][[role green 1.00[br](128319ns)]][[role red 2.03[br](260550ns)]][[role blue 1.53[br](196665ns)]]]
[[gcd<unsigned> (uniform random numbers)][[role green 1.15[br](212670ns)]][[role blue 1.61[br](298254ns)]][[role green 1.00[br](184955ns)]][[role green 1.17[br](216091ns)]][[role blue 1.80[br](332689ns)]][[role blue 1.62[br](299958ns)]]]
]
]

[template table_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[table:table_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64 Library Comparison with GNU C++ version 9.2.0 on Windows x64
[[Function][boost 1.73][boost 1.73[br]promote_double<false>][tr1/cmath][math.h]]
[[assoc_laguerre[br](2240/2240 tests selected)][[role green 1.08[br](137ns)]][[role green 1.00[br](127ns)]][[role green 1.08[br](137ns)]][[role grey -]]]
[[assoc_legendre[br](110/400 tests selected)][[role grey -]][[role grey -]][[role green 1.00[br](40ns)]][[role grey -]]]
[[beta[br](2204/2204 tests selected)][[role blue 1.60[br](322ns)]][[role green 1.18[br](237ns)]][[role green 1.00[br](201ns)]][[role grey -]]]
[[cbrt[br](85/85 tests selected)][[role red 4.67[br](70ns)]][[role green 1.00[br](15ns)]][[role blue 2.00[br](30ns)]][[role red 2.13[br](32ns)]]]
[[cyl_bessel_i (integer order)[br](515/526 tests selected)][[role red 3.06[br](597ns)]][[role green 1.00[br](195ns)]][[role green 1.01[br](196ns)]][[role grey -]]]
[[cyl_bessel_i[br](215/240 tests selected)][[role red 5.68[br](949ns)]][[role red 2.32[br](387ns)]][[role green 1.00[br](167ns)]][[role grey -]]]
[[cyl_bessel_j (integer order)[br](253/268 tests selected)][[role blue 1.92[br](184ns)]][[role green 1.00[br](96ns)]][[role red 3.12[br](300ns)]][[role blue 1.94[br](186ns)]]]
[[cyl_bessel_j[br](442/451 tests selected)][[role red 2.15[br](886ns)]][[role blue 1.21[br](499ns)]][[role green 1.00[br](412ns)]][[role grey -]]]
[[cyl_bessel_k (integer order)[br](505/508 tests selected)][[role red 18.17[br](3724ns)]][[role green 1.00[br](205ns)]][[role red 8.40[br](1722ns)]][[role grey -]]]
[[cyl_bessel_k[br](187/279 tests selected)][[role red 19.68[br](6847ns)]][[role green 1.00[br](348ns)]][[role red 6.31[br](2196ns)]][[role grey -]]]
[[cyl_neumann (integer order)[br](424/428 tests selected)][[role red 2.13[br](348ns)]][[role blue 1.55[br](252ns)]][[role red 3.83[br](624ns)]][[role green 1.00[br](163ns)]]]
[[cyl_neumann[br](428/450 tests selected)][[role red 12.46[br](10032ns)]][[role red 7.10[br](5715ns)]][[role green 1.00[br](805ns)]][[role grey -]]]
[[ellint_1 (complete)[br](109/109 tests selected)][[role blue 1.64[br](77ns)]][[role green 1.00[br](47ns)]][[role red 2.36[br](111ns)]][[role grey -]]]
[[ellint_1[br](627/629 tests selected)][[role blue 1.41[br](349ns)]][[role green 1.00[br](248ns)]][[role green 1.09[br](270ns)]][[role grey -]]]
[[ellint_2 (complete)[br](110/110 tests selected)][[role red 2.11[br](57ns)]][[role green 1.00[br](27ns)]][[role red 9.37[br](253ns)]][[role grey -]]]
[[ellint_2[br](527/530 tests selected)][[role blue 1.50[br](583ns)]][[role green 1.00[br](388ns)]][[role green 1.06[br](412ns)]][[role grey -]]]
[[ellint_3 (complete)[br](0/500 tests selected)][[role green nan[br](0ns)]][[role green nan[br](0ns)]][[role green nan[br](0ns)]][[role grey -]]]
[[ellint_3[br](22/845 tests selected)][[role red 2.58[br](670ns)]][[role blue 1.53[br](398ns)]][[role green 1.00[br](260ns)]][[role grey -]]]
[[erf[br](950/950 tests selected)][[role green 1.00[br](33ns)]][[role grey -]][[role green 1.15[br](38ns)]][[role blue 1.30[br](43ns)]]]
[[erfc[br](950/950 tests selected)][[role blue 1.76[br](90ns)]][[role green 1.00[br](51ns)]][[role green 1.08[br](55ns)]][[role blue 1.25[br](64ns)]]]
[[expint[br](436/436 tests selected)][[role blue 1.53[br](92ns)]][[role green 1.00[br](60ns)]][[role blue 1.83[br](110ns)]][[role grey -]]]
[[expm1[br](80/80 tests selected)][[role blue 1.38[br](33ns)]][[role green 1.08[br](26ns)]][[role green 1.00[br](24ns)]][[role green 1.00[br](24ns)]]]
[[laguerre[br](280/280 tests selected)][[role green 1.07[br](112ns)]][[role green 1.00[br](105ns)]][[role green 1.03[br](108ns)]][[role grey -]]]
[[legendre[br](300/300 tests selected)][[role blue 1.25[br](320ns)]][[role green 1.00[br](255ns)]][[role blue 1.27[br](323ns)]][[role grey -]]]
[[lgamma[br](400/400 tests selected)][[role red 3.40[br](214ns)]][[role red 2.54[br](160ns)]][[role green 1.00[br](63ns)]][[role green 1.02[br](64ns)]]]
[[log1p[br](80/80 tests selected)][[role blue 1.71[br](29ns)]][[role green 1.00[br](17ns)]][[role blue 1.53[br](26ns)]][[role blue 1.71[br](29ns)]]]
[[sph_bessel[br](483/483 tests selected)][[role blue 1.48[br](975ns)]][[role green 1.00[br](661ns)]][[role red 3.02[br](1999ns)]][[role grey -]]]
[[sph_neumann[br](284/284 tests selected)][[role red 2.96[br](3153ns)]][[role green 1.00[br](1064ns)]][[role red 2.73[br](2906ns)]][[role grey -]]]
[[tgamma[br](400/400 tests selected)][[role red 3.32[br](259ns)]][[role red 2.03[br](158ns)]][[role green 1.01[br](79ns)]][[role green 1.00[br](78ns)]]]
[[zeta[br](448/448 tests selected)][[role blue 1.40[br](310ns)]][[role green 1.00[br](221ns)]][[role red 918.24[br](202930ns)]][[role grey -]]]
]
]

[template table_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_[]
[table:table_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_ Polynomial Arithmetic (Microsoft Visual C++ version 14.2, Windows x64)
[[Function][std::uint64_t][double][cpp_int]]
[[operator *][[role blue 1.54[br](951ns)]][[role green 1.00[br](617ns)]][[role red 15.22[br](9391ns)]]]
[[operator * (int)][[role green 1.16[br](135ns)]][[role green 1.00[br](116ns)]][[role red 5.22[br](605ns)]]]
[[operator *=][[role blue 1.30[br](371957ns)]][[role green 1.00[br](286462ns)]][[role red 17.11[br](4901613ns)]]]
[[operator *= (int)][[role green 1.00[br](14157ns)]][[role green 1.04[br](14670ns)]][[role red 19.69[br](278738ns)]]]
[[operator +][[role blue 1.41[br](273ns)]][[role green 1.00[br](194ns)]][[role red 6.20[br](1203ns)]]]
[[operator + (int)][[role blue 1.25[br](126ns)]][[role green 1.00[br](101ns)]][[role red 3.47[br](350ns)]]]
[[operator +=][[role blue 1.35[br](42ns)]][[role green 1.00[br](31ns)]][[role red 11.16[br](346ns)]]]
[[operator += (int)][[role blue 1.25[br](5ns)]][[role green 1.00[br](4ns)]][[role red 25.50[br](102ns)]]]
[[operator -][[role blue 1.20[br](231ns)]][[role green 1.00[br](192ns)]][[role red 6.44[br](1236ns)]]]
[[operator - (int)][[role green 1.20[br](121ns)]][[role green 1.00[br](101ns)]][[role red 3.34[br](337ns)]]]
[[operator -=][[role blue 1.35[br](42ns)]][[role green 1.00[br](31ns)]][[role red 11.13[br](345ns)]]]
[[operator -= (int)][[role green 1.00[br](4ns)]][[role green 1.00[br](4ns)]][[role red 23.50[br](94ns)]]]
[[operator /][[role red 2.17[br](1164ns)]][[role green 1.00[br](537ns)]][[role red 51.34[br](27568ns)]]]
[[operator / (int)][[role green 1.17[br](138ns)]][[role green 1.00[br](118ns)]][[role red 9.73[br](1148ns)]]]
[[operator /=][[role green 1.08[br](13ns)]][[role green 1.00[br](12ns)]][[role red 192.42[br](2309ns)]]]
[[operator /= (int)][[role green 1.00[br](697ns)]][[role red 36.29[br](25293ns)]][[role red 2700.21[br](1882045ns)]]]
]
]

[template table_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[table:table_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Rational Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role blue 1.92[br](23ns)]][[role blue 1.92[br](23ns)]][[role green 1.00[br](12ns)]][[role green 1.17[br](14ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]]]
[[Order  3][[role blue 1.89[br](34ns)]][[role red 2.28[br](41ns)]][[role blue 1.67[br](30ns)]][[role blue 1.61[br](29ns)]][[role green 1.06[br](19ns)]][[role green 1.00[br](18ns)]][[role green 1.00[br](18ns)]][[role green 1.00[br](18ns)]]]
[[Order  4][[role blue 1.72[br](43ns)]][[role red 2.16[br](54ns)]][[role blue 1.64[br](41ns)]][[role blue 1.60[br](40ns)]][[role green 1.00[br](25ns)]][[role green 1.00[br](25ns)]][[role green 1.00[br](25ns)]][[role green 1.04[br](26ns)]]]
[[Order  5][[role green 1.08[br](53ns)]][[role blue 1.41[br](69ns)]][[role green 1.00[br](49ns)]][[role green 1.00[br](49ns)]][[role green 1.08[br](53ns)]][[role green 1.08[br](53ns)]][[role green 1.00[br](49ns)]][[role green 1.10[br](54ns)]]]
[[Order  6][[role green 1.08[br](65ns)]][[role blue 1.42[br](85ns)]][[role green 1.02[br](61ns)]][[role green 1.00[br](60ns)]][[role green 1.05[br](63ns)]][[role blue 1.23[br](74ns)]][[role blue 1.25[br](75ns)]][[role blue 1.40[br](84ns)]]]
[[Order  7][[role green 1.06[br](75ns)]][[role blue 1.37[br](97ns)]][[role green 1.01[br](72ns)]][[role green 1.00[br](71ns)]][[role green 1.14[br](81ns)]][[role green 1.01[br](72ns)]][[role green 1.20[br](85ns)]][[role blue 1.35[br](96ns)]]]
[[Order  8][[role green 1.07[br](87ns)]][[role blue 1.38[br](112ns)]][[role green 1.04[br](84ns)]][[role green 1.02[br](83ns)]][[role green 1.01[br](82ns)]][[role green 1.00[br](81ns)]][[role red 2.49[br](202ns)]][[role red 2.60[br](211ns)]]]
[[Order  9][[role green 1.16[br](103ns)]][[role blue 1.61[br](143ns)]][[role green 1.18[br](105ns)]][[role blue 1.27[br](113ns)]][[role green 1.01[br](90ns)]][[role green 1.02[br](91ns)]][[role green 1.02[br](91ns)]][[role green 1.00[br](89ns)]]]
[[Order 10][[role green 1.15[br](115ns)]][[role blue 1.46[br](146ns)]][[role green 1.14[br](114ns)]][[role green 1.12[br](112ns)]][[role green 1.01[br](101ns)]][[role green 1.02[br](102ns)]][[role green 1.01[br](101ns)]][[role green 1.00[br](100ns)]]]
[[Order 11][[role blue 1.21[br](131ns)]][[role blue 1.48[br](160ns)]][[role green 1.17[br](126ns)]][[role green 1.16[br](125ns)]][[role green 1.00[br](108ns)]][[role blue 1.27[br](137ns)]][[role green 1.00[br](108ns)]][[role green 1.01[br](109ns)]]]
[[Order 12][[role blue 1.26[br](148ns)]][[role blue 1.53[br](179ns)]][[role green 1.19[br](139ns)]][[role green 1.19[br](139ns)]][[role green 1.02[br](119ns)]][[role blue 1.24[br](145ns)]][[role green 1.00[br](117ns)]][[role green 1.00[br](117ns)]]]
[[Order 13][[role blue 1.31[br](163ns)]][[role blue 1.71[br](212ns)]][[role blue 1.23[br](153ns)]][[role blue 1.52[br](189ns)]][[role green 1.01[br](125ns)]][[role blue 1.29[br](160ns)]][[role green 1.01[br](125ns)]][[role green 1.00[br](124ns)]]]
[[Order 14][[role blue 1.42[br](190ns)]][[role blue 1.56[br](209ns)]][[role blue 1.32[br](177ns)]][[role blue 1.47[br](197ns)]][[role green 1.02[br](137ns)]][[role blue 1.31[br](175ns)]][[role green 1.00[br](134ns)]][[role green 1.01[br](136ns)]]]
[[Order 15][[role blue 1.34[br](194ns)]][[role blue 1.51[br](219ns)]][[role blue 1.36[br](197ns)]][[role blue 1.46[br](212ns)]][[role green 1.02[br](148ns)]][[role blue 1.30[br](188ns)]][[role green 1.00[br](145ns)]][[role red 2.23[br](323ns)]]]
[[Order 16][[role blue 1.38[br](216ns)]][[role blue 1.56[br](244ns)]][[role blue 1.36[br](212ns)]][[role blue 1.31[br](204ns)]][[role green 1.15[br](179ns)]][[role blue 1.34[br](209ns)]][[role green 1.00[br](156ns)]][[role red 2.10[br](328ns)]]]
[[Order 17][[role blue 1.39[br](227ns)]][[role blue 1.67[br](273ns)]][[role blue 1.34[br](218ns)]][[role blue 1.69[br](275ns)]][[role green 1.00[br](163ns)]][[role blue 1.32[br](215ns)]][[role green 1.02[br](167ns)]][[role red 2.53[br](412ns)]]]
[[Order 18][[role blue 1.37[br](242ns)]][[role blue 1.73[br](306ns)]][[role blue 1.40[br](248ns)]][[role blue 1.56[br](276ns)]][[role green 1.06[br](187ns)]][[role blue 1.32[br](233ns)]][[role green 1.00[br](177ns)]][[role red 2.15[br](380ns)]]]
[[Order 19][[role blue 1.28[br](254ns)]][[role blue 1.60[br](319ns)]][[role blue 1.27[br](253ns)]][[role blue 1.51[br](300ns)]][[role green 1.00[br](199ns)]][[role blue 1.22[br](243ns)]][[role blue 1.80[br](359ns)]][[role blue 1.92[br](382ns)]]]
[[Order 20][[role blue 1.28[br](268ns)]][[role blue 1.62[br](338ns)]][[role blue 1.27[br](265ns)]][[role blue 1.56[br](325ns)]][[role green 1.00[br](209ns)]][[role blue 1.24[br](259ns)]][[role blue 1.87[br](391ns)]][[role red 2.04[br](427ns)]]]
]
]

[template table_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[table:table_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Polynomial Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64
[[Function][Method 0[br](Double Coefficients)][Method 0[br](Integer Coefficients)][Method 1[br](Double Coefficients)][Method 1[br](Integer Coefficients)][Method 2[br](Double Coefficients)][Method 2[br](Integer Coefficients)][Method 3[br](Double Coefficients)][Method 3[br](Integer Coefficients)]]
[[Order  2][[role grey -]][[role grey -]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]][[role green 1.00[br](6ns)]]]
[[Order  3][[role red 2.33[br](21ns)]][[role red 3.33[br](30ns)]][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]][[role green 1.00[br](9ns)]]]
[[Order  4][[role blue 2.00[br](24ns)]][[role red 3.00[br](36ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]][[role green 1.00[br](12ns)]][[role green 1.08[br](13ns)]][[role green 1.08[br](13ns)]]]
[[Order  5][[role blue 1.56[br](25ns)]][[role red 2.31[br](37ns)]][[role green 1.00[br](16ns)]][[role green 1.00[br](16ns)]][[role green 1.13[br](18ns)]][[role green 1.13[br](18ns)]][[role blue 1.56[br](25ns)]][[role blue 1.56[br](25ns)]]]
[[Order  6][[role blue 1.48[br](31ns)]][[role red 2.19[br](46ns)]][[role green 1.05[br](22ns)]][[role green 1.00[br](21ns)]][[role green 1.00[br](21ns)]][[role green 1.00[br](21ns)]][[role blue 1.29[br](27ns)]][[role blue 1.29[br](27ns)]]]
[[Order  7][[role blue 1.54[br](37ns)]][[role red 2.33[br](56ns)]][[role green 1.08[br](26ns)]][[role green 1.08[br](26ns)]][[role green 1.04[br](25ns)]][[role green 1.00[br](24ns)]][[role green 1.13[br](27ns)]][[role green 1.17[br](28ns)]]]
[[Order  8][[role blue 1.53[br](46ns)]][[role red 2.23[br](67ns)]][[role green 1.07[br](32ns)]][[role green 1.07[br](32ns)]][[role green 1.00[br](30ns)]][[role green 1.00[br](30ns)]][[role green 1.03[br](31ns)]][[role green 1.03[br](31ns)]]]
[[Order  9][[role blue 1.35[br](46ns)]][[role red 2.06[br](70ns)]][[role green 1.18[br](40ns)]][[role blue 1.32[br](45ns)]][[role green 1.00[br](34ns)]][[role green 1.00[br](34ns)]][[role green 1.09[br](37ns)]][[role green 1.06[br](36ns)]]]
[[Order 10][[role blue 1.38[br](54ns)]][[role red 2.13[br](83ns)]][[role blue 1.21[br](47ns)]][[role green 1.15[br](45ns)]][[role green 1.00[br](39ns)]][[role green 1.00[br](39ns)]][[role green 1.03[br](40ns)]][[role green 1.03[br](40ns)]]]
[[Order 11][[role blue 1.48[br](62ns)]][[role red 2.24[br](94ns)]][[role blue 1.24[br](52ns)]][[role blue 1.26[br](53ns)]][[role green 1.07[br](45ns)]][[role green 1.00[br](42ns)]][[role green 1.10[br](46ns)]][[role green 1.02[br](43ns)]]]
[[Order 12][[role blue 1.48[br](71ns)]][[role red 2.27[br](109ns)]][[role blue 1.25[br](60ns)]][[role blue 1.27[br](61ns)]][[role green 1.04[br](50ns)]][[role green 1.00[br](48ns)]][[role green 1.00[br](48ns)]][[role green 1.00[br](48ns)]]]
[[Order 13][[role blue 1.55[br](76ns)]][[role red 2.33[br](114ns)]][[role blue 1.31[br](64ns)]][[role blue 1.31[br](64ns)]][[role green 1.04[br](51ns)]][[role green 1.04[br](51ns)]][[role green 1.02[br](50ns)]][[role green 1.00[br](49ns)]]]
[[Order 14][[role blue 1.53[br](84ns)]][[role red 2.40[br](132ns)]][[role blue 1.44[br](79ns)]][[role blue 1.40[br](77ns)]][[role green 1.04[br](57ns)]][[role green 1.02[br](56ns)]][[role green 1.00[br](55ns)]][[role green 1.00[br](55ns)]]]
[[Order 15][[role blue 1.51[br](95ns)]][[role red 2.33[br](147ns)]][[role blue 1.37[br](86ns)]][[role blue 1.38[br](87ns)]][[role green 1.05[br](66ns)]][[role green 1.06[br](67ns)]][[role green 1.00[br](63ns)]][[role green 1.00[br](63ns)]]]
[[Order 16][[role blue 1.47[br](106ns)]][[role red 2.18[br](157ns)]][[role blue 1.40[br](101ns)]][[role blue 1.33[br](96ns)]][[role green 1.01[br](73ns)]][[role green 1.03[br](74ns)]][[role green 1.00[br](72ns)]][[role green 1.04[br](75ns)]]]
[[Order 17][[role blue 1.46[br](114ns)]][[role red 2.08[br](162ns)]][[role blue 1.44[br](112ns)]][[role blue 1.44[br](112ns)]][[role green 1.00[br](78ns)]][[role green 1.01[br](79ns)]][[role green 1.05[br](82ns)]][[role green 1.03[br](80ns)]]]
[[Order 18][[role blue 1.48[br](126ns)]][[role red 2.08[br](177ns)]][[role blue 1.44[br](122ns)]][[role blue 1.46[br](124ns)]][[role green 1.02[br](87ns)]][[role green 1.04[br](88ns)]][[role green 1.01[br](86ns)]][[role green 1.00[br](85ns)]]]
[[Order 19][[role blue 1.49[br](136ns)]][[role red 2.07[br](188ns)]][[role blue 1.47[br](134ns)]][[role blue 1.47[br](134ns)]][[role green 1.00[br](91ns)]][[role green 1.01[br](92ns)]][[role green 1.05[br](96ns)]][[role green 1.03[br](94ns)]]]
[[Order 20][[role blue 1.52[br](150ns)]][[role red 2.05[br](203ns)]][[role blue 1.45[br](144ns)]][[role blue 1.46[br](145ns)]][[role green 1.00[br](99ns)]][[role green 1.02[br](101ns)]][[role green 1.02[br](101ns)]][[role green 1.02[br](101ns)]]]
]
]

[template table_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[table:table_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 gcd method comparison with Microsoft Visual C++ version 14.2 on Windows x64
[[Function][gcd
boost 1.73][Euclid_gcd
boost 1.73][Stein_gcd
boost 1.73][mixed_binary_gcd
boost 1.73][Stein_gcd_textbook
boost 1.73][gcd_euclid_textbook
boost 1.73]]
[[gcd<boost::multiprecision::uint1024_t> (Trivial cases)][[role green 1.01[br](811ns)]][[role green 1.00[br](806ns)]][[role red 4.49[br](3619ns)]][[role red 4.37[br](3524ns)]][[role blue 1.54[br](1240ns)]][[role green 1.17[br](947ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (adjacent Fibonacci numbers)][[role green 1.00[br](17221009ns)]][[role red 3.10[br](53378856ns)]][[role red 3.49[br](60085356ns)]][[role red 2.71[br](46662362ns)]][[role blue 1.43[br](24687809ns)]][[role red 3.60[br](62017387ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (permutations of Fibonacci numbers)][[role red 4.79[br](8947276300ns)]][[role green 1.00[br](1869827499ns)]][[role red 16.49[br](30836050300ns)]][[role red 2.95[br](5512590399ns)]][[role red 9.35[br](17476759399ns)]][[role blue 1.59[br](2969003299ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (random prime number products)][[role green 1.15[br](1366950ns)]][[role green 1.00[br](1184715ns)]][[role red 6.07[br](7192390ns)]][[role blue 1.69[br](2004764ns)]][[role red 2.88[br](3414226ns)]][[role green 1.03[br](1223450ns)]]]
[[gcd<boost::multiprecision::uint1024_t> (uniform random numbers)][[role green 1.13[br](94422587ns)]][[role green 1.10[br](91927462ns)]][[role red 2.46[br](205656225ns)]][[role blue 1.79[br](150321950ns)]][[role blue 1.26[br](105849675ns)]][[role green 1.00[br](83747287ns)]]]
[[gcd<boost::multiprecision::uint256_t> (Trivial cases)][[role green 1.12[br](529ns)]][[role blue 1.22[br](578ns)]][[role red 5.71[br](2706ns)]][[role red 5.01[br](2376ns)]][[role blue 1.62[br](768ns)]][[role green 1.00[br](474ns)]]]
[[gcd<boost::multiprecision::uint256_t> (adjacent Fibonacci numbers)][[role green 1.00[br](6910946ns)]][[role red 2.03[br](14038607ns)]][[role red 4.15[br](28656946ns)]][[role red 2.36[br](16280003ns)]][[role blue 1.83[br](12632765ns)]][[role blue 1.79[br](12358175ns)]]]
[[gcd<boost::multiprecision::uint256_t> (permutations of Fibonacci numbers)][[role green 1.00[br](3546690299ns)]][[role blue 1.24[br](4410071600ns)]][[role red 4.54[br](16088449000ns)]][[role red 2.08[br](7376147399ns)]][[role blue 1.87[br](6630678299ns)]][[role green 1.11[br](3921678899ns)]]]
[[gcd<boost::multiprecision::uint256_t> (random prime number products)][[role blue 1.24[br](1402017ns)]][[role green 1.19[br](1342771ns)]][[role red 10.57[br](11937009ns)]][[role red 2.30[br](2592407ns)]][[role red 3.17[br](3578886ns)]][[role green 1.00[br](1129228ns)]]]
[[gcd<boost::multiprecision::uint256_t> (uniform random numbers)][[role green 1.00[br](9555357ns)]][[role blue 1.38[br](13230160ns)]][[role red 3.58[br](34160918ns)]][[role red 2.17[br](20739521ns)]][[role blue 1.66[br](15830168ns)]][[role blue 1.25[br](11919907ns)]]]
[[gcd<boost::multiprecision::uint512_t> (Trivial cases)][[role green 1.09[br](610ns)]][[role green 1.05[br](586ns)]][[role red 4.52[br](2524ns)]][[role red 5.42[br](3032ns)]][[role blue 1.53[br](858ns)]][[role green 1.00[br](559ns)]]]
[[gcd<boost::multiprecision::uint512_t> (adjacent Fibonacci numbers)][[role green 1.00[br](15008157ns)]][[role red 2.19[br](32823187ns)]][[role red 3.54[br](53103662ns)]][[role red 2.51[br](37681662ns)]][[role blue 1.67[br](25128434ns)]][[role red 2.06[br](30897006ns)]]]
[[gcd<boost::multiprecision::uint512_t> (permutations of Fibonacci numbers)][[role blue 1.70[br](7824618799ns)]][[role green 1.06[br](4905917200ns)]][[role red 6.42[br](29578499900ns)]][[role blue 1.96[br](9014054500ns)]][[role red 2.82[br](12972133700ns)]][[role green 1.00[br](4607798200ns)]]]
[[gcd<boost::multiprecision::uint512_t> (random prime number products)][[role green 1.20[br](1429033ns)]][[role green 1.00[br](1192363ns)]][[role red 6.71[br](8006331ns)]][[role blue 1.66[br](1983967ns)]][[role red 3.05[br](3641579ns)]][[role green 1.00[br](1193514ns)]]]
[[gcd<boost::multiprecision::uint512_t> (uniform random numbers)][[role green 1.00[br](28993946ns)]][[role green 1.13[br](32874618ns)]][[role red 3.71[br](107613600ns)]][[role red 2.24[br](64869562ns)]][[role blue 1.39[br](40246987ns)]][[role blue 1.26[br](36427993ns)]]]
[[gcd<unsigned long long> (Trivial cases)][[role blue 1.61[br](143ns)]][[role blue 1.88[br](167ns)]][[role green 1.09[br](97ns)]][[role blue 1.66[br](148ns)]][[role green 1.00[br](89ns)]][[role blue 1.25[br](111ns)]]]
[[gcd<unsigned long long> (adjacent Fibonacci numbers)][[role blue 1.65[br](18657ns)]][[role red 9.12[br](102852ns)]][[role green 1.00[br](11278ns)]][[role blue 1.65[br](18642ns)]][[role blue 1.36[br](15386ns)]][[role red 7.61[br](85867ns)]]]
[[gcd<unsigned long long> (permutations of Fibonacci numbers)][[role green 1.18[br](1759315ns)]][[role blue 1.23[br](1829739ns)]][[role red 2.48[br](3696867ns)]][[role green 1.20[br](1792095ns)]][[role blue 1.92[br](2869829ns)]][[role green 1.00[br](1493466ns)]]]
[[gcd<unsigned long long> (random prime number products)][[role green 1.03[br](419624ns)]][[role blue 1.26[br](513559ns)]][[role blue 1.66[br](677592ns)]][[role green 1.00[br](407357ns)]][[role blue 1.24[br](505557ns)]][[role green 1.05[br](426446ns)]]]
[[gcd<unsigned long long> (uniform random numbers)][[role green 1.15[br](802062ns)]][[role blue 1.29[br](895731ns)]][[role blue 1.38[br](959675ns)]][[role green 1.16[br](810488ns)]][[role green 1.00[br](696259ns)]][[role green 1.10[br](768043ns)]]]
[[gcd<unsigned long> (Trivial cases)][[role red 2.05[br](115ns)]][[role blue 1.61[br](90ns)]][[role blue 1.80[br](101ns)]][[role blue 1.98[br](111ns)]][[role blue 1.55[br](87ns)]][[role green 1.00[br](56ns)]]]
[[gcd<unsigned long> (adjacent Fibonacci numbers)][[role blue 1.26[br](3438ns)]][[role red 8.19[br](22429ns)]][[role green 1.00[br](2739ns)]][[role blue 1.30[br](3567ns)]][[role green 1.15[br](3146ns)]][[role red 5.44[br](14903ns)]]]
[[gcd<unsigned long> (permutations of Fibonacci numbers)][[role green 1.17[br](205858ns)]][[role blue 1.52[br](268100ns)]][[role red 2.43[br](427978ns)]][[role green 1.13[br](198590ns)]][[role red 2.02[br](356193ns)]][[role green 1.00[br](175939ns)]]]
[[gcd<unsigned long> (random prime number products)][[role green 1.01[br](214230ns)]][[role blue 1.32[br](278903ns)]][[role blue 1.93[br](406951ns)]][[role green 1.12[br](237142ns)]][[role blue 1.70[br](358996ns)]][[role green 1.00[br](211247ns)]]]
[[gcd<unsigned long> (uniform random numbers)][[role blue 1.29[br](382560ns)]][[role blue 1.46[br](431960ns)]][[role blue 1.77[br](524430ns)]][[role blue 1.26[br](373023ns)]][[role blue 1.27[br](377903ns)]][[role green 1.00[br](296476ns)]]]
[[gcd<unsigned short> (Trivial cases)][[role blue 1.79[br](118ns)]][[role blue 1.41[br](93ns)]][[role blue 1.47[br](97ns)]][[role blue 1.73[br](114ns)]][[role blue 1.42[br](94ns)]][[role green 1.00[br](66ns)]]]
[[gcd<unsigned short> (adjacent Fibonacci numbers)][[role green 1.16[br](821ns)]][[role red 7.62[br](5377ns)]][[role green 1.00[br](706ns)]][[role green 1.17[br](823ns)]][[role green 1.15[br](810ns)]][[role red 5.04[br](3557ns)]]]
[[gcd<unsigned short> (permutations of Fibonacci numbers)][[role green 1.00[br](11485ns)]][[role red 3.82[br](43640ns)]][[role green 1.16[br](13294ns)]][[role green 1.00[br](11428ns)]][[role red 2.19[br](25029ns)]][[role red 2.11[br](24145ns)]]]
[[gcd<unsigned short> (random prime number products)][[role blue 1.26[br](123821ns)]][[role blue 1.92[br](188438ns)]][[role red 2.21[br](216289ns)]][[role green 1.12[br](109274ns)]][[role blue 1.67[br](163434ns)]][[role green 1.00[br](97914ns)]]]
[[gcd<unsigned short> (uniform random numbers)][[role green 1.16[br](169639ns)]][[role blue 1.44[br](212132ns)]][[role blue 1.62[br](237308ns)]][[role green 1.16[br](170196ns)]][[role blue 1.30[br](191524ns)]][[role green 1.00[br](146827ns)]]]
[[gcd<unsigned> (Trivial cases)][[role blue 1.98[br](117ns)]][[role blue 1.61[br](95ns)]][[role blue 1.90[br](112ns)]][[role blue 2.00[br](118ns)]][[role blue 1.61[br](95ns)]][[role green 1.00[br](59ns)]]]
[[gcd<unsigned> (adjacent Fibonacci numbers)][[role blue 1.28[br](3381ns)]][[role red 8.39[br](22209ns)]][[role green 1.00[br](2648ns)]][[role blue 1.30[br](3436ns)]][[role blue 1.34[br](3540ns)]][[role red 5.64[br](14937ns)]]]
[[gcd<unsigned> (permutations of Fibonacci numbers)][[role green 1.08[br](197785ns)]][[role blue 1.47[br](269176ns)]][[role red 2.37[br](435412ns)]][[role green 1.12[br](205095ns)]][[role red 2.08[br](382592ns)]][[role green 1.00[br](183636ns)]]]
[[gcd<unsigned> (random prime number products)][[role green 1.09[br](214890ns)]][[role blue 1.42[br](279881ns)]][[role blue 1.99[br](392760ns)]][[role green 1.05[br](206420ns)]][[role blue 1.61[br](317337ns)]][[role green 1.00[br](197431ns)]]]
[[gcd<unsigned> (uniform random numbers)][[role blue 1.26[br](385229ns)]][[role blue 1.35[br](411167ns)]][[role blue 1.68[br](512335ns)]][[role blue 1.23[br](375323ns)]][[role blue 1.32[br](402786ns)]][[role green 1.00[br](305574ns)]]]
]
]

[template table_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[table:table_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Distribution performance comparison for different performance options with Microsoft Visual C++ version 14.2 on Windows x64
[[Function][boost 1.73][Boost[br]promote_double<false>[br]digits10<10>][Boost[br]float[br]promote_float<false>]]
[[ArcSine (CDF)][[role blue 1.30[br](43ns)]][[role green 1.00[br](33ns)]][[role green 1.00[br](33ns)]]]
[[ArcSine (PDF)][[role green 1.00[br](17ns)]][[role green 1.06[br](18ns)]][[role green 1.00[br](17ns)]]]
[[ArcSine (quantile)][[role green 1.20[br](30ns)]][[role green 1.00[br](25ns)]][[role green 1.20[br](30ns)]]]
[[Beta (CDF)][[role blue 1.37[br](158ns)]][[role blue 1.22[br](140ns)]][[role green 1.00[br](115ns)]]]
[[Beta (PDF)][[role green 1.08[br](104ns)]][[role green 1.14[br](109ns)]][[role green 1.00[br](96ns)]]]
[[Beta (quantile)][[role green 1.07[br](806ns)]][[role green 1.11[br](833ns)]][[role green 1.00[br](753ns)]]]
[[Binomial (CDF)][[role blue 1.80[br](413ns)]][[role blue 1.50[br](344ns)]][[role green 1.00[br](230ns)]]]
[[Binomial (PDF)][[role green 1.01[br](127ns)]][[role blue 1.41[br](178ns)]][[role green 1.00[br](126ns)]]]
[[Binomial (quantile)][[role blue 1.38[br](2024ns)]][[role green 1.18[br](1727ns)]][[role green 1.00[br](1466ns)]]]
[[Cauchy (CDF)][[role green 1.00[br](26ns)]][[role green 1.04[br](27ns)]][[role blue 1.23[br](32ns)]]]
[[Cauchy (PDF)][[role green 1.00[br](8ns)]][[role blue 1.38[br](11ns)]][[role green 1.13[br](9ns)]]]
[[Cauchy (quantile)][[role blue 1.37[br](26ns)]][[role green 1.00[br](19ns)]][[role blue 1.37[br](26ns)]]]
[[ChiSquared (CDF)][[role blue 1.35[br](676ns)]][[role blue 1.59[br](798ns)]][[role green 1.00[br](501ns)]]]
[[ChiSquared (PDF)][[role green 1.00[br](93ns)]][[role blue 1.27[br](118ns)]][[role blue 1.22[br](113ns)]]]
[[ChiSquared (quantile)][[role blue 1.62[br](1073ns)]][[role blue 1.83[br](1211ns)]][[role green 1.00[br](662ns)]]]
[[Exponential (CDF)][[role blue 1.70[br](17ns)]][[role green 1.10[br](11ns)]][[role green 1.00[br](10ns)]]]
[[Exponential (PDF)][[role blue 1.50[br](15ns)]][[role blue 1.70[br](17ns)]][[role green 1.00[br](10ns)]]]
[[Exponential (quantile)][[role green 1.18[br](20ns)]][[role green 1.00[br](17ns)]][[role green 1.00[br](17ns)]]]
[[ExtremeValue (CDF)][[role green 1.18[br](26ns)]][[role green 1.18[br](26ns)]][[role green 1.00[br](22ns)]]]
[[ExtremeValue (PDF)][[role green 1.08[br](27ns)]][[role green 1.04[br](26ns)]][[role green 1.00[br](25ns)]]]
[[ExtremeValue (quantile)][[role blue 1.48[br](34ns)]][[role green 1.09[br](25ns)]][[role green 1.00[br](23ns)]]]
[[F (CDF)][[role blue 1.56[br](277ns)]][[role blue 1.28[br](228ns)]][[role green 1.00[br](178ns)]]]
[[F (PDF)][[role green 1.07[br](97ns)]][[role green 1.13[br](103ns)]][[role green 1.00[br](91ns)]]]
[[F (quantile)][[role green 1.17[br](901ns)]][[role green 1.00[br](770ns)]][[role green 1.08[br](833ns)]]]
[[Gamma (CDF)][[role blue 1.30[br](234ns)]][[role blue 1.29[br](233ns)]][[role green 1.00[br](180ns)]]]
[[Gamma (PDF)][[role blue 1.25[br](85ns)]][[role blue 1.25[br](85ns)]][[role green 1.00[br](68ns)]]]
[[Gamma (quantile)][[role blue 1.64[br](640ns)]][[role blue 1.28[br](501ns)]][[role green 1.00[br](390ns)]]]
[[Geometric (CDF)][[role green 1.13[br](18ns)]][[role green 1.00[br](16ns)]][[role green 1.19[br](19ns)]]]
[[Geometric (PDF)][[role blue 1.85[br](24ns)]][[role blue 1.54[br](20ns)]][[role green 1.00[br](13ns)]]]
[[Geometric (quantile)][[role green 1.18[br](20ns)]][[role green 1.00[br](17ns)]][[role green 1.00[br](17ns)]]]
[[Hypergeometric (CDF)][[role green 1.00[br](244196ns)]][[role green 1.07[br](260490ns)]][[role green 1.11[br](271879ns)]]]
[[Hypergeometric (PDF)][[role green 1.00[br](272497ns)]][[role green 1.04[br](282183ns)]][[role green 1.09[br](296020ns)]]]
[[Hypergeometric (quantile)][[role green 1.00[br](308077ns)]][[role green 1.00[br](307365ns)]][[role green 1.06[br](326617ns)]]]
[[InverseChiSquared (CDF)][[role blue 1.65[br](584ns)]][[role blue 1.30[br](459ns)]][[role green 1.00[br](353ns)]]]
[[InverseChiSquared (PDF)][[role blue 1.37[br](78ns)]][[role blue 1.32[br](75ns)]][[role green 1.00[br](57ns)]]]
[[InverseChiSquared (quantile)][[role blue 1.68[br](884ns)]][[role blue 1.30[br](684ns)]][[role green 1.00[br](527ns)]]]
[[InverseGamma (CDF)][[role blue 1.36[br](244ns)]][[role green 1.17[br](210ns)]][[role green 1.00[br](179ns)]]]
[[InverseGamma (PDF)][[role blue 1.36[br](91ns)]][[role blue 1.39[br](93ns)]][[role green 1.00[br](67ns)]]]
[[InverseGamma (quantile)][[role blue 1.58[br](638ns)]][[role green 1.12[br](452ns)]][[role green 1.00[br](403ns)]]]
[[InverseGaussian (CDF)][[role blue 1.28[br](109ns)]][[role blue 1.32[br](112ns)]][[role green 1.00[br](85ns)]]]
[[InverseGaussian (PDF)][[role green 1.09[br](12ns)]][[role green 1.18[br](13ns)]][[role green 1.00[br](11ns)]]]
[[InverseGaussian (quantile)][[role blue 1.58[br](1651ns)]][[role green 1.15[br](1209ns)]][[role green 1.00[br](1048ns)]]]
[[Laplace (CDF)][[role green 1.00[br](13ns)]][[role green 1.00[br](13ns)]][[role green 1.00[br](13ns)]]]
[[Laplace (PDF)][[role green 1.08[br](14ns)]][[role blue 1.46[br](19ns)]][[role green 1.00[br](13ns)]]]
[[Laplace (quantile)][[role green 1.08[br](14ns)]][[role green 1.00[br](13ns)]][[role green 1.00[br](13ns)]]]
[[LogNormal (CDF)][[role green 1.04[br](79ns)]][[role green 1.00[br](76ns)]][[role green 1.08[br](82ns)]]]
[[LogNormal (PDF)][[role blue 1.25[br](35ns)]][[role green 1.07[br](30ns)]][[role green 1.00[br](28ns)]]]
[[LogNormal (quantile)][[role green 1.13[br](61ns)]][[role green 1.09[br](59ns)]][[role green 1.00[br](54ns)]]]
[[Logistic (CDF)][[role green 1.00[br](14ns)]][[role green 1.07[br](15ns)]][[role blue 1.36[br](19ns)]]]
[[Logistic (PDF)][[role green 1.06[br](18ns)]][[role green 1.00[br](17ns)]][[role blue 1.29[br](22ns)]]]
[[Logistic (quantile)][[role green 1.00[br](15ns)]][[role blue 1.33[br](20ns)]][[role blue 1.33[br](20ns)]]]
[[NegativeBinomial (CDF)][[role blue 1.69[br](481ns)]][[role blue 1.33[br](378ns)]][[role green 1.00[br](285ns)]]]
[[NegativeBinomial (PDF)][[role green 1.01[br](114ns)]][[role green 1.00[br](113ns)]][[role green 1.09[br](123ns)]]]
[[NegativeBinomial (quantile)][[role blue 1.21[br](2651ns)]][[role green 1.00[br](2186ns)]][[role green 1.17[br](2554ns)]]]
[[NonCentralBeta (CDF)][[role blue 1.90[br](735ns)]][[role blue 1.54[br](597ns)]][[role green 1.00[br](387ns)]]]
[[NonCentralBeta (PDF)][[role blue 1.62[br](489ns)]][[role blue 1.56[br](471ns)]][[role green 1.00[br](302ns)]]]
[[NonCentralBeta (quantile)][[role red 2.35[br](14689ns)]][[role red 2.10[br](13173ns)]][[role green 1.00[br](6263ns)]]]
[[NonCentralChiSquared (CDF)][[role blue 1.84[br](2643ns)]][[role blue 1.45[br](2087ns)]][[role green 1.00[br](1438ns)]]]
[[NonCentralChiSquared (PDF)][[role blue 1.36[br](290ns)]][[role blue 1.28[br](272ns)]][[role green 1.00[br](213ns)]]]
[[NonCentralChiSquared (quantile)][[role red 2.49[br](16692ns)]][[role blue 1.59[br](10665ns)]][[role green 1.00[br](6699ns)]]]
[[NonCentralF (CDF)][[role blue 1.54[br](608ns)]][[role blue 1.36[br](538ns)]][[role green 1.00[br](396ns)]]]
[[NonCentralF (PDF)][[role blue 1.44[br](467ns)]][[role blue 1.30[br](420ns)]][[role green 1.00[br](324ns)]]]
[[NonCentralF (quantile)][[role blue 1.73[br](9122ns)]][[role blue 1.44[br](7572ns)]][[role green 1.00[br](5271ns)]]]
[[NonCentralT (CDF)][[role blue 1.65[br](2375ns)]][[role blue 1.38[br](1985ns)]][[role green 1.00[br](1441ns)]]]
[[NonCentralT (PDF)][[role blue 1.58[br](1701ns)]][[role blue 1.34[br](1440ns)]][[role green 1.00[br](1075ns)]]]
[[NonCentralT (quantile)][[role blue 1.93[br](23683ns)]][[role blue 1.35[br](16597ns)]][[role green 1.00[br](12284ns)]]]
[[Normal (CDF)][[role green 1.09[br](89ns)]][[role green 1.00[br](82ns)]][[role blue 1.29[br](106ns)]]]
[[Normal (PDF)][[role blue 1.33[br](28ns)]][[role blue 1.38[br](29ns)]][[role green 1.00[br](21ns)]]]
[[Normal (quantile)][[role green 1.02[br](44ns)]][[role green 1.00[br](43ns)]][[role green 1.02[br](44ns)]]]
[[Pareto (CDF)][[role blue 1.26[br](34ns)]][[role blue 1.30[br](35ns)]][[role green 1.00[br](27ns)]]]
[[Pareto (PDF)][[role blue 1.50[br](102ns)]][[role blue 1.56[br](106ns)]][[role green 1.00[br](68ns)]]]
[[Pareto (quantile)][[role blue 1.79[br](50ns)]][[role blue 1.36[br](38ns)]][[role green 1.00[br](28ns)]]]
[[Poisson (CDF)][[role green 1.11[br](84ns)]][[role green 1.00[br](76ns)]][[role green 1.01[br](77ns)]]]
[[Poisson (PDF)][[role blue 1.40[br](49ns)]][[role blue 1.43[br](50ns)]][[role green 1.00[br](35ns)]]]
[[Poisson (quantile)][[role green 1.10[br](440ns)]][[role green 1.00[br](400ns)]][[role green 1.10[br](441ns)]]]
[[Rayleigh (CDF)][[role blue 1.25[br](15ns)]][[role green 1.08[br](13ns)]][[role green 1.00[br](12ns)]]]
[[Rayleigh (PDF)][[role green 1.08[br](14ns)]][[role green 1.08[br](14ns)]][[role green 1.00[br](13ns)]]]
[[Rayleigh (quantile)][[role green 1.15[br](23ns)]][[role green 1.15[br](23ns)]][[role green 1.00[br](20ns)]]]
[[SkewNormal (CDF)][[role green 1.01[br](259ns)]][[role green 1.00[br](256ns)]][[role green 1.13[br](289ns)]]]
[[SkewNormal (PDF)][[role green 1.03[br](94ns)]][[role green 1.00[br](91ns)]][[role green 1.08[br](98ns)]]]
[[SkewNormal (quantile)][[role blue 1.47[br](2843ns)]][[role green 1.00[br](1936ns)]][[role blue 1.24[br](2391ns)]]]
[[StudentsT (CDF)][[role blue 1.83[br](429ns)]][[role blue 1.85[br](434ns)]][[role green 1.00[br](235ns)]]]
[[StudentsT (PDF)][[role blue 1.36[br](146ns)]][[role blue 1.26[br](135ns)]][[role green 1.00[br](107ns)]]]
[[StudentsT (quantile)][[role blue 1.53[br](729ns)]][[role blue 1.57[br](749ns)]][[role green 1.00[br](476ns)]]]
[[Weibull (CDF)][[role blue 1.62[br](63ns)]][[role blue 1.51[br](59ns)]][[role green 1.00[br](39ns)]]]
[[Weibull (PDF)][[role blue 1.75[br](89ns)]][[role blue 1.76[br](90ns)]][[role green 1.00[br](51ns)]]]
[[Weibull (quantile)][[role blue 1.63[br](62ns)]][[role blue 1.55[br](59ns)]][[role green 1.00[br](38ns)]]]
]
]

[template table_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[table:table_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Distribution performance comparison with Microsoft Visual C++ version 14.2 on Windows x64
[[Function][boost 1.73]]
[[ArcSine (CDF)][[role green 1.00[br](43ns)]]]
[[ArcSine (PDF)][[role green 1.00[br](17ns)]]]
[[ArcSine (quantile)][[role green 1.00[br](30ns)]]]
[[Beta (CDF)][[role green 1.00[br](158ns)]]]
[[Beta (PDF)][[role green 1.00[br](104ns)]]]
[[Beta (quantile)][[role green 1.00[br](806ns)]]]
[[Binomial (CDF)][[role green 1.00[br](413ns)]]]
[[Binomial (PDF)][[role green 1.00[br](127ns)]]]
[[Binomial (quantile)][[role green 1.00[br](2024ns)]]]
[[Cauchy (CDF)][[role green 1.00[br](26ns)]]]
[[Cauchy (PDF)][[role green 1.00[br](8ns)]]]
[[Cauchy (quantile)][[role green 1.00[br](26ns)]]]
[[ChiSquared (CDF)][[role green 1.00[br](676ns)]]]
[[ChiSquared (PDF)][[role green 1.00[br](93ns)]]]
[[ChiSquared (quantile)][[role green 1.00[br](1073ns)]]]
[[Exponential (CDF)][[role green 1.00[br](17ns)]]]
[[Exponential (PDF)][[role green 1.00[br](15ns)]]]
[[Exponential (quantile)][[role green 1.00[br](20ns)]]]
[[ExtremeValue (CDF)][[role green 1.00[br](26ns)]]]
[[ExtremeValue (PDF)][[role green 1.00[br](27ns)]]]
[[ExtremeValue (quantile)][[role green 1.00[br](34ns)]]]
[[F (CDF)][[role green 1.00[br](277ns)]]]
[[F (PDF)][[role green 1.00[br](97ns)]]]
[[F (quantile)][[role green 1.00[br](901ns)]]]
[[Gamma (CDF)][[role green 1.00[br](234ns)]]]
[[Gamma (PDF)][[role green 1.00[br](85ns)]]]
[[Gamma (quantile)][[role green 1.00[br](640ns)]]]
[[Geometric (CDF)][[role green 1.00[br](18ns)]]]
[[Geometric (PDF)][[role green 1.00[br](24ns)]]]
[[Geometric (quantile)][[role green 1.00[br](20ns)]]]
[[Hypergeometric (CDF)][[role green 1.00[br](244196ns)]]]
[[Hypergeometric (PDF)][[role green 1.00[br](272497ns)]]]
[[Hypergeometric (quantile)][[role green 1.00[br](308077ns)]]]
[[InverseChiSquared (CDF)][[role green 1.00[br](584ns)]]]
[[InverseChiSquared (PDF)][[role green 1.00[br](78ns)]]]
[[InverseChiSquared (quantile)][[role green 1.00[br](884ns)]]]
[[InverseGamma (CDF)][[role green 1.00[br](244ns)]]]
[[InverseGamma (PDF)][[role green 1.00[br](91ns)]]]
[[InverseGamma (quantile)][[role green 1.00[br](638ns)]]]
[[InverseGaussian (CDF)][[role green 1.00[br](109ns)]]]
[[InverseGaussian (PDF)][[role green 1.00[br](12ns)]]]
[[InverseGaussian (quantile)][[role green 1.00[br](1651ns)]]]
[[Laplace (CDF)][[role green 1.00[br](13ns)]]]
[[Laplace (PDF)][[role green 1.00[br](14ns)]]]
[[Laplace (quantile)][[role green 1.00[br](14ns)]]]
[[LogNormal (CDF)][[role green 1.00[br](79ns)]]]
[[LogNormal (PDF)][[role green 1.00[br](35ns)]]]
[[LogNormal (quantile)][[role green 1.00[br](61ns)]]]
[[Logistic (CDF)][[role green 1.00[br](14ns)]]]
[[Logistic (PDF)][[role green 1.00[br](18ns)]]]
[[Logistic (quantile)][[role green 1.00[br](15ns)]]]
[[NegativeBinomial (CDF)][[role green 1.00[br](481ns)]]]
[[NegativeBinomial (PDF)][[role green 1.00[br](114ns)]]]
[[NegativeBinomial (quantile)][[role green 1.00[br](2651ns)]]]
[[NonCentralBeta (CDF)][[role green 1.00[br](735ns)]]]
[[NonCentralBeta (PDF)][[role green 1.00[br](489ns)]]]
[[NonCentralBeta (quantile)][[role green 1.00[br](14689ns)]]]
[[NonCentralChiSquared (CDF)][[role green 1.00[br](2643ns)]]]
[[NonCentralChiSquared (PDF)][[role green 1.00[br](290ns)]]]
[[NonCentralChiSquared (quantile)][[role green 1.00[br](16692ns)]]]
[[NonCentralF (CDF)][[role green 1.00[br](608ns)]]]
[[NonCentralF (PDF)][[role green 1.00[br](467ns)]]]
[[NonCentralF (quantile)][[role green 1.00[br](9122ns)]]]
[[NonCentralT (CDF)][[role green 1.00[br](2375ns)]]]
[[NonCentralT (PDF)][[role green 1.00[br](1701ns)]]]
[[NonCentralT (quantile)][[role green 1.00[br](23683ns)]]]
[[Normal (CDF)][[role green 1.00[br](89ns)]]]
[[Normal (PDF)][[role green 1.00[br](28ns)]]]
[[Normal (quantile)][[role green 1.00[br](44ns)]]]
[[Pareto (CDF)][[role green 1.00[br](34ns)]]]
[[Pareto (PDF)][[role green 1.00[br](102ns)]]]
[[Pareto (quantile)][[role green 1.00[br](50ns)]]]
[[Poisson (CDF)][[role green 1.00[br](84ns)]]]
[[Poisson (PDF)][[role green 1.00[br](49ns)]]]
[[Poisson (quantile)][[role green 1.00[br](440ns)]]]
[[Rayleigh (CDF)][[role green 1.00[br](15ns)]]]
[[Rayleigh (PDF)][[role green 1.00[br](14ns)]]]
[[Rayleigh (quantile)][[role green 1.00[br](23ns)]]]
[[SkewNormal (CDF)][[role green 1.00[br](259ns)]]]
[[SkewNormal (PDF)][[role green 1.00[br](94ns)]]]
[[SkewNormal (quantile)][[role green 1.00[br](2843ns)]]]
[[StudentsT (CDF)][[role green 1.00[br](429ns)]]]
[[StudentsT (PDF)][[role green 1.00[br](146ns)]]]
[[StudentsT (quantile)][[role green 1.00[br](729ns)]]]
[[Weibull (CDF)][[role green 1.00[br](63ns)]]]
[[Weibull (PDF)][[role green 1.00[br](89ns)]]]
[[Weibull (quantile)][[role green 1.00[br](62ns)]]]
]
]

[template table_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[table:table_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Library Comparison with Microsoft Visual C++ version 14.2 on Windows x64
[[Function][boost 1.73][math.h]]
[[cbrt[br](85/85 tests selected)][[role green 1.00[br](51ns)]][[role blue 1.22[br](62ns)]]]
[[cyl_bessel_j (integer order)[br](267/268 tests selected)][[role green 1.00[br](123ns)]][[role blue 1.50[br](185ns)]]]
[[cyl_neumann (integer order)[br](428/428 tests selected)][[role green 1.01[br](158ns)]][[role green 1.00[br](156ns)]]]
[[erf[br](950/950 tests selected)][[role red 2.15[br](43ns)]][[role green 1.00[br](20ns)]]]
[[erfc[br](950/950 tests selected)][[role green 1.00[br](54ns)]][[role green 1.09[br](59ns)]]]
[[expm1[br](80/80 tests selected)][[role green 1.10[br](11ns)]][[role green 1.00[br](10ns)]]]
[[lgamma[br](400/400 tests selected)][[role green 1.00[br](80ns)]][[role blue 1.60[br](128ns)]]]
[[log1p[br](80/80 tests selected)][[role green 1.00[br](14ns)]][[role green 1.07[br](15ns)]]]
[[tgamma[br](400/400 tests selected)][[role green 1.00[br](74ns)]][[role red 12.53[br](927ns)]]]
]
]

[template table_Compiler_Comparison_on_Windows_x64[]
[table:table_Compiler_Comparison_on_Windows_x64 Compiler Comparison on Windows x64
[[Function][Microsoft Visual C++ version 14.2[br]boost 1.73][GNU C++ version 9.2.0[br]boost 1.73][GNU C++ version 9.2.0[br]boost 1.73[br]promote_double<false>]]
[[assoc_laguerre][[role blue 1.41[br](179ns)]][[role green 1.08[br](137ns)]][[role green 1.00[br](127ns)]]]
[[assoc_legendre][[role blue 1.76[br](248ns)]][[role blue 1.36[br](192ns)]][[role green 1.00[br](141ns)]]]
[[beta][[role green 1.00[br](123ns)]][[role red 2.62[br](322ns)]][[role blue 1.93[br](237ns)]]]
[[beta (incomplete)][[role green 1.00[br](470ns)]][[role red 2.95[br](1385ns)]][[role blue 1.58[br](741ns)]]]
[[cbrt][[role red 3.40[br](51ns)]][[role red 4.67[br](70ns)]][[role green 1.00[br](15ns)]]]
[[cyl_bessel_i][[role green 1.00[br](281ns)]][[role red 3.38[br](949ns)]][[role blue 1.38[br](387ns)]]]
[[cyl_bessel_i (integer order)][[role green 1.00[br](195ns)]][[role red 3.06[br](597ns)]][[role green 1.00[br](195ns)]]]
[[cyl_bessel_j][[role green 1.00[br](371ns)]][[role red 2.39[br](886ns)]][[role blue 1.35[br](499ns)]]]
[[cyl_bessel_j (integer order)][[role blue 1.28[br](123ns)]][[role blue 1.92[br](184ns)]][[role green 1.00[br](96ns)]]]
[[cyl_bessel_k][[role green 1.11[br](385ns)]][[role red 19.68[br](6847ns)]][[role green 1.00[br](348ns)]]]
[[cyl_bessel_k (integer order)][[role green 1.06[br](217ns)]][[role red 18.17[br](3724ns)]][[role green 1.00[br](205ns)]]]
[[cyl_neumann][[role green 1.17[br](6696ns)]][[role blue 1.76[br](10032ns)]][[role green 1.00[br](5715ns)]]]
[[cyl_neumann (integer order)][[role green 1.00[br](158ns)]][[role red 2.20[br](348ns)]][[role blue 1.59[br](252ns)]]]
[[digamma][[role green 1.00[br](20ns)]][[role red 3.45[br](69ns)]][[role red 2.30[br](46ns)]]]
[[ellint_1][[role blue 1.57[br](390ns)]][[role blue 1.41[br](349ns)]][[role green 1.00[br](248ns)]]]
[[ellint_1 (complete)][[role blue 1.64[br](77ns)]][[role blue 1.64[br](77ns)]][[role green 1.00[br](47ns)]]]
[[ellint_2][[role blue 1.81[br](702ns)]][[role blue 1.50[br](583ns)]][[role green 1.00[br](388ns)]]]
[[ellint_2 (complete)][[role red 3.11[br](84ns)]][[role red 2.11[br](57ns)]][[role green 1.00[br](27ns)]]]
[[ellint_3][[role red 3.47[br](1381ns)]][[role blue 1.68[br](670ns)]][[role green 1.00[br](398ns)]]]
[[ellint_3 (complete)][[role red inf[br](802ns)]][[role green -nan(ind)[br](0ns)]][[role green -nan(ind)[br](0ns)]]]
[[ellint_rc][[role blue 1.55[br](59ns)]][[role red 2.21[br](84ns)]][[role green 1.00[br](38ns)]]]
[[ellint_rd][[role blue 1.32[br](271ns)]][[role blue 1.26[br](260ns)]][[role green 1.00[br](206ns)]]]
[[ellint_rf][[role blue 1.27[br](62ns)]][[role blue 1.94[br](95ns)]][[role green 1.00[br](49ns)]]]
[[ellint_rj][[role blue 1.46[br](264ns)]][[role red 2.29[br](414ns)]][[role green 1.00[br](181ns)]]]
[[erf][[role blue 1.30[br](43ns)]][[role blue 1.85[br](61ns)]][[role green 1.00[br](33ns)]]]
[[erfc][[role green 1.06[br](54ns)]][[role blue 1.76[br](90ns)]][[role green 1.00[br](51ns)]]]
[[expint][[role green 1.00[br](27ns)]][[role red 3.41[br](92ns)]][[role red 2.22[br](60ns)]]]
[[expint (En)][[role green 1.00[br](106ns)]][[role blue 1.94[br](206ns)]][[role blue 1.29[br](137ns)]]]
[[expm1][[role green 1.00[br](11ns)]][[role red 3.00[br](33ns)]][[role red 2.36[br](26ns)]]]
[[gamma_p][[role green 1.00[br](303ns)]][[role blue 2.00[br](605ns)]][[role green 1.17[br](355ns)]]]
[[gamma_p_inv][[role green 1.00[br](1266ns)]][[role blue 1.85[br](2341ns)]][[role green 1.15[br](1460ns)]]]
[[gamma_q][[role green 1.00[br](294ns)]][[role red 2.10[br](618ns)]][[role blue 1.21[br](356ns)]]]
[[gamma_q_inv][[role green 1.00[br](1194ns)]][[role blue 1.66[br](1987ns)]][[role green 1.14[br](1357ns)]]]
[[ibeta][[role green 1.00[br](512ns)]][[role red 2.63[br](1344ns)]][[role blue 1.31[br](673ns)]]]
[[ibeta_inv][[role green 1.00[br](1910ns)]][[role red 2.49[br](4751ns)]][[role blue 1.48[br](2822ns)]]]
[[ibetac][[role green 1.00[br](525ns)]][[role red 2.60[br](1365ns)]][[role blue 1.27[br](668ns)]]]
[[ibetac_inv][[role green 1.00[br](1676ns)]][[role red 2.85[br](4778ns)]][[role blue 1.74[br](2910ns)]]]
[[jacobi_cn][[role green 1.00[br](181ns)]][[role red 3.10[br](561ns)]][[role blue 2.00[br](362ns)]]]
[[jacobi_dn][[role green 1.00[br](203ns)]][[role red 3.03[br](616ns)]][[role blue 1.93[br](392ns)]]]
[[jacobi_sn][[role green 1.00[br](202ns)]][[role red 2.81[br](568ns)]][[role blue 1.73[br](350ns)]]]
[[laguerre][[role green 1.02[br](107ns)]][[role green 1.07[br](112ns)]][[role green 1.00[br](105ns)]]]
[[legendre][[role green 1.11[br](283ns)]][[role blue 1.25[br](320ns)]][[role green 1.00[br](255ns)]]]
[[legendre Q][[role green 1.00[br](309ns)]][[role blue 1.51[br](466ns)]][[role green 1.15[br](354ns)]]]
[[lgamma][[role green 1.00[br](80ns)]][[role red 2.67[br](214ns)]][[role blue 2.00[br](160ns)]]]
[[log1p][[role green 1.00[br](14ns)]][[role red 2.07[br](29ns)]][[role blue 1.21[br](17ns)]]]
[[polygamma][[role green 1.00[br](4193ns)]][[role blue 1.85[br](7743ns)]][[role blue 1.91[br](8018ns)]]]
[[sph_bessel][[role green 1.01[br](668ns)]][[role blue 1.48[br](975ns)]][[role green 1.00[br](661ns)]]]
[[sph_neumann][[role green 1.07[br](1138ns)]][[role red 2.96[br](3153ns)]][[role green 1.00[br](1064ns)]]]
[[tgamma][[role green 1.00[br](74ns)]][[role red 3.50[br](259ns)]][[role red 2.14[br](158ns)]]]
[[tgamma (incomplete)][[role green 1.00[br](208ns)]][[role red 2.30[br](478ns)]][[role blue 1.64[br](342ns)]]]
[[trigamma][[role green 1.00[br](12ns)]][[role red 2.83[br](34ns)]][[role green 1.17[br](14ns)]]]
[[zeta][[role green 1.00[br](117ns)]][[role red 2.65[br](310ns)]][[role blue 1.89[br](221ns)]]]
]
]


[template table_Compiler_Option_Comparison_on_Windows_x64[]
[table:table_Compiler_Option_Comparison_on_Windows_x64 Compiler Option Comparison on Windows x64
[[Function][cl /Od (x86 build)][cl /arch:sse2 /Ox (x86 build)][cl /Ox (x64 build)]]
[[boost::math::cbrt][[role red 5.05[br](202ns)]][[role green 1.20[br](48ns)]][[role green 1.00[br](40ns)]]]
[[boost::math::cyl_bessel_j (integer orders)][[role red 4.38[br](530ns)]][[role green 1.00[br](121ns)]][[role green 1.02[br](124ns)]]]
[[boost::math::ibeta_inv][[role red 4.52[br](8277ns)]][[role green 1.11[br](2042ns)]][[role green 1.00[br](1833ns)]]]
]
]


[/sections:]
[template section_Distribution_performance_comparison_for_different_performance_options_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[section:section_Distribution_performance_comparison_for_different_performance_options_with_Intel_C_C_0x_mode_version_1910_on_linux Distribution performance comparison for different performance options with Intel C++ C++0x mode version 1910 on linux]
[table_Distribution_performance_comparison_for_different_performance_options_with_Intel_C_C_0x_mode_version_1910_on_linux]
[endsect]
]

[template section_Distribution_performance_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[section:section_Distribution_performance_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux Distribution performance comparison with Intel C++ C++0x mode version 1910 on linux]
[table_Distribution_performance_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[endsect]
]

[template section_gcd_method_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[section:section_gcd_method_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux gcd method comparison with Intel C++ C++0x mode version 1910 on linux]
[table_gcd_method_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[endsect]
]

[template section_Polynomial_Arithmetic_Intel_C_C_0x_mode_version_1910_linux_[]
[section:section_Polynomial_Arithmetic_Intel_C_C_0x_mode_version_1910_linux_ Polynomial Arithmetic (Intel C++ C++0x mode version 1910, linux)]
[table_Polynomial_Arithmetic_Intel_C_C_0x_mode_version_1910_linux_]
[endsect]
]

[template section_Rational_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[section:section_Rational_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux Rational Method Comparison with Intel C++ C++0x mode version 1910 on linux]
[table_Rational_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[endsect]
]

[template section_Polynomial_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[section:section_Polynomial_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux Polynomial Method Comparison with Intel C++ C++0x mode version 1910 on linux]
[table_Polynomial_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[endsect]
]

[template section_Library_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux[]
[section:section_Library_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux Library Comparison with Intel C++ C++0x mode version 1910 on linux]
[table_Library_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[endsect]
]

[template section_Distribution_performance_comparison_for_different_performance_options_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[section:section_Distribution_performance_comparison_for_different_performance_options_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Distribution performance comparison for different performance options with Clang version 9.0.0 (tags/RELEASE_900/final) on linux]
[table_Distribution_performance_comparison_for_different_performance_options_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[endsect]
]

[template section_Distribution_performance_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[section:section_Distribution_performance_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Distribution performance comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux]
[table_Distribution_performance_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[endsect]
]

[template section_gcd_method_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[section:section_gcd_method_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux gcd method comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux]
[table_gcd_method_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[endsect]
]

[template section_Polynomial_Arithmetic_Clang_version_9_0_0_tags_RELEASE_900_final_linux_[]
[section:section_Polynomial_Arithmetic_Clang_version_9_0_0_tags_RELEASE_900_final_linux_ Polynomial Arithmetic (Clang version 9.0.0 (tags/RELEASE_900/final), linux)]
[table_Polynomial_Arithmetic_Clang_version_9_0_0_tags_RELEASE_900_final_linux_]
[endsect]
]

[template section_Rational_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[section:section_Rational_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Rational Method Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux]
[table_Rational_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[endsect]
]

[template section_Polynomial_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[section:section_Polynomial_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Polynomial Method Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux]
[table_Polynomial_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[endsect]
]

[template section_Library_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux[]
[section:section_Library_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux Library Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux]
[table_Library_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[endsect]
]

[template section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_1_20191008_on_linux[]
[section:section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_1_20191008_on_linux Distribution performance comparison for different performance options with GNU C++ version 9.2.1 20191008 on linux]
[table_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_1_20191008_on_linux]
[endsect]
]

[template section_Distribution_performance_comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[section:section_Distribution_performance_comparison_with_GNU_C_version_9_2_1_20191008_on_linux Distribution performance comparison with GNU C++ version 9.2.1 20191008 on linux]
[table_Distribution_performance_comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[endsect]
]

[template section_gcd_method_comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[section:section_gcd_method_comparison_with_GNU_C_version_9_2_1_20191008_on_linux gcd method comparison with GNU C++ version 9.2.1 20191008 on linux]
[table_gcd_method_comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[endsect]
]

[template section_Polynomial_Arithmetic_GNU_C_version_9_2_1_20191008_linux_[]
[section:section_Polynomial_Arithmetic_GNU_C_version_9_2_1_20191008_linux_ Polynomial Arithmetic (GNU C++ version 9.2.1 20191008, linux)]
[table_Polynomial_Arithmetic_GNU_C_version_9_2_1_20191008_linux_]
[endsect]
]

[template section_Rational_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[section:section_Rational_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux Rational Method Comparison with GNU C++ version 9.2.1 20191008 on linux]
[table_Rational_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[endsect]
]

[template section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[section:section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux Polynomial Method Comparison with GNU C++ version 9.2.1 20191008 on linux]
[table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[endsect]
]

[template section_Compiler_Comparison_on_linux[]
[section:section_Compiler_Comparison_on_linux Compiler Comparison on linux]
[table_Compiler_Comparison_on_linux]
[endsect]
]

[template section_Library_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux[]
[section:section_Library_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux Library Comparison with GNU C++ version 9.2.1 20191008 on linux]
[table_Library_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[endsect]
]

[template section_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_[]
[section:section_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_ Polynomial Arithmetic (GNU C++ version 9.2.0, Windows x64)]
[table_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_]
[endsect]
]

[template section_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[section:section_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64 Rational Method Comparison with GNU C++ version 9.2.0 on Windows x64]
[table_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[endsect]
]

[template section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[section:section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64 Polynomial Method Comparison with GNU C++ version 9.2.0 on Windows x64]
[table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[endsect]
]

[template section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64[]
[section:section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64 Distribution performance comparison for different performance options with GNU C++ version 9.2.0 on Windows x64]
[table_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64]
[endsect]
]

[template section_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[section:section_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64 Distribution performance comparison with GNU C++ version 9.2.0 on Windows x64]
[table_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[endsect]
]

[template section_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[section:section_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64 gcd method comparison with GNU C++ version 9.2.0 on Windows x64]
[table_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[endsect]
]

[template section_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64[]
[section:section_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64 Library Comparison with GNU C++ version 9.2.0 on Windows x64]
[table_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[endsect]
]

[template section_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_[]
[section:section_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_ Polynomial Arithmetic (Microsoft Visual C++ version 14.2, Windows x64)]
[table_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_]
[endsect]
]

[template section_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[section:section_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Rational Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64]
[table_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[endsect]
]

[template section_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[section:section_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Polynomial Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64]
[table_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[endsect]
]

[template section_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[section:section_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 gcd method comparison with Microsoft Visual C++ version 14.2 on Windows x64]
[table_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[endsect]
]

[template section_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[section:section_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Distribution performance comparison for different performance options with Microsoft Visual C++ version 14.2 on Windows x64]
[table_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[endsect]
]

[template section_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[section:section_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Distribution performance comparison with Microsoft Visual C++ version 14.2 on Windows x64]
[table_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[endsect]
]

[template section_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64[]
[section:section_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64 Library Comparison with Microsoft Visual C++ version 14.2 on Windows x64]
[table_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[endsect]
]

[template section_Compiler_Comparison_on_Windows_x64[]
[section:section_Compiler_Comparison_on_Windows_x64 Compiler Comparison on Windows x64]
[table_Compiler_Comparison_on_Windows_x64]
[endsect]
]


[template section_Compiler_Option_Comparison_on_Windows_x64[]
[section:section_Compiler_Option_Comparison_on_Windows_x64 Compiler Option Comparison on Windows x64]
[table_Compiler_Option_Comparison_on_Windows_x64]
[endsect]
]

[template performance_all_sections[]
[section_Compiler_Comparison_on_Windows_x64]
[section_Compiler_Comparison_on_linux]
[section_Compiler_Option_Comparison_on_Windows_x64]
[section_Distribution_performance_comparison_for_different_performance_options_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64]
[section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_1_20191008_on_linux]
[section_Distribution_performance_comparison_for_different_performance_options_with_Intel_C_C_0x_mode_version_1910_on_linux]
[section_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[section_Distribution_performance_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[section_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[section_Distribution_performance_comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[section_Distribution_performance_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[section_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[section_Library_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[section_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[section_Library_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[section_Library_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[section_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[section_Polynomial_Arithmetic_Clang_version_9_0_0_tags_RELEASE_900_final_linux_]
[section_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_]
[section_Polynomial_Arithmetic_GNU_C_version_9_2_1_20191008_linux_]
[section_Polynomial_Arithmetic_Intel_C_C_0x_mode_version_1910_linux_]
[section_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_]
[section_Polynomial_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[section_Polynomial_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[section_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[section_Rational_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[section_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[section_Rational_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[section_Rational_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[section_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[section_gcd_method_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[section_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[section_gcd_method_comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[section_gcd_method_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[section_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
]

[template performance_all_tables[]
[table_Compiler_Comparison_on_Windows_x64]
[table_Compiler_Comparison_on_linux]
[table_Compiler_Option_Comparison_on_Windows_x64]
[table_Distribution_performance_comparison_for_different_performance_options_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[table_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64]
[table_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_1_20191008_on_linux]
[table_Distribution_performance_comparison_for_different_performance_options_with_Intel_C_C_0x_mode_version_1910_on_linux]
[table_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[table_Distribution_performance_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[table_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[table_Distribution_performance_comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[table_Distribution_performance_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[table_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[table_Library_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[table_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[table_Library_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[table_Library_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[table_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[table_Polynomial_Arithmetic_Clang_version_9_0_0_tags_RELEASE_900_final_linux_]
[table_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_]
[table_Polynomial_Arithmetic_GNU_C_version_9_2_1_20191008_linux_]
[table_Polynomial_Arithmetic_Intel_C_C_0x_mode_version_1910_linux_]
[table_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_]
[table_Polynomial_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[table_Polynomial_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[table_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[table_Rational_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[table_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[table_Rational_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[table_Rational_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[table_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
[table_gcd_method_comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux]
[table_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64]
[table_gcd_method_comparison_with_GNU_C_version_9_2_1_20191008_on_linux]
[table_gcd_method_comparison_with_Intel_C_C_0x_mode_version_1910_on_linux]
[table_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64]
]
