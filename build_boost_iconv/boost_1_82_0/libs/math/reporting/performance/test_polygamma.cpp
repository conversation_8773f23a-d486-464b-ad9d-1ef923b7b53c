//  Copyright <PERSON> 2015.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifdef _MSC_VER
#  pragma warning (disable : 4224)
#endif

#include <boost/math/special_functions/polygamma.hpp>
#include <boost/array.hpp>
#include <boost/lexical_cast.hpp>
#include "../../test/table_type.hpp"
#include "table_helper.hpp"
#include "performance.hpp"
#include <iostream>

typedef double T;
typedef double value_type;
#define SC_(x) static_cast<double>(x)
   std::array<std::array<value_type, 3>, 484> data1 =
      { { 
      {{ 1, SC_(0.1250000), SC_(65.388133444988034473142999334395961) }}, {{ 1, SC_(2.250000), SC_(0.55732915450711073927131911933522402) }}, {{ 1, SC_(4.375000), SC_(0.25666408805722660683906428275458774) }}, {{ 1, SC_(6.500000), SC_(0.16628453574995823763989666631218566) }}, {{ 1, SC_(8.625000), SC_(0.12292237374423990274075995315923687) }}, {{ 1, SC_(10.75000), SC_(0.097483848201852104395946001854344927) }}, {{ 1, SC_(12.87500), SC_(0.080764208092843621858393487209278638) }}, {{ 1, SC_(15.00000), SC_(0.068938227847683806226155216756371670) }}, {{ 1, SC_(17.12500), SC_(0.060132263162293455894576107399989891) }}, {{ 1, SC_(19.25000), SC_(0.053320703915617277211139745531295189) }}, {{ 1, SC_(21.37500), SC_(0.047895038036916716105500109226810942) }}, {{ 1, SC_(23.50000), SC_(0.043471416266946770249685779030294199) }}, {{ 1, SC_(25.62500), SC_(0.039795743807625238080963836217545550) }}, {{ 1, SC_(27.75000), SC_(0.036693131333593477569076090983653779) }}, {{ 1, SC_(29.87500), SC_(0.034039266877179098641898178094001935) }}, {{ 1, SC_(32.00000), SC_(0.031743366520302090126581680438741427) }}, {{ 1, SC_(34.12500), SC_(0.029737585673522726661363528635488348) }}, {{ 1, SC_(36.25000), SC_(0.027970204614894933106878169214392067) }}, {{ 1, SC_(38.37500), SC_(0.026401106865951764123858232364900665) }}, {{ 1, SC_(40.50000), SC_(0.024998698201356741322280011143883922) }}, {{ 1, SC_(42.62500), SC_(0.023737757818075642720991864115881164) }}, {{ 1, SC_(44.75000), SC_(0.022597908441287364284087900916682571) }}, {{ 1, SC_(46.87500), SC_(0.021562506914486557632388530071308920) }}, {{ 1, SC_(49.00000), SC_(0.020617826354560516060031453062401102) }}, {{ 1, SC_(51.12500), SC_(0.019752444228552790805040230288386135) }}, {{ 1, SC_(53.25000), SC_(0.018956778300513446216011889734099110) }}, {{ 1, SC_(55.37500), SC_(0.018222730375562878627773770314126276) }}, {{ 1, SC_(57.50000), SC_(0.017543409716574620734228673575882677) }}, {{ 1, SC_(59.62500), SC_(0.016912916093398919581541485278641593) }}, {{ 1, SC_(61.75000), SC_(0.016326167985389235938281994221076159) }}, {{ 1, SC_(63.87500), SC_(0.015778765341125640054231784371915358) }}, {{ 1, SC_(66.00000), SC_(0.015266879048806385777045778219279459) }}, {{ 1, SC_(68.12500), SC_(0.014787161242916062152535888615850260) }}, {{ 1, SC_(70.25000), SC_(0.014336672004276912093324664070489886) }}, {{ 1, SC_(72.37500), SC_(0.013912819061256593888508241399678441) }}, {{ 1, SC_(74.50000), SC_(0.013513307879079644573772830117155790) }}, {{ 1, SC_(76.62500), SC_(0.013136100107643257539226305043251166) }}, {{ 1, SC_(78.75000), SC_(0.012779378799112389298746113446648982) }}, {{ 1, SC_(80.87500), SC_(0.012441519142554280549428650598112729) }}, {{ 1, SC_(83.00000), SC_(0.012121063720980953787190412456820037) }}, {{ 1, SC_(85.12500), SC_(0.011816701495952671887865236708275449) }}, {{ 1, SC_(87.25000), SC_(0.011527249880640584213306489206202069) }}, {{ 1, SC_(89.37500), SC_(0.011251639384481213426240562514542477) }}, {{ 1, SC_(91.50000), SC_(0.010988900409103388104670465071922664) }}, {{ 1, SC_(93.62500), SC_(0.010738151851930343087942933225290955) }}, {{ 1, SC_(95.75000), SC_(0.010498591235178571927709342934117247) }}, {{ 1, SC_(97.87500), SC_(0.010269486127251686167359605922136260) }}, {{ 1, SC_(100.0000), SC_(0.010050166663333571395245668465701423) }},
      {{ 2, SC_(0.1250000), -SC_(1025.7533381181356825956689300565174) }}, {{ 2, SC_(2.250000), -SC_(0.30373993753692033333796717884398989) }}, {{ 2, SC_(4.375000), -SC_(0.065528725397877855792664680804766330) }}, {{ 2, SC_(6.500000), -SC_(0.027587910706876798794117450572831562) }}, {{ 2, SC_(8.625000), -SC_(0.015091062676061564388822078971884395) }}, {{ 2, SC_(10.75000), -SC_(0.0094956196449265900776488965631791775) }}, {{ 2, SC_(12.87500), -SC_(0.0065193261909178260169885198194705291) }}, {{ 2, SC_(15.00000), -SC_(0.0047506027165515547467791223768191188) }}, {{ 2, SC_(17.12500), -SC_(0.0036148020016626802195565448384834283) }}, {{ 2, SC_(19.25000), -SC_(0.0028424250740909855631736845850335535) }}, {{ 2, SC_(21.37500), -SC_(0.0022934967923297751145806065882712900) }}, {{ 2, SC_(23.50000), -SC_(0.0018894667868625909895476094900477678) }}, {{ 2, SC_(25.62500), -SC_(0.0015834924252652953218803111387922996) }}, {{ 2, SC_(27.75000), -SC_(0.0013462349527320363170378913859580860) }}, {{ 2, SC_(29.87500), -SC_(0.0011585598948326545653381467670779893) }}, {{ 2, SC_(32.00000), -SC_(0.0010075567602140907392185110593117265) }}, {{ 2, SC_(34.12500), -SC_(0.00088425886906787461365402045268994574) }}, {{ 2, SC_(36.25000), -SC_(0.00078228136778540401396894643668418462) }}, {{ 2, SC_(38.37500), -SC_(0.00069697797537723210697105880606724159) }}, {{ 2, SC_(40.50000), -SC_(0.00062490237932923289658683588812998732) }}, {{ 2, SC_(42.62500), -SC_(0.00056345469641484389456121935536309197) }}, {{ 2, SC_(44.75000), -SC_(0.00051064374134295093656385520125286421) }}, {{ 2, SC_(46.87500), -SC_(0.00046492369550612086723038302919171885) }}, {{ 2, SC_(49.00000), -SC_(0.00042507970884222510504308867471824948) }}, {{ 2, SC_(51.12500), -SC_(0.00039014637079445100439645401142194535) }}, {{ 2, SC_(53.25000), -SC_(0.00035934868438211062777790080207505881) }}, {{ 2, SC_(55.37500), -SC_(0.00033205871518117505559928737076624192) }}, {{ 2, SC_(57.50000), -SC_(0.00030776333242771756953580263456220628) }}, {{ 2, SC_(59.62500), -SC_(0.00028603991345613245802207114123372414) }}, {{ 2, SC_(61.75000), -SC_(0.00026653784162151616772904552992231739) }}, {{ 2, SC_(63.87500), -SC_(0.00024896427102287300629668349085350311) }}, {{ 2, SC_(66.00000), -SC_(0.00023307306946180321476975587412226332) }}, {{ 2, SC_(68.12500), -SC_(0.00021865615382096049855997233359992101) }}, {{ 2, SC_(70.25000), -SC_(0.00020553664405312492990454318541834320) }}, {{ 2, SC_(72.37500), -SC_(0.00019356341228034103704385457246984425) }}, {{ 2, SC_(74.50000), -SC_(0.00018260671130395075946075978013398780) }}, {{ 2, SC_(76.62500), -SC_(0.00017255464497899193114313963889049197) }}, {{ 2, SC_(78.75000), -SC_(0.00016331030013937037094502788633900241) }}, {{ 2, SC_(80.87500), -SC_(0.00015478940207215993505449988937898640) }}, {{ 2, SC_(83.00000), -SC_(0.00014691838710034332570083901051760808) }}, {{ 2, SC_(85.12500), -SC_(0.00013963280957351165370765015817038278) }}, {{ 2, SC_(87.25000), -SC_(0.00013287601856558888563037791437997733) }}, {{ 2, SC_(89.37500), -SC_(0.00012659805332837531702241737466058293) }}, {{ 2, SC_(91.50000), -SC_(0.00012075471712784846612240303590084686) }}, {{ 2, SC_(93.62500), -SC_(0.00011530679728326736002584104318350829) }}, {{ 2, SC_(95.75000), -SC_(0.00011021940561565095981861374083495299) }}, {{ 2, SC_(97.87500), -SC_(0.00010546141852085692313553484671110170) }}, {{ 2, SC_(100.0000), -SC_(0.00010100499983334999700083300446059382) }},
      {{ 3, SC_(0.1250000), SC_(24580.143419063566218511004446647010) }},
      {{ 3, SC_(2.250000), SC_(0.32454400918839602279124382903505677) }},
      {{ 3, SC_(4.375000), SC_(0.033289201205556512286673394063908247) }},
      {{ 3, SC_(6.500000), SC_(0.0091336635043781405048050655353658508) }},
      {{ 3, SC_(8.625000), SC_(0.0037008460120616755687668778806054328) }},
      {{ 3, SC_(10.75000), SC_(0.0018484328773891268475922730275004208) }},
      {{ 3, SC_(12.87500), SC_(0.0010519186241203463873018930415985105) }},
      {{ 3, SC_(15.00000), SC_(0.00065447977828273734841733708901750530) }},
      {{ 3, SC_(17.12500), SC_(0.00043447135395979214393136263736764201) }},
      {{ 3, SC_(19.25000), SC_(0.00030297696415718385912539504824566570) }},
      {{ 3, SC_(21.37500), SC_(0.00021961042047214603636739002880115737) }},
      {{ 3, SC_(23.50000), SC_(0.00016422394665239820056068884910278128) }},
      {{ 3, SC_(25.62500), SC_(0.00012599930135391694524067581601102767) }},
      {{ 3, SC_(27.75000), SC_(0.000098773010741268191080883613389009048) }},
      {{ 3, SC_(29.87500), SC_(0.000078857844307768334137453508928722671) }},
      {{ 3, SC_(32.00000), SC_(0.000063955754777976299576673673574642423) }},
      {{ 3, SC_(34.12500), SC_(0.000052583702941352521813139097855885393) }},
      {{ 3, SC_(36.25000), SC_(0.000043755438122384003302367816109980192) }},
      {{ 3, SC_(38.37500), SC_(0.000036797707573246758497786056137200200) }},
      {{ 3, SC_(40.50000), SC_(0.000031240239710655936422248716017582946) }},
      {{ 3, SC_(42.62500), SC_(0.000026747791378195202271687207455056302) }},
      {{ 3, SC_(44.75000), SC_(0.000023076997706632396178157160717335565) }},
      {{ 3, SC_(46.87500), SC_(0.000020048287815935784161580614536631137) }},
      {{ 3, SC_(49.00000), SC_(0.000017527197874026635143654793647828466) }},
      {{ 3, SC_(51.12500), SC_(0.000015411686998050652806770684541876757) }},
      {{ 3, SC_(53.25000), SC_(0.000013623370966529973793708331754693666) }},
      {{ 3, SC_(55.37500), SC_(0.000012101363299593660023105041673385376) }},
      {{ 3, SC_(57.50000), SC_(0.000010797882726896889062768134787291884) }},
      {{ 3, SC_(59.62500), SC_(9.6750769605707758334815857396558355e-6) }},
      {{ 3, SC_(61.75000), SC_(8.7026966259856900409582330070865268e-6) }},
      {{ 3, SC_(63.87500), SC_(7.8563716857343030149244371160376495e-6) }},
      {{ 3, SC_(66.00000), SC_(7.1163203299934132027680033779014197e-6) }},
      {{ 3, SC_(68.12500), SC_(6.4663719906627627544882175944382226e-6) }},
      {{ 3, SC_(70.25000), SC_(5.8932210515208497328937862935232496e-6) }},
      {{ 3, SC_(72.37500), SC_(5.3858517367657111634358444590928906e-6) }},
      {{ 3, SC_(74.50000), SC_(4.9350912436686398240670814427342376e-6) }},
      {{ 3, SC_(76.62500), SC_(4.5332598242018110991734569100539592e-6) }},
      {{ 3, SC_(78.75000), SC_(4.1738947808706992483423293125251133e-6) }},
      {{ 3, SC_(80.87500), SC_(3.8515312659502461861484363442184908e-6) }},
      {{ 3, SC_(83.00000), SC_(3.5615270636518630626786644585266764e-6) }},
      {{ 3, SC_(85.12500), SC_(3.2999216708248315372665461067377996e-6) }},
      {{ 3, SC_(87.25000), SC_(3.0633223042636924720337703682152499e-6) }},
      {{ 3, SC_(89.37500), SC_(2.8488111819903247190925375784544775e-6) }},
      {{ 3, SC_(91.50000), SC_(2.6538697141941088454844912572034392e-6) }},
      {{ 3, SC_(93.62500), SC_(2.4763162120667457234467192228222670e-6) }},
      {{ 3, SC_(95.75000), SC_(2.3142544621402797124759791203351867e-6) }},
      {{ 3, SC_(97.87500), SC_(2.1660310796121506708372890790676070e-6) }},
      {{ 3, SC_(100.0000), SC_(2.0301999900013330334332872946449855e-6) }},

      {{4, SC_(0.1250000), SC_(-786445.98543106378579320120709638297)}}, {{ 4, SC_(2.250000), SC_(-0.51106863793373355822715252195899576) }}, {{ 4, SC_(4.375000), SC_(-0.025241882074562753654505456518792508) }}, {{ 4, SC_(6.500000), SC_(-0.0045259302803220607103496010172883494) }}, {{ 4, SC_(8.625000), SC_(-0.0013596929106556359886012273921730071) }}, {{ 4, SC_(10.75000), SC_(-0.00053930828860547053427689740051604949) }}, {{ 4, SC_(12.87500), SC_(-0.00025445997266103206171225355293726865) }}, {{ 4, SC_(15.00000), SC_(-0.00013519619187519276575068431301221388) }}, {{ 4, SC_(17.12500), SC_(-0.000078306716822025065793290599586946118) }}, {{ 4, SC_(19.25000), SC_(-0.000048430513924395561509999792045098081) }}, {{ 4, SC_(21.37500), SC_(-0.000031536705873201397805285969259914559) }}, {{ 4, SC_(23.50000), SC_(-0.000021407049050977062373311093992814121) }}, {{ 4, SC_(25.62500), SC_(-0.000015036764178343754461565939319516893) }}, {{ 4, SC_(27.75000), SC_(-0.000010869219781124567432001391245344089) }}, {{ 4, SC_(29.87500), SC_(-8.0504604926168752698902834477348881e-6) }}, {{ 4, SC_(32.00000), SC_(-6.0889806370027137702207132674152980e-6) }}, {{ 4, SC_(34.12500), SC_(-4.6901011823268163094417546531023856e-6) }}, {{ 4, SC_(36.25000), SC_(-3.6708283086618325558290000911536581e-6) }}, {{ 4, SC_(38.37500), SC_(-2.9139932244650290336213711603586301e-6) }}, {{ 4, SC_(40.50000), SC_(-2.3425302303691304543510535971847968e-6) }}, {{ 4, SC_(42.62500), SC_(-1.9045296486050512642801504306671373e-6) }}, {{ 4, SC_(44.75000), SC_(-1.5642760453399369047824852836708697e-6) }}, {{ 4, SC_(46.87500), SC_(-1.2967233822729326645555539185332769e-6) }}, {{ 4, SC_(49.00000), SC_(-1.0840030171141394983603065427611252e-6) }}, {{ 4, SC_(51.12500), SC_(-9.1316643010960703020517255319750485e-7) }}, {{ 4, SC_(53.25000), SC_(-7.7469609700462921021995856208285815e-7) }}, {{ 4, SC_(55.37500), SC_(-6.6150474476547305805911346012045165e-7) }}, {{ 4, SC_(57.50000), SC_(-5.6825133351570053043820949353677183e-7) }}, {{ 4, SC_(59.62500), SC_(-4.9086620236193161135912086142572580e-7) }}, {{ 4, SC_(61.75000), SC_(-4.2621666772058776224801114961267801e-7) }}, {{ 4, SC_(63.87500), SC_(-3.7186839586672891333608898041067112e-7) }}, {{ 4, SC_(66.00000), SC_(-3.2591301914111222705427951896642626e-7) }}, {{ 4, SC_(68.12500), SC_(-2.8684217920848561693233665614958777e-7) }}, {{ 4, SC_(70.25000), SC_(-2.5345451083204479337777798526354757e-7) }}, {{ 4, SC_(72.37500), SC_(-2.2478626654835416271537021644553013e-7) }}, {{ 4, SC_(74.50000), SC_(-2.0005909073946645851596541397664333e-7) }}, {{ 4, SC_(76.62500), SC_(-1.7864035957298817513241281766878220e-7) }}, {{ 4, SC_(78.75000), SC_(-1.6001281551287093216803849072235519e-7) }}, {{ 4, SC_(80.87500), SC_(-1.4375113796429048125939526070340983e-7) }}, {{ 4, SC_(83.00000), SC_(-1.2950373350296884735201964028079076e-7) }}, {{ 4, SC_(85.12500), SC_(-1.1697848507486443027254417751822546e-7) }}, {{ 4, SC_(87.25000), SC_(-1.0593152651055065213702117539700727e-7) }}, {{ 4, SC_(89.37500), SC_(-9.6158345290200883062520858433963680e-8) }}, {{ 4, SC_(91.50000), SC_(-8.7486689164653963817234101603388082e-8) }}, {{ 4, SC_(93.62500), SC_(-7.9770879280803145900384972987304463e-8) }}, {{ 4, SC_(95.75000), SC_(-7.2887226653828502529665531368895369e-8) }}, {{ 4, SC_(97.87500), SC_(-6.6730319180606630829991063550458595e-8) }}, {{ 4, SC_(100.0000), SC_(-6.1209999300119967012993094755881001e-8) }},
      {{ 9, SC_(0.1250000), SC_(3.8963943320506514766700086867372762e14) }}, {{ 9, SC_(2.250000), SC_(112.10537259293726188704511169205760) }}, {{ 9, SC_(4.375000), SC_(0.16363919906361224458550935569570297) }}, {{ 9, SC_(6.500000), SC_(0.0036236228486465262827554601212241334) }}, {{ 9, SC_(8.625000), SC_(0.00024724827512609608284324030730925865) }}, {{ 9, SC_(10.75000), SC_(0.000031173409615380334086095590039151370) }}, {{ 9, SC_(12.87500), SC_(5.7824557132102074668570643058203530e-6) }}, {{ 9, SC_(15.00000), SC_(1.3980855499116564037825647833211369e-6) }}, {{ 9, SC_(17.12500), SC_(4.1002910484982160273499509487840966e-7) }}, {{ 9, SC_(19.25000), SC_(1.3928434419796988121776758599785632e-7) }}, {{ 9, SC_(21.37500), SC_(5.3108201638858702713365922453188085e-8) }}, {{ 9, SC_(23.50000), SC_(2.2228120959414339842295401083512930e-8) }}, {{ 9, SC_(25.62500), SC_(1.0046019155325212007574589173916857e-8) }}, {{ 9, SC_(27.75000), SC_(4.8421087709589697640039499715706350e-9) }}, {{ 9, SC_(29.87500), SC_(2.4651114661905897591489828702449678e-9) }}, {{ 9, SC_(32.00000), SC_(1.3154897461853304542190391130140025e-9) }}, {{ 9, SC_(34.12500), SC_(7.3134444425276046658844002904667737e-10) }}, {{ 9, SC_(36.25000), SC_(4.2146721224691157996140805304942419e-10) }}, {{ 9, SC_(38.37500), SC_(2.5073387292785072270991813660631786e-10) }}, {{ 9, SC_(40.50000), SC_(1.5344896969374692327054949367251260e-10) }}, {{ 9, SC_(42.62500), SC_(9.6326225603670754883495513330468241e-11) }}, {{ 9, SC_(44.75000), SC_(6.1868579168628392331266656512721531e-11) }}, {{ 9, SC_(46.87500), SC_(4.0570340164556015067828668493424902e-11) }}, {{ 9, SC_(49.00000), SC_(2.7111471879948743580114339016340155e-11) }}, {{ 9, SC_(51.12500), SC_(1.8433178567812423799249519881074934e-11) }}, {{ 9, SC_(53.25000), SC_(1.2733080158190412770374826663936684e-11) }}, {{ 9, SC_(55.37500), SC_(8.9250208887659481803261595053553675e-12) }}, {{ 9, SC_(57.50000), SC_(6.3408264036510339424452353875188095e-12) }}, {{ 9, SC_(59.62500), SC_(4.5615718874164287974321379689375635e-12) }}, {{ 9, SC_(61.75000), SC_(3.3199519699896432328216923633601725e-12) }}, {{ 9, SC_(63.87500), SC_(2.4426063512782352683767956278168985e-12) }}, {{ 9, SC_(66.00000), SC_(1.8153877149006577317987652651306379e-12) }}, {{ 9, SC_(68.12500), SC_(1.3620688165735114474674715908706633e-12) }}, {{ 9, SC_(70.25000), SC_(1.0310696339614726306516051424066462e-12) }}, {{ 9, SC_(72.37500), SC_(7.8705425492600705644491688042239872e-13) }}, {{ 9, SC_(74.50000), SC_(6.0553290145315817804599098427153448e-13) }}, {{ 9, SC_(76.62500), SC_(4.6934676954218461465393390444338856e-13) }}, {{ 9, SC_(78.75000), SC_(3.6634942521175284527379986400237305e-13) }}, {{ 9, SC_(80.87500), SC_(2.8785887689513567008615786864512179e-13) }}, {{ 9, SC_(83.00000), SC_(2.2761232508374172373558791779207099e-13) }}, {{ 9, SC_(85.12500), SC_(1.8105271275784390697733304142062576e-13) }}, {{ 9, SC_(87.25000), SC_(1.4483676610519321733096991924616972e-13) }}, {{ 9, SC_(89.37500), SC_(1.1649247346426341778218700567243322e-13) }}, {{ 9, SC_(91.50000), SC_(9.4178413859615655080323473554582586e-14) }}, {{ 9, SC_(93.62500), SC_(7.6513170761508530170189422972149840e-14) }}, {{ 9, SC_(95.75000), SC_(6.2453415533716351149737976646666822e-14) }}, {{ 9, SC_(97.87500), SC_(5.1206083919072129278059542340974138e-14) }}, {{ 9, SC_(100.0000), SC_(4.2164633350081151607323910418414347e-14) }},
      {{ SC_(12.0), SC_(0.1250000), SC_(-2.6333391446175784623707514121843937e20) }}, {{ SC_(12.0), SC_(2.250000), SC_(-12755.934552347367694976392995238872) }}, {{ SC_(12.0), SC_(4.375000), SC_(-2.3995726885358590731215736760290659) }}, {{ SC_(12.0), SC_(6.500000), SC_(-0.015498964669830504389631890538267195) }}, {{ SC_(12.0), SC_(8.625000), SC_(-0.00043875188128348404126256495384460962) }}, {{ SC_(12.0), SC_(10.75000), SC_(-0.000027944407762544917990491373502083128) }}, {{ SC_(12.0), SC_(12.87500), SC_(-2.9683309167523389772660608703858297e-6) }}, {{ SC_(12.0), SC_(15.00000), SC_(-4.4822030612790888239848346241379762e-7) }}, {{ SC_(12.0), SC_(17.12500), SC_(-8.7479493941258027953346726543995677e-8) }}, {{ SC_(12.0), SC_(19.25000), SC_(-2.0757514198487629655709801633624060e-8) }}, {{ SC_(12.0), SC_(21.37500), SC_(-5.7438353740388980670397346200747331e-9) }}, {{ SC_(12.0), SC_(23.50000), SC_(-1.7993432749405246733914335090978470e-9) }}, {{ SC_(12.0), SC_(25.62500), SC_(-6.2435444143873592979499739047338207e-10) }}, {{ SC_(12.0), SC_(27.75000), SC_(-2.3603187097640550778749196581740161e-10) }}, {{ SC_(12.0), SC_(29.87500), SC_(-9.5975541975412793814120279791068637e-11) }}, {{ SC_(12.0), SC_(32.00000), SC_(-4.1552035282000578511439617844316334e-11) }}, {{ SC_(12.0), SC_(34.12500), SC_(-1.8998432787826251814581286958206123e-11) }}, {{ SC_(12.0), SC_(36.25000), SC_(-9.1125382347537588010833133716666482e-12) }}, {{ SC_(12.0), SC_(38.37500), SC_(-4.5599501480319696078145812150042737e-12) }}, {{ SC_(12.0), SC_(40.50000), SC_(-2.3695979124099134723655912747303232e-12) }}, {{ SC_(12.0), SC_(42.62500), SC_(-1.2737626539048915719376237689570656e-12) }}, {{ SC_(12.0), SC_(44.75000), SC_(-7.0592220046106837677774762353832582e-13) }}, {{ SC_(12.0), SC_(46.87500), SC_(-4.0219605264396476495155503895759108e-13) }}, {{ SC_(12.0), SC_(49.00000), SC_(-2.3499370395545407582624144041709511e-13) }}, {{ SC_(12.0), SC_(51.12500), SC_(-1.4049959061173089543038862427082956e-13) }}, {{ SC_(12.0), SC_(53.25000), SC_(-8.5797116983505159074486972598899288e-14) }}, {{ SC_(12.0), SC_(55.37500), SC_(-5.3422568073471069670219359482696794e-14) }}, {{ SC_(12.0), SC_(57.50000), SC_(-3.3867985175935679891876577636637264e-14) }}, {{ SC_(12.0), SC_(59.62500), SC_(-2.1832074877153446073077836903455745e-14) }}, {{ SC_(12.0), SC_(61.75000), SC_(-1.4293240909063248751348970309438269e-14) }}, {{ SC_(12.0), SC_(63.87500), SC_(-9.4937480883301538244379833116307891e-15) }}, {{ SC_(12.0), SC_(66.00000), SC_(-6.3914967852421984438071853365851405e-15) }}, {{ SC_(12.0), SC_(68.12500), SC_(-4.3576413857619763879519099830883388e-15) }}, {{ SC_(12.0), SC_(70.25000), SC_(-3.0063897012087057369199810835484853e-15) }}, {{ SC_(12.0), SC_(72.37500), SC_(-2.0973711510201259912443465435969711e-15) }}, {{ SC_(12.0), SC_(74.50000), SC_(-1.4786311290892013378232248983192765e-15) }}, {{ SC_(12.0), SC_(76.62500), SC_(-1.0527885762581410791135439292370286e-15) }}, {{ SC_(12.0), SC_(78.75000), SC_(-7.5662859409010038912157660753875556e-16) }}, {{ SC_(12.0), SC_(80.87500), SC_(-5.4861427907366815368388985862444385e-16) }}, {{ SC_(12.0), SC_(83.00000), SC_(-4.0113795601837316346341436637916290e-16) }}, {{ SC_(12.0), SC_(85.12500), SC_(-2.9564974628089487210535317533851159e-16) }}, {{ SC_(12.0), SC_(87.25000), SC_(-2.1955682892705039626630737602169576e-16) }}, {{ SC_(12.0), SC_(89.37500), SC_(-1.6422644673043790572542870203840550e-16) }}, {{ SC_(12.0), SC_(91.50000), SC_(-1.2368534305097629421391380808602245e-16) }}, {{ SC_(12.0), SC_(93.62500), SC_(-9.3763732289494543868014071205929613e-17) }}, {{ SC_(12.0), SC_(95.75000), SC_(-7.1526151114554258090139227356676697e-17) }}, {{ SC_(12.0), SC_(97.87500), SC_(-5.4889394969371742548386275256109609e-17) }}, {{ SC_(12.0), SC_(100.0000), SC_(-4.2363681689608104413899863907775333e-17) }},
      {{ SC_(21.0), SC_(0.1250000), SC_(3.7698461389048740847200205590867822e39) }}, {{ SC_(21.0), SC_(2.250000), SC_(9.1298158507949915159597719407312508e11) }}, {{ SC_(21.0), SC_(4.375000), SC_(408886.47063811418290988174462904689) }}, {{ SC_(21.0), SC_(6.500000), SC_(69.783498668647519157069128850360732) }}, {{ SC_(21.0), SC_(8.625000), SC_(0.14574011463957290617412866498392900) }}, {{ SC_(21.0), SC_(10.75000), SC_(0.0012181648740805635977027563578320036) }}, {{ SC_(21.0), SC_(12.87500), SC_(0.000024558760763169978336155197432207549) }}, {{ SC_(21.0), SC_(15.00000), SC_(9.0946144696294632279197453808024020e-7) }}, {{ SC_(21.0), SC_(17.12500), SC_(5.2548180764045016519118256734019257e-8) }}, {{ SC_(21.0), SC_(19.25000), SC_(4.2632373548174335144878212855377856e-9) }}, {{ SC_(21.0), SC_(21.37500), SC_(4.5191821412507380972584044138294203e-10) }}, {{ SC_(21.0), SC_(23.50000), SC_(5.9459999472668271337052481421711044e-11) }}, {{ SC_(21.0), SC_(25.62500), SC_(9.3494613148584596286144185433362367e-12) }}, {{ SC_(21.0), SC_(27.75000), SC_(1.7071269974977763414024284983419274e-12) }}, {{ SC_(21.0), SC_(29.87500), SC_(3.5397471216602229335658443914126577e-13) }}, {{ SC_(21.0), SC_(32.00000), SC_(8.1890165923201938880286772740175496e-14) }}, {{ SC_(21.0), SC_(34.12500), SC_(2.0838387642645373192280813068168954e-14) }}, {{ SC_(21.0), SC_(36.25000), SC_(5.7652633708208411748169589666923627e-15) }}, {{ SC_(21.0), SC_(38.37500), SC_(1.7175814132832512497760866952875142e-15) }}, {{ SC_(21.0), SC_(40.50000), SC_(5.4658871187824521586899247607212215e-16) }}, {{ SC_(21.0), SC_(42.62500), SC_(1.8454073409395762586978128093893739e-16) }}, {{ SC_(21.0), SC_(44.75000), SC_(6.5718894398363114531765518843039215e-17) }}, {{ SC_(21.0), SC_(46.87500), SC_(2.4563292426503881973678226160508883e-17) }}, {{ SC_(21.0), SC_(49.00000), SC_(9.5940884241955097871390055431773988e-18) }}, {{ SC_(21.0), SC_(51.12500), SC_(3.9012613551839347983144664217896505e-18) }}, {{ SC_(21.0), SC_(53.25000), SC_(1.6460967024588312755378806948280578e-18) }}, {{ SC_(21.0), SC_(55.37500), SC_(7.1860306265059509271853406338496567e-19) }}, {{ SC_(21.0), SC_(57.50000), SC_(3.2373123407072660945448849676538922e-19) }}, {{ SC_(21.0), SC_(59.62500), SC_(1.5015620637437584782988688648328655e-19) }}, {{ SC_(21.0), SC_(61.75000), SC_(7.1560282337372458319334209159595311e-20) }}, {{ SC_(21.0), SC_(63.87500), SC_(3.4975910507684158405937390693690592e-20) }}, {{ SC_(21.0), SC_(66.00000), SC_(1.7502964214288619731071110181806997e-20) }}, {{ SC_(21.0), SC_(68.12500), SC_(8.9546013195046736291336879943433535e-21) }}, {{ SC_(21.0), SC_(70.25000), SC_(4.6771364193808931649132626805767700e-21) }}, {{ SC_(21.0), SC_(72.37500), SC_(2.4909977030434827195554109631354701e-21) }}, {{ SC_(21.0), SC_(74.50000), SC_(1.3512434774057027230504407213596735e-21) }}, {{ SC_(21.0), SC_(76.62500), SC_(7.4577901408689598214597184764730976e-22) }}, {{ SC_(21.0), SC_(78.75000), SC_(4.1839783582393157943543388920119532e-22) }}, {{ SC_(21.0), SC_(80.87500), SC_(2.3839167824682948660923670294251560e-22) }}, {{ SC_(21.0), SC_(83.00000), SC_(1.3783660126553262954802012537509343e-22) }}, {{ SC_(21.0), SC_(85.12500), SC_(8.0813880329362090238603817798156305e-23) }}, {{ SC_(21.0), SC_(87.25000), SC_(4.8012632134261346749651412145722164e-23) }}, {{ SC_(21.0), SC_(89.37500), SC_(2.8886517006206031302171279370237901e-23) }}, {{ SC_(21.0), SC_(91.50000), SC_(1.7589221011224124901492492436031577e-23) }}, {{ SC_(21.0), SC_(93.62500), SC_(1.0833513342557474766046276358625049e-23) }}, {{ SC_(21.0), SC_(95.75000), SC_(6.7458879620191471687975317800222219e-24) }}, {{ SC_(21.0), SC_(97.87500), SC_(4.2446964227424748308143188339893397e-24) }}, {{ SC_(21.0), SC_(100.0000), SC_(2.6977147877389616650544376447910561e-24) }},
      {{ SC_(30.0), SC_(0.1250000), SC_(-2.6269370855717061268373196091559060e60) }}, {{ SC_(30.0), SC_(2.250000), SC_(-3.2063201132225624894497621128724710e21) }}, {{ SC_(30.0), SC_(4.375000), SC_(-3.5816122065666942298125086110013811e12) }}, {{ SC_(30.0), SC_(6.500000), SC_(-1.6926495147567915305098150929608351e7) }}, {{ SC_(30.0), SC_(8.625000), SC_(-2691.9893080722988179729741180559768) }}, {{ SC_(30.0), SC_(10.75000), SC_(-3.0129827880548721952335149662772758) }}, {{ SC_(30.0), SC_(12.87500), SC_(-0.011679541591944513522798667560485778) }}, {{ SC_(30.0), SC_(15.00000), SC_(-0.00010699799160744992062995477402482814) }}, {{ SC_(30.0), SC_(17.12500), SC_(-1.8412916811312822615145612742242341e-6) }}, {{ SC_(30.0), SC_(19.25000), SC_(-5.1296809753659048984675389168991678e-8) }}, {{ SC_(30.0), SC_(21.37500), SC_(-2.0897092756900426602247473552496515e-9) }}, {{ SC_(30.0), SC_(23.50000), SC_(-1.1575741931670926101304469613657363e-10) }}, {{ SC_(30.0), SC_(25.62500), SC_(-8.2634766034599236789755939810493909e-12) }}, {{ SC_(30.0), SC_(27.75000), SC_(-7.2982420192371959627923444899651880e-13) }}, {{ SC_(30.0), SC_(29.87500), SC_(-7.7259330615365968247618603604357394e-14) }}, {{ SC_(30.0), SC_(32.00000), SC_(-9.5598830564651701596127591449043084e-15) }}, {{ SC_(30.0), SC_(34.12500), SC_(-1.3549801683291963236022185422404195e-15) }}, {{ SC_(30.0), SC_(36.25000), SC_(-2.1637420158752325246771018243486794e-16) }}, {{ SC_(30.0), SC_(38.37500), SC_(-3.8399012944498044530335003629202925e-17) }}, {{ SC_(30.0), SC_(40.50000), SC_(-7.4867790463777103523228388855693400e-18) }}, {{ SC_(30.0), SC_(42.62500), SC_(-1.5882468053156791511681017429675896e-18) }}, {{ SC_(30.0), SC_(44.75000), SC_(-3.6357619494056634364065401737558109e-19) }}, {{ SC_(30.0), SC_(46.87500), SC_(-8.9173782150303341835362824820507660e-20) }}, {{ SC_(30.0), SC_(49.00000), SC_(-2.3289849068324860513642836089935388e-20) }}, {{ SC_(30.0), SC_(51.12500), SC_(-6.4424396179339080523684393804219419e-21) }}, {{ SC_(30.0), SC_(53.25000), SC_(-1.8786312072016996388558435362347671e-21) }}, {{ SC_(30.0), SC_(55.37500), SC_(-5.7508919690939180263189604207862429e-22) }}, {{ SC_(30.0), SC_(57.50000), SC_(-1.8413295011736722141223685570224223e-22) }}, {{ SC_(30.0), SC_(59.62500), SC_(-6.1461867250080302453576793713083061e-23) }}, {{ SC_(30.0), SC_(61.75000), SC_(-2.1324740914631425269500808021967305e-23) }}, {{ SC_(30.0), SC_(63.87500), SC_(-7.6704605421877601246419439338550908e-24) }}, {{ SC_(30.0), SC_(66.00000), SC_(-2.8535639781622817027659809125071790e-24) }}, {{ SC_(30.0), SC_(68.12500), SC_(-1.0955940788660649214956488504913491e-24) }}, {{ SC_(30.0), SC_(70.25000), SC_(-4.3327278415671627426087404900842932e-25) }}, {{ SC_(30.0), SC_(72.37500), SC_(-1.7617839186667213519985857533371320e-25) }}, {{ SC_(30.0), SC_(74.50000), SC_(-7.3539453800410337613851853725254145e-26) }}, {{ SC_(30.0), SC_(76.62500), SC_(-3.1464535209737263958150922875079198e-26) }}, {{ SC_(30.0), SC_(78.75000), SC_(-1.3780503803711969857070836997294630e-26) }}, {{ SC_(30.0), SC_(80.87500), SC_(-6.1703357256500029511495096803651889e-27) }}, {{ SC_(30.0), SC_(83.00000), SC_(-2.8213173028841107933813502855693772e-27) }}, {{ SC_(30.0), SC_(85.12500), SC_(-1.3159280846978733546278731706334709e-27) }}, {{ SC_(30.0), SC_(87.25000), SC_(-6.2549157954995389707945629132366569e-28) }}, {{ SC_(30.0), SC_(89.37500), SC_(-3.0270730786291364371674711274018267e-28) }}, {{ SC_(30.0), SC_(91.50000), SC_(-1.4902791126813033033236323354658204e-28) }}, {{ SC_(30.0), SC_(93.62500), SC_(-7.4578261147126651898615763642561310e-29) }}, {{ SC_(30.0), SC_(95.75000), SC_(-3.7908508349033587121694154134344755e-29) }}, {{ SC_(30.0), SC_(97.87500), SC_(-1.9558790071355202604313002586790408e-29) }}, {{ SC_(30.0), SC_(100.0000), SC_(-1.0236429687189538253202730650097974e-29) }},
      {{ SC_(1.0), SC_(1.0), SC_(1.6449340668482264364724151666460252) }}, {{ SC_(2.0), SC_(1.0), SC_(-2.4041138063191885707994763230229000) }}, {{ SC_(3.0), SC_(1.0), SC_(6.4939394022668291490960221792470074) }}, {{ SC_(4.0), SC_(1.0), SC_(-24.886266123440878231952771674968820) }}, {{ SC_(5.0), SC_(1.0), SC_(122.08116743813389676574215157491046) }}, {{ SC_(6.0), SC_(1.0), SC_(-726.01147971498443532465423589185367) }}, {{ SC_(7.0), SC_(1.0), SC_(5060.5498752376394704685736020836084) }}, {{ SC_(8.0), SC_(1.0), SC_(-40400.978398747634885327823655450854) }}, {{ SC_(9.0), SC_(1.0), SC_(363240.91142238262680714352556574776) }}, {{ SC_(10.0), SC_(1.0), SC_(-3.6305933116066287129906188428320541e6) }}, {{ SC_(11.0), SC_(1.0), SC_(3.9926622987731086702327073240472015e7) }}, {{ SC_(12.0), SC_(1.0), SC_(-4.7906037988983145242687676449906363e8) }}, {{ SC_(13.0), SC_(1.0), SC_(6.2274021934109717641928534089474159e9) }}, {{ SC_(14.0), SC_(1.0), SC_(-8.7180957830172067845191220310364358e10) }}, {{ SC_(15.0), SC_(1.0), SC_(1.3076943522189138208900999074851102e12) }}, {{ SC_(16.0), SC_(1.0), SC_(-2.0922949679481510906631655688111514e13) }}, {{ SC_(17.0), SC_(1.0), SC_(3.5568878585922371597561239671618245e14) }}, {{ SC_(18.0), SC_(1.0), SC_(-6.4023859228189214007356494533239755e15) }}, {{ SC_(19.0), SC_(1.0), SC_(1.2164521645363939666987669627404138e17) }}, {{ SC_(20.0), SC_(1.0), SC_(-2.4329031685078613217372568182431975e18) }}, {{ SC_(21.0), SC_(1.0), SC_(5.1090954354370285677650274860473481e19) }}, {{ SC_(22.0), SC_(1.0), SC_(-1.1240008617808912306021529490019443e21) }}, {{ SC_(23.0), SC_(1.0), SC_(2.5852018279876877767780261785042411e22) }}, {{ SC_(24.0), SC_(1.0), SC_(-6.2044842022477556107699152049463576e23) }}, {{ SC_(25.0), SC_(1.0), SC_(1.5511210274472132898983174657140502e25) }}, {{ SC_(26.0), SC_(1.0), SC_(-4.0329146413141407973995741108882374e26) }}, {{ SC_(27.0), SC_(1.0), SC_(1.0888869490983028015891074275710607e28) }}, {{ SC_(28.0), SC_(1.0), SC_(-3.0488834517961710017831014422416477e29) }}, {{ SC_(29.0), SC_(1.0), SC_(8.8417620019742774502390188870678590e30) }}, {{ SC_(30.0), SC_(1.0), SC_(-2.6525285993570947629478654469554513e32) }}, {{ SC_(31.0), SC_(1.0), SC_(8.2228386560924560722190676893264356e33) }}, {{ SC_(32.0), SC_(1.0), SC_(-2.6313083696432603856870073439642460e35) }}, {{ SC_(33.0), SC_(1.0), SC_(8.6833176193173226237779341368101141e36) }}, {{ SC_(34.0), SC_(1.0), SC_(-2.9523279904819655207731742551529264e38) }}, {{ SC_(35.0), SC_(1.0), SC_(1.0333147966536512091762081841406431e40) }}, {{ SC_(36.0), SC_(1.0), SC_(-3.7199332679260782597263218144656884e41) }}, {{ SC_(37.0), SC_(1.0), SC_(1.3763753091276417298557030711497089e43) }}, {{ SC_(38.0), SC_(1.0), SC_(-5.2302261746755248448805548317868492e44) }}, {{ SC_(39.0), SC_(1.0), SC_(2.0397882081215995125998316157358476e46) }}, {{ SC_(40.0), SC_(1.0), SC_(-8.1591528324826876968158659241126765e47) }}, {{ SC_(41.0), SC_(1.0), SC_(3.3452526613171413332404690085486207e49) }}, {{ SC_(42.0), SC_(1.0), SC_(-1.4050061177530396292499296757724836e51) }}, {{ SC_(43.0), SC_(1.0), SC_(6.0415263063377269847520379087368697e52) }}, {{ SC_(44.0), SC_(1.0), SC_(-2.6582715747885243206668113464973395e54) }}, {{ SC_(45.0), SC_(1.0), SC_(1.1962222086548189449597808756809115e56) }}, {{ SC_(46.0), SC_(1.0), SC_(-5.5026221598121280483325452519279417e57) }}, {{ SC_(47.0), SC_(1.0), SC_(2.5862324151116909945729536986576770e59) }}, {{ SC_(48.0), SC_(1.0), SC_(-1.2413915592536094722406207462376651e61) }}, {{ SC_(49.0), SC_(1.0), SC_(6.0828186403426810113507774083359074e62) }}, {{ SC_(50.0), SC_(1.0), SC_(-3.0414093201713391550183240542952737e64) }},
      {{ SC_(1.0), SC_(0.5), SC_(4.9348022005446793094172454999380756) }}, {{ SC_(2.0), SC_(0.5), SC_(-16.828796644234319995596334261160300) }}, {{ SC_(3.0), SC_(0.5), SC_(97.409091034002437236440332688705111) }}, {{ SC_(4.0), SC_(0.5), SC_(-771.47424982666722519053592192403342) }}, {{ SC_(5.0), SC_(0.5), SC_(7691.1135486024354962417555492193592) }}, {{ SC_(6.0), SC_(0.5), SC_(-92203.457923803023286231087958265416) }}, {{ SC_(7.0), SC_(0.5), SC_(1.2904402181855980649694862685313201e6) }}, {{ SC_(8.0), SC_(0.5), SC_(-2.0644899961760041426402517887935387e7) }}, {{ SC_(9.0), SC_(0.5), SC_(3.7159545238509742722370782665375996e8) }}, {{ SC_(10.0), SC_(0.5), SC_(-7.4318245088587689754917967712772148e9) }}, {{ SC_(11.0), SC_(0.5), SC_(1.6349952113475880004602936491973290e11) }}, {{ SC_(12.0), SC_(0.5), SC_(-3.9239835716776094268285475780118302e12) }}, {{ SC_(13.0), SC_(0.5), SC_(1.0202353013465195041277151739878551e14) }}, {{ SC_(14.0), SC_(0.5), SC_(-2.8566584452212481470833807159097089e15) }}, {{ SC_(15.0), SC_(0.5), SC_(8.5699749372666517252032697437036695e16) }}, {{ SC_(16.0), SC_(0.5), SC_(-2.7423919374393211160431177426964643e18) }}, {{ SC_(17.0), SC_(0.5), SC_(9.3241325391494482576994960512370215e19) }}, {{ SC_(18.0), SC_(0.5), SC_(-3.3566877083169638444274914449348672e21) }}, {{ SC_(19.0), SC_(0.5), SC_(1.2755413284287493036311595679555294e23) }}, {{ SC_(20.0), SC_(0.5), SC_(-5.1021653127394298787426098736355399e24) }}, {{ SC_(21.0), SC_(0.5), SC_(2.1429094312139835232862558079810850e26) }}, {{ SC_(22.0), SC_(0.5), SC_(-9.4288014971412166432678344430683531e27) }}, {{ SC_(23.0), SC_(0.5), SC_(4.3372486886542455183876952472394031e29) }}, {{ SC_(24.0), SC_(0.5), SC_(-2.0818793705491236054644197662022342e31) }}, {{ SC_(25.0), SC_(0.5), SC_(1.0409396852737427640356547073711139e33) }}, {{ SC_(26.0), SC_(0.5), SC_(-5.4128863634220427078493834793146517e34) }}, {{ SC_(27.0), SC_(0.5), SC_(2.9229586362476475227234677536391722e36) }}, {{ SC_(28.0), SC_(0.5), SC_(-1.6368568362986349120390762957016873e38) }}, {{ SC_(29.0), SC_(0.5), SC_(9.4937696505319902685274359255316741e39) }}, {{ SC_(30.0), SC_(0.5), SC_(-5.6962617903191757168598825608931775e41) }}, {{ SC_(31.0), SC_(0.5), SC_(3.5316823099978851326405053801048261e43) }}, {{ SC_(32.0), SC_(0.5), SC_(-2.2602766783986456717032825683189511e45) }}, {{ SC_(33.0), SC_(0.5), SC_(1.4917826077431059644231123371238779e47) }}, {{ SC_(34.0), SC_(0.5), SC_(-1.0144121732653120152568117095915465e49) }}, {{ SC_(35.0), SC_(0.5), SC_(7.1008852128571840121789056825569522e50) }}, {{ SC_(36.0), SC_(0.5), SC_(-5.1126373532571724660603059705232204e52) }}, {{ SC_(37.0), SC_(0.5), SC_(3.7833516414103076192831949360932899e54) }}, {{ SC_(38.0), SC_(0.5), SC_(-2.8753472474718337892361988468491097e56) }}, {{ SC_(39.0), SC_(0.5), SC_(2.2427708530280303552352874820086301e58) }}, {{ SC_(40.0), SC_(0.5), SC_(-1.7942166824224242840898439541031383e60) }}, {{ SC_(41.0), SC_(0.5), SC_(1.4712576795863879129267798604374659e62) }}, {{ SC_(42.0), SC_(0.5), SC_(-1.2358564508525658468509652718307801e64) }}, {{ SC_(43.0), SC_(0.5), SC_(1.0628365477332066282896715879731180e66) }}, {{ SC_(44.0), SC_(0.5), SC_(-9.3529616200522183289427782398136808e67) }}, {{ SC_(45.0), SC_(0.5), SC_(8.4176654580469964960466008955275434e69) }}, {{ SC_(46.0), SC_(0.5), SC_(-7.7442522214032367763622903043252397e71) }}, {{ SC_(47.0), SC_(0.5), SC_(7.2795970881190425697803703632702328e73) }}, {{ SC_(48.0), SC_(0.5), SC_(-6.9884132045942808669890971414448669e75) }}, {{ SC_(49.0), SC_(0.5), SC_(6.8486449405023952496492961188997479e77) }}, {{ SC_(50.0), SC_(0.5), SC_(-6.8486449405023952496492897589943408e79) }},
   }};
   std::array<std::array<value_type, 3>, 284> big_data =
   { {
      {{ 1, SC_(2.0000000000000000000000000000000000), SC_(0.64493406684822643647241516664602519) }}, {{ 1, SC_(4.0000000000000000000000000000000000), SC_(0.28382295573711532536130405553491408) }}, {{ 1, SC_(8.0000000000000000000000000000000000), SC_(0.13313701469403142513454668592040161) }}, {{ 1, SC_(16.000000000000000000000000000000000), SC_(0.064493783403239361781710772311927225) }}, {{ 1, SC_(32.000000000000000000000000000000000), SC_(0.031743366520302090126581680438741427) }}, {{ 1, SC_(64.000000000000000000000000000000000), SC_(0.015747706064338930155744003071350465) }}, {{ 1, SC_(128.00000000000000000000000000000000), SC_(0.0078430970500146151295391657680446584) }}, {{ 1, SC_(256.00000000000000000000000000000000), SC_(0.0039138893286083964054615299292933721) }}, {{ 1, SC_(512.00000000000000000000000000000000), SC_(0.0019550335903952979329050939908745913) }}, {{ 1, SC_(1024.0000000000000000000000000000000), SC_(0.00097703949237860262165259669085763056) }},
      {{ 1, SC_(2048.0000000000000000000000000000000), SC_(0.00048840047869210349388677277938304048) }}, {{ 1, SC_(4096.0000000000000000000000000000000), SC_(0.00024417042974770687112825193241674713) }}, {{ 1, SC_(8192.0000000000000000000000000000000), SC_(0.00012207776338376182351559927851701587) }}, {{ 1, SC_(16384.000000000000000000000000000000), SC_(0.000061037018933044843502668828413112893) }}, {{ 1, SC_(32768.000000000000000000000000000000), SC_(0.000030518043791024259310109487753004549) }},
      {{ 2, SC_(2.0000000000000000000000000000000000), SC_(-0.40411380631918857079947632302289998) }}, {{ 2, SC_(4.0000000000000000000000000000000000), SC_(-0.080039732245114496725402248948825907) }}, {{ 2, SC_(8.0000000000000000000000000000000000), SC_(-0.017699569195767773909291677736213879) }}, {{ 2, SC_(16.000000000000000000000000000000000), SC_(-0.0041580101239589621541865297842265262) }}, {{ 2, SC_(32.000000000000000000000000000000000), SC_(-0.0010075567602140907392185110593117265) }}, {{ 2, SC_(64.000000000000000000000000000000000), SC_(-0.00024798512216328534949893202341675581) }}, {{ 2, SC_(128.00000000000000000000000000000000), SC_(-0.000061513856015459056093727063597403146) }}, {{ 2, SC_(256.00000000000000000000000000000000), SC_(-0.000015318510122005107648117663349723503) }}, {{ 2, SC_(512.00000000000000000000000000000000), SC_(-3.8221551221702861883051574825692679e-6) }}, {{ 2, SC_(1024.0000000000000000000000000000000), SC_(-9.5460609372807180482794242236285690e-7) }}, {{ 2, SC_(2048.0000000000000000000000000000000), SC_(-2.3853502284509660646447307719091929e-7) }}, {{ 2, SC_(4096.0000000000000000000000000000000), SC_(-5.9619198466975795959019740002484769e-8) }}, {{ 2, SC_(8192.0000000000000000000000000000000), SC_(-1.4902980294273504017537750635770593e-8) }}, {{ 2, SC_(16384.000000000000000000000000000000), SC_(-3.7255176790762511898502424554181168e-9) }}, {{ 2, SC_(32768.000000000000000000000000000000), SC_(-9.3135099675858978849200435754135096e-10) }}, {{ 2, SC_(65536.000000000000000000000000000000), SC_(-2.3283419639465348371721333739142464e-10) }}, {{ 2, SC_(131072.00000000000000000000000000000), SC_(-5.8208105004371323183654400926421586e-11) }}, {{ 2, SC_(262144.00000000000000000000000000000), SC_(-1.4551970739623962182873920142648449e-11) }}, {{ 2, SC_(524288.00000000000000000000000000000), SC_(-3.6379857459922343037889500943887953e-12) }}, {{ 2, SC_(1.0485760000000000000000000000000000e6), SC_(-9.0949556913507981662486265691361231e-13) }}, {{ 2, SC_(2.0971520000000000000000000000000000e6), SC_(-2.2737378386347515742334544652596257e-13) }}, {{ 2, SC_(4.1943040000000000000000000000000000e6), SC_(-5.6843432413336786525629259100975662e-14) }}, {{ 2, SC_(8.3886080000000000000000000000000000e6), SC_(-1.4210856409267999200219031777240284e-14) }}, {{ 2, SC_(1.6777216000000000000000000000000000e7), SC_(-3.5527138905587440538179478730582199e-15) }}, {{ 2, SC_(3.3554432000000000000000000000000000e7), SC_(-8.8817844616990522846624354086352033e-16) }}, {{ 2, SC_(6.7108864000000000000000000000000000e7), SC_(-2.2204460823375378294874032126041593e-16) }}, {{ 2, SC_(1.3421772800000000000000000000000000e8), SC_(-5.5511151644848134838439376350034016e-17) }}, {{ 2, SC_(2.6843545600000000000000000000000000e8), SC_(-1.3877787859513245136156122749960089e-17) }}, {{ 2, SC_(5.3687091200000000000000000000000000e8), SC_(-3.4694469584159627304129087489268059e-18) }}, {{ 2, SC_(1.0737418240000000000000000000000000e9), SC_(-8.6736173879619711452843652170069542e-19) }}, {{ 2, SC_(2.1474836480000000000000000000000000e9), SC_(-2.1684043459807508269328995828313535e-19) }}, {{ 2, SC_(4.2949672960000000000000000000000000e9), SC_(-5.4210108636896996185378196868612602e-20) }}, {{ 2, SC_(8.5899345920000000000000000000000000e9), SC_(-1.3552527157646527235627019117855720e-20) }}, {{ 2, SC_(1.7179869184000000000000000000000000e10), SC_(-3.3881317892144165825842826725813744e-21) }}, {{ 2, SC_(3.4359738368000000000000000000000000e10), SC_(-8.4703294727895224235683785200562563e-22) }}, {{ 2, SC_(6.8719476736000000000000000000000000e10), SC_(-2.1175823681665657267812262331022925e-22) }}, {{ 2, SC_(1.3743895347200000000000000000000000e11), SC_(-5.2939559203778957180649004761553143e-23) }}, {{ 2, SC_(2.7487790694400000000000000000000000e11), SC_(-1.3234889800896591046552307550599825e-23) }}, {{ 2, SC_(5.4975581388800000000000000000000000e11), SC_(-3.3087224502181292305618503541427776e-24) }}, {{ 2, SC_(1.0995116277760000000000000000000000e12), SC_(-8.2718061255377999125593529818894574e-25) }}, {{ 2, SC_(2.1990232555520000000000000000000000e12), SC_(-2.0679515313835095826591797740024589e-25) }}, {{ 2, SC_(4.3980465111040000000000000000000000e12), SC_(-5.1698788284575984622971267465834721e-26) }}, {{ 2, SC_(8.7960930222080000000000000000000000e12), SC_(-1.2924697071142526787804288756502028e-26) }}, {{ 2, SC_(1.7592186044416000000000000000000000e13), SC_(-3.2311742677854480259587561910416561e-27) }}, {{ 2, SC_(3.5184372088832000000000000000000000e13), SC_(-8.0779356694633904761564954897872834e-28) }}, {{ 2, SC_(7.0368744177664000000000000000000000e13), SC_(-2.0194839173658189204465744995814610e-28) }}, {{ 2, SC_(1.4073748835532800000000000000000000e14), SC_(-5.0487097934145114278757495332542949e-29) }}, {{ 2, SC_(2.8147497671065600000000000000000000e14), SC_(-1.2621774483536233728138515438750504e-29) }}, {{ 2, SC_(5.6294995342131200000000000000000000e14), SC_(-3.1554436208840528268407715604044070e-30) }}, {{ 2, SC_(1.1258999068426240000000000000000000e15), SC_(-7.8886090522101250606096072769163284e-31) }}, {{ 2, SC_(2.2517998136852480000000000000000000e15), SC_(-1.9721522630525303893408616162178294e-31) }}, {{ 2, SC_(4.5035996273704960000000000000000000e15), SC_(-4.9303806576313248785877287867808721e-32) }}, {{ 2, SC_(9.0071992547409920000000000000000000e15), SC_(-1.2325951644078310828013790399747782e-32) }}, {{ 2, SC_(1.8014398509481984000000000000000000e16), SC_(-3.0814879110195775359465061540364098e-33) }}, {{ 2, SC_(3.6028797018963968000000000000000000e16), SC_(-7.7037197775489436260450885777153639e-34) }}, {{ 2, SC_(7.2057594037927936000000000000000000e16), SC_(-1.9259299443872358797836250435068840e-34) }}, {{ 2, SC_(1.4411518807585587200000000000000000e17), SC_(-4.8148248609680896660495037326147640e-35) }}, {{ 2, SC_(2.8823037615171174400000000000000000e17), SC_(-1.2037062152420224123361810736346353e-35) }}, {{ 2, SC_(5.7646075230342348800000000000000000e17), SC_(-3.0092655381050560256202091096877686e-36) }}, {{ 2, SC_(1.1529215046068469760000000000000000e18), SC_(-7.5231638452626400575252183062208969e-37) }}, {{ 2, SC_(2.3058430092136939520000000000000000e18), SC_(-1.8807909613156600135656415180554087e-37) }}, {{ 2, SC_(4.6116860184273879040000000000000000e18), SC_(-4.7019774032891500328945249720137522e-38) }}, {{ 2, SC_(9.2233720368547758080000000000000000e18), SC_(-1.1754943508222875080961838901128419e-38) }}, {{ 2, SC_(1.8446744073709551616000000000000000e19), SC_(-2.9387358770557187700811505341688594e-39) }}, {{ 2, SC_(3.6893488147419103232000000000000000e19), SC_(-7.3468396926392969250037398465305920e-40) }}, {{ 2, SC_(7.3786976294838206464000000000000000e19), SC_(-1.8367099231598242312260429005212034e-40) }}, {{ 2, SC_(1.4757395258967641292800000000000000e20), SC_(-4.5917748078995605780339921749137029e-41) }}, {{ 2, SC_(2.9514790517935282585600000000000000e20), SC_(-1.1479437019748901445046086591797625e-41) }}, {{ 2, SC_(5.9029581035870565171200000000000000e20), SC_(-2.8698592549372253612566599172635773e-42) }}, {{ 2, SC_(1.1805916207174113034240000000000000e21), SC_(-7.1746481373430634031355726298016569e-43) }}, {{ 2, SC_(2.3611832414348226068480000000000000e21), SC_(-1.7936620343357658507831335120307534e-43) }}, {{ 2, SC_(4.7223664828696452136960000000000000e21), SC_(-4.4841550858394146269568842233023076e-44) }}, {{ 2, SC_(9.4447329657392904273920000000000000e21), SC_(-1.1210387714598536567391023612287549e-44) }}, {{ 2, SC_(1.8889465931478580854784000000000000e22), SC_(-2.8025969286496341418476075348258598e-45) }}, {{ 2, SC_(3.7778931862957161709568000000000000e22), SC_(-7.0064923216240853546188333767571150e-46) }}, {{ 2, SC_(7.5557863725914323419136000000000000e22), SC_(-1.7516230804060213386546851616508370e-46) }}, {{ 2, SC_(1.5111572745182864683827200000000000e23), SC_(-4.3790577010150533466366839259540402e-47) }}, {{ 2, SC_(3.0223145490365729367654400000000000e23), SC_(-1.0947644252537633366591673592168785e-47) }}, {{ 2, SC_(6.0446290980731458735308800000000000e23), SC_(-2.7369110631344083416479138702026569e-48) }}, {{ 2, SC_(1.2089258196146291747061760000000000e24), SC_(-6.8422776578360208541197790157072179e-49) }}, {{ 2, SC_(2.4178516392292583494123520000000000e24), SC_(-1.7105694144590052135299440464518764e-49) }}, {{ 2, SC_(4.8357032784585166988247040000000000e24), SC_(-4.2764235361475130338248592317860310e-50) }}, {{ 2, SC_(9.6714065569170333976494080000000000e24), SC_(-1.0691058840368782584562146974035503e-50) }}, {{ 2, SC_(1.9342813113834066795298816000000000e25), SC_(-2.6727647100921956461405366053301788e-51) }}, {{ 2, SC_(3.8685626227668133590597632000000000e25), SC_(-6.6819117752304891153513413406020758e-52) }}, {{ 2, SC_(7.7371252455336267181195264000000000e25), SC_(-1.6704779438076222788378353135600976e-52) }}, {{ 2, SC_(1.5474250491067253436239052800000000e26), SC_(-4.1761948595190556970945882569122172e-53) }}, {{ 2, SC_(3.0948500982134506872478105600000000e26), SC_(-1.0440487148797639242736470608545510e-53) }}, {{ 2, SC_(6.1897001964269013744956211200000000e26), SC_(-2.6101217871994098106841176479194982e-54) }}, {{ 2, SC_(1.2379400392853802748991242240000000e27), SC_(-6.5253044679985245267102941145276465e-55) }}, {{ 2, SC_(2.4758800785707605497982484480000000e27), SC_(-1.6313261169996311316775735279730243e-55) }}, {{ 2, SC_(4.9517601571415210995964968960000000e27), SC_(-4.0783152924990778291939338191089514e-56) }}, {{ 2, SC_(9.9035203142830421991929937920000000e27), SC_(-1.0195788231247694572984834546742867e-56) }}, {{ 2, SC_(1.9807040628566084398385987584000000e28), SC_(-2.5489470578119236432462086365570278e-57) }}, {{ 2, SC_(3.9614081257132168796771975168000000e28), SC_(-6.3723676445298091081155215912317084e-58) }}, {{ 2, SC_(7.9228162514264337593543950336000000e28), SC_(-1.5930919111324522770288803977878195e-58) }}, {{ 2, SC_(1.5845632502852867518708790067200000e29), SC_(-3.9827297778311306925722009944444141e-59) }}, {{ 2, SC_(3.1691265005705735037417580134400000e29), SC_(-9.9568244445778267314305024860796170e-60) }}, {{ 2, SC_(6.3382530011411470074835160268800000e29), SC_(-2.4892061111444566828576256215159770e-60) }}, {{ 2, SC_(1.2676506002282294014967032053760000e30), SC_(-6.2230152778611417071440640537850333e-61) }},
      {{ 4, SC_(2.0000000000000000000000000000000000), SC_(-0.88626612344087823195277167496882003)}}, {{ 4, SC_(4.0000000000000000000000000000000000), SC_(-0.037500691342112799854006242870054601) }}, {{ 4, SC_(8.0000000000000000000000000000000000), SC_(-0.0018687951506376135155684814141062787) }}, {{ 4, SC_(16.000000000000000000000000000000000), SC_(-0.00010359125360358782747907937474060894) }}, {{ 4, SC_(32.000000000000000000000000000000000), SC_(-6.0889806370027137702207132674152980e-6) }}, {{ 4, SC_(64.000000000000000000000000000000000), SC_(-3.6894923384141876864824136311646410e-7) }}, {{ 4, SC_(128.00000000000000000000000000000000), SC_(-2.2703261395872369173970078168917455e-8) }}, {{ 4, SC_(256.00000000000000000000000000000000), SC_(-1.4079333251018200826827045864949986e-9) }}, {{ 4, SC_(512.00000000000000000000000000000000), SC_(-8.7653106993395973543058191653743071e-11) }}, {{ 4, SC_(1024.0000000000000000000000000000000), SC_(-5.4676350252855605594922081412167012e-12) }}, {{ 4, SC_(2048.0000000000000000000000000000000), SC_(-3.4139371559748457865769094969096427e-13) }}, {{ 4, SC_(4096.0000000000000000000000000000000), SC_(-2.1326692531241146202038690286850691e-14) }}, {{ 4, SC_(8192.0000000000000000000000000000000), SC_(-1.3325929232891576568330300144201981e-15) }}, {{ 4, SC_(16384.000000000000000000000000000000), SC_(-8.3276891759241673633332539309962691e-17) }}, {{ 4, SC_(32768.000000000000000000000000000000), SC_(-5.2044880733635773100598702692535183e-18) }}, {{ 4, SC_(65536.000000000000000000000000000000), SC_(-3.2527057803921971134912503489998463e-19) }}, {{ 4, SC_(131072.00000000000000000000000000000), SC_(-2.0329100928805067785989990248970031e-20) }}, {{ 4, SC_(262144.00000000000000000000000000000), SC_(-1.2705591144350687435054305331329679e-21) }}, {{ 4, SC_(524288.00000000000000000000000000000), SC_(-7.9409641728159744142284095369593220e-23) }}, {{ 4, SC_(1.0485760000000000000000000000000000e6), SC_(-4.9630931416565518652153749436192000e-24) }}, {{ 4, SC_(2.0971520000000000000000000000000000e6), SC_(-3.1019302553034238539128749896589195e-25) }}, {{ 4, SC_(4.1943040000000000000000000000000000e6), SC_(-1.9387054851177155898453895640231670e-26) }}, {{ 4, SC_(8.3886080000000000000000000000000000e6), SC_(-1.2116906393089944897903424066391815e-27) }}, {{ 4, SC_(1.6777216000000000000000000000000000e7), SC_(-7.5730655929014196050202587407391615e-29) }}, {{ 4, SC_(3.3554432000000000000000000000000000e7), SC_(-4.7331657134447220363116949282744848e-30) }}, {{ 4, SC_(6.7108864000000000000000000000000000e7), SC_(-2.9582284827408716767299704712288552e-31) }}, {{ 4, SC_(1.3421772800000000000000000000000000e8), SC_(-1.8488927741623954373880437387993075e-32) }}, {{ 4, SC_(2.6843545600000000000000000000000000e8), SC_(-1.1555579752419193033729099502283072e-33) }}, {{ 4, SC_(5.3687091200000000000000000000000000e8), SC_(-7.2222373183570650057583536416196423e-35) }}, {{ 4, SC_(1.0737418240000000000000000000000000e9), SC_(-4.5138983155653748230741551964743254e-36) }}, {{ 4, SC_(2.1474836480000000000000000000000000e9), SC_(-2.8211864446009246407535785204401521e-37) }}, {{ 4, SC_(4.2949672960000000000000000000000000e9), SC_(-1.7632415270545045810527364994409865e-38) }}, {{ 4, SC_(8.5899345920000000000000000000000000e9), SC_(-1.1020259541524799509144333467867914e-39) }}, {{ 4, SC_(1.7179869184000000000000000000000000e10), SC_(-6.8876622126511702800708681871153888e-41) }}, {{ 4, SC_(3.4359738368000000000000000000000000e10), SC_(-4.3047888826564097334549177850495839e-42) }}, {{ 4, SC_(6.8719476736000000000000000000000000e10), SC_(-2.6904930515819524297904926510117385e-43) }}, {{ 4, SC_(1.3743895347200000000000000000000000e11), SC_(-1.6815581572142503768636183351690905e-44) }}, {{ 4, SC_(2.7487790694400000000000000000000000e11), SC_(-1.0509738482512596443662561409356175e-45) }}, {{ 4, SC_(5.4975581388800000000000000000000000e11), SC_(-6.5685865515464763986220054285432304e-47) }}, {{ 4, SC_(1.0995116277760000000000000000000000e12), SC_(-4.1053665947090801308053030433926733e-48) }}, {{ 4, SC_(2.1990232555520000000000000000000000e12), SC_(-2.5658541216908414510241138209492617e-49) }}, {{ 4, SC_(4.3980465111040000000000000000000000e12), SC_(-1.6036588260560466472871963710133920e-50) }}, {{ 4, SC_(8.7960930222080000000000000000000000e12), SC_(-1.0022867662848012609285994319421665e-51) }}, {{ 4, SC_(1.7592186044416000000000000000000000e13), SC_(-6.2642922892792957132228143635273796e-53) }}, {{ 4, SC_(3.5184372088832000000000000000000000e13), SC_(-3.9151826807993372683952177161081714e-54) }}, {{ 4, SC_(7.0368744177664000000000000000000000e13), SC_(-2.4469891754995162451316856809457970e-55) }}, {{ 4, SC_(1.4073748835532800000000000000000000e14), SC_(-1.5293682346871759195775143660953743e-56) }}, {{ 4, SC_(2.8147497671065600000000000000000000e14), SC_(-9.5585514667947815797663735871501036e-58) }}, {{ 4, SC_(5.6294995342131200000000000000000000e14), SC_(-5.9740946667467172631061424917674488e-59) }}, {{ 4, SC_(1.1258999068426240000000000000000000e15), SC_(-3.7338091667166916568638887448064559e-60) }},
      {{ 5, SC_(2.0000000000000000000000000000000000), SC_(2.0811674381338967657421515749104633) }}, {{ 5, SC_(4.0000000000000000000000000000000000), SC_(0.041558384635954378910875854745854295) }}, {{ 5, SC_(8.0000000000000000000000000000000000), SC_(0.00098951000477133869852907040195234770) }}, {{ 5, SC_(16.000000000000000000000000000000000), SC_(0.000026687171525751195904263272526023849) }}, {{ 5, SC_(32.000000000000000000000000000000000), SC_(7.7287973331327549424848375559872037e-7) }}, {{ 5, SC_(64.000000000000000000000000000000000), SC_(2.3238496018000614929174818530730601e-8) }}, {{ 5, SC_(128.00000000000000000000000000000000), SC_(7.1224092682782859288630591196158856e-10) }}, {{ 5, SC_(256.00000000000000000000000000000000), SC_(2.2041868318688703092956583673488732e-11) }}, {{ 5, SC_(512.00000000000000000000000000000000), SC_(6.8545820059344569325690341822051481e-13) }}, 
      {{ 5, SC_(1024.0000000000000000000000000000000), SC_(2.1368374599013908699681782557176684e-14) }}, {{ 5, SC_(2048.0000000000000000000000000000000), SC_(6.6694736345106372565672272296621720e-16) }}, {{ 5, SC_(4096.0000000000000000000000000000000), SC_(2.0829390307857624148581922800405889e-17) }}, {{ 5, SC_(8192.0000000000000000000000000000000), SC_(6.5071985107212205839819397506176587e-19) }}, {{ 5, SC_(16384.000000000000000000000000000000), SC_(2.0331892850726898586186509892109697e-20) }},
      {{ SC_(30.0),  SC_(2.0000000000000000000000000000000000), SC_(-1.2351841765847806469554512503841320e23) }}, {{ SC_(30.0),  SC_(4.0000000000000000000000000000000000), SC_(-5.7574709672867347088590035301645472e13) }}, {{ SC_(30.0),  SC_(8.0000000000000000000000000000000000), SC_(-27506.955293920803735428099734719508) }}, {{ SC_(30.0),  SC_(16.000000000000000000000000000000000), SC_(-0.000014776733178819597558222752004691771) }}, {{ SC_(30.0),  SC_(32.000000000000000000000000000000000), SC_(-9.5598830564651701596127591449043084e-15) }}, {{ SC_(30.0),  SC_(64.000000000000000000000000000000000), SC_(-7.2304485310918134795078579958346544e-24) }}, {{ SC_(30.0),  SC_(128.00000000000000000000000000000000), SC_(-6.0283544055510492723170660715813893e-33) }}, {{ SC_(30.0),  SC_(256.00000000000000000000000000000000), SC_(-5.3033945300844267682288152122688373e-42) }}, {{ SC_(30.0),  SC_(512.00000000000000000000000000000000), SC_(-4.7984984493788567623415259200478215e-51) }}, {{ SC_(30.0),  SC_(1024.0000000000000000000000000000000), SC_(-4.4044059984515492135344714328158802e-60) }}, {{ SC_(30.0),  SC_(2048.0000000000000000000000000000000), SC_(-4.0720911710023652995468796384464697e-69) }}, {{ SC_(30.0),  SC_(4096.0000000000000000000000000000000), SC_(-3.7785912001586472052142107275635729e-78) }}, {{ SC_(30.0),  SC_(8192.0000000000000000000000000000000), SC_(-3.5126550370582447330888831842922732e-87) }}, {{ SC_(30.0),  SC_(16384.000000000000000000000000000000), SC_(-3.2684225122971143182856602549511625e-96) }}, {{ SC_(30.0),  SC_(32768.000000000000000000000000000000), SC_(-3.0425628731892016567765116031607680e-105) }}, {{ SC_(30.0),  SC_(65536.000000000000000000000000000000), SC_(-2.8329590706806369266402498060974102e-114) }}, {{ SC_(30.0),  SC_(131072.00000000000000000000000000000), SC_(-2.6380968281136772828042603723998837e-123) }}, {{ SC_(30.0),  SC_(262144.00000000000000000000000000000), SC_(-2.4567785517794442581328674255623958e-132) }}, {{ SC_(30.0),  SC_(524288.00000000000000000000000000000), SC_(-2.2879878661829847442965875847125058e-141) }}, {{ SC_(30.0),  SC_(1.0485760000000000000000000000000000e6), SC_(-2.1308242685223148657953902073871316e-150) }}, {{ SC_(30.0),  SC_(2.0971520000000000000000000000000000e6), SC_(-1.9844705497704998113296263703338786e-159) }}, {{ SC_(30.0),  SC_(4.1943040000000000000000000000000000e6), SC_(-1.8481756120690176942737056133829355e-168) }}, {{ SC_(30.0),  SC_(8.3886080000000000000000000000000000e6), SC_(-1.7212445915482984340950265207437808e-177) }}, {{ SC_(30.0),  SC_(1.6777216000000000000000000000000000e7), SC_(-1.6030325113209352859611074788378703e-186) }}, {{ SC_(30.0),  SC_(3.3554432000000000000000000000000000e7), SC_(-1.4929396982396668313427669448409354e-195) }}, {{ SC_(30.0),  SC_(6.7108864000000000000000000000000000e7), SC_(-1.3904081327297841105213325845203069e-204) }}, {{ SC_(30.0),  SC_(1.3421772800000000000000000000000000e8), SC_(-1.2949183372218004102189189609880637e-213) }}, {{ SC_(30.0),  SC_(2.6843545600000000000000000000000000e8), SC_(-1.2059866123484480516357481544084177e-222) }}, {{ SC_(30.0),  SC_(5.3687091200000000000000000000000000e8), SC_(-1.1231625253833571996113692012033249e-231) }}, {{ SC_(30.0),  SC_(1.0737418240000000000000000000000000e9), SC_(-1.0460266002388281642669220414065145e-240) }}, {{ SC_(30.0),  SC_(2.1474836480000000000000000000000000e9), SC_(-9.7418817964607553111407834656180990e-250) }}, {{ SC_(30.0),  SC_(4.2949672960000000000000000000000000e9), SC_(-9.0728344045929855573515168095233657e-259) }}, {{ SC_(30.0),  SC_(8.5899345920000000000000000000000000e9), SC_(-8.4497354819902512502834172688634013e-268) }}, {{ SC_(30.0),  SC_(1.7179869184000000000000000000000000e10), SC_(-7.8694293970360059201227925630244920e-277) }}, {{ SC_(30.0),  SC_(3.4359738368000000000000000000000000e10), SC_(-7.3289772436027860614787732271106255e-286) }}, {{ SC_(30.0),  SC_(6.8719476736000000000000000000000000e10), SC_(-6.8256419543205073368020956733620994e-295) }}, {{ SC_(30.0),  SC_(1.3743895347200000000000000000000000e11), SC_(-6.3568744376074160576260336726346352e-304) }}, {{ SC_(30.0),  SC_(2.7487790694400000000000000000000000e11), SC_(-5.9203006674167918417546432760193032e-313) }}, {{ SC_(30.0),  SC_(5.4975581388800000000000000000000000e11), SC_(-5.5137096599259016840358774274418939e-322) }}, {{ SC_(30.0),  SC_(1.0995116277760000000000000000000000e12), SC_(-5.1350422760943708365732963065500637e-331) }}, {{ SC_(30.0),  SC_(2.1990232555520000000000000000000000e12), SC_(-4.7823807932989146872399082006693143e-340) }}, {{ SC_(30.0),  SC_(4.3980465111040000000000000000000000e12), SC_(-4.4539391931915691818714510320431840e-349) }}, {{ SC_(30.0),  SC_(8.7960930222080000000000000000000000e12), SC_(-4.1480541165768856815409742503596800e-358) }}, {{ SC_(30.0),  SC_(1.7592186044416000000000000000000000e13), SC_(-3.8631764394914254898921483964257641e-367) }}, {{ SC_(30.0),  SC_(3.5184372088832000000000000000000000e13), SC_(-3.5978634278194778773174853574513736e-376) }}, {{ SC_(30.0),  SC_(7.0368744177664000000000000000000000e13), SC_(-3.3507714307109927261253979955016489e-385) }}, {{ SC_(30.0),  SC_(1.4073748835532800000000000000000000e14), SC_(-3.1206490757974196189832923991915393e-394) }}, {{ SC_(30.0),  SC_(2.8147497671065600000000000000000000e14), SC_(-2.9063309317429115226430982195302982e-403) }}, {{ SC_(30.0),  SC_(5.6294995342131200000000000000000000e14), SC_(-2.7067316060353387914883678696809847e-412) }}, {{ SC_(30.0),  SC_(1.1258999068426240000000000000000000e15), SC_(-2.5208402481258872249902802122831258e-421) }}, {{ SC_(30.0),  SC_(2.2517998136852480000000000000000000e15), SC_(-2.3477154300789073415295882015261417e-430) }}, {{ SC_(30.0),  SC_(4.5035996273704960000000000000000000e15), SC_(-2.1864803788055661339931292249701424e-439) }}, {{ SC_(30.0),  SC_(9.0071992547409920000000000000000000e15), SC_(-2.0363185357354232042765617960742937e-448) }}, {{ SC_(30.0),  SC_(1.8014398509481984000000000000000000e16), SC_(-1.8964694214383340521722742474983073e-457) }}, {{ SC_(30.0),  SC_(3.6028797018963968000000000000000000e16), SC_(-1.7662247842534755008372178353398608e-466) }}, {{ SC_(30.0),  SC_(7.2057594037927936000000000000000000e16), SC_(-1.6449250134206145379392674041454779e-475) }}, {{ SC_(30.0),  SC_(1.4411518807585587200000000000000000e17), SC_(-1.5319557985482871222587909400586267e-484) }}, {{ SC_(30.0),  SC_(2.8823037615171174400000000000000000e17), SC_(-1.4267450185011020326364788255126534e-493) }}, {{ SC_(30.0),  SC_(5.7646075230342348800000000000000000e17), SC_(-1.3287598439502548384585954790099239e-502) }}, {{ SC_(30.0),  SC_(1.1529215046068469760000000000000000e18), SC_(-1.2375040389134127843853680088571555e-511) }}, {{ SC_(30.0),  SC_(2.3058430092136939520000000000000000e18), SC_(-1.1525154476178928989312989675999669e-520) }}, {{ SC_(30.0),  SC_(4.6116860184273879040000000000000000e18), SC_(-1.0733636539596066765325327508232535e-529) }}, {{ SC_(30.0),  SC_(9.2233720368547758080000000000000000e18), SC_(-9.9964780170433844885501818972479211e-539) }}, {{ SC_(30.0),  SC_(1.8446744073709551616000000000000000e19), SC_(-9.3099456439198781553856371993072353e-548) }}, {{ SC_(30.0),  SC_(3.6893488147419103232000000000000000e19), SC_(-8.6705625466256198953841239814504762e-557) }}, {{ SC_(30.0),  SC_(7.3786976294838206464000000000000000e19), SC_(-8.0750906342879122995040411743125170e-566) }}, {{ SC_(30.0),  SC_(1.4757395258967641292800000000000000e20), SC_(-7.5205141997783559362248117439409145e-575) }}, {{ SC_(30.0),  SC_(2.9514790517935282585600000000000000e20), SC_(-7.0040246469698435960734303533011581e-584) }}, {{ SC_(30.0),  SC_(5.9029581035870565171200000000000000e20), SC_(-6.5230062668862227312246814035998837e-593) }}, {{ SC_(30.0),  SC_(1.1805916207174113034240000000000000e21), SC_(-6.0750229907093781336599992450810038e-602) }}, {{ SC_(30.0),  SC_(2.3611832414348226068480000000000000e21), SC_(-5.6578060525556822620531601462211155e-611) }}, {{ SC_(30.0),  SC_(4.7223664828696452136960000000000000e21), SC_(-5.2692424995411953535258666060047970e-620) }}, {{ SC_(30.0),  SC_(9.4447329657392904273920000000000000e21), SC_(-4.9073644909460054277605359114212468e-629) }}, {{ SC_(30.0),  SC_(1.8889465931478580854784000000000000e22), SC_(-4.5703393322844108825145652571006034e-638) }}, {{ SC_(30.0),  SC_(3.7778931862957161709568000000000000e22), SC_(-4.2564601938095045113123493445833636e-647) }}, {{ SC_(30.0),  SC_(7.5557863725914323419136000000000000e22), SC_(-3.9641374664469664090420159863859993e-656) }}, {{ SC_(30.0),  SC_(1.5111572745182864683827200000000000e23), SC_(-3.6918907113810688341424078675611601e-665) }}, {{ SC_(30.0),  SC_(3.0223145490365729367654400000000000e23), SC_(-3.4383411625223875363750612697898904e-674) }}, {{ SC_(30.0),  SC_(6.0446290980731458735308800000000000e23), SC_(-3.2022047438867274079238771888571055e-683) }}, {{ SC_(30.0),  SC_(1.2089258196146291747061760000000000e24), SC_(-2.9822855665224859564787125744222496e-692) }}, {{ SC_(30.0),  SC_(2.4178516392292583494123520000000000e24), SC_(-2.7774698720523025435197111896762965e-701) }}, {{ SC_(30.0),  SC_(4.8357032784585166988247040000000000e24), SC_(-2.5867203921566741015014262629362602e-710) }}, {{ SC_(30.0),  SC_(9.6714065569170333976494080000000000e24), SC_(-2.4090710954337139627909495039162813e-719) }}, {{ SC_(30.0),  SC_(1.9342813113834066795298816000000000e25), SC_(-2.2436222950310576360588405614024340e-728) }}, {{ SC_(30.0),  SC_(3.8685626227668133590597632000000000e25), SC_(-2.0895360922730133273255449640176061e-737) }}, {{ SC_(30.0),  SC_(7.7371252455336267181195264000000000e25), SC_(-1.9460321332076688551581879695116458e-746) }}, {{ SC_(30.0),  SC_(1.5474250491067253436239052800000000e26), SC_(-1.8123836565834180034307649180969011e-755) }}, {{ SC_(30.0),  SC_(3.0948500982134506872478105600000000e26), SC_(-1.6879138132402841033700526042422183e-764) }}, {{ SC_(30.0),  SC_(6.1897001964269013744956211200000000e26), SC_(-1.5719922382759713599179429591983936e-773) }}, {{ SC_(30.0),  SC_(1.2379400392853802748991242240000000e27), SC_(-1.4640318586267264186571752095136003e-782) }}, {{ SC_(30.0),  SC_(2.4758800785707605497982484480000000e27), SC_(-1.3634859198953271085928894585406723e-791) }}, {{ SC_(30.0),  SC_(4.9517601571415210995964968960000000e27), SC_(-1.2698452173688701433994709089494920e-800) }}, {{ SC_(30.0),  SC_(9.9035203142830421991929937920000000e27), SC_(-1.1826355172031280988822420193126128e-809) }}, {{ SC_(30.0),  SC_(1.9807040628566084398385987584000000e28), SC_(-1.1014151547133252945563215933898419e-818) }}, {{ SC_(30.0),  SC_(3.9614081257132168796771975168000000e28), SC_(-1.0257727976081197099353387886404875e-827) }}, {{ SC_(30.0),  SC_(7.9228162514264337593543950336000000e28), SC_(-9.5532536283891621039746216353614015e-837) }}, {{ SC_(30.0),  SC_(1.5845632502852867518708790067200000e29), SC_(-8.8971607651460562869670071028704372e-846) }}, {{ SC_(30.0),  SC_(3.1691265005705735037417580134400000e29), SC_(-8.2861266705636459281360796675545348e-855) }}, {{ SC_(30.0),  SC_(6.3382530011411470074835160268800000e29), SC_(-7.7170568244193177000955489160105929e-864) }}, {{ SC_(30.0),  SC_(1.2676506002282294014967032053760000e30), SC_(-7.1870692301721476950641245729469486e-873) }},
      {{ SC_(31.0), SC_(2.0000000000000000000000000000000000), SC_(1.9145332544935048093264355986676073e24) }}, {{ SC_(31.0), SC_(4.0000000000000000000000000000000000), SC_(4.4611518561919816922776795853710092e14) }}, {{ SC_(31.0), SC_(8.0000000000000000000000000000000000), SC_(106267.95619808419253963903669279065) }}, {{ SC_(31.0), SC_(16.000000000000000000000000000000000), SC_(0.000028318136062027075392587779376335821) }}, {{ SC_(31.0), SC_(32.000000000000000000000000000000000), SC_(9.0814734303237413772295578988209818e-15) }}
   } };
   std::array<std::array<value_type, 3>, 551> neg_data =
   { {
      {{ SC_(1.0), SC_(-12.750), SC_(19.663772856722737612034697464751605) }}, {{ SC_(1.0), SC_(-12.250), SC_(19.660817549236368273654684043826967) }}, {{ SC_(1.0), SC_(-11.750), SC_(19.657621376522814505537196503582823) }}, {{ SC_(1.0), SC_(-11.250), SC_(19.654153659190554029589711115880278) }}, {{ SC_(1.0), SC_(-10.750), SC_(19.650378280099093364749509767503149) }}, {{ SC_(1.0), SC_(-10.250), SC_(19.646252424622652795021809881312377) }}, {{ SC_(1.0), SC_(-9.7500), SC_(19.641724953976865133273035997897957) }}, {{ SC_(1.0), SC_(-9.2500), SC_(19.636734280660725370869519577921538) }}, {{ SC_(1.0), SC_(-8.7500), SC_(19.631205558842085383108670448917024) }}, {{ SC_(1.0), SC_(-8.2500), SC_(19.625046917622010980803778160828770) }}, {{ SC_(1.0), SC_(-7.7500), SC_(19.618144334352289464741323510141514) }}, {{ SC_(1.0), SC_(-7.2500), SC_(19.610354539293269015698176691590937) }}, {{ SC_(1.0), SC_(-6.7500), SC_(19.601495010731061577124257953429755) }}, {{ SC_(1.0), SC_(-6.2500), SC_(19.591329569019785068016844943671793) }}, {{ SC_(1.0), SC_(-5.7500), SC_(19.579547136931335925546754524074474) }}, {{ SC_(1.0), SC_(-5.2500), SC_(19.565729569019785068016844943671793) }}, {{ SC_(1.0), SC_(-4.7500), SC_(19.549301390239464469970194977760674) }}, {{ SC_(1.0), SC_(-4.2500), SC_(19.529448389881463072551992335962043) }}, {{ SC_(1.0), SC_(-3.7500), SC_(19.504980060599575273294294700752364) }}, {{ SC_(1.0), SC_(-3.2500), SC_(19.474085068082155114074483685443011) }}, {{ SC_(1.0), SC_(-2.7500), SC_(19.433868949488464162183183589641253) }}, {{ SC_(1.0), SC_(-2.2500), SC_(19.379410511869137362595193744614609) }}, {{ SC_(1.0), SC_(-1.7500), SC_(19.301637544529786476232770366500757) }}, {{ SC_(1.0), SC_(-1.2500), SC_(19.181879647671606498397662880417078) }}, {{ SC_(1.0), SC_(-0.75000), SC_(18.975106932284888517049096897113002) }}, {{ SC_(1.0), SC_(-0.25000), SC_(18.541879647671606498397662880417078) }},
      {{ SC_(2.0), SC_(-12.750), SC_(-124.03079461415823384604153251543681) }}, {{ SC_(2.0), SC_(-12.250), SC_(124.01896466745858356132308878716344) }}, {{ SC_(2.0), SC_(-11.750), SC_(-124.03175955222881001960976796032603) }}, {{ SC_(2.0), SC_(-11.250), SC_(124.01787668541028735821044014586602) }}, {{ SC_(2.0), SC_(-10.750), SC_(-124.03299241970518808612682102178640) }}, {{ SC_(2.0), SC_(-10.250), SC_(124.01647202148710491650947992638728) }}, {{ SC_(2.0), SC_(-9.7500), SC_(-124.03460234084420729198290916496876) }}, {{ SC_(2.0), SC_(-9.2500), SC_(124.01461482266526541911391108670126) }}, {{ SC_(2.0), SC_(-8.7500), SC_(-124.03676016548723903560636876475972) }}, {{ SC_(2.0), SC_(-8.2500), SC_(124.01208782525148933477537240192445) }}, {{ SC_(2.0), SC_(-7.7500), SC_(-124.03974558822776381694747663647984) }}, {{ SC_(2.0), SC_(-7.2500), SC_(124.00852603656573370687098416695770) }}, {{ SC_(2.0), SC_(-6.7500), SC_(-124.04404218787195165891317097369578) }}, {{ SC_(2.0), SC_(-6.2500), SC_(124.00327776890408296268303058132483) }}, {{ SC_(2.0), SC_(-5.7500), SC_(-124.05054526159038888901020902683808) }}, {{ SC_(2.0), SC_(-5.2500), SC_(123.99508576890408296268303058132483) }}, {{ SC_(2.0), SC_(-4.7500), SC_(-124.06106552130930069964553408642549) }}, {{ SC_(2.0), SC_(-4.2500), SC_(123.98126436732757934536308673076874) }}, {{ SC_(2.0), SC_(-3.7500), SC_(-124.07972713378925404561433420306057) }}, {{ SC_(2.0), SC_(-3.2500), SC_(123.95521103942202265902072971875978) }}, {{ SC_(2.0), SC_(-2.7500), SC_(-124.11765305971517997154026012898649) }}, {{ SC_(2.0), SC_(-2.2500), SC_(123.89694977406016558118732052440384) }}, {{ SC_(2.0), SC_(-1.7500), SC_(-124.21382135423058192495874247308867) }}, {{ SC_(2.0), SC_(-1.2500), SC_(123.72136678366236036856729308956159) }}, {{ SC_(2.0), SC_(-0.75000), SC_(-124.58699919679617959259722643810325) }}, {{ SC_(2.0), SC_(-0.25000), SC_(122.69736678366236036856729308956159) }},
      {{ SC_(3.0), SC_(-12.750), SC_(1558.5445992104061926890981987122713) }}, {{ SC_(3.0), SC_(-12.250), SC_(1558.5444945580353369268010524200916) }}, {{ SC_(3.0), SC_(-11.750), SC_(1558.5443721661542924129644962546503) }}, {{ SC_(3.0), SC_(-11.250), SC_(1558.5442281134520807137938731609983) }}, {{ SC_(3.0), SC_(-10.750), SC_(1558.5440573914794724810878018559796) }}, {{ SC_(3.0), SC_(-10.250), SC_(1558.5438535364058987293402837691373) }}, {{ SC_(3.0), SC_(-9.7500), SC_(1558.5436081111616066561977307462543) }}, {{ SC_(3.0), SC_(-9.2500), SC_(1558.5433099660190188764440197184975) }}, {{ SC_(3.0), SC_(-8.7500), SC_(1558.5429441651175968889289739463186) }}, {{ SC_(3.0), SC_(-8.2500), SC_(1558.5424903992902266328747639288402) }}, {{ SC_(3.0), SC_(-7.7500), SC_(1558.5419205916065598210405941045860) }}, {{ SC_(3.0), SC_(-7.2500), SC_(1558.5411952034044973136368045706704) }}, {{ SC_(3.0), SC_(-6.7500), SC_(1558.5402573917442935596345188772766) }}, {{ SC_(3.0), SC_(-6.2500), SC_(1558.5390235064410556263866168800637) }}, {{ SC_(3.0), SC_(-5.7500), SC_(1558.5373671367583214573691686314356) }}, {{ SC_(3.0), SC_(-5.2500), SC_(1558.5350913464410556263866168800637) }}, {{ SC_(3.0), SC_(-4.7500), SC_(1558.5318783056006283387768251220856) }}, {{ SC_(3.0), SC_(-4.2500), SC_(1558.5271934026830535593466489654603) }}, {{ SC_(3.0), SC_(-3.7500), SC_(1558.5200920240343420150070566273687) }}, {{ SC_(3.0), SC_(-3.2500), SC_(1558.5088028182791311925167498981598) }}, {{ SC_(3.0), SC_(-2.7500), SC_(1558.4897512832936012742663158866280) }}, {{ SC_(3.0), SC_(-2.2500), SC_(1558.4550231887143400437474491033697) }}, {{ SC_(3.0), SC_(-1.7500), SC_(1558.3848404165495264159916078748802) }}, {{ SC_(3.0), SC_(-1.2500), SC_(1558.2209125348505997602540791902467) }}, {{ SC_(3.0), SC_(-0.75000), SC_(1557.7451069721513589857542067919980) }}, {{ SC_(3.0), SC_(-0.25000), SC_(1555.7633125348505997602540791902467) }},
      {{ SC_(4.0), SC_(-12.750), SC_(-24481.574976569827769932951761311307) }}, {{ SC_(4.0), SC_(-12.250), SC_(24481.574556933476371183897773040987) }}, {{ SC_(4.0), SC_(-11.750), SC_(-24481.575047799396993548993707180364) }}, {{ SC_(4.0), SC_(-11.250), SC_(24481.574469931163471195977061446181) }}, {{ SC_(4.0), SC_(-10.750), SC_(-24481.575154956733102461973007401188) }}, {{ SC_(4.0), SC_(-10.250), SC_(24481.574336748213717601504674106852) }}, {{ SC_(4.0), SC_(-9.7500), SC_(-24481.575322130804866489839080372249) }}, {{ SC_(4.0), SC_(-9.2500), SC_(24481.574124623184691317447595452944) }}, {{ SC_(4.0), SC_(-8.7500), SC_(-24481.575594518925485881539083161966) }}, {{ SC_(4.0), SC_(-8.2500), SC_(24481.573770215950618995904133489849) }}, {{ SC_(4.0), SC_(-7.7500), SC_(-24481.576062438244817112573771089615) }}, {{ SC_(4.0), SC_(-7.2500), SC_(24481.573142242187841144152395619221) }}, {{ SC_(4.0), SC_(-6.7500), SC_(-24481.576920863980180344267229271453) }}, {{ SC_(4.0), SC_(-6.2500), SC_(24481.571944064552838833945395514059) }}, {{ SC_(4.0), SC_(-5.7500), SC_(-24481.578633607675571219683733120840) }}, {{ SC_(4.0), SC_(-5.2500), SC_(24481.569427482152838833945395514059) }}, {{ SC_(4.0), SC_(-4.7500), SC_(-24481.582451925002662084791450344735) }}, {{ SC_(4.0), SC_(-4.2500), SC_(24481.563410001194361068581610436266) }}, {{ SC_(4.0), SC_(-3.7500), SC_(-24481.592377214742692673229150129760) }}, {{ SC_(4.0), SC_(-3.2500), SC_(24481.546101215873022370388764255277) }}, {{ SC_(4.0), SC_(-2.7500), SC_(-24481.624740671532816130019273586550) }}, {{ SC_(4.0), SC_(-2.2500), SC_(24481.479910902562510187288086353997) }}, {{ SC_(4.0), SC_(-1.7500), SC_(-24481.777338295887834105691576149093) }}, {{ SC_(4.0), SC_(-1.2500), SC_(24481.063714184582527461077650952890) }}, {{ SC_(4.0), SC_(-0.75000), SC_(-24483.239586168797931089091350052823) }}, {{ SC_(4.0), SC_(-0.25000), SC_(24473.199394184582527461077650952890) }},
      {{ SC_(5.0), SC_(-12.750), SC_(492231.26705220367447285602722829798) }}, {{ SC_(5.0), SC_(-12.250), SC_(492231.26703986858726773334478356804) }}, {{ SC_(5.0), SC_(-11.750), SC_(492231.26702427051007143797156325129) }}, {{ SC_(5.0), SC_(-11.250), SC_(492231.26700435743914528929551352935) }}, {{ SC_(5.0), SC_(-10.750), SC_(492231.26697867164364211329952060413) }}, {{ SC_(5.0), SC_(-10.250), SC_(492231.26694516501703258064111915631) }}, {{ SC_(5.0), SC_(-9.7500), SC_(492231.26690091626142628638506805946) }}, {{ SC_(5.0), SC_(-9.2500), SC_(492231.26684168939311732012547103246) }}, {{ SC_(5.0), SC_(-8.7500), SC_(492231.26676123004572403423122047499) }}, {{ SC_(5.0), SC_(-8.2500), SC_(492231.26665011791524038956143753889) }}, {{ SC_(5.0), SC_(-7.7500), SC_(492231.26649384757753475935425594490) }}, {{ SC_(5.0), SC_(-7.2500), SC_(492231.26626952775598108546947519306) }}, {{ SC_(5.0), SC_(-6.7500), SC_(492231.26594002452246170664879905339) }}, {{ SC_(5.0), SC_(-6.2500), SC_(492231.26544319835253121636119925846) }}, {{ SC_(5.0), SC_(-5.7500), SC_(492231.26467132548883883596990731311) }}, {{ SC_(5.0), SC_(-5.2500), SC_(492231.26342993243253121636119925846) }}, {{ SC_(5.0), SC_(-4.7500), SC_(492231.26135104955223808370232711841) }}, {{ SC_(5.0), SC_(-4.2500), SC_(492231.25769899818636191601473727961) }}, {{ SC_(5.0), SC_(-3.7500), SC_(492231.25090337614167956955737997628) }}, {{ SC_(5.0), SC_(-3.2500), SC_(492231.23733572133772815343491824315) }}, {{ SC_(5.0), SC_(-2.7500), SC_(492231.20775210042151496050388203390) }}, {{ SC_(5.0), SC_(-2.2500), SC_(492231.13550447009078633328002916426) }}, {{ SC_(5.0), SC_(-1.7500), SC_(492230.93030187432148227746333192018) }}, {{ SC_(5.0), SC_(-1.2500), SC_(492230.21062287457971360836795049513) }}, {{ SC_(5.0), SC_(-0.75000), SC_(492226.75245080886406232489254933809) }}, {{ SC_(5.0), SC_(-0.25000), SC_(492198.75334287457971360836795049513) }},
      {{ SC_(6.0), SC_(-12.750), SC_(-1.1791224761262553923199740571475659e7) }}, {{ SC_(6.0), SC_(-12.250), SC_(1.1791224761212959953338096554813394e7) }}, {{ SC_(6.0), SC_(-11.750), SC_(-1.1791224761275698941741584362376857e7) }}, {{ SC_(6.0), SC_(-11.250), SC_(1.1791224761195566737931185183742355e7) }}, {{ SC_(6.0), SC_(-10.750), SC_(-1.1791224761298983469279962918313528e7) }}, {{ SC_(6.0), SC_(-10.250), SC_(1.1791224761163997446137740568065356e7) }}, {{ SC_(6.0), SC_(-9.7500), SC_(-1.1791224761342381822144610498473088e7) }}, {{ SC_(6.0), SC_(-9.2500), SC_(1.1791224761103426349211734412564015e7) }}, {{ SC_(6.0), SC_(-8.7500), SC_(-1.1791224761428342570269073362379294e7) }}, {{ SC_(6.0), SC_(-8.2500), SC_(1.1791224760979163768967238911569317e7) }}, {{ SC_(6.0), SC_(-7.7500), SC_(-1.1791224761611690548456004706583543e7) }}, {{ SC_(6.0), SC_(-7.2500), SC_(1.1791224760702370925869563208323974e7) }}, {{ SC_(6.0), SC_(-6.7500), SC_(-1.1791224762040456784641593897905007e7) }}, {{ SC_(6.0), SC_(-6.2500), SC_(1.1791224760018512109221395670440442e7) }}, {{ SC_(6.0), SC_(-5.7500), SC_(-1.1791224763168189258973034501364332e7) }}, {{ SC_(6.0), SC_(-5.2500), SC_(1.1791224758085776826021395670440442e7) }}, {{ SC_(6.0), SC_(-4.7500), SC_(-1.1791224766632825018904254258839318e7) }}, {{ SC_(6.0), SC_(-4.2500), SC_(1.1791224751536137687542195274483895e7) }}, {{ SC_(6.0), SC_(-3.7500), SC_(-1.1791224779829886169083430020877813e7) }}, {{ SC_(6.0), SC_(-3.2500), SC_(1.1791224722787982136529824573562902e7) }}, {{ SC_(6.0), SC_(-2.7500), SC_(-1.1791224848871927321346804506474521e7) }}, {{ SC_(6.0), SC_(-2.2500), SC_(1.1791224534791825988329541210690757e7) }}, {{ SC_(6.0), SC_(-1.7500), SC_(-1.1791225454217875175963567504038405e7) }}, {{ SC_(6.0), SC_(-1.2500), SC_(1.1791222068440904625468941445147639e7) }}, {{ SC_(6.0), SC_(-0.75000), SC_(-1.1791239778278671029974833461007258e7) }}, {{ SC_(6.0), SC_(-0.25000), SC_(1.1791071073496904625468941445147639e7) }},
      {{ SC_(7.0), SC_(-12.750), SC_(3.3035269585550319411369451657990653e8) }}, {{ SC_(7.0), SC_(-12.250), SC_(3.3035269585550014530679554645726841e8) }}, {{ SC_(7.0), SC_(-11.750), SC_(3.3035269585549597724076958273392155e8) }}, {{ SC_(7.0), SC_(-11.250), SC_(3.3035269585549020632656302567379925e8) }}, {{ SC_(7.0), SC_(-10.750), SC_(3.3035269585548210560734246359421460e8) }}, {{ SC_(7.0), SC_(-10.250), SC_(3.3035269585547056321166932680182245e8) }}, {{ SC_(7.0), SC_(-9.7500), SC_(3.3035269585545384621477943726294791e8) }}, {{ SC_(7.0), SC_(-9.2500), SC_(3.3035269585542919758450034698830934e8) }}, {{ SC_(7.0), SC_(-8.7500), SC_(3.3035269585539213080586956648886140e8) }}, {{ SC_(7.0), SC_(-8.2500), SC_(3.3035269585533516103728829633890794e8) }}, {{ SC_(7.0), SC_(-7.7500), SC_(3.3035269585524545242332002141349801e8) }}, {{ SC_(7.0), SC_(-7.2500), SC_(3.3035269585510030650375087453009371e8) }}, {{ SC_(7.0), SC_(-6.7500), SC_(3.3035269585485817969386206988585281e8) }}, {{ SC_(7.0), SC_(-6.2500), SC_(3.3035269585444002902560781621765444e8) }}, {{ SC_(7.0), SC_(-5.7500), SC_(3.3035269585368867935011094629708018e8) }}, {{ SC_(7.0), SC_(-5.2500), SC_(3.3035269585227536550842381621765444e8) }}, {{ SC_(7.0), SC_(-4.7500), SC_(3.3035269584947086190323815702711063e8) }}, {{ SC_(7.0), SC_(-4.2500), SC_(3.3035269584354251332378488235637904e8) }}, {{ SC_(7.0), SC_(-3.7500), SC_(3.3035269583002256126086884537779074e8) }}, {{ SC_(7.0), SC_(-3.2500), SC_(3.3035269579619261006329391884897976e8) }}, {{ SC_(7.0), SC_(-2.7500), SC_(3.3035269570114408444331054633801022e8) }}, {{ SC_(7.0), SC_(-2.2500), SC_(3.3035269539127781220563177006740899e8) }}, {{ SC_(7.0), SC_(-1.7500), SC_(3.3035269416026348990428605870784761e8) }}, {{ SC_(7.0), SC_(-1.2500), SC_(3.3035268771818605685450990413016373e8) }}, {{ SC_(7.0), SC_(-0.75000), SC_(3.3035263686402030648824099487997220e8) }}, {{ SC_(7.0), SC_(-0.25000), SC_(3.3035184214649965685450990413016373e8) }},
      {{ SC_(8.0), SC_(-7.7500), SC_(-1.0569114259666913771892699180522388e10) }}, {{ SC_(8.0), SC_(-7.2500), SC_(1.0569114259666319911021283350705169e10) }}, {{ SC_(8.0), SC_(-6.7500), SC_(-1.0569114259667313537290849324034796e10) }}, {{ SC_(8.0), SC_(-6.2500), SC_(1.0569114259665591328976435838084546e10) }}, {{ SC_(8.0), SC_(-5.7500), SC_(-1.0569114259668699611772332137177045e10) }}, {{ SC_(8.0), SC_(-5.2500), SC_(1.0569114259662820559674440318084546e10) }}, {{ SC_(8.0), SC_(-4.7500), SC_(-1.0569114259674567879524502974422220e10) }}, {{ SC_(8.0), SC_(-4.2500), SC_(1.0569114259649513356345466704581650e10) }}, {{ SC_(8.0), SC_(-3.7500), SC_(-1.0569114259707322912185335499305285e10) }}, {{ SC_(8.0), SC_(-3.2500), SC_(1.0569114259560384126678660185038311e10) }}, {{ SC_(8.0), SC_(-2.7500), SC_(-1.0569114259982263662729459870590150e10) }}, {{ SC_(8.0), SC_(-2.2500), SC_(1.0569114258563670778105953357268290e10) }}, {{ SC_(8.0), SC_(-1.7500), SC_(-1.0569114264464825392297531107332441e10) }}, {{ SC_(8.0), SC_(-1.2500), SC_(1.0569114231281566759079742278380307e10) }}, {{ SC_(8.0), SC_(-0.75000), SC_(-1.0569114526390508516485165684831300e10) }}, {{ SC_(8.0), SC_(-0.25000), SC_(1.0569108819622773799079742278380307e10) }},
      {{ SC_(9.0), SC_(-7.7500), SC_(3.8051374324233954962308727459400485e11) }}, {{ SC_(9.0), SC_(-7.2500), SC_(3.8051374324233938918730928010849146e11) }}, {{ SC_(9.0), SC_(-6.7500), SC_(3.8051374324233908537939910023379690e11) }}, {{ SC_(9.0), SC_(-6.2500), SC_(3.8051374324233848474063291767903138e11) }}, {{ SC_(9.0), SC_(-5.7500), SC_(3.8051374324233723728009045648294056e11) }}, {{ SC_(9.0), SC_(-5.2500), SC_(3.8051374324233449483283804413023138e11) }}, {{ SC_(9.0), SC_(-4.7500), SC_(3.8051374324232805216534792821594812e11) }}, {{ SC_(9.0), SC_(-4.2500), SC_(3.8051374324231168248427408936422641e11) }}, {{ SC_(9.0), SC_(-3.7500), SC_(3.8051374324226598999820108764248547e11) }}, {{ SC_(9.0), SC_(-3.2500), SC_(3.8051374324212293823321496967578170e11) }}, {{ SC_(9.0), SC_(-2.7500), SC_(3.8051374324160613219689518915140179e11) }}, {{ SC_(9.0), SC_(-2.2500), SC_(3.8051374323936280896024439692195702e11) }}, {{ SC_(9.0), SC_(-1.7500), SC_(3.8051374322693593017285422874024520e11) }}, {{ SC_(9.0), SC_(-1.2500), SC_(3.8051374313023439288413955260640509e11) }}, {{ SC_(9.0), SC_(-0.75000), SC_(3.8051374187988955981988925091310821e11) }}, {{ SC_(9.0), SC_(-0.25000), SC_(3.8051370416629108357213955260640509e11) }},
      {{ SC_(10.0), SC_(-7.7500), SC_(-1.5220204740668341333676731352020598e13)}}, {{ SC_(10.0), SC_(-7.2500), SC_(1.5220204740668340669650877835677384e13) }}, {{ SC_(10.0), SC_(-6.7500), SC_(-1.5220204740668341932700845125388608e13) }}, {{ SC_(10.0), SC_(-6.2500), SC_(1.5220204740668339422138220784050543e13) }}, {{ SC_(10.0), SC_(-5.7500), SC_(-1.5220204740668344670625746819834321e13) }}, {{ SC_(10.0), SC_(-5.2500), SC_(1.5220204740668333038285748986372463e13) }}, {{ SC_(10.0), SC_(-4.7500), SC_(-1.5220204740668360644738342521168221e13) }}, {{ SC_(10.0), SC_(-4.2500), SC_(1.5220204740668289586193246215389596e13) }}, {{ SC_(10.0), SC_(-3.7500), SC_(-1.5220204740668491301932335869743932e13) }}, {{ SC_(10.0), SC_(-3.2500), SC_(1.5220204740667845482073107110240315e13) }}, {{ SC_(10.0), SC_(-2.7500), SC_(-1.5220204740670250922735818265720155e13) }}, {{ SC_(10.0), SC_(-2.2500), SC_(1.5220204740659352776617813040228546e13) }}, {{ SC_(10.0), SC_(-1.7500), SC_(-1.5220204740723597111914149030851634e13) }}, {{ SC_(10.0), SC_(-1.2500), SC_(1.5220204740174337594057347065492760e13) }}, {{ SC_(10.0), SC_(-0.75000), SC_(-1.5220204748421004942502520332720988e13) }}, {{ SC_(10.0), SC_(-0.25000), SC_(1.5220204428462791119561347065492760e13) }},
      {{ SC_(11.0), SC_(-7.7500), SC_(6.6969403856797204470999075620782555e14)}}, {{ SC_(11.0), SC_(-7.2500), SC_(6.6969403856797204443997065278152669e14) }}, {{ SC_(11.0), SC_(-6.7500), SC_(6.6969403856797204385976298181981934e14) }}, {{ SC_(11.0), SC_(-6.2500), SC_(6.6969403856797204254719282828940321e14) }}, {{ SC_(11.0), SC_(-5.7500), SC_(6.6969403856797203939795943831775966e14) }}, {{ SC_(11.0), SC_(-5.2500), SC_(6.6969403856797203131161247792548979e14) }}, {{ SC_(11.0), SC_(-4.7500), SC_(6.6969403856797200883878751610651220e14) }}, {{ SC_(11.0), SC_(-4.2500), SC_(6.6969403856797194026913294831009712e14) }}, {{ SC_(11.0), SC_(-3.7500), SC_(6.6969403856797170626423300519402108e14) }}, {{ SC_(11.0), SC_(-3.2500), SC_(6.6969403856797079082317494121441662e14) }}, {{ SC_(11.0), SC_(-2.7500), SC_(6.6969403856796654470987612349915749e14) }}, {{ SC_(11.0), SC_(-2.2500), SC_(6.6969403856794204628163394590053064e14) }}, {{ SC_(11.0), SC_(-1.7500), SC_(6.6969403856775315995316280043863158e14) }}, {{ SC_(11.0), SC_(-1.2500), SC_(6.6969403856557086094467166780182235e14) }}, {{ SC_(11.0), SC_(-0.75000), SC_(6.6969403851936945358946446654116707e14) }}, {{ SC_(11.0), SC_(-0.25000), SC_(6.6969403582250925196910686780182235e14) }},
      {{ SC_(12.0), SC_(-7.7500), SC_(-3.2145233093874118337222380549030432e16)}}, {{ SC_(12.0), SC_(-7.2500), SC_(3.2145233093874118336089459241510371e16) }}, {{ SC_(12.0), SC_(-6.7500), SC_(-3.2145233093874118338538862264211861e16) }}, {{ SC_(12.0), SC_(-6.2500), SC_(3.2145233093874118332956585600971684e16) }}, {{ SC_(12.0), SC_(-5.7500), SC_(-3.2145233093874118346470957452659967e16) }}, {{ SC_(12.0), SC_(-5.2500), SC_(3.2145233093874118311384271328272970e16) }}, {{ SC_(12.0), SC_(-4.7500), SC_(-3.2145233093874118410246620594666049e16) }}, {{ SC_(12.0), SC_(-4.2500), SC_(3.2145233093874118103287175260580644e16) }}, {{ SC_(12.0), SC_(-3.7500), SC_(-3.2145233093874119174645495148550237e16) }}, {{ SC_(12.0), SC_(-3.2500), SC_(3.2145233093874114857792705593486958e16) }}, {{ SC_(12.0), SC_(-2.7500), SC_(-3.2145233093874135691619437169973801e16) }}, {{ SC_(12.0), SC_(-2.2500), SC_(3.2145233093874008724100861918481841e16) }}, {{ SC_(12.0), SC_(-1.7500), SC_(-3.2145233093875066825103277125147005e16) }}, {{ SC_(12.0), SC_(-1.2500), SC_(3.2145233093861362402303729768622063e16) }}, {{ SC_(12.0), SC_(-0.75000), SC_(-3.2145233094206840811597208557586761e16) }}, {{ SC_(12.0), SC_(-0.25000), SC_(3.2145233067527970956138307688622063e16) }},
      {{ SC_(13.0), SC_(-7.7500), SC_(1.6715535177261375555623235851859237e18)}}, {{ SC_(13.0), SC_(-7.2500), SC_(1.6715535177261375555616811505615971e18) }}, {{ SC_(13.0), SC_(-6.7500), SC_(1.6715535177261375555601152932765871e18) }}, {{ SC_(13.0), SC_(-6.2500), SC_(1.6715535177261375555560635840337346e18) }}, {{ SC_(13.0), SC_(-5.7500), SC_(1.6715535177261375555448386655062426e18) }}, {{ SC_(13.0), SC_(-5.2500), SC_(1.6715535177261375555111931703465213e18) }}, {{ SC_(13.0), SC_(-4.7500), SC_(1.6715535177261375554006502097069245e18) }}, {{ SC_(13.0), SC_(-4.2500), SC_(1.6715535177261375549959051229408070e18) }}, {{ SC_(13.0), SC_(-3.7500), SC_(1.6715535177261375533086111846120836e18) }}, {{ SC_(13.0), SC_(-3.2500), SC_(1.6715535177261375450685102745473439e18) }}, {{ SC_(13.0), SC_(-2.7500), SC_(1.6715535177261374960497681856044819e18) }}, {{ SC_(13.0), SC_(-2.2500), SC_(1.6715535177261371205337428998473234e18) }}, {{ SC_(13.0), SC_(-1.7500), SC_(1.6715535177261330943278445785436631e18) }}, {{ SC_(13.0), SC_(-1.2500), SC_(1.6715535177260640528966928029814669e18) }}, {{ SC_(13.0), SC_(-0.75000), SC_(1.6715535177236684875710325164741107e18) }}, {{ SC_(13.0), SC_(-0.25000), SC_(1.6715535174521967818565724133494669e18) }},
      {{ SC_(20.0), SC_(-7.7500), SC_(-1.0700016187896297695358366297227405e31)}}, {{ SC_(20.0), SC_(-7.2500), SC_(1.0700016187896297695358366297227207e31) }}, {{ SC_(20.0), SC_(-6.7500), SC_(-1.0700016187896297695358366297227919e31) }}, {{ SC_(20.0), SC_(-6.2500), SC_(1.0700016187896297695358366297225123e31) }}, {{ SC_(20.0), SC_(-5.7500), SC_(-1.0700016187896297695358366297237267e31) }}, {{ SC_(20.0), SC_(-5.2500), SC_(1.0700016187896297695358366297178064e31) }}, {{ SC_(20.0), SC_(-4.7500), SC_(-1.0700016187896297695358366297508349e31) }}, {{ SC_(20.0), SC_(-4.2500), SC_(1.0700016187896297695358366295346680e31) }}, {{ SC_(20.0), SC_(-3.7500), SC_(-1.0700016187896297695358366312489970e31) }}, {{ SC_(20.0), SC_(-3.2500), SC_(1.0700016187896297695358366140480322e31) }}, {{ SC_(20.0), SC_(-2.7500), SC_(-1.0700016187896297695358368457690989e31) }}, {{ SC_(20.0), SC_(-2.2500), SC_(1.0700016187896297695358322831891149e31) }}, {{ SC_(20.0), SC_(-1.7500), SC_(-1.0700016187896297695359814356987569e31) }}, {{ SC_(20.0), SC_(-1.2500), SC_(1.0700016187896297695260533442006924e31) }}, {{ SC_(20.0), SC_(-0.75000), SC_(-1.0700016187896297714516730336449055e31) }}, {{ SC_(20.0), SC_(-0.25000), SC_(1.0700016187896275255700182817756420e31) }},
      {{ SC_(23.0), SC_(-7.7500), SC_(7.2766958095269026379022334905108869e36)}}, {{ SC_(23.0), SC_(-7.2500), SC_(7.2766958095269026379022334905108869e36) }}, {{ SC_(23.0), SC_(-6.7500), SC_(7.2766958095269026379022334905108869e36) }}, {{ SC_(23.0), SC_(-6.2500), SC_(7.2766958095269026379022334905108868e36) }}, {{ SC_(23.0), SC_(-5.7500), SC_(7.2766958095269026379022334905108866e36) }}, {{ SC_(23.0), SC_(-5.2500), SC_(7.2766958095269026379022334905108848e36) }}, {{ SC_(23.0), SC_(-4.7500), SC_(7.2766958095269026379022334905108714e36) }}, {{ SC_(23.0), SC_(-4.2500), SC_(7.2766958095269026379022334905107503e36) }}, {{ SC_(23.0), SC_(-3.7500), SC_(7.2766958095269026379022334905093860e36) }}, {{ SC_(23.0), SC_(-3.2500), SC_(7.2766958095269026379022334904893135e36) }}, {{ SC_(23.0), SC_(-2.7500), SC_(7.2766958095269026379022334900771270e36) }}, {{ SC_(23.0), SC_(-2.2500), SC_(7.2766958095269026379022334770834817e36) }}, {{ SC_(23.0), SC_(-1.7500), SC_(7.2766958095269026379022327513062336e36) }}, {{ SC_(23.0), SC_(-1.2500), SC_(7.2766958095269026379021422520579094e36) }}, {{ SC_(23.0), SC_(-0.75000), SC_(7.2766958095269026378642504512809960e36) }}, {{ SC_(23.0), SC_(-0.25000), SC_(7.2766958095269025158194448897624671e36) }},
      {{ SC_(3.0), -SC_(4.25), SC_(1558.5271934026830535593466489654603)}}, {{ SC_(4.0), -SC_(4.25), SC_(24481.563410001194361068581610436266) }}, {{ SC_(5.0), -SC_(4.25), SC_(492231.25769899818636191601473727961) }}, {{ SC_(6.0), -SC_(4.25), SC_(1.1791224751536137687542195274483895e7) }}, {{ SC_(7.0), -SC_(4.25), SC_(3.3035269584354251332378488235637904e8) }}, {{ SC_(8.0), -SC_(4.25), SC_(1.0569114259649513356345466704581650e10) }}, {{ SC_(9.0), -SC_(4.25), SC_(3.8051374324231168248427408936422641e11) }}, {{ SC_(10.0), -SC_(4.25), SC_(1.5220204740668289586193246215389596e13) }}, {{ SC_(11.0), -SC_(4.25), SC_(6.6969403856797194026913294831009712e14) }}, {{ SC_(12.0), -SC_(4.25), SC_(3.2145233093874118103287175260580644e16) }}, {{ SC_(13.0), -SC_(4.25), SC_(1.6715535177261375549959051229408070e18) }}, {{ SC_(14.0), -SC_(4.25), SC_(9.3606970885707978198242133390297266e19) }}, {{ SC_(15.0), -SC_(4.25), SC_(5.6164187748870728746250217782924909e21) }}, {{ SC_(16.0), -SC_(4.25), SC_(3.5945079045721164734031460047595950e23) }}, {{ SC_(17.0), -SC_(4.25), SC_(2.4442654003427929640663726375953543e25) }}, {{ SC_(18.0), -SC_(4.25), SC_(1.7598710821897274459494049818707809e27) }}, {{ SC_(19.0), -SC_(4.25), SC_(1.3375020239985042298043467701659026e29) }}, {{ SC_(20.0), -SC_(4.25), SC_(1.0700016187896297695358366295346680e31) }}, {{ SC_(21.0), -SC_(4.25), SC_(8.9880135989785359476536358803633961e32) }}, {{ SC_(22.0), -SC_(4.25), SC_(7.9094519667650484338896180683524747e34) }}, {{ SC_(23.0), -SC_(4.25), SC_(7.2766958095269026379022334905107503e36) }},
      {{ SC_(3.0), SC_(-4.75), SC_(1558.5318783056006283387768251220856) }}, {{ SC_(4.0), SC_(-4.75), SC_(-24481.582451925002662084791450344735) }}, {{ SC_(5.0), SC_(-4.75), SC_(492231.26135104955223808370232711841) }}, {{ SC_(6.0), SC_(-4.75), SC_(-1.1791224766632825018904254258839318e7) }}, {{ SC_(7.0), SC_(-4.75), SC_(3.3035269584947086190323815702711063e8) }}, {{ SC_(8.0), SC_(-4.75), SC_(-1.0569114259674567879524502974422220e10) }}, {{ SC_(9.0), SC_(-4.75), SC_(3.8051374324232805216534792821594812e11) }}, {{ SC_(10.0), SC_(-4.75), SC_(-1.5220204740668360644738342521168221e13) }}, {{ SC_(11.0), SC_(-4.75), SC_(6.6969403856797200883878751610651220e14) }}, {{ SC_(12.0), SC_(-4.75), SC_(-3.2145233093874118410246620594666049e16) }}, {{ SC_(13.0), SC_(-4.75), SC_(1.6715535177261375554006502097069245e18) }}, {{ SC_(14.0), SC_(-4.75), SC_(-9.3606970885707978200117071697879591e19) }}, {{ SC_(15.0), SC_(-4.75), SC_(5.6164187748870728746282195405281448e21) }}, {{ SC_(16.0), SC_(-4.75), SC_(-3.5945079045721164734032997263075953e23) }}, {{ SC_(17.0), SC_(-4.75), SC_(2.4442654003427929640663758934254941e25) }}, {{ SC_(18.0), SC_(-4.75), SC_(-1.7598710821897274459494051446575390e27) }}, {{ SC_(19.0), SC_(-4.75), SC_(1.3375020239985042298043467743154361e29) }}, {{ SC_(20.0), SC_(-4.75), SC_(-1.0700016187896297695358366297508349e31) }}, {{ SC_(21.0), SC_(-4.75), SC_(8.9880135989785359476536358804280906e32) }}, {{ SC_(22.0), SC_(-4.75), SC_(-7.9094519667650484338896180683559907e34) }}, {{ SC_(23.0), SC_(-4.75), SC_(7.2766958095269026379022334905108714e36) }},
      {{ SC_(1.0), SC_(-9.5), SC_(9.7696874450302318856305468284306792)}}, {{ SC_(3.0), SC_(-9.5), SC_(194.81619198176773011863271713047162) }}, {{ SC_(5.0), SC_(-9.5), SC_(15382.226860156995915624995579131219) }}, {{ SC_(7.0), SC_(-9.5), SC_(2.5808804363008334969805587475917565e6) }}, {{ SC_(9.0), SC_(-9.5), SC_(7.4319090477015599086877150154313919e8) }}, {{ SC_(11.0), SC_(-9.5), SC_(3.2699904226951756570017589463296126e11) }}, {{ SC_(13.0), SC_(-9.5), SC_(2.0404706026930390078103975418992502e14) }}, {{ SC_(15.0), SC_(-9.5), SC_(1.7139949874533303450398622617829339e17) }}, {{ SC_(17.0), SC_(-9.5), SC_(1.8648265078298896515398973583300966e20) }}, {{ SC_(19.0), SC_(-9.5), SC_(2.5510826568574986072623191304028880e23) }}, {{ SC_(21.0), SC_(-9.5), SC_(4.2858188624279670465725116159418790e26) }},
      {{ SC_(2.0), SC_(-9.5), SC_(-0.0099751442477151692853059194570941025) }}, {{ SC_(4.0), SC_(-9.5), SC_(-0.00059506011900940675655749713967447346) }}, {{ SC_(6.0), SC_(-9.5), SC_(-0.00011794286977626608581527674104044053) }}, {{ SC_(8.0), SC_(-9.5), SC_(-0.000048934615584055214532361558113243801) }}, {{ SC_(10.0), SC_(-9.5), SC_(-0.000034696555222805555969152083201249795) }}, {{ SC_(12.0), SC_(-9.5), SC_(-0.000037470635416758472254487967117333555) }}, {{ SC_(14.0), SC_(-9.5), SC_(-0.000057218576281198884425118075685027774) }}, {{ SC_(16.0), SC_(-9.5), SC_(-0.00011728023376485851827598955099805232) }}, {{ SC_(18.0), SC_(-9.5), SC_(-0.00031049110045758006635527458576736345) }}, {{ SC_(20.0), SC_(-9.5), SC_(-0.0010307637762451416598018081796345932) }},
      {{ SC_(2.0), SC_(-9.5367431640625000000000000000000000e-7), SC_(2.3058430092136939495958800005662742e18) }}, {{ SC_(2.0), SC_(-4.7683715820312500000000000000000000e-7), SC_(1.8446744073709551613595883097126372e19) }}, {{ SC_(2.0), SC_(-2.3841857910156250000000000000000000e-7), SC_(1.4757395258967641292559588464540430e20) }}, {{ SC_(2.0), SC_(-1.1920928955078125000000000000000000e-7), SC_(1.1805916207174113034215958854195427e21) }}, {{ SC_(2.0), SC_(-5.9604644775390625000000000000000000e-8), SC_(9.4447329657392904273895958858066118e21) }}, {{ SC_(2.0), SC_(-2.9802322387695312500000000000000000e-8), SC_(7.5557863725914323419133595886000146e22) }}, {{ SC_(2.0), SC_(-1.4901161193847656250000000000000000e-8), SC_(6.0446290980731458735308559588609691e23) }}, {{ SC_(2.0), SC_(-7.4505805969238281250000000000000000e-9), SC_(4.8357032784585166988247015958861453e24) }}, {{ SC_(2.0), SC_(-3.7252902984619140625000000000000000e-9), SC_(3.8685626227668133590597629595886169e25) }}, {{ SC_(2.0), SC_(-1.8626451492309570312500000000000000e-9), SC_(3.0948500982134506872478105359588618e26) }}, {{ SC_(2.0), SC_(-9.3132257461547851562500000000000000e-10), SC_(2.4758800785707605497982484455958862e27) }}, {{ SC_(2.0), SC_(-4.6566128730773925781250000000000000e-10), SC_(1.9807040628566084398385987581595886e28) }}, {{ SC_(2.0), SC_(-2.3283064365386962890625000000000000e-10), SC_(1.5845632502852867518708790066959589e29) }}, {{ SC_(2.0), SC_(-1.1641532182693481445312500000000000e-10), SC_(1.2676506002282294014967032053735959e30) }}, {{ SC_(2.0), SC_(-5.8207660913467407226562500000000000e-11), SC_(1.0141204801825835211973625643005596e31) }}, {{ SC_(2.0), SC_(-2.9103830456733703613281250000000000e-11), SC_(8.1129638414606681695789005144061596e31) }}, {{ SC_(2.0), SC_(-1.4551915228366851806640625000000000e-11), SC_(6.4903710731685345356631204115250960e32) }}, {{ SC_(2.0), SC_(-7.2759576141834259033203125000000000e-12), SC_(5.1922968585348276285304963292200936e33) }}, {{ SC_(2.0), SC_(-3.6379788070917129516601562500000000e-12), SC_(4.1538374868278621028243970633760766e34) }}, {{ SC_(2.0), SC_(-1.8189894035458564758300781250000000e-12), SC_(3.3230699894622896822595176507008614e35) }}, {{ SC_(2.0), SC_(-9.0949470177292823791503906250000000e-13), SC_(2.6584559915698317458076141205606891e36) }}, {{ SC_(2.0), SC_(-4.5474735088646411895751953125000000e-13), SC_(2.1267647932558653966460912964485513e37) }}, {{ SC_(2.0), SC_(-2.2737367544323205947875976562500000e-13), SC_(1.7014118346046923173168730371588411e38) }}, {{ SC_(2.0), SC_(-1.1368683772161602973937988281250000e-13), SC_(1.3611294676837538538534984297270728e39) }}, {{ SC_(2.0), SC_(-5.6843418860808014869689941406250000e-14), SC_(1.0889035741470030830827987437816583e40) }}, {{ SC_(2.0), SC_(-2.8421709430404007434844970703125000e-14), SC_(8.7112285931760246646623899502532662e40) }}, {{ SC_(2.0), SC_(-1.4210854715202003717422485351562500e-14), SC_(6.9689828745408197317299119602026130e41) }}, {{ SC_(2.0), SC_(-7.1054273576010018587112426757812500e-15), SC_(5.5751862996326557853839295681620904e42) }}, {{ SC_(2.0), SC_(-3.5527136788005009293556213378906250e-15), SC_(4.4601490397061246283071436545296723e43) }}, {{ SC_(2.0), SC_(-1.7763568394002504646778106689453125e-15), SC_(3.5681192317648997026457149236237378e44) }}, {{ SC_(2.0), SC_(-8.8817841970012523233890533447265625e-16), SC_(2.8544953854119197621165719388989903e45) }}, {{ SC_(2.0), SC_(-4.4408920985006261616945266723632812e-16), SC_(2.2835963083295358096932575511191922e46) }}, {{ SC_(2.0), SC_(-2.2204460492503130808472633361816406e-16), SC_(1.8268770466636286477546060408953538e47) }}, {{ SC_(2.0), SC_(-1.1102230246251565404236316680908203e-16), SC_(1.4615016373309029182036848327162830e48) }}, {{ SC_(2.0), SC_(-5.5511151231257827021181583404541016e-17), SC_(1.1692013098647223345629478661730264e49) }}, {{ SC_(2.0), SC_(-2.7755575615628913510590791702270508e-17), SC_(9.3536104789177786765035829293842113e49) }}, {{ SC_(2.0), SC_(-1.3877787807814456755295395851135254e-17), SC_(7.4828883831342229412028663435073691e50) }}, {{ SC_(2.0), SC_(-6.9388939039072283776476979255676270e-18), SC_(5.9863107065073783529622930748058952e51) }}, {{ SC_(2.0), SC_(-3.4694469519536141888238489627838135e-18), SC_(4.7890485652059026823698344598447162e52) }}, {{ SC_(2.0), SC_(-1.7347234759768070944119244813919067e-18), SC_(3.8312388521647221458958675678757730e53) }}, {{ SC_(2.0), SC_(-8.6736173798840354720596224069595337e-19), SC_(3.0649910817317777167166940543006184e54) }}, {{ SC_(2.0), SC_(-4.3368086899420177360298112034797668e-19), SC_(2.4519928653854221733733552434404947e55) }}, {{ SC_(2.0), SC_(-2.1684043449710088680149056017398834e-19), SC_(1.9615942923083377386986841947523958e56) }}, {{ SC_(2.0), SC_(-1.0842021724855044340074528008699417e-19), SC_(1.5692754338466701909589473558019166e57) }}, {{ SC_(2.0), SC_(-5.4210108624275221700372640043497086e-20), SC_(1.2554203470773361527671578846415333e58) }}, {{ SC_(2.0), SC_(-2.7105054312137610850186320021748543e-20), SC_(1.0043362776618689222137263077132266e59) }}, {{ SC_(2.0), SC_(-1.3552527156068805425093160010874271e-20), SC_(8.0346902212949513777098104617058130e59) }}, {{ SC_(2.0), SC_(-6.7762635780344027125465800054371357e-21), SC_(6.4277521770359611021678483693646504e60) }}, {{ SC_(2.0), SC_(-3.3881317890172013562732900027185678e-21), SC_(5.1422017416287688817342786954917203e61) }},
      {{ SC_(3.0), SC_(-9.5367431640625000000000000000000000e-7), SC_(7.2535549176877750482370624939631357e24) }}, {{ SC_(3.0), SC_(-4.7683715820312500000000000000000000e-7), SC_(1.1605687868300440077179290249395127e26) }}, {{ SC_(3.0), SC_(-2.3841857910156250000000000000000000e-7), SC_(1.8569100589280704123486863424939453e27) }}, {{ SC_(3.0), SC_(-1.1920928955078125000000000000000000e-7), SC_(2.9710560942849126597578981382493942e28) }}, {{ SC_(3.0), SC_(-5.9604644775390625000000000000000000e-8), SC_(4.7536897508558602556126370202249394e29) }}, {{ SC_(3.0), SC_(-2.9802322387695312500000000000000000e-8), SC_(7.6059036013693764089802192322624939e30) }}, {{ SC_(3.0), SC_(-1.4901161193847656250000000000000000e-8), SC_(1.2169445762191002254368350771610249e32) }}, {{ SC_(3.0), SC_(-7.4505805969238281250000000000000000e-9), SC_(1.9471113219505603606989361234575425e33) }}, {{ SC_(3.0), SC_(-3.7252902984619140625000000000000000e-9), SC_(3.1153781151208965771182977975320582e34) }}, {{ SC_(3.0), SC_(-1.8626451492309570312500000000000000e-9), SC_(4.9846049841934345233892764760512922e35) }}, {{ SC_(3.0), SC_(-9.3132257461547851562500000000000000e-10), SC_(7.9753679747094952374228423616820675e36) }}, {{ SC_(3.0), SC_(-4.6566128730773925781250000000000000e-10), SC_(1.2760588759535192379876547778691308e38) }}, {{ SC_(3.0), SC_(-2.3283064365386962890625000000000000e-10), SC_(2.0416942015256307807802476445906093e39) }}, {{ SC_(3.0), SC_(-1.1641532182693481445312500000000000e-10), SC_(3.2667107224410092492483962313449748e40) }}, {{ SC_(3.0), SC_(-5.8207660913467407226562500000000000e-11), SC_(5.2267371559056147987974339701519597e41) }}, {{ SC_(3.0), SC_(-2.9103830456733703613281250000000000e-11), SC_(8.3627794494489836780758943522431356e42) }}, {{ SC_(3.0), SC_(-1.4551915228366851806640625000000000e-11), SC_(1.3380447119118373884921430963589017e44) }}, {{ SC_(3.0), SC_(-7.2759576141834259033203125000000000e-12), SC_(2.1408715390589398215874289541742427e45) }}, {{ SC_(3.0), SC_(-3.6379788070917129516601562500000000e-12), SC_(3.4253944624943037145398863266787883e46) }}, {{ SC_(3.0), SC_(-1.8189894035458564758300781250000000e-12), SC_(5.4806311399908859432638181226860613e47) }}, {{ SC_(3.0), SC_(-9.0949470177292823791503906250000000e-13), SC_(8.7690098239854175092221089962976981e48) }}, {{ SC_(3.0), SC_(-4.5474735088646411895751953125000000e-13), SC_(1.4030415718376668014755374394076317e50) }}, {{ SC_(3.0), SC_(-2.2737367544323205947875976562500000e-13), SC_(2.2448665149402668823608599030522107e51) }}, {{ SC_(3.0), SC_(-1.1368683772161602973937988281250000e-13), SC_(3.5917864239044270117773758448835371e52) }}, {{ SC_(3.0), SC_(-5.6843418860808014869689941406250000e-14), SC_(5.7468582782470832188438013518136594e53) }}, {{ SC_(3.0), SC_(-2.8421709430404007434844970703125000e-14), SC_(9.1949732451953331501500821629018551e54) }}, {{ SC_(3.0), SC_(-1.4210854715202003717422485351562500e-14), SC_(1.4711957192312533040240131460642968e56) }}, {{ SC_(3.0), SC_(-7.1054273576010018587112426757812500e-15), SC_(2.3539131507700052864384210337028749e57) }}, {{ SC_(3.0), SC_(-3.5527136788005009293556213378906250e-15), SC_(3.7662610412320084583014736539245998e58) }}, {{ SC_(3.0), SC_(-1.7763568394002504646778106689453125e-15), SC_(6.0260176659712135332823578462793598e59) }}, {{ SC_(3.0), SC_(-8.8817841970012523233890533447265625e-16), SC_(9.6416282655539416532517725540469756e60) }}, {{ SC_(3.0), SC_(-4.4408920985006261616945266723632812e-16), SC_(1.5426605224886306645202836086475161e62) }}, {{ SC_(3.0), SC_(-2.2204460492503130808472633361816406e-16), SC_(2.4682568359818090632324537738360258e63) }}, {{ SC_(3.0), SC_(-1.1102230246251565404236316680908203e-16), SC_(3.9492109375708945011719260381376412e64) }}, {{ SC_(3.0), SC_(-5.5511151231257827021181583404541016e-17), SC_(6.3187375001134312018750816610202259e65) }}, {{ SC_(3.0), SC_(-2.7755575615628913510590791702270508e-17), SC_(1.0109980000181489923000130657632362e67) }}, {{ SC_(3.0), SC_(-1.3877787807814456755295395851135254e-17), SC_(1.6175968000290383876800209052211778e68) }}, {{ SC_(3.0), SC_(-6.9388939039072283776476979255676270e-18), SC_(2.5881548800464614202880334483538845e69) }}, {{ SC_(3.0), SC_(-3.4694469519536141888238489627838135e-18), SC_(4.1410478080743382724608535173662153e70) }}, {{ SC_(3.0), SC_(-1.7347234759768070944119244813919067e-18), SC_(6.6256764929189412359373656277859444e71) }}, {{ SC_(3.0), SC_(-8.6736173798840354720596224069595337e-19), SC_(1.0601082388670305977499785004457511e73) }}, {{ SC_(3.0), SC_(-4.3368086899420177360298112034797668e-19), SC_(1.6961731821872489563999656007132018e74) }}, {{ SC_(3.0), SC_(-2.1684043449710088680149056017398834e-19), SC_(2.7138770914995983302399449611411228e75) }}, {{ SC_(3.0), SC_(-1.0842021724855044340074528008699417e-19), SC_(4.3422033463993573283839119378257965e76) }}, {{ SC_(3.0), SC_(-5.4210108624275221700372640043497086e-20), SC_(6.9475253542389717254142591005212745e77) }}, {{ SC_(3.0), SC_(-2.7105054312137610850186320021748543e-20), SC_(1.1116040566782354760662814560834039e79) }}, {{ SC_(3.0), SC_(-1.3552527156068805425093160010874271e-20), SC_(1.7785664906851767617060503297334463e80) }}, {{ SC_(3.0), SC_(-6.7762635780344027125465800054371357e-21), SC_(2.8457063850962828187296805275735140e81) }}, {{ SC_(3.0), SC_(-3.3881317890172013562732900027185678e-21), SC_(4.5531302161540525099674888441176224e82) }},
      {{ SC_(124.0), SC_(-1.500), SC_(-2.7249890458922632375522129837125443e157) }}, {{ SC_(124.0), SC_(-2.500), SC_(-1.4769313224896369911103029786543928e139) }}, {{ SC_(124.0), SC_(-3.500), SC_(-3.3597086916687478281510460837686247e125) }}, {{ SC_(124.0), SC_(-4.500), SC_(-4.2907148995777554014718947851654811e114) }}, {{ SC_(124.0), SC_(-5.500), SC_(-3.6618139249627692553752809354502259e105) }}, {{ SC_(124.0), SC_(-6.500), SC_(-6.2403362354301509400433157402892941e97) }}, {{ SC_(124.0), SC_(-7.500), SC_(-1.0011531735317576688720095395178850e91) }}, {{ SC_(124.0), SC_(-8.500), SC_(-9.1710019415963060853316564350633528e84) }}, {{ SC_(124.0), SC_(-9.500), SC_(-3.3822714836539651302726236480037681e79) }}, {{ SC_(124.0), SC_(-10.50), SC_(-3.8962670995991768118677582868234531e74) }}, {{ SC_(124.0), SC_(-11.50), SC_(-1.1591591018132801852584224474253891e70) }}, {{ SC_(124.0), SC_(-12.50), SC_(-7.6948850968760327095578155247202587e65) }}, {{ SC_(124.0), SC_(-13.50), SC_(-1.0161777096507539745875317371106118e62) }}, {{ SC_(124.0), SC_(-14.50), SC_(-2.4354484351409547531920463990216679e58) }}, {{ SC_(124.0), SC_(-15.50), SC_(-9.8321990426222594076174716964878395e54) }}, {{ SC_(124.0), SC_(-16.50), SC_(-6.2882245279340391405004802996498744e51) }}, {{ SC_(124.0), SC_(-17.50), SC_(-6.0534504786126893203140318104359656e48) }}, {{ SC_(124.0), SC_(-18.50), SC_(-8.4019112566928602772066808945728022e45) }}, {{ SC_(124.0), SC_(-19.50), SC_(-1.6209265582255125824031570171026802e43) }}, {{ SC_(124.0), SC_(-20.50), SC_(-4.2125071484047517848318042867661534e40) }},
      {{ SC_(125.0), SC_(-1.500), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-2.500), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-3.500), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-4.500), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-5.500), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-6.500), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-7.500), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-8.500), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-9.500), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-10.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-11.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-12.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-13.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-14.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-15.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-16.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-17.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-18.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-19.50), SC_(3.2032092294989705945080639466924596e247) }}, {{ SC_(125.0), SC_(-20.50), SC_(3.2032092294989705945080639466924596e247) }},
      {{ SC_(125.0), SC_(-1.5000002384185791015625000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-2.5000002384185791015625000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-3.5000002384185791015625000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-4.5000002384185791015625000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-5.5000002384185791015625000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-6.5000002384185791015625000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-7.5000002384185791015625000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-8.5000002384185791015625000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-9.5000002384185791015625000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-10.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-11.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-12.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-13.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-14.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-15.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-16.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-17.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-18.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-19.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-20.500000238418579101562500000000000), SC_(3.2032092353263025675858789018286326e247) }},
      {{ SC_(125.0), SC_(-1.4999997615814208984375000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-2.4999997615814208984375000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-3.4999997615814208984375000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-4.4999997615814208984375000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-5.4999997615814208984375000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-6.4999997615814208984375000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-7.4999997615814208984375000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-8.4999997615814208984375000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-9.4999997615814208984375000000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-10.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-11.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-12.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-13.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-14.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-15.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-16.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-17.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-18.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-19.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }}, {{ SC_(125.0), SC_(-20.499999761581420898437500000000000), SC_(3.2032092353263025675858789018286326e247) }},
   } };
   std::array<std::array<value_type, 3>, 103> neg_double_data =
   { {
      { { SC_(124.0), SC_(-1.4999997615814208984375000000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-2.4999997615814208984375000000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-3.4999997615814208984375000000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-4.4999997615814208984375000000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-5.4999997615814208984375000000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-6.4999997615814208984375000000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-7.4999997615814208984375000000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-8.4999997615814208984375000000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-9.4999997615814208984375000000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-10.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-11.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-12.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-13.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-14.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-15.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-16.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-17.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-18.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-19.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-20.499999761581420898437500000000000), SC_(7.6370459352527012474320227016934851e240) } },
      { { SC_(124.0), SC_(-1.5000002384185791015625000000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-2.5000002384185791015625000000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-3.5000002384185791015625000000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-4.5000002384185791015625000000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-5.5000002384185791015625000000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-6.5000002384185791015625000000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-7.5000002384185791015625000000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-8.5000002384185791015625000000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-9.5000002384185791015625000000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-10.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-11.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-12.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-13.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-14.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-15.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-16.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-17.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-18.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-19.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } }, { { SC_(124.0), SC_(-20.500000238418579101562500000000000), SC_(-7.6370459352527012474320227016934851e240) } },
      { { SC_(1.0), SC_(-0.500), SC_(8.9348022005446793094172454999380756) } }, { { SC_(2.0), SC_(-0.500), SC_(-0.82879664423431999559633426116029987) } }, { { SC_(3.0), SC_(-0.500), SC_(193.40909103400243723644033268870511) } }, { { SC_(4.0), SC_(-0.500), SC_(-3.4742498266672251905359219240334210) } }, { { SC_(5.0), SC_(-0.500), SC_(15371.113548602435496241755549219359) } }, { { SC_(6.0), SC_(-0.500), SC_(-43.457923803023286231087958265415698) } }, { { SC_(7.0), SC_(-0.500), SC_(2.5806802181855980649694862685313201e6) } }, { { SC_(8.0), SC_(-0.500), SC_(-1059.9617600414264025178879353865365) } }, { { SC_(9.0), SC_(-0.500), SC_(7.4318457238509742722370782665375996e8) } }, { { SC_(10.), SC_(-0.500), SC_(-42108.858768975491796771277214753871) } }, { { SC_(11.), SC_(-0.500), SC_(3.2699873393475880004602936491973290e11) } }, { { SC_(12.), SC_(-0.500), SC_(-2.4644776094268285475780118302319831e6) } }, { { SC_(13.), SC_(-0.500), SC_(2.0404703892185195041277151739878551e14) } }, { { SC_(14.), SC_(-0.500), SC_(-1.9917964814708338071590970890436861e8) } }, { { SC_(15.), SC_(-0.500), SC_(1.7139949675391451725203269743703670e17) } }, { { SC_(16.), SC_(-0.500), SC_(-2.1239385116043117742696464301184360e10) } }, { { SC_(17.), SC_(-0.500), SC_(1.8648265054229230657699496051237022e20) } }, { { SC_(18.), SC_(-0.500), SC_(-2.8882421804274914449348671586694211e12) } }, { { SC_(19.), SC_(-0.500), SC_(2.5510826564916635359511595679555294e23) } }, { { SC_(20.), SC_(-0.500), SC_(-4.8777294946260987363553987421237456e14) } }, { { SC_(21.), SC_(-0.500), SC_(4.2858188623596794335838558079810850e26) } },
      { { SC_(1.0), SC_(-0.50000023841857910156250000000000000), SC_(8.9348023981506946089014375825505155) } }, { { SC_(2.0), SC_(-0.50000023841857910156250000000000000), SC_(-0.82884275655508842604754532179574729) } }, { { SC_(3.0), SC_(-0.50000023841857910156250000000000000), SC_(193.40909186276501767728859938348815) } }, { { SC_(4.0), SC_(-0.50000023841857910156250000000000000), SC_(-3.4779145857199327369685563062655118) } }, { { SC_(5.0), SC_(-0.50000023841857910156250000000000000), SC_(15371.113559036959283359095676640936) } }, { { SC_(6.0), SC_(-0.50000023841857910156250000000000000), SC_(-44.073205913790411411330389206216025) } }, { { SC_(7.0), SC_(-0.50000023841857910156250000000000000), SC_(2.5806802184594352176701819301926979e6) } }, { { SC_(8.0), SC_(-0.50000023841857910156250000000000000), SC_(-1237.1507698016190706446362670415721) } }, { { SC_(9.0), SC_(-0.50000023841857910156250000000000000), SC_(7.4318457240443082449903207673842183e8) } }, { { SC_(10.), SC_(-0.50000023841857910156250000000000000), SC_(-120071.43228224150936593763797588950) } }, { { SC_(11.), SC_(-0.50000023841857910156250000000000000), SC_(3.2699873394114574294629126382636465e11) } }, { { SC_(12.), SC_(-0.50000023841857910156250000000000000), SC_(-5.1113082699448800419004327980237078e7) } }, { { SC_(13.), SC_(-0.50000023841857910156250000000000000), SC_(2.0404703892677090523475108203530504e14) } }, { { SC_(14.), SC_(-0.50000023841857910156250000000000000), SC_(-4.1064004123360078851054877806228941e10) } }, { { SC_(15.), SC_(-0.50000023841857910156250000000000000), SC_(1.7139949675921973682361201187974158e17) } }, { { SC_(16.), SC_(-0.50000023841857910156250000000000000), SC_(-4.4482167955078907488604509935932075e13) } }, { { SC_(17.), SC_(-0.50000023841857910156250000000000000), SC_(1.8648265054954360818722434334340996e20) } }, { { SC_(18.), SC_(-0.50000023841857910156250000000000000), SC_(-6.0825438456286690418846111378451293e16) } }, { { SC_(19.), SC_(-0.50000023841857910156250000000000000), SC_(2.5510826566134749972709942636451521e23) } }, { { SC_(20.), SC_(-0.50000023841857910156250000000000000), SC_(-1.0218237211995580504353583819436291e20) } }, { { SC_(21.), SC_(-0.50000023841857910156250000000000000), SC_(4.2858188626062237162861505116019247e26) } },
      { { SC_(1.0), SC_(-0.49999976158142089843750000000000000), SC_(8.9348020029496580439061915046847633) } }, { { SC_(2.0), SC_(-0.49999976158142089843750000000000000), SC_(-0.82875053191374905338324754695139008) } }, { { SC_(3.0), SC_(-0.49999976158142089843750000000000000), SC_(193.40909020611360344139301017638823) } }, { { SC_(4.0), SC_(-0.49999976158142089843750000000000000), SC_(-3.4705850676169879410688441586536334) } }, { { SC_(5.0), SC_(-0.49999976158142089843750000000000000), SC_(15371.113538314606395712740903053532) } }, { { SC_(6.0), SC_(-0.49999976158142089843750000000000000), SC_(-42.842641692316412901148012739850716) } }, { { SC_(7.0), SC_(-0.49999976158142089843750000000000000), SC_(2.5806802179540060642078552424908302e6) } }, { { SC_(8.0), SC_(-0.49999976158142089843750000000000000), SC_(-882.77275028362734588789574976519641) } }, { { SC_(9.0), SC_(-0.49999976158142089843750000000000000), SC_(7.4318457238435175594844592942103986e8) } }, { { SC_(10.), SC_(-0.49999976158142089843750000000000000), SC_(35853.714744150436439369297880163123) } }, { { SC_(11.), SC_(-0.49999976158142089843750000000000000), SC_(3.2699873393997058844655604408250598e11) } }, { { SC_(12.), SC_(-0.49999976158142089843750000000000000), SC_(4.6184127480583821271680125577145403e7) } }, { { SC_(13.), SC_(-0.49999976158142089843750000000000000), SC_(2.0404703892667592897735663251491103e14) } }, { { SC_(14.), SC_(-0.49999976158142089843750000000000000), SC_(4.0665644827064704770358560360332387e10) } }, { { SC_(15.), SC_(-0.49999976158142089843750000000000000), SC_(1.7139949675920960909557128308150980e17) } }, { { SC_(16.), SC_(-0.49999976158142089843750000000000000), SC_(4.4439689184846657075559083382657063e13) } }, { { SC_(17.), SC_(-0.49999976158142089843750000000000000), SC_(1.8648265054954223096603082369745267e20) } }, { { SC_(18.), SC_(-0.49999976158142089843750000000000000), SC_(6.0819661971925807709274166342293111e16) } }, { { SC_(19.), SC_(-0.49999976158142089843750000000000000), SC_(2.5510826566134726713883235580466596e23) } }, { { SC_(20.), SC_(-0.49999976158142089843750000000000000), SC_(1.0218139657405687413065653078640133e20) } }, { { SC_(21.), SC_(-0.49999976158142089843750000000000000), SC_(4.2858188626062232387116210610555278e26) } },
   } };

   std::array<std::array<value_type, 3>, 90> small_data =
   { {
      {{ SC_(0.0), SC_(0.12500000000000000000), SC_(-8.3884926632958548678027429230863430) }}, {{ SC_(0.0), SC_(0.062500000000000000000), SC_(-16.478853490060104366505723782801995) }}, {{ SC_(0.0), SC_(0.031250000000000000000), SC_(-32.526953288606118111369026129964135) }}, {{ SC_(0.0), SC_(0.015625000000000000000), SC_(-64.551802973167856670965920212624596) }}, {{ SC_(0.0), SC_(0.0078125000000000000000), SC_(-128.56443747297672763722041223143322) }}, {{ SC_(0.0), SC_(0.0039062500000000000000), SC_(-256.57080841886464838984737508407824) }}, {{ SC_(0.0), SC_(0.0019531250000000000000), SC_(-512.57400748048652546824732749592750) }}, {{ SC_(0.0), SC_(0.00097656250000000000000), SC_(-1024.5756104293406219086220979096446) }}, {{ SC_(0.0), SC_(0.00048828125000000000000), SC_(-2048.5764127609059633822989920937770) }}, {{ SC_(0.0), SC_(0.00024414062500000000000), SC_(-4096.5768141413027972625364884707221) }}, {{ SC_(0.0), SC_(0.00012207031250000000000), SC_(-8192.5770148851960259755970875167303) }}, {{ SC_(0.0), SC_(0.000061035156250000000000), SC_(-16384.577115270571506673278270921248) }}, {{ SC_(0.0), SC_(0.000030517578125000000000), SC_(-32768.577165466617109315191527852551) }}, {{ SC_(0.0), SC_(0.000015258789062500000000), SC_(-65536.577190565479456940587995127301) }}, {{ SC_(0.0), SC_(7.6293945312500000000e-6), SC_(-131072.57720311512052742189906385878) }}, {{ SC_(0.0), SC_(3.8146972656250000000e-6), SC_(-262144.57720938999353809133982546559) }}, {{ SC_(0.0), SC_(1.9073486328125000000e-6), SC_(-524288.57721252744316244096483807868) }}, {{ SC_(0.0), SC_(9.5367431640625000000e-7), SC_(-1.0485765772140961712543892173131386e6) }},
      {{ SC_(1.0), SC_(0.1250000000), SC_(65.388133444988034473142999334395961) }}, {{ SC_(1.0), SC_(0.06250000000), SC_(257.50642004291541426394984152786018) }}, {{ SC_(1.0), SC_(0.03125000000), SC_(1025.5728544782377088851896549789956) }}, {{ SC_(1.0), SC_(0.01562500000), SC_(4097.6081469812325471140472931934309) }}, {{ SC_(1.0), SC_(0.007812500000), SC_(16385.626348148031663597978251925972) }}, {{ SC_(1.0), SC_(0.003906250000), SC_(65537.635592296074077546680509110271) }}, {{ SC_(1.0), SC_(0.001953125000), SC_(262145.64025088744769438583827382756) }}, {{ SC_(1.0), SC_(0.0009765625000), SC_(1.0485776425893921526170408061678298e6) }},
      {{ SC_(2.0), SC_(0.1250000000), SC_(-1025.7533381181356825956689300565174) }}, {{ SC_(2.0), SC_(0.06250000000), SC_(-8194.0423055503627202407284588855458) }}, {{ SC_(2.0), SC_(0.03125000000), SC_(-65538.212736402744663973874571262931) }}, {{ SC_(2.0), SC_(0.01562500000), SC_(-524290.30560802491992997062359624105) }}, {{ SC_(2.0), SC_(0.007812500000), SC_(-4.1943063541297826472489756741474152e6) }}, {{ SC_(2.0), SC_(0.003906250000), SC_(-3.3554434378935516909394862712904232e7) }}, {{ SC_(2.0), SC_(0.001953125000), SC_(-2.6843545839147764655287988662280398e8) }}, {{ SC_(2.0), SC_(0.0009765625000), SC_(-2.1474836503977839163960630063909364e9) }},
      {{ SC_(3.0), SC_(0.1250000000), SC_(24580.143419063566218511004446647010) }}, {{ SC_(3.0), SC_(0.06250000000), SC_(393221.15036999967974263906424748910) }}, {{ SC_(3.0), SC_(0.03125000000), SC_(6.2914617723523498519444110540563165e6) }}, {{ SC_(3.0), SC_(0.01562500000), SC_(1.0066330211954465631968224525194028e8) }}, {{ SC_(3.0), SC_(0.007812500000), SC_(1.6106127423031841473495039368910042e9) }}, {{ SC_(3.0), SC_(0.003906250000), SC_(2.5769803782397651667126674423858834e10) }}, {{ SC_(3.0), SC_(0.001953125000), SC_(4.1231686042244556536661660289768233e11) }}, {{ SC_(3.0), SC_(0.0009765625000), SC_(6.5970697666624696945083422683684831e12) }},
      {{ SC_(4.0), SC_(0.1250000000), SC_(-786445.98543106378579320120709638297) }}, {{ SC_(4.0), SC_(0.06250000000), SC_(-2.5165842491343080297812493001330106e7) }}, {{ SC_(4.0), SC_(0.03125000000), SC_(-8.0530638940150780088628643019449463e8) }}, {{ SC_(4.0), SC_(0.01562500000), SC_(-2.5769803799064252508878172105001377e10) }}, {{ SC_(4.0), SC_(0.007812500000), SC_(-8.2463372085595426712260437166652246e11) }}, {{ SC_(4.0), SC_(0.003906250000), SC_(-2.6388279066648414875708308182697262e13) }}, {{ SC_(4.0), SC_(0.001953125000), SC_(-8.4442493013199264920484069629201316e14) }}, {{ SC_(4.0), SC_(0.0009765625000), SC_(-2.7021597764223000767391638642998277e16) }},
      {{ SC_(5.0), SC_(0.1250000000), SC_(3.1457340659602645662942019557433307e7) }}, {{ SC_(5.0), SC_(0.06250000000), SC_(2.0132660051504893647288429933363318e9) }}, {{ SC_(5.0), SC_(0.03125000000), SC_(1.2884901898167237155557768979087387e11) }}, {{ SC_(5.0), SC_(0.01562500000), SC_(8.2463372084313301694493047619778287e12) }}, {{ SC_(5.0), SC_(0.007812500000), SC_(5.2776558133259656048321206289799569e14) }}, {{ SC_(5.0), SC_(0.003906250000), SC_(3.3776997205278839283396175959111085e16) }}, {{ SC_(5.0), SC_(0.001953125000), SC_(2.1617278211378382006727785506307164e18) }}, {{ SC_(5.0), SC_(0.0009765625000), SC_(1.3835058055282163724137457865337740e20) }},
      {{ SC_(8.0), SC_(0.1250000000), SC_(-5.4116588069756277838328695722389595e12) }}, {{ SC_(8.0), SC_(0.06250000000), SC_(-2.7707693020189462516270216110672946e15) }}, {{ SC_(8.0), SC_(0.03125000000), SC_(-1.4186338826217368769684713903764732e18) }}, {{ SC_(8.0), SC_(0.01562500000), SC_(-7.2634054790231363002428528775599797e20) }}, {{ SC_(8.0), SC_(0.007812500000), SC_(-3.7188636052598456061623085548707189e23) }}, {{ SC_(8.0), SC_(0.003906250000), SC_(-1.9040581658930409501626172937578262e26) }}, {{ SC_(8.0), SC_(0.001953125000), SC_(-9.7487778093723696648306072338399010e28) }}, {{ SC_(8.0), SC_(0.0009765625000), SC_(-4.9913742383986532683932688751727976e31) }},
      {{ SC_(15.0), SC_(0.1250000000), SC_(3.6807761227792200230957850246407904e26) }}, {{ SC_(15.0), SC_(0.06250000000), SC_(2.4122334398245883325511911077327284e31) }}, {{ SC_(15.0), SC_(0.03125000000), SC_(1.5808813071234422095882610857505513e36) }}, {{ SC_(15.0), SC_(0.01562500000), SC_(1.0360463734364190864757622613687259e41) }}, {{ SC_(15.0), SC_(0.007812500000), SC_(6.7898335129529161251275555560392101e45) }}, {{ SC_(15.0), SC_(0.003906250000), SC_(4.4497852910488231117635948092058560e50) }}, {{ SC_(15.0), SC_(0.001953125000), SC_(2.9162112883417567145253894941611498e55) }}, {{ SC_(15.0), SC_(0.0009765625000), SC_(1.9111682299276536804313592588934511e60) }},
      {{ SC_(22.0), SC_(0.1250000000), SC_(-6.6349292044725783891012472579673570e41) }}, {{ SC_(22.0), SC_(0.06250000000), SC_(-5.5657820204072306855435555719642654e48) }}, {{ SC_(22.0), SC_(0.03125000000), SC_(-4.6689163582644258586596154617085763e55) }}, {{ SC_(22.0), SC_(0.01562500000), SC_(-3.9165709114267828873358919539012256e62) }}, {{ SC_(22.0), SC_(0.007812500000), SC_(-3.2854578080162002342968961931631453e69) }}, {{ SC_(22.0), SC_(0.003906250000), SC_(-2.7560417651987161415024817781137906e76) }}, {{ SC_(22.0), SC_(0.001953125000), SC_(-2.3119353999880071814336850663739568e83) }}, {{ SC_(22.0), SC_(0.0009765625000), SC_(-1.9393919791822596946232062017265105e90) }},
      {{ SC_(35.0), SC_(0.1250000000), SC_(3.3532982327901451846973629635910627e72) }}, {{ SC_(35.0), SC_(0.06250000000), SC_(2.3043689989709229438987285737704404e83) }}, {{ SC_(35.0), SC_(0.03125000000), SC_(1.5835503181594194718369731136519624e94) }}, {{ SC_(35.0), SC_(0.01562500000), SC_(1.0882074924904162473416628106826351e105) }}, {{ SC_(35.0), SC_(0.007812500000), SC_(7.4781049464136054012151819362261716e115) }}, {{ SC_(35.0), SC_(0.003906250000), SC_(5.1389145889443628300269064119650184e126) }}, {{ SC_(35.0), SC_(0.001953125000), SC_(3.5314352154325314429637711743107931e137) }}, {{ SC_(35.0), SC_(0.0009765625000), SC_(2.4267838013160699267233738410387795e148) }}
   } };
   using std::ldexp;

   std::array<std::array<value_type, 3>, 23> bug_cases = 
   { {
      {{ SC_(171.0), SC_(2.0), SC_(2.073093314165313149880140394410e257) }}, {{ SC_(171.0), SC_(5.0), SC_(7.42911976071332889749264626321716781e188) }},
      {{ SC_(166.0), SC_(2.0), SC_(-4.8129498903508823293044351695484095e247) }}, {{ SC_(166.0), SC_(3.0), SC_(-1.8843912448604502196243093626013895e218) }},
      {{ SC_(171.0), SC_(23.0), SC_(7.53143916217078889612817829861181739e74) }}, {{ SC_(168.0), SC_(150.0), SC_(-6.5266062780306068333215312257902920e-66) }},
      {{ SC_(169.0), SC_(202.0), SC_(9.2734049986021958613510169328055599e-88) }},
      {{ SC_(20.0), SC_(-9.5), SC_(-0.0010307637762451416598018081796345932)}}, {{ SC_(21.0), SC_(-9.5), SC_(4.2858188624279670465725116159418790e26) }}, {{ SC_(22.0), SC_(-9.5), SC_(-0.0041914420015886426448664611125724338) }}, {{ SC_(23.0), SC_(-9.5), SC_(8.6744973773084910367753904944787155e29) }}, {{ SC_(24.0), SC_(-9.5), SC_(-0.020482527998674199369359987617445420) }}, {{ SC_(25.0), SC_(-9.5), SC_(2.0818793705474855280713094147422278e33) }}, {{ SC_(26.0), SC_(-9.5), SC_(-0.11840299831136879082790399023048278) }}, {{ SC_(27.0), SC_(-9.5), SC_(5.8459172724952950454469355072783445e36) }}, {{ SC_(28.0), SC_(-9.5), SC_(-0.79896867888629450211348696225656872) }}, {{ SC_(29.0), SC_(-9.5), SC_(1.8987539301063980537054871851063348e40) }}, {{ SC_(30.0), SC_(-9.5), SC_(-6.2224465669723272001827279817965087) }},
      {{ SC_(1.0), ldexp(value_type(1), 120), SC_(7.5231638452626400509999138382223723e-37) }}, {{ SC_(2.0), ldexp(value_type(1), 120), SC_(-5.6597994242666952296931995568048699e-73) }}, {{ SC_(3.0), ldexp(value_type(1), 120), SC_(8.5159196800163014398201074324946177e-109) }}, {{ SC_(10.0), ldexp(value_type(1), 120), SC_(-2.1075031678562075551498983356333178e-356) }}, {{ SC_(15.0), ldexp(value_type(1), 120), SC_(1.2201582392961399809842378412624410e-531) }},
   } };

int main()
{
#include "expint_data.ipp"
#include "expint_small_data.ipp"
#include "expint_1_data.ipp"

   add_data(data1);
   add_data(big_data);
   add_data(neg_data);
   add_data(neg_double_data);
   add_data(small_data);
   add_data(bug_cases);

   unsigned data_total = data.size();

   screen_data([](const std::vector<double>& v){  return boost::math::polygamma(static_cast<int>(v[0]), v[1]);  }, [](const std::vector<double>& v){ return v[2];  });


#if defined(TEST_GSL) && !defined(COMPILER_COMPARISON_TABLES)
   screen_data([](const std::vector<double>& v){  return gsl_sf_psi_n(static_cast<int>(v[0]), v[1]);  }, [](const std::vector<double>& v){ return v[2];  });
#endif
#if defined(TEST_RMATH) && !defined(COMPILER_COMPARISON_TABLES)
   screen_data([](const std::vector<double>& v){  return ::psigamma(v[1], static_cast<int>(v[0]));  }, [](const std::vector<double>& v){ return v[2];  });
#endif

   unsigned data_used = data.size();
   std::string function = "polygamma[br](" + boost::lexical_cast<std::string>(data_used) + "/" + boost::lexical_cast<std::string>(data_total) + " tests selected)";
   std::string function_short = "polygamma";

   double time;

   time = exec_timed_test([](const std::vector<double>& v){  return boost::math::polygamma(static_cast<int>(v[0]), v[1]);  });
   std::cout << time << std::endl;
#if !defined(COMPILER_COMPARISON_TABLES) && (defined(TEST_GSL) || defined(TEST_RMATH))
   report_execution_time(time, std::string("Library Comparison with ") + std::string(compiler_name()) + std::string(" on ") + platform_name(), function, boost_name());
#endif
   report_execution_time(time, std::string("Compiler Comparison on ") + std::string(platform_name()), function_short, compiler_name() + std::string("[br]") + boost_name());
   //
   // Boost again, but with promotion to long double turned off:
   //
#if !defined(COMPILER_COMPARISON_TABLES)
   if(sizeof(long double) != sizeof(double))
   {
      time = exec_timed_test([](const std::vector<double>& v){  return boost::math::polygamma(static_cast<int>(v[0]), v[1], boost::math::policies::make_policy(boost::math::policies::promote_double<false>()));  });
      std::cout << time << std::endl;
#if !defined(COMPILER_COMPARISON_TABLES) && (defined(TEST_GSL) || defined(TEST_RMATH))
      report_execution_time(time, std::string("Library Comparison with ") + std::string(compiler_name()) + std::string(" on ") + platform_name(), function, boost_name() + "[br]promote_double<false>");
#endif
      report_execution_time(time, std::string("Compiler Comparison on ") + std::string(platform_name()), function_short, compiler_name() + std::string("[br]") + boost_name() + "[br]promote_double<false>");
   }
#endif


#if defined(TEST_GSL) && !defined(COMPILER_COMPARISON_TABLES)
   time = exec_timed_test([](const std::vector<double>& v){  return gsl_sf_psi_n(static_cast<int>(v[0]), v[1]);  });
   std::cout << time << std::endl;
   report_execution_time(time, std::string("Library Comparison with ") + std::string(compiler_name()) + std::string(" on ") + platform_name(), function, "GSL " GSL_VERSION);
#endif
#if defined(TEST_RMATH) && !defined(COMPILER_COMPARISON_TABLES)
   time = exec_timed_test([](const std::vector<double>& v){  return ::psigamma(v[1], static_cast<int>(v[0]));  });
   std::cout << time << std::endl;
   report_execution_time(time, std::string("Library Comparison with ") + std::string(compiler_name()) + std::string(" on ") + platform_name(), function, "Rmath "  R_VERSION_STRING);
#endif

   return 0;
}

