<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<title>Special Function and Distribution Performance Report</title>
<link rel="stylesheet" href="boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="index.html" title="Special Function and Distribution Performance Report">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav"></div>
<div class="article">
<div class="titlepage">
<div>
<div><h2 class="title">
<a name="special_function_and_distributio"></a>Special Function and Distribution Performance Report</h2></div>
<div><div class="legalnotice">
<a name="special_function_and_distributio.legal"></a><p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></div>
</div>
<hr>
</div>
<div class="toc">
<p><b>Table of Contents</b></p>
<dl class="toc">
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Compiler_Comparison_on_Windows_x64">Compiler
    Comparison on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Compiler_Option_Comparison_on_Windows_x64">Compiler
    Option Comparison on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64">Distribution
    performance comparison for different performance options with GNU C++ version
    9.2.0 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64">Distribution
    performance comparison for different performance options with Microsoft Visual
    C++ version 14.2 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64">Distribution
    performance comparison with GNU C++ version 9.2.0 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64">Distribution
    performance comparison with Microsoft Visual C++ version 14.2 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64">Library
    Comparison with GNU C++ version 9.2.0 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64">Library
    Comparison with Microsoft Visual C++ version 14.2 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_">Polynomial
    Arithmetic (GNU C++ version 9.2.0, Windows x64)</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_">Polynomial
    Arithmetic (Microsoft Visual C++ version 14.2, Windows x64)</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64">Polynomial
    Method Comparison with GNU C++ version 9.2.0 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64">Polynomial
    Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64">Rational
    Method Comparison with GNU C++ version 9.2.0 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64">Rational
    Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64">gcd
    method comparison with GNU C++ version 9.2.0 on Windows x64</a></span></dt>
<dt><span class="section"><a href="index.html#special_function_and_distributio.section_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64">gcd
    method comparison with Microsoft Visual C++ version 14.2 on Windows x64</a></span></dt>
</dl>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Compiler_Comparison_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Compiler_Comparison_on_Windows_x64" title="Compiler Comparison on Windows x64">Compiler
    Comparison on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Compiler_Comparison_on_Windows_x64.table_Compiler_Comparison_on_Windows_x64"></a><p class="title"><b>Table&#160;1.&#160;Compiler Comparison on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Compiler Comparison on Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                Microsoft Visual C++ version 14.2<br> boost 1.73
              </p>
            </th>
<th>
              <p>
                GNU C++ version 9.2.0<br> boost 1.73
              </p>
            </th>
<th>
              <p>
                GNU C++ version 9.2.0<br> boost 1.73<br> promote_double&lt;false&gt;
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                assoc_laguerre
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (179ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (137ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (127ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                assoc_legendre
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.76<br> (248ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (192ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (141ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                beta
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (123ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.62<br> (322ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.93<br> (237ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                beta (incomplete)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (470ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.95<br> (1385ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.58<br> (741ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cbrt
              </p>
            </td>
<td>
              <p>
                <span class="red">3.40<br> (51ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.67<br> (70ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (15ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_i
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (281ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.38<br> (949ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (387ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_i (integer order)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (195ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.06<br> (597ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (195ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_j
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (371ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.39<br> (886ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (499ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_j (integer order)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (123ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.92<br> (184ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (96ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_k
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (385ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">19.68<br> (6847ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (348ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_k (integer order)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (217ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">18.17<br> (3724ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (205ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_neumann
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (6696ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.76<br> (10032ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5715ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_neumann (integer order)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (158ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.20<br> (348ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.59<br> (252ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                digamma
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (20ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.45<br> (69ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.30<br> (46ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_1
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.57<br> (390ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (349ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (248ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_1 (complete)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.64<br> (77ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.64<br> (77ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (47ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_2
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.81<br> (702ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.50<br> (583ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (388ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_2 (complete)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.11<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.11<br> (57ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (27ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_3
              </p>
            </td>
<td>
              <p>
                <span class="red">3.47<br> (1381ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.68<br> (670ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (398ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_3 (complete)
              </p>
            </td>
<td>
              <p>
                <span class="red">inf<br> (802ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">-nan(ind)<br> (0ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">-nan(ind)<br> (0ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_rc
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (59ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.21<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (38ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_rd
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (271ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (260ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (206ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_rf
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (62ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.94<br> (95ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_rj
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (264ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.29<br> (414ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (181ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                erf
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (43ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.85<br> (61ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                erfc
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (54ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.76<br> (90ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (51ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                expint
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (27ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.41<br> (92ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.22<br> (60ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                expint (En)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (106ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.94<br> (206ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (137ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                expm1
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (11ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.36<br> (26ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gamma_p
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (303ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (605ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (355ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gamma_p_inv
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1266ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.85<br> (2341ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (1460ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gamma_q
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (294ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.10<br> (618ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (356ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gamma_q_inv
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1194ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.66<br> (1987ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (1357ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ibeta
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (512ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.63<br> (1344ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (673ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ibeta_inv
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1910ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.49<br> (4751ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (2822ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ibetac
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (525ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.60<br> (1365ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (668ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ibetac_inv
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1676ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.85<br> (4778ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.74<br> (2910ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                jacobi_cn
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (181ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.10<br> (561ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (362ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                jacobi_dn
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (203ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.03<br> (616ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.93<br> (392ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                jacobi_sn
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (202ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.81<br> (568ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.73<br> (350ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                laguerre
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (107ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (105ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                legendre
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (283ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (320ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (255ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                legendre Q
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (309ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.51<br> (466ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (354ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                lgamma
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (80ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.67<br> (214ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (160ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                log1p
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.07<br> (29ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (17ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                polygamma
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (4193ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.85<br> (7743ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.91<br> (8018ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                sph_bessel
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (668ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (975ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (661ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                sph_neumann
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (1138ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.96<br> (3153ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1064ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                tgamma
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (74ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.50<br> (259ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.14<br> (158ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                tgamma (incomplete)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (208ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.30<br> (478ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.64<br> (342ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                trigamma
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.83<br> (34ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (14ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                zeta
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (117ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.65<br> (310ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.89<br> (221ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Compiler_Option_Comparison_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Compiler_Option_Comparison_on_Windows_x64" title="Compiler Option Comparison on Windows x64">Compiler
    Option Comparison on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Compiler_Option_Comparison_on_Windows_x64.table_Compiler_Option_Comparison_on_Windows_x64"></a><p class="title"><b>Table&#160;2.&#160;Compiler Option Comparison on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Compiler Option Comparison on Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                cl /Od (x86 build)
              </p>
            </th>
<th>
              <p>
                cl /arch:sse2 /Ox (x86 build)
              </p>
            </th>
<th>
              <p>
                cl /Ox (x64 build)
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                boost::math::cbrt
              </p>
            </td>
<td>
              <p>
                <span class="red">5.05<br> (202ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.20<br> (48ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (40ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                boost::math::cyl_bessel_j (integer orders)
              </p>
            </td>
<td>
              <p>
                <span class="red">4.38<br> (530ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (121ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (124ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                boost::math::ibeta_inv
              </p>
            </td>
<td>
              <p>
                <span class="red">4.52<br> (8277ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (2042ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1833ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64" title="Distribution performance comparison for different performance options with GNU C++ version 9.2.0 on Windows x64">Distribution
    performance comparison for different performance options with GNU C++ version
    9.2.0 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64.table_Distribution_performance_comparison_for_different_performance_options_with_GNU_C_version_9_2_0_on_Windows_x64"></a><p class="title"><b>Table&#160;3.&#160;Distribution performance comparison for different performance options
      with GNU C++ version 9.2.0 on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Distribution performance comparison for different performance options
      with GNU C++ version 9.2.0 on Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                boost 1.73
              </p>
            </th>
<th>
              <p>
                Boost<br> promote_double&lt;false&gt;
              </p>
            </th>
<th>
              <p>
                Boost<br> promote_double&lt;false&gt;<br> digits10&lt;10&gt;
              </p>
            </th>
<th>
              <p>
                Boost<br> float<br> promote_float&lt;false&gt;
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                ArcSine (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (20ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.20<br> (64ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ArcSine (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.60<br> (8ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ArcSine (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (54ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (55ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.32<br> (362ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (205ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (183ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (156ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.44<br> (302ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (139ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (140ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (124ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.76<br> (1968ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (1383ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1118ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (1155ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.57<br> (959ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (350ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (341ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (269ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.39<br> (339ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (142ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.20<br> (171ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (148ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.20<br> (4255ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.42<br> (1884ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (1582ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1328ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (19ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (20ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.18<br> (54ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (4ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.67<br> (5ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (3ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (4ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (23ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (19ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (23ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.79<br> (953ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (529ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (434ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (341ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.82<br> (189ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (104ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (105ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (106ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.40<br> (1452ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.49<br> (901ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (717ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (605ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (29ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (30ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (29ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (54ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (51ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (50ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (52ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.89<br> (36ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (19ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (20ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (23ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (104ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (99ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (103ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (144ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (144ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (138ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (142ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (61ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (60ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (68ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.55<br> (668ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.58<br> (297ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (232ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (188ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.29<br> (291ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (135ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (129ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (127ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.17<br> (2215ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (1163ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1023ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (1090ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.94<br> (492ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (301ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (280ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (254ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (236ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (152ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (152ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (153ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.95<br> (1204ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (837ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (619ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (644ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (40ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (29ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (29ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (31ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (47ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.64<br> (36ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (24ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (49938ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (45127ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (45445ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (50682ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (53353ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (49364ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (47376ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.20<br> (57034ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (105555ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (132253ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (143254ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (179941ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.48<br> (1326ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (647ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (508ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (381ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.87<br> (217ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (126ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (116ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (117ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.81<br> (1852ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.57<br> (1035ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (800ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (658ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.95<br> (516ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (320ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (289ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (264ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.67<br> (256ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (167ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (161ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (153ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.94<br> (1268ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (884ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (652ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (666ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (172ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (172ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.82<br> (171ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (94ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (28ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (32ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (28ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (30ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.94<br> (2657ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.93<br> (2635ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.72<br> (2359ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1368ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (50ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (53ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (50ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (51ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (32ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (37ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.73<br> (176ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (127ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (131ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (102ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (87ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (86ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (90ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (116ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (94ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (95ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (100ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (47ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (47ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (47ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (47ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (49ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (34ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (38ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.95<br> (1158ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.66<br> (485ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (386ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (293ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.29<br> (307ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (135ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (134ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (136ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.81<br> (6154ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (2608ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2190ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (2752ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.62<br> (1450ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (806ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (708ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (553ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.58<br> (969ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (490ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (433ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (375ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.69<br> (37583ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (20369ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.81<br> (18498ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (10193ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.22<br> (4037ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.79<br> (3256ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (2332ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1819ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.58<br> (630ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (514ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (399ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (409ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.14<br> (33255ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.94<br> (20620ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (13388ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (10603ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.48<br> (1426ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (762ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (652ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (576ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.74<br> (1306ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (652ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (548ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (477ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.64<br> (22025ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (11560ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (9319ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (8356ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.75<br> (6473ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (3155ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.63<br> (2819ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1727ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.81<br> (4098ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (2040ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.42<br> (2066ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1456ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.80<br> (65926ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.87<br> (32431ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (23756ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17331ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.78<br> (135ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (116ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (93ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (76ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (48ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (59ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (57ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (64ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.45<br> (80ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (55ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (55ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (56ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (59ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (52ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (60ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (55ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (96ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (92ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (97ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (90ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (82ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (82ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (85ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.97<br> (254ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (134ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (150ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (129ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (171ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (106ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (111ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.16<br> (1128ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (641ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (657ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (523ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (47ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (38ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (39ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (41ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (70ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (66ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (70ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (48ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (24ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (31ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (669ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (632ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (549ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (479ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (173ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (159ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (156ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (136ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.15<br> (6968ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.82<br> (5903ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (4287ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (3237ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.15<br> (1151ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.98<br> (721ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.03<br> (741ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (365ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.12<br> (360ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (186ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (188ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (170ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (1461ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (1161ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (1099ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (859ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (96ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (77ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (85ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (74ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (164ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (139ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (160ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (148ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (133ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (134ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (119ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (123ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64" title="Distribution performance comparison for different performance options with Microsoft Visual C++ version 14.2 on Windows x64">Distribution
    performance comparison for different performance options with Microsoft Visual
    C++ version 14.2 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64.table_Distribution_performance_comparison_for_different_performance_options_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><p class="title"><b>Table&#160;4.&#160;Distribution performance comparison for different performance options
      with Microsoft Visual C++ version 14.2 on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Distribution performance comparison for different performance options
      with Microsoft Visual C++ version 14.2 on Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                boost 1.73
              </p>
            </th>
<th>
              <p>
                Boost<br> promote_double&lt;false&gt;<br> digits10&lt;10&gt;
              </p>
            </th>
<th>
              <p>
                Boost<br> float<br> promote_float&lt;false&gt;
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                ArcSine (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (43ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ArcSine (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ArcSine (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.20<br> (30ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.20<br> (30ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (158ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (140ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (115ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (104ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (109ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (96ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (806ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (833ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (753ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.80<br> (413ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.50<br> (344ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (230ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (127ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (178ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (126ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (2024ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (1727ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1466ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (27ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (32ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (8ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (11ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (9ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (19ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (26ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (676ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.59<br> (798ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (501ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (93ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (118ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (113ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.62<br> (1073ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (1211ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (662ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (11ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (10ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.50<br> (15ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (10ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (20ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (22ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (27ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (25ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (34ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (23ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (277ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (228ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (178ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (97ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (103ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (91ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (901ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (770ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (833ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (234ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (233ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (180ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (85ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (85ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (68ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.64<br> (640ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (501ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (390ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (16ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (19ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.85<br> (24ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.54<br> (20ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (20ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (244196ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (260490ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (271879ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (272497ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (282183ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (296020ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (308077ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (307365ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (326617ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.65<br> (584ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (459ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (353ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (78ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (75ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (57ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.68<br> (884ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (684ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (527ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (244ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (210ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (179ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (91ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.39<br> (93ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (67ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.58<br> (638ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (452ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (403ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (109ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (85ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (11ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.58<br> (1651ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (1209ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1048ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (19ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (79ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (76ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (82ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (35ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (30ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (28ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (61ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (59ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (54ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (15ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (19ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (22ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (15ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (20ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (20ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.69<br> (481ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (378ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (285ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (114ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (113ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (123ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (2651ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2186ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (2554ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.90<br> (735ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.54<br> (597ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (387ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.62<br> (489ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (471ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (302ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.35<br> (14689ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.10<br> (13173ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6263ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.84<br> (2643ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.45<br> (2087ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1438ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (290ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (272ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (213ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.49<br> (16692ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.59<br> (10665ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6699ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.54<br> (608ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (538ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (396ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (467ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (420ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (324ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.73<br> (9122ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (7572ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5271ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.65<br> (2375ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (1985ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1441ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.58<br> (1701ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.34<br> (1440ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1075ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.93<br> (23683ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (16597ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12284ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (89ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (82ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (106ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (28ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (29ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (21ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (44ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (43ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (44ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (34ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (35ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (27ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.50<br> (102ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (106ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (68ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.79<br> (50ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (38ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (28ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (76ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (77ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.43<br> (50ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (35ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (440ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (400ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (441ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (15ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (23ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (23ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (20ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (259ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (256ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (289ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (94ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (91ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (98ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.47<br> (2843ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1936ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (2391ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (429ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.85<br> (434ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (235ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (146ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (135ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (107ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (729ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.57<br> (749ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (476ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.62<br> (63ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.51<br> (59ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (39ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.75<br> (89ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.76<br> (90ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (51ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.63<br> (62ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (59ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (38ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64" title="Distribution performance comparison with GNU C++ version 9.2.0 on Windows x64">Distribution
    performance comparison with GNU C++ version 9.2.0 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64.table_Distribution_performance_comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><p class="title"><b>Table&#160;5.&#160;Distribution performance comparison with GNU C++ version 9.2.0 on Windows
      x64</b></p>
<div class="table-contents"><table class="table" summary="Distribution performance comparison with GNU C++ version 9.2.0 on Windows
      x64">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                boost 1.73
              </p>
            </th>
<th>
              <p>
                Boost<br> promote_double&lt;false&gt;
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                ArcSine (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (26ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ArcSine (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ArcSine (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (53ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.77<br> (362ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (205ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.17<br> (302ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (139ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.42<br> (1968ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1383ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.74<br> (959ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (350ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.39<br> (339ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (142ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.26<br> (4255ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1884ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (19ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (20ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (4ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (5ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (23ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.80<br> (953ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (529ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.82<br> (189ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (104ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (1452ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (901ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (29ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (54ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (51ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.89<br> (36ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (19ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (104ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (101ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (144ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (144ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (61ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.25<br> (668ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (297ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.16<br> (291ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (135ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.90<br> (2215ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1163ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.63<br> (492ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (301ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (236ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (152ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (1204ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (837ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (40ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (29ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.64<br> (36ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (22ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (49938ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (45127ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (53353ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49364ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (105555ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (132253ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.05<br> (1326ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (647ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.72<br> (217ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (126ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.79<br> (1852ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1035ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (516ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (320ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (256ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (167ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.43<br> (1268ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (884ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (172ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (172ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (28ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (32ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (2657ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2635ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (50ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (50ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.39<br> (176ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (127ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (87ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (86ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (116ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (94ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (47ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (47ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (34ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.39<br> (1158ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (485ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.27<br> (307ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (135ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.36<br> (6154ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2608ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.80<br> (1450ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (806ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.98<br> (969ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (490ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.85<br> (37583ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (20369ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (4037ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (3256ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (630ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (514ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (33255ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (20620ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.87<br> (1426ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (762ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.00<br> (1306ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (652ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.91<br> (22025ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (11560ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.05<br> (6473ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (3155ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.01<br> (4098ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2040ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.03<br> (65926ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (32431ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (135ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (116ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (48ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (59ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.45<br> (80ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (55ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (59ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (52ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (96ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (92ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (82ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (84ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.90<br> (254ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (134ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (171ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (112ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.76<br> (1128ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (641ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (47ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (38ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (70ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (48ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (24ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (669ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (632ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (173ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (159ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (6968ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5903ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.60<br> (1151ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (721ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.94<br> (360ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (186ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (1461ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1161ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (96ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (77ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (164ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (139ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (133ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (134ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64" title="Distribution performance comparison with Microsoft Visual C++ version 14.2 on Windows x64">Distribution
    performance comparison with Microsoft Visual C++ version 14.2 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64.table_Distribution_performance_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><p class="title"><b>Table&#160;6.&#160;Distribution performance comparison with Microsoft Visual C++ version
      14.2 on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Distribution performance comparison with Microsoft Visual C++ version
      14.2 on Windows x64">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                boost 1.73
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                ArcSine (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (43ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ArcSine (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ArcSine (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (30ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (158ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (104ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Beta (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (806ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (413ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (127ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Binomial (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2024ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (26ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (8ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Cauchy (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (26ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (676ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (93ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1073ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (15ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Exponential (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (20ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (26ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (27ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ExtremeValue (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (34ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (277ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (97ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                F (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (901ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (234ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (85ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Gamma (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (640ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (24ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Geometric (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (20ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (244196ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (272497ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Hypergeometric (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (308077ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (584ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (78ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (884ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (244ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (91ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGamma (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (638ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (109ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                InverseGaussian (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1651ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (14ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Laplace (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (14ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (79ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (35ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                LogNormal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (61ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (14ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Logistic (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (15ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (481ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (114ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NegativeBinomial (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2651ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (735ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (489ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralBeta (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (14689ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2643ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (290ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralChiSquared (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (16692ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (608ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (467ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralF (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9122ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2375ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1701ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                NonCentralT (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (23683ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (89ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (28ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Normal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (44ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (34ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (102ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Pareto (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (50ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (84ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Poisson (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (440ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (15ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (14ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Rayleigh (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (23ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (259ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (94ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                SkewNormal (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2843ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (429ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (146ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                StudentsT (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (729ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (CDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (63ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (PDF)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (89ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Weibull (quantile)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (62ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64" title="Library Comparison with GNU C++ version 9.2.0 on Windows x64">Library
    Comparison with GNU C++ version 9.2.0 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64.table_Library_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><p class="title"><b>Table&#160;7.&#160;Library Comparison with GNU C++ version 9.2.0 on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Library Comparison with GNU C++ version 9.2.0 on Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                boost 1.73
              </p>
            </th>
<th>
              <p>
                boost 1.73<br> promote_double&lt;false&gt;
              </p>
            </th>
<th>
              <p>
                tr1/cmath
              </p>
            </th>
<th>
              <p>
                math.h
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                assoc_laguerre<br> (2240/2240 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (137ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (127ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (137ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                assoc_legendre<br> (110/400 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (40ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                beta<br> (2204/2204 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.60<br> (322ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (237ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (201ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cbrt<br> (85/85 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">4.67<br> (70ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (15ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (30ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.13<br> (32ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_i (integer order)<br> (515/526 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.06<br> (597ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (195ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (196ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_i<br> (215/240 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">5.68<br> (949ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.32<br> (387ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (167ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_j (integer order)<br> (253/268 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.92<br> (184ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (96ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.12<br> (300ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.94<br> (186ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_j<br> (442/451 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.15<br> (886ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (499ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (412ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_k (integer order)<br> (505/508 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">18.17<br> (3724ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (205ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">8.40<br> (1722ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_k<br> (187/279 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">19.68<br> (6847ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (348ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.31<br> (2196ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_neumann (integer order)<br> (424/428 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.13<br> (348ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (252ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.83<br> (624ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (163ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_neumann<br> (428/450 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">12.46<br> (10032ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">7.10<br> (5715ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (805ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_1 (complete)<br> (109/109 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.64<br> (77ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (47ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.36<br> (111ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_1<br> (627/629 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (349ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (248ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (270ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_2 (complete)<br> (110/110 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.11<br> (57ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (27ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">9.37<br> (253ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_2<br> (527/530 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.50<br> (583ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (388ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (412ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_3 (complete)<br> (0/500 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">nan<br> (0ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">nan<br> (0ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">nan<br> (0ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                ellint_3<br> (22/845 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.58<br> (670ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (398ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (260ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                erf<br> (950/950 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (38ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (43ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                erfc<br> (950/950 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.76<br> (90ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (51ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (55ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (64ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                expint<br> (436/436 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (92ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (60ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (110ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                expm1<br> (80/80 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (24ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (24ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                laguerre<br> (280/280 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (105ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (108ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                legendre<br> (300/300 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (320ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (255ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (323ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                lgamma<br> (400/400 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.40<br> (214ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.54<br> (160ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (63ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (64ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                log1p<br> (80/80 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.71<br> (29ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.71<br> (29ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                sph_bessel<br> (483/483 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (975ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (661ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.02<br> (1999ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                sph_neumann<br> (284/284 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.96<br> (3153ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1064ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.73<br> (2906ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                tgamma<br> (400/400 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.32<br> (259ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.03<br> (158ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (79ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (78ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                zeta<br> (448/448 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (310ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (221ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">918.24<br> (202930ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64" title="Library Comparison with Microsoft Visual C++ version 14.2 on Windows x64">Library
    Comparison with Microsoft Visual C++ version 14.2 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64.table_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><p class="title"><b>Table&#160;8.&#160;Library Comparison with Microsoft Visual C++ version 14.2 on Windows
      x64</b></p>
<div class="table-contents"><table class="table" summary="Library Comparison with Microsoft Visual C++ version 14.2 on Windows
      x64">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                boost 1.73
              </p>
            </th>
<th>
              <p>
                math.h
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                cbrt<br> (85/85 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (51ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (62ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_bessel_j (integer order)<br> (267/268 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (123ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.50<br> (185ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                cyl_neumann (integer order)<br> (428/428 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (158ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (156ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                erf<br> (950/950 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.15<br> (43ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (20ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                erfc<br> (950/950 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (54ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (59ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                expm1<br> (80/80 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (11ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (10ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                lgamma<br> (400/400 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (80ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.60<br> (128ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                log1p<br> (80/80 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (15ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                tgamma<br> (400/400 tests selected)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (74ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">12.53<br> (927ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_"></a><a class="link" href="index.html#special_function_and_distributio.section_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_" title="Polynomial Arithmetic (GNU C++ version 9.2.0, Windows x64)">Polynomial
    Arithmetic (GNU C++ version 9.2.0, Windows x64)</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_.table_Polynomial_Arithmetic_GNU_C_version_9_2_0_Windows_x64_"></a><p class="title"><b>Table&#160;9.&#160;Polynomial Arithmetic (GNU C++ version 9.2.0, Windows x64)</b></p>
<div class="table-contents"><table class="table" summary="Polynomial Arithmetic (GNU C++ version 9.2.0, Windows x64)">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                boost::uint64_t
              </p>
            </th>
<th>
              <p>
                double
              </p>
            </th>
<th>
              <p>
                cpp_int
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                operator *
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (503ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (502ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">15.20<br> (7629ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator * (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (114ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (109ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.04<br> (658ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator *=
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (223824ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (215955ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">19.30<br> (4168184ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator *= (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (13931ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (13163ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">26.10<br> (343615ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator +
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (163ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (186ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.04<br> (985ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator + (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (116ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (100ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.07<br> (407ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator +=
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (16ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">22.81<br> (365ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator += (int)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (4ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (3ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">33.00<br> (99ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator -
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (159ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (185ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.66<br> (1059ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator - (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (113ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (102ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.75<br> (382ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator -=
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (16ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">23.38<br> (374ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator -= (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (3ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (3ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">31.00<br> (93ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator /
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (767ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (533ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">41.38<br> (22054ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator / (int)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (138ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (107ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">13.58<br> (1453ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator /=
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (11ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (10ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">194.00<br> (1940ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator /= (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (679ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">21.14<br> (14351ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3447.12<br> (2340595ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_"></a><a class="link" href="index.html#special_function_and_distributio.section_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_" title="Polynomial Arithmetic (Microsoft Visual C++ version 14.2, Windows x64)">Polynomial
    Arithmetic (Microsoft Visual C++ version 14.2, Windows x64)</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_.table_Polynomial_Arithmetic_Microsoft_Visual_C_version_14_2_Windows_x64_"></a><p class="title"><b>Table&#160;10.&#160;Polynomial Arithmetic (Microsoft Visual C++ version 14.2, Windows x64)</b></p>
<div class="table-contents"><table class="table" summary="Polynomial Arithmetic (Microsoft Visual C++ version 14.2, Windows x64)">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                boost::uint64_t
              </p>
            </th>
<th>
              <p>
                double
              </p>
            </th>
<th>
              <p>
                cpp_int
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                operator *
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.54<br> (951ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (617ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">15.22<br> (9391ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator * (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (135ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (116ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">5.22<br> (605ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator *=
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (371957ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (286462ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">17.11<br> (4901613ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator *= (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (14157ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (14670ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">19.69<br> (278738ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator +
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (273ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (194ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.20<br> (1203ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator + (int)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (126ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.47<br> (350ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator +=
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (42ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (31ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">11.16<br> (346ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator += (int)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (5ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (4ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">25.50<br> (102ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator -
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.20<br> (231ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (192ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.44<br> (1236ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator - (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.20<br> (121ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.34<br> (337ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator -=
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (42ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (31ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">11.13<br> (345ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator -= (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (4ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (4ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">23.50<br> (94ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator /
              </p>
            </td>
<td>
              <p>
                <span class="red">2.17<br> (1164ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (537ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">51.34<br> (27568ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator / (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (138ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (118ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">9.73<br> (1148ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator /=
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">192.42<br> (2309ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                operator /= (int)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (697ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">36.29<br> (25293ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2700.21<br> (1882045ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64" title="Polynomial Method Comparison with GNU C++ version 9.2.0 on Windows x64">Polynomial
    Method Comparison with GNU C++ version 9.2.0 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64.table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><p class="title"><b>Table&#160;11.&#160;Polynomial Method Comparison with GNU C++ version 9.2.0 on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Polynomial Method Comparison with GNU C++ version 9.2.0 on Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                Method 0<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 0<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 1<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 1<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 2<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 2<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 3<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 3<br> (Integer Coefficients)
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                Order 2
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 3
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.56<br> (23ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (10ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 4
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.50<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.42<br> (29ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 5
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.31<br> (37ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (16ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (17ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (18ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 6
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (31ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.14<br> (45ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (21ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (21ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (22ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 7
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (34ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.15<br> (56ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (29ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (27ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (31ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 8
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (41ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.23<br> (67ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (32ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (31ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (31ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.20<br> (36ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (30ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 9
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.58<br> (52ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.42<br> (80ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (38ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (38ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (34ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 10
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.51<br> (56ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.41<br> (89ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (45ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (45ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (37ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (38ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (39ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (39ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 11
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.46<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (52ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (52ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (41ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (41ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (41ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (41ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 12
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (78ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.63<br> (121ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (60ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (59ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (48ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (47ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (47ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 13
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.78<br> (87ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.78<br> (136ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (63ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (63ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (50ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 14
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.79<br> (95ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.75<br> (146ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.43<br> (76ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.43<br> (76ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (54ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (53ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 15
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.63<br> (103ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.51<br> (158ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.43<br> (90ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (63ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (64ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 16
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (119ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.31<br> (171ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (97ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (97ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (75ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (75ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (75ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (74ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 17
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.67<br> (127ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.42<br> (184ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.42<br> (108ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (107ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (76ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (76ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (77ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (77ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 18
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.66<br> (136ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.39<br> (196ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (116ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (118ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (86ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (87ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (82ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 19
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.72<br> (146ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.51<br> (213ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.59<br> (135ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (133ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (86ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (86ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (85ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (87ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 20
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (158ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.52<br> (234ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (144ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.59<br> (148ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (98ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (95ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (93ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (99ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64" title="Polynomial Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64">Polynomial
    Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64.table_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><p class="title"><b>Table&#160;12.&#160;Polynomial Method Comparison with Microsoft Visual C++ version 14.2
      on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Polynomial Method Comparison with Microsoft Visual C++ version 14.2
      on Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                Method 0<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 0<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 1<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 1<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 2<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 2<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 3<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 3<br> (Integer Coefficients)
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                Order 2
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 3
              </p>
            </td>
<td>
              <p>
                <span class="red">2.33<br> (21ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.33<br> (30ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 4
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (24ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.00<br> (36ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 5
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.31<br> (37ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (16ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (16ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (25ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 6
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (31ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.19<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (21ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (21ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (21ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (27ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (27ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 7
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.54<br> (37ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.33<br> (56ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (24ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (27ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (28ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 8
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.23<br> (67ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (32ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (32ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (30ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (30ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (31ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (31ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 9
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.06<br> (70ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (40ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (45ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (34ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (34ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (37ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (36ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 10
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (54ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.13<br> (83ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (47ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (45ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (39ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (39ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (40ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (40ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 11
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (62ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.24<br> (94ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (52ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (45ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (42ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (46ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (43ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 12
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (71ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.27<br> (109ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (60ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (61ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (50ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (48ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (48ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (48ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 13
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (76ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.33<br> (114ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (51ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (51ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (50ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 14
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.40<br> (132ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (79ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (77ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (57ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (56ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (55ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (55ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 15
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.51<br> (95ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.33<br> (147ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (86ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (87ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (66ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (67ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (63ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (63ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 16
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.47<br> (106ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.18<br> (157ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (96ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (73ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (74ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (72ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (75ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 17
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (114ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.08<br> (162ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (78ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (79ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (82ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (80ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 18
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (126ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.08<br> (177ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (122ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (124ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (87ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (88ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (86ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (85ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 19
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.49<br> (136ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.07<br> (188ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.47<br> (134ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.47<br> (134ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (91ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (92ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (96ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (94ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 20
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.52<br> (150ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.05<br> (203ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.45<br> (144ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (145ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (99ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (101ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64" title="Rational Method Comparison with GNU C++ version 9.2.0 on Windows x64">Rational
    Method Comparison with GNU C++ version 9.2.0 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64.table_Rational_Method_Comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><p class="title"><b>Table&#160;13.&#160;Rational Method Comparison with GNU C++ version 9.2.0 on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Rational Method Comparison with GNU C++ version 9.2.0 on Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                Method 0<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 0<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 1<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 1<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 2<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 2<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 3<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 3<br> (Integer Coefficients)
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                Order 2
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (22ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (13ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 3
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (33ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.17<br> (39ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (28ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 4
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.65<br> (43ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (52ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (38ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (38ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (26ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (27ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (29ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 5
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (56ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (67ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (48ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (54ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (56ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (54ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 6
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (62ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (76ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (61ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (61ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (62ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (62ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (79ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 7
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (74ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (93ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (73ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (72ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (73ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (73ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (74ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (73ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 8
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (90ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (104ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (82ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (82ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (82ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (92ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (86ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 9
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (119ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.66<br> (156ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (97ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (96ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (94ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (95ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (94ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (95ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 10
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (128ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (147ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (111ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (105ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (107ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (105ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (113ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 11
              </p>
            </td>
<td>
              <p>
                <span class="green">1.20<br> (140ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (169ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (125ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (124ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (117ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (125ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (118ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (122ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 12
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (155ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (165ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (137ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (140ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (128ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (154ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (130ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (125ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 13
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (171ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (183ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (159ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (153ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (143ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (135ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (136ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (138ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 14
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (178ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (196ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (168ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (166ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (174ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (168ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (173ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (153ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 15
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (196ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.47<br> (217ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (182ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (181ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (148ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (150ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (170ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (152ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 16
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (209ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.39<br> (223ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (202ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (205ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (160ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (161ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (174ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (161ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 17
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.34<br> (221ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (241ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (217ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (226ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (165ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (175ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (178ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (165ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 18
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.52<br> (264ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (266ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (246ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.43<br> (249ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (214ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (179ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (174ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (182ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 19
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (252ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (292ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.54<br> (288ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.39<br> (259ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (187ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (228ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (191ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (195ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 20
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.34<br> (271ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.59<br> (322ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.39<br> (280ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (294ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (214ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (205ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (202ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (202ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64" title="Rational Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64">Rational
    Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64.table_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><p class="title"><b>Table&#160;14.&#160;Rational Method Comparison with Microsoft Visual C++ version 14.2 on
      Windows x64</b></p>
<div class="table-contents"><table class="table" summary="Rational Method Comparison with Microsoft Visual C++ version 14.2 on
      Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                Method 0<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 0<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 1<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 1<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 2<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 2<br> (Integer Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 3<br> (Double Coefficients)
              </p>
            </th>
<th>
              <p>
                Method 3<br> (Integer Coefficients)
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                Order 2
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="grey">-</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.92<br> (23ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.92<br> (23ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (14ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (12ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 3
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.89<br> (34ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.28<br> (41ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.67<br> (30ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (29ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (19ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 4
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.72<br> (43ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.16<br> (54ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.64<br> (41ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.60<br> (40ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (25ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (26ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 5
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (69ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (53ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (49ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (54ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 6
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (65ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.42<br> (85ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (61ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (60ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (63ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (74ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (75ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (84ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 7
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (75ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (97ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (72ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (71ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (81ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (72ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.20<br> (85ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (96ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 8
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (87ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (84ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (83ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (82ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (81ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.49<br> (202ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.60<br> (211ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 9
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (103ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (143ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (105ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (113ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (90ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (91ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (91ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (89ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 10
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (115ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (146ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (114ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (102ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (100ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 11
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.21<br> (131ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.48<br> (160ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (126ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (125ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (108ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (137ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (108ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (109ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 12
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (148ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (179ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (139ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (139ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (119ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (145ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (117ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (117ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 13
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (163ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.71<br> (212ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (153ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.52<br> (189ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (125ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (160ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (125ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (124ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 14
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.42<br> (190ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (209ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (177ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.47<br> (197ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (137ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (175ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (134ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (136ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 15
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.34<br> (194ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.51<br> (219ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (197ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (212ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (148ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (188ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (145ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.23<br> (323ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 16
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (216ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (244ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (212ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (204ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (179ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.34<br> (209ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (156ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.10<br> (328ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 17
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.39<br> (227ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.67<br> (273ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.34<br> (218ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.69<br> (275ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (163ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (215ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (167ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.53<br> (412ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 18
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.37<br> (242ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.73<br> (306ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (248ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (276ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (187ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (233ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (177ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.15<br> (380ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 19
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (254ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.60<br> (319ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (253ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.51<br> (300ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (199ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (243ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.80<br> (359ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.92<br> (382ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                Order 20
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (268ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.62<br> (338ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (265ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (325ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (209ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (259ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.87<br> (391ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.04<br> (427ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64" title="gcd method comparison with GNU C++ version 9.2.0 on Windows x64">gcd
    method comparison with GNU C++ version 9.2.0 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64.table_gcd_method_comparison_with_GNU_C_version_9_2_0_on_Windows_x64"></a><p class="title"><b>Table&#160;15.&#160;gcd method comparison with GNU C++ version 9.2.0 on Windows x64</b></p>
<div class="table-contents"><table class="table" summary="gcd method comparison with GNU C++ version 9.2.0 on Windows x64">
<colgroup>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                gcd boost 1.73
              </p>
            </th>
<th>
              <p>
                Euclid_gcd boost 1.73
              </p>
            </th>
<th>
              <p>
                Stein_gcd boost 1.73
              </p>
            </th>
<th>
              <p>
                mixed_binary_gcd boost 1.73
              </p>
            </th>
<th>
              <p>
                Stein_gcd_textbook boost 1.73
              </p>
            </th>
<th>
              <p>
                gcd_euclid_textbook boost 1.73
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (585ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (761ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.82<br> (2237ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.97<br> (2321ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.43<br> (836ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (645ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (adjacent Fibonacci
                numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9970176ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">7.06<br> (70352275ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.96<br> (39452018ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.33<br> (33171075ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.04<br> (20368737ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">7.38<br> (73577712ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (permutations of Fibonacci
                numbers)
              </p>
            </td>
<td>
              <p>
                <span class="red">3.58<br> (5700044700ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (1619575299ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">15.19<br> (24170880700ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.10<br> (4926301699ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">7.61<br> (12103557199ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1591386600ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (random prime number
                products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (776840ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (1420825ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">7.78<br> (6040362ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.39<br> (1853658ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.19<br> (3251426ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.96<br> (1522179ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (55462256ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.02<br> (112246250ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.55<br> (141227725ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.21<br> (122643774ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.49<br> (82400762ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.99<br> (110242300ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (442ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (475ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.12<br> (1815ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.11<br> (1813ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (515ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (441ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (4055238ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.74<br> (15153867ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.14<br> (12714485ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.78<br> (11263817ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (7405233ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.79<br> (15349360ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (permutations of Fibonacci
                numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2188053200ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.24<br> (4905530400ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.53<br> (7720779699ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.26<br> (4951713400ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.06<br> (4508168099ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.60<br> (5692910900ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (random prime number
                products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (788189ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.65<br> (1298322ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.56<br> (3592013ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.51<br> (1186279ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.77<br> (2184586ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (1337848ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5971862ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.75<br> (16440456ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.13<br> (18696806ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.48<br> (14818301ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.65<br> (9829225ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.16<br> (18848609ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (473ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (522ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.35<br> (1113ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.54<br> (1201ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (617ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (497ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (8919442ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.88<br> (43541675ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.74<br> (42250737ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.64<br> (32424337ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.68<br> (14998360ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.90<br> (43720662ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (permutations of Fibonacci
                numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (4874074099ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (5941210899ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.28<br> (15985377299ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.50<br> (7304170300ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.76<br> (8559919799ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (6002105200ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (random prime number
                products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (829159ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.59<br> (1318798ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">8.12<br> (6731670ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.91<br> (1581731ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.08<br> (2551970ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.58<br> (1308443ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (18120096ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.35<br> (42631487ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.97<br> (71846612ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.10<br> (56237574ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.49<br> (27081093ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.66<br> (48247731ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.85<br> (109ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.44<br> (144ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (59ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.88<br> (111ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.68<br> (99ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.08<br> (123ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.98<br> (17394ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">14.61<br> (85221ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (5832ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.98<br> (17351ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.20<br> (12805ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">14.60<br> (85125ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (permutations of Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (1203049ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (1508607ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (1307113ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1159442ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.23<br> (2585039ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (1455556ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (random prime number products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (267158ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.88<br> (441001ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (234725ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.07<br> (249997ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.02<br> (473466ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.78<br> (418669ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.39<br> (507147ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.14<br> (784228ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (365889ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.33<br> (488432ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.75<br> (641184ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.08<br> (760185ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (70ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (66ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (57ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (68ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.63<br> (93ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (64ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.79<br> (2678ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">10.20<br> (15231ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1493ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.85<br> (2765ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.07<br> (3093ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">9.50<br> (14177ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (permutations of Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (130874ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.43<br> (187180ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (171288ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (132289ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.45<br> (321281ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (169852ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (random prime number products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.02<br> (132073ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.56<br> (202025ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (143913ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (129448ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.03<br> (263053ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.40<br> (181659ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (209599ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (296090ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (183672ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (214530ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.76<br> (322600ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (284838ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (74ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (65ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (62ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (80ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (95ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (67ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (694ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.51<br> (2915ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (448ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.65<br> (737ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.42<br> (634ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.06<br> (2716ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (permutations of Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.31<br> (10776ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.35<br> (19287ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (8206ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (11598ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.63<br> (13337ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.21<br> (18163ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (random prime number products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (48625ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.82<br> (84692ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (47933ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (46539ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.94<br> (136663ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.68<br> (78386ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (73231ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.72<br> (120140ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (69680ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (72636ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.51<br> (175204ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (118679ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (73ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (64ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (56ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (69ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.62<br> (91ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.14<br> (64ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.81<br> (2689ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">10.14<br> (15051ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1485ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.92<br> (2845ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.10<br> (3117ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">9.74<br> (14464ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (permutations of Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (125228ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.45<br> (182101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (169753ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (130303ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.50<br> (312889ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (176940ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (random prime number products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.04<br> (133297ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (199022ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (134178ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (128319ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.03<br> (260550ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (196665ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (212670ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (298254ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (184955ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (216091ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.80<br> (332689ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.62<br> (299958ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="special_function_and_distributio.section_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><a class="link" href="index.html#special_function_and_distributio.section_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64" title="gcd method comparison with Microsoft Visual C++ version 14.2 on Windows x64">gcd
    method comparison with Microsoft Visual C++ version 14.2 on Windows x64</a>
</h2></div></div></div>
<div class="table">
<a name="special_function_and_distributio.section_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64.table_gcd_method_comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64"></a><p class="title"><b>Table&#160;16.&#160;gcd method comparison with Microsoft Visual C++ version 14.2 on Windows
      x64</b></p>
<div class="table-contents"><table class="table" summary="gcd method comparison with Microsoft Visual C++ version 14.2 on Windows
      x64">
<colgroup>
<col>
<col>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              <p>
                Function
              </p>
            </th>
<th>
              <p>
                gcd boost 1.73
              </p>
            </th>
<th>
              <p>
                Euclid_gcd boost 1.73
              </p>
            </th>
<th>
              <p>
                Stein_gcd boost 1.73
              </p>
            </th>
<th>
              <p>
                mixed_binary_gcd boost 1.73
              </p>
            </th>
<th>
              <p>
                Stein_gcd_textbook boost 1.73
              </p>
            </th>
<th>
              <p>
                gcd_euclid_textbook boost 1.73
              </p>
            </th>
</tr></thead>
<tbody>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (811ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (806ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.49<br> (3619ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.37<br> (3524ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.54<br> (1240ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (947ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (adjacent Fibonacci
                numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (17221009ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.10<br> (53378856ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.49<br> (60085356ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.71<br> (46662362ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.43<br> (24687809ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.60<br> (62017387ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (permutations of Fibonacci
                numbers)
              </p>
            </td>
<td>
              <p>
                <span class="red">4.79<br> (8947276300ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1869827499ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">16.49<br> (30836050300ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.95<br> (5512590399ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">9.35<br> (17476759399ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.59<br> (2969003299ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (random prime number
                products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (1366950ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1184715ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.07<br> (7192390ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.69<br> (2004764ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.88<br> (3414226ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (1223450ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint1024_t&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (94422587ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (91927462ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.46<br> (205656225ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.79<br> (150321950ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (105849675ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (83747287ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (529ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.22<br> (578ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">5.71<br> (2706ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">5.01<br> (2376ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.62<br> (768ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (474ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (6910946ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.03<br> (14038607ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.15<br> (28656946ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.36<br> (16280003ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.83<br> (12632765ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.79<br> (12358175ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (permutations of Fibonacci
                numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (3546690299ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (4410071600ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.54<br> (16088449000ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.08<br> (7376147399ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.87<br> (6630678299ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.11<br> (3921678899ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (random prime number
                products)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (1402017ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.19<br> (1342771ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">10.57<br> (11937009ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.30<br> (2592407ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.17<br> (3578886ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1129228ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint256_t&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (9555357ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (13230160ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.58<br> (34160918ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.17<br> (20739521ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.66<br> (15830168ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (11919907ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (610ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (586ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">4.52<br> (2524ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">5.42<br> (3032ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.53<br> (858ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (559ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (15008157ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.19<br> (32823187ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.54<br> (53103662ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.51<br> (37681662ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.67<br> (25128434ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.06<br> (30897006ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (permutations of Fibonacci
                numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (7824618799ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.06<br> (4905917200ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.42<br> (29578499900ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.96<br> (9014054500ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.82<br> (12972133700ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (4607798200ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (random prime number
                products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.20<br> (1429033ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1192363ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">6.71<br> (8006331ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.66<br> (1983967ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.05<br> (3641579ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1193514ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;boost::multiprecision::uint512_t&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (28993946ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (32874618ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.71<br> (107613600ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.24<br> (64869562ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.39<br> (40246987ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (36427993ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (143ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.88<br> (167ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (97ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.66<br> (148ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (89ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.25<br> (111ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.65<br> (18657ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">9.12<br> (102852ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (11278ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.65<br> (18642ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.36<br> (15386ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">7.61<br> (85867ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (permutations of Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.18<br> (1759315ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (1829739ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.48<br> (3696867ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.20<br> (1792095ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.92<br> (2869829ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (1493466ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (random prime number products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.03<br> (419624ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (513559ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.66<br> (677592ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (407357ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.24<br> (505557ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (426446ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long long&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (802062ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (895731ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.38<br> (959675ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (810488ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (696259ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.10<br> (768043ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="red">2.05<br> (115ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (90ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.80<br> (101ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.98<br> (111ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.55<br> (87ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (56ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (3438ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">8.19<br> (22429ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2739ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (3567ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (3146ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">5.44<br> (14903ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (permutations of Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (205858ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.52<br> (268100ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.43<br> (427978ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.13<br> (198590ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.02<br> (356193ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (175939ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (random prime number products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.01<br> (214230ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (278903ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.93<br> (406951ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (237142ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.70<br> (358996ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (211247ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned long&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.29<br> (382560ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.46<br> (431960ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.77<br> (524430ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (373023ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.27<br> (377903ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (296476ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.79<br> (118ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.41<br> (93ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.47<br> (97ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.73<br> (114ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.42<br> (94ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (66ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (821ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">7.62<br> (5377ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (706ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.17<br> (823ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.15<br> (810ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">5.04<br> (3557ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (permutations of Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (11485ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">3.82<br> (43640ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (13294ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (11428ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.19<br> (25029ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.11<br> (24145ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (random prime number products)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (123821ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.92<br> (188438ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.21<br> (216289ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (109274ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.67<br> (163434ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (97914ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned short&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (169639ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.44<br> (212132ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.62<br> (237308ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.16<br> (170196ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (191524ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (146827ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (Trivial cases)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.98<br> (117ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (95ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.90<br> (112ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">2.00<br> (118ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (95ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (59ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (adjacent Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.28<br> (3381ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">8.39<br> (22209ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (2648ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.30<br> (3436ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.34<br> (3540ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">5.64<br> (14937ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (permutations of Fibonacci numbers)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.08<br> (197785ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.47<br> (269176ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.37<br> (435412ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.12<br> (205095ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="red">2.08<br> (382592ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (183636ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (random prime number products)
              </p>
            </td>
<td>
              <p>
                <span class="green">1.09<br> (214890ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.42<br> (279881ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.99<br> (392760ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.05<br> (206420ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.61<br> (317337ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (197431ns)</span>
              </p>
            </td>
</tr>
<tr>
<td>
              <p>
                gcd&lt;unsigned&gt; (uniform random numbers)
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.26<br> (385229ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.35<br> (411167ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.68<br> (512335ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.23<br> (375323ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="blue">1.32<br> (402786ns)</span>
              </p>
            </td>
<td>
              <p>
                <span class="green">1.00<br> (305574ns)</span>
              </p>
            </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"><p><small>Last revised: April 03, 2020 at 11:57:28 GMT</small></p></td>
<td align="right"><div class="copyright-footer"></div></td>
</tr></table>
<hr>
<div class="spirit-nav"></div>
</body>
</html>
