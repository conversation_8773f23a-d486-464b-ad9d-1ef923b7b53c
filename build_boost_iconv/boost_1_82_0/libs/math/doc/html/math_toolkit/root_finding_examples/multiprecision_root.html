<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Root-finding using Boost.Multiprecision</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../root_finding_examples.html" title="Examples of Root-Finding (with and without derivatives)">
<link rel="prev" href="5th_root_eg.html" title="Computing the Fifth Root">
<link rel="next" href="nth_root.html" title="Generalizing to Compute the nth root">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="5th_root_eg.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../root_finding_examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="nth_root.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.root_finding_examples.multiprecision_root"></a><a class="link" href="multiprecision_root.html" title="Root-finding using Boost.Multiprecision">Root-finding
      using Boost.Multiprecision</a>
</h3></div></div></div>
<p>
        The apocryphally astute reader might, by now, be asking "How do we know
        if this computes the 'right' answer?".
      </p>
<p>
        For most values, there is, sadly, no 'right' answer. This is because values
        can only rarely be <span class="emphasis"><em>exactly represented</em></span> by C++ floating-point
        types. What we do want is the 'best' representation - one that is the nearest
        <a href="http://en.wikipedia.org/wiki/Floating_point#Representable_numbers.2C_conversion_and_rounding" target="_top">representable</a>
        value. (For more about how numbers are represented see <a href="http://en.wikipedia.org/wiki/Floating_point" target="_top">Floating
        point</a>).
      </p>
<p>
        Of course, we might start with finding an external reference source like
        <a href="http://www.wolframalpha.com/" target="_top">Wolfram Alpha</a>, as above,
        but this is not always possible.
      </p>
<p>
        Another way to reassure is to compute 'reference' values at higher precision
        with which to compare the results of our iterative computations using built-in
        like <code class="computeroutput"><span class="keyword">double</span></code>. They should agree
        within the tolerance that was set.
      </p>
<p>
        The result of <code class="computeroutput"><span class="keyword">static_cast</span></code>ing
        to <code class="computeroutput"><span class="keyword">double</span></code> from a higher-precision
        type like <code class="computeroutput"><span class="identifier">cpp_bin_float_50</span></code>
        is guaranteed to be the <span class="bold"><strong>nearest representable</strong></span>
        <code class="computeroutput"><span class="keyword">double</span></code> value.
      </p>
<p>
        For example, the cube root functions in our example for <code class="computeroutput"><span class="identifier">cbrt</span><span class="special">(</span><span class="number">28.</span><span class="special">)</span></code>
        compute
      </p>
<p>
        <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">cbrt</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">28.</span><span class="special">)</span> <span class="special">=</span>
        <span class="number">3.0365889718756627</span></code>
      </p>
<p>
        WolframAlpha says <code class="computeroutput"><span class="number">3.036588971875662519420809578505669635581453977248111123242141</span><span class="special">...</span></code>
      </p>
<p>
        <code class="computeroutput"><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">3.03658897187566251942080957850</span><span class="special">)</span>
        <span class="special">=</span> <span class="number">3.0365889718756627</span></code>
      </p>
<p>
        This example <code class="computeroutput"><span class="identifier">cbrt</span><span class="special">(</span><span class="number">28.</span><span class="special">)</span> <span class="special">=</span>
        <span class="number">3.0365889718756627</span></code>
      </p>
<div class="tip"><table border="0" summary="Tip">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Tip]" src="../../../../../../doc/src/images/tip.png"></td>
<th align="left">Tip</th>
</tr>
<tr><td align="left" valign="top">
<p>
          To ensure that all potentially significant decimal digits are displayed
          use <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">max_digits10</span></code> (or if not available on
          older platforms or compilers use <code class="computeroutput"><span class="number">2</span><span class="special">+</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;::</span><span class="identifier">digits</span><span class="special">*</span><span class="number">3010</span><span class="special">/</span><span class="number">10000</span></code>).<br>
        </p>
<p>
          Ideally, values should agree to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric</span><span class="special">-</span><span class="identifier">limits</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">digits10</span></code>
          decimal digits.
        </p>
<p>
          This also means that a 'reference' value to be <span class="bold"><strong>input</strong></span>
          or <code class="computeroutput"><span class="keyword">static_cast</span></code> should have
          at least <code class="computeroutput"><span class="identifier">max_digits10</span></code> decimal
          digits (17 for 64-bit <code class="computeroutput"><span class="keyword">double</span></code>).
        </p>
</td></tr>
</table></div>
<p>
        If we wish to compute <span class="bold"><strong>higher-precision values</strong></span>
        then, on some platforms, we may be able to use <code class="computeroutput"><span class="keyword">long</span>
        <span class="keyword">double</span></code> with a higher precision than
        <code class="computeroutput"><span class="keyword">double</span></code> to compare with the very
        common <code class="computeroutput"><span class="keyword">double</span></code> and/or a more
        efficient built-in quad floating-point type like <code class="computeroutput"><span class="identifier">__float128</span></code>.
      </p>
<p>
        Almost all platforms can easily use <a href="../../../../../../libs/multiprecision/doc/html/index.html" target="_top">Boost.Multiprecision</a>,
        for example, <a href="../../../../../../libs/multiprecision/doc/html/boost_multiprecision/tut/floats/cpp_dec_float.html" target="_top">cpp_dec_float</a>
        or a binary type <a href="../../../../../../libs/multiprecision/doc/html/boost_multiprecision/tut/floats/cpp_bin_float.html" target="_top">cpp_bin_float</a>
        types, to compute values at very much higher precision.
      </p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
          With multiprecision types, it is debatable whether to use the type <code class="computeroutput"><span class="identifier">T</span></code> for computing the initial guesses.
          Type <code class="computeroutput"><span class="keyword">double</span></code> is like to be
          accurate enough for the method used in these examples. This would limit
          the exponent range of possible values to that of <code class="computeroutput"><span class="keyword">double</span></code>.
          There is also the cost of conversion to and from type <code class="computeroutput"><span class="identifier">T</span></code>
          to consider. In these examples, <code class="computeroutput"><span class="keyword">double</span></code>
          is used via <code class="computeroutput"><span class="keyword">typedef</span> <span class="keyword">double</span>
          <span class="identifier">guess_type</span></code>.
        </p></td></tr>
</table></div>
<p>
        Since the functors and functions used above are templated on the value type,
        we can very simply use them with any of the <a href="../../../../../../libs/multiprecision/doc/html/index.html" target="_top">Boost.Multiprecision</a>
        types. As a reminder, here's our toy cube root function using 2 derivatives
        and C++11 lambda functions to find the root:
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">cbrt_2deriv_lambda</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">x</span><span class="special">)</span>
<span class="special">{</span>
   <span class="comment">// return cube root of x using 1st and 2nd derivatives and Halley.</span>
   <span class="comment">//using namespace std;  // Help ADL of std functions.</span>
   <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">tools</span><span class="special">;</span>
   <span class="keyword">int</span> <span class="identifier">exponent</span><span class="special">;</span>
   <span class="identifier">frexp</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span> <span class="special">&amp;</span><span class="identifier">exponent</span><span class="special">);</span>                                <span class="comment">// Get exponent of z (ignore mantissa).</span>
   <span class="identifier">T</span> <span class="identifier">guess</span> <span class="special">=</span> <span class="identifier">ldexp</span><span class="special">(</span><span class="number">1.</span><span class="special">,</span> <span class="identifier">exponent</span> <span class="special">/</span> <span class="number">3</span><span class="special">);</span>                    <span class="comment">// Rough guess is to divide the exponent by three.</span>
   <span class="identifier">T</span> <span class="identifier">min</span> <span class="special">=</span> <span class="identifier">ldexp</span><span class="special">(</span><span class="number">0.5</span><span class="special">,</span> <span class="identifier">exponent</span> <span class="special">/</span> <span class="number">3</span><span class="special">);</span>                     <span class="comment">// Minimum possible value is half our guess.</span>
   <span class="identifier">T</span> <span class="identifier">max</span> <span class="special">=</span> <span class="identifier">ldexp</span><span class="special">(</span><span class="number">2.</span><span class="special">,</span> <span class="identifier">exponent</span> <span class="special">/</span> <span class="number">3</span><span class="special">);</span>                      <span class="comment">// Maximum possible value is twice our guess.</span>
   <span class="keyword">const</span> <span class="keyword">int</span> <span class="identifier">digits</span> <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">digits</span><span class="special">;</span>  <span class="comment">// Maximum possible binary digits accuracy for type T.</span>
   <span class="comment">// digits used to control how accurate to try to make the result.</span>
   <span class="keyword">int</span> <span class="identifier">get_digits</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">digits</span> <span class="special">*</span> <span class="number">0.4</span><span class="special">);</span>    <span class="comment">// Accuracy triples with each step, so stop when just</span>
   <span class="comment">// over one third of the digits are correct.</span>
   <span class="identifier">std</span><span class="special">::</span><span class="identifier">uintmax_t</span> <span class="identifier">maxit</span> <span class="special">=</span> <span class="number">20</span><span class="special">;</span>
   <span class="identifier">T</span> <span class="identifier">result</span> <span class="special">=</span> <span class="identifier">halley_iterate</span><span class="special">(</span>
      <span class="comment">// lambda function:</span>
      <span class="special">[</span><span class="identifier">x</span><span class="special">](</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">g</span><span class="special">){</span> <span class="keyword">return</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">make_tuple</span><span class="special">(</span><span class="identifier">g</span> <span class="special">*</span> <span class="identifier">g</span> <span class="special">*</span> <span class="identifier">g</span> <span class="special">-</span> <span class="identifier">x</span><span class="special">,</span> <span class="number">3</span> <span class="special">*</span> <span class="identifier">g</span> <span class="special">*</span> <span class="identifier">g</span><span class="special">,</span> <span class="number">6</span> <span class="special">*</span> <span class="identifier">g</span><span class="special">);</span> <span class="special">},</span>
      <span class="identifier">guess</span><span class="special">,</span> <span class="identifier">min</span><span class="special">,</span> <span class="identifier">max</span><span class="special">,</span> <span class="identifier">get_digits</span><span class="special">,</span> <span class="identifier">maxit</span><span class="special">);</span>
   <span class="keyword">return</span> <span class="identifier">result</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
        Some examples below are 50 decimal digit decimal and binary types (and on
        some platforms a much faster <code class="computeroutput"><span class="identifier">float128</span></code>
        or <code class="computeroutput"><span class="identifier">quad_float</span></code> type ) that
        we can use with these includes:
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">multiprecision</span><span class="special">/</span><span class="identifier">cpp_bin_float</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span> <span class="comment">// For cpp_bin_float_50.</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">multiprecision</span><span class="special">/</span><span class="identifier">cpp_dec_float</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span> <span class="comment">// For cpp_dec_float_50.</span>
<span class="preprocessor">#ifndef</span> <span class="identifier">_MSC_VER</span>  <span class="comment">// float128 is not yet supported by Microsoft compiler at 2013.</span>
<span class="preprocessor">#  include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">multiprecision</span><span class="special">/</span><span class="identifier">float128</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span> <span class="comment">// Requires libquadmath.</span>
<span class="preprocessor">#endif</span>
</pre>
<p>
        Some using statements simplify their use:
      </p>
<pre class="programlisting">  <span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">multiprecision</span><span class="special">::</span><span class="identifier">cpp_dec_float_50</span><span class="special">;</span> <span class="comment">// decimal.</span>
  <span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">multiprecision</span><span class="special">::</span><span class="identifier">cpp_bin_float_50</span><span class="special">;</span> <span class="comment">// binary.</span>
<span class="preprocessor">#ifndef</span> <span class="identifier">_MSC_VER</span>  <span class="comment">// Not supported by Microsoft compiler.</span>
  <span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">multiprecision</span><span class="special">::</span><span class="identifier">float128</span><span class="special">;</span>
<span class="preprocessor">#endif</span>
</pre>
<p>
        They can be used thus:
      </p>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span><span class="special">.</span><span class="identifier">precision</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">cpp_dec_float_50</span><span class="special">&gt;::</span><span class="identifier">digits10</span><span class="special">);</span>

<span class="identifier">cpp_dec_float_50</span> <span class="identifier">two</span> <span class="special">=</span> <span class="number">2</span><span class="special">;</span> <span class="comment">// </span>
<span class="identifier">cpp_dec_float_50</span>  <span class="identifier">r</span> <span class="special">=</span> <span class="identifier">cbrt_2deriv</span><span class="special">(</span><span class="identifier">two</span><span class="special">);</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"cbrt("</span> <span class="special">&lt;&lt;</span> <span class="identifier">two</span> <span class="special">&lt;&lt;</span> <span class="string">") = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">r</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

<span class="identifier">r</span> <span class="special">=</span> <span class="identifier">cbrt_2deriv</span><span class="special">(</span><span class="number">2.</span><span class="special">);</span> <span class="comment">// Passing a double, so ADL will compute a double precision result.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"cbrt("</span> <span class="special">&lt;&lt;</span> <span class="identifier">two</span> <span class="special">&lt;&lt;</span> <span class="string">") = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">r</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="comment">// cbrt(2) = 1.2599210498948731906665443602832965552806854248047 'wrong' from digits 17 onwards!</span>
<span class="identifier">r</span> <span class="special">=</span> <span class="identifier">cbrt_2deriv</span><span class="special">(</span><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">cpp_dec_float_50</span><span class="special">&gt;(</span><span class="number">2.</span><span class="special">));</span> <span class="comment">// Passing a cpp_dec_float_50, </span>
<span class="comment">// so will compute a cpp_dec_float_50 precision result.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"cbrt("</span> <span class="special">&lt;&lt;</span> <span class="identifier">two</span> <span class="special">&lt;&lt;</span> <span class="string">") = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">r</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="identifier">r</span> <span class="special">=</span> <span class="identifier">cbrt_2deriv</span><span class="special">&lt;</span><span class="identifier">cpp_dec_float_50</span><span class="special">&gt;(</span><span class="number">2.</span><span class="special">);</span> <span class="comment">// Explicitly a cpp_dec_float_50, so will compute a cpp_dec_float_50 precision result.</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"cbrt("</span> <span class="special">&lt;&lt;</span> <span class="identifier">two</span> <span class="special">&lt;&lt;</span> <span class="string">") = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">r</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="comment">// cpp_dec_float_50 1.2599210498948731647672106072782283505702514647015</span>
</pre>
<p>
        A reference value computed by <a href="http://www.wolframalpha.com/" target="_top">Wolfram
        Alpha</a> is
      </p>
<pre class="programlisting"><span class="identifier">N</span><span class="special">[</span><span class="number">2</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">3</span><span class="special">),</span> <span class="number">50</span><span class="special">]</span>  <span class="number">1.2599210498948731647672106072782283505702514647015</span>
</pre>
<p>
        which agrees exactly.
      </p>
<p>
        To <span class="bold"><strong>show</strong></span> values to their full precision,
        it is necessary to adjust the <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream</span></code>
        <code class="computeroutput"><span class="identifier">precision</span></code> to suit the type,
        for example:
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">show_cube_root</span><span class="special">(</span><span class="identifier">T</span> <span class="identifier">value</span><span class="special">)</span>
<span class="special">{</span> <span class="comment">// Demonstrate by printing the root using all definitely significant digits.</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span><span class="special">.</span><span class="identifier">precision</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">digits10</span><span class="special">);</span>
  <span class="identifier">T</span> <span class="identifier">r</span> <span class="special">=</span> <span class="identifier">cbrt_2deriv</span><span class="special">(</span><span class="identifier">value</span><span class="special">);</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"value = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">value</span> <span class="special">&lt;&lt;</span> <span class="string">", cube root ="</span> <span class="special">&lt;&lt;</span> <span class="identifier">r</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
  <span class="keyword">return</span> <span class="identifier">r</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<pre class="programlisting"><span class="identifier">show_cube_root</span><span class="special">(</span><span class="number">2.</span><span class="special">);</span>
<span class="identifier">show_cube_root</span><span class="special">(</span><span class="number">2.L</span><span class="special">);</span>
<span class="identifier">show_cube_root</span><span class="special">(</span><span class="identifier">two</span><span class="special">);</span>
</pre>
<p>
        which outputs:
      </p>
<pre class="programlisting">cbrt(2) = 1.2599210498948731647672106072782283505702514647015

value = 2, cube root =1.25992104989487
value = 2, cube root =1.25992104989487
value = 2, cube root =1.2599210498948731647672106072782283505702514647015
</pre>
<div class="tip"><table border="0" summary="Tip">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Tip]" src="../../../../../../doc/src/images/tip.png"></td>
<th align="left">Tip</th>
</tr>
<tr><td align="left" valign="top">
<p>
          Be <span class="bold"><strong>very careful</strong></span> about the floating-point
          type <code class="computeroutput"><span class="identifier">T</span></code> that is passed to
          the root-finding function. Carelessly passing a integer by writing <code class="computeroutput"><span class="identifier">cpp_dec_float_50</span> <span class="identifier">r</span>
          <span class="special">=</span> <span class="identifier">cbrt_2deriv</span><span class="special">(</span><span class="number">2</span><span class="special">);</span></code>
          or <code class="computeroutput"><span class="identifier">show_cube_root</span><span class="special">(</span><span class="number">2</span><span class="special">);</span></code> will
          provoke many warnings and compile errors.
        </p>
<p>
          Even <code class="computeroutput"><span class="identifier">show_cube_root</span><span class="special">(</span><span class="number">2.F</span><span class="special">);</span></code> will
          produce warnings because <code class="computeroutput"><span class="keyword">typedef</span>
          <span class="keyword">double</span> <span class="identifier">guess_type</span></code>
          defines the type used to compute the guess and bracket values as <code class="computeroutput"><span class="keyword">double</span></code>.
        </p>
<p>
          Even more treacherous is passing a <code class="computeroutput"><span class="keyword">double</span></code>
          as in <code class="computeroutput"><span class="identifier">cpp_dec_float_50</span> <span class="identifier">r</span> <span class="special">=</span> <span class="identifier">cbrt_2deriv</span><span class="special">(</span><span class="number">2.</span><span class="special">);</span></code> which
          silently gives the 'wrong' result, computing a <code class="computeroutput"><span class="keyword">double</span></code>
          result and <span class="bold"><strong>then</strong></span> converting to <code class="computeroutput"><span class="identifier">cpp_dec_float_50</span></code>! All digits beyond
          <code class="computeroutput"><span class="identifier">max_digits10</span></code> will be incorrect.
          Making the <code class="computeroutput"><span class="identifier">cbrt</span></code> type explicit
          with <code class="computeroutput"><span class="identifier">cbrt_2deriv</span><span class="special">&lt;</span><span class="identifier">cpp_dec_float_50</span><span class="special">&gt;(</span><span class="number">2.</span><span class="special">);</span></code> will
          give you the desired 50 decimal digit precision result.
        </p>
</td></tr>
</table></div>
<p>
        Full code of this example is at <a href="../../../../example/root_finding_multiprecision_example.cpp" target="_top">root_finding_multiprecision_example.cpp</a>.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="5th_root_eg.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../root_finding_examples.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="nth_root.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
