<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Incomplete Beta Functions</title>
<link rel="stylesheet" href="../../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../sf_beta.html" title="Beta Functions">
<link rel="prev" href="beta_function.html" title="Beta">
<link rel="next" href="ibeta_inv_function.html" title="The Incomplete Beta Function Inverses">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="beta_function.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../sf_beta.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ibeta_inv_function.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="math_toolkit.sf_beta.ibeta_function"></a><a class="link" href="ibeta_function.html" title="Incomplete Beta Functions">Incomplete Beta
      Functions</a>
</h3></div></div></div>
<h5>
<a name="math_toolkit.sf_beta.ibeta_function.h0"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.ibeta_function.synopsis"></a></span><a class="link" href="ibeta_function.html#math_toolkit.sf_beta.ibeta_function.synopsis">Synopsis</a>
      </h5>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">math</span><span class="special">/</span><span class="identifier">special_functions</span><span class="special">/</span><span class="identifier">beta</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span> <span class="keyword">namespace</span> <span class="identifier">math</span><span class="special">{</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ibeta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ibeta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ibetac</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ibetac</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">beta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">beta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">betac</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">betac</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>

<span class="special">}}</span> <span class="comment">// namespaces</span>
</pre>
<h5>
<a name="math_toolkit.sf_beta.ibeta_function.h1"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.ibeta_function.description"></a></span><a class="link" href="ibeta_function.html#math_toolkit.sf_beta.ibeta_function.description">Description</a>
      </h5>
<p>
        There are four <a href="http://en.wikipedia.org/wiki/Incomplete_beta_function" target="_top">incomplete
        beta functions</a> : two are normalised versions (also known as <span class="emphasis"><em>regularized</em></span>
        beta functions) that return values in the range [0, 1], and two are non-normalised
        and return values in the range [0, <a class="link" href="beta_function.html" title="Beta">beta</a>(a,
        b)]. Users interested in statistical applications should use the normalised
        (or <a href="http://mathworld.wolfram.com/RegularizedBetaFunction.html" target="_top">regularized</a>
        ) versions (ibeta and ibetac).
      </p>
<p>
        All of these functions require <span class="emphasis"><em>0 &lt;= x &lt;= 1</em></span>.
      </p>
<p>
        The normalized functions <a class="link" href="ibeta_function.html" title="Incomplete Beta Functions">ibeta</a>
        and <a class="link" href="ibeta_function.html" title="Incomplete Beta Functions">ibetac</a> require
        <span class="emphasis"><em>a,b &gt;= 0</em></span>, and in addition that not both <span class="emphasis"><em>a</em></span>
        and <span class="emphasis"><em>b</em></span> are zero.
      </p>
<p>
        The functions <a class="link" href="beta_function.html" title="Beta">beta</a>
        and <a class="link" href="ibeta_function.html" title="Incomplete Beta Functions">betac</a> require
        <span class="emphasis"><em>a,b &gt; 0</em></span>.
      </p>
<p>
        The return type of these functions is computed using the <a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>result
        type calculation rules</em></span></a> when T1, T2 and T3 are different
        types.
      </p>
<p>
        The final <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a> argument is optional and can
        be used to control the behaviour of the function: how it handles errors,
        what level of precision to use etc. Refer to the <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">policy
        documentation for more details</a>.
      </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ibeta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ibeta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<p>
        Returns the normalised incomplete beta function of a, b and x:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta3.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../graphs/ibeta.svg" align="middle"></span>

        </p></blockquote></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ibetac</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">ibetac</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<p>
        Returns the normalised complement of the incomplete beta function of a, b
        and x:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta4.svg"></span>

        </p></blockquote></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">beta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">beta</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<p>
        Returns the full (non-normalised) incomplete beta function of a, b and x:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta1.svg"></span>

        </p></blockquote></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">betac</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T1</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T2</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">T3</span><span class="special">,</span> <span class="keyword">class</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&gt;</span>
<a class="link" href="../result_type.html" title="Calculation of the Type of the Result"><span class="emphasis"><em>calculated-result-type</em></span></a> <span class="identifier">betac</span><span class="special">(</span><span class="identifier">T1</span> <span class="identifier">a</span><span class="special">,</span> <span class="identifier">T2</span> <span class="identifier">b</span><span class="special">,</span> <span class="identifier">T3</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../../policy.html" title="Chapter 21. Policies: Controlling Precision, Error Handling etc">Policy</a><span class="special">&amp;);</span>
</pre>
<p>
        Returns the full (non-normalised) complement of the incomplete beta function
        of a, b and x:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta2.svg"></span>

        </p></blockquote></div>
<h5>
<a name="math_toolkit.sf_beta.ibeta_function.h2"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.ibeta_function.accuracy"></a></span><a class="link" href="ibeta_function.html#math_toolkit.sf_beta.ibeta_function.accuracy">Accuracy</a>
      </h5>
<p>
        The following tables give peak and mean relative errors in over various domains
        of a, b and x, along with comparisons to the <a href="http://www.gnu.org/software/gsl/" target="_top">GSL-1.9</a>
        and <a href="http://www.netlib.org/cephes/" target="_top">Cephes</a> libraries.
        Note that only results for the widest floating-point type on the system are
        given as narrower types have <a class="link" href="../relative_error.html#math_toolkit.relative_error.zero_error">effectively
        zero error</a>.
      </p>
<p>
        Note that the results for 80 and 128-bit long doubles are noticeably higher
        than for doubles: this is because the wider exponent range of these types
        allow more extreme test cases to be tested. For example expected results
        that are zero at double precision, may be finite but exceptionally small
        with the wider exponent range of the long double types.
      </p>
<div class="table">
<a name="math_toolkit.sf_beta.ibeta_function.table_ibeta"></a><p class="title"><b>Table 8.18. Error rates for ibeta</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibeta">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 682ε (Mean = 32.6ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 22.9ε (Mean = 3.35ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.97ε (Mean = 2.09ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 21.3ε (Mean = 2.75ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.4ε (Mean = 1.93ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 690ε (Mean = 151ε))<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 232ε (Mean = 27.9ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 50ε (Mean = 12.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 124ε (Mean = 18.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 106ε (Mean = 16.3ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Large and Diverse Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.26ε (Mean = 0.063ε)</span><br> <br>
                  (<span class="emphasis"><em>GSL 2.1:</em></span> Max = 3.9e+05ε (Mean = 1.82e+04ε)
                  <a class="link" href="../logs_and_tables/logs.html#errors_GNU_C_version_7_1_0_linux_double_ibeta_GSL_2_1_Incomplete_Beta_Function_Large_and_Diverse_Values">And
                  other failures.</a>)<br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span>
                  Max = 574ε (Mean = 49.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.96e+04ε (Mean = 997ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.98e+04ε (Mean = 2.07e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.32e+03ε (Mean = 68.5ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Integer Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>GSL
                  2.1:</em></span> Max = 254ε (Mean = 50.9ε))<br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 62.2ε (Mean = 8.95ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 4.45ε (Mean = 0.814ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 44.5ε (Mean = 10.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.85ε (Mean = 0.791ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.sf_beta.ibeta_function.table_ibetac"></a><p class="title"><b>Table 8.19. Error rates for ibetac</b></p>
<div class="table-contents"><table class="table" summary="Error rates for ibetac">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 22.4ε (Mean = 3.67ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 10.6ε (Mean = 2.22ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 13.8ε (Mean = 2.68ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.94ε (Mean = 1.71ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 204ε (Mean = 25.8ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 73.9ε (Mean = 11.2ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 132ε (Mean = 19.8ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 56.7ε (Mean = 14.3ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Large and Diverse Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.981ε (Mean = 0.0573ε)</span><br>
                  <br> (<span class="emphasis"><em>Rmath 3.2.3:</em></span> Max = 889ε (Mean = 68.4ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.45e+04ε (Mean = 1.32e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.31e+04ε (Mean = 2.04e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.88e+03ε (Mean = 82.7ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Integer Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span><br> <br> (<span class="emphasis"><em>Rmath
                  3.2.3:</em></span> Max = 84.6ε (Mean = 18ε))
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 5.34ε (Mean = 1.11ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 107ε (Mean = 17.1ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.37ε (Mean = 1.03ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.sf_beta.ibeta_function.table_beta_incomplete_"></a><p class="title"><b>Table 8.20. Error rates for beta (incomplete)</b></p>
<div class="table-contents"><table class="table" summary="Error rates for beta (incomplete)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0ε (Mean = 0ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.1ε (Mean = 2.32ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 18.7ε (Mean = 3.19ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.94ε (Mean = 2.17ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.568ε (Mean = 0.0254ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 69.2ε (Mean = 13.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 174ε (Mean = 25ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 90ε (Mean = 12.7ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Large and Diverse Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.999ε (Mean = 0.0325ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.84e+04ε (Mean = 2.76e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 6.86e+04ε (Mean = 2.79e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 633ε (Mean = 29.7ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Integer Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.786ε (Mean = 0.0323ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.6ε (Mean = 3.6ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 51.8ε (Mean = 11ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 26ε (Mean = 6.28ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="math_toolkit.sf_beta.ibeta_function.table_betac"></a><p class="title"><b>Table 8.21. Error rates for betac</b></p>
<div class="table-contents"><table class="table" summary="Error rates for betac">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> double
                </p>
              </th>
<th>
                <p>
                  GNU C++ version 7.1.0<br> linux<br> long double
                </p>
              </th>
<th>
                <p>
                  Sun compiler version 0x5150<br> Sun Solaris<br> long double
                </p>
              </th>
<th>
                <p>
                  Microsoft Visual C++ version 14.1<br> Win32<br> double
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.676ε (Mean = 0.0302ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 9.92ε (Mean = 2.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.2ε (Mean = 2.94ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 8.94ε (Mean = 2.06ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Medium Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.949ε (Mean = 0.098ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 63.5ε (Mean = 13.5ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 97.6ε (Mean = 24.3ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 90.6ε (Mean = 14.8ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Large and Diverse Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.12ε (Mean = 0.0458ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.05e+05ε (Mean = 5.45e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 1.04e+05ε (Mean = 5.46e+03ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 3.72e+03ε (Mean = 113ε)</span>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  Incomplete Beta Function: Small Integer Values
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 0.586ε (Mean = 0.0314ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 11.1ε (Mean = 3.65ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 103ε (Mean = 17.4ε)</span>
                </p>
              </td>
<td>
                <p>
                  <span class="blue">Max = 26.2ε (Mean = 6.36ε)</span>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><h5>
<a name="math_toolkit.sf_beta.ibeta_function.h3"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.ibeta_function.testing"></a></span><a class="link" href="ibeta_function.html#math_toolkit.sf_beta.ibeta_function.testing">Testing</a>
      </h5>
<p>
        There are two sets of tests: spot tests compare values taken from <a href="http://functions.wolfram.com/webMathematica/FunctionEvaluation.jsp?name=BetaRegularized" target="_top">Mathworld's
        online function evaluator</a> with this implementation: they provide
        a basic "sanity check" for the implementation, with one spot-test
        in each implementation-domain (see implementation notes below).
      </p>
<p>
        Accuracy tests use data generated at very high precision (with <a href="http://shoup.net/ntl/doc/RR.txt" target="_top">NTL
        RR class</a> set at 1000-bit precision), using the "textbook"
        continued fraction representation (refer to the first continued fraction
        in the implementation discussion below). Note that this continued fraction
        is <span class="emphasis"><em>not</em></span> used in the implementation, and therefore we
        have test data that is fully independent of the code.
      </p>
<h5>
<a name="math_toolkit.sf_beta.ibeta_function.h4"></a>
        <span class="phrase"><a name="math_toolkit.sf_beta.ibeta_function.implementation"></a></span><a class="link" href="ibeta_function.html#math_toolkit.sf_beta.ibeta_function.implementation">Implementation</a>
      </h5>
<p>
        This implementation is closely based upon <a href="http://portal.acm.org/citation.cfm?doid=131766.131776" target="_top">"Algorithm
        708; Significant digit computation of the incomplete beta function ratios",
        DiDonato and Morris, ACM, 1992.</a>
      </p>
<p>
        All four of these functions share a common implementation: this is passed
        both x and y, and can return either p or q where these are related by:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta_inv5.svg"></span>

        </p></blockquote></div>
<p>
        so at any point we can swap a for b, x for y and p for q if this results
        in a more favourable position. Generally such swaps are performed so that
        we always compute a value less than 0.9: when required this can then be subtracted
        from 1 without undue cancellation error.
      </p>
<p>
        The following continued fraction representation is found in many textbooks
        but is not used in this implementation - it's both slower and less accurate
        than the alternatives - however it is used to generate test data:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta5.svg"></span>

        </p></blockquote></div>
<p>
        The following continued fraction is due to <a href="http://portal.acm.org/citation.cfm?doid=131766.131776" target="_top">Didonato
        and Morris</a>, and is used in this implementation when a and b are both
        greater than 1:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta6.svg"></span>

        </p></blockquote></div>
<p>
        For smallish b and x then a series representation can be used:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta7.svg"></span>

        </p></blockquote></div>
<p>
        When b &lt;&lt; a then the transition from 0 to 1 occurs very close to x
        = 1 and some care has to be taken over the method of computation, in that
        case the following series representation is used:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta8.svg"></span>

        </p></blockquote></div>
<p>
        Where Q(a,x) is an <a href="http://functions.wolfram.com/GammaBetaErf/Gamma2/" target="_top">incomplete
        gamma function</a>. Note that this method relies on keeping a table of
        all the p<sub>n </sub> previously computed, which does limit the precision of the method,
        depending upon the size of the table used.
      </p>
<p>
        When <span class="emphasis"><em>a</em></span> and <span class="emphasis"><em>b</em></span> are both small integers,
        then we can relate the incomplete beta to the binomial distribution and use
        the following finite sum:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta12.svg"></span>

        </p></blockquote></div>
<p>
        Finally we can sidestep difficult areas, or move to an area with a more efficient
        means of computation, by using the duplication formulae:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta10.svg"></span>

        </p></blockquote></div>
<div class="blockquote"><blockquote class="blockquote"><p>
          <span class="inlinemediaobject"><img src="../../../equations/ibeta11.svg"></span>

        </p></blockquote></div>
<p>
        The domains of a, b and x for which the various methods are used are identical
        to those described in the <a href="http://portal.acm.org/citation.cfm?doid=131766.131776" target="_top">Didonato
        and Morris TOMS 708 paper</a>.
      </p>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="beta_function.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../sf_beta.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ibeta_inv_function.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
