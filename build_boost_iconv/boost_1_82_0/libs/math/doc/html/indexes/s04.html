<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Macro Index</title>
<link rel="stylesheet" href="../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../indexes.html" title="Chapter 25. Indexes">
<link rel="prev" href="s03.html" title="Typedef Index">
<link rel="next" href="s05.html" title="Index">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="s03.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../indexes.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="s05.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="idm206355"></a>Macro Index</h2></div></div></div>
<p><a class="link" href="s04.html#idx_id_102">B</a> <a class="link" href="s04.html#idx_id_106">F</a></p>
<div class="variablelist"><dl class="variablelist">
<dt>
<a name="idx_id_102"></a><span class="term">B</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_DEFINE_MATH_CONSTANT</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/new_const.html" title="Defining New Constants"><span class="index-entry-level-1">Defining New Constants</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/constants_faq.html" title="Math Constants FAQs"><span class="index-entry-level-1">Math Constants FAQs</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_FLOAT128_C</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/exact_typdefs.html" title="Exact-Width Floating-Point typedefs"><span class="index-entry-level-1">Exact-Width Floating-Point typedef s</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/examples.html" title="Examples"><span class="index-entry-level-1">Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/macros.html" title="Floating-Point Constant Macros"><span class="index-entry-level-1">Floating-Point Constant Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/greatest_typdefs.html" title="Greatest-width floating-point typedef"><span class="index-entry-level-1">Greatest-width floating-point typedef</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_FLOAT16_C</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/exact_typdefs.html" title="Exact-Width Floating-Point typedefs"><span class="index-entry-level-1">Exact-Width Floating-Point typedef s</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/macros.html" title="Floating-Point Constant Macros"><span class="index-entry-level-1">Floating-Point Constant Macros</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_FLOAT32_C</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/exact_typdefs.html" title="Exact-Width Floating-Point typedefs"><span class="index-entry-level-1">Exact-Width Floating-Point typedef s</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/examples.html" title="Examples"><span class="index-entry-level-1">Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/macros.html" title="Floating-Point Constant Macros"><span class="index-entry-level-1">Floating-Point Constant Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/greatest_typdefs.html" title="Greatest-width floating-point typedef"><span class="index-entry-level-1">Greatest-width floating-point typedef</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_FLOAT64_C</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/exact_typdefs.html" title="Exact-Width Floating-Point typedefs"><span class="index-entry-level-1">Exact-Width Floating-Point typedef s</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/macros.html" title="Floating-Point Constant Macros"><span class="index-entry-level-1">Floating-Point Constant Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/greatest_typdefs.html" title="Greatest-width floating-point typedef"><span class="index-entry-level-1">Greatest-width floating-point typedef</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_FLOAT80_C</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/exact_typdefs.html" title="Exact-Width Floating-Point typedefs"><span class="index-entry-level-1">Exact-Width Floating-Point typedef s</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/examples.html" title="Examples"><span class="index-entry-level-1">Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/macros.html" title="Floating-Point Constant Macros"><span class="index-entry-level-1">Floating-Point Constant Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/greatest_typdefs.html" title="Greatest-width floating-point typedef"><span class="index-entry-level-1">Greatest-width floating-point typedef</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_FLOATMAX_C</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/macros.html" title="Floating-Point Constant Macros"><span class="index-entry-level-1">Floating-Point Constant Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/greatest_typdefs.html" title="Greatest-width floating-point typedef"><span class="index-entry-level-1">Greatest-width floating-point typedef</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_FPU_EXCEPTION_GUARD</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_impl.html" title="Implementation"><span class="index-entry-level-1">Implementation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_HAS_LOG1P</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/log1p.html" title="log1p"><span class="index-entry-level-1">log1p</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_JOIN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/test_data.html" title="Graphing, Profiling, and Generating Test Data for Special Functions"><span class="index-entry-level-1">Graphing, Profiling, and Generating Test Data for Special Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_ASSERT</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/arcine_dist.html" title="Arcsine Distribution"><span class="index-entry-level-1">Arcsine Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/binom_eg/binomial_quiz_example.html" title="Binomial Quiz Example"><span class="index-entry-level-1">Binomial Quiz Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/examples.html" title="Examples"><span class="index-entry-level-1">Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/hyperexponential_dist.html" title="Hyperexponential Distribution"><span class="index-entry-level-1">Hyperexponential Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/facets_intro.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/polynomials.html" title="Polynomials"><span class="index-entry-level-1">Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/series_evaluation.html" title="Series Evaluation"><span class="index-entry-level-1">Series Evaluation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_ASSERT_UNDEFINED_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/changing_policy_defaults.html" title="Changing the Policy Defaults"><span class="index-entry-level-1">Changing the Policy Defaults</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/assert_undefined.html" title="Mathematically Undefined Function Policies"><span class="index-entry-level-1">Mathematically Undefined Function Policies</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_CONTROL_FP</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_DECLARE_DISTRIBUTIONS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/namespace_pol.html" title="Setting Polices at Namespace Scope"><span class="index-entry-level-1">Setting Polices at Namespace Scope</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/namespace_policies.html" title="Setting Policies at Namespace or Translation Unit Scope"><span class="index-entry-level-1">Setting Policies at Namespace or Translation Unit Scope</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tradoffs.html" title="Trading Accuracy for Performance"><span class="index-entry-level-1">Trading Accuracy for Performance</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_DECLARE_SPECIAL_FUNCTIONS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/namespace_pol.html" title="Setting Polices at Namespace Scope"><span class="index-entry-level-1">Setting Polices at Namespace Scope</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/namespace_policies.html" title="Setting Policies at Namespace or Translation Unit Scope"><span class="index-entry-level-1">Setting Policies at Namespace or Translation Unit Scope</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tradoffs.html" title="Trading Accuracy for Performance"><span class="index-entry-level-1">Trading Accuracy for Performance</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_DENORM_ERROR_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_DIGITS10_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_DISABLE_FLOAT128</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_DISCRETE_QUANTILE_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/binom_eg/binomial_quiz_example.html" title="Binomial Quiz Example"><span class="index-entry-level-1">Binomial Quiz Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/geometric_eg.html" title="Geometric Distribution Examples"><span class="index-entry-level-1">Geometric Distribution Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/neg_binom_eg/negative_binomial_example1.html" title="Negative Binomial Sales Quota Example."><span class="index-entry-level-1">Negative Binomial Sales Quota Example.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_DOMAIN_ERROR_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/changing_policy_defaults.html" title="Changing the Policy Defaults"><span class="index-entry-level-1">Changing the Policy Defaults</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/error_eg.html" title="Error Handling Example"><span class="index-entry-level-1">Error Handling Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_EVALUATION_ERROR_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_EXPLICIT_TEMPLATE_TYPE_SPEC</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/new_const.html" title="Defining New Constants"><span class="index-entry-level-1">Defining New Constants</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_INDETERMINATE_RESULT_ERROR_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_INSTRUMENT_CODE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_INSTRUMENT_FPU</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_INSTRUMENT_VARIABLE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_INT_TABLE_TYPE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_tuning" title="Table 1.12. Boost.Math Tuning"><span class="index-entry-level-1">Boost.Math Tuning</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html" title="Performance Tuning Macros"><span class="index-entry-level-1">Performance Tuning Macros</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_INT_VALUE_SUFFIX</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_tuning" title="Table 1.12. Boost.Math Tuning"><span class="index-entry-level-1">Boost.Math Tuning</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_MAX_POLY_ORDER</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_tuning" title="Table 1.12. Boost.Math Tuning"><span class="index-entry-level-1">Boost.Math Tuning</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html" title="Performance Tuning Macros"><span class="index-entry-level-1">Performance Tuning Macros</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_MAX_ROOT_ITERATION_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/iteration_pol.html" title="Iteration Limits Policies"><span class="index-entry-level-1">Iteration Limits Policies</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_MAX_SERIES_ITERATION_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/iteration_pol.html" title="Iteration Limits Policies"><span class="index-entry-level-1">Iteration Limits Policies</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_NO_ATOMIC_INT</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_thread.html" title="Thread Safety"><span class="index-entry-level-1">Thread Safety</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_NO_DEDUCED_FUNCTION_POINTERS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_NO_LONG_DOUBLE_MATH_FUNCTIONS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/compilers_overview.html#math_toolkit.compilers_overview.supported_tested_compilers" title="Table 1.9. Supported/Tested Compilers"><span class="index-entry-level-1">Supported/Tested Compilers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_NO_REAL_CONCEPT_TESTS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_OVERFLOW_ERROR_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/changing_policy_defaults.html" title="Changing the Policy Defaults"><span class="index-entry-level-1">Changing the Policy Defaults</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/ct_pow.html" title="Compile Time Power of a Runtime Base"><span class="index-entry-level-1">Compile Time Power of a Runtime Base</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/error_eg.html" title="Error Handling Example"><span class="index-entry-level-1">Error Handling Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/geometric_eg.html" title="Geometric Distribution Examples"><span class="index-entry-level-1">Geometric Distribution Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/neg_binom_eg/negative_binomial_example1.html" title="Negative Binomial Sales Quota Example."><span class="index-entry-level-1">Negative Binomial Sales Quota Example.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_POLE_ERROR_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_POLY_METHOD</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_tuning" title="Table 1.12. Boost.Math Tuning"><span class="index-entry-level-1">Boost.Math Tuning</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html" title="Performance Tuning Macros"><span class="index-entry-level-1">Performance Tuning Macros</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_PROMOTE_DOUBLE_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_PROMOTE_FLOAT_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_RATIONAL_METHOD</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_tuning" title="Table 1.12. Boost.Math Tuning"><span class="index-entry-level-1">Boost.Math Tuning</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html" title="Performance Tuning Macros"><span class="index-entry-level-1">Performance Tuning Macros</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_ROUNDING_ERROR_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_SMALL_CONSTANT</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_STANDALONE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/error_handling.html" title="Error Handling"><span class="index-entry-level-1">Error Handling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/standalone.html" title="Standalone Usage"><span class="index-entry-level-1">Standalone Usage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_STD_USING</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/new_const.html" title="Defining New Constants"><span class="index-entry-level-1">Defining New Constants</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_impl.html" title="Implementation"><span class="index-entry-level-1">Implementation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_TEST_VALUE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_UNDERFLOW_ERROR_POLICY</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/policy_defaults.html" title="Using Macros to Change the Policy Defaults"><span class="index-entry-level-1">Using Macros to Change the Policy Defaults</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_USE_C99</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_USE_FLOAT128</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/config_macros.html#math_toolkit.config_macros.boost_math_macros" title="Table 1.11. Boost.Math Macros"><span class="index-entry-level-1">Boost.Math Macros</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_STRINGIZE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/changing_policy_defaults.html" title="Changing the Policy Defaults"><span class="index-entry-level-1">Changing the Policy Defaults</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/error_eg.html" title="Error Handling Example"><span class="index-entry-level-1">Error Handling Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/test_data.html" title="Graphing, Profiling, and Generating Test Data for Special Functions"><span class="index-entry-level-1">Graphing, Profiling, and Generating Test Data for Special Functions</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_106"></a><span class="term">F</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">FP_INFINITE</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">FP_NAN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">FP_NORMAL</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">FP_SUBNORMAL</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">FP_ZERO</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li></ul></div>
</li>
</ul></div></dd>
</dl></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="s03.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../indexes.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="s05.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
