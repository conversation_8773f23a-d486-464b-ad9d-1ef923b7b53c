<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function Index</title>
<link rel="stylesheet" href="../math.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Math Toolkit 4.1.0">
<link rel="up" href="../indexes.html" title="Chapter 25. Indexes">
<link rel="prev" href="../indexes.html" title="Chapter 25. Indexes">
<link rel="next" href="s02.html" title="Class Index">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../indexes.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../indexes.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="s02.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="idm196250"></a>Function Index</h2></div></div></div>
<p><a class="link" href="s01.html#idx_id_0">1</a> <a class="link" href="s01.html#idx_id_1">2</a> <a class="link" href="s01.html#idx_id_2">4</a> <a class="link" href="s01.html#idx_id_5">A</a> <a class="link" href="s01.html#idx_id_6">B</a> <a class="link" href="s01.html#idx_id_7">C</a> <a class="link" href="s01.html#idx_id_8">D</a> <a class="link" href="s01.html#idx_id_9">E</a> <a class="link" href="s01.html#idx_id_10">F</a> <a class="link" href="s01.html#idx_id_11">G</a> <a class="link" href="s01.html#idx_id_12">H</a> <a class="link" href="s01.html#idx_id_13">I</a> <a class="link" href="s01.html#idx_id_14">J</a> <a class="link" href="s01.html#idx_id_15">K</a> <a class="link" href="s01.html#idx_id_16">L</a> <a class="link" href="s01.html#idx_id_17">M</a> <a class="link" href="s01.html#idx_id_18">N</a> <a class="link" href="s01.html#idx_id_19">O</a> <a class="link" href="s01.html#idx_id_20">P</a> <a class="link" href="s01.html#idx_id_21">Q</a> <a class="link" href="s01.html#idx_id_22">R</a> <a class="link" href="s01.html#idx_id_23">S</a> <a class="link" href="s01.html#idx_id_24">T</a> <a class="link" href="s01.html#idx_id_25">U</a> <a class="link" href="s01.html#idx_id_26">V</a> <a class="link" href="s01.html#idx_id_27">W</a> <a class="link" href="s01.html#idx_id_28">X</a> <a class="link" href="s01.html#idx_id_29">Y</a> <a class="link" href="s01.html#idx_id_30">Z</a> <a class="link" href="s01.html#idx_id_31">_</a></p>
<div class="variablelist"><dl class="variablelist">
<dt>
<a name="idx_id_0"></a><span class="term">1</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">10</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/diff0.html" title="Lanczos Smoothing Derivatives"><span class="index-entry-level-1">Lanczos Smoothing Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/diff.html" title="Numerical Differentiation"><span class="index-entry-level-1">Numerical Differentiation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/signal_statistics.html" title="Signal Statistics"><span class="index-entry-level-1">Signal Statistics</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_1"></a><span class="term">2</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">2</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/arcine_dist.html" title="Arcsine Distribution"><span class="index-entry-level-1">Arcsine Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_d.html" title="Elliptic Integral D - Legendre Form"><span class="index-entry-level-1">Elliptic Integral D - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_intro.html" title="Elliptic Integral Overview"><span class="index-entry-level-1">Elliptic Integral Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_1.html" title="Elliptic Integrals of the First Kind - Legendre Form"><span class="index-entry-level-1">Elliptic Integrals of the First Kind - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_2.html" title="Elliptic Integrals of the Second Kind - Legendre Form"><span class="index-entry-level-1">Elliptic Integrals of the Second Kind - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_3.html" title="Elliptic Integrals of the Third Kind - Legendre Form"><span class="index-entry-level-1">Elliptic Integrals of the Third Kind - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_root.html" title="Finding Zeros of Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Finding Zeros of Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/kolmogorov_smirnov_dist.html" title="Kolmogorov-Smirnov Distribution"><span class="index-entry-level-1">Kolmogorov-Smirnov Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre_stieltjes.html" title="Legendre-Stieltjes Polynomials"><span class="index-entry-level-1">Legendre-Stieltjes Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/constants.html#math_toolkit.constants.mathematical_constants" title="Table 4.1. Mathematical Constants"><span class="index-entry-level-1">Mathematical Constants</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_beta_dist.html" title="Noncentral Beta Distribution"><span class="index-entry-level-1">Noncentral Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html#math_toolkit.tuning.table_Polynomial_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux" title="Table 22.8. Polynomial Method Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux"><span class="index-entry-level-1">Polynomial Method Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html#math_toolkit.tuning.table_Polynomial_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux" title="Table 22.6. Polynomial Method Comparison with GNU C++ version 9.2.1 20191008 on linux"><span class="index-entry-level-1">Polynomial Method Comparison with GNU C++ version 9.2.1 20191008 on linux</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html#math_toolkit.tuning.table_Polynomial_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux" title="Table 22.10. Polynomial Method Comparison with Intel C++ C++0x mode version 1910 on linux"><span class="index-entry-level-1">Polynomial Method Comparison with Intel C++ C++0x mode version 1910 on linux</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html#math_toolkit.tuning.table_Polynomial_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64" title="Table 22.4. Polynomial Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64"><span class="index-entry-level-1">Polynomial Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html#math_toolkit.tuning.table_Rational_Method_Comparison_with_Clang_version_9_0_0_tags_RELEASE_900_final_on_linux" title="Table 22.9. Rational Method Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux"><span class="index-entry-level-1">Rational Method Comparison with Clang version 9.0.0 (tags/RELEASE_900/final) on linux</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html#math_toolkit.tuning.table_Rational_Method_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux" title="Table 22.7. Rational Method Comparison with GNU C++ version 9.2.1 20191008 on linux"><span class="index-entry-level-1">Rational Method Comparison with GNU C++ version 9.2.1 20191008 on linux</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html#math_toolkit.tuning.table_Rational_Method_Comparison_with_Intel_C_C_0x_mode_version_1910_on_linux" title="Table 22.11. Rational Method Comparison with Intel C++ C++0x mode version 1910 on linux"><span class="index-entry-level-1">Rational Method Comparison with Intel C++ C++0x mode version 1910 on linux</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tuning.html#math_toolkit.tuning.table_Rational_Method_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64" title="Table 22.5. Rational Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64"><span class="index-entry-level-1">Rational Method Comparison with Microsoft Visual C++ version 14.2 on Windows x64</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/sph_harm.html" title="Spherical Harmonics"><span class="index-entry-level-1">Spherical Harmonics</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_2"></a><span class="term">4</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">41</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li></ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_5"></a><span class="term">A</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">A</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/kolmogorov_smirnov_dist.html" title="Kolmogorov-Smirnov Distribution"><span class="index-entry-level-1">Kolmogorov-Smirnov Distribution</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">abscissa</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_cubic_b.html" title="Cardinal Cubic B-spline interpolation"><span class="index-entry-level-1">Cardinal Cubic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/gauss_kronrod.html" title="Gauss-Kronrod Quadrature"><span class="index-entry-level-1">Gauss-Kronrod Quadrature</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/gauss.html" title="Gauss-Legendre quadrature"><span class="index-entry-level-1">Gauss-Legendre quadrature</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/brent_minima.html" title="Locating Function Minima using Brent's algorithm"><span class="index-entry-level-1">Locating Function Minima using Brent's algorithm</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">absolute_gini_coefficient</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/signal_statistics.html" title="Signal Statistics"><span class="index-entry-level-1">Signal Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">acosh</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/inv_hyper/acosh.html" title="acosh"><span class="index-entry-level-1">acosh</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">acoshf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">acoshl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">addition</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/fibonacci_numbers.html" title="Fibonacci Numbers"><span class="index-entry-level-1">Fibonacci Numbers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">add_fn</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">agm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/agm.html" title="Arithmetic-Geometric Mean"><span class="index-entry-level-1">Arithmetic-Geometric Mean</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/constants.html#math_toolkit.constants.mathematical_constants" title="Table 4.1. Mathematical Constants"><span class="index-entry-level-1">Mathematical Constants</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Ai</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/airy/airy_root.html" title="Finding Zeros of Airy Functions"><span class="index-entry-level-1">Finding Zeros of Airy Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_root.html" title="Finding Zeros of Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Finding Zeros of Bessel Functions of the First and Second Kinds</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">airy_ai</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/airy/ai.html" title="Airy Ai Function"><span class="index-entry-level-1">Airy Ai Function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">airy_ai_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/airy/aip.html" title="Airy Ai' Function"><span class="index-entry-level-1">Airy Ai' Function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">airy_ai_zero</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/airy/airy_root.html" title="Finding Zeros of Airy Functions"><span class="index-entry-level-1">Finding Zeros of Airy Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">airy_bi</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/airy/bi.html" title="Airy Bi Function"><span class="index-entry-level-1">Airy Bi Function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">airy_bi_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/airy/bip.html" title="Airy Bi' Function"><span class="index-entry-level-1">Airy Bi' Function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">airy_bi_zero</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/airy/airy_root.html" title="Finding Zeros of Airy Functions"><span class="index-entry-level-1">Finding Zeros of Airy Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">al</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">anderson_darling_normality_statistic</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/anderson_darling.html" title="The Anderson-Darling Test"><span class="index-entry-level-1">The Anderson-Darling Test</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">apply_recurrence_relation_backward</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/recurrence.html" title="Tools For 3-Term Recurrence Relations"><span class="index-entry-level-1">Tools For 3-Term Recurrence Relations</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">apply_recurrence_relation_forward</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/recurrence.html" title="Tools For 3-Term Recurrence Relations"><span class="index-entry-level-1">Tools For 3-Term Recurrence Relations</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">area</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tutorial/non_templ.html" title="Use in non-template code"><span class="index-entry-level-1">Use in non-template code</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tutorial/templ.html" title="Use in template code"><span class="index-entry-level-1">Use in template code</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">arguments</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/factorials/sf_binomial.html" title="Binomial Coefficients"><span class="index-entry-level-1">Binomial Coefficients</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">asinh</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/inv_hyper/asinh.html" title="asinh"><span class="index-entry-level-1">asinh</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">asinhf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">asinhl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">assert</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/facets_intro.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">assoc_laguerre</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/comparisons.html#math_toolkit.comparisons.table_Library_Comparison_with_GNU_C_version_9_2_1_20191008_on_linux" title="Table 22.15. Library Comparison with GNU C++ version 9.2.1 20191008 on linux"><span class="index-entry-level-1">Library Comparison with GNU C++ version 9.2.1 20191008 on linux</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">assoc_laguerref</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">assoc_laguerrel</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">assoc_legendre</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">assoc_legendref</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">assoc_legendrel</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">at</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bilinear_uniform.html" title="Bilinear Uniform Interpolation"><span class="index-entry-level-1">Bilinear Uniform Interpolation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">atanh</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/inv_hyper/atanh.html" title="atanh"><span class="index-entry-level-1">atanh</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">atanhf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">atanhl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_6"></a><span class="term">B</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">background_color</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">barycentric_rational</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/barycentric.html" title="Barycentric Rational Interpolation"><span class="index-entry-level-1">Barycentric Rational Interpolation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">bernoulli_b2n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/bernoulli_numbers.html" title="Bernoulli Numbers"><span class="index-entry-level-1">Bernoulli Numbers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">beta</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/sf_beta/beta_function.html" title="Beta"><span class="index-entry-level-1">Beta</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/factorials/sf_binomial.html" title="Binomial Coefficients"><span class="index-entry-level-1">Binomial Coefficients</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_function.html#math_toolkit.sf_beta.ibeta_function.table_beta_incomplete_" title="Table 8.20. Error rates for beta (incomplete)"><span class="index-entry-level-1">Error rates for beta (incomplete)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_function.html" title="Incomplete Beta Functions"><span class="index-entry-level-1">Incomplete Beta Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_beta_dist.html" title="Noncentral Beta Distribution"><span class="index-entry-level-1">Noncentral Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">betac</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_function.html" title="Incomplete Beta Functions"><span class="index-entry-level-1">Incomplete Beta Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">betaf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">betal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">binomial_coefficient</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/factorials/sf_binomial.html" title="Binomial Coefficients"><span class="index-entry-level-1">Binomial Coefficients</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">bisect</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_noderiv/bisect.html" title="Bisection"><span class="index-entry-level-1">Bisection</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_noderiv.html" title="Root Finding Without Derivatives"><span class="index-entry-level-1">Root Finding Without Derivatives</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">black_body</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/color_maps.html" title="Color Maps"><span class="index-entry-level-1">Color Maps</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">bool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/polynomials.html" title="Polynomials"><span class="index-entry-level-1">Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_FLOAT128_C</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/greatest_typdefs.html" title="Greatest-width floating-point typedef"><span class="index-entry-level-1">Greatest-width floating-point typedef</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_MATH_DECLARE_SPECIAL_FUNCTIONS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/namespace_policies.html" title="Setting Policies at Namespace or Translation Unit Scope"><span class="index-entry-level-1">Setting Policies at Namespace or Translation Unit Scope</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">bracket_and_solve_root</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/roots_noderiv/bracket_solve.html" title="Bracket and Solve Root"><span class="index-entry-level-1">Bracket and Solve Root</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/cbrt_eg.html" title="Finding the Cubed Root With and Without Derivatives"><span class="index-entry-level-1">Finding the Cubed Root With and Without Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_noderiv.html" title="Root Finding Without Derivatives"><span class="index-entry-level-1">Root Finding Without Derivatives</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">branch</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hints.html" title="Other Hints and tips"><span class="index-entry-level-1">Other Hints and tips</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">brent_find_minima</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/brent_minima.html" title="Locating Function Minima using Brent's algorithm"><span class="index-entry-level-1">Locating Function Minima using Brent's algorithm</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">by</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/conventions.html" title="Document Conventions"><span class="index-entry-level-1">Document Conventions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/airy/airy_root.html" title="Finding Zeros of Airy Functions"><span class="index-entry-level-1">Finding Zeros of Airy Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html" title="Incomplete Gamma Functions"><span class="index-entry-level-1">Incomplete Gamma Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/issues.html" title="Known Issues, and TODO List"><span class="index-entry-level-1">Known Issues, and TODO List</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/vector_barycentric.html" title="Vector-valued Barycentric Rational Interpolation"><span class="index-entry-level-1">Vector-valued Barycentric Rational Interpolation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">bytes</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/daubechies.html" title="Daubechies Wavelets and Scaling Functions"><span class="index-entry-level-1">Daubechies Wavelets and Scaling Functions</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_7"></a><span class="term">C</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">c</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/triangular_dist.html" title="Triangular Distribution"><span class="index-entry-level-1">Triangular Distribution</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">called</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_impl.html" title="Implementation"><span class="index-entry-level-1">Implementation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cancel</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/naive_monte_carlo.html" title="Naive Monte Carlo Integration"><span class="index-entry-level-1">Naive Monte Carlo Integration</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cardinal_b_spline</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/cardinal_b_splines.html" title="Cardinal B-splines"><span class="index-entry-level-1">Cardinal B-splines</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cardinal_b_spline_double_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/cardinal_b_splines.html" title="Cardinal B-splines"><span class="index-entry-level-1">Cardinal B-splines</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cardinal_b_spline_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/cardinal_b_splines.html" title="Cardinal B-splines"><span class="index-entry-level-1">Cardinal B-splines</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cardinal_cubic_b_spline</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_cubic_b.html" title="Cardinal Cubic B-spline interpolation"><span class="index-entry-level-1">Cardinal Cubic B-spline interpolation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">case</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/extreme_dist.html" title="Extreme Value Distribution"><span class="index-entry-level-1">Extreme Value Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/facets_intro.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/constants_faq.html" title="Math Constants FAQs"><span class="index-entry-level-1">Math Constants FAQs</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/relative_error.html" title="Relative Error"><span class="index-entry-level-1">Relative Error</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cbrt</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/powers/cbrt.html" title="cbrt"><span class="index-entry-level-1">cbrt</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples.html" title="Examples of Root-Finding (with and without derivatives)"><span class="index-entry-level-1">Examples of Root-Finding (with and without derivatives)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/comparisons.html#math_toolkit.comparisons.table_Library_Comparison_with_Microsoft_Visual_C_version_14_2_on_Windows_x64" title="Table 22.14. Library Comparison with Microsoft Visual C++ version 14.2 on Windows x64"><span class="index-entry-level-1">Library Comparison with Microsoft Visual C++ version 14.2 on Windows x64</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/multiprecision_root.html" title="Root-finding using Boost.Multiprecision"><span class="index-entry-level-1">Root-finding using Boost.Multiprecision</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cbrtf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cbrtl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cdf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/arcine_dist.html" title="Arcsine Distribution"><span class="index-entry-level-1">Arcsine Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/binom_eg/binomial_coinflip_example.html" title="Binomial Coin-Flipping Example"><span class="index-entry-level-1">Binomial Coin-Flipping Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/discrete_quant_ref.html" title="Discrete Quantile Policies"><span class="index-entry-level-1">Discrete Quantile Policies</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/future.html" title="Extras/Future Directions"><span class="index-entry-level-1">Extras/Future Directions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/generic.html" title="Generic operations common to all distributions are non-member functions"><span class="index-entry-level-1">Generic operations common to all distributions are non-member functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/neg_binom_eg/negative_binomial_example1.html" title="Negative Binomial Sales Quota Example."><span class="index-entry-level-1">Negative Binomial Sales Quota Example.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">changesign</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sign_functions.html" title="Sign Manipulation Functions"><span class="index-entry-level-1">Sign Manipulation Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">chebyshev_clenshaw_recurrence</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">chebyshev_next</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">chebyshev_t</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">chebyshev_transform</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">chebyshev_t_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">chebyshev_u</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">checked_narrowing_cast</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/error_handling.html" title="Error Handling"><span class="index-entry-level-1">Error Handling</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">chf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">clip</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/cohen_acceleration.html" title="Cohen Acceleration"><span class="index-entry-level-0">cohen_acceleration</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">complement</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/st_eg/tut_mean_intervals.html" title="Calculating confidence intervals on the mean with the Students-t distribution"><span class="index-entry-level-1">Calculating confidence intervals on the mean with the Students-t distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/complements.html" title="Complements are supported too - and when to use them"><span class="index-entry-level-1">Complements are supported too - and when to use them</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/summary.html" title="Summary"><span class="index-entry-level-1">Summary</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">complex_newton</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_deriv.html" title="Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder"><span class="index-entry-level-1">Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">complex_step_derivative</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/diff.html" title="Numerical Differentiation"><span class="index-entry-level-1">Numerical Differentiation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">compute</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/new_const.html" title="Defining New Constants"><span class="index-entry-level-1">Defining New Constants</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">comp_ellint_1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">comp_ellint_1f</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">comp_ellint_1l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">comp_ellint_2</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">comp_ellint_2f</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">comp_ellint_2l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">comp_ellint_3</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">comp_ellint_3f</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">comp_ellint_3l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">conf_hyperg</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">conf_hypergf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">conf_hypergl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">conj</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_value_ops.html" title="Octonion Value Operations"><span class="index-entry-level-1">Octonion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/value_op.html" title="Quaternion Value Operations"><span class="index-entry-level-1">Quaternion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">constexpr</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/constants_intro.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">constructors</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_mem_fun.html" title="Octonion Member Functions"><span class="index-entry-level-1">Octonion Member Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_mem_fun.html" title="Quaternion Member Functions"><span class="index-entry-level-1">Quaternion Member Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">continued_fraction_a</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/cf.html" title="Continued Fraction Evaluation"><span class="index-entry-level-1">Continued Fraction Evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">continued_fraction_b</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/cf.html" title="Continued Fraction Evaluation"><span class="index-entry-level-1">Continued Fraction Evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">converge</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/minimax.html" title="Minimax Approximations and the Remez Algorithm"><span class="index-entry-level-1">Minimax Approximations and the Remez Algorithm</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/mbessel.html" title="Modified Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Modified Bessel Functions of the First and Second Kinds</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">convergence</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_deriv.html" title="Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder"><span class="index-entry-level-1">Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">copysign</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sign_functions.html" title="Sign Manipulation Functions"><span class="index-entry-level-1">Sign Manipulation Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">copysignf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">copysignl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">correlation_coefficient</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bivariate_statistics.html" title="Bivariate Statistics"><span class="index-entry-level-1">Bivariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/cos_pi.html" title="cos_pi"><span class="index-entry-level-0">cos_pi</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">covariance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bivariate_statistics.html" title="Bivariate Statistics"><span class="index-entry-level-1">Bivariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">crop_color</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cubic_roots</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cubic_roots.html" title="Roots of Cubic Polynomials"><span class="index-entry-level-1">Roots of Cubic Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cubic_root_condition_number</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cubic_roots.html" title="Roots of Cubic Polynomials"><span class="index-entry-level-1">Roots of Cubic Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cylindrical</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_create.html" title="Octonion Creation Functions"><span class="index-entry-level-1">Octonion Creation Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/create.html" title="Quaternion Creation Functions"><span class="index-entry-level-1">Quaternion Creation Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cylindrospherical</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/create.html" title="Quaternion Creation Functions"><span class="index-entry-level-1">Quaternion Creation Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_i</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/logs.html" title="Error Logs For Error Rate Tables"><span class="index-entry-level-1">Error Logs For Error Rate Tables</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/mbessel.html#math_toolkit.bessel.mbessel.table_cyl_bessel_i_integer_orders_" title="Table 8.44. Error rates for cyl_bessel_i (integer orders)"><span class="index-entry-level-1">Error rates for cyl_bessel_i (integer orders)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/mbessel.html" title="Modified Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Modified Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_if</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_il</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_i_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_derivatives.html" title="Derivatives of the Bessel Functions"><span class="index-entry-level-1">Derivatives of the Bessel Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_derivatives.html#math_toolkit.bessel.bessel_derivatives.table_cyl_bessel_i_prime_integer_orders_" title="Table 8.50. Error rates for cyl_bessel_i_prime (integer orders)"><span class="index-entry-level-1">Error rates for cyl_bessel_i_prime (integer orders)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_j</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/logs.html" title="Error Logs For Error Rate Tables"><span class="index-entry-level-1">Error Logs For Error Rate Tables</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html#math_toolkit.bessel.bessel_first.table_cyl_bessel_j_integer_orders_" title="Table 8.40. Error rates for cyl_bessel_j (integer orders)"><span class="index-entry-level-1">Error rates for cyl_bessel_j (integer orders)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_jl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_j_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_derivatives.html" title="Derivatives of the Bessel Functions"><span class="index-entry-level-1">Derivatives of the Bessel Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_derivatives.html#math_toolkit.bessel.bessel_derivatives.table_cyl_bessel_j_prime_integer_orders_" title="Table 8.52. Error rates for cyl_bessel_j_prime (integer orders)"><span class="index-entry-level-1">Error rates for cyl_bessel_j_prime (integer orders)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_j_zero</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_root.html" title="Finding Zeros of Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Finding Zeros of Bessel Functions of the First and Second Kinds</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_k</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/logs.html" title="Error Logs For Error Rate Tables"><span class="index-entry-level-1">Error Logs For Error Rate Tables</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/mbessel.html#math_toolkit.bessel.mbessel.table_cyl_bessel_k_integer_orders_" title="Table 8.46. Error rates for cyl_bessel_k (integer orders)"><span class="index-entry-level-1">Error rates for cyl_bessel_k (integer orders)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/mbessel.html" title="Modified Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Modified Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_kf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_kl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_bessel_k_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_derivatives.html" title="Derivatives of the Bessel Functions"><span class="index-entry-level-1">Derivatives of the Bessel Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_derivatives.html#math_toolkit.bessel.bessel_derivatives.table_cyl_bessel_k_prime_integer_orders_" title="Table 8.54. Error rates for cyl_bessel_k_prime (integer orders)"><span class="index-entry-level-1">Error rates for cyl_bessel_k_prime (integer orders)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_hankel_1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hankel/cyl_hankel.html" title="Cyclic Hankel Functions"><span class="index-entry-level-1">Cyclic Hankel Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_hankel_2</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hankel/cyl_hankel.html" title="Cyclic Hankel Functions"><span class="index-entry-level-1">Cyclic Hankel Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_neumann</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/logs.html" title="Error Logs For Error Rate Tables"><span class="index-entry-level-1">Error Logs For Error Rate Tables</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html#math_toolkit.bessel.bessel_first.table_cyl_neumann_integer_orders_" title="Table 8.42. Error rates for cyl_neumann (integer orders)"><span class="index-entry-level-1">Error rates for cyl_neumann (integer orders)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_neumannl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_neumann_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_derivatives.html" title="Derivatives of the Bessel Functions"><span class="index-entry-level-1">Derivatives of the Bessel Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/all_table.html#math_toolkit.logs_and_tables.all_table.table_cyl_neumann_prime_integer_orders_" title="Table 23.25. Error rates for cyl_neumann_prime (integer orders)"><span class="index-entry-level-1">Error rates for cyl_neumann_prime (integer orders)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cyl_neumann_zero</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_root.html" title="Finding Zeros of Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Finding Zeros of Bessel Functions of the First and Second Kinds</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_8"></a><span class="term">D</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">D</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_d.html" title="Elliptic Integral D - Legendre Form"><span class="index-entry-level-1">Elliptic Integral D - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_intro.html" title="Elliptic Integral Overview"><span class="index-entry-level-1">Elliptic Integral Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">data</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_cubic_b.html" title="Cardinal Cubic B-spline interpolation"><span class="index-entry-level-1">Cardinal Cubic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/inverse_chi_squared_eg.html" title="Inverse Chi-Squared Distribution Bayes Example"><span class="index-entry-level-1">Inverse Chi-Squared Distribution Bayes Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/polynomials.html" title="Polynomials"><span class="index-entry-level-1">Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/st_eg/tut_mean_test.html" title='Testing a sample mean for difference from a "true" mean'><span class="index-entry-level-1">Testing a sample mean for difference from a "true" mean</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">daubechies_scaling_filter</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/daubechies_filters.html" title="Daubechies Filters"><span class="index-entry-level-1">Daubechies Filters</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">daubechies_wavelet_filter</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/daubechies_filters.html" title="Daubechies Filters"><span class="index-entry-level-1">Daubechies Filters</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">defined</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/catmull_rom.html" title="Catmull-Rom Splines"><span class="index-entry-level-1">Catmull-Rom Splines</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/exact_typdefs.html" title="Exact-Width Floating-Point typedefs"><span class="index-entry-level-1">Exact-Width Floating-Point typedef s</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sign_functions.html" title="Sign Manipulation Functions"><span class="index-entry-level-1">Sign Manipulation Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">derivative</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/autodiff.html" title="Automatic Differentiation"><span class="index-entry-level-1">Automatic Differentiation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/cbrt_eg.html" title="Finding the Cubed Root With and Without Derivatives"><span class="index-entry-level-1">Finding the Cubed Root With and Without Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_deriv.html" title="Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder"><span class="index-entry-level-1">Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/digamma.html" title="Digamma"><span class="index-entry-level-0">digamma</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">dis</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/empirical_cdf.html" title="Empirical Cumulative Distribution Function"><span class="index-entry-level-1">Empirical Cumulative Distribution Function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/anderson_darling.html" title="The Anderson-Darling Test"><span class="index-entry-level-1">The Anderson-Darling Test</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ljung_box.html" title="The Ljung-Box Test"><span class="index-entry-level-1">The Ljung-Box Test</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">distribution</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/bernoulli_dist.html" title="Bernoulli Distribution"><span class="index-entry-level-1">Bernoulli Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_faq.html" title="Boost.Math Frequently Asked Questions (FAQs)"><span class="index-entry-level-1">Boost.Math Frequently Asked Questions (FAQs)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/neg_binom_eg/neg_binom_conf.html" title="Calculating Confidence Limits on the Frequency of Occurrence for the Negative Binomial Distribution"><span class="index-entry-level-1">Calculating Confidence Limits on the Frequency of Occurrence for the Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/cauchy_dist.html" title="Cauchy-Lorentz Distribution"><span class="index-entry-level-1">Cauchy-Lorentz Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/changing_policy_defaults.html" title="Changing the Policy Defaults"><span class="index-entry-level-1">Changing the Policy Defaults</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/dist_construct_eg.html" title="Distribution Construction Examples"><span class="index-entry-level-1">Distribution Construction Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/objects.html" title="Distributions are Objects"><span class="index-entry-level-1">Distributions are Objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/binom_eg/binom_size_eg.html" title="Estimating Sample Sizes for a Binomial Distribution."><span class="index-entry-level-1">Estimating Sample Sizes for a Binomial Distribution.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/neg_binom_eg/neg_binom_size_eg.html" title="Estimating Sample Sizes for the Negative Binomial."><span class="index-entry-level-1">Estimating Sample Sizes for the Negative Binomial.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/generic.html" title="Generic operations common to all distributions are non-member functions"><span class="index-entry-level-1">Generic operations common to all distributions are non-member functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/hyperexponential_dist.html" title="Hyperexponential Distribution"><span class="index-entry-level-1">Hyperexponential Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/kolmogorov_smirnov_dist.html" title="Kolmogorov-Smirnov Distribution"><span class="index-entry-level-1">Kolmogorov-Smirnov Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/normal_example/normal_misc.html" title="Some Miscellaneous Examples of the Normal (Gaussian) Distribution"><span class="index-entry-level-1">Some Miscellaneous Examples of the Normal (Gaussian) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/nccs_eg/nccs_power_eg.html" title="Tables of the power function of the chi2 test."><span class="index-entry-level-1">Tables of the power function of the chi 2 test.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/triangular_dist.html" title="Triangular Distribution"><span class="index-entry-level-1">Triangular Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">domain</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cubic_hermite.html" title="Cubic Hermite interpolation"><span class="index-entry-level-1">Cubic Hermite interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta1.html" title="Jacobi Theta Function θ1"><span class="index-entry-level-1">Jacobi Theta Function θ 1</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta2.html" title="Jacobi Theta Function θ2"><span class="index-entry-level-1">Jacobi Theta Function θ 2</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta3.html" title="Jacobi Theta Function θ3"><span class="index-entry-level-1">Jacobi Theta Function θ 3</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta4.html" title="Jacobi Theta Function θ4"><span class="index-entry-level-1">Jacobi Theta Function θ 4</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta_overview.html" title="Overview of the Jacobi Theta Functions"><span class="index-entry-level-1">Overview of the Jacobi Theta Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/owens_t.html" title="Owen's T function"><span class="index-entry-level-1">Owen's T function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/polynomials.html" title="Polynomials"><span class="index-entry-level-1">Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quintic_hermite.html" title="Quintic Hermite interpolation"><span class="index-entry-level-1">Quintic Hermite interpolation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">double</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/cbrt_comparison.html" title="Comparison of Cube Root Finding Algorithms"><span class="index-entry-level-1">Comparison of Cube Root Finding Algorithms</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/brent_minima.html" title="Locating Function Minima using Brent's algorithm"><span class="index-entry-level-1">Locating Function Minima using Brent's algorithm</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_tests.html" title="Test Program"><span class="index-entry-level-1">Test Program</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/high_precision/why_high_precision.html" title="Why use a high-precision library rather than built-in floating-point types?"><span class="index-entry-level-1">Why use a high-precision library rather than built-in floating-point types?</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/factorials/sf_double_factorial.html" title="Double Factorial"><span class="index-entry-level-0">double_factorial</span></a></p></li>
</ul></div></dd>
<dt>
<a name="idx_id_9"></a><span class="term">E</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">edit_control_point</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bezier_polynomial.html" title="Bezier Polynomials"><span class="index-entry-level-1">Bezier Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_1.html" title="Elliptic Integrals of the First Kind - Legendre Form"><span class="index-entry-level-1">Elliptic Integrals of the First Kind - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/all_table.html#math_toolkit.logs_and_tables.all_table.table_ellint_1_complete_" title="Table 23.28. Error rates for ellint_1 (complete)"><span class="index-entry-level-1">Error rates for ellint_1 (complete)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_1f</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_1l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_2</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_2.html" title="Elliptic Integrals of the Second Kind - Legendre Form"><span class="index-entry-level-1">Elliptic Integrals of the Second Kind - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/logs.html" title="Error Logs For Error Rate Tables"><span class="index-entry-level-1">Error Logs For Error Rate Tables</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/all_table.html#math_toolkit.logs_and_tables.all_table.table_ellint_2_complete_" title="Table 23.30. Error rates for ellint_2 (complete)"><span class="index-entry-level-1">Error rates for ellint_2 (complete)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_2f</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_2l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_3</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_d.html" title="Elliptic Integral D - Legendre Form"><span class="index-entry-level-1">Elliptic Integral D - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_3.html" title="Elliptic Integrals of the Third Kind - Legendre Form"><span class="index-entry-level-1">Elliptic Integrals of the Third Kind - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/logs.html" title="Error Logs For Error Rate Tables"><span class="index-entry-level-1">Error Logs For Error Rate Tables</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/all_table.html#math_toolkit.logs_and_tables.all_table.table_ellint_3_complete_" title="Table 23.32. Error rates for ellint_3 (complete)"><span class="index-entry-level-1">Error rates for ellint_3 (complete)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_3f</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_3l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_d</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_d.html" title="Elliptic Integral D - Legendre Form"><span class="index-entry-level-1">Elliptic Integral D - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_d.html#math_toolkit.ellint.ellint_d.table_ellint_d_complete_" title="Table 8.66. Error rates for ellint_d (complete)"><span class="index-entry-level-1">Error rates for ellint_d (complete)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_rc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_carlson.html" title="Elliptic Integrals - Carlson Form"><span class="index-entry-level-1">Elliptic Integrals - Carlson Form</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_rd</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_carlson.html" title="Elliptic Integrals - Carlson Form"><span class="index-entry-level-1">Elliptic Integrals - Carlson Form</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_rf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_carlson.html" title="Elliptic Integrals - Carlson Form"><span class="index-entry-level-1">Elliptic Integrals - Carlson Form</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_rg</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_carlson.html" title="Elliptic Integrals - Carlson Form"><span class="index-entry-level-1">Elliptic Integrals - Carlson Form</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ellint_rj</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_carlson.html" title="Elliptic Integrals - Carlson Form"><span class="index-entry-level-1">Elliptic Integrals - Carlson Form</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">entropy</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">envelope_color</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">epsilon</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/brent_minima.html" title="Locating Function Minima using Brent's algorithm"><span class="index-entry-level-1">Locating Function Minima using Brent's algorithm</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">epsilon_difference</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/float_comparison.html" title="Floating-point Comparison"><span class="index-entry-level-1">Floating-point Comparison</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_erf/error_function.html" title="Error Function erf and complement erfc"><span class="index-entry-level-1">Error Function erf and complement erfc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erfc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/autodiff.html" title="Automatic Differentiation"><span class="index-entry-level-1">Automatic Differentiation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_erf/error_function.html" title="Error Function erf and complement erfc"><span class="index-entry-level-1">Error Function erf and complement erfc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/normal_dist.html" title="Normal (Gaussian) Distribution"><span class="index-entry-level-1">Normal (Gaussian) Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erfcf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erfcl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erfc_inv</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_erf/error_inv.html" title="Error Function Inverses"><span class="index-entry-level-1">Error Function Inverses</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erff</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erfl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erf_inv</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_erf/error_inv.html" title="Error Function Inverses"><span class="index-entry-level-1">Error Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">eta</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_sinh_sinh.html" title="sinh_sinh"><span class="index-entry-level-1">sinh_sinh</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">evaluate_even_polynomial</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rational.html" title="Polynomial and Rational Function Evaluation"><span class="index-entry-level-1">Polynomial and Rational Function Evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">evaluate_odd_polynomial</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rational.html" title="Polynomial and Rational Function Evaluation"><span class="index-entry-level-1">Polynomial and Rational Function Evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">evaluate_polynomial</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rational.html" title="Polynomial and Rational Function Evaluation"><span class="index-entry-level-1">Polynomial and Rational Function Evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">evaluate_polynomial_estrin</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/estrin.html" title="Estrin's method for polynomial evaluation"><span class="index-entry-level-1">Estrin's method for polynomial evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">evaluate_rational</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rational.html" title="Polynomial and Rational Function Evaluation"><span class="index-entry-level-1">Polynomial and Rational Function Evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">evaluation_condition_number</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cond.html" title="Condition Numbers"><span class="index-entry-level-1">Condition Numbers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">excess_kurtosis</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">exp2</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">exp2f</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">exp2l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">expint</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/logs.html" title="Error Logs For Error Rate Tables"><span class="index-entry-level-1">Error Logs For Error Rate Tables</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/expint/expint_i.html#math_toolkit.expint.expint_i.table_expint_Ei_" title="Table 8.78. Error rates for expint (Ei)"><span class="index-entry-level-1">Error rates for expint (Ei)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/expint/expint_n.html#math_toolkit.expint.expint_n.table_expint_En_" title="Table 8.77. Error rates for expint (En)"><span class="index-entry-level-1">Error rates for expint (En)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/expint/expint_i.html" title="Exponential Integral Ei"><span class="index-entry-level-1">Exponential Integral Ei</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/expint/expint_n.html" title="Exponential Integral En"><span class="index-entry-level-1">Exponential Integral En</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">expintf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">expintl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">expint_as_fraction</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/cf.html" title="Continued Fraction Evaluation"><span class="index-entry-level-1">Continued Fraction Evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">expm1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/powers/expm1.html" title="expm1"><span class="index-entry-level-1">expm1</span></a></strong></span></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">expm1f</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">expm1l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">extended_kindlmann</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/color_maps.html" title="Color Maps"><span class="index-entry-level-1">Color Maps</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_10"></a><span class="term">F</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/factorials/sf_factorial.html" title="Factorial"><span class="index-entry-level-0">factorial</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">factors</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_pfq.html" title="Hypergeometric pFq"><span class="index-entry-level-1">Hypergeometric p F q</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/factorials/sf_falling_factorial.html" title="Falling Factorial"><span class="index-entry-level-0">falling_factorial</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fdim</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fdimf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fdiml</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fibonacci</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/fibonacci_numbers.html" title="Fibonacci Numbers"><span class="index-entry-level-1">Fibonacci Numbers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">find_alpha</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">find_degrees_of_freedom</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/chi_squared_dist.html" title="Chi Squared Distribution"><span class="index-entry-level-1">Chi Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_chi_squared_dist.html" title="Noncentral Chi-Squared Distribution"><span class="index-entry-level-1">Noncentral Chi-Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/students_t_dist.html" title="Students t Distribution"><span class="index-entry-level-1">Students t Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">find_location</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dist_algorithms.html" title="Distribution Algorithms"><span class="index-entry-level-1">Distribution Algorithms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">find_lower_bound_on_p</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/binomial_dist.html" title="Binomial Distribution"><span class="index-entry-level-1">Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/geometric_dist.html" title="Geometric Distribution"><span class="index-entry-level-1">Geometric Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">find_non_centrality</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_chi_squared_dist.html" title="Noncentral Chi-Squared Distribution"><span class="index-entry-level-1">Noncentral Chi-Squared Distribution</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">find_scale</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dist_algorithms.html" title="Distribution Algorithms"><span class="index-entry-level-1">Distribution Algorithms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">find_upper_bound_on_p</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/binomial_dist.html" title="Binomial Distribution"><span class="index-entry-level-1">Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/geometric_dist.html" title="Geometric Distribution"><span class="index-entry-level-1">Geometric Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">finite_difference_derivative</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/diff.html" title="Numerical Differentiation"><span class="index-entry-level-1">Numerical Differentiation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">first_four_moments</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">float</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">float_advance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/next_float/float_advance.html" title="Advancing a floating-point Value by a Specific Representation Distance (ULP) float_advance"><span class="index-entry-level-1">Advancing a floating-point Value by a Specific Representation Distance (ULP) float_advance</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">float_distance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/next_float/float_advance.html" title="Advancing a floating-point Value by a Specific Representation Distance (ULP) float_advance"><span class="index-entry-level-1">Advancing a floating-point Value by a Specific Representation Distance (ULP) float_advance</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/next_float/float_distance.html" title="Calculating the Representation Distance Between Two floating-point Values (ULP) float_distance"><span class="index-entry-level-1">Calculating the Representation Distance Between Two floating-point Values (ULP) float_distance</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/cbrt_eg.html" title="Finding the Cubed Root With and Without Derivatives"><span class="index-entry-level-1">Finding the Cubed Root With and Without Derivatives</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">float_next</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/next_float/float_next.html" title="Finding the Next Greater Representable Value (float_next)"><span class="index-entry-level-1">Finding the Next Greater Representable Value (float_next)</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">float_prior</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/next_float/float_prior.html" title="Finding the Next Smaller Representable Value (float_prior)"><span class="index-entry-level-1">Finding the Next Smaller Representable Value (float_prior)</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fma</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fmaf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fmal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fmax</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fmaxf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fmaxl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fmin</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fminf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fminl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">font_color</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">for</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">forward_cardinal_b_spline</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/cardinal_b_splines.html" title="Cardinal B-splines"><span class="index-entry-level-1">Cardinal B-splines</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fpclassify</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">freedom</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/cs_eg/chi_sq_test.html" title="Chi-Square Test for the Standard Deviation"><span class="index-entry-level-1">Chi-Square Test for the Standard Deviation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_chi_squared_dist.html" title="Noncentral Chi-Squared Distribution"><span class="index-entry-level-1">Noncentral Chi-Squared Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">function</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/binom_eg/binomial_quiz_example.html" title="Binomial Quiz Example"><span class="index-entry-level-1">Binomial Quiz Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/cf.html" title="Continued Fraction Evaluation"><span class="index-entry-level-1">Continued Fraction Evaluation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/new_const.html" title="Defining New Constants"><span class="index-entry-level-1">Defining New Constants</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/dist_construct_eg.html" title="Distribution Construction Examples"><span class="index-entry-level-1">Distribution Construction Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_erf/error_inv.html" title="Error Function Inverses"><span class="index-entry-level-1">Error Function Inverses</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/airy/airy_root.html" title="Finding Zeros of Airy Functions"><span class="index-entry-level-1">Finding Zeros of Airy Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/generic.html" title="Generic operations common to all distributions are non-member functions"><span class="index-entry-level-1">Generic operations common to all distributions are non-member functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/heuman_lambda.html" title="Heuman Lambda Function"><span class="index-entry-level-1">Heuman Lambda Function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_impl.html" title="Implementation"><span class="index-entry-level-1">Implementation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_chi_squared_dist.html" title="Inverse Chi Squared Distribution"><span class="index-entry-level-1">Inverse Chi Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gamma_dist.html" title="Inverse Gamma Distribution"><span class="index-entry-level-1">Inverse Gamma Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gaussian_dist.html" title="Inverse Gaussian (or Inverse Normal) Distribution"><span class="index-entry-level-1">Inverse Gaussian (or Inverse Normal) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/jacobi_zeta.html" title="Jacobi Zeta Function"><span class="index-entry-level-1">Jacobi Zeta Function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta_overview.html" title="Overview of the Jacobi Theta Functions"><span class="index-entry-level-1">Overview of the Jacobi Theta Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/pareto.html" title="Pareto Distribution"><span class="index-entry-level-1">Pareto Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rational.html" title="Polynomial and Rational Function Evaluation"><span class="index-entry-level-1">Polynomial and Rational Function Evaluation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/variates.html" title="Random Variates and Distribution Parameters"><span class="index-entry-level-1">Random Variates and Distribution Parameters</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/reference.html" title="Reference"><span class="index-entry-level-1">Reference</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/normal_example/normal_misc.html" title="Some Miscellaneous Examples of the Normal (Gaussian) Distribution"><span class="index-entry-level-1">Some Miscellaneous Examples of the Normal (Gaussian) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">functions</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_intro.html" title="About the Math Toolkit"><span class="index-entry-level-1">About the Math Toolkit</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta3.html" title="Jacobi Theta Function θ3"><span class="index-entry-level-1">Jacobi Theta Function θ 3</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta4.html" title="Jacobi Theta Function θ4"><span class="index-entry-level-1">Jacobi Theta Function θ 4</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_t_dist.html" title="Noncentral T Distribution"><span class="index-entry-level-1">Noncentral T Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sinc/sinc_overview.html" title="Sinus Cardinal and Hyperbolic Sinus Cardinal Functions Overview"><span class="index-entry-level-1">Sinus Cardinal and Hyperbolic Sinus Cardinal Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fvar</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/autodiff.html" title="Automatic Differentiation"><span class="index-entry-level-1">Automatic Differentiation</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_11"></a><span class="term">G</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">G</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/kolmogorov_smirnov_dist.html" title="Kolmogorov-Smirnov Distribution"><span class="index-entry-level-1">Kolmogorov-Smirnov Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/owens_t.html" title="Owen's T function"><span class="index-entry-level-1">Owen's T function</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gamma_p</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma_inv.html" title="Incomplete Gamma Function Inverses"><span class="index-entry-level-1">Incomplete Gamma Function Inverses</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html" title="Incomplete Gamma Functions"><span class="index-entry-level-1">Incomplete Gamma Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gamma_p_derivative</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/gamma_derivatives.html" title="Derivative of the Incomplete Gamma Function"><span class="index-entry-level-1">Derivative of the Incomplete Gamma Function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gamma_p_inv</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/chi_squared_dist.html" title="Chi Squared Distribution"><span class="index-entry-level-1">Chi Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma_inv.html" title="Incomplete Gamma Function Inverses"><span class="index-entry-level-1">Incomplete Gamma Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gamma_p_inva</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma_inv.html" title="Incomplete Gamma Function Inverses"><span class="index-entry-level-1">Incomplete Gamma Function Inverses</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gamma_q</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html" title="Incomplete Gamma Functions"><span class="index-entry-level-1">Incomplete Gamma Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gamma_q_inv</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/chi_squared_dist.html" title="Chi Squared Distribution"><span class="index-entry-level-1">Chi Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma_inv.html" title="Incomplete Gamma Function Inverses"><span class="index-entry-level-1">Incomplete Gamma Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gamma_q_inva</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma_inv.html" title="Incomplete Gamma Function Inverses"><span class="index-entry-level-1">Incomplete Gamma Function Inverses</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gegenbauer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/gegenbauer.html" title="Gegenbauer Polynomials"><span class="index-entry-level-1">Gegenbauer Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gegenbauer_derivative</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/gegenbauer.html" title="Gegenbauer Polynomials"><span class="index-entry-level-1">Gegenbauer Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gegenbauer_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/gegenbauer.html" title="Gegenbauer Polynomials"><span class="index-entry-level-1">Gegenbauer Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">get</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/new_const.html" title="Defining New Constants"><span class="index-entry-level-1">Defining New Constants</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tutorial/user_def.html" title="Use With User-Defined Types"><span class="index-entry-level-1">Use With User-Defined Types</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">get_epsilon</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">get_from_string</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/new_const.html" title="Defining New Constants"><span class="index-entry-level-1">Defining New Constants</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">gini_coefficient</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_12"></a><span class="term">H</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">halley_iterate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_deriv.html" title="Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder"><span class="index-entry-level-1">Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hamming_distance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hazard</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hermite</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/hermite.html" title="Hermite Polynomials"><span class="index-entry-level-1">Hermite Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hermitef</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hermitel</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hermite_next</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/hermite.html" title="Hermite Polynomials"><span class="index-entry-level-1">Hermite Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">heuman_lambda</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/heuman_lambda.html" title="Heuman Lambda Function"><span class="index-entry-level-1">Heuman Lambda Function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">horizontal_lines</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hoyer_sparsity</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/signal_statistics.html" title="Signal Statistics"><span class="index-entry-level-1">Signal Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/hyperexponential_dist.html" title="Hyperexponential Distribution"><span class="index-entry-level-0">hyperexponential_distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hyperg</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypergeometric_0F1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_0f1.html" title="Hypergeometric 0F1"><span class="index-entry-level-1">Hypergeometric 0 F 1 </span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypergeometric_1F0</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_1f0.html" title="Hypergeometric 1F0"><span class="index-entry-level-1">Hypergeometric 1 F 0 </span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypergeometric_1F1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_1f1.html" title="Hypergeometric 1F1"><span class="index-entry-level-1">Hypergeometric 1 F 1</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypergeometric_1F1_regularized</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_1f1.html" title="Hypergeometric 1F1"><span class="index-entry-level-1">Hypergeometric 1 F 1</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypergeometric_2F0</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_2f0.html" title="Hypergeometric 2F0"><span class="index-entry-level-1">Hypergeometric 2 F 0</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypergeometric_pFq</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_pfq.html" title="Hypergeometric pFq"><span class="index-entry-level-1">Hypergeometric p F q</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypergeometric_pFq_precision</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_pfq.html" title="Hypergeometric pFq"><span class="index-entry-level-1">Hypergeometric p F q</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypergf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypergl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypot</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/powers/hypot.html" title="hypot"><span class="index-entry-level-1">hypot</span></a></strong></span></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypotf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">hypotl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_13"></a><span class="term">I</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">i</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_overview.html" title="Overview"><span class="index-entry-level-1">Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_sinh_sinh.html" title="sinh_sinh"><span class="index-entry-level-1">sinh_sinh</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ibeta</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_function.html" title="Incomplete Beta Functions"><span class="index-entry-level-1">Incomplete Beta Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ibetac</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_function.html" title="Incomplete Beta Functions"><span class="index-entry-level-1">Incomplete Beta Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/students_t_dist.html" title="Students t Distribution"><span class="index-entry-level-1">Students t Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ibetac_inv</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ibetac_inva</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ibetac_invb</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ibeta_derivative</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/binomial_dist.html" title="Binomial Distribution"><span class="index-entry-level-1">Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/beta_derivative.html" title="Derivative of the Incomplete Beta Function"><span class="index-entry-level-1">Derivative of the Incomplete Beta Function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/f_dist.html" title="F Distribution"><span class="index-entry-level-1">F Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ibeta_inv</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ibeta_inva</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ibeta_invb</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/beta_dist.html" title="Beta Distribution"><span class="index-entry-level-1">Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ilogb</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ilogbf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ilogbl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">inferno</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/color_maps.html" title="Color Maps"><span class="index-entry-level-1">Color Maps</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">infinity</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/error_eg.html" title="Error Handling Example"><span class="index-entry-level-1">Error Handling Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bad_roots.html" title="Examples Where Root Finding Goes Wrong"><span class="index-entry-level-1">Examples Where Root Finding Goes Wrong</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/students_t_dist.html" title="Students t Distribution"><span class="index-entry-level-1">Students t Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">integrate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_trigonometric.html" title="Cardinal Trigonometric interpolation"><span class="index-entry-level-1">Cardinal Trigonometric interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_exp_sinh.html" title="exp_sinh"><span class="index-entry-level-1">exp_sinh</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fourier_integrals.html" title="Fourier Integrals"><span class="index-entry-level-1">Fourier Integrals</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/gauss_kronrod.html" title="Gauss-Kronrod Quadrature"><span class="index-entry-level-1">Gauss-Kronrod Quadrature</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/gauss.html" title="Gauss-Legendre quadrature"><span class="index-entry-level-1">Gauss-Legendre quadrature</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/naive_monte_carlo.html" title="Naive Monte Carlo Integration"><span class="index-entry-level-1">Naive Monte Carlo Integration</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_overview.html" title="Overview"><span class="index-entry-level-1">Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/polynomials.html" title="Polynomials"><span class="index-entry-level-1">Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_sinh_sinh.html" title="sinh_sinh"><span class="index-entry-level-1">sinh_sinh</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_tanh_sinh.html" title="tanh_sinh"><span class="index-entry-level-1">tanh_sinh</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">interquartile_range</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">interval</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/binomial_dist.html" title="Binomial Distribution"><span class="index-entry-level-1">Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">inverse</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma_inv.html#math_toolkit.sf_gamma.igamma_inv.table_gamma_p_inv" title="Table 8.13. Error rates for gamma_p_inv"><span class="index-entry-level-1">Error rates for gamma_p_inv</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma_inv.html#math_toolkit.sf_gamma.igamma_inv.table_gamma_q_inv" title="Table 8.14. Error rates for gamma_q_inv"><span class="index-entry-level-1">Error rates for gamma_q_inv</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">iround</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/real_concepts.html" title="Conceptual Requirements for Real Number Types"><span class="index-entry-level-1">Conceptual Requirements for Real Number Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rounding/round.html" title="Rounding Functions"><span class="index-entry-level-1">Rounding Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">isfinite</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">isgreater</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">isgreaterequal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">isinf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">isless</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">islessequal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">isnan</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">isnormal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fpclass.html" title="Floating-Point Classification: Infinities and NaNs"><span class="index-entry-level-1">Floating-Point Classification: Infinities and NaNs</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">isunordered</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">itrunc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/real_concepts.html" title="Conceptual Requirements for Real Number Types"><span class="index-entry-level-1">Conceptual Requirements for Real Number Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rounding/trunc.html" title="Truncation Functions"><span class="index-entry-level-1">Truncation Functions</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_14"></a><span class="term">J</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">J</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_intro.html" title="Elliptic Integral Overview"><span class="index-entry-level-1">Elliptic Integral Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_carlson.html" title="Elliptic Integrals - Carlson Form"><span class="index-entry-level-1">Elliptic Integrals - Carlson Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/issues.html" title="Known Issues, and TODO List"><span class="index-entry-level-1">Known Issues, and TODO List</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/jacobi.html" title="Jacobi Polynomials"><span class="index-entry-level-1">Jacobi Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_cd</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_cd.html" title="Jacobi Elliptic Function cd"><span class="index-entry-level-1">Jacobi Elliptic Function cd</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_cn</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_cn.html" title="Jacobi Elliptic Function cn"><span class="index-entry-level-1">Jacobi Elliptic Function cn</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_cs</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_cs.html" title="Jacobi Elliptic Function cs"><span class="index-entry-level-1">Jacobi Elliptic Function cs</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_dc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_dc.html" title="Jacobi Elliptic Function dc"><span class="index-entry-level-1">Jacobi Elliptic Function dc</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_derivative</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/jacobi.html" title="Jacobi Polynomials"><span class="index-entry-level-1">Jacobi Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_dn</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_dn.html" title="Jacobi Elliptic Function dn"><span class="index-entry-level-1">Jacobi Elliptic Function dn</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_double_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/jacobi.html" title="Jacobi Polynomials"><span class="index-entry-level-1">Jacobi Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_ds</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_ds.html" title="Jacobi Elliptic Function ds"><span class="index-entry-level-1">Jacobi Elliptic Function ds</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_elliptic</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_elliptic.html" title="Jacobi Elliptic SN, CN and DN"><span class="index-entry-level-1">Jacobi Elliptic SN, CN and DN</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_nc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_nc.html" title="Jacobi Elliptic Function nc"><span class="index-entry-level-1">Jacobi Elliptic Function nc</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_nd</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_nd.html" title="Jacobi Elliptic Function nd"><span class="index-entry-level-1">Jacobi Elliptic Function nd</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_ns</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_ns.html" title="Jacobi Elliptic Function ns"><span class="index-entry-level-1">Jacobi Elliptic Function ns</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/jacobi.html" title="Jacobi Polynomials"><span class="index-entry-level-1">Jacobi Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_sc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_sc.html" title="Jacobi Elliptic Function sc"><span class="index-entry-level-1">Jacobi Elliptic Function sc</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_sd</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_sd.html" title="Jacobi Elliptic Function sd"><span class="index-entry-level-1">Jacobi Elliptic Function sd</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_sn</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_sn.html" title="Jacobi Elliptic Function sn"><span class="index-entry-level-1">Jacobi Elliptic Function sn</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta1.html" title="Jacobi Theta Function θ1"><span class="index-entry-level-1">Jacobi Theta Function θ 1</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta1tau</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta1.html" title="Jacobi Theta Function θ1"><span class="index-entry-level-1">Jacobi Theta Function θ 1</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta2</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta2.html" title="Jacobi Theta Function θ2"><span class="index-entry-level-1">Jacobi Theta Function θ 2</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta2tau</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta2.html" title="Jacobi Theta Function θ2"><span class="index-entry-level-1">Jacobi Theta Function θ 2</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta3</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta3.html" title="Jacobi Theta Function θ3"><span class="index-entry-level-1">Jacobi Theta Function θ 3</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta3m1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta3.html" title="Jacobi Theta Function θ3"><span class="index-entry-level-1">Jacobi Theta Function θ 3</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta3m1tau</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta3.html" title="Jacobi Theta Function θ3"><span class="index-entry-level-1">Jacobi Theta Function θ 3</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta3tau</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta3.html" title="Jacobi Theta Function θ3"><span class="index-entry-level-1">Jacobi Theta Function θ 3</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta4</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta4.html" title="Jacobi Theta Function θ4"><span class="index-entry-level-1">Jacobi Theta Function θ 4</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta4m1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta4.html" title="Jacobi Theta Function θ4"><span class="index-entry-level-1">Jacobi Theta Function θ 4</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta4m1tau</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta4.html" title="Jacobi Theta Function θ4"><span class="index-entry-level-1">Jacobi Theta Function θ 4</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_theta4tau</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta4.html" title="Jacobi Theta Function θ4"><span class="index-entry-level-1">Jacobi Theta Function θ 4</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jacobi_zeta</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/jacobi_zeta.html" title="Jacobi Zeta Function"><span class="index-entry-level-1">Jacobi Zeta Function</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_15"></a><span class="term">K</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">k</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/elliptic_eg.html" title="A More complex example - Inverting the Elliptic Integrals"><span class="index-entry-level-1">A More complex example - Inverting the Elliptic Integrals</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/agm.html" title="Arithmetic-Geometric Mean"><span class="index-entry-level-1">Arithmetic-Geometric Mean</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bilinear_uniform.html" title="Bilinear Uniform Interpolation"><span class="index-entry-level-1">Bilinear Uniform Interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/cardinal_b_splines.html" title="Cardinal B-splines"><span class="index-entry-level-1">Cardinal B-splines</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/catmull_rom.html" title="Catmull-Rom Splines"><span class="index-entry-level-1">Catmull-Rom Splines</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_intro.html" title="Elliptic Integral Overview"><span class="index-entry-level-1">Elliptic Integral Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_1.html" title="Elliptic Integrals of the First Kind - Legendre Form"><span class="index-entry-level-1">Elliptic Integrals of the First Kind - Legendre Form</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/gegenbauer.html" title="Gegenbauer Polynomials"><span class="index-entry-level-1">Gegenbauer Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/hypergeometric_dist.html" title="Hypergeometric Distribution"><span class="index-entry-level-1">Hypergeometric Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi/jacobi_elliptic.html" title="Jacobi Elliptic SN, CN and DN"><span class="index-entry-level-1">Jacobi Elliptic SN, CN and DN</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/issues.html" title="Known Issues, and TODO List"><span class="index-entry-level-1">Known Issues, and TODO List</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quintic_hermite.html" title="Quintic Hermite interpolation"><span class="index-entry-level-1">Quintic Hermite interpolation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">kahan_sum_series</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/series_evaluation.html" title="Series Evaluation"><span class="index-entry-level-1">Series Evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">kindlmann</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/color_maps.html" title="Color Maps"><span class="index-entry-level-1">Color Maps</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">kurtosis</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">kurtosis_excess</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gamma_dist.html" title="Inverse Gamma Distribution"><span class="index-entry-level-1">Inverse Gamma Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_16"></a><span class="term">L</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre.html" title="Legendre (and Associated) Polynomials"><span class="index-entry-level-1">Legendre (and Associated) Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">l0_pseudo_norm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">l1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_value_ops.html" title="Octonion Value Operations"><span class="index-entry-level-1">Octonion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/value_op.html" title="Quaternion Value Operations"><span class="index-entry-level-1">Quaternion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">l1_distance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">l1_norm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cond.html" title="Condition Numbers"><span class="index-entry-level-1">Condition Numbers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">l2_distance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">l2_norm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">laguerre</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/laguerre.html#math_toolkit.sf_poly.laguerre.table_laguerre_n_m_x_" title="Table 8.36. Error rates for laguerre(n, m, x)"><span class="index-entry-level-1">Error rates for laguerre(n, m, x)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/laguerre.html#math_toolkit.sf_poly.laguerre.table_laguerre_n_x_" title="Table 8.35. Error rates for laguerre(n, x)"><span class="index-entry-level-1">Error rates for laguerre(n, x)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/laguerre.html" title="Laguerre (and Associated) Polynomials"><span class="index-entry-level-1">Laguerre (and Associated) Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">laguerref</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">laguerrel</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">laguerre_next</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/laguerre.html" title="Laguerre (and Associated) Polynomials"><span class="index-entry-level-1">Laguerre (and Associated) Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lambert_w0</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lambert_w0_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lambert_wm1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lambert_wm1_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ldexp</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/real_concepts.html" title="Conceptual Requirements for Real Number Types"><span class="index-entry-level-1">Conceptual Requirements for Real Number Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">legendre</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">legendref</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">legendrel</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">legendre_next</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre.html" title="Legendre (and Associated) Polynomials"><span class="index-entry-level-1">Legendre (and Associated) Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">legendre_p</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/logs.html" title="Error Logs For Error Rate Tables"><span class="index-entry-level-1">Error Logs For Error Rate Tables</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre.html#math_toolkit.sf_poly.legendre.table_legendre_p_associated_" title="Table 8.34. Error rates for legendre_p (associated)"><span class="index-entry-level-1">Error rates for legendre_p (associated)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre.html" title="Legendre (and Associated) Polynomials"><span class="index-entry-level-1">Legendre (and Associated) Polynomials</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">legendre_p_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre.html" title="Legendre (and Associated) Polynomials"><span class="index-entry-level-1">Legendre (and Associated) Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">legendre_p_zeros</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre.html" title="Legendre (and Associated) Polynomials"><span class="index-entry-level-1">Legendre (and Associated) Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">legendre_q</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre.html" title="Legendre (and Associated) Polynomials"><span class="index-entry-level-1">Legendre (and Associated) Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lgamma</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/lgamma.html" title="Log Gamma"><span class="index-entry-level-1">Log Gamma</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/namespace_pol.html" title="Setting Polices at Namespace Scope"><span class="index-entry-level-1">Setting Polices at Namespace Scope</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lgammaf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lgammal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ljung_box</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ljung_box.html" title="The Ljung-Box Test"><span class="index-entry-level-1">The Ljung-Box Test</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">llrint</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">llrintf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">llrintl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">llround</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rounding/round.html" title="Rounding Functions"><span class="index-entry-level-1">Rounding Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">llroundf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">llroundl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lltrunc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rounding/trunc.html" title="Truncation Functions"><span class="index-entry-level-1">Truncation Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">location</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/cauchy_dist.html" title="Cauchy-Lorentz Distribution"><span class="index-entry-level-1">Cauchy-Lorentz Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bad_roots.html" title="Examples Where Root Finding Goes Wrong"><span class="index-entry-level-1">Examples Where Root Finding Goes Wrong</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/extreme_dist.html" title="Extreme Value Distribution"><span class="index-entry-level-1">Extreme Value Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_location_eg.html" title="Find Location (Mean) Example"><span class="index-entry-level-1">Find Location (Mean) Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_scale_eg.html" title="Find Scale (Standard Deviation) Example"><span class="index-entry-level-1">Find Scale (Standard Deviation) Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/laplace_dist.html" title="Laplace Distribution"><span class="index-entry-level-1">Laplace Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/lognormal_dist.html" title="Log Normal Distribution"><span class="index-entry-level-1">Log Normal Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/logistic_dist.html" title="Logistic Distribution"><span class="index-entry-level-1">Logistic Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/normal_dist.html" title="Normal (Gaussian) Distribution"><span class="index-entry-level-1">Normal (Gaussian) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/skew_normal_dist.html" title="Skew Normal Distribution"><span class="index-entry-level-1">Skew Normal Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">log1p</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/powers/log1p.html" title="log1p"><span class="index-entry-level-1">log1p</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/series_evaluation.html" title="Series Evaluation"><span class="index-entry-level-1">Series Evaluation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">log1pf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">log1pl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">log2</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/ct_pow.html" title="Compile Time Power of a Runtime Base"><span class="index-entry-level-1">Compile Time Power of a Runtime Base</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">log2f</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">log2l</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">logaddexp</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/logaddexp.html" title="logaddexp and logsumexp"><span class="index-entry-level-1">logaddexp and logsumexp</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">logb</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">logbf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">logbl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">logsumexp</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/logaddexp.html" title="logaddexp and logsumexp"><span class="index-entry-level-1">logaddexp and logsumexp</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">log_hypergeometric_1F1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_1f1.html" title="Hypergeometric 1F1"><span class="index-entry-level-1">Hypergeometric 1 F 1</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lookup</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/autodiff.html" title="Automatic Differentiation"><span class="index-entry-level-1">Automatic Differentiation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/bernoulli_numbers.html" title="Bernoulli Numbers"><span class="index-entry-level-1">Bernoulli Numbers</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lp_distance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lp_norm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lrint</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lrintf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lrintl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lround</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rounding/round.html" title="Rounding Functions"><span class="index-entry-level-1">Rounding Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lroundf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lroundl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ltrunc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rounding/trunc.html" title="Truncation Functions"><span class="index-entry-level-1">Truncation Functions</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_17"></a><span class="term">M</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">m</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/sph_harm.html" title="Spherical Harmonics"><span class="index-entry-level-1">Spherical Harmonics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">m2m4_snr_estimator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/signal_statistics.html" title="Signal Statistics"><span class="index-entry-level-1">Signal Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">m2m4_snr_estimator_db</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/signal_statistics.html" title="Signal Statistics"><span class="index-entry-level-1">Signal Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">make_ftuple</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/autodiff.html" title="Automatic Differentiation"><span class="index-entry-level-1">Automatic Differentiation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">make_fvar</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/autodiff.html" title="Automatic Differentiation"><span class="index-entry-level-1">Automatic Differentiation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">mean</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/dist_construct_eg.html" title="Distribution Construction Examples"><span class="index-entry-level-1">Distribution Construction Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_location_eg.html" title="Find Location (Mean) Example"><span class="index-entry-level-1">Find Location (Mean) Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_scale_eg.html" title="Find Scale (Standard Deviation) Example"><span class="index-entry-level-1">Find Scale (Standard Deviation) Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/geometric_dist.html" title="Geometric Distribution"><span class="index-entry-level-1">Geometric Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gaussian_dist.html" title="Inverse Gaussian (or Inverse Normal) Distribution"><span class="index-entry-level-1">Inverse Gaussian (or Inverse Normal) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/normal_dist.html" title="Normal (Gaussian) Distribution"><span class="index-entry-level-1">Normal (Gaussian) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/poisson_dist.html" title="Poisson Distribution"><span class="index-entry-level-1">Poisson Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/uniform_dist.html" title="Uniform Distribution"><span class="index-entry-level-1">Uniform Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">means_and_covariance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bivariate_statistics.html" title="Bivariate Statistics"><span class="index-entry-level-1">Bivariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">mean_and_sample_variance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">median</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/runs_test.html" title="Runs tests"><span class="index-entry-level-1">Runs tests</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">median_absolute_deviation</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">message</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/bernoulli_numbers.html" title="Bernoulli Numbers"><span class="index-entry-level-1">Bernoulli Numbers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">min</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/students_t_dist.html" title="Students t Distribution"><span class="index-entry-level-1">Students t Distribution</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">mode</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/gamma_dist.html" title="Gamma (and Erlang) Distribution"><span class="index-entry-level-1">Gamma (and Erlang) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/triangular_dist.html" title="Triangular Distribution"><span class="index-entry-level-1">Triangular Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">msg</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">multipolar</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_create.html" title="Octonion Creation Functions"><span class="index-entry-level-1">Octonion Creation Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/create.html" title="Quaternion Creation Functions"><span class="index-entry-level-1">Quaternion Creation Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_18"></a><span class="term">N</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/cbrt_eg.html" title="Finding the Cubed Root With and Without Derivatives"><span class="index-entry-level-1">Finding the Cubed Root With and Without Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/generic.html" title="Generic operations common to all distributions are non-member functions"><span class="index-entry-level-1">Generic operations common to all distributions are non-member functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/kolmogorov_smirnov_dist.html" title="Kolmogorov-Smirnov Distribution"><span class="index-entry-level-1">Kolmogorov-Smirnov Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre_stieltjes.html" title="Legendre-Stieltjes Polynomials"><span class="index-entry-level-1">Legendre-Stieltjes Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/trapezoidal.html" title="Trapezoidal Quadrature"><span class="index-entry-level-1">Trapezoidal Quadrature</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nan</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/facets_intro.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/reference.html" title="Reference"><span class="index-entry-level-1">Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nanf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nanl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nan_color</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nearbyint</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nearbyintf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nearbyintl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">newton_raphson_iterate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_deriv.html" title="Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder"><span class="index-entry-level-1">Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nextafter</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/next_float/nextafter.html" title="Finding the Next Representable Value in a Specific Direction (nextafter)"><span class="index-entry-level-1">Finding the Next Representable Value in a Specific Direction (nextafter)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/next_float/ulp.html" title="Obtaining the Size of a Unit In the Last Place - ULP"><span class="index-entry-level-1">Obtaining the Size of a Unit In the Last Place - ULP</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nextafterf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nextafterl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nexttoward</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nexttowardf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nexttowardl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nonfinite_num_get</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets.html" title="Facets for Floating-Point Infinities and NaNs"><span class="index-entry-level-1">Facets for Floating-Point Infinities and NaNs</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nonfinite_num_put</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets.html" title="Facets for Floating-Point Infinities and NaNs"><span class="index-entry-level-1">Facets for Floating-Point Infinities and NaNs</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">norm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_value_ops.html" title="Octonion Value Operations"><span class="index-entry-level-1">Octonion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/value_op.html" title="Quaternion Value Operations"><span class="index-entry-level-1">Quaternion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/namespace_policies.html" title="Setting Policies at Namespace or Translation Unit Scope"><span class="index-entry-level-1">Setting Policies at Namespace or Translation Unit Scope</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">normalize</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_1f1.html" title="Hypergeometric 1F1"><span class="index-entry-level-1">Hypergeometric 1 F 1</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">not</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/st_eg/two_sample_students_t.html" title="Comparing the means of two samples with the Students-t test"><span class="index-entry-level-1">Comparing the means of two samples with the Students-t test</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/facets_intro.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/issues.html" title="Known Issues, and TODO List"><span class="index-entry-level-1">Known Issues, and TODO List</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_overview.html" title="Overview"><span class="index-entry-level-1">Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_non_mem.html" title="Quaternion Non-Member Operators"><span class="index-entry-level-1">Quaternion Non-Member Operators</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_19"></a><span class="term">O</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">O</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/autodiff.html" title="Automatic Differentiation"><span class="index-entry-level-1">Automatic Differentiation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/empirical_cdf.html" title="Empirical Cumulative Distribution Function"><span class="index-entry-level-1">Empirical Cumulative Distribution Function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/fibonacci_numbers.html" title="Fibonacci Numbers"><span class="index-entry-level-1">Fibonacci Numbers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/diff0.html" title="Lanczos Smoothing Derivatives"><span class="index-entry-level-1">Lanczos Smoothing Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/mbessel.html" title="Modified Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Modified Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_tanh_sinh.html" title="tanh_sinh"><span class="index-entry-level-1">tanh_sinh</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">octonion</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_mem_fun.html" title="Octonion Member Functions"><span class="index-entry-level-1">Octonion Member Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_specialization.html" title="Octonion Specializations"><span class="index-entry-level-1">Octonion Specializations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/octonion.html" title="Template Class octonion"><span class="index-entry-level-1">Template Class octonion</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">odd</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hypergeometric/hypergeometric_1f1.html" title="Hypergeometric 1F1"><span class="index-entry-level-1">Hypergeometric 1 F 1</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">of</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bivariate_statistics.html" title="Bivariate Statistics"><span class="index-entry-level-1">Bivariate Statistics</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cubic_hermite.html" title="Cubic Hermite interpolation"><span class="index-entry-level-1">Cubic Hermite interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/hyperexponential_dist.html" title="Hyperexponential Distribution"><span class="index-entry-level-1">Hyperexponential Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/issues.html" title="Known Issues, and TODO List"><span class="index-entry-level-1">Known Issues, and TODO List</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre.html" title="Legendre (and Associated) Polynomials"><span class="index-entry-level-1">Legendre (and Associated) Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_value_ops.html" title="Octonion Value Operations"><span class="index-entry-level-1">Octonion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/value_op.html" title="Quaternion Value Operations"><span class="index-entry-level-1">Quaternion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/sph_harm.html" title="Spherical Harmonics"><span class="index-entry-level-1">Spherical Harmonics</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_tanh_sinh.html" title="tanh_sinh"><span class="index-entry-level-1">tanh_sinh</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">one</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_faq.html" title="Boost.Math Frequently Asked Questions (FAQs)"><span class="index-entry-level-1">Boost.Math Frequently Asked Questions (FAQs)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/cs_eg/chi_sq_size.html" title="Estimating the Required Sample Sizes for a Chi-Square Test for the Standard Deviation"><span class="index-entry-level-1">Estimating the Required Sample Sizes for a Chi-Square Test for the Standard Deviation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/remez.html" title="The Remez Method"><span class="index-entry-level-1">The Remez Method</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">operator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/elliptic_eg.html" title="A More complex example - Inverting the Elliptic Integrals"><span class="index-entry-level-1">A More complex example - Inverting the Elliptic Integrals</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/barycentric.html" title="Barycentric Rational Interpolation"><span class="index-entry-level-1">Barycentric Rational Interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bezier_polynomial.html" title="Bezier Polynomials"><span class="index-entry-level-1">Bezier Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bilinear_uniform.html" title="Bilinear Uniform Interpolation"><span class="index-entry-level-1">Bilinear Uniform Interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_cubic_b.html" title="Cardinal Cubic B-spline interpolation"><span class="index-entry-level-1">Cardinal Cubic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_quadratic_b.html" title="Cardinal Quadratic B-spline interpolation"><span class="index-entry-level-1">Cardinal Quadratic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_quintic_b.html" title="Cardinal Quintic B-spline interpolation"><span class="index-entry-level-1">Cardinal Quintic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_trigonometric.html" title="Cardinal Trigonometric interpolation"><span class="index-entry-level-1">Cardinal Trigonometric interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/catmull_rom.html" title="Catmull-Rom Splines"><span class="index-entry-level-1">Catmull-Rom Splines</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/cohen_acceleration.html" title="Cohen Acceleration"><span class="index-entry-level-1">Cohen Acceleration</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/5th_root_eg.html" title="Computing the Fifth Root"><span class="index-entry-level-1">Computing the Fifth Root</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cond.html" title="Condition Numbers"><span class="index-entry-level-1">Condition Numbers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/cf.html" title="Continued Fraction Evaluation"><span class="index-entry-level-1">Continued Fraction Evaluation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cubic_hermite.html" title="Cubic Hermite interpolation"><span class="index-entry-level-1">Cubic Hermite interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/daubechies.html" title="Daubechies Wavelets and Scaling Functions"><span class="index-entry-level-1">Daubechies Wavelets and Scaling Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/empirical_cdf.html" title="Empirical Cumulative Distribution Function"><span class="index-entry-level-1">Empirical Cumulative Distribution Function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/fibonacci_numbers.html" title="Fibonacci Numbers"><span class="index-entry-level-1">Fibonacci Numbers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/cbrt_eg.html" title="Finding the Cubed Root With and Without Derivatives"><span class="index-entry-level-1">Finding the Cubed Root With and Without Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/nth_root.html" title="Generalizing to Compute the nth root"><span class="index-entry-level-1">Generalizing to Compute the nth root</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/test_data.html" title="Graphing, Profiling, and Generating Test Data for Special Functions"><span class="index-entry-level-1">Graphing, Profiling, and Generating Test Data for Special Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/diff0.html" title="Lanczos Smoothing Derivatives"><span class="index-entry-level-1">Lanczos Smoothing Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre_stieltjes.html" title="Legendre-Stieltjes Polynomials"><span class="index-entry-level-1">Legendre-Stieltjes Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/brent_minima.html" title="Locating Function Minima using Brent's algorithm"><span class="index-entry-level-1">Locating Function Minima using Brent's algorithm</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/makima.html" title="Modified Akima interpolation"><span class="index-entry-level-1">Modified Akima interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pchip.html" title="PCHIP interpolation"><span class="index-entry-level-1">PCHIP interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/polynomials.html" title="Polynomials"><span class="index-entry-level-1">Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quintic_hermite.html" title="Quintic Hermite interpolation"><span class="index-entry-level-1">Quintic Hermite interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/series_evaluation.html" title="Series Evaluation"><span class="index-entry-level-1">Series Evaluation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_noderiv/root_termination.html" title="Termination Condition Functors"><span class="index-entry-level-1">Termination Condition Functors</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/vector_barycentric.html" title="Vector-valued Barycentric Rational Interpolation"><span class="index-entry-level-1">Vector-valued Barycentric Rational Interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/wavelet_transforms.html" title="Wavelet Transforms"><span class="index-entry-level-1">Wavelet Transforms</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/whittaker_shannon.html" title="Whittaker-Shannon interpolation"><span class="index-entry-level-1">Whittaker-Shannon interpolation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">oracle_snr</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/signal_statistics.html" title="Signal Statistics"><span class="index-entry-level-1">Signal Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">oracle_snr_db</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/signal_statistics.html" title="Signal Statistics"><span class="index-entry-level-1">Signal Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">owens_t</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/owens_t.html" title="Owen's T function"><span class="index-entry-level-1">Owen's T function</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_20"></a><span class="term">P</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">P</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_faq.html" title="Boost.Math Frequently Asked Questions (FAQs)"><span class="index-entry-level-1">Boost.Math Frequently Asked Questions (FAQs)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_cubic_b.html" title="Cardinal Cubic B-spline interpolation"><span class="index-entry-level-1">Cardinal Cubic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/catmull_rom.html" title="Catmull-Rom Splines"><span class="index-entry-level-1">Catmull-Rom Splines</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/digamma.html" title="Digamma"><span class="index-entry-level-1">Digamma</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dist_algorithms.html" title="Distribution Algorithms"><span class="index-entry-level-1">Distribution Algorithms</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_location_eg.html" title="Find Location (Mean) Example"><span class="index-entry-level-1">Find Location (Mean) Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_mean_and_sd_eg.html" title="Find mean and standard deviation example"><span class="index-entry-level-1">Find mean and standard deviation example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_scale_eg.html" title="Find Scale (Standard Deviation) Example"><span class="index-entry-level-1">Find Scale (Standard Deviation) Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/generic.html" title="Generic operations common to all distributions are non-member functions"><span class="index-entry-level-1">Generic operations common to all distributions are non-member functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/variates.html" title="Random Variates and Distribution Parameters"><span class="index-entry-level-1">Random Variates and Distribution Parameters</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cubic_roots.html" title="Roots of Cubic Polynomials"><span class="index-entry-level-1">Roots of Cubic Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/remez.html" title="The Remez Method"><span class="index-entry-level-1">The Remez Method</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">p1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/catmull_rom.html" title="Catmull-Rom Splines"><span class="index-entry-level-1">Catmull-Rom Splines</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pdf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/arcine_dist.html" title="Arcsine Distribution"><span class="index-entry-level-1">Arcsine Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/generic.html" title="Generic operations common to all distributions are non-member functions"><span class="index-entry-level-1">Generic operations common to all distributions are non-member functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">plasma</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/color_maps.html" title="Color Maps"><span class="index-entry-level-1">Color Maps</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">point</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/result_type.html" title="Calculation of the Type of the Result"><span class="index-entry-level-1">Calculation of the Type of the Result</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">polar</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/sph_harm.html" title="Spherical Harmonics"><span class="index-entry-level-1">Spherical Harmonics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/polygamma.html" title="Polygamma"><span class="index-entry-level-0">polygamma</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">positive</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/gauss_kronrod.html" title="Gauss-Kronrod Quadrature"><span class="index-entry-level-1">Gauss-Kronrod Quadrature</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/gauss.html" title="Gauss-Legendre quadrature"><span class="index-entry-level-1">Gauss-Legendre quadrature</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pow</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/ct_pow.html" title="Compile Time Power of a Runtime Base"><span class="index-entry-level-1">Compile Time Power of a Runtime Base</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">powm1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/logs_and_tables/all_table.html#math_toolkit.logs_and_tables.all_table.table_boost_math_powm1" title="Table 23.7. Error rates for boost::math::powm1"><span class="index-entry-level-1">Error rates for boost::math::powm1</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../math_toolkit/powers/powm1.html" title="powm1"><span class="index-entry-level-1">powm1</span></a></strong></span></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/barycentric.html" title="Barycentric Rational Interpolation"><span class="index-entry-level-1">Barycentric Rational Interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bezier_polynomial.html" title="Bezier Polynomials"><span class="index-entry-level-1">Bezier Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_cubic_b.html" title="Cardinal Cubic B-spline interpolation"><span class="index-entry-level-1">Cardinal Cubic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_quadratic_b.html" title="Cardinal Quadratic B-spline interpolation"><span class="index-entry-level-1">Cardinal Quadratic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_quintic_b.html" title="Cardinal Quintic B-spline interpolation"><span class="index-entry-level-1">Cardinal Quintic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_trigonometric.html" title="Cardinal Trigonometric interpolation"><span class="index-entry-level-1">Cardinal Trigonometric interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/catmull_rom.html" title="Catmull-Rom Splines"><span class="index-entry-level-1">Catmull-Rom Splines</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cubic_hermite.html" title="Cubic Hermite interpolation"><span class="index-entry-level-1">Cubic Hermite interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/daubechies.html" title="Daubechies Wavelets and Scaling Functions"><span class="index-entry-level-1">Daubechies Wavelets and Scaling Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/legendre_stieltjes.html" title="Legendre-Stieltjes Polynomials"><span class="index-entry-level-1">Legendre-Stieltjes Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/makima.html" title="Modified Akima interpolation"><span class="index-entry-level-1">Modified Akima interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pchip.html" title="PCHIP interpolation"><span class="index-entry-level-1">PCHIP interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/polynomials.html" title="Polynomials"><span class="index-entry-level-1">Polynomials</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/primes.html" title="Prime Numbers"><span class="index-entry-level-1">Prime Numbers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quintic_hermite.html" title="Quintic Hermite interpolation"><span class="index-entry-level-1">Quintic Hermite interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/vector_barycentric.html" title="Vector-valued Barycentric Rational Interpolation"><span class="index-entry-level-1">Vector-valued Barycentric Rational Interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/whittaker_shannon.html" title="Whittaker-Shannon interpolation"><span class="index-entry-level-1">Whittaker-Shannon interpolation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">push_back</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cubic_hermite.html" title="Cubic Hermite interpolation"><span class="index-entry-level-1">Cubic Hermite interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/makima.html" title="Modified Akima interpolation"><span class="index-entry-level-1">Modified Akima interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pchip.html" title="PCHIP interpolation"><span class="index-entry-level-1">PCHIP interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quintic_hermite.html" title="Quintic Hermite interpolation"><span class="index-entry-level-1">Quintic Hermite interpolation</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_21"></a><span class="term">Q</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">q</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/jacobi_theta/jacobi_theta_overview.html" title="Overview of the Jacobi Theta Functions"><span class="index-entry-level-1">Overview of the Jacobi Theta Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">quad</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/chebyshev.html" title="Chebyshev Polynomials"><span class="index-entry-level-1">Chebyshev Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">quantile</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/complements.html" title="Complements are supported too - and when to use them"><span class="index-entry-level-1">Complements are supported too - and when to use them</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_concept.html" title="Conceptual Requirements for Distribution Types"><span class="index-entry-level-1">Conceptual Requirements for Distribution Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_chi_squared_dist.html" title="Inverse Chi Squared Distribution"><span class="index-entry-level-1">Inverse Chi Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gamma_dist.html" title="Inverse Gamma Distribution"><span class="index-entry-level-1">Inverse Gamma Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gaussian_dist.html" title="Inverse Gaussian (or Inverse Normal) Distribution"><span class="index-entry-level-1">Inverse Gaussian (or Inverse Normal) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/neg_binom_eg/negative_binomial_example1.html" title="Negative Binomial Sales Quota Example."><span class="index-entry-level-1">Negative Binomial Sales Quota Example.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/namespace_policies.html" title="Setting Policies at Namespace or Translation Unit Scope"><span class="index-entry-level-1">Setting Policies at Namespace or Translation Unit Scope</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/skew_normal_dist.html" title="Skew Normal Distribution"><span class="index-entry-level-1">Skew Normal Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/normal_example/normal_misc.html" title="Some Miscellaneous Examples of the Normal (Gaussian) Distribution"><span class="index-entry-level-1">Some Miscellaneous Examples of the Normal (Gaussian) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/triangular_dist.html" title="Triangular Distribution"><span class="index-entry-level-1">Triangular Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">quartic_roots</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quartic_roots.html" title="Roots of Quartic Polynomials"><span class="index-entry-level-1">Roots of Quartic Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">quaternion</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_mem_fun.html" title="Quaternion Member Functions"><span class="index-entry-level-1">Quaternion Member Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/spec.html" title="Quaternion Specializations"><span class="index-entry-level-1">Quaternion Specializations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat.html" title="Template Class quaternion"><span class="index-entry-level-1">Template Class quaternion</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">quiet_NaN</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bivariate_statistics.html" title="Bivariate Statistics"><span class="index-entry-level-1">Bivariate Statistics</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_22"></a><span class="term">R</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">r</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/barycentric.html" title="Barycentric Rational Interpolation"><span class="index-entry-level-1">Barycentric Rational Interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/digamma.html" title="Digamma"><span class="index-entry-level-1">Digamma</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ellint/ellint_intro.html" title="Elliptic Integral Overview"><span class="index-entry-level-1">Elliptic Integral Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_erf/error_function.html" title="Error Function erf and complement erfc"><span class="index-entry-level-1">Error Function erf and complement erfc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_erf/error_inv.html" title="Error Function Inverses"><span class="index-entry-level-1">Error Function Inverses</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/expint/expint_i.html" title="Exponential Integral Ei"><span class="index-entry-level-1">Exponential Integral Ei</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/lgamma.html" title="Log Gamma"><span class="index-entry-level-1">Log Gamma</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/minimax.html" title="Minimax Approximations and the Remez Algorithm"><span class="index-entry-level-1">Minimax Approximations and the Remez Algorithm</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/zetas/zeta.html" title="Riemann Zeta Function"><span class="index-entry-level-1">Riemann Zeta Function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/remez.html" title="The Remez Method"><span class="index-entry-level-1">The Remez Method</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">range</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_caveats.html" title="Caveats"><span class="index-entry-level-1">Caveats</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/compilers_overview.html" title="Compilers"><span class="index-entry-level-1">Compilers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/gauss_kronrod.html" title="Gauss-Kronrod Quadrature"><span class="index-entry-level-1">Gauss-Kronrod Quadrature</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">RealType</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/real_concepts.html" title="Conceptual Requirements for Real Number Types"><span class="index-entry-level-1">Conceptual Requirements for Real Number Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/directories.html" title="Directory and File Structure"><span class="index-entry-level-1">Directory and File Structure</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">refinement</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">relative_difference</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/float_comparison.html" title="Floating-point Comparison"><span class="index-entry-level-1">Floating-point Comparison</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">remainder</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">remainderf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">remainderl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">remquo</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">remquof</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">remquol</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">resize</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/catmull_rom.html" title="Catmull-Rom Splines"><span class="index-entry-level-1">Catmull-Rom Splines</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">return_data</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/empirical_cdf.html" title="Empirical Cumulative Distribution Function"><span class="index-entry-level-1">Empirical Cumulative Distribution Function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">return_x</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/barycentric.html" title="Barycentric Rational Interpolation"><span class="index-entry-level-1">Barycentric Rational Interpolation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">return_y</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/barycentric.html" title="Barycentric Rational Interpolation"><span class="index-entry-level-1">Barycentric Rational Interpolation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">riemann_zeta</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">riemann_zetaf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">riemann_zetal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">rint</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">rintf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">rintl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/factorials/sf_rising_factorial.html" title="Rising Factorial"><span class="index-entry-level-0">rising_factorial</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">root</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/root_n_comparison.html#math_toolkit.root_comparison.root_n_comparison.root_11_0" title="Table 10.8. 11th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_AVX"><span class="index-entry-level-1">11th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_AVX</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/root_n_comparison.html#math_toolkit.root_comparison.root_n_comparison.root_11_1" title="Table 10.11. 11th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_SSE2"><span class="index-entry-level-1">11th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_SSE2</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/root_n_comparison.html#math_toolkit.root_comparison.root_n_comparison.root_11" title="Table 10.5. 11th root(28) for float, double, long double and cpp_bin_float_50 types, using _X86_SSE2"><span class="index-entry-level-1">11th root(28) for float, double, long double and cpp_bin_float_50 types, using _X86_SSE2</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/root_n_comparison.html#math_toolkit.root_comparison.root_n_comparison.root_5_0" title="Table 10.6. 5th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_AVX"><span class="index-entry-level-1">5th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_AVX</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/root_n_comparison.html#math_toolkit.root_comparison.root_n_comparison.root_5_1" title="Table 10.9. 5th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_SSE2"><span class="index-entry-level-1">5th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_SSE2</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/root_n_comparison.html#math_toolkit.root_comparison.root_n_comparison.root_5" title="Table 10.3. 5th root(28) for float, double, long double and cpp_bin_float_50 types, using _X86_SSE2"><span class="index-entry-level-1">5th root(28) for float, double, long double and cpp_bin_float_50 types, using _X86_SSE2</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/root_n_comparison.html#math_toolkit.root_comparison.root_n_comparison.root_7_0" title="Table 10.7. 7th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_AVX"><span class="index-entry-level-1">7th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_AVX</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/root_n_comparison.html#math_toolkit.root_comparison.root_n_comparison.root_7_1" title="Table 10.10. 7th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_SSE2"><span class="index-entry-level-1">7th root(28) for float, double, long double and cpp_bin_float_50 types, using _X64_SSE2</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/root_n_comparison.html#math_toolkit.root_comparison.root_n_comparison.root_7" title="Table 10.4. 7th root(28) for float, double, long double and cpp_bin_float_50 types, using _X86_SSE2"><span class="index-entry-level-1">7th root(28) for float, double, long double and cpp_bin_float_50 types, using _X86_SSE2</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_comparison/cbrt_comparison.html#math_toolkit.root_comparison.cbrt_comparison.cbrt_4" title="Table 10.1. Cube root(28) for float, double, long double and cpp_bin_float_50"><span class="index-entry-level-1">Cube root(28) for float, double, long double and cpp_bin_float_50</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples.html" title="Examples of Root-Finding (with and without derivatives)"><span class="index-entry-level-1">Examples of Root-Finding (with and without derivatives)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/cbrt_eg.html" title="Finding the Cubed Root With and Without Derivatives"><span class="index-entry-level-1">Finding the Cubed Root With and Without Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_root.html" title="Finding Zeros of Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Finding Zeros of Bessel Functions of the First and Second Kinds</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">round</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/real_concepts.html" title="Conceptual Requirements for Real Number Types"><span class="index-entry-level-1">Conceptual Requirements for Real Number Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rounding/round.html" title="Rounding Functions"><span class="index-entry-level-1">Rounding Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">roundf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">roundl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">rsqrt</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/rsqrt.html" title="Reciprocal square root"><span class="index-entry-level-1">Reciprocal square root</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">runs_above_and_below_median</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/runs_test.html" title="Runs tests"><span class="index-entry-level-1">Runs tests</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">runs_above_and_below_threshold</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/runs_test.html" title="Runs tests"><span class="index-entry-level-1">Runs tests</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_23"></a><span class="term">S</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sample_absolute_gini_coefficient</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/signal_statistics.html" title="Signal Statistics"><span class="index-entry-level-1">Signal Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sample_gini_coefficient</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sample_variance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">scalbln</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">scalblnf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">scalblnl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">scalbn</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">scalbnf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">scalbnl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">scale</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/cauchy_dist.html" title="Cauchy-Lorentz Distribution"><span class="index-entry-level-1">Cauchy-Lorentz Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/extreme_dist.html" title="Extreme Value Distribution"><span class="index-entry-level-1">Extreme Value Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_mean_and_sd_eg.html" title="Find mean and standard deviation example"><span class="index-entry-level-1">Find mean and standard deviation example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_scale_eg.html" title="Find Scale (Standard Deviation) Example"><span class="index-entry-level-1">Find Scale (Standard Deviation) Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/gamma_dist.html" title="Gamma (and Erlang) Distribution"><span class="index-entry-level-1">Gamma (and Erlang) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_chi_squared_dist.html" title="Inverse Chi Squared Distribution"><span class="index-entry-level-1">Inverse Chi Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/inverse_chi_squared_eg.html" title="Inverse Chi-Squared Distribution Bayes Example"><span class="index-entry-level-1">Inverse Chi-Squared Distribution Bayes Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gamma_dist.html" title="Inverse Gamma Distribution"><span class="index-entry-level-1">Inverse Gamma Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gaussian_dist.html" title="Inverse Gaussian (or Inverse Normal) Distribution"><span class="index-entry-level-1">Inverse Gaussian (or Inverse Normal) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/laplace_dist.html" title="Laplace Distribution"><span class="index-entry-level-1">Laplace Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/lognormal_dist.html" title="Log Normal Distribution"><span class="index-entry-level-1">Log Normal Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/logistic_dist.html" title="Logistic Distribution"><span class="index-entry-level-1">Logistic Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/normal_dist.html" title="Normal (Gaussian) Distribution"><span class="index-entry-level-1">Normal (Gaussian) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/pareto.html" title="Pareto Distribution"><span class="index-entry-level-1">Pareto Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/skew_normal_dist.html" title="Skew Normal Distribution"><span class="index-entry-level-1">Skew Normal Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/weibull_dist.html" title="Weibull Distribution"><span class="index-entry-level-1">Weibull Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">schroder_iterate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_deriv.html" title="Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder"><span class="index-entry-level-1">Root Finding With Derivatives: Newton-Raphson, Halley &amp; Schröder</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">semipolar</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/create.html" title="Quaternion Creation Functions"><span class="index-entry-level-1">Quaternion Creation Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">series</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html" title="Incomplete Gamma Functions"><span class="index-entry-level-1">Incomplete Gamma Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/owens_t.html" title="Owen's T function"><span class="index-entry-level-1">Owen's T function</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">set</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/fibonacci_numbers.html" title="Fibonacci Numbers"><span class="index-entry-level-1">Fibonacci Numbers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/reference.html" title="Reference"><span class="index-entry-level-1">Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">set_zero</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/polynomials.html" title="Polynomials"><span class="index-entry-level-1">Polynomials</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">shape</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/cauchy_dist.html" title="Cauchy-Lorentz Distribution"><span class="index-entry-level-1">Cauchy-Lorentz Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/gamma_dist.html" title="Gamma (and Erlang) Distribution"><span class="index-entry-level-1">Gamma (and Erlang) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gamma_dist.html" title="Inverse Gamma Distribution"><span class="index-entry-level-1">Inverse Gamma Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gaussian_dist.html" title="Inverse Gaussian (or Inverse Normal) Distribution"><span class="index-entry-level-1">Inverse Gaussian (or Inverse Normal) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/pareto.html" title="Pareto Distribution"><span class="index-entry-level-1">Pareto Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/skew_normal_dist.html" title="Skew Normal Distribution"><span class="index-entry-level-1">Skew Normal Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/weibull_dist.html" title="Weibull Distribution"><span class="index-entry-level-1">Weibull Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sign</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sign_functions.html" title="Sign Manipulation Functions"><span class="index-entry-level-1">Sign Manipulation Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">signbit</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sign_functions.html" title="Sign Manipulation Functions"><span class="index-entry-level-1">Sign Manipulation Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sinc/sinc_pi.html" title="sinc_pi"><span class="index-entry-level-0">sinc_pi</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sinc/sinhc_pi.html" title="sinhc_pi"><span class="index-entry-level-0">sinhc_pi</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/sin_pi.html" title="sin_pi"><span class="index-entry-level-0">sin_pi</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/st_eg/tut_mean_intervals.html" title="Calculating confidence intervals on the mean with the Students-t distribution"><span class="index-entry-level-1">Calculating confidence intervals on the mean with the Students-t distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/catmull_rom.html" title="Catmull-Rom Splines"><span class="index-entry-level-1">Catmull-Rom Splines</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/estrin.html" title="Estrin's method for polynomial evaluation"><span class="index-entry-level-1">Estrin's method for polynomial evaluation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/test_data.html" title="Graphing, Profiling, and Generating Test Data for Special Functions"><span class="index-entry-level-1">Graphing, Profiling, and Generating Test Data for Special Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/polynomials.html" title="Polynomials"><span class="index-entry-level-1">Polynomials</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">skewness</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/bernoulli_dist.html" title="Bernoulli Distribution"><span class="index-entry-level-1">Bernoulli Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/geometric_dist.html" title="Geometric Distribution"><span class="index-entry-level-1">Geometric Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/kolmogorov_smirnov_dist.html" title="Kolmogorov-Smirnov Distribution"><span class="index-entry-level-1">Kolmogorov-Smirnov Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/triangular_dist.html" title="Triangular Distribution"><span class="index-entry-level-1">Triangular Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">small</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/expm1.html" title="expm1"><span class="index-entry-level-1">expm1</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/inverse_chi_squared_eg.html" title="Inverse Chi-Squared Distribution Bayes Example"><span class="index-entry-level-1">Inverse Chi-Squared Distribution Bayes Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/mbessel.html" title="Modified Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Modified Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">smooth_cool_warm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/color_maps.html" title="Color Maps"><span class="index-entry-level-1">Color Maps</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">spherical</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_create.html" title="Octonion Creation Functions"><span class="index-entry-level-1">Octonion Creation Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/create.html" title="Quaternion Creation Functions"><span class="index-entry-level-1">Quaternion Creation Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">spherical_harmonic</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/sph_harm.html" title="Spherical Harmonics"><span class="index-entry-level-1">Spherical Harmonics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">spherical_harmonic_i</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/sph_harm.html" title="Spherical Harmonics"><span class="index-entry-level-1">Spherical Harmonics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">spherical_harmonic_r</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_poly/sph_harm.html" title="Spherical Harmonics"><span class="index-entry-level-1">Spherical Harmonics</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_bessel</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/sph_bessel.html" title="Spherical Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Spherical Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_bessell</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_bessel_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_derivatives.html" title="Derivatives of the Bessel Functions"><span class="index-entry-level-1">Derivatives of the Bessel Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_hankel_1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hankel/sph_hankel.html" title="Spherical Hankel Functions"><span class="index-entry-level-1">Spherical Hankel Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_hankel_2</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hankel/sph_hankel.html" title="Spherical Hankel Functions"><span class="index-entry-level-1">Spherical Hankel Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_legendre</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_legendref</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_legendrel</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_neumann</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/sph_bessel.html" title="Spherical Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Spherical Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_neumannl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/tr1_ref.html" title="TR1 C Functions Quick Reference"><span class="index-entry-level-1">TR1 C Functions Quick Reference</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sph_neumann_prime</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_derivatives.html" title="Derivatives of the Bessel Functions"><span class="index-entry-level-1">Derivatives of the Bessel Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/sqrt1pm1.html" title="sqrt1pm1"><span class="index-entry-level-0">sqrt1pm1</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">standard_deviation</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_location_eg.html" title="Find Location (Mean) Example"><span class="index-entry-level-1">Find Location (Mean) Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/find_eg/find_scale_eg.html" title="Find Scale (Standard Deviation) Example"><span class="index-entry-level-1">Find Scale (Standard Deviation) Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/normal_dist.html" title="Normal (Gaussian) Distribution"><span class="index-entry-level-1">Normal (Gaussian) Distribution</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">step</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">subtraction</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/digamma.html" title="Digamma"><span class="index-entry-level-1">Digamma</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sum</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cond.html" title="Condition Numbers"><span class="index-entry-level-1">Condition Numbers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sum_series</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/series_evaluation.html" title="Series Evaluation"><span class="index-entry-level-1">Series Evaluation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sup</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_value_ops.html" title="Octonion Value Operations"><span class="index-entry-level-1">Octonion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/value_op.html" title="Quaternion Value Operations"><span class="index-entry-level-1">Quaternion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">support</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_implementation.html" title="Additional Implementation Notes"><span class="index-entry-level-1">Additional Implementation Notes</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/daubechies.html" title="Daubechies Wavelets and Scaling Functions"><span class="index-entry-level-1">Daubechies Wavelets and Scaling Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sup_distance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sup_norm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_24"></a><span class="term">T</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">t</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/st_eg/tut_mean_intervals.html" title="Calculating confidence intervals on the mean with the Students-t distribution"><span class="index-entry-level-1">Calculating confidence intervals on the mean with the Students-t distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_t_dist.html#math_toolkit.dist_ref.dists.nc_t_dist.table_non_central_t_CDF" title="Table 5.8. Error rates for non central t CDF"><span class="index-entry-level-1">Error rates for non central t CDF</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_t_dist.html#math_toolkit.dist_ref.dists.nc_t_dist.table_non_central_t_CDF_complement" title="Table 5.9. Error rates for non central t CDF complement"><span class="index-entry-level-1">Error rates for non central t CDF complement</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/owens_t.html#math_toolkit.owens_t.table_owens_t" title="Table 8.86. Error rates for owens_t"><span class="index-entry-level-1">Error rates for owens_t</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_impl.html" title="Implementation"><span class="index-entry-level-1">Implementation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/issues.html" title="Known Issues, and TODO List"><span class="index-entry-level-1">Known Issues, and TODO List</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rational.html" title="Polynomial and Rational Function Evaluation"><span class="index-entry-level-1">Polynomial and Rational Function Evaluation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/skew_normal_dist.html" title="Skew Normal Distribution"><span class="index-entry-level-1">Skew Normal Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/vector_barycentric.html" title="Vector-valued Barycentric Rational Interpolation"><span class="index-entry-level-1">Vector-valued Barycentric Rational Interpolation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tangent_t2n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/tangent_numbers.html" title="Tangent Numbers"><span class="index-entry-level-1">Tangent Numbers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">term</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lanczos.html" title="The Lanczos Approximation"><span class="index-entry-level-1">The Lanczos Approximation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">terms</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_t_dist.html" title="Noncentral T Distribution"><span class="index-entry-level-1">Noncentral T Distribution</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">test</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/error_test.html" title="Relative Error and Testing"><span class="index-entry-level-1">Relative Error and Testing</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/normal_example/normal_misc.html" title="Some Miscellaneous Examples of the Normal (Gaussian) Distribution"><span class="index-entry-level-1">Some Miscellaneous Examples of the Normal (Gaussian) Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tgamma</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/ad_hoc_sf_policies.html" title="Changing the Policy on an Ad Hoc Basis for the Special Functions"><span class="index-entry-level-1">Changing the Policy on an Ad Hoc Basis for the Special Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html#math_toolkit.sf_gamma.igamma.table_gamma_p" title="Table 8.9. Error rates for gamma_p"><span class="index-entry-level-1">Error rates for gamma_p</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html#math_toolkit.sf_gamma.igamma.table_gamma_q" title="Table 8.10. Error rates for gamma_q"><span class="index-entry-level-1">Error rates for gamma_q</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html#math_toolkit.sf_gamma.igamma.table_tgamma_incomplete_" title="Table 8.12. Error rates for tgamma (incomplete)"><span class="index-entry-level-1">Error rates for tgamma (incomplete)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html#math_toolkit.sf_gamma.igamma.table_tgamma_lower" title="Table 8.11. Error rates for tgamma_lower"><span class="index-entry-level-1">Error rates for tgamma_lower</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/tgamma.html" title="Gamma"><span class="index-entry-level-1">Gamma</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html" title="Incomplete Gamma Functions"><span class="index-entry-level-1">Incomplete Gamma Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/lgamma.html" title="Log Gamma"><span class="index-entry-level-1">Log Gamma</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/namespace_pol.html" title="Setting Polices at Namespace Scope"><span class="index-entry-level-1">Setting Polices at Namespace Scope</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/namespace_policies.html" title="Setting Policies at Namespace or Translation Unit Scope"><span class="index-entry-level-1">Setting Policies at Namespace or Translation Unit Scope</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tgamma1pm1</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/tgamma.html#math_toolkit.sf_gamma.tgamma.table_tgamma1pm1" title="Table 8.2. Error rates for tgamma1pm1"><span class="index-entry-level-1">Error rates for tgamma1pm1</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/tgamma.html" title="Gamma"><span class="index-entry-level-1">Gamma</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tgammaf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tgammal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tgamma_delta_ratio</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/gamma_ratios.html" title="Ratios of Gamma Functions"><span class="index-entry-level-1">Ratios of Gamma Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tgamma_lower</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/igamma.html" title="Incomplete Gamma Functions"><span class="index-entry-level-1">Incomplete Gamma Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tgamma_ratio</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/gamma_ratios.html" title="Ratios of Gamma Functions"><span class="index-entry-level-1">Ratios of Gamma Functions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">title</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tol</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/elliptic_eg.html" title="A More complex example - Inverting the Elliptic Integrals"><span class="index-entry-level-1">A More complex example - Inverting the Elliptic Integrals</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_noderiv/bisect.html" title="Bisection"><span class="index-entry-level-1">Bisection</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/cbrt_eg.html" title="Finding the Cubed Root With and Without Derivatives"><span class="index-entry-level-1">Finding the Cubed Root With and Without Derivatives</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">toms748_solve</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_noderiv/TOMS748.html" title="Algorithm TOMS 748: Alefeld, Potra and Shi: Enclosing zeros of continuous functions"><span class="index-entry-level-1">Algorithm TOMS 748: Alefeld, Potra and Shi: Enclosing zeros of continuous functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/roots_noderiv.html" title="Root Finding Without Derivatives"><span class="index-entry-level-1">Root Finding Without Derivatives</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">total_variation</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/norms.html" title="Norms"><span class="index-entry-level-1">Norms</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">to_8bit_rgba</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/color_maps.html" title="Color Maps"><span class="index-entry-level-1">Color Maps</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">trapezoidal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/trapezoidal.html" title="Trapezoidal Quadrature"><span class="index-entry-level-1">Trapezoidal Quadrature</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_gamma/trigamma.html" title="Trigamma"><span class="index-entry-level-0">trigamma</span></a></p></li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">trunc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/real_concepts.html" title="Conceptual Requirements for Real Number Types"><span class="index-entry-level-1">Conceptual Requirements for Real Number Types</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ccmath.html" title="Constexpr CMath"><span class="index-entry-level-1">Constexpr CMath</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/rounding/trunc.html" title="Truncation Functions"><span class="index-entry-level-1">Truncation Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">truncf</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">truncl</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/overview_tr1.html" title="C99 and C++ TR1 C-style Functions"><span class="index-entry-level-1">C99 and C++ TR1 C-style Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/main_tr1.html" title="C99 and TR1 C Functions Overview"><span class="index-entry-level-1">C99 and TR1 C Functions Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/c99.html" title="C99 C Functions"><span class="index-entry-level-1">C99 C Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">typeid</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_25"></a><span class="term">U</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">u</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/t_test.html" title="t-tests"><span class="index-entry-level-1">t -tests</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/z_test.html" title="z-tests"><span class="index-entry-level-1">z -tests</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ulp</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/next_float/ulp.html" title="Obtaining the Size of a Unit In the Last Place - ULP"><span class="index-entry-level-1">Obtaining the Size of a Unit In the Last Place - ULP</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ulp_envelope</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">unchecked_bernoulli_b2n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/bernoulli_numbers.html" title="Bernoulli Numbers"><span class="index-entry-level-1">Bernoulli Numbers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">unchecked_factorial</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/factorials/sf_factorial.html" title="Factorial"><span class="index-entry-level-1">Factorial</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">unchecked_fibonacci</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/fibonacci_numbers.html" title="Fibonacci Numbers"><span class="index-entry-level-1">Fibonacci Numbers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">unreal</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_mem_fun.html" title="Octonion Member Functions"><span class="index-entry-level-1">Octonion Member Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_specialization.html" title="Octonion Specializations"><span class="index-entry-level-1">Octonion Specializations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_value_ops.html" title="Octonion Value Operations"><span class="index-entry-level-1">Octonion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_mem_fun.html" title="Quaternion Member Functions"><span class="index-entry-level-1">Quaternion Member Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/spec.html" title="Quaternion Specializations"><span class="index-entry-level-1">Quaternion Specializations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/value_op.html" title="Quaternion Value Operations"><span class="index-entry-level-1">Quaternion Value Operations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat_synopsis.html" title="Synopsis"><span class="index-entry-level-1">Synopsis</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/octonion.html" title="Template Class octonion"><span class="index-entry-level-1">Template Class octonion</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/quat.html" title="Template Class quaternion"><span class="index-entry-level-1">Template Class quaternion</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">update_target_error</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/naive_monte_carlo.html" title="Naive Monte Carlo Integration"><span class="index-entry-level-1">Naive Monte Carlo Integration</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">user_denorm_error</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/error_handling_policies.html" title="Error Handling Policies"><span class="index-entry-level-1">Error Handling Policies</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">user_domain_error</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/error_handling_policies.html" title="Error Handling Policies"><span class="index-entry-level-1">Error Handling Policies</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">user_evaluation_error</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">user_indeterminate_result_error</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/error_handling_policies.html" title="Error Handling Policies"><span class="index-entry-level-1">Error Handling Policies</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">user_overflow_error</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/powers/ct_pow.html" title="Compile Time Power of a Runtime Base"><span class="index-entry-level-1">Compile Time Power of a Runtime Base</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/error_handling_policies.html" title="Error Handling Policies"><span class="index-entry-level-1">Error Handling Policies</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">user_pole_error</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/error_handling_policies.html" title="Error Handling Policies"><span class="index-entry-level-1">Error Handling Policies</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">user_rounding_error</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/error_handling_policies.html" title="Error Handling Policies"><span class="index-entry-level-1">Error Handling Policies</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">user_underflow_error</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_tutorial/user_def_err_pol.html" title="Calling User Defined Error Handlers"><span class="index-entry-level-1">Calling User Defined Error Handlers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/pol_ref/error_handling_policies.html" title="Error Handling Policies"><span class="index-entry-level-1">Error Handling Policies</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_26"></a><span class="term">V</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">v</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_first.html" title="Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_cubic_b.html" title="Cardinal Cubic B-spline interpolation"><span class="index-entry-level-1">Cardinal Cubic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_quintic_b.html" title="Cardinal Quintic B-spline interpolation"><span class="index-entry-level-1">Cardinal Quintic B-spline interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cardinal_trigonometric.html" title="Cardinal Trigonometric interpolation"><span class="index-entry-level-1">Cardinal Trigonometric interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hankel/cyl_hankel.html" title="Cyclic Hankel Functions"><span class="index-entry-level-1">Cyclic Hankel Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/empirical_cdf.html" title="Empirical Cumulative Distribution Function"><span class="index-entry-level-1">Empirical Cumulative Distribution Function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/diff0.html" title="Lanczos Smoothing Derivatives"><span class="index-entry-level-1">Lanczos Smoothing Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/mbessel.html" title="Modified Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Modified Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_chi_squared_dist.html" title="Noncentral Chi-Squared Distribution"><span class="index-entry-level-1">Noncentral Chi-Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/sph_bessel.html" title="Spherical Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Spherical Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/t_test.html" title="t-tests"><span class="index-entry-level-1">t -tests</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/anderson_darling.html" title="The Anderson-Darling Test"><span class="index-entry-level-1">The Anderson-Darling Test</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ljung_box.html" title="The Ljung-Box Test"><span class="index-entry-level-1">The Ljung-Box Test</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/whittaker_shannon.html" title="Whittaker-Shannon interpolation"><span class="index-entry-level-1">Whittaker-Shannon interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/z_test.html" title="z-tests"><span class="index-entry-level-1">z -tests</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">value</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/next_float/float_distance.html" title="Calculating the Representation Distance Between Two floating-point Values (ULP) float_distance"><span class="index-entry-level-1">Calculating the Representation Distance Between Two floating-point Values (ULP) float_distance</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/chi_squared_dist.html" title="Chi Squared Distribution"><span class="index-entry-level-1">Chi Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/generic.html" title="Generic operations common to all distributions are non-member functions"><span class="index-entry-level-1">Generic operations common to all distributions are non-member functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">values</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/cauchy_dist.html" title="Cauchy-Lorentz Distribution"><span class="index-entry-level-1">Cauchy-Lorentz Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/dist_params.html" title="Discrete Probability Distributions"><span class="index-entry-level-1">Discrete Probability Distributions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/overview/generic.html" title="Generic operations common to all distributions are non-member functions"><span class="index-entry-level-1">Generic operations common to all distributions are non-member functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/geometric_dist.html" title="Geometric Distribution"><span class="index-entry-level-1">Geometric Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/fp_facets/facets_intro.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/inverse_chi_squared_eg.html" title="Inverse Chi-Squared Distribution Bayes Example"><span class="index-entry-level-1">Inverse Chi-Squared Distribution Bayes Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/constants_faq.html" title="Math Constants FAQs"><span class="index-entry-level-1">Math Constants FAQs</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/negative_binomial_dist.html" title="Negative Binomial Distribution"><span class="index-entry-level-1">Negative Binomial Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_beta_dist.html" title="Noncentral Beta Distribution"><span class="index-entry-level-1">Noncentral Beta Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_chi_squared_dist.html" title="Noncentral Chi-Squared Distribution"><span class="index-entry-level-1">Noncentral Chi-Squared Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/owens_t.html" title="Owen's T function"><span class="index-entry-level-1">Owen's T function</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/special_tut/special_tut_test.html" title="Testing"><span class="index-entry-level-1">Testing</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/high_precision/use_multiprecision.html" title="Using Boost.Multiprecision"><span class="index-entry-level-1">Using Boost.Multiprecision</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">variance</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/cs_eg/chi_sq_size.html" title="Estimating the Required Sample Sizes for a Chi-Square Test for the Standard Deviation"><span class="index-entry-level-1">Estimating the Required Sample Sizes for a Chi-Square Test for the Standard Deviation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/geometric_dist.html" title="Geometric Distribution"><span class="index-entry-level-1">Geometric Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/inverse_chi_squared_eg.html" title="Inverse Chi-Squared Distribution Bayes Example"><span class="index-entry-level-1">Inverse Chi-Squared Distribution Bayes Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/inverse_gamma_dist.html" title="Inverse Gamma Distribution"><span class="index-entry-level-1">Inverse Gamma Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/lognormal_dist.html" title="Log Normal Distribution"><span class="index-entry-level-1">Log Normal Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/logistic_dist.html" title="Logistic Distribution"><span class="index-entry-level-1">Logistic Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/nmp.html" title="Non-Member Properties"><span class="index-entry-level-1">Non-Member Properties</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/triangular_dist.html" title="Triangular Distribution"><span class="index-entry-level-1">Triangular Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/uniform_dist.html" title="Uniform Distribution"><span class="index-entry-level-1">Uniform Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/univariate_statistics.html" title="Univariate Statistics"><span class="index-entry-level-1">Univariate Statistics</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">vertical_lines</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">viridis</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/color_maps.html" title="Color Maps"><span class="index-entry-level-1">Color Maps</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_27"></a><span class="term">W</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">W</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/lambert_w.html" title="Lambert W function"><span class="index-entry-level-1">Lambert W function</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">weights</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/gauss_kronrod.html" title="Gauss-Kronrod Quadrature"><span class="index-entry-level-1">Gauss-Kronrod Quadrature</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/gauss.html" title="Gauss-Legendre quadrature"><span class="index-entry-level-1">Gauss-Legendre quadrature</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">width</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/ulps_plots.html" title="ULPs Plots"><span class="index-entry-level-1">ULPs Plots</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_28"></a><span class="term">X</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">x</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/barycentric.html" title="Barycentric Rational Interpolation"><span class="index-entry-level-1">Barycentric Rational Interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hankel/cyl_hankel.html" title="Cyclic Hankel Functions"><span class="index-entry-level-1">Cyclic Hankel Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/dist_construct_eg.html" title="Distribution Construction Examples"><span class="index-entry-level-1">Distribution Construction Examples</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bessel/bessel_root.html" title="Finding Zeros of Bessel Functions of the First and Second Kinds"><span class="index-entry-level-1">Finding Zeros of Bessel Functions of the First and Second Kinds</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/nc_t_dist.html" title="Noncentral T Distribution"><span class="index-entry-level-1">Noncentral T Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/oct_overview.html" title="Overview"><span class="index-entry-level-1">Overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/hankel/sph_hankel.html" title="Spherical Hankel Functions"><span class="index-entry-level-1">Spherical Hankel Functions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sf_beta/ibeta_inv_function.html" title="The Incomplete Beta Function Inverses"><span class="index-entry-level-1">The Incomplete Beta Function Inverses</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_29"></a><span class="term">Y</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">y</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/barycentric.html" title="Barycentric Rational Interpolation"><span class="index-entry-level-1">Barycentric Rational Interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/cubic_hermite.html" title="Cubic Hermite interpolation"><span class="index-entry-level-1">Cubic Hermite interpolation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/internals/minimax.html" title="Minimax Approximations and the Remez Algorithm"><span class="index-entry-level-1">Minimax Approximations and the Remez Algorithm</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/vector_barycentric.html" title="Vector-valued Barycentric Rational Interpolation"><span class="index-entry-level-1">Vector-valued Barycentric Rational Interpolation</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_30"></a><span class="term">Z</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">zero</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/number_series/bernoulli_numbers.html" title="Bernoulli Numbers"><span class="index-entry-level-1">Bernoulli Numbers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/binom_eg/binomial_coinflip_example.html" title="Binomial Coin-Flipping Example"><span class="index-entry-level-1">Binomial Coin-Flipping Example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/bad_roots.html" title="Examples Where Root Finding Goes Wrong"><span class="index-entry-level-1">Examples Where Root Finding Goes Wrong</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/root_finding_examples/cbrt_eg.html" title="Finding the Cubed Root With and Without Derivatives"><span class="index-entry-level-1">Finding the Cubed Root With and Without Derivatives</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/dist_ref/dists/geometric_dist.html" title="Geometric Distribution"><span class="index-entry-level-1">Geometric Distribution</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/double_exponential/de_tanh_sinh_2_arg.html" title="Handling functions with large features near an endpoint with tanh-sinh quadrature"><span class="index-entry-level-1">Handling functions with large features near an endpoint with tanh-sinh quadrature</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/history1.html" title="History and What's New"><span class="index-entry-level-1">History and What's New</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/brent_minima.html" title="Locating Function Minima using Brent's algorithm"><span class="index-entry-level-1">Locating Function Minima using Brent's algorithm</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/stat_tut/weg/neg_binom_eg/negative_binomial_example1.html" title="Negative Binomial Sales Quota Example."><span class="index-entry-level-1">Negative Binomial Sales Quota Example.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/sign_functions.html" title="Sign Manipulation Functions"><span class="index-entry-level-1">Sign Manipulation Functions</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">zeta</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/expint/expint_i.html" title="Exponential Integral Ei"><span class="index-entry-level-1">Exponential Integral Ei</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/zetas/zeta.html" title="Riemann Zeta Function"><span class="index-entry-level-1">Riemann Zeta Function</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_31"></a><span class="term">_</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">__has_include</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../math_toolkit/standalone.html" title="Standalone Usage"><span class="index-entry-level-1">Standalone Usage</span></a></p></li></ul></div>
</li></ul></div></dd>
</dl></div>
</div>
<div class="copyright-footer">Copyright © 2006-2021 Nikhar Agrawal, Anton Bikineev, Matthew Borland,
      Paul A. Bristow, Marco Guazzone, Christopher Kormanyos, Hubert Holin, Bruno
      Lalande, John Maddock, Evan Miller, Jeremy Murphy, Matthew Pulver, Johan Råde,
      Gautam Sewani, Benjamin Sobotta, Nicholas Thompson, Thijs van den Berg, Daryle
      Walker and Xiaogang Zhang<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../indexes.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../indexes.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="s02.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
