<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="750" height ="400" version="1.1"
xmlns:svg ="http://www.w3.org/2000/svg"
xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
xmlns:cc="http://web.resource.org/cc/"
xmlns:dc="http://purl.org/dc/elements/1.1/"
xmlns ="http://www.w3.org/2000/svg"
>
<!-- SVG plot written using Boost.Plot program (C<PERSON>) --> 
<!-- Use, modification and distribution of Boost.Plot subject to the --> 
<!-- Boost Software License, Version 1.0.--> 
<!-- (See accompanying file LICENSE_1_0.txt --> 
<!-- or copy at http://www.boost.org/LICENSE_1_0.txt) --> 

<clipPath id="plot_window"><rect x="85.2" y="59" width="473.6" height="281"/></clipPath>
<g id="imageBackground" stroke="rgb(119,136,153)" fill="rgb(255,255,255)" stroke-width="2"><rect x="0" y="0" width="750" height="400"/></g>
<g id="plotBackground" stroke="rgb(119,136,153)" fill="rgb(255,255,255)" stroke-width="2"><rect x="84.2" y="58" width="475.6" height="283"/></g>
<g id="yMinorGrid" stroke="rgb(200,220,255)" stroke-width="0.5"></g>
<g id="yMajorGrid" stroke="rgb(200,220,255)" stroke-width="1"></g>
<g id="xMinorGrid" stroke="rgb(200,220,255)" stroke-width="0.5"></g>
<g id="xMajorGrid" stroke="rgb(200,220,255)" stroke-width="1"></g>
<g id="yAxis" stroke="rgb(0,0,0)" stroke-width="1"><line x1="322" y1="58" x2="322" y2="346"/><line x1="84.2" y1="58" x2="84.2" y2="341"/></g>
<g id="xAxis" stroke="rgb(0,0,0)" stroke-width="1"><line x1="79.2" y1="341" x2="559.8" y2="341"/><line x1="79.2" y1="341" x2="559.8" y2="341"/></g>
<g id="yMinorTicks" stroke="rgb(0,0,0)" stroke-width="1"><path d="M82.2,335.9 L84.2,335.9 M82.2,330.7 L84.2,330.7 M82.2,325.6 L84.2,325.6 M82.2,320.4 L84.2,320.4 M82.2,310.1 L84.2,310.1 M82.2,305 L84.2,305 M82.2,299.8 L84.2,299.8 M82.2,294.7 L84.2,294.7 M82.2,284.4 L84.2,284.4 M82.2,279.3 L84.2,279.3 M82.2,274.1 L84.2,274.1 M82.2,269 L84.2,269 M82.2,258.7 L84.2,258.7 M82.2,253.5 L84.2,253.5 M82.2,248.4 L84.2,248.4 M82.2,243.2 L84.2,243.2 M82.2,232.9 L84.2,232.9 M82.2,227.8 L84.2,227.8 M82.2,222.7 L84.2,222.7 M82.2,217.5 L84.2,217.5 M82.2,207.2 L84.2,207.2 M82.2,202.1 L84.2,202.1 M82.2,196.9 L84.2,196.9 M82.2,191.8 L84.2,191.8 M82.2,181.5 L84.2,181.5 M82.2,176.3 L84.2,176.3 M82.2,171.2 L84.2,171.2 M82.2,166.1 L84.2,166.1 M82.2,155.8 L84.2,155.8 M82.2,150.6 L84.2,150.6 M82.2,145.5 L84.2,145.5 M82.2,140.3 L84.2,140.3 M82.2,130 L84.2,130 M82.2,124.9 L84.2,124.9 M82.2,119.7 L84.2,119.7 M82.2,114.6 L84.2,114.6 M82.2,104.3 L84.2,104.3 M82.2,99.16 L84.2,99.16 M82.2,94.02 L84.2,94.02 M82.2,88.87 L84.2,88.87 M82.2,78.58 L84.2,78.58 M82.2,73.44 L84.2,73.44 M82.2,68.29 L84.2,68.29 M82.2,63.15 L84.2,63.15 M82.2,341 L84.2,341 " fill="none"/></g>
<g id="xMinorTicks" stroke="rgb(0,0,0)" stroke-width="1"><path d="M347.7,341 L347.7,343 M373.4,341 L373.4,343 M399.1,341 L399.1,343 M424.8,341 L424.8,343 M476.2,341 L476.2,343 M501.9,341 L501.9,343 M527.6,341 L527.6,343 M553.3,341 L553.3,343 M296.3,341 L296.3,343 M270.6,341 L270.6,343 M244.9,341 L244.9,343 M219.2,341 L219.2,343 M167.8,341 L167.8,343 M142.1,341 L142.1,343 M116.4,341 L116.4,343 M90.7,341 L90.7,343 " fill="none"/></g>
<g id="yMajorTicks" stroke="rgb(0,0,0)" stroke-width="2"><path d="M79.2,341 L84.2,341 M79.2,315.3 L84.2,315.3 M79.2,289.5 L84.2,289.5 M79.2,263.8 L84.2,263.8 M79.2,238.1 L84.2,238.1 M79.2,212.4 L84.2,212.4 M79.2,186.6 L84.2,186.6 M79.2,160.9 L84.2,160.9 M79.2,135.2 L84.2,135.2 M79.2,109.5 L84.2,109.5 M79.2,83.73 L84.2,83.73 M79.2,58 L84.2,58 M79.2,341 L84.2,341 " fill="none"/></g>
<g id="xMajorTicks" stroke="rgb(0,0,0)" stroke-width="2"><path d="M322,341 L322,346 M450.5,341 L450.5,346 M322,341 L322,346 M193.5,341 L193.5,346 " fill="none"/></g>
<g id="xTicksValues">
<text x="322" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">0</text>
<text x="450.5" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">10</text>
<text x="322" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">0</text>
<text x="193.5" y="361.6" text-anchor="middle" font-size="12" font-family="Verdana">-10</text></g>
<g id="yTicksValues">
<text x="73.2" y="343.4" text-anchor="end" font-size="12" font-family="Verdana">0</text>
<text x="73.2" y="317.7" text-anchor="end" font-size="12" font-family="Verdana">0.1</text>
<text x="73.2" y="291.9" text-anchor="end" font-size="12" font-family="Verdana">0.2</text>
<text x="73.2" y="266.2" text-anchor="end" font-size="12" font-family="Verdana">0.3</text>
<text x="73.2" y="240.5" text-anchor="end" font-size="12" font-family="Verdana">0.4</text>
<text x="73.2" y="214.8" text-anchor="end" font-size="12" font-family="Verdana">0.5</text>
<text x="73.2" y="189" text-anchor="end" font-size="12" font-family="Verdana">0.6</text>
<text x="73.2" y="163.3" text-anchor="end" font-size="12" font-family="Verdana">0.7</text>
<text x="73.2" y="137.6" text-anchor="end" font-size="12" font-family="Verdana">0.8</text>
<text x="73.2" y="111.9" text-anchor="end" font-size="12" font-family="Verdana">0.9</text>
<text x="73.2" y="86.13" text-anchor="end" font-size="12" font-family="Verdana">1</text>
<text x="73.2" y="60.4" text-anchor="end" font-size="12" font-family="Verdana">1.1</text>
<text x="73.2" y="343.4" text-anchor="end" font-size="12" font-family="Verdana">0</text></g>
<g id="yLabel">
<text x="42.9" y="199.5" text-anchor="middle" transform = "rotate(-90 42.9 199.5 )" font-size="14" font-family="Verdana">Probability</text></g>
<g id="xLabel">
<text x="322" y="376.7" text-anchor="middle" font-size="14" font-family="Verdana">Random Variable</text></g>
<g id="plotLines" stroke-width="2"><g clip-path="url(#plot_window)" stroke="rgb(0,0,139)" stroke-width="1"><path d="M84.2,335.7 L86.58,335.3 L88.96,334.8 L91.33,334.4 L93.71,333.8 L96.09,333.3 L98.47,332.7 L100.8,332.1 L103.2,331.4 L105.6,330.6 L108,329.8 L110.4,328.9 L112.7,328 L115.1,327 L117.5,325.8 L119.9,324.7 L122.2,323.4 L124.6,322 L127,320.5 L129.4,318.9 L131.8,317.1 L134.1,315.3 L136.5,313.2 L138.9,311.1 L141.3,308.7 L143.6,306.2 L146,303.5 L148.4,300.7 L150.8,297.6 L153.2,294.3 L155.5,290.7 L157.9,287 L160.3,282.9 L162.7,278.7 L165,274.1 L167.4,269.3 L169.8,264.2 L172.2,258.8 L174.6,253.2 L176.9,247.2 L179.3,241 L181.7,234.5 L184.1,227.8 L186.4,220.8 L188.8,213.6 L191.2,206.2 L193.6,198.7 L196,191 L198.3,183.2 L200.7,175.4 L203.1,167.6 L205.5,159.9 L207.8,152.3 L210.2,145 L212.6,137.8 L215,131 L217.4,124.6 L219.7,118.6 L222.1,113.1 L224.5,108 L226.9,103.5 L229.2,99.59 L231.6,96.18 L234,93.3 L236.4,90.92 L238.8,88.99 L241.1,87.47 L243.5,86.32 L245.9,85.46 L248.3,84.84 L250.6,84.42 L253,84.14 L255.4,83.96 L257.8,83.85 L260.2,83.79 L262.5,83.76 L264.9,83.74 L267.3,83.73 L269.7,83.73 L272,83.73 L274.4,83.73 L276.8,83.73 L279.2,83.73 L281.6,83.73 L283.9,83.73 L286.3,83.73 L288.7,83.73 L291.1,83.73 L293.4,83.73 L295.8,83.73 L298.2,83.73 L300.6,83.73 L303,83.73 L305.3,83.73 L307.7,83.73 L310.1,83.73 L312.5,83.73 L314.8,83.73 L317.2,83.73 L319.6,83.73 L322,83.73 L324.4,83.73 L326.7,83.73 L329.1,83.73 L331.5,83.73 L333.9,83.73 L336.2,83.73 L338.6,83.73 L341,83.73 L343.4,83.73 L345.8,83.73 L348.1,83.73 L350.5,83.73 L352.9,83.73 L355.3,83.73 L357.6,83.73 L360,83.73 L362.4,83.73 L364.8,83.73 L367.2,83.73 L369.5,83.73 L371.9,83.73 L374.3,83.73 L376.7,83.73 L379,83.73 L381.4,83.73 L383.8,83.73 L386.2,83.73 L388.6,83.73 L390.9,83.73 L393.3,83.73 L395.7,83.73 L398.1,83.73 L400.4,83.73 L402.8,83.73 L405.2,83.73 L407.6,83.73 L410,83.73 L412.3,83.73 L414.7,83.73 L417.1,83.73 L419.5,83.73 L421.8,83.73 L424.2,83.73 L426.6,83.73 L429,83.73 L431.4,83.73 L433.7,83.73 L436.1,83.73 L438.5,83.73 L440.9,83.73 L443.2,83.73 L445.6,83.73 L448,83.73 L450.4,83.73 L452.8,83.73 L455.1,83.73 L457.5,83.73 L459.9,83.73 L462.3,83.73 L464.6,83.73 L467,83.73 L469.4,83.73 L471.8,83.73 L474.2,83.73 L476.5,83.73 L478.9,83.73 L481.3,83.73 L483.7,83.73 L486,83.73 L488.4,83.73 L490.8,83.73 L493.2,83.73 L495.6,83.73 L497.9,83.73 L500.3,83.73 L502.7,83.73 L505.1,83.73 L507.4,83.73 L509.8,83.73 L512.2,83.73 L514.6,83.73 L517,83.73 L519.3,83.73 L521.7,83.73 L524.1,83.73 L526.5,83.73 L528.8,83.73 L531.2,83.73 L533.6,83.73 L536,83.73 L538.4,83.73 L540.7,83.73 L543.1,83.73 L545.5,83.73 L547.9,83.73 L550.2,83.73 L552.6,83.73 L555,83.73 L557.4,83.73 L559.8,83.73 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(139,0,0)" stroke-width="1"><path d="M84.2,341 L86.58,341 L88.96,341 L91.33,341 L93.71,341 L96.09,340.9 L98.47,340.9 L100.8,340.9 L103.2,340.9 L105.6,340.9 L108,340.9 L110.4,340.9 L112.7,340.9 L115.1,340.9 L117.5,340.9 L119.9,340.9 L122.2,340.8 L124.6,340.8 L127,340.8 L129.4,340.8 L131.8,340.8 L134.1,340.7 L136.5,340.7 L138.9,340.7 L141.3,340.6 L143.6,340.6 L146,340.6 L148.4,340.5 L150.8,340.4 L153.2,340.4 L155.5,340.3 L157.9,340.2 L160.3,340.1 L162.7,340 L165,339.9 L167.4,339.7 L169.8,339.6 L172.2,339.4 L174.6,339.2 L176.9,338.9 L179.3,338.7 L181.7,338.4 L184.1,338 L186.4,337.6 L188.8,337.1 L191.2,336.6 L193.6,336 L196,335.3 L198.3,334.5 L200.7,333.6 L203.1,332.6 L205.5,331.4 L207.8,330.1 L210.2,328.5 L212.6,326.8 L215,324.8 L217.4,322.5 L219.7,319.9 L222.1,316.9 L224.5,313.5 L226.9,309.7 L229.2,305.4 L231.6,300.6 L234,295.1 L236.4,289 L238.8,282.3 L241.1,274.8 L243.5,266.5 L245.9,257.5 L248.3,247.7 L250.6,237.2 L253,226 L255.4,214.2 L257.8,202 L260.2,189.5 L262.5,176.8 L264.9,164.3 L267.3,152.1 L269.7,140.5 L272,129.7 L274.4,120.1 L276.8,111.6 L279.2,104.4 L281.6,98.53 L283.9,93.93 L286.3,90.47 L288.7,87.99 L291.1,86.3 L293.4,85.21 L295.8,84.54 L298.2,84.15 L300.6,83.93 L303,83.82 L305.3,83.77 L307.7,83.75 L310.1,83.74 L312.5,83.73 L314.8,83.73 L317.2,83.73 L319.6,83.73 L322,83.73 L324.4,83.73 L326.7,83.73 L329.1,83.73 L331.5,83.73 L333.9,83.73 L336.2,83.73 L338.6,83.73 L341,83.73 L343.4,83.73 L345.8,83.73 L348.1,83.73 L350.5,83.73 L352.9,83.73 L355.3,83.73 L357.6,83.73 L360,83.73 L362.4,83.73 L364.8,83.73 L367.2,83.73 L369.5,83.73 L371.9,83.73 L374.3,83.73 L376.7,83.73 L379,83.73 L381.4,83.73 L383.8,83.73 L386.2,83.73 L388.6,83.73 L390.9,83.73 L393.3,83.73 L395.7,83.73 L398.1,83.73 L400.4,83.73 L402.8,83.73 L405.2,83.73 L407.6,83.73 L410,83.73 L412.3,83.73 L414.7,83.73 L417.1,83.73 L419.5,83.73 L421.8,83.73 L424.2,83.73 L426.6,83.73 L429,83.73 L431.4,83.73 L433.7,83.73 L436.1,83.73 L438.5,83.73 L440.9,83.73 L443.2,83.73 L445.6,83.73 L448,83.73 L450.4,83.73 L452.8,83.73 L455.1,83.73 L457.5,83.73 L459.9,83.73 L462.3,83.73 L464.6,83.73 L467,83.73 L469.4,83.73 L471.8,83.73 L474.2,83.73 L476.5,83.73 L478.9,83.73 L481.3,83.73 L483.7,83.73 L486,83.73 L488.4,83.73 L490.8,83.73 L493.2,83.73 L495.6,83.73 L497.9,83.73 L500.3,83.73 L502.7,83.73 L505.1,83.73 L507.4,83.73 L509.8,83.73 L512.2,83.73 L514.6,83.73 L517,83.73 L519.3,83.73 L521.7,83.73 L524.1,83.73 L526.5,83.73 L528.8,83.73 L531.2,83.73 L533.6,83.73 L536,83.73 L538.4,83.73 L540.7,83.73 L543.1,83.73 L545.5,83.73 L547.9,83.73 L550.2,83.73 L552.6,83.73 L555,83.73 L557.4,83.73 L559.8,83.73 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(0,100,0)" stroke-width="1"><path d="M84.2,341 L86.58,341 L88.96,341 L91.33,341 L93.71,341 L96.09,341 L98.47,341 L100.8,341 L103.2,341 L105.6,341 L108,341 L110.4,341 L112.7,341 L115.1,341 L117.5,341 L119.9,341 L122.2,341 L124.6,341 L127,341 L129.4,341 L131.8,341 L134.1,341 L136.5,341 L138.9,341 L141.3,341 L143.6,341 L146,341 L148.4,341 L150.8,341 L153.2,341 L155.5,341 L157.9,341 L160.3,341 L162.7,341 L165,341 L167.4,341 L169.8,341 L172.2,341 L174.6,341 L176.9,341 L179.3,341 L181.7,341 L184.1,341 L186.4,341 L188.8,341 L191.2,341 L193.6,341 L196,341 L198.3,341 L200.7,341 L203.1,341 L205.5,341 L207.8,341 L210.2,341 L212.6,341 L215,341 L217.4,341 L219.7,341 L222.1,341 L224.5,341 L226.9,341 L229.2,341 L231.6,341 L234,341 L236.4,341 L238.8,341 L241.1,341 L243.5,341 L245.9,341 L248.3,341 L250.6,341 L253,341 L255.4,340.9 L257.8,340.9 L260.2,340.9 L262.5,340.9 L264.9,340.8 L267.3,340.8 L269.7,340.7 L272,340.6 L274.4,340.5 L276.8,340.3 L279.2,340 L281.6,339.7 L283.9,339.2 L286.3,338.5 L288.7,337.5 L291.1,336.2 L293.4,334.5 L295.8,332.1 L298.2,328.9 L300.6,324.7 L303,319.2 L305.3,312.1 L307.7,303.3 L310.1,292.6 L312.5,279.7 L314.8,265 L317.2,248.5 L319.6,230.8 L322,212.4 L324.4,194 L326.7,176.2 L329.1,159.8 L331.5,145 L333.9,132.2 L336.2,121.4 L338.6,112.6 L341,105.5 L343.4,100 L345.8,95.81 L348.1,92.62 L350.5,90.24 L352.9,88.48 L355.3,87.19 L357.6,86.25 L360,85.56 L362.4,85.07 L364.8,84.71 L367.2,84.44 L369.5,84.25 L371.9,84.12 L374.3,84.02 L376.7,83.94 L379,83.89 L381.4,83.85 L383.8,83.82 L386.2,83.8 L388.6,83.78 L390.9,83.77 L393.3,83.76 L395.7,83.75 L398.1,83.75 L400.4,83.74 L402.8,83.74 L405.2,83.74 L407.6,83.73 L410,83.73 L412.3,83.73 L414.7,83.73 L417.1,83.73 L419.5,83.73 L421.8,83.73 L424.2,83.73 L426.6,83.73 L429,83.73 L431.4,83.73 L433.7,83.73 L436.1,83.73 L438.5,83.73 L440.9,83.73 L443.2,83.73 L445.6,83.73 L448,83.73 L450.4,83.73 L452.8,83.73 L455.1,83.73 L457.5,83.73 L459.9,83.73 L462.3,83.73 L464.6,83.73 L467,83.73 L469.4,83.73 L471.8,83.73 L474.2,83.73 L476.5,83.73 L478.9,83.73 L481.3,83.73 L483.7,83.73 L486,83.73 L488.4,83.73 L490.8,83.73 L493.2,83.73 L495.6,83.73 L497.9,83.73 L500.3,83.73 L502.7,83.73 L505.1,83.73 L507.4,83.73 L509.8,83.73 L512.2,83.73 L514.6,83.73 L517,83.73 L519.3,83.73 L521.7,83.73 L524.1,83.73 L526.5,83.73 L528.8,83.73 L531.2,83.73 L533.6,83.73 L536,83.73 L538.4,83.73 L540.7,83.73 L543.1,83.73 L545.5,83.73 L547.9,83.73 L550.2,83.73 L552.6,83.73 L555,83.73 L557.4,83.73 L559.8,83.73 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(255,140,0)" stroke-width="1"><path d="M84.2,341 L86.58,341 L88.96,341 L91.33,341 L93.71,341 L96.09,341 L98.47,341 L100.8,341 L103.2,341 L105.6,341 L108,341 L110.4,341 L112.7,341 L115.1,341 L117.5,341 L119.9,341 L122.2,341 L124.6,341 L127,341 L129.4,341 L131.8,341 L134.1,341 L136.5,341 L138.9,341 L141.3,341 L143.6,341 L146,341 L148.4,341 L150.8,341 L153.2,341 L155.5,341 L157.9,341 L160.3,341 L162.7,341 L165,341 L167.4,341 L169.8,341 L172.2,341 L174.6,341 L176.9,341 L179.3,341 L181.7,341 L184.1,341 L186.4,341 L188.8,341 L191.2,341 L193.6,341 L196,341 L198.3,341 L200.7,341 L203.1,341 L205.5,341 L207.8,341 L210.2,341 L212.6,341 L215,341 L217.4,341 L219.7,341 L222.1,341 L224.5,341 L226.9,341 L229.2,341 L231.6,341 L234,341 L236.4,341 L238.8,341 L241.1,341 L243.5,341 L245.9,341 L248.3,341 L250.6,341 L253,341 L255.4,341 L257.8,341 L260.2,341 L262.5,341 L264.9,341 L267.3,341 L269.7,341 L272,341 L274.4,341 L276.8,341 L279.2,341 L281.6,341 L283.9,341 L286.3,341 L288.7,341 L291.1,341 L293.4,341 L295.8,341 L298.2,341 L300.6,341 L303,341 L305.3,341 L307.7,341 L310.1,341 L312.5,341 L314.8,341 L317.2,341 L319.6,341 L322,341 L324.4,341 L326.7,341 L329.1,341 L331.5,341 L333.9,341 L336.2,341 L338.6,341 L341,340.9 L343.4,340.8 L345.8,340.6 L348.1,340.2 L350.5,339.5 L352.9,338.4 L355.3,336.7 L357.6,334.3 L360,330.8 L362.4,326.2 L364.8,320.3 L367.2,313.2 L369.5,304.7 L371.9,295 L374.3,284.2 L376.7,272.7 L379,260.5 L381.4,247.9 L383.8,235.3 L386.2,222.7 L388.6,210.5 L390.9,198.7 L393.3,187.5 L395.7,177 L398.1,167.2 L400.4,158.2 L402.8,150 L405.2,142.5 L407.6,135.7 L410,129.6 L412.3,124.2 L414.7,119.3 L417.1,115 L419.5,111.2 L421.8,107.8 L424.2,104.9 L426.6,102.3 L429,99.98 L431.4,97.97 L433.7,96.21 L436.1,94.67 L438.5,93.32 L440.9,92.14 L443.2,91.11 L445.6,90.21 L448,89.42 L450.4,88.73 L452.8,88.12 L455.1,87.6 L457.5,87.14 L459.9,86.73 L462.3,86.38 L464.6,86.07 L467,85.79 L469.4,85.56 L471.8,85.35 L474.2,85.16 L476.5,85 L478.9,84.86 L481.3,84.73 L483.7,84.62 L486,84.52 L488.4,84.43 L490.8,84.36 L493.2,84.29 L495.6,84.23 L497.9,84.18 L500.3,84.13 L502.7,84.09 L505.1,84.05 L507.4,84.02 L509.8,83.99 L512.2,83.96 L514.6,83.94 L517,83.92 L519.3,83.9 L521.7,83.88 L524.1,83.87 L526.5,83.85 L528.8,83.84 L531.2,83.83 L533.6,83.82 L536,83.81 L538.4,83.8 L540.7,83.8 L543.1,83.79 L545.5,83.79 L547.9,83.78 L550.2,83.78 L552.6,83.77 L555,83.77 L557.4,83.76 L559.8,83.76 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(127,255,0)" stroke-width="1"><path d="M84.2,341 L86.58,341 L88.96,341 L91.33,341 L93.71,341 L96.09,341 L98.47,341 L100.8,341 L103.2,341 L105.6,341 L108,341 L110.4,341 L112.7,341 L115.1,341 L117.5,341 L119.9,341 L122.2,341 L124.6,341 L127,341 L129.4,341 L131.8,341 L134.1,341 L136.5,341 L138.9,341 L141.3,341 L143.6,341 L146,341 L148.4,341 L150.8,341 L153.2,341 L155.5,341 L157.9,341 L160.3,341 L162.7,341 L165,341 L167.4,341 L169.8,341 L172.2,341 L174.6,341 L176.9,341 L179.3,341 L181.7,341 L184.1,341 L186.4,341 L188.8,341 L191.2,341 L193.6,341 L196,341 L198.3,341 L200.7,341 L203.1,341 L205.5,341 L207.8,341 L210.2,341 L212.6,341 L215,341 L217.4,341 L219.7,341 L222.1,341 L224.5,341 L226.9,341 L229.2,341 L231.6,341 L234,341 L236.4,341 L238.8,341 L241.1,341 L243.5,341 L245.9,341 L248.3,341 L250.6,341 L253,341 L255.4,341 L257.8,341 L260.2,341 L262.5,341 L264.9,341 L267.3,341 L269.7,341 L272,341 L274.4,341 L276.8,341 L279.2,341 L281.6,341 L283.9,341 L286.3,341 L288.7,341 L291.1,341 L293.4,341 L295.8,341 L298.2,341 L300.6,341 L303,341 L305.3,341 L307.7,341 L310.1,341 L312.5,341 L314.8,341 L317.2,341 L319.6,341 L322,341 L324.4,341 L326.7,341 L329.1,341 L331.5,341 L333.9,341 L336.2,341 L338.6,341 L341,341 L343.4,341 L345.8,341 L348.1,341 L350.5,341 L352.9,341 L355.3,341 L357.6,341 L360,341 L362.4,341 L364.8,341 L367.2,341 L369.5,341 L371.9,341 L374.3,341 L376.7,341 L379,341 L381.4,341 L383.8,340.9 L386.2,340.9 L388.6,340.8 L390.9,340.6 L393.3,340.3 L395.7,339.9 L398.1,339.3 L400.4,338.4 L402.8,337.3 L405.2,335.7 L407.6,333.8 L410,331.4 L412.3,328.5 L414.7,325.1 L417.1,321.2 L419.5,316.7 L421.8,311.7 L424.2,306.1 L426.6,300.1 L429,293.7 L431.4,286.9 L433.7,279.8 L436.1,272.4 L438.5,264.8 L440.9,257.1 L443.2,249.3 L445.6,241.5 L448,233.7 L450.4,226.1 L452.8,218.5 L455.1,211.1 L457.5,203.9 L459.9,196.9 L462.3,190.2 L464.6,183.7 L467,177.5 L469.4,171.5 L471.8,165.9 L474.2,160.5 L476.5,155.4 L478.9,150.6 L481.3,146.1 L483.7,141.8 L486,137.8 L488.4,134 L490.8,130.5 L493.2,127.2 L495.6,124.1 L497.9,121.2 L500.3,118.5 L502.7,116 L505.1,113.7 L507.4,111.5 L509.8,109.5 L512.2,107.6 L514.6,105.9 L517,104.2 L519.3,102.7 L521.7,101.4 L524.1,100.1 L526.5,98.88 L528.8,97.78 L531.2,96.75 L533.6,95.81 L536,94.93 L538.4,94.12 L540.7,93.37 L543.1,92.67 L545.5,92.03 L547.9,91.43 L550.2,90.88 L552.6,90.37 L555,89.9 L557.4,89.46 L559.8,89.05 " fill="none"/></g>
<g clip-path="url(#plot_window)" stroke="rgb(0,0,139)" stroke-width="1"><path d="M84.2,341 L86.58,341 L88.96,341 L91.33,341 L93.71,341 L96.09,341 L98.47,341 L100.8,341 L103.2,341 L105.6,341 L108,341 L110.4,341 L112.7,341 L115.1,341 L117.5,341 L119.9,341 L122.2,341 L124.6,341 L127,341 L129.4,341 L131.8,341 L134.1,341 L136.5,341 L138.9,341 L141.3,341 L143.6,341 L146,341 L148.4,341 L150.8,341 L153.2,341 L155.5,341 L157.9,341 L160.3,341 L162.7,341 L165,341 L167.4,341 L169.8,341 L172.2,341 L174.6,341 L176.9,341 L179.3,341 L181.7,341 L184.1,341 L186.4,341 L188.8,341 L191.2,341 L193.6,341 L196,341 L198.3,341 L200.7,341 L203.1,341 L205.5,341 L207.8,341 L210.2,341 L212.6,341 L215,341 L217.4,341 L219.7,341 L222.1,341 L224.5,341 L226.9,341 L229.2,341 L231.6,341 L234,341 L236.4,341 L238.8,341 L241.1,341 L243.5,341 L245.9,341 L248.3,341 L250.6,341 L253,341 L255.4,341 L257.8,341 L260.2,341 L262.5,341 L264.9,341 L267.3,341 L269.7,341 L272,341 L274.4,341 L276.8,341 L279.2,341 L281.6,341 L283.9,341 L286.3,341 L288.7,341 L291.1,341 L293.4,341 L295.8,341 L298.2,341 L300.6,341 L303,341 L305.3,341 L307.7,341 L310.1,341 L312.5,341 L314.8,341 L317.2,341 L319.6,341 L322,341 L324.4,341 L326.7,341 L329.1,341 L331.5,341 L333.9,341 L336.2,341 L338.6,341 L341,341 L343.4,341 L345.8,341 L348.1,341 L350.5,341 L352.9,341 L355.3,341 L357.6,341 L360,341 L362.4,341 L364.8,341 L367.2,341 L369.5,341 L371.9,341 L374.3,341 L376.7,341 L379,341 L381.4,341 L383.8,341 L386.2,341 L388.6,341 L390.9,341 L393.3,341 L395.7,341 L398.1,341 L400.4,341 L402.8,341 L405.2,341 L407.6,341 L410,341 L412.3,341 L414.7,341 L417.1,341 L419.5,341 L421.8,341 L424.2,341 L426.6,341 L429,341 L431.4,341 L433.7,341 L436.1,341 L438.5,341 L440.9,341 L443.2,341 L445.6,341 L448,341 L450.4,341 L452.8,341 L455.1,341 L457.5,341 L459.9,341 L462.3,341 L464.6,341 L467,341 L469.4,340.9 L471.8,340.9 L474.2,340.8 L476.5,340.6 L478.9,340.3 L481.3,339.8 L483.7,339 L486,337.7 L488.4,335.8 L490.8,332.9 L493.2,328.9 L495.6,323.5 L497.9,316.4 L500.3,307.3 L502.7,296.1 L505.1,282.8 L507.4,267.5 L509.8,250.5 L512.2,232.3 L514.6,213.4 L517,194.5 L519.3,176.2 L521.7,159.1 L524.1,143.6 L526.5,130.1 L528.8,118.6 L531.2,109.3 L533.6,101.9 L536,96.32 L538.4,92.19 L540.7,89.24 L543.1,87.21 L545.5,85.86 L547.9,85 L550.2,84.46 L552.6,84.14 L555,83.95 L557.4,83.84 L559.8,83.79 " fill="none"/></g>
</g>
<g id="plotPoints" clip-path="url(#plot_window)"></g>
<g id="legendBackground" stroke="rgb(119,136,153)" fill="rgb(255,255,255)" stroke-width="1"><rect x="573.8" y="58" width="150.2" height="195"/><rect x="573.8" y="58" width="150.2" height="195"/></g>
<g id="legendPoints"><g stroke="rgb(0,0,139)" fill="rgb(255,255,255)" stroke-width="1"><line x1="588.8" y1="88" x2="603.8" y2="88"/></g>
<g stroke="rgb(139,0,0)" fill="rgb(255,255,255)" stroke-width="1"><line x1="588.8" y1="118" x2="603.8" y2="118"/></g>
<g stroke="rgb(0,100,0)" fill="rgb(255,255,255)" stroke-width="1"><line x1="588.8" y1="148" x2="603.8" y2="148"/></g>
<g stroke="rgb(255,140,0)" fill="rgb(255,255,255)" stroke-width="1"><line x1="588.8" y1="178" x2="603.8" y2="178"/></g>
<g stroke="rgb(127,255,0)" fill="rgb(255,255,255)" stroke-width="1"><line x1="588.8" y1="208" x2="603.8" y2="208"/></g>
<g stroke="rgb(0,0,139)" fill="rgb(255,255,255)" stroke-width="1"><line x1="588.8" y1="238" x2="603.8" y2="238"/></g>
</g>
<g id="legendText">
<text x="611.3" y="88" font-size="15" font-family="Verdana">v=10, &#x3B4;=-10</text>
<text x="611.3" y="118" font-size="15" font-family="Verdana">v=10, &#x3B4;=-5</text>
<text x="611.3" y="148" font-size="15" font-family="Verdana">v=10, &#x3B4;=0</text>
<text x="611.3" y="178" font-size="15" font-family="Verdana">v=10, &#x3B4;=5</text>
<text x="611.3" y="208" font-size="15" font-family="Verdana">v=10, &#x3B4;=10</text>
<text x="611.3" y="238" font-size="15" font-family="Verdana">v=inf, &#x3B4;=15</text></g>
<g id="title">
<text x="375" y="40" text-anchor="middle" font-size="20" font-family="Verdana">Non Central T CDF</text></g>
<g id="plotXValues"></g>
<g id="plotYValues"></g>
</svg>
