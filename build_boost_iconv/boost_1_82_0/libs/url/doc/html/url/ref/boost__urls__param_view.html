<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>param_view</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../../pt01.html" title="Part Two: Reference. ">
<link rel="prev" href="boost__urls__param_pct_view/operator_param_view.html" title="param_pct_view::operator param_view">
<link rel="next" href="boost__urls__param_view/key.html" title="param_view::key">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost__urls__param_pct_view/operator_param_view.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../pt01.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost__urls__param_view/key.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="url.ref.boost__urls__param_view"></a><a class="link" href="boost__urls__param_view.html" title="param_view">param_view</a>
</h4></div></div></div>
<p>
        A query parameter.
      </p>
<h5>
<a name="url.ref.boost__urls__param_view.h0"></a>
        <span class="phrase"><a name="url.ref.boost__urls__param_view.synopsis"></a></span><a class="link" href="boost__urls__param_view.html#url.ref.boost__urls__param_view.synopsis">Synopsis</a>
      </h5>
<p>
        Defined in header <code class="literal">&lt;<a href="https://github.com/boostorg/url/blob/master/include/boost/url/param.hpp" target="_top">boost/url/param.hpp</a>&gt;</code>
      </p>
<pre class="programlisting"><span class="keyword">struct</span> <span class="identifier">param_view</span>
</pre>
<h5>
<a name="url.ref.boost__urls__param_view.h1"></a>
        <span class="phrase"><a name="url.ref.boost__urls__param_view.member_functions"></a></span><a class="link" href="boost__urls__param_view.html#url.ref.boost__urls__param_view.member_functions">Member
        Functions</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <span class="bold"><strong><a class="link" href="boost__urls__param_view/operator_param.html" title="param_view::operator param">operator
                  param</a></strong></span>
                </p>
              </td>
<td>
                <p>
                  Conversion.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong><a class="link" href="boost__urls__param_view/param_view.html" title="param_view::param_view">param_view</a> <span class="silver">[constructor]</span></strong></span>
                </p>
              </td>
<td>
                <p>
                  Constructor.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h5>
<a name="url.ref.boost__urls__param_view.h2"></a>
        <span class="phrase"><a name="url.ref.boost__urls__param_view.data_members"></a></span><a class="link" href="boost__urls__param_view.html#url.ref.boost__urls__param_view.data_members">Data
        Members</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <span class="bold"><strong><a class="link" href="boost__urls__param_view/has_value.html" title="param_view::has_value">has_value</a></strong></span>
                </p>
              </td>
<td>
                <p>
                  True if a value is present.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong><a class="link" href="boost__urls__param_view/key.html" title="param_view::key">key</a></strong></span>
                </p>
              </td>
<td>
                <p>
                  The key.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <span class="bold"><strong><a class="link" href="boost__urls__param_view/value.html" title="param_view::value">value</a></strong></span>
                </p>
              </td>
<td>
                <p>
                  The value.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h5>
<a name="url.ref.boost__urls__param_view.h3"></a>
        <span class="phrase"><a name="url.ref.boost__urls__param_view.description"></a></span><a class="link" href="boost__urls__param_view.html#url.ref.boost__urls__param_view.description">Description</a>
      </h5>
<p>
        Objects of this type represent a single key and value pair in a query string
        where a key is always present and may be empty, while the presence of a value
        is indicated by <a class="link" href="boost__urls__param_view/has_value.html" title="param_view::has_value"><code class="computeroutput"><span class="identifier">has_value</span></code></a> equal to true. An empty
        value is distinct from no value. Depending on where the object was obtained,
        the strings may or may not contain percent escapes. For most usages, key
        comparisons are case-sensitive and duplicate keys in a query are possible.
        However, it is the authority that has final control over how the query is
        interpreted.
      </p>
<p>
        Keys and values in this object reference external character buffers. Ownership
        of the buffers is not transferred; the caller is responsible for ensuring
        that the assigned buffers remain valid until they are no longer referenced.
      </p>
<h5>
<a name="url.ref.boost__urls__param_view.h4"></a>
        <span class="phrase"><a name="url.ref.boost__urls__param_view.bnf"></a></span><a class="link" href="boost__urls__param_view.html#url.ref.boost__urls__param_view.bnf">BNF</a>
      </h5>
<pre class="programlisting"><span class="identifier">query</span><span class="special">-</span><span class="identifier">params</span>    <span class="special">=</span> <span class="identifier">query</span><span class="special">-</span><span class="identifier">param</span> <span class="special">*(</span> <span class="string">"&amp;"</span> <span class="identifier">query</span><span class="special">-</span><span class="identifier">param</span> <span class="special">)</span>
<span class="identifier">query</span><span class="special">-</span><span class="identifier">param</span>     <span class="special">=</span> <span class="identifier">key</span> <span class="special">[</span> <span class="string">"="</span> <span class="identifier">value</span> <span class="special">]</span>
<span class="identifier">key</span>             <span class="special">=</span> <span class="special">*</span><span class="identifier">qpchar</span>
<span class="identifier">value</span>           <span class="special">=</span> <span class="special">*(</span> <span class="identifier">qpchar</span> <span class="special">/</span> <span class="string">"="</span> <span class="special">)</span>
</pre>
<h5>
<a name="url.ref.boost__urls__param_view.h5"></a>
        <span class="phrase"><a name="url.ref.boost__urls__param_view.specification"></a></span><a class="link" href="boost__urls__param_view.html#url.ref.boost__urls__param_view.specification">Specification</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            <a href="https://en.wikipedia.org/wiki/Query_string" target="_top">Query string
            (Wikipedia)</a>
          </li></ul></div>
<h5>
<a name="url.ref.boost__urls__param_view.h6"></a>
        <span class="phrase"><a name="url.ref.boost__urls__param_view.see_also"></a></span><a class="link" href="boost__urls__param_view.html#url.ref.boost__urls__param_view.see_also">See
        Also</a>
      </h5>
<p>
        <a class="link" href="boost__urls__param.html" title="param"><code class="computeroutput"><span class="identifier">param</span></code></a>,
        <a class="link" href="boost__urls__param_pct_view.html" title="param_pct_view"><code class="computeroutput"><span class="identifier">param_pct_view</span></code></a>.
      </p>
<p>
        Convenience header <code class="literal">&lt;<a href="https://github.com/boostorg/url/blob/master/include/boost/url.hpp" target="_top">boost/url.hpp</a>&gt;</code>
      </p>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost__urls__param_pct_view/operator_param_view.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../pt01.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost__urls__param_view/key.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
