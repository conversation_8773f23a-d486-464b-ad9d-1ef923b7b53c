<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>url_base::persist</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../boost__urls__url_base.html" title="url_base">
<link rel="prev" href="operator_string_view.html" title="url_base::operator string_view">
<link rel="next" href="has_scheme.html" title="url_base::has_scheme">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="operator_string_view.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__url_base.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="has_scheme.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="url.ref.boost__urls__url_base.persist"></a><a class="link" href="persist.html" title="url_base::persist">url_base::persist</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm122800"></a>
        </p>
<p>
          (Inherited from <a class="link" href="../boost__urls__url_view_base.html" title="url_view_base"><code class="computeroutput"><span class="identifier">url_view_base</span></code></a>)
        </p>
<p>
          Return a shared, persistent copy of the url.
        </p>
<h6>
<a name="url.ref.boost__urls__url_base.persist.h0"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url_base.persist.synopsis"></a></span><a class="link" href="persist.html#url.ref.boost__urls__url_base.persist.synopsis">Synopsis</a>
        </h6>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">shared_ptr</span><span class="special">&lt;</span> <span class="identifier">url_view</span> <span class="keyword">const</span> <span class="special">&gt;</span>
<span class="identifier">persist</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<h6>
<a name="url.ref.boost__urls__url_base.persist.h1"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url_base.persist.description"></a></span><a class="link" href="persist.html#url.ref.boost__urls__url_base.persist.description">Description</a>
        </h6>
<p>
          This function returns a read-only copy of the url, with shared lifetime.
          The returned value owns (persists) the underlying string. The algorithm
          used to create the value minimizes the number of individual memory allocations,
          making it more efficient than when using direct standard library functions.
        </p>
<h6>
<a name="url.ref.boost__urls__url_base.persist.h2"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url_base.persist.example"></a></span><a class="link" href="persist.html#url.ref.boost__urls__url_base.persist.example">Example</a>
        </h6>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">shared_ptr</span><span class="special">&lt;</span> <span class="identifier">url_view</span> <span class="keyword">const</span> <span class="special">&gt;</span> <span class="identifier">sp</span><span class="special">;</span>
<span class="special">{</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">s</span><span class="special">(</span> <span class="string">"http://example.com"</span> <span class="special">);</span>
    <span class="identifier">url_view</span> <span class="identifier">u</span><span class="special">(</span> <span class="identifier">s</span> <span class="special">);</span>                        <span class="comment">// u references characters in s</span>

    <span class="identifier">assert</span><span class="special">(</span> <span class="identifier">u</span><span class="special">.</span><span class="identifier">data</span><span class="special">()</span> <span class="special">==</span> <span class="identifier">s</span><span class="special">.</span><span class="identifier">data</span><span class="special">()</span> <span class="special">);</span>         <span class="comment">// same buffer</span>

    <span class="identifier">sp</span> <span class="special">=</span> <span class="identifier">u</span><span class="special">.</span><span class="identifier">persist</span><span class="special">();</span>

    <span class="identifier">assert</span><span class="special">(</span> <span class="identifier">sp</span><span class="special">-&gt;</span><span class="identifier">data</span><span class="special">()</span> <span class="special">!=</span> <span class="identifier">s</span><span class="special">.</span><span class="identifier">data</span><span class="special">()</span> <span class="special">);</span>       <span class="comment">// different buffer</span>
    <span class="identifier">assert</span><span class="special">(</span> <span class="identifier">sp</span><span class="special">-&gt;</span><span class="identifier">buffer</span><span class="special">()</span> <span class="special">==</span> <span class="identifier">s</span><span class="special">);</span>             <span class="comment">// same contents</span>

    <span class="comment">// s is destroyed and thus u</span>
    <span class="comment">// becomes invalid, but sp remains valid.</span>
<span class="special">}</span>
</pre>
<h6>
<a name="url.ref.boost__urls__url_base.persist.h3"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url_base.persist.complexity"></a></span><a class="link" href="persist.html#url.ref.boost__urls__url_base.persist.complexity">Complexity</a>
        </h6>
<p>
          Linear in <code class="computeroutput"><span class="keyword">this</span><span class="special">-&gt;</span><span class="identifier">size</span><span class="special">()</span></code>.
        </p>
<h6>
<a name="url.ref.boost__urls__url_base.persist.h4"></a>
          <span class="phrase"><a name="url.ref.boost__urls__url_base.persist.exception_safety"></a></span><a class="link" href="persist.html#url.ref.boost__urls__url_base.persist.exception_safety">Exception
          Safety</a>
        </h6>
<p>
          Calls to allocate may throw.
        </p>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="operator_string_view.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__url_base.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="has_scheme.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
