<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>parse_uri_reference</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../../pt01.html" title="Part Two: Reference. ">
<link rel="prev" href="boost__urls__parse_uri.html" title="parse_uri">
<link rel="next" href="boost__urls__parse_ipv6_address.html" title="parse_ipv6_address">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost__urls__parse_uri.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../pt01.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost__urls__parse_ipv6_address.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="url.ref.boost__urls__parse_uri_reference"></a><a class="link" href="boost__urls__parse_uri_reference.html" title="parse_uri_reference">parse_uri_reference</a>
</h4></div></div></div>
<p>
        <a class="indexterm" name="idm146497"></a>
      </p>
<p>
        Return a reference to a parsed URL string.
      </p>
<h5>
<a name="url.ref.boost__urls__parse_uri_reference.h0"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_uri_reference.synopsis"></a></span><a class="link" href="boost__urls__parse_uri_reference.html#url.ref.boost__urls__parse_uri_reference.synopsis">Synopsis</a>
      </h5>
<p>
        Defined in header <code class="literal">&lt;<a href="https://github.com/boostorg/url/blob/master/include/boost/url/parse.hpp" target="_top">boost/url/parse.hpp</a>&gt;</code>
      </p>
<pre class="programlisting"><span class="identifier">result</span><span class="special">&lt;</span> <span class="identifier">url_view</span> <span class="special">&gt;</span>
<span class="identifier">parse_uri_reference</span><span class="special">(</span>
    <span class="identifier">string_view</span> <span class="identifier">s</span><span class="special">);</span>
</pre>
<h5>
<a name="url.ref.boost__urls__parse_uri_reference.h1"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_uri_reference.description"></a></span><a class="link" href="boost__urls__parse_uri_reference.html#url.ref.boost__urls__parse_uri_reference.description">Description</a>
      </h5>
<p>
        This function parses a string according to the grammar below and returns
        a view referencing the passed string upon success, else returns an error.
        Ownership of the string is not transferred; the caller is responsible for
        ensuring that the lifetime of the character buffer extends until the view
        is no longer being accessed.
      </p>
<h5>
<a name="url.ref.boost__urls__parse_uri_reference.h2"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_uri_reference.example"></a></span><a class="link" href="boost__urls__parse_uri_reference.html#url.ref.boost__urls__parse_uri_reference.example">Example</a>
      </h5>
<pre class="programlisting"><span class="identifier">result</span><span class="special">&lt;</span> <span class="identifier">url_view</span> <span class="special">&gt;</span> <span class="special">=</span> <span class="identifier">parse_uri_reference</span><span class="special">(</span> <span class="string">"ws://echo.example.com/?name=boost#demo"</span> <span class="special">);</span>
</pre>
<h5>
<a name="url.ref.boost__urls__parse_uri_reference.h3"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_uri_reference.bnf"></a></span><a class="link" href="boost__urls__parse_uri_reference.html#url.ref.boost__urls__parse_uri_reference.bnf">BNF</a>
      </h5>
<pre class="programlisting"><span class="identifier">URI</span><span class="special">-</span><span class="identifier">reference</span> <span class="special">=</span> <span class="identifier">URI</span> <span class="special">/</span> <span class="identifier">relative</span><span class="special">-</span><span class="identifier">ref</span>

<span class="identifier">URI</span>           <span class="special">=</span> <span class="identifier">scheme</span> <span class="string">":"</span> <span class="identifier">hier</span><span class="special">-</span><span class="identifier">part</span> <span class="special">[</span> <span class="string">"?"</span> <span class="identifier">query</span> <span class="special">]</span> <span class="special">[</span> <span class="string">"#"</span> <span class="identifier">fragment</span> <span class="special">]</span>

<span class="identifier">relative</span><span class="special">-</span><span class="identifier">ref</span>  <span class="special">=</span> <span class="identifier">relative</span><span class="special">-</span><span class="identifier">part</span> <span class="special">[</span> <span class="string">"?"</span> <span class="identifier">query</span> <span class="special">]</span> <span class="special">[</span> <span class="string">"#"</span> <span class="identifier">fragment</span> <span class="special">]</span>

<span class="identifier">hier</span><span class="special">-</span><span class="identifier">part</span>     <span class="special">=</span> <span class="string">"//"</span> <span class="identifier">authority</span> <span class="identifier">path</span><span class="special">-</span><span class="identifier">abempty</span>
              <span class="special">/</span> <span class="identifier">path</span><span class="special">-</span><span class="identifier">absolute</span>
              <span class="special">/</span> <span class="identifier">path</span><span class="special">-</span><span class="identifier">rootless</span>
              <span class="special">/</span> <span class="identifier">path</span><span class="special">-</span><span class="identifier">empty</span>

<span class="identifier">relative</span><span class="special">-</span><span class="identifier">part</span> <span class="special">=</span> <span class="string">"//"</span> <span class="identifier">authority</span> <span class="identifier">path</span><span class="special">-</span><span class="identifier">abempty</span>
              <span class="special">/</span> <span class="identifier">path</span><span class="special">-</span><span class="identifier">absolute</span>
              <span class="special">/</span> <span class="identifier">path</span><span class="special">-</span><span class="identifier">noscheme</span>
              <span class="special">/</span> <span class="identifier">path</span><span class="special">-</span><span class="identifier">abempty</span>
              <span class="special">/</span> <span class="identifier">path</span><span class="special">-</span><span class="identifier">empty</span>
</pre>
<h5>
<a name="url.ref.boost__urls__parse_uri_reference.h4"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_uri_reference.exceptions"></a></span><a class="link" href="boost__urls__parse_uri_reference.html#url.ref.boost__urls__parse_uri_reference.exceptions">Exceptions</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Type
                </p>
              </th>
<th>
                <p>
                  Thrown On
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">length_error</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">s</span><span class="special">.</span><span class="identifier">size</span><span class="special">()</span>
                  <span class="special">&gt;</span> <span class="identifier">url_view</span><span class="special">::</span><span class="identifier">max_size</span></code>
                </p>
              </td>
</tr></tbody>
</table></div>
<h5>
<a name="url.ref.boost__urls__parse_uri_reference.h5"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_uri_reference.return_value"></a></span><a class="link" href="boost__urls__parse_uri_reference.html#url.ref.boost__urls__parse_uri_reference.return_value">Return Value</a>
      </h5>
<p>
        A <a class="link" href="boost__urls__result.html" title="result"><code class="computeroutput"><span class="identifier">result</span></code></a>
        containing a value or an error
      </p>
<h5>
<a name="url.ref.boost__urls__parse_uri_reference.h6"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_uri_reference.parameters"></a></span><a class="link" href="boost__urls__parse_uri_reference.html#url.ref.boost__urls__parse_uri_reference.parameters">Parameters</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">s</span></code>
                </p>
              </td>
<td>
                <p>
                  The string to parse
                </p>
              </td>
</tr></tbody>
</table></div>
<h5>
<a name="url.ref.boost__urls__parse_uri_reference.h7"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_uri_reference.specification"></a></span><a class="link" href="boost__urls__parse_uri_reference.html#url.ref.boost__urls__parse_uri_reference.specification">Specification</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <a href="https://datatracker.ietf.org/doc/html/rfc3986#section-4.1" target="_top">4.1.
            URI Reference (rfc3986)</a>
          </li>
<li class="listitem">
            <a href="https://www.rfc-editor.org/errata/eid5428" target="_top">Errata ID: 5428
            (rfc3986)</a>
          </li>
</ul></div>
<h5>
<a name="url.ref.boost__urls__parse_uri_reference.h8"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_uri_reference.see_also"></a></span><a class="link" href="boost__urls__parse_uri_reference.html#url.ref.boost__urls__parse_uri_reference.see_also">See
        Also</a>
      </h5>
<p>
        <a class="link" href="boost__urls__parse_absolute_uri.html" title="parse_absolute_uri"><code class="computeroutput"><span class="identifier">parse_absolute_uri</span></code></a>,
        <a class="link" href="boost__urls__parse_origin_form.html" title="parse_origin_form"><code class="computeroutput"><span class="identifier">parse_origin_form</span></code></a>,
        <a class="link" href="boost__urls__parse_relative_ref.html" title="parse_relative_ref"><code class="computeroutput"><span class="identifier">parse_relative_ref</span></code></a>,
        <a class="link" href="boost__urls__parse_uri.html" title="parse_uri"><code class="computeroutput"><span class="identifier">parse_uri</span></code></a>,
        <a class="link" href="boost__urls__url_view.html" title="url_view"><code class="computeroutput"><span class="identifier">url_view</span></code></a>.
      </p>
<p>
        Convenience header <code class="literal">&lt;<a href="https://github.com/boostorg/url/blob/master/include/boost/url.hpp" target="_top">boost/url.hpp</a>&gt;</code>
      </p>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost__urls__parse_uri.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../pt01.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost__urls__parse_ipv6_address.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
