<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>parse_path</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../../pt01.html" title="Part Two: Reference. ">
<link rel="prev" href="boost__urls__parse_query.html" title="parse_query">
<link rel="next" href="boost__urls__parse_absolute_uri.html" title="parse_absolute_uri">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost__urls__parse_query.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../pt01.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost__urls__parse_absolute_uri.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="url.ref.boost__urls__parse_path"></a><a class="link" href="boost__urls__parse_path.html" title="parse_path">parse_path</a>
</h4></div></div></div>
<p>
        <a class="indexterm" name="idm145762"></a>
      </p>
<p>
        Parse a string and return an encoded segment view.
      </p>
<h5>
<a name="url.ref.boost__urls__parse_path.h0"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_path.synopsis"></a></span><a class="link" href="boost__urls__parse_path.html#url.ref.boost__urls__parse_path.synopsis">Synopsis</a>
      </h5>
<p>
        Defined in header <code class="literal">&lt;<a href="https://github.com/boostorg/url/blob/master/include/boost/url/parse_path.hpp" target="_top">boost/url/parse_path.hpp</a>&gt;</code>
      </p>
<pre class="programlisting"><span class="identifier">result</span><span class="special">&lt;</span> <span class="identifier">segments_encoded_view</span> <span class="special">&gt;</span>
<span class="identifier">parse_path</span><span class="special">(</span>
    <span class="identifier">string_view</span> <span class="identifier">s</span><span class="special">);</span>
</pre>
<h5>
<a name="url.ref.boost__urls__parse_path.h1"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_path.description"></a></span><a class="link" href="boost__urls__parse_path.html#url.ref.boost__urls__parse_path.description">Description</a>
      </h5>
<p>
        This function parses the string and returns the corresponding path object
        if the string is valid, otherwise returns an error.
      </p>
<h5>
<a name="url.ref.boost__urls__parse_path.h2"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_path.bnf"></a></span><a class="link" href="boost__urls__parse_path.html#url.ref.boost__urls__parse_path.bnf">BNF</a>
      </h5>
<pre class="programlisting"><span class="identifier">path</span>          <span class="special">=</span> <span class="special">[</span> <span class="string">"/"</span> <span class="special">]</span> <span class="identifier">segment</span> <span class="special">*(</span> <span class="string">"/"</span> <span class="identifier">segment</span> <span class="special">)</span>
</pre>
<h5>
<a name="url.ref.boost__urls__parse_path.h3"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_path.exception_safety"></a></span><a class="link" href="boost__urls__parse_path.html#url.ref.boost__urls__parse_path.exception_safety">Exception
        Safety</a>
      </h5>
<p>
        No-throw guarantee.
      </p>
<h5>
<a name="url.ref.boost__urls__parse_path.h4"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_path.return_value"></a></span><a class="link" href="boost__urls__parse_path.html#url.ref.boost__urls__parse_path.return_value">Return
        Value</a>
      </h5>
<p>
        A valid view on success, otherwise an error code.
      </p>
<h5>
<a name="url.ref.boost__urls__parse_path.h5"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_path.parameters"></a></span><a class="link" href="boost__urls__parse_path.html#url.ref.boost__urls__parse_path.parameters">Parameters</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">s</span></code>
                </p>
              </td>
<td>
                <p>
                  The string to parse
                </p>
              </td>
</tr></tbody>
</table></div>
<h5>
<a name="url.ref.boost__urls__parse_path.h6"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_path.specification"></a></span><a class="link" href="boost__urls__parse_path.html#url.ref.boost__urls__parse_path.specification">Specification</a>
      </h5>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            <a href="https://datatracker.ietf.org/doc/html/rfc3986#section-3.3" target="_top">3.3.
            Path (rfc3986)</a>
          </li></ul></div>
<h5>
<a name="url.ref.boost__urls__parse_path.h7"></a>
        <span class="phrase"><a name="url.ref.boost__urls__parse_path.see_also"></a></span><a class="link" href="boost__urls__parse_path.html#url.ref.boost__urls__parse_path.see_also">See
        Also</a>
      </h5>
<p>
        <a class="link" href="boost__urls__segments_encoded_view.html" title="segments_encoded_view"><code class="computeroutput"><span class="identifier">segments_encoded_view</span></code></a>.
      </p>
<p>
        Convenience header <code class="literal">&lt;<a href="https://github.com/boostorg/url/blob/master/include/boost/url.hpp" target="_top">boost/url.hpp</a>&gt;</code>
      </p>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="boost__urls__parse_query.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../pt01.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="boost__urls__parse_absolute_uri.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
