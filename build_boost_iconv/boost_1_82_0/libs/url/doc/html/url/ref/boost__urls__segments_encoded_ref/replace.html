<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>segments_encoded_ref::replace</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.URL">
<link rel="up" href="../boost__urls__segments_encoded_ref.html" title="segments_encoded_ref">
<link rel="prev" href="erase/overload2.html" title="segments_encoded_ref::erase (2 of 2 overloads)">
<link rel="next" href="replace/overload1.html" title="segments_encoded_ref::replace (1 of 4 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="erase/overload2.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__segments_encoded_ref.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="replace/overload1.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="url.ref.boost__urls__segments_encoded_ref.replace"></a><a class="link" href="replace.html" title="segments_encoded_ref::replace">segments_encoded_ref::replace</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm55556"></a>
        </p>
<p>
          Replace segments.
        </p>
<pre class="programlisting"><span class="identifier">iterator</span>
<a class="link" href="replace/overload1.html" title="segments_encoded_ref::replace (1 of 4 overloads)">replace</a><span class="special">(</span>
    <span class="identifier">iterator</span> <span class="identifier">pos</span><span class="special">,</span>
    <span class="identifier">pct_string_view</span> <span class="identifier">s</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="replace/overload1.html" title="segments_encoded_ref::replace (1 of 4 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>

<span class="identifier">iterator</span>
<a class="link" href="replace/overload2.html" title="segments_encoded_ref::replace (2 of 4 overloads)">replace</a><span class="special">(</span>
    <span class="identifier">iterator</span> <span class="identifier">from</span><span class="special">,</span>
    <span class="identifier">iterator</span> <span class="identifier">to</span><span class="special">,</span>
    <span class="identifier">pct_string_view</span> <span class="identifier">s</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="replace/overload2.html" title="segments_encoded_ref::replace (2 of 4 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>

<span class="identifier">iterator</span>
<a class="link" href="replace/overload3.html" title="segments_encoded_ref::replace (3 of 4 overloads)">replace</a><span class="special">(</span>
    <span class="identifier">iterator</span> <span class="identifier">from</span><span class="special">,</span>
    <span class="identifier">iterator</span> <span class="identifier">to</span><span class="special">,</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">pct_string_view</span> <span class="special">&gt;</span> <span class="identifier">init</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="replace/overload3.html" title="segments_encoded_ref::replace (3 of 4 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>

<span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">FwdIt</span><span class="special">&gt;</span>
<span class="identifier">iterator</span>
<a class="link" href="replace/overload4.html" title="segments_encoded_ref::replace (4 of 4 overloads)">replace</a><span class="special">(</span>
    <span class="identifier">iterator</span> <span class="identifier">from</span><span class="special">,</span>
    <span class="identifier">iterator</span> <span class="identifier">to</span><span class="special">,</span>
    <span class="identifier">FwdIt</span> <span class="identifier">first</span><span class="special">,</span>
    <span class="identifier">FwdIt</span> <span class="identifier">last</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="replace/overload4.html" title="segments_encoded_ref::replace (4 of 4 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2021, 2022 Vinnie Falco, Alan de Freitas<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="erase/overload2.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__urls__segments_encoded_ref.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="replace/overload1.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
