<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html><meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BOOST_OUTCOME_REQUIRES(&hellip;) - Boost.Outcome documentation</title>
<link rel="stylesheet" href="../../css/boost.css" type="text/css">
<meta name="generator" content="Hugo 0.52 with Boostdoc theme">
<meta name="viewport" content="width=device-width,initial-scale=1.0"/>

<link rel="icon" href="../../images/favicon.ico" type="image/ico"/>
<body><div class="spirit-nav">
<a accesskey="p" href="../../reference/macros/nodiscard.html"><img src="../../images/prev.png" alt="Prev"></a>
    <a accesskey="u" href="../../reference/macros.html"><img src="../../images/up.png" alt="Up"></a>
    <a accesskey="h" href="../../index.html"><img src="../../images/home.png" alt="Home"></a><a accesskey="n" href="../../reference/macros/symbol_visible.html"><img src="../../images/next.png" alt="Next"></a></div><div id="content">
  <div class="titlepage"><div><div><h1 style="clear: both"><code>BOOST_OUTCOME_REQUIRES(...)</code></h1></div></div></div>
  <p>A C++ 20 <code>requires(...)</code>, if available.</p>

<p><em>Overridable</em>: Define before inclusion.</p>

<p><em>Default</em>: To <code>requires(...)</code> if on C++ 20 or later, else to nothing.</p>

<p><em>Header</em>: <code>&lt;boost/outcome/config.hpp&gt;</code></p>


        </div><p><small>Last revised: December 12, 2018 at 22:01:42 UTC</small></p>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../reference/macros/nodiscard.html"><img src="../../images/prev.png" alt="Prev"></a>
    <a accesskey="u" href="../../reference/macros.html"><img src="../../images/up.png" alt="Up"></a>
    <a accesskey="h" href="../../index.html"><img src="../../images/home.png" alt="Home"></a><a accesskey="n" href="../../reference/macros/symbol_visible.html"><img src="../../images/next.png" alt="Next"></a></div></body>
</html>
