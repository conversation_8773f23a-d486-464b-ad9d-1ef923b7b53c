<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html><meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>basic_outcome(const failure_type&lt;EC&gt; &amp;) - Boost.Outcome documentation</title>
<link rel="stylesheet" href="../../../css/boost.css" type="text/css">
<meta name="generator" content="Hugo 0.52 with Boostdoc theme">
<meta name="viewport" content="width=device-width,initial-scale=1.0"/>

<link rel="icon" href="../../../images/favicon.ico" type="image/ico"/>
<body><div class="spirit-nav">
<a accesskey="p" href="../../../reference/types/basic_outcome/implicit_success_move_constructor.html"><img src="../../../images/prev.png" alt="Prev"></a>
    <a accesskey="u" href="../../../reference/types/basic_outcome.html"><img src="../../../images/up.png" alt="Up"></a>
    <a accesskey="h" href="../../../index.html"><img src="../../../images/home.png" alt="Home"></a><a accesskey="n" href="../../../reference/types/basic_outcome/implicit_failure_error_move_constructor.html"><img src="../../../images/next.png" alt="Next"></a></div><div id="content">
  <div class="titlepage"><div><div><h1 style="clear: both"><code>basic_outcome(const failure_type&lt;EC&gt; &amp;)</code></h1></div></div></div>
  <p>Implicit error-from-failure-type-sugar copy constructor used to disambiguate the construction of the error type.
Calls <a href="../../../reference/policies/base/on_outcome_copy_construction.html" class="api-reference"><code>void on_outcome_copy_construction(T *, U &amp;&amp;) noexcept</code></a>
 with <code>this</code> and <code>const failure_type&lt;EC&gt; &amp;</code>.</p>

<p><em>Requires</em>: <code>predicate::enable_compatible_conversion&lt;void, EC, void, void&gt;</code> is true.</p>

<p><em>Complexity</em>: Same as for the <code>error_type</code> constructor which accepts <code>EC</code>. Constexpr, triviality and noexcept of underlying operations is propagated.</p>


        </div><p><small>Last revised: December 17, 2020 at 11:27:06 UTC</small></p>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../../reference/types/basic_outcome/implicit_success_move_constructor.html"><img src="../../../images/prev.png" alt="Prev"></a>
    <a accesskey="u" href="../../../reference/types/basic_outcome.html"><img src="../../../images/up.png" alt="Up"></a>
    <a accesskey="h" href="../../../index.html"><img src="../../../images/home.png" alt="Home"></a><a accesskey="n" href="../../../reference/types/basic_outcome/implicit_failure_error_move_constructor.html"><img src="../../../images/next.png" alt="Next"></a></div></body>
</html>
