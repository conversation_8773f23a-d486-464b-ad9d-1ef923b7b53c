<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html>
<head>
   <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
   <meta name="GENERATOR" content="Mozilla/4.77 [en] (X11; U; Linux 2.2.19 i686) [Netscape]">
   <meta name="Author" content="<PERSON><PERSON>">
   <meta name="Description" content="Small library to propose minmax_element algorithm.">
   <title>Boost minmax library synopsis</title>
</head>
<body text="#000000" bgcolor="#FFFFFF" link="#0000EE" vlink="#551A8B" alink="#FF0000">

<center>
<h1>
Minmax_element complete synopsis</h1></center>

<h3>
Synopsis of <tt>&lt;boost/algorithm/minmax.hpp></tt></h3>

<pre>#include &lt;boost/tuple/tuple.hpp>

namespace boost {

  template &lt;class T>
  tuple&lt;T const&amp;, T const&amp;> >
  minmax(const T&amp; a, const T&amp; b);

  template &lt;class T, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  tuple&lt;T const&amp;, T const&amp;> >
  minmax(const T&amp; a, const T&amp; b, BinaryPredicate comp);

}
</pre>

<h3>
Synopsis of <tt>&lt;boost/algorithm/minmax_element.hpp></tt></h3>

<pre>#include &lt;utility> //for std::pair

namespace boost {

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  minmax_element(ForwardIterator first, ForwardIterator last);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  minmax_element(ForwardIterator first, ForwardIterator last,
                 BinaryPredicate comp);

  // Variants

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>>
  ForwardIterator first_min_element(ForwardIterator first, ForwardIterator last);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  ForwardIterator first_min_element(ForwardIterator first, ForwardIterator last,
                                    BinaryPredicate comp);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>>
  ForwardIterator last_min_element(ForwardIterator first, ForwardIterator last);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  ForwardIterator last_min_element(ForwardIterator first, ForwardIterator last,
                                   BinaryPredicate comp);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>>
  ForwardIterator first_max_element(ForwardIterator first, ForwardIterator last);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  ForwardIterator first_max_element(ForwardIterator first, ForwardIterator last,
                                    BinaryPredicate comp);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>>
  ForwardIterator last_max_element(ForwardIterator first, ForwardIterator last);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  ForwardIterator last_max_element(ForwardIterator first, ForwardIterator last,
                                   BinaryPredicate comp);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  first_min_first_max_element(ForwardIterator first, ForwardIterator last);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  first_min_first_max_element(ForwardIterator first, ForwardIterator last,
                             BinaryPredicate comp);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  first_min_last_max_element(ForwardIterator first, ForwardIterator last);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  first_min_last_max_element(ForwardIterator first, ForwardIterator last,
                             BinaryPredicate comp);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  last_min_first_max_element(ForwardIterator first, ForwardIterator last);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  last_min_first_max_element(ForwardIterator first, ForwardIterator last,
                             BinaryPredicate comp);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  last_min_last_max_element(ForwardIterator first, ForwardIterator last);

  template &lt;class <a href="https://www.boost.org/sgi/stl/ForwardIterator.html">ForwardIterator</a>, class <a href="https://www.boost.org/sgi/stl/BinaryPredicate.html">BinaryPredicate</a>>
  std::pair&lt;ForwardIterator,ForwardIterator>
  last_min_last_max_element(ForwardIterator first, ForwardIterator last,
                            BinaryPredicate comp);

}</pre>

<hr SIZE="6">
<br>Last modified 2002-07-01
<p><font face="Arial,Helvetica"><font size=-1>&copy; Copyright Herv&eacute;
Br&ouml;nnimann, Polytechnic University, 2002--2004. 
Use, modification, and distribution is subject to the Boost Software
License, Version 1.0. (See accompanying file <a href="../../../../LICENSE_1_0.txt">License_1_0.txt</a> or copy at
<a href="http://www.boost.org/LICENSE_1_0.txt">http://www.boost.org/LICENSE_1_0.txt</a>)
</font></font>
</body>
</html>
