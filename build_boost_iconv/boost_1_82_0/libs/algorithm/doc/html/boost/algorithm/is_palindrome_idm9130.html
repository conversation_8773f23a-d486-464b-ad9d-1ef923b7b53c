<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function template is_palindrome</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost Algorithm Library">
<link rel="up" href="../../header/boost/algorithm/is_palindrome_hpp.html" title="Header &lt;boost/algorithm/is_palindrome.hpp&gt;">
<link rel="prev" href="is_palindrome_idm9116.html" title="Function template is_palindrome">
<link rel="next" href="is_palindrome_idm9149.html" title="Function is_palindrome">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_palindrome_idm9116.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/algorithm/is_palindrome_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="is_palindrome_idm9149.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.algorithm.is_palindrome_idm9130"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function template is_palindrome</span></h2>
<p>boost::algorithm::is_palindrome</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../header/boost/algorithm/is_palindrome_hpp.html" title="Header &lt;boost/algorithm/is_palindrome.hpp&gt;">boost/algorithm/is_palindrome.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> R<span class="special">,</span> <span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
  <span class="keyword">bool</span> <span class="identifier">is_palindrome</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">R</span> <span class="special">&amp;</span> range<span class="special">,</span> <span class="identifier">Predicate</span> p<span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm15484"></a><h2>Description</h2>
<p>

</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>This function will return true for empty sequences and for palindromes. For other sequences function will return false. Complexity: O(N). </p></td></tr>
</table></div>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">p</code></span></p></td>
<td><p>A predicate used to compare the values.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">range</code></span></p></td>
<td><p>The range to be tested. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>true if the entire sequence is palindrome</p></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2010-2012 Marshall Clow<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="is_palindrome_idm9116.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/algorithm/is_palindrome_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="is_palindrome_idm9149.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
