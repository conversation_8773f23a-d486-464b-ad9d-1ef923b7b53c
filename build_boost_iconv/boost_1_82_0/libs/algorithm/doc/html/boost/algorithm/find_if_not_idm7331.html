<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function template find_if_not</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost Algorithm Library">
<link rel="up" href="../../header/boost/algorithm/cxx11/find_if_not_hpp.html" title="Header &lt;boost/algorithm/cxx11/find_if_not.hpp&gt;">
<link rel="prev" href="../../header/boost/algorithm/cxx11/find_if_not_hpp.html" title="Header &lt;boost/algorithm/cxx11/find_if_not.hpp&gt;">
<link rel="next" href="find_if_not_idm7355.html" title="Function template find_if_not">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../header/boost/algorithm/cxx11/find_if_not_hpp.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/algorithm/cxx11/find_if_not_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="find_if_not_idm7355.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.algorithm.find_if_not_idm7331"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function template find_if_not</span></h2>
<p>boost::algorithm::find_if_not — Finds the first element in the sequence that does not satisfy the predicate. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../header/boost/algorithm/cxx11/find_if_not_hpp.html" title="Header &lt;boost/algorithm/cxx11/find_if_not.hpp&gt;">boost/algorithm/cxx11/find_if_not.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
  <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <span class="identifier">InputIterator</span> 
  <span class="identifier">find_if_not</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> <span class="identifier">Predicate</span> p<span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm9508"></a><h2>Description</h2>
<p>

</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>This function is part of the C++2011 standard library. </p></td></tr>
</table></div>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The start of the input sequence </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>One past the end of the input sequence </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">p</code></span></p></td>
<td><p>A predicate for testing the elements of the range </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The iterator pointing to the desired element.</p></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2010-2012 Marshall Clow<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../header/boost/algorithm/cxx11/find_if_not_hpp.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/algorithm/cxx11/find_if_not_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="find_if_not_idm7355.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
